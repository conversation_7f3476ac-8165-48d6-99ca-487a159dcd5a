//=============================================================================
// VisuMZ_WeatherEffects_PerfPatch_v2.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc [Patch] Performance optimizations for VisuMZ_2_WeatherEffects.js (FIXED VERSION)
 * <AUTHOR> Performance Patch v2
 * @orderAfter VisuMZ_2_WeatherEffects
 * @help
 * This patch optimizes VisuMZ_2_WeatherEffects.js by:
 * 1. Throttling particle updates based on actual VisuMZ structure
 * 2. Smart visibility culling for off-screen particles
 * 3. Limiting maximum particles per weather layer
 * 4. Optimizing expensive method calls
 *
 * @param maxParticlesPerLayer
 * @text Max Particles Per Layer
 * @type number
 * @min 1
 * @desc Maximum number of active particles per weather layer. Default: 50
 * @default 50
 *
 * @param particleUpdateThrottle
 * @text Particle Update Throttle (frames)
 * @type number
 * @min 1
 * @desc Update particles every N frames. Higher = better performance. Default: 2
 * @default 2
 *
 * @param enableVisibilityCulling
 * @text Enable Visibility Culling
 * @type boolean
 * @desc Skip updates for particles outside the visible screen area. Default: true
 * @default true
 *
 * @param debugMode
 * @text Debug Mode
 * @type boolean
 * @desc Show performance information in console. Default: false
 * @default false
 */

(() => {
    'use strict';

    // Wait for proper initialization
    if (!Imported.VisuMZ_2_WeatherEffects) {
        console.warn(
            '[VisuMZ_WeatherEffects_PerfPatch_v2] VisuMZ_2_WeatherEffects not found. Patch disabled.'
        );
        return;
    }

    // Delayed initialization to ensure all classes are loaded
    const initPatch = () => {
        if (typeof Sprite_WeatherParticle === 'undefined' || typeof Weather === 'undefined') {
            setTimeout(initPatch, 100);
            return;
        }

        console.log('[VisuMZ_WeatherEffects_PerfPatch_v2] Applying performance optimizations...');

        const parameters = PluginManager.parameters('VisuMZ_WeatherEffects_PerfPatch_v2');
        const settings = {
            maxParticlesPerLayer: Number(parameters.maxParticlesPerLayer || 50),
            particleUpdateThrottle: Number(parameters.particleUpdateThrottle || 2),
            enableVisibilityCulling: parameters.enableVisibilityCulling !== 'false',
            debugMode: parameters.debugMode === 'true',
        };

        // Debug logging
        function debugLog(message) {
            if (settings.debugMode) {
                console.log(`[VisuMZ_WeatherEffects_PerfPatch_v2] ${message}`);
            }
        }

        // Performance statistics
        const perfStats = {
            particlesSkipped: 0,
            particlesUpdated: 0,
            particlesCulled: 0,
        };

        // 🎯 OPTIMIZATION 1: Throttle particle updates based on actual VisuMZ structure
        const _Sprite_WeatherParticle_update = Sprite_WeatherParticle.prototype.update;
        Sprite_WeatherParticle.prototype.update = function () {
            // Initialize update counter
            if (!this._updateCounter) this._updateCounter = 0;
            this._updateCounter++;

            // Always call parent update for basic sprite functionality
            Sprite.prototype.update.call(this);

            // Handle "none" type immediately (from original code)
            if (this.type === 'none') {
                return;
            }

            // Handle not loaded ready state (from original code)
            if (this._notLoadedReady) {
                return;
            }

            // Handle respawn delay (from original code)
            if (this._respawnDelay > 0) {
                this._opacityFadeInTime = 0;
                this.opacity = 0;
                return this._respawnDelay--;
            }

            // Throttle expensive updates
            if (this._updateCounter % settings.particleUpdateThrottle !== 0) {
                perfStats.particlesSkipped++;
                return;
            }

            // 🎯 OPTIMIZATION 2: Visibility culling
            if (settings.enableVisibilityCulling && !this.isParticleVisible()) {
                perfStats.particlesCulled++;
                this.visible = false;
                return;
            }

            // Perform the actual updates (from original VisuMZ code)
            this.updateLifespan();
            this.updateFlags();
            this.updateScale();
            this.updatePosition();
            this.updateOpacity();

            perfStats.particlesUpdated++;
        };

        // 🎯 OPTIMIZATION 3: Smart visibility culling
        Sprite_WeatherParticle.prototype.isParticleVisible = function () {
            // Safety check for Graphics object
            if (typeof Graphics === 'undefined' || !Graphics.width || !Graphics.height) {
                return true; // Assume visible if we can't determine bounds
            }

            const margin = 100; // Extra margin for smooth transitions
            const screenWidth = Graphics.width;
            const screenHeight = Graphics.height;

            return (
                this.x > -margin &&
                this.x < screenWidth + margin &&
                this.y > -margin &&
                this.y < screenHeight + margin
            );
        };

        // 🎯 OPTIMIZATION 4: Limit maximum particles per layer
        const _Weather_addSprite = Weather.prototype._addSprite;
        Weather.prototype._addSprite = function () {
            // Check if we've hit the particle limit
            if (this._sprites && this._sprites.length >= settings.maxParticlesPerLayer) {
                debugLog(
                    `Particle limit reached (${settings.maxParticlesPerLayer}) for weather layer`
                );
                return;
            }

            // Call original method
            _Weather_addSprite.call(this);
        };

        // 🎯 OPTIMIZATION 5: Performance monitoring
        if (settings.debugMode) {
            setInterval(() => {
                debugLog(
                    `Performance Stats - Updated: ${perfStats.particlesUpdated}, Skipped: ${perfStats.particlesSkipped}, Culled: ${perfStats.particlesCulled}`
                );
                // Reset counters
                perfStats.particlesUpdated = 0;
                perfStats.particlesSkipped = 0;
                perfStats.particlesCulled = 0;
            }, 5000);
        }

        debugLog(
            `Weather Effects Performance Patch v2 loaded! Settings: MaxParticles=${settings.maxParticlesPerLayer}, Throttle=${settings.particleUpdateThrottle}, Culling=${settings.enableVisibilityCulling}`
        );
    };

    // Start initialization
    initPatch();
})();
