/*:
 * @target MZ
 * @plugindesc Adds an ASCII symbol next to enemies' names and applies stat adjustments when they have State 99.
 * <AUTHOR>
 *
 * @help
 * This plugin automatically appends an ASCII symbol to any enemy affected by State 99,
 * and applies specific stat boosts and effects based on a randomly determined elite type.
 *
 * No plugin commands are required.
 */

(() => {
    const ELITE_STATE_ID = 99; // Change this if your elite state ID is different
    const RANDOM_VAR_ID = 138; // Variable used temporarily for rolling elite type

    // Define Elite Variants and Effects
    const ELITE_TYPES = [
        {
            symbol: '⚔',
            apply: enemy => {
                enemy._eliteType = 'Brutal';
                enemy.addBuff(2, 5); // Increase ATK
                enemy.addBuff(6, 5); // Increase CRIT Rate
                enemy.addDebuff(3, 5); // Reduce DEF
            },
        },

        {
            symbol: '🛡',
            apply: enemy => {
                enemy._eliteType = 'Resilient';
                enemy.addBuff(1, 5); // Increase HP
                enemy.addBuff(3, 5); // Increase DEF
                enemy.addDebuff(6, 5); // Reduce AGI
            },
        },

        {
            symbol: '🎭',
            apply: enemy => {
                enemy._eliteType = 'Tactician';
                enemy._tacticianBuff = true; // Enables buffing effect
            },
        },

        {
            symbol: '☠',
            apply: enemy => {
                enemy._eliteType = 'Poisonous';
                enemy._poisonousElite = true; // Enables poison-on-hit behavior
            },
        },

        {
            symbol: '🔮',
            apply: enemy => {
                enemy._eliteType = 'Hexing';
                enemy._hexingElite = true; // Enables hexing effect when hit
            },
        },

        {
            symbol: '🩸',
            apply: enemy => {
                enemy._eliteType = 'Vampiric';
                enemy._vampiricElite = true; // Enables lifesteal effect
            },
        },
    ];

    const _Game_Enemy_name = Game_Enemy.prototype.name;
    Game_Enemy.prototype.name = function () {
        let originalName = _Game_Enemy_name.call(this);
        if (this.isStateAffected(ELITE_STATE_ID)) {
            return `${this._eliteSymbol ?? ''} ${originalName}`.trim();
        }
        return originalName;
    };

    const _Game_Battler_addState = Game_Battler.prototype.addState;
    Game_Battler.prototype.addState = function (stateId) {
        _Game_Battler_addState.call(this, stateId);
        if (stateId === ELITE_STATE_ID && this.isEnemy()) {
            let randomElite = Math.floor(Math.random() * ELITE_TYPES.length);
            this._eliteSymbol = ELITE_TYPES[randomElite].symbol;
            ELITE_TYPES[randomElite].apply(this);
            if (this._sprite) {
                this._sprite.updateBitmap(); // Force sprite redraw
                this._sprite.refresh(); // Ensure UI updates immediately
            }
            this.refresh(); // Ensure UI updates immediately
        }
    };

    const _Game_Battler_initMembers = Game_Battler.prototype.initMembers;
    Game_Battler.prototype.initMembers = function () {
        _Game_Battler_initMembers.call(this);
        this._eliteSymbol = '';
    };

    const _Game_Battler_onTurnEnd = Game_Battler.prototype.onTurnEnd;
    Game_Battler.prototype.onTurnEnd = function () {
        _Game_Battler_onTurnEnd.call(this);
        if (this.isEnemy() && this._tacticianBuff) {
            let allies = $gameTroop.aliveMembers().filter(e => e !== this);
            if (allies.length > 0) {
                let randomAlly = allies[Math.floor(Math.random() * allies.length)];
                randomAlly.addState(Math.floor(Math.random() * 10) + 20); // Applies random buff state
            }
        }
    };

    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function (target) {
        _Game_Action_apply.call(this, target);
        if (target.isEnemy()) {
            if (target._poisonousElite && Math.random() < 0.5) {
                this.subject().addState(4); // Apply Poison state
                target.gainHp(target.param(5) * 2); // Heal amount based on MDF * 2
            }
            if (target._hexingElite && Math.random() < 0.3) {
                this.subject().addDebuff(Math.floor(Math.random() * 8), 3); // Random stat debuff
            }
        }
        if (this.subject().isEnemy() && this.subject()._vampiricElite) {
            let lifesteal = Math.floor(target.result().hpDamage * 0.2);
            this.subject().gainHp(lifesteal);
        }
    };
})();
