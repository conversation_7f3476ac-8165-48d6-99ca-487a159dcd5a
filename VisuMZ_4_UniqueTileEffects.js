//=============================================================================
// VisuStella MZ - Unique Tile Effects
// VisuMZ_4_UniqueTileEffects.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_UniqueTileEffects = true;

var VisuMZ = VisuMZ || {};
VisuMZ.UniqueTileEffects = VisuMZ.UniqueTileEffects || {};
VisuMZ.UniqueTileEffects.version = 1.01;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.01] [UniqueTileEffects]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Unique_Tile_Effects_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin allows you to add new properties to tiles, by marking them with
 * regions or assigning those properties to the terrain tags. These new unique
 * tile effects allow more player and event interactivity with the environment.
 * Such interactivity ranges from tiles that cause the character to slide from
 * one end to the other to other tiles that bounce the character forward.
 *
 * Features include all (but not limited to) the following:
 *
 * * Slippery tiles will cause affected characters on them to slide forward
 *   until they stop and hit a solid object or wall.
 * * Some tiles can force movement in a specific direction by simply standing
 *   on top of them.
 * * Pitfall tiles can have the player dropping into them and taking damage.
 *   Objects can also be thrown into them and having them erased.
 * * The player and characters can now fall into the water and drown. However,
 *   players can also learn how to swim and turn water tiles into traversible
 *   parts of the map.
 * * Quicksand tiles will cause affected characters to sink down further
 *   whenever they take a step up to a limited amount of times.
 * * Lava tiles will cause affected characters to burn constantly.
 * * Shock tiles instantly jolt characters that step upon them. If it's the
 *   player, send them back to their previous position, too.
 * * Bounce tiles will cause characters that step onto them to bounce forward a
 *   certain number of tiles based on the bounce strength.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_2_MovementEffects
 *
 * Footstep sounds can be played for certain unique tiles if the VisuStella MZ
 * Movement Effects plugin is installed. Modify the settings found in the
 * Plugin Parameters to allow them to do so.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_2_MovementEffects
 *
 * Smart Rush, Smart Jump, and Smart Blink cannot be used while on certain
 * unique tiles, like slippery tiles and force move tiles. This is because the
 * movement behavior will conflict with these tiles' properties. Some actions
 * like Smart Rush will be cut short when going onto these regions.
 *
 * The general footstep sound effects will also stop playing on these unique
 * tiles. Instead, different sound effects declared through the Unique Tile
 * Effects plugin will be played instead.
 *
 * Both Smart Rush and Smart Jump can move the player into a pitfall and other
 * similar tiles. Smart Blink, however, will not.
 *
 * ---
 *
 * VisuMZ_3_EventChainReact
 *
 * While on certain unique tiles, some objects cannot be pushed or pulled. For
 * example, the player cannot pull objects with the Plugin Commands while there
 * is a slippery tile or force move tile behind the player.
 *
 * Objects also cannot be pulled while on unique tiles like slippery tiles and
 * force move tiles.
 *
 * ---
 *
 * ============================================================================
 * Warning! RPG Maker MZ Version 1.5.0+ Water-Tile Bug!
 * ============================================================================
 *
 * It seems like there's a new bug that occurs if you create a tileset from
 * scratch in RPG Maker MZ version 1.5.0+ and version 1.6.0+! What this bug
 * does is it causes many tiles to become water tiles without intending to.
 * You can find this out by turning off all the plugins in your project,
 * putting a Ship or Boat on what are normally ground tiles, and then seeing
 * the Ship or Boat traverse through it.
 *
 * Naturally, this causes problems with the Unique Tile Effects plugin as the
 * water tiles allow for swimming and drowning.
 *
 * There are two ways to fix this. We cannot fix it through code in this plugin
 * as it's a problem that involves the tileset json data there are ways to work
 * around it so that you can get the proper water-flags to go where they need
 * to be at.
 *
 * ---
 *
 * 1. Copy a working un-bugged tileset onto the currently bugged one and
 *    reapply the tile features like passability, terrain tags, etc. This will
 *    make sure the water-passability tiles get copied over correctly.
 *
 * 2. If you're on RPG Maker MZ version 1.5.0 or above, select a working
 *    un-bugged tileset (usually a pre-existing tileset when a new project is
 *    made), click the "Copy Page" button, go to the bugged tileset and press
 *    "Paste Page". You'll have to reapply any different properties like
 *    passabilities and terrain tags, but the water tile flags should now be
 *    working properly.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Bounce Tile-Related Notetags ===
 *
 * Bounce tiles will cause affected characters that step over them to jump
 * forward a specified amount of spaces based on their regions/terrain tags.
 * This allows characters to scale past obstacles and/or other events!
 *
 * This jump effect has no rules other than it jumps forward that many spaces.
 * The no rules bit means the jump tile is capable of launching characters into
 * walls and/or on top of other events, so place your bounce tiles carefully!
 * We are not responsible for any weird landing locations!
 *
 * If you are making maps where events can launch themselves with the aid of
 * bounce tiles, we recommend putting either slippery tiles or force move tiles
 * to move those events out of the way in the scenario where the player is
 * following behind. This is to prevent the player from landing on top of the
 * event.
 *
 * ---
 *
 * <Bounce d Region: x>
 * <Bounce d Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become bounce tiles, causing
 *   the characters to jump forward a specific 'distance'.
 * - Replace 'd' with a number from 1 to 9 representing the distance to jump.
 *   Numbers above 10 will be ignored to prevent clipping bugs.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a bounce tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Bounce d Terrain Tag: x>
 * <Bounce d Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become bounce tiles,
 *   causing the characters to jump forward a specific 'distance'.
 * - Replace 'd' with a number from 1 to 9 representing the distance to jump.
 *   Numbers above 10 will be ignored to prevent clipping bugs.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a bounce tile marker.
 *
 * ---
 *
 * <Can Bounce>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by bounce tiles and jump.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Bounce>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by bounce tiles and not jump.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Bounce>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by bounce tiles and not jump.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by bounce tiles and not jump.
 *
 * ---
 *
 * <Avoid Bounce>
 * <Beware Bounce>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on bounce tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Bounce>
 * <Ignore Bounce>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto bounce tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Force Move Tile-Related Notetags ===
 *
 * Force move tiles cause affected characters on top of them to move in a
 * specified direction continuously. This means that even if a character is
 * stopped midway, transferred to the tile, or relocated to the force move
 * tile, once the path is clear, the character will be sent automatically
 * moving in the designated direction.
 *
 * ---
 *
 * <Force direction Region: x>
 * <Force direction Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become force move tiles,
 *   causing the characters on top of the go moving in the direction the tiles
 *   direct them to upon stepping on them.
 * - Replace 'direction' with a string that is either 'down', 'left', 'right',
 *   or 'up' (without the quotes) to designate the direction the tile will
 *   automatically move the character.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a force move tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Force direction Terrain Tag: x>
 * <Force direction Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these regions will become force move tiles,
 *   causing the characters on top of the go moving in the direction the tiles
 *   direct them to upon stepping on them.
 * - Replace 'direction' with a string that is either 'down', 'left', 'right',
 *   or 'up' (without the quotes) to designate the direction the tile will
 *   automatically move the character.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a force move tile marker.
 *
 * ---
 *
 * <Can Force Move>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by force move tiles.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Force Move>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by force move tiles.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Force Move>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by force move tiles.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by force move tiles.
 *
 * ---
 *
 * <Avoid Force Move>
 * <Beware Force Move>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on force move tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Force Move>
 * <Ignore Force Move>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto force move tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Lava Tile-Related Notetags ===
 *
 * Lava tiles cause affected characters on top of them to continuously burn
 * over time regardless of whether or not they're moving on the lava tile.
 * When this happens to a player, the party will take lava burn damage, and
 * events will be erased.
 *
 * When the character steps off a lava tile and is still surviving, the
 * otherwise continuously lava effect wears off. However, the damage remains
 * for the events and the number of times they can survive further lava burns
 * has been reduced.
 *
 * ---
 *
 * <Lava Region: x>
 * <Lava Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become lava tiles, causing
 *   the characters on top to continuously take damage regardless of whether or
 *   not they're moving on the lava tile.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a lava tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Lava Terrain Tag: x>
 * <Lava Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become lava tiles,
 *   causing the characters on top to continuously take damage regardless of
 *   whether or not they're moving on the lava tile.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a lava tile marker.
 *
 * ---
 *
 * <Can Lava Burn>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by lava tiles and burn.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Lava Burn>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by lava tiles and not burn.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Lava Burn>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by lava tiles and not burn.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by lava tiles and not burn.
 *
 * ---
 *
 * <Lava Burn Max: x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - If the event can be affected by lava tiles and burn, this determines the
 *   maximum amount of times they can burn before erasure.
 * - Replace 'x' with a number representing the maximum number of times the
 *   event can burn before erasure.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Avoid Lava>
 * <Beware Lava>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on lava tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Lava>
 * <Ignore Lava>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto lava tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Pitfall Tile-Related Notetags ===
 *
 * Pitfalls (or bottomless pits) are tiles that when an affected character
 * walks over it, the character will fall to its doom. Players will have their
 * whole party take fall damage and return to a previously safe location.
 * Events will be erased as if an event command took care of them. Characters
 * can jump over pitfalls and be unharmed. Jumping directly into a pitfall will
 * trigger a lethal reaction.
 *
 * ---
 *
 * <Pitfall Region: x>
 * <Pitfall Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become pitfall tiles,
 *   causing the characters that walk over them to fall in.
 *   - If the player character falls in, the party takes damage.
 *   - If an event falls in, it is erased.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a pitfall tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Pitfall Terrain Tag: x>
 * <Pitfall Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become pitfall tiles,
 *   causing the characters on top of the go sliding in the direction they're
 *   facing upon stepping on them.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a pitfall tile marker.
 *
 * ---
 *
 * <Pitfall Transfer: mapID>
 * <Pitfall Transfer: mapID, x, y>
 *
 * - Used for: Map Notetags
 * - If the player falls into a pitfall tile on this specific map, the player
 *   will be transferred to a different map (after taking damage).
 *   - If the <Pitfall Transfer: mapID> variant is used, transfer the player to
 *     the new map, but keep the current X, Y coordinates.
 *   - If the <Pitfall Transfer: mapID, x, y> variant is used, transfer the
 *     player to specific X, Y coordinates, too.
 *   - The player's direction will be retained in both cases.
 * - Replace 'mapID' with a number representing the ID of the map to transfer
 *   to. Use '0' to keep it to the current map.
 * - Replace 'x' and 'y' with numbers representing the respective target X and
 *   Y coordinates on the new map. If these aren't used, then the player will
 *   retain the current X and Y coordinates.
 *
 * ---
 *
 * <Can Pitfall>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by pitfall tiles and fall in.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Pitfall>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by pitfall tiles and can't fall in. They are
 *   also unable to walk into them without Through-state assistance.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Pitfall>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by pitfall tiles and can't fall in.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by pitfall tiles and can't fall in.
 *
 * ---
 *
 * <Avoid Pitfall>
 * <Beware Pitfall>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on pitfall tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Pitfall>
 * <Ignore Pitfall>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto pitfall tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Quicksand Tile-Related Notetags ===
 *
 * Quicksand tiles cause affected characters on top of them to slowly sink
 * deeper with each step they take and visibly go deeper until they fully sink.
 * When this happens to a player, the party will take sandsink damage and then
 * return to a previously safe location. Events will be erased.
 *
 * When a character steps on a non-quicksand tile, the sandsink counter resets
 * and the character fully resurfaces itself from the ground.
 *
 * ---
 *
 * <Quicksand Region: x>
 * <Quicksand Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become quicksand tiles,
 *   causing the characters to slowly sink deeper with each step.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a quicksand tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Quicksand Terrain Tag: x>
 * <Quicksand Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become quicksand tiles,
 *   causing the characters to slowly sink deeper with each step.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a quicksand tile marker.
 *
 * ---
 *
 * <Can Sandsink>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by quicksand tiles and sandsink.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Sandsink>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by quicksand tiles and not sandsink.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Sandsink>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by quicksand tiles and not sandsink.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by quicksand tiles and not sandsink.
 *
 * ---
 *
 * <Avoid Quicksand>
 * <Beware Quicksand>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on quicksand tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Quicksand>
 * <Ignore Quicksand>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto quicksand tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Shock Tile-Related Notetags ===
 *
 * When characters that are affected by shock tiles and step over them, the
 * tile will shock and zap them. The player will have the whole party take
 * shock damage and then return to a previously safe location. Events will be
 * erased immediately.
 *
 * ---
 *
 * <Shock Region: x>
 * <Shock Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become shock tiles, causing
 *   the characters to be shocked and zapped.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a shock tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Shock Terrain Tag: x>
 * <Shock Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become shock tiles,
 *   causing the characters to be shocked and zapped.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a shock tile marker.
 *
 * ---
 *
 * <Can Shock>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by shock tiles and being zapped.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Shock>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by shock tiles and not be zapped.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Sandsink>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by shock tiles and not be zapped.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by shock tiles and not be zapped.
 *
 * ---
 *
 * <Avoid Shock>
 * <Beware Shock>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on shock tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Shock>
 * <Ignore Shock>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto shock tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Slippery Tile-Related Notetags ===
 *
 * Slippery tiles cause affected characters on top of them to slide forward
 * until they hit a wall or another object. Once they stop, they can turn and
 * move a different direction. If this new direction is still on the slippery
 * tile, the character will continue sliding that direction, too.
 *
 * ---
 *
 * <Slippery Region: x>
 * <Slippery Regions: x, x, x>
 *
 * - Used for: Map Notetags
 * - Any map tiles marked with these regions will become slippery tiles,
 *   causing the characters on top of the go sliding in the direction they're
 *   facing upon stepping on them.
 * - Replace 'x' with a number from 1 to 255 representing the ID of the region
 *   you wish to use as a slippery tile marker.
 * - If you use this notetag, it will override the default region settings
 *   found in the Plugin Parameters.
 *
 * ---
 *
 * <Slippery Terrain Tag: x>
 * <Slippery Terrain Tags: x, x, x>
 *
 * - Used for: Tileset Notetags
 * - Any map tiles marked with these terrain tags will become slippery tiles,
 *   causing the characters on top of the go sliding in the direction they're
 *   facing upon stepping on them.
 * - Replace 'x' with a number from 1 to 7 representing the ID of the terrain
 *   tag you wish to use as a slippery tile marker.
 *
 * ---
 *
 * <Can Slip>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by slippery tiles and slide.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Slip>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by slippery tiles and not slide.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Slip>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become unaffected by slippery tiles and not slide.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become unaffected by slippery tiles and not slide.
 *
 * ---
 *
 * <Avoid Slippery>
 * <Beware Slippery>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on slippery tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Slippery>
 * <Ignore Slippery>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto slippery tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Water Tile-Related Notetags ===
 *
 * Water tiles are anything that a boat or ship can pass over and do not need
 * to be marked by regions or terrain tags.  Water tiles can cause affected
 * characters to drown in them when walking over them. Players will have their
 * whole party take drowning damage and then return to a previously safe
 * location. Events will be erased. If the player is given the ability to swim,
 * the player can traverse water tiles without any drowning problems.
 *
 * ---
 *
 * <Can Drown>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event capable of being affected by water tiles and drown.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Anti-Drown>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes the event unaffected by water tiles and not drown. They are also
 *   unable to naturally walk into them withou Through-state assistance.
 * - This will override the default settings found in the Plugin Parameters.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Can Swim>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - If the party has an actor equipped a weapon or armor with this notetag,
 *   the player will become able to swim in water and traverse them.
 * - If the party has a regular item with this notetag in their inventory,
 *   the player will become able to swim in water and traverse them.
 *
 * ---
 *
 * <Avoid Drown>
 * <Beware Drown>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Causes the event to avoid stepping on water tiles when self moving.
 * - The event can still step onto them when moved via event movement routes.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Careless Drown>
 * <Ignore Drown>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - The event can carelessly move onto water tiles when self moving.
 * - This will override the default settings found in the Plugin Parameters.
 * - The notetag and comment tag variants do the same thing. Which you choose
 *   to use is entirely up to personal preference.
 * - If this is placed in a notetag, the effect will be present across
 *   all event pages used.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Player Plugin Commands ===
 *
 * ---
 *
 * Player: Set Bounce Status
 * - Changes the player character's ability to navigate on Bounce Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Bounce Tiles.
 *
 * ---
 *
 * Player: Set Force Move Status
 * - Changes the player character's ability to navigate on Force Move Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Force Move Tiles.
 *
 * ---
 *
 * Player: Set Lava Status
 * - Changes the player character's ability to navigate on Lava Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Lava Tiles.
 *
 * ---
 *
 * Player: Set Quicksand Status
 * - Changes the player character's ability to navigate on Quicksand Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Quicksand Tiles.
 *
 * ---
 *
 * Player: Set Shock Status
 * - Changes the player character's ability to navigate on Shock Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Shock Tiles.
 *
 * ---
 *
 * Player: Set Slippery Status
 * - Changes the player character's ability to navigate on Slippery Tiles.
 *
 *   Immune?:
 *   - Changes the player character's ability to navigate on Slippery Tiles.
 *
 * ---
 *
 * Player: Set Swimming Status
 * - Changes the player character's ability to navigate on water tiles.
 *
 *   Allow?:
 *   - Changes the player character's ability to navigate on water tiles.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Bounce Tile Settings
 * ============================================================================
 *
 * Settings related to Bounce Tiles.
 *
 * Bounce tiles will cause affected characters that step over them to jump
 * forward a specified amount of spaces based on their regions/terrain tags.
 * This allows characters to scale past obstacles and/or other events!
 *
 * This jump effect has no rules other than it jumps forward that many spaces.
 * The no rules bit means the jump tile is capable of launching characters into
 * walls and/or on top of other events, so place your bounce tiles carefully!
 * We are not responsible for any weird landing locations!
 *
 * If you are making maps where events can launch themselves with the aid of
 * bounce tiles, we recommend putting either slippery tiles or force move tiles
 * to move those events out of the way in the scenario where the player is
 * following behind. This is to prevent the player from landing on top of the
 * event.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *
 *     Bounce 1-9 Region(s):
 *     - Which region(s) will be used to mark this tile type?
 *     - Ignore if map has <Bounce 1-9 Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 * ---
 *
 * Sound Effects
 *
 *   Effect:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Force Move Tile Settings
 * ============================================================================
 *
 * Settings related to Force Move Tiles.
 *
 * Force move tiles cause affected characters on top of them to move in a
 * specified direction continuously. This means that even if a character is
 * stopped midway, transferred to the tile, or relocated to the force move
 * tile, once the path is clear, the character will be sent automatically
 * moving in the designated direction.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *
 *     Down Region(s):
 *     Left Region(s):
 *     Right Region(s):
 *     Up Region(s):
 *     - Which region(s) will be used to mark this tile type?
 *     - Ignore if map has <Force direction Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Tile Move Speed:
 *   - Forces this move speed when on this tile type.
 *   - Use 0 to use the character's current move speed.
 *
 *   Tile Sprite Pattern:
 *   - Forces this sprite pattern when on this tile type.
 *   - Use -1 to allow characters to freely animate.
 *
 * ---
 *
 * Sound Effects
 *
 *   Footsteps:
 *
 *     Enabled?:
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *     - Requires VisuMZ_2_MovementEffects.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Lava Tile Settings
 * ============================================================================
 *
 * Settings related to Lava Tiles.
 *
 * Lava tiles cause affected characters on top of them to continuously burn
 * over time regardless of whether or not they're moving on the lava tile.
 * When this happens to a player, the party will take lava burn damage, and
 * events will be erased.
 *
 * When the character steps off a lava tile and is still surviving, the
 * otherwise continuously lava effect wears off. However, the damage remains
 * for the events and the number of times they can survive further lava burns
 * has been reduced.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *   - Which region(s) will be used to mark this tile type?
 *   - Ignore if map has <Lava Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *     Event Lava Burn Max:
 *     - Default number of times an event can suffer lava burns before
 *       being erased.
 *
 *   Lava Burn Frame Delay:
 *   - Frames to delay lava burn iterations from one another.
 *   - Lower: less delay.
 *   - Higher: larger delay.
 *
 * ---
 *
 * Damage Calculation
 *
 *   Allow Death?:
 *   - Can actors die from the damage dealt by this tile?
 *
 *   Rate Damage:
 *   - What percentile of the actor's MaxHP is dealt as damage?
 *
 *   Flat Damage:
 *   - What flat amount of damage is dealt to the actor?
 *
 * ---
 *
 * Animation
 *
 *   Animation ID:
 *   - Play this animation when the effect activates.
 *   - Requires VisuMZ_0_CoreEngine.
 *
 *   Mirror Animation:
 *   - Mirror the effect animation?
 *   - Requires VisuMZ_0_CoreEngine.
 *
 *   Mute Animation:
 *   - Mute the effect animation?
 *   - Requires VisuMZ_0_CoreEngine.
 *
 * ---
 *
 * Sound Effects
 *
 *   Footsteps:
 *
 *     Enabled?:
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *     - Requires VisuMZ_2_MovementEffects.
 *
 *   Damage:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Pitfall Tile Settings
 * ============================================================================
 *
 * Settings related to Pitfall Tiles.
 *
 * Pitfalls (or bottomless pits) are tiles that when an affected character
 * walks over it, the character will fall to its doom. Players will have their
 * whole party take fall damage and return to a previously safe location.
 * Events will be erased as if an event command took care of them. Characters
 * can jump over pitfalls and be unharmed. Jumping directly into a pitfall will
 * trigger a lethal reaction.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *   - Which region(s) will be used to mark this tile type?
 *   - Ignore if map has <Pitfall Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Fall Duration:
 *   - How many frames will the falling animation take?
 *
 * ---
 *
 * Damage Calculation
 *
 *   Allow Death?:
 *   - Can actors die from the damage dealt by this tile?
 *
 *   Rate Damage:
 *   - What percentile of the actor's MaxHP is dealt as damage?
 *
 *   Flat Damage:
 *   - What flat amount of damage is dealt to the actor?
 *
 * ---
 *
 * Sound Effects
 *
 *   Fall:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 *   Damage:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Quicksand Tile Settings
 * ============================================================================
 *
 * Settings related to Quicksand Tiles.
 *
 * Quicksand tiles cause affected characters on top of them to slowly sink
 * deeper with each step they take and visibly go deeper until they fully sink.
 * When this happens to a player, the party will take sandsink damage and then
 * return to a previously safe location. Events will be erased.
 *
 * When a character steps on a non-quicksand tile, the sandsink counter resets
 * and the character fully resurfaces itself from the ground.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *   - Which region(s) will be used to mark this tile type?
 *   - Ignore if map has <Quicksand Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Steps to Sandsink:
 *   - How many steps will it take on quicksand before sandsinking a character?
 *
 *   Tile Move Speed:
 *   - Forces this move speed when on this tile type.
 *   - Use 0 to use the character's current move speed.
 *
 * ---
 *
 * Damage Calculation
 *
 *   Allow Death?:
 *   - Can actors die from the damage dealt by this tile?
 *
 *   Rate Damage:
 *   - What percentile of the actor's MaxHP is dealt as damage?
 *
 *   Flat Damage:
 *   - What flat amount of damage is dealt to the actor?
 *
 * ---
 *
 * Sound Effects
 *
 *   Footsteps:
 *
 *     Enabled?:
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *     - Requires VisuMZ_2_MovementEffects.
 *
 *   Damage:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Shock Tile Settings
 * ============================================================================
 *
 * Settings related to Shock Tiles.
 *
 * When characters that are affected by shock tiles and step over them, the
 * tile will shock and zap them. The player will have the whole party take
 * shock damage and then return to a previously safe location. Events will be
 * erased immediately.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *   - Which region(s) will be used to mark this tile type?
 *   - Ignore if map has <Shock Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Shock Input Delay:
 *   - How many frames to wait before giving player move input?
 *   - Lower: less delay. Higher: larger delay.
 *
 * ---
 *
 * Damage Calculation
 *
 *   Allow Death?:
 *   - Can actors die from the damage dealt by this tile?
 *
 *   Rate Damage:
 *   - What percentile of the actor's MaxHP is dealt as damage?
 *
 *   Flat Damage:
 *   - What flat amount of damage is dealt to the actor?
 *
 * ---
 *
 * Animation
 *
 *   Animation ID:
 *   - Play this animation when the effect activates.
 *   - Requires VisuMZ_0_CoreEngine.
 *
 *   Mirror Animation:
 *   - Mirror the effect animation?
 *   - Requires VisuMZ_0_CoreEngine.
 *
 *   Mute Animation:
 *   - Mute the effect animation?
 *   - Requires VisuMZ_0_CoreEngine.
 *
 * ---
 *
 * Sound Effects
 *
 *   Damage:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Slippery Tile Settings
 * ============================================================================
 *
 * Settings related to Slippery Tiles.
 *
 * Slippery tiles cause affected characters on top of them to slide forward
 * until they hit a wall or another object. Once they stop, they can turn and
 * move a different direction. If this new direction is still on the slippery
 * tile, the character will continue sliding that direction, too.
 *
 * ---
 *
 * General
 *
 *   Default Region(s):
 *   - Which region(s) will be used to mark this tile type?
 *   - Ignore if map has <Slippery Region: x> notetag.
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Tile Move Speed:
 *   - Forces this move speed when on this tile type.
 *   - Use 0 to use the character's current move speed.
 *
 *   Tile Sprite Pattern:
 *   - Forces this sprite pattern when on this tile type.
 *   - Use -1 to allow characters to freely animate.
 *
 * ---
 *
 * Sound Effects
 *
 *   Footsteps:
 *
 *     Enabled?:
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *     - Requires VisuMZ_2_MovementEffects.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Water Tile Settings
 * ============================================================================
 *
 * Settings related to Water Tiles.
 *
 * Water tiles are anything that a boat or ship can pass over and do not need
 * to be marked by regions or terrain tags.  Water tiles can cause affected
 * characters to drown in them when walking over them. Players will have their
 * whole party take drowning damage and then return to a previously safe
 * location. Events will be erased. If the player is given the ability to swim,
 * the player can traverse water tiles without any drowning problems.
 *
 * ---
 *
 * General
 *
 *   Event Defaults:
 *
 *     Default Affected:
 *     - Are events affected by this tile by default?
 *
 *     Default Avoid:
 *     - Will events avoid stepping on this tile by default?
 *
 *   Drowning Duration:
 *   - How many frames will the drowning animation take?
 *
 *   Player Can Drown?:
 *   - Allow the player to be able to step into water tiles without the ability
 *     to swim and causing player to drown?
 *
 *   Swimming Depth:
 *   - How many pixels will the player character be submerged when in a state
 *     of swimming?
 *
 *   Tile Move Speed:
 *   - Forces this move speed when on this tile type.
 *   - Use 0 to use the character's current move speed.
 *
 * ---
 *
 * Damage Calculation
 *
 *   Allow Death?:
 *   - Can actors die from the damage dealt by this tile?
 *
 *   Rate Damage:
 *   - What percentile of the actor's MaxHP is dealt as damage?
 *
 *   Flat Damage:
 *   - What flat amount of damage is dealt to the actor?
 *
 * ---
 *
 * Sound Effects
 *
 *   Footsteps:
 *
 *     Enabled?:
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *     - Requires VisuMZ_2_MovementEffects.
 *
 *   Drown:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 *   Damage:
 *
 *     Filename:
 *     Volume:
 *     Pitch:
 *     Pan:
 *     - Properties of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.01: December 15, 2022
 * * Bug Fixes!
 * ** Fixed a bug where you cannot respawn on X = 0 or Y = 0.
 * * Documentation Update!
 * ** Added new section: "Warning! RPG Maker MZ Version 1.5.0+ Water-Tile Bug!"
 * *** It seems like there's a new bug that occurs if you create a tileset from
 *     scratch in RPG Maker MZ version 1.5.0+ and version 1.6.0+! What this bug
 *     does is it causes many tiles to become water tiles without intending to.
 *     You can find this out by turning off all the plugins in your project,
 *     putting a Ship or Boat on what are normally ground tiles, and then
 *     seeing the Ship or Boat traverse through it.
 * *** Naturally, this causes problems with the Unique Tile Effects plugin as
 *     the water tiles allow for swimming and drowning.
 * *** There are two ways to fix this. We cannot fix it through code in this
 *     plugin as it's a problem that involves the tileset json data there are
 *     ways to work around it so that you can get the proper water-flags to go
 *     where they need to be at.
 * **** 1. Copy a working un-bugged tileset onto the currently bugged one and
 *      reapply the tile features like passability, terrain tags, etc. This
 *      will make sure the water-passability tiles get copied over correctly.
 * **** 2. If you're on RPG Maker MZ version 1.5.0 or above, select a working
 *      un-bugged tileset (usually a pre-existing tileset when a new project is
 *      made), click the "Copy Page" button, go to the bugged tileset and press
 *      "Paste Page". You'll have to reapply any different properties like
 *      passabilities and terrain tags, but the water tile flags should now be
 *      working properly.
 *
 * Version 1.00 Official Release Date: January 6, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiBounce
 * @text Player: Set Bounce Status
 * @desc Changes the player character's ability to navigate on Bounce Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Bounce
 * @off Force Bounce
 * @desc Changes the player character's ability to navigate on Bounce Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiForceMove
 * @text Player: Set Force Move Status
 * @desc Changes the player character's ability to navigate on Force Move Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Force Move
 * @off Force Move
 * @desc Changes the player character's ability to navigate on Force Move Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiLava
 * @text Player: Set Lava Status
 * @desc Changes the player character's ability to walk on Lava Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Burn
 * @off Can Burn
 * @desc Changes the player character's ability to walk on Lava Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiQuicksand
 * @text Player: Set Quicksand Status
 * @desc Changes the player character's ability to walk on Quicksand Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Sink
 * @off Can Sink
 * @desc Changes the player character's ability to walk on Quicksand Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiShock
 * @text Player: Set Shock Status
 * @desc Changes the player character's ability to walk on Shock Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Shock
 * @off Can Shock
 * @desc Changes the player character's ability to walk on Shock Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAntiSlippery
 * @text Player: Set Slippery Status
 * @desc Changes the player character's ability to walk on Slippery Tiles.
 *
 * @arg Setting:eval
 * @text Immune?
 * @type boolean
 * @on No Slip
 * @off Can Slip
 * @desc Changes the player character's ability to walk on Slippery Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PlayerSetAllowSwimming
 * @text Player: Set Swimming Status
 * @desc Changes the player character's ability to swim on water Tiles.
 *
 * @arg Setting:eval
 * @text Allow?
 * @type boolean
 * @on Can Swim
 * @off Cannot Swim
 * @desc Changes the player character's ability to swim on water Tiles.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param UniqueTileEffects
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param NotetagPartyWide:eval
 * @text Reserve Party Notetags?
 * @type boolean
 * @on All Members Allowed
 * @off Battle Members Only
 * @desc Should immunity notetags check all party members including
 * the reserve members or only battle members?
 * @default true
 *
 * @param Bounce:struct
 * @text Bounce Tile Settings
 * @type struct<Bounce>
 * @desc Settings related to Bounce Tiles.
 * @default {"General":"","DefaultRegions":"","Bounce1Regions:arraynum":"[]","Bounce2Regions:arraynum":"[]","Bounce3Regions:arraynum":"[]","Bounce4Regions:arraynum":"[]","Bounce5Regions:arraynum":"[]","Bounce6Regions:arraynum":"[]","Bounce7Regions:arraynum":"[]","Bounce8Regions:arraynum":"[]","Bounce9Regions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"false","Sound":"","Effect":"","effectName:str":"Jump2","effectVolume:num":"50","effectPitch:num":"120","effectPan:num":"0"}
 *
 * @param ForceMove:struct
 * @text Force Move Tile Settings
 * @type struct<ForceMove>
 * @desc Settings related to Force Move Tiles.
 * @default {"General":"","DefaultRegions":"","DownRegions:arraynum":"[]","LeftRegions:arraynum":"[]","RightRegions:arraynum":"[]","UpRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"false","MoveSpeed:num":"6","Pattern:num":"2","Sound":"","Footsteps":"","footstepsEnabled:eval":"true","footstepsName:str":"Skill1","footstepsVolume:num":"10","footstepsPitch:num":"130","footstepsPan:num":"0"}
 *
 * @param Lava:struct
 * @text Lava Tile Settings
 * @type struct<Lava>
 * @desc Settings related to Lava Tiles.
 * @default {"General":"","DefaultRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"true","LavaBurnEventMax:num":"6","LavaBurnTimer:num":"20","DmgCalc":"","DmgDeathAllow:eval":"true","DmgRate:num":"0.05","DmgFlat:num":"50","Animation":"","AnimationID:num":"0","AnimationMirror:eval":"false","AnimationMute:eval":"false","Sound":"","Footsteps":"","footstepsEnabled:eval":"true","footstepsName:str":"Fire2","footstepsVolume:num":"10","footstepsPitch:num":"120","footstepsPan:num":"0","Damage":"","damageName:str":"Fire8","damageVolume:num":"30","damagePitch:num":"100","damagePan:num":"0"}
 *
 * @param Pitfall:struct
 * @text Pitfall Tile Settings
 * @type struct<Pitfall>
 * @desc Settings related to Pitfall Tiles.
 * @default {"General":"","DefaultRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"true","EffectDuration:num":"20","DmgCalc":"","DmgDeathAllow:eval":"true","DmgRate:num":"0.20","DmgFlat:num":"20","Sound":"","Effect":"","effectName:str":"Fall","effectVolume:num":"50","effectPitch:num":"80","effectPan:num":"0","Damage":"","damageName:str":"Earth4","damageVolume:num":"30","damagePitch:num":"80","damagePan:num":"0"}
 *
 * @param Quicksand:struct
 * @text Quicksand Tile Settings
 * @type struct<Quicksand>
 * @desc Settings related to Quicksand Tiles.
 * @default {"General":"","DefaultRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"true","StepsSandSink:num":"10","MoveSpeed:num":"3","DmgCalc":"","DmgDeathAllow:eval":"true","DmgRate:num":"0.30","DmgFlat:num":"40","Sound":"","Footsteps":"","footstepsEnabled:eval":"true","footstepsName:str":"Blow2","footstepsVolume:num":"10","footstepsPitch:num":"60","footstepsPan:num":"0","Damage":"","damageName:str":"Earth3","damageVolume:num":"30","damagePitch:num":"120","damagePan:num":"0"}
 *
 * @param Shock:struct
 * @text Shock Tile Settings
 * @type struct<Shock>
 * @desc Settings related to Shock Tiles.
 * @default {"General":"","DefaultRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"true","ShockTimer:num":"60","DmgCalc":"","DmgDeathAllow:eval":"true","DmgRate:num":"0.25","DmgFlat:num":"60","Animation":"","AnimationID:num":"0","AnimationMirror:eval":"false","AnimationMute:eval":"false","Sound":"","Damage":"","damageName:str":"Thunder3","damageVolume:num":"30","damagePitch:num":"120","damagePan:num":"0"}
 *
 * @param Slippery:struct
 * @text Slippery Tile Settings
 * @type struct<Slippery>
 * @desc Settings related to Slippery Tiles.
 * @default {"General":"","DefaultRegions:arraynum":"[]","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"false","MoveSpeed:num":"4","Pattern:num":"2","Sound":"","Footsteps":"","footstepsEnabled:eval":"true","footstepsName:str":"Sand","footstepsVolume:num":"10","footstepsPitch:num":"150","footstepsPan:num":"0"}
 *
 * @param Swimming:struct
 * @text Water Tile Settings
 * @type struct<Swimming>
 * @desc Settings related to Water Tiles.
 * @default {"General":"","EventDefaults":"","DefaultAffected:eval":"false","DefaultAvoid:eval":"true","EffectDuration:num":"30","playerCanDrown:eval":"true","SwimmingDepth:num":"16","MoveSpeed:num":"4","DmgCalc":"","DmgDeathAllow:eval":"true","DmgRate:num":"0.10","DmgFlat:num":"15","Sound":"","Footsteps":"","footstepsEnabled:eval":"true","footstepsName:str":"Water1","footstepsVolume:num":"10","footstepsPitch:num":"80","footstepsPan:num":"0","Effect":"","effectName:str":"Dive","effectVolume:num":"50","effectPitch:num":"120","effectPan:num":"0","Damage":"","damageName:str":"Water3","damageVolume:num":"30","damagePitch:num":"80","damagePan:num":"0"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Slippery Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Slippery:
 *
 * @param General
 *
 * @param DefaultRegions:arraynum
 * @text Default Region(s)
 * @parent General
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Slippery Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default false
 *
 * @param MoveSpeed:num
 * @text Tile Move Speed
 * @parent General
 * @type number
 * @desc Forces this move speed when on this tile type.
 * Use 0 to use the character's current move speed.
 * @default 4
 *
 * @param Pattern:num
 * @text Tile Sprite Pattern
 * @parent General
 * @desc Forces this sprite pattern when on this tile type.
 * Use -1 to allow characters to freely animate.
 * @default 2
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Footsteps
 * @parent Sound
 *
 * @param footstepsEnabled:eval
 * @text Enabled?
 * @parent Footsteps
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable footstep sounds for this tile?
 * Requires VisuMZ_2_MovementEffects!
 * @default true
 *
 * @param footstepsName:str
 * @text Filename
 * @parent Footsteps
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default Sand
 *
 * @param footstepsVolume:num
 * @text Volume
 * @parent Footsteps
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 10
 *
 * @param footstepsPitch:num
 * @text Pitch
 * @parent Footsteps
 * @type number
 * @desc Pitch of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 150
 *
 * @param footstepsPan:num
 * @text Pan
 * @parent Footsteps
 * @desc Pan of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Force Move Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ForceMove:
 *
 * @param General
 *
 * @param DefaultRegions
 * @text Default Region(s)
 * @parent General
 *
 * @param DownRegions:arraynum
 * @text Down Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Force Down Region: x> notetag.
 * @default []
 *
 * @param LeftRegions:arraynum
 * @text Left Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Force Left Region: x> notetag.
 * @default []
 *
 * @param RightRegions:arraynum
 * @text Right Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Force Right Region: x> notetag.
 * @default []
 *
 * @param UpRegions:arraynum
 * @text Up Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Force Up Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default false
 *
 * @param MoveSpeed:num
 * @text Tile Move Speed
 * @parent General
 * @type number
 * @desc Forces this move speed when on this tile type.
 * Use 0 to use the character's current move speed.
 * @default 6
 *
 * @param Pattern:num
 * @text Tile Sprite Pattern
 * @parent General
 * @desc Forces this sprite pattern when on this tile type.
 * Use -1 to allow characters to freely animate.
 * @default 2
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Footsteps
 * @parent Sound
 *
 * @param footstepsEnabled:eval
 * @text Enabled?
 * @parent Footsteps
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable footstep sounds for this tile?
 * Requires VisuMZ_2_MovementEffects!
 * @default true
 *
 * @param footstepsName:str
 * @text Filename
 * @parent Footsteps
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default Skill1
 *
 * @param footstepsVolume:num
 * @text Volume
 * @parent Footsteps
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 10
 *
 * @param footstepsPitch:num
 * @text Pitch
 * @parent Footsteps
 * @type number
 * @desc Pitch of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 130
 *
 * @param footstepsPan:num
 * @text Pan
 * @parent Footsteps
 * @desc Pan of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Pitfall Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Pitfall:
 *
 * @param General
 *
 * @param DefaultRegions:arraynum
 * @text Default Region(s)
 * @parent General
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Pitfall Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default true
 *
 * @param EffectDuration:num
 * @text Fall Duration
 * @parent General
 * @type number
 * @min 1
 * @desc How many frames will the falling animation take?
 * @default 20
 *
 * @param DmgCalc
 * @text Damage Calculation
 *
 * @param DmgDeathAllow:eval
 * @text Allow Death?
 * @parent DmgCalc
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Can actors die from the damage dealt by this tile?
 * @default true
 *
 * @param DmgRate:num
 * @text Rate Damage
 * @parent DmgCalc
 * @desc What percentile of the actor's MaxHP is dealt as damage?
 * @default 0.20
 *
 * @param DmgFlat:num
 * @text Flat Damage
 * @parent DmgCalc
 * @type number
 * @desc What flat amount of damage is dealt to the actor?
 * @default 20
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Effect
 * @text Fall
 * @parent Sound
 *
 * @param effectName:str
 * @text Filename
 * @parent Effect
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Fall
 *
 * @param effectVolume:num
 * @text Volume
 * @parent Effect
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 50
 *
 * @param effectPitch:num
 * @text Pitch
 * @parent Effect
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 80
 *
 * @param effectPan:num
 * @text Pan
 * @parent Effect
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Damage
 * @parent Sound
 *
 * @param damageName:str
 * @text Filename
 * @parent Damage
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Earth4
 *
 * @param damageVolume:num
 * @text Volume
 * @parent Damage
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 30
 *
 * @param damagePitch:num
 * @text Pitch
 * @parent Damage
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 80
 *
 * @param damagePan:num
 * @text Pan
 * @parent Damage
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Swimming Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Swimming:
 *
 * @param General
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default true
 *
 * @param EffectDuration:num
 * @text Drowning Duration
 * @parent General
 * @type number
 * @min 1
 * @desc How many frames will the drowning animation take?
 * @default 30
 *
 * @param playerCanDrown:eval
 * @text Player Can Drown?
 * @parent General
 * @type boolean
 * @on Drown
 * @off Avoid
 * @desc Allow the player to be able to step into water tiles
 * without the ability to swim and causing player to drown?
 * @default true
 *
 * @param SwimmingDepth:num
 * @text Swimming Depth
 * @parent General
 * @type number
 * @desc How many pixels will the player character be submerged
 * when in a state of swimming?
 * @default 16
 *
 * @param MoveSpeed:num
 * @text Tile Move Speed
 * @parent General
 * @type number
 * @desc Forces this move speed when on this tile type.
 * Use 0 to use the character's current move speed.
 * @default 4
 *
 * @param DmgCalc
 * @text Damage Calculation
 *
 * @param DmgDeathAllow:eval
 * @text Allow Death?
 * @parent DmgCalc
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Can actors die from the damage dealt by this tile?
 * @default true
 *
 * @param DmgRate:num
 * @text Rate Damage
 * @parent DmgCalc
 * @desc What percentile of the actor's MaxHP is dealt as damage?
 * @default 0.10
 *
 * @param DmgFlat:num
 * @text Flat Damage
 * @parent DmgCalc
 * @type number
 * @desc What flat amount of damage is dealt to the actor?
 * @default 15
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Footsteps
 * @parent Sound
 *
 * @param footstepsEnabled:eval
 * @text Enabled?
 * @parent Footsteps
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable footstep sounds for this tile?
 * Requires VisuMZ_2_MovementEffects!
 * @default true
 *
 * @param footstepsName:str
 * @text Filename
 * @parent Footsteps
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default Water1
 *
 * @param footstepsVolume:num
 * @text Volume
 * @parent Footsteps
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 10
 *
 * @param footstepsPitch:num
 * @text Pitch
 * @parent Footsteps
 * @type number
 * @desc Pitch of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 80
 *
 * @param footstepsPan:num
 * @text Pan
 * @parent Footsteps
 * @desc Pan of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 0
 *
 * @param Effect
 * @text Drown
 * @parent Sound
 *
 * @param effectName:str
 * @text Filename
 * @parent Effect
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Dive
 *
 * @param effectVolume:num
 * @text Volume
 * @parent Effect
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 50
 *
 * @param effectPitch:num
 * @text Pitch
 * @parent Effect
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param effectPan:num
 * @text Pan
 * @parent Effect
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Damage
 * @parent Sound
 *
 * @param damageName:str
 * @text Filename
 * @parent Damage
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Water3
 *
 * @param damageVolume:num
 * @text Volume
 * @parent Damage
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 30
 *
 * @param damagePitch:num
 * @text Pitch
 * @parent Damage
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 80
 *
 * @param damagePan:num
 * @text Pan
 * @parent Damage
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Quicksand Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Quicksand:
 *
 * @param General
 *
 * @param DefaultRegions:arraynum
 * @text Default Region(s)
 * @parent General
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Quicksand Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default true
 *
 * @param StepsSandSink:num
 * @text Steps to Sandsink
 * @parent General
 * @type number
 * @min 1
 * @desc How many steps will it take on quicksand before
 * sandsinking a character?
 * @default 10
 *
 * @param MoveSpeed:num
 * @text Tile Move Speed
 * @parent General
 * @type number
 * @desc Forces this move speed when on this tile type.
 * Use 0 to use the character's current move speed.
 * @default 3
 *
 * @param DmgCalc
 * @text Damage Calculation
 *
 * @param DmgDeathAllow:eval
 * @text Allow Death?
 * @parent DmgCalc
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Can actors die from the damage dealt by this tile?
 * @default true
 *
 * @param DmgRate:num
 * @text Rate Damage
 * @parent DmgCalc
 * @desc What percentile of the actor's MaxHP is dealt as damage?
 * @default 0.30
 *
 * @param DmgFlat:num
 * @text Flat Damage
 * @parent DmgCalc
 * @type number
 * @desc What flat amount of damage is dealt to the actor?
 * @default 40
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Footsteps
 * @parent Sound
 *
 * @param footstepsEnabled:eval
 * @text Enabled?
 * @parent Footsteps
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable footstep sounds for this tile?
 * Requires VisuMZ_2_MovementEffects!
 * @default true
 *
 * @param footstepsName:str
 * @text Filename
 * @parent Footsteps
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default Blow2
 *
 * @param footstepsVolume:num
 * @text Volume
 * @parent Footsteps
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 10
 *
 * @param footstepsPitch:num
 * @text Pitch
 * @parent Footsteps
 * @type number
 * @desc Pitch of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 60
 *
 * @param footstepsPan:num
 * @text Pan
 * @parent Footsteps
 * @desc Pan of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 0
 *
 * @param Damage
 * @parent Sound
 *
 * @param damageName:str
 * @text Filename
 * @parent Damage
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Earth3
 *
 * @param damageVolume:num
 * @text Volume
 * @parent Damage
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 30
 *
 * @param damagePitch:num
 * @text Pitch
 * @parent Damage
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param damagePan:num
 * @text Pan
 * @parent Damage
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Lava Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Lava:
 *
 * @param General
 *
 * @param DefaultRegions:arraynum
 * @text Default Region(s)
 * @parent General
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Lava Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default true
 *
 * @param LavaBurnEventMax:num
 * @text Event Lava Burn Max
 * @parent EventDefaults
 * @type number
 * @min 1
 * @desc Default number of times an event can suffer lava burns
 * before being erased.
 * @default 6
 *
 * @param LavaBurnTimer:num
 * @text Lava Burn Frame Delay
 * @parent General
 * @type number
 * @min 1
 * @desc Frames to delay lava burn iterations from one another.
 * Lower: less delay. Higher: larger delay.
 * @default 20
 *
 * @param DmgCalc
 * @text Damage Calculation
 *
 * @param DmgDeathAllow:eval
 * @text Allow Death?
 * @parent DmgCalc
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Can actors die from the damage dealt by this tile?
 * @default true
 *
 * @param DmgRate:num
 * @text Rate Damage
 * @parent DmgCalc
 * @desc What percentile of the actor's MaxHP is dealt as damage?
 * @default 0.05
 *
 * @param DmgFlat:num
 * @text Flat Damage
 * @parent DmgCalc
 * @type number
 * @desc What flat amount of damage is dealt to the actor?
 * @default 50
 *
 * @param Animation
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Animation
 * @type animation
 * @desc Play this animation when the effect activates.
 * Requires VisuMZ_0_CoreEngine.
 * @default 0
 *
 * @param AnimationMirror:eval
 * @text Mirror Animation
 * @parent Animation
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the effect animation?
 * Requires VisuMZ_0_CoreEngine.
 * @default false
 *
 * @param AnimationMute:eval
 * @text Mute Animation
 * @parent Animation
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the effect animation?
 * Requires VisuMZ_0_CoreEngine.
 * @default false
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Footsteps
 * @text Footsteps
 * @parent Sound
 *
 * @param footstepsEnabled:eval
 * @text Enabled?
 * @parent Footsteps
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable footstep sounds for this tile?
 * Requires VisuMZ_2_MovementEffects!
 * @default true
 *
 * @param footstepsName:str
 * @text Filename
 * @parent Footsteps
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default Fire2
 *
 * @param footstepsVolume:num
 * @text Volume
 * @parent Footsteps
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 10
 *
 * @param footstepsPitch:num
 * @text Pitch
 * @parent Footsteps
 * @type number
 * @desc Pitch of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 120
 *
 * @param footstepsPan:num
 * @text Pan
 * @parent Footsteps
 * @desc Pan of the sound effect played.
 * Requires VisuMZ_2_MovementEffects!
 * @default 0
 *
 * @param Damage
 * @parent Sound
 *
 * @param damageName:str
 * @text Filename
 * @parent Damage
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Fire8
 *
 * @param damageVolume:num
 * @text Volume
 * @parent Damage
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 30
 *
 * @param damagePitch:num
 * @text Pitch
 * @parent Damage
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param damagePan:num
 * @text Pan
 * @parent Damage
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Shock Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Shock:
 *
 * @param General
 *
 * @param DefaultRegions:arraynum
 * @text Default Region(s)
 * @parent General
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Shock Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default true
 *
 * @param ShockTimer:num
 * @text Shock Input Delay
 * @parent General
 * @type number
 * @min 1
 * @desc How many frames to wait before giving player move input?
 * Lower: less delay. Higher: larger delay.
 * @default 60
 *
 * @param DmgCalc
 * @text Damage Calculation
 *
 * @param DmgDeathAllow:eval
 * @text Allow Death?
 * @parent DmgCalc
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Can actors die from the damage dealt by this tile?
 * @default true
 *
 * @param DmgRate:num
 * @text Rate Damage
 * @parent DmgCalc
 * @desc What percentile of the actor's MaxHP is dealt as damage?
 * @default 0.25
 *
 * @param DmgFlat:num
 * @text Flat Damage
 * @parent DmgCalc
 * @type number
 * @desc What flat amount of damage is dealt to the actor?
 * @default 60
 *
 * @param Animation
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Animation
 * @type animation
 * @desc Play this animation when the effect activates.
 * Requires VisuMZ_0_CoreEngine.
 * @default 0
 *
 * @param AnimationMirror:eval
 * @text Mirror Animation
 * @parent Animation
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the effect animation?
 * Requires VisuMZ_0_CoreEngine.
 * @default false
 *
 * @param AnimationMute:eval
 * @text Mute Animation
 * @parent Animation
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the effect animation?
 * Requires VisuMZ_0_CoreEngine.
 * @default false
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Damage
 * @parent Sound
 *
 * @param damageName:str
 * @text Filename
 * @parent Damage
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Thunder3
 *
 * @param damageVolume:num
 * @text Volume
 * @parent Damage
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 30
 *
 * @param damagePitch:num
 * @text Pitch
 * @parent Damage
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param damagePan:num
 * @text Pan
 * @parent Damage
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Bounce Tile Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Bounce:
 *
 * @param General
 *
 * @param DefaultRegions
 * @text Default Region(s)
 * @parent General
 *
 * @param Bounce1Regions:arraynum
 * @text Bounce 1 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 1 Region: x> notetag.
 * @default []
 *
 * @param Bounce2Regions:arraynum
 * @text Bounce 2 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 2 Region: x> notetag.
 * @default []
 *
 * @param Bounce3Regions:arraynum
 * @text Bounce 3 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 3 Region: x> notetag.
 * @default []
 *
 * @param Bounce4Regions:arraynum
 * @text Bounce 4 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 4 Region: x> notetag.
 * @default []
 *
 * @param Bounce5Regions:arraynum
 * @text Bounce 5 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 5 Region: x> notetag.
 * @default []
 *
 * @param Bounce6Regions:arraynum
 * @text Bounce 6 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 6 Region: x> notetag.
 * @default []
 *
 * @param Bounce7Regions:arraynum
 * @text Bounce 7 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 7 Region: x> notetag.
 * @default []
 *
 * @param Bounce8Regions:arraynum
 * @text Bounce 8 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 8 Region: x> notetag.
 * @default []
 *
 * @param Bounce9Regions:arraynum
 * @text Bounce 9 Region(s)
 * @parent DefaultRegions
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which region(s) will be used to mark this tile type?
 * Ignore if map has <Bounce 9 Region: x> notetag.
 * @default []
 *
 * @param EventDefaults
 * @text Event Defaults
 * @parent General
 *
 * @param DefaultAffected:eval
 * @text Default Affected
 * @parent EventDefaults
 * @type boolean
 * @on Affected
 * @off Unaffected
 * @desc Are events affected by this tile by default?
 * @default false
 *
 * @param DefaultAvoid:eval
 * @text Default Avoid
 * @parent EventDefaults
 * @type boolean
 * @on Avoid
 * @off Normal
 * @desc Will events avoid stepping on this tile by default?
 * @default false
 *
 * @param Sound
 * @text Sound Effects
 *
 * @param Effect
 * @parent Sound
 *
 * @param effectName:str
 * @text Filename
 * @parent Effect
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Jump2
 *
 * @param effectVolume:num
 * @text Volume
 * @parent Effect
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 50
 *
 * @param effectPitch:num
 * @text Pitch
 * @parent Effect
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param effectPan:num
 * @text Pan
 * @parent Effect
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
//=============================================================================

const _0xf6447d = _0x177a;
(function (_0x368f64, _0x3778b0) {
    const _0x29df5e = _0x177a,
        _0x54c0fe = _0x368f64();
    while (!![]) {
        try {
            const _0x461e46 =
                (-parseInt(_0x29df5e(0x108)) / 0x1) * (-parseInt(_0x29df5e(0xda)) / 0x2) +
                parseInt(_0x29df5e(0x1e8)) / 0x3 +
                -parseInt(_0x29df5e(0xac)) / 0x4 +
                -parseInt(_0x29df5e(0x225)) / 0x5 +
                (parseInt(_0x29df5e(0x148)) / 0x6) * (-parseInt(_0x29df5e(0xfb)) / 0x7) +
                -parseInt(_0x29df5e(0x22b)) / 0x8 +
                parseInt(_0x29df5e(0xee)) / 0x9;
            if (_0x461e46 === _0x3778b0) break;
            else _0x54c0fe['push'](_0x54c0fe['shift']());
        } catch (_0x3d96bc) {
            _0x54c0fe['push'](_0x54c0fe['shift']());
        }
    }
})(_0x976c, 0xa66b6);
var label = _0xf6447d(0x224),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0xf6447d(0x12a)](function (_0x2cbd9e) {
        const _0x476660 = _0xf6447d;
        return (
            _0x2cbd9e[_0x476660(0x1cf)] &&
            _0x2cbd9e[_0x476660(0x14b)][_0x476660(0x1d6)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label][_0xf6447d(0x110)] = VisuMZ[label][_0xf6447d(0x110)] || {}),
    (VisuMZ['ConvertParams'] = function (_0x446d79, _0x52afa6) {
        const _0xab2352 = _0xf6447d;
        for (const _0x4b0e87 in _0x52afa6) {
            if (_0x4b0e87['match'](/(.*):(.*)/i)) {
                if (_0xab2352(0x178) !== 'vDuYt') {
                    const _0x2490fc = 0xa - this['direction']();
                    if (_0x2490fc % 0x2 !== 0x0) return;
                    const _0x517316 = _0xbc02ea[_0xab2352(0xae)](this['x'], _0x2490fc),
                        _0x4ae1b4 = _0xb589b3['roundYWithDirection'](this['y'], _0x2490fc);
                    if (
                        !this['behindUniqueTileMeetsPullTargetEventConditions'](
                            _0x517316,
                            _0x4ae1b4
                        )
                    )
                        return;
                    _0x5b0007[_0xab2352(0x224)][_0xab2352(0x243)][_0xab2352(0xe5)](
                        this,
                        _0x2f657e,
                        _0x488051,
                        _0x198290
                    );
                } else {
                    const _0xce45e9 = String(RegExp['$1']),
                        _0x8d8ef8 = String(RegExp['$2'])[_0xab2352(0x251)]()[_0xab2352(0x75)]();
                    let _0x441507, _0xddfdea, _0x595298;
                    switch (_0x8d8ef8) {
                        case 'NUM':
                            _0x441507 =
                                _0x52afa6[_0x4b0e87] !== '' ? Number(_0x52afa6[_0x4b0e87]) : 0x0;
                            break;
                        case _0xab2352(0x1a0):
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea[_0xab2352(0x18f)](_0x5be4db =>
                                    Number(_0x5be4db)
                                )));
                            break;
                        case 'EVAL':
                            _0x441507 =
                                _0x52afa6[_0x4b0e87] !== '' ? eval(_0x52afa6[_0x4b0e87]) : null;
                            break;
                        case _0xab2352(0x1dd):
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea[_0xab2352(0x18f)](_0x1bfdac =>
                                    eval(_0x1bfdac)
                                )));
                            break;
                        case _0xab2352(0xcb):
                            _0x441507 =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : '';
                            break;
                        case _0xab2352(0x210):
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON['parse'](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea['map'](_0x11975a =>
                                    JSON[_0xab2352(0x8c)](_0x11975a)
                                )));
                            break;
                        case _0xab2352(0xb5):
                            _0x441507 =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? new Function(JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87]))
                                    : new Function(_0xab2352(0xe9));
                            break;
                        case _0xab2352(0x1fa):
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea[_0xab2352(0x18f)](
                                    _0x1ea758 => new Function(JSON[_0xab2352(0x8c)](_0x1ea758))
                                )));
                            break;
                        case _0xab2352(0x234):
                            _0x441507 =
                                _0x52afa6[_0x4b0e87] !== '' ? String(_0x52afa6[_0x4b0e87]) : '';
                            break;
                        case 'ARRAYSTR':
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON['parse'](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea[_0xab2352(0x18f)](_0xdec425 =>
                                    String(_0xdec425)
                                )));
                            break;
                        case _0xab2352(0x1d2):
                            ((_0x595298 =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : {}),
                                (_0x441507 = VisuMZ[_0xab2352(0x152)]({}, _0x595298)));
                            break;
                        case _0xab2352(0xbc):
                            ((_0xddfdea =
                                _0x52afa6[_0x4b0e87] !== ''
                                    ? JSON[_0xab2352(0x8c)](_0x52afa6[_0x4b0e87])
                                    : []),
                                (_0x441507 = _0xddfdea['map'](_0x1f99bf =>
                                    VisuMZ[_0xab2352(0x152)]({}, JSON['parse'](_0x1f99bf))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x446d79[_0xce45e9] = _0x441507;
                }
            }
        }
        return _0x446d79;
    }),
    (_0x5b889a => {
        const _0x398107 = _0xf6447d,
            _0x4c8a43 = _0x5b889a[_0x398107(0x1de)];
        for (const _0x26c452 of dependencies) {
            if (!Imported[_0x26c452]) {
                (alert(_0x398107(0x114)['format'](_0x4c8a43, _0x26c452)),
                    SceneManager[_0x398107(0xc7)]());
                break;
            }
        }
        const _0x33e4d0 = _0x5b889a[_0x398107(0x14b)];
        if (_0x33e4d0[_0x398107(0x171)](/\[Version[ ](.*?)\]/i)) {
            if (_0x398107(0x1d9) === _0x398107(0x120)) {
                if (_0x242982 === _0x398107(0x13a)) return;
                const _0xd904f7 = _0x5d1893[_0x398107(0x25c)][_0x1c0c17][_0x534ef2];
                if (!_0xd904f7) return;
                if (_0x561201 === 'effect' && _0x231a02[_0x398107(0x1bc)] === _0x360931) return;
                if (_0x15cb87 === _0x398107(0x1a4) && _0x94cbea[_0x398107(0x1bc)] === _0x30b834)
                    return;
                const _0x30d23c = {
                    name: _0xd904f7[_0x398107(0x1de)] || '',
                    volume: _0xd904f7['volume'] || 0x0,
                    pitch: _0xd904f7[_0x398107(0x1b4)] || 0x0,
                    pan: _0xd904f7[_0x398107(0x202)] || 0x0,
                };
                if (_0x30d23c[_0x398107(0x1de)] === '') return;
                (_0x2a624e &&
                    _0x2547e7[_0x398107(0xe2)] &&
                    _0x221699[_0x398107(0x238)][_0x398107(0xa0)](_0x30d23c, _0x1bed04),
                    _0x5b7a71[_0x398107(0xec)](_0x30d23c));
            } else {
                const _0x231667 = Number(RegExp['$1']);
                _0x231667 !== VisuMZ[label]['version'] &&
                    (alert(_0x398107(0xed)[_0x398107(0x226)](_0x4c8a43, _0x231667)),
                    SceneManager['exit']());
            }
        }
        if (_0x33e4d0[_0x398107(0x171)](/\[Tier[ ](\d+)\]/i)) {
            const _0x46020d = Number(RegExp['$1']);
            if (_0x46020d < tier) {
                if ('cumys' === _0x398107(0x122))
                    (alert(_0x398107(0x252)[_0x398107(0x226)](_0x4c8a43, _0x46020d, tier)),
                        SceneManager[_0x398107(0xc7)]());
                else return _0x398107(0x13a);
            } else tier = Math[_0x398107(0x146)](_0x46020d, tier);
        }
        VisuMZ['ConvertParams'](VisuMZ[label]['Settings'], _0x5b889a[_0x398107(0xdd)]);
    })(pluginData),
    PluginManager['registerCommand'](pluginData[_0xf6447d(0x1de)], _0xf6447d(0x213), _0x46850f => {
        const _0x2423df = _0xf6447d;
        VisuMZ[_0x2423df(0x152)](_0x46850f, _0x46850f);
        const _0x1c3fb0 = _0x46850f[_0x2423df(0x1cc)];
        $gamePlayer[_0x2423df(0x254)](_0x2423df(0x1a4), _0x1c3fb0);
    }),
    PluginManager[_0xf6447d(0xdb)](pluginData[_0xf6447d(0x1de)], _0xf6447d(0x16a), _0x4a37d1 => {
        const _0x2600d2 = _0xf6447d;
        VisuMZ[_0x2600d2(0x152)](_0x4a37d1, _0x4a37d1);
        const _0x228429 = _0x4a37d1[_0x2600d2(0x1cc)];
        $gamePlayer[_0x2600d2(0x254)](_0x2600d2(0x21d), _0x228429);
    }),
    PluginManager[_0xf6447d(0xdb)](pluginData[_0xf6447d(0x1de)], _0xf6447d(0x17f), _0x2489f6 => {
        const _0x40d8d9 = _0xf6447d;
        VisuMZ[_0x40d8d9(0x152)](_0x2489f6, _0x2489f6);
        const _0xfa54d7 = _0x2489f6['Setting'];
        $gamePlayer[_0x40d8d9(0x254)](_0x40d8d9(0x135), _0xfa54d7);
    }),
    PluginManager[_0xf6447d(0xdb)](pluginData[_0xf6447d(0x1de)], _0xf6447d(0x187), _0x5ec6b4 => {
        const _0x2abdc4 = _0xf6447d;
        VisuMZ[_0x2abdc4(0x152)](_0x5ec6b4, _0x5ec6b4);
        const _0x527286 = _0x5ec6b4['Setting'];
        $gamePlayer[_0x2abdc4(0x254)](_0x2abdc4(0x1aa), _0x527286);
    }),
    PluginManager['registerCommand'](pluginData[_0xf6447d(0x1de)], _0xf6447d(0x25f), _0x5df64e => {
        const _0x1249b4 = _0xf6447d;
        VisuMZ['ConvertParams'](_0x5df64e, _0x5df64e);
        const _0x1b5e69 = _0x5df64e[_0x1249b4(0x1cc)];
        $gamePlayer[_0x1249b4(0x254)](_0x1249b4(0x20d), _0x1b5e69);
    }),
    PluginManager['registerCommand'](
        pluginData[_0xf6447d(0x1de)],
        'PlayerSetAntiSlippery',
        _0x105bdb => {
            const _0x1afd36 = _0xf6447d;
            VisuMZ[_0x1afd36(0x152)](_0x105bdb, _0x105bdb);
            const _0x39a321 = _0x105bdb[_0x1afd36(0x1cc)];
            $gamePlayer['setImmuneToUniqueTileType'](_0x1afd36(0xca), _0x39a321);
        }
    ),
    PluginManager[_0xf6447d(0xdb)](
        pluginData[_0xf6447d(0x1de)],
        'PlayerSetAllowSwimming',
        _0x175253 => {
            const _0x239098 = _0xf6447d;
            VisuMZ[_0x239098(0x152)](_0x175253, _0x175253);
            const _0x2b2f68 = _0x175253[_0x239098(0x1cc)];
            $gamePlayer['setImmuneToUniqueTileType']('swimming', _0x2b2f68);
        }
    ),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x24f)] = function (_0x158f06) {
        const _0x34754f = _0xf6447d,
            _0x46a2a3 = [
                _0x34754f(0xca),
                _0x34754f(0x237),
                _0x34754f(0xa7),
                _0x34754f(0x1aa),
                _0x34754f(0x135),
                _0x34754f(0x20d),
            ];
        if (_0x158f06)
            _0x34754f(0x209) !== 'mdINd'
                ? _0x1fa36c(this[_0x34754f(0x168)][_0x34754f(0x139)](this), 0x64)
                : (_0x46a2a3[_0x34754f(0xb9)](
                      _0x34754f(0x179),
                      _0x34754f(0xc4),
                      _0x34754f(0x127),
                      'up'
                  ),
                  _0x46a2a3[_0x34754f(0xb9)](
                      _0x34754f(0x1ec),
                      'bounce2',
                      _0x34754f(0x118),
                      _0x34754f(0xe8),
                      _0x34754f(0x191)
                  ),
                  _0x46a2a3[_0x34754f(0xb9)](
                      _0x34754f(0x177),
                      _0x34754f(0xcd),
                      _0x34754f(0x208),
                      _0x34754f(0x164)
                  ));
        else {
            if (_0x34754f(0x1d0) === 'ffkJx')
                (_0x46a2a3[_0x34754f(0xb9)](_0x34754f(0x21d)),
                    _0x46a2a3[_0x34754f(0xb9)]('bounce'));
            else {
                const _0x5b79f9 = _0x691aba['$1']
                    [_0x34754f(0x269)](',')
                    [_0x34754f(0x18f)](_0x5c1c65 => _0x2cfa86(_0x5c1c65) || 0x0);
                this[_0x34754f(0x1b8)][_0x441617] = _0x5b79f9;
            }
        }
        return _0x46a2a3;
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1ba)] = {
        slippery: {
            regions: /<SLIP(?:|PERY) REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<SLIP(?:|PERY) (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
            affected: /<(?:|CAN )SLIP>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )SLIP(?:|PERY)>/i,
            avoid: /<(?:AVOID|BEWARE) (?:SLIP|SLIPPERY|ICE)>/i,
            ignore: /<(?:IGNORE|CARELESS) (?:SLIP|SLIPPERY|ICE)>/i,
        },
        down: {
            regions: /<FORCE DOWN REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<FORCE DOWN (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        left: {
            regions: /<FORCE LEFT REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<FORCE LEFT (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        right: {
            regions: /<FORCE RIGHT REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<FORCE RIGHT (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        up: {
            regions: /<FORCE UP REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<FORCE UP (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        forceMove: {
            affected: /<(?:|CAN )FORCE MOVE>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )FORCE MOVE>/i,
            avoid: /<(?:AVOID|BEWARE) FORCE MOVE>/i,
            ignore: /<(?:IGNORE|CARELESS) FORCE MOVE>/i,
        },
        pitfall: {
            regions: /<PIT(?:|FALL) REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<PIT(?:|FALL) (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
            affected: /<(?:|CAN )PIT(?:|FALL)>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )PIT(?:|FALL)>/i,
            avoid: /<(?:AVOID|BEWARE) PIT(?:|FALL)>/i,
            ignore: /<(?:IGNORE|CARELESS) PIT(?:|FALL)>/i,
            mapTransfer: /<PIT(?:|FALL) (?:TRANSFER|TELEPORT):[ ](.*)>/i,
        },
        swimming: {
            affected: /<(?:|CAN )DROWN(?:|ING)>/i,
            immune: /<(?:ANTI|ANTI-|ANTI |CAN |)(?:SWIM|DROWN)(?:|ING|MING)>/i,
            avoid: /<(?:AVOID|BEWARE) DROWN(?:|ING)>/i,
            ignore: /<(?:IGNORE|CARELESS) DROWN(?:|ING)>/i,
        },
        quicksand: {
            regions: /<QUICKSAND REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<QUICKSAND (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
            affected: /<(?:|CAN )(?:|SAND)SINK>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )(?:QUICKSAND|SAND|SANDSINK|SINK)>/i,
            avoid: /<(?:AVOID|BEWARE) QUICKSAND>/i,
            ignore: /<(?:IGNORE|CARELESS) QUICKSAND>/i,
        },
        lava: {
            regions: /<LAVA REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<LAVA (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
            affected: /<(?:|CAN )(?:|LAVA|LAVA )BURN>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )(?:LAVA|BURN|LAVABURN|LAVA BURN)>/i,
            avoid: /<(?:AVOID|BEWARE) LAVA>/i,
            ignore: /<(?:IGNORE|CARELESS) LAVA>/i,
            burnMax: /<(?:|LAVA|LAVA )BURN MAX:[ ](\d+)>/i,
        },
        shock: {
            regions: /<SHOCK REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<SHOCK (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
            affected: /<(?:|CAN )SHOCK>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )SHOCK>/i,
            avoid: /<(?:AVOID|BEWARE) SHOCK>/i,
            ignore: /<(?:IGNORE|CARELESS) SHOCK>/i,
        },
        bounce1: {
            regions: /<BOUNCE 1 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 1 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce2: {
            regions: /<BOUNCE 2 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 2 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce3: {
            regions: /<BOUNCE 3 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 3 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce4: {
            regions: /<BOUNCE 4 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 4 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce5: {
            regions: /<BOUNCE 5 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 5 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce6: {
            regions: /<BOUNCE 6 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 6 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce7: {
            regions: /<BOUNCE 7 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 7 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce8: {
            regions: /<BOUNCE 8 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 8 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce9: {
            regions: /<BOUNCE 9 REGION(?:|S):[ ](.*)>/i,
            terrainTags: /<BOUNCE 9 (?:|TERRAIN|TERRAIN )TAG(?:|S):[ ](.*)>/i,
        },
        bounce: {
            affected: /<(?:|CAN )BOUNCE>/i,
            immune: /<(?:ANTI|ANTI-|ANTI )BOUNCE>/i,
            avoid: /<(?:AVOID|BEWARE) BOUNCE>/i,
            ignore: /<(?:IGNORE|CARELESS) BOUNCE>/i,
        },
    }),
    (SoundManager[_0xf6447d(0x25c)] = {
        slippery: {
            footstep: {
                enabled:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1ff)][_0xf6447d(0xd6)] ?? !![],
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Slippery'][_0xf6447d(0x10f)] ??
                    'Sand',
                volume:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1ff)][_0xf6447d(0x1fd)] ?? 0xa,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Slippery'][_0xf6447d(0xe6)] ?? 0x96,
                pan:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x1ff)][
                        'footstepsPan'
                    ] ?? 0x0,
            },
        },
        forceMove: {
            footstep: {
                enabled:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['ForceMove'][_0xf6447d(0xd6)] ??
                    !![],
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1a6)][
                        _0xf6447d(0x10f)
                    ] ?? 'Skill1',
                volume:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['ForceMove'][_0xf6447d(0x1fd)] ??
                    0xa,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1a6)][
                        'footstepsPitch'
                    ] ?? 0x82,
                pan:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['ForceMove'][_0xf6447d(0x241)] ??
                    0x0,
            },
        },
        pitfall: {
            effect: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0x13e)] ??
                    _0xf6447d(0x13c),
                volume:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0xe1)] ?? 0x32,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x235)]['effectPitch'] ??
                    0x50,
                pan:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0x219)] ??
                    0x0,
            },
            damage: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x235)][_0xf6447d(0x97)] ??
                    _0xf6447d(0x14e),
                volume:
                    VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x235)][_0xf6447d(0x205)] ??
                    0x1e,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0xe0)] ?? 0x50,
                pan:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0x1bd)] ??
                    0x0,
            },
        },
        swimming: {
            footstep: {
                enabled:
                    VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x197)][_0xf6447d(0xd6)] ??
                    !![],
                name:
                    VisuMZ['UniqueTileEffects']['Settings']['Swimming']['footstepsName'] ??
                    _0xf6447d(0xd1),
                volume: VisuMZ[_0xf6447d(0x224)]['Settings']['Swimming'][_0xf6447d(0x1fd)] ?? 0xa,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0xe6)] ??
                    0x50,
                pan:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][
                        _0xf6447d(0x241)
                    ] ?? 0x0,
            },
            effect: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][
                        _0xf6447d(0x13e)
                    ] ?? _0xf6447d(0x94),
                volume:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0xe1)] ??
                    0x32,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][
                        _0xf6447d(0x117)
                    ] ?? 0x78,
                pan:
                    VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x197)][_0xf6447d(0x219)] ??
                    0x0,
            },
            damage: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x97)] ??
                    _0xf6447d(0x199),
                volume:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x197)][_0xf6447d(0x205)] ??
                    0x1e,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)]['damagePitch'] ??
                    0x50,
                pan:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Swimming'][_0xf6447d(0x1bd)] ??
                    0x0,
            },
        },
        quicksand: {
            footstep: {
                enabled:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x9b)][_0xf6447d(0xd6)] ?? !![],
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x10f)] ??
                    _0xf6447d(0xf8),
                volume:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x9b)][_0xf6447d(0x1fd)] ?? 0xa,
                pitch:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x9b)][
                        _0xf6447d(0xe6)
                    ] ?? 0x3c,
                pan:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x241)] ??
                    0x0,
            },
            damage: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x97)] ??
                    _0xf6447d(0x223),
                volume:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x9b)][_0xf6447d(0x205)] ?? 0x1e,
                pitch:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Quicksand'][_0xf6447d(0xe0)] ??
                    0x78,
                pan: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x9b)][_0xf6447d(0x1bd)] ?? 0x0,
            },
        },
        lava: {
            footstep: {
                enabled:
                    VisuMZ['UniqueTileEffects']['Settings']['Lava']['footstepsEnabled'] ?? !![],
                name:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0xd4)][_0xf6447d(0x10f)] ??
                    _0xf6447d(0x17a),
                volume:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0xd4)][
                        _0xf6447d(0x1fd)
                    ] ?? 0xa,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0xe6)] ??
                    0x78,
                pan: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Lava'][_0xf6447d(0x241)] ?? 0x0,
            },
            damage: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Lava'][_0xf6447d(0x97)] ??
                    _0xf6447d(0x102),
                volume:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x205)] ??
                    0x1e,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0xe0)] ??
                    0x64,
                pan: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Lava'][_0xf6447d(0x1bd)] ?? 0x0,
            },
        },
        shock: {
            damage: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x244)][_0xf6447d(0x97)] ??
                    _0xf6447d(0xd7),
                volume:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x244)][
                        _0xf6447d(0x205)
                    ] ?? 0x1e,
                pitch:
                    VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x244)][_0xf6447d(0xe0)] ?? 0x78,
                pan:
                    VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Shock'][_0xf6447d(0x1bd)] ?? 0x0,
            },
        },
        bounce: {
            effect: {
                name:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Bounce'][_0xf6447d(0x13e)] ??
                    _0xf6447d(0x1e5),
                volume:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)]['effectVolume'] ??
                    0x32,
                pitch:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)][
                        _0xf6447d(0x117)
                    ] ?? 0x78,
                pan:
                    VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)][
                        _0xf6447d(0x219)
                    ] ?? 0x0,
            },
        },
    }),
    (SoundManager['playUniqueTileSfx'] = function (_0x238e59, _0x322a81, _0xd1bde) {
        const _0x43e63e = _0xf6447d;
        if (_0x238e59 === _0x43e63e(0x13a)) return;
        const _0x3e068d = SoundManager['UNIQUE_TILE_SFX'][_0x238e59][_0x322a81];
        if (!_0x3e068d) return;
        if (_0x322a81 === 'effect' && _0xd1bde[_0x43e63e(0x1bc)] === Game_Follower) return;
        if (_0x238e59 === _0x43e63e(0x1a4) && _0xd1bde['constructor'] === Game_Follower) return;
        const _0xb5e06e = {
            name: _0x3e068d[_0x43e63e(0x1de)] || '',
            volume: _0x3e068d[_0x43e63e(0xf7)] || 0x0,
            pitch: _0x3e068d[_0x43e63e(0x1b4)] || 0x0,
            pan: _0x3e068d[_0x43e63e(0x202)] || 0x0,
        };
        if (_0xb5e06e[_0x43e63e(0x1de)] === '') return;
        (_0xd1bde &&
            Imported['VisuMZ_2_MovementEffects'] &&
            VisuMZ[_0x43e63e(0x238)][_0x43e63e(0xa0)](_0xb5e06e, _0xd1bde),
            AudioManager['playSe'](_0xb5e06e));
    }),
    (SceneManager[_0xf6447d(0x1ab)] = function () {
        const _0x45ff7c = _0xf6447d;
        return this[_0x45ff7c(0x263)] && this['_scene']['constructor'] === Scene_Map;
    }),
    (Game_Map[_0xf6447d(0x25d)] = {
        slippery: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1ff)][_0xf6447d(0x151)],
        down: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['ForceMove'][_0xf6447d(0x80)],
        left: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['ForceMove']['LeftRegions'],
        right: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1a6)][_0xf6447d(0x204)],
        up: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1a6)][_0xf6447d(0xad)],
        pitfall: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x235)]['DefaultRegions'],
        quicksand: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x151)],
        lava: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0xd4)]['DefaultRegions'],
        shock: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x244)][_0xf6447d(0x151)],
        bounce1: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x15a)]['Bounce1Regions'],
        bounce2: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Bounce'][_0xf6447d(0x23f)],
        bounce3: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x15a)][_0xf6447d(0x20b)],
        bounce4: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x15a)][_0xf6447d(0xc6)],
        bounce5: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x15a)]['Bounce5Regions'],
        bounce6: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)][_0xf6447d(0x132)],
        bounce7: VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x15a)][_0xf6447d(0x16c)],
        bounce8: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Bounce']['Bounce8Regions'],
        bounce9: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Bounce'][_0xf6447d(0x18d)],
    }),
    (VisuMZ[_0xf6447d(0x224)]['Game_Map_setupEvents'] =
        Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x190)]),
    (Game_Map['prototype'][_0xf6447d(0x190)] = function () {
        const _0x4a0165 = _0xf6447d;
        (this[_0x4a0165(0x10a)](),
            VisuMZ[_0x4a0165(0x224)][_0x4a0165(0xa6)][_0x4a0165(0xe5)](this));
    }),
    (Game_Map['prototype']['setupUniqueTileData'] = function () {
        const _0xb0b3a0 = _0xf6447d;
        (this[_0xb0b3a0(0x26c)](), this['parseUniqueTileNotetags']());
    }),
    (Game_Map['prototype'][_0xf6447d(0x26c)] = function () {
        const _0x56a127 = _0xf6447d,
            _0x2f96d3 = VisuMZ['UniqueTileEffects'][_0x56a127(0x24f)](!![]);
        this[_0x56a127(0x142)] = {};
        for (const _0xf8acac of _0x2f96d3) {
            _0x56a127(0x255) !== _0x56a127(0x255)
                ? (this['_uniqueTileData'][_0x1d5886]['regions'] =
                      _0x13bef5[_0x56a127(0x25d)][_0x557b8d][_0x56a127(0xd3)]())
                : ((this['_uniqueTileData'][_0xf8acac] = { regions: [], terrainTags: [] }),
                  Game_Map[_0x56a127(0x25d)][_0xf8acac] &&
                      (this[_0x56a127(0x142)][_0xf8acac][_0x56a127(0x265)] =
                          Game_Map[_0x56a127(0x25d)][_0xf8acac]['clone']()));
        }
        ((this[_0x56a127(0x11b)] = {}), (this[_0x56a127(0x1b8)] = {}));
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x1e2)] = function () {
        const _0x272aeb = _0xf6447d,
            _0x9d4720 = VisuMZ['UniqueTileEffects'][_0x272aeb(0x1ba)],
            _0x23536e = $dataMap ? $dataMap[_0x272aeb(0x1ee)] || '' : '',
            _0x2539ef = this[_0x272aeb(0x266)]()
                ? this[_0x272aeb(0x266)]()[_0x272aeb(0x1ee)] || ''
                : '',
            _0x4a7c14 = VisuMZ[_0x272aeb(0x224)][_0x272aeb(0x24f)](!![]);
        for (const _0x5a6d2d of _0x4a7c14) {
            if (_0x272aeb(0x16f) === 'lnBNb') return !![];
            else {
                if (!_0x9d4720[_0x5a6d2d]) continue;
                if (
                    _0x9d4720[_0x5a6d2d][_0x272aeb(0x265)] &&
                    _0x23536e[_0x272aeb(0x171)](_0x9d4720[_0x5a6d2d][_0x272aeb(0x265)])
                ) {
                    if (_0x272aeb(0x172) === _0x272aeb(0x1c6)) {
                        const _0x527da4 = _0x3c3019['getUniqueTileXyType'](
                            this['x'],
                            this['y'],
                            !![]
                        );
                        return [
                            _0x272aeb(0x170),
                            'bounce1',
                            _0x272aeb(0x124),
                            'bounce3',
                            'bounce4',
                            'bounce5',
                            _0x272aeb(0x177),
                            _0x272aeb(0xcd),
                            _0x272aeb(0x208),
                            _0x272aeb(0x164),
                        ]['indexOf'](_0x527da4);
                    } else
                        this[_0x272aeb(0x142)][_0x5a6d2d][_0x272aeb(0x265)] = RegExp['$1']
                            [_0x272aeb(0x269)](',')
                            [
                                _0x272aeb(0x18f)
                            ](_0x204262 => Number(_0x204262)[_0x272aeb(0x81)](0x1, 0xff));
                }
                _0x9d4720[_0x5a6d2d][_0x272aeb(0x18e)] &&
                    _0x2539ef[_0x272aeb(0x171)](_0x9d4720[_0x5a6d2d][_0x272aeb(0x18e)]) &&
                    (this[_0x272aeb(0x142)][_0x5a6d2d][_0x272aeb(0x18e)] = RegExp['$1']
                        [_0x272aeb(0x269)](',')
                        [
                            _0x272aeb(0x18f)
                        ](_0x7fbbb => Number(_0x7fbbb)[_0x272aeb(0x81)](0x1, 0x7)));
                if (
                    _0x9d4720[_0x5a6d2d][_0x272aeb(0x198)] &&
                    _0x23536e[_0x272aeb(0x171)](_0x9d4720[_0x5a6d2d][_0x272aeb(0x198)])
                ) {
                    if (_0x272aeb(0x18a) !== _0x272aeb(0x18a)) return _0x36161c['SWIMMING_DEPTH'];
                    else {
                        const _0x36833d = RegExp['$1']
                            [_0x272aeb(0x269)](',')
                            [_0x272aeb(0x18f)](_0x3fc917 => Number(_0x3fc917) || 0x0);
                        this[_0x272aeb(0x1b8)][_0x5a6d2d] = _0x36833d;
                    }
                }
            }
        }
    }),
    (Game_Map['prototype'][_0xf6447d(0x13d)] = function (_0x5932a4) {
        const _0x8dc650 = _0xf6447d;
        if (this[_0x8dc650(0x142)] === undefined) this[_0x8dc650(0x10a)]();
        return this[_0x8dc650(0x142)][_0x5932a4] || {};
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x1bb)] = function (_0x29d3f7) {
        const _0x47b689 = _0xf6447d;
        return this[_0x47b689(0x13d)](_0x29d3f7)[_0x47b689(0x265)] || [];
    }),
    (Game_Map[_0xf6447d(0x1f6)]['getUniqueTileTerrainTags'] = function (_0x4ec223) {
        const _0x2d740f = _0xf6447d;
        return this[_0x2d740f(0x13d)](_0x4ec223)['terrainTags'] || [];
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x11e)] = function (_0x2b0ae8, _0x2b809f, _0x117ff0) {
        const _0x1130b1 = _0xf6447d;
        if (this['isSwimmingTile'](_0x2b0ae8, _0x2b809f)) return _0x1130b1(0xa7);
        const _0x457612 = [_0x2b0ae8, _0x2b809f];
        if (this[_0x1130b1(0x11b)][_0x457612] !== undefined) {
            const _0x35740f = this[_0x1130b1(0x11b)][_0x457612];
            if (
                !_0x117ff0 &&
                [_0x1130b1(0x179), _0x1130b1(0xc4), _0x1130b1(0x127), 'up']['includes'](_0x35740f)
            )
                return 'forceMove';
            if (
                !_0x117ff0 &&
                [_0x1130b1(0x1ec), 'bounce2', 'bounce3', _0x1130b1(0xe8), 'bounce5']['includes'](
                    _0x35740f
                )
            )
                return _0x1130b1(0x1a4);
            if (
                !_0x117ff0 &&
                [_0x1130b1(0x177), _0x1130b1(0xcd), _0x1130b1(0x208), _0x1130b1(0x164)][
                    _0x1130b1(0x1d6)
                ](_0x35740f)
            )
                return 'bounce';
            return _0x35740f;
        }
        this['_uniqueTileXyType'][_0x457612] = _0x1130b1(0x13a);
        const _0x4a136e = this[_0x1130b1(0x215)](_0x2b0ae8, _0x2b809f),
            _0x4487fd = this[_0x1130b1(0xb6)](_0x2b0ae8, _0x2b809f),
            _0x427624 = VisuMZ[_0x1130b1(0x224)][_0x1130b1(0x24f)](!![]);
        for (const _0x39e0c1 of _0x427624) {
            if (_0x1130b1(0x17d) === _0x1130b1(0x17d)) {
                const _0x42dc95 = this[_0x1130b1(0x1bb)](_0x39e0c1);
                if (_0x42dc95[_0x1130b1(0x1d6)](_0x4a136e)) {
                    this[_0x1130b1(0x11b)][_0x457612] = _0x39e0c1;
                    break;
                }
                if (this[_0x1130b1(0x11b)][_0x457612] !== _0x1130b1(0x13a)) break;
                const _0x234d93 = this[_0x1130b1(0x1c1)](_0x39e0c1);
                if (_0x234d93['includes'](_0x4487fd)) {
                    this[_0x1130b1(0x11b)][_0x457612] = _0x39e0c1;
                    break;
                }
                if (this['_uniqueTileXyType'][_0x457612] !== _0x1130b1(0x13a)) break;
            } else {
                if (_0x1a7722['_vehicleGettingOn']) return;
                if (_0x20b1d4['isInVehicle']()) return;
                _0x18f3b9[_0x1130b1(0x1f6)][_0x1130b1(0x9c)][_0x1130b1(0xe5)](this);
            }
        }
        if (
            !_0x117ff0 &&
            [_0x1130b1(0x179), _0x1130b1(0xc4), _0x1130b1(0x127), 'up']['includes'](
                this['_uniqueTileXyType'][_0x457612]
            )
        )
            return _0x1130b1(0x21d);
        if (
            !_0x117ff0 &&
            [
                _0x1130b1(0x1ec),
                _0x1130b1(0x124),
                _0x1130b1(0x118),
                'bounce4',
                _0x1130b1(0x191),
                _0x1130b1(0x177),
                'bounce7',
                _0x1130b1(0x208),
                _0x1130b1(0x164),
            ][_0x1130b1(0x1d6)](this[_0x1130b1(0x11b)][_0x457612])
        ) {
            if (_0x1130b1(0x182) !== 'ULYtb') return _0x1130b1(0x1a4);
            else {
                const _0x5d2522 = this[_0x1130b1(0x1c4)](),
                    _0x483588 = this[_0x1130b1(0x247)](),
                    _0x470d7e = (this[_0x1130b1(0x18c)]() + this[_0x1130b1(0x227)]()) * _0x5d2522,
                    _0x7fcaa5 = (this[_0x1130b1(0x200)]() + this[_0x1130b1(0x112)]()) * _0x483588;
                this[_0x1130b1(0x242)](
                    _0x470d7e,
                    _0x7fcaa5,
                    _0x5d2522,
                    _0x483588 - this[_0x1130b1(0x96)]
                );
            }
        }
        return this[_0x1130b1(0x11b)][_0x457612];
    }),
    (Game_Map[_0xf6447d(0x1f6)]['isUniqueTile'] = function (_0x12c64a, _0x50f060) {
        const _0xcad913 = _0xf6447d;
        return this['getUniqueTileXyType'](_0x12c64a, _0x50f060, ![]) !== _0xcad913(0x13a);
    }),
    (Game_Map[_0xf6447d(0x1f6)]['checkUniqueTileType'] = function (
        _0x3ff8c1,
        _0x54fb35,
        _0x259bef
    ) {
        return this['getUniqueTileXyType'](_0x3ff8c1, _0x54fb35, ![]) === _0x259bef;
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x15d)] = function (_0xd68b66, _0x2c3365) {
        const _0x4561ea = _0xf6447d;
        return (
            this[_0x4561ea(0x173)](_0xd68b66, _0x2c3365) &&
            !this[_0x4561ea(0xce)](_0xd68b66, _0x2c3365)
        );
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0xf0)] = function (_0x215c09, _0x37254e) {
        const _0x4b6135 = _0xf6447d;
        this[_0x4b6135(0x14a)] = !![];
        let _0x1dcd67 = ![];
        if (this[_0x4b6135(0x11d)](_0x215c09, _0x37254e, 0x2)) _0x1dcd67 = !![];
        if (this[_0x4b6135(0x11d)](_0x215c09, _0x37254e, 0x4)) _0x1dcd67 = !![];
        if (this[_0x4b6135(0x11d)](_0x215c09, _0x37254e, 0x6)) _0x1dcd67 = !![];
        if (this['isPassable'](_0x215c09, _0x37254e, 0x8)) _0x1dcd67 = !![];
        return ((this[_0x4b6135(0x14a)] = ![]), _0x1dcd67);
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0xce)] = function (_0x3f9cb0, _0x2bd8b7) {
        const _0x20262a = _0xf6447d,
            _0x2fbd30 = this[_0x20262a(0x24d)](_0x3f9cb0, _0x2bd8b7)[_0x20262a(0x12a)](
                _0x1b1051 =>
                    _0x1b1051 &&
                    !_0x1b1051[_0x20262a(0x121)] &&
                    _0x1b1051[_0x20262a(0xf1)] === 0x0 &&
                    (_0x1b1051['characterName']() !== '' || _0x1b1051[_0x20262a(0x1ea)]() > 0x0)
            );
        return _0x2fbd30['length'] > 0x0;
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0xde)] = function (_0x1be6c8, _0x4352ac) {
        const _0x57557c = _0xf6447d;
        return (
            this['checkPassageNoEvents'](_0x1be6c8, _0x4352ac, 0x200) ||
            this[_0x57557c(0x162)](_0x1be6c8, _0x4352ac, 0x400)
        );
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0xf9)] = function (_0x1e022c, _0x586453) {
        const _0x143a6b = _0xf6447d;
        return (
            this[_0x143a6b(0xde)](_0x1e022c, _0x586453) &&
            this[_0x143a6b(0xce)](_0x1e022c, _0x586453)
        );
    }),
    (Game_Map['prototype']['isSwimmingTileWithNoBridge'] = function (_0x154026, _0x2691cc) {
        const _0x3f1a0a = _0xf6447d;
        return (
            this['isSwimmingTile'](_0x154026, _0x2691cc) &&
            !this[_0x3f1a0a(0xce)](_0x154026, _0x2691cc)
        );
    }),
    (Game_Map[_0xf6447d(0x1f6)][_0xf6447d(0x162)] = function (_0x3907d4, _0x2a7de7, _0x5df43d) {
        const _0x59c1df = _0xf6447d,
            _0x729472 = this[_0x59c1df(0x181)](),
            _0x4f0807 = this['layeredTiles'](_0x3907d4, _0x2a7de7);
        for (const _0x46eedd of _0x4f0807) {
            if (_0x59c1df(0x17e) === _0x59c1df(0x15c)) {
                if (_0x4214c6['isInVehicle']()) return ![];
                if (_0x171af7[_0x59c1df(0x8e)]()) return ![];
            } else {
                const _0x353cdf = _0x729472[_0x46eedd];
                if (_0x353cdf === undefined || _0x353cdf === null) {
                    if (_0x59c1df(0xab) !== _0x59c1df(0xab)) {
                        const _0x279f1e = _0x11763c[_0x59c1df(0xbe)](this);
                        if (_0x279f1e) {
                            _0x279f1e[_0x59c1df(0x220)]();
                            return;
                        }
                    } else {
                        let _0x4d6ca2 = _0x59c1df(0x185) + '\x0a';
                        ((_0x4d6ca2 += _0x59c1df(0x100) + '\x0a'),
                            (_0x4d6ca2 += 'and\x20add\x20it\x20onto\x20this\x20one.'),
                            alert(_0x4d6ca2),
                            SceneManager[_0x59c1df(0xc7)]());
                    }
                }
                if ((_0x353cdf & 0x10) !== 0x0) continue;
                if ((_0x353cdf & _0x5df43d) === 0x0) return !![];
                if ((_0x353cdf & _0x5df43d) === _0x5df43d) return ![];
            }
        }
        return ![];
    }),
    (Game_CharacterBase[_0xf6447d(0x15f)] = {
        slippery:
            VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x1ff)]['MoveSpeed'] ?? 0x4,
        forceMove: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1a6)]['MoveSpeed'] ?? 0x6,
        swimming:
            VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x1d1)] ?? 0x4,
        quicksand:
            VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x1d1)] ?? 0x3,
    }),
    (Game_CharacterBase[_0xf6447d(0x16b)] = {
        slippery:
            VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1ff)][_0xf6447d(0xf3)] ?? 0x2,
        forceMove: VisuMZ[_0xf6447d(0x224)]['Settings']['ForceMove'][_0xf6447d(0xf3)] ?? 0x2,
    }),
    (Game_CharacterBase[_0xf6447d(0x1be)] =
        VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x140)] ?? !![]),
    (Game_CharacterBase['SWIMMING_DEPTH'] =
        VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x1af)] ?? 0x10),
    (Game_CharacterBase[_0xf6447d(0x7e)] =
        VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0xbb)] ?? 0xa),
    (Game_CharacterBase[_0xf6447d(0x10e)] = {
        id: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)]['AnimationID'] ?? 0x7d,
        mirror:
            VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x15b)] ?? ![],
        mute: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)]['AnimationMute'] ?? !![],
    }),
    (Game_CharacterBase[_0xf6447d(0x211)] =
        VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x23c)] ?? 0x14),
    (Game_CharacterBase[_0xf6447d(0xc8)] =
        VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x23c)] ?? 0x6),
    (Game_CharacterBase[_0xf6447d(0x184)] = {
        id: VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x244)][_0xf6447d(0x1cd)] ?? 0x4d,
        mirror: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Shock'][_0xf6447d(0x15b)] ?? ![],
        mute:
            VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x244)][_0xf6447d(0x154)] ?? !![],
    }),
    (Game_CharacterBase['SHOCK_DELAY'] =
        VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Shock'][_0xf6447d(0x19d)] ?? 0x3c),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1cb)] =
        Game_CharacterBase['prototype'][_0xf6447d(0x12f)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x12f)] = function () {
        const _0x5436fb = _0xf6447d;
        (VisuMZ[_0x5436fb(0x224)]['Game_CharacterBase_initMembers']['call'](this),
            this[_0x5436fb(0x10a)]());
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['setupUniqueTileData'] = function () {
        const _0x3f3780 = _0xf6447d;
        ((this[_0x3f3780(0x245)] = {}),
            (this[_0x3f3780(0x91)] = -0x1),
            (this[_0x3f3780(0x222)] = -0x1),
            (this[_0x3f3780(0xe7)] = 0x0));
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x1ac)] = function () {
        const _0x287e5d = _0xf6447d;
        if (this[_0x287e5d(0x121)]) return ![];
        return $gameMap[_0x287e5d(0x173)](this['x'], this['y']);
    }),
    (Game_Vehicle['prototype'][_0xf6447d(0x1ac)] = function () {
        return ![];
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x16d)] = function () {
        const _0xd1e8b8 = _0xf6447d;
        if (this[_0xd1e8b8(0x121)]) return _0xd1e8b8(0x13a);
        return $gameMap[_0xd1e8b8(0x11e)](this['x'], this['y'], ![]);
    }),
    (Game_Vehicle[_0xf6447d(0x1f6)][_0xf6447d(0x16d)] = function () {
        const _0x1b71cc = _0xf6447d;
        return _0x1b71cc(0x13a);
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x115)] = function (_0x270260, _0x7d0b86) {
        const _0x4c5280 = _0xf6447d;
        if (this['_uniqueTileAffected'] === undefined) this[_0x4c5280(0x10a)]();
        ((this[_0x4c5280(0x245)][_0x270260] = _0x7d0b86), this[_0x4c5280(0x1b9)]());
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x26a)] = function (_0x81132b) {
        const _0x45ed15 = _0xf6447d;
        if (_0x81132b === _0x45ed15(0x13a)) return ![];
        if (this[_0x45ed15(0x121)]) return ![];
        if (this[_0x45ed15(0x245)] === undefined) this[_0x45ed15(0x10a)]();
        return this['_uniqueTileAffected'][_0x81132b];
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x26a)] = function (_0x178b72) {
        const _0x55c1c9 = _0xf6447d;
        if (_0x178b72 === _0x55c1c9(0x13a)) return ![];
        if (this['isImmuneToUniqueTileType'](_0x178b72)) {
            if (_0x178b72 === _0x55c1c9(0xa7)) return !![];
            return ![];
        }
        if (_0x178b72 === _0x55c1c9(0xa7)) return Game_CharacterBase[_0x55c1c9(0x1be)];
        return !![];
    }),
    (Game_Follower['prototype'][_0xf6447d(0x26a)] = function (_0x167aa0) {
        const _0x38027f = _0xf6447d;
        return $gamePlayer[_0x38027f(0x26a)](_0x167aa0);
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0x1f2)] = function () {
        const _0x5d7173 = _0xf6447d;
        if (this['_erased']) return ![];
        const _0x363f94 = this['getUniqueTileType']();
        if (_0x363f94 === _0x5d7173(0x13a)) return ![];
        return this['isUniqueTileAffected'](_0x363f94);
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['clearUniqueTileTempData'] = function () {
        const _0x4bf2f9 = _0xf6447d;
        ((this[_0x4bf2f9(0x91)] = -0x1), (this[_0x4bf2f9(0x222)] = -0x1));
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x21a)] = function () {
        const _0x11858d = _0xf6447d;
        this['clearUniqueTileTempData']();
        if (!this[_0x11858d(0x1ac)]()) return;
        if ($gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])) return;
        if (this[_0x11858d(0x121)]) return;
        if (this[_0x11858d(0x1f2)]()) {
            const _0x3e09f9 = this[_0x11858d(0x16d)]();
            ((this[_0x11858d(0x91)] = Game_CharacterBase[_0x11858d(0x15f)][_0x3e09f9] ?? -0x1),
                (this[_0x11858d(0x222)] = Game_CharacterBase[_0x11858d(0x16b)][_0x3e09f9] ?? -0x1));
        }
    }),
    (VisuMZ[_0xf6447d(0x224)]['Game_CharacterBase_realMoveSpeed'] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xc3)]),
    (Game_CharacterBase['prototype'][_0xf6447d(0xc3)] = function () {
        const _0x345e96 = _0xf6447d;
        if (!this['isDebugThrough']()) {
            if ('eQevP' === _0x345e96(0x99)) {
                if (this[_0x345e96(0x203)]()) return this[_0x345e96(0x19f)]();
                if (!this[_0x345e96(0x203)]() && this[_0x345e96(0x15e)]()) return !![];
            } else {
                if (this[_0x345e96(0x91)] === undefined) this[_0x345e96(0x21a)]();
                if (this[_0x345e96(0x91)] > 0x0) return this[_0x345e96(0x91)];
            }
        }
        return VisuMZ['UniqueTileEffects'][_0x345e96(0x23a)]['call'](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x105)] =
        Game_CharacterBase['prototype'][_0xf6447d(0x1e0)]),
    (Game_CharacterBase['prototype'][_0xf6447d(0x1e0)] = function () {
        const _0x2a29bd = _0xf6447d;
        if (!this[_0x2a29bd(0x192)]()) {
            if (this[_0x2a29bd(0x23d)] && !this['_stepAnime']) {
                if (this[_0x2a29bd(0x222)] === undefined) this[_0x2a29bd(0x21a)]();
                if (this[_0x2a29bd(0x222)] > -0x1) return this[_0x2a29bd(0x222)];
            }
        }
        return VisuMZ[_0x2a29bd(0x224)][_0x2a29bd(0x105)][_0x2a29bd(0xe5)](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xcf)] = Game_Player[_0xf6447d(0x1f6)]['isDashing']),
    (Game_Player[_0xf6447d(0x1f6)]['isDashing'] = function () {
        const _0x239014 = _0xf6447d;
        if (this['isDashingAffectedByUniqueTile']()) return ![];
        return VisuMZ['UniqueTileEffects'][_0x239014(0xcf)][_0x239014(0xe5)](this);
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x9e)] = function () {
        const _0x230a2e = _0xf6447d;
        if (this['isDebugThrough']()) return ![];
        if ($gameMap[_0x230a2e(0x149)](this['x'], this['y'], 'pitfall')) return ![];
        if ($gameMap[_0x230a2e(0x149)](this['x'], this['y'], _0x230a2e(0x1a4))) return ![];
        if ($gameMap[_0x230a2e(0xce)](this['x'], this['y'])) return ![];
        return this[_0x230a2e(0x1f2)]();
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xd2)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x24b)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x24b)] = function (_0xf662f7, _0x3a1c3b) {
        const _0x41bef7 = _0xf6447d;
        (VisuMZ[_0x41bef7(0x224)][_0x41bef7(0xd2)][_0x41bef7(0xe5)](this, _0xf662f7, _0x3a1c3b),
            this[_0x41bef7(0x21a)](),
            this[_0x41bef7(0x8d)](0x1),
            (this[_0x41bef7(0x13f)] = !![]));
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xa4)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xb3)]),
    (Game_CharacterBase['prototype']['moveStraight'] = function (_0xa6b2bf) {
        const _0x38d383 = _0xf6447d;
        if (this === $gamePlayer) this[_0x38d383(0x10d)]();
        (VisuMZ[_0x38d383(0x224)][_0x38d383(0xa4)][_0x38d383(0xe5)](this, _0xa6b2bf),
            this['updateUniqueTileData'](),
            this[_0x38d383(0x8d)](0x1),
            (this['_uniqueTileMoveDirection'] = _0xa6b2bf),
            (this[_0x38d383(0x13f)] = ![]));
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x236)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x19b)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['moveDiagonally'] = function (_0x46209f, _0x2f12db) {
        const _0x473c17 = _0xf6447d;
        if (this === $gamePlayer) this['registerLastUniqueTileCoordinate']();
        (VisuMZ['UniqueTileEffects'][_0x473c17(0x236)][_0x473c17(0xe5)](this, _0x46209f, _0x2f12db),
            this[_0x473c17(0x21a)](),
            this[_0x473c17(0x8d)](0x2),
            (this[_0x473c17(0x13f)] = ![]));
    }),
    (VisuMZ[_0xf6447d(0x224)]['Game_CharacterBase_jump'] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x217)]),
    (Game_CharacterBase['prototype'][_0xf6447d(0x217)] = function (_0x21678e, _0x51e1e8) {
        const _0x5c839d = _0xf6447d;
        (VisuMZ[_0x5c839d(0x224)][_0x5c839d(0x123)][_0x5c839d(0xe5)](this, _0x21678e, _0x51e1e8),
            this['updateUniqueTileData'](),
            (this[_0x5c839d(0x246)] = this[_0x5c839d(0x1da)]()),
            (this[_0x5c839d(0x13f)] = ![]));
    }),
    (VisuMZ[_0xf6447d(0x224)]['Game_CharacterBase_isStopping'] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xaa)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xaa)] = function () {
        const _0x1bfe6f = _0xf6447d;
        if (this['_isPitfalling']) return !![];
        if (this['_isDrowning']) return !![];
        return VisuMZ[_0x1bfe6f(0x224)][_0x1bfe6f(0x240)][_0x1bfe6f(0xe5)](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1b6)] = Game_Player[_0xf6447d(0x1f6)]['moveByInput']),
    (Game_Player[_0xf6447d(0x1f6)]['moveByInput'] = function () {
        const _0xf549f9 = _0xf6447d;
        if (this[_0xf549f9(0x258)]) return;
        if (this[_0xf549f9(0x1bf)]) return;
        if (this[_0xf549f9(0x1a5)] !== undefined && this[_0xf549f9(0x1a5)]-- > 0x0) return;
        VisuMZ['UniqueTileEffects']['Game_Player_moveByInput'][_0xf549f9(0xe5)](this);
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x8b)] = function () {
        const _0x4138e8 = _0xf6447d;
        if ($gameMap[_0x4138e8(0xce)](this['x'], this['y'])) return ![];
        if (this[_0x4138e8(0x1f2)]()) {
            if (_0x4138e8(0x156) !== _0x4138e8(0x12c)) {
                const _0x123db2 = this['getUniqueTileType']();
                if (
                    SoundManager['UNIQUE_TILE_SFX'][_0x123db2]['footstep'] &&
                    SoundManager['UNIQUE_TILE_SFX'][_0x123db2][_0x4138e8(0x231)]['enabled']
                ) {
                    if (_0x4138e8(0x90) === 'yQrUa')
                        this[_0x4138e8(0x142)][_0xcd106a][_0x4138e8(0x18e)] = _0x479ef8['$1']
                            [_0x4138e8(0x269)](',')
                            [
                                _0x4138e8(0x18f)
                            ](_0x105f29 => _0x17c631(_0x105f29)[_0x4138e8(0x81)](0x1, 0x7));
                    else return !![];
                }
            } else
                return this[_0x4138e8(0x10c)]
                    [_0x4138e8(0x183)]()
                    ['some'](_0x4ef947 => !_0x4ef947[_0x4138e8(0x203)]());
        }
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x14d)] =
        Game_CharacterBase['prototype']['canMakeFootstepSounds']),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['canMakeFootstepSounds'] = function () {
        const _0x4f9395 = _0xf6447d;
        if (this[_0x4f9395(0x8b)]()) return ![];
        if (this[_0x4f9395(0x121)]) return ![];
        return VisuMZ['UniqueTileEffects'][_0x4f9395(0x14d)][_0x4f9395(0xe5)](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x161)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x13b)]),
    (Game_CharacterBase['prototype'][_0xf6447d(0x13b)] = function () {
        const _0x30ef78 = _0xf6447d;
        (VisuMZ[_0x30ef78(0x224)][_0x30ef78(0x161)][_0x30ef78(0xe5)](this),
            this[_0x30ef78(0x104)]());
    }),
    (VisuMZ['UniqueTileEffects'][_0xf6447d(0xd0)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x12b)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x12b)] = function () {
        const _0x5c7d76 = _0xf6447d;
        (VisuMZ[_0x5c7d76(0x224)]['Game_CharacterBase_updateJump']['call'](this),
            this['_jumpCount'] === 0x0 && this[_0x5c7d76(0x267)]() && this[_0x5c7d76(0x17c)]());
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0x104)] = function () {
        const _0xe4557d = _0xf6447d;
        (this[_0xe4557d(0x267)]() && this[_0xe4557d(0x17c)](),
            this === $gamePlayer && this[_0xe4557d(0x109)]() && this['gatherFollowers']());
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0x267)] = function () {
        const _0x5c6825 = _0xf6447d;
        if (!Imported['VisuMZ_2_MovementEffects']) return ![];
        if (!$gameSystem[_0x5c6825(0x22f)]()) return ![];
        if (!this[_0x5c6825(0x1ac)]()) return ![];
        if (!this[_0x5c6825(0x1f2)]()) return ![];
        if (!this[_0x5c6825(0x8b)]()) return ![];
        return !![];
    }),
    (Game_CharacterBase['prototype']['makeUniqueTileFootstepSounds'] = function () {
        const _0x1dce12 = _0xf6447d,
            _0x4fad5f = this[_0x1dce12(0x16d)]();
        SoundManager[_0x1dce12(0x175)](_0x4fad5f, 'footstep', this);
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x109)] = function () {
        const _0x54a7c3 = _0xf6447d;
        if (this[_0x54a7c3(0x192)]()) return ![];
        if (this[_0x54a7c3(0x22a)]()) {
            if (_0x54a7c3(0x165) !== _0x54a7c3(0x11c)) {
                if (this['isSwimming']()) return this[_0x54a7c3(0x19f)]();
                if (!this['isSwimming']() && this[_0x54a7c3(0x15e)]()) return !![];
            } else
                ((this[_0x54a7c3(0x257)] = 0x0),
                    (this[_0x54a7c3(0xb2)] = _0x4794f7),
                    this[_0x54a7c3(0xfc)]());
        }
        const _0x1fb990 = this[_0x54a7c3(0x22e)](),
            _0x3305c3 = $gameMap[_0x54a7c3(0x11e)](_0x1fb990['x'], _0x1fb990['y']),
            _0x40c8aa = this[_0x54a7c3(0x26a)](_0x3305c3),
            _0x522b1 = $gameMap[_0x54a7c3(0xce)](_0x1fb990['x'], _0x1fb990['y']);
        if (!this[_0x54a7c3(0x1f2)]() || $gameMap[_0x54a7c3(0xce)](this['x'], this['y']))
            return _0x40c8aa && !_0x522b1;
        return ![];
    }),
    (Game_Player['prototype'][_0xf6447d(0x15e)] = function () {
        const _0x564b4e = _0xf6447d;
        return this['_followers']
            ['visibleFollowers']()
            [_0x564b4e(0xe3)](_0x59679d => _0x59679d[_0x564b4e(0x203)]());
    }),
    (Game_Player['prototype']['anyFollowersNotSwimming'] = function () {
        const _0x4bcb58 = _0xf6447d;
        return this[_0x4bcb58(0x10c)]
            ['visibleFollowers']()
            [_0x4bcb58(0xe3)](_0x55001e => !_0x55001e[_0x4bcb58(0x203)]());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x82)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x89)]),
    (Game_CharacterBase['prototype'][_0xf6447d(0x89)] = function (_0x26ac77, _0xa3b2a7, _0x2ed72d) {
        const _0x3fc7f4 = _0xf6447d;
        if (this['_uniqueTileSelfMove']) {
            const _0x5880f2 = $gameMap[_0x3fc7f4(0xae)](_0x26ac77, _0x2ed72d),
                _0x205763 = $gameMap[_0x3fc7f4(0x95)](_0xa3b2a7, _0x2ed72d),
                _0x223ca2 = $gameMap[_0x3fc7f4(0x11e)](_0x5880f2, _0x205763, ![]);
            if (this[_0x3fc7f4(0x126)](_0x223ca2)) return ![];
        }
        if (this[_0x3fc7f4(0x26a)](_0x3fc7f4(0x237))) {
            if ($gameMap[_0x3fc7f4(0x149)](this['x'], this['y'], _0x3fc7f4(0x237))) {
                if (!$gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])) {
                    if ('kbXdQ' !== _0x3fc7f4(0x19c)) return ![];
                    else {
                        if (this[_0x3fc7f4(0x1bc)] === _0x428484) return;
                        (this[_0x3fc7f4(0xb3)](this['forceMoveDirection']()),
                            this === _0x448351 && _0x4591e3[_0x3fc7f4(0x85)]([0x1, 0x2]));
                    }
                }
            }
            const _0x9bc83b = $gameMap[_0x3fc7f4(0xae)](_0x26ac77, _0x2ed72d),
                _0x30ae74 = $gameMap[_0x3fc7f4(0x95)](_0xa3b2a7, _0x2ed72d);
            if ($gameMap[_0x3fc7f4(0x149)](_0x9bc83b, _0x30ae74, _0x3fc7f4(0x237))) return !![];
        }
        if (this['isUniqueTileAffected'](_0x3fc7f4(0xa7))) {
            if (_0x3fc7f4(0xa1) === 'WiGDa') {
                if ($gameMap[_0x3fc7f4(0x149)](this['x'], this['y'], 'swimming')) {
                    if (!$gameMap[_0x3fc7f4(0xce)](this['x'], this['y']))
                        return this[_0x3fc7f4(0x22a)]();
                }
                const _0x6d5bd0 = $gameMap[_0x3fc7f4(0xae)](_0x26ac77, _0x2ed72d),
                    _0x2a3842 = $gameMap[_0x3fc7f4(0x95)](_0xa3b2a7, _0x2ed72d);
                if ($gameMap[_0x3fc7f4(0x149)](_0x6d5bd0, _0x2a3842, _0x3fc7f4(0xa7)))
                    return _0x3fc7f4(0x1ce) === _0x3fc7f4(0x1ce)
                        ? !![]
                        : _0x41ac2a[_0x3fc7f4(0x176)](
                              this[_0x3fc7f4(0x25a)][_0x3fc7f4(0x7a)]() * this['patternHeight']()
                          );
            } else return _0x4615c1[_0x3fc7f4(0xce)](this['x'], this['y']);
        }
        return VisuMZ[_0x3fc7f4(0x224)]['Game_CharacterBase_isMapPassable'][_0x3fc7f4(0xe5)](
            this,
            _0x26ac77,
            _0xa3b2a7,
            _0x2ed72d
        );
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x194)] =
        Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x163)]),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x163)] = function () {
        const _0x5a5718 = _0xf6447d;
        VisuMZ[_0x5a5718(0x224)][_0x5a5718(0x194)][_0x5a5718(0xe5)](this);
        if (this[_0x5a5718(0xc9)]()) this[_0x5a5718(0xb1)]();
        if (this[_0x5a5718(0x87)]()) this[_0x5a5718(0x1db)]();
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0xc9)] = function () {
        const _0x37fc5a = _0xf6447d;
        if (this['isMoving']()) return ![];
        if (this['isJumping']()) return ![];
        if (this['isDebugThrough']()) return ![];
        if (this['_erased']) return ![];
        if (this === $gamePlayer) {
            if (_0x37fc5a(0xfd) === _0x37fc5a(0x233))
                ((_0x31ea38['_executeFloorDamage'] = !![]),
                    this[_0x37fc5a(0x25e)](),
                    (_0x570378[_0x37fc5a(0x86)] = ![]));
            else {
                if ($gamePlayer[_0x37fc5a(0x83)]()) return ![];
                if ($gamePlayer[_0x37fc5a(0x8e)]()) return ![];
            }
        }
        if (this[_0x37fc5a(0x1bc)] === Game_Follower && $gamePlayer['isDebugThrough']()) return ![];
        if (!this[_0x37fc5a(0x1f2)]()) return ![];
        if ($gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])) return ![];
        return !![];
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xb1)] = function () {
        const _0x36dac5 = _0xf6447d,
            _0x25c93f = this[_0x36dac5(0x16d)]();
        this['processUniqueTileEffect'](_0x25c93f);
    }),
    (Game_CharacterBase['prototype']['processUniqueTileEffect'] = function (_0x473b11) {
        const _0xcbf95b = _0xf6447d;
        switch (_0x473b11) {
            case _0xcbf95b(0xca):
                this['processSlipperyEffect']();
                break;
            case _0xcbf95b(0x21d):
                this[_0xcbf95b(0xa2)]();
                break;
            case _0xcbf95b(0x237):
                this[_0xcbf95b(0xbf)]();
                break;
            case _0xcbf95b(0xa7):
                this['processDrowningEffect']();
                break;
            case _0xcbf95b(0x20d):
                this[_0xcbf95b(0x1b7)]();
                break;
            case _0xcbf95b(0x1a4):
                this[_0xcbf95b(0x150)]();
                break;
        }
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['processSlipperyEffect'] = function () {
        const _0x3b665b = _0xf6447d;
        if (this[_0x3b665b(0x13f)]) return;
        if (this[_0x3b665b(0x1bc)] === Game_Follower) return;
        const _0x4c426a = this['direction']();
        if ([0x7, 0x1][_0x3b665b(0x1d6)](_0x4c426a)) this[_0x3b665b(0x260)](0x4);
        if ([0x9, 0x3]['includes'](_0x4c426a)) this[_0x3b665b(0x260)](0x6);
        (this[_0x3b665b(0xb3)](this[_0x3b665b(0xf5)]()),
            this === $gamePlayer && $gamePlayer[_0x3b665b(0x85)]([0x1, 0x2]),
            !this[_0x3b665b(0x93)]() && (this[_0x3b665b(0x13f)] = !![]));
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0xf5)] = function () {
        const _0x4af1e5 = _0xf6447d;
        return this['_uniqueTileMoveDirection'] || this[_0x4af1e5(0x1da)]();
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0xa2)] = function () {
        const _0x1a782e = _0xf6447d;
        if (this[_0x1a782e(0x1bc)] === Game_Follower) return;
        (this[_0x1a782e(0xb3)](this[_0x1a782e(0x153)]()),
            this === $gamePlayer &&
                (_0x1a782e(0x180) === 'rDQTN'
                    ? _0x2bc90f['checkEventTriggerHere']([0x1, 0x2])
                    : $gamePlayer[_0x1a782e(0x85)]([0x1, 0x2])));
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x153)] = function () {
        const _0x190fc0 = _0xf6447d,
            _0x3037cb = $gameMap[_0x190fc0(0x11e)](this['x'], this['y'], !![]);
        switch (_0x3037cb) {
            case 'down':
                return 0x2;
            case _0x190fc0(0xc4):
                return 0x4;
            case _0x190fc0(0x127):
                return 0x6;
            case 'up':
                return 0x8;
            default:
                return 0x0;
        }
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['processPitfallEffect'] = function () {
        const _0x3dce29 = _0xf6447d;
        if (this[_0x3dce29(0x258)]) return;
        if ($gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])) return;
        ((this[_0x3dce29(0x258)] = !![]),
            SoundManager[_0x3dce29(0x175)]('pitfall', _0x3dce29(0xa5), this));
        const _0x448a32 = SceneManager[_0x3dce29(0x263)]['_spriteset'];
        if (_0x448a32) {
            if (_0x3dce29(0x256) !== 'cNElw') {
                const _0x331eec = _0x448a32[_0x3dce29(0xbe)](this);
                if (_0x331eec) {
                    _0x331eec[_0x3dce29(0x133)]();
                    return;
                }
            } else {
                if (this[_0x3dce29(0x1d4)](_0x534cbd, _0x5e4d40)) return !![];
                return _0x45fb['UniqueTileEffects'][_0x3dce29(0x20e)][_0x3dce29(0xe5)](
                    this,
                    _0x34a8d3,
                    _0x313d97
                );
            }
        }
        this[_0x3dce29(0x1ad)]();
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x1ad)] = function () {
        const _0x447bd3 = _0xf6447d;
        setTimeout(this[_0x447bd3(0xc5)][_0x447bd3(0x139)](this), 0x64);
    }),
    (Game_CharacterBase['prototype']['turnOffPitfalling'] = function () {
        const _0x357112 = _0xf6447d;
        this[_0x357112(0x258)] = ![];
    }),
    (Game_Player['prototype'][_0xf6447d(0x1ad)] = function () {
        const _0x2b06db = _0xf6447d;
        $gameParty[_0x2b06db(0x1c3)]();
        if ($gameParty[_0x2b06db(0xa9)]()) {
            if (_0x2b06db(0x188) !== 'wIOTA') {
                this[_0x2b06db(0x10c)][_0x2b06db(0x76)]();
                return;
            } else {
                _0xd6cf24['startDrowningEffect']();
                return;
            }
        }
        Game_Character[_0x2b06db(0x1f6)][_0x2b06db(0x1ad)][_0x2b06db(0xe5)](this);
        if (this[_0x2b06db(0x167)](_0x2b06db(0x237))) return;
        (this[_0x2b06db(0x12d)](), this['_followers'][_0x2b06db(0x1ad)]());
        if (SceneManager['isSceneMap']()) {
            const _0x307cd1 =
                SceneManager[_0x2b06db(0x263)][_0x2b06db(0x206)][_0x2b06db(0xbe)](this);
            if (_0x307cd1) _0x307cd1[_0x2b06db(0x189)]();
        }
    }),
    (Game_Followers[_0xf6447d(0x1f6)]['onPitfallFinish'] = function () {
        const _0x391968 = _0xf6447d;
        for (const _0x71efd8 of this[_0x391968(0x24a)]) {
            if ('hnzvI' === _0x391968(0xcc)) return this[_0x391968(0x1b2)](_0x391968(0xa7));
            else {
                if (!_0x71efd8) continue;
                _0x71efd8[_0x391968(0x1ad)]();
                if (SceneManager['isSceneMap']()) {
                    const _0x598ae9 =
                        SceneManager['_scene'][_0x391968(0x206)]['findTargetSprite'](_0x71efd8);
                    if (_0x598ae9) _0x598ae9[_0x391968(0x189)]();
                }
            }
        }
    }),
    (Game_Followers['prototype'][_0xf6447d(0x76)] = function () {
        const _0x42680e = _0xf6447d;
        for (const _0x2306a7 of this[_0x42680e(0x24a)]) {
            if (!_0x2306a7) continue;
            _0x2306a7['_isPitfalling'] = !![];
        }
    }),
    (Game_Follower[_0xf6447d(0x1f6)][_0xf6447d(0x1ad)] = function () {
        const _0x290ce8 = _0xf6447d;
        if ($gameParty['isAllDead']()) return;
        (Game_Character[_0x290ce8(0x1f6)]['onPitfallFinish'][_0x290ce8(0xe5)](this),
            this['locate']($gamePlayer['x'], $gamePlayer['y']),
            this[_0x290ce8(0x260)]($gamePlayer[_0x290ce8(0x1da)]()),
            this[_0x290ce8(0x1f7)](),
            this[_0x290ce8(0x21a)]());
        if ($gamePlayer[_0x290ce8(0x258)]) return;
        if (SceneManager[_0x290ce8(0x1ab)]()) {
            const _0x5642eb =
                SceneManager[_0x290ce8(0x263)][_0x290ce8(0x206)]['findTargetSprite'](this);
            _0x5642eb && (_0x5642eb[_0x290ce8(0x189)](), _0x5642eb[_0x290ce8(0x163)]());
        }
    }),
    (Game_Event[_0xf6447d(0x1f6)]['onPitfallFinish'] = function () {
        const _0x259e5e = _0xf6447d;
        (Game_Character[_0x259e5e(0x1f6)][_0x259e5e(0x1ad)][_0x259e5e(0xe5)](this),
            this[_0x259e5e(0x79)]());
    }),
    (Game_Character['prototype'][_0xf6447d(0x22a)] = function () {
        return ![];
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x22a)] = function () {
        const _0x549759 = _0xf6447d;
        return this[_0x549759(0x1b2)](_0x549759(0xa7));
    }),
    (Game_Follower[_0xf6447d(0x1f6)]['canSwimInWater'] = function () {
        const _0x213239 = _0xf6447d;
        return $gamePlayer[_0x213239(0x22a)]();
    }),
    (Game_Character[_0xf6447d(0x1f6)][_0xf6447d(0x203)] = function () {
        const _0x46e8bc = _0xf6447d;
        return (
            this[_0x46e8bc(0x22a)]() &&
            $gameMap[_0x46e8bc(0xde)](this['x'], this['y']) &&
            !$gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])
        );
    }),
    (Game_Character['prototype'][_0xf6447d(0x262)] = function () {
        const _0x2346db = _0xf6447d;
        return Game_CharacterBase[_0x2346db(0xff)];
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['processDrowningEffect'] = function () {
        const _0x42f60c = _0xf6447d;
        if (this['_isDrowning']) return;
        if ($gameMap[_0x42f60c(0xce)](this['x'], this['y'])) return;
        if (this['canSwimInWater']()) return;
        ((this[_0x42f60c(0x1bf)] = !![]),
            SoundManager['playUniqueTileSfx']('swimming', _0x42f60c(0xa5), this));
        const _0x5ee0e0 = SceneManager['_scene'][_0x42f60c(0x206)];
        if (_0x5ee0e0) {
            if (_0x42f60c(0x77) === _0x42f60c(0x77)) {
                const _0x37109f = _0x5ee0e0[_0x42f60c(0xbe)](this);
                if (_0x37109f) {
                    _0x37109f[_0x42f60c(0x220)]();
                    return;
                }
            } else _0x5e9000(this[_0x42f60c(0xc5)][_0x42f60c(0x139)](this), 0x64);
        }
        this[_0x42f60c(0x10b)]();
    }),
    (Game_Follower[_0xf6447d(0x1f6)]['processDrowningEffect'] = function () {
        const _0x133360 = _0xf6447d;
        if ($gamePlayer['_vehicleGettingOn']) return;
        if ($gamePlayer[_0x133360(0x83)]()) return;
        Game_Character[_0x133360(0x1f6)]['processDrowningEffect'][_0x133360(0xe5)](this);
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x10b)] = function () {
        const _0x3fd6de = _0xf6447d;
        setTimeout(this[_0x3fd6de(0x168)]['bind'](this), 0x64);
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x168)] = function () {
        this['_isDrowning'] = ![];
    }),
    (Game_Player['prototype'][_0xf6447d(0x10b)] = function () {
        const _0x10f80d = _0xf6447d;
        $gameParty[_0x10f80d(0x1c3)]();
        if ($gameParty[_0x10f80d(0xa9)]()) {
            if (_0x10f80d(0x130) === _0x10f80d(0x130)) {
                this['_followers'][_0x10f80d(0x138)]();
                return;
            } else this[_0x10f80d(0xc2)]();
        }
        (Game_Character[_0x10f80d(0x1f6)][_0x10f80d(0x10b)][_0x10f80d(0xe5)](this),
            this['gotoLastSafestCoordinate'](),
            this[_0x10f80d(0x10c)]['onDrowningFinish']());
        if (SceneManager['isSceneMap']()) {
            const _0x974b6f =
                SceneManager[_0x10f80d(0x263)][_0x10f80d(0x206)]['findTargetSprite'](this);
            if (_0x974b6f) _0x974b6f[_0x10f80d(0x1d8)]();
        }
    }),
    (Game_Followers[_0xf6447d(0x1f6)]['onDrowningFinish'] = function () {
        const _0x3bbd21 = _0xf6447d;
        for (const _0x55b0f8 of this[_0x3bbd21(0x24a)]) {
            if (!_0x55b0f8) continue;
            _0x55b0f8[_0x3bbd21(0x10b)]();
            if (SceneManager[_0x3bbd21(0x1ab)]()) {
                const _0x414614 =
                    SceneManager[_0x3bbd21(0x263)][_0x3bbd21(0x206)][_0x3bbd21(0xbe)](_0x55b0f8);
                if (_0x414614) _0x414614[_0x3bbd21(0x1d8)]();
            }
        }
    }),
    (Game_Followers[_0xf6447d(0x1f6)][_0xf6447d(0x138)] = function () {
        const _0x21557d = _0xf6447d;
        for (const _0x46d281 of this[_0x21557d(0x24a)]) {
            if (!_0x46d281) continue;
            _0x46d281[_0x21557d(0x1bf)] = !![];
        }
    }),
    (Game_Follower[_0xf6447d(0x1f6)][_0xf6447d(0x10b)] = function () {
        const _0x2577f1 = _0xf6447d;
        if ($gameParty['isAllDead']()) return;
        (Game_Character['prototype'][_0x2577f1(0x10b)]['call'](this),
            this['locate']($gamePlayer['x'], $gamePlayer['y']),
            this[_0x2577f1(0x260)]($gamePlayer['direction']()),
            this[_0x2577f1(0x1f7)](),
            this[_0x2577f1(0x21a)]());
        if ($gamePlayer[_0x2577f1(0x1bf)]) return;
        if (SceneManager[_0x2577f1(0x1ab)]()) {
            const _0x312511 =
                SceneManager[_0x2577f1(0x263)][_0x2577f1(0x206)]['findTargetSprite'](this);
            _0x312511 && (_0x312511[_0x2577f1(0x1d8)](), _0x312511[_0x2577f1(0x163)]());
        }
    }),
    (Game_Event[_0xf6447d(0x1f6)]['onDrowningFinish'] = function () {
        const _0x4a1818 = _0xf6447d;
        (Game_Character['prototype'][_0x4a1818(0x10b)][_0x4a1818(0xe5)](this), this['erase']());
    }),
    (Game_Character[_0xf6447d(0x1f6)][_0xf6447d(0xd8)] = function () {
        const _0x8eff1b = _0xf6447d;
        return (
            this['isUniqueTileAffected']('quicksand') &&
            this[_0x8eff1b(0x16d)]() === 'quicksand' &&
            !$gameMap[_0x8eff1b(0xce)](this['x'], this['y'])
        );
    }),
    (Game_Character['prototype'][_0xf6447d(0x7a)] = function () {
        const _0x1cb45b = _0xf6447d;
        return (
            (this[_0x1cb45b(0xe7)] = this[_0x1cb45b(0xe7)] || 0x0),
            (this[_0x1cb45b(0xe7)] / Game_CharacterBase['QUICKSAND_MAX_STEPS'])['clamp'](0x0, 0x1)
        );
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['updateQuicksand'] = function (_0x344b25) {
        const _0x1a8406 = _0xf6447d;
        _0x344b25 = _0x344b25 || 0x1;
        if (this[_0x1a8406(0xd8)]()) {
            if (_0x1a8406(0x1a2) === 'nGkDe')
                ((this[_0x1a8406(0xe7)] += _0x344b25),
                    this[_0x1a8406(0xe7)] >= Game_CharacterBase[_0x1a8406(0x7e)] &&
                        this[_0x1a8406(0x160)]());
            else {
                if (!this['_character']) return 0x0;
                if (this[_0x1a8406(0x25a)][_0x1a8406(0x203)]()) return this[_0x1a8406(0x14c)]();
                else {
                    if (this[_0x1a8406(0x25a)][_0x1a8406(0xd8)]()) return this[_0x1a8406(0x158)]();
                }
                return 0x0;
            }
        } else {
            if (_0x1a8406(0x229) !== _0x1a8406(0x229)) {
                if (!_0x3b5b8a[_0x1a8406(0x228)](this)) return ![];
                return _0x75419['UniqueTileEffects'][_0x1a8406(0x230)][_0x1a8406(0xe5)](this);
            } else this[_0x1a8406(0xe7)] = 0x0;
        }
    }),
    (Game_CharacterBase['prototype'][_0xf6447d(0x160)] = function () {}),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x160)] = function () {
        const _0x2ca1b1 = _0xf6447d;
        (Input[_0x2ca1b1(0x1ae)](), TouchInput[_0x2ca1b1(0x1ae)](), $gameParty[_0x2ca1b1(0x1c3)]());
        if ($gameParty[_0x2ca1b1(0xa9)]()) {
            if ('EnBxx' === _0x2ca1b1(0xb0)) {
                if (!_0xc8205d) {
                    if (this['canPreventSafestCoordinateRegistration']()) return;
                }
                this[_0x2ca1b1(0x136)] = {
                    x: this['x'],
                    y: this['y'],
                    d: this[_0x2ca1b1(0x1da)](),
                };
            } else return;
        }
        (Game_Character[_0x2ca1b1(0x1f6)][_0x2ca1b1(0x160)][_0x2ca1b1(0xe5)](this),
            this[_0x2ca1b1(0x12d)]());
    }),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x160)] = function () {
        const _0x27d15b = _0xf6447d;
        (Game_Character[_0x27d15b(0x1f6)][_0x27d15b(0x160)][_0x27d15b(0xe5)](this),
            this[_0x27d15b(0x79)]());
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x87)] = function () {
        const _0x2f71f6 = _0xf6447d;
        if (!this[_0x2f71f6(0x26a)](_0x2f71f6(0x135))) return ![];
        if (this[_0x2f71f6(0x144)]()) return ![];
        if (this[_0x2f71f6(0x192)]()) return ![];
        if (this['_erased']) return ![];
        if (this === $gamePlayer) {
            if ($gamePlayer['isInVehicle']()) return ![];
            if ($gamePlayer['isTransferring']()) return ![];
        }
        if (this[_0x2f71f6(0x1bc)] === Game_Follower && $gamePlayer[_0x2f71f6(0x192)]()) return ![];
        if (!this[_0x2f71f6(0x1f2)]()) return ![];
        if (this[_0x2f71f6(0x16d)]() !== 'lava') return ![];
        if ($gameMap['hasBelowPriorityEventsXy'](this['x'], this['y'])) return ![];
        return Graphics[_0x2f71f6(0x16e)] % Game_CharacterBase['LAVA_DELAY'] === 0x0;
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x1db)] = function () {
        const _0x184e0c = _0xf6447d;
        if (this['constructor'] === Game_Follower && !this[_0x184e0c(0x14f)]()) return;
        if (Imported[_0x184e0c(0x84)]) {
            if (_0x184e0c(0x22d) !== _0x184e0c(0x22d)) {
                if (this[_0x184e0c(0x106)](_0x16eae5, _0x430497)) return !![];
                return _0x2e9110[_0x184e0c(0x224)][_0x184e0c(0x268)]['call'](
                    this,
                    _0x227e42,
                    _0x212113
                );
            } else {
                const _0x2cd7b6 = Game_CharacterBase[_0x184e0c(0x10e)];
                if (_0x2cd7b6['id'] <= 0x0) return;
                $gameTemp['requestFauxAnimation'](
                    [this],
                    _0x2cd7b6['id'],
                    _0x2cd7b6['mirror'],
                    _0x2cd7b6[_0x184e0c(0xf6)]
                );
            }
        }
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x1db)] = function () {
        const _0x152b7c = _0xf6447d;
        $gameParty['processUniqueTileDamage']();
        if ($gameParty[_0x152b7c(0xa9)]()) {
            if ('BAzUA' === _0x152b7c(0x137)) {
                if (!this['isDebugThrough']()) {
                    if (this[_0x152b7c(0x91)] === _0xaab296) this[_0x152b7c(0x21a)]();
                    if (this[_0x152b7c(0x91)] > 0x0) return this[_0x152b7c(0x91)];
                }
                return _0x31e30c[_0x152b7c(0x224)][_0x152b7c(0x23a)][_0x152b7c(0xe5)](this);
            } else return;
        }
        Game_Character[_0x152b7c(0x1f6)][_0x152b7c(0x1db)]['call'](this);
    }),
    (Game_Event[_0xf6447d(0x1f6)]['processLavaEffect'] = function () {
        const _0x519c29 = _0xf6447d;
        (Game_Character[_0x519c29(0x1f6)][_0x519c29(0x1db)]['call'](this),
            (this[_0x519c29(0x11a)] = this[_0x519c29(0x11a)] || 0x0),
            this[_0x519c29(0x11a)]++,
            SoundManager[_0x519c29(0x175)](_0x519c29(0x135), 'damage', this),
            this[_0x519c29(0x11a)] >=
                (this[_0x519c29(0x9a)] ?? Game_CharacterBase[_0x519c29(0xc8)]) &&
                (_0x519c29(0x141) === _0x519c29(0x141)
                    ? this[_0x519c29(0x79)]()
                    : (_0x60ec51[_0x519c29(0x224)][_0x519c29(0x11f)][_0x519c29(0xe5)](this),
                      this[_0x519c29(0x20f)]())));
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x1b7)] = function () {
        const _0x30dd02 = _0xf6447d;
        if (Imported[_0x30dd02(0x84)]) {
            const _0x1a5206 = Game_CharacterBase[_0x30dd02(0x184)];
            if (_0x1a5206['id'] <= 0x0) return;
            $gameTemp[_0x30dd02(0x232)](
                [this],
                _0x1a5206['id'],
                _0x1a5206[_0x30dd02(0x239)],
                _0x1a5206['mute']
            );
        }
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x1b7)] = function () {
        const _0xb98b8 = _0xf6447d;
        if (this['_isShocked']) return;
        (Input['clear'](),
            TouchInput[_0xb98b8(0x1ae)](),
            $gameParty[_0xb98b8(0x1c3)](),
            Game_Character['prototype'][_0xb98b8(0x1b7)][_0xb98b8(0xe5)](this));
        if ($gameParty[_0xb98b8(0xa9)]()) {
            this[_0xb98b8(0x1c9)] = !![];
            return;
        }
        (this['gotoLastSafestCoordinate'](),
            (this['_shockEffectDelay'] = Game_CharacterBase[_0xb98b8(0xdc)]));
    }),
    (Game_Follower[_0xf6447d(0x1f6)][_0xf6447d(0x1b7)] = function () {}),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x1b7)] = function () {
        const _0x17095e = _0xf6447d;
        if (this[_0x17095e(0x1c9)]) return;
        (Game_Character[_0x17095e(0x1f6)][_0x17095e(0x1b7)][_0x17095e(0xe5)](this),
            SoundManager['playUniqueTileSfx'](_0x17095e(0x20d), _0x17095e(0x1f0), this),
            (this[_0x17095e(0x1c9)] = !![]),
            setTimeout(this[_0x17095e(0x79)]['bind'](this), 0x64));
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)]['processBounceEffect'] = function () {
        const _0x1c23a6 = _0xf6447d,
            _0x1d68de = this[_0x1c23a6(0x1e7)]();
        let _0x142473 = 0x0,
            _0x446601 = 0x0;
        const _0x200ee5 = this[_0x1c23a6(0x143)]();
        if ([0x7, 0x1][_0x1c23a6(0x1d6)](_0x200ee5)) this[_0x1c23a6(0x260)](0x4);
        if ([0x9, 0x3][_0x1c23a6(0x1d6)](_0x200ee5)) this[_0x1c23a6(0x260)](0x6);
        switch (_0x200ee5) {
            case 0x2:
                _0x446601 = _0x1d68de;
                break;
            case 0x4:
                _0x142473 = -_0x1d68de;
                break;
            case 0x6:
                _0x142473 = _0x1d68de;
                break;
            case 0x8:
                _0x446601 = -_0x1d68de;
                break;
        }
        (this[_0x1c23a6(0x217)](_0x142473, _0x446601),
            (_0x142473 !== 0x0 || _0x446601 !== 0x0) &&
                (_0x1c23a6(0xea) !== _0x1c23a6(0xea)
                    ? ((this[_0x1c23a6(0x24c)] = 0x0),
                      (this['_drowningDuration'] = 0x0),
                      (this[_0x1c23a6(0x96)] = 0x0))
                    : SoundManager[_0x1c23a6(0x175)](_0x1c23a6(0x1a4), _0x1c23a6(0xa5), this)));
    }),
    (Game_Follower[_0xf6447d(0x1f6)][_0xf6447d(0x150)] = function () {}),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x143)] = function () {
        const _0x52196e = _0xf6447d;
        return this[_0x52196e(0x246)] || this[_0x52196e(0x1da)]();
    }),
    (Game_CharacterBase[_0xf6447d(0x1f6)][_0xf6447d(0x1e7)] = function () {
        const _0xe7e994 = _0xf6447d,
            _0x13373a = $gameMap[_0xe7e994(0x11e)](this['x'], this['y'], !![]);
        return [
            'bounce0',
            _0xe7e994(0x1ec),
            'bounce2',
            _0xe7e994(0x118),
            _0xe7e994(0xe8),
            _0xe7e994(0x191),
            _0xe7e994(0x177),
            _0xe7e994(0xcd),
            _0xe7e994(0x208),
            _0xe7e994(0x164),
        ][_0xe7e994(0x1d7)](_0x13373a);
    }),
    (Game_Player['prototype'][_0xf6447d(0x10a)] = function () {
        const _0x10dbea = _0xf6447d;
        (Game_Character[_0x10dbea(0x1f6)][_0x10dbea(0x10a)]['call'](this),
            (this[_0x10dbea(0x1c8)] = {}),
            (this['_uniqueTilePartyImmune'] = {}));
    }),
    (Game_Player['prototype'][_0xf6447d(0x1b2)] = function (_0x5e981e) {
        const _0x59e56b = _0xf6447d;
        if (this[_0x59e56b(0x1c8)] === undefined) this[_0x59e56b(0x10a)]();
        if (this[_0x59e56b(0x8a)] === undefined) this[_0x59e56b(0xa3)]();
        return this[_0x59e56b(0x1c8)][_0x5e981e] || this[_0x59e56b(0x8a)][_0x5e981e];
    }),
    (Game_Player[_0xf6447d(0x1f6)]['setImmuneToUniqueTileType'] = function (_0x7d2fe9, _0x5867d4) {
        const _0xbeac6d = _0xf6447d;
        if (this[_0xbeac6d(0x1c8)] === undefined) this[_0xbeac6d(0x10a)]();
        ((this[_0xbeac6d(0x1c8)][_0x7d2fe9] = _0x5867d4), this[_0xbeac6d(0x1b9)]());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x11f)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x1b9)]),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x1b9)] = function () {
        const _0x189e81 = _0xf6447d;
        (VisuMZ[_0x189e81(0x224)]['Game_Player_refresh']['call'](this),
            this['clearUniqueTilePartyImmunity']());
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x20f)] = function () {
        const _0x30a385 = _0xf6447d;
        this[_0x30a385(0x8a)] = undefined;
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0xa3)] = function () {
        const _0x48823a = _0xf6447d;
        this[_0x48823a(0x8a)] = {};
        const _0x5b402a = VisuMZ[_0x48823a(0x224)][_0x48823a(0x1ba)],
            _0x3c4dc2 = VisuMZ[_0x48823a(0x224)][_0x48823a(0x24f)]();
        for (const _0x14744b of _0x3c4dc2) {
            if (!_0x5b402a[_0x14744b]) continue;
            const _0x47ea1f = _0x5b402a[_0x14744b][_0x48823a(0x9d)];
            _0x47ea1f &&
                (this['_uniqueTilePartyImmune'][_0x14744b] =
                    $gameParty[_0x48823a(0x134)](_0x47ea1f));
        }
    }),
    (Game_Party['prototype'][_0xf6447d(0x134)] = function (_0x5e710d) {
        const _0x2a5357 = _0xf6447d;
        if (
            this[_0x2a5357(0xa8)]()[_0x2a5357(0xe3)](
                _0x3af314 => _0x3af314 && _0x3af314[_0x2a5357(0x1ee)]['match'](_0x5e710d)
            )
        )
            return !![];
        const _0x4d88d8 = Game_Party[_0x2a5357(0xd9)][_0x2a5357(0x1c5)][_0x2a5357(0x1a8)],
            _0x211e45 = _0x4d88d8 ? this[_0x2a5357(0x259)]() : this[_0x2a5357(0xaf)]();
        for (const _0x273617 of _0x211e45) {
            if (_0x2a5357(0x1dc) !== _0x2a5357(0x1dc)) {
                const _0x2e5bcb = _0xe1e6b1[_0x2a5357(0x184)];
                if (_0x2e5bcb['id'] <= 0x0) return;
                _0x54febe[_0x2a5357(0x232)](
                    [this],
                    _0x2e5bcb['id'],
                    _0x2e5bcb['mirror'],
                    _0x2e5bcb['mute']
                );
            } else {
                if (!_0x273617) continue;
                if (
                    _0x273617['equips']()[_0x2a5357(0xe3)](
                        _0x5128f8 => _0x5128f8 && _0x5128f8['note'][_0x2a5357(0x171)](_0x5e710d)
                    )
                )
                    return !![];
            }
        }
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x78)] = Game_Party['prototype'][_0xf6447d(0x21e)]),
    (Game_Party[_0xf6447d(0x1f6)][_0xf6447d(0x21e)] = function (_0x529e73, _0x48561e, _0x2a5a16) {
        const _0x3b965a = _0xf6447d;
        (VisuMZ['UniqueTileEffects'][_0x3b965a(0x78)]['call'](
            this,
            _0x529e73,
            _0x48561e,
            _0x2a5a16
        ),
            $gamePlayer[_0x3b965a(0x20f)]());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x147)] = Game_Actor['prototype'][_0xf6447d(0x1c7)]),
    (Game_Actor[_0xf6447d(0x1f6)][_0xf6447d(0x1c7)] = function (_0x28f029, _0x5cd128) {
        const _0x455d96 = _0xf6447d;
        (VisuMZ[_0x455d96(0x224)]['Game_Actor_changeEquip'][_0x455d96(0xe5)](
            this,
            _0x28f029,
            _0x5cd128
        ),
            $gamePlayer[_0x455d96(0x20f)]());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x218)] = Game_Actor['prototype'][_0xf6447d(0x1e1)]),
    (Game_Actor[_0xf6447d(0x1f6)][_0xf6447d(0x1e1)] = function (_0x2c5b50, _0xfcafe2) {
        const _0x3789d4 = _0xf6447d;
        (VisuMZ['UniqueTileEffects'][_0x3789d4(0x218)]['call'](this, _0x2c5b50, _0xfcafe2),
            $gamePlayer['clearUniqueTilePartyImmunity']());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1b1)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x24b)]),
    (Game_Player[_0xf6447d(0x1f6)]['locate'] = function (_0x29c69a, _0xfb9d1e) {
        const _0x19863a = _0xf6447d;
        (VisuMZ[_0x19863a(0x224)]['Game_Player_locate'][_0x19863a(0xe5)](
            this,
            _0x29c69a,
            _0xfb9d1e
        ),
            this[_0x19863a(0x10d)](),
            this['registerLastSafestCoordinate'](!![]));
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xf2)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x13b)]),
    (Game_Player['prototype'][_0xf6447d(0x13b)] = function () {
        const _0x3e20ce = _0xf6447d;
        (VisuMZ['UniqueTileEffects']['Game_Player_increaseSteps'][_0x3e20ce(0xe5)](this),
            this[_0x3e20ce(0x111)]());
    }),
    (Game_Player['prototype'][_0xf6447d(0x10d)] = function () {
        this['_lastUniqueTileCoordinate'] = { x: this['x'], y: this['y'] };
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x22e)] = function () {
        const _0x3af0e3 = _0xf6447d;
        return this[_0x3af0e3(0x1f3)] || { x: this['x'], y: this['y'] };
    }),
    (Game_Player[_0xf6447d(0x1f6)]['registerLastSafestCoordinate'] = function (_0x59e12c) {
        const _0x19b3d7 = _0xf6447d;
        if (!_0x59e12c) {
            if (this[_0x19b3d7(0x221)]()) return;
        }
        this[_0x19b3d7(0x136)] = { x: this['x'], y: this['y'], d: this['direction']() };
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x221)] = function () {
        const _0x4ce42b = _0xf6447d;
        if (this[_0x4ce42b(0x1ac)]()) return !![];
        if (!$gameMap['isRawPassableByAnyDirection'](this['x'], this['y'])) return !![];
        if ($gameMap['isDamageFloor'](this['x'], this['y'])) return !![];
        if ($gameMap[_0x4ce42b(0x1a9)](this['x'], this['y'])) return !![];
        if ($gameMap[_0x4ce42b(0x248)](this['x'], this['y'])) return !![];
        return ![];
    }),
    (Game_Player[_0xf6447d(0x1f6)]['gotoLastSafestCoordinate'] = function () {
        const _0x1efcd8 = _0xf6447d;
        if (this[_0x1efcd8(0x136)] === undefined) return;
        (this[_0x1efcd8(0x260)](this['_lastSafestCoordinate']['d'] || this[_0x1efcd8(0x1da)]()),
            this['locate'](
                this[_0x1efcd8(0x136)]['x'] ?? this['x'],
                this[_0x1efcd8(0x136)]['y'] ?? this['y']
            ),
            this[_0x1efcd8(0x88)]());
    }),
    (Game_Player[_0xf6447d(0x1f6)]['clearUniqueTileSpriteEffects'] = function () {
        const _0x1a3403 = _0xf6447d;
        if (!SceneManager[_0x1a3403(0x1ab)]()) return;
        const _0x12ea92 = SceneManager[_0x1a3403(0x263)]['_spriteset'];
        if (!_0x12ea92) return;
        {
            const _0x300302 = _0x12ea92['findTargetSprite'](this);
            if (_0x300302) _0x300302['clearUniqueTileSpriteEffects']();
        }
        for (const _0x16e471 of this[_0x1a3403(0x10c)][_0x1a3403(0x183)]()) {
            if (_0x1a3403(0xfe) === _0x1a3403(0xfe)) {
                const _0x43a027 = _0x12ea92[_0x1a3403(0xbe)](_0x16e471);
                if (_0x43a027) _0x43a027[_0x1a3403(0x88)]();
            } else return _0x417822[_0x1a3403(0x26a)](_0x1a3403(0xa7));
        }
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x167)] = function (_0xa5e7dd) {
        const _0x13125c = _0xf6447d,
            _0x477526 = $gameMap[_0x13125c(0x1b8)][_0xa5e7dd];
        if (!_0x477526) return ![];
        const _0x5815bc = _0x477526[0x0] || $gameMap['mapId'](),
            _0x282b67 = _0x477526[0x1] ?? this['x'],
            _0x39e95b = _0x477526[0x2] ?? this['y'];
        if (
            _0x5815bc === $gameMap[_0x13125c(0x1a1)]() &&
            _0x282b67 === this['x'] &&
            _0x39e95b === this['y']
        )
            return ![];
        return (
            this[_0x13125c(0x193)](_0x5815bc, _0x282b67, _0x39e95b, this[_0x13125c(0x1da)](), 0x0),
            !![]
        );
    }),
    (Game_Event[_0xf6447d(0x207)] = {
        slippery: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1ff)][_0xf6447d(0x1fe)],
        forceMove: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x1a6)][_0xf6447d(0x1fe)],
        pitfall: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x235)]['DefaultAffected'],
        swimming:
            VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x197)]['DefaultAffected'],
        quicksand: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x1fe)],
        lava: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x1fe)],
        shock: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Shock'][_0xf6447d(0x1fe)],
        bounce: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)][_0xf6447d(0x1fe)],
    }),
    (Game_Event[_0xf6447d(0x174)] = {
        slippery: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Slippery'][_0xf6447d(0x113)],
        forceMove: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x1a6)][_0xf6447d(0x113)],
        pitfall: VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x235)]['DefaultAvoid'],
        swimming: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x113)],
        quicksand: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Quicksand'][_0xf6447d(0x113)],
        lava: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x113)],
        shock: VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x244)]['DefaultAvoid'],
        bounce: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x15a)][_0xf6447d(0x113)],
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1fb)] =
        Game_Event[_0xf6447d(0x1f6)]['clearPageSettings']),
    (Game_Event[_0xf6447d(0x1f6)]['clearPageSettings'] = function () {
        const _0x2d2f56 = _0xf6447d;
        (VisuMZ[_0x2d2f56(0x224)][_0x2d2f56(0x1fb)][_0x2d2f56(0xe5)](this),
            this[_0x2d2f56(0x1fc)]());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1a7)] = Game_Event['prototype'][_0xf6447d(0x12e)]),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x12e)] = function () {
        const _0x30b7a9 = _0xf6447d;
        (VisuMZ['UniqueTileEffects'][_0x30b7a9(0x1a7)][_0x30b7a9(0xe5)](this),
            this[_0x30b7a9(0x1ed)]());
    }),
    (Game_Event['prototype'][_0xf6447d(0x1ed)] = function () {
        const _0x3f5958 = _0xf6447d;
        if (!this[_0x3f5958(0x8f)]()) return;
        (this[_0x3f5958(0x1fc)](), this[_0x3f5958(0x7d)](), this[_0x3f5958(0x7c)]());
    }),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x7d)] = function () {
        const _0x526795 = _0xf6447d,
            _0x57f48b = this[_0x526795(0x8f)]()[_0x526795(0x1ee)];
        if (_0x57f48b === '') return;
        this[_0x526795(0xdf)](_0x57f48b);
    }),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x7c)] = function () {
        const _0x59a678 = _0xf6447d;
        if (!this[_0x59a678(0x9f)]()) return;
        const _0x34a9e5 = this['list']();
        let _0x52c685 = '';
        for (const _0x3855aa of _0x34a9e5) {
            if ([0x6c, 0x198]['includes'](_0x3855aa[_0x59a678(0x21b)])) {
                if (_0x52c685 !== '') _0x52c685 += '\x0a';
                _0x52c685 += _0x3855aa[_0x59a678(0xdd)][0x0];
            }
        }
        this[_0x59a678(0xdf)](_0x52c685);
    }),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x1fc)] = function () {
        const _0x546119 = _0xf6447d;
        ((this[_0x546119(0x245)] = {}), (this[_0x546119(0x7f)] = {}));
        const _0x6f6ae = VisuMZ[_0x546119(0x224)][_0x546119(0x24f)]();
        for (const _0xa40282 of _0x6f6ae) {
            _0x546119(0x1d5) === _0x546119(0x1d5)
                ? ((this['_uniqueTileAffected'][_0xa40282] =
                      Game_Event[_0x546119(0x207)][_0xa40282] || ![]),
                  (this[_0x546119(0x7f)][_0xa40282] =
                      Game_Event[_0x546119(0x174)][_0xa40282] || ![]))
                : this[_0x546119(0x17c)]();
        }
        this['_lavaBurnMax'] = Game_CharacterBase[_0x546119(0xc8)] || 0x1;
    }),
    (Game_Event[_0xf6447d(0x1f6)]['checkUniqueTileEffectsStringTags'] = function (_0x592695) {
        const _0x4267ca = _0xf6447d,
            _0x91c113 = VisuMZ['UniqueTileEffects'][_0x4267ca(0x1ba)],
            _0x343304 = VisuMZ[_0x4267ca(0x224)]['TileTypes']();
        for (const _0x8032e5 of _0x343304) {
            if (!_0x91c113[_0x8032e5]) continue;
            _0x91c113[_0x8032e5]['affected'] &&
                _0x592695[_0x4267ca(0x171)](_0x91c113[_0x8032e5][_0x4267ca(0x1c2)]) &&
                (this[_0x4267ca(0x245)][_0x8032e5] = !![]);
            _0x91c113[_0x8032e5][_0x4267ca(0x1a3)] &&
                _0x592695['match'](_0x91c113[_0x8032e5][_0x4267ca(0x1a3)]) &&
                (this[_0x4267ca(0x7f)][_0x8032e5] = !![]);
            if (
                _0x91c113[_0x8032e5][_0x4267ca(0x19a)] &&
                _0x592695[_0x4267ca(0x171)](_0x91c113[_0x8032e5]['ignore'])
            ) {
                if (_0x4267ca(0x21c) === _0x4267ca(0x21c))
                    this['_uniqueTileAvoid'][_0x8032e5] = ![];
                else {
                    if (this['isDebugThrough']()) return ![];
                    if (this[_0x4267ca(0x22a)]()) {
                        if (this[_0x4267ca(0x203)]()) return this[_0x4267ca(0x19f)]();
                        if (!this['isSwimming']() && this[_0x4267ca(0x15e)]()) return !![];
                    }
                    const _0xa8703a = this[_0x4267ca(0x22e)](),
                        _0x21bb68 = _0x38f8b4[_0x4267ca(0x11e)](_0xa8703a['x'], _0xa8703a['y']),
                        _0x52e84f = this[_0x4267ca(0x26a)](_0x21bb68),
                        _0x307a19 = _0x26ee98[_0x4267ca(0xce)](_0xa8703a['x'], _0xa8703a['y']);
                    if (
                        !this[_0x4267ca(0x1f2)]() ||
                        _0xc5ceb8[_0x4267ca(0xce)](this['x'], this['y'])
                    )
                        return _0x52e84f && !_0x307a19;
                    return ![];
                }
            }
        }
        if (_0x592695[_0x4267ca(0x171)](_0x91c113[_0x4267ca(0x135)][_0x4267ca(0x1e4)])) {
            if (_0x4267ca(0x131) === _0x4267ca(0x131))
                this[_0x4267ca(0x9a)] = Math['max'](Number(RegExp['$1']), 0x1);
            else {
                _0x240e91[_0x4267ca(0x1c3)]();
                if (_0x3053b3[_0x4267ca(0xa9)]()) {
                    this[_0x4267ca(0x10c)][_0x4267ca(0x138)]();
                    return;
                }
                (_0xc117fa[_0x4267ca(0x1f6)][_0x4267ca(0x10b)]['call'](this),
                    this['gotoLastSafestCoordinate'](),
                    this['_followers'][_0x4267ca(0x10b)]());
                if (_0xb487b9[_0x4267ca(0x1ab)]()) {
                    const _0x5ab7c5 =
                        _0x4892b4[_0x4267ca(0x263)]['_spriteset'][_0x4267ca(0xbe)](this);
                    if (_0x5ab7c5) _0x5ab7c5[_0x4267ca(0x1d8)]();
                }
            }
        }
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1e3)] = Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x101)]),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x101)] = function () {
        const _0x3f9365 = _0xf6447d;
        if (this[_0x3f9365(0x258)]) return;
        if (this[_0x3f9365(0x1bf)]) return;
        ((this[_0x3f9365(0x1f9)] = !![]),
            VisuMZ[_0x3f9365(0x224)][_0x3f9365(0x1e3)]['call'](this),
            (this[_0x3f9365(0x1f9)] = undefined));
    }),
    (Game_Event[_0xf6447d(0x1f6)]['isUniqueTileAffected'] = function (_0x730456) {
        const _0xccdd3 = _0xf6447d;
        if (this[_0xccdd3(0x1f9)]) {
            if (_0xccdd3(0x155) !== _0xccdd3(0x155)) return !![];
            else {
                if (this['_uniqueTileAvoid'] === undefined) {
                    if (_0xccdd3(0x195) === _0xccdd3(0x195)) this[_0xccdd3(0x1ed)]();
                    else return !![];
                }
                if (this[_0xccdd3(0x126)](_0x730456)) return ![];
            }
        }
        return Game_Character[_0xccdd3(0x1f6)][_0xccdd3(0x26a)][_0xccdd3(0xe5)](this, _0x730456);
    }),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x126)] = function (_0x109df1) {
        const _0x3df994 = _0xf6447d;
        return (
            this['_uniqueTileAvoid'] === undefined && this[_0x3df994(0x1ed)](),
            this[_0x3df994(0x7f)][_0x109df1]
        );
    }),
    (Game_Party[_0xf6447d(0xd9)] = {
        general: {
            immuneReserveParty: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['NotetagPartyWide'],
        },
        pitfall: {
            deathAllow:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0x22c)] ?? !![],
            dmgRate: VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x235)]['DmgRate'] ?? 0.2,
            dmgFlat:
                VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)]['Pitfall'][_0xf6447d(0xe4)] ?? 0x14,
        },
        swimming: {
            deathAllow:
                VisuMZ[_0xf6447d(0x224)]['Settings'][_0xf6447d(0x197)][_0xf6447d(0x22c)] ?? !![],
            dmgRate:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x1f8)] ??
                0.1,
            dmgFlat: VisuMZ[_0xf6447d(0x224)]['Settings']['Swimming']['DmgFlat'] ?? 0xf,
        },
        quicksand: {
            deathAllow:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x22c)] ??
                !![],
            dmgRate:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0x1f8)] ??
                0.3,
            dmgFlat:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x9b)][_0xf6447d(0xe4)] ??
                0x28,
        },
        lava: {
            deathAllow:
                VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0xd4)][_0xf6447d(0x22c)] ?? !![],
            dmgRate:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0x1f8)] ??
                0.05,
            dmgFlat:
                VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0xd4)][_0xf6447d(0xe4)] ??
                0x32,
        },
        shock: {
            deathAllow:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x244)][_0xf6447d(0x22c)] ??
                !![],
            dmgRate:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x244)]['DmgRate'] ?? 0.25,
            dmgFlat:
                VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x110)][_0xf6447d(0x244)][_0xf6447d(0xe4)] ??
                0x3c,
        },
    }),
    (Game_Party['prototype'][_0xf6447d(0x1c3)] = function () {
        const _0xa88a2d = _0xf6447d,
            _0x121404 = $gamePlayer[_0xa88a2d(0x16d)]();
        SoundManager[_0xa88a2d(0x175)](_0x121404, 'damage', $gamePlayer);
        for (const _0x4c9525 of this['aliveMembers']()) {
            if (!_0x4c9525) continue;
            _0x4c9525[_0xa88a2d(0x1c3)](_0x121404);
        }
    }),
    (Game_Actor[_0xf6447d(0x1f6)][_0xf6447d(0x1c3)] = function (_0x16df0b) {
        const _0x5791d2 = _0xf6447d,
            _0xd5d15a = Game_Party[_0x5791d2(0xd9)][_0x16df0b];
        if (!_0xd5d15a) return;
        let _0x184108 = 0x0;
        ((_0x184108 += _0xd5d15a['dmgRate'] * this[_0x5791d2(0x92)]),
            (_0x184108 += _0xd5d15a['dmgFlat']),
            (_0x184108 = Math[_0x5791d2(0x1ef)](_0x184108)));
        !_0xd5d15a['deathAllow'] && (_0x184108 = Math['min'](_0x184108, this['hp'] - 0x1));
        _0x184108 > 0x0 &&
            (($gameTemp[_0x5791d2(0x86)] = !![]),
            this[_0x5791d2(0x25e)](),
            ($gameTemp['_executeFloorDamage'] = ![]));
        this[_0x5791d2(0x1ca)](-_0x184108);
        if (this[_0x5791d2(0x107)]()) this[_0x5791d2(0x23e)]();
    }),
    (Sprite_Character[_0xf6447d(0xba)] =
        VisuMZ['UniqueTileEffects']['Settings'][_0xf6447d(0x235)]['EffectDuration'] ?? 0x14),
    (Sprite_Character[_0xf6447d(0x159)] =
        VisuMZ['UniqueTileEffects'][_0xf6447d(0x110)][_0xf6447d(0x197)][_0xf6447d(0x166)] ?? 0x1e),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1b5)] = Sprite_Character['prototype'][_0xf6447d(0x12f)]),
    (Sprite_Character['prototype'][_0xf6447d(0x12f)] = function () {
        const _0x3e7272 = _0xf6447d;
        (VisuMZ[_0x3e7272(0x224)][_0x3e7272(0x1b5)]['call'](this), this[_0x3e7272(0x20c)]());
    }),
    (Sprite_Character['prototype'][_0xf6447d(0x20c)] = function () {
        const _0x1ceaa4 = _0xf6447d;
        ((this[_0x1ceaa4(0x24c)] = 0x0),
            (this[_0x1ceaa4(0x257)] = 0x0),
            (this['_uniqueTileFrameMinusY'] = 0x0));
    }),
    (Sprite_Character['prototype'][_0xf6447d(0x88)] = function () {
        const _0x38de77 = _0xf6447d;
        ((this[_0x38de77(0x96)] = 0x0), this['update']());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x250)] =
        Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x163)]),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x163)] = function () {
        const _0x13fc18 = _0xf6447d;
        (this['updatePitfallEffect'](),
            VisuMZ['UniqueTileEffects']['Sprite_Character_update'][_0x13fc18(0xe5)](this),
            this['updateDrowningEffect']());
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1e9)] = Sprite_Character['prototype'][_0xf6447d(0xfc)]),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0xfc)] = function () {
        const _0x9c91ea = _0xf6447d;
        (this[_0x9c91ea(0x23b)](),
            this[_0x9c91ea(0x96)] > 0x0
                ? this[_0x9c91ea(0x1f1)]()
                : (VisuMZ[_0x9c91ea(0x224)][_0x9c91ea(0x1e9)][_0x9c91ea(0xe5)](this),
                  this[_0x9c91ea(0x1c0)]()));
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x23b)] = function () {
        const _0x31063d = _0xf6447d,
            _0x5e5d5d = this[_0x31063d(0x1e6)](),
            _0x22d004 = 0x1;
        if (this['_uniqueTileFrameMinusY'] > _0x5e5d5d) {
            if (_0x31063d(0x128) === _0x31063d(0x19e)) {
                if (this[_0x31063d(0x16d)]() === _0x31063d(0x237))
                    return _0x5336ab[_0x31063d(0xce)](this['x'], this['y']);
                if (this[_0x31063d(0x16d)]() === _0x31063d(0xa7))
                    return _0x6a06a7[_0x31063d(0xce)](this['x'], this['y']);
                return ![];
            } else
                this['_uniqueTileFrameMinusY'] = Math[_0x31063d(0x146)](
                    this[_0x31063d(0x96)] - _0x22d004 * 0x4,
                    _0x5e5d5d
                );
        } else
            this['_uniqueTileFrameMinusY'] < _0x5e5d5d
                ? (this['_uniqueTileFrameMinusY'] = Math[_0x31063d(0xb4)](
                      this[_0x31063d(0x96)] + _0x22d004,
                      _0x5e5d5d
                  ))
                : 'DVxDt' === _0x31063d(0x129)
                  ? ((this['_uniqueTileAffected'][_0x20ed8b] =
                        _0x285435[_0x31063d(0x207)][_0x52e87f] || ![]),
                    (this[_0x31063d(0x7f)][_0x17099a] =
                        _0x3db655[_0x31063d(0x174)][_0x551613] || ![]))
                  : (this[_0x31063d(0x96)] = _0x5e5d5d);
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x1e6)] = function () {
        const _0x881c98 = _0xf6447d;
        if (!this[_0x881c98(0x25a)]) return 0x0;
        if (this[_0x881c98(0x25a)][_0x881c98(0x203)]())
            return this['getUniqueTileCharaSwimFrameTarget']();
        else {
            if (this['_character'][_0x881c98(0xd8)]())
                return this['getUniqueTileCharaQuicksandFrameTarget']();
        }
        return 0x0;
    }),
    (Sprite_Character['prototype'][_0xf6447d(0xb7)] = function () {
        const _0xb8a4c9 = _0xf6447d;
        if (this[_0xb8a4c9(0x24c)] <= 0x0) return;
        const _0x28ba98 = this[_0xb8a4c9(0x24c)];
        ((this[_0xb8a4c9(0x1b3)]['x'] =
            (this[_0xb8a4c9(0x1b3)]['x'] * (_0x28ba98 - 0x1)) / _0x28ba98),
            (this['scale']['y'] = (this['scale']['y'] * (_0x28ba98 - 0x1)) / _0x28ba98),
            this[_0xb8a4c9(0x24c)]--);
        if (this[_0xb8a4c9(0x24c)] <= 0x0) {
            if (this[_0xb8a4c9(0x25a)]) this[_0xb8a4c9(0x25a)]['onPitfallFinish']();
            this['finishPitfallEffect']();
        }
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x133)] = function () {
        const _0x13ecaa = _0xf6447d;
        if ($gameParty[_0x13ecaa(0xa9)]()) return;
        ((this[_0x13ecaa(0x24c)] = Sprite_Character[_0x13ecaa(0xba)]),
            (this[_0x13ecaa(0x249)] = {
                scaleX: this[_0x13ecaa(0x1b3)]['x'],
                scaleY: this['scale']['y'],
                shadowScaleX: this[_0x13ecaa(0xd5)] ? this[_0x13ecaa(0xd5)]['scale']['x'] : 0x0,
                shadowScaleY: this[_0x13ecaa(0xd5)] ? this[_0x13ecaa(0xd5)]['scale']['y'] : 0x0,
            }));
    }),
    (Sprite_Character[_0xf6447d(0x1f6)]['finishPitfallEffect'] = function () {
        const _0x165520 = _0xf6447d;
        this[_0x165520(0x24c)] = 0x0;
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x189)] = function () {
        const _0x5e0be2 = _0xf6447d;
        this[_0x5e0be2(0x24c)] = 0x0;
        if (!this[_0x5e0be2(0x249)]) return;
        ((this[_0x5e0be2(0x1b3)]['x'] = this[_0x5e0be2(0x249)][_0x5e0be2(0x17b)]),
            (this[_0x5e0be2(0x1b3)]['y'] = this[_0x5e0be2(0x249)][_0x5e0be2(0xef)]),
            this[_0x5e0be2(0xd5)] &&
                (_0x5e0be2(0xfa) === 'osXyY'
                    ? ((this[_0x5e0be2(0xd5)][_0x5e0be2(0x1b3)]['x'] =
                          this[_0x5e0be2(0x249)]['shadowScaleX']),
                      (this['_shadowSprite'][_0x5e0be2(0x1b3)]['y'] =
                          this[_0x5e0be2(0x249)][_0x5e0be2(0x21f)]))
                    : this[_0x5e0be2(0x160)]()),
            (this['_pitfallData'] = undefined));
    }),
    (Sprite_Character[_0xf6447d(0x1f6)]['updateCharacterFrameDrowning'] = function () {
        const _0x1c955b = _0xf6447d;
        if (this[_0x1c955b(0xb2)] !== undefined) {
            if (_0x1c955b(0x7b) === _0x1c955b(0x1d3))
                (_0x4c291c[_0x1c955b(0xb9)](_0x1c955b(0x21d)), _0x423dcc['push'](_0x1c955b(0x1a4)));
            else {
                const _0x3e353e = Math['floor'](
                    this[_0x1c955b(0x98)][_0x1c955b(0x1f4)] * this[_0x1c955b(0xb2)]
                );
                ((this['_frame']['height'] = _0x3e353e), this['_refresh']());
            }
        }
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0xc0)] = function () {
        const _0x55bd2d = _0xf6447d;
        if (this[_0x55bd2d(0x257)] <= 0x0) return;
        const _0x73f683 = this['_drowningDuration'];
        ((this[_0x55bd2d(0xb2)] = (this[_0x55bd2d(0xb2)] * (_0x73f683 - 0x1)) / _0x73f683),
            this[_0x55bd2d(0xfc)](),
            this['_drowningDuration']--);
        if (this[_0x55bd2d(0x257)] <= 0x0) {
            if (this[_0x55bd2d(0x25a)]) this[_0x55bd2d(0x25a)]['onDrowningFinish']();
            this[_0x55bd2d(0x169)]();
        }
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x220)] = function () {
        const _0x21eb5c = _0xf6447d;
        if ($gameParty[_0x21eb5c(0xa9)]()) return;
        ((this[_0x21eb5c(0x257)] = Sprite_Character[_0x21eb5c(0x159)]),
            (this[_0x21eb5c(0xb2)] = 0x1));
    }),
    (Sprite_Character[_0xf6447d(0x1f6)]['finishDrowningEffect'] = function () {
        this['_drowningDuration'] = 0x0;
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x1d8)] = function () {
        const _0x577b63 = _0xf6447d;
        ((this[_0x577b63(0x257)] = 0x0),
            (this[_0x577b63(0xb2)] = undefined),
            this[_0x577b63(0xfc)]());
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x14c)] = function () {
        const _0x170302 = _0xf6447d;
        return this[_0x170302(0x25a)][_0x170302(0x262)]();
    }),
    (Sprite_Character[_0xf6447d(0x1f6)]['updateCharacterSwimmingFrame'] = function () {
        const _0x2b50e1 = _0xf6447d,
            _0x1c80b7 = this['patternWidth'](),
            _0x321ea1 = this[_0x2b50e1(0x247)](),
            _0x867af3 = (this[_0x2b50e1(0x18c)]() + this[_0x2b50e1(0x227)]()) * _0x1c80b7,
            _0x2b2b1d = (this[_0x2b50e1(0x200)]() + this[_0x2b50e1(0x112)]()) * _0x321ea1;
        this[_0x2b50e1(0x242)](_0x867af3, _0x2b2b1d, _0x1c80b7, _0x321ea1 - this[_0x2b50e1(0x96)]);
    }),
    (Sprite_Character[_0xf6447d(0x1f6)][_0xf6447d(0x158)] = function () {
        const _0x1b57d5 = _0xf6447d;
        return Math[_0x1b57d5(0x176)](
            this[_0x1b57d5(0x25a)][_0x1b57d5(0x7a)]() * this[_0x1b57d5(0x247)]()
        );
    }));
Imported[_0xf6447d(0xe2)] &&
    ((Game_Player[_0xf6447d(0x1f6)]['canUniqueTileEndSmartAction'] = function () {
        const _0x5a4a4d = _0xf6447d;
        if (this[_0x5a4a4d(0x1f2)]()) {
            if (this['getUniqueTileType']() === _0x5a4a4d(0x237))
                return $gameMap[_0x5a4a4d(0xce)](this['x'], this['y']);
            if (this['getUniqueTileType']() === _0x5a4a4d(0xa7))
                return _0x5a4a4d(0x261) !== _0x5a4a4d(0x261)
                    ? !![]
                    : $gameMap[_0x5a4a4d(0xce)](this['x'], this['y']);
            return ![];
        }
        return !![];
    }),
    (VisuMZ['UniqueTileEffects']['Game_Player_canSmartRush'] =
        Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x25b)]),
    (Game_Player['prototype']['canSmartRush'] = function () {
        const _0x48f41d = _0xf6447d;
        if (!this[_0x48f41d(0x26b)]()) return ![];
        return VisuMZ['UniqueTileEffects'][_0x48f41d(0x1b0)][_0x48f41d(0xe5)](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xeb)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x212)]),
    (Game_Player[_0xf6447d(0x1f6)]['canSmartBlink'] = function (_0x5921d4) {
        const _0x180f77 = _0xf6447d;
        if (!this[_0x180f77(0x26b)]()) return ![];
        return VisuMZ[_0x180f77(0x224)][_0x180f77(0xeb)][_0x180f77(0xe5)](this, _0x5921d4);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x119)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x24e)]),
    (Game_Player['prototype'][_0xf6447d(0x24e)] = function () {
        const _0x1a25cc = _0xf6447d;
        if (!this[_0x1a25cc(0x26b)]()) return ![];
        return VisuMZ['UniqueTileEffects'][_0x1a25cc(0x119)][_0x1a25cc(0xe5)](this);
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x214)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0xf4)]),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0xf4)] = function () {
        const _0x2d64da = _0xf6447d;
        if (!this[_0x2d64da(0x26b)]()) {
            if (_0x2d64da(0x1df) === _0x2d64da(0x103))
                (_0x31cf34[_0x2d64da(0x224)][_0x2d64da(0x1a7)][_0x2d64da(0xe5)](this),
                    this[_0x2d64da(0x1ed)]());
            else {
                this[_0x2d64da(0x253)]();
                return;
            }
        }
        return VisuMZ[_0x2d64da(0x224)][_0x2d64da(0x214)]['call'](this);
    }),
    (Game_Player['prototype'][_0xf6447d(0x1d4)] = function (_0x543bd1, _0x449d60) {
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x20e)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x196)]),
    (Game_Player['prototype'][_0xf6447d(0x196)] = function (_0x100c6d, _0x170709) {
        const _0x14ee9a = _0xf6447d;
        if (this['isMovementEffectsUniqueTilesSmartJumpBreakable'](_0x100c6d, _0x170709))
            return !![];
        return VisuMZ[_0x14ee9a(0x224)][_0x14ee9a(0x20e)][_0x14ee9a(0xe5)](
            this,
            _0x100c6d,
            _0x170709
        );
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0xbd)] = function (_0x47a487, _0x4cceb3) {
        const _0x40ec02 = _0xf6447d;
        if ($gameMap[_0x40ec02(0x149)](_0x47a487, _0x4cceb3, 'pitfall')) {
            if ('TBUAA' !== _0x40ec02(0x264)) {
                const _0x1272cd = _0x1859fb[_0x40ec02(0x1b8)][_0x2d22a2];
                if (!_0x1272cd) return ![];
                const _0x1de419 = _0x1272cd[0x0] || _0x291ab4[_0x40ec02(0x1a1)](),
                    _0x2e9fbc = _0x1272cd[0x1] ?? this['x'],
                    _0x1b45c9 = _0x1272cd[0x2] ?? this['y'];
                if (
                    _0x1de419 === _0x4b9cf7[_0x40ec02(0x1a1)]() &&
                    _0x2e9fbc === this['x'] &&
                    _0x1b45c9 === this['y']
                )
                    return ![];
                return (
                    this[_0x40ec02(0x193)](
                        _0x1de419,
                        _0x2e9fbc,
                        _0x1b45c9,
                        this[_0x40ec02(0x1da)](),
                        0x0
                    ),
                    !![]
                );
            } else return $gamePlayer[_0x40ec02(0x26a)](_0x40ec02(0x237));
        }
        if ($gameMap[_0x40ec02(0x149)](_0x47a487, _0x4cceb3, _0x40ec02(0xa7)))
            return $gamePlayer[_0x40ec02(0x26a)](_0x40ec02(0xa7));
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x1eb)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x125)]),
    (Game_Player['prototype']['isTileSmartJumpCompatible'] = function (_0x820d59, _0x48e46b) {
        const _0x5ea088 = _0xf6447d;
        if (this['isMovementEffectsUniqueTilesSmartJumpCompatible'](_0x820d59, _0x48e46b))
            return !![];
        return VisuMZ[_0x5ea088(0x224)][_0x5ea088(0x1eb)]['call'](this, _0x820d59, _0x48e46b);
    }),
    (Game_Player['prototype']['isMovementEffectsUniqueTilesSmartBlinkBreakable'] = function (
        _0x4ee1f5,
        _0x3e9be0
    ) {
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0xb8)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x20a)]),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x20a)] = function (_0x382f3a, _0x102e65) {
        const _0x275384 = _0xf6447d;
        if (this[_0x275384(0x157)](_0x382f3a, _0x102e65)) return !![];
        return VisuMZ['UniqueTileEffects'][_0x275384(0xb8)][_0x275384(0xe5)](
            this,
            _0x382f3a,
            _0x102e65
        );
    }),
    (Game_Player['prototype'][_0xf6447d(0x106)] = function (_0x5280bf, _0x488f2b) {
        return ![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x268)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x216)]),
    (Game_Player[_0xf6447d(0x1f6)]['isTileSmartBlinkCompatible'] = function (_0x451ffc, _0x42b8f4) {
        const _0x11ca47 = _0xf6447d;
        if (this[_0x11ca47(0x106)](_0x451ffc, _0x42b8f4)) return !![];
        return VisuMZ[_0x11ca47(0x224)][_0x11ca47(0x268)][_0x11ca47(0xe5)](
            this,
            _0x451ffc,
            _0x42b8f4
        );
    }));
function _0x177a(_0x50d844, _0x2782bc) {
    const _0x976c43 = _0x976c();
    return (
        (_0x177a = function (_0x177a89, _0x3127b8) {
            _0x177a89 = _0x177a89 - 0x75;
            let _0x3ecd33 = _0x976c43[_0x177a89];
            return _0x3ecd33;
        }),
        _0x177a(_0x50d844, _0x2782bc)
    );
}
function _0x976c() {
    const _0x730740 = [
        '_executeFloorDamage',
        'canUpdateUniqueTileLava',
        'clearUniqueTileSpriteEffects',
        'isMapPassable',
        '_uniqueTilePartyImmune',
        'doesCurrentTileHaveUniqueFootstepSfx',
        'parse',
        'updateQuicksand',
        'isTransferring',
        'event',
        'ajszv',
        '_uniqueTileMoveSpeed',
        'mhp',
        'isMovementSucceeded',
        'Dive',
        'roundYWithDirection',
        '_uniqueTileFrameMinusY',
        'damageName',
        '_frame',
        'ZQFTg',
        '_lavaBurnMax',
        'Quicksand',
        'processDrowningEffect',
        'immune',
        'isDashingAffectedByUniqueTile',
        'page',
        'ApplyFootstepSfxModifiers',
        'WiGDa',
        'processForceMoveEffect',
        'refreshUniqueTileImmunityPartyCheck',
        'Game_CharacterBase_moveStraight',
        'effect',
        'Game_Map_setupEvents',
        'swimming',
        'items',
        'isAllDead',
        'isStopping',
        'ZftCY',
        '3213620iciauk',
        'UpRegions',
        'roundXWithDirection',
        'battleMembers',
        'JjfeO',
        'updateUniqueTileEffects',
        '_drowningHeightRate',
        'moveStraight',
        'min',
        'FUNC',
        'terrainTag',
        'updatePitfallEffect',
        'Game_Player_isTileSmartBlinkBreakable',
        'push',
        'PITFALL_DURATION',
        'StepsSandSink',
        'ARRAYSTRUCT',
        'isMovementEffectsUniqueTilesSmartJumpCompatible',
        'findTargetSprite',
        'processPitfallEffect',
        'updateDrowningEffect',
        'behindUniqueTileMeetsPullTargetEventConditions',
        'gatherFollowers',
        'realMoveSpeed',
        'left',
        'turnOffPitfalling',
        'Bounce4Regions',
        'exit',
        'LAVA_EVENT_MAX',
        'canUpdateUniqueTileEffects',
        'slippery',
        'JSON',
        'fQEEl',
        'bounce7',
        'hasBelowPriorityEventsXy',
        'Game_Player_isDashing',
        'Game_CharacterBase_updateJump',
        'Water1',
        'Game_CharacterBase_locate',
        'clone',
        'Lava',
        '_shadowSprite',
        'footstepsEnabled',
        'Thunder3',
        'isQuicksandSinking',
        'UNIQUE_TILE_DAMAGE',
        '73468KtKgIR',
        'registerCommand',
        'SHOCK_DELAY',
        'parameters',
        'isSwimmingTile',
        'checkUniqueTileEffectsStringTags',
        'damagePitch',
        'effectVolume',
        'VisuMZ_2_MovementEffects',
        'some',
        'DmgFlat',
        'call',
        'footstepsPitch',
        '_quicksandSteps',
        'bounce4',
        'return\x200',
        'VCdTA',
        'Game_Player_canSmartBlink',
        'playSe',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        '27270144nvWUwN',
        'scaleY',
        'isRawPassableByAnyDirection',
        '_priorityType',
        'Game_Player_increaseSteps',
        'Pattern',
        'moveBySmartRush',
        'slipperyMoveDirection',
        'mute',
        'volume',
        'Blow2',
        'isSwimmingTileWithBridge',
        'osXyY',
        '36862zYIGZc',
        'updateCharacterFrame',
        'pLGNR',
        'dcxaO',
        'SWIMMING_DEPTH',
        'Click\x20\x22Copy\x20Page\x22\x20from\x20another\x20tileset\x27s\x20pages',
        'updateSelfMovement',
        'Fire8',
        'oiiop',
        'increaseStepsUniqueTileEffects',
        'Game_CharacterBase_pattern',
        'isMovementEffectsUniqueTilesSmartBlinkCompatible',
        'isDead',
        '19TBKToh',
        'meetsUniqueTileGatherConditions',
        'setupUniqueTileData',
        'onDrowningFinish',
        '_followers',
        'registerLastUniqueTileCoordinate',
        'LAVA_ANIMATION',
        'footstepsName',
        'Settings',
        'registerLastSafestCoordinate',
        'characterPatternY',
        'DefaultAvoid',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'setUniqueTileAffected',
        'hasPullConditionsUniqueTiles',
        'effectPitch',
        'bounce3',
        'Game_Player_canSmartJump',
        '_lavaBurnTimes',
        '_uniqueTileXyType',
        'lVlHM',
        'isPassable',
        'getUniqueTileXyType',
        'Game_Player_refresh',
        'zBjTn',
        '_erased',
        'cumys',
        'Game_CharacterBase_jump',
        'bounce2',
        'isTileSmartJumpCompatible',
        'isUniqueTileAvoided',
        'right',
        'CTjFn',
        'DQJHa',
        'filter',
        'updateJump',
        'joFWg',
        'gotoLastSafestCoordinate',
        'setupPageSettings',
        'initMembers',
        'LiJbT',
        'JWETb',
        'Bounce6Regions',
        'startPitfallEffect',
        'hasUniqueTileEffects',
        'lava',
        '_lastSafestCoordinate',
        'SohkS',
        'onDrowningDead',
        'bind',
        'none',
        'increaseSteps',
        'Fall',
        'getUniqueTileData',
        'effectName',
        '_tempSlipperyTileStop',
        'playerCanDrown',
        'ZCEuQ',
        '_uniqueTileData',
        'bounceMoveDirection',
        'isJumping',
        'canBePushed',
        'max',
        'Game_Actor_changeEquip',
        '1536XGFEZz',
        'checkUniqueTileType',
        '_bypassPassableAnyDirection',
        'description',
        'getUniqueTileCharaSwimFrameTarget',
        'Game_CharacterBase_canMakeFootstepSounds',
        'Earth4',
        'isVisible',
        'processBounceEffect',
        'DefaultRegions',
        'ConvertParams',
        'forceMoveDirection',
        'AnimationMute',
        'QTVDt',
        'WJeSy',
        'isMovementEffectsUniqueTilesSmartBlinkBreakable',
        'getUniqueTileCharaQuicksandFrameTarget',
        'DROWNING_DURATION',
        'Bounce',
        'AnimationMirror',
        'qbSxG',
        'isUniqueTileWithoutBelow',
        'anyFollowersSwimming',
        'UNIQUE_TILE_MOVE_SPEED',
        'onQuicksandSink',
        'Game_CharacterBase_increaseSteps',
        'checkPassageNoEvents',
        'update',
        'bounce9',
        'beygf',
        'EffectDuration',
        'processUniqueTileTransfer',
        'turnOffDrowning',
        'finishDrowningEffect',
        'PlayerSetAntiForceMove',
        'UNIQUE_TILE_PATTERN',
        'Bounce7Regions',
        'getUniqueTileType',
        'frameCount',
        'AzyTX',
        'bounce0',
        'match',
        'ExAwf',
        'isUniqueTile',
        'UNIQUE_TILE_AVOID_DEFAULT',
        'playUniqueTileSfx',
        'floor',
        'bounce6',
        'vDuYt',
        'down',
        'Fire2',
        'scaleX',
        'makeUniqueTileFootstepSounds',
        'ssImS',
        'YbbTS',
        'PlayerSetAntiLava',
        'ENkrA',
        'tilesetFlags',
        'EiinH',
        'visibleFollowers',
        'SHOCK_ANIMATION',
        'Current\x20tileset\x20has\x20incomplete\x20flag\x20data.',
        'VisuMZ_3_EventChainReact',
        'PlayerSetAntiQuicksand',
        'UmRNn',
        'restorePrePitfallScale',
        'AbdFl',
        'pullTargetEvent',
        'characterBlockX',
        'Bounce9Regions',
        'terrainTags',
        'map',
        'setupEvents',
        'bounce5',
        'isDebugThrough',
        'reserveTransfer',
        'Game_CharacterBase_update',
        'EZwYy',
        'isTileSmartJumpBreakable',
        'Swimming',
        'mapTransfer',
        'Water3',
        'ignore',
        'moveDiagonally',
        'KlBAq',
        'ShockTimer',
        'AYhDt',
        'anyFollowersNotSwimming',
        'ARRAYNUM',
        'mapId',
        'nGkDe',
        'avoid',
        'bounce',
        '_shockEffectDelay',
        'ForceMove',
        'Game_Event_setupPageSettings',
        'immuneReserveParty',
        'isBoatPassable',
        'quicksand',
        'isSceneMap',
        'isOnUniqueTile',
        'onPitfallFinish',
        'clear',
        'SwimmingDepth',
        'Game_Player_canSmartRush',
        'Game_Player_locate',
        'isImmuneToUniqueTileType',
        'scale',
        'pitch',
        'Sprite_Character_initMembers',
        'Game_Player_moveByInput',
        'processShockEffect',
        '_uniqueTileTransfer',
        'refresh',
        'RegExp',
        'getUniqueTileRegions',
        'constructor',
        'damagePan',
        'PLAYER_CAN_DROWN',
        '_isDrowning',
        'updateCharacterFrameDrowning',
        'getUniqueTileTerrainTags',
        'affected',
        'processUniqueTileDamage',
        'patternWidth',
        'general',
        'iycdT',
        'changeEquip',
        '_uniqueTileCmdImmune',
        '_isShocked',
        'gainHp',
        'Game_CharacterBase_initMembers',
        'Setting',
        'AnimationID',
        'Bpemu',
        'status',
        'ffkJx',
        'MoveSpeed',
        'STRUCT',
        'wzyGM',
        'isMovementEffectsUniqueTilesSmartJumpBreakable',
        'wZpaT',
        'includes',
        'indexOf',
        'restorePreDrowning',
        'NDVdk',
        'direction',
        'processLavaEffect',
        'iLVAF',
        'ARRAYEVAL',
        'name',
        'mDbgQ',
        'pattern',
        'forceChangeEquip',
        'parseUniqueTileNotetags',
        'Game_Event_updateSelfMovement',
        'burnMax',
        'Jump2',
        'getUniqueTileCharaFrameTarget',
        'getBounceTileDistance',
        '3338946kZZScH',
        'Sprite_Character_updateCharacterFrame',
        'tileId',
        'Game_Player_isTileSmartJumpCompatible',
        'bounce1',
        'setupUniqueTileEffectsEffects',
        'note',
        'ceil',
        'damage',
        'updateCharacterSwimmingFrame',
        'isAffectedByCurrentUniqueTile',
        '_lastUniqueTileCoordinate',
        'height',
        'Game_Event_canBePulled',
        'prototype',
        'resetPattern',
        'DmgRate',
        '_uniqueTileSelfMove',
        'ARRAYFUNC',
        'Game_Event_clearPageSettings',
        'initUniqueTileEffectsEffects',
        'footstepsVolume',
        'DefaultAffected',
        'Slippery',
        'characterBlockY',
        'canBePulled',
        'pan',
        'isSwimming',
        'RightRegions',
        'damageVolume',
        '_spriteset',
        'UNIQUE_TILE_AFFECTED_DEFAULT',
        'bounce8',
        'mdINd',
        'isTileSmartBlinkBreakable',
        'Bounce3Regions',
        'initMembersUniqueTileEffects',
        'shock',
        'Game_Player_isTileSmartJumpBreakable',
        'clearUniqueTilePartyImmunity',
        'ARRAYJSON',
        'LAVA_DELAY',
        'canSmartBlink',
        'PlayerSetAntiBounce',
        'Game_Player_moveBySmartRush',
        'regionId',
        'isTileSmartBlinkCompatible',
        'jump',
        'Game_Actor_forceChangeEquip',
        'effectPan',
        'updateUniqueTileData',
        'code',
        'FFcRJ',
        'forceMove',
        'gainItem',
        'shadowScaleY',
        'startDrowningEffect',
        'canPreventSafestCoordinateRegistration',
        '_uniqueTilePattern',
        'Earth3',
        'UniqueTileEffects',
        '5394355wPJSmL',
        'format',
        'characterPatternX',
        'hasPushConditionsUniqueTiles',
        'BbGEc',
        'canSwimInWater',
        '7431344DGuSCX',
        'DmgDeathAllow',
        'hLXap',
        'getLastUniqueTileCoordinate',
        'canMakeFootstepSounds',
        'Game_Event_canBePushed',
        'footstep',
        'requestFauxAnimation',
        'FXSrT',
        'STR',
        'Pitfall',
        'Game_CharacterBase_moveDiagonally',
        'pitfall',
        'MovementEffects',
        'mirror',
        'Game_CharacterBase_realMoveSpeed',
        'calcUniqueTileCharaFrame',
        'LavaBurnTimer',
        '_walkAnime',
        'performCollapse',
        'Bounce2Regions',
        'Game_CharacterBase_isStopping',
        'footstepsPan',
        'setFrame',
        'Game_Player_pullTargetEvent',
        'Shock',
        '_uniqueTileAffected',
        '_uniqueTileMoveDirection',
        'patternHeight',
        'isShipPassable',
        '_pitfallData',
        '_data',
        'locate',
        '_pitfallDuration',
        'eventsXy',
        'canSmartJump',
        'TileTypes',
        'Sprite_Character_update',
        'toUpperCase',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'endSmartRush',
        'setImmuneToUniqueTileType',
        'kpSAo',
        'tjmOM',
        '_drowningDuration',
        '_isPitfalling',
        'allMembers',
        '_character',
        'canSmartRush',
        'UNIQUE_TILE_SFX',
        'UNIQUE_TILE_REGIONS',
        'performMapDamage',
        'PlayerSetAntiShock',
        'setDirection',
        'WhRhb',
        'swimmingDepth',
        '_scene',
        'TBUAA',
        'regions',
        'tileset',
        'canMakeUniqueTileFootstepSounds',
        'Game_Player_isTileSmartBlinkCompatible',
        'split',
        'isUniqueTileAffected',
        'canUniqueTileEndSmartAction',
        'initUniqueTileData',
        'trim',
        'onPitfallDead',
        'CEFhS',
        'Game_Party_gainItem',
        'erase',
        'getQuicksandSinkRate',
        'XqDjq',
        'setupUniqueTileEffectsCommentTags',
        'setupUniqueTileEffectsNotetags',
        'QUICKSAND_MAX_STEPS',
        '_uniqueTileAvoid',
        'DownRegions',
        'clamp',
        'Game_CharacterBase_isMapPassable',
        'isInVehicle',
        'VisuMZ_0_CoreEngine',
        'checkEventTriggerHere',
    ];
    _0x976c = function () {
        return _0x730740;
    };
    return _0x976c();
}
Imported[_0xf6447d(0x186)] &&
    ((Game_Player['prototype'][_0xf6447d(0x228)] = function (_0x53c85e) {
        return !![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x230)] = Game_Event['prototype'][_0xf6447d(0x145)]),
    (Game_Event['prototype'][_0xf6447d(0x145)] = function () {
        const _0x3d1311 = _0xf6447d;
        if (!$gamePlayer[_0x3d1311(0x228)](this)) return ![];
        return VisuMZ[_0x3d1311(0x224)][_0x3d1311(0x230)][_0x3d1311(0xe5)](this);
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x116)] = function (_0x25ff46) {
        return !![];
    }),
    (VisuMZ[_0xf6447d(0x224)]['Game_Event_canBePulled'] =
        Game_Event[_0xf6447d(0x1f6)]['canBePulled']),
    (Game_Event[_0xf6447d(0x1f6)][_0xf6447d(0x201)] = function () {
        const _0x57bc67 = _0xf6447d;
        if (!$gamePlayer[_0x57bc67(0x116)](this)) return ![];
        return VisuMZ[_0x57bc67(0x224)][_0x57bc67(0x1f5)]['call'](this);
    }),
    (Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0xc1)] = function (_0x378a02, _0x200913) {
        const _0x68469c = _0xf6447d;
        if ($gameMap[_0x68469c(0x173)](_0x378a02, _0x200913)) {
            const _0xdf08bc = $gameMap[_0x68469c(0x11e)](_0x378a02, _0x200913);
            if (_0xdf08bc === 'pitfall' && $gameMap[_0x68469c(0xce)](_0x378a02, _0x200913))
                return !![];
            if ($gameMap[_0x68469c(0xf9)](this['x'], this['y'])) return !![];
            return ![];
        }
        return !![];
    }),
    (VisuMZ[_0xf6447d(0x224)][_0xf6447d(0x243)] = Game_Player[_0xf6447d(0x1f6)][_0xf6447d(0x18b)]),
    (Game_Player[_0xf6447d(0x1f6)]['pullTargetEvent'] = function (_0xa718f3, _0x4e0a52, _0x28092f) {
        const _0x5b33da = _0xf6447d,
            _0x174f4c = 0xa - this[_0x5b33da(0x1da)]();
        if (_0x174f4c % 0x2 !== 0x0) return;
        const _0xac0c38 = $gameMap[_0x5b33da(0xae)](this['x'], _0x174f4c),
            _0x3662aa = $gameMap[_0x5b33da(0x95)](this['y'], _0x174f4c);
        if (!this[_0x5b33da(0xc1)](_0xac0c38, _0x3662aa)) return;
        VisuMZ['UniqueTileEffects']['Game_Player_pullTargetEvent']['call'](
            this,
            _0xa718f3,
            _0x4e0a52,
            _0x28092f
        );
    }));
