//=============================================================================
// VisuStella MZ - Main Menu Core
// VisuMZ_1_MainMenuCore.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_1_MainMenuCore = true;

var VisuMZ = VisuMZ || {};
VisuMZ.MainMenuCore = VisuMZ.MainMenuCore || {};
VisuMZ.MainMenuCore.version = 1.2;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 1] [Version 1.20] [MainMenuCore]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Main_Menu_Core_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The Main Menu Core plugin is designed to give you more control over the Main
 * Menu outside of RPG Maker MZ's editor's control. Game devs are given control
 * over how commands work, visual aesthetics pertaining to the Main Menu, and
 * assign menu images to actors as background portraits.
 *
 * Features include all (but not limited to) the following:
 *
 * * Control over general Main Menu settings.
 * * The ability to set Menu Background Portraits for individual actors.
 * * Flexibility in changing which commands appear in the Main Menu.
 * * Add new windows like the Playtime Window and Variable windows.
 * * Change the style of how the windows are arranged in the Main Menu.
 * * Change the way the status list is displayed and the way it's displayed.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 1 ------
 *
 * This plugin is a Tier 1 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * <Menu Portrait: filename>
 *
 * - Used for: Actor
 * - This is used with the "Portrait" style Main Menu List.
 * - Sets the menu image for the actor to 'filename'.
 * - Replace 'filename' with a picture found within your game project's
 *   img/pictures/ folder. Filenames are case sensitive. Leave out the filename
 *   extension from the notetag.
 *
 * ---
 *
 * <Menu Portrait Offset: +x, +y>
 * <Menu Portrait Offset: -x, -y>
 *
 * <Menu Portrait Offset X: +x>
 * <Menu Portrait Offset X: -x>
 *
 * <Menu Portrait Offset Y: +y>
 * <Menu Portrait Offset Y: -y>
 *
 * - Used for: Actor
 * - This is used with the "Portrait" style Main Menu List.
 * - Offsets the X and Y coordinates for the menu image.
 * - Replace 'x' with a number value that offsets the x coordinate.
 * - Negative x values offset left. Positive x values offset right.
 * - Replace 'y' with a number value that offsets the y coordinate.
 * - Negative y values offset up. Positive x values offset down.
 * - This only applies to the Main Menu portraits.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Actor Plugin Commands ===
 *
 * ---
 *
 * Actor: Change Menu Image (Group)
 * Actor: Change Menu Image (Range)
 * Actor: Change Menu Image (JS)
 * - Changes the actor's Menu Image.
 * - Each version has a different means of selecting Actor ID's.
 *
 *   Actor ID:
 *   - Select which ID(s) to affect.
 *
 *   Filename:
 *   - Selected actor(s) will have their menu images changed to this.
 *
 * ---
 *
 * === Menu Command Plugin Commands ===
 *
 * ---
 *
 * Menu Command: Clear Forced Settings
 * - Clear any forced settings for the menu command symbols.
 *
 *   Symbol(s):
 *   - Insert the symbols of the menu commands here.
 *   - The symbols are case sensitive.
 *   - VisuStella is NOT responsible for any errors produced by menus that
 *     become accessible outside of their intended usage.
 *
 * ---
 *
 * Menu Command: Force Disable
 * - Forcefully disable specific menu commands via their symbols.
 * - Matching forced enabled symbols will be overwritten.
 *
 *   Symbol(s):
 *   - Insert the symbols of the menu commands here.
 *   - The symbols are case sensitive.
 *   - VisuStella is NOT responsible for any errors produced by menus that
 *     become accessible outside of their intended usage.
 *
 * ---
 *
 * Menu Command: Force Enable
 * - Forcefully enable specific menu commands via their symbols.
 * - Matching forced disabled symbols will be overwritten.
 *
 *   Symbol(s):
 *   - Insert the symbols of the menu commands here.
 *   - The symbols are case sensitive.
 *   - VisuStella is NOT responsible for any errors produced by menus that
 *     become accessible outside of their intended usage.
 *
 * ---
 *
 * Menu Command: Force Hide
 * - Forcefully hide specific menu commands via their symbols.
 * - Matching forced shown symbols will be overwritten.
 *
 *   Symbol(s):
 *   - Insert the symbols of the menu commands here.
 *   - The symbols are case sensitive.
 *   - VisuStella is NOT responsible for any errors produced by menus that
 *     become accessible outside of their intended usage.
 *
 * ---
 *
 * Menu Command: Force Show
 * - Forcefully show specific menu commands via their symbols.
 * - Matching forced hidden symbols will be overwritten.
 *
 *   Symbol(s):
 *   - Insert the symbols of the menu commands here.
 *   - The symbols are case sensitive.
 *   - VisuStella is NOT responsible for any errors produced by menus that
 *     become accessible outside of their intended usage.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * These general settings contain various settings on how the Main Menu scene
 * displays certain windows, alters how specific windows behave, and determines
 * which scenes would display actor menu images as background portraits.
 *
 * ---
 *
 * Gold Window
 *
 *   Thinner Gold Window:
 *   - Make the Gold Window thinner in the Main Menu?
 *   - Used to match the Playtime and Variable Windows.
 *   - Only applies to the Command Window style: Default Vertical.
 *
 *   Auto Adjust Height:
 *   - Automatically adjust the height for the thinner Gold Window?
 *
 *   Auto Adjust Y:
 *   - Automatically adjust the Y position for the thinner Gold Window?
 *
 * ---
 *
 * Status Window
 *
 *   Select Last?:
 *   - When picking a personal command from the Command Window, select the
 *     last picked actor or always the first?
 *
 * ---
 *
 * Solo Party
 *
 *   Solo Quick Mode:
 *   - When selecting "Skills", "Equip", or "Status" with one party member,
 *     immediately go to the scene.
 *
 * ---
 *
 * Sub Menus
 *
 *   Menus with Actor BG's:
 *   - A list of the menus that would be compatible with Actor Menu Backgrounds
 *
 *   JS: Actor BG Action:
 *   - Code used to determine how to display the sprites upon loading.
 *
 * ---
 *
 * Party Window
 *
 *   Show Reserve Memebers:
 *   - Show reserve members while on the Main Menu scene?
 *
 *   Hide Main Menu Only
 *   - If reserve members are hidden, hide them only in the main menu or
 *     all scenes?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Command Window List
 * ============================================================================
 *
 * The Command Window functions as a hub to the various scenes linked from the
 * Main Menu. These include 'Item', 'Skill', 'Equip', 'Status', 'Save', and
 * so on. This Plugin Parameter is an array that lets you add, remove, and/or
 * alter the Command Window's various commands, how they're handled, whether or
 * not they're visible, and how they react when selected.
 *
 * These will require knowledge of JavaScript to use them properly.
 *
 * ---
 *
 * Command Window List
 *
 *   Symbol:
 *   - The symbol used for this command.
 *
 *   Subcategory:
 *   - The subcategory used for this command.
 *   - Leave empty for no subcategory.
 *
 *   Icon:
 *   - Icon used for this command.
 *   - Use 0 for no icon.
 *
 *   STR: Text:
 *   - Displayed text used for this title command.
 *   - If this has a value, ignore the JS: Text version.
 *
 *   JS: Text:
 *   - JavaScript code used to determine string used for the displayed name.
 *
 *   JS: Show:
 *   - JavaScript code used to determine if the item is shown or not.
 *
 *   JS: Enable:
 *   - JavaScript code used to determine if the item is enabled or not.
 *
 *   JS: Ext:
 *   - JavaScript code used to determine any ext data that should be added.
 *
 *   JS: Run Code:
 *   - JavaScript code that runs once this command is selected.
 *
 *   JS: Personal Code:
 *   - JavaScript code that runs once the actor list is selected with this
 *     command highlighted.
 *
 * ---
 *
 * ==== Subcategories ====
 *
 * Subcategories are a new addition to the Main Menu Core version 1.18. When a
 * subcategory is set, it will only display Command Window items that belong
 * to that subcategory. Those Command Window items do not appear when there is
 * no subcategory active or if it's a different subcategory.
 *
 * ---
 *
 * To create a subcategory, a few things must be done:
 *
 * 1. The subcategory symbol must be "subcategory".
 *
 * 2. The string returned by JS: Ext determines the subcategory. In the default
 *    Plugin Parameters, 'datalog' is returned as the subcategory. This becomes
 *    the subcategory when picked.
 *
 * 3. For the JS: Run Code, have the following code somewhere in it:
 *
 *    const ext = arguments[0];
 *    this.setSubcategory(ext);
 *
 * ---
 *
 * To make a Command Window item be a part of a subcategory do the following:
 *
 * 1. Take the JS: Ext string value (case sensitive).
 *
 * 2. Set it as the target Command Window item's "Subcategory" value.
 *
 * 3. If the subcategory doesn't exist, then this Command Window item will
 *    appear normally.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Playtime Window
 * ============================================================================
 *
 * The Playtime Window is an optional feature that can be displayed in the
 * Main Menu. As its name suggests, it displays the playtime of the player's
 * current play through.
 *
 * ---
 *
 * Playtime Window
 *
 *   Enable:
 *   - Use the Playtime Window?
 *
 *   Adjust Command Window:
 *   - Adjust the command window's height to fit in the Playtime Window?
 *
 *   Background Type:
 *   - Select background type for the Playtime window.
 *
 *   Font Size:
 *   - Font size used for displaying Gold inside the Playtime window.
 *
 *   Time Icon:
 *   - Icon displayed for the 'Time' label.
 *
 *   Time Text:
 *   - Text for the display of 'Time' in the Playtime window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for the Playtime window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Variable Window
 * ============================================================================
 *
 * The Variable Window is an optional feature that can be displayed in the
 * Main Menu. If enabled, the Variable Window will display variables of the
 * game dev's choice in the Main Menu itself.
 *
 * ---
 *
 * Variable Window
 *
 *   Enable:
 *   - Use the Variable Window?
 *
 *   Adjust Command Window:
 *   - Adjust the command window's height to fit in the Variable Window?
 *
 *   Background Type:
 *   - Select background type for the Variable window.
 *
 *   Font Size:
 *   - Font size used for displaying Gold inside the Variable window.
 *
 *   Variable List:
 *   - Select variables to be displayed into the window.
 *     Use \i[x] to determine their icon.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for the Variable window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Command Window Style & Command Style Settings
 * ============================================================================
 *
 * This determines how the Main Menu appears based on the Command Window Style.
 * If anything but the 'Default' is used, then these settings will take over
 * the window placement settings for the Main Menu. This means that even if you
 * are using VisuStella's Core Engine, the window layouts will be overwritten.
 *
 * ---
 *
 * Command Window Style:
 * - Choose the positioning and style of the Main Menu Command Window.
 * - This will automatically rearrange windows.
 *
 *   Default Vertical Side Style:
 *   - The default Main Menu layout style.
 *   - Affected by VisuStella's Core Engine's Plugin Parameter settings.
 *
 *   Top Horizontal Style:
 *   - Puts the Command Window at the top of the screen.
 *   - Gold, Playlist, and Variable Windows are moved to the bottom.
 *   - The Actor List Window is placed in the middle.
 *   - Unaffected by VisuStella's Core Engine's Plugin Parameter settings.
 *
 *   Bottom Horizontal Style:
 *   - Puts the Command Window at the bottom of the screen.
 *   - Gold, Playlist, and Variable Windows are moved to the top.
 *   - The Actor List Window is placed in the middle.
 *   - Unaffected by VisuStella's Core Engine's Plugin Parameter settings.
 *
 *   Mobile Full Screen Style:
 *   - Puts the Command Window at the center of the screen with larger buttons.
 *   - Gold, Playlist, and Variable Windows are moved to the bottom.
 *   - The Actor List Window is hidden until prompted to be selected.
 *   - Unaffected by VisuStella's Core Engine's Plugin Parameter settings.
 *
 * ---
 *
 * Command Style Settings
 *
 *   Style:
 *   - How do you wish to draw command entries in the Command Window?
 *   - Text Only: displays only text.
 *   - Icon Only: displays only the icon.
 *   - Icon + Text: displays icon first, then text.
 *   - Automatic: determines the best fit for the size
 *
 *   Text Alignment:
 *   - Decide how you want the text to be aligned.
 *   - Left, Center, or Right
 *
 *   Rows:
 *   - Number of visible rows.
 *   - Applies only to Top, Bottom, and Mobile styles.
 *
 *   Columns:
 *   - Number of maximum columns.
 *   - Applies only to Top, Bottom, and Mobile styles.
 *
 *   Mobile Thickness:
 *   - The thickness of the buttons for mobile version.
 *   - Applies only to Top, Bottom, and Mobile styles.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Status Graphic, Status List Style, & List Style Settings
 * ============================================================================
 *
 * Choose how the contents Actor Status List Window in the Main Menu appears.
 * This can range from the which actor graphic is drawn to the style used for
 * the data that's displayed.
 *
 * ---
 *
 * Status Graphic:
 * - Choose how the graphic for actor graphics appear in status-like menus.
 *
 *   None:
 *   - Don't display any graphic for the actors.
 *
 *   Face:
 *   - Display the actors' faces. This is the default option in RPG Maker MZ.
 *
 *   Map Sprite:
 *   - Display the actors' map sprites.
 *
 *   Sideview Battler:
 *   - Display the actors' sideview battlers.
 *
 * ---
 *
 * Main Menu List Style
 * - Choose how the actor status list looks in the Main Menu.
 *
 * Inner-Menu List Style
 * - Choose how the actor status list looks in the inner menus like Scene_Item,
 *   Scene_Skill, etc.
 *
 *   Default Horizontal Style:
 *   - This is the default style found in RPG Maker MZ's Main Menu.
 *
 *   Vertical Style:
 *   - Makes the display for the actor list vertical instead of horizontal.
 *
 *   Portrait Style:
 *   - Similar to the vertical style, except each actor's Menu Image is
 *     displayed in the background instead. Portraits are required.
 *   - If there is no Menu Image used, this will switch over to the Vertical
 *     Style and use a face graphic instead.
 *
 *   Solo Style:
 *   - Used for solo party member games. Extends the whole view of the Status
 *     Window to accomodate a single actor.
 *
 *   Thin Horizontal Style:
 *   - Makes the selectable menu entries for the actors a single line thin.
 *
 *   Thicker Horizontal Style:
 *   - Makes the selectable menu entries for the actors two lines thick.
 *
 * ---
 *
 * List Styles
 *   JavaScript code used to determine how the individual styles are drawn.
 *
 *   JS: Default:
 *   JS: Vertical:
 *   JS: Portrait:
 *   JS: Solo:
 *   JS: Thin:
 *   JS: Thicker:
 *   - Code used to draw the data for these styles.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.20: March 16, 2022
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 * * Feature Update!
 * ** Updated the default Plugin Parameters for 'Command Window List' to
 *    include a 'Bestiary' command.
 * *** This is for the upcoming VisuStella MZ plugins.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'bestiary' option(s) and click
 *      copy. Go to the target project's Main Menu Core's 'Command Window List'
 *      plugin parameter. Paste the command where you want it to go.
 *
 * Version 1.19: December 15, 2022
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 * * Feature Update!
 * ** Updated the default Plugin Parameters for 'Command Window List' to
 *    include a 'CG Gallery', 'Credits Page', and 'Patch Notes' command.
 * *** This is for the upcoming VisuStella MZ plugins.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'cgGallery', 'creditsPage', or
 *      'patchNotes' option(s) and click copy. Go to the target project's Main
 *      Menu Core's 'Command Window List' plugin parameter. Paste the command
 *      where you want it to go.
 *
 * Version 1.18: October 27, 2022
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 * * Documentation Update!
 * ** Help file updated for new features.
 * ** Added a new section into Plugin Parameters: Command Window List for
 *    "Subcategories" and adding info on how they are handled.
 * * Feature Update!
 * ** Updated the default Plugin Parameters for 'Command Window List' to
 *    include a 'Tutorial List' command.
 * *** This is for the upcoming VisuMZ_2_TutorialPanelSys plugin.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'tutorialList' option and click
 *      copy. Go to the target project's Main Menu Core's 'Command Window List'
 *      plugin parameter. Paste the command where you want it to go.
 * ** Subcategory called "Datalog" is now added.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'subcategory' option and click
 *      copy. Go to the target project's Main Menu Core's 'Command Window List'
 *      plugin parameter. Paste the command where you want it to go.
 * **** Existing entries for Quest, Message Log, and Combat Log are now added
 *      to the Datalog subcategory.
 * * New Features!
 * ** Subcategory support is now added for the Main Menu Command Window.
 * *** Subcategories allow you to make some Command Window items invisible
 *     until a subcategory is selected. This helps reduce clutter and save room
 *     on the Command Window command list.
 *
 * Version 1.17: August 18, 2022
 * * Bug Fixes!
 * ** Changed actor graphics now reflect properly for those using the default
 *    status menu. Fix made by Irina.
 *
 * Version 1.16: April 21, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Commands added by Arisu and sponsored by AndyL:
 * *** Menu Command: Clear Forced Settings
 * *** Menu Command: Force Disable
 * *** Menu Command: Force Enable
 * *** Menu Command: Force Hide
 * *** Menu Command: Force Show
 * **** These new Plugin Commands allow you to forcefully show, hide, enable,
 *      or disable Plugin Commands regardless of their required settings.
 * **** We are not responsible for errors that occur by accessing menus that
 *      should otherwise be disabled or hidden.
 *
 * Version 1.15: February 10, 2022
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.14: October 25, 2021
 * * Bug Fixes!
 * ** Plugin Parameter settings for automatic Command Window height adjustment
 *    should now work properly. Fix made by Irina.
 * * Documentation Update!
 * ** Added a note for the Help File: Gold Window > Thinner Gold Window
 * *** Only applies to the Command Window style: Default Vertical.
 *
 * Version 1.13: October 21, 2021
 * * Feature Update!
 * ** Rounding update applied to picture portraits so that coordinates aren't
 *    drawn on non-whole numbers due to base images having odd values. Update
 *    made by Olivia.
 *
 * Version 1.12: July 16, 2021
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 * * Feature Update!
 * ** Updated the default Plugin Parameters for 'Command Window List' to
 *    include a 'Message Log' command.
 * *** This is for the upcoming VisuMZ_3_MessageLog plugin.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'MessageLog' option and click
 *      copy. Go to the target project's Main Menu Core's 'Command Window List'
 *      plugin parameter. Paste the command where you want it to go.
 *
 * Version 1.11: May 14, 2021
 * * Feature Update!
 * ** Updated the default Plugin Parameters for 'Command Window List' to
 *    include a 'Load' command after the 'Save' command.
 * *** This allows players to access the load game screen from the Main Menu.
 * *** Projects with the Main Menu Core already installed will not have this
 *     update, but you can copy over the settings from a new project with the
 *     following steps:
 * **** Create a new project. Install Main Menu Core. Open up the new project's
 *      'Command Window List'. Right click the 'Load' option and click copy.
 *      Go to the target project's Main Menu Core's 'Command Window List'
 *      plugin parameter. Paste the command where you want it to go.
 *
 * Version 1.10: April 16, 2021
 * * Feature Update!
 * ** Default style for List Styles now have its code updated with the
 *    JS: Default plugin parameter for games whose vertical screen resolution
 *    is larger than normal.
 * *** To update this, do either of the following:
 * **** Open up the Main Menu Core Plugin Parameters. Select and press delete
 *      on "List Style Settings". Press Enter. New updated settings will be
 *      replaced for the JS: Default settings.
 * **** Or Delete the existing VisuMZ_1_MainMenuCore.js in the Plugin Manager
 *      list and install the newest version.
 *
 * Version 1.09: March 19, 2021
 * * Documentation Update!
 * ** Added clarity for the "Portrait Style" in Plugin Parameters section for
 *    "Status Graphic, Status List Style, & List Style Settings":
 * *** If there is no Menu Image used, this will switch over to the Vertical
 *     Style and use a face graphic instead.
 *
 * Version 1.08: February 26, 2021
 * * Feature Update!
 * ** Default Plugin Parameters for the List Style Settings defaults have been
 *    updated with tighter coordinate values to allow for more accurate display
 *    of UI element positioning. Update made by Olivia.
 *
 * Version 1.07: January 1, 2021
 * * Documentation Update!
 * ** Added documentation for new feature(s)!
 * ** Removed "<Menu Image: filename>" version of notetag to reduce confusion
 *    and to stick with the norm declared by the Battle Core.
 * * New Features!
 * ** New notetags added by Yanfly:
 * *** <Menu Portrait Offset: +x, +y>
 * *** <Menu Portrait Offset X: +x>
 * *** <Menu Portrait Offset Y: +y>
 * **** This is used with the "Portrait" style Main Menu list.
 * **** Offsets the X and Y coordinates for the menu portrait.
 *
 * Version 1.06: December 11, 2020
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.05: October 11, 2020
 * * Documentation Update!
 * ** Documentation added for the new plugin parameter.
 * * New Features!
 * ** New plugin parameter added by Yanfly.
 * *** Plugin Parameters > General > Status Window > Select Last?
 * **** When picking a personal command from the Command Window, select the
 *      last picked actor or always the first?
 *
 * Version 1.04: October 4, 2020
 * * Feature Update!
 * ** Certain windows will now pre-load all associated image types for the
 *    actor upon being created to avoid custom JS drawing problems.
 *    Change made by Irina.
 * ** Failsafes have been added to prevent non-existent variables from crashing
 *    the game if a user does not remove them from the variable list. Change
 *    made by Irina.
 *
 * Version 1.03: September 20, 2020
 * * Documentation Update!
 * ** Added the alternative notetag <Menu Portrait: filename> that also works
 *    the same way as <Menu Image: filename>.
 *
 * Version 1.02: September 13, 2020
 * * Compatibility Update!
 * ** Better compatibility for SV Actor graphics.
 *
 * Version 1.01: August 23, 2020
 * * Bug Fixes!
 * ** Skill check plugin parameter for show fixed. Fixed by Yanfly and Shaz.
 * *** Users updating from version 1.00 will need to fix this problem by either
 *     removing the plugin from the Plugin Manager list and reinstalling it, or
 *     going to Plugin Parameters > Command Window List > skill >
 *     JS: Show > and changing 'this.needsCommand("item")' to
 *     'this.needsCommand("skill")'
 *
 * Version 1.00: August 20, 2020
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChangeActorMenuImageGroup
 * @text Actor: Change Menu Image (Group)
 * @desc Changes the actor's Menu Image.
 * Select from a group of actor ID's to change.
 *
 * @arg Step1:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Step2:str
 * @text Filename
 * @type file
 * @dir img/pictures/
 * @desc Selected actor(s) will have their menu images changed to this.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChangeActorMenuImageRange
 * @text Actor: Change Menu Image (Range)
 * @desc Changes the actor's Menu Image.
 * Select from a range of actor ID's to change.
 *
 * @arg Step1
 * @text Actor ID Range
 *
 * @arg Step1Start:num
 * @text Range Start
 * @parent Step1
 * @type actor
 * @desc Select which Actor ID to start from.
 * @default 1
 *
 * @arg Step1End:num
 * @text Range End
 * @parent Step1
 * @type actor
 * @desc Select which Actor ID to end at.
 * @default 4
 *
 * @arg Step2:str
 * @text Filename
 * @type file
 * @dir img/pictures/
 * @desc Selected actor(s) will have their menu images changed to this.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChangeActorMenuImageJS
 * @text Actor: Change Menu Image (JS)
 * @desc Changes the actor's Menu Image.
 * Select from a group of actor ID's using JavaScript.
 *
 * @arg Step1:arrayeval
 * @text Actor ID(s)
 * @type string[]
 * @desc Enter which Actor ID(s) to affect.
 * You may use JavaScript code.
 * @default ["$gameVariables.value(1)"]
 *
 * @arg Step2:str
 * @text Filename
 * @type file
 * @dir img/pictures/
 * @desc Selected actor(s) will have their menu images changed to this.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_MenuCommand
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command MenuCommandClear
 * @text Menu Command: Clear Forced Settings
 * @desc Clear any forced settings for the menu command symbols.
 *
 * @arg Symbols:arraystr
 * @text Symbol(s)
 * @type string[]
 * @desc Insert the symbols of the menu commands here.
 * The symbols are case sensitive.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command MenuCommandForceDisable
 * @text Menu Command: Force Disable
 * @desc Forcefully disable specific menu commands via their symbols.
 * Matching forced enabled symbols will be overwritten.
 *
 * @arg Symbols:arraystr
 * @text Symbol(s)
 * @type string[]
 * @desc Insert the symbols of the menu commands here.
 * The symbols are case sensitive.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command MenuCommandForceEnable
 * @text Menu Command: Force Enable
 * @desc Forcefully enable specific menu commands via their symbols.
 * Matching forced disabled symbols will be overwritten.
 *
 * @arg Symbols:arraystr
 * @text Symbol(s)
 * @type string[]
 * @desc Insert the symbols of the menu commands here.
 * The symbols are case sensitive.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command MenuCommandForceHide
 * @text Menu Command: Force Hide
 * @desc Forcefully hide specific menu commands via their symbols.
 * Matching forced shown symbols will be overwritten.
 *
 * @arg Symbols:arraystr
 * @text Symbol(s)
 * @type string[]
 * @desc Insert the symbols of the menu commands here.
 * The symbols are case sensitive.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command MenuCommandForceShow
 * @text Menu Command: Force Show
 * @desc Forcefully show specific menu commands via their symbols.
 * Matching forced hidden symbols will be overwritten.
 *
 * @arg Symbols:arraystr
 * @text Symbol(s)
 * @type string[]
 * @desc Insert the symbols of the menu commands here.
 * The symbols are case sensitive.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MainMenuCore
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc General settings pertaining to the Main Menu and related.
 * @default {"GoldWindow":"","ThinGoldWindow:eval":"true","AutoGoldHeight:eval":"true","AutoGoldY:eval":"true","StatusWindow":"","StatusSelectLast:eval":"false","SoloParty":"","SoloQuick:eval":"true","SubMenus":"","ActorBgMenus:arraystr":"[\"Scene_Skill\"]","ActorBgMenuJS:func":"\"this.anchor.x = 0.5;\\nconst scale = 1.25;\\nthis.scale.x = this.scale.y = scale;\\nthis.x = Graphics.width;\\nthis.y = Graphics.height - (this.bitmap.height * Math.abs(scale));\\nthis._targetX = Graphics.width * 3 / 4;\\nthis._targetY = Graphics.height - (this.bitmap.height * Math.abs(scale));\\nthis._duration = 10;\\nthis.opacity = 0;\"","PartyWindow":"","ShowReserve:eval":"true","HideMainMenuOnly:eval":"true"}
 *
 * @param CommandList:arraystruct
 * @text Command Window List
 * @parent General:struct
 * @type struct<Command>[]
 * @desc Window commands used by the Main Menu.
 * Add new commands here.
 * @default ["{\"Symbol:str\":\"item\",\"Icon:num\":\"208\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.item;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"item\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandItem();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"classChange\",\"Icon:num\":\"133\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.classChangeMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_2_ClassChangeSystem &&\\\\n    this.isClassChangeCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled() &&\\\\n    this.isClassChangeCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPersonal();\\\"\",\"PersonalHandlerJS:func\":\"\\\"SceneManager.push(Scene_ClassChange);\\\"\"}","{\"Symbol:str\":\"skill\",\"Icon:num\":\"101\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.skill;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"skill\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPersonal();\\\"\",\"PersonalHandlerJS:func\":\"\\\"SceneManager.push(Scene_Skill);\\\"\"}","{\"Symbol:str\":\"equip\",\"Icon:num\":\"137\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.equip;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"equip\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPersonal();\\\"\",\"PersonalHandlerJS:func\":\"\\\"SceneManager.push(Scene_Equip);\\\"\"}","{\"Symbol:str\":\"status\",\"Icon:num\":\"82\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.status;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"status\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPersonal();\\\"\",\"PersonalHandlerJS:func\":\"\\\"SceneManager.push(Scene_Status);\\\"\"}","{\"Symbol:str\":\"itemCrafting\",\"Icon:num\":\"223\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.ItemCraftingMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_2_ItemCraftingSys &&\\\\n    this.isItemCraftingCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isItemCraftingCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandItemCrafting();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"subcategory\",\"Subcategory:str\":\"\",\"Icon:num\":\"230\",\"TextStr:str\":\"Datalog\",\"TextJS:func\":\"\\\"return 'Text';\\\"\",\"ShowJS:func\":\"\\\"return this.isSubcategoryVisible(arguments[1]);\\\"\",\"EnableJS:func\":\"\\\"return true;\\\"\",\"ExtJS:func\":\"\\\"// This becomes the subcategory name. Case-sensitive.\\\\n\\\\nreturn 'datalog';\\\"\",\"CallHandlerJS:func\":\"\\\"const ext = arguments[0];\\\\nthis.setSubcategory(ext);\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"quest\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"186\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.questCommandName;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_2_QuestSystem &&\\\\n    this.isQuestCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isQuestCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandQuest();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"bestiary\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"10\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.BestiaryMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_2_Bestiary &&\\\\n    this.isBestiaryCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isBestiaryCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandBestiary();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"tutorialList\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"187\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.tutorial.menuCmd;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_2_TutorialPanelSys &&\\\\n    this.isTutorialListCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isTutorialListCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandTutorialList();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"messageLog\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"193\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.MessageLogMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_3_MessageLog &&\\\\n    this.isMessageLogCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isMessageLogCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandMessageLog();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"combatLog\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"189\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.combatLog_BattleCmd_Name;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_4_CombatLog &&\\\\n    this.isCombatLogCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isCombatLogCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandCombatLog();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"cgGallery\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"311\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.cgGalleryMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_4_CGGallery &&\\\\n    this.isCgGalleryCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isCgGalleryCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandCgGallery();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"creditsPage\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"193\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.CreditsPageMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_4_CreditsPage &&\\\\n    this.isCreditsPageCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isCreditsPageCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandCreditsPage();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"patchNotes\",\"Subcategory:str\":\"datalog\",\"Icon:num\":\"83\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.PatchNotesMenuCommand;\\\"\",\"ShowJS:func\":\"\\\"return Imported.VisuMZ_4_PatchNotes &&\\\\n    this.isPatchNotesCommandVisible();\\\"\",\"EnableJS:func\":\"\\\"return this.isPatchNotesCommandEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPatchNotes();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"formation\",\"Icon:num\":\"75\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.formation;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"formation\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.isFormationEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandFormation();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"options\",\"Icon:num\":\"83\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.options;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"options\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.isOptionsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandOptions();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"save\",\"Icon:num\":\"189\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.save;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"save\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.isSaveEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandSave();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"load\",\"Icon:num\":\"191\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return 'Load';\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"save\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return DataManager.isAnySavefileExists();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandLoad();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"commonEvent1\",\"Icon:num\":\"88\",\"TextStr:str\":\"Common Event 1\",\"TextJS:func\":\"\\\"return 'Text';\\\"\",\"ShowJS:func\":\"\\\"return false;\\\"\",\"EnableJS:func\":\"\\\"return true;\\\"\",\"ExtJS:func\":\"\\\"return 1;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandCommonEvent();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}","{\"Symbol:str\":\"commonEvent2\",\"Icon:num\":\"87\",\"TextStr:str\":\"Common Event 2\",\"TextJS:func\":\"\\\"return 'Text';\\\"\",\"ShowJS:func\":\"\\\"return false;\\\"\",\"EnableJS:func\":\"\\\"return this.areMainCommandsEnabled();\\\"\",\"ExtJS:func\":\"\\\"return 2;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandPersonal();\\\"\",\"PersonalHandlerJS:func\":\"\\\"// Declare Ext\\\\nconst ext = arguments[0];\\\\n\\\\n// Declare Status Window\\\\nconst statusWindow = SceneManager._scene._statusWindow;\\\\n\\\\n// Declare Actor ID\\\\nconst actorId = statusWindow.actor(statusWindow.index()).actorId();\\\\n\\\\n// Set variable 1 to Actor ID\\\\n$gameVariables.setValue(1, actorId);\\\\n\\\\n// Prepare Common Event ext to run\\\\n$gameTemp.reserveCommonEvent(ext);\\\\n\\\\n// Exit Main Menu\\\\nSceneManager._scene.popScene();\\\"\"}","{\"Symbol:str\":\"gameEnd\",\"Icon:num\":\"187\",\"TextStr:str\":\"\",\"TextJS:func\":\"\\\"return TextManager.gameEnd;\\\"\",\"ShowJS:func\":\"\\\"return this.needsCommand(\\\\\\\"gameEnd\\\\\\\");\\\"\",\"EnableJS:func\":\"\\\"return this.isGameEndEnabled();\\\"\",\"ExtJS:func\":\"\\\"return null;\\\"\",\"CallHandlerJS:func\":\"\\\"SceneManager._scene.commandGameEnd();\\\"\",\"PersonalHandlerJS:func\":\"\\\"const ext = arguments[0];\\\"\"}"]
 *
 * @param Playtime:struct
 * @text Playtime Window
 * @type struct<Playtime>
 * @desc Settings for the Playtime Window.
 * @default {"Enable:eval":"true","AdjustCommandHeight:func":"true","BgType:num":"0","FontSize:num":"24","Icon:num":"75","Time:str":"Time","WindowRect:func":"\"const rows = 1;\\nconst ww = this.mainCommandWidth();\\nconst wh = this.calcWindowHeight(rows, false);\\nconst wx = this.isRightInputMode() ? Graphics.boxWidth - ww : 0;\\nlet wy = this.mainAreaBottom() - wh;\\nif (this._goldWindow) wy -= this._goldWindow.height;\\nif (this.canCreateVariableWindow()) wy -= this.variableWindowRect().height;\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param Variable:struct
 * @text Variable Window
 * @type struct<Variable>
 * @desc Settings for the Variable Window.
 * @default {"Enable:eval":"false","AdjustCommandHeight:func":"true","BgType:num":"0","FontSize:num":"24","VarList:arraynum":"[\"1\",\"2\"]","WindowRect:func":"\"const rows = VisuMZ.MainMenuCore.Settings.Variable.VarList.length;\\nconst ww = this.mainCommandWidth();\\nconst wh = this.calcWindowHeight(rows, false);\\nconst wx = this.isRightInputMode() ? Graphics.boxWidth - ww : 0;\\nlet wy = this.mainAreaBottom() - wh;\\nif (this._goldWindow) wy -= this._goldWindow.height;\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param ParamBreak1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param CommandWindowStyle:str
 * @text Command Window Style
 * @type select
 * @option Default Vertical Side Style
 * @value default
 * @option Top Horizontal Style
 * @value top
 * @option Thin Top Horizontal Style
 * @value thinTop
 * @option Bottom Horizontal Style
 * @value bottom
 * @option Thin Bottom Horizontal Style
 * @value thinBottom
 * @option Mobile Full Screen Style
 * @value mobile
 * @desc Choose the positioning and style of the Main Menu Command Window. This will automatically rearrange windows.
 * @default top
 *
 * @param CustomCmdWin:struct
 * @text Command Style Settings
 * @parent CommandWindowStyle:str
 * @type struct<CustomCmdWin>
 * @desc Settings for the non-default Command Window Styles.
 * @default {"Style:str":"auto","TextAlign:str":"center","Rows:num":"2","Cols:num":"4","MobileThickness:num":"5"}
 *
 * @param ParamBreak2
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param StatusGraphic:str
 * @text Status Graphic
 * @type select
 * @option None
 * @value none
 * @option Face
 * @value face
 * @option Map Sprite
 * @value sprite
 * @option Sideview Battler
 * @value svbattler
 * @desc Choose how the actor graphics appear in status-like menus.
 * @default face
 *
 * @param StatusListStyle:str
 * @text Main Menu List Style
 * @type select
 * @option Default Horizontal Style
 * @value default
 * @option Vertical Style
 * @value vertical
 * @option Portrait Style
 * @value portrait
 * @option Solo Style
 * @value solo
 * @option Thin Horizontal Style
 * @value thin
 * @option Thicker Horizontal Style
 * @value thicker
 * @desc Choose how the actor status list looks in the Main Menu.
 * @default portrait
 *
 * @param InnerMenuListStyle:str
 * @text Inner-Menu List Style
 * @parent StatusListStyle:str
 * @type select
 * @option Default Horizontal Style
 * @value default
 * @option Vertical Style
 * @value vertical
 * @option Portrait Style
 * @value portrait
 * @option Solo Style
 * @value solo
 * @option Thin Horizontal Style
 * @value thin
 * @option Thicker Horizontal Style
 * @value thicker
 * @desc Choose how the actor status list looks in the inner menus
 * like Scene_Item, Scene_Skill, etc.
 * @default default
 *
 * @param ListStyles:struct
 * @text List Style Settings
 * @parent StatusListStyle:str
 * @type struct<ListStyles>
 * @desc JavaScript code used to determine how the individual styles are drawn.
 * @default {"DefaultStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n// Draw Actor Graphic\\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\\nconst gy = rect.y + (this.graphicType() === 'face' ? 1 : 0);\\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\\n\\n// Draw Status Stuff\\nconst sx = rect.x + 180;\\nconst sy = rect.y + rect.height / 2 - this.lineHeight() * 1.5;\\nconst lineHeight = this.lineHeight();\\nconst sx2 = sx + 180;\\nthis.drawActorName(actor, sx, sy);\\nthis.drawActorLevel(actor, sx, sy + lineHeight * 1);\\nthis.drawActorIcons(actor, sx, sy + lineHeight * 2);\\nthis.drawActorClass(actor, sx2, sy);\\n\\n// Place Gauges\\nconst sy2 = sy + lineHeight;\\nconst gaugeLineHeight = this.gaugeLineHeight();\\nthis.placeGauge(actor, \\\"hp\\\", sx2, sy2);\\nthis.placeGauge(actor, \\\"mp\\\", sx2, sy2 + gaugeLineHeight);\\nconst roomForTp = (sy2 + gaugeLineHeight * 3) <= rect.y + rect.height;\\nif ($dataSystem.optDisplayTp && roomForTp) {\\n    this.placeGauge(actor, \\\"tp\\\", sx2, sy2 + gaugeLineHeight * 2);\\n}\\n\\n// Following Requires VisuStella MZ's Core Engine\\n// Draw Additional Parameter Data if Enough Room\\nconst sx3 = sx2 + 180;\\nconst sw = rect.width - sx3 - 2;\\nif (Imported.VisuMZ_0_CoreEngine && sw >= 300) {\\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\\n    const pw = Math.floor(sw / 2) - 24;\\n    let px = sx3;\\n    let py = rect.y + Math.floor((rect.height - (Math.ceil(params.length / 2) * gaugeLineHeight)) / 2);\\n    let counter = 0;\\n    for (const param of params) {\\n        this.resetFontSettings();\\n        this.drawParamText(px, py, pw, param, true);\\n        this.resetTextColor();\\n        this.contents.fontSize -= 8;\\n        const paramValue = actor.paramValueByName(param, true);\\n        this.contents.drawText(paramValue, px, py, pw, gaugeLineHeight, 'right');\\n        counter++;\\n        if (counter % 2 === 0) {\\n            px = sx3;\\n            py += gaugeLineHeight;\\n        } else {\\n            px += pw + 24;\\n        }\\n    }\\n}\"","VerticalStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n\\nconst lineHeight = this.lineHeight();\\nconst gaugeLineHeight = this.gaugeLineHeight();\\nconst totalHeight = (lineHeight * 4.5) + (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2));\\n\\n// Draw Actor Graphic\\nconst gw = rect.width;\\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\\nconst gx = rect.x;\\nconst gy = Math.max(rect.y, rect.y + (rect.height - totalHeight - gh) / 2);\\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\\n\\n// Draw Actor Name\\nlet sx = rect.x;\\nlet sy = Math.max(gy + gh, rect.y + (rect.height - totalHeight + gh) / 2);\\nlet sw = rect.width;\\nthis.drawText(actor.name(), sx, sy, sw, 'center');\\n\\n// Draw Actor Level\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.drawActorLevel(actor, sx, sy);\\n\\n// Draw Actor Class\\nconst className = actor.currentClass().name;\\nsx = rect.x + Math.round((rect.width - this.textSizeEx(className).width) / 2);\\nsy += lineHeight;\\nthis.drawTextEx(className, sx, sy, sw);\\n\\n// Draw Actor Icons\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.drawActorIcons(actor, sx, sy);\\n\\n// Draw Gauges\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.placeGauge(actor, \\\"hp\\\", sx, sy);\\nsy += gaugeLineHeight;\\nthis.placeGauge(actor, \\\"mp\\\", sx, sy);\\nsy += gaugeLineHeight;\\nif ($dataSystem.optDisplayTp) {\\n    this.placeGauge(actor, \\\"tp\\\", sx, sy);\\n}\"","PortraitStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n// Make Constants\\nconst lineHeight = this.lineHeight();\\nconst gaugeLineHeight = this.gaugeLineHeight();\\nconst totalHeight = (lineHeight * 4.5) + (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2));\\n\\n// Draw Actor Graphic\\nconst gw = rect.width;\\nconst gh = rect.height;\\nconst gx = rect.x;\\nconst gy = rect.y;\\nthis.drawItemActorMenuImage(actor, gx, gy, gw, gh);\\n\\n// Draw Dark Rectangle\\nlet sx = rect.x;\\nlet sy = Math.max(rect.y, rect.y + (rect.height - totalHeight));\\nlet sw = rect.width;\\nlet sh = rect.y + rect.height - sy;\\nthis.contents.fillRect(sx+1, sy+lineHeight/2, sw-2, sh-1-lineHeight/2, ColorManager.dimColor1());\\nthis.contents.gradientFillRect(sx+1, sy-lineHeight/2, sw-2, lineHeight, ColorManager.dimColor2(), ColorManager.dimColor1(), true);\\n\\n// Draw Actor Name\\nthis.drawText(actor.name(), sx, sy, sw, 'center');\\n\\n// Draw Actor Level\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.drawActorLevel(actor, sx, sy);\\n\\n// Draw Actor Class\\nconst className = actor.currentClass().name;\\nsx = rect.x + Math.round((rect.width - this.textSizeEx(className).width) / 2);\\nsy += lineHeight;\\nthis.drawTextEx(className, sx, sy, sw);\\n\\n// Draw Actor Icons\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.drawActorIcons(actor, sx, sy);\\n\\n// Draw Gauges\\nsx = rect.x + Math.round((rect.width - 128) / 2);\\nsy += lineHeight;\\nthis.placeGauge(actor, \\\"hp\\\", sx, sy);\\nsy += gaugeLineHeight;\\nthis.placeGauge(actor, \\\"mp\\\", sx, sy);\\nsy += gaugeLineHeight;\\nif ($dataSystem.optDisplayTp) {\\n    this.placeGauge(actor, \\\"tp\\\", sx, sy);\\n}\"","SoloStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n// Make Constants\\nconst lineHeight = this.lineHeight();\\nconst gaugeLineHeight = this.gaugeLineHeight();\\n\\n// Draw Actor Graphic\\nlet sx = rect.x;\\nlet sy = rect.y;\\nlet sw = rect.width;\\nlet sh = rect.height;\\n\\n// Portrait\\nif (actor.getMenuImage() !== '') {\\n    this.drawItemActorMenuImage(actor, rect.x, rect.y, rect.width, rect.height);\\n\\n// Everything Else\\n} else {\\n    const gx = Math.max(0, rect.x + Math.floor(((rect.width / 2) - ImageManager.faceWidth) / 2));\\n    const gy = Math.max(0, rect.y + rect.height - Math.ceil(lineHeight * 4.5) - ImageManager.faceHeight);\\n    this.drawActorGraphic(actor, gx, gy, ImageManager.faceWidth, ImageManager.faceHeight);\\n}\\n\\n// Draw Dark Rectangle\\nsh = Math.ceil(lineHeight * 4.5);\\nsy = rect.y + rect.height - sh;\\nthis.contents.fillRect(sx+1, sy+lineHeight/2, sw-2, sh-1-lineHeight/2, ColorManager.dimColor1());\\nthis.contents.gradientFillRect(sx+1, sy-lineHeight/2, sw-2, lineHeight, ColorManager.dimColor2(), ColorManager.dimColor1(), true);\\n\\n// Draw Actor Name\\nsw = Math.round(rect.width / 2);\\nthis.drawText(actor.name(), sx, sy, sw, 'center');\\n\\n// Draw Actor Level\\nsx = Math.max(0, rect.x + Math.floor(((rect.width / 2) - 128) / 2));\\nsy += lineHeight;\\nthis.drawActorLevel(actor, sx, sy);\\n\\n// Draw Actor Class\\nconst className = actor.currentClass().name;\\nsx = rect.x + Math.round(((rect.width / 2) - this.textSizeEx(className).width) / 2);\\nsy += lineHeight;\\nthis.drawTextEx(className, sx, sy, sw);\\n\\n// Draw Actor Icons\\nsx = rect.x + Math.round(((rect.width / 2) - 128) / 2);\\nsy += lineHeight;\\nthis.drawActorIcons(actor, sx, sy);\\n\\n// Prepare Stat Coordinates\\nsx = rect.x + Math.floor(rect.width / 2);\\nsw = rect.width / 2;\\nsh = rect.height;\\nconst sx3 = sx;\\nconst cw = rect.width - sx3 - 2;\\n\\n// Prepare Total Content Height to vertically center the content.\\nlet totalHeight = gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2);\\nif (Imported.VisuMZ_0_CoreEngine && cw >= 300) {\\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\\n    totalHeight += lineHeight;\\n    totalHeight += Math.ceil(params.length / 2) * gaugeLineHeight;\\n}\\nconst equips = actor.equips();\\ntotalHeight += lineHeight;\\ntotalHeight += equips.length * lineHeight;\\nsy = Math.max(rect.y, rect.y + Math.floor((rect.height - totalHeight) / 2));\\n\\n// Place Gauges\\nthis.placeGauge(actor, \\\"hp\\\", sx, sy);\\nsy += gaugeLineHeight;\\nthis.placeGauge(actor, \\\"mp\\\", sx, sy);\\nif ($dataSystem.optDisplayTp) {\\n    sy += gaugeLineHeight;\\n    this.placeGauge(actor, \\\"tp\\\", sx, sy);\\n    sy += gaugeLineHeight;\\n}\\nlet ny = sy;\\n\\n// Following Requires VisuStella MZ's Core Engine\\n// Draw Additional Parameter Data if Enough Room\\nif (Imported.VisuMZ_0_CoreEngine && cw >= 300) {\\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\\n    sy += lineHeight;\\n    const pw = Math.floor(cw / 2) - 24;\\n    let px = sx3;\\n    let counter = 0;\\n    for (const param of params) {\\n        this.resetFontSettings();\\n        this.drawParamText(px, sy, pw, param, true);\\n        this.resetTextColor();\\n        this.contents.fontSize -= 8;\\n        const paramValue = actor.paramValueByName(param, true);\\n        this.contents.drawText(paramValue, px, sy, pw, gaugeLineHeight, 'right');\\n        counter++;\\n        if (counter % 2 === 0) {\\n            px = sx3;\\n            sy += gaugeLineHeight;\\n        } else {\\n            px += pw + 24;\\n        }\\n    }\\n    ny += lineHeight;\\n    ny += Math.ceil(params.length / 2) * gaugeLineHeight;\\n}\\n\\n// Draw Actor Equipment\\nthis.resetFontSettings();\\nsx = rect.x + Math.floor(rect.width / 2);\\nsy = ny + lineHeight;\\nsw = rect.width / 2;\\nfor (const equip of equips) {\\n    if (equip) {\\n        this.drawItemName(equip, sx, sy, sw);\\n        sy += lineHeight;\\n        if (sy + lineHeight > rect.y + rect.height) return;\\n    }\\n}\"","ThinStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n// Draw Actor Graphic\\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\\nconst gy = rect.y + (this.graphicType() === 'face' ? 1 : 0);\\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\\n\\n// Draw Status Stuff\\nconst lineHeight = this.lineHeight();\\nlet sx = rect.x + 160;\\nlet sy = rect.y + ((rect.height - lineHeight) / 2) - 2;\\n\\n// Draw Actor Data\\nthis.drawActorName(actor, sx, sy);\\nthis.drawActorLevel(actor, gx+8, rect.y + rect.height - lineHeight +1);\\n\\n// Place Gauges\\nsx += 180;\\nsy += (lineHeight - this.gaugeLineHeight()) / 2;\\nthis.placeGauge(actor, \\\"hp\\\", sx, sy);\\nsx += 140;\\nif ((sx + 128) > rect.x + rect.width) return;\\nthis.placeGauge(actor, \\\"mp\\\", sx, sy);\\nsx += 140;\\nif ((sx + 128) > rect.x + rect.width) return;\\nif ($dataSystem.optDisplayTp) this.placeGauge(actor, \\\"tp\\\", sx, sy);\"","ThickerStyle:func":"\"// Declare Constants\\nconst actor = arguments[0];\\nconst rect = arguments[1];\\n\\n// Draw Actor Graphic\\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\\nconst gy = rect.y + (this.graphicType() === 'face' ? 1 : 0);\\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\\n\\n// Draw Status Stuff\\nconst lineHeight = this.lineHeight();\\nconst gaugeLineHeight = this.gaugeLineHeight();\\nlet sx = rect.x + 160;\\nlet sy = rect.y + ((rect.height - (lineHeight * 2)) / 2) - 2;\\n\\n// Draw Actor Data\\nthis.drawActorLevel(actor, gx+8, rect.y + rect.height - lineHeight +1);\\nthis.drawActorName(actor, sx, sy);\\nthis.drawActorClass(actor, sx, sy + lineHeight);\\n//this.drawActorLevel(actor, sx, sy + lineHeight);\\n\\n// Place Gauges\\nsx += 180;\\nsy = rect.y + ((rect.height - (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2))) / 2) - 2;\\nthis.placeGauge(actor, \\\"hp\\\", sx, sy);\\nthis.placeGauge(actor, \\\"mp\\\", sx, sy + gaugeLineHeight);\\nif ($dataSystem.optDisplayTp) this.placeGauge(actor, \\\"tp\\\", sx, sy + (gaugeLineHeight * 2));\\nsx += 160;\\n\\n// Following Requires VisuStella MZ's Core Engine\\n// Draw Additional Parameter Data if Enough Room\\nconst sx3 = sx;\\nconst sw = rect.width - sx3 - 2;\\nif (Imported.VisuMZ_0_CoreEngine && sw >= 300) {\\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\\n    const pw = Math.floor(sw / 2) - 24;\\n    sy = rect.y + ((rect.height - (gaugeLineHeight * Math.ceil(params.length / 2))) / 2) - 2;\\n    let px = sx3;\\n    let py = rect.y + Math.floor((rect.height - (Math.ceil(params.length / 2) * gaugeLineHeight)) / 2);\\n    let counter = 0;\\n    for (const param of params) {\\n        this.resetFontSettings();\\n        this.drawParamText(px, py, pw, param, true);\\n        this.resetTextColor();\\n        this.contents.fontSize -= 8;\\n        const paramValue = actor.paramValueByName(param, true);\\n        this.contents.drawText(paramValue, px, py, pw, gaugeLineHeight, 'right');\\n        counter++;\\n        if (counter % 2 === 0) {\\n            px = sx3;\\n            py += gaugeLineHeight;\\n        } else {\\n            px += pw + 24;\\n        }\\n    }\\n}\""}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Commands Struct
 * ----------------------------------------------------------------------------
 */
/*~struct~Command:
 *
 * @param Symbol:str
 * @text Symbol
 * @desc The symbol used for this command.
 * @default Symbol
 *
 * @param Subcategory:str
 * @text Subcategory
 * @desc The subcategory used for this command.
 * Leave empty for no subcategory.
 * @default
 *
 * @param Icon:num
 * @text Icon
 * @desc Icon used for this command.
 * Use 0 for no icon.
 * @default 0
 *
 * @param TextStr:str
 * @text STR: Text
 * @desc Displayed text used for this menu command.
 * If this has a value, ignore the JS: Text version.
 * @default Untitled
 *
 * @param TextJS:func
 * @text JS: Text
 * @type note
 * @desc JavaScript code used to determine string used for the displayed name.
 * @default "return 'Text';"
 *
 * @param ShowJS:func
 * @text JS: Show
 * @type note
 * @desc JavaScript code used to determine if the item is shown or not.
 * @default "return true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @type note
 * @desc JavaScript code used to determine if the item is enabled or not.
 * @default "return true;"
 *
 * @param ExtJS:func
 * @text JS: Ext
 * @type note
 * @desc JavaScript code used to determine any ext data that should be added.
 * @default "return null;"
 *
 * @param CallHandlerJS:func
 * @text JS: Run Code
 * @type note
 * @desc JavaScript code that runs once this command is selected.
 * @default "const ext = arguments[0];"
 *
 * @param PersonalHandlerJS:func
 * @text JS: Personal Code
 * @type note
 * @desc JavaScript code that runs once the actor list is selected with this command highlighted.
 * @default "const ext = arguments[0];"
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param GoldWindow
 * @text Gold Window
 *
 * @param ThinGoldWindow:eval
 * @text Thinner Gold Window
 * @parent GoldWindow
 * @type boolean
 * @on Thinner
 * @off Normal
 * @desc Make the Gold Window thinner in the Main Menu?
 * Used to match the Playtime and Variable Windows.
 * @default true
 *
 * @param AutoGoldHeight:eval
 * @text Auto Adjust Height
 * @parent GoldWindow
 * @type boolean
 * @on Automatic
 * @off Manual
 * @desc Automatically adjust the height for the thinner Gold Window?
 * @default true
 *
 * @param AutoGoldY:eval
 * @text Auto Adjust Y
 * @parent GoldWindow
 * @type boolean
 * @on Automatic
 * @off Manual
 * @desc Automatically adjust the Y position for the thinner Gold Window?
 * @default true
 *
 * @param StatusWindow
 * @text Status Window
 *
 * @param StatusSelectLast:eval
 * @text Select Last?
 * @parent StatusWindow
 * @type boolean
 * @on Last Picked Actor
 * @off Always First Actor
 * @desc When picking a personal command from the Command Window,
 * select the last picked actor or always the first?
 * @default false
 *
 * @param SoloParty
 * @text Solo Party
 *
 * @param SoloQuick:eval
 * @text Solo Quick Mode
 * @parent SoloParty
 * @type boolean
 * @on Quick
 * @off Normal
 * @desc When selecting "Skills", "Equip", or "Status" with one party member, immediately go to the scene.
 * @default true
 *
 * @param SubMenus
 * @text Sub Menus
 *
 * @param ActorBgMenus:arraystr
 * @text Menus with Actor BG's
 * @parent SubMenus
 * @type string[]
 * @desc A list of the menus that would be compatible with Actor Menu Backgrounds.
 * @default ["Scene_Skill","Scene_Equip","Scene_Status"]
 *
 * @param ActorBgMenuJS:func
 * @text JS: Actor BG Action
 * @parent SubMenus
 * @type note
 * @desc Code used to determine how to display the sprites upon loading.
 * @default "this.anchor.x = 0.5;\nconst scale = 1.25;\nthis.scale.x = this.scale.y = scale;\nthis.x = Graphics.width;\nthis.y = Graphics.height - (this.bitmap.height * Math.abs(scale));\nthis._targetX = Graphics.width * 3 / 4;\nthis._targetY = Graphics.height - (this.bitmap.height * Math.abs(scale));\nthis._duration = 10;\nthis.opacity = 0;"
 *
 * @param PartyWindow
 * @text Party Window
 *
 * @param ShowReserve:eval
 * @text Show Reserve Memebers
 * @parent PartyWindow
 * @type boolean
 * @on Show Reserve Members
 * @off Hide Reserve Members
 * @desc Show reserve members while on the Main Menu scene?
 * @default true
 *
 * @param HideMainMenuOnly:eval
 * @text Hide Main Menu Only
 * @parent ShowReserve:eval
 * @type boolean
 * @on Hide in Main Menu Only
 * @off Hide in all Scenes
 * @desc If reserve members are hidden, hide them only in the main menu or all scenes?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Playtime Window
 * ----------------------------------------------------------------------------
 */
/*~struct~Playtime:
 *
 * @param Enable:eval
 * @text Use Window
 * @type boolean
 * @on Enable
 * @off Don't
 * @desc Use the Playtime Window?
 * @default true
 *
 * @param AdjustCommandHeight:eval
 * @text Adjust Command Window
 * @type boolean
 * @on Enable
 * @off Normal
 * @desc Adjust the command window's height to fit in the Playtime Window?
 * @default true
 *
 * @param BgType:num
 * @text Background Type
 * @type select
 * @option Window
 * @value 0
 * @option Dim
 * @value 1
 * @option Transparent
 * @value 2
 * @desc Select background type for the Playtime window.
 * @default 0
 *
 * @param FontSize:num
 * @text Font Size
 * @type number
 * @min 1
 * @desc Font size used for displaying Gold inside the Playtime window.
 * Default: 26
 * @default 20
 *
 * @param Icon:num
 * @text Time Icon
 * @desc Icon displayed for the 'Time' label.
 * @default 75
 *
 * @param Time:str
 * @text Time Text
 * @desc Text for the display of 'Time' in the Playtime window.
 * @default Time
 *
 * @param WindowRect:func
 * @text JS: X, Y, W, H
 * @type note
 * @desc Code used to determine the dimensions for the Playtime window.
 * @default "const rows = 1;\nconst ww = this.mainCommandWidth();\nconst wh = this.calcWindowHeight(rows, false);\nconst wx = this.isRightInputMode() ? Graphics.boxWidth - ww : 0;\nlet wy = this.mainAreaBottom() - wh;\nif (this._goldWindow) wy -= this._goldWindow.height;\nif (this.canCreateVariableWindow()) wy -= this.variableWindowRect().height;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Variable Window
 * ----------------------------------------------------------------------------
 */
/*~struct~Variable:
 *
 * @param Enable:eval
 * @text Use Window
 * @type boolean
 * @on Enable
 * @off Don't
 * @desc Use the Variable Window?
 * @default false
 *
 * @param AdjustCommandHeight:eval
 * @text Adjust Command Window
 * @type boolean
 * @on Enable
 * @off Normal
 * @desc Adjust the command window's height to fit in the Variable Window?
 * @default true
 *
 * @param BgType:num
 * @text Background Type
 * @type select
 * @option Window
 * @value 0
 * @option Dim
 * @value 1
 * @option Transparent
 * @value 2
 * @desc Select background type for the Variable window.
 * @default 0
 *
 * @param FontSize:num
 * @text Font Size
 * @type number
 * @min 1
 * @desc Font size used for displaying Gold inside the Variable window.
 * Default: 26
 * @default 20
 *
 * @param VarList:arraynum
 * @text Variable List
 * @type variable[]
 * @desc Select variables to be displayed into the window.
 * Use \i[x] to determine their icon.
 * @default ["1","2","3"]
 *
 * @param WindowRect:func
 * @text JS: X, Y, W, H
 * @type note
 * @desc Code used to determine the dimensions for the Variable window.
 * @default "const rows = VisuMZ.MainMenuCore.Settings.Variable.VarList.length;\nconst ww = this.mainCommandWidth();\nconst wh = this.calcWindowHeight(rows, false);\nconst wx = this.isRightInputMode() ? Graphics.boxWidth - ww : 0;\nlet wy = this.mainAreaBottom() - wh;\nif (this._goldWindow) wy -= this._goldWindow.height;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Horizontal Command Window Style
 * ----------------------------------------------------------------------------
 */
/*~struct~CustomCmdWin:
 *
 * @param Style:str
 * @text Command Style
 * @parent MainList
 * @type select
 * @option Text Only
 * @value text
 * @option Icon Only
 * @value icon
 * @option Icon + Text
 * @value iconText
 * @option Automatic
 * @value auto
 * @desc How do you wish to draw command entries in the Command Window?
 * @default auto
 *
 * @param TextAlign:str
 * @text Text Alignment
 * @type combo
 * @option left
 * @option center
 * @option right
 * @desc Decide how you want the text to be aligned.
 * @default center
 *
 * @param Rows:num
 * @text Rows
 * @type number
 * @min 1
 * @desc Number of visible rows.
 * @default 2
 *
 * @param Cols:num
 * @text Columns
 * @type number
 * @min 1
 * @desc Number of maximum columns.
 * @default 4
 *
 * @param MobileThickness:num
 * @text Mobile Thickness
 * @type number
 * @min 1
 * @desc The thickness of the buttons for mobile version.
 * @default 5
 *
 */
/* ----------------------------------------------------------------------------
 * List Style Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ListStyles:
 *
 * @param DefaultStyle:func
 * @text JS: Default
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n// Draw Actor Graphic\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\nconst gy = rect.y + Math.floor((rect.height - gh) / 2);\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\n\n// Draw Status Stuff\nconst sx = rect.x + 180;\nconst sy = rect.y + rect.height / 2 - this.lineHeight() * 1.5;\nconst lineHeight = this.lineHeight();\nconst sx2 = sx + 180;\nthis.drawActorName(actor, sx, sy);\nthis.drawActorLevel(actor, sx, sy + lineHeight * 1);\nthis.drawActorIcons(actor, sx, sy + lineHeight * 2);\nthis.drawActorClass(actor, sx2, sy);\n\n// Place Gauges\nconst sy2 = sy + lineHeight;\nconst gaugeLineHeight = this.gaugeLineHeight();\nthis.placeGauge(actor, \"hp\", sx2, sy2);\nthis.placeGauge(actor, \"mp\", sx2, sy2 + gaugeLineHeight);\nconst roomForTp = (sy2 + gaugeLineHeight * 3) <= rect.y + rect.height;\nif ($dataSystem.optDisplayTp && roomForTp) {\n    this.placeGauge(actor, \"tp\", sx2, sy2 + gaugeLineHeight * 2);\n}\n\n// Following Requires VisuStella MZ's Core Engine\n// Draw Additional Parameter Data if Enough Room\nconst sx3 = sx2 + 180;\nconst sw = rect.width - sx3 - 2;\nif (Imported.VisuMZ_0_CoreEngine && sw >= 300) {\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\n    const pw = Math.floor(sw / 2) - 24;\n    let px = sx3;\n    let py = rect.y + Math.floor((rect.height - (Math.ceil(params.length / 2) * gaugeLineHeight)) / 2);\n    let counter = 0;\n    for (const param of params) {\n        this.resetFontSettings();\n        this.drawParamText(px, py, pw, param, true);\n        this.resetTextColor();\n        this.contents.fontSize -= 8;\n        const paramValue = actor.paramValueByName(param, true);\n        this.contents.drawText(paramValue, px, py, pw, gaugeLineHeight, 'right');\n        counter++;\n        if (counter % 2 === 0) {\n            px = sx3;\n            py += gaugeLineHeight;\n        } else {\n            px += pw + 24;\n        }\n    }\n}"
 *
 * @param VerticalStyle:func
 * @text JS: Vertical
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n\nconst lineHeight = this.lineHeight();\nconst gaugeLineHeight = this.gaugeLineHeight();\nconst totalHeight = (lineHeight * 4.5) + (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2));\n\n// Draw Actor Graphic\nconst gw = rect.width;\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\nconst gx = rect.x;\nconst gy = Math.max(rect.y, rect.y + (rect.height - totalHeight - gh) / 2);\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\n\n// Draw Actor Name\nlet sx = rect.x;\nlet sy = Math.max(gy + gh, rect.y + (rect.height - totalHeight + gh) / 2);\nlet sw = rect.width;\nthis.drawText(actor.name(), sx, sy, sw, 'center');\n\n// Draw Actor Level\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.drawActorLevel(actor, sx, sy);\n\n// Draw Actor Class\nconst className = actor.currentClass().name;\nsx = rect.x + Math.round((rect.width - this.textSizeEx(className).width) / 2);\nsy += lineHeight;\nthis.drawTextEx(className, sx, sy, sw);\n\n// Draw Actor Icons\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.drawActorIcons(actor, sx, sy);\n\n// Draw Gauges\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.placeGauge(actor, \"hp\", sx, sy);\nsy += gaugeLineHeight;\nthis.placeGauge(actor, \"mp\", sx, sy);\nsy += gaugeLineHeight;\nif ($dataSystem.optDisplayTp) {\n    this.placeGauge(actor, \"tp\", sx, sy);\n}"
 *
 * @param PortraitStyle:func
 * @text JS: Portrait
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n// Make Constants\nconst lineHeight = this.lineHeight();\nconst gaugeLineHeight = this.gaugeLineHeight();\nconst totalHeight = (lineHeight * 4.5) + (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2));\n\n// Draw Actor Graphic\nconst gw = rect.width;\nconst gh = rect.height;\nconst gx = rect.x;\nconst gy = rect.y;\nthis.drawItemActorMenuImage(actor, gx, gy, gw, gh);\n\n// Draw Dark Rectangle\nlet sx = rect.x;\nlet sy = Math.max(rect.y, rect.y + (rect.height - totalHeight));\nlet sw = rect.width;\nlet sh = rect.y + rect.height - sy;\nthis.contents.fillRect(sx+1, sy+lineHeight/2, sw-2, sh-1-lineHeight/2, ColorManager.dimColor1());\nthis.contents.gradientFillRect(sx+1, sy-lineHeight/2, sw-2, lineHeight, ColorManager.dimColor2(), ColorManager.dimColor1(), true);\n\n// Draw Actor Name\nthis.drawText(actor.name(), sx, sy, sw, 'center');\n\n// Draw Actor Level\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.drawActorLevel(actor, sx, sy);\n\n// Draw Actor Class\nconst className = actor.currentClass().name;\nsx = rect.x + Math.round((rect.width - this.textSizeEx(className).width) / 2);\nsy += lineHeight;\nthis.drawTextEx(className, sx, sy, sw);\n\n// Draw Actor Icons\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.drawActorIcons(actor, sx, sy);\n\n// Draw Gauges\nsx = rect.x + Math.round((rect.width - 128) / 2);\nsy += lineHeight;\nthis.placeGauge(actor, \"hp\", sx, sy);\nsy += gaugeLineHeight;\nthis.placeGauge(actor, \"mp\", sx, sy);\nsy += gaugeLineHeight;\nif ($dataSystem.optDisplayTp) {\n    this.placeGauge(actor, \"tp\", sx, sy);\n}"
 *
 * @param SoloStyle:func
 * @text JS: Solo
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n// Make Constants\nconst lineHeight = this.lineHeight();\nconst gaugeLineHeight = this.gaugeLineHeight();\n\n// Draw Actor Graphic\nlet sx = rect.x;\nlet sy = rect.y;\nlet sw = rect.width;\nlet sh = rect.height;\n\n// Portrait\nif (actor.getMenuImage() !== '') {\n    this.drawItemActorMenuImage(actor, rect.x, rect.y, rect.width, rect.height);\n\n// Everything Else\n} else {\n    const gx = Math.max(0, rect.x + Math.floor(((rect.width / 2) - ImageManager.faceWidth) / 2));\n    const gy = Math.max(0, rect.y + rect.height - Math.ceil(lineHeight * 4.5) - ImageManager.faceHeight);\n    this.drawActorGraphic(actor, gx, gy, ImageManager.faceWidth, ImageManager.faceHeight);\n}\n\n// Draw Dark Rectangle\nsh = Math.ceil(lineHeight * 4.5);\nsy = rect.y + rect.height - sh;\nthis.contents.fillRect(sx+1, sy+lineHeight/2, sw-2, sh-1-lineHeight/2, ColorManager.dimColor1());\nthis.contents.gradientFillRect(sx+1, sy-lineHeight/2, sw-2, lineHeight, ColorManager.dimColor2(), ColorManager.dimColor1(), true);\n\n// Draw Actor Name\nsw = Math.round(rect.width / 2);\nthis.drawText(actor.name(), sx, sy, sw, 'center');\n\n// Draw Actor Level\nsx = Math.max(0, rect.x + Math.floor(((rect.width / 2) - 128) / 2));\nsy += lineHeight;\nthis.drawActorLevel(actor, sx, sy);\n\n// Draw Actor Class\nconst className = actor.currentClass().name;\nsx = rect.x + Math.round(((rect.width / 2) - this.textSizeEx(className).width) / 2);\nsy += lineHeight;\nthis.drawTextEx(className, sx, sy, sw);\n\n// Draw Actor Icons\nsx = rect.x + Math.round(((rect.width / 2) - 128) / 2);\nsy += lineHeight;\nthis.drawActorIcons(actor, sx, sy);\n\n// Prepare Stat Coordinates\nsx = rect.x + Math.floor(rect.width / 2);\nsw = rect.width / 2;\nsh = rect.height;\nconst sx3 = sx;\nconst cw = rect.width - sx3 - 2;\n\n// Prepare Total Content Height to vertically center the content.\nlet totalHeight = gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2);\nif (Imported.VisuMZ_0_CoreEngine && cw >= 300) {\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\n    totalHeight += lineHeight;\n    totalHeight += Math.ceil(params.length / 2) * gaugeLineHeight;\n}\nconst equips = actor.equips();\ntotalHeight += lineHeight;\ntotalHeight += equips.length * lineHeight;\nsy = Math.max(rect.y, rect.y + Math.floor((rect.height - totalHeight) / 2));\n\n// Place Gauges\nthis.placeGauge(actor, \"hp\", sx, sy);\nsy += gaugeLineHeight;\nthis.placeGauge(actor, \"mp\", sx, sy);\nif ($dataSystem.optDisplayTp) {\n    sy += gaugeLineHeight;\n    this.placeGauge(actor, \"tp\", sx, sy);\n    sy += gaugeLineHeight;\n}\nlet ny = sy;\n\n// Following Requires VisuStella MZ's Core Engine\n// Draw Additional Parameter Data if Enough Room\nif (Imported.VisuMZ_0_CoreEngine && cw >= 300) {\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\n    sy += lineHeight;\n    const pw = Math.floor(cw / 2) - 24;\n    let px = sx3;\n    let counter = 0;\n    for (const param of params) {\n        this.resetFontSettings();\n        this.drawParamText(px, sy, pw, param, true);\n        this.resetTextColor();\n        this.contents.fontSize -= 8;\n        const paramValue = actor.paramValueByName(param, true);\n        this.contents.drawText(paramValue, px, sy, pw, gaugeLineHeight, 'right');\n        counter++;\n        if (counter % 2 === 0) {\n            px = sx3;\n            sy += gaugeLineHeight;\n        } else {\n            px += pw + 24;\n        }\n    }\n    ny += lineHeight;\n    ny += Math.ceil(params.length / 2) * gaugeLineHeight;\n}\n\n// Draw Actor Equipment\nthis.resetFontSettings();\nsx = rect.x + Math.floor(rect.width / 2);\nsy = ny + lineHeight;\nsw = rect.width / 2;\nfor (const equip of equips) {\n    if (equip) {\n        this.drawItemName(equip, sx, sy, sw);\n        sy += lineHeight;\n        if (sy + lineHeight > rect.y + rect.height) return;\n    }\n}"
 *
 * @param ThinStyle:func
 * @text JS: Thin
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n// Draw Actor Graphic\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\nconst gy = rect.y + (this.graphicType() === 'face' ? 1 : 0);\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\n\n// Draw Status Stuff\nconst lineHeight = this.lineHeight();\nlet sx = rect.x + 160;\nlet sy = rect.y + ((rect.height - lineHeight) / 2) - 2;\n\n// Draw Actor Data\nthis.drawActorName(actor, sx, sy);\nthis.drawActorLevel(actor, gx+8, rect.y + rect.height - lineHeight +1);\n\n// Place Gauges\nsx += 180;\nsy += (lineHeight - this.gaugeLineHeight()) / 2;\nthis.placeGauge(actor, \"hp\", sx, sy);\nsx += 140;\nif ((sx + 128) > rect.x + rect.width) return;\nthis.placeGauge(actor, \"mp\", sx, sy);\nsx += 140;\nif ((sx + 128) > rect.x + rect.width) return;\nif ($dataSystem.optDisplayTp) this.placeGauge(actor, \"tp\", sx, sy);"
 *
 * @param ThickerStyle:func
 * @text JS: Thicker
 * @type note
 * @desc Code used to draw the data for this particular style.
 * @default "// Declare Constants\nconst actor = arguments[0];\nconst rect = arguments[1];\n\n// Draw Actor Graphic\nconst gx = rect.x + (this.graphicType() === 'face' ? 1 : 0);\nconst gy = rect.y + (this.graphicType() === 'face' ? 1 : 0);\nconst gw = Math.min(rect.width, ImageManager.faceWidth);\nconst gh = Math.min(rect.height, ImageManager.faceHeight);\nthis.drawActorGraphic(actor, gx, gy, gw, gh);\n\n// Draw Status Stuff\nconst lineHeight = this.lineHeight();\nconst gaugeLineHeight = this.gaugeLineHeight();\nlet sx = rect.x + 160;\nlet sy = rect.y + ((rect.height - (lineHeight * 2)) / 2) - 2;\n\n// Draw Actor Data\nthis.drawActorLevel(actor, gx+8, rect.y + rect.height - lineHeight +1);\nthis.drawActorName(actor, sx, sy);\nthis.drawActorClass(actor, sx, sy + lineHeight);\n//this.drawActorLevel(actor, sx, sy + lineHeight);\n\n// Place Gauges\nsx += 180;\nsy = rect.y + ((rect.height - (gaugeLineHeight * ($dataSystem.optDisplayTp ? 3 : 2))) / 2) - 2;\nthis.placeGauge(actor, \"hp\", sx, sy);\nthis.placeGauge(actor, \"mp\", sx, sy + gaugeLineHeight);\nif ($dataSystem.optDisplayTp) this.placeGauge(actor, \"tp\", sx, sy + (gaugeLineHeight * 2));\nsx += 160;\n\n// Following Requires VisuStella MZ's Core Engine\n// Draw Additional Parameter Data if Enough Room\nconst sx3 = sx;\nconst sw = rect.width - sx3 - 2;\nif (Imported.VisuMZ_0_CoreEngine && sw >= 300) {\n    const params = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;\n    const pw = Math.floor(sw / 2) - 24;\n    sy = rect.y + ((rect.height - (gaugeLineHeight * Math.ceil(params.length / 2))) / 2) - 2;\n    let px = sx3;\n    let py = rect.y + Math.floor((rect.height - (Math.ceil(params.length / 2) * gaugeLineHeight)) / 2);\n    let counter = 0;\n    for (const param of params) {\n        this.resetFontSettings();\n        this.drawParamText(px, py, pw, param, true);\n        this.resetTextColor();\n        this.contents.fontSize -= 8;\n        const paramValue = actor.paramValueByName(param, true);\n        this.contents.drawText(paramValue, px, py, pw, gaugeLineHeight, 'right');\n        counter++;\n        if (counter % 2 === 0) {\n            px = sx3;\n            py += gaugeLineHeight;\n        } else {\n            px += pw + 24;\n        }\n    }\n}"
 *
 */
//=============================================================================

const _0x5e858a = _0x11e2;
(function (_0x1ca803, _0x5ae0bc) {
    const _0x5164e2 = _0x11e2,
        _0x66ef7c = _0x1ca803();
    while (!![]) {
        try {
            const _0x3af4e6 =
                (-parseInt(_0x5164e2(0x221)) / 0x1) * (parseInt(_0x5164e2(0x208)) / 0x2) +
                (-parseInt(_0x5164e2(0xd7)) / 0x3) * (-parseInt(_0x5164e2(0x210)) / 0x4) +
                (-parseInt(_0x5164e2(0x1d4)) / 0x5) * (-parseInt(_0x5164e2(0x196)) / 0x6) +
                (-parseInt(_0x5164e2(0x1fe)) / 0x7) * (parseInt(_0x5164e2(0x202)) / 0x8) +
                (-parseInt(_0x5164e2(0x1d0)) / 0x9) * (-parseInt(_0x5164e2(0xda)) / 0xa) +
                (parseInt(_0x5164e2(0xf1)) / 0xb) * (-parseInt(_0x5164e2(0x1b3)) / 0xc) +
                parseInt(_0x5164e2(0x14b)) / 0xd;
            if (_0x3af4e6 === _0x5ae0bc) break;
            else _0x66ef7c['push'](_0x66ef7c['shift']());
        } catch (_0x19ca2f) {
            _0x66ef7c['push'](_0x66ef7c['shift']());
        }
    }
})(_0x10b8, 0x22e20);
var label = _0x5e858a(0xfb),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x5e858a(0x206)](function (_0x5b3d6c) {
        const _0x389914 = _0x5e858a;
        return (
            _0x5b3d6c[_0x389914(0x1aa)] &&
            _0x5b3d6c[_0x389914(0x130)][_0x389914(0x1ec)]('[' + label + ']')
        );
    })[0x0];
function _0x11e2(_0x980edf, _0x46d81f) {
    const _0x10b832 = _0x10b8();
    return (
        (_0x11e2 = function (_0x11e2e0, _0x54e473) {
            _0x11e2e0 = _0x11e2e0 - 0xbe;
            let _0xdca300 = _0x10b832[_0x11e2e0];
            return _0xdca300;
        }),
        _0x11e2(_0x980edf, _0x46d81f)
    );
}
((VisuMZ[label]['Settings'] = VisuMZ[label]['Settings'] || {}),
    (VisuMZ['ConvertParams'] = function (_0x5b77c6, _0xef8fe7) {
        const _0x188772 = _0x5e858a;
        for (const _0x2abaf7 in _0xef8fe7) {
            if (_0x2abaf7[_0x188772(0xc2)](/(.*):(.*)/i)) {
                const _0x1da37e = String(RegExp['$1']),
                    _0x39147c = String(RegExp['$2'])[_0x188772(0x160)]()[_0x188772(0x232)]();
                let _0x318de6, _0x1c1791, _0x105c87;
                switch (_0x39147c) {
                    case _0x188772(0xd1):
                        _0x318de6 =
                            _0xef8fe7[_0x2abaf7] !== '' ? Number(_0xef8fe7[_0x2abaf7]) : 0x0;
                        break;
                    case 'ARRAYNUM':
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7])
                                : []),
                            (_0x318de6 = _0x1c1791['map'](_0x555d2a => Number(_0x555d2a))));
                        break;
                    case 'EVAL':
                        _0x318de6 = _0xef8fe7[_0x2abaf7] !== '' ? eval(_0xef8fe7[_0x2abaf7]) : null;
                        break;
                    case 'ARRAYEVAL':
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== '' ? JSON['parse'](_0xef8fe7[_0x2abaf7]) : []),
                            (_0x318de6 = _0x1c1791[_0x188772(0x11c)](_0x1c69a1 =>
                                eval(_0x1c69a1)
                            )));
                        break;
                    case 'JSON':
                        _0x318de6 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7])
                                : '';
                        break;
                    case _0x188772(0x1a6):
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7])
                                : []),
                            (_0x318de6 = _0x1c1791['map'](_0x50ffae => JSON['parse'](_0x50ffae))));
                        break;
                    case _0x188772(0x20d):
                        _0x318de6 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? new Function(JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7]))
                                : new Function(_0x188772(0x20c));
                        break;
                    case _0x188772(0x1d3):
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== '' ? JSON['parse'](_0xef8fe7[_0x2abaf7]) : []),
                            (_0x318de6 = _0x1c1791[_0x188772(0x11c)](
                                _0x2c942c => new Function(JSON[_0x188772(0x12c)](_0x2c942c))
                            )));
                        break;
                    case _0x188772(0x134):
                        _0x318de6 = _0xef8fe7[_0x2abaf7] !== '' ? String(_0xef8fe7[_0x2abaf7]) : '';
                        break;
                    case _0x188772(0x1f5):
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== '' ? JSON['parse'](_0xef8fe7[_0x2abaf7]) : []),
                            (_0x318de6 = _0x1c1791[_0x188772(0x11c)](_0x451766 =>
                                String(_0x451766)
                            )));
                        break;
                    case 'STRUCT':
                        ((_0x105c87 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7])
                                : {}),
                            (_0x5b77c6[_0x1da37e] = {}),
                            VisuMZ[_0x188772(0x16a)](_0x5b77c6[_0x1da37e], _0x105c87));
                        continue;
                    case _0x188772(0x12a):
                        ((_0x1c1791 =
                            _0xef8fe7[_0x2abaf7] !== ''
                                ? JSON[_0x188772(0x12c)](_0xef8fe7[_0x2abaf7])
                                : []),
                            (_0x318de6 = _0x1c1791[_0x188772(0x11c)](_0xf6b6f4 =>
                                VisuMZ[_0x188772(0x16a)]({}, JSON['parse'](_0xf6b6f4))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x5b77c6[_0x1da37e] = _0x318de6;
            }
        }
        return _0x5b77c6;
    }),
    (_0x5a8174 => {
        const _0x6f7303 = _0x5e858a,
            _0x1b5073 = _0x5a8174['name'];
        for (const _0x3a976d of dependencies) {
            if (!Imported[_0x3a976d]) {
                (alert(_0x6f7303(0x1a0)[_0x6f7303(0x1a5)](_0x1b5073, _0x3a976d)),
                    SceneManager[_0x6f7303(0x192)]());
                break;
            }
        }
        const _0xd3ddd8 = _0x5a8174[_0x6f7303(0x130)];
        if (_0xd3ddd8['match'](/\[Version[ ](.*?)\]/i)) {
            const _0x20ffec = Number(RegExp['$1']);
            _0x20ffec !== VisuMZ[label]['version'] &&
                (alert(_0x6f7303(0x118)['format'](_0x1b5073, _0x20ffec)), SceneManager['exit']());
        }
        if (_0xd3ddd8[_0x6f7303(0xc2)](/\[Tier[ ](\d+)\]/i)) {
            const _0x13ec06 = Number(RegExp['$1']);
            _0x13ec06 < tier
                ? (alert(_0x6f7303(0x113)['format'](_0x1b5073, _0x13ec06, tier)),
                  SceneManager[_0x6f7303(0x192)]())
                : (tier = Math[_0x6f7303(0x1a8)](_0x13ec06, tier));
        }
        VisuMZ['ConvertParams'](VisuMZ[label]['Settings'], _0x5a8174['parameters']);
    })(pluginData),
    PluginManager[_0x5e858a(0x1c8)](pluginData['name'], _0x5e858a(0x16d), _0x5147f9 => {
        const _0x2c23e6 = _0x5e858a;
        VisuMZ[_0x2c23e6(0x16a)](_0x5147f9, _0x5147f9);
        const _0x412a5e = _0x5147f9[_0x2c23e6(0xc9)],
            _0x3bad2f = _0x5147f9[_0x2c23e6(0x218)];
        for (let _0x1ee21c of _0x412a5e) {
            _0x1ee21c = parseInt(_0x1ee21c) || 0x0;
            if (_0x1ee21c <= 0x0) continue;
            const _0xf5de4a = $gameActors[_0x2c23e6(0x10f)](_0x1ee21c);
            if (!_0xf5de4a) continue;
            _0xf5de4a[_0x2c23e6(0x177)](_0x3bad2f);
        }
    }),
    PluginManager[_0x5e858a(0x1c8)](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x1a9), _0x19a08d => {
        const _0x28601a = _0x5e858a;
        VisuMZ[_0x28601a(0x16a)](_0x19a08d, _0x19a08d);
        const _0x53091b =
                _0x19a08d['Step1End'] >= _0x19a08d[_0x28601a(0x237)]
                    ? _0x19a08d[_0x28601a(0x237)]
                    : _0x19a08d[_0x28601a(0x103)],
            _0x18b955 =
                _0x19a08d[_0x28601a(0x103)] >= _0x19a08d[_0x28601a(0x237)]
                    ? _0x19a08d[_0x28601a(0x103)]
                    : _0x19a08d[_0x28601a(0x237)],
            _0x944625 = Array(_0x18b955 - _0x53091b + 0x1)
                [_0x28601a(0x205)]()
                [_0x28601a(0x11c)]((_0x1a1b3e, _0x2f2b12) => _0x53091b + _0x2f2b12),
            _0x76023 = _0x19a08d[_0x28601a(0x218)];
        for (let _0x12737f of _0x944625) {
            _0x12737f = parseInt(_0x12737f) || 0x0;
            if (_0x12737f <= 0x0) continue;
            const _0x4ab731 = $gameActors[_0x28601a(0x10f)](_0x12737f);
            if (!_0x4ab731) continue;
            _0x4ab731[_0x28601a(0x177)](_0x76023);
        }
    }),
    PluginManager['registerCommand'](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x153), _0x567bd5 => {
        const _0x15a793 = _0x5e858a;
        VisuMZ[_0x15a793(0x16a)](_0x567bd5, _0x567bd5);
        const _0x13568c = _0x567bd5[_0x15a793(0xc9)];
        let _0x26003a = [];
        while (_0x13568c[_0x15a793(0x1b4)] > 0x0) {
            const _0x9a6e3f = _0x13568c[_0x15a793(0xce)]();
            Array[_0x15a793(0x1b2)](_0x9a6e3f)
                ? (_0x26003a = _0x26003a[_0x15a793(0x124)](_0x9a6e3f))
                : _0x26003a[_0x15a793(0x1ef)](_0x9a6e3f);
        }
        const _0x4e4e11 = _0x567bd5[_0x15a793(0x218)];
        for (let _0x10ca47 of _0x26003a) {
            _0x10ca47 = parseInt(_0x10ca47) || 0x0;
            if (_0x10ca47 <= 0x0) continue;
            const _0x5cd810 = $gameActors['actor'](_0x10ca47);
            if (!_0x5cd810) continue;
            _0x5cd810[_0x15a793(0x177)](_0x4e4e11);
        }
    }),
    PluginManager[_0x5e858a(0x1c8)](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x1b5), _0x4c799c => {
        const _0x5b0e8a = _0x5e858a;
        VisuMZ[_0x5b0e8a(0x16a)](_0x4c799c, _0x4c799c);
        const _0x94f836 = _0x4c799c[_0x5b0e8a(0x165)] || [];
        for (const _0x3d9891 of _0x94f836) {
            $gameSystem[_0x5b0e8a(0x14a)](_0x3d9891);
        }
    }),
    PluginManager['registerCommand'](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x109), _0x4f9cad => {
        const _0xd7209d = _0x5e858a;
        VisuMZ['ConvertParams'](_0x4f9cad, _0x4f9cad);
        const _0x18538b = _0x4f9cad[_0xd7209d(0x165)] || [];
        for (const _0x1f98a3 of _0x18538b) {
            $gameSystem[_0xd7209d(0x204)](_0x1f98a3);
        }
    }),
    PluginManager[_0x5e858a(0x1c8)](pluginData[_0x5e858a(0x16b)], _0x5e858a(0xeb), _0x103dae => {
        const _0xec85f7 = _0x5e858a;
        VisuMZ[_0xec85f7(0x16a)](_0x103dae, _0x103dae);
        const _0x12ebbc = _0x103dae['Symbols'] || [];
        for (const _0x22d8d8 of _0x12ebbc) {
            $gameSystem['forceDisableMainMenuCommand'](_0x22d8d8);
        }
    }),
    PluginManager[_0x5e858a(0x1c8)](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x1a7), _0x4bd723 => {
        const _0x2ba6fe = _0x5e858a;
        VisuMZ[_0x2ba6fe(0x16a)](_0x4bd723, _0x4bd723);
        const _0x9b8c94 = _0x4bd723[_0x2ba6fe(0x165)] || [];
        for (const _0x5df674 of _0x9b8c94) {
            $gameSystem['forceHideMainMenuCommand'](_0x5df674);
        }
    }),
    PluginManager[_0x5e858a(0x1c8)](pluginData[_0x5e858a(0x16b)], _0x5e858a(0x207), _0x9e9d4 => {
        const _0x330c1e = _0x5e858a;
        VisuMZ[_0x330c1e(0x16a)](_0x9e9d4, _0x9e9d4);
        const _0x1d57f4 = _0x9e9d4[_0x330c1e(0x165)] || [];
        for (const _0x43e97a of _0x1d57f4) {
            $gameSystem[_0x330c1e(0xf2)](_0x43e97a);
        }
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x119)] = Game_System[_0x5e858a(0x17f)][_0x5e858a(0x21b)]),
    (Game_System[_0x5e858a(0x17f)][_0x5e858a(0x21b)] = function () {
        const _0x4b1081 = _0x5e858a;
        (VisuMZ[_0x4b1081(0xfb)][_0x4b1081(0x119)][_0x4b1081(0x20f)](this),
            this[_0x4b1081(0x21a)]());
    }),
    (Game_System[_0x5e858a(0x17f)]['initMainMenuCore'] = function () {
        const _0x52bbe2 = _0x5e858a;
        this[_0x52bbe2(0xd5)] = this['_mainMenuCore'] || {
            forceShow: [],
            forceHide: [],
            forceEnable: [],
            forceDisable: [],
        };
    }),
    (Game_System[_0x5e858a(0x17f)][_0x5e858a(0x1e4)] = function () {
        const _0x23a0c9 = _0x5e858a;
        if (this[_0x23a0c9(0xd5)] === undefined) this[_0x23a0c9(0x21a)]();
        const _0x23a465 = ['forceShow', _0x23a0c9(0x1ff), 'forceEnable', _0x23a0c9(0x1ea)];
        for (const _0x5f44ea of _0x23a465) {
            this[_0x23a0c9(0xd5)][_0x5f44ea] = this[_0x23a0c9(0xd5)][_0x5f44ea] || [];
        }
        return this[_0x23a0c9(0xd5)];
    }),
    (Game_System[_0x5e858a(0x17f)]['getMainMenuSymbolState'] = function (_0x241b3d, _0x252527) {
        const _0x5a9ae7 = _0x5e858a,
            _0x3ffcb7 = this[_0x5a9ae7(0x1e4)]();
        if (!_0x3ffcb7[_0x252527]) return ![];
        return _0x3ffcb7[_0x252527][_0x5a9ae7(0x1ec)](_0x241b3d);
    }),
    (Game_System['prototype'][_0x5e858a(0x14a)] = function (_0x55659a) {
        const _0x25a830 = _0x5e858a,
            _0x9ec4e0 = this[_0x25a830(0x1e4)](),
            _0xb2ccf5 = [_0x25a830(0x21c), 'forceHide', 'forceEnable', _0x25a830(0x1ea)];
        for (const _0x58ef95 of _0xb2ccf5) {
            _0x9ec4e0[_0x58ef95][_0x25a830(0x14e)](_0x55659a);
        }
    }),
    (Game_System[_0x5e858a(0x17f)][_0x5e858a(0xf2)] = function (_0x444de5) {
        const _0x501c1d = _0x5e858a,
            _0x1203a4 = this[_0x501c1d(0x1e4)]();
        (!_0x1203a4[_0x501c1d(0x21c)]['includes'](_0x444de5) &&
            _0x1203a4[_0x501c1d(0x21c)]['push'](_0x444de5),
            _0x1203a4[_0x501c1d(0x1ff)]['remove'](_0x444de5));
    }),
    (Game_System[_0x5e858a(0x17f)][_0x5e858a(0xfe)] = function (_0x2abd28) {
        const _0x1bd7b6 = _0x5e858a,
            _0x1c432e = this[_0x1bd7b6(0x1e4)]();
        (!_0x1c432e[_0x1bd7b6(0x1ff)][_0x1bd7b6(0x1ec)](_0x2abd28) &&
            _0x1c432e[_0x1bd7b6(0x1ff)][_0x1bd7b6(0x1ef)](_0x2abd28),
            _0x1c432e[_0x1bd7b6(0x21c)]['remove'](_0x2abd28));
    }),
    (Game_System['prototype']['forceEnableMainMenuCommand'] = function (_0x15e422) {
        const _0xd2158e = _0x5e858a,
            _0x3c5f56 = this[_0xd2158e(0x1e4)]();
        (!_0x3c5f56['forceEnable'][_0xd2158e(0x1ec)](_0x15e422) &&
            _0x3c5f56[_0xd2158e(0x123)][_0xd2158e(0x1ef)](_0x15e422),
            _0x3c5f56[_0xd2158e(0x1ea)][_0xd2158e(0x14e)](_0x15e422));
    }),
    (Game_System[_0x5e858a(0x17f)][_0x5e858a(0x1b6)] = function (_0xe2eeeb) {
        const _0x304d5e = _0x5e858a,
            _0x50477e = this[_0x304d5e(0x1e4)]();
        (!_0x50477e[_0x304d5e(0x1ea)]['includes'](_0xe2eeeb) &&
            _0x50477e['forceDisable'][_0x304d5e(0x1ef)](_0xe2eeeb),
            _0x50477e[_0x304d5e(0x123)][_0x304d5e(0x14e)](_0xe2eeeb));
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x11d)] = Game_Actor[_0x5e858a(0x17f)][_0x5e858a(0x120)]),
    (Game_Actor[_0x5e858a(0x17f)][_0x5e858a(0x120)] = function (_0x18ef63) {
        const _0xca8cfb = _0x5e858a;
        (VisuMZ[_0xca8cfb(0xfb)]['Game_Actor_setup'][_0xca8cfb(0x20f)](this, _0x18ef63),
            this['initMenuImage']());
    }),
    (Game_Actor[_0x5e858a(0x17f)]['initMenuImage'] = function () {
        const _0x42f666 = _0x5e858a;
        ((this['_menuImage'] = ''),
            this[_0x42f666(0x10f)]() &&
                this[_0x42f666(0x10f)]()[_0x42f666(0x23d)]['match'](
                    /<MENU (?:IMAGE|PORTRAIT):[ ](.*)>/i
                ) &&
                (this[_0x42f666(0x1ed)] = String(RegExp['$1'])));
    }),
    (Game_Actor[_0x5e858a(0x17f)][_0x5e858a(0x1ac)] = function () {
        const _0x104fd1 = _0x5e858a;
        if (this['_menuImage'] === undefined) this[_0x104fd1(0xd0)]();
        return this[_0x104fd1(0x1ed)];
    }),
    (Game_Actor['prototype'][_0x5e858a(0x177)] = function (_0xaf0f94) {
        const _0x53a1d8 = _0x5e858a;
        if (this[_0x53a1d8(0x1ed)] === undefined) this[_0x53a1d8(0xd0)]();
        this[_0x53a1d8(0x1ed)] = _0xaf0f94;
    }),
    (Game_Actor[_0x5e858a(0x17f)]['getMenuImageOffsetX'] = function () {
        const _0x5c797a = _0x5e858a;
        if (
            this[_0x5c797a(0x10f)]()[_0x5c797a(0x23d)][_0x5c797a(0xc2)](
                /<MENU (?:IMAGE|PORTRAIT) OFFSET X:[ ]([\+\-]\d+)>/i
            )
        )
            return Number(RegExp['$1']);
        else {
            if (
                this[_0x5c797a(0x10f)]()[_0x5c797a(0x23d)][_0x5c797a(0xc2)](
                    /<MENU (?:IMAGE|PORTRAIT) OFFSET:[ ]([\+\-]\d+),[ ]([\+\-]\d+)>/i
                )
            )
                return Number(RegExp['$1']);
        }
        return 0x0;
    }),
    (Game_Actor['prototype'][_0x5e858a(0x1d9)] = function () {
        const _0x1c3eb2 = _0x5e858a;
        if (
            this[_0x1c3eb2(0x10f)]()[_0x1c3eb2(0x23d)][_0x1c3eb2(0xc2)](
                /<MENU (?:IMAGE|PORTRAIT) OFFSET Y:[ ]([\+\-]\d+)>/i
            )
        )
            return Number(RegExp['$1']);
        else {
            if (
                this[_0x1c3eb2(0x10f)]()[_0x1c3eb2(0x23d)][_0x1c3eb2(0xc2)](
                    /<MENU (?:IMAGE|PORTRAIT) OFFSET:[ ]([\+\-]\d+),[ ]([\+\-]\d+)>/i
                )
            )
                return Number(RegExp['$2']);
        }
        return 0x0;
    }),
    (Scene_MenuBase[_0x5e858a(0x17f)]['isDisplayActorMenuBackgroundImage'] = function () {
        const _0x30d0be = _0x5e858a;
        return VisuMZ[_0x30d0be(0xfb)][_0x30d0be(0x126)][_0x30d0be(0x227)][_0x30d0be(0x1b7)][
            _0x30d0be(0x1ec)
        ](this['constructor'][_0x30d0be(0x16b)]);
    }),
    (VisuMZ[_0x5e858a(0xfb)]['Scene_MenuBase_createBackground'] =
        Scene_MenuBase[_0x5e858a(0x17f)][_0x5e858a(0x12e)]),
    (Scene_MenuBase[_0x5e858a(0x17f)][_0x5e858a(0x12e)] = function () {
        const _0x41ef0b = _0x5e858a;
        (VisuMZ[_0x41ef0b(0xfb)][_0x41ef0b(0xcd)][_0x41ef0b(0x20f)](this),
            this[_0x41ef0b(0x224)]());
    }),
    (Scene_MenuBase['prototype'][_0x5e858a(0x224)] = function () {
        const _0x567e35 = _0x5e858a;
        ((this[_0x567e35(0x13e)] = new Sprite_MenuBackgroundActor()),
            this[_0x567e35(0x15b)](this[_0x567e35(0x13e)]));
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x10d)] = Scene_MenuBase['prototype'][_0x5e858a(0xe2)]),
    (Scene_MenuBase[_0x5e858a(0x17f)][_0x5e858a(0xe2)] = function () {
        const _0x52db4a = _0x5e858a;
        (VisuMZ[_0x52db4a(0xfb)][_0x52db4a(0x10d)][_0x52db4a(0x20f)](this),
            this['isDisplayActorMenuBackgroundImage']() &&
                this[_0x52db4a(0x13e)] &&
                this[_0x52db4a(0x13e)][_0x52db4a(0x1e2)](this[_0x52db4a(0x15c)]));
    }),
    (VisuMZ['MainMenuCore']['Scene_Menu_create'] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1f0)]),
    (Scene_Menu['prototype']['create'] = function () {
        const _0x16e36b = _0x5e858a;
        (VisuMZ[_0x16e36b(0xfb)][_0x16e36b(0x236)][_0x16e36b(0x20f)](this),
            this[_0x16e36b(0xf9)](),
            this[_0x16e36b(0x15f)](),
            this['createDummyWindow']());
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x171)] = function () {
        const _0xd1b3c9 = _0x5e858a,
            _0x1144fa = this['commandWindowRect'](),
            _0x479bb2 = new Window_MenuCommand(_0x1144fa);
        (_0x479bb2[_0xd1b3c9(0x189)](
            _0xd1b3c9(0xbf),
            this[_0xd1b3c9(0x1dd)][_0xd1b3c9(0x152)](this)
        ),
            this[_0xd1b3c9(0x157)](_0x479bb2),
            (this[_0xd1b3c9(0xe0)] = _0x479bb2));
    }),
    (VisuMZ['MainMenuCore']['Scene_Menu_commandWindowRect'] =
        Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x18b)]),
    (Scene_Menu[_0x5e858a(0x17f)]['commandWindowRect'] = function () {
        const _0x1a14f7 = _0x5e858a,
            _0x4336a6 = this[_0x1a14f7(0xc0)]();
        if (_0x4336a6 === _0x1a14f7(0x136)) return this['commandWindowRectTopStyle']();
        else {
            if (_0x4336a6 === _0x1a14f7(0x225)) return this['commandWindowRectThinTopStyle']();
            else {
                if (_0x4336a6 === 'bottom') return this['commandWindowRectBottomStyle']();
                else {
                    if (_0x4336a6 === _0x1a14f7(0x13b))
                        return this['commandWindowRectThinBottomStyle']();
                    else {
                        if (_0x4336a6 === 'mobile') return this[_0x1a14f7(0x19d)]();
                        else {
                            const _0x31c627 =
                                VisuMZ[_0x1a14f7(0xfb)][_0x1a14f7(0x20e)][_0x1a14f7(0x20f)](this);
                            return (this[_0x1a14f7(0x159)](_0x31c627), _0x31c627);
                        }
                    }
                }
            }
        }
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x159)] = function (_0x12bf36) {
        const _0x5506bd = _0x5e858a;
        (this[_0x5506bd(0x1d6)]() &&
            (_0x12bf36['height'] -= this[_0x5506bd(0xf0)]()[_0x5506bd(0x115)]),
            this['adjustCommandHeightByVariable']() &&
                (_0x12bf36[_0x5506bd(0x115)] -= this[_0x5506bd(0x180)]()[_0x5506bd(0x115)]));
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xcc)] = function () {
        const _0x5bb7ec = _0x5e858a,
            _0x3ad2d6 =
                VisuMZ[_0x5bb7ec(0xfb)][_0x5bb7ec(0x126)][_0x5bb7ec(0xf6)][_0x5bb7ec(0x145)],
            _0xdf11c0 = Graphics[_0x5bb7ec(0x154)],
            _0x38b6d4 = this[_0x5bb7ec(0x211)](_0x3ad2d6, !![]),
            _0x1b84aa = 0x0,
            _0x1e8bd6 = this['mainAreaTop']();
        return new Rectangle(_0x1b84aa, _0x1e8bd6, _0xdf11c0, _0x38b6d4);
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x163)] = function () {
        const _0x269cf5 = _0x5e858a,
            _0x34d305 = VisuMZ['MainMenuCore'][_0x269cf5(0x126)][_0x269cf5(0xf6)][_0x269cf5(0x145)],
            _0x2b6b25 = Graphics[_0x269cf5(0x154)],
            _0xa0c3a0 = this[_0x269cf5(0x211)](0x1, !![]),
            _0x1e438a = 0x0,
            _0x411fe2 = this['mainAreaTop']();
        return new Rectangle(_0x1e438a, _0x411fe2, _0x2b6b25, _0xa0c3a0);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xea)] = function () {
        const _0xd803ff = _0x5e858a,
            _0x2db936 = VisuMZ['MainMenuCore'][_0xd803ff(0x126)]['CustomCmdWin'][_0xd803ff(0x145)],
            _0x37f1c8 = Graphics[_0xd803ff(0x154)],
            _0x231f76 = this[_0xd803ff(0x211)](_0x2db936, !![]),
            _0x3dec2d = 0x0,
            _0x2ada34 = this[_0xd803ff(0x11f)]() - _0x231f76;
        return new Rectangle(_0x3dec2d, _0x2ada34, _0x37f1c8, _0x231f76);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x11a)] = function () {
        const _0x527862 = _0x5e858a,
            _0x79c21e =
                VisuMZ[_0x527862(0xfb)][_0x527862(0x126)][_0x527862(0xf6)][_0x527862(0x145)],
            _0x4eed18 = Graphics[_0x527862(0x154)],
            _0x1d8b8e = this[_0x527862(0x211)](0x1, !![]),
            _0x1931bf = 0x0,
            _0x2969c8 = this[_0x527862(0x11f)]() - _0x1d8b8e;
        return new Rectangle(_0x1931bf, _0x2969c8, _0x4eed18, _0x1d8b8e);
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['commandWindowRectMobileStyle'] = function () {
        const _0x13e992 = _0x5e858a,
            _0x424565 = VisuMZ['MainMenuCore'][_0x13e992(0x126)][_0x13e992(0xf6)][_0x13e992(0x145)],
            _0x40a072 = Graphics['boxWidth'],
            _0x27182b = Window_MenuCommand['prototype'][_0x13e992(0x228)](_0x424565),
            _0x33cf3f = 0x0,
            _0x491cdd = Math[_0x13e992(0x162)]((Graphics[_0x13e992(0x1c1)] - _0x27182b) / 0x2);
        return new Rectangle(_0x33cf3f, _0x491cdd, _0x40a072, _0x27182b);
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['commandWindowStyle'] = function () {
        const _0x210dd5 = _0x5e858a;
        return VisuMZ[_0x210dd5(0xfb)][_0x210dd5(0x126)][_0x210dd5(0x174)];
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x1cf)] = function () {
        const _0x49e043 = _0x5e858a;
        if (this[_0x49e043(0xc0)]() !== _0x49e043(0x15e)) return !![];
        return VisuMZ[_0x49e043(0xfb)]['Settings'][_0x49e043(0x227)][_0x49e043(0x110)];
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x14f)] = function () {
        const _0x3e60ac = _0x5e858a,
            _0xd726e3 = this[_0x3e60ac(0x1c2)]();
        ((this[_0x3e60ac(0xc8)] = this[_0x3e60ac(0x1cf)]()
            ? new Window_ThinGold(_0xd726e3)
            : new Window_Gold(_0xd726e3)),
            this[_0x3e60ac(0x157)](this['_goldWindow']));
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x1b1)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1c2)]),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1c2)] = function () {
        const _0x13613e = _0x5e858a,
            _0x20b8d7 = this[_0x13613e(0xc0)]();
        if ([_0x13613e(0x136), 'thinTop', _0x13613e(0x156)]['includes'](_0x20b8d7))
            return this['goldWindowRectTopStyle']();
        else {
            if (['bottom', 'thinBottom'][_0x13613e(0x1ec)](_0x20b8d7))
                return this[_0x13613e(0xfd)]();
            else {
                const _0x9f4d99 =
                    VisuMZ['MainMenuCore']['Scene_Menu_goldWindowRect'][_0x13613e(0x20f)](this);
                return (this[_0x13613e(0x14c)](_0x9f4d99), _0x9f4d99);
            }
        }
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['applyThinnerGoldWindowRect'] = function (_0x2f59e9) {
        const _0x1fadbe = _0x5e858a;
        if (this['thinGoldWindow']()) {
            if (VisuMZ[_0x1fadbe(0xfb)][_0x1fadbe(0x126)][_0x1fadbe(0x227)][_0x1fadbe(0x231)]) {
                const _0x429182 = _0x2f59e9[_0x1fadbe(0x115)] - this['calcWindowHeight'](0x1, ![]);
                _0x2f59e9['y'] += _0x429182;
            }
            VisuMZ[_0x1fadbe(0xfb)][_0x1fadbe(0x126)]['General'][_0x1fadbe(0xf5)] &&
                (_0x2f59e9[_0x1fadbe(0x115)] = this[_0x1fadbe(0x211)](0x1, ![]));
        }
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x239)] = function () {
        const _0x113b27 = _0x5e858a,
            _0x378d0e = this[_0x113b27(0xdd)](),
            _0x1a0b7c = this[_0x113b27(0x211)](0x1, ![]),
            _0xaf4aa0 = Graphics[_0x113b27(0x154)] - _0x378d0e,
            _0x34fe8c = this['mainAreaBottom']() - _0x1a0b7c;
        return new Rectangle(_0xaf4aa0, _0x34fe8c, _0x378d0e, _0x1a0b7c);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xfd)] = function () {
        const _0xf751be = _0x5e858a,
            _0x130306 = this['mainCommandWidth'](),
            _0x5c32ba = this['calcWindowHeight'](0x1, ![]),
            _0x588367 = Graphics[_0xf751be(0x154)] - _0x130306,
            _0x25b545 = this[_0xf751be(0x108)]();
        return new Rectangle(_0x588367, _0x25b545, _0x130306, _0x5c32ba);
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x142)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1bb)]),
    (Scene_Menu['prototype'][_0x5e858a(0x1bb)] = function () {
        const _0x205224 = _0x5e858a;
        (VisuMZ[_0x205224(0xfb)][_0x205224(0x142)]['call'](this), this[_0x205224(0x1fa)]());
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1fa)] = function () {
        const _0x3bfdb8 = _0x5e858a;
        this['commandWindowStyle']() === 'mobile' &&
            (this[_0x3bfdb8(0x114)][_0x3bfdb8(0x146)] = 0x0);
    }),
    (VisuMZ['MainMenuCore'][_0x5e858a(0x1dc)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x21f)]),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x21f)] = function () {
        const _0x587b0d = _0x5e858a,
            _0x29ff6c = this[_0x587b0d(0xc0)]();
        if (['top', 'thinTop'][_0x587b0d(0x1ec)](_0x29ff6c)) return this[_0x587b0d(0x1de)]();
        else {
            if (['bottom', _0x587b0d(0x13b)][_0x587b0d(0x1ec)](_0x29ff6c))
                return this[_0x587b0d(0xec)]();
            else
                return _0x29ff6c === _0x587b0d(0x156)
                    ? this[_0x587b0d(0xd6)]()
                    : VisuMZ[_0x587b0d(0xfb)][_0x587b0d(0x1dc)]['call'](this);
        }
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1de)] = function () {
        const _0x310908 = _0x5e858a,
            _0x4032d3 = Graphics[_0x310908(0x154)],
            _0x5920e6 =
                this[_0x310908(0x21e)]() -
                this[_0x310908(0xe0)][_0x310908(0x115)] -
                this[_0x310908(0xc8)][_0x310908(0x115)],
            _0xcf15f9 = 0x0,
            _0x2a7776 = this[_0x310908(0xe0)]['y'] + this['_commandWindow'][_0x310908(0x115)];
        return new Rectangle(_0xcf15f9, _0x2a7776, _0x4032d3, _0x5920e6);
    }),
    (Scene_Menu['prototype'][_0x5e858a(0xec)] = function () {
        const _0x4656cb = _0x5e858a,
            _0x4fd251 = Graphics[_0x4656cb(0x154)],
            _0x70b6c1 =
                this[_0x4656cb(0x21e)]() -
                this[_0x4656cb(0xe0)][_0x4656cb(0x115)] -
                this[_0x4656cb(0xc8)][_0x4656cb(0x115)],
            _0x36be59 = 0x0,
            _0x23230c = this[_0x4656cb(0xc8)]['y'] + this[_0x4656cb(0xc8)][_0x4656cb(0x115)];
        return new Rectangle(_0x36be59, _0x23230c, _0x4fd251, _0x70b6c1);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xd6)] = function () {
        const _0x31e619 = _0x5e858a,
            _0x335d5b = Graphics[_0x31e619(0x154)],
            _0x176c16 = this[_0x31e619(0x21e)]() - this[_0x31e619(0xc8)]['height'],
            _0x5b9efc = 0x0,
            _0x29ba93 = this[_0x31e619(0x11f)]() - this['_goldWindow']['height'] - _0x176c16;
        return new Rectangle(_0x5b9efc, _0x29ba93, _0x335d5b, _0x176c16);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xf9)] = function () {
        const _0x40e710 = _0x5e858a;
        if (!this[_0x40e710(0x1df)]()) return new Rectangle(0x0, 0x0, 0x0, 0x0);
        const _0x5ac0b7 = this[_0x40e710(0xf0)]();
        ((this[_0x40e710(0x1e7)] = new Window_Playtime(_0x5ac0b7)),
            this[_0x40e710(0x1e7)][_0x40e710(0xcb)](
                VisuMZ[_0x40e710(0xfb)]['Settings'][_0x40e710(0x1ee)][_0x40e710(0xca)]
            ),
            this[_0x40e710(0x157)](this['_playtimeWindow']));
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['canCreatePlaytimeWindow'] = function () {
        const _0x25be39 = _0x5e858a;
        return VisuMZ['MainMenuCore'][_0x25be39(0x126)][_0x25be39(0x1ee)][_0x25be39(0x229)];
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x1d6)] = function () {
        const _0x13cca7 = _0x5e858a;
        return (
            this[_0x13cca7(0x1df)]() &&
            (VisuMZ[_0x13cca7(0xfb)]['Settings'][_0x13cca7(0x1ee)]['AdjustCommandHeight'] ?? !![])
        );
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xf0)] = function () {
        const _0x5de272 = _0x5e858a,
            _0x5ae501 = this[_0x5de272(0xc0)]();
        if ([_0x5de272(0x136), _0x5de272(0x225), _0x5de272(0x156)][_0x5de272(0x1ec)](_0x5ae501))
            return this['playtimeWindowRectTopStyle']();
        else
            return [_0x5de272(0x161), _0x5de272(0x13b)]['includes'](_0x5ae501)
                ? this[_0x5de272(0x17d)]()
                : VisuMZ['MainMenuCore'][_0x5de272(0x126)]['Playtime'][_0x5de272(0x235)]['call'](
                      this
                  );
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1f1)] = function () {
        const _0x501b10 = _0x5e858a,
            _0x401cb9 = this[_0x501b10(0xdd)](),
            _0x4e3aed = this[_0x501b10(0x211)](0x1, ![]),
            _0x3bc1fe = 0x0,
            _0xdad94d = this['mainAreaBottom']() - _0x4e3aed;
        return new Rectangle(_0x3bc1fe, _0xdad94d, _0x401cb9, _0x4e3aed);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x17d)] = function () {
        const _0xdf381 = _0x5e858a,
            _0x5b2c87 = this['mainCommandWidth'](),
            _0x2b0e53 = this['calcWindowHeight'](0x1, ![]),
            _0x471ea3 = 0x0,
            _0x4506ec = this[_0xdf381(0x108)]();
        return new Rectangle(_0x471ea3, _0x4506ec, _0x5b2c87, _0x2b0e53);
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x15f)] = function () {
        const _0x2b1c79 = _0x5e858a;
        if (!this[_0x2b1c79(0xed)]()) return new Rectangle(0x0, 0x0, 0x0, 0x0);
        const _0x117732 = this[_0x2b1c79(0x180)]();
        ((this['_variableWindow'] = new Window_MenuVariables(_0x117732)),
            this[_0x2b1c79(0x1be)][_0x2b1c79(0xcb)](
                VisuMZ[_0x2b1c79(0xfb)][_0x2b1c79(0x126)][_0x2b1c79(0x201)][_0x2b1c79(0xca)]
            ),
            this[_0x2b1c79(0x157)](this[_0x2b1c79(0x1be)]));
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xed)] = function () {
        const _0x3aadf1 = _0x5e858a;
        return VisuMZ['MainMenuCore']['Settings'][_0x3aadf1(0x201)]['Enable'];
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x1cc)] = function () {
        const _0x2331b3 = _0x5e858a;
        return (
            this['canCreateVariableWindow']() &&
            (VisuMZ[_0x2331b3(0xfb)]['Settings'][_0x2331b3(0x201)]['AdjustCommandHeight'] ?? !![])
        );
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['variableWindowRect'] = function () {
        const _0x2df698 = _0x5e858a,
            _0xd3b896 = this[_0x2df698(0xc0)]();
        if (['top', 'thinTop', _0x2df698(0x156)]['includes'](_0xd3b896))
            return this[_0x2df698(0x209)]();
        else
            return [_0x2df698(0x161), _0x2df698(0x13b)][_0x2df698(0x1ec)](_0xd3b896)
                ? this['variableWindowRectBottomStyle']()
                : VisuMZ[_0x2df698(0xfb)][_0x2df698(0x126)][_0x2df698(0x201)][_0x2df698(0x235)][
                      _0x2df698(0x20f)
                  ](this);
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['variableWindowRectTopStyle'] = function () {
        const _0x5147d6 = _0x5e858a,
            _0x4a9c6c =
                Graphics[_0x5147d6(0x154)] -
                this[_0x5147d6(0xc8)][_0x5147d6(0x1f9)] -
                (this['_playtimeWindow'] ? this['_playtimeWindow'][_0x5147d6(0x1f9)] : 0x0),
            _0x558a6a = this[_0x5147d6(0x211)](0x1, ![]),
            _0x56cac1 = this['_goldWindow']['x'] - _0x4a9c6c,
            _0x2cf06a = this[_0x5147d6(0x11f)]() - _0x558a6a;
        return new Rectangle(_0x56cac1, _0x2cf06a, _0x4a9c6c, _0x558a6a);
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['variableWindowRectBottomStyle'] = function () {
        const _0xaeb29 = _0x5e858a,
            _0x50e8bb =
                Graphics[_0xaeb29(0x154)] -
                this[_0xaeb29(0xc8)][_0xaeb29(0x1f9)] -
                (this[_0xaeb29(0x1e7)] ? this[_0xaeb29(0x1e7)][_0xaeb29(0x1f9)] : 0x0),
            _0x8f6c25 = this[_0xaeb29(0x211)](0x1, ![]),
            _0x23b41b = this[_0xaeb29(0xc8)]['x'] - _0x50e8bb,
            _0x4c1b24 = this[_0xaeb29(0x108)]();
        return new Rectangle(_0x23b41b, _0x4c1b24, _0x50e8bb, _0x8f6c25);
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x132)] = function () {
        const _0x27f92f = _0x5e858a;
        if (!this['needsDummyWindow']()) return;
        const _0x3b530b = this['variableWindowRect']();
        ((this[_0x27f92f(0xe9)] = new Window_Base(_0x3b530b)),
            this[_0x27f92f(0xe9)][_0x27f92f(0xcb)](
                VisuMZ[_0x27f92f(0xfb)][_0x27f92f(0x126)]['Variable']['BgType']
            ),
            this[_0x27f92f(0x157)](this[_0x27f92f(0xe9)]));
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['needsDummyWindow'] = function () {
        const _0x3dacdb = _0x5e858a;
        if ([_0x3dacdb(0x15e), 'mobile'][_0x3dacdb(0x1ec)](this[_0x3dacdb(0xc0)]())) return ![];
        if (this['_variableWindow']) return ![];
        return !![];
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0xd2)] = Scene_Menu[_0x5e858a(0x17f)]['commandPersonal']),
    (Scene_Menu[_0x5e858a(0x17f)]['commandPersonal'] = function () {
        const _0x4f475b = _0x5e858a;
        if (this[_0x4f475b(0x1ae)]() && this['_statusWindow'])
            ($gameParty['setTargetActor']($gameParty[_0x4f475b(0x15d)]()[0x0]),
                this[_0x4f475b(0x23a)]());
        else {
            if (this['commandWindowStyle']() === _0x4f475b(0x156))
                this['_statusWindow'][_0x4f475b(0x18a)]();
            VisuMZ['MainMenuCore'][_0x4f475b(0xd2)][_0x4f475b(0x20f)](this);
        }
    }),
    (Scene_Menu['prototype']['isSoloQuickMode'] = function () {
        const _0x97f68e = _0x5e858a;
        return (
            VisuMZ[_0x97f68e(0xfb)]['Settings'][_0x97f68e(0x227)][_0x97f68e(0x19c)] &&
            $gameParty[_0x97f68e(0x15d)]()[_0x97f68e(0x1b4)] <= 0x1
        );
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x23a)] = function () {
        const _0x5462af = _0x5e858a,
            _0x2d15f8 = this['_commandWindow']['currentSymbol'](),
            _0x5e3bae = this[_0x5462af(0xe0)][_0x5462af(0x1e3)]();
        for (const _0x56006f of Window_MenuCommand[_0x5462af(0x1e5)]) {
            if (_0x56006f[_0x5462af(0x191)] === _0x2d15f8) {
                _0x56006f[_0x5462af(0x10b)][_0x5462af(0x20f)](this, _0x5e3bae);
                return;
            }
        }
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x1c4)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x182)]),
    (Scene_Menu[_0x5e858a(0x17f)]['onPersonalCancel'] = function () {
        const _0x1d9dce = _0x5e858a;
        VisuMZ[_0x1d9dce(0xfb)][_0x1d9dce(0x1c4)][_0x1d9dce(0x20f)](this);
        if (this[_0x1d9dce(0xc0)]() === _0x1d9dce(0x156)) this[_0x1d9dce(0x114)][_0x1d9dce(0xe6)]();
    }),
    (Scene_Menu['prototype'][_0x5e858a(0x199)] = function () {
        const _0x51a660 = _0x5e858a,
            _0x3aaaa9 = parseInt(this[_0x51a660(0xe0)][_0x51a660(0x1e3)]());
        _0x3aaaa9
            ? ($gameTemp[_0x51a660(0x1f4)](_0x3aaaa9), this[_0x51a660(0x1c6)]())
            : this[_0x51a660(0xe0)]['activate']();
    }),
    (VisuMZ['MainMenuCore'][_0x5e858a(0x23f)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xd4)]),
    (Scene_Menu[_0x5e858a(0x17f)]['commandFormation'] = function () {
        const _0x42c075 = _0x5e858a;
        VisuMZ[_0x42c075(0xfb)][_0x42c075(0x23f)][_0x42c075(0x20f)](this);
        if (this[_0x42c075(0xc0)]() === _0x42c075(0x156)) this[_0x42c075(0x114)]['open']();
    }),
    (VisuMZ['MainMenuCore'][_0x5e858a(0x1c9)] = Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0xdc)]),
    (Scene_Menu['prototype'][_0x5e858a(0xdc)] = function () {
        const _0x2dfde = _0x5e858a;
        VisuMZ['MainMenuCore'][_0x2dfde(0x1c9)][_0x2dfde(0x20f)](this);
        if (this[_0x2dfde(0xc0)]() === _0x2dfde(0x156)) this['_statusWindow'][_0x2dfde(0xe6)]();
    }),
    (Scene_Menu[_0x5e858a(0x17f)][_0x5e858a(0x131)] = function () {
        const _0x2f9f2a = _0x5e858a;
        SceneManager[_0x2f9f2a(0x1ef)](Scene_Load);
    }),
    (Scene_Menu[_0x5e858a(0x17f)]['commandCancel'] = function () {
        const _0x42602f = _0x5e858a;
        this[_0x42602f(0xe0)]['currentSubcategory']() !== ''
            ? this['_commandWindow'][_0x42602f(0x12d)]()
            : this[_0x42602f(0x1c6)]();
    }));
function _0x10b8() {
    const _0x436e5f = [
        'lineHeight',
        'drawItemStatusPortraitStyleOnLoad',
        'forceDisable',
        'updatePosition',
        'includes',
        '_menuImage',
        'Playtime',
        'push',
        'create',
        'playtimeWindowRectTopStyle',
        'opacity',
        'drawPendingItemBackground',
        'reserveCommonEvent',
        'ARRAYSTR',
        '_targetY',
        'loadPicture',
        'faceWidth',
        'width',
        'adjustStatusWindowMobile',
        '_bitmapReady',
        'textSizeEx',
        'getSubcategoryList',
        '70231KtspIH',
        'forceHide',
        'variables',
        'Variable',
        '152jIEkSq',
        'isMainMenuCommandVisible',
        'forceEnableMainMenuCommand',
        'fill',
        'filter',
        'MenuCommandForceShow',
        '40202qabMtb',
        'variableWindowRectTopStyle',
        'hasStaticSvBattler',
        'drawTextEx',
        'return\x200',
        'FUNC',
        'Scene_Menu_commandWindowRect',
        'call',
        '1136uAiypX',
        'calcWindowHeight',
        'drawTimeLabel',
        'setSubcategory',
        'getMenuImageOffsetX',
        'findExt',
        'Window_MenuCommand_initialize',
        'isMainMenuCommandEnabled',
        'Step2',
        'loadOtherActorImages',
        'initMainMenuCore',
        'initialize',
        'forceShow',
        'commandNameWindowDrawText',
        'mainAreaHeight',
        'statusWindowRect',
        'loadCharacter',
        '9bDSEAw',
        'updateCommandNameWindow',
        'drawItemStyleIcon',
        'createActorMenuBackgroundImageSprite',
        'thinTop',
        'commandNameWindowCenter',
        'General',
        'fittingHeight',
        'Enable',
        'loadBitmap',
        'text',
        'colSpacing',
        'svActorVertCells',
        'fontSize',
        'Window_MenuStatus_itemHeight',
        'Time',
        'AutoGoldY',
        'trim',
        'numVisibleRows',
        'changePaintOpacity',
        'WindowRect',
        'Scene_Menu_create',
        'Step1Start',
        'loadSvActor',
        'goldWindowRectTopStyle',
        'onPersonalOk',
        'drawItemStatus',
        'floor',
        'note',
        'Subcategory',
        'Scene_Menu_commandFormation',
        'min',
        'drawItemStatusSoloStyleOnLoad',
        'cancel',
        'commandWindowStyle',
        'doesSubcategoryExist',
        'match',
        'commandStyleCheck',
        '_data',
        'drawActorFace',
        'FontSize',
        'commandName',
        '_goldWindow',
        'Step1',
        'BgType',
        'setBackgroundType',
        'commandWindowRectTopStyle',
        'Scene_MenuBase_createBackground',
        'shift',
        'addLoadListener',
        'initMenuImage',
        'NUM',
        'Scene_Menu_commandPersonal',
        'HideMainMenuOnly',
        'commandFormation',
        '_mainMenuCore',
        'statusWindowRectMobileStyle',
        '33bjSbMK',
        'updateTimer',
        'isExpGaugeDrawn',
        '149770hPBKge',
        'iconText',
        'onFormationCancel',
        'mainCommandWidth',
        'battlerName',
        'maxCols',
        '_commandWindow',
        'characterIndex',
        'updateActor',
        'sprite',
        '_commandNameWindow',
        'ShowJS',
        'close',
        '_subcategory',
        'ExtJS',
        '_dummyWindow',
        'commandWindowRectBottomStyle',
        'MenuCommandForceDisable',
        'statusWindowRectBottomStyle',
        'canCreateVariableWindow',
        'resetFontSettings',
        'icon',
        'playtimeWindowRect',
        '3077569wCbjYA',
        'forceShowMainMenuCommand',
        'drawItemActorSvBattler',
        'drawItemStatusThickerStyle',
        'AutoGoldHeight',
        'CustomCmdWin',
        'addFormationCommand',
        'addMainCommands',
        'createPlaytimeWindow',
        'selectLast',
        'MainMenuCore',
        'Window_StatusBase_loadFaceImages',
        'goldWindowRectBottomStyle',
        'forceHideMainMenuCommand',
        'drawAllItems',
        'drawItemActorFace',
        'loadFaceImages',
        'iconWidth',
        'Step1End',
        '_duration',
        'solo',
        'setTopRow',
        'isCommandEnabled',
        'mainAreaTop',
        'MenuCommandForceEnable',
        'drawItemStatusThinStyle',
        'PersonalHandlerJS',
        'isBigCharacter',
        'Scene_MenuBase_updateActor',
        'itemLineRect',
        'actor',
        'ThinGoldWindow',
        'onBitmapLoad',
        'drawItemActorSprite',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        '_statusWindow',
        'height',
        'formation',
        'maxVisibleItems',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'Game_System_initialize',
        'commandWindowRectThinBottomStyle',
        'addOptionsCommand',
        'map',
        'Game_Actor_setup',
        'systemColor',
        'mainAreaBottom',
        'setup',
        'drawItemStatusPortraitStyle',
        'options',
        'forceEnable',
        'concat',
        'index',
        'Settings',
        'addSymbolBridge',
        'commandNameWindowDrawBackground',
        'VerticalStyle',
        'ARRAYSTRUCT',
        'thicker',
        'parse',
        'removeSubcategory',
        'createBackground',
        'isIncludedInSubcategory',
        'description',
        'commandLoad',
        'createDummyWindow',
        '_scene',
        'STR',
        'save',
        'top',
        'PortraitStyle',
        'makeCommandList',
        'CallHandlerJS',
        'update',
        'thinBottom',
        'EnableJS',
        'center',
        '_actorMenuBgSprite',
        'subcategory',
        'none',
        'PixelateImageRendering',
        'Scene_Menu_createStatusWindow',
        '_targetX',
        'innerHeight',
        'Rows',
        'openness',
        'SUBCATEGORY_LIST',
        'Window_MenuStatus_selectLast',
        'bitmap',
        'clearShowMainMenuCommand',
        '4714710TgjmbY',
        'applyThinnerGoldWindowRect',
        'playtimeText',
        'remove',
        'createGoldWindow',
        'TextJS',
        'select',
        'bind',
        'ChangeActorMenuImageJS',
        'boxWidth',
        'drawItemStyleIconText',
        'mobile',
        'addWindow',
        'item',
        'adjustDefaultCommandWindowRect',
        'ThickerStyle',
        'addChild',
        '_actor',
        'members',
        'default',
        'createVariableWindow',
        'toUpperCase',
        'bottom',
        'round',
        'commandWindowRectThinTopStyle',
        'ceil',
        'Symbols',
        'faceHeight',
        'drawText',
        'portrait',
        'maxBattleMembers',
        'ConvertParams',
        'name',
        'innerWidth',
        'ChangeActorMenuImageGroup',
        'getMainMenuSymbolState',
        'smoothSelect',
        'TextStr',
        'createCommandWindow',
        'drawItemImage',
        'drawActorGraphic',
        'CommandWindowStyle',
        'addGameEndCommand',
        'constructor',
        'setMenuImage',
        'activate',
        'itemTextAlign',
        '_timer',
        'vertical',
        'drawItemBackground',
        'playtimeWindowRectBottomStyle',
        'commandStyle',
        'prototype',
        'variableWindowRect',
        'topIndex',
        'onPersonalCancel',
        'drawIcon',
        'gameEnd',
        'characterName',
        'Window_MenuStatus_drawItemImage',
        'addSaveCommand',
        'Icon',
        'setHandler',
        'open',
        'commandWindowRect',
        'ListStyles',
        'StatusSelectLast',
        'contents',
        '\x5cI[%1]%2',
        'graphicType',
        'Symbol',
        'exit',
        'isSubcategoryVisible',
        'currentSubcategory',
        'drawTimeIcon',
        '611238qRpJZr',
        'isBattleMember',
        'makeMainMenuCoreCommandList',
        'commandCommonEvent',
        'refresh',
        'itemHeight',
        'SoloQuick',
        'commandWindowRectMobileStyle',
        'svbattler',
        'updateDuration',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'right',
        'iconHeight',
        'normalColor',
        'showOnlyBattleMembers',
        'format',
        'ARRAYJSON',
        'MenuCommandForceHide',
        'max',
        'ChangeActorMenuImageRange',
        'status',
        'ThinStyle',
        'getMenuImage',
        'StatusGraphic',
        'isSoloQuickMode',
        'left',
        'blt',
        'Scene_Menu_goldWindowRect',
        'isArray',
        '12ifLECw',
        'length',
        'MenuCommandClear',
        'forceDisableMainMenuCommand',
        'ActorBgMenus',
        'addOriginalCommands',
        'replace',
        'clear',
        'createStatusWindow',
        'drawItemActorMenuImage',
        'VisuMZ_0_CoreEngine',
        '_variableWindow',
        'createCommandNameWindow',
        '_playtimeText',
        'boxHeight',
        'goldWindowRect',
        'VarList',
        'Scene_Menu_onPersonalCancel',
        'svActorHorzCells',
        'popScene',
        'callUpdateHelp',
        'registerCommand',
        'Scene_Menu_onFormationCancel',
        'resetTextColor',
        'ShowReserve',
        'adjustCommandHeightByVariable',
        'maxItems',
        'SoloStyle',
        'thinGoldWindow',
        '135LCVtwk',
        'changeTextColor',
        'thin',
        'ARRAYFUNC',
        '10YJPhux',
        'drawItemStatusDefaultStyle',
        'adjustCommandHeightByPlaytime',
        'drawItemStatusVerticalStyle',
        'drawSvActor',
        'getMenuImageOffsetY',
        'listStyle',
        'updateOpacity',
        'Scene_Menu_statusWindowRect',
        'commandCancel',
        'statusWindowRectTopStyle',
        'canCreatePlaytimeWindow',
        'drawPlaytime',
        'drawItem',
        'setActor',
        'currentExt',
        'mainMenuCoreSettings',
        '_commandList',
        'drawItemStatusSoloStyle',
        '_playtimeWindow',
    ];
    _0x10b8 = function () {
        return _0x436e5f;
    };
    return _0x10b8();
}
function Sprite_MenuBackgroundActor() {
    this['initialize'](...arguments);
}
((Sprite_MenuBackgroundActor[_0x5e858a(0x17f)] = Object[_0x5e858a(0x1f0)](Sprite['prototype'])),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)]['constructor'] = Sprite_MenuBackgroundActor),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)][_0x5e858a(0x21b)] = function () {
        const _0x528b0c = _0x5e858a;
        ((this[_0x528b0c(0x15c)] = null),
            (this['_bitmapReady'] = ![]),
            Sprite[_0x528b0c(0x17f)][_0x528b0c(0x21b)][_0x528b0c(0x20f)](this),
            (this['x'] = Graphics[_0x528b0c(0x1f9)]));
    }),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)][_0x5e858a(0x1e2)] = function (_0x4514c4) {
        const _0x293256 = _0x5e858a;
        this[_0x293256(0x15c)] !== _0x4514c4 &&
            ((this[_0x293256(0x15c)] = _0x4514c4), this[_0x293256(0x22a)]());
    }),
    (Sprite_MenuBackgroundActor['prototype'][_0x5e858a(0x22a)] = function () {
        const _0x105d7f = _0x5e858a;
        ((this[_0x105d7f(0x1fb)] = ![]),
            this[_0x105d7f(0x15c)]
                ? ((this['bitmap'] = ImageManager['loadPicture'](
                      this['_actor'][_0x105d7f(0x1ac)]()
                  )),
                  this[_0x105d7f(0x149)][_0x105d7f(0xcf)](
                      this[_0x105d7f(0x111)][_0x105d7f(0x152)](this)
                  ))
                : (this[_0x105d7f(0x149)] = new Bitmap(0x1, 0x1)));
    }),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)][_0x5e858a(0x111)] = function () {
        const _0x3390f3 = _0x5e858a;
        ((this[_0x3390f3(0x1fb)] = !![]),
            VisuMZ['MainMenuCore'][_0x3390f3(0x126)][_0x3390f3(0x227)]['ActorBgMenuJS'][
                _0x3390f3(0x20f)
            ](this));
    }),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)]['update'] = function () {
        const _0x9c7106 = _0x5e858a;
        (Sprite[_0x9c7106(0x17f)][_0x9c7106(0x13a)]['call'](this),
            this['_bitmapReady'] &&
                (this['updateOpacity'](), this[_0x9c7106(0x1eb)](), this[_0x9c7106(0x19f)]()));
    }),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)][_0x5e858a(0x1db)] = function () {
        const _0x402f4d = _0x5e858a;
        if (this['_duration'] > 0x0) {
            const _0x5e068c = this[_0x402f4d(0x104)];
            this[_0x402f4d(0x1f2)] =
                (this[_0x402f4d(0x1f2)] * (_0x5e068c - 0x1) + 0xff) / _0x5e068c;
        }
    }),
    (Sprite_MenuBackgroundActor[_0x5e858a(0x17f)][_0x5e858a(0x1eb)] = function () {
        const _0x410e19 = _0x5e858a;
        if (this[_0x410e19(0x104)] > 0x0) {
            const _0x2f7194 = this[_0x410e19(0x104)];
            ((this['x'] = (this['x'] * (_0x2f7194 - 0x1) + this[_0x410e19(0x143)]) / _0x2f7194),
                (this['y'] = (this['y'] * (_0x2f7194 - 0x1) + this[_0x410e19(0x1f6)]) / _0x2f7194));
        }
    }),
    (Sprite_MenuBackgroundActor['prototype']['updateDuration'] = function () {
        const _0x2e7fb9 = _0x5e858a;
        if (this[_0x2e7fb9(0x104)] > 0x0) this['_duration']--;
    }),
    (ImageManager[_0x5e858a(0x1c5)] = ImageManager['svActorHorzCells'] || 0x9),
    (ImageManager[_0x5e858a(0x22d)] = ImageManager[_0x5e858a(0x22d)] || 0x6),
    (Window_Base[_0x5e858a(0x17f)][_0x5e858a(0x1d8)] = function (_0x212680, _0x30c24e, _0x3b1b1b) {
        const _0x32a8d5 = _0x5e858a,
            _0x30bfdc = _0x212680[_0x32a8d5(0xc2)](/\$/i),
            _0xeba3b6 = ImageManager[_0x32a8d5(0x238)](_0x212680),
            _0x51438c = _0xeba3b6['width'] / (_0x30bfdc ? 0x1 : ImageManager[_0x32a8d5(0x1c5)]),
            _0x2c1b5d =
                _0xeba3b6[_0x32a8d5(0x115)] / (_0x30bfdc ? 0x1 : ImageManager[_0x32a8d5(0x22d)]),
            _0x3a0466 = 0x0,
            _0x290295 = 0x0;
        this[_0x32a8d5(0x18e)][_0x32a8d5(0x1b0)](
            _0xeba3b6,
            _0x3a0466,
            _0x290295,
            _0x51438c,
            _0x2c1b5d,
            _0x30c24e - _0x51438c / 0x2,
            _0x3b1b1b - _0x2c1b5d
        );
    }),
    (Window_MenuCommand[_0x5e858a(0x1e5)] =
        VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x126)]['CommandList']),
    (Window_MenuCommand[_0x5e858a(0x147)] = undefined),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x216)] =
        Window_MenuCommand[_0x5e858a(0x17f)]['initialize']),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x21b)] = function (_0x35936c) {
        const _0x5b2491 = _0x5e858a;
        ((this[_0x5b2491(0xe7)] = ''),
            VisuMZ[_0x5b2491(0xfb)][_0x5b2491(0x216)][_0x5b2491(0x20f)](this, _0x35936c),
            this['createCommandNameWindow'](_0x35936c));
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x1bf)] = function (_0x4d4de4) {
        const _0x41f2fd = _0x5e858a,
            _0x42e915 = new Rectangle(
                0x0,
                0x0,
                _0x4d4de4[_0x41f2fd(0x1f9)],
                _0x4d4de4[_0x41f2fd(0x115)]
            );
        ((this['_commandNameWindow'] = new Window_Base(_0x42e915)),
            (this[_0x41f2fd(0xe4)][_0x41f2fd(0x1f2)] = 0x0),
            this[_0x41f2fd(0x15b)](this['_commandNameWindow']),
            this[_0x41f2fd(0x222)]());
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x1c7)] = function () {
        const _0x5acaf5 = _0x5e858a;
        Window_HorzCommand[_0x5acaf5(0x17f)][_0x5acaf5(0x1c7)]['call'](this);
        if (this[_0x5acaf5(0xe4)]) this[_0x5acaf5(0x222)]();
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x222)] = function () {
        const _0x582a36 = _0x5e858a,
            _0x4d0530 = this[_0x582a36(0xe4)];
        _0x4d0530[_0x582a36(0x18e)][_0x582a36(0x1ba)]();
        const _0x413061 = this[_0x582a36(0xc3)](this[_0x582a36(0x125)]());
        if (_0x413061 === _0x582a36(0xef)) {
            const _0xa48b83 = this[_0x582a36(0x10e)](this[_0x582a36(0x125)]());
            let _0x5d99b7 = this[_0x582a36(0xc7)](this[_0x582a36(0x125)]());
            ((_0x5d99b7 = _0x5d99b7[_0x582a36(0x1b9)](/\\I\[(\d+)\]/gi, '')),
                _0x4d0530[_0x582a36(0xee)](),
                this[_0x582a36(0x128)](_0x5d99b7, _0xa48b83),
                this[_0x582a36(0x21d)](_0x5d99b7, _0xa48b83),
                this[_0x582a36(0x226)](_0x5d99b7, _0xa48b83));
        }
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x128)] = function (_0x399bbc, _0x9c4725) {}),
    (Window_MenuCommand['prototype'][_0x5e858a(0x21d)] = function (_0x416584, _0x1347ac) {
        const _0x14ecba = _0x5e858a,
            _0x42c19e = this['_commandNameWindow'];
        _0x42c19e['drawText'](
            _0x416584,
            0x0,
            _0x1347ac['y'],
            _0x42c19e[_0x14ecba(0x16c)],
            _0x14ecba(0x13d)
        );
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x226)] = function (_0x413e9a, _0x500f87) {
        const _0x2b77d2 = _0x5e858a,
            _0x582961 = this[_0x2b77d2(0xe4)],
            _0x3bbd49 = $gameSystem['windowPadding'](),
            _0x4aa2ea =
                _0x500f87['x'] +
                Math[_0x2b77d2(0x23c)](_0x500f87[_0x2b77d2(0x1f9)] / 0x2) +
                _0x3bbd49;
        ((_0x582961['x'] = _0x582961[_0x2b77d2(0x1f9)] / -0x2 + _0x4aa2ea),
            (_0x582961['y'] = Math[_0x2b77d2(0x23c)](_0x500f87['height'] / 0x4)));
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)]['itemHeight'] = function () {
        const _0x2b6014 = _0x5e858a,
            _0x4e5a23 = SceneManager[_0x2b6014(0x133)][_0x2b6014(0xc0)]();
        if (_0x4e5a23 === _0x2b6014(0x156)) {
            const _0x4853fa =
                VisuMZ[_0x2b6014(0xfb)]['Settings'][_0x2b6014(0xf6)]['MobileThickness'];
            return this[_0x2b6014(0x1e8)]() * _0x4853fa + 0x8;
        } else return Window_Command[_0x2b6014(0x17f)][_0x2b6014(0x19b)][_0x2b6014(0x20f)](this);
    }),
    (Window_MenuCommand['prototype'][_0x5e858a(0x138)] = function () {
        const _0x26b90f = _0x5e858a;
        this[_0x26b90f(0x198)]();
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x198)] = function () {
        const _0x4b7f88 = _0x5e858a;
        for (const _0x317e21 of Window_MenuCommand['_commandList']) {
            const _0x3a5217 = _0x317e21['Symbol'];
            if (this[_0x4b7f88(0x203)](_0x3a5217, _0x317e21)) {
                let _0x26819e = _0x317e21[_0x4b7f88(0x170)];
                if (['', 'Untitled'][_0x4b7f88(0x1ec)](_0x26819e))
                    _0x26819e = _0x317e21[_0x4b7f88(0x150)][_0x4b7f88(0x20f)](this);
                const _0x40fc76 = _0x317e21['Icon'];
                _0x40fc76 > 0x0 &&
                    this[_0x4b7f88(0x17e)]() !== _0x4b7f88(0x22b) &&
                    (_0x26819e = _0x4b7f88(0x18f)[_0x4b7f88(0x1a5)](_0x40fc76, _0x26819e));
                const _0x51d0fc = this[_0x4b7f88(0x217)](_0x3a5217, _0x317e21),
                    _0x37b35b = _0x317e21[_0x4b7f88(0xe8)]['call'](this);
                (this['addCommand'](_0x26819e, _0x3a5217, _0x51d0fc, _0x37b35b),
                    this[_0x4b7f88(0x189)](
                        _0x3a5217,
                        _0x317e21[_0x4b7f88(0x139)][_0x4b7f88(0x152)](this, _0x37b35b)
                    ));
            }
            this[_0x4b7f88(0x127)](_0x3a5217);
        }
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x203)] = function (
        _0x2e148b,
        _0x2981d8,
        _0x40d8d5
    ) {
        const _0x148b2f = _0x5e858a;
        if (!_0x40d8d5) {
            if (!this[_0x148b2f(0x12f)](_0x2e148b, _0x2981d8)) return ![];
        }
        if ($gameSystem['getMainMenuSymbolState'](_0x2e148b, _0x148b2f(0x21c))) return !![];
        if ($gameSystem[_0x148b2f(0x16e)](_0x2e148b, 'forceHide')) return ![];
        return _0x2981d8[_0x148b2f(0xe5)][_0x148b2f(0x20f)](this, _0x2e148b, _0x2981d8);
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x217)] = function (_0x33ad5e, _0x320ec5) {
        const _0x3e4b8d = _0x5e858a;
        if ($gameSystem[_0x3e4b8d(0x16e)](_0x33ad5e, 'forceEnable')) return !![];
        if ($gameSystem[_0x3e4b8d(0x16e)](_0x33ad5e, _0x3e4b8d(0x1ea))) return ![];
        return _0x320ec5[_0x3e4b8d(0x13c)][_0x3e4b8d(0x20f)](this, _0x33ad5e, _0x320ec5);
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x127)] = function (_0xb3bf07) {
        const _0x430917 = _0x5e858a;
        switch (_0xb3bf07) {
            case _0x430917(0x158):
                this[_0x430917(0xf8)]();
                break;
            case _0x430917(0x116):
                (this[_0x430917(0xf7)](), this[_0x430917(0x1b8)]());
                break;
            case _0x430917(0x122):
                this['addOptionsCommand']();
                break;
            case _0x430917(0x135):
                this[_0x430917(0x187)]();
                break;
            case _0x430917(0x184):
                this[_0x430917(0x175)]();
                break;
        }
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0xf8)] = function () {}),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0xf7)] = function () {}),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x1b8)] = function () {}),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x11b)] = function () {}),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x187)] = function () {}),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x175)] = function () {}),
    (Window_MenuCommand['prototype']['maxCols'] = function () {
        const _0x21efa0 = _0x5e858a,
            _0x1e820b = SceneManager['_scene'][_0x21efa0(0xc0)]();
        if ([_0x21efa0(0x225), 'thinBottom'][_0x21efa0(0x1ec)](_0x1e820b))
            return this['_list'] ? this[_0x21efa0(0x1cd)]() : 0x4;
        else
            return _0x1e820b !== _0x21efa0(0x15e)
                ? VisuMZ[_0x21efa0(0xfb)]['Settings']['CustomCmdWin']['Cols']
                : Window_Command['prototype']['maxCols'][_0x21efa0(0x20f)](this);
    }),
    (Window_MenuCommand['prototype'][_0x5e858a(0x194)] = function () {
        return this['_subcategory'] || '';
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x12f)] = function (_0x5a6ec8, _0x177004) {
        const _0x107f08 = _0x5e858a,
            _0x3f7013 = _0x177004['Subcategory'] || '';
        if (!this[_0x107f08(0xc1)](_0x3f7013) && this['currentSubcategory']() === '') return !![];
        return _0x3f7013 === this[_0x107f08(0x194)]();
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0xc1)] = function (_0x475e05) {
        const _0x227563 = _0x5e858a;
        return this[_0x227563(0x1fd)]()[_0x227563(0x1ec)](_0x475e05);
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x1fd)] = function () {
        const _0x13d2fa = _0x5e858a;
        if (Window_MenuCommand[_0x13d2fa(0x147)] !== undefined)
            return Window_MenuCommand['SUBCATEGORY_LIST'];
        Window_MenuCommand[_0x13d2fa(0x147)] = [];
        for (const _0x222f9 of Window_MenuCommand[_0x13d2fa(0x1e5)]) {
            const _0xf3550b = _0x222f9[_0x13d2fa(0x191)];
            if (_0xf3550b !== _0x13d2fa(0x13f)) continue;
            const _0x2a7389 = _0x222f9['ExtJS']['call'](this);
            Window_MenuCommand[_0x13d2fa(0x147)][_0x13d2fa(0x1ef)](_0x2a7389);
        }
        return Window_MenuCommand[_0x13d2fa(0x147)];
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x193)] = function (_0x2fb539) {
        const _0x3d1eb3 = _0x5e858a;
        if (!_0x2fb539) return !![];
        const _0x261178 = _0x2fb539[_0x3d1eb3(0xe8)][_0x3d1eb3(0x20f)](this);
        for (const _0x580f70 of Window_MenuCommand[_0x3d1eb3(0x1e5)]) {
            if (_0x580f70 === _0x2fb539) continue;
            const _0x2e3e95 = _0x580f70[_0x3d1eb3(0x23e)] || '';
            if (_0x2e3e95 !== _0x261178) continue;
            const _0x2ff533 = _0x580f70[_0x3d1eb3(0x191)];
            if (this[_0x3d1eb3(0x203)](_0x2ff533, _0x580f70, !![])) return !![];
        }
        return ![];
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x213)] = function (_0x3f3fd7) {
        const _0x13e99b = _0x5e858a;
        _0x3f3fd7 = _0x3f3fd7;
        if (this['currentSubcategory']() === _0x3f3fd7) return;
        ((this['_subcategory'] = _0x3f3fd7),
            this[_0x13e99b(0x19a)](),
            this[_0x13e99b(0x151)](0x0),
            this[_0x13e99b(0x106)](0x0),
            this[_0x13e99b(0x178)]());
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x12d)] = function () {
        const _0x2dc675 = _0x5e858a,
            _0x770128 = this['currentSubcategory']();
        ((this[_0x2dc675(0xe7)] = ''), this[_0x2dc675(0x19a)](), this['setTopRow'](0x0));
        this['_scrollDuration'] > 0x1 &&
            ((this['_scrollDuration'] = 0x1), this['updateSmoothScroll']());
        const _0x43a9c4 = Math['max'](this[_0x2dc675(0x215)](_0x770128), 0x0);
        (this[_0x2dc675(0x16f)](_0x43a9c4), this[_0x2dc675(0x178)]());
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)]['itemTextAlign'] = function () {
        const _0x557cfa = _0x5e858a;
        return VisuMZ[_0x557cfa(0xfb)][_0x557cfa(0x126)]['CustomCmdWin']['TextAlign'];
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x1e1)] = function (_0x218cb0) {
        const _0x573533 = _0x5e858a,
            _0x36cdd9 = this[_0x573533(0xc3)](_0x218cb0);
        if (_0x36cdd9 === _0x573533(0xdb)) this[_0x573533(0x155)](_0x218cb0);
        else
            _0x36cdd9 === _0x573533(0xef)
                ? this[_0x573533(0x223)](_0x218cb0)
                : Window_Command[_0x573533(0x17f)][_0x573533(0x1e1)][_0x573533(0x20f)](
                      this,
                      _0x218cb0
                  );
    }),
    (Window_MenuCommand['prototype']['commandStyle'] = function () {
        const _0x30b7af = _0x5e858a;
        return VisuMZ['MainMenuCore'][_0x30b7af(0x126)][_0x30b7af(0xf6)]['Style'];
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)]['commandStyleCheck'] = function (_0x509c1c) {
        const _0x620705 = _0x5e858a,
            _0x263f77 = this[_0x620705(0x17e)]();
        if (_0x263f77 !== 'auto') return _0x263f77;
        else {
            const _0x203500 = this[_0x620705(0xc7)](_0x509c1c);
            if (_0x203500['match'](/\\I\[(\d+)\]/i)) {
                const _0x2a8a13 = this[_0x620705(0x10e)](_0x509c1c),
                    _0xc12d7d = this[_0x620705(0x1fc)](_0x203500)[_0x620705(0x1f9)];
                return _0xc12d7d <= _0x2a8a13[_0x620705(0x1f9)] ? _0x620705(0xdb) : _0x620705(0xef);
            } else return 'text';
        }
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x155)] = function (_0x32c89e) {
        const _0x1c1551 = _0x5e858a,
            _0x4e042c = this[_0x1c1551(0x10e)](_0x32c89e),
            _0x18325e = this[_0x1c1551(0xc7)](_0x32c89e),
            _0x3bf266 = this['textSizeEx'](_0x18325e)[_0x1c1551(0x1f9)];
        this[_0x1c1551(0x234)](this[_0x1c1551(0x107)](_0x32c89e));
        let _0x21faad = this[_0x1c1551(0x179)]();
        if (_0x21faad === 'right')
            this[_0x1c1551(0x20b)](
                _0x18325e,
                _0x4e042c['x'] + _0x4e042c['width'] - _0x3bf266,
                _0x4e042c['y'],
                _0x3bf266
            );
        else {
            if (_0x21faad === 'center') {
                const _0x2e6016 =
                    _0x4e042c['x'] +
                    Math[_0x1c1551(0x23c)]((_0x4e042c[_0x1c1551(0x1f9)] - _0x3bf266) / 0x2);
                this[_0x1c1551(0x20b)](_0x18325e, _0x2e6016, _0x4e042c['y'], _0x3bf266);
            } else this[_0x1c1551(0x20b)](_0x18325e, _0x4e042c['x'], _0x4e042c['y'], _0x3bf266);
        }
    }),
    (Window_MenuCommand[_0x5e858a(0x17f)][_0x5e858a(0x223)] = function (_0x583ab6) {
        const _0x34816c = _0x5e858a;
        this['commandName'](_0x583ab6)[_0x34816c(0xc2)](/\\I\[(\d+)\]/i);
        const _0x2720c8 = Number(RegExp['$1']),
            _0x5558bb = this[_0x34816c(0x10e)](_0x583ab6),
            _0x50a3e2 =
                _0x5558bb['x'] +
                Math[_0x34816c(0x23c)](
                    (_0x5558bb[_0x34816c(0x1f9)] - ImageManager[_0x34816c(0x102)]) / 0x2
                ),
            _0x16620d =
                _0x5558bb['y'] + (_0x5558bb['height'] - ImageManager[_0x34816c(0x1a2)]) / 0x2;
        this['drawIcon'](_0x2720c8, _0x50a3e2, _0x16620d);
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0xfc)] =
        Window_StatusBase[_0x5e858a(0x17f)][_0x5e858a(0x101)]),
    (Window_StatusBase[_0x5e858a(0x17f)][_0x5e858a(0x101)] = function () {
        const _0x2ad1fd = _0x5e858a;
        (VisuMZ['MainMenuCore'][_0x2ad1fd(0xfc)]['call'](this), this['loadOtherActorImages']());
    }),
    (Window_StatusBase[_0x5e858a(0x17f)][_0x5e858a(0x219)] = function () {
        const _0x22244f = _0x5e858a;
        for (const _0x10f703 of $gameParty['members']()) {
            if (!_0x10f703) continue;
            (_0x10f703[_0x22244f(0x185)]() &&
                ImageManager[_0x22244f(0x220)](_0x10f703['characterName']()),
                _0x10f703[_0x22244f(0xde)]() &&
                    ImageManager[_0x22244f(0x238)](_0x10f703[_0x22244f(0xde)]()),
                _0x10f703[_0x22244f(0x1ac)]() &&
                    ImageManager['loadPicture'](_0x10f703[_0x22244f(0x1ac)]()));
        }
    }),
    (Window_StatusBase[_0x5e858a(0x17f)][_0x5e858a(0x190)] = function () {
        const _0x508e98 = _0x5e858a;
        return VisuMZ[_0x508e98(0xfb)][_0x508e98(0x126)][_0x508e98(0x1ad)];
    }),
    (Window_StatusBase['prototype'][_0x5e858a(0x100)] = function (
        _0x9fa577,
        _0x32ea1b,
        _0xd87477,
        _0x5d0f54,
        _0x47a23d
    ) {
        const _0x11b100 = _0x5e858a;
        ((_0x5d0f54 = _0x5d0f54 || ImageManager[_0x11b100(0x1f8)]),
            (_0x47a23d = _0x47a23d || ImageManager[_0x11b100(0x166)]));
        const _0x3174ef = ImageManager['faceWidth'],
            _0x4bfb6f = _0x47a23d - 0x2,
            _0x368432 = _0x32ea1b + Math[_0x11b100(0x23c)]((_0x5d0f54 - _0x3174ef) / 0x2);
        (this[_0x11b100(0x176)] === Window_MenuStatus &&
            this[_0x11b100(0x234)](_0x9fa577[_0x11b100(0x197)]()),
            this['drawActorFace'](_0x9fa577, _0x368432, _0xd87477, _0x3174ef, _0x4bfb6f),
            this['changePaintOpacity'](!![]));
    }),
    (Window_StatusBase[_0x5e858a(0x17f)]['drawItemActorSprite'] = function (
        _0x2c18c5,
        _0x98def3,
        _0x4bc02b,
        _0x4c9a9d,
        _0x54181b
    ) {
        const _0x52b1b0 = _0x5e858a;
        ((_0x4c9a9d = _0x4c9a9d || ImageManager[_0x52b1b0(0x1f8)]),
            (_0x54181b = _0x54181b || ImageManager[_0x52b1b0(0x166)]));
        const _0x2baf4d = _0x2c18c5[_0x52b1b0(0x185)](),
            _0x4c84ef = _0x2c18c5[_0x52b1b0(0xe1)](),
            _0x15e555 = ImageManager['loadCharacter'](_0x2baf4d),
            _0x4d294c = ImageManager[_0x52b1b0(0x10c)](_0x2baf4d),
            _0x57058c = _0x15e555[_0x52b1b0(0x1f9)] / (_0x4d294c ? 0x3 : 0xc),
            _0x25035f = _0x15e555[_0x52b1b0(0x115)] / (_0x4d294c ? 0x4 : 0x8),
            _0x59f2fd = _0x4c9a9d,
            _0x404f04 = _0x54181b - 0x2,
            _0x2d402a = _0x98def3 + Math[_0x52b1b0(0x23c)](_0x59f2fd / 0x2),
            _0x31c4fb = _0x4bc02b + Math[_0x52b1b0(0x164)]((_0x54181b + _0x25035f) / 0x2);
        this[_0x52b1b0(0x176)] === Window_MenuStatus &&
            this[_0x52b1b0(0x234)](_0x2c18c5[_0x52b1b0(0x197)]());
        const _0x5054b5 = Math[_0x52b1b0(0x240)](_0x4c9a9d, _0x57058c),
            _0x33243c = Math[_0x52b1b0(0x240)](_0x54181b, _0x25035f),
            _0x238d94 = Math[_0x52b1b0(0x23c)](
                _0x98def3 + Math[_0x52b1b0(0x1a8)](_0x4c9a9d - _0x57058c, 0x0) / 0x2
            ),
            _0x2a0c87 = Math[_0x52b1b0(0x23c)](
                _0x4bc02b + Math[_0x52b1b0(0x1a8)](_0x54181b - _0x25035f, 0x0) / 0x2
            ),
            _0x3143cd = _0x4d294c ? 0x0 : _0x4c84ef,
            _0x350f0d = ((_0x3143cd % 0x4) * 0x3 + 0x1) * _0x57058c,
            _0x5cd54e = Math[_0x52b1b0(0x23c)](_0x3143cd / 0x4) * 0x4 * _0x25035f;
        (this[_0x52b1b0(0x18e)][_0x52b1b0(0x1b0)](
            _0x15e555,
            _0x350f0d,
            _0x5cd54e,
            _0x5054b5,
            _0x33243c,
            _0x238d94,
            _0x2a0c87
        ),
            this[_0x52b1b0(0x234)](!![]));
    }),
    (Window_StatusBase['prototype'][_0x5e858a(0xf3)] = function (
        _0x189bf2,
        _0x33c92d,
        _0x206ae1,
        _0x11bca3,
        _0x5f0249
    ) {
        const _0x47c656 = _0x5e858a;
        ((_0x11bca3 = _0x11bca3 || ImageManager[_0x47c656(0x1f8)]),
            (_0x5f0249 = _0x5f0249 || ImageManager['faceHeight']));
        const _0x5ea0b9 = ImageManager['loadSvActor'](_0x189bf2[_0x47c656(0xde)]()),
            _0x181c01 = _0x5ea0b9[_0x47c656(0x1f9)] / ImageManager['svActorHorzCells'],
            _0x35872b = _0x5ea0b9[_0x47c656(0x115)] / ImageManager[_0x47c656(0x22d)],
            _0x73b74e = _0x11bca3,
            _0x614a68 = _0x5f0249 - 0x2,
            _0x1e06ee = _0x33c92d + Math[_0x47c656(0x23c)](_0x73b74e / 0x2),
            _0x16d140 = _0x206ae1 + Math[_0x47c656(0x164)]((_0x5f0249 + _0x35872b) / 0x2);
        this[_0x47c656(0x176)] === Window_MenuStatus &&
            this[_0x47c656(0x234)](_0x189bf2['isBattleMember']());
        const _0x32ecc5 = _0x189bf2[_0x47c656(0x20a)] && _0x189bf2[_0x47c656(0x20a)](),
            _0x50d7c4 = 0x0,
            _0x377aa6 = 0x0,
            _0x214168 = _0x32ecc5 ? _0x5ea0b9[_0x47c656(0x1f9)] : _0x181c01,
            _0x10896c = _0x32ecc5 ? _0x5ea0b9[_0x47c656(0x115)] : _0x35872b,
            _0x59a00b = Math[_0x47c656(0x240)](0x1, _0x11bca3 / _0x214168, _0x5f0249 / _0x10896c),
            _0x286876 = _0x59a00b * _0x214168,
            _0x5e362a = _0x59a00b * _0x10896c,
            _0x8c38f = Math['floor'](_0x33c92d + Math['max'](_0x11bca3 - _0x286876, 0x0) / 0x2),
            _0x1337dc = Math[_0x47c656(0x23c)](
                _0x206ae1 + Math['max'](_0x5f0249 - _0x5e362a, 0x0) / 0x2
            );
        (this[_0x47c656(0x18e)][_0x47c656(0x1b0)](
            _0x5ea0b9,
            _0x50d7c4,
            _0x377aa6,
            _0x214168,
            _0x10896c,
            _0x8c38f,
            _0x1337dc,
            _0x286876,
            _0x5e362a
        ),
            this[_0x47c656(0x234)](!![]));
    }),
    (Window_StatusBase[_0x5e858a(0x17f)][_0x5e858a(0x1bc)] = function (
        _0x380215,
        _0x1cd37d,
        _0x3b2048,
        _0x36eb6d,
        _0x2a9ca3
    ) {
        const _0x54eab3 = _0x5e858a,
            _0x477c3e = ImageManager[_0x54eab3(0x1f7)](_0x380215[_0x54eab3(0x1ac)]());
        ((_0x36eb6d = (_0x36eb6d || ImageManager[_0x54eab3(0x1f8)]) - 0x2),
            (_0x2a9ca3 = (_0x2a9ca3 || ImageManager[_0x54eab3(0x166)]) - 0x2));
        const _0x58244b = _0x477c3e[_0x54eab3(0x1f9)],
            _0x1772cc = _0x477c3e[_0x54eab3(0x115)],
            _0x3a13bd = _0x36eb6d,
            _0x4f1657 = _0x2a9ca3 - 0x2,
            _0xa03725 = _0x1cd37d + Math['floor'](_0x3a13bd / 0x2),
            _0x5708cf = _0x3b2048 + Math[_0x54eab3(0x164)]((_0x2a9ca3 + _0x1772cc) / 0x2);
        this[_0x54eab3(0x176)] === Window_MenuStatus &&
            this[_0x54eab3(0x234)](_0x380215[_0x54eab3(0x197)]());
        const _0x1bc82f = Math[_0x54eab3(0x240)](_0x36eb6d, _0x58244b),
            _0x48b381 = Math[_0x54eab3(0x240)](_0x2a9ca3, _0x1772cc),
            _0x5cb6ac = _0x1cd37d + 0x1,
            _0x433396 = Math['max'](_0x3b2048 + 0x1, _0x3b2048 + _0x4f1657 - _0x1772cc + 0x3);
        let _0x1d6b76 = Math[_0x54eab3(0x162)]((_0x58244b - _0x1bc82f) / 0x2),
            _0x218b32 = Math[_0x54eab3(0x162)]((_0x1772cc - _0x48b381) / 0x2);
        ((_0x1d6b76 -= _0x380215[_0x54eab3(0x214)]()),
            (_0x218b32 -= _0x380215['getMenuImageOffsetY']()));
        if (Imported[_0x54eab3(0x1bd)]) {
            if (VisuMZ['CoreEngine'][_0x54eab3(0x126)]['QoL'][_0x54eab3(0x141)]) {
            }
        }
        (this[_0x54eab3(0x18e)][_0x54eab3(0x1b0)](
            _0x477c3e,
            _0x1d6b76,
            _0x218b32,
            _0x1bc82f,
            _0x48b381,
            _0x5cb6ac,
            _0x433396
        ),
            this[_0x54eab3(0x234)](!![]));
    }),
    (Window_Status[_0x5e858a(0x17f)][_0x5e858a(0xc5)] = function (
        _0x2ac090,
        _0x9efd76,
        _0x28f302,
        _0x13e671,
        _0x3fd88b
    ) {
        const _0x156e1 = _0x5e858a;
        switch (this[_0x156e1(0x190)]()) {
            case _0x156e1(0x140):
                break;
            case _0x156e1(0xe3):
                this[_0x156e1(0x112)](_0x2ac090, _0x9efd76, _0x28f302, _0x13e671, _0x3fd88b);
                break;
            case _0x156e1(0x19e):
                this['drawItemActorSvBattler'](
                    _0x2ac090,
                    _0x9efd76,
                    _0x28f302,
                    _0x13e671,
                    _0x3fd88b
                );
                break;
            default:
                Window_StatusBase[_0x156e1(0x17f)][_0x156e1(0xc5)][_0x156e1(0x20f)](
                    this,
                    _0x2ac090,
                    _0x9efd76,
                    _0x28f302,
                    _0x13e671,
                    _0x3fd88b
                );
                break;
        }
    }),
    (VisuMZ[_0x5e858a(0xfb)][_0x5e858a(0x148)] = Window_MenuStatus['prototype'][_0x5e858a(0xfa)]),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0xfa)] = function () {
        const _0x55d8d6 = _0x5e858a;
        VisuMZ[_0x55d8d6(0xfb)][_0x55d8d6(0x126)][_0x55d8d6(0x227)][_0x55d8d6(0x18d)]
            ? VisuMZ[_0x55d8d6(0xfb)][_0x55d8d6(0x148)][_0x55d8d6(0x20f)](this)
            : this[_0x55d8d6(0x16f)](0x0);
    }),
    (VisuMZ[_0x5e858a(0xfb)]['Window_MenuStatus_maxItems'] =
        Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1cd)]),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1cd)] = function () {
        const _0x2e7385 = _0x5e858a;
        return this[_0x2e7385(0x1a4)]()
            ? $gameParty['battleMembers']()[_0x2e7385(0x1b4)]
            : VisuMZ['MainMenuCore']['Window_MenuStatus_maxItems'][_0x2e7385(0x20f)](this);
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1a4)] = function () {
        const _0x4577aa = _0x5e858a,
            _0x343677 = VisuMZ['MainMenuCore'][_0x4577aa(0x126)][_0x4577aa(0x227)];
        if (_0x343677[_0x4577aa(0x1cb)] === undefined) _0x343677['ShowReserve'] = !![];
        const _0xad993f = SceneManager['_scene'];
        if (!_0x343677[_0x4577aa(0x1cb)]) {
            if (_0x343677[_0x4577aa(0xd3)]) return _0xad993f[_0x4577aa(0x176)] === Scene_Menu;
            return !![];
        }
        return ![];
    }),
    (Window_MenuStatus['prototype'][_0x5e858a(0x1da)] = function () {
        const _0x36904a = _0x5e858a,
            _0x1615c6 = SceneManager[_0x36904a(0x133)][_0x36904a(0x176)];
        return _0x1615c6 === Scene_Menu
            ? VisuMZ[_0x36904a(0xfb)][_0x36904a(0x126)]['StatusListStyle']
            : VisuMZ[_0x36904a(0xfb)][_0x36904a(0x126)]['InnerMenuListStyle'];
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x233)] = function () {
        const _0xf53b9e = _0x5e858a,
            _0x287ec2 = this[_0xf53b9e(0x1da)]();
        switch (_0x287ec2) {
            case 'vertical':
            case _0xf53b9e(0x168):
                return 0x1;
            case _0xf53b9e(0x105):
                return 0x1;
            default:
                return $gameParty[_0xf53b9e(0x169)]();
        }
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0xdf)] = function () {
        const _0x4353e3 = _0x5e858a,
            _0x5c1c37 = this['listStyle']();
        switch (_0x5c1c37) {
            case _0x4353e3(0x17b):
            case _0x4353e3(0x168):
                return $gameParty[_0x4353e3(0x169)]();
            default:
                return 0x1;
        }
    }),
    (VisuMZ['MainMenuCore'][_0x5e858a(0x22f)] = Window_MenuStatus['prototype'][_0x5e858a(0x19b)]),
    (Window_MenuStatus['prototype'][_0x5e858a(0x19b)] = function () {
        const _0x34b078 = _0x5e858a,
            _0x13cb53 = this[_0x34b078(0x1da)]();
        switch (_0x13cb53) {
            case 'vertical':
            case _0x34b078(0x168):
            case _0x34b078(0x105):
                return this[_0x34b078(0x144)];
            case _0x34b078(0x1d2):
                return Window_Selectable[_0x34b078(0x17f)]['itemHeight']['call'](this);
            case _0x34b078(0x12b):
                return this[_0x34b078(0x1e8)]() * 0x2 + 0x8;
            default:
                return VisuMZ[_0x34b078(0xfb)]['Window_MenuStatus_itemHeight'][_0x34b078(0x20f)](
                    this
                );
        }
    }),
    (Window_MenuStatus['prototype'][_0x5e858a(0x1e1)] = function (_0x59c3cd) {
        const _0x1b268d = _0x5e858a;
        (this[_0x1b268d(0x1f3)](_0x59c3cd), this[_0x1b268d(0x23b)](_0x59c3cd));
    }),
    (VisuMZ['MainMenuCore'][_0x5e858a(0x186)] =
        Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x172)]),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x173)] = function (
        _0x56305b,
        _0x302dda,
        _0x1dcf64,
        _0x36f87f,
        _0x9975c3
    ) {
        const _0x10695d = _0x5e858a;
        switch (this[_0x10695d(0x190)]()) {
            case _0x10695d(0x140):
                break;
            case _0x10695d(0xe3):
                this['drawItemActorSprite'](
                    _0x56305b,
                    _0x302dda,
                    _0x1dcf64 + 0x1,
                    _0x36f87f,
                    _0x9975c3 - 0x2
                );
                break;
            case _0x10695d(0x19e):
                this[_0x10695d(0xf3)](
                    _0x56305b,
                    _0x302dda,
                    _0x1dcf64 + 0x1,
                    _0x36f87f,
                    _0x9975c3 - 0x2
                );
                break;
            default:
                this['drawItemActorFace'](_0x56305b, _0x302dda, _0x1dcf64, _0x36f87f, _0x9975c3);
                break;
        }
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x23b)] = function (_0x2cdf53) {
        const _0x1b30cb = _0x5e858a;
        this[_0x1b30cb(0xee)]();
        const _0x1e627e = this['actor'](_0x2cdf53),
            _0x5964bb = this['itemRect'](_0x2cdf53),
            _0x473d2c = this['listStyle']();
        switch (_0x473d2c) {
            case _0x1b30cb(0x17b):
                this[_0x1b30cb(0x1d7)](_0x1e627e, _0x5964bb);
                break;
            case _0x1b30cb(0x168):
                this[_0x1b30cb(0x121)](_0x1e627e, _0x5964bb);
                break;
            case _0x1b30cb(0x105):
                this['drawItemStatusSoloStyle'](_0x1e627e, _0x5964bb);
                break;
            case 'thin':
                this[_0x1b30cb(0x10a)](_0x1e627e, _0x5964bb);
                break;
            case _0x1b30cb(0x12b):
                this['drawItemStatusThickerStyle'](_0x1e627e, _0x5964bb);
                break;
            default:
                this[_0x1b30cb(0x1d5)](_0x1e627e, _0x5964bb);
                break;
        }
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1d7)] = function (_0x1a7de8, _0x4308af) {
        const _0x84dfa7 = _0x5e858a;
        VisuMZ['MainMenuCore'][_0x84dfa7(0x126)][_0x84dfa7(0x18c)][_0x84dfa7(0x129)][
            _0x84dfa7(0x20f)
        ](this, _0x1a7de8, _0x4308af);
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x121)] = function (_0x4ff704, _0x4d3278) {
        const _0x45f7d1 = _0x5e858a;
        if (_0x4ff704[_0x45f7d1(0x1ac)]() !== '') {
            const _0x2e7b04 = ImageManager[_0x45f7d1(0x1f7)](_0x4ff704[_0x45f7d1(0x1ac)]());
            _0x2e7b04[_0x45f7d1(0xcf)](this[_0x45f7d1(0x1e9)]['bind'](this, _0x4ff704, _0x4d3278));
        } else this['drawItemStatusVerticalStyle'](_0x4ff704, _0x4d3278);
    }),
    (Window_MenuStatus['prototype'][_0x5e858a(0x1e9)] = function (_0xe5862, _0xa9ca61) {
        const _0x4ec39a = _0x5e858a;
        VisuMZ['MainMenuCore'][_0x4ec39a(0x126)]['ListStyles'][_0x4ec39a(0x137)][_0x4ec39a(0x20f)](
            this,
            _0xe5862,
            _0xa9ca61
        );
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1e6)] = function (_0x1ee8f4, _0x446f7e) {
        const _0x57edd6 = _0x5e858a,
            _0x216e99 = ImageManager[_0x57edd6(0x1f7)](_0x1ee8f4[_0x57edd6(0x1ac)]());
        _0x216e99[_0x57edd6(0xcf)](
            this['drawItemStatusSoloStyleOnLoad']['bind'](this, _0x1ee8f4, _0x446f7e)
        );
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0xbe)] = function (_0xf80aa1, _0xea569e) {
        const _0x2be24e = _0x5e858a;
        VisuMZ[_0x2be24e(0xfb)][_0x2be24e(0x126)][_0x2be24e(0x18c)][_0x2be24e(0x1ce)][
            _0x2be24e(0x20f)
        ](this, _0xf80aa1, _0xea569e);
    }),
    (Window_MenuStatus['prototype']['drawItemStatusThinStyle'] = function (_0x465500, _0x1a3e84) {
        const _0x4d9326 = _0x5e858a;
        VisuMZ[_0x4d9326(0xfb)][_0x4d9326(0x126)][_0x4d9326(0x18c)][_0x4d9326(0x1ab)][
            _0x4d9326(0x20f)
        ](this, _0x465500, _0x1a3e84);
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0xf4)] = function (_0x126c6a, _0x2843fa) {
        const _0x4dd349 = _0x5e858a;
        VisuMZ[_0x4dd349(0xfb)][_0x4dd349(0x126)][_0x4dd349(0x18c)][_0x4dd349(0x15a)][
            _0x4dd349(0x20f)
        ](this, _0x126c6a, _0x2843fa);
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0xd9)] = function () {
        const _0x3790e7 = _0x5e858a,
            _0x5dd71c = this[_0x3790e7(0x1da)]();
        if ([_0x3790e7(0x1d2), _0x3790e7(0x12b)][_0x3790e7(0x1ec)](_0x5dd71c)) return ![];
        return Window_StatusBase[_0x3790e7(0x17f)][_0x3790e7(0xd9)]['call'](this);
    }),
    (Window_MenuStatus[_0x5e858a(0x17f)][_0x5e858a(0x1d5)] = function (_0x3a4b59, _0x49857d) {
        const _0x358407 = _0x5e858a;
        VisuMZ[_0x358407(0xfb)][_0x358407(0x126)]['ListStyles']['DefaultStyle'][_0x358407(0x20f)](
            this,
            _0x3a4b59,
            _0x49857d
        );
    }),
    (Window_SkillStatus['prototype'][_0x5e858a(0xc5)] = function (
        _0x36e6c5,
        _0x112a00,
        _0x3b8b66,
        _0x3fa509,
        _0x28a804
    ) {
        const _0x154a9e = _0x5e858a;
        switch (this[_0x154a9e(0x190)]()) {
            case _0x154a9e(0x140):
                break;
            case _0x154a9e(0xe3):
                this['drawItemActorSprite'](_0x36e6c5, _0x112a00, _0x3b8b66, _0x3fa509, _0x28a804);
                break;
            case _0x154a9e(0x19e):
                this['drawItemActorSvBattler'](
                    _0x36e6c5,
                    _0x112a00,
                    _0x3b8b66,
                    _0x3fa509,
                    _0x28a804
                );
                break;
            default:
                Window_StatusBase[_0x154a9e(0x17f)][_0x154a9e(0xc5)]['call'](
                    this,
                    _0x36e6c5,
                    _0x112a00,
                    _0x3b8b66,
                    _0x3fa509,
                    _0x28a804
                );
                break;
        }
    }),
    (Window_EquipStatus[_0x5e858a(0x17f)]['drawActorFace'] = function (
        _0x510ec7,
        _0x10d7e2,
        _0x272ff3,
        _0x5d3777,
        _0x52846b
    ) {
        const _0x30df0c = _0x5e858a;
        switch (this[_0x30df0c(0x190)]()) {
            case 'none':
                break;
            case 'sprite':
                this[_0x30df0c(0x112)](_0x510ec7, _0x10d7e2, _0x272ff3, _0x5d3777, _0x52846b);
                break;
            case 'svbattler':
                this[_0x30df0c(0xf3)](_0x510ec7, _0x10d7e2, _0x272ff3, _0x5d3777, _0x52846b);
                break;
            default:
                Window_StatusBase[_0x30df0c(0x17f)][_0x30df0c(0xc5)]['call'](
                    this,
                    _0x510ec7,
                    _0x10d7e2,
                    _0x272ff3,
                    _0x5d3777,
                    _0x52846b
                );
                break;
        }
    }));
function Window_ThinGold() {
    this['initialize'](...arguments);
}
((Window_ThinGold[_0x5e858a(0x17f)] = Object[_0x5e858a(0x1f0)](Window_Gold['prototype'])),
    (Window_ThinGold[_0x5e858a(0x17f)][_0x5e858a(0x176)] = Window_ThinGold),
    (Window_ThinGold[_0x5e858a(0x17f)][_0x5e858a(0x19b)] = function () {
        const _0x30b26c = _0x5e858a;
        return this[_0x30b26c(0x1e8)]();
    }),
    (Window_ThinGold[_0x5e858a(0x17f)]['colSpacing'] = function () {
        const _0x3ec83f = _0x5e858a;
        return Window_Selectable[_0x3ec83f(0x17f)][_0x3ec83f(0x22c)][_0x3ec83f(0x20f)](this);
    }));
function Window_Playtime() {
    const _0x18b4c5 = _0x5e858a;
    this[_0x18b4c5(0x21b)](...arguments);
}
((Window_Playtime[_0x5e858a(0x17f)] = Object['create'](Window_Selectable[_0x5e858a(0x17f)])),
    (Window_Playtime['prototype']['constructor'] = Window_Playtime),
    (Window_Playtime['prototype'][_0x5e858a(0x21b)] = function (_0x5482a6) {
        const _0x2c4cf7 = _0x5e858a;
        ((this[_0x2c4cf7(0x1c0)] = $gameSystem[_0x2c4cf7(0x14d)]()),
            (this[_0x2c4cf7(0x17a)] = 0x3c),
            Window_Selectable[_0x2c4cf7(0x17f)][_0x2c4cf7(0x21b)]['call'](this, _0x5482a6),
            this[_0x2c4cf7(0x19a)]());
    }),
    (Window_Playtime['prototype']['itemHeight'] = function () {
        const _0x438978 = _0x5e858a;
        return this[_0x438978(0x1e8)]();
    }),
    (Window_Playtime['prototype'][_0x5e858a(0x13a)] = function () {
        const _0x116c7d = _0x5e858a;
        (Window_Selectable[_0x116c7d(0x17f)][_0x116c7d(0x13a)][_0x116c7d(0x20f)](this),
            this[_0x116c7d(0xd8)]());
    }),
    (Window_Playtime[_0x5e858a(0x17f)][_0x5e858a(0xd8)] = function () {
        const _0x52fc20 = _0x5e858a;
        if (this[_0x52fc20(0x17a)]-- > 0x0) {
            if (this['_timer'] <= 0x0) this['refresh']();
        }
    }),
    (Window_Playtime[_0x5e858a(0x17f)][_0x5e858a(0x19a)] = function () {
        const _0x1f0368 = _0x5e858a;
        this[_0x1f0368(0x17a)] = 0x3c;
        const _0x52c7d3 = this['itemLineRect'](0x0),
            _0xcc39b5 = _0x52c7d3['x'],
            _0x172777 = _0x52c7d3['y'],
            _0x561c2c = _0x52c7d3[_0x1f0368(0x1f9)];
        (this[_0x1f0368(0x18e)][_0x1f0368(0x1ba)](),
            this['drawTimeIcon'](_0x52c7d3),
            this[_0x1f0368(0x212)](_0x52c7d3),
            this[_0x1f0368(0x1e0)](_0x52c7d3));
    }),
    (Window_Playtime[_0x5e858a(0x17f)]['resetFontSettings'] = function () {
        const _0x2b162c = _0x5e858a;
        (Window_Selectable[_0x2b162c(0x17f)][_0x2b162c(0xee)][_0x2b162c(0x20f)](this),
            (this['contents'][_0x2b162c(0x22e)] =
                VisuMZ[_0x2b162c(0xfb)][_0x2b162c(0x126)][_0x2b162c(0x1ee)][_0x2b162c(0xc6)]));
    }),
    (Window_Playtime[_0x5e858a(0x17f)][_0x5e858a(0x195)] = function (_0x595055) {
        const _0x53ef3b = _0x5e858a;
        if (VisuMZ[_0x53ef3b(0xfb)]['Settings'][_0x53ef3b(0x1ee)][_0x53ef3b(0x188)] > 0x0) {
            const _0x382727 =
                    VisuMZ[_0x53ef3b(0xfb)][_0x53ef3b(0x126)]['Playtime'][_0x53ef3b(0x188)],
                _0x5d0e8d =
                    _0x595055['y'] + (this['lineHeight']() - ImageManager[_0x53ef3b(0x1a2)]) / 0x2;
            this['drawIcon'](_0x382727, _0x595055['x'], _0x5d0e8d);
            const _0x3a37a2 = ImageManager[_0x53ef3b(0x102)] + 0x4;
            ((_0x595055['x'] += _0x3a37a2), (_0x595055[_0x53ef3b(0x1f9)] -= _0x3a37a2));
        }
    }),
    (Window_Playtime[_0x5e858a(0x17f)]['drawTimeLabel'] = function (_0x2d471c) {
        const _0x511bd6 = _0x5e858a;
        (this[_0x511bd6(0xee)](), this[_0x511bd6(0x1d1)](ColorManager[_0x511bd6(0x11e)]()));
        const _0x3bdcac =
            VisuMZ[_0x511bd6(0xfb)][_0x511bd6(0x126)][_0x511bd6(0x1ee)][_0x511bd6(0x230)];
        (this['drawText'](
            _0x3bdcac,
            _0x2d471c['x'],
            _0x2d471c['y'],
            _0x2d471c[_0x511bd6(0x1f9)],
            'left'
        ),
            this[_0x511bd6(0x1ca)]());
    }),
    (Window_Playtime[_0x5e858a(0x17f)][_0x5e858a(0x1e0)] = function (_0x4b5d23) {
        const _0x1a4556 = _0x5e858a,
            _0x464b0f = $gameSystem[_0x1a4556(0x14d)]();
        this[_0x1a4556(0x167)](
            _0x464b0f,
            _0x4b5d23['x'],
            _0x4b5d23['y'],
            _0x4b5d23[_0x1a4556(0x1f9)],
            _0x1a4556(0x1a1)
        );
    }));
function Window_MenuVariables() {
    const _0x4bcf13 = _0x5e858a;
    this[_0x4bcf13(0x21b)](...arguments);
}
((Window_MenuVariables[_0x5e858a(0x17f)] = Object[_0x5e858a(0x1f0)](
    Window_Selectable['prototype']
)),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0x176)] = Window_MenuVariables),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0x21b)] = function (_0x3b6c99) {
        const _0x3ce498 = _0x5e858a;
        (Window_Selectable['prototype'][_0x3ce498(0x21b)][_0x3ce498(0x20f)](this, _0x3b6c99),
            (this[_0x3ce498(0xc4)] =
                VisuMZ[_0x3ce498(0xfb)][_0x3ce498(0x126)][_0x3ce498(0x201)][_0x3ce498(0x1c3)]),
            this[_0x3ce498(0x19a)]());
    }),
    (Window_MenuVariables[_0x5e858a(0x17f)]['itemHeight'] = function () {
        const _0x4324f8 = _0x5e858a;
        return this[_0x4324f8(0x1e8)]();
    }),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0xdf)] = function () {
        const _0xedd6ea = _0x5e858a,
            _0x503477 = SceneManager['_scene'][_0xedd6ea(0xc0)]();
        return _0x503477 === _0xedd6ea(0x15e)
            ? 0x1
            : VisuMZ[_0xedd6ea(0xfb)][_0xedd6ea(0x126)][_0xedd6ea(0x201)][_0xedd6ea(0x1c3)][
                  _0xedd6ea(0x1b4)
              ];
    }),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0xee)] = function () {
        const _0x7e7b6e = _0x5e858a;
        (Window_Selectable[_0x7e7b6e(0x17f)][_0x7e7b6e(0xee)][_0x7e7b6e(0x20f)](this),
            (this[_0x7e7b6e(0x18e)][_0x7e7b6e(0x22e)] =
                VisuMZ[_0x7e7b6e(0xfb)]['Settings'][_0x7e7b6e(0x201)][_0x7e7b6e(0xc6)]),
            this[_0x7e7b6e(0x1d1)](ColorManager[_0x7e7b6e(0x11e)]()));
    }),
    (Window_MenuVariables[_0x5e858a(0x17f)]['maxItems'] = function () {
        const _0x284620 = _0x5e858a;
        return this['_data'][_0x284620(0x1b4)];
    }),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0xff)] = function () {
        const _0x372b16 = _0x5e858a,
            _0x25d5a2 = this[_0x372b16(0x181)]();
        for (let _0x492189 = 0x0; _0x492189 < this[_0x372b16(0x117)](); _0x492189++) {
            const _0x5f0c3f = _0x25d5a2 + _0x492189;
            _0x5f0c3f < this[_0x372b16(0x1cd)]() &&
                (this['drawItemBackground'](_0x5f0c3f), this[_0x372b16(0x1e1)](_0x5f0c3f));
        }
    }),
    (Window_MenuVariables['prototype'][_0x5e858a(0x17c)] = function (_0x48f616) {}),
    (Window_MenuVariables[_0x5e858a(0x17f)][_0x5e858a(0x1e1)] = function (_0x295fd7) {
        const _0x5704d8 = _0x5e858a,
            _0x1bd0e8 = this['_data'][_0x295fd7];
        if (_0x1bd0e8 <= 0x0) return;
        if (!$dataSystem[_0x5704d8(0x200)][_0x1bd0e8]) return;
        const _0x5aba45 = this[_0x5704d8(0x10e)](_0x295fd7);
        this[_0x5704d8(0xee)]();
        let _0x2ace85 = 0x0,
            _0x463610 = $dataSystem[_0x5704d8(0x200)][_0x1bd0e8]['trim']();
        _0x463610[_0x5704d8(0xc2)](/\\I\[(\d+)\]/i) &&
            ((_0x2ace85 = Number(RegExp['$1'])),
            (_0x463610 = _0x463610[_0x5704d8(0x1b9)](/\\I\[(\d+)\]/i, '')[_0x5704d8(0x232)]()));
        if (_0x2ace85 > 0x0) {
            const _0x4744cc =
                _0x5aba45['y'] + (this['lineHeight']() - ImageManager[_0x5704d8(0x1a2)]) / 0x2;
            this[_0x5704d8(0x183)](_0x2ace85, _0x5aba45['x'], _0x4744cc);
            const _0x7ce935 = ImageManager[_0x5704d8(0x102)] + 0x4;
            ((_0x5aba45['x'] += _0x7ce935), (_0x5aba45[_0x5704d8(0x1f9)] -= _0x7ce935));
        }
        (this['drawText'](
            _0x463610,
            _0x5aba45['x'],
            _0x5aba45['y'],
            _0x5aba45[_0x5704d8(0x1f9)],
            _0x5704d8(0x1af)
        ),
            this[_0x5704d8(0x1d1)](ColorManager[_0x5704d8(0x1a3)]()),
            this[_0x5704d8(0x167)](
                $gameVariables['value'](_0x1bd0e8),
                _0x5aba45['x'],
                _0x5aba45['y'],
                _0x5aba45[_0x5704d8(0x1f9)],
                _0x5704d8(0x1a1)
            ));
    }));
