/*:
 * @target MZ
 * @plugindesc v1.0.0 VisuStella Enhanced Patches Compatibility Fix
 * <AUTHOR> Optimizer
 * @orderAfter VisuStella_EnhancedPatches
 * @help VisuStella_CompatibilityFix.js
 *
 * ============================================================================
 * VisuStella Enhanced Patches Compatibility Fix
 * ============================================================================
 *
 * This plugin fixes compatibility issues between the enhanced patches and
 * specific versions of VisuStella plugins by providing safe method checks
 * and fallbacks.
 *
 * @param enableSafeMode
 * @text Enable Safe Mode
 * @desc Use safe method calls with existence checks
 * @type boolean
 * @default true
 */

(() => {
    'use strict';

    const parameters = PluginManager.parameters('VisuStella_CompatibilityFix');
    const enableSafeMode = parameters['enableSafeMode'] !== 'false';

    if (!enableSafeMode) return;

    console.log('[VisuStella_CompatibilityFix] Applying compatibility fixes...');

    //=============================================================================
    // Safe Method Call Helper
    //=============================================================================

    function safeMethodCall(object, methodName, ...args) {
        if (object && typeof object[methodName] === 'function') {
            try {
                return object[methodName].apply(object, args);
            } catch (error) {
                console.warn(`[CompatibilityFix] Error calling ${methodName}:`, error.message);
                return undefined;
            }
        }
        return undefined;
    }

    //=============================================================================
    // Lighting Effects Compatibility Fixes
    //=============================================================================

    // Fix for Sprite_LightBase methods that might not exist
    if (typeof Sprite_LightBase !== 'undefined') {
        const _Sprite_LightBase_update = Sprite_LightBase.prototype.update;
        Sprite_LightBase.prototype.update = function() {
            // Use global frame counter if available
            const globalCounter = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.getGlobalFrameCounter() : 
                (this._updateCounter || 0);
            
            if (!this._updateCounter) this._updateCounter = 0;
            this._updateCounter++;

            // Always call parent update safely
            if (Sprite.prototype.update) {
                Sprite.prototype.update.call(this);
            }

            // Safe method calls for lighting-specific methods
            safeMethodCall(this, 'checkProperties');

            if (!this.isEnabled || !this.isEnabled() || !this._source) {
                safeMethodCall(this, 'updateVisibility');
                return;
            }

            // Use adaptive throttling if available
            const shouldThrottle = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.shouldThrottle('lights', globalCounter) :
                (globalCounter % 2 !== 0);

            if (shouldThrottle) {
                return;
            }

            // Enhanced visibility culling with safe fallback
            if (window.VisuStellaPerformanceHelpers) {
                const visibilityKey = `light_${this._source._eventId || 'player'}_${Math.floor(this.x)}_${Math.floor(this.y)}`;
                const isVisible = window.VisuStellaPerformanceHelpers.checkVisibility(visibilityKey, () => {
                    const margin = 100;
                    return this.x > -margin && this.x < Graphics.width + margin &&
                           this.y > -margin && this.y < Graphics.height + margin;
                }, 1500);

                if (!isVisible) {
                    this.visible = false;
                    return;
                }
            }

            // Safe calls to lighting-specific update methods
            safeMethodCall(this, 'updateLightSprite');
            safeMethodCall(this, 'updateBehavior');
            
            // Fallback to original update if enhanced methods don't exist
            if (!this.updateLightSprite && !this.updateBehavior && _Sprite_LightBase_update) {
                _Sprite_LightBase_update.call(this);
            }
        };

        // Safe behavior update with existence checks
        const _Sprite_LightBase_updateBehavior = Sprite_LightBase.prototype.updateBehavior;
        Sprite_LightBase.prototype.updateBehavior = function() {
            // Check if lightData method exists
            if (!this.lightData || typeof this.lightData !== 'function') {
                // Fallback to original method if it exists
                if (_Sprite_LightBase_updateBehavior) {
                    return _Sprite_LightBase_updateBehavior.call(this);
                }
                return;
            }

            const lightData = this.lightData();
            if (!lightData) {
                if (_Sprite_LightBase_updateBehavior) {
                    return _Sprite_LightBase_updateBehavior.call(this);
                }
                return;
            }

            // Enhanced caching with safe fallback
            if (window.VisuStellaPerformanceHelpers) {
                const globalCounter = window.VisuStellaPerformanceHelpers.getGlobalFrameCounter();
                const cacheKey = `${lightData.blink || 0}_${lightData.flicker || 0}_${lightData.flash || 0}_${lightData.flare || 0}_${lightData.glow || 0}_${lightData.pulse || 0}_${Math.floor(globalCounter / 4)}`;

                const cached = window.VisuStellaPerformanceHelpers.getFromCache('lightBehavior', cacheKey);
                if (cached !== undefined) {
                    this.opacity = cached.opacity;
                    this.scale.x = cached.scaleX;
                    this.scale.y = cached.scaleY;
                    return;
                }

                // Store original values
                const originalOpacity = this.opacity;
                const originalScaleX = this.scale.x;
                const originalScaleY = this.scale.y;

                // Call original method safely
                if (_Sprite_LightBase_updateBehavior) {
                    _Sprite_LightBase_updateBehavior.call(this);
                }

                // Cache the result
                window.VisuStellaPerformanceHelpers.setInCache('lightBehavior', cacheKey, {
                    opacity: this.opacity,
                    scaleX: this.scale.x,
                    scaleY: this.scale.y
                }, 120);
            } else {
                // Fallback to original method
                if (_Sprite_LightBase_updateBehavior) {
                    _Sprite_LightBase_updateBehavior.call(this);
                }
            }
        };
    }

    //=============================================================================
    // Weather Effects Compatibility Fixes
    //=============================================================================

    if (typeof Sprite_WeatherParticle !== 'undefined') {
        const _Sprite_WeatherParticle_update = Sprite_WeatherParticle.prototype.update;
        Sprite_WeatherParticle.prototype.update = function() {
            // Use global frame counter if available
            const globalCounter = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.getGlobalFrameCounter() : 
                (this._updateCounter || 0);
            
            if (!this._updateCounter) this._updateCounter = 0;
            this._updateCounter++;

            // Always call parent update safely
            if (Sprite.prototype.update) {
                Sprite.prototype.update.call(this);
            }

            // Handle special states immediately
            if (this.type === 'none' || this._notLoadedReady) {
                return;
            }

            if (this._respawnDelay > 0) {
                this._opacityFadeInTime = 0;
                this.opacity = 0;
                return this._respawnDelay--;
            }

            // Use adaptive throttling if available
            const shouldThrottle = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.shouldThrottle('particles', globalCounter) :
                (globalCounter % 2 !== 0);

            if (shouldThrottle) {
                return;
            }

            // Enhanced visibility culling with safe fallback
            if (window.VisuStellaPerformanceHelpers) {
                const visibilityKey = `particle_${Math.floor(this.x / 100)}_${Math.floor(this.y / 100)}`;
                const isVisible = window.VisuStellaPerformanceHelpers.checkVisibility(visibilityKey, () => {
                    const margin = 100;
                    return this.x > -margin && this.x < Graphics.width + margin &&
                           this.y > -margin && this.y < Graphics.height + margin;
                }, 1000);

                if (!isVisible) {
                    this.visible = false;
                    return;
                }
            }

            // Safe calls to weather-specific update methods
            safeMethodCall(this, 'updateLifespan');
            safeMethodCall(this, 'updateFlags');
            safeMethodCall(this, 'updateScale');
            safeMethodCall(this, 'updatePosition');
            safeMethodCall(this, 'updateOpacity');
            
            // Fallback to original update if enhanced methods don't exist
            if (!this.updateLifespan && _Sprite_WeatherParticle_update) {
                _Sprite_WeatherParticle_update.call(this);
            }
        };
    }

    //=============================================================================
    // Event Move Core Compatibility Fixes
    //=============================================================================

    if (typeof Game_Event !== 'undefined') {
        const _Game_Event_update = Game_Event.prototype.update;
        Game_Event.prototype.update = function() {
            // Use global frame counter if available
            const globalCounter = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.getGlobalFrameCounter() : 
                (this._updateCounter || 0);
            
            if (!this._updateCounter) this._updateCounter = 0;
            this._updateCounter++;

            // Always update critical events
            if (this._alwaysUpdateMove || this._trigger === 2 || this._trigger === 3 || this._trigger === 4) {
                _Game_Event_update.call(this);
                return;
            }

            // Use adaptive throttling if available
            const shouldThrottle = window.VisuStellaPerformanceHelpers ? 
                window.VisuStellaPerformanceHelpers.shouldThrottle('events', globalCounter) :
                (globalCounter % 5 !== 0);

            if (shouldThrottle) {
                return;
            }

            // Enhanced visibility check with safe fallback
            if (window.VisuStellaPerformanceHelpers && this.isNearTheScreen) {
                const visibilityKey = `event_${this._mapId}_${this._eventId}_${Math.floor($gameMap._displayX)}_${Math.floor($gameMap._displayY)}`;
                const isVisible = window.VisuStellaPerformanceHelpers.checkVisibility(visibilityKey, () => this.isNearTheScreen(), 2000);
                
                if (!isVisible) {
                    // Extra throttling for off-screen events
                    if (window.VisuStellaPerformanceHelpers.shouldThrottle('events', globalCounter * 2)) {
                        return;
                    }
                }
            }

            _Game_Event_update.call(this);
        };
    }

    //=============================================================================
    // Scene Compatibility Fixes
    //=============================================================================

    // Safe scene update with existence checks
    const _Scene_Map_update = Scene_Map.prototype.update;
    Scene_Map.prototype.update = function() {
        const globalCounter = window.VisuStellaPerformanceHelpers ? 
            window.VisuStellaPerformanceHelpers.getGlobalFrameCounter() : 0;
        
        const performanceMode = window.VisuStellaPerformanceHelpers ? 
            window.VisuStellaPerformanceHelpers.getPerformanceMode() : 'normal';

        // Adaptive scene update throttling with safe fallback
        if (performanceMode === 'aggressive' && window.VisuStellaPerformanceHelpers) {
            if (window.VisuStellaPerformanceHelpers.shouldThrottle('core', globalCounter)) {
                // Still call essential updates
                if (Scene_Base.prototype.update) {
                    Scene_Base.prototype.update.call(this);
                }
                if (this.updateMainMultiply) {
                    this.updateMainMultiply();
                }
                return;
            }
        }

        _Scene_Map_update.call(this);
    };

    console.log('[VisuStella_CompatibilityFix] Compatibility fixes applied successfully');
})();
