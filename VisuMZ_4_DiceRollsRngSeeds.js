//=============================================================================
// VisuStella MZ - Dice Rolls & RNG Seeds
// VisuMZ_4_DiceRollsRngSeeds.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_DiceRollsRngSeeds = true;

var VisuMZ = VisuMZ || {};
VisuMZ.DiceRollsRngSeeds = VisuMZ.DiceRollsRngSeeds || {};
VisuMZ.DiceRollsRngSeeds.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.00] [DiceRollsRngSeeds]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Dice_Rolls_and_RNG_Seeds_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Games that use random elements in their game can benefit by using visualized
 * Dice Rolls in order to give players an understanding of what kind of logic
 * is happening behind the randomization. Various things like switches, items,
 * and skills can also affect the roll values to give the player more control
 * over the nature of RNG. However, to give the game developer more control,
 * Random Number Seeds are also provided through this plugin. These seeds allow
 * for games to function in a more controlled pattern and prevent things like
 * save scumming for better results. Though the numbers themselves are still
 * random, they will exhibit predetermined and predictable outcomes through the
 * usage of Plugin Commands.
 *
 * Features include all (but not limited to) the following:
 *
 * * Visualized Dice Rolls can be called through Plugin Commands, using 4-sided
 *   dice, 6-sided dice, 8-sided, 10-sided, 12-sided, and 20-sided dice.
 * * Dice Rolls can be modified through dice effects, either automatic or bonus
 *   effets using player input.
 * * Dice effects include changing the number of Dice Rolled, the sides each
 *   dice has, and/or modifiers that affect the final roll.
 * * Random Number Seeds allow for predetermined randomized outcomes when
 *   rolling for random numbers.
 * * Custom Random Number Seeds can be numbers or text, allowing you to make
 *   truly unique cases for various random number batches.
 * * Dice Rolls can also be controlled through Random Number Seeds.
 * * Reset the custom seed state and begin the seed anew.
 * * Notetags can allow skills and items to also utilize Random Number Seeds to
 *   give RNG-generated results.
 * * "Daily" effect can affect and provide specialized Random Number Seeds for
 *   just that day.
 * * "Unique" effect will affect and provide specialized Random Number Seed for
 *   that save game and any other branching saves made after.
 * * Control these seeded random numbers through Plugin Commands or JavaScript
 *   script calls.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Dice Rolls Visualized
 * ============================================================================
 *
 * This section explains how visualized Dice Rolls work. These visualized Dice
 * Rolls give a player a visual understanding of how the randomized numbers
 * play out before them.
 *
 * ---
 *
 * === Dice Count, Advantage, Disadvantage ===
 *
 * Depending on the type of Dice Roll you're making, dice count can mean
 * different things. For the Plugin Command "Dice: Roll Value", the dice count
 * refers to just the total number of Dice Rolled, and from there, the highest,
 * lowest, average, or total amount calculated from the dice will be recorded.
 *
 * However, when using the Plugin Command "Dice: Target Value", the mechanics
 * known as "Advantage" and "Disadvantage" are used.
 *
 * When in an "Advantage" state, the player throws multiple dice (anything
 * above 1 dice). From there, the highest value is selected if going for
 * "above/equal" target value. The lowest value is selected if the Plugin
 * Command has the player aiming for below/equal target value.
 *
 * In a "Disadvantage" state, just like with the "Advantage" state, the player
 * throws multiple dice. However, the opposite happens. When going for
 * above/equal target value, instead of selecting the highest value, the lowest
 * dice value is selected instead. When going for "below/equal" target value,
 * the highest value is selected rather than the lowest.
 *
 * ---
 *
 * === Dice Sides ===
 *
 * The number of sides a dice has will be either 4, 6, 8, 10, 12, or 20 for the
 * purpose of this plugin. While we can create more options, these are the most
 * popular polyhedral dice that you will see, stemming from a variety of board
 * games and tabletop RPG's. These dice will also be known as D4, D6, D8, D10,
 * D12, and D20 each referring to the number of sides they have.
 *
 * The number of sides a dice has will determine its rank also. For the purpose
 * of this plugin, there are six ranks to match the six types of dice we have
 * available.
 *
 *   Rank 1 - D4
 *   Rank 2 - D6
 *   Rank 3 - D8
 *   Rank 4 - D10
 *   Rank 5 - D12
 *   Rank 6 - D20
 *
 * Ranks are used in Dice Roll automatic effects and bonus effects. By raising
 * a dice's rank, a D6 can transform into a D8 for example. Lowering a dice's
 * rank can transform said D6 into a D4. For this plugin, ranks cannot go below
 * Rank 1 (D4) and cannot go above Rank 6 (D20).
 *
 * ---
 *
 * === Dice Modifiers ===
 *
 * When rolls are decided, modifiers are then applied to the Dice Roll number.
 * For example, if a player has "Modifier +3" and rolls a 10, the number adds
 * the +3 modifier and becomes a 13. The 13 is then used to record the numeric
 * value of the roll and/or determine if the roll is successful or not. The
 * modified roll number can also exceed the number of sides on the dice.
 *
 * Negative modifiers can also exist and send the roll value going the other
 * way. For example, if a player has "Modifier -2" and rolls a 10, the number
 * applies the -2 modifier and becomes 8 instead. The 8 is then used to record
 * the numeric value of the roll and/or determine if the roll is successful or
 * not. The modified roll number can also go below 1 and reach 0 or negative
 * numbers, too.
 *
 * Modifiers can also have a random element. This random element is calculated
 * when the modifier is being applied and will utilize the Dice Roll's Random
 * Number Seed if specified. As such, the display range for the modifier will
 * show the modifier minimum with the potential maximum modifier.
 *
 * ---
 *
 * === Natural Rolls ===
 *
 * Regarding dice modifiers, there is a slight rule tweak if you are using the
 * Plugin Command "Dice: Target Value" to roll dice. If the command option of
 * "Allow Natural Rolls?" parameter is enabled and set to "true", then there
 * are two special numbers that, when rolled, will ignore modifiers and will
 * count as either an automatic success or an automatic failure. These numbers
 * are 1 and whatever number is the maximum number of sides on the dice.
 *
 * For example, on a D20, a Natural 1 will result in instant failure while a
 * Natural 20 will result in instant success regardless of the modifiers or
 * whatever target value needs to be reached. If a target value needed to be
 * reached happens to be 25, and the player rolls a Natural 20, it will still
 * count as a success provided that the player is going for an "above/equal"
 * target value.
 *
 * The opposite is true when going for a "below/equal" target value. Rolling a
 * Natural 1 will count as an automatic success while rolling the max value of
 * the dice will count as an automatic failure regardless of the target value.
 *
 * Natural rolls will still be recorded normally to variables and switches.
 * There's no indicators suggesting that they are "critical successes" or
 * "critical failures". Instead, just as "success" and "failure". If you wish
 * to portray the "critical" nature of the roll, best use the variable to
 * determine if anything 1 and below, or anything equal or higher than max to
 * be considered "criticals" of either extreme.
 *
 * ---
 *
 * === Automatic Effects ===
 *
 * Automatic Effects are dice effects that are automatically applied at the
 * start of a Dice Roll, from before the Dice Roll appears on screen. These
 * effects can modify dice counts, alter advantage and disadvantage, raise or
 * lower dice ranks, and adjust Dice Roll modifiers. The effects range from
 * general effects that always occur or based off certain JavaScript conditions
 * to other effects like if a certain actor has a skill available to use.
 *
 * If, for whatever reason, an Automatic Effect's conditions were to suddenly
 * be enabled during the middle of the Dice Roll, it would not suddenly become
 * active and grant the dice effects to the roll. Likewise, if an Automatic
 * Effect's conditions were to suddenly be unmet during the middle of a Dice
 * Roll, the dice effects would not be revoked either. The Automatic Effects to
 * be used will always be determined at the start, before the Dice Roll even
 * appears on screen.
 *
 * ---
 *
 * === Bonus Effects ===
 *
 * Unlike Automatic Effects, Bonus Effects are applied by the player manually.
 * They can alter dice counts, advantage, disadvantage, ranks, and modifiers
 * just like Automatic Effects can, but the player has the option of doing so.
 * Some effects have a limited number of times they can be used while others.
 * Other effects will not only require an actor to know a specific skill, but
 * also to be able to pay for them.
 *
 * These Bonus Effects, once selected and activated, will grant their dice
 * effects for the rest of the Dice Roll. Even if the conditions would be unmet
 * later, the dice effects will remain until the Dice Roll finishes. However,
 * the Bonus Effects that are available to be selected from the start will be
 * the only ones available for selection at all during the Dice Roll. This
 * means that even if a show condition (like switches or JS: Show) is met
 * halfway through, it will not appear in the list unless it was there from the
 * start. The same goes for variable, item, and skill conditions. This is to
 * match the same enabling/visibility settings that Automatic Effects have.
 *
 * ---
 *
 * ============================================================================
 * Random Number Seeds
 * ============================================================================
 *
 * This section explains how Random Number Seeds work.
 *
 * ---
 *
 * === Random Number Seeds ===
 *
 * Seeds will initialize a random number generator. Retrieving random numbers
 * from this same seed will give the same sequence of random numbers when
 * restarted or continued. If a different seed is used, then different number
 * sequences will occur. It's useful for replication experiments or simulations
 * while preventing things like save scumming for different randomized results.
 *
 * Let's take a look at how this works. If we use the Plugin Command to
 * generate a random number between 0 and 100 using the seed "RpgTsukuru", then
 * the numbers will appear in this order:
 *
 *   11   82   25   2   16   44   69   41   87   97   43   99   2   21   15
 *
 * And so on. This will occur whenever you start a new game or reset the
 * "RpgTsukuru" seed in that exact order when looking for numbers between 0 and
 * 100. This is because the algorithm used to solve for the Random Number Seed
 * will always yield those results adding a level of predictability.
 *
 * *NOTE* Keep in mind that when you are using the script call, the value you
 * will get will be a float (a decimal number between 0 and 1 like 0.123456789)
 * and not a clean and clear integar like with the Plugin Commands. This is to
 * replicate the "Math.random()" JavaScript function so all you have to do is
 * simply replace "Math.random()" with "$rngSeed('RpgTsukuru')" to get the kind
 * of randomized results you would with Math.random() except through a seed.
 *
 * Now, the reason why we would have multiple seed keys is so that you can have
 * repeatable results for things that are totally different from one another.
 * For instance, "SafeCombination" will have a different door locked than
 * "HideAndSeek" for different simulations. This allows you to do them in any
 * order and have the safe combinations and hide and seek locations be the same
 * per the simulation you are doing things in.
 *
 * ---
 *
 * === Daily Marker ===
 *
 * For those who want certain puzzles to vary from day to day, but will always
 * have the same starting pattern upon a reset, then apply the Daily Marker to
 * the Random Number Seed either in the Plugin Command or script call. This
 * will make it yield different results than from the non-version, so do NOT
 * expect it to give the same number results.
 *
 * The daily marker will change at 12:00am on the dot based on the player's
 * PC's current time zone and time/date settings.
 *
 * ---
 *
 * === Save-Unique Marker ===
 *
 * This plugin will drop a unique marker for each playthrough and that marker
 * is the "Save-Unique" marker. It can be applied to a Random Number Seed to
 * make it different from the pure seed and/or daily marker-applied seed. This
 * way, the "RpgTsukuru" seed won't yield the same numbers as it did for a past
 * playthrough.
 *
 * Keep in mind that since this is per save/playthrough, that means any
 * branching saves will retain the same marker and provide the same results
 * through seeded random numbers.
 *
 * *NOTE* If this plugin is installed after multiple saves have been made, then
 * each of those saves will have their own "Save-Unique" marker regardless of
 * their origins, shared or not. This is NOT a bug and it cannot be changed.
 *
 * ---
 *
 * === Potential Uses for Random Number Seeds ===
 *
 * Here are some potential uses for Random Number Seeds:
 *
 * * Randomized puzzles: Puzzles can have different randomized starting points,
 * and by seeding them, it prevents the player from abusing the reset
 * function to get a favorable starting point.
 *
 * * Randomized cutscenes: Sometimes, you want certain event cutscenes to
 * trigger randomly. This allows for that to happen without the player save
 * scumming to get the desired cutscene. Instead, the player will have to
 * make do with whatever cutscene they found.
 *
 * * Randomized gambling: Does your game have a casino? Do you want to make
 * sure your player has to live with the conequences of the risks taken? By
 * using a Random Number Seed, reloading a save is no longer possible to let
 * them escape their fate from chance.
 *
 * * Randomized choices: If you're using the VisuStella MZ Message Core and
 * using the <Shuffle> text code for choices, you can use this plugin's text
 * codes to add in Random Number Seeds to allow for randomized choices, too.
 * By using the Random Number Seed, reloading a save won't let the player get
 * new choices with the shuffled choices.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_1_BattleCore
 *
 * Those using VisuStella MZ's Battle Core can launch visualized Dice Roll
 * Plugin Commands from this plugin during the middle of Action Sequences as
 * long as there is not a conflicting effect during it.
 *
 * Conflict effects include Active Chain Skills, Input Combo Skills, or
 * Evolution Matrix Skills. Dice-related Plugin Commands will not run at all
 * while these skill mechanics are active.
 *
 * ---
 *
 * VisuMZ_1_ItemsEquipsCore
 *
 * During Dice Rolls, you can have certain Automatic Effects or Bonus Effects
 * require the presence or usage of items, weapons, and/or armors. If those
 * items have a <Color: x> notetag inside them, the VisuStella MZ Items &
 * Equips Core will allow the color effect to go through. However, if the color
 * notetag is <Color: #rrggbb>, extra help will be required from the VisuStella
 * MZ Message Core for it to work.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Action-Related Notetags ===
 *
 * ---
 *
 * <RNG Seed: x>
 * <Daily Seed: x>
 * <Unique Seed: x>
 * <Daily Unique Seed: x>
 *
 * - Used for: Skill, Item Notetags
 * - Causes the skill or item to utilize Random Number Seeds to yield
 *   predictable outcomes for damage, hit rate, critical rate, etc.
 * - Replace 'x' with the "seed" you would like to use.
 *   - Replace 'x' with "auto" to automatically name the seed based on the
 *     skill/item's name.
 *   - Seed will be furthered modified by the plugin based on the user, the
 *     target, and the action used as to prevent RNG manipulation.
 * - "RNG Seed" variant is a generalized seed.
 * - "Daily Seed" variant will have results vary based on the day.
 * - "Unique Seed" variant will have results vary based on save marker.
 * - "Daily Unique Seed" variant will have results vary based on the day and
 *     and save marker.
 * - Only applies to the happenings of inside Game_Action.prototype.apply and
 *   Action Sequence - MECH: Action Effect.
 *   - For all other things, utilize the Plugin Commands for this plugin to
 *     grab RNG-generated values within variables and use that or use the
 *     script calls to access RNG-generated results.
 * - If this notetag is not used, default to the settings found in the Plugin
 *   Parameters for the default action Random Number Generator seed.
 *
 *   Example:
 *
 *     <Unique Seed: RpgTsukuru>
 *
 * ---
 *
 * <No RNG Seed>
 *
 * - Used for: Skill, Item Notetags
 * - Used to suppress the Plugin Parameter's default action Random Number
 *   Generator seed and to prevent this skill/item from using RNG seeds.
 *
 * ---
 *
 * ============================================================================
 * Available Text Codes
 * ============================================================================
 *
 * The following are text codes that you may use with this plugin.
 *
 * === Type-Related Text Codes ===
 *
 * ---
 *
 * ----------------------   ---------------------------------------------------
 * Text Code                Effect (Show Choice Text Only)
 * ----------------------   ---------------------------------------------------
 *
 * <RNG Seed: x>            Allows <Shuffle> text code to use seed 'x'.
 *                          Requires VisuMZ_1_MessageCore!
 *
 * <Daily Seed: x>          Allows <Shuffle> text code to use daily seed 'x'.
 *                          Requires VisuMZ_1_MessageCore!
 *
 * <Unique Seed: x>         Allows <Shuffle> text code to use save unique
 *                          seed 'x'. Requires VisuMZ_1_MessageCore!
 *
 * <Daily Unique Seed: x>   Allows <Shuffle> text code to use daily and save
 *                          unique seed 'x'. Requires VisuMZ_1_MessageCore!
 *
 * Example:
 *
 *   Show Choices:
 *
 *     Choice A <Shuffle: 3>
 *     Choice B <Unique Seed: RpgTsukuru>
 *     Choice C
 *     Choice D
 *     Choice E
 *     Choice F
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Dice Plugin Commands ===
 *
 * ---
 *
 * Dice: Roll Value
 * - Rolls a dice for any value and stores the result to a variable.
 *
 *   Dice Sides:
 *   - How many sides does this dice have?
 *
 *     Total Dice Rolls:
 *     - How many Dice Rolls do you want out of this?
 *     - You may use code.
 *     - Dice Rolls have a cap.
 *
 *   Variable ID:
 *   - Insert the ID of the Variable to save the Dice Roll results to.
 *   - Use 0 to not use.
 *   - Results are after modifiers.
 *
 *     Result Type:
 *     - What type of result do you want recorded to the variable?
 *
 *   Title Text:
 *   - What is the title of this Dice Roll?
 *   - Text codes allowed.
 *   - Leave empty to not use.
 *
 *   Appearance & Seed:
 *   - What are the dice appearance & seed settings used for this Dice Roll?
 *
 *   Auto Dice Effects:
 *   - Adjust the modifier settings that automatically apply to the Dice Roll.
 *
 *   Bonus Dice Effects:
 *   - Adjust the modifier settings are optionally applied to the Dice Roll
 *    selected by the player.
 *
 * ---
 *
 * Dice: Target Value
 * - Rolls a dice for a target value and stores the result to a variable.
 *
 *   Dice Sides:
 *   - How many sides does this dice have?
 *
 *   Switch ID:
 *   - Insert the ID of the Switch to save the Dice Roll results to.
 *   - Use 0 to not use.
 *
 *     Allow Natural Rolls?:
 *     - Allow natural rolls which bypass modifiers?
 *     - Natural 1 or Max = auto-fail or auto-success
 *
 *     Result Type:
 *     - What type of result do you want recorded to the switch?
 *
 *     Target Roll Value:
 *     - What should the target value be equal to?
 *     - You may use code.
 *
 *     Variable ID:
 *     - Insert the ID of the Variable to save the Dice Roll value to.
 *     - Use 0 to not use.
 *     - Results are after modifiers.
 *
 *   Title Text:
 *   - What is the title of this Dice Roll?
 *   - Text codes allowed. Leave empty to not use.
 *
 *   Appearance & Seed:
 *   - What are the dice appearance & seed settings used for this Dice Roll?
 *
 *   Auto Dice Effects:
 *   - Adjust the modifier settings that automatically apply to the Dice Roll.
 *
 *   Bonus Dice Effects:
 *   - Adjust the modifier settings are optionally applied to the Dice Roll
 *    selected by the player.
 *
 * ---
 *
 * Dice Appearance & Seed Settings
 *
 *   Appearance Overrides:
 *
 *     D4 Image Filename(s):
 *     D6 Image Filename(s):
 *     D8 Image Filename(s):
 *     D10 Image Filename(s):
 *     D12 Image Filename(s):
 *     D20 Image Filename(s):
 *     - Use custom images for this dice?
 *     - Priority over colors.
 *     - Overrides default parameters.
 *     - Location: img/pictures/
 *
 *     Dice Colors(s):
 *     - Use #rrggbb for custom colors or regular numbers for text colors from
 *       the Window Skin.
 *     - Overrides default parameters.
 *
 *   Seed:
 *   - What is the Random Number Seed used for this Dice Roll?
 *   - Use numbers or text.
 *   - Use "none" to not use a seed.
 *
 *     Daily Marker:
 *     - Apply daily marker to Random Number results?
 *
 *     Save-Unique Marker:
 *     - Apply save-unique marker to Random Number results?
 *
 * ---
 *
 * Auto Dice Effects
 *
 *   General Auto-Effects:
 *   - These auto-effects will be automatically activated as long its
 *     conditions are met at the start of the dice-roll.
 *
 *   Variable Requirements:
 *   - These auto-effects require a variable(s) to be at least a certain value
 *     to automatically activate.
 *
 *   Item Requirements:
 *   - These auto-effects require an item(s) to have a certain quantity to
 *     automatically activate.
 *
 *   Weapon Requirements:
 *   - These auto-effects require a weapon(s) to be in party possession or is
 *     equipped to automatically activate.
 *
 *   Armor Requirements:
 *   - These auto-effects require an armor(s) to be in party possession or is
 *     equipped to automatically activate.
 *
 *   Skill Requirements:
 *   - These auto-effects require a skill(s) to be available within the party
 *     to automatically activate.
 *
 * ---
 *
 * Auto-Effects
 * - This section is a consolidation of the various Auto Dice Effects.
 *
 *   Auto Effect Name:
 *   - What is the name of this effect?
 *   - Text codes allowed.
 *
 *     Auto Effect Icon:
 *     - The icon used for this effect.
 *
 *   Reference Data:
 *
 *     Required Variable ID:
 *     - This variable is to be checked from.
 *     - Changes don't affect effect mid-roll.
 *
 *       Required Value:
 *       - Variable requires at least this much in value.
 *       - You may use code.
 *       - Changes don't affect effect mid-roll.
 *
 *     Required Item ID:
 *     - This is the item whose quantity is checked.
 *     - Changes don't affect effect mid-roll.
 *
 *       Required Value:
 *       - Quantity requires at least this much in value.
 *       - You may use code.
 *       - Changes don't affect effect mid-roll.
 *
 *     Required Weapon ID:
 *     - This is the weapon whose presence is checked.
 *     - Changes don't affect effect mid-roll.
 *
 *       Include Equipped?:
 *       - Allow equipped weapons to be included in the count.
 *
 *     Required Armor ID:
 *     - This is the armor whose presence is checked.
 *     - Changes don't affect effect mid-roll.
 *
 *       Include Equipped?:
 *       - Allow equipped armors to be included in the count.
 *
 *     Required Skill ID:
 *     - This is the skill whose presence is checked.
 *     - Changes don't affect effect mid-roll.
 *
 *       Skill Learned Only?:
 *       - Require the skill to be learned only or allow temporary skills?
 *
 *       Skill User(s):
 *       - Select which actor(s) to check for the required skill.
 *         - Party Leader
 *         - Any Party Member
 *         - Every Party Member
 *         - Any Battle Member
 *         - Every Battle Member
 *         - Any Specific Actor(s)
 *         - Every Specific Actor(s)
 *
 *       Specific Actor ID(s):
 *       - Determine which "Specific Actor(s)" to pick from.
 *       - Specific actor(s) must be in the party, main or reserve.
 *
 *       Show User Name?:
 *       - Shows the skill user's name next to the skill name?
 *
 *       Require User Alive?:
 *       - Requires the skill user to be alive?
 *       - Or can they be dead or alive?
 *
 *   Dice Effects:
 *
 *     Count/Advantage:
 *     - Change dice count (roll value) or advantage/disadvantage
 *       (target value).
 *     - You may use code.
 *
 *     Rank/Type:
 *     - Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 *     - You may use code.
 *     - Higher rank = more sides.
 *
 *     Modifier (Static):
 *     - Alters the finalized rolled dice value.
 *     - You may use code.
 *
 *     Modifier (Random):
 *     - Adds a random element to the dice value.
 *     - You may use code.
 *
 *   Additional Text:
 *
 *     Pre-Effect Text:
 *     - What text is added before the dice effects text?
 *     - Text codes allowed.
 *
 *     Post-Effect Text:
 *     - What text is added after the dice effects text?
 *     - Text codes allowed.
 *
 *   Effect Conditions:
 *
 *     Switch ID:
 *     - This Switch is required to be ON to meet conditions.
 *     - Use 0 to not use.
 *     - Does not reveal mid-Dice Roll.
 *
 *     JS: Condition:
 *     - JavaScript code used to determine the conditions for this dice effect
 *       to be automatically activated.
 *     - Does not reveal mid-Dice Roll.
 *
 * ---
 *
 * Bonus Dice Effects
 *
 *   General Bonus Effects:
 *   - Add general bonus dice effects here that the player can select to use
 *     the effect.
 *
 *   Variable Cost Effects:
 *   - Variable Bonus Effects require deducting from variable(s) as a cost to
 *     use the effect.
 *
 *   Item Cost Effects:
 *   - Item Bonus Effects require consuming the target item(s) as a cost to use
 *     the effect.
 *
 *   Weapon Cost Effects:
 *   - Weapon Bonus Effects require consuming the target weapon(s) as a cost to
 *     use the effect.
 *
 *   Armor Cost Effects:
 *   - Armor Bonus Effects require consuming target the armor(s) as a cost to
 *     use the effect.
 *
 *   Skill Cost Effects:
 *   - Skill Bonus Effects require paying skill costs to activate and use the
 *     effect.
 *
 * ---
 *
 * Bonus Effects
 * - This section is a consolidation of the various Bonus Dice Effects.
 *
 *   Bonus Effect Name:
 *   - What is the name of this effect?
 *   - Text codes allowed.
 *
 *     Bonus Effect Icon:
 *     - The icon used for this effect.
 *
 *     Animation ID:
 *     - Play this animation when the effect activates.
 *     - Animation will play on the player character.
 *
 *   Effect Costs:
 *
 *     Variable Cost ID:
 *     - This variable is to be deducted from.
 *     - Changes do not reveal effect mid-Dice Roll.
 *
 *       Cost Value:
 *       - Deduct this much from the target variable.
 *       - You may use code.
 *
 *       Show Variable Cost?:
 *       - Show variable cost?
 *
 *     Item Cost ID:
 *     - This item is to be deducted from.
 *     - Changes do not reveal effect mid-Dice Roll.
 *
 *       Cost Value:
 *       - Deduct this much from the target item.
 *       - You may use code.
 *
 *       Show Item Cost?:
 *       - Show item cost?
 *
 *     Weapon Cost ID:
 *     - This weapon is to be deducted from.
 *     - Changes do not reveal effect mid-Dice Roll.
 *
 *       Cost Value:
 *       - Deduct this much from the target weapon.
 *       - You may use code.
 *
 *       Show Weapon Cost?:
 *       - Show weapon cost?
 *
 *     Armor Cost ID:
 *     - This armor is to be deducted from.
 *     - Changes do not reveal effect mid-Dice Roll.
 *
 *       Cost Value:
 *       - Deduct this much from the target armor.
 *       - You may use code.
 *
 *       Show Armor Cost?:
 *       - Show armor cost?
 *
 *     Skill Cost ID:
 *     - This skill's cost is to be paid by an actor.
 *     - Changes do not reveal effect mid-Dice Roll.
 *
 *       Skill Learned Only?:
 *       - Require the skill to be learned only or allow temporary skills?
 *
 *       Skill User(s):
 *       - Select which actor(s) to check for the required skill.
 *         - Party Leader
 *         - Any Party Member
 *         - Every Party Member
 *         - Any Battle Member
 *         - Every Battle Member
 *         - Any Specific Actor(s)
 *         - Every Specific Actor(s)
 *
 *       Specific Actor ID(s):
 *       - Determine which "Specific Actor(s)" to pick from.
 *       - Specific actor(s) must be in the party, main or reserve.
 *
 *       Show User Name?:
 *       - Shows the skill user's name next to the skill name?
 *
 *       Require User Alive?:
 *       - Requires the skill user to be alive? Or can they be dead or alive?
 *
 *       Show Skill Cost?:
 *       - Show skill cost?
 *
 *     Maximum Uses:
 *     - How many times can this effect be used?
 *     - You may use code.
 *     - Over 1000000 for unlimited.
 *
 *       Show Uses Left?:
 *       - Show how many uses are left?
 *
 *   Dice Effects:
 *
 *     Count/Advantage:
 *     - Change dice count (roll value) or advantage/disadvantage
 *       (target value).
 *     - You may use code.
 *
 *     Rank/Type:
 *     - Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 *     - You may use code.
 *     - Higher rank = more sides.
 *
 *     Modifier (Static):
 *     - Alters the finalized rolled dice value.
 *     - You may use code.
 *
 *     Modifier (Random):
 *     - Adds a random element to the dice value.
 *     - You may use code.
 *
 *   Additional Text:
 *
 *     Pre-Cost Text:
 *     - What text is added before the dice cost text?
 *     - Text codes allowed.
 *
 *     Post-Cost Text:
 *     - What text is added after the dice cost text?
 *     - Text codes allowed.
 *
 *     Pre-Effect Text:
 *     - What text is added before the dice effects text?
 *     - Text codes allowed.
 *
 *     Post-Effect Text:
 *     - What text is added after the dice effects text?
 *     - Text codes allowed.
 *
 *   Effect Conditions:
 *
 *     Show Switch ID:
 *     - This Switch is required to be ON to show effect.
 *     - Use 0 to not use.
 *     - Does not reveal mid-Dice Roll.
 *
 *     Enable Switch ID:
 *     - This Switch is required to be ON to enable effect.
 *     - Use 0 to not use.
 *
 *     JS: Show:
 *     - JavaScript code used to determine show conditions for this dice
 *       effect to become available for use.
 *     - Does not reveal mid-Dice Roll.
 *
 *     JS: Enable:
 *     - JavaScript code used to determine enable conditions for this dice
 *       effect to become usable.
 *
 *     JS: On Select:
 *     - JavaScript code used to determine what happens when this dice effect
 *       is selected and activated.
 *
 * ---
 *
 * === RNG Plugin Commands ===
 *
 * ---
 *
 * RNG: Random Number Between X and Y
 * - Uses a seed to determine a random number between X and Y.
 *
 *   Variable ID:
 *   - Insert the ID of the Variable to save this value to.
 *
 *   Minimum:
 *   - Minimum value the random number can be.
 *   - You may use JavaScript code.
 *
 *   Maximum:
 *   - Maximum value the random number can be.
 *   - You may use JavaScript code.
 *
 *   Seed:
 *   - What is the Random Number Seed?
 *   - Use numbers or text.
 *
 *   Daily Marker:
 *   - Apply daily marker to Random Number results?
 *
 *   Save-Unique Marker:
 *   - Apply save-unique marker to Random Number results?
 *
 * ---
 *
 * RNG: Reset Random Seed
 * - Resets the random state for the target Random Number Seed.
 *
 *   Seed:
 *   - What is the Random Number Seed?
 *   - Use numbers or text.
 *
 *   Daily Marker:
 *   - Apply daily marker to Random Number Seed?
 *
 *   Save-Unique Marker:
 *   - Apply save-unique marker to Random Number Seed?
 *
 * ---
 *
 * ============================================================================
 * Script Calls
 * ============================================================================
 *
 * The following are Script Calls that can be used with this plugin. These are
 * made for JavaScript proficient users. We are not responsible if you use them
 * incorrectly or for unintended usage.
 *
 * ---
 *
 * === Dice Roll-Related Script Calls ===
 *
 * ---
 *
 * $addDiceCount(value)
 *
 * - Adds 'value' to the current Dice Count.
 * - Use with "Dice: Roll Value". Adds 'value' to current dice count.
 * - Best used with the "JS: On Select" parameter found within the bonus
 *   effects you can add to a Dice Roll Plugin Command.
 *
 * ---
 *
 * $addDiceAdvantage(value)
 * $addDiceDisadvantage(value)
 *
 * - Adds 'value' to the current Dice Advantage/Disadvantage.
 * - Use with "Dice: Target Value". Adds 'value' to dice advantage or
 *   disadvantage. Advantages and disadvantages offset each other.
 * - Best used with the "JS: On Select" parameter found within the bonus
 *   effects you can add to a Dice Roll Plugin Command.
 *
 * ---
 *
 * $addDiceRank(value)
 *
 * - Adds 'value' to the current Dice Rank.
 * - Ranks range from 1 to 6.
 *   - Rank 1 - D4
 *   - Rank 2 - D6
 *   - Rank 3 - D8
 *   - Rank 4 - D10
 *   - Rank 5 - D12
 *   - Rank 6 - D20
 * - Best used with the "JS: On Select" parameter found within the bonus
 *   effects you can add to a Dice Roll Plugin Command.
 *
 * ---
 *
 * $addDiceModifier(value)
 * $addDiceModRand(value)
 *
 * - Adds 'value' to the current Dice Modifier or the potential Dice Modifier
 *   Random Range.
 * - Best used with the "JS: On Select" parameter found within the bonus
 *   effects you can add to a Dice Roll Plugin Command.
 *
 * ---
 *
 * === Random Seed-Related Script Calls ===
 *
 * ---
 *
 * $rngSeed(seed)
 * $dailyRngSeed(seed)
 * $uniqueRngSeed(seed)
 * $dailyUniqueRngSeed(seed)
 *
 * - Returns a float (a decimal number between 0 and 1) randomized from 'seed'.
 * - Replace 'seed' with a number or string representing the seed you wish to
 *   use to retrieve random numbers from.
 * - This will bump up the seed state to the next random number.
 * - Use the 'daily' variant for a customized seed based on the current date.
 * - Use the 'unique' variant for a customized seed based on the current save
 *   that will differ from other saves.
 *
 * - Examples:
 *
 *   - $rngSeed(12345)
 *   - $rngSeed("Don Miguel")
 *
 *   - $dailyRngSeed(67890)
 *   - $dailyRngSeed("Yoji Ojima")
 *
 *   - $uniqueRngSeed(246810)
 *   - $uniqueRngSeed("VisuStella")
 *
 * ---
 *
 * $resetRngSeed(seed)
 * $resetDailyRngSeed(seed)
 * $resetUniqueRngSeed(seed)
 * $resetDailyUniqueRngSeed(seed)
 *
 * - Resets the seed state for random number generator 'seed' set.
 * - Replace 'seed' with a number or string representing the seed you wish to
 *   reset the seed state for.
 * - Use the 'daily' variant to reset the customized seed based on the current
 *   date.
 * - Use the 'unique' variant to reset the customized seed based on the current
 *   save that differs from other saves.
 *
 * - Examples:
 *
 *   - $resetRngSeed(12345)
 *   - $resetRngSeed("Don Miguel")
 *
 *   - $resetDailyRngSeed(67890)
 *   - $resetDailyRngSeed("Yoji Ojima")
 *
 *   - $resetUniqueRngSeed(246810)
 *   - $resetUniqueRngSeed("VisuStella")
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Dice Roll Settings
 * ============================================================================
 *
 * Adjust the Dice Roll settings to fit your game. These range from mechanics
 * to how the many-sided dice appear in-game and how the rolling animation will
 * play out.
 *
 * If you do not use images for the various sided-dice, do not worry. This
 * plugin will use pre-rendered graphics to generate them for usage.
 *
 * ---
 *
 * Mechanics:
 *
 *   Max Dice Count:
 *   - What is the maximum number of dice that can be thrown at a time?
 *
 * ---
 *
 * Dice Appearances:
 *
 *   D4 Appearance:
 *   D6 Appearance:
 *   D8 Appearance:
 *   D10 Appearance:
 *   D12 Appearance:
 *   D20 Appearance:
 *   - Adjust the Dice Appearance settings for the dice here.
 *
 *     Image Filename(s):
 *     - Use custom images for this dice?
 *     - Priority over colors.
 *     - Location: img/pictures/
 *
 *     Dice Colors(s):
 *     - Use #rrggbb for custom colors or regular numbers for text colors from
 *       the Window Skin.
 *
 *     Number Settings:
 *
 *       Font Size:
 *       - Font size used for dice number.
 *
 *       Number Outline:
 *       - What width to use for number outline?
 *       - Use 0 to not use an outline.
 *
 *       Offset X:
 *       - Offsets the number x position.
 *       - Negative: left. Positive: right.
 *
 *       Offset Y:
 *       - Offsets the number y position.
 *       - Negative: up. Positive: down.
 *
 *     Position Settings:
 *
 *       Offset X:
 *       - Offsets the sprite x position.
 *       - Negative: left. Positive: right.
 *
 *       Offset Y:
 *       - Offsets the sprite y position.
 *       - Negative: up. Positive: down.
 *
 * ---
 *
 * Color Ratios
 *
 *   Border Ratio:
 *   - Used for generated colors.
 *   - Darkness ratio for border color.
 *
 *   Color 1 Ratio:
 *   - Used for generated colors.
 *   - Darkness ratio for darker color.
 *
 *   Color 2 Ratio:
 *   - Used for generated colors.
 *   - Darkness ratio for middle color.
 *
 *   Color 3 Ratio:
 *   - Used for generated colors.
 *   - Darkness ratio for main color.
 *
 * ---
 *
 * Arrange Offset:
 *
 *   Offset X:
 *   - Offsets the dice arrangement x position.
 *   - Negative: left. Positive: right.
 *
 *   Offset Y:
 *   - Offsets the dice arrangement y position.
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * Fade Settings:
 *
 *   Fade In Duration:
 *   - How many frames it takes to fade in dice?
 *   - 60 frames = 1 second.
 *
 *   Fade Out Duration:
 *   - How many frames it takes to fade out dice?
 *   - 60 frames = 1 second.
 *
 *   Finalize Delay:
 *   - How many frames to wait before fading out?
 *   - 60 frames = 1 second.
 *
 * ---
 *
 * Rolling Effect:
 *
 *   Dice Roll Duration:
 *   - How many frames it takes to roll a dice?
 *   - 60 frames = 1 second.
 *
 *   Between Roll Delay:
 *   - How many frames between multiple dice rolls?
 *   - 60 frames = 1 second.
 *
 *   Dice Roll Height:
 *   - How high should the dice jump up for its roll in pixels?
 *
 *   Dice Rotate Speed:
 *   - How many degrees does the dice rotate per frame while rolling?
 *
 * ---
 *
 * Movement Settings:
 *
 *   Move Duration:
 *   - How many frames it takes to move dice?
 *   - 60 frames = 1 second.
 *
 *   Scaling Duration:
 *   - How many frames it takes to change dice scale?
 *   - 60 frames = 1 second.
 *
 *   Number Climb Tick:
 *   - How many frames are there between number ticks?
 *   - 60 frames = 1 second.
 *
 *   Max Number Duration:
 *   - Max number of frames to process number ticks?
 *   - 60 frames = 1 second.
 *
 *   Number Color Shift:
 *   - Allow dice number colors to change due to modifiers?
 *
 *   Pre-Modifier Delay:
 *   - Delay frames before applying modifiers?
 *   -60 frames = 1 second.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Random Seed Settings
 * ============================================================================
 *
 * Random Number Seeds are generated using the linear congruential generator
 * (LCG) algorithm to yield a sequence of pseudo-randomized numbers calculated
 * with a nearly predictable but non-repeating piecewise linear equation.
 *
 * More information regarding the algorithm can be found here:
 * https://en.wikipedia.org/wiki/Linear_congruential_generator
 *
 * ---
 *
 * Linear Congruential Generator
 *
 *   Modulus:
 *   - The linear congruential generator modulus.
 *   - Do not change unless you know what you're doing.
 *
 *   Multiplier:
 *   - The linear congruential generator multiplier.
 *   - Do not change unless you know what you're doing.
 *
 *   Increment:
 *   - The linear congruential generator increment.
 *   - Do not change unless you know what you're doing.
 *
 * ---
 *
 * Action Defaults
 *
 *   Default RNG Seed?
 *   - Default seed used for actions.
 *   - Use 'auto' to automatically create the seed based off the item or
 *     skill's database name.
 *   - Leave empty or 'none' to not use.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Sound Settings
 * ============================================================================
 *
 * These settings let you adjust the sound effects used for this plugin.
 *
 * ---
 *
 * Sound Types
 *
 *   Dice Throw Sound:
 *   Increment Tick Sound:
 *   Bonus Use Sound:
 *   Roll Success Sound:
 *   Roll Failure Sound:
 *   Critical Failure Sound:
 *   Critical Success Sound:
 *
 *     Filename:
 *     - Filename of the sound effect played.
 *
 *     Volume:
 *     - Volume of the sound effect played.
 *
 *     Pitch:
 *     - Pitch of the sound effect played.
 *
 *     Pan:
 *     - Pan of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Vocabulary Settings
 * ============================================================================
 *
 * These settings let you adjust the text displayed for this plugin.
 *
 * ---
 *
 * Command Window:
 *
 *   Roll Command:
 *   - Text used for the Roll Dice command.
 *   - Text codes allowed.
 *
 *   Effects Command:
 *   - Text used for the View Effects command.
 *   - Text codes allowed.
 *
 *   Bonus Command:
 *   - Text used for the Add Bonus command.
 *   -Text codes allowed.
 *
 * ---
 *
 * Text Colors:
 *
 *   Dice Count Up:
 *   Dice Count Down:
 *   Dice Rank Up:
 *   Dice Rank Down:
 *   Roll Modifier Up:
 *   Roll Modifier Down:
 *   - Text color used for this effect type.
 *   - Insert number for text colors from the Window Skin.
 *
 * ---
 *
 * Data Window Labels:
 *
 *   Dice Count:
 *   Advantage:
 *   Disadvantage:
 *   Dice Rank:
 *   Modifiers:
 *   - Text used to display this data label.
 *   - Text codes allowed.
 *
 * ---
 *
 * Data Window Ranks:
 *
 *   Rank 1: D4:
 *   Rank 2: D6:
 *   Rank 3: D8:
 *   Rank 4: D10:
 *   Rank 5: D12:
 *   Rank 6: D20:
 *   - Text used to display this dice rank.
 *   - Text codes allowed.
 *
 * ---
 *
 * Dice Effects:
 *
 *   Dice Display Format:
 *   - How the overall dice effect is displayed.
 *   - %1 - Effect
 *
 *   Pre/Post-Effect:
 *   - How pre/post effect is displayed.
 *   - %1 - Prev; %2 - Post
 *
 *   Order Format:
 *   - Order format used for dice effects.
 *   - %1 - Count; %2 - Rank; %3 - Modifier
 *
 *   Positive Plus Sign?:
 *   - Show + sign for positive numbers?
 *
 *   Dice Count Format:
 *   - Text format used for Dice Count effect.
 *   - %1 - Effect Number
 *
 *   Advantage Format:
 *   - Text format used for Advantage effect.
 *   - %1 - Effect Number
 *
 *   Disadvantage Format:
 *   - Text format used for Disdvantage effect.
 *   - %1 - Effect Number
 *
 *   Dice Rank Format:
 *   - Text format used for Dice Rank effect.
 *   - %1 - Effect Number
 *
 *   Roll Modifier Format:
 *   - Text format used for Roll Modifier effect.
 *   - %1 - Effect Number
 *
 * ---
 *
 * Effect Costs:
 *
 *   Cost Display Format:
 *   - How the overall cost is displayed.
 *   - %1 - Cost Text
 *
 *   Pre/Post-Cost:
 *   - How pre/post cost text is displayed.
 *   - %1 - Prev; %2 - Post
 *
 *   Used Up:
 *   - How a cost is displayed when its uses are spent.
 *   - Text codes allowed.
 *
 *   Use Times Format:
 *   - How use times are formated.
 *   - %1 - Current; %2 - Max Uses; %3 - Remaining
 *
 *     Show Max Use of 1?:
 *     - Show the use cost when there's only 1 max use?
 *
 *     Unlimited Uses:
 *     - Text used to indicate unlimited usage.
 *     - Text codes allowed.
 *
 *   Variable Cost Format:
 *   - How variable cost is displayed.
 *   - %1 - Cost; %2 - Quantity
 *
 *   Item Cost Format:
 *   - How item cost is displayed.
 *   - %1 - Cost; %2 - Quantity
 *
 *   Weapon Cost Format:
 *   - How weapon cost is displayed.
 *   - %1 - Cost; %2 - Quantity
 *
 *   Variable Cost Format:
 *   - How variable cost is displayed.
 *   - %1 - Cost; %2 - Quantity
 *
 *   Skill User's Name:
 *   - How the skill user's name is displayed.
 *   - %1 - Name
 *
 *     User to Cost Format:
 *     - How the skill user's name is displayed to cost.
 *     - %1 - Name; %2 - Skill Cost
 *
 * ---
 *
 * Subtitle Window:
 *
 *   Roll for Total:
 *   - Subtitle text for a roll total.
 *
 *   Roll for Highest:
 *   - Subtitle text to roll for highest.
 *
 *   Roll for Average:
 *   - Subtitle text to roll for average.
 *
 *   Roll for Lowest:
 *   - Subtitle text to roll for lowest.
 *
 *   Roll: > Target:
 *   - Subtitle text to roll for above target.
 *   - %1 - Target Roll
 *
 *   Roll: >= Target:
 *   - Subtitle text to roll for above/equal target.
 *   - %1 - Target Roll
 *
 *   Roll: <= Target:
 *   - Subtitle text to roll for below/equal target.
 *   - %1 - Target Roll
 *
 *   Roll: < Target:
 *   - Subtitle text to roll for below target.
 *   - %1 - Target Roll
 *
 *   Critical Success:
 *   - Subtitle text for a Critical Success on a Natural Roll.
 *
 *   Critical Failure:
 *   - Subtitle text for a Critical Failure on a Natural Roll.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Window Settings
 * ============================================================================
 *
 * These settings let you adjust the windows displayed for this plugin.
 *
 * ---
 *
 * Sprite Container:
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Title Window:
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Subtitle Window:
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Data Windows:
 *
 *   Dice Count: BG Type:
 *   - Select background type for this window.
 *
 *     JS: X, Y, W, H:
 *     - Code used to determine the dimensions for this window.
 *
 *   Dice Rank: BG Type:
 *   - Select background type for this window.
 *
 *     JS: X, Y, W, H:
 *     - Code used to determine the dimensions for this window.
 *
 *   Modifier: BG Type:
 *   - Select background type for this window.
 *
 *     JS: X, Y, W, H:
 *     - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Command Window:
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Show "View Effects"?:
 *   - Show "View Effects" command?
 *
 *   Show "Add Bonus"?:
 *   - Show "Add Bonus" command?
 *
 *   Text Align:
 *   - Text alignment for this window?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * List Windows:
 *
 *   Effect List: BG Type:
 *   - Select background type for this window.
 *
 *   Bonus List: BG Type:
 *   - Select background type for this window.
 *
 *   Window Columns:
 *   - How many columns are used for these windows?
 *
 *   Column Spacing:
 *   - How much spacing is there between columns?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Irina
 * * Olivia
 * * Yanfly
 *
 * Creazilla Open-Source
 * * Many of the canvas drawings are made by various artists under Creazilla.
 * * These are under the Creazilla Open-Source License.
 * * They are free for personal and commercial use. No attribution required.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: January 26, 2024
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Dice_RollValue
 * @text Dice: Roll Value
 * @desc Rolls a dice for any value and stores the result to a variable.
 *
 * @arg DiceSides:num
 * @text Dice Sides
 * @type select
 * @option 4
 * @option 6
 * @option 8
 * @option 10
 * @option 12
 * @option 20
 * @desc How many sides does this dice have?
 * @default 20
 *
 * @arg TotalRolls:eval
 * @text Total Dice Rolls
 * @parent DiceSides:num
 * @desc How many Dice Rolls do you want out of this?
 * You may use code. Dice Rolls have a cap.
 * @default 1
 *
 * @arg VariableID:num
 * @text Variable ID
 * @type variable
 * @desc Insert the ID of the Variable to save the Dice Roll
 * results to. Use 0 to not use. Results are after modifiers.
 * @default 1
 *
 * @arg ResultType:str
 * @text Result Type
 * @parent VariableID:num
 * @type select
 * @option total
 * @option highest
 * @option average
 * @option lowest
 * @desc What type of result do you want recorded to the variable?
 * @default highest
 *
 * @arg Title:str
 * @text Title Text
 * @desc What is the title of this Dice Roll?
 * Text codes allowed. Leave empty to not use.
 * @default Dice Check
 *
 * @arg DiceSeed:struct
 * @text Appearance & Seed
 * @type struct<DiceSeed>
 * @desc What are the dice appearance & seed settings used for this Dice Roll?
 * @default {"Seed:str":"none","ApplyDaily:eval":"false","ApplyUnique:eval":"false"}
 *
 * @arg AutoMods:struct
 * @text Auto Dice Effects
 * @type struct<AutoMods>
 * @desc Adjust the modifier settings that automatically apply to
 * the Dice Roll.
 * @default {"GeneralEffects:arraystruct":"[]","VariableEffects:arraystruct":"[]","ItemEffects:arraystruct":"[]","WeaponEffects:arraystruct":"[]","ArmorEffects:arraystruct":"[]","SkillEffects:arraystruct":"[]"}
 *
 * @arg BonusMods:struct
 * @text Bonus Dice Effects
 * @type struct<BonusMods>
 * @desc Adjust the modifier settings are optionally applied to
 * the Dice Roll selected by the player.
 * @default {"GeneralEffects:arraystruct":"[]","VariableEffects:arraystruct":"[]","ItemEffects:arraystruct":"[]","WeaponEffects:arraystruct":"[]","ArmorEffects:arraystruct":"[]","SkillEffects:arraystruct":"[]"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Dice_TargetValue
 * @text Dice: Target Value
 * @desc Rolls a dice for a target value and stores the result to a variable.
 *
 * @arg DiceSides:num
 * @text Dice Sides
 * @type select
 * @option 4
 * @option 6
 * @option 8
 * @option 10
 * @option 12
 * @option 20
 * @desc How many sides does this dice have?
 * @default 20
 *
 * @arg SwitchID:num
 * @text Switch ID
 * @type switch
 * @desc Insert the ID of the Switch to save the Dice Roll
 * results to. Use 0 to not use.
 * @default 1
 *
 * @arg NaturalRolls:eval
 * @text Allow Natural Rolls?
 * @parent SwitchID:num
 * @type boolean
 * @on Allow
 * @off Don't Allow
 * @desc Allow natural rolls which bypass modifiers?
 * Natural 1 or Max = auto-fail or auto-success
 * @default true
 *
 * @arg ResultType:str
 * @text Result Type
 * @parent SwitchID:num
 * @type select
 * @option above target
 * @value aboveTarget
 * @option above/equal to target
 * @value aboveEqualTarget
 * @option below/equal to target
 * @value belowEqualTarget
 * @option below target
 * @value belowTarget
 * @desc What type of result do you want recorded to the switch?
 * @default aboveEqualTarget
 *
 * @arg TargetValue:eval
 * @text Target Roll Value
 * @parent SwitchID:num
 * @desc What should the target value be equal to?
 * You may use code.
 * @default 10
 *
 * @arg VariableID:num
 * @text Variable ID
 * @parent SwitchID:num
 * @type variable
 * @desc Insert the ID of the Variable to save the Dice Roll
 * value to. Use 0 to not use. Results are after modifiers.
 * @default 1
 *
 * @arg Title:str
 * @text Title Text
 * @desc What is the title of this Dice Roll?
 * Text codes allowed. Leave empty to not use.
 * @default Dice Check
 *
 * @arg DiceSeed:struct
 * @text Appearance & Seed
 * @type struct<DiceSeed>
 * @desc What are the dice appearance & seed settings used for this Dice Roll?
 * @default {"Seed:str":"none","ApplyDaily:eval":"false","ApplyUnique:eval":"false"}
 *
 * @arg AutoMods:struct
 * @text Auto Dice Effects
 * @type struct<AutoMods>
 * @desc Adjust the modifier settings that automatically apply to
 * the Dice Roll.
 * @default {"GeneralEffects:arraystruct":"[]","VariableEffects:arraystruct":"[]","ItemEffects:arraystruct":"[]","WeaponEffects:arraystruct":"[]","ArmorEffects:arraystruct":"[]","SkillEffects:arraystruct":"[]"}
 *
 * @arg BonusMods:struct
 * @text Bonus Dice Effects
 * @type struct<BonusMods>
 * @desc Adjust the modifier settings are optionally applied to
 * the Dice Roll selected by the player.
 * @default {"GeneralEffects:arraystruct":"[]","VariableEffects:arraystruct":"[]","ItemEffects:arraystruct":"[]","WeaponEffects:arraystruct":"[]","ArmorEffects:arraystruct":"[]","SkillEffects:arraystruct":"[]"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Rng
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Variable_RngRange
 * @text RNG: Random Number Between X and Y
 * @desc Uses a seed to determine a random number between X and Y.
 *
 * @arg VariableID:num
 * @text Variable ID
 * @type variable
 * @desc Insert the ID of the Variable to save this value to.
 * @default 1
 *
 * @arg Min:eval
 * @text Minimum
 * @parent VariableID:num
 * @desc Minimum value the random number can be.
 * You may use JavaScript code.
 * @default 0
 *
 * @arg Max:eval
 * @text Maximum
 * @parent VariableID:num
 * @desc Maximum value the random number can be.
 * You may use JavaScript code.
 * @default 100
 *
 * @arg Seed:str
 * @text Seed
 * @desc What is the Random Number Seed?
 * Use numbers or text.
 * @default RpgTsukuru
 *
 * @arg ApplyDaily:eval
 * @text Daily Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply daily marker to Random Number results?
 * @default false
 *
 * @arg ApplyUnique:eval
 * @text Save-Unique Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply save-unique marker to Random Number results?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Reset_RngSeed
 * @text RNG: Reset Random Seed
 * @desc Resets the random state for the target Random Number Seed.
 *
 * @arg Seed:str
 * @text Seed
 * @desc What is the Random Number Seed?
 * Use numbers or text.
 * @default RpgTsukuru
 *
 * @arg ApplyDaily:eval
 * @text Daily Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply daily marker to Random Number Seed?
 * @default false
 *
 * @arg ApplyUnique:eval
 * @text Save-Unique Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply save-unique marker to Random Number Seed?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param DiceRollsRngSeeds
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Dice:struct
 * @text Dice Roll Settings
 * @parent Defaults
 * @type struct<Dice>
 * @desc Adjust the Dice Roll settings to fit your game.
 * @default {"Mechanics":"","MaxDiceCount:num":"10","Appearances":"","D4:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"60\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+0\",\"fontOffsetY:num\":\"+18\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+8\"}","D6:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"72\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+4\",\"fontOffsetY:num\":\"+4\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+0\"}","D8:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"40\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+0\",\"fontOffsetY:num\":\"+0\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+0\"}","D10:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"40\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+0\",\"fontOffsetY:num\":\"+8\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+0\"}","D12:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"40\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+0\",\"fontOffsetY:num\":\"+0\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+0\"}","D20:struct":"{\"filenames:arraystr\":\"[]\",\"colors:arraystr\":\"[\\\"#ff7479\\\",\\\"#ffa67b\\\",\\\"#f7941d\\\",\\\"#fff200\\\",\\\"#d3a87d\\\",\\\"#8dc63f\\\",\\\"#6cf77f\\\",\\\"#2aec88\\\",\\\"#22e6d8\\\",\\\"#6bd6ff\\\",\\\"#2eacfe\\\",\\\"#2895ff\\\",\\\"#7477e4\\\",\\\"#af70df\\\",\\\"#e16ede\\\",\\\"#ff8bd0\\\"]\",\"NumberSettings\":\"\",\"fontSize:num\":\"32\",\"outlineWidth:num\":\"4\",\"fontOffsetX:num\":\"+0\",\"fontOffsetY:num\":\"+0\",\"PositionSettings\":\"\",\"positionOffsetX:num\":\"+0\",\"positionOffsetY:num\":\"+0\"}","ColorRatio":"","ColorBorderRatio:num":"0.30","Color1Ratio:num":"0.60","Color2Ratio:num":"0.80","Color3Ratio:num":"1.00","ArrangeOffset":"","ArrangeOffsetX:num":"+0","ArrangeOffsetY:num":"+0","FadeSettings":"","fadeInDuration:num":"30","fadeOutDuration:num":"20","finalizeDelay:num":"60","RollSettings":"","rollDuration:num":"60","rollDelay:num":"10","rollHeight:num":"160","rotateSpeed:num":"+15","MoveSettings":"","moveDuration:num":"20","scaleDuration:num":"40","numberDuration:num":"4","maxNumberDuration:num":"80","numberColorShift:eval":"true","rollModDelay:num":"60"}
 *
 * @param RngSeed:struct
 * @text Random Seed Settings
 * @type struct<RngSeed>
 * @desc Set up the Random Number Seed algorithm settings.
 * @default {"LCG":"","modulus:num":"2147483648","multiplier:num":"1103515245","increment:num":"12345","Action":"","DefaultRngSeed:str":"none"}
 *
 * @param Sound:struct
 * @text Sound Settings
 * @type struct<Sound>
 * @desc These settings let you adjust the sound effects used for this plugin.
 * @default {"Throw":"","throw_name:str":"Bow2","throw_volume:num":"90","throw_pitch:num":"120","throw_pan:num":"0","Tick":"","tick_name:str":"Cursor1","tick_volume:num":"90","tick_pitch:num":"150","tick_pan:num":"0","Bonus":"","bonus_name:str":"Skill3","bonus_volume:num":"90","bonus_pitch:num":"120","bonus_pan:num":"0","Success":"","success_name:str":"Chime2","success_volume:num":"90","success_pitch:num":"100","success_pan:num":"0","Failure":"","failure_name:str":"Buzzer2","failure_volume:num":"90","failure_pitch:num":"100","failure_pan:num":"0","CritSuccess":"","crit_success_name:str":"Bell1","crit_success_volume:num":"90","crit_success_pitch:num":"100","crit_success_pan:num":"0","CritFailure":"","crit_failure_name:str":"Battle2","crit_failure_volume:num":"90","crit_failure_pitch:num":"100","crit_failure_pan:num":"0"}
 *
 * @param Vocab:struct
 * @text Vocabulary Settings
 * @type struct<Vocab>
 * @desc These settings let you adjust the text displayed for this plugin.
 * @default {"CommandWindow":"","CommandRoll:str":"Roll Dice","CommandEffects:str":"View Effects","CommandBonus:str":"Add Bonus","TextColors":"","diceUpColor:num":"24","diceDownColor:num":"27","rankUpColor:num":"21","rankDownColor:num":"4","modUpColor:num":"6","modDownColor:num":"2","DataWindows":"","DataDiceCount:str":"\\C[16]Dice Count:","DataAdvantage:str":"\\C[24]Advantage:","DataDisadvantage:str":"\\C[27]Disadvantage:","DataDiceRank:str":"\\C[16]Dice Rank:","DataModifiers:str":"\\C[16]Modifiers:","DataWindowRanks":"","DataRank1:str":"D4","DataRank2:str":"D6","DataRank3:str":"D8","DataRank4:str":"D10","DataRank5:str":"D12","DataRank6:str":"D20","DiceEffects":"","DiceDisplayFormat:str":"\\}%1\\{","DicePrePostFormat:str":"%1 %2","DiceOrderFmt:str":"%1 %2 %3","DicePlusSigns:eval":"true","DiceCount:str":"Dice%1","DiceAdvantage:str":"Adv%1","DiceDisadvantage:str":"Dis%1","DiceRank:str":"Rank%1","DiceModifier:str":"Roll%1","EffectCosts":"","CostDisplayFormat:str":"\\}%1\\{","CostPrePostFormat:str":"%1 %2","CostUsedUp:str":"USED!","CostUseTimesFmt:str":"(×%1/%2)","CostShowMaxUse1:eval":"false","CostUnlimitedUse:str":"∞","CostVarFormat:str":"(×%2-%1)","CostItemFormat:str":"(×%2-%1)","CostWeaponFormat:str":"(×%2-%1)","CostArmorFormat:str":"(×%2-%1)","CostSkillUserName:str":"[%1]","CostSkillUserFmt:str":"%1➤%2","Subtitle":"","SubtitleTotal:str":"Roll for \\C[23]total\\C[0] number","SubtitleHighest:str":"Roll for \\C[24]highest\\C[0] number","SubtitleAverage:str":"Roll for \\C[21]average\\C[0] number","SubtitleLowest:str":"Roll for \\C[21]lowest\\C[0] number","SubtitleAboveTarget:str":"\\C[5]Difficulty Class:\\C[0] Above \\C[24]%1\\C[0]","SubtitleAboveEqualTarget:str":"\\C[5]Difficulty Class:\\C[0] At least \\C[24]%1\\C[0]","SubtitleBelowEqualTarget:str":"\\C[5]Difficulty Class:\\C[0] At most \\C[24]%1\\C[0]","SubtitleBelowTarget:str":"\\C[5]Difficulty Class:\\C[0] Below \\C[24]%1\\C[0]","SubtitleCritSuccess:str":"\\C[17]CRITICAL SUCCESS!","SubtitleCritFailure:str":"\\C[2]CRITICAL FAILURE!"}
 *
 * @param Window:struct
 * @text Window Settings
 * @type struct<Window>
 * @desc These settings let you adjust the windows displayed for this plugin.
 * @default {"Container":"","Container_RectJS:func":"\"const ww = Math.min(Math.round(Graphics.width * 0.90), 816);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = this.calcWindowHeight(2, false) + this.calcWindowHeight(1, false);\\nconst wh = Graphics.height - wy - this.calcWindowHeight(4, true) - this.calcWindowHeight(1, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","TitleWindow":"","Title_BgType:num":"0","Title_RectJS:func":"\"const ww = Math.min(Math.round(Graphics.width * 0.80), 716);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = this.calcWindowHeight(2, false) - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\"","SubtitleWindow":"","Subtitle_BgType:num":"0","Subtitle_RectJS:func":"\"const ww = Math.min(Math.round(Graphics.width * 0.80), 716);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = Graphics.height - this.calcWindowHeight(4, true) - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\"","DataWindows":"","DataCount_BgType:num":"0","DataCount_RectJS:func":"\"const base = Math.min(Math.round(Graphics.width * 0.90), 816);\\nconst ww = Math.floor(base / 3);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.round((Graphics.width - base) / 2);\\nconst wy = this.calcWindowHeight(2, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","DataRank_BgType:num":"0","DataRank_RectJS:func":"\"const base = Math.min(Math.round(Graphics.width * 0.90), 816);\\nconst ww = Math.ceil(base / 3);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = this.calcWindowHeight(2, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","DataMod_BgType:num":"0","DataMod_RectJS:func":"\"const base = Math.min(Math.round(Graphics.width * 0.90), 816);\\nconst ww = Math.floor(base / 3);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.round((Graphics.width + ww) / 2);\\nconst wy = this.calcWindowHeight(2, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","CommandWindow":"","Command_BgType:num":"0","Command_TextAlign:str":"center","Command_RectJS:func":"\"const ww = Math.min(Math.round(Graphics.width * 0.50), 360);\\nconst wh = this.calcWindowHeight(this.totalDiceRollChoices(), true);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = Graphics.height - this.calcWindowHeight(4, true) + Math.round((this.calcWindowHeight(4, true) - this.calcWindowHeight(3, true)) / 2);\\nreturn new Rectangle(wx, wy, ww, wh);\"","CommandShowEffects:eval":"true","CommandShowBonus:eval":"true","ListWindows":"","EffectList_BgType:num":"0","BonusList_BgType:num":"0","ListColumns:num":"2","ListColSpacing:num":"16","List_RectJS:func":"\"const ww = Math.min(Graphics.width, 1016);\\nconst wh = this.calcWindowHeight(4, true);\\nconst wx = Math.round((Graphics.width - ww) / 2);\\nconst wy = Graphics.height - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Dice Roll Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Dice:
 *
 * @param Mechanics
 * @text Mechanics
 *
 * @param MaxDiceCount:num
 * @text Max Dice Count
 * @parent Mechanics
 * @type number
 * @min 1
 * @desc What is the maximum number of dice that can be thrown at a time?
 * @default 10
 *
 * @param Appearances
 * @text Dice Appearances
 *
 * @param D4:struct
 * @text D4 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D4 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"60","outlineWidth:num":"4","fontOffsetX:num":"+0","fontOffsetY:num":"+18","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+8"}
 *
 * @param D6:struct
 * @text D6 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D6 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"72","outlineWidth:num":"4","fontOffsetX:num":"+4","fontOffsetY:num":"+4","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+0"}
 *
 * @param D8:struct
 * @text D8 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D8 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"40","outlineWidth:num":"4","fontOffsetX:num":"+0","fontOffsetY:num":"+0","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+0"}
 *
 * @param D10:struct
 * @text D10 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D10 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"40","outlineWidth:num":"4","fontOffsetX:num":"+0","fontOffsetY:num":"+8","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+0"}
 *
 * @param D12:struct
 * @text D12 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D12 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"40","outlineWidth:num":"4","fontOffsetX:num":"+0","fontOffsetY:num":"+0","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+0"}
 *
 * @param D20:struct
 * @text D20 Appearance
 * @parent Appearances
 * @type struct<DiceType>
 * @desc Adjust the Dice Appearance settings for the D20 here.
 * @default {"filenames:arraystr":"[]","colors:arraystr":"[\"#ff7479\",\"#ffa67b\",\"#f7941d\",\"#fff200\",\"#d3a87d\",\"#8dc63f\",\"#6cf77f\",\"#2aec88\",\"#22e6d8\",\"#6bd6ff\",\"#2eacfe\",\"#2895ff\",\"#7477e4\",\"#af70df\",\"#e16ede\",\"#ff8bd0\"]","NumberSettings":"","fontSize:num":"32","outlineWidth:num":"4","fontOffsetX:num":"+0","fontOffsetY:num":"+0","PositionSettings":"","positionOffsetX:num":"+0","positionOffsetY:num":"+0"}
 *
 * @param ColorRatio
 * @text Color Ratios
 *
 * @param ColorBorderRatio:num
 * @text Border Ratio
 * @parent ColorRatio
 * @desc Used for generated colors.
 * Darkness ratio for border color.
 * @default 0.30
 *
 * @param Color1Ratio:num
 * @text Color 1 Ratio
 * @parent ColorRatio
 * @desc Used for generated colors.
 * Darkness ratio for darker color.
 * @default 0.60
 *
 * @param Color2Ratio:num
 * @text Color 2 Ratio
 * @parent ColorRatio
 * @desc Used for generated colors.
 * Darkness ratio for middle color.
 * @default 0.80
 *
 * @param Color3Ratio:num
 * @text Color 3 Ratio
 * @parent ColorRatio
 * @desc Used for generated colors.
 * Darkness ratio for main color.
 * @default 1.00
 *
 * @param ArrangeOffset
 * @text Arrange Offset
 *
 * @param ArrangeOffsetX:num
 * @text Offset X
 * @parent ArrangeOffset
 * @desc Offsets the dice arrangement x position.
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param ArrangeOffsetY:num
 * @text Offset Y
 * @parent ArrangeOffset
 * @desc Offsets the dice arrangement y position.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param FadeSettings
 * @text Fade Settings
 *
 * @param fadeInDuration:num
 * @text Fade In Duration
 * @parent FadeSettings
 * @type number
 * @min 1
 * @desc How many frames it takes to fade in dice?
 * 60 frames = 1 second.
 * @default 30
 *
 * @param fadeOutDuration:num
 * @text Fade Out Duration
 * @parent FadeSettings
 * @type number
 * @min 1
 * @desc How many frames it takes to fade out dice?
 * 60 frames = 1 second.
 * @default 20
 *
 * @param finalizeDelay:num
 * @text Finalize Delay
 * @parent FadeSettings
 * @type number
 * @min 1
 * @desc How many frames to wait before fading out?
 * 60 frames = 1 second.
 * @default 60
 *
 * @param RollSettings
 * @text Rolling Effect
 *
 * @param rollDuration:num
 * @text Dice Roll Duration
 * @parent RollSettings
 * @type number
 * @min 1
 * @desc How many frames it takes to roll a dice?
 * 60 frames = 1 second.
 * @default 60
 *
 * @param rollDelay:num
 * @text Between Roll Delay
 * @parent RollSettings
 * @type number
 * @min 1
 * @desc How many frames between multiple dice rolls?
 * 60 frames = 1 second.
 * @default 10
 *
 * @param rollHeight:num
 * @text Dice Roll Height
 * @parent RollSettings
 * @type number
 * @min 1
 * @desc How high should the dice jump up for its roll in pixels?
 * @default 160
 *
 * @param rotateSpeed:num
 * @text Dice Rotate Speed
 * @parent RollSettings
 * @desc How many degrees does the dice rotate per frame while rolling?
 * @default +15
 *
 * @param MoveSettings
 * @text Movement Settings
 *
 * @param moveDuration:num
 * @text Move Duration
 * @parent MoveSettings
 * @type number
 * @min 1
 * @desc How many frames it takes to move dice?
 * 60 frames = 1 second.
 * @default 20
 *
 * @param scaleDuration:num
 * @text Scaling Duration
 * @parent MoveSettings
 * @type number
 * @min 1
 * @desc How many frames it takes to change dice scale?
 * 60 frames = 1 second.
 * @default 40
 *
 * @param numberDuration:num
 * @text Number Climb Tick
 * @parent MoveSettings
 * @type number
 * @min 1
 * @desc How many frames are there between number ticks?
 * 60 frames = 1 second.
 * @default 4
 *
 * @param maxNumberDuration:num
 * @text Max Number Duration
 * @parent MoveSettings
 * @type number
 * @min 1
 * @desc Max number of frames to process number ticks?
 * 60 frames = 1 second.
 * @default 80
 *
 * @param numberColorShift:eval
 * @text Number Color Shift
 * @parent MoveSettings
 * @type boolean
 * @on Shift Color
 * @off Don't Shift
 * @desc Allow dice number colors to change due to modifiers?
 * @default true
 *
 * @param rollModDelay:num
 * @text Pre-Modifier Delay
 * @parent MoveSettings
 * @type number
 * @min 1
 * @desc Delay frames before applying modifiers?
 * 60 frames = 1 second.
 * @default 60
 *
 */
/* ----------------------------------------------------------------------------
 * Dice Type Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~DiceType:
 *
 * @param filenames:arraystr
 * @text Image Filename(s)
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Location: img/pictures/
 * @default []
 *
 * @param colors:arraystr
 * @text Dice Colors(s)
 * @type string[]
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default ["#ff7479","#ffa67b","#f7941d","#fff200","#d3a87d","#8dc63f","#6cf77f","#2aec88","#22e6d8","#6bd6ff","#2eacfe","#2895ff","#7477e4","#af70df","#e16ede","#ff8bd0"]
 *
 * @param NumberSettings
 * @text Number Settings
 *
 * @param fontSize:num
 * @text Font Size
 * @parent NumberSettings
 * @type number
 * @min 1
 * @desc Font size used for dice number.
 * @default 40
 *
 * @param outlineWidth:num
 * @text Number Outline
 * @parent NumberSettings
 * @type number
 * @min 0
 * @desc What width to use for number outline?
 * Use 0 to not use an outline.
 * @default 4
 *
 * @param fontOffsetX:num
 * @text Offset X
 * @parent NumberSettings
 * @desc Offsets the number x position.
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param fontOffsetY:num
 * @text Offset Y
 * @parent NumberSettings
 * @desc Offsets the number y position.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param PositionSettings
 * @text Position Settings
 *
 * @param positionOffsetX:num
 * @text Offset X
 * @parent PositionSettings
 * @desc Offsets the sprite x position.
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param positionOffsetY:num
 * @text Offset Y
 * @parent PositionSettings
 * @desc Offsets the sprite y position.
 * Negative: up. Positive: down.
 * @default +0
 *
 */
/* ----------------------------------------------------------------------------
 * RNG Seed Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~RngSeed:
 *
 * @param LCG
 * @text Linear Congruential
 *
 * @param modulus:num
 * @text Modulus
 * @parent LCG
 * @desc The linear congruential generator modulus.
 * Do not change unless you know what you're doing.
 * @default 2147483648
 *
 * @param multiplier:num
 * @text Multiplier
 * @parent LCG
 * @desc The linear congruential generator multiplier.
 * Do not change unless you know what you're doing.
 * @default 1103515245
 *
 * @param increment:num
 * @text Increment
 * @parent LCG
 * @desc The linear congruential generator increment.
 * Do not change unless you know what you're doing.
 * @default 12345
 *
 * @param Action
 * @text Action Defaults
 *
 * @param DefaultRngSeed:str
 * @text Default RNG Seed?
 * @parent Action
 * @desc Default seed used for actions. Use 'auto' to auto seed.
 * Leave empty or 'none' to not use.
 * @default none
 *
 */
/* ----------------------------------------------------------------------------
 * Sound Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Sound:
 *
 * @param Throw
 * @text Dice Throw Sound
 *
 * @param throw_name:str
 * @text Filename
 * @parent Throw
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Bow2
 *
 * @param throw_volume:num
 * @text Volume
 * @parent Throw
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param throw_pitch:num
 * @text Pitch
 * @parent Throw
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param throw_pan:num
 * @text Pan
 * @parent Throw
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Tick
 * @text Increment Tick Sound
 *
 * @param tick_name:str
 * @text Filename
 * @parent Tick
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Cursor1
 *
 * @param tick_volume:num
 * @text Volume
 * @parent Tick
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param tick_pitch:num
 * @text Pitch
 * @parent Tick
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 150
 *
 * @param tick_pan:num
 * @text Pan
 * @parent Tick
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Bonus
 * @text Bonus Use Sound
 *
 * @param bonus_name:str
 * @text Filename
 * @parent Bonus
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Skill3
 *
 * @param bonus_volume:num
 * @text Volume
 * @parent Bonus
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param bonus_pitch:num
 * @text Pitch
 * @parent Bonus
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param bonus_pan:num
 * @text Pan
 * @parent Bonus
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Success
 * @text Roll Success Sound
 *
 * @param success_name:str
 * @text Filename
 * @parent Success
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Chime2
 *
 * @param success_volume:num
 * @text Volume
 * @parent Success
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param success_pitch:num
 * @text Pitch
 * @parent Success
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param success_pan:num
 * @text Pan
 * @parent Success
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Failure
 * @text Roll Failure Sound
 *
 * @param failure_name:str
 * @text Filename
 * @parent Failure
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Buzzer2
 *
 * @param failure_volume:num
 * @text Volume
 * @parent Failure
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param failure_pitch:num
 * @text Pitch
 * @parent Failure
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param failure_pan:num
 * @text Pan
 * @parent Failure
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param CritSuccess
 * @text Critical Success Sound
 *
 * @param crit_success_name:str
 * @text Filename
 * @parent CritSuccess
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Bell1
 *
 * @param crit_success_volume:num
 * @text Volume
 * @parent CritSuccess
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param crit_success_pitch:num
 * @text Pitch
 * @parent CritSuccess
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param crit_success_pan:num
 * @text Pan
 * @parent CritSuccess
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param CritFailure
 * @text Critical Failure Sound
 *
 * @param crit_failure_name:str
 * @text Filename
 * @parent CritFailure
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Battle2
 *
 * @param crit_failure_volume:num
 * @text Volume
 * @parent CritFailure
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param crit_failure_pitch:num
 * @text Pitch
 * @parent CritFailure
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param crit_failure_pan:num
 * @text Pan
 * @parent CritFailure
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Vocabulary Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Vocab:
 *
 * @param CommandWindow
 * @text Command Window
 *
 * @param CommandRoll:str
 * @text Roll Command
 * @parent CommandWindow
 * @desc Text used for the Roll Dice command.
 * Text codes allowed.
 * @default Roll Dice
 *
 * @param CommandEffects:str
 * @text Effects Command
 * @parent CommandWindow
 * @desc Text used for the View Effects command.
 * Text codes allowed.
 * @default View Effects
 *
 * @param CommandBonus:str
 * @text Bonus Command
 * @parent CommandWindow
 * @desc Text used for the Add Bonus command.
 * Text codes allowed.
 * @default Add Bonus
 *
 * @param TextColors
 * @text Text Colors
 *
 * @param diceUpColor:num
 * @text Dice Count Up
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 24
 *
 * @param diceDownColor:num
 * @text Dice Count Down
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 27
 *
 * @param rankUpColor:num
 * @text Dice Rank Up
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 21
 *
 * @param rankDownColor:num
 * @text Dice Rank Down
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 4
 *
 * @param modUpColor:num
 * @text Roll Modifier Up
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 6
 *
 * @param modDownColor:num
 * @text Roll Modifier Down
 * @parent TextColors
 * @type number
 * @min 0
 * @max 31
 * @desc Text color used for this effect type.
 * Insert number for text colors from the Window Skin.
 * @default 2
 *
 * @param DataWindows
 * @text Data Window Labels
 *
 * @param DataDiceCount:str
 * @text Dice Count
 * @parent DataWindows
 * @desc Text used to display this data label.
 * Text codes allowed.
 * @default \C[16]Dice Count:
 *
 * @param DataAdvantage:str
 * @text Advantage
 * @parent DataWindows
 * @desc Text used to display this data label.
 * Text codes allowed.
 * @default \C[24]Advantage:
 *
 * @param DataDisadvantage:str
 * @text Disadvantage
 * @parent DataWindows
 * @desc Text used to display this data label.
 * Text codes allowed.
 * @default \C[27]Disadvantage:
 *
 * @param DataDiceRank:str
 * @text Dice Rank
 * @parent DataWindows
 * @desc Text used to display this data label.
 * Text codes allowed.
 * @default \C[16]Dice Rank:
 *
 * @param DataModifiers:str
 * @text Modifiers
 * @parent DataWindows
 * @desc Text used to display this data label.
 * Text codes allowed.
 * @default \C[16]Modifiers:
 *
 * @param DataWindowRanks
 * @text Data Window Ranks
 *
 * @param DataRank1:str
 * @text Rank 1: D4
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D4
 *
 * @param DataRank2:str
 * @text Rank 2: D6
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D6
 *
 * @param DataRank3:str
 * @text Rank 3: D8
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D8
 *
 * @param DataRank4:str
 * @text Rank 4: D10
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D10
 *
 * @param DataRank5:str
 * @text Rank 5: D12
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D12
 *
 * @param DataRank6:str
 * @text Rank 6: D20
 * @parent DataWindowRanks
 * @desc Text used to display this dice rank.
 * Text codes allowed.
 * @default D20
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param DiceDisplayFormat:str
 * @text Dice Display Format
 * @parent DiceEffects
 * @desc How the overall dice effect is displayed.
 * %1 - Effect
 * @default \}%1\{
 *
 * @param DicePrePostFormat:str
 * @text Pre/Post-Effect
 * @parent DiceEffects
 * @desc How pre/post effect is displayed.
 * %1 - Prev; %2 - Post
 * @default %1 %2
 *
 * @param DiceOrderFmt:str
 * @text Order Format
 * @parent DiceEffects
 * @desc Order format used for dice effects.
 * %1 - Count; %2 - Rank; %3 - Modifier
 * @default %1 %2 %3
 *
 * @param DicePlusSigns:eval
 * @text Positive Plus Sign?
 * @parent DiceEffects
 * @type boolean
 * @on Use +
 * @off Don't Use
 * @desc Show + sign for positive numbers?
 * @default true
 *
 * @param DiceCount:str
 * @text Dice Count Format
 * @parent DiceEffects
 * @desc Text format used for Dice Count effect.
 * %1 - Effect Number
 * @default Dice%1
 *
 * @param DiceAdvantage:str
 * @text Advantage Format
 * @parent DiceEffects
 * @desc Text format used for Advantage effect.
 * %1 - Effect Number
 * @default Adv%1
 *
 * @param DiceDisadvantage:str
 * @text Disadvantage Format
 * @parent DiceEffects
 * @desc Text format used for Disdvantage effect.
 * %1 - Effect Number
 * @default Dis%1
 *
 * @param DiceRank:str
 * @text Dice Rank Format
 * @parent DiceEffects
 * @desc Text format used for Dice Rank effect.
 * %1 - Effect Number
 * @default Rank%1
 *
 * @param DiceModifier:str
 * @text Roll Modifier Format
 * @parent DiceEffects
 * @desc Text format used for Roll Modifier effect.
 * %1 - Effect Number
 * @default Roll%1
 *
 * @param EffectCosts
 * @text Effect Costs
 *
 * @param CostDisplayFormat:str
 * @text Cost Display Format
 * @parent EffectCosts
 * @desc How the overall cost is displayed.
 * %1 - Cost Text
 * @default \}%1\{
 *
 * @param CostPrePostFormat:str
 * @text Pre/Post-Cost
 * @parent EffectCosts
 * @desc How pre/post cost text is displayed.
 * %1 - Prev; %2 - Post
 * @default %1 %2
 *
 * @param CostUsedUp:str
 * @text Used Up
 * @parent EffectCosts
 * @desc How a cost is displayed when its uses are spent.
 * Text codes allowed.
 * @default USED!
 *
 * @param CostUseTimesFmt:str
 * @text Use Times Format
 * @parent EffectCosts
 * @desc How use times are formated.
 * %1 - Current; %2 - Max Uses; %3 - Remaining
 * @default (×%1/%2)
 *
 * @param CostShowMaxUse1:eval
 * @text Show Max Use of 1?
 * @parent CostUseTimesFmt:str
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the use cost when there's only 1 max use?
 * @default false
 *
 * @param CostUnlimitedUse:str
 * @text Unlimited Uses
 * @parent CostUseTimesFmt:str
 * @desc Text used to indicate unlimited usage.
 * Text codes allowed.
 * @default ∞
 *
 * @param CostVarFormat:str
 * @text Variable Cost Format
 * @parent EffectCosts
 * @desc How variable cost is displayed.
 * %1 - Cost; %2 - Quantity
 * @default (×%2-%1)
 *
 * @param CostItemFormat:str
 * @text Item Cost Format
 * @parent EffectCosts
 * @desc How item cost is displayed.
 * %1 - Cost; %2 - Quantity
 * @default (×%2-%1)
 *
 * @param CostWeaponFormat:str
 * @text Weapon Cost Format
 * @parent EffectCosts
 * @desc How weapon cost is displayed.
 * %1 - Cost; %2 - Quantity
 * @default (×%2-%1)
 *
 * @param CostArmorFormat:str
 * @text Variable Cost Format
 * @parent EffectCosts
 * @desc How variable cost is displayed.
 * %1 - Cost; %2 - Quantity
 * @default (×%2-%1)
 *
 * @param CostSkillUserName:str
 * @text Skill User's Name
 * @parent EffectCosts
 * @desc How the skill user's name is displayed.
 * %1 - Name
 * @default [%1]
 *
 * @param CostSkillUserFmt:str
 * @text User to Cost Format
 * @parent CostSkillUserName:str
 * @desc How the skill user's name is displayed to cost.
 * %1 - Name; %2 - Skill Cost
 * @default %1➤%2
 *
 * @param Subtitle
 * @text Subtitle Window
 *
 * @param SubtitleTotal:str
 * @text Roll for Total
 * @parent Subtitle
 * @desc Subtitle text for a roll total.
 * @default Roll for \C[23]total\C[0] number
 *
 * @param SubtitleHighest:str
 * @text Roll for Highest
 * @parent Subtitle
 * @desc Subtitle text to roll for highest.
 * @default Roll for \C[24]highest\C[0] number
 *
 * @param SubtitleAverage:str
 * @text Roll for Average
 * @parent Subtitle
 * @desc Subtitle text to roll for average.
 * @default Roll for \C[21]average\C[0] number
 *
 * @param SubtitleLowest:str
 * @text Roll for Lowest
 * @parent Subtitle
 * @desc Subtitle text to roll for lowest.
 * @default Roll for \C[21]lowest\C[0] number
 *
 * @param SubtitleAboveTarget:str
 * @text Roll: > Target
 * @parent Subtitle
 * @desc Subtitle text to roll for above target.
 * %1 - Target Roll
 * @default \C[5]Difficulty Class:\C[0] Above \C[24]%1\C[0]
 *
 * @param SubtitleAboveEqualTarget:str
 * @text Roll: >= Target
 * @parent Subtitle
 * @desc Subtitle text to roll for above/equal target.
 * %1 - Target Roll
 * @default \C[5]Difficulty Class:\C[0] At least \C[24]%1\C[0]
 *
 * @param SubtitleBelowEqualTarget:str
 * @text Roll: <= Target
 * @parent Subtitle
 * @desc Subtitle text to roll for below/equal target.
 * %1 - Target Roll
 * @default \C[5]Difficulty Class:\C[0] At most \C[24]%1\C[0]
 *
 * @param SubtitleBelowTarget:str
 * @text Roll: < Target
 * @parent Subtitle
 * @desc Subtitle text to roll for below target.
 * %1 - Target Roll
 * @default \C[5]Difficulty Class:\C[0] Below \C[24]%1\C[0]
 *
 * @param SubtitleCritSuccess:str
 * @text Critical Success
 * @parent Subtitle
 * @desc Subtitle text for a Critical Success on a Natural Roll.
 * @default \C[17]CRITICAL SUCCESS!
 *
 * @param SubtitleCritFailure:str
 * @text Critical Failure
 * @parent Subtitle
 * @desc Subtitle text for a Critical Failure on a Natural Roll.
 * @default \C[2]CRITICAL FAILURE!
 *
 */
/* ----------------------------------------------------------------------------
 * Window Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Window:
 *
 * @param Container
 * @text Sprite Container
 *
 * @param Container_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Container
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.min(Math.round(Graphics.width * 0.90), 816);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = this.calcWindowHeight(2, false) + this.calcWindowHeight(1, false);\nconst wh = Graphics.height - wy - this.calcWindowHeight(4, true) - this.calcWindowHeight(1, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param TitleWindow
 * @text Title Window
 *
 * @param Title_BgType:num
 * @text Background Type
 * @parent TitleWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param Title_RectJS:func
 * @text JS: X, Y, W, H
 * @parent TitleWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.min(Math.round(Graphics.width * 0.80), 716);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = this.calcWindowHeight(2, false) - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param SubtitleWindow
 * @text Subtitle Window
 *
 * @param Subtitle_BgType:num
 * @text Background Type
 * @parent SubtitleWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param Subtitle_RectJS:func
 * @text JS: X, Y, W, H
 * @parent SubtitleWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.min(Math.round(Graphics.width * 0.80), 716);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = Graphics.height - this.calcWindowHeight(4, true) - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param DataWindows
 * @text Data Windows
 *
 * @param DataCount_BgType:num
 * @text Dice Count: BG Type
 * @parent DataWindows
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DataCount_RectJS:func
 * @text JS: X, Y, W, H
 * @parent DataCount_BgType:num
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const base = Math.min(Math.round(Graphics.width * 0.90), 816);\nconst ww = Math.floor(base / 3);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.round((Graphics.width - base) / 2);\nconst wy = this.calcWindowHeight(2, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param DataRank_BgType:num
 * @text Dice Rank: BG Type
 * @parent DataWindows
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DataRank_RectJS:func
 * @text JS: X, Y, W, H
 * @parent DataRank_BgType:num
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const base = Math.min(Math.round(Graphics.width * 0.90), 816);\nconst ww = Math.ceil(base / 3);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = this.calcWindowHeight(2, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param DataMod_BgType:num
 * @text Modifier: BG Type
 * @parent DataWindows
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DataMod_RectJS:func
 * @text JS: X, Y, W, H
 * @parent DataMod_BgType:num
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const base = Math.min(Math.round(Graphics.width * 0.90), 816);\nconst ww = Math.floor(base / 3);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.round((Graphics.width + ww) / 2);\nconst wy = this.calcWindowHeight(2, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param CommandWindow
 * @text Command Window
 *
 * @param Command_BgType:num
 * @text Background Type
 * @parent CommandWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param CommandShowEffects:eval
 * @text Show "View Effects"?
 * @parent Animation
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show "View Effects" command?
 * @default true
 *
 * @param CommandShowBonus:eval
 * @text Show "Add Bonus"?
 * @parent Animation
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show "Add Bonus" command?
 * @default true
 *
 * @param Command_TextAlign:str
 * @text Text Align
 * @parent CommandWindow
 * @type combo
 * @option left
 * @option center
 * @option right
 * @desc Text alignment for this window?
 * @default center
 *
 * @param Command_RectJS:func
 * @text JS: X, Y, W, H
 * @parent CommandWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.min(Math.round(Graphics.width * 0.50), 360);\nconst wh = this.calcWindowHeight(this.totalDiceRollChoices(), true);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = Graphics.height - this.calcWindowHeight(4, true) + Math.round((this.calcWindowHeight(4, true) - this.calcWindowHeight(3, true)) / 2);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param ListWindows
 * @text List Windows
 *
 * @param EffectList_BgType:num
 * @text Effect List: BG Type
 * @parent ListWindows
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param BonusList_BgType:num
 * @text Bonus List: BG Type
 * @parent ListWindows
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param ListColumns:num
 * @text Window Columns
 * @parent ListWindows
 * @type number
 * @min 1
 * @desc How many columns are used for these windows?
 * @default 2
 *
 * @param ListColSpacing:num
 * @text Column Spacing
 * @parent ListWindows
 * @type number
 * @min 0
 * @desc How much spacing is there between columns?
 * @default 16
 *
 * @param List_RectJS:func
 * @text JS: X, Y, W, H
 * @parent ListWindows
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.min(Graphics.width, 1016);\nconst wh = this.calcWindowHeight(4, true);\nconst wx = Math.round((Graphics.width - ww) / 2);\nconst wy = Graphics.height - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Dice Seed Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~DiceSeed:
 *
 * @param Appearance
 * @text Appearance Overrides
 *
 * @param d4_filenames:arraystr
 * @text D4 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param d6_filenames:arraystr
 * @text D6 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param d8_filenames:arraystr
 * @text D8 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param d10_filenames:arraystr
 * @text D10 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param d12_filenames:arraystr
 * @text D12 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param d20_filenames:arraystr
 * @text D20 Image Filename(s)
 * @parent Appearance
 * @type file[]
 * @dir img/pictures/
 * @require 1
 * @desc Use custom images for this dice? Priority over colors.
 * Overrides default parameters. Location: img/pictures/
 * @default []
 *
 * @param colors:arraystr
 * @text Dice Colors(s)
 * @parent Appearance
 * @type string[]
 * @desc Use #rrggbb for custom colors or regular numbers for text
 * colors from the Window Skin. Overrides default parameters.
 * @default []
 *
 * @param Seed:str
 * @text Seed
 * @desc What is the Random Number Seed used for this Dice Roll?
 * Use numbers or text. Use "none" to not use a seed.
 * @default none
 *
 * @param ApplyDaily:eval
 * @text Daily Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply daily marker to Random Number results?
 * @default false
 *
 * @param ApplyUnique:eval
 * @text Save-Unique Marker
 * @parent Seed:str
 * @type boolean
 * @on Apply To Seed
 * @off Don't Apply
 * @desc Apply save-unique marker to Random Number results?
 * @default false
 *
 */
/* ----------------------------------------------------------------------------
 * Auto Dice Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~AutoMods:
 *
 * @param GeneralEffects:arraystruct
 * @text General Auto-Effects
 * @type struct<GeneralAutoEffect>[]
 * @desc These auto-effects will be automatically activated as long
 * its conditions are met at the start of the dice-roll.
 * @default []
 *
 * @param VariableEffects:arraystruct
 * @text Variable Requirements
 * @type struct<VariableAutoEffect>[]
 * @desc These auto-effects require a variable(s) to be at least a
 * certain value to automatically activate.
 * @default []
 *
 * @param ItemEffects:arraystruct
 * @text Item Requirements
 * @type struct<ItemAutoEffect>[]
 * @desc These auto-effects require an item(s) to have a certain
 * quantity to automatically activate.
 * @default []
 *
 * @param WeaponEffects:arraystruct
 * @text Weapon Requirements
 * @type struct<WeaponAutoEffect>[]
 * @desc These auto-effects require a weapon(s) to be in party
 * possession or is equipped to automatically activate.
 * @default []
 *
 * @param ArmorEffects:arraystruct
 * @text Armor Requirements
 * @type struct<ArmorAutoEffect>[]
 * @desc These auto-effects require an armor(s) to be in party
 * possession or is equipped to automatically activate.
 * @default []
 *
 * @param SkillEffects:arraystruct
 * @text Skill Requirements
 * @type struct<SkillAutoEffect>[]
 * @desc These auto-effects require a skill(s) to be available
 * within the party to automatically activate.
 * @default []
 *
 */
/* ----------------------------------------------------------------------------
 * Bonus Dice Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~BonusMods:
 *
 * @param GeneralEffects:arraystruct
 * @text General Bonus Effects
 * @type struct<GeneralBonusEffect>[]
 * @desc Add general bonus dice effects here that the player can
 * select to use the effect.
 * @default []
 *
 * @param VariableEffects:arraystruct
 * @text Variable Cost Effects
 * @type struct<VariableBonusEffect>[]
 * @desc Variable Bonus Effects require deducting from variable(s)
 * as a cost to use the effect.
 * @default []
 *
 * @param ItemEffects:arraystruct
 * @text Item Cost Effects
 * @type struct<ItemBonusEffect>[]
 * @desc Item Bonus Effects require consuming the target item(s)
 * as a cost to use the effect.
 * @default []
 *
 * @param WeaponEffects:arraystruct
 * @text Weapon Cost Effects
 * @type struct<WeaponBonusEffect>[]
 * @desc Weapon Bonus Effects require consuming the target
 * weapon(s) as a cost to use the effect.
 * @default []
 *
 * @param ArmorEffects:arraystruct
 * @text Armor Cost Effects
 * @type struct<ArmorBonusEffect>[]
 * @desc Armor Bonus Effects require consuming target the armor(s)
 * as a cost to use the effect.
 * @default []
 *
 * @param SkillEffects:arraystruct
 * @text Skill Cost Effects
 * @type struct<SkillBonusEffect>[]
 * @desc Skill Bonus Effects require paying skill costs to
 * activate and use the effect.
 * @default []
 *
 */
/* ----------------------------------------------------------------------------
 * General Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~GeneralAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed.
 * @default Untitled
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * @default 87
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * General Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~GeneralBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed.
 * @default Untitled
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * @default 87
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
/* ----------------------------------------------------------------------------
 * Variable Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~VariableAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed.
 * @default Untitled
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * @default 87
 *
 * @param RefData
 * @text Reference Data
 *
 * @param VariableReqID:num
 * @text Required Variable ID
 * @parent RefData
 * @type variable
 * @desc This variable is to be checked from.
 * Changes don't affect effect mid-roll.
 * @default 2
 *
 * @param VariableReqValue:eval
 * @text Required Value
 * @parent VariableReqID:num
 * @desc Variable requires at least this much in value.
 * You may use code. Changes don't affect effect mid-roll.
 * @default 1
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * Variable Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~VariableBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed.
 * @default Untitled
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * @default 87
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param VariableCostID:num
 * @text Variable Cost ID
 * @parent Costs
 * @type variable
 * @desc This variable is to be deducted from.
 * Changes do not reveal effect mid-Dice Roll.
 * @default 2
 *
 * @param VariableCostValue:eval
 * @text Cost Value
 * @parent VariableCostID:num
 * @desc Deduct this much from the target variable.
 * You may use code.
 * @default 1
 *
 * @param ShowVariableCost:eval
 * @text Show Variable Cost?
 * @parent VariableCostID:num
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show variable cost?
 * @default true
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
/* ----------------------------------------------------------------------------
 * Item Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ItemAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param RefData
 * @text Reference Data
 *
 * @param ItemReqID:num
 * @text Required Item ID
 * @parent RefData
 * @type item
 * @desc This is the item whose quantity is checked.
 * Changes don't affect effect mid-roll.
 * @default 7
 *
 * @param ItemReqValue:eval
 * @text Required Value
 * @parent ItemReqID:num
 * @desc Quantity requires at least this much in value.
 * You may use code. Changes don't affect effect mid-roll.
 * @default 1
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * Item Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ItemBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param ItemCostID:num
 * @text Item Cost ID
 * @parent Costs
 * @type item
 * @desc This item is to be deducted from.
 * Changes do not reveal effect mid-Dice Roll.
 * @default 7
 *
 * @param ItemCostValue:eval
 * @text Cost Value
 * @parent ItemCostID:num
 * @desc Deduct this much from the target item.
 * You may use code.
 * @default 1
 *
 * @param ShowItemCost:eval
 * @text Show Item Cost?
 * @parent ItemCostID:num
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show item cost?
 * @default true
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
/* ----------------------------------------------------------------------------
 * Weapon Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~WeaponAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param RefData
 * @text Reference Data
 *
 * @param WeaponReqID:num
 * @text Required Weapon ID
 * @parent RefData
 * @type weapon
 * @desc This is the weapon whose presence is checked.
 * Changes don't affect effect mid-roll.
 * @default 1
 *
 * @param WeaponIncludeEquip:eval
 * @text Include Equipped?
 * @type boolean
 * @parent WeaponReqID:num
 * @on Include Equipped
 * @off Unequipped Only
 * @desc Allow equipped weapons to be included in the count.
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * Weapon Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~WeaponBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param WeaponCostID:num
 * @text Weapon Cost ID
 * @parent Costs
 * @type weapon
 * @desc This weapon is to be deducted from.
 * Changes do not reveal effect mid-Dice Roll.
 * @default 1
 *
 * @param WeaponCostValue:eval
 * @text Cost Value
 * @parent WeaponCostID:num
 * @desc Deduct this much from the target weapon.
 * You may use code.
 * @default 1
 *
 * @param ShowWeaponCost:eval
 * @text Show Weapon Cost?
 * @parent WeaponCostID:num
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show weapon cost?
 * @default true
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
/* ----------------------------------------------------------------------------
 * Armor Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ArmorAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param RefData
 * @text Reference Data
 *
 * @param ArmorReqID:num
 * @text Required Armor ID
 * @parent RefData
 * @type armor
 * @desc This is the armor whose presence is checked.
 * Changes don't affect effect mid-roll.
 * @default 1
 *
 * @param ArmorIncludeEquip:eval
 * @text Include Equipped?
 * @type boolean
 * @parent ArmorReqID:num
 * @on Include Equipped
 * @off Unequipped Only
 * @desc Allow equipped armors to be included in the count.
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * Armor Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~ArmorBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param ArmorCostID:num
 * @text Armor Cost ID
 * @parent Costs
 * @type armor
 * @desc This armor is to be deducted from.
 * Changes do not reveal effect mid-Dice Roll.
 * @default 2
 *
 * @param ArmorCostValue:eval
 * @text Cost Value
 * @parent ArmorCostID:num
 * @desc Deduct this much from the target armor.
 * You may use code.
 * @default 1
 *
 * @param ShowArmorCost:eval
 * @text Show Armor Cost?
 * @parent ArmorCostID:num
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show armor cost?
 * @default true
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
/* ----------------------------------------------------------------------------
 * Skill Auto Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SkillAutoEffect:
 *
 * @param Name:str
 * @text Auto Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Auto Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param RefData
 * @text Reference Data
 *
 * @param SkillReqID:num
 * @text Required Skill ID
 * @parent RefData
 * @type skill
 * @desc This is the skill whose presence is checked.
 * Changes don't affect effect mid-roll.
 * @default 52
 *
 * @param SkillLearnedOnly:eval
 * @text Skill Learned Only?
 * @parent SkillReqID:num
 * @type boolean
 * @on Learned Only
 * @off Temp Skills Allowed
 * @desc Require the skill to be learned only or allow temporary skills?
 * @default false
 *
 * @param SkillUser:str
 * @text Skill User(s)
 * @parent SkillReqID:num
 * @type select
 * @option Party Leader
 * @value leader
 * @option Any Party Member
 * @value anyMember
 * @option Every Party Member
 * @value everyMember
 * @option Any Battle Member
 * @value anyBattleMember
 * @option Every Battle Member
 * @value everyBattleMember
 * @option Any Specific Actor(s)
 * @value anySpecificActor
 * @option Every Specific Actor(s)
 * @value everySpecificActor
 * @desc Select which actor(s) to check for the required skill.
 * @default everyBattleMember
 *
 * @param SpecificActorIDs:arraynum
 * @text Specific Actor ID(s)
 * @parent SkillReqID:num
 * @type actor[]
 * @desc Determine which "Specific Actor(s)" to pick from.
 * Specific actor(s) must be in the party, main or reserve.
 * @default ["1"]
 *
 * @param SkillListUserName:eval
 * @text Show User Name?
 * @parent SkillReqID:num
 * @type boolean
 * @on Show Name
 * @off Don't Show
 * @desc Shows the skill user's name next to the skill name?
 * @default true
 *
 * @param SkillUserAlive:eval
 * @text Require User Alive?
 * @parent SkillReqID:num
 * @type boolean
 * @on Must Be Alive
 * @off Can Be Dead
 * @desc Requires the skill user to be alive?
 * Or can they be dead or alive?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param SwitchID:num
 * @text Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to meet conditions.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param ConditionJS:func
 * @text JS: Condition
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine the conditions for
 * this dice effect to be automatically activated.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 */
/* ----------------------------------------------------------------------------
 * Skill Bonus Effects Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SkillBonusEffect:
 *
 * @param Name:str
 * @text Bonus Effect Name
 * @desc What is the name of this effect?
 * Text codes allowed. Use "AutoName" to auto-name.
 * @default AutoName
 *
 * @param Icon:num
 * @text Bonus Effect Icon
 * @parent Name:str
 * @desc The icon used for this effect.
 * Use 1000000 to auto-select icon.
 * @default 1000000
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Name:str
 * @type animation
 * @desc Play this animation when the effect activates.
 * Animation will play on the player character.
 * @default 0
 *
 * @param Costs
 * @text Effect Costs
 *
 * @param SkillCostID:num
 * @text Skill Cost ID
 * @parent Costs
 * @type skill
 * @desc This skill's cost is to be paid by an actor.
 * Changes do not reveal effect mid-Dice Roll.
 * @default 52
 *
 * @param SkillLearnedOnly:eval
 * @text Skill Learned Only?
 * @parent SkillCostID:num
 * @type boolean
 * @on Learned Only
 * @off Temp Skills Allowed
 * @desc Require the skill to be learned only or allow temporary skills?
 * @default false
 *
 * @param SkillUser:str
 * @text Skill User(s)
 * @parent SkillCostID:num
 * @type select
 * @option Party Leader
 * @value leader
 * @option Any Party Member
 * @value anyMember
 * @option Every Party Member
 * @value everyMember
 * @option Any Battle Member
 * @value anyBattleMember
 * @option Every Battle Member
 * @value everyBattleMember
 * @option Any Specific Actor(s)
 * @value anySpecificActor
 * @option Every Specific Actor(s)
 * @value everySpecificActor
 * @desc Select which actor(s) to check for the required skill.
 * @default everyBattleMember
 *
 * @param SpecificActorIDs:arraynum
 * @text Specific Actor ID(s)
 * @parent SkillCostID:num
 * @type actor[]
 * @desc Determine which "Specific Actor(s)" to pick from.
 * Specific actor(s) must be in the party, main or reserve.
 * @default ["1"]
 *
 * @param SkillListUserName:eval
 * @text Show User Name?
 * @parent SkillCostID:num
 * @type boolean
 * @on Show Name
 * @off Don't Show
 * @desc Shows the skill user's name next to the skill name?
 * @default true
 *
 * @param SkillUserAlive:eval
 * @text Require User Alive?
 * @parent SkillCostID:num
 * @type boolean
 * @on Must Be Alive
 * @off Can Be Dead
 * @desc Requires the skill user to be alive?
 * Or can they be dead or alive?
 * @default true
 *
 * @param ShowSkillCost:eval
 * @text Show Skill Cost?
 * @parent SkillCostID:num
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show skill cost?
 * @default true
 *
 * @param MaxUses:eval
 * @text Maximum Uses
 * @parent Costs
 * @desc How many times can this effect be used?
 * You may use code. Over 1000000 for unlimited.
 * @default 1
 *
 * @param ShowUses:eval
 * @text Show Uses Left?
 * @parent MaxUses:eval
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how many uses are left?
 * @default true
 *
 * @param DiceEffects
 * @text Dice Effects
 *
 * @param Advantage:eval
 * @text Count/Advantage
 * @parent DiceEffects
 * @desc Change dice count (roll value) or advantage/disadvantage
 * (target value). You may use code.
 * @default +0
 *
 * @param DiceRank:eval
 * @text Rank/Type
 * @parent DiceEffects
 * @desc Raises/lowers dice rank: D4, D6, D8, D10, D12, D20.
 * You may use code. Higher rank = more sides.
 * @default +0
 *
 * @param DiceModifier:eval
 * @text Modifier (Static)
 * @parent DiceEffects
 * @desc Alters the finalized rolled dice value.
 * You may use code.
 * @default +0
 *
 * @param DiceModifierRand:eval
 * @text Modifier (Random)
 * @parent DiceEffects
 * @desc Adds a random element to the dice value.
 * You may use code.
 * @default +0
 *
 * @param Text
 * @text Additional Text
 *
 * @param PreCostText:str
 * @text Pre-Cost Text
 * @parent Text
 * @desc What text is added before the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PostCostText:str
 * @text Post-Cost Text
 * @parent Text
 * @desc What text is added after the dice cost text?
 * Text codes allowed.
 * @default
 *
 * @param PreEffectText:str
 * @text Pre-Effect Text
 * @parent Text
 * @desc What text is added before the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param PostEffectText:str
 * @text Post-Effect Text
 * @parent Text
 * @desc What text is added after the dice effects text?
 * Text codes allowed.
 * @default
 *
 * @param Conditions
 * @text Effect Conditions
 *
 * @param ShowSwitchID:num
 * @text Show Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to show effect.
 * Use 0 to not use. Does not reveal mid-Dice Roll.
 * @default 0
 *
 * @param EnableSwitchID:num
 * @text Enable Switch ID
 * @parent Conditions
 * @type switch
 * @desc This Switch is required to be ON to enable effect.
 * Use 0 to not use.
 * @default 0
 *
 * @param ShowJS:func
 * @text JS: Show
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine show conditions for
 * this dice effect to become available for use.
 * @default "// Does not reveal mid-Dice Roll.\nreturn true;"
 *
 * @param EnableJS:func
 * @text JS: Enable
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine enable conditions for
 * this dice effect to become usable.
 * @default "return true;"
 *
 * @param ActivateJS:func
 * @text JS: On Select
 * @parent Conditions
 * @type note
 * @desc JavaScript code used to determine what happens when this
 * dice effect is selected and activated.
 * @default "// Do Nothing"
 *
 */
//=============================================================================

const _0x457895 = _0x3a35;
(function (_0x4f40e0, _0x550303) {
    const _0x592e68 = _0x3a35,
        _0xc42c3a = _0x4f40e0();
    while (!![]) {
        try {
            const _0x18c251 =
                (-parseInt(_0x592e68(0x42f)) / 0x1) * (parseInt(_0x592e68(0x2d8)) / 0x2) +
                (-parseInt(_0x592e68(0x220)) / 0x3) * (-parseInt(_0x592e68(0x406)) / 0x4) +
                parseInt(_0x592e68(0x332)) / 0x5 +
                parseInt(_0x592e68(0x3fb)) / 0x6 +
                (-parseInt(_0x592e68(0x19c)) / 0x7) * (-parseInt(_0x592e68(0x435)) / 0x8) +
                -parseInt(_0x592e68(0x213)) / 0x9 +
                -parseInt(_0x592e68(0x11f)) / 0xa;
            if (_0x18c251 === _0x550303) break;
            else _0xc42c3a['push'](_0xc42c3a['shift']());
        } catch (_0x2587e1) {
            _0xc42c3a['push'](_0xc42c3a['shift']());
        }
    }
})(_0x1786, 0x60102);
var label = _0x457895(0x201),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x457895(0x398)](function (_0x261968) {
        const _0x9e4f5d = _0x457895;
        return (
            _0x261968[_0x9e4f5d(0x25b)] &&
            _0x261968[_0x9e4f5d(0x131)][_0x9e4f5d(0x313)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label][_0x457895(0x42e)] = VisuMZ[label][_0x457895(0x42e)] || {}),
    (VisuMZ[_0x457895(0x17d)] = function (_0x299d5e, _0x26c385) {
        const _0x32f0da = _0x457895;
        for (const _0x4106d1 in _0x26c385) {
            if (_0x32f0da(0x117) !== 'GgtOe') {
                if (_0x4106d1[_0x32f0da(0x20b)](/(.*):(.*)/i)) {
                    if (_0x32f0da(0x19e) === _0x32f0da(0x19e)) {
                        const _0x17383d = String(RegExp['$1']),
                            _0x364c52 = String(RegExp['$2'])
                                [_0x32f0da(0x1d3)]()
                                [_0x32f0da(0x1ea)]();
                        let _0x1feb93, _0x109920, _0x2d3c83;
                        switch (_0x364c52) {
                            case _0x32f0da(0x331):
                                _0x1feb93 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? Number(_0x26c385[_0x4106d1])
                                        : 0x0;
                                break;
                            case 'ARRAYNUM':
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920[_0x32f0da(0x3fe)](_0xd26f7d =>
                                        Number(_0xd26f7d)
                                    )));
                                break;
                            case 'EVAL':
                                _0x1feb93 =
                                    _0x26c385[_0x4106d1] !== '' ? eval(_0x26c385[_0x4106d1]) : null;
                                break;
                            case 'ARRAYEVAL':
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920['map'](_0x86cf8e => eval(_0x86cf8e))));
                                break;
                            case _0x32f0da(0x1aa):
                                _0x1feb93 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : '';
                                break;
                            case _0x32f0da(0x3cf):
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON['parse'](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920['map'](_0x17eccd =>
                                        JSON['parse'](_0x17eccd)
                                    )));
                                break;
                            case _0x32f0da(0x1c1):
                                _0x1feb93 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? new Function(JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1]))
                                        : new Function('return\x200');
                                break;
                            case _0x32f0da(0x108):
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920[_0x32f0da(0x3fe)](
                                        _0x5e7f2a => new Function(JSON[_0x32f0da(0x41d)](_0x5e7f2a))
                                    )));
                                break;
                            case _0x32f0da(0x3f1):
                                _0x1feb93 =
                                    _0x26c385[_0x4106d1] !== '' ? String(_0x26c385[_0x4106d1]) : '';
                                break;
                            case _0x32f0da(0x200):
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920[_0x32f0da(0x3fe)](_0x593deb =>
                                        String(_0x593deb)
                                    )));
                                break;
                            case 'STRUCT':
                                ((_0x2d3c83 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : {}),
                                    (_0x1feb93 = VisuMZ[_0x32f0da(0x17d)]({}, _0x2d3c83)));
                                break;
                            case _0x32f0da(0x142):
                                ((_0x109920 =
                                    _0x26c385[_0x4106d1] !== ''
                                        ? JSON[_0x32f0da(0x41d)](_0x26c385[_0x4106d1])
                                        : []),
                                    (_0x1feb93 = _0x109920['map'](_0x4e49ed =>
                                        VisuMZ[_0x32f0da(0x17d)](
                                            {},
                                            JSON[_0x32f0da(0x41d)](_0x4e49ed)
                                        )
                                    )));
                                break;
                            default:
                                continue;
                        }
                        _0x299d5e[_0x17383d] = _0x1feb93;
                    } else this[_0x32f0da(0x41e)](_0x38c47e);
                }
            } else
                (_0x4ac88e(_0x32f0da(0x43c)['format'](_0x340dc2, _0x1ceea3)),
                    _0x37c7a9[_0x32f0da(0x182)]());
        }
        return _0x299d5e;
    }),
    (_0xbbd6b8 => {
        const _0x53b51e = _0x457895,
            _0x1f59aa = _0xbbd6b8[_0x53b51e(0x366)];
        for (const _0x35e7d6 of dependencies) {
            if (!Imported[_0x35e7d6]) {
                if (_0x53b51e(0x347) === 'PJvTS') {
                    if (
                        _0x232f2e[_0x53b51e(0x201)][_0x53b51e(0x42e)][_0x53b51e(0x3e5)][
                            _0x53b51e(0x148)
                        ]
                    )
                        return _0x466cf4[_0x53b51e(0x201)][_0x53b51e(0x42e)][_0x53b51e(0x3e5)][
                            _0x53b51e(0x148)
                        ][_0x53b51e(0x2a3)](this);
                    const _0xb89cf1 = _0x168a37[_0x53b51e(0xf6)](
                            _0x22317f[_0x53b51e(0x23d)](_0x309247['width'] * 0.8),
                            0x2cc
                        ),
                        _0x50fc20 = this[_0x53b51e(0x21a)](0x1, ![]),
                        _0x3889b6 = _0x380f7b['round'](
                            (_0x36cb18[_0x53b51e(0x155)] - _0xb89cf1) / 0x2
                        ),
                        _0x574b65 = this[_0x53b51e(0x21a)](0x2, ![]) - _0x50fc20;
                    return new _0x13abb6(_0x3889b6, _0x574b65, _0xb89cf1, _0x50fc20);
                } else {
                    (alert(
                        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.'[
                            _0x53b51e(0x253)
                        ](_0x1f59aa, _0x35e7d6)
                    ),
                        SceneManager[_0x53b51e(0x182)]());
                    break;
                }
            }
        }
        const _0x2825a1 = _0xbbd6b8[_0x53b51e(0x131)];
        if (_0x2825a1[_0x53b51e(0x20b)](/\[Version[ ](.*?)\]/i)) {
            const _0x2119fd = Number(RegExp['$1']);
            _0x2119fd !== VisuMZ[label]['version'] &&
                (alert(_0x53b51e(0x43c)[_0x53b51e(0x253)](_0x1f59aa, _0x2119fd)),
                SceneManager[_0x53b51e(0x182)]());
        }
        if (_0x2825a1[_0x53b51e(0x20b)](/\[Tier[ ](\d+)\]/i)) {
            const _0x13b81b = Number(RegExp['$1']);
            if (_0x13b81b < tier)
                (alert(
                    '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.'[
                        'format'
                    ](_0x1f59aa, _0x13b81b, tier)
                ),
                    SceneManager[_0x53b51e(0x182)]());
            else {
                if (_0x53b51e(0x102) !== 'GlRCz') {
                    const _0x298195 = _0x3c1139[_0x53b51e(0x3a3)](_0x375c9b['VariableCostID']),
                        _0x53a8ba = _0x551556['max'](_0x4220ab[_0x53b51e(0x17c)] || 0x1, 0x1),
                        _0x6ff6c = _0x298195 - _0x53a8ba;
                    _0x11884d['setValue'](_0x487d21[_0x53b51e(0x1ee)], _0x6ff6c);
                } else tier = Math[_0x53b51e(0x137)](_0x13b81b, tier);
            }
        }
        VisuMZ[_0x53b51e(0x17d)](VisuMZ[label][_0x53b51e(0x42e)], _0xbbd6b8['parameters']);
    })(pluginData),
    PluginManager[_0x457895(0x34f)](pluginData[_0x457895(0x366)], _0x457895(0x1a7), _0x386776 => {
        const _0x3e1d27 = _0x457895;
        if (!SceneManager['canRollDice']()) return;
        VisuMZ[_0x3e1d27(0x17d)](_0x386776, _0x386776);
        const _0x4e29d6 = {};
        ((_0x4e29d6[_0x3e1d27(0x23b)] = _0x3e1d27(0x208)),
            VisuMZ[_0x3e1d27(0x201)]['SetupDiceRoll'](_0x4e29d6, _0x386776));
    }),
    PluginManager[_0x457895(0x34f)](pluginData[_0x457895(0x366)], _0x457895(0x3b7), _0x2b6356 => {
        const _0xc4979e = _0x457895;
        if (!SceneManager[_0xc4979e(0x232)]()) return;
        VisuMZ[_0xc4979e(0x17d)](_0x2b6356, _0x2b6356);
        const _0x3ab7ef = {};
        ((_0x3ab7ef['type'] = 'targetValue'),
            VisuMZ[_0xc4979e(0x201)]['SetupDiceRoll'](_0x3ab7ef, _0x2b6356));
    }),
    (VisuMZ['DiceRollsRngSeeds'][_0x457895(0x190)] = function (_0x43ec09, _0x366fd5) {
        const _0x1df39b = _0x457895;
        for (const _0x354602 in _0x366fd5) {
            _0x43ec09[_0x354602] = _0x366fd5[_0x354602];
        }
        ((_0x43ec09[_0x1df39b(0x43d)] = _0x43ec09[_0x1df39b(0x43d)] || 0x1),
            (_0x43ec09[_0x1df39b(0x43d)] = _0x43ec09[_0x1df39b(0x43d)]['clamp'](
                0x1,
                VisuMZ[_0x1df39b(0x201)][_0x1df39b(0x424)]
            )));
        if (![0x4, 0x6, 0x8, 0xa, 0xc, 0x14][_0x1df39b(0x313)](_0x43ec09[_0x1df39b(0x106)])) {
            if (_0x1df39b(0x311) !== _0x1df39b(0x258)) _0x43ec09[_0x1df39b(0x106)] = 0x14;
            else {
                const _0x207939 = _0x5f3d0f[_0x5af16b['WeaponCostID']],
                    _0x4cb5db = _0x8993c6[_0x1df39b(0x137)](
                        _0x4295c3[_0x1df39b(0x310)] || 0x1,
                        0x1
                    ),
                    _0x416008 = _0x367dbb[_0x1df39b(0x3e8)](_0x207939);
                if (_0x4cb5db > _0x416008) return ![];
            }
        }
        _0x43ec09[_0x1df39b(0x32f)] = [0x0, 0x4, 0x6, 0x8, 0xa, 0xc, 0x14][_0x1df39b(0x2be)](
            _0x43ec09['DiceSides']
        );
        _0x43ec09['SwitchID'] && $gameSwitches[_0x1df39b(0x176)](_0x43ec09[_0x1df39b(0x3ed)], ![]);
        if (_0x43ec09[_0x1df39b(0x154)]) {
            if (_0x1df39b(0x1a6) !== _0x1df39b(0x1a6)) {
                if (!_0x5c36c8['SETTINGS'][_0x1df39b(0x194)]) return ![];
                const _0xd47813 = _0x432ce4[_0x1df39b(0x15c)] || {};
                return (
                    (_0xd47813[_0x1df39b(0x346)] = _0xd47813[_0x1df39b(0x346)] || []),
                    _0xd47813[_0x1df39b(0x346)]['length'] > 0x0
                );
            } else $gameVariables['setValue'](_0x43ec09[_0x1df39b(0x154)], 0x0);
        }
        SceneManager['setupDiceRoll'](_0x43ec09);
    }),
    PluginManager[_0x457895(0x34f)](pluginData['name'], _0x457895(0x1a0), _0x2c3e3f => {
        const _0x314d36 = _0x457895;
        VisuMZ['ConvertParams'](_0x2c3e3f, _0x2c3e3f);
        const _0x4b2e9b = _0x2c3e3f[_0x314d36(0x154)] || 0x1,
            _0x198964 = Math['min'](_0x2c3e3f['Min'] || 0x0, _0x2c3e3f['Max'] || 0x0),
            _0x6f7c7d = Math['max'](
                _0x2c3e3f[_0x314d36(0x38f)] || 0x0,
                _0x2c3e3f[_0x314d36(0x1af)] || 0x0
            ),
            _0x12ea6b = String(_0x2c3e3f[_0x314d36(0x35a)]) || '-',
            _0x26c1be = _0x2c3e3f[_0x314d36(0x245)],
            _0xf0567a = _0x2c3e3f[_0x314d36(0x171)],
            _0x3dd7df = $gameSystem[_0x314d36(0x282)](
                _0x198964,
                _0x6f7c7d,
                _0x12ea6b,
                _0x26c1be,
                _0xf0567a
            );
        $gameVariables[_0x314d36(0x176)](_0x4b2e9b, _0x3dd7df);
    }),
    PluginManager['registerCommand'](pluginData[_0x457895(0x366)], _0x457895(0x285), _0x4f0a23 => {
        const _0x12ced1 = _0x457895;
        VisuMZ[_0x12ced1(0x17d)](_0x4f0a23, _0x4f0a23);
        const _0x45d9d7 = String(_0x4f0a23[_0x12ced1(0x35a)]) || '-',
            _0x4c0133 = _0x4f0a23[_0x12ced1(0x245)],
            _0x1eae65 = _0x4f0a23[_0x12ced1(0x171)];
        $gameSystem[_0x12ced1(0x303)](_0x45d9d7, _0x4c0133, _0x1eae65);
    }),
    (VisuMZ[_0x457895(0x201)]['RegExp'] = {
        rngSeed: /<(?:RNG|SEED|RNG SEED):[ ](.*?)>/gi,
        dailySeed: /<DAILY (?:RNG|SEED|RNG SEED):[ ](.*?)>/gi,
        uniqueSeed: /<UNIQUE (?:RNG|SEED|RNG SEED):[ ](.*?)>/gi,
        dailyUniqueSeed: /<DAILY UNIQUE (?:RNG|SEED|RNG SEED):[ ](.*?)>/gi,
        noRngSeed: /<NO (?:RNG|SEED|RNG SEED)>/gi,
    }),
    (ImageManager[_0x457895(0x114)] = {
        border: VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)][_0x457895(0x3d3)] ?? 0.3,
        color1:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x24e)] ?? 0.6,
        color2:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x150)] ?? 0.8,
        color3:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x221)] ?? 0x1,
    }),
    (ImageManager[_0x457895(0x13b)] = function (_0x2e0db8) {
        const _0x1bd9c9 = _0x457895,
            _0x56e507 = _0x2e0db8[Math['randomInt'](_0x2e0db8[_0x1bd9c9(0x1a1)])];
        return this['loadPicture'](_0x56e507);
    }),
    (ImageManager[_0x457895(0x1be)] = function (_0x2d7bee, _0x3eb8c2) {
        const _0x1fa9a2 = _0x457895,
            _0xb61427 = _0x3eb8c2[Math[_0x1fa9a2(0x3dd)](_0x3eb8c2[_0x1fa9a2(0x1a1)])] ?? '6';
        switch (_0x2d7bee) {
            case 'd4':
                return this['createDiceGraphic_D4'](_0xb61427);
            case 'd6':
                return this['createDiceGraphic_D6'](_0xb61427);
            case 'd8':
                return this['createDiceGraphic_D8'](_0xb61427);
            case _0x1fa9a2(0x2eb):
                return this[_0x1fa9a2(0x417)](_0xb61427);
            case _0x1fa9a2(0x388):
                return this[_0x1fa9a2(0xf0)](_0xb61427);
            case 'd20':
            default:
                return this[_0x1fa9a2(0x3d2)](_0xb61427);
        }
    }),
    (ImageManager[_0x457895(0x11d)] = function (_0x19bb9e) {
        const _0x2d43e2 = _0x457895,
            _0x5a7778 = _0x2d43e2(0x1f7)[_0x2d43e2(0x253)](_0x19bb9e);
        this['_cache_createDiceGraphic_D4'] = this[_0x2d43e2(0x35d)] || {};
        if (this['_cache_createDiceGraphic_D4'][_0x5a7778] !== undefined)
            return this[_0x2d43e2(0x35d)][_0x5a7778];
        const _0x1b019d = ImageManager[_0x2d43e2(0x114)],
            _0x2f6236 = ColorManager['darkenColor'](_0x19bb9e, _0x1b019d[_0x2d43e2(0x1a4)]),
            _0x245aad = ColorManager['darkenColor'](_0x19bb9e, _0x1b019d[_0x2d43e2(0x2b1)]),
            _0xdf5779 = ColorManager[_0x2d43e2(0x364)](_0x19bb9e, _0x1b019d[_0x2d43e2(0x397)]),
            _0x474a41 = ColorManager[_0x2d43e2(0x364)](_0x19bb9e, _0x1b019d['color3']),
            _0x2cd8d4 = new Bitmap(0x90, 0x84),
            _0x237876 = _0x2cd8d4[_0x2d43e2(0x1c9)];
        return (
            _0x237876[_0x2d43e2(0x10f)](),
            _0x237876[_0x2d43e2(0x355)](),
            (_0x237876[_0x2d43e2(0x2ef)] = _0x245aad),
            (_0x237876[_0x2d43e2(0x3e4)] = 0.070004),
            (_0x237876[_0x2d43e2(0x403)] = _0x2d43e2(0x236)),
            (_0x237876[_0x2d43e2(0x36e)] = _0x2d43e2(0x254)),
            _0x237876[_0x2d43e2(0x292)](71.362275, 2.51497),
            _0x237876[_0x2d43e2(0x356)](3.772455, 80.793412),
            _0x237876[_0x2d43e2(0x356)](3.458084, 128.57784),
            _0x237876[_0x2d43e2(0x356)](138.63772, 128.89221),
            _0x237876[_0x2d43e2(0x356)](141.15269, 123.5479),
            _0x237876[_0x2d43e2(0x356)](74.820358, 3.143712),
            _0x237876['fill'](),
            _0x237876[_0x2d43e2(0x355)](),
            (_0x237876[_0x2d43e2(0x2ef)] = _0xdf5779),
            (_0x237876[_0x2d43e2(0x3e4)] = 0.070004),
            (_0x237876['lineCap'] = _0x2d43e2(0x236)),
            (_0x237876[_0x2d43e2(0x36e)] = 'miter'),
            _0x237876[_0x2d43e2(0x292)](27.979042, 51.242515),
            _0x237876[_0x2d43e2(0x356)](7.54491, 129.20659),
            _0x237876[_0x2d43e2(0x356)](102.48503, 130.1497),
            _0x237876['lineTo'](78.907184, 8.802395),
            _0x237876[_0x2d43e2(0x356)](73.562873, 2.200599),
            _0x237876[_0x2d43e2(0x356)](65.703592, 6.916168),
            _0x237876[_0x2d43e2(0x2b4)](),
            _0x237876[_0x2d43e2(0x355)](),
            (_0x237876['fillStyle'] = _0x474a41),
            (_0x237876[_0x2d43e2(0x3e4)] = 0.070004),
            (_0x237876[_0x2d43e2(0x403)] = _0x2d43e2(0x236)),
            (_0x237876['lineJoin'] = 'miter'),
            _0x237876[_0x2d43e2(0x292)](74.034429, 3.733159),
            _0x237876[_0x2d43e2(0x356)](6.051647, 130.77844),
            _0x237876[_0x2d43e2(0x356)](138.9521, 129.52096),
            _0x237876[_0x2d43e2(0x356)](139.58084, 115.68862),
            _0x237876[_0x2d43e2(0x2b4)](),
            _0x237876[_0x2d43e2(0x355)](),
            (_0x237876[_0x2d43e2(0x3e4)] = 0.200081),
            (_0x237876['fillStyle'] = _0x2f6236),
            _0x237876[_0x2d43e2(0x292)](142.99234, 119.79907),
            _0x237876[_0x2d43e2(0x356)](80.034674, 4.285633),
            _0x237876[_0x2d43e2(0x22d)](
                78.746093,
                1.921966,
                76.388647,
                0.339813,
                73.721044,
                0.048365
            ),
            _0x237876[_0x2d43e2(0x22d)](
                71.05344,
                -0.243082,
                68.412353,
                0.792958,
                66.647774,
                2.823054
            ),
            _0x237876[_0x2d43e2(0x356)](2.022392, 77.187254),
            _0x237876[_0x2d43e2(0x22d)](0.718566, 78.688698, 0.00016, 80.613438, 0x0, 82.605604),
            _0x237876['lineTo'](0.0086, 123.69423),
            _0x237876[_0x2d43e2(0x22d)](
                0.008596,
                128.244864,
                3.679844,
                131.935056,
                8.211283,
                131.93924
            ),
            _0x237876[_0x2d43e2(0x356)](135.78679, 132.00004),
            _0x237876[_0x2d43e2(0x22d)](
                138.686568,
                132.001109,
                141.371843,
                130.465961,
                142.851216,
                127.961372
            ),
            _0x237876[_0x2d43e2(0x22d)](
                144.33059,
                125.456782,
                144.384624,
                122.35426,
                142.99337,
                119.79925
            ),
            _0x237876[_0x2d43e2(0x292)](138.06471, 122.459),
            _0x237876[_0x2d43e2(0x22d)](
                138.498285,
                123.259091,
                138.480377,
                124.229324,
                138.017574,
                125.012729
            ),
            _0x237876[_0x2d43e2(0x22d)](
                137.554772,
                125.796134,
                136.715441,
                126.276987,
                135.80839,
                126.27837
            ),
            _0x237876['lineTo'](8.134774, 126.38434),
            _0x237876['bezierCurveTo'](
                6.713611,
                126.384341,
                5.56153,
                125.227382,
                5.561529,
                123.8002
            ),
            _0x237876['lineTo'](5.570129, 82.635814),
            _0x237876[_0x2d43e2(0x22d)](
                5.570355,
                82.011671,
                5.795514,
                81.408699,
                6.204007,
                80.938304
            ),
            _0x237876[_0x2d43e2(0x356)](70.911354, 6.490869),
            _0x237876[_0x2d43e2(0x22d)](
                71.464977,
                5.854944,
                72.293144,
                5.530881,
                73.129255,
                5.623001
            ),
            _0x237876[_0x2d43e2(0x22d)](
                73.965366,
                5.715121,
                74.703867,
                6.211795,
                75.107024,
                6.953136
            ),
            _0x237876[_0x2d43e2(0x2b4)](),
            _0x237876[_0x2d43e2(0x355)](),
            (_0x237876[_0x2d43e2(0x3e4)] = 0.200081),
            (_0x237876[_0x2d43e2(0x2ef)] = _0x2f6236),
            _0x237876['moveTo'](37.330905, 75.898964),
            _0x237876[_0x2d43e2(0x22d)](
                48.287944,
                54.473064,
                59.227374,
                32.888023,
                70.149194,
                11.14384
            ),
            _0x237876[_0x2d43e2(0x22d)](
                70.194461,
                11.052912,
                70.171827,
                10.982186,
                70.081294,
                10.931663
            ),
            _0x237876['lineTo'](70.081294, 10.931663),
            _0x237876[_0x2d43e2(0x22d)](
                70.000807,
                10.886196,
                69.937921,
                10.903876,
                69.892634,
                10.984703
            ),
            _0x237876[_0x2d43e2(0x22d)](
                56.148547,
                35.143784,
                42.542805,
                59.451901,
                29.075406,
                83.909054
            ),
            _0x237876[_0x2d43e2(0x22d)](
                22.233555,
                96.342231,
                15.950114,
                109.240203,
                10.225085,
                122.60297
            ),
            _0x237876[_0x2d43e2(0x22d)](
                10.169752,
                122.724223,
                10.209966,
                122.78485,
                10.345728,
                122.78485
            ),
            _0x237876['bezierCurveTo'](
                10.406001,
                122.79085,
                10.476448,
                122.78075,
                10.557069,
                122.75455
            ),
            _0x237876[_0x2d43e2(0x22d)](
                10.582169,
                122.74495,
                10.602322,
                122.72975,
                10.617529,
                122.70895
            ),
            _0x237876[_0x2d43e2(0x22d)](
                19.109484,
                109.720043,
                28.013963,
                94.116678,
                37.330965,
                75.898854
            ),
            _0x237876['fill'](),
            _0x237876[_0x2d43e2(0x24f)](),
            _0x2cd8d4[_0x2d43e2(0x1ca)][_0x2d43e2(0x20d)](),
            (_0x2cd8d4[_0x2d43e2(0x2ce)] = ![]),
            (_0x2cd8d4[_0x2d43e2(0x43b)] = !![]),
            (this[_0x2d43e2(0x35d)][_0x5a7778] = _0x2cd8d4),
            this[_0x2d43e2(0x35d)][_0x5a7778]
        );
    }),
    (ImageManager['createDiceGraphic_D6'] = function (_0x324ccc) {
        const _0x4466ca = _0x457895,
            _0x5156d4 = _0x4466ca(0x1f7)[_0x4466ca(0x253)](_0x324ccc);
        this[_0x4466ca(0x2c0)] = this['_cache_createDiceGraphic_D6'] || {};
        if (this[_0x4466ca(0x2c0)][_0x5156d4] !== undefined)
            return this[_0x4466ca(0x2c0)][_0x5156d4];
        const _0x10bef1 = ImageManager[_0x4466ca(0x114)],
            _0x18be7c = ColorManager['darkenColor'](_0x324ccc, _0x10bef1[_0x4466ca(0x1a4)]),
            _0x41c8a1 = ColorManager['darkenColor'](_0x324ccc, _0x10bef1['color1']),
            _0x2208f5 = ColorManager[_0x4466ca(0x364)](_0x324ccc, _0x10bef1[_0x4466ca(0x397)]),
            _0x39df89 = ColorManager['darkenColor'](_0x324ccc, _0x10bef1[_0x4466ca(0x33a)]),
            _0x349b91 = new Bitmap(0x90, 0x90),
            _0x10271c = _0x349b91[_0x4466ca(0x1c9)];
        return (
            _0x10271c[_0x4466ca(0x10f)](),
            _0x10271c[_0x4466ca(0x355)](),
            (_0x10271c['fillStyle'] = _0x41c8a1),
            (_0x10271c[_0x4466ca(0x3e4)] = 0.070004),
            (_0x10271c['lineCap'] = _0x4466ca(0x236)),
            (_0x10271c[_0x4466ca(0x36e)] = _0x4466ca(0x254)),
            _0x10271c[_0x4466ca(0x292)](4.715569, 5.02994),
            _0x10271c[_0x4466ca(0x356)](3.772455, 126.69162),
            _0x10271c['lineTo'](16.661676, 140.83832),
            _0x10271c['lineTo'](138.9521, 140.52395),
            _0x10271c[_0x4466ca(0x356)](141.46706, 17.290419),
            _0x10271c[_0x4466ca(0x356)](127.32036, 2.200599),
            _0x10271c[_0x4466ca(0x2b4)](),
            _0x10271c[_0x4466ca(0x355)](),
            (_0x10271c[_0x4466ca(0x2ef)] = _0x2208f5),
            (_0x10271c[_0x4466ca(0x3e4)] = 0.070004),
            (_0x10271c['lineCap'] = _0x4466ca(0x236)),
            (_0x10271c[_0x4466ca(0x36e)] = _0x4466ca(0x254)),
            _0x10271c['moveTo'](3.693862, 3.772455),
            _0x10271c[_0x4466ca(0x356)](18.547904, 18.233533),
            _0x10271c[_0x4466ca(0x356)](141.46706, 20.11976),
            _0x10271c[_0x4466ca(0x356)](124.17665, 2.51497),
            _0x10271c[_0x4466ca(0x2b4)](),
            _0x10271c['beginPath'](),
            (_0x10271c[_0x4466ca(0x2ef)] = _0x39df89),
            (_0x10271c[_0x4466ca(0x3e4)] = 0.070004),
            (_0x10271c['lineCap'] = 'butt'),
            (_0x10271c['lineJoin'] = 'miter'),
            _0x10271c[_0x4466ca(0x292)](16.976048, 17.919161),
            _0x10271c[_0x4466ca(0x356)](17.565494, 139.10929),
            _0x10271c['lineTo'](138.9521, 139.89521),
            _0x10271c[_0x4466ca(0x356)](138.20546, 17.290418),
            _0x10271c[_0x4466ca(0x2b4)](),
            _0x10271c[_0x4466ca(0x355)](),
            (_0x10271c[_0x4466ca(0x2ef)] = _0x18be7c),
            (_0x10271c[_0x4466ca(0x3e4)] = 0.236709),
            _0x10271c[_0x4466ca(0x292)](14.173331, 142.03073),
            _0x10271c[_0x4466ca(0x22d)](
                15.56833,
                143.31965,
                18.197349,
                144.00885,
                20.164647,
                143.9999
            ),
            _0x10271c[_0x4466ca(0x22d)](
                56.821931,
                143.91636,
                93.485179,
                143.91336,
                130.15439,
                143.9909
            ),
            _0x10271c[_0x4466ca(0x22d)](
                134.285703,
                144.002833,
                137.078667,
                143.686573,
                138.53328,
                143.04212
            ),
            _0x10271c[_0x4466ca(0x22d)](
                142.85239,
                141.1265,
                143.997,
                137.28663,
                143.997,
                132.50693
            ),
            _0x10271c['bezierCurveTo'](144.003, 94.675085, 0x90, 56.840257, 143.988, 19.002446),
            _0x10271c[_0x4466ca(0x22d)](
                143.988,
                16.746858,
                142.35157,
                14.491268,
                140.581,
                12.710067
            ),
            _0x10271c[_0x4466ca(0x22d)](
                137.236733,
                9.338616,
                133.791053,
                5.883626,
                130.24396,
                2.345096
            ),
            _0x10271c[_0x4466ca(0x22d)](128.30348, 0.411734, 126.01427, 0x0, 123.09909, 0x0),
            _0x10271c[_0x4466ca(0x22d)](85.947002, 0.005867, 48.791933, 0.005867, 11.633883, 0x0),
            _0x10271c[_0x4466ca(0x22d)](8.581591, 0x0, 6.298333, 0.402783, 4.784107, 1.20835),
            _0x10271c['bezierCurveTo'](1.594702, 2.903027, 0x0, 5.776219, 0x0, 9.827926),
            _0x10271c[_0x4466ca(0x22d)](0.0178, 48.316157, 0.029733, 86.804389, 0.0358, 125.29262),
            _0x10271c['bezierCurveTo'](
                0.0358,
                126.754573,
                0.631946,
                128.21355,
                1.824239,
                129.66955
            ),
            _0x10271c[_0x4466ca(0x22d)](
                1.860039,
                129.71735,
                2.924168,
                130.812323,
                5.016627,
                132.95447
            ),
            _0x10271c[_0x4466ca(0x22d)](
                9.141984,
                137.173263,
                12.194276,
                140.198617,
                14.173501,
                142.03053
            ),
            _0x10271c['moveTo'](6.867516, 125.0243),
            _0x10271c[_0x4466ca(0x22d)](
                6.749547,
                124.752693,
                6.688663,
                124.457155,
                6.688656,
                124.15609
            ),
            _0x10271c[_0x4466ca(0x356)](6.715356, 10.069795),
            _0x10271c[_0x4466ca(0x22d)](
                6.715356,
                7.670994,
                7.913619,
                6.471593,
                10.310144,
                6.471593
            ),
            _0x10271c[_0x4466ca(0x356)](123.85904, 6.471593),
            _0x10271c['bezierCurveTo'](
                124.609764,
                6.471537,
                125.332823,
                6.770686,
                125.87105,
                7.304014
            ),
            _0x10271c[_0x4466ca(0x356)](136.70906, 18.143374),
            _0x10271c[_0x4466ca(0x22d)](
                137.10848,
                18.543175,
                137.30819,
                19.023532,
                137.30819,
                19.584446
            ),
            _0x10271c['lineTo'](137.29919, 134.46735),
            _0x10271c[_0x4466ca(0x22d)](
                137.299189,
                136.118432,
                135.961994,
                137.456898,
                134.31248,
                137.4569
            ),
            _0x10271c[_0x4466ca(0x22d)](
                93.899449,
                137.468833,
                55.996208,
                137.468833,
                20.602757,
                137.4569
            ),
            _0x10271c[_0x4466ca(0x22d)](
                19.511804,
                137.4569,
                18.593735,
                137.086933,
                17.848549,
                136.347
            ),
            _0x10271c[_0x4466ca(0x22d)](
                14.450491,
                132.969587,
                11.055413,
                129.580237,
                7.663315,
                126.17895
            ),
            _0x10271c[_0x4466ca(0x22d)](
                7.311589,
                125.826883,
                7.046302,
                125.442,
                6.867456,
                125.0243
            ),
            _0x10271c[_0x4466ca(0x2b4)](),
            _0x10271c['beginPath'](),
            (_0x10271c[_0x4466ca(0x2ef)] = _0x18be7c),
            (_0x10271c['lineWidth'] = 0.236709),
            _0x10271c['moveTo'](129.27809, 17.337767),
            _0x10271c['bezierCurveTo'](
                130.166357,
                17.27214,
                131.054623,
                17.32585,
                131.94289,
                17.498897
            ),
            _0x10271c[_0x4466ca(0x22d)](
                132.044237,
                17.51671,
                132.109813,
                17.498804,
                132.13962,
                17.445177
            ),
            _0x10271c['bezierCurveTo'](
                132.151553,
                17.427364,
                132.15752,
                17.40648,
                132.15752,
                17.382527
            ),
            _0x10271c[_0x4466ca(0x22d)](
                132.15752,
                17.33478,
                132.12772,
                17.310907,
                132.06812,
                17.310907
            ),
            _0x10271c['bezierCurveTo'](
                95.649281,
                16.606782,
                58.029211,
                15.950393,
                19.207908,
                15.341742
            ),
            _0x10271c[_0x4466ca(0x22d)](
                18.510409,
                15.329789,
                17.878489,
                15.117954,
                17.312149,
                14.706239
            ),
            _0x10271c[_0x4466ca(0x356)](9.210464, 8.753989),
            _0x10271c[_0x4466ca(0x22d)](9.106352, 8.680326, 8.964189, 8.693342, 8.875156, 8.784688),
            _0x10271c['bezierCurveTo'](8.786122, 8.876034, 8.776633, 9.01861, 8.852775, 9.12097),
            _0x10271c['lineTo'](15.121291, 17.67789),
            _0x10271c[_0x4466ca(0x22d)](
                15.294177,
                17.922543,
                15.383593,
                18.197032,
                15.38954,
                18.501357
            ),
            _0x10271c['bezierCurveTo'](
                15.902233,
                46.505278,
                16.414923,
                74.789653,
                16.927609,
                103.35448
            ),
            _0x10271c[_0x4466ca(0x22d)](
                17.052796,
                110.532987,
                17.201836,
                118.081453,
                17.374729,
                125.99988
            ),
            _0x10271c[_0x4466ca(0x22d)](
                17.380729,
                126.077473,
                17.422456,
                126.11627,
                17.499909,
                126.11627
            ),
            _0x10271c['lineTo'](17.499909, 126.11627),
            _0x10271c['bezierCurveTo'](
                17.577416,
                126.11027,
                17.616169,
                126.071473,
                17.616169,
                125.99988
            ),
            _0x10271c[_0x4466ca(0x22d)](
                17.562569,
                122.87607,
                17.893379,
                119.38528,
                17.929139,
                116.70005
            ),
            _0x10271c[_0x4466ca(0x22d)](
                18.388172,
                84.542985,
                18.930668,
                52.368018,
                19.556628,
                20.175148
            ),
            _0x10271c[_0x4466ca(0x22d)](
                19.568561,
                19.685841,
                19.818944,
                19.438204,
                20.307777,
                19.432236
            ),
            _0x10271c[_0x4466ca(0x22d)](
                56.297375,
                18.942929,
                92.009759,
                18.286541,
                127.44493,
                17.463072
            ),
            _0x10271c[_0x4466ca(0x22d)](
                127.480663,
                17.463072,
                128.09172,
                17.421304,
                129.2781,
                17.337767
            ),
            _0x10271c['fill'](),
            _0x10271c[_0x4466ca(0x24f)](),
            _0x349b91['_baseTexture']['update'](),
            (_0x349b91['_customModified'] = ![]),
            (_0x349b91[_0x4466ca(0x43b)] = !![]),
            (this['_cache_createDiceGraphic_D6'][_0x5156d4] = _0x349b91),
            this[_0x4466ca(0x2c0)][_0x5156d4]
        );
    }),
    (ImageManager[_0x457895(0x345)] = function (_0x3db086) {
        const _0x2e0e4e = _0x457895,
            _0x2849f8 = 'color-%1'[_0x2e0e4e(0x253)](_0x3db086);
        this[_0x2e0e4e(0x368)] = this[_0x2e0e4e(0x368)] || {};
        if (this[_0x2e0e4e(0x368)][_0x2849f8] !== undefined) {
            if (_0x2e0e4e(0x2e6) === _0x2e0e4e(0x187)) _0x21e22a += 0x1;
            else return this[_0x2e0e4e(0x368)][_0x2849f8];
        }
        const _0xe8ba57 = ImageManager[_0x2e0e4e(0x114)],
            _0x1e7d8a = ColorManager['darkenColor'](_0x3db086, _0xe8ba57[_0x2e0e4e(0x1a4)]),
            _0x1f0fd7 = ColorManager['darkenColor'](_0x3db086, _0xe8ba57[_0x2e0e4e(0x2b1)]),
            _0x4a47ab = ColorManager[_0x2e0e4e(0x364)](_0x3db086, _0xe8ba57[_0x2e0e4e(0x397)]),
            _0x5a54eb = ColorManager[_0x2e0e4e(0x364)](_0x3db086, _0xe8ba57[_0x2e0e4e(0x33a)]),
            _0x408045 = new Bitmap(0x80, 0x90),
            _0x4ec316 = _0x408045[_0x2e0e4e(0x1c9)];
        return (
            _0x4ec316[_0x2e0e4e(0x10f)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316[_0x2e0e4e(0x2ef)] = _0x1f0fd7),
            (_0x4ec316['lineWidth'] = 0.070004),
            (_0x4ec316['lineCap'] = 'butt'),
            (_0x4ec316[_0x2e0e4e(0x36e)] = _0x2e0e4e(0x254)),
            _0x4ec316['moveTo'](63.798391, 2.667529),
            _0x4ec316['lineTo'](2.445235, 54.017452),
            _0x4ec316[_0x2e0e4e(0x356)](2.889823, 106.92344),
            _0x4ec316[_0x2e0e4e(0x356)](63.576096, 142.49048),
            _0x4ec316[_0x2e0e4e(0x356)](124.04008, 106.92344),
            _0x4ec316[_0x2e0e4e(0x356)](124.92925, 52.68369),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316[_0x2e0e4e(0x2ef)] = _0x4a47ab),
            (_0x4ec316[_0x2e0e4e(0x3e4)] = 0.070004),
            (_0x4ec316[_0x2e0e4e(0x403)] = _0x2e0e4e(0x236)),
            (_0x4ec316[_0x2e0e4e(0x36e)] = _0x2e0e4e(0x254)),
            _0x4ec316[_0x2e0e4e(0x292)](62.020039, 2.889823),
            _0x4ec316['lineTo'](2.667529, 52.905982),
            _0x4ec316[_0x2e0e4e(0x356)](2.39361, 107.04248),
            _0x4ec316[_0x2e0e4e(0x356)](125.46592, 107.029),
            _0x4ec316['lineTo'](123.81778, 54.239747),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316['fillStyle'] = _0x5a54eb),
            (_0x4ec316[_0x2e0e4e(0x3e4)] = 0.070004),
            (_0x4ec316[_0x2e0e4e(0x403)] = 'butt'),
            (_0x4ec316['lineJoin'] = 'miter'),
            _0x4ec316[_0x2e0e4e(0x292)](63.517153, 5.07973),
            _0x4ec316['lineTo'](4.821002, 107.09016),
            _0x4ec316[_0x2e0e4e(0x356)](122.09753, 107.08341),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316['lineWidth'] = 0.171967),
            (_0x4ec316['fillStyle'] = _0x1e7d8a),
            _0x4ec316[_0x2e0e4e(0x292)](55.320945, 140.39891),
            _0x4ec316[_0x2e0e4e(0x22d)](
                38.24877,
                130.50225,
                20.777956,
                120.39327,
                2.908502,
                110.07197
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](0.744151, 108.81754, 0.0097, 106.13323, 0.0032, 103.52041),
            _0x4ec316[_0x2e0e4e(0x22d)](
                -0.001067,
                87.505495,
                -0.001067,
                71.490582,
                0.0032,
                55.475671
            ),
            _0x4ec316['bezierCurveTo'](0.0032, 53.560466, 0.869808, 51.85758, 2.603023, 50.367014),
            _0x4ec316[_0x2e0e4e(0x22d)](
                21.278424,
                34.269773,
                39.997156,
                18.135701,
                58.759217,
                1.964798
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                60.713418,
                0.283579,
                62.756445,
                -0.318713,
                64.888299,
                0.157921
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                66.157877,
                0.439568,
                67.76977,
                1.425334,
                69.723977,
                3.115219
            ),
            _0x4ec316['bezierCurveTo'](
                88.174044,
                19.008808,
                106.624118,
                34.902397,
                125.0742,
                50.795984
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                126.73811,
                52.225889,
                127.20607,
                54.611228,
                127.20607,
                56.977067
            ),
            _0x4ec316['bezierCurveTo'](
                127.223403,
                72.615004,
                127.22997,
                88.255111,
                127.22577,
                103.89739
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                127.22577,
                105.78659,
                126.699313,
                107.383313,
                125.6464,
                108.68756
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                125.057087,
                109.41984,
                123.768007,
                110.360107,
                121.77916,
                111.50836
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                104.28668,
                121.59568,
                86.653375,
                131.776153,
                68.879244,
                142.04978
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                67.098364,
                143.076727,
                65.796286,
                143.68119,
                64.97301,
                143.86317
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                61.710228,
                144.57163,
                58.785427,
                142.40727,
                55.321155,
                140.39891
            ),
            _0x4ec316[_0x2e0e4e(0x292)](64.556818, 138.982),
            _0x4ec316[_0x2e0e4e(0x356)](121.40197, 106.15923),
            _0x4ec316[_0x2e0e4e(0x22d)](
                122.000949,
                105.813192,
                122.370041,
                105.171617,
                122.3704,
                104.47585
            ),
            _0x4ec316[_0x2e0e4e(0x356)](122.3704, 55.800642),
            _0x4ec316[_0x2e0e4e(0x22d)](
                122.370061,
                55.193745,
                122.10681,
                54.61744,
                121.64895,
                54.221257
            ),
            _0x4ec316[_0x2e0e4e(0x356)](65.00529, 5.416064),
            _0x4ec316[_0x2e0e4e(0x22d)](
                64.186735,
                4.712585,
                62.977044,
                4.712585,
                62.158489,
                5.416064
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                43.392094,
                21.586967,
                24.608367,
                37.775201,
                5.807306,
                53.980766
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                5.092354,
                54.600395,
                4.734878,
                55.477838,
                4.734878,
                56.613095
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                4.739145,
                72.212032,
                4.741278,
                88.03629,
                4.741278,
                104.08587
            ),
            _0x4ec316['bezierCurveTo'](
                4.741668,
                104.999757,
                5.22686,
                105.843896,
                6.015193,
                106.30223
            ),
            _0x4ec316[_0x2e0e4e(0x356)](62.613351, 138.982),
            _0x4ec316[_0x2e0e4e(0x22d)](
                63.212683,
                139.327803,
                63.952775,
                139.327803,
                64.556718,
                138.982
            ),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316['beginPath'](),
            (_0x4ec316[_0x2e0e4e(0x3e4)] = 0.171967),
            (_0x4ec316[_0x2e0e4e(0x2ef)] = _0x1e7d8a),
            _0x4ec316[_0x2e0e4e(0x292)](60.676584, 10.524717),
            _0x4ec316['bezierCurveTo'](
                52.30082,
                26.032666,
                41.526718,
                45.61367,
                28.354277,
                69.267731
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                21.599069,
                81.404575,
                14.622875,
                92.629315,
                7.425695,
                102.94195
            ),
            _0x4ec316['bezierCurveTo'](
                7.365035,
                103.032937,
                7.28704,
                103.05027,
                7.191711,
                102.99395
            ),
            _0x4ec316['lineTo'](7.055227, 102.90945),
            _0x4ec316[_0x2e0e4e(0x22d)](
                7.02924,
                102.896517,
                7.02277,
                102.879183,
                7.035817,
                102.85745
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                12.863754,
                90.46496,
                19.384979,
                78.234957,
                26.59949,
                66.16744
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                38.298703,
                46.584261,
                50.049905,
                26.897097,
                62.035089,
                7.463416
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                62.065422,
                7.411416,
                62.108756,
                7.400583,
                62.165089,
                7.430916
            ),
            _0x4ec316['lineTo'](62.171089, 7.430916),
            _0x4ec316['bezierCurveTo'](
                62.227422,
                7.46125,
                62.240422,
                7.504583,
                62.210089,
                7.560916
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                61.71612,
                8.553182,
                61.204822,
                9.541115,
                60.676193,
                10.524714
            ),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316['lineWidth'] = 0.171967),
            (_0x4ec316[_0x2e0e4e(0x2ef)] = _0x1e7d8a),
            _0x4ec316[_0x2e0e4e(0x292)](89.8401, 53.220321),
            _0x4ec316[_0x2e0e4e(0x22d)](
                81.464329,
                38.10668,
                73.140562,
                22.949709,
                64.868799,
                7.749407
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                64.855866,
                7.727741,
                64.860199,
                7.710407,
                64.881799,
                7.697407
            ),
            _0x4ec316[_0x2e0e4e(0x356)](65.03779, 7.606417),
            _0x4ec316['bezierCurveTo'](
                65.128777,
                7.554424,
                65.20027,
                7.573921,
                65.252271,
                7.664907
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                77.350121,
                27.479895,
                89.309311,
                47.344711,
                101.12984,
                67.259354
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                108.080033,
                78.967229,
                114.284943,
                90.636105,
                119.74457,
                102.26598
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                119.831203,
                102.4523,
                119.872393,
                102.567127,
                119.86814,
                102.61046
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                119.816073,
                102.965767,
                119.68825,
                102.9961,
                119.48467,
                102.70146
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                110.714603,
                89.862668,
                102.626986,
                76.636067,
                95.221819,
                63.021658
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                93.540606,
                59.932201,
                91.746727,
                56.665089,
                89.84018,
                53.220322
            ),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x355)](),
            (_0x4ec316[_0x2e0e4e(0x3e4)] = 0.171967),
            (_0x4ec316['fillStyle'] = _0x1e7d8a),
            _0x4ec316[_0x2e0e4e(0x292)](11.331931, 107.17316),
            _0x4ec316[_0x2e0e4e(0x22d)](
                11.305944,
                107.17316,
                11.292951,
                107.16016,
                11.292951,
                107.13416
            ),
            _0x4ec316['lineTo'](11.292951, 107.12816),
            _0x4ec316[_0x2e0e4e(0x22d)](
                11.288684,
                107.067493,
                11.316851,
                107.039327,
                11.377451,
                107.04366
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                12.709861,
                107.04366,
                13.983774,
                106.78368,
                15.342182,
                106.72518
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                48.104293,
                105.299607,
                80.684415,
                105.3451,
                113.08255,
                106.86166
            ),
            _0x4ec316['bezierCurveTo'](
                113.823497,
                106.896327,
                114.642443,
                106.96132,
                115.53939,
                107.05664
            ),
            _0x4ec316['bezierCurveTo'](
                115.85137,
                107.091307,
                115.85137,
                107.11514,
                115.53939,
                107.12814
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                92.444281,
                108.1594,
                70.230952,
                108.512543,
                48.899401,
                108.18757
            ),
            _0x4ec316[_0x2e0e4e(0x22d)](
                36.901218,
                108.00125,
                24.378735,
                107.663273,
                11.331954,
                107.17364
            ),
            _0x4ec316[_0x2e0e4e(0x2b4)](),
            _0x4ec316[_0x2e0e4e(0x24f)](),
            _0x408045[_0x2e0e4e(0x1ca)]['update'](),
            (_0x408045[_0x2e0e4e(0x2ce)] = ![]),
            (_0x408045[_0x2e0e4e(0x43b)] = !![]),
            (this['_cache_createDiceGraphic_D8'][_0x2849f8] = _0x408045),
            this[_0x2e0e4e(0x368)][_0x2849f8]
        );
    }),
    (ImageManager['createDiceGraphic_D10'] = function (_0x54488c) {
        const _0x20e2ca = _0x457895,
            _0x9935c7 = _0x20e2ca(0x1f7)[_0x20e2ca(0x253)](_0x54488c);
        this[_0x20e2ca(0x26a)] = this[_0x20e2ca(0x26a)] || {};
        if (this[_0x20e2ca(0x26a)][_0x9935c7] !== undefined) {
            if (_0x20e2ca(0x23f) !== 'meImM') {
                if (!_0x2b1f2d[_0x20e2ca(0x232)]()) return;
                _0x6bb348[_0x20e2ca(0x17d)](_0x5db72f, _0x1029de);
                const _0x4c0f0c = {};
                ((_0x4c0f0c['type'] = _0x20e2ca(0x208)),
                    _0x394f85[_0x20e2ca(0x201)][_0x20e2ca(0x190)](_0x4c0f0c, _0x54801f));
            } else return this[_0x20e2ca(0x26a)][_0x9935c7];
        }
        const _0x199b4e = ImageManager[_0x20e2ca(0x114)],
            _0x39415a = ColorManager[_0x20e2ca(0x364)](_0x54488c, _0x199b4e[_0x20e2ca(0x1a4)]),
            _0x3963a5 = ColorManager[_0x20e2ca(0x364)](_0x54488c, _0x199b4e[_0x20e2ca(0x2b1)]),
            _0x5c3287 = ColorManager[_0x20e2ca(0x364)](_0x54488c, _0x199b4e['color2']),
            _0x326b88 = ColorManager[_0x20e2ca(0x364)](_0x54488c, _0x199b4e['color3']),
            _0x434d96 = new Bitmap(0x8a, 0x90),
            _0x1dd3fb = _0x434d96[_0x20e2ca(0x1c9)];
        return (
            _0x1dd3fb[_0x20e2ca(0x10f)](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x3963a5),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.070004),
            (_0x1dd3fb[_0x20e2ca(0x403)] = _0x20e2ca(0x236)),
            (_0x1dd3fb[_0x20e2ca(0x36e)] = _0x20e2ca(0x254)),
            _0x1dd3fb[_0x20e2ca(0x292)](68.466567, 2.22294),
            _0x1dd3fb[_0x20e2ca(0x356)](3.112117, 73.579328),
            _0x1dd3fb['lineTo'](2.000646, 91.140559),
            _0x1dd3fb[_0x20e2ca(0x356)](69.800329, 142.26819),
            _0x1dd3fb[_0x20e2ca(0x356)](136.26625, 90.918264),
            _0x1dd3fb[_0x20e2ca(0x356)](135.59937, 73.801623),
            _0x1dd3fb[_0x20e2ca(0x2b4)](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x5c3287),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.070004),
            (_0x1dd3fb[_0x20e2ca(0x403)] = _0x20e2ca(0x236)),
            (_0x1dd3fb[_0x20e2ca(0x36e)] = _0x20e2ca(0x254)),
            _0x1dd3fb['moveTo'](1.02811, 88.111803),
            _0x1dd3fb[_0x20e2ca(0x356)](27.564461, 96.920205),
            _0x1dd3fb[_0x20e2ca(0x356)](109.14638, 96.475615),
            _0x1dd3fb[_0x20e2ca(0x356)](136.26625, 88.473029),
            _0x1dd3fb[_0x20e2ca(0x356)](135.59937, 75.357682),
            _0x1dd3fb[_0x20e2ca(0x356)](69.355742, 1.333764),
            _0x1dd3fb[_0x20e2ca(0x356)](2.667529, 74.24621),
            _0x1dd3fb[_0x20e2ca(0x2b4)](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x326b88),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.070004),
            (_0x1dd3fb[_0x20e2ca(0x403)] = _0x20e2ca(0x236)),
            (_0x1dd3fb[_0x20e2ca(0x36e)] = _0x20e2ca(0x254)),
            _0x1dd3fb['moveTo'](69.022301, 3.973506),
            _0x1dd3fb[_0x20e2ca(0x356)](27.460057, 96.239837),
            _0x1dd3fb['lineTo'](69.133449, 119.14961),
            _0x1dd3fb[_0x20e2ca(0x356)](110.88543, 95.972083),
            _0x1dd3fb['fill'](),
            _0x1dd3fb['beginPath'](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x39415a),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.170223),
            _0x1dd3fb[_0x20e2ca(0x292)](70.860844, 143.75437),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                68.613957,
                144.34416,
                66.425179,
                143.85693,
                64.610885,
                142.45298
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                43.941256,
                126.494447,
                23.275932,
                110.52737,
                2.614911,
                94.55175
            ),
            _0x1dd3fb['bezierCurveTo'](0.931899, 93.248223, 0.073175, 91.3891, 0.03874, 88.97438),
            _0x1dd3fb[_0x20e2ca(0x22d)](-0.012913, 84.70908, -0.012913, 80.40532, 0.03874, 76.0631),
            _0x1dd3fb[_0x20e2ca(0x22d)](0.064563, 74.03088, 0.923289, 72.49871, 2.369561, 70.87679),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                22.350489,
                48.575871,
                42.327113,
                26.272817,
                62.299433,
                3.967626
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                64.12449,
                1.933276,
                65.553543,
                0.747285,
                66.586594,
                0.409653
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                68.368607,
                -0.163043,
                70.070989,
                -0.135266,
                71.69374,
                0.492983
            ),
            _0x1dd3fb['bezierCurveTo'](
                72.662226,
                0.864809,
                73.858844,
                1.847793,
                75.283593,
                3.441937
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                95.707871,
                26.234346,
                116.127847,
                49.02889,
                136.54352,
                71.82557
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                137.55074,
                72.94104,
                137.97042,
                74.59502,
                137.98333,
                76.22335
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                138.013463,
                81.074163,
                138.00273,
                85.75616,
                137.95113,
                90.26934
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                137.92533,
                92.29514,
                136.07226,
                94.07734,
                134.42584,
                95.35308
            ),
            _0x1dd3fb['bezierCurveTo'](
                114.56544,
                110.700453,
                94.705035,
                126.04569,
                74.844624,
                141.38879
            ),
            _0x1dd3fb['bezierCurveTo'](
                73.127175,
                142.71795,
                71.799274,
                143.506473,
                70.860921,
                143.75436
            ),
            _0x1dd3fb['moveTo'](69.459768, 139.2476),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                69.817339,
                139.158905,
                70.151647,
                138.997306,
                70.441167,
                138.77321
            ),
            _0x1dd3fb['bezierCurveTo'](
                91.123709,
                122.780483,
                111.593183,
                106.96085,
                131.84959,
                91.31431
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                133.06343,
                90.37192,
                133.28941,
                89.81419,
                133.28941,
                88.36535
            ),
            _0x1dd3fb['bezierCurveTo'](
                133.30221,
                84.35649,
                133.29581,
                80.371133,
                133.27021,
                76.40928
            ),
            _0x1dd3fb['bezierCurveTo'](
                133.266169,
                75.719594,
                133.00893,
                75.056053,
                132.54699,
                74.54375
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](70.680058, 5.467746),
            _0x1dd3fb['bezierCurveTo'](
                70.263495,
                5.002932,
                69.666728,
                4.737032,
                69.040089,
                4.737032
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                68.413451,
                4.737032,
                67.816684,
                5.002932,
                67.400121,
                5.467746
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](5.404149, 74.63991),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                5.029741,
                75.056805,
                4.822905,
                75.595327,
                4.823058,
                76.15285
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](4.823058, 89.17311),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                4.822819,
                89.840556,
                5.132664,
                90.470052,
                5.662411,
                90.87838
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                26.392302,
                106.90102,
                47.053322,
                122.87451,
                67.645472,
                138.79885
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                68.235172,
                139.25615,
                68.839937,
                139.405733,
                69.459768,
                139.2476
            ),
            _0x1dd3fb['fill'](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x39415a),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.170223),
            _0x1dd3fb['moveTo'](111.14983, 94.5261),
            _0x1dd3fb['bezierCurveTo'](
                111.494183,
                94.5261,
                111.83638,
                94.48336,
                112.17642,
                94.39788
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                118.181033,
                92.927673,
                124.18134,
                91.50662,
                130.17734,
                90.13472
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                130.263433,
                90.117653,
                130.319387,
                90.14972,
                130.3452,
                90.23092
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](130.3452, 90.23692),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                130.366733,
                90.3053,
                130.343067,
                90.352313,
                130.2742,
                90.37796
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                124.002727,
                92.745673,
                117.750617,
                95.128343,
                111.51787,
                97.52597
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                110.315917,
                97.989569,
                108.961418,
                97.424938,
                108.45746,
                96.25022
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                95.957533,
                67.047222,
                83.520025,
                37.918929,
                71.144935,
                8.865341
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                71.114808,
                8.805508,
                71.132032,
                8.760635,
                71.196605,
                8.730721
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](71.202905, 8.730721),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                71.263165,
                8.705068,
                71.308359,
                8.722165,
                71.338487,
                8.782011
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                84.518528,
                37.194524,
                97.722225,
                65.730981,
                110.94958,
                94.39138
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                110.985536,
                94.473528,
                111.064271,
                94.526485,
                111.14973,
                94.526
            ),
            _0x1dd3fb[_0x20e2ca(0x2b4)](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x39415a),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.170223),
            _0x1dd3fb[_0x20e2ca(0x292)](26.555869, 94.55175),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                26.803164,
                94.61133,
                27.059474,
                94.4881,
                27.169243,
                94.25685
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                40.254556,
                65.895621,
                53.357086,
                37.566448,
                66.476833,
                9.269329
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                66.50696,
                9.200953,
                66.554309,
                9.181723,
                66.618881,
                9.211639
            ),
            _0x1dd3fb['lineTo'](66.625181, 9.217939),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                66.694046,
                9.247853,
                66.713412,
                9.294866,
                66.683281,
                9.35898
            ),
            _0x1dd3fb['bezierCurveTo'](
                54.226438,
                38.664727,
                41.851363,
                67.654207,
                29.558056,
                96.32742
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                29.072454,
                97.464199,
                27.762897,
                98.013768,
                26.594493,
                97.57111
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                20.387577,
                95.19913,
                14.103183,
                92.827147,
                7.74131,
                90.45516
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                7.659528,
                90.425227,
                7.631553,
                90.36967,
                7.657385,
                90.28849
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](7.657385, 90.28249),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                7.678911,
                90.218357,
                7.721957,
                90.192723,
                7.786524,
                90.20559
            ),
            _0x1dd3fb['bezierCurveTo'](
                14.101049,
                91.63305,
                20.357465,
                93.081883,
                26.555771,
                94.55209
            ),
            _0x1dd3fb[_0x20e2ca(0x2b4)](),
            _0x1dd3fb[_0x20e2ca(0x355)](),
            (_0x1dd3fb[_0x20e2ca(0x2ef)] = _0x39415a),
            (_0x1dd3fb[_0x20e2ca(0x3e4)] = 0.170223),
            _0x1dd3fb[_0x20e2ca(0x292)](67.238707, 119.92555),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                56.219497,
                113.232717,
                45.137873,
                106.57407,
                33.993836,
                99.94961
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                33.92066,
                99.906877,
                33.905597,
                99.849177,
                33.948646,
                99.77651
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](33.948646, 99.77051),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                33.991693,
                99.697857,
                34.049804,
                99.680757,
                34.12298,
                99.71921
            ),
            _0x1dd3fb['bezierCurveTo'](
                45.641499,
                105.56155,
                57.147104,
                111.397483,
                68.639797,
                117.22701
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                68.879128,
                117.348042,
                69.162341,
                117.348042,
                69.401672,
                117.22701
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                81.083757,
                111.307737,
                92.830406,
                105.34145,
                104.64162,
                99.32815
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                104.714793,
                99.29395,
                104.77075,
                99.31105,
                104.80949,
                99.37945
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](104.81549, 99.38545),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                104.854223,
                99.45811,
                104.839157,
                99.51581,
                104.77029,
                99.55855
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                93.406727,
                106.33687,
                82.120645,
                113.087407,
                70.912043,
                119.81016
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                70.675632,
                119.955797,
                70.520616,
                120.202065,
                70.492367,
                120.47688
            ),
            _0x1dd3fb['bezierCurveTo'](
                69.98445,
                125.994413,
                69.575535,
                131.2662,
                69.26562,
                136.29224
            ),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                69.26562,
                136.304973,
                69.259193,
                136.31134,
                69.24634,
                136.31134
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](69.072006, 136.31134),
            _0x1dd3fb['bezierCurveTo'](
                68.977307,
                136.31534,
                68.925654,
                136.270467,
                68.917049,
                136.17672
            ),
            _0x1dd3fb[_0x20e2ca(0x356)](67.600227, 120.50885),
            _0x1dd3fb[_0x20e2ca(0x22d)](
                67.580353,
                120.267319,
                67.445899,
                120.050402,
                67.238675,
                119.92555
            ),
            _0x1dd3fb[_0x20e2ca(0x2b4)](),
            _0x1dd3fb[_0x20e2ca(0x24f)](),
            _0x434d96['_baseTexture'][_0x20e2ca(0x20d)](),
            (_0x434d96[_0x20e2ca(0x2ce)] = ![]),
            (_0x434d96[_0x20e2ca(0x43b)] = !![]),
            (this[_0x20e2ca(0x26a)][_0x9935c7] = _0x434d96),
            this[_0x20e2ca(0x26a)][_0x9935c7]
        );
    }),
    (ImageManager['createDiceGraphic_D12'] = function (_0x4e9482) {
        const _0xda032f = _0x457895,
            _0x4bb3db = _0xda032f(0x1f7)[_0xda032f(0x253)](_0x4e9482);
        this[_0xda032f(0x174)] = this[_0xda032f(0x174)] || {};
        if (this[_0xda032f(0x174)][_0x4bb3db] !== undefined)
            return this[_0xda032f(0x174)][_0x4bb3db];
        const _0x47c816 = ImageManager[_0xda032f(0x114)],
            _0x2512a3 = ColorManager[_0xda032f(0x364)](_0x4e9482, _0x47c816[_0xda032f(0x1a4)]),
            _0x3cd056 = ColorManager['darkenColor'](_0x4e9482, _0x47c816[_0xda032f(0x2b1)]),
            _0x533d5e = ColorManager[_0xda032f(0x364)](_0x4e9482, _0x47c816[_0xda032f(0x397)]),
            _0xc63de = ColorManager[_0xda032f(0x364)](_0x4e9482, _0x47c816[_0xda032f(0x33a)]),
            _0x43baba = new Bitmap(0x8a, 0x90),
            _0x564cf2 = _0x43baba['_context'];
        return (
            _0x564cf2[_0xda032f(0x10f)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x3cd056),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.070004),
            (_0x564cf2[_0xda032f(0x403)] = _0xda032f(0x236)),
            (_0x564cf2[_0xda032f(0x36e)] = _0xda032f(0x254)),
            _0x564cf2[_0xda032f(0x292)](68.532932, 2.829341),
            _0x564cf2['lineTo'](27.035929, 15.404191),
            _0x564cf2['lineTo'](3.143712, 49.98503),
            _0x564cf2['lineTo'](3.143712, 93.053891),
            _0x564cf2[_0xda032f(0x356)](26.407185, 127.9491),
            _0x564cf2['lineTo'](67.275447, 142.72455),
            _0x564cf2['lineTo'](108.77245, 129.20659),
            _0x564cf2[_0xda032f(0x356)](135.17964, 94.311376),
            _0x564cf2[_0xda032f(0x356)](134.86527, 49.670657),
            _0x564cf2['lineTo'](109.71557, 15.08982),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2['fillStyle'] = _0x533d5e),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.070004),
            (_0x564cf2['lineCap'] = _0xda032f(0x236)),
            (_0x564cf2[_0xda032f(0x36e)] = _0xda032f(0x254)),
            _0x564cf2[_0xda032f(0x292)](68.847305, 2.829341),
            _0x564cf2[_0xda032f(0x356)](27.664672, 15.718563),
            _0x564cf2[_0xda032f(0x356)](1.848088, 50.002463),
            _0x564cf2[_0xda032f(0x356)](27.979042, 59.101796),
            _0x564cf2['lineTo'](69.901565, 29.293736),
            _0x564cf2[_0xda032f(0x356)](109.34399, 59.212943),
            _0x564cf2['lineTo'](94.052578, 107.68169),
            _0x564cf2['lineTo'](110.32524, 128.50484),
            _0x564cf2[_0xda032f(0x356)](134.5509, 93.053891),
            _0x564cf2[_0xda032f(0x356)](133.92215, 50.2994),
            _0x564cf2[_0xda032f(0x356)](110.34431, 15.404191),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x2ef)] = _0xc63de),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.070004),
            (_0x564cf2['lineCap'] = _0xda032f(0x236)),
            (_0x564cf2[_0xda032f(0x36e)] = 'miter'),
            _0x564cf2[_0xda032f(0x292)](69.911477, 28.453639),
            _0x564cf2[_0xda032f(0x356)](27.675608, 57.852024),
            _0x564cf2[_0xda032f(0x356)](42.680456, 106.64557),
            _0x564cf2['lineTo'](94.141528, 107.70147),
            _0x564cf2['lineTo'](110.25785, 59.130215),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2['moveTo'](28.253509, 12.808104),
            _0x564cf2[_0xda032f(0x22d)](
                40.529028,
                8.788817,
                53.209656,
                4.672435,
                66.295394,
                0.458958
            ),
            _0x564cf2[_0xda032f(0x22d)](
                68.169311,
                -0.150709,
                69.696963,
                -0.164256,
                71.720249,
                0.486046
            ),
            _0x564cf2['bezierCurveTo'](
                84.552511,
                4.622748,
                97.377985,
                8.782032,
                110.19667,
                12.963897
            ),
            _0x564cf2[_0xda032f(0x22d)](
                111.101943,
                13.261956,
                112.004953,
                14.025168,
                112.9057,
                15.253533
            ),
            _0x564cf2[_0xda032f(0x22d)](
                120.695587,
                25.88432,
                128.469627,
                36.528655,
                136.22782,
                47.186538
            ),
            _0x564cf2[_0xda032f(0x22d)](
                137.66721,
                49.157793,
                138.00669,
                50.451642,
                137.9999,
                53.059662
            ),
            _0x564cf2[_0xda032f(0x22d)](
                137.990833,
                65.921377,
                137.984067,
                78.780835,
                137.9796,
                91.638035
            ),
            _0x564cf2[_0xda032f(0x22d)](
                137.9726,
                94.611853,
                136.86612,
                95.905704,
                134.86999,
                98.64243
            ),
            _0x564cf2[_0xda032f(0x22d)](
                127.55085,
                108.677097,
                120.240763,
                118.718537,
                112.93973,
                128.76675
            ),
            _0x564cf2[_0xda032f(0x22d)](
                112.084237,
                129.949957,
                110.952643,
                130.76962,
                109.54495,
                131.22574
            ),
            _0x564cf2[_0xda032f(0x22d)](
                96.866582,
                135.34438,
                84.195005,
                139.46528,
                71.53022,
                143.58844
            ),
            _0x564cf2['bezierCurveTo'](
                69.859988,
                144.13036,
                68.203337,
                144.137093,
                66.560266,
                143.60864
            ),
            _0x564cf2[_0xda032f(0x22d)](
                53.791373,
                139.471933,
                41.022479,
                135.337487,
                28.253586,
                131.2053
            ),
            _0x564cf2[_0xda032f(0x22d)](
                26.732725,
                130.7108,
                25.564923,
                129.54566,
                24.526122,
                128.12987
            ),
            _0x564cf2[_0xda032f(0x22d)](
                15.161074,
                115.304297,
                7.377985,
                104.612552,
                1.176856,
                96.054636
            ),
            _0x564cf2[_0xda032f(0x22d)](
                0.420953,
                95.011427,
                0.040738,
                93.877899,
                0.036211,
                92.65405
            ),
            _0x564cf2[_0xda032f(0x22d)](
                -0.000006,
                78.771706,
                -0.009056,
                65.108391,
                0.009061,
                51.664104
            ),
            _0x564cf2[_0xda032f(0x22d)](
                0.013509,
                50.182839,
                0.552147,
                48.710607,
                1.624975,
                47.247406
            ),
            _0x564cf2['bezierCurveTo'](
                9.279068,
                36.801779,
                17.001056,
                26.19809,
                24.790941,
                15.436337
            ),
            _0x564cf2[_0xda032f(0x22d)](
                25.741479,
                14.126682,
                26.895703,
                13.250568,
                28.253614,
                12.807996
            ),
            _0x564cf2[_0xda032f(0x292)](30.127426, 126.53809),
            _0x564cf2[_0xda032f(0x356)](67.992782, 138.8195),
            _0x564cf2['bezierCurveTo'](
                68.611049,
                139.017805,
                69.278067,
                139.017805,
                69.90065,
                138.8195
            ),
            _0x564cf2[_0xda032f(0x356)](107.55552, 126.61938),
            _0x564cf2[_0xda032f(0x22d)](
                108.151101,
                126.425483,
                108.669361,
                126.048349,
                109.03565,
                125.5423
            ),
            _0x564cf2[_0xda032f(0x356)](132.5411, 93.297694),
            _0x564cf2[_0xda032f(0x22d)](
                132.784022,
                92.963133,
                132.914722,
                92.560065,
                132.91452,
                92.1461
            ),
            _0x564cf2[_0xda032f(0x356)](132.90752, 51.874208),
            _0x564cf2[_0xda032f(0x22d)](
                132.90752,
                51.445183,
                132.783043,
                51.059061,
                132.53409,
                50.715842
            ),
            _0x564cf2[_0xda032f(0x22d)](
                125.137923,
                40.545693,
                117.71011,
                30.3349,
                110.25065,
                20.083461
            ),
            _0x564cf2[_0xda032f(0x22d)](
                109.77538,
                19.426377,
                108.7909,
                17.780275,
                107.95578,
                17.509313
            ),
            _0x564cf2[_0xda032f(0x22d)](
                95.178057,
                13.359061,
                82.468118,
                9.233647,
                69.825963,
                5.133071
            ),
            _0x564cf2[_0xda032f(0x22d)](
                69.261362,
                4.948454,
                68.652437,
                4.948454,
                68.087836,
                5.133071
            ),
            _0x564cf2[_0xda032f(0x356)](30.439745, 17.33996),
            _0x564cf2[_0xda032f(0x22d)](
                29.78194,
                17.551412,
                29.206865,
                17.966324,
                28.796674,
                18.525424
            ),
            _0x564cf2['lineTo'](5.413442, 50.634554),
            _0x564cf2[_0xda032f(0x22d)](
                5.087021,
                51.079141,
                4.911036,
                51.617755,
                4.911014,
                52.172269
            ),
            _0x564cf2[_0xda032f(0x22d)](
                4.906561,
                64.695281,
                4.904334,
                77.33571,
                4.904334,
                90.093556
            ),
            _0x564cf2['bezierCurveTo'](4.904334, 91.109667, 4.788909, 92.47803, 5.352445, 93.25705),
            _0x564cf2['bezierCurveTo'](
                13.296227,
                104.158797,
                21.176639,
                114.979257,
                28.993682,
                125.71843
            ),
            _0x564cf2[_0xda032f(0x22d)](
                29.283369,
                126.115843,
                29.66132,
                126.389063,
                30.127537,
                126.53809
            ),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2['moveTo'](68.821109, 7.165295),
            _0x564cf2[_0xda032f(0x22d)](
                68.830176,
                7.074968,
                68.879967,
                7.029805,
                68.970483,
                7.029805
            ),
            _0x564cf2[_0xda032f(0x356)](68.977183, 7.029805),
            _0x564cf2[_0xda032f(0x22d)](
                69.058648,
                7.029805,
                69.101648,
                7.070453,
                69.106182,
                7.151751
            ),
            _0x564cf2[_0xda032f(0x22d)](
                69.64482,
                13.975505,
                70.192511,
                20.69539,
                70.749256,
                27.311407
            ),
            _0x564cf2['bezierCurveTo'](
                70.780943,
                27.681722,
                70.943892,
                27.984297,
                71.238103,
                28.219132
            ),
            _0x564cf2['lineTo'](105.2402, 54.976739),
            _0x564cf2[_0xda032f(0x22d)](
                105.317147,
                55.039959,
                105.326213,
                55.109957,
                105.2674,
                55.186734
            ),
            _0x564cf2[_0xda032f(0x356)](105.15196, 55.349312),
            _0x564cf2[_0xda032f(0x22d)](
                105.138493,
                55.367372,
                105.12266,
                55.369605,
                105.10446,
                55.356012
            ),
            _0x564cf2['bezierCurveTo'](
                92.933075,
                47.10975,
                80.725465,
                38.947011,
                68.48163,
                30.867794
            ),
            _0x564cf2[_0xda032f(0x22d)](
                67.534853,
                30.240487,
                67.004401,
                29.148276,
                67.09656,
                28.015907
            ),
            _0x564cf2[_0xda032f(0x22d)](
                67.644251,
                21.250861,
                68.219101,
                14.300657,
                68.821109,
                7.165295
            ),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2['beginPath'](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2[_0xda032f(0x292)](24.043983, 59.210536),
            _0x564cf2[_0xda032f(0x22d)](
                18.318124,
                56.731223,
                12.605845,
                54.342231,
                6.907144,
                52.043561
            ),
            _0x564cf2['bezierCurveTo'](
                6.821144,
                52.007427,
                6.791719,
                51.946458,
                6.818869,
                51.860654
            ),
            _0x564cf2[_0xda032f(0x356)](6.825549, 51.853954),
            _0x564cf2[_0xda032f(0x22d)](
                6.857231,
                51.750091,
                6.925123,
                51.711704,
                7.029223,
                51.738792
            ),
            _0x564cf2[_0xda032f(0x22d)](
                13.569828,
                53.314893,
                20.17154,
                54.875188,
                26.834359,
                56.419678
            ),
            _0x564cf2[_0xda032f(0x22d)](
                27.241793,
                56.514746,
                27.669203,
                56.438772,
                28.015742,
                56.209683
            ),
            _0x564cf2[_0xda032f(0x356)](63.402901, 32.703679),
            _0x564cf2['bezierCurveTo'](
                63.479851,
                32.654012,
                63.545484,
                32.667566,
                63.5998,
                32.744339
            ),
            _0x564cf2[_0xda032f(0x356)](63.708425, 32.893373),
            _0x564cf2[_0xda032f(0x22d)](
                63.717492,
                32.90686,
                63.715258,
                32.91815,
                63.701725,
                32.927243
            ),
            _0x564cf2[_0xda032f(0x22d)](
                52.340533,
                41.77419,
                41.022342,
                50.684361,
                29.747151,
                59.657756
            ),
            _0x564cf2['bezierCurveTo'](
                27.920761,
                61.114182,
                25.836367,
                59.982911,
                24.043924,
                59.210666
            ),
            _0x564cf2['fill'](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2[_0xda032f(0x292)](111.48669, 60.511159),
            _0x564cf2[_0xda032f(0x22d)](
                106.322097,
                74.140603,
                101.241243,
                87.90327,
                96.244128,
                101.79916
            ),
            _0x564cf2['bezierCurveTo'](
                96.239661,
                101.82176,
                96.226078,
                101.828493,
                96.203378,
                101.81936
            ),
            _0x564cf2[_0xda032f(0x356)](96.026853, 101.76516),
            _0x564cf2[_0xda032f(0x22d)](
                95.949904,
                101.738027,
                95.922744,
                101.688347,
                95.945373,
                101.61612
            ),
            _0x564cf2[_0xda032f(0x356)](108.03079, 58.776886),
            _0x564cf2[_0xda032f(0x22d)](
                108.324507,
                57.73976,
                109.15528,
                56.941965,
                110.20345,
                56.690471
            ),
            _0x564cf2[_0xda032f(0x356)](130.70112, 51.833456),
            _0x564cf2['bezierCurveTo'](
                130.778067,
                51.815396,
                130.827857,
                51.842493,
                130.85049,
                51.914746
            ),
            _0x564cf2['lineTo'](130.91159, 52.124741),
            _0x564cf2[_0xda032f(0x22d)](
                130.920657,
                52.151848,
                130.91159,
                52.169914,
                130.88439,
                52.178941
            ),
            _0x564cf2[_0xda032f(0x22d)](
                124.57011,
                54.622124,
                118.262613,
                57.232401,
                111.9619,
                60.009774
            ),
            _0x564cf2['bezierCurveTo'](
                111.735587,
                60.10913,
                111.577167,
                60.276224,
                111.48664,
                60.511056
            ),
            _0x564cf2['fill'](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2['moveTo'](29.536737, 63.593364),
            _0x564cf2[_0xda032f(0x22d)](
                34.850696,
                77.647317,
                40.053758,
                91.687722,
                45.145924,
                105.71458
            ),
            _0x564cf2[_0xda032f(0x22d)](
                45.50686,
                106.702147,
                45.304356,
                107.808202,
                44.616339,
                108.60711
            ),
            _0x564cf2[_0xda032f(0x22d)](
                39.981336,
                114.012823,
                35.28749,
                119.48402,
                30.5348,
                125.0207
            ),
            _0x564cf2[_0xda032f(0x22d)](
                30.471433,
                125.097473,
                30.399009,
                125.106507,
                30.317526,
                125.0478
            ),
            _0x564cf2[_0xda032f(0x356)](30.310826, 125.0408),
            _0x564cf2['bezierCurveTo'](
                30.229361,
                124.982067,
                30.215787,
                124.909807,
                30.270106,
                124.82402
            ),
            _0x564cf2[_0xda032f(0x22d)](
                33.936467,
                118.894447,
                37.566616,
                112.982933,
                41.160553,
                107.08948
            ),
            _0x564cf2[_0xda032f(0x22d)](
                41.350922,
                106.773789,
                41.402463,
                106.391764,
                41.303127,
                106.03273
            ),
            _0x564cf2['bezierCurveTo'](
                37.338042,
                91.997061,
                33.348053,
                77.848381,
                29.333162,
                63.586689
            ),
            _0x564cf2['bezierCurveTo'](
                29.319642,
                63.550556,
                29.330959,
                63.527972,
                29.367112,
                63.518939
            ),
            _0x564cf2[_0xda032f(0x356)](29.373812, 63.512239),
            _0x564cf2[_0xda032f(0x22d)](
                29.450761,
                63.489659,
                29.505078,
                63.516756,
                29.536761,
                63.593529
            ),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x355)](),
            (_0x564cf2[_0xda032f(0x3e4)] = 0.179435),
            (_0x564cf2[_0xda032f(0x2ef)] = _0x2512a3),
            _0x564cf2[_0xda032f(0x292)](49.579507, 106.77133),
            _0x564cf2[_0xda032f(0x22d)](
                50.439517,
                106.613277,
                51.313106,
                106.5207,
                52.200275,
                106.4936
            ),
            _0x564cf2[_0xda032f(0x22d)](
                65.770334,
                106.114247,
                79.50787,
                105.597157,
                93.412881,
                104.94233
            ),
            _0x564cf2[_0xda032f(0x22d)](
                94.9609,
                104.87463,
                96.054018,
                105.76877,
                96.834818,
                107.10326
            ),
            _0x564cf2[_0xda032f(0x22d)](
                100.292966,
                113.001227,
                103.84843,
                118.817903,
                107.50121,
                124.55329
            ),
            _0x564cf2[_0xda032f(0x22d)](
                107.514743,
                124.580357,
                107.51021,
                124.60069,
                107.48761,
                124.61429
            ),
            _0x564cf2[_0xda032f(0x356)](107.33824, 124.72267),
            _0x564cf2[_0xda032f(0x22d)](
                107.27034,
                124.772337,
                107.20923,
                124.765603,
                107.15491,
                124.70247
            ),
            _0x564cf2[_0xda032f(0x356)](93.731956, 109.09492),
            _0x564cf2[_0xda032f(0x22d)](
                93.537347,
                108.869067,
                93.290671,
                108.749367,
                92.99193,
                108.73582
            ),
            _0x564cf2[_0xda032f(0x22d)](
                79.707031,
                108.202927,
                66.791032,
                107.708417,
                54.243931,
                107.25229
            ),
            _0x564cf2[_0xda032f(0x22d)](
                52.560122,
                107.18909,
                50.935155,
                107.049093,
                49.369031,
                106.8323
            ),
            _0x564cf2['bezierCurveTo'](
                49.361744,
                106.8323,
                49.355011,
                106.828421,
                49.351367,
                106.822125
            ),
            _0x564cf2[_0xda032f(0x22d)](
                49.347724,
                106.815829,
                49.347724,
                106.808071,
                49.351367,
                106.801775
            ),
            _0x564cf2['bezierCurveTo'](
                49.355011,
                106.795479,
                49.361744,
                106.7916,
                49.369031,
                106.7916
            ),
            _0x564cf2['bezierCurveTo'](
                49.441447,
                106.7916,
                49.511605,
                106.784867,
                49.579505,
                106.7714
            ),
            _0x564cf2[_0xda032f(0x2b4)](),
            _0x564cf2[_0xda032f(0x24f)](),
            _0x43baba[_0xda032f(0x1ca)][_0xda032f(0x20d)](),
            (_0x43baba[_0xda032f(0x2ce)] = ![]),
            (_0x43baba['_smooth'] = !![]),
            (this[_0xda032f(0x174)][_0x4bb3db] = _0x43baba),
            this[_0xda032f(0x174)][_0x4bb3db]
        );
    }),
    (ImageManager[_0x457895(0x3d2)] = function (_0x30d403) {
        const _0x1f1076 = _0x457895,
            _0x53830c = 'color-%1'['format'](_0x30d403);
        this[_0x1f1076(0x426)] = this['_cache_createDiceGraphic_D20'] || {};
        if (this[_0x1f1076(0x426)][_0x53830c] !== undefined) {
            if (_0x1f1076(0x30a) === _0x1f1076(0x2db))
                _0x12c869 += _0x310dae[_0x1f1076(0x43f)](_0x457cbf[_0x1f1076(0x373)] || 0x0);
            else return this[_0x1f1076(0x426)][_0x53830c];
        }
        const _0x364826 = ImageManager[_0x1f1076(0x114)],
            _0x4fd43e = ColorManager[_0x1f1076(0x364)](_0x30d403, _0x364826[_0x1f1076(0x1a4)]),
            _0x5a2383 = ColorManager['darkenColor'](_0x30d403, _0x364826[_0x1f1076(0x2b1)]),
            _0x2fd23d = ColorManager[_0x1f1076(0x364)](_0x30d403, _0x364826[_0x1f1076(0x397)]),
            _0x30bd15 = ColorManager[_0x1f1076(0x364)](_0x30d403, _0x364826[_0x1f1076(0x33a)]),
            _0x235fe4 = new Bitmap(0x80, 0x90),
            _0x2d954d = _0x235fe4[_0x1f1076(0x1c9)];
        return (
            _0x2d954d[_0x1f1076(0x10f)](),
            _0x2d954d['beginPath'](),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x5a2383),
            (_0x2d954d['lineWidth'] = 0.070004),
            (_0x2d954d[_0x1f1076(0x403)] = _0x1f1076(0x236)),
            (_0x2d954d[_0x1f1076(0x36e)] = _0x1f1076(0x254)),
            _0x2d954d[_0x1f1076(0x292)](64.242978, 2.445235),
            _0x2d954d['lineTo'](3.556705, 36.678518),
            _0x2d954d[_0x1f1076(0x356)](2.22294, 107.36802),
            _0x2d954d['lineTo'](63.798391, 142.04589),
            _0x2d954d[_0x1f1076(0x356)](125.59614, 107.81261),
            _0x2d954d[_0x1f1076(0x356)](124.70696, 36.23393),
            _0x2d954d[_0x1f1076(0x2b4)](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x2fd23d),
            (_0x2d954d[_0x1f1076(0x3e4)] = 0.070004),
            (_0x2d954d[_0x1f1076(0x403)] = _0x1f1076(0x236)),
            (_0x2d954d[_0x1f1076(0x36e)] = _0x1f1076(0x254)),
            _0x2d954d[_0x1f1076(0x292)](64.242978, 20.006464),
            _0x2d954d['lineTo'](5.446204, 38.915352),
            _0x2d954d[_0x1f1076(0x356)](16.894348, 100.6992),
            _0x2d954d[_0x1f1076(0x356)](64.020685, 138.0446),
            _0x2d954d[_0x1f1076(0x356)](110.92473, 100.81035),
            _0x2d954d[_0x1f1076(0x356)](122.03943, 38.234576),
            _0x2d954d[_0x1f1076(0x2b4)](),
            _0x2d954d['beginPath'](),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x30bd15),
            (_0x2d954d['lineWidth'] = 0.070004),
            (_0x2d954d[_0x1f1076(0x403)] = _0x1f1076(0x236)),
            (_0x2d954d[_0x1f1076(0x36e)] = 'miter'),
            _0x2d954d['moveTo'](63.974551, 20.748503),
            _0x2d954d[_0x1f1076(0x356)](16.504491, 100.75598),
            _0x2d954d[_0x1f1076(0x356)](110.65868, 100.5988),
            _0x2d954d[_0x1f1076(0x2b4)](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d[_0x1f1076(0x3e4)] = 0.171604),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x4fd43e),
            _0x2d954d[_0x1f1076(0x292)](59.025664, 142.33392),
            _0x2d954d[_0x1f1076(0x22d)](
                40.596453,
                131.78455,
                22.147669,
                121.26744,
                3.698885,
                110.74387
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                2.268039,
                109.93139,
                1.241657,
                108.835183,
                0.619739,
                107.45525
            ),
            _0x2d954d['bezierCurveTo'](
                0.215275,
                106.56539,
                0.013043,
                105.153217,
                0.013043,
                103.21873
            ),
            _0x2d954d[_0x1f1076(0x22d)](0.013043, 82.02973, 0.008695, 60.840728, 0x0, 39.651723),
            _0x2d954d['bezierCurveTo'](0x0, 36.926258, 1.217741, 34.867113, 3.653224, 33.474286),
            _0x2d954d[_0x1f1076(0x22d)](
                23.02836,
                22.387567,
                42.075141,
                11.498594,
                60.793568,
                0.807368
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                62.863729,
                -0.374814,
                65.14047,
                -0.258746,
                67.623793,
                1.155574
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                86.955442,
                12.139122,
                105.963085,
                22.955014,
                124.64672,
                33.60325
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                127.15831,
                35.03477,
                128.0129,
                37.678554,
                127.99985,
                40.496442
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                127.904177,
                62.386162,
                127.878077,
                83.869638,
                127.92155,
                104.94687
            ),
            _0x2d954d['bezierCurveTo'](
                127.92755,
                108.10007,
                125.91227,
                109.75728,
                123.05493,
                111.38869
            ),
            _0x2d954d['bezierCurveTo'](
                104.632258,
                121.907963,
                86.207403,
                132.42294,
                67.780365,
                142.93362
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                64.720788,
                144.68109,
                62.183104,
                144.13944,
                59.025675,
                142.33392
            ),
            _0x2d954d['moveTo'](123.21152, 39.729104),
            _0x2d954d['bezierCurveTo'](
                123.210686,
                38.859111,
                122.740792,
                38.055514,
                121.97855,
                37.62052
            ),
            _0x2d954d[_0x1f1076(0x356)](65.210052, 5.211534),
            _0x2d954d[_0x1f1076(0x22d)](
                64.447092,
                4.776126,
                63.507086,
                4.776126,
                62.744126,
                5.211534
            ),
            _0x2d954d[_0x1f1076(0x356)](5.962579, 37.626971),
            _0x2d954d[_0x1f1076(0x22d)](
                5.200339,
                38.061967,
                4.730448,
                38.865565,
                4.729616,
                39.735557
            ),
            _0x2d954d['lineTo'](4.729616, 104.37943),
            _0x2d954d['bezierCurveTo'](
                4.73045,
                105.24942,
                5.200341,
                106.053015,
                5.962579,
                106.48801
            ),
            _0x2d954d[_0x1f1076(0x356)](62.744126, 138.89055),
            _0x2d954d[_0x1f1076(0x22d)](
                63.507086,
                139.325957,
                64.447092,
                139.325957,
                65.210052,
                138.89055
            ),
            _0x2d954d[_0x1f1076(0x356)](122.00465, 106.48156),
            _0x2d954d['bezierCurveTo'](
                122.766887,
                106.046564,
                123.236777,
                105.242969,
                123.23761,
                104.37298
            ),
            _0x2d954d[_0x1f1076(0x2b4)](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d['lineWidth'] = 0.171604),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x4fd43e),
            _0x2d954d['moveTo'](67.60422, 23.086129),
            _0x2d954d[_0x1f1076(0x22d)](
                67.325879,
                23.090462,
                67.254118,
                23.21298,
                67.388938,
                23.453681
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                80.910215,
                47.381048,
                94.307546,
                71.198795,
                107.58093,
                94.906922
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                107.61573,
                94.967095,
                107.602697,
                95.016532,
                107.54183,
                95.055231
            ),
            _0x2d954d[_0x1f1076(0x356)](107.54183, 95.055231),
            _0x2d954d[_0x1f1076(0x22d)](
                107.480963,
                95.089624,
                107.433127,
                95.076738,
                107.39832,
                95.016571
            ),
            _0x2d954d['bezierCurveTo'](
                92.733193,
                70.908633,
                78.083307,
                46.749118,
                63.448663,
                22.538026
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                63.339938,
                22.353176,
                63.181197,
                22.290843,
                62.97244,
                22.351027
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                44.828089,
                27.625709,
                26.666343,
                32.881046,
                8.487204,
                38.117038
            ),
            _0x2d954d['bezierCurveTo'](
                8.404571,
                38.142838,
                8.350209,
                38.114895,
                8.324117,
                38.033208
            ),
            _0x2d954d[_0x1f1076(0x356)](8.324117, 38.026408),
            _0x2d954d[_0x1f1076(0x22d)](
                8.298021,
                37.944743,
                8.326289,
                37.89101,
                8.408922,
                37.865208
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                26.022684,
                31.825333,
                43.934356,
                25.695181,
                62.143939,
                19.474753
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                62.444021,
                19.37588,
                62.607111,
                19.169536,
                62.633208,
                18.855719
            ),
            _0x2d954d[_0x1f1076(0x356)](63.75527, 6.4686),
            _0x2d954d[_0x1f1076(0x22d)](
                63.759537,
                6.404116,
                63.794333,
                6.371874,
                63.85966,
                6.371874
            ),
            _0x2d954d[_0x1f1076(0x356)](64.048864, 6.365414),
            _0x2d954d['bezierCurveTo'](
                64.092377,
                6.365414,
                64.116301,
                6.38476,
                64.120634,
                6.423452
            ),
            _0x2d954d[_0x1f1076(0x356)](65.360123, 18.765433),
            _0x2d954d[_0x1f1076(0x22d)](
                65.395549,
                19.118212,
                65.637947,
                19.41834,
                65.979866,
                19.532777
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                83.567532,
                25.400698,
                101.189993,
                31.27507,
                118.84725,
                37.155892
            ),
            _0x2d954d['bezierCurveTo'](
                118.925537,
                37.181665,
                118.953803,
                37.233256,
                118.93205,
                37.310664
            ),
            _0x2d954d[_0x1f1076(0x356)](118.92605, 37.317264),
            _0x2d954d[_0x1f1076(0x22d)](
                118.904383,
                37.398935,
                118.85002,
                37.426882,
                118.76296,
                37.401104
            ),
            _0x2d954d['bezierCurveTo'](
                102.058039,
                32.723967,
                85.127026,
                27.967299,
                67.969919,
                23.131099
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                67.848142,
                23.096706,
                67.726366,
                23.081659,
                67.604589,
                23.085959
            ),
            _0x2d954d['fill'](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d[_0x1f1076(0x3e4)] = 0.171604),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x4fd43e),
            _0x2d954d['moveTo'](17.750752, 96.183678),
            _0x2d954d['bezierCurveTo'](
                17.811639,
                96.458807,
                17.913842,
                96.476003,
                18.057363,
                96.235268
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                31.343792,
                74.255287,
                44.701981,
                52.268853,
                58.13193,
                30.275964
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                58.16673,
                30.215784,
                58.214566,
                30.202884,
                58.275439,
                30.237264
            ),
            _0x2d954d[_0x1f1076(0x356)](58.281839, 30.243864),
            _0x2d954d['bezierCurveTo'](
                58.355767,
                30.282551,
                58.370987,
                30.338433,
                58.327499,
                30.411512
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                45.323852,
                53.612344,
                32.29189,
                76.830387,
                19.231613,
                100.06564
            ),
            _0x2d954d['bezierCurveTo'](
                19.116793,
                100.267839,
                19.16332,
                100.522137,
                19.342512,
                100.67178
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                33.916271,
                112.824607,
                48.509598,
                125.020427,
                63.122494,
                137.25924
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                63.170341,
                137.30224,
                63.174624,
                137.34737,
                63.135344,
                137.39463
            ),
            _0x2d954d[_0x1f1076(0x356)](63.135344, 137.39463),
            _0x2d954d[_0x1f1076(0x22d)](
                63.096191,
                137.44623,
                63.050534,
                137.452663,
                62.998374,
                137.41393
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                47.698309,
                125.880163,
                32.385205,
                114.292643,
                17.059061,
                102.65137
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                16.859006,
                102.500923,
                16.637203,
                102.460093,
                16.393652,
                102.52888
            ),
            _0x2d954d[_0x1f1076(0x356)](7.091036, 105.1855),
            _0x2d954d[_0x1f1076(0x22d)](
                5.603652,
                105.61108,
                5.536241,
                105.447723,
                6.888805,
                104.69543
            ),
            _0x2d954d[_0x1f1076(0x356)](15.082465, 100.14295),
            _0x2d954d[_0x1f1076(0x22d)](
                15.291222,
                100.022563,
                15.37603,
                99.844151,
                15.336889,
                99.607715
            ),
            _0x2d954d[_0x1f1076(0x356)](5.792966, 41.760309),
            _0x2d954d[_0x1f1076(0x22d)](
                5.784273,
                41.687238,
                5.81689,
                41.644253,
                5.890818,
                41.631352
            ),
            _0x2d954d[_0x1f1076(0x356)](6.086525, 41.592652),
            _0x2d954d['bezierCurveTo'](
                6.103936,
                41.588385,
                6.114808,
                41.594812,
                6.119142,
                41.611932
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                9.872396,
                59.88202,
                13.749598,
                78.072581,
                17.750749,
                96.183614
            ),
            _0x2d954d['fill'](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d[_0x1f1076(0x3e4)] = 0.171604),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x4fd43e),
            _0x2d954d['moveTo'](112.75416, 100.07843),
            _0x2d954d['bezierCurveTo'](
                115.615853,
                101.776477,
                118.494943,
                103.47452,
                121.39143,
                105.17256
            ),
            _0x2d954d['bezierCurveTo'](
                121.447963,
                105.20696,
                121.471897,
                105.273593,
                121.46323,
                105.37246
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                121.450163,
                105.50144,
                121.38057,
                105.546567,
                121.25445,
                105.50784
            ),
            _0x2d954d['lineTo'](111.51469, 102.56098),
            _0x2d954d[_0x1f1076(0x22d)](
                111.298152,
                102.495879,
                111.062316,
                102.539122,
                110.8819,
                102.67701
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                95.616641,
                114.270997,
                80.405746,
                125.809093,
                65.249215,
                137.2913
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                65.197008,
                137.3343,
                65.149172,
                137.327867,
                65.105706,
                137.272
            ),
            _0x2d954d[_0x1f1076(0x356)](65.099306, 137.272),
            _0x2d954d[_0x1f1076(0x22d)](
                65.060153,
                137.2204,
                65.066703,
                137.170963,
                65.118956,
                137.12369
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                79.788394,
                124.807497,
                94.433909,
                112.532143,
                109.0555,
                100.29763
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                109.334214,
                100.067917,
                109.525292,
                99.749342,
                109.59696,
                99.394878
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                113.4198,
                80.454168,
                117.303523,
                61.444678,
                121.24813,
                42.366409
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                121.26553,
                42.28903,
                121.315547,
                42.25679,
                121.39818,
                42.269689
            ),
            _0x2d954d[_0x1f1076(0x356)](121.39818, 42.269689),
            _0x2d954d[_0x1f1076(0x22d)](
                121.476467,
                42.282542,
                121.5091,
                42.32767,
                121.49608,
                42.405072
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                118.56046,
                61.513439,
                115.59222,
                80.621803,
                112.59136,
                99.730165
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                112.568158,
                99.869788,
                112.633014,
                100.008271,
                112.75444,
                100.07838
            ),
            _0x2d954d['fill'](),
            _0x2d954d[_0x1f1076(0x355)](),
            (_0x2d954d['lineWidth'] = 0.171604),
            (_0x2d954d[_0x1f1076(0x2ef)] = _0x4fd43e),
            _0x2d954d['moveTo'](103.73853, 100.7426),
            _0x2d954d['bezierCurveTo'](
                103.73853,
                100.807067,
                103.705897,
                100.841447,
                103.64063,
                100.84574
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                77.154751,
                102.079507,
                50.658001,
                102.075207,
                24.15038,
                100.83284
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                24.093847,
                100.82884,
                24.06558,
                100.79874,
                24.06558,
                100.74254
            ),
            _0x2d954d[_0x1f1076(0x356)](24.06558, 100.74254),
            _0x2d954d[_0x1f1076(0x22d)](
                24.06558,
                100.678073,
                24.096023,
                100.64369,
                24.15691,
                100.63939
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                50.625389,
                99.470108,
                77.113442,
                99.463675,
                103.62107,
                100.62009
            ),
            _0x2d954d[_0x1f1076(0x22d)](
                103.699357,
                100.62009,
                103.7385,
                100.65878,
                103.7385,
                100.73616
            ),
            _0x2d954d[_0x1f1076(0x2b4)](),
            _0x2d954d[_0x1f1076(0x24f)](),
            _0x235fe4[_0x1f1076(0x1ca)][_0x1f1076(0x20d)](),
            (_0x235fe4['_customModified'] = ![]),
            (_0x235fe4[_0x1f1076(0x43b)] = !![]),
            (this[_0x1f1076(0x426)][_0x53830c] = _0x235fe4),
            this['_cache_createDiceGraphic_D20'][_0x53830c]
        );
    }),
    (SoundManager['playDiceSound'] = function (_0x32b7e6) {
        const _0x59c29b = _0x457895,
            _0x16fb1f = VisuMZ[_0x59c29b(0x201)][_0x59c29b(0x42e)][_0x59c29b(0xf5)],
            _0x559559 = {
                name: _0x16fb1f[_0x59c29b(0x3ca)[_0x59c29b(0x253)](_0x32b7e6)] || '',
                volume: _0x16fb1f['%1_volume'[_0x59c29b(0x253)](_0x32b7e6)] || 0x0,
                pitch: _0x16fb1f[_0x59c29b(0x252)['format'](_0x32b7e6)] || 0x0,
                pan: _0x16fb1f[_0x59c29b(0x294)[_0x59c29b(0x253)](_0x32b7e6)] || 0x0,
            };
        if (_0x559559['name'] === '') return;
        AudioManager[_0x59c29b(0x2a6)](_0x559559);
    }),
    (SoundManager[_0x457895(0x267)] = function () {
        this['playDiceSound']('throw');
    }),
    (SoundManager['playDiceIncrement'] = function () {
        const _0xa08691 = _0x457895;
        this[_0xa08691(0x32b)](_0xa08691(0x1f4));
    }),
    (SoundManager[_0x457895(0x379)] = function () {
        const _0x2e4fb0 = _0x457895;
        this['playDiceSound'](_0x2e4fb0(0x284));
    }),
    (SoundManager[_0x457895(0x1dd)] = function (_0x3c58e9) {
        const _0x56a57f = _0x457895;
        this[_0x56a57f(0x32b)](_0x3c58e9 ? 'crit_success' : 'success');
    }),
    (SoundManager[_0x457895(0x16c)] = function (_0x2151f7) {
        const _0x451af8 = _0x457895;
        this[_0x451af8(0x32b)](_0x2151f7 ? _0x451af8(0x2a5) : _0x451af8(0x1ed));
    }),
    (TextManager[_0x457895(0x3dc)] = {
        choices: {
            roll:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x3ef)] ??
                _0x457895(0x25f),
            effects:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x2bf)] ??
                'View\x20Effects',
            bonus:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)][_0x457895(0x32d)] ??
                'Add\x20Bonus',
        },
        colors: {
            diceUpColor:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x427)] ??
                0x18,
            diceDownColor:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x3a2)] ?? 0x1b,
            rankUpColor:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['rankUpColor'] ?? 0x15,
            rankDownColor:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['rankDownColor'] ??
                0x4,
            modUpColor:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x138)] ??
                0x6,
            modDownColor:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)]['Vocab'][_0x457895(0x2c4)] ?? 0x2,
        },
        data: {
            count:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x38e)] ??
                '\x5cC[16]Dice\x20Count:',
            advantage:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x209)] ??
                '\x5cC[24]Advantage:',
            disadvantage:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x3f7)] ??
                _0x457895(0x437),
            rank:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x180)] ??
                _0x457895(0x385),
            modifier:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)][_0x457895(0x136)] ??
                _0x457895(0x1da),
            rank1:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['DataRank1'] ?? 'D4',
            rank2: VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)]['Vocab']['DataRank2'] ?? 'D6',
            rank3: VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)]['DataRank3'] ?? 'D8',
            rank4:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['DataRank4'] ?? 'D10',
            rank5:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x234)] ??
                'D12',
            rank6:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x2dc)] ??
                _0x457895(0x181),
        },
        bonus: {
            displayFmt:
                VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x1e6)][_0x457895(0x383)] ??
                _0x457895(0x3b4),
            costTextFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x387)] ??
                '%1\x20%2',
            usedUp: VisuMZ[_0x457895(0x201)]['Settings']['Vocab']['CostUsedUp'] ?? _0x457895(0x197),
            useTimesFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x3e3)] ??
                _0x457895(0x2b9),
            showUseMax1:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)]['Vocab']['CostShowMaxUse1'] ?? ![],
            unlimitedUse:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x2a1)] ?? '∞',
            variableCostFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x3fa)] ??
                _0x457895(0x357),
            itemCostFmt:
                VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x1e6)][_0x457895(0x1eb)] ??
                '(×%2-%1)',
            weaponCostFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['CostWeaponFormat'] ??
                _0x457895(0x357),
            armorCostFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x280)] ??
                _0x457895(0x357),
            skillUserFmt:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)][_0x457895(0x1ef)] ??
                _0x457895(0x3be),
            skillUserCostFmt:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)][_0x457895(0x145)] ?? '%1➤%2',
        },
        effectList: {
            displayFmt:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][
                    'DiceDisplayFormat'
                ] ?? _0x457895(0x3b4),
            effectTextFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x126)] ??
                _0x457895(0x416),
            orderFmt:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)]['Vocab'][_0x457895(0x392)] ??
                _0x457895(0x2e4),
            plusSign:
                VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x1e6)][_0x457895(0x240)] ?? !![],
            diceCountFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)]['DiceCount'] ??
                _0x457895(0x3b8),
            advantageFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x246)] ??
                _0x457895(0x3eb),
            disadvantageFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x1db)] ??
                'Dis%1',
            rankFmt:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)][_0x457895(0x32f)] ??
                _0x457895(0x241),
            modifierFmt:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0xf7)] ??
                _0x457895(0x24a),
        },
        subtitle: {
            total:
                VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x1e6)]['SubtitleTotal'] ??
                _0x457895(0x3d1),
            highest:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x275)] ??
                _0x457895(0x1dc),
            average:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][
                    'SubtitleAverage'
                ] ?? 'Roll\x20for\x20\x5cC[21]average\x5cC[0]\x20number',
            lowest:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab']['SubtitleLowest'] ??
                _0x457895(0x390),
            aboveTarget:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][
                    'SubtitleAboveTarget'
                ] ?? _0x457895(0x3d6),
            aboveEqualTarget:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Vocab'][_0x457895(0x42d)] ??
                _0x457895(0x296),
            belowEqualTarget:
                VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x1e6)][
                    'SubtitleBelowEqualTarget'
                ] ?? '\x5cC[5]Difficulty\x20Class:\x5cC[0]\x20At\x20most\x20\x5cC[24]%1\x5cC[0]',
            belowTarget:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][
                    'SubtitleBelowTarget'
                ] ?? _0x457895(0x218),
            criticalSuccess:
                VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x1e6)]['SubtitleCritSuccess'] ??
                '\x5cC[17]CRITICAL\x20SUCCESS!\x5cC[0]',
            criticalFailure:
                VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x1e6)][_0x457895(0x378)] ??
                _0x457895(0x1a3),
        },
    }),
    (TextManager['getDiceRollSubtitle'] = function () {
        const _0x113f95 = _0x457895,
            _0x3ff606 = SceneManager[_0x113f95(0x15c)] || {},
            _0x3e216d = _0x3ff606[_0x113f95(0xfa)]['trim']();
        switch (_0x3e216d) {
            case _0x113f95(0x359):
            case _0x113f95(0x235):
            case _0x113f95(0x25a):
            case _0x113f95(0x1a8):
                return TextManager[_0x113f95(0x3dc)][_0x113f95(0x37b)][_0x3e216d] || '';
            case 'aboveTarget':
            case _0x113f95(0x17f):
            case _0x113f95(0x348):
            case _0x113f95(0x30d):
                const _0x2d8b3a = _0x3ff606[_0x113f95(0x35b)] || 0x0,
                    _0x28031f = TextManager['DICE_ROLL'][_0x113f95(0x37b)][_0x3e216d] || '';
                return _0x28031f[_0x113f95(0x253)](_0x2d8b3a);
        }
        return '';
    }),
    (TextManager['getDiceRollEffectsText'] = function (_0x45e01b) {
        const _0x173c88 = _0x457895;
        if (!_0x45e01b) return '';
        const _0xde2bee = SceneManager[_0x173c88(0x15c)] || {},
            _0x45fccc = TextManager[_0x173c88(0x3dc)][_0x173c88(0x13a)],
            _0x195166 = TextManager[_0x173c88(0x3dc)][_0x173c88(0x16d)],
            _0x4571ad = _0x45fccc[_0x173c88(0x15e)];
        let _0x1f203b = '';
        if (_0x45e01b[_0x173c88(0x227)]) {
            if (_0xde2bee[_0x173c88(0x23b)] === _0x173c88(0x208)) {
                let _0x341c9c = String(_0x45e01b[_0x173c88(0x227)]);
                if (_0x4571ad && _0x45e01b[_0x173c88(0x227)] >= 0x0) _0x341c9c = '+' + _0x341c9c;
                _0x1f203b = _0x45fccc[_0x173c88(0x2a0)][_0x173c88(0x253)](_0x341c9c);
            } else {
                if (_0x173c88(0x2c2) === _0x173c88(0x132)) return this[_0x173c88(0x26a)][_0x444454];
                else {
                    let _0xdc59cd = String(Math[_0x173c88(0x43f)](_0x45e01b[_0x173c88(0x227)]));
                    if (_0x4571ad) _0xdc59cd = '+' + _0xdc59cd;
                    const _0x2b0b95 =
                        _0x45e01b[_0x173c88(0x227)] >= 0x0
                            ? _0x45fccc[_0x173c88(0x338)]
                            : _0x45fccc[_0x173c88(0x17e)];
                    _0x1f203b = _0x2b0b95[_0x173c88(0x253)](_0xdc59cd);
                }
            }
            const _0x434870 =
                _0x45e01b[_0x173c88(0x227)] >= 0x0
                    ? _0x195166[_0x173c88(0x427)]
                    : _0x195166[_0x173c88(0x3a2)];
            _0x1f203b = '\x5cC[%2]%1\x5cC[0]'[_0x173c88(0x253)](_0x1f203b, _0x434870);
        }
        let _0x22d22f = '';
        if (_0x45e01b[_0x173c88(0x32f)]) {
            let _0x51c3dc = String(_0x45e01b[_0x173c88(0x32f)]);
            if (_0x4571ad && _0x45e01b[_0x173c88(0x32f)] >= 0x0) _0x51c3dc = '+' + _0x51c3dc;
            _0x22d22f = _0x45fccc[_0x173c88(0x175)][_0x173c88(0x253)](_0x51c3dc);
            const _0x266a2f =
                _0x45e01b['DiceRank'] >= 0x0
                    ? _0x195166[_0x173c88(0x1fa)]
                    : _0x195166[_0x173c88(0x2ba)];
            _0x22d22f = _0x173c88(0x36d)[_0x173c88(0x253)](_0x22d22f, _0x266a2f);
        }
        let _0x47e5c6 = '';
        if (_0x45e01b['DiceModifier'] || _0x45e01b[_0x173c88(0x42c)]) {
            const _0x36d349 = _0x45e01b[_0x173c88(0xf7)] || 0x0,
                _0x534a76 = _0x36d349 + (_0x45e01b[_0x173c88(0x42c)] || 0x0),
                _0x168718 = Math[_0x173c88(0xf6)](_0x36d349, _0x534a76),
                _0x44cedd = Math[_0x173c88(0x137)](_0x36d349, _0x534a76);
            let _0x41cb1e = String(_0x168718 || 0x0);
            Math[_0x173c88(0x43f)](_0x168718) > Math[_0x173c88(0x43f)](_0x44cedd) &&
                (_0x41cb1e = String(_0x44cedd || 0x0));
            if (_0x4571ad && _0x168718 >= 0x0) _0x41cb1e = '+' + _0x41cb1e;
            _0x44cedd !== _0x168718 &&
                (Math[_0x173c88(0x43f)](_0x168718) > Math[_0x173c88(0x43f)](_0x44cedd)
                    ? (_0x41cb1e = _0x173c88(0x20c)[_0x173c88(0x253)](_0x41cb1e, _0x168718))
                    : (_0x41cb1e = _0x173c88(0x20c)[_0x173c88(0x253)](_0x41cb1e, _0x44cedd)));
            _0x47e5c6 = _0x45fccc[_0x173c88(0x27f)]['format'](_0x41cb1e);
            const _0x2d9ba7 =
                _0x168718 >= 0x0 || _0x44cedd >= 0x0
                    ? _0x195166[_0x173c88(0x138)]
                    : _0x195166['modDownColor'];
            _0x47e5c6 = _0x173c88(0x36d)[_0x173c88(0x253)](_0x47e5c6, _0x2d9ba7);
        }
        let _0x5cdc40 = _0x45fccc[_0x173c88(0x320)]
            ['format'](_0x1f203b, _0x22d22f, _0x47e5c6)
            ['trim']();
        return (
            (_0x5cdc40 = _0x5cdc40[_0x173c88(0x308)](/[ ][ ]*/gi, '\x20')),
            (_0x5cdc40 = _0x5cdc40[_0x173c88(0x308)](/[ ]*\\}[ ]*/gi, '\x5c}')),
            (_0x5cdc40 = _0x5cdc40[_0x173c88(0x308)](/[ ]*\\{[ ]*/gi, '\x5c{')),
            _0x5cdc40[_0x173c88(0x1ea)]()
        );
    }),
    (ColorManager[_0x457895(0x21d)] = function (_0x31b6c8) {
        const _0x57677c = _0x457895;
        return (
            (_0x31b6c8 = String(_0x31b6c8)),
            _0x31b6c8[_0x57677c(0x20b)](/#(.*)/i)
                ? '#%1'['format'](String(RegExp['$1']))
                : this[_0x57677c(0x27e)](Number(_0x31b6c8))
        );
    }),
    (ColorManager['darkenColor'] = function (_0x4d249b, _0xcb562) {
        const _0x963b2c = _0x457895;
        _0xcb562 = _0xcb562 || 0x0;
        let _0x548746 = ColorManager[_0x963b2c(0x21d)](_0x4d249b),
            _0x59e2e = parseInt(_0x548746[_0x963b2c(0x3a9)](0x1, 0x3), 0x10),
            _0x2244e5 = parseInt(_0x548746['slice'](0x3, 0x5), 0x10),
            _0x1f5f0b = parseInt(_0x548746[_0x963b2c(0x3a9)](0x5, 0x7), 0x10);
        return (
            (_0x59e2e = Math['max'](
                0x0,
                Math[_0x963b2c(0xf6)](0xff, Math[_0x963b2c(0x3f3)](_0x59e2e * _0xcb562))
            )[_0x963b2c(0x2d6)](0x10)),
            (_0x2244e5 = Math[_0x963b2c(0x137)](
                0x0,
                Math[_0x963b2c(0xf6)](0xff, Math[_0x963b2c(0x3f3)](_0x2244e5 * _0xcb562))
            )[_0x963b2c(0x2d6)](0x10)),
            (_0x1f5f0b = Math[_0x963b2c(0x137)](
                0x0,
                Math[_0x963b2c(0xf6)](0xff, Math[_0x963b2c(0x3f3)](_0x1f5f0b * _0xcb562))
            )[_0x963b2c(0x2d6)](0x10)),
            '#' +
                _0x59e2e[_0x963b2c(0x29e)](0x2, '0') +
                _0x2244e5[_0x963b2c(0x29e)](0x2, '0') +
                _0x1f5f0b[_0x963b2c(0x29e)](0x2, '0')
        );
    }),
    (SceneManager['isRollingDice'] = function () {
        const _0xbfb245 = _0x457895;
        return this[_0xbfb245(0x2f0)];
    }),
    (SceneManager['canRollDice'] = function () {
        const _0x3f7146 = _0x457895;
        if (this[_0x3f7146(0x212)]()) {
            if ($gameTemp[_0x3f7146(0x436)]()) {
                if (_0x3f7146(0x3bf) === _0x3f7146(0x17b)) {
                    ((this[_0x3f7146(0x35f)] = 'modifiers'), (this[_0x3f7146(0x40c)] = 0x0));
                    if (this[_0x3f7146(0x158)]) return;
                    const _0x383eee = _0x4ac71b[_0x3f7146(0x15c)] || {};
                    if (_0x383eee[_0x3f7146(0x3cb)] === 0x0 && _0x383eee[_0x3f7146(0x173)] === 0x0)
                        return;
                    this['_rollingDelay'] += _0x3a8b83['SETTINGS'][_0x3f7146(0x3f9)] || 0x1;
                } else {
                    const _0x42e8c1 = _0x3f7146(0x37e);
                    console[_0x3f7146(0x293)](_0x42e8c1);
                }
            }
            return ![];
        }
        if (Imported[_0x3f7146(0x1f0)]) {
            if ('YrqGt' !== 'YrqGt') {
                const _0x2ff140 = _0x1db804[_0x3f7146(0x3a3)](_0x4d537c['VariableCostID']),
                    _0x404254 = _0xf4fd16[_0x3f7146(0x137)](
                        _0x544988[_0x3f7146(0x17c)] || 0x1,
                        0x1
                    ),
                    _0x277613 = _0x5b7ae8[_0x3f7146(0x2ec)];
                ((_0x3fd85e += '\x20' + _0x277613[_0x3f7146(0x253)](_0x404254, _0x2ff140)),
                    (_0x4bf5aa = _0x59907e[_0x3f7146(0x1ea)]()));
            } else {
                if (this[_0x3f7146(0x207)]()) {
                    if ($gameTemp[_0x3f7146(0x436)]()) {
                        const _0x3ad380 = _0x3f7146(0x1a2);
                        console[_0x3f7146(0x293)](_0x3ad380);
                    }
                    return ![];
                }
            }
        }
        if (this['isSceneBattle']()) {
            const _0x3d70b9 = this['_scene'][_0x3f7146(0x2c3)];
            if (Imported[_0x3f7146(0x129)]) {
                if (this[_0x3f7146(0x353)][_0x3f7146(0x1f9)]()) {
                    const _0x388fd1 = _0x3f7146(0x15a);
                    return (console['log'](_0x388fd1), ![]);
                }
            }
            if (Imported['VisuMZ_3_InputComboSkills']) {
                if (_0x3f7146(0x2de) === _0x3f7146(0x3b2)) return this[_0x3f7146(0x2f0)];
                else {
                    if (_0x3d70b9[_0x3f7146(0x25c)]) {
                        if (_0x3f7146(0x43a) !== 'Enaws') _0x46339a += 0x1;
                        else {
                            const _0x4f3828 = _0x3f7146(0x2f6);
                            return (console[_0x3f7146(0x293)](_0x4f3828), ![]);
                        }
                    }
                }
            }
            if (Imported['VisuMZ_3_EvoMatrixSkills']) {
                if (_0x3f7146(0x1c5) === _0x3f7146(0x1c5)) {
                    if (_0x3d70b9[_0x3f7146(0x1d4)]) {
                        const _0x174c4c =
                            'Cannot\x20roll\x20dice\x20during\x20Evolution\x20Matrix\x20Skills.';
                        return (console[_0x3f7146(0x293)](_0x174c4c), ![]);
                    }
                } else {
                    if (!_0x30a67d[_0x3f7146(0x37f)]['showEffects']) return ![];
                    if (this[_0x3f7146(0x151)]()) return !![];
                    const _0x4ca7fb = _0x4ae714['_diceRollSettings'] || {};
                    return (
                        (_0x4ca7fb[_0x3f7146(0x2bb)] = _0x4ca7fb['effects'] || []),
                        _0x4ca7fb[_0x3f7146(0x2bb)][_0x3f7146(0x1a1)] > 0x0
                    );
                }
            }
        }
        return !this[_0x3f7146(0x2f0)];
    }),
    (SceneManager[_0x457895(0x122)] = function (_0x1d4697) {
        const _0x584fc5 = _0x457895;
        if (!this['canRollDice']()) return;
        ((this[_0x584fc5(0x2f0)] = !![]),
            (this['_diceRollSettings'] = _0x1d4697),
            (this['_diceRollDelay'] = 0x0),
            this[_0x584fc5(0x242)]());
        const _0x44ed7e = $gameTemp[_0x584fc5(0x2fd)]();
        _0x44ed7e &&
            (_0x584fc5(0x24b) !== _0x584fc5(0x3a8)
                ? _0x44ed7e[_0x584fc5(0xf4)](_0x584fc5(0x2a8))
                : this['isDiceBonusEffectIncluded'](_0x2d89da) &&
                  this['addDiceBonusEffects'](_0x1449f5));
        const _0xf3a55a = this[_0x584fc5(0x353)];
        if (_0xf3a55a) ((this['_diceRollDelay'] = 0x3c), _0xf3a55a[_0x584fc5(0x122)]());
        else {
            if (_0x584fc5(0x1a9) === 'LtXaf') this[_0x584fc5(0x307)]();
            else {
                const _0x256d98 = _0x33b4db[_0x584fc5(0x15c)] || {},
                    _0x288363 = _0x256d98[_0x584fc5(0x346)] || [];
                return _0x288363;
            }
        }
    }),
    (SceneManager[_0x457895(0x242)] = function () {
        const _0x40ede8 = _0x457895,
            _0x5d2f91 = this[_0x40ede8(0x15c)];
        ((_0x5d2f91['effects'] = []),
            (_0x5d2f91[_0x40ede8(0x373)] = 0x0),
            (_0x5d2f91[_0x40ede8(0x16a)] = 0x0),
            (_0x5d2f91[_0x40ede8(0x3cb)] = 0x0),
            (_0x5d2f91[_0x40ede8(0x173)] = 0x0),
            this[_0x40ede8(0x372)](),
            this[_0x40ede8(0x2aa)]());
    }),
    (VisuMZ[_0x457895(0x201)]['EFFECT_LIST_KEYS'] = [
        'GeneralEffects',
        'VariableEffects',
        _0x457895(0x404),
        _0x457895(0x3b9),
        _0x457895(0x3b5),
    ]),
    (SceneManager['setupDiceAutoEffects'] = function () {
        const _0x2c1589 = _0x457895,
            _0x5464a8 = this['_diceRollSettings'],
            _0x53efa7 = _0x5464a8[_0x2c1589(0x3df)] || {},
            _0x4066ee = VisuMZ[_0x2c1589(0x201)][_0x2c1589(0x425)]['clone']();
        for (const _0x3c4423 of _0x4066ee) {
            if (_0x2c1589(0x433) === _0x2c1589(0x433)) {
                if (!_0x53efa7[_0x3c4423]) continue;
                const _0x30249f = _0x53efa7[_0x3c4423] || [];
                for (const _0x3f7518 of _0x30249f) {
                    this[_0x2c1589(0x125)](_0x3f7518) &&
                        (_0x2c1589(0x265) === 'sjISS'
                            ? this['applyDiceEffect'](_0x3f7518)
                            : _0x5e94c1[_0x2c1589(0x307)]());
                }
            } else {
                const _0x511812 = _0x280dab[_0x2c1589(0x15c)] || {};
                _0x511812[_0x2c1589(0x23b)] === _0x2c1589(0x208) &&
                    ((_0x511812[_0x2c1589(0x373)] = _0x511812[_0x2c1589(0x373)] || 0x0),
                    (_0x511812['advantage'] += _0x5cadc5),
                    (_0x511812['advantage'] = _0x43968d[_0x2c1589(0x23d)](
                        _0x511812[_0x2c1589(0x373)]
                    )));
            }
        }
        if (_0x53efa7[_0x2c1589(0x2b6)]) {
            const _0x1f1b5b = _0x53efa7[_0x2c1589(0x2b6)] || [];
            for (const _0x249111 of _0x1f1b5b) {
                const _0x40bd3a = this[_0x2c1589(0x3a4)](_0x249111);
                for (const _0x36c4ca of _0x40bd3a) {
                    if (this['meetsDiceAutoEffectConditions'](_0x36c4ca)) {
                        if (_0x2c1589(0x2f4) !== _0x2c1589(0x2f4)) {
                            _0xd55e72 = _0x43ef38(_0xa466f9);
                            if (_0x36b5d4) {
                                const _0x3fb7b2 = _0x25afc7[_0x2c1589(0x201)][_0x2c1589(0x27c)]();
                                _0x2b59ba = _0xa5c9d[_0x2c1589(0x201)][_0x2c1589(0x34b)](
                                    _0x3125ef,
                                    _0x3fb7b2
                                );
                            }
                            if (_0x197e6d) {
                                const _0x1ab641 = this[_0x2c1589(0xfc)]();
                                _0x228e10 = _0x55eec4[_0x2c1589(0x201)][_0x2c1589(0x34b)](
                                    _0xb8eeae,
                                    _0x1ab641
                                );
                            }
                            return _0xc63422;
                        } else this['applyDiceEffect'](_0x36c4ca);
                    }
                }
            }
        }
    }),
    (SceneManager['meetsDiceAutoEffectConditions'] = function (_0x162887) {
        const _0x2c5e54 = _0x457895;
        if (_0x162887['VariableReqID']) {
            const _0x154593 = _0x162887['VariableReqValue'] ?? 0x1;
            if ($gameVariables['value'](_0x162887[_0x2c5e54(0x39d)]) < _0x154593) return ![];
        }
        if (_0x162887[_0x2c5e54(0x413)]) {
            if (_0x2c5e54(0x365) === _0x2c5e54(0x365)) {
                const _0x4199c2 = $dataItems[_0x162887[_0x2c5e54(0x413)]],
                    _0x70ea47 = _0x162887[_0x2c5e54(0x18d)] ?? 0x1;
                if ($gameParty[_0x2c5e54(0x3e8)](_0x4199c2) < _0x70ea47) return ![];
            } else {
                const _0x39fe62 = _0x5a25c3[_0x2c5e54(0x202)];
                ((_0x2a8c89 = _0x39fe62[_0x2c5e54(0x253)](_0x4e2630[_0x2c5e54(0x432)], _0x511925)),
                    (_0x5b1fc7 = _0x4c2706['trim']()));
            }
        }
        if (_0x162887[_0x2c5e54(0x3ac)]) {
            if ('rKQrB' === 'rKQrB') {
                const _0x11dfaf = $dataWeapons[_0x162887['WeaponReqID']],
                    _0xa1bc7a = _0x162887[_0x2c5e54(0x21f)];
                if (!$gameParty[_0x2c5e54(0x23e)](_0x11dfaf, _0xa1bc7a)) return ![];
            } else {
                const _0x3d96e5 = _0x26c9f3[_0x234a90['WeaponCostID']],
                    _0x470660 = _0x2b165a['max'](_0x13f1c9[_0x2c5e54(0x310)] || 0x1, 0x1);
                _0x22bea6[_0x2c5e54(0x3c5)](_0x3d96e5, _0x470660, ![]);
            }
        }
        if (_0x162887[_0x2c5e54(0x274)]) {
            const _0xe420ee = $dataArmors[_0x162887[_0x2c5e54(0x274)]],
                _0x2e47a3 = _0x162887['ArmorIncludeEquip'];
            if (!$gameParty[_0x2c5e54(0x23e)](_0xe420ee, _0x2e47a3)) return ![];
        }
        if (_0x162887[_0x2c5e54(0x1e7)]) {
            if (_0x2c5e54(0x400) !== _0x2c5e54(0x316)) {
                const _0x5c5de7 = $gameActors['actor'](_0x162887[_0x2c5e54(0x29b)]);
                if (!_0x5c5de7) return ![];
                if (_0x162887[_0x2c5e54(0x2e9)] && _0x5c5de7[_0x2c5e54(0x3bc)]()) return ![];
            } else return _0x296d50(_0x1ff8d2, !![], !![]);
        }
        if (_0x162887[_0x2c5e54(0x3ed)]) {
            if (!$gameSwitches[_0x2c5e54(0x3a3)](_0x162887['SwitchID'])) return ![];
        }
        if (_0x162887['ConditionJS']) {
            if (!_0x162887[_0x2c5e54(0x2c9)]()) return ![];
        }
        return !![];
    }),
    (SceneManager[_0x457895(0x419)] = function (_0x2c8ed4) {
        const _0x250531 = _0x457895,
            _0x3423c9 = this['_diceRollSettings'];
        (_0x3423c9[_0x250531(0x2bb)][_0x250531(0x3af)](_0x2c8ed4),
            _0x2c8ed4[_0x250531(0x227)] && (_0x3423c9[_0x250531(0x373)] += _0x2c8ed4['Advantage']),
            _0x2c8ed4[_0x250531(0x32f)] && (_0x3423c9[_0x250531(0x16a)] += _0x2c8ed4['DiceRank']),
            _0x2c8ed4[_0x250531(0xf7)] &&
                (_0x3423c9[_0x250531(0x3cb)] += _0x2c8ed4[_0x250531(0xf7)]),
            _0x2c8ed4[_0x250531(0x42c)] &&
                (_0x3423c9[_0x250531(0x173)] += _0x2c8ed4[_0x250531(0x42c)]));
    }),
    (SceneManager[_0x457895(0x3a4)] = function (_0x5e093b) {
        const _0x1eebc8 = _0x457895,
            _0x341eef = _0x5e093b[_0x1eebc8(0x1e7)] || _0x5e093b[_0x1eebc8(0x369)] || 0x0;
        if (_0x341eef <= 0x0) return [];
        const _0x1e7f7c = $dataSkills[_0x341eef];
        if (!_0x1e7f7c) return [];
        const _0x45dd0c = _0x5e093b[_0x1eebc8(0x33b)] ?? ![],
            _0x2d0bf7 = [],
            _0xbe9d40 = _0x5e093b['SkillUser'];
        switch (_0xbe9d40) {
            case 'leader':
                $gameParty['leader']() &&
                    _0x2d0bf7[_0x1eebc8(0x3af)]($gameParty[_0x1eebc8(0x321)]()[_0x1eebc8(0x23c)]());
                break;
            case _0x1eebc8(0x33c):
            case _0x1eebc8(0x26e):
            case _0x1eebc8(0x3de):
                {
                    let _0x44cf53 = [];
                    if (_0xbe9d40 === _0x1eebc8(0x33c)) _0x44cf53 = $gameParty[_0x1eebc8(0x210)]();
                    else {
                        if (_0xbe9d40 === _0x1eebc8(0x26e))
                            _0x1eebc8(0x2ca) !== _0x1eebc8(0x418)
                                ? (_0x44cf53 = $gameParty[_0x1eebc8(0x266)]())
                                : (_0x1d5711[_0x24f927] = _0x2b4a9d[_0x1b0c38]);
                        else {
                            if (_0xbe9d40 === _0x1eebc8(0x3de)) {
                                const _0x98ea55 = _0x5e093b['SpecificActorIDs'] || [];
                                _0x44cf53 = _0x98ea55[_0x1eebc8(0x3fe)](_0x2f8702 =>
                                    $gameActors[_0x1eebc8(0x1b6)](_0x2f8702)
                                )['filter'](_0x4a085c =>
                                    $gameParty['allMembers']()[_0x1eebc8(0x313)](_0x4a085c)
                                );
                            }
                        }
                    }
                    const _0x413c0e = _0x44cf53[_0x1eebc8(0x14c)](
                        _0x46388d =>
                            _0x46388d &&
                            (_0x45dd0c
                                ? _0x46388d[_0x1eebc8(0x120)](_0x341eef)
                                : _0x46388d[_0x1eebc8(0x1ff)](_0x341eef)) &&
                            _0x46388d['canPaySkillCost'](_0x1e7f7c)
                    );
                    if (_0x413c0e) _0x2d0bf7['push'](_0x413c0e[_0x1eebc8(0x23c)]());
                }
                break;
            case _0x1eebc8(0x198):
            case _0x1eebc8(0x14a):
            case _0x1eebc8(0x27a):
                {
                    let _0x26aae5 = [];
                    if (_0xbe9d40 === _0x1eebc8(0x198)) _0x26aae5 = $gameParty[_0x1eebc8(0x210)]();
                    else {
                        if (_0xbe9d40 === 'everyBattleMember')
                            _0x26aae5 = $gameParty['battleMembers']();
                        else {
                            if (_0xbe9d40 === _0x1eebc8(0x27a)) {
                                if (_0x1eebc8(0x2af) !== _0x1eebc8(0x2b3)) {
                                    const _0x239f82 = _0x5e093b[_0x1eebc8(0x2df)] || [];
                                    _0x26aae5 = _0x239f82['map'](_0x3097a3 =>
                                        $gameActors['actor'](_0x3097a3)
                                    )[_0x1eebc8(0x398)](_0x36e4ca =>
                                        $gameParty[_0x1eebc8(0x210)]()[_0x1eebc8(0x313)](_0x36e4ca)
                                    );
                                } else {
                                    let _0x248966 = [];
                                    if (_0x33d77a === _0x1eebc8(0x33c))
                                        _0x248966 = _0x3ff808[_0x1eebc8(0x210)]();
                                    else {
                                        if (_0x4d24ac === 'anyBattleMember')
                                            _0x248966 = _0x1c27f5[_0x1eebc8(0x266)]();
                                        else {
                                            if (_0xfbdbeb === _0x1eebc8(0x3de)) {
                                                const _0x93bf81 =
                                                    _0x2ed94e['SpecificActorIDs'] || [];
                                                _0x248966 = _0x93bf81[_0x1eebc8(0x3fe)](_0x4999bf =>
                                                    _0xb7dba0['actor'](_0x4999bf)
                                                )[_0x1eebc8(0x398)](_0x7eec90 =>
                                                    _0x5baff6[_0x1eebc8(0x210)]()['includes'](
                                                        _0x7eec90
                                                    )
                                                );
                                            }
                                        }
                                    }
                                    const _0x30322b = _0x248966[_0x1eebc8(0x14c)](
                                        _0x19ea1f =>
                                            _0x19ea1f &&
                                            (_0x4c3efe
                                                ? _0x19ea1f[_0x1eebc8(0x120)](_0x5cc7a4)
                                                : _0x19ea1f[_0x1eebc8(0x1ff)](_0x4e3fbb)) &&
                                            _0x19ea1f[_0x1eebc8(0x3ea)](_0x39f814)
                                    );
                                    if (_0x30322b)
                                        _0xc0967f[_0x1eebc8(0x3af)](_0x30322b['actorId']());
                                }
                            }
                        }
                    }
                    for (const _0x49f26c of _0x26aae5) {
                        if (_0x45dd0c && !_0x49f26c[_0x1eebc8(0x120)](_0x341eef)) continue;
                        if (!_0x49f26c['hasSkill'](_0x341eef)) continue;
                        _0x2d0bf7[_0x1eebc8(0x3af)](_0x49f26c[_0x1eebc8(0x23c)]());
                    }
                }
                break;
        }
        if (_0x2d0bf7[_0x1eebc8(0x1a1)] <= 0x0) return [];
        const _0x3d665c = [],
            _0x1ee0b4 = [_0x1eebc8(0x1e3), 'SpecificActorIDs'];
        for (const _0x5d12c5 of _0x2d0bf7) {
            const _0x385bb7 = {};
            _0x385bb7['SkillUserActorID'] = _0x5d12c5;
            for (const _0x4152fd in _0x5e093b) {
                if (_0x1ee0b4['includes'](_0x4152fd)) continue;
                _0x385bb7[_0x4152fd] = _0x5e093b[_0x4152fd];
            }
            _0x3d665c['push'](_0x385bb7);
        }
        return _0x3d665c;
    }),
    (SceneManager[_0x457895(0x2aa)] = function () {
        const _0x588abd = _0x457895,
            _0x53981b = this['_diceRollSettings'];
        _0x53981b[_0x588abd(0x346)] = [];
        const _0xef7bbe = _0x53981b[_0x588abd(0x14d)] || {},
            _0x390bc1 = VisuMZ['DiceRollsRngSeeds'][_0x588abd(0x425)]['clone']();
        for (const _0x54c3c4 of _0x390bc1) {
            if ('wmOPL' !== 'vnZns') {
                if (!_0xef7bbe[_0x54c3c4]) continue;
                const _0x274a0f = _0xef7bbe[_0x54c3c4] || [];
                for (const _0x1c9a50 of _0x274a0f) {
                    this[_0x588abd(0x430)](_0x1c9a50) && this[_0x588abd(0x26f)](_0x1c9a50);
                }
            } else this[_0x588abd(0x314)]();
        }
        if (_0xef7bbe[_0x588abd(0x2b6)]) {
            if (_0x588abd(0x382) !== _0x588abd(0x382)) {
                let _0x49d62d = this[_0x588abd(0x118)](_0x5b3825)['trim']();
                const _0x238e64 = _0x568473[_0x588abd(0x3dc)]['effectList'];
                if (_0x4ed2df[_0x588abd(0x432)]) {
                    const _0x40a1d2 = _0x238e64[_0x588abd(0x202)];
                    ((_0x49d62d = _0x40a1d2['format'](_0x4fe401[_0x588abd(0x432)], _0x49d62d)),
                        (_0x49d62d = _0x49d62d['trim']()));
                }
                if (_0x2866bc['PostEffectText']) {
                    const _0x666b72 = _0x238e64[_0x588abd(0x202)];
                    ((_0x49d62d = _0x666b72['format'](_0x49d62d, _0x1b2ad4[_0x588abd(0x143)])),
                        (_0x49d62d = _0x49d62d[_0x588abd(0x1ea)]()));
                }
                _0x49d62d = _0x238e64['displayFmt']
                    [_0x588abd(0x253)](_0x49d62d)
                    [_0x588abd(0x1ea)]();
                const _0x5dbd96 = this[_0x588abd(0x3a1)](_0x49d62d)['width'];
                this[_0x588abd(0x3f2)](
                    _0x49d62d,
                    _0x45c2fb['x'] + _0x4f927d[_0x588abd(0x155)] - _0x5dbd96,
                    _0x5982d8['y']
                );
            } else {
                const _0x2998bf = _0xef7bbe['SkillEffects'] || [];
                for (const _0x12ed4a of _0x2998bf) {
                    const _0x36d903 = this['createSkillEffectDupes'](_0x12ed4a);
                    for (const _0x20210c of _0x36d903) {
                        if (_0x588abd(0xfb) !== _0x588abd(0xfb))
                            this['createNewRandomNumberSeed'](_0x209ba8);
                        else {
                            if (this[_0x588abd(0x430)](_0x20210c)) {
                                if (_0x588abd(0x139) !== _0x588abd(0x1fb))
                                    this[_0x588abd(0x26f)](_0x20210c);
                                else {
                                    ((this[_0x588abd(0x35f)] = _0x588abd(0x237)),
                                        _0x38aec7['_scene'][_0x588abd(0x13e)]());
                                    const _0x3ca731 = _0x21537e[_0x588abd(0x215)](
                                        (0xc / 0x3c) * 0x3e8
                                    );
                                    _0x1a5e44(
                                        _0x24b531[_0x588abd(0x307)][_0x588abd(0x323)](_0x3d1c04),
                                        _0x3ca731
                                    );
                                }
                            }
                        }
                    }
                }
            }
        }
    }),
    (SceneManager[_0x457895(0x430)] = function (_0xa7139f) {
        const _0x3890c8 = _0x457895;
        if (_0xa7139f[_0x3890c8(0x1ee)]) {
            if (_0x3890c8(0x26c) !== 'gSmFU')
                ((this[_0x3890c8(0x278)] = _0x48956a(_0x1fd194['$1'])),
                    (this[_0x3890c8(0x421)] = !![]),
                    (_0x281e2c = _0x15f869[_0x3890c8(0x308)](_0x3211b7[_0x3890c8(0x352)], '')));
            else {
                if ($gameVariables[_0x3890c8(0x3a3)](_0xa7139f[_0x3890c8(0x1ee)]) <= 0x0)
                    return ![];
            }
        }
        if (_0xa7139f['ItemCostID']) {
            if (_0x3890c8(0x3d5) !== _0x3890c8(0x37d)) {
                const _0x13d48f = $dataItems[_0xa7139f[_0x3890c8(0x342)]];
                if ($gameParty[_0x3890c8(0x3e8)](_0x13d48f) <= 0x0) return ![];
            } else {
                if (!_0x1a5ac1[_0x3890c8(0x3a3)](_0x33bac7[_0x3890c8(0xf1)])) return ![];
            }
        }
        if (_0xa7139f[_0x3890c8(0x343)]) {
            const _0x3e84c1 = $dataWeapons[_0xa7139f['WeaponCostID']];
            if ($gameParty[_0x3890c8(0x3e8)](_0x3e84c1) <= 0x0) return ![];
        }
        if (_0xa7139f[_0x3890c8(0x11c)]) {
            if (_0x3890c8(0x3ab) !== _0x3890c8(0x360)) {
                const _0x56b368 = $dataArmors[_0xa7139f[_0x3890c8(0x11c)]];
                if ($gameParty[_0x3890c8(0x3e8)](_0x56b368) <= 0x0) return ![];
            } else return _0x3c5762(_0x27299a, ![], !![]);
        }
        if (_0xa7139f['SkillCostID']) {
            if (_0x3890c8(0x2d5) !== _0x3890c8(0x2d5)) _0x9d037[_0x3890c8(0x20a)]();
            else {
                const _0x4bb5b5 = $gameActors[_0x3890c8(0x1b6)](_0xa7139f[_0x3890c8(0x29b)]);
                if (!_0x4bb5b5) return ![];
                if (_0xa7139f['SkillUserAlive'] && _0x4bb5b5[_0x3890c8(0x3bc)]()) return ![];
            }
        }
        if (_0xa7139f[_0x3890c8(0x12b)]) {
            if (_0x3890c8(0x110) === _0x3890c8(0x110)) {
                if (!$gameSwitches['value'](_0xa7139f['ShowSwitchID'])) return ![];
            } else {
                if (_0x2e92e4 === _0x1e193f) return _0xccccf['random']();
                return _0x252bfd['getNextFloatRandomNumberFromSeed'](
                    _0x3c9607,
                    _0x571152,
                    _0x53fb70
                );
            }
        }
        if (_0xa7139f[_0x3890c8(0x1f2)]) {
            if (!_0xa7139f[_0x3890c8(0x1f2)]()) return ![];
        }
        return !![];
    }),
    (SceneManager[_0x457895(0x26f)] = function (_0x1633bb) {
        const _0x5b0ca8 = _0x457895,
            _0x28794b = this[_0x5b0ca8(0x15c)];
        (_0x28794b[_0x5b0ca8(0x346)][_0x5b0ca8(0x3af)](_0x1633bb),
            (_0x1633bb[_0x5b0ca8(0x3d4)] = Math[_0x5b0ca8(0x137)](
                Math['ceil'](_0x1633bb['MaxUses'] || 0x1),
                0x1
            )),
            (_0x1633bb[_0x5b0ca8(0x295)] = 0x0));
    }),
    (SceneManager[_0x457895(0x307)] = function () {
        const _0x492dd5 = _0x457895;
        ((this[_0x492dd5(0x2f0)] = ![]), (this[_0x492dd5(0x15c)] = undefined));
        const _0x337bfe = this[_0x492dd5(0x353)];
        _0x337bfe && _0x337bfe[_0x492dd5(0x307)]();
    }),
    (SceneManager[_0x457895(0x37c)] = function () {
        const _0x3e11c1 = _0x457895;
        return this['_scene'] && this[_0x3e11c1(0x353)][_0x3e11c1(0x2ed)] === Scene_Battle;
    }),
    (Game_Temp['prototype'][_0x457895(0x330)] = function (_0x4549f7) {
        const _0xb15234 = _0x457895;
        this[_0xb15234(0x1ac)] = _0x4549f7;
    }),
    (Game_Temp[_0x457895(0x428)]['getLastPluginCommandInterpreter'] = function () {
        return this['_lastPluginCommandInterpreter'];
    }),
    (VisuMZ[_0x457895(0x201)]['Game_Interpreter_PluginCommand'] =
        Game_Interpreter[_0x457895(0x428)][_0x457895(0x32a)]),
    (Game_Interpreter[_0x457895(0x428)]['command357'] = function (_0x57972c) {
        const _0x2ed89c = _0x457895;
        return (
            $gameTemp[_0x2ed89c(0x330)](this),
            VisuMZ[_0x2ed89c(0x201)][_0x2ed89c(0x39b)][_0x2ed89c(0x2a3)](this, _0x57972c)
        );
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x399)] =
        Game_Interpreter[_0x457895(0x428)][_0x457895(0x3a6)]),
    (Game_Interpreter[_0x457895(0x428)][_0x457895(0x3a6)] = function () {
        const _0x2d55ba = _0x457895;
        if (this[_0x2d55ba(0x163)] === 'diceRoll') {
            if (SceneManager[_0x2d55ba(0x212)]()) return !![];
            this['_waitMode'] = '';
        }
        return VisuMZ[_0x2d55ba(0x201)]['Game_Interpreter_updateWaitMode'][_0x2d55ba(0x2a3)](this);
    }),
    (VisuMZ[_0x457895(0x201)]['m'] =
        VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x38b)][_0x457895(0x40e)] ??
        0x80000000),
    (VisuMZ[_0x457895(0x201)]['a'] =
        VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x38b)][_0x457895(0x395)] ??
        0x41c64e6d),
    (VisuMZ[_0x457895(0x201)]['c'] =
        VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x38b)][_0x457895(0x3b3)] ?? 0x3039),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x2ac)] = Game_System['prototype'][_0x457895(0x31e)]),
    (Game_System[_0x457895(0x428)]['initialize'] = function () {
        const _0x1d2a8c = _0x457895;
        (VisuMZ[_0x1d2a8c(0x201)][_0x1d2a8c(0x2ac)][_0x1d2a8c(0x2a3)](this),
            this[_0x1d2a8c(0x261)]());
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x261)] = function () {
        const _0x375c7e = _0x457895;
        ((this[_0x375c7e(0x35c)] = {}),
            (this[_0x375c7e(0x251)] = Math[_0x375c7e(0x3dd)](0x80000000)));
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x169)] = function (_0x3da825, _0x43f6a9, _0x512500) {
        const _0x20fe3b = _0x457895;
        if (this[_0x20fe3b(0x35c)] === undefined) {
            if (_0x20fe3b(0x18a) !== _0x20fe3b(0x41c)) this[_0x20fe3b(0x261)]();
            else {
                if (!_0x4d41eb[_0x20fe3b(0x2c9)]()) return ![];
            }
        }
        const _0x5d4eea = this[_0x20fe3b(0x161)](_0x3da825, _0x43f6a9, _0x512500);
        return (
            this[_0x20fe3b(0x35c)][_0x5d4eea] === undefined &&
                this['createNewRandomNumberSeed'](_0x5d4eea),
            this[_0x20fe3b(0x35c)][_0x5d4eea]
        );
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0xfc)] = function () {
        const _0x233fb9 = _0x457895;
        return (
            this[_0x233fb9(0x251)] === undefined && this['initRandomNumberSeeds'](),
            this[_0x233fb9(0x251)]
        );
    }),
    (VisuMZ['DiceRollsRngSeeds'][_0x457895(0x34b)] = function (_0x183ccd, _0x5c5d18) {
        const _0x23ae84 = _0x457895;
        ((_0x183ccd = String(_0x183ccd)), (_0x5c5d18 = String(_0x5c5d18)));
        let _0x5ee2f3 = '',
            _0x186654 = 0x0,
            _0x305788 = 0x0;
        while (_0x186654 < _0x183ccd[_0x23ae84(0x1a1)] || _0x305788 < _0x5c5d18[_0x23ae84(0x1a1)]) {
            if (_0x186654 < _0x183ccd[_0x23ae84(0x1a1)]) _0x5ee2f3 += _0x183ccd[_0x186654++];
            if (_0x305788 < _0x5c5d18[_0x23ae84(0x1a1)]) _0x5ee2f3 += _0x5c5d18[_0x305788++];
        }
        return _0x5ee2f3;
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x27c)] = function () {
        const _0xdccf9c = _0x457895,
            _0x4e0d4b = new Date();
        let _0x4fe50f = String(_0x4e0d4b[_0xdccf9c(0x18f)]())[_0xdccf9c(0x29e)](0x2, '0'),
            _0x236ab8 = String(_0x4e0d4b[_0xdccf9c(0x1fd)]() + 0x1)[_0xdccf9c(0x29e)](0x2, '0'),
            _0x6e4fd1 = _0x4e0d4b[_0xdccf9c(0x140)]();
        return _0x4fe50f + '-' + _0x236ab8 + '-' + _0x6e4fd1;
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x161)] = function (_0x520a5b, _0x454bdc, _0x490c3e) {
        const _0x1d7736 = _0x457895;
        _0x520a5b = String(_0x520a5b);
        if (_0x454bdc) {
            const _0x1edfbf = VisuMZ[_0x1d7736(0x201)][_0x1d7736(0x27c)]();
            _0x520a5b = VisuMZ['DiceRollsRngSeeds']['Interweave'](_0x520a5b, _0x1edfbf);
        }
        if (_0x490c3e) {
            if (_0x1d7736(0x103) === 'SpBXG')
                ((this[_0x1d7736(0x2cf)] = 0x1), (this['_numberText'] = '1'));
            else {
                const _0x59b22d = this[_0x1d7736(0xfc)]();
                _0x520a5b = VisuMZ['DiceRollsRngSeeds']['Interweave'](_0x520a5b, _0x59b22d);
            }
        }
        return _0x520a5b;
    }),
    (Game_System['prototype'][_0x457895(0x423)] = function (_0x4f5e60) {
        const _0x129738 = _0x457895;
        _0x4f5e60 = String(_0x4f5e60);
        var _0xf82d08 = [];
        const _0xceefd5 = _0x4f5e60[_0x129738(0x1a1)];
        for (var _0x12e194 = 0x0; _0x12e194 < _0xceefd5; _0x12e194++) {
            if (_0x129738(0x111) === 'smhDt') {
                const _0x1652c1 = _0x432769[_0x129738(0x1b6)](_0x5c27f9[_0x129738(0x29b)]);
                if (_0x1652c1) {
                    const _0xa4ec59 = _0x28c460[_0x491f90[_0x129738(0x369)]];
                    _0x1652c1['paySkillCost'](_0xa4ec59);
                }
            } else _0xf82d08['push'](_0x4f5e60[_0x129738(0x11e)](_0x12e194));
        }
        let _0x390628 = _0xf82d08[_0x129738(0x224)]('') + 0x0;
        while (_0x390628 > VisuMZ['DiceRollsRngSeeds']['m']) {
            if (_0x129738(0x26b) !== 'WOHgx') {
                const _0x92d1e9 = _0x210fe6[_0x129738(0x3a3)](_0x377d68['VariableCostID']),
                    _0x52a6a4 = _0x46455d['max'](_0x54e183[_0x129738(0x17c)] || 0x1, 0x1);
                if (_0x52a6a4 > _0x92d1e9) return ![];
            } else _0x390628 = Math[_0x129738(0x3f3)](_0x390628 / VisuMZ[_0x129738(0x201)]['m']);
        }
        this[_0x129738(0x35c)][_0x4f5e60] = _0x390628 % VisuMZ[_0x129738(0x201)]['m'];
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x328)] = function (_0x1e58ee, _0x73580b, _0x2a3ee6) {
        const _0x442c3d = _0x457895,
            _0x4eb564 = VisuMZ[_0x442c3d(0x201)];
        let _0x12d87d = this['getRandomNumberSeeds'](_0x1e58ee, _0x73580b, _0x2a3ee6);
        const _0x24b78b = _0x4eb564['m'];
        _0x12d87d = (_0x4eb564['a'] * _0x12d87d + _0x4eb564['c']) % _0x24b78b;
        const _0x227622 = this[_0x442c3d(0x161)](_0x1e58ee, _0x73580b, _0x2a3ee6);
        return (
            (this['_randomNumberSeeds'][_0x227622] = _0x12d87d),
            this['_randomNumberSeeds'][_0x227622]
        );
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x31c)] = function (_0x5ce71b, _0x5c68c2, _0x559c10) {
        const _0x263ffd = _0x457895,
            _0x95ecd5 = VisuMZ[_0x263ffd(0x201)]['m'] - 0x1;
        return this[_0x263ffd(0x328)](_0x5ce71b, _0x5c68c2, _0x559c10) / _0x95ecd5;
    }),
    (Game_System[_0x457895(0x428)][_0x457895(0x282)] = function (
        _0x1017c2,
        _0x2ad724,
        _0x5ec814,
        _0x286c1c,
        _0x1e894a
    ) {
        const _0x324d44 = _0x457895,
            _0x4cf486 = _0x2ad724 - _0x1017c2 + 0x1,
            _0xce76b4 = this['getNextFloatRandomNumberFromSeed'](_0x5ec814, _0x286c1c, _0x1e894a),
            _0x17c164 = Math[_0x324d44(0x3f3)](_0xce76b4 * _0x4cf486);
        return _0x1017c2 + _0x17c164;
    }),
    (Game_System[_0x457895(0x428)]['resetRandomNumberSeed'] = function (
        _0x32dd87,
        _0x31be87,
        _0x2711ee
    ) {
        const _0x5dd6cf = _0x457895;
        if (_0x32dd87 === undefined) return;
        const _0x239de7 = this[_0x5dd6cf(0x161)](_0x32dd87, _0x31be87, _0x2711ee);
        this[_0x5dd6cf(0x423)](_0x239de7);
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x33d)] = Game_Message['prototype'][_0x457895(0x1b5)]),
    (Game_Message[_0x457895(0x428)][_0x457895(0x1b5)] = function () {
        const _0x54c91c = _0x457895;
        (this[_0x54c91c(0x329)](),
            VisuMZ[_0x54c91c(0x201)][_0x54c91c(0x33d)]['call'](this),
            this[_0x54c91c(0x105)]());
    }),
    (Game_Message[_0x457895(0x428)][_0x457895(0x105)] = function () {
        const _0x3a1378 = _0x457895;
        ((this[_0x3a1378(0x278)] = ''),
            (this[_0x3a1378(0x2bc)] = ![]),
            (this[_0x3a1378(0x421)] = ![]));
    }),
    (Game_Message[_0x457895(0x428)][_0x457895(0x329)] = function () {
        const _0x24a267 = _0x457895;
        this[_0x24a267(0x105)]();
        const _0x4678c2 = VisuMZ[_0x24a267(0x201)][_0x24a267(0x1d0)],
            _0x258a1d = this[_0x24a267(0x279)][_0x24a267(0x1a1)];
        for (let _0x4367b7 = 0x0; _0x4367b7 < _0x258a1d; _0x4367b7++) {
            let _0x372ff3 = this['_choices'][_0x4367b7];
            _0x372ff3[_0x24a267(0x20b)](_0x4678c2['rngSeed']) &&
                ((this[_0x24a267(0x278)] = String(RegExp['$1'])),
                (_0x372ff3 = _0x372ff3[_0x24a267(0x308)](_0x4678c2['rngSeed'], '')));
            _0x372ff3['match'](_0x4678c2[_0x24a267(0x15d)]) &&
                ((this[_0x24a267(0x278)] = String(RegExp['$1'])),
                (this[_0x24a267(0x2bc)] = !![]),
                (_0x372ff3 = _0x372ff3[_0x24a267(0x308)](_0x4678c2[_0x24a267(0x15d)], '')));
            if (_0x372ff3[_0x24a267(0x20b)](_0x4678c2['uniqueSeed'])) {
                if (_0x24a267(0x429) === _0x24a267(0x429))
                    ((this[_0x24a267(0x278)] = String(RegExp['$1'])),
                        (this[_0x24a267(0x421)] = !![]),
                        (_0x372ff3 = _0x372ff3[_0x24a267(0x308)](_0x4678c2[_0x24a267(0x352)], '')));
                else {
                    const _0x25020a = this['_settingsKey'] || _0x24a267(0xfe);
                    return _0x88959d[_0x24a267(0x37f)][_0x25020a];
                }
            }
            (_0x372ff3[_0x24a267(0x20b)](_0x4678c2[_0x24a267(0x306)]) &&
                ((this[_0x24a267(0x278)] = String(RegExp['$1'])),
                (this[_0x24a267(0x2bc)] = !![]),
                (this['_rngSaveUnique'] = !![]),
                (_0x372ff3 = _0x372ff3['replace'](_0x4678c2['dailyUniqueSeed'], ''))),
                (this['_choices'][_0x4367b7] = _0x372ff3['trim']()));
        }
    }));
VisuMZ[_0x457895(0x3c2)] &&
    ((VisuMZ[_0x457895(0x201)][_0x457895(0x370)] = VisuMZ[_0x457895(0x3c2)][_0x457895(0x186)]),
    (VisuMZ[_0x457895(0x3c2)][_0x457895(0x186)] = function (_0x192656) {
        const _0x15950d = _0x457895;
        if ($gameMessage && $gameMessage[_0x15950d(0x278)] !== '') {
            if (_0x15950d(0x290) !== _0x15950d(0x277))
                return VisuMZ[_0x15950d(0x3c2)]['ShuffleSeededArray'][_0x15950d(0x2a3)](
                    this,
                    _0x192656
                );
            else {
                const _0x591f20 = _0x3df749(_0x55bbf3['$1']);
                _0x591f20 !== _0x63be3b[_0x50a677][_0x15950d(0x1e8)] &&
                    (_0x386be4(
                        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                            _0x15950d(0x253)
                        ](_0x359a91, _0x591f20)
                    ),
                    _0x2393d9[_0x15950d(0x182)]());
            }
        } else {
            if (_0x15950d(0x109) === _0x15950d(0x36f)) {
                const _0x5a30c0 = _0x23927b[_0x3e17bd['ArmorCostID']];
                if (_0x1cd8e7['numItems'](_0x5a30c0) <= 0x0) return ![];
            } else
                return VisuMZ[_0x15950d(0x201)]['VisuMZ_ShuffleArray'][_0x15950d(0x2a3)](
                    this,
                    _0x192656
                );
        }
    }),
    (VisuMZ[_0x457895(0x3c2)][_0x457895(0x3ba)] = function (_0x4a58ac) {
        const _0x8c592f = _0x457895,
            _0x53ec4c = $gameMessage[_0x8c592f(0x278)] || '',
            _0x2b7249 = $gameMessage[_0x8c592f(0x2bc)] || ![],
            _0x586823 = $gameMessage['_rngSaveUnique'] || ![];
        var _0x5a9a83, _0x3ceba1, _0x1c6669;
        for (_0x1c6669 = _0x4a58ac[_0x8c592f(0x1a1)] - 0x1; _0x1c6669 > 0x0; _0x1c6669--) {
            const _0x592594 = $rngSeed(_0x53ec4c, _0x2b7249, _0x586823);
            ((_0x5a9a83 = Math[_0x8c592f(0x3f3)](_0x592594 * (_0x1c6669 + 0x1))),
                (_0x3ceba1 = _0x4a58ac[_0x1c6669]),
                (_0x4a58ac[_0x1c6669] = _0x4a58ac[_0x5a9a83]),
                (_0x4a58ac[_0x5a9a83] = _0x3ceba1));
        }
        return _0x4a58ac;
    }));
((Game_Temp['prototype'][_0x457895(0x105)] = function () {
    const _0x39d7bf = _0x457895;
    ((this[_0x39d7bf(0x278)] = ''), (this[_0x39d7bf(0x2bc)] = ![]), (this['_rngSaveUnique'] = ![]));
}),
    (VisuMZ[_0x457895(0x201)]['Game_Action_apply'] = Game_Action['prototype'][_0x457895(0x149)]),
    (Game_Action[_0x457895(0x428)][_0x457895(0x149)] = function (_0x59109c) {
        const _0x54b999 = _0x457895;
        ($gameTemp[_0x54b999(0x105)](),
            this[_0x54b999(0x34e)](_0x59109c),
            VisuMZ[_0x54b999(0x201)][_0x54b999(0x10b)][_0x54b999(0x2a3)](this, _0x59109c),
            $gameTemp[_0x54b999(0x105)]());
    }),
    (Game_Action['prototype']['applyRngAspects'] = function (_0x30f835) {
        const _0x221b9b = _0x457895;
        if (!this['item']()) return;
        if (!_0x30f835) return;
        let _0x5bdfff =
            VisuMZ['DiceRollsRngSeeds']['Settings'][_0x221b9b(0x38b)][_0x221b9b(0x22c)] ?? '';
        if (_0x5bdfff['toLowerCase']()[_0x221b9b(0x1ea)]() === _0x221b9b(0x317)) _0x5bdfff = '';
        let _0x26dbc8 = ![],
            _0x2a99a8 = VisuMZ[_0x221b9b(0x201)][_0x221b9b(0x42e)][_0x221b9b(0x38b)][
                'DefaultRngSeed'
            ]
                ? !![]
                : ![];
        const _0x495fea = VisuMZ['DiceRollsRngSeeds'][_0x221b9b(0x1d0)],
            _0x1f31bb = this[_0x221b9b(0x165)]()[_0x221b9b(0x10c)] || '';
        if (_0x1f31bb['match'](_0x495fea['noRngSeed']))
            ((_0x5bdfff = ''), (_0x26dbc8 = ![]), (_0x2a99a8 = ![]));
        else {
            if (_0x1f31bb[_0x221b9b(0x20b)](_0x495fea[_0x221b9b(0x1cb)]))
                ((_0x5bdfff = String(RegExp['$1'])[_0x221b9b(0x1ea)]()),
                    (_0x26dbc8 = ![]),
                    (_0x2a99a8 = ![]));
            else {
                if (_0x1f31bb[_0x221b9b(0x20b)](_0x495fea['dailySeed']))
                    'EwRyd' !== _0x221b9b(0x2d9)
                        ? (_0x30f7f2 =
                              _0x6187f1[_0x4835cd[_0x221b9b(0x3ac)] || _0x5574d6['WeaponCostID']])
                        : ((_0x5bdfff = String(RegExp['$1'])['trim']()),
                          (_0x26dbc8 = !![]),
                          (_0x2a99a8 = ![]));
                else {
                    if (_0x1f31bb[_0x221b9b(0x20b)](_0x495fea[_0x221b9b(0x352)]))
                        ((_0x5bdfff = String(RegExp['$1'])[_0x221b9b(0x1ea)]()),
                            (_0x26dbc8 = ![]),
                            (_0x2a99a8 = !![]));
                    else
                        _0x1f31bb['match'](_0x495fea[_0x221b9b(0x306)]) &&
                            (_0x221b9b(0x401) !== 'VepLJ'
                                ? ((_0x5bdfff = String(RegExp['$1'])[_0x221b9b(0x1ea)]()),
                                  (_0x26dbc8 = !![]),
                                  (_0x2a99a8 = !![]))
                                : _0x3f0fbc[_0x221b9b(0x176)](_0x79775d['VariableID'], 0x0));
                }
            }
        }
        _0x5bdfff['toLowerCase']()[_0x221b9b(0x1ea)]() === _0x221b9b(0x2f1) &&
            (_0x5bdfff = this[_0x221b9b(0x165)]()[_0x221b9b(0x366)] || _0x221b9b(0x28d));
        if (_0x5bdfff) {
            if ('YEhkZ' !== 'YEhkZ') {
                if (!_0x418786) return ![];
                return !![];
            } else {
                if (this[_0x221b9b(0x17a)]()[_0x221b9b(0x115)]())
                    _0x5bdfff += _0x221b9b(0x18e)[_0x221b9b(0x253)](this['subject']()['actorId']());
                else {
                    if (_0x221b9b(0x189) === _0x221b9b(0x189))
                        _0x5bdfff += '-enemy-%1-%2'['format'](
                            this[_0x221b9b(0x17a)]()[_0x221b9b(0x247)](),
                            this[_0x221b9b(0x17a)]()[_0x221b9b(0x2f5)]()
                        );
                    else
                        return (
                            this[_0x221b9b(0x353)] &&
                            this[_0x221b9b(0x353)]['constructor'] === _0xbc7ee0
                        );
                }
                _0x5bdfff += _0x221b9b(0x1f8);
                if (_0x30f835[_0x221b9b(0x115)]())
                    _0x5bdfff += '-actor-%1'['format'](_0x30f835[_0x221b9b(0x23c)]());
                else {
                    if (_0x221b9b(0x28e) !== _0x221b9b(0x257))
                        _0x5bdfff += '-enemy-%1-%2'['format'](
                            _0x30f835['enemyId'](),
                            _0x30f835[_0x221b9b(0x2f5)]()
                        );
                    else
                        return (
                            _0xebd8ff['setLastPluginCommandInterpreter'](this),
                            _0x591bbb['DiceRollsRngSeeds'][_0x221b9b(0x39b)]['call'](
                                this,
                                _0x5e6954
                            )
                        );
                }
                (this[_0x221b9b(0x3f6)]()
                    ? _0x221b9b(0x204) !== _0x221b9b(0x1c4)
                        ? (_0x5bdfff += '-skill-%1'['format'](this['item']()['id']))
                        : (_0x3ad202[_0x221b9b(0x24d)](_0x1b8209),
                          _0xbc0f16[_0x221b9b(0x299)](0x1),
                          this[_0x221b9b(0x43e)](_0x3d341c))
                    : (_0x5bdfff += _0x221b9b(0x276)[_0x221b9b(0x253)](this['item']()['id'])),
                    ($gameTemp[_0x221b9b(0x278)] = _0x5bdfff),
                    ($gameTemp['_rngDaily'] = _0x26dbc8),
                    ($gameTemp[_0x221b9b(0x421)] = _0x2a99a8));
            }
        }
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x3d0)] = Math[_0x457895(0x3d9)]),
    (Math[_0x457895(0x3d9)] = function () {
        const _0x1f7f16 = _0x457895;
        return $gameTemp['_rngSeed']
            ? VisuMZ[_0x1f7f16(0x201)][_0x1f7f16(0xff)]()
            : VisuMZ[_0x1f7f16(0x201)][_0x1f7f16(0x3d0)][_0x1f7f16(0x2a3)](this);
    }),
    (VisuMZ[_0x457895(0x201)]['InjectedRngSeed'] = function () {
        const _0x1ba856 = _0x457895,
            _0x45edad = $gameTemp['_rngSeed'] || '',
            _0x1b5f34 = $gameTemp[_0x1ba856(0x2bc)] || ![],
            _0x4f166b = $gameTemp[_0x1ba856(0x421)] || ![];
        return $rngSeed(_0x45edad, _0x1b5f34, _0x4f166b);
    }),
    (Scene_Message[_0x457895(0x3dc)] = {
        choiceBgType:
            VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x3e5)][_0x457895(0x28b)] ?? 0x0,
        titleBgType: VisuMZ[_0x457895(0x201)]['Settings']['Window'][_0x457895(0x1e5)] ?? 0x0,
        advantageBgType:
            VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x3e5)]['DataCount_BgType'] ?? 0x0,
        rankBgType:
            VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x222)] ??
            0x0,
        modifierBgType:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)]['DataMod_BgType'] ?? 0x0,
        subtitleBgType:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x407)] ?? 0x0,
        effectsBgType:
            VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x3e5)][_0x457895(0x326)] ?? 0x0,
        bonusBgType:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x3c6)] ?? 0x0,
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x122)] = function () {
        const _0x2fe15c = _0x457895;
        this[_0x2fe15c(0x41b)]();
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x307)] = function () {
        const _0x4b80d4 = _0x457895;
        this[_0x4b80d4(0x314)]();
    }),
    (Scene_Message['prototype']['createAllDiceRollWindows'] = function () {
        const _0x2ee8d8 = _0x457895;
        (this[_0x2ee8d8(0x22f)](),
            this[_0x2ee8d8(0x297)](),
            this[_0x2ee8d8(0x309)](),
            this[_0x2ee8d8(0x30b)](),
            this[_0x2ee8d8(0x2d7)](),
            this[_0x2ee8d8(0x412)](),
            this[_0x2ee8d8(0x172)](),
            this[_0x2ee8d8(0x411)](),
            this[_0x2ee8d8(0x226)]());
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x22f)] = function () {
        const _0x40afdc = _0x457895,
            _0x3206c4 = this[_0x40afdc(0x1d9)](),
            _0x159274 = new Window_DiceSprite(_0x3206c4);
        (this[_0x40afdc(0x248)](_0x159274),
            (this[_0x40afdc(0x203)] = _0x159274),
            _0x159274[_0x40afdc(0x3c3)](0x2));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x1d9)] = function () {
        const _0x41f3dc = _0x457895;
        if (VisuMZ[_0x41f3dc(0x201)][_0x41f3dc(0x42e)][_0x41f3dc(0x3e5)]['Container_RectJS']) {
            if (_0x41f3dc(0x2dd) !== _0x41f3dc(0x2dd)) {
                if (this[_0x41f3dc(0x2e2)]()) return;
                if (this['_numberShiftDuration'] <= 0x0) return;
                const _0x5f3c9c = this['_numberShiftDuration'];
                ((this['_numberValue'] =
                    (this[_0x41f3dc(0x2cf)] * (_0x5f3c9c - 0x1) + this[_0x41f3dc(0x27b)]) /
                    _0x5f3c9c),
                    this[_0x41f3dc(0x29a)]--);
                this[_0x41f3dc(0x29a)] <= 0x0 &&
                    (this[_0x41f3dc(0x2cf)] = this['_numberShiftTarget']);
                const _0x3f9293 = this[_0x41f3dc(0x374)];
                ((this['_numberText'] = _0x36f649(
                    _0x60eb57[_0x41f3dc(0x23d)](this[_0x41f3dc(0x2cf)])
                )),
                    this[_0x41f3dc(0x35e)](),
                    _0x3f9293 !== this['_numberText'] && _0x4500fc[_0x41f3dc(0x1cc)]());
            } else
                return VisuMZ['DiceRollsRngSeeds'][_0x41f3dc(0x42e)][_0x41f3dc(0x3e5)][
                    _0x41f3dc(0x229)
                ][_0x41f3dc(0x2a3)](this);
        }
        const _0x16d625 = Math[_0x41f3dc(0xf6)](Math['round'](Graphics['width'] * 0.9), 0x330),
            _0x50bfd0 = Math['round']((Graphics[_0x41f3dc(0x155)] - _0x16d625) / 0x2),
            _0xfcfa02 = this['calcWindowHeight'](0x2, ![]) + this[_0x41f3dc(0x21a)](0x1, ![]),
            _0x451ff3 =
                Graphics[_0x41f3dc(0xf8)] -
                _0xfcfa02 -
                this[_0x41f3dc(0x21a)](0x4, !![]) -
                this[_0x41f3dc(0x21a)](0x1, ![]);
        return new Rectangle(_0x50bfd0, _0xfcfa02, _0x16d625, _0x451ff3);
    }),
    (Scene_Message['prototype'][_0x457895(0x297)] = function () {
        const _0x5e7e66 = _0x457895,
            _0x44fe1a = this[_0x5e7e66(0x211)](),
            _0x3d6c2d = new Window_DiceChoice(_0x44fe1a);
        (this[_0x5e7e66(0x248)](_0x3d6c2d),
            _0x3d6c2d['setHandler'](
                _0x5e7e66(0x26d),
                this['commandDiceRoll'][_0x5e7e66(0x323)](this)
            ),
            _0x3d6c2d['setHandler']('effects', this[_0x5e7e66(0x2d2)]['bind'](this)),
            _0x3d6c2d[_0x5e7e66(0x409)]('bonus', this['commandDiceBonus']['bind'](this)),
            (this[_0x5e7e66(0x119)] = _0x3d6c2d),
            _0x3d6c2d[_0x5e7e66(0x3c3)](Scene_Message[_0x5e7e66(0x3dc)][_0x5e7e66(0x381)]));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x211)] = function () {
        const _0x43cc57 = _0x457895;
        if (VisuMZ[_0x43cc57(0x201)]['Settings'][_0x43cc57(0x3e5)][_0x43cc57(0x10d)]) {
            if ('HDlTI' !== _0x43cc57(0x1d1)) _0x3101d0['playDiceFailure'](this['_naturalRoll']);
            else
                return VisuMZ[_0x43cc57(0x201)]['Settings']['Window']['Command_RectJS']['call'](
                    this
                );
        }
        const _0x3992ec = Math[_0x43cc57(0xf6)](
                Math[_0x43cc57(0x23d)](Graphics[_0x43cc57(0x155)] * 0.5),
                0x168
            ),
            _0x190eea = this[_0x43cc57(0x21a)](this['totalDiceRollChoices'](), !![]),
            _0x409058 = Math[_0x43cc57(0x23d)]((Graphics[_0x43cc57(0x155)] - _0x3992ec) / 0x2),
            _0x2d74a3 =
                Graphics[_0x43cc57(0xf8)] -
                this['calcWindowHeight'](0x4, !![]) +
                Math[_0x43cc57(0x23d)](
                    (this['calcWindowHeight'](0x4, !![]) - this[_0x43cc57(0x21a)](0x3, !![])) / 0x2
                );
        return new Rectangle(_0x409058, _0x2d74a3, _0x3992ec, _0x190eea);
    }),
    (Scene_Message['prototype'][_0x457895(0x386)] = function () {
        const _0x923472 = _0x457895;
        return VisuMZ[_0x923472(0x201)][_0x923472(0x25d)]();
    }),
    (Scene_Message['prototype'][_0x457895(0x309)] = function () {
        const _0x159ca3 = _0x457895,
            _0x5d94e8 = SceneManager[_0x159ca3(0x15c)] || {},
            _0x2057fe = _0x5d94e8[_0x159ca3(0x144)] || '';
        if (!_0x2057fe) return;
        const _0x4c0b64 = this[_0x159ca3(0x260)](),
            _0x4adbae = new Window_DiceText(_0x4c0b64);
        (this[_0x159ca3(0x248)](_0x4adbae),
            _0x4adbae[_0x159ca3(0x391)](_0x2057fe),
            (this[_0x159ca3(0x1ae)] = _0x4adbae),
            _0x4adbae['setBackgroundType'](Scene_Message[_0x159ca3(0x3dc)]['titleBgType']));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x260)] = function () {
        const _0x2b52fa = _0x457895;
        if (VisuMZ[_0x2b52fa(0x201)][_0x2b52fa(0x42e)][_0x2b52fa(0x3e5)][_0x2b52fa(0x148)]) {
            if (_0x2b52fa(0x1c8) === _0x2b52fa(0x2a7)) {
                if (_0x496cfa[_0x2b52fa(0x436)]()) {
                    const _0x829dd7 = _0x2b52fa(0x37e);
                    _0x37fe48['log'](_0x829dd7);
                }
                return ![];
            } else
                return VisuMZ[_0x2b52fa(0x201)][_0x2b52fa(0x42e)]['Window']['Title_RectJS'][
                    _0x2b52fa(0x2a3)
                ](this);
        }
        const _0x5a3471 = Math[_0x2b52fa(0xf6)](
                Math[_0x2b52fa(0x23d)](Graphics[_0x2b52fa(0x155)] * 0.8),
                0x2cc
            ),
            _0x598c94 = this[_0x2b52fa(0x21a)](0x1, ![]),
            _0x5a1e72 = Math['round']((Graphics[_0x2b52fa(0x155)] - _0x5a3471) / 0x2),
            _0x289db2 = this[_0x2b52fa(0x21a)](0x2, ![]) - _0x598c94;
        return new Rectangle(_0x5a1e72, _0x289db2, _0x5a3471, _0x598c94);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x30b)] = function () {
        const _0x2cd984 = _0x457895,
            _0x248588 = this[_0x2cd984(0x12c)](),
            _0x2dee64 = new Window_DiceData(_0x248588);
        (_0x2dee64['setDataType'](_0x2cd984(0x373)),
            this[_0x2cd984(0x248)](_0x2dee64),
            (this[_0x2cd984(0x300)] = _0x2dee64),
            _0x2dee64[_0x2cd984(0x3c3)](Scene_Message[_0x2cd984(0x3dc)][_0x2cd984(0x1e1)]));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x12c)] = function () {
        const _0x5aac40 = _0x457895;
        if (VisuMZ[_0x5aac40(0x201)][_0x5aac40(0x42e)][_0x5aac40(0x3e5)][_0x5aac40(0x1b9)]) {
            if (_0x5aac40(0x2a2) !== _0x5aac40(0x312))
                return VisuMZ[_0x5aac40(0x201)][_0x5aac40(0x42e)][_0x5aac40(0x3e5)][
                    _0x5aac40(0x1b9)
                ][_0x5aac40(0x2a3)](this);
            else {
                const _0x2e99a7 =
                    _0x5a1e94[_0x6cdef0[_0x5aac40(0x3dd)](_0x171566[_0x5aac40(0x1a1)])] ?? '6';
                switch (_0x5a259b) {
                    case 'd4':
                        return this['createDiceGraphic_D4'](_0x2e99a7);
                    case 'd6':
                        return this[_0x5aac40(0x107)](_0x2e99a7);
                    case 'd8':
                        return this[_0x5aac40(0x345)](_0x2e99a7);
                    case _0x5aac40(0x2eb):
                        return this[_0x5aac40(0x417)](_0x2e99a7);
                    case _0x5aac40(0x388):
                        return this[_0x5aac40(0xf0)](_0x2e99a7);
                    case 'd20':
                    default:
                        return this[_0x5aac40(0x3d2)](_0x2e99a7);
                }
            }
        }
        const _0x3f296f = Math['min'](
                Math[_0x5aac40(0x23d)](Graphics[_0x5aac40(0x155)] * 0.9),
                0x330
            ),
            _0x5b616d = Math[_0x5aac40(0x3f3)](_0x3f296f / 0x3),
            _0x18dff2 = this['calcWindowHeight'](0x1, ![]),
            _0x55e3e4 = Math[_0x5aac40(0x23d)]((Graphics[_0x5aac40(0x155)] - _0x3f296f) / 0x2),
            _0x34677b = this[_0x5aac40(0x21a)](0x2, ![]);
        return new Rectangle(_0x55e3e4, _0x34677b, _0x5b616d, _0x18dff2);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x2d7)] = function () {
        const _0x71bd8 = _0x457895,
            _0x4632b8 = this[_0x71bd8(0x21c)](),
            _0x307032 = new Window_DiceData(_0x4632b8);
        (_0x307032[_0x71bd8(0x389)]('rank'),
            this[_0x71bd8(0x248)](_0x307032),
            (this['_diceRoll_RankWindow'] = _0x307032),
            _0x307032['setBackgroundType'](Scene_Message['DICE_ROLL']['rankBgType']));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x21c)] = function () {
        const _0x203862 = _0x457895;
        if (VisuMZ[_0x203862(0x201)][_0x203862(0x42e)]['Window'][_0x203862(0x270)])
            return VisuMZ[_0x203862(0x201)]['Settings'][_0x203862(0x3e5)][_0x203862(0x270)][
                _0x203862(0x2a3)
            ](this);
        const _0x4a90f9 = Math['min'](Math['round'](Graphics[_0x203862(0x155)] * 0.9), 0x330),
            _0x5b2bdb = Math[_0x203862(0x215)](_0x4a90f9 / 0x3),
            _0x4d59b1 = this[_0x203862(0x21a)](0x1, ![]),
            _0x29e29c = Math[_0x203862(0x23d)]((Graphics['width'] - _0x5b2bdb) / 0x2),
            _0x319cdd = this['calcWindowHeight'](0x2, ![]);
        return new Rectangle(_0x29e29c, _0x319cdd, _0x5b2bdb, _0x4d59b1);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x412)] = function () {
        const _0x3fe294 = _0x457895,
            _0x18fc50 = this[_0x3fe294(0x3e0)](),
            _0xa1e310 = new Window_DiceData(_0x18fc50);
        (_0xa1e310['setDataType'](_0x3fe294(0x375)),
            this[_0x3fe294(0x248)](_0xa1e310),
            (this[_0x3fe294(0x3c8)] = _0xa1e310),
            _0xa1e310[_0x3fe294(0x3c3)](Scene_Message['DICE_ROLL'][_0x3fe294(0x396)]));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x3e0)] = function () {
        const _0x227bd9 = _0x457895;
        if (VisuMZ[_0x227bd9(0x201)]['Settings'][_0x227bd9(0x3e5)][_0x227bd9(0x325)])
            return VisuMZ[_0x227bd9(0x201)]['Settings']['Window'][_0x227bd9(0x325)][
                _0x227bd9(0x2a3)
            ](this);
        const _0x34406b = Math[_0x227bd9(0xf6)](
                Math[_0x227bd9(0x23d)](Graphics['width'] * 0.9),
                0x330
            ),
            _0x5f04e3 = Math[_0x227bd9(0x3f3)](_0x34406b / 0x3),
            _0xc8ce = this[_0x227bd9(0x21a)](0x1, ![]),
            _0x4f0026 = Math['round']((Graphics[_0x227bd9(0x155)] + _0x5f04e3) / 0x2),
            _0x5cfb13 = this[_0x227bd9(0x21a)](0x2, ![]);
        return new Rectangle(_0x4f0026, _0x5cfb13, _0x5f04e3, _0xc8ce);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x172)] = function () {
        const _0x2a9637 = _0x457895,
            _0x7a1a5b = TextManager[_0x2a9637(0x1cd)]();
        if (!_0x7a1a5b) return;
        const _0x4ff078 = this[_0x2a9637(0x22e)](),
            _0x36826a = new Window_DiceText(_0x4ff078);
        (this[_0x2a9637(0x248)](_0x36826a),
            _0x36826a[_0x2a9637(0x391)](_0x7a1a5b),
            (this[_0x2a9637(0x2d1)] = _0x36826a),
            _0x36826a['setBackgroundType'](Scene_Message[_0x2a9637(0x3dc)]['subtitleBgType']));
    }),
    (Scene_Message['prototype'][_0x457895(0x22e)] = function () {
        const _0x5b1d2e = _0x457895;
        if (VisuMZ[_0x5b1d2e(0x201)][_0x5b1d2e(0x42e)]['Window']['Subtitle_RectJS'])
            return VisuMZ[_0x5b1d2e(0x201)][_0x5b1d2e(0x42e)]['Window'][_0x5b1d2e(0x1bb)][
                _0x5b1d2e(0x2a3)
            ](this);
        const _0x586f64 = Math[_0x5b1d2e(0xf6)](
                Math[_0x5b1d2e(0x23d)](Graphics[_0x5b1d2e(0x155)] * 0.8),
                0x2cc
            ),
            _0x4aa65f = this[_0x5b1d2e(0x21a)](0x1, ![]),
            _0x4211da = Math['round']((Graphics['width'] - _0x586f64) / 0x2),
            _0xd529ef = Graphics['height'] - this[_0x5b1d2e(0x21a)](0x4, !![]) - _0x4aa65f;
        return new Rectangle(_0x4211da, _0xd529ef, _0x586f64, _0x4aa65f);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x411)] = function () {
        const _0x2a7c5b = _0x457895,
            _0x1a2350 = this[_0x2a7c5b(0x3e6)](),
            _0x3268d1 = new Window_DiceEffectsList(_0x1a2350);
        (this[_0x2a7c5b(0x248)](_0x3268d1),
            _0x3268d1[_0x2a7c5b(0x409)]('cancel', this[_0x2a7c5b(0x1d8)][_0x2a7c5b(0x323)](this)),
            (this['_diceRoll_EffectsWindow'] = _0x3268d1),
            _0x3268d1['setBackgroundType'](Scene_Message[_0x2a7c5b(0x3dc)][_0x2a7c5b(0x127)]));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x226)] = function () {
        const _0x503f41 = _0x457895,
            _0x424bfa = this[_0x503f41(0x3e6)](),
            _0x5c8c1c = new Window_DiceBonusList(_0x424bfa);
        (this['addChild'](_0x5c8c1c),
            _0x5c8c1c[_0x503f41(0x409)](_0x503f41(0x40a), this[_0x503f41(0x130)]['bind'](this)),
            _0x5c8c1c['setHandler'](
                _0x503f41(0x1c2),
                this[_0x503f41(0x12d)][_0x503f41(0x323)](this)
            ),
            (this['_diceRoll_BonusWindow'] = _0x5c8c1c),
            _0x5c8c1c[_0x503f41(0x3c3)](Scene_Message[_0x503f41(0x3dc)][_0x503f41(0x31d)]));
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x3e6)] = function () {
        const _0x3e144a = _0x457895;
        if (VisuMZ[_0x3e144a(0x201)]['Settings']['Window']['List_RectJS'])
            return VisuMZ[_0x3e144a(0x201)][_0x3e144a(0x42e)][_0x3e144a(0x3e5)]['List_RectJS'][
                'call'
            ](this);
        const _0x29a00 = Math[_0x3e144a(0xf6)](Graphics[_0x3e144a(0x155)], 0x3f8),
            _0x475f38 = this[_0x3e144a(0x21a)](0x4, !![]),
            _0x1672a4 = Math['round']((Graphics['width'] - _0x29a00) / 0x2),
            _0x57ea26 = Graphics[_0x3e144a(0xf8)] - _0x475f38;
        return new Rectangle(_0x1672a4, _0x57ea26, _0x29a00, _0x475f38);
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x13e)] = function () {
        const _0x22b3dd = _0x457895,
            _0x24abf9 = [
                _0x22b3dd(0x1ae),
                _0x22b3dd(0x300),
                _0x22b3dd(0x384),
                _0x22b3dd(0x3c8),
                _0x22b3dd(0x2d1),
            ];
        for (const _0xeb1eea of _0x24abf9) {
            if (_0x22b3dd(0x2cc) === _0x22b3dd(0x243))
                (_0x476576(_0x22b3dd(0x24c)[_0x22b3dd(0x253)](_0x3dc70f, _0x2933d4, _0xead9da)),
                    _0x35be48['exit']());
            else {
                if (!this[_0xeb1eea]) continue;
                this[_0xeb1eea][_0x22b3dd(0x19d)]();
            }
        }
    }),
    (Scene_Message['prototype'][_0x457895(0x314)] = function () {
        const _0x5a1125 = _0x457895,
            _0x4e1277 = [
                '_diceRoll_SpriteWindow',
                _0x5a1125(0x119),
                _0x5a1125(0x1ae),
                _0x5a1125(0x300),
                _0x5a1125(0x384),
                _0x5a1125(0x3c8),
                _0x5a1125(0x2d1),
                _0x5a1125(0x2c7),
                _0x5a1125(0x422),
            ];
        for (const _0x5ab738 of _0x4e1277) {
            if (!this[_0x5ab738]) continue;
            (this['removeChild'](this[_0x5ab738]), (this[_0x5ab738] = undefined));
        }
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x184)] = function () {
        const _0x1e2322 = _0x457895;
        (this[_0x1e2322(0x203)]['startDiceRoll'](), this[_0x1e2322(0x119)][_0x1e2322(0x19d)]());
    }),
    (Scene_Message['prototype']['commandDiceEffects'] = function () {
        const _0x1cbd5b = _0x457895;
        (this['_diceRoll_ChoiceWindow'][_0x1cbd5b(0x19d)](),
            this['_diceRoll_EffectsWindow'][_0x1cbd5b(0x13d)](),
            this[_0x1cbd5b(0x2c7)][_0x1cbd5b(0x112)](),
            this[_0x1cbd5b(0x2c7)][_0x1cbd5b(0x188)]());
    }),
    (Scene_Message[_0x457895(0x428)]['onDiceRollEffectCancel'] = function () {
        const _0x2d985f = _0x457895;
        (this[_0x2d985f(0x2c7)][_0x2d985f(0x19d)](),
            this[_0x2d985f(0x2c7)][_0x2d985f(0x2b7)](),
            this[_0x2d985f(0x119)][_0x2d985f(0x13d)](),
            this[_0x2d985f(0x119)][_0x2d985f(0x112)]());
    }),
    (Scene_Message['prototype'][_0x457895(0x31b)] = function () {
        const _0x2e69d6 = _0x457895;
        (this[_0x2e69d6(0x119)][_0x2e69d6(0x19d)](),
            this['_diceRoll_BonusWindow'][_0x2e69d6(0x13d)](),
            this[_0x2e69d6(0x422)][_0x2e69d6(0x112)](),
            this[_0x2e69d6(0x422)][_0x2e69d6(0x188)]());
    }),
    (Scene_Message['prototype'][_0x457895(0x130)] = function () {
        const _0x462882 = _0x457895;
        (this['_diceRoll_BonusWindow']['close'](),
            this[_0x462882(0x422)][_0x462882(0x2b7)](),
            this['_diceRoll_ChoiceWindow']['open'](),
            this[_0x462882(0x119)][_0x462882(0x112)](),
            this[_0x462882(0x119)]['refresh']());
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x12d)] = function () {
        const _0x1f845d = _0x457895,
            _0x51bbd0 = this[_0x1f845d(0x422)][_0x1f845d(0x21b)]();
        if (_0x51bbd0) {
            if ('jLeiB' !== _0x1f845d(0x287)) this[_0x1f845d(0x41e)](_0x51bbd0);
            else {
                const _0x22b68f = this['diceRollEffectsWindowRect'](),
                    _0x5660e0 = new _0x4676e5(_0x22b68f);
                (this[_0x1f845d(0x248)](_0x5660e0),
                    _0x5660e0[_0x1f845d(0x409)](
                        _0x1f845d(0x40a),
                        this[_0x1f845d(0x130)][_0x1f845d(0x323)](this)
                    ),
                    _0x5660e0['setHandler'](
                        _0x1f845d(0x1c2),
                        this[_0x1f845d(0x12d)][_0x1f845d(0x323)](this)
                    ),
                    (this[_0x1f845d(0x422)] = _0x5660e0),
                    _0x5660e0[_0x1f845d(0x3c3)](_0x23e8f[_0x1f845d(0x3dc)]['bonusBgType']));
            }
        }
        (this[_0x1f845d(0x422)][_0x1f845d(0x112)](), this[_0x1f845d(0x422)][_0x1f845d(0x188)]());
    }),
    (Scene_Message[_0x457895(0x428)][_0x457895(0x41e)] = function (_0x16122e) {
        const _0x1acfcb = _0x457895;
        SceneManager[_0x1acfcb(0x419)](_0x16122e);
        if (_0x16122e[_0x1acfcb(0x123)]) {
            if ('TRLIL' === _0x1acfcb(0x335)) {
                if (
                    _0x3ed556[_0x1acfcb(0x2ea)][_0x1acfcb(0x16d)] &&
                    _0x370420[_0x1acfcb(0x2ea)][_0x1acfcb(0x16d)]['length'] > 0x0
                )
                    return _0x398d32[_0x1acfcb(0x2ea)]['colors'];
            } else $gameTemp['requestAnimation']([$gamePlayer], _0x16122e[_0x1acfcb(0x123)]);
        }
        if (_0x16122e[_0x1acfcb(0x1ee)]) {
            if (_0x1acfcb(0x225) === _0x1acfcb(0x225)) {
                const _0x4a5b80 = $gameVariables[_0x1acfcb(0x3a3)](_0x16122e[_0x1acfcb(0x1ee)]),
                    _0x4e0f55 = Math[_0x1acfcb(0x137)](_0x16122e[_0x1acfcb(0x17c)] || 0x1, 0x1),
                    _0x13a63d = _0x4a5b80 - _0x4e0f55;
                $gameVariables[_0x1acfcb(0x176)](_0x16122e['VariableCostID'], _0x13a63d);
            } else
                (_0x11f4f5[_0x1acfcb(0x428)][_0x1acfcb(0x31e)][_0x1acfcb(0x2a3)](this, _0x314796),
                    (this[_0x1acfcb(0x250)] = 0x0),
                    this[_0x1acfcb(0x13d)]());
        }
        if (_0x16122e['ItemCostID']) {
            const _0xae448d = $dataItems[_0x16122e[_0x1acfcb(0x342)]],
                _0x238d92 = Math[_0x1acfcb(0x137)](_0x16122e[_0x1acfcb(0x354)] || 0x1, 0x1);
            $gameParty['loseItem'](_0xae448d, _0x238d92, ![]);
        }
        if (_0x16122e[_0x1acfcb(0x343)]) {
            const _0x336c10 = $dataWeapons[_0x16122e[_0x1acfcb(0x343)]],
                _0x1b2bc5 = Math['max'](_0x16122e[_0x1acfcb(0x310)] || 0x1, 0x1);
            $gameParty[_0x1acfcb(0x3c5)](_0x336c10, _0x1b2bc5, ![]);
        }
        if (_0x16122e[_0x1acfcb(0x11c)]) {
            const _0x366103 = $dataArmors[_0x16122e['ArmorCostID']],
                _0x2e76c6 = Math[_0x1acfcb(0x137)](_0x16122e[_0x1acfcb(0x301)] || 0x1, 0x1);
            $gameParty[_0x1acfcb(0x3c5)](_0x366103, _0x2e76c6, ![]);
        }
        if (_0x16122e[_0x1acfcb(0x369)]) {
            const _0x47e191 = $gameActors['actor'](_0x16122e[_0x1acfcb(0x29b)]);
            if (_0x47e191) {
                const _0x9816bc = $dataSkills[_0x16122e[_0x1acfcb(0x369)]];
                _0x47e191['paySkillCost'](_0x9816bc);
            }
        }
        if (_0x16122e[_0x1acfcb(0x3d4)]) {
            if (_0x1acfcb(0x230) !== 'iTuSN')
                ((_0x16122e[_0x1acfcb(0x295)] = _0x16122e['CurrentUses'] || 0x0),
                    (_0x16122e['CurrentUses'] += 0x1));
            else {
                const _0x2593b6 = _0x21f827[_0x49e3b3[_0x1acfcb(0x11c)]],
                    _0x58c303 = _0x3c71ac[_0x1acfcb(0x137)](
                        _0x593116[_0x1acfcb(0x301)] || 0x1,
                        0x1
                    );
                _0x3aefe3[_0x1acfcb(0x3c5)](_0x2593b6, _0x58c303, ![]);
            }
        }
        _0x16122e[_0x1acfcb(0x20a)] && _0x16122e[_0x1acfcb(0x20a)]();
    }));
function Sprite_DiceCheck() {
    const _0x24cd44 = _0x457895;
    this[_0x24cd44(0x31e)](...arguments);
}
((Sprite_DiceCheck[_0x457895(0x428)] = Object[_0x457895(0x3cd)](Sprite['prototype'])),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2ed)] = Sprite_DiceCheck),
    (Sprite_DiceCheck[_0x457895(0x37f)] = {
        d4: VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)]['Dice']['D4'] ?? {},
        d6: VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x30f)]['D6'] ?? {},
        d8: VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x30f)]['D8'] ?? {},
        d10: VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x1a5)] ?? {},
        d12: VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)]['D12'] ?? {},
        d20: VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x181)] ?? {},
        offsetX:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)]['ArrangeOffsetX'] ?? 0x0,
        offsetY:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)]['ArrangeOffsetY'] ?? 0x0,
        fadeInDuration:
            VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x30f)][_0x457895(0x1ad)] ?? 0x1e,
        fadeOutDuration:
            VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)]['fadeOutDuration'] ?? 0x14,
        finalizeDelay:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)]['finalizeDelay'] ?? 0x3c,
        rollDuration:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)][_0x457895(0x1d6)] ?? 0x3c,
        rollDelay: VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)][_0x457895(0x11a)] ?? 0xa,
        rollHeight:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)]['rollHeight'] ?? 0xa0,
        rotateSpeed: VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)]['rotateSpeed'] ?? 0xf,
        moveDuration: VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Dice'][_0x457895(0x2ab)] ?? 0x14,
        scaleDuration: VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Dice'][_0x457895(0x2a4)] ?? 0x28,
        numberDuration:
            VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)][_0x457895(0xef)] ?? 0x4,
        maxNumberDuration:
            VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x30f)]['maxNumberDuration'] ??
            0x50,
        numberColorShift:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)]['Dice']['numberColorShift'] ?? !![],
        rollModDelay:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x30f)]['rollModDelay'] ?? 0x3c,
    }),
    (Sprite_DiceCheck['prototype']['initialize'] = function () {
        const _0x20b4f6 = _0x457895;
        (Sprite['prototype'][_0x20b4f6(0x31e)]['call'](this),
            this['initMembers'](),
            this[_0x20b4f6(0x1f1)](),
            this[_0x20b4f6(0x217)](),
            this[_0x20b4f6(0x36c)]());
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x38d)] = function () {
        const _0x1876b1 = _0x457895;
        ((this[_0x1876b1(0x351)]['x'] = 0.5),
            (this[_0x1876b1(0x351)]['y'] = 0.5),
            (this[_0x1876b1(0x29d)] = 0x0),
            (this[_0x1876b1(0x15b)] = 'd%1'[_0x1876b1(0x253)](
                VisuMZ[_0x1876b1(0x201)][_0x1876b1(0x302)]()
            )),
            (this[_0x1876b1(0x2ad)] = 0x0),
            (this[_0x1876b1(0x21e)] = 0xff),
            (this[_0x1876b1(0x255)] = 0x0),
            (this['_rollDuration'] = 0x0),
            (this[_0x1876b1(0x3d8)] = 0x0),
            (this[_0x1876b1(0x393)] = 0x0),
            (this['_moveDuration'] = 0x0),
            (this['_moveTargetX'] = 0x0),
            (this['_numberShiftDuration'] = 0x0),
            (this[_0x1876b1(0x27b)] = 0x0),
            (this[_0x1876b1(0x239)] = 0x0),
            (this[_0x1876b1(0x42b)] = 0x0));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2f8)] = function () {
        const _0x566df7 = _0x457895,
            _0x93d281 = this[_0x566df7(0x15b)] || _0x566df7(0xfe);
        return Sprite_DiceCheck['SETTINGS'][_0x93d281];
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x29c)] = function () {
        const _0x26a64a = _0x457895,
            _0x2f0a23 = SceneManager[_0x26a64a(0x15c)] || {};
        if (_0x2f0a23[_0x26a64a(0x2ea)]) {
            const _0x28d1d5 = _0x26a64a(0x2b8)[_0x26a64a(0x253)](
                this[_0x26a64a(0x15b)] || _0x26a64a(0xfe)
            );
            if (_0x2f0a23[_0x26a64a(0x2ea)][_0x28d1d5]) {
                if (_0x26a64a(0x19a) !== _0x26a64a(0x19a))
                    _0x51ddcf += _0x26a64a(0x18e)[_0x26a64a(0x253)](
                        this[_0x26a64a(0x17a)]()[_0x26a64a(0x23c)]()
                    );
                else return _0x2f0a23[_0x26a64a(0x2ea)][_0x28d1d5];
            }
        }
        const _0xe3ca27 = this['diceSettings']();
        return _0xe3ca27[_0x26a64a(0x286)] || [];
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x2e7)] = function () {
        const _0xdb91f1 = _0x457895,
            _0xb75f12 = SceneManager[_0xdb91f1(0x15c)] || {};
        if (_0xb75f12[_0xdb91f1(0x2ea)]) {
            if (_0xdb91f1(0x34d) !== _0xdb91f1(0x2ae)) {
                if (
                    _0xb75f12['DiceSeed'][_0xdb91f1(0x16d)] &&
                    _0xb75f12[_0xdb91f1(0x2ea)][_0xdb91f1(0x16d)][_0xdb91f1(0x1a1)] > 0x0
                )
                    return _0xdb91f1(0x216) === _0xdb91f1(0x216)
                        ? _0xb75f12[_0xdb91f1(0x2ea)][_0xdb91f1(0x16d)]
                        : this[_0xdb91f1(0x29a)] > 0x0;
            } else this[_0xdb91f1(0x3c9)] = _0x16ca8a[_0xdb91f1(0x13b)](_0x681805);
        }
        const _0x5a0175 = this[_0xdb91f1(0x2f8)]();
        return _0x5a0175[_0xdb91f1(0x16d)] || [0x8, 0xa, 0x15, 0x11, 0x1d, 0x17, 0x1f, 0x1b];
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x1f1)] = function () {
        const _0x48c95e = _0x457895,
            _0x277a75 = this['getImageFilenames']();
        if (_0x277a75[_0x48c95e(0x1a1)] > 0x0) {
            if (_0x48c95e(0x3da) !== 'HkYWC') {
                const _0x250694 = new _0x45770c();
                ((_0x250694[_0x48c95e(0x3c9)] = new _0x38229a(0x64, 0x64)),
                    this['addChild'](_0x250694));
                const _0x17ba13 = this[_0x48c95e(0x2f8)]();
                ((_0x250694['x'] = _0x17ba13[_0x48c95e(0x3db)] || 0x0),
                    (_0x250694['y'] = _0x17ba13[_0x48c95e(0x1fc)] || 0x0),
                    (_0x250694[_0x48c95e(0x351)]['x'] = 0.5),
                    (_0x250694[_0x48c95e(0x351)]['y'] = 0.5),
                    (this['_numberSprite'] = _0x250694),
                    (this[_0x48c95e(0x2cf)] = 0x0),
                    (this[_0x48c95e(0x374)] = '?'),
                    (this['_numberColor'] = 0x0),
                    this[_0x48c95e(0x35e)]());
            } else this[_0x48c95e(0x3c9)] = ImageManager[_0x48c95e(0x13b)](_0x277a75);
        } else {
            if (_0x48c95e(0x1fe) !== _0x48c95e(0x1fe))
                _0xa02e6[_0x48c95e(0x303)](_0xd65c99, _0x5d0302, _0x320e1a);
            else {
                const _0x36f0dc = this['_settingsKey'],
                    _0x2ba60c = this[_0x48c95e(0x2e7)]();
                this['bitmap'] = ImageManager[_0x48c95e(0x1be)](_0x36f0dc, _0x2ba60c);
            }
        }
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x217)] = function () {
        const _0x584774 = _0x457895,
            _0x5bb465 = new Sprite();
        ((_0x5bb465[_0x584774(0x3c9)] = new Bitmap(0x64, 0x64)), this[_0x584774(0x248)](_0x5bb465));
        const _0x32f8c7 = this['diceSettings']();
        ((_0x5bb465['x'] = _0x32f8c7['fontOffsetX'] || 0x0),
            (_0x5bb465['y'] = _0x32f8c7[_0x584774(0x1fc)] || 0x0),
            (_0x5bb465[_0x584774(0x351)]['x'] = 0.5),
            (_0x5bb465['anchor']['y'] = 0.5),
            (this[_0x584774(0x238)] = _0x5bb465),
            (this[_0x584774(0x2cf)] = 0x0),
            (this[_0x584774(0x374)] = '?'),
            (this['_numberColor'] = 0x0),
            this[_0x584774(0x35e)]());
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x36c)] = function () {
        const _0x49dc27 = _0x457895;
        ((this[_0x49dc27(0x1e4)] = new PIXI[_0x49dc27(0x134)][_0x49dc27(0x228)]()),
            (this[_0x49dc27(0x1e4)]['blur'] = 0x0),
            (this[_0x49dc27(0x134)] = this['filters'] || []),
            this['filters'][_0x49dc27(0x3af)](this[_0x49dc27(0x1e4)]));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x29f)] = function (_0x27c6d3, _0x54175d) {
        const _0x3f7651 = _0x457895;
        ((this['_baseX'] = _0x27c6d3), (this[_0x3f7651(0x141)] = _0x54175d));
        {
            const _0x12e64f = Sprite_DiceCheck['SETTINGS'];
            ((this[_0x3f7651(0xfd)] += _0x12e64f[_0x3f7651(0x152)]),
                (this[_0x3f7651(0x141)] += _0x12e64f['offsetY']));
        }
        {
            if ('zHJVJ' !== _0x3f7651(0x18c))
                (_0x329521[_0x3f7651(0x2f2)](_0x35556['modDownColor']),
                    _0x5acbc5 &&
                        _0x60e567[_0x3f7651(0x391)](
                            _0x1c8ad5[_0x3f7651(0x3dc)]['subtitle'][_0x3f7651(0x361)]
                        ));
            else {
                const _0x3fcc61 = this['diceSettings']();
                ((this[_0x3f7651(0xfd)] += _0x3fcc61[_0x3f7651(0x101)]),
                    (this[_0x3f7651(0x141)] += _0x3fcc61[_0x3f7651(0x16f)]));
            }
        }
        ((this['x'] = this[_0x3f7651(0xfd)]),
            (this['y'] = this[_0x3f7651(0x141)]),
            this[_0x3f7651(0x3fd)]());
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x30c)] = function (_0x3f911a) {
        const _0x430bcb = _0x457895;
        ((this['_baseScale'] = _0x3f911a),
            (this[_0x430bcb(0x344)]['x'] = this['scale']['y'] = _0x3f911a));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0xf3)] = function (_0x4156aa) {
        const _0x47dfed = _0x457895,
            _0x495cb4 = Sprite_DiceCheck[_0x47dfed(0x37f)];
        ((this[_0x47dfed(0x2c6)] = _0x495cb4[_0x47dfed(0x1d6)]),
            (this[_0x47dfed(0x3d8)] = this[_0x47dfed(0x2c6)]),
            (this[_0x47dfed(0x393)] = (_0x4156aa || 0x0) * (_0x495cb4[_0x47dfed(0x11a)] || 0x0)));
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x20d)] = function () {
        const _0xc38e39 = _0x457895;
        (Sprite[_0xc38e39(0x428)]['update'][_0xc38e39(0x2a3)](this),
            this['updateRoll'](),
            this[_0xc38e39(0x196)](),
            this['updateMove'](),
            this[_0xc38e39(0x31f)](),
            this[_0xc38e39(0x315)](),
            this['updateScaleEffect'](),
            this[_0xc38e39(0x263)]());
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x3c0)] = function () {
        const _0x270132 = _0x457895;
        if (this[_0x270132(0x405)]()) return !![];
        if (this[_0x270132(0x2e2)]()) return !![];
        if (this[_0x270132(0x2cd)]()) return !![];
        if (this[_0x270132(0x268)]()) return !![];
        return ![];
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x18b)] = function () {
        const _0x35a371 = _0x457895;
        if (this[_0x35a371(0x393)] > 0x0) return this[_0x35a371(0x393)]--;
        if (this['_rollDuration'] <= 0x0) return;
        const _0x21e266 = this[_0x35a371(0x2c6)],
            _0x5022d1 = this[_0x35a371(0x3d8)],
            _0xb4304e = _0x5022d1 - _0x21e266,
            _0x1cd4e6 = _0x5022d1 / 0x2,
            _0x32fee0 = Sprite_DiceCheck[_0x35a371(0x37f)]['rollHeight'] || 0x1,
            _0x282c24 = -_0x32fee0 / Math[_0x35a371(0x2d4)](_0x1cd4e6, 0x2),
            _0x13757d = -(
                _0x282c24 * Math[_0x35a371(0x2d4)](_0xb4304e - _0x1cd4e6, 0x2) +
                _0x32fee0
            );
        ((this['y'] = this[_0x35a371(0x141)] + _0x13757d),
            this[_0x35a371(0x376)](),
            _0x21e266 === _0x5022d1 && SoundManager[_0x35a371(0x267)](),
            this[_0x35a371(0x2c6)]--,
            this[_0x35a371(0x2c6)] <= 0x0 &&
                ((this['y'] = this['_baseY']), this[_0x35a371(0x376)](!![])));
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x405)] = function () {
        const _0x47b40d = _0x457895;
        return this[_0x47b40d(0x393)] > 0x0 || this['_rollDuration'] > 0x0;
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x196)] = function () {
        const _0xce2a52 = _0x457895;
        if (this['y'] === this[_0xce2a52(0x141)]) {
            if (this['angle'] !== 0x0) this[_0xce2a52(0x3bb)] = 0x0;
            return;
        }
        const _0x5ded1d = Sprite_DiceCheck[_0xce2a52(0x37f)][_0xce2a52(0x40d)] || 0x0;
        this[_0xce2a52(0x3bb)] += _0x5ded1d;
    }),
    (Sprite_DiceCheck[_0x457895(0x428)]['updateMove'] = function () {
        const _0xe92d74 = _0x457895;
        if (this[_0xe92d74(0x371)] <= 0x0) return;
        const _0xa9f39d = this['_moveDuration'];
        ((this['x'] = (this['x'] * (_0xa9f39d - 0x1) + this['_moveTargetX']) / _0xa9f39d),
            this[_0xe92d74(0x371)]--);
        if (this[_0xe92d74(0x371)] <= 0x0) {
            if (_0xe92d74(0x19b) === _0xe92d74(0x319)) {
                if (this['_finalizeDuration'] > 0x0) return this['_finalizeDuration']--;
                if (this[_0xe92d74(0x2ad)] <= 0x0) return;
                const _0x238486 = this[_0xe92d74(0x2ad)];
                ((this[_0xe92d74(0x29d)] =
                    (this[_0xe92d74(0x29d)] * (_0x238486 - 0x1) + this[_0xe92d74(0x21e)]) /
                    _0x238486),
                    this[_0xe92d74(0x2ad)]--,
                    this[_0xe92d74(0x2ad)] <= 0x0 &&
                        (this[_0xe92d74(0x29d)] = this[_0xe92d74(0x21e)]));
            } else this['x'] = this['_moveTargetX'];
        }
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x24d)] = function (_0x76cf53) {
        const _0x3d2d01 = _0x457895,
            _0x2d23af = Sprite_DiceCheck[_0x3d2d01(0x37f)],
            _0x20c757 = this[_0x3d2d01(0x2f8)]();
        ((_0x76cf53 = _0x76cf53 || 0x0),
            (_0x76cf53 += _0x2d23af[_0x3d2d01(0x152)] || 0x0),
            (_0x76cf53 += _0x20c757[_0x3d2d01(0x101)] || 0x0));
        if (this['x'] === _0x76cf53) return;
        ((this[_0x3d2d01(0x1ec)] = _0x76cf53),
            (this[_0x3d2d01(0x371)] = _0x2d23af[_0x3d2d01(0x2ab)] || 0x1));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2e2)] = function () {
        return this['_moveDuration'] > 0x0;
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x31f)] = function () {
        const _0x3a9424 = _0x457895;
        if (this['isMoving']()) return;
        if (this[_0x3a9424(0x29a)] <= 0x0) return;
        const _0x477bde = this['_numberShiftDuration'];
        ((this[_0x3a9424(0x2cf)] =
            (this[_0x3a9424(0x2cf)] * (_0x477bde - 0x1) + this[_0x3a9424(0x27b)]) / _0x477bde),
            this[_0x3a9424(0x29a)]--);
        this[_0x3a9424(0x29a)] <= 0x0 &&
            ('kGFWj' === _0x3a9424(0x168)
                ? (this[_0x3a9424(0x2cf)] = this[_0x3a9424(0x27b)])
                : this['playDiceSound'](_0xd32ec0 ? _0x3a9424(0x2a5) : _0x3a9424(0x1ed)));
        const _0xaf5dbe = this['_numberText'];
        ((this[_0x3a9424(0x374)] = String(Math['round'](this[_0x3a9424(0x2cf)]))),
            this['drawNumber'](),
            _0xaf5dbe !== this[_0x3a9424(0x374)] && SoundManager[_0x3a9424(0x1cc)]());
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x376)] = function (_0x453517) {
        const _0x3884e1 = _0x457895,
            _0xa71fed = SceneManager[_0x3884e1(0x15c)] || {},
            _0xbe6a73 = _0xa71fed['DiceSeed'] || {},
            _0x3ac703 = VisuMZ[_0x3884e1(0x201)][_0x3884e1(0x302)]();
        if (
            _0x453517 &&
            _0xbe6a73[_0x3884e1(0x35a)] &&
            _0xbe6a73[_0x3884e1(0x35a)] !== _0x3884e1(0x317)
        ) {
            if (_0x3884e1(0x157) === _0x3884e1(0x157)) {
                const _0x1b47f5 = _0xbe6a73[_0x3884e1(0x35a)],
                    _0x9ab15f = _0xbe6a73[_0x3884e1(0x245)],
                    _0x501052 = _0xbe6a73['ApplyUnique'];
                this['_numberValue'] = $gameSystem[_0x3884e1(0x282)](
                    0x1,
                    _0x3ac703,
                    _0x1b47f5,
                    _0x9ab15f,
                    _0x501052
                );
            } else _0x57b8cd = _0x17d67a !== 0x1;
        } else
            _0x3884e1(0x1c0) !== _0x3884e1(0x231)
                ? (this[_0x3884e1(0x2cf)] = Math[_0x3884e1(0x3dd)](_0x3ac703) + 0x1)
                : ((_0xd768a6[_0x3884e1(0x295)] = _0x5a1d0a[_0x3884e1(0x295)] || 0x0),
                  (_0x2eb414[_0x3884e1(0x295)] += 0x1));
        ((this[_0x3884e1(0x374)] = String(Math[_0x3884e1(0x23d)](this[_0x3884e1(0x2cf)]))),
            this[_0x3884e1(0x35e)]());
    }),
    (Sprite_DiceCheck[_0x457895(0x428)]['drawNumber'] = function () {
        const _0x1b6add = _0x457895,
            _0x5b53d3 = this[_0x1b6add(0x238)],
            _0x1f3fa2 = _0x5b53d3[_0x1b6add(0x3c9)],
            _0x12c2bf = this['diceSettings']();
        (_0x1f3fa2['clear'](),
            (_0x1f3fa2['fontFace'] = $gameSystem[_0x1b6add(0x1ab)]()),
            (_0x1f3fa2[_0x1b6add(0x362)] =
                _0x12c2bf[_0x1b6add(0x362)] ?? $gameSystem[_0x1b6add(0x1e2)]() + 0x4),
            (_0x1f3fa2['outlineColor'] = _0x1b6add(0x438)),
            (_0x1f3fa2['outlineWidth'] = _0x12c2bf['outlineWidth'] ?? 0x4),
            (_0x1f3fa2[_0x1b6add(0x27e)] = ColorManager[_0x1b6add(0x21d)](this['_numberColor'])),
            _0x1f3fa2['drawText'](this[_0x1b6add(0x374)], 0x0, 0x0, 0x64, 0x64, 'center'));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2f2)] = function (_0x24512d) {
        const _0x8b521b = _0x457895;
        if (!Sprite_DiceCheck['SETTINGS'][_0x8b521b(0xf2)]) return;
        ((this[_0x8b521b(0x128)] = _0x24512d), this[_0x8b521b(0x35e)]());
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2d0)] = function (_0x270d15, _0x5d39dc) {
        const _0x5ab760 = _0x457895;
        _0x5d39dc && ((this[_0x5ab760(0x2cf)] = 0x1), (this[_0x5ab760(0x374)] = '1'));
        if (this['number']() === _0x270d15) return;
        const _0x5a740d = Sprite_DiceCheck[_0x5ab760(0x37f)],
            _0x1be507 = Math['abs'](this[_0x5ab760(0x3ae)]() - _0x270d15),
            _0x22afdc = _0x5a740d[_0x5ab760(0xef)] || 0x1,
            _0xcc71e = _0x5a740d[_0x5ab760(0x28c)] || 0x1;
        ((this['_numberShiftTarget'] = _0x270d15),
            (this[_0x5ab760(0x29a)] = (_0x22afdc * _0x1be507)['clamp'](0x1, _0xcc71e)));
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x3ae)] = function () {
        const _0x2c3dfe = _0x457895;
        return Math[_0x2c3dfe(0x23d)](this[_0x2c3dfe(0x2cf)]);
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x2cd)] = function () {
        const _0x30cf1b = _0x457895;
        return this[_0x30cf1b(0x29a)] > 0x0;
    }),
    (Sprite_DiceCheck[_0x457895(0x428)]['updateOpacity'] = function () {
        const _0xc7b99d = _0x457895;
        if (this['_finalizeDuration'] > 0x0) return this[_0xc7b99d(0x255)]--;
        if (this[_0xc7b99d(0x2ad)] <= 0x0) return;
        const _0xfcd4ba = this[_0xc7b99d(0x2ad)];
        ((this[_0xc7b99d(0x29d)] =
            (this['opacity'] * (_0xfcd4ba - 0x1) + this['_fadeTarget']) / _0xfcd4ba),
            this[_0xc7b99d(0x2ad)]--,
            this[_0xc7b99d(0x2ad)] <= 0x0 &&
                (_0xc7b99d(0x334) !== _0xc7b99d(0x334)
                    ? this['meetsDiceAutoEffectConditions'](_0x45b372) &&
                      this[_0xc7b99d(0x419)](_0x456c43)
                    : (this[_0xc7b99d(0x29d)] = this[_0xc7b99d(0x21e)])));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)]['startFadeIn'] = function () {
        const _0x12e699 = _0x457895,
            _0x5ff3ff = Sprite_DiceCheck[_0x12e699(0x37f)];
        ((this[_0x12e699(0x2ad)] = _0x5ff3ff['fadeInDuration']), (this[_0x12e699(0x21e)] = 0xff));
    }),
    (Sprite_DiceCheck['prototype'][_0x457895(0x37a)] = function (_0x3645c6) {
        const _0x40d666 = _0x457895,
            _0x130255 = Sprite_DiceCheck[_0x40d666(0x37f)];
        ((this[_0x40d666(0x2ad)] = _0x130255[_0x40d666(0x291)]),
            (this['_fadeTarget'] = 0x0),
            _0x3645c6 && (this[_0x40d666(0x255)] = _0x130255[_0x40d666(0x12f)] || 0x1));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x1f3)] = function () {
        const _0xee179e = _0x457895;
        if (this[_0xee179e(0x2e2)]()) return;
        if (this[_0xee179e(0x239)] <= 0x0) return;
        const _0x5c6b44 = this[_0xee179e(0x239)];
        ((this[_0xee179e(0x344)]['x'] =
            (this[_0xee179e(0x344)]['x'] * (_0x5c6b44 - 0x1) + this[_0xee179e(0x42b)]) / _0x5c6b44),
            (this[_0xee179e(0x344)]['y'] =
                (this[_0xee179e(0x344)]['y'] * (_0x5c6b44 - 0x1) + this[_0xee179e(0x42b)]) /
                _0x5c6b44),
            this['_scaleDuration']--);
        if (this[_0xee179e(0x239)] <= 0x0) {
            if (_0xee179e(0x199) !== _0xee179e(0x199)) {
                const _0x23c637 =
                    _0x44373a['x'] +
                    _0x4b27fa[_0xee179e(0x3f3)]((_0x444ff8['width'] - _0x535a74) / 0x2);
                this[_0xee179e(0x3f2)](_0x3d880b, _0x23c637, _0x1fe1a3['y'], _0x2a0053);
            } else
                ((this['scale']['x'] = this[_0xee179e(0x42b)]),
                    (this[_0xee179e(0x344)]['y'] = this[_0xee179e(0x42b)]));
        }
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x299)] = function (_0x45c5af) {
        const _0x4a6700 = _0x457895;
        if (this['scale']['x'] === _0x45c5af && this[_0x4a6700(0x344)]['y'] === _0x45c5af) return;
        ((this[_0x4a6700(0x42b)] = _0x45c5af),
            (this[_0x4a6700(0x239)] = Sprite_DiceCheck['SETTINGS']['scaleDuration'] || 0x1));
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x268)] = function () {
        const _0x52d6c6 = _0x457895;
        return this[_0x52d6c6(0x239)] > 0x0;
    }),
    (Sprite_DiceCheck[_0x457895(0x428)][_0x457895(0x263)] = function () {
        const _0x9735da = _0x457895;
        if (!this[_0x9735da(0x1e4)]) return;
        if (this['y'] === this['_baseY']) {
            if (_0x9735da(0x39e) !== 'VJNEv') {
                if (this[_0x9735da(0x1e4)][_0x9735da(0x2f7)] !== 0x0)
                    this[_0x9735da(0x1e4)][_0x9735da(0x2f7)] = 0x0;
                return;
            } else {
                const _0x42528f = _0x9735da(0x3f4);
                return (_0x1febac[_0x9735da(0x293)](_0x42528f), ![]);
            }
        }
        this[_0x9735da(0x1e4)][_0x9735da(0x2f7)] = Math[_0x9735da(0xf6)](
            this[_0x9735da(0x1e4)][_0x9735da(0x2f7)] + 0.2,
            0x2
        );
    }));
function Window_DiceText() {
    this['initialize'](...arguments);
}
((Window_DiceText[_0x457895(0x428)] = Object['create'](Window_Base[_0x457895(0x428)])),
    (Window_DiceText[_0x457895(0x428)][_0x457895(0x2ed)] = Window_DiceText),
    (Window_DiceText[_0x457895(0x428)]['initialize'] = function (_0x4e23b1) {
        const _0x2526e5 = _0x457895;
        (Window_Base[_0x2526e5(0x428)][_0x2526e5(0x31e)][_0x2526e5(0x2a3)](this, _0x4e23b1),
            (this['openness'] = 0x0),
            this['open']());
    }),
    (Window_DiceText['prototype'][_0x457895(0x391)] = function (_0x564761, _0x47b171, _0x5a230b) {
        const _0x436706 = _0x457895;
        ((_0x47b171 = _0x47b171 || 0x0),
            (_0x5a230b = _0x5a230b || 0x0),
            this['contents']['clear']());
        const _0x29cdf6 = this['textSizeEx'](_0x564761),
            _0x9efa85 =
                Math['round']((this[_0x436706(0x146)] - _0x29cdf6[_0x436706(0x155)]) / 0x2) +
                _0x47b171,
            _0x4ab505 =
                Math['round']((this[_0x436706(0x339)] - _0x29cdf6['height']) / 0x2) + _0x5a230b;
        this[_0x436706(0x3f2)](_0x564761, _0x9efa85, _0x4ab505);
    }));
function Window_DiceData() {
    const _0x2a1421 = _0x457895;
    this[_0x2a1421(0x31e)](...arguments);
}
((Window_DiceData['prototype'] = Object[_0x457895(0x3cd)](Window_Base[_0x457895(0x428)])),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x2ed)] = Window_DiceData),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x31e)] = function (_0xf698d5) {
        const _0x14d054 = _0x457895;
        (Window_Base[_0x14d054(0x428)]['initialize'][_0x14d054(0x2a3)](this, _0xf698d5),
            (this[_0x14d054(0x250)] = 0x0),
            this[_0x14d054(0x13d)]());
    }),
    (Window_DiceData[_0x457895(0x428)]['diceSettings'] = function () {
        const _0x3cc210 = _0x457895;
        return SceneManager[_0x3cc210(0x15c)] || {};
    }),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x389)] = function (_0x3b3eaf) {
        const _0x33564c = _0x457895;
        ((this['_dataType'] = _0x3b3eaf), this[_0x33564c(0x188)]());
    }),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x20d)] = function () {
        const _0x12b611 = _0x457895;
        (Window_Base[_0x12b611(0x428)]['update'][_0x12b611(0x2a3)](this),
            this['needsRefresh']() && (this['cacheData'](), this[_0x12b611(0x188)]()));
    }),
    (Window_DiceData['prototype'][_0x457895(0x14e)] = function () {
        const _0x35e33f = _0x457895,
            _0xb6d910 = this[_0x35e33f(0x2f8)]();
        switch (this['_dataType']) {
            case 'advantage':
                return this[_0x35e33f(0x116)] !== (_0xb6d910['advantage'] || 0x0);
            case _0x35e33f(0x1f6):
                return this[_0x35e33f(0x273)] !== (_0xb6d910['rankAdjust'] || 0x0);
            case 'modifier':
                return (
                    this[_0x35e33f(0x11b)] !== (_0xb6d910[_0x35e33f(0x3cb)] || 0x0) ||
                    this[_0x35e33f(0x2a9)] !== (_0xb6d910['modRand'] || 0x0)
                );
        }
        return ![];
    }),
    (Window_DiceData[_0x457895(0x428)]['cacheData'] = function () {
        const _0x427a4c = _0x457895,
            _0x201167 = this[_0x427a4c(0x2f8)]();
        switch (this[_0x427a4c(0x13c)]) {
            case 'advantage':
                this[_0x427a4c(0x116)] = _0x201167[_0x427a4c(0x373)] || 0x0;
                break;
            case _0x427a4c(0x1f6):
                this[_0x427a4c(0x273)] = _0x201167[_0x427a4c(0x16a)] || 0x0;
                break;
            case _0x427a4c(0x375):
                ((this[_0x427a4c(0x11b)] = _0x201167['modifiers'] || 0x0),
                    (this['_lastModRand'] = _0x201167['modRand'] || 0x0));
                break;
        }
    }),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x188)] = function () {
        const _0x112647 = _0x457895;
        (this['contents'][_0x112647(0x420)](),
            this[_0x112647(0x166)](),
            this[_0x112647(0x3e9)](),
            this[_0x112647(0x2cb)]());
    }),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x3e9)] = function () {
        const _0x34f5b5 = _0x457895,
            _0x10e9b7 = TextManager['DICE_ROLL']['data'];
        let _0x239486 = this['_dataType'];
        if (_0x239486 === _0x34f5b5(0x373)) {
            const _0x5049d0 = this[_0x34f5b5(0x2f8)]();
            if (_0x5049d0['type'] === _0x34f5b5(0x208) || _0x5049d0[_0x34f5b5(0x373)] === 0x0)
                _0x239486 = _0x34f5b5(0x1de);
            else {
                if (_0x5049d0['advantage'] > 0x0) _0x239486 = _0x34f5b5(0x373);
                else _0x5049d0[_0x34f5b5(0x373)] < 0x0 && (_0x239486 = _0x34f5b5(0x193));
            }
        }
        const _0x306196 = _0x10e9b7[_0x239486];
        this['drawTextEx'](_0x306196, this[_0x34f5b5(0x10e)](), 0x0);
    }),
    (Window_DiceData[_0x457895(0x428)][_0x457895(0x2cb)] = function () {
        const _0x50a7d4 = _0x457895,
            _0x52a92b = this[_0x50a7d4(0x2f8)](),
            _0x58b456 = TextManager[_0x50a7d4(0x3dc)][_0x50a7d4(0x13a)]['plusSign'];
        let _0x6a3d85 = '';
        switch (this['_dataType']) {
            case _0x50a7d4(0x373):
                if (_0x52a92b[_0x50a7d4(0x23b)] === _0x50a7d4(0x208))
                    _0x6a3d85 = VisuMZ[_0x50a7d4(0x201)]['TotalDiceToRoll']();
                else {
                    if ('MHNkj' !== _0x50a7d4(0x3f8)) {
                        _0x50430a = _0x1d0f3e(_0x563724);
                        var _0x2b7e6b = [];
                        const _0x41da8c = _0x2cdfa0[_0x50a7d4(0x1a1)];
                        for (var _0x372f97 = 0x0; _0x372f97 < _0x41da8c; _0x372f97++) {
                            _0x2b7e6b[_0x50a7d4(0x3af)](_0x5045bf[_0x50a7d4(0x11e)](_0x372f97));
                        }
                        let _0x27e0af = _0x2b7e6b['join']('') + 0x0;
                        while (_0x27e0af > _0x1ab132['DiceRollsRngSeeds']['m']) {
                            _0x27e0af = _0x43dbb3['floor'](
                                _0x27e0af / _0x11a08f[_0x50a7d4(0x201)]['m']
                            );
                        }
                        this[_0x50a7d4(0x35c)][_0x521d39] =
                            _0x27e0af % _0x5d80b8[_0x50a7d4(0x201)]['m'];
                    } else {
                        _0x6a3d85 = Math[_0x50a7d4(0x43f)](this[_0x50a7d4(0x116)])[
                            _0x50a7d4(0x2bd)
                        ](0x0, VisuMZ['DiceRollsRngSeeds'][_0x50a7d4(0x424)] - 0x1);
                        if (_0x58b456) _0x6a3d85 = '+' + _0x6a3d85;
                    }
                }
                break;
            case 'rank':
                const _0x36b99e = VisuMZ[_0x50a7d4(0x201)][_0x50a7d4(0x156)](),
                    _0x26624c = _0x50a7d4(0x1e0)['format'](_0x36b99e);
                _0x6a3d85 = TextManager[_0x50a7d4(0x3dc)]['data'][_0x26624c];
                break;
            case 'modifier':
                const _0x1f8660 = this[_0x50a7d4(0x11b)] || 0x0,
                    _0x2b7d13 = _0x1f8660 + (this['_lastModRand'] || 0x0),
                    _0x548e14 = Math[_0x50a7d4(0xf6)](_0x1f8660, _0x2b7d13),
                    _0x9d013 = Math[_0x50a7d4(0x137)](_0x1f8660, _0x2b7d13);
                _0x6a3d85 = String(_0x548e14 || 0x0);
                Math['abs'](_0x548e14) > Math['abs'](_0x9d013) &&
                    (_0x6a3d85 = String(_0x9d013 || 0x0));
                if (_0x58b456 && _0x548e14 >= 0x0) _0x6a3d85 = '+' + _0x6a3d85;
                _0x9d013 !== _0x548e14 &&
                    (Math['abs'](_0x548e14) > Math['abs'](_0x9d013)
                        ? (_0x6a3d85 = _0x50a7d4(0x20c)['format'](_0x6a3d85, _0x548e14))
                        : (_0x6a3d85 = '%1~%2'[_0x50a7d4(0x253)](_0x6a3d85, _0x9d013)));
                break;
        }
        _0x6a3d85 = String(_0x6a3d85);
        const _0x5c7948 = this['textSizeEx'](_0x6a3d85)[_0x50a7d4(0x155)],
            _0x43679b = this[_0x50a7d4(0x146)] - _0x5c7948 - this[_0x50a7d4(0x10e)]();
        this['drawTextEx'](_0x6a3d85, _0x43679b, 0x0);
    }));
function Window_DiceSprite() {
    const _0x39c237 = _0x457895;
    this[_0x39c237(0x31e)](...arguments);
}
((Window_DiceSprite[_0x457895(0x428)] = Object[_0x457895(0x3cd)](Window_Base[_0x457895(0x428)])),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x2ed)] = Window_DiceSprite),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x31e)] = function (_0x220087) {
        const _0x34554d = _0x457895;
        (Window_Base[_0x34554d(0x428)][_0x34554d(0x31e)][_0x34554d(0x2a3)](this, _0x220087),
            this[_0x34554d(0x38d)](),
            this[_0x34554d(0x159)]());
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x38d)] = function () {
        const _0xb89937 = _0x457895;
        ((this['_phase'] = 'prepare'), (this[_0xb89937(0x255)] = 0x0), this[_0xb89937(0x32c)]());
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x32c)] = function () {
        const _0xb3846d = _0x457895;
        this[_0xb3846d(0x340)] = VisuMZ[_0xb3846d(0x201)][_0xb3846d(0x302)]();
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x159)] = function () {
        const _0x50edd7 = _0x457895;
        ((this[_0x50edd7(0x410)] = new Sprite()),
            this[_0x50edd7(0x41a)](this['_mainContainer']),
            (this[_0x50edd7(0x205)] = new Sprite()),
            this['addChildToBack'](this[_0x50edd7(0x205)]),
            (this[_0x50edd7(0x410)]['x'] = this[_0x50edd7(0x205)]['x'] = 0x0),
            (this[_0x50edd7(0x410)]['y'] = this[_0x50edd7(0x205)]['y'] =
                this[_0x50edd7(0xf8)] - 0x48 - this[_0x50edd7(0x380)]()));
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x3ee)] = function () {
        const _0x145a01 = _0x457895;
        if (this['_mainContainer'] && this[_0x145a01(0x410)][_0x145a01(0x233)]['length'] > 0x0)
            return !![];
        if (
            this[_0x145a01(0x205)] &&
            this['_subContainer'][_0x145a01(0x233)][_0x145a01(0x1a1)] > 0x0
        )
            return !![];
        return ![];
    }),
    (Window_DiceSprite[_0x457895(0x428)]['update'] = function () {
        const _0x3a0cf6 = _0x457895;
        Window_Base['prototype']['update'][_0x3a0cf6(0x2a3)](this);
        if (!this[_0x3a0cf6(0x410)]) return;
        if (!this[_0x3a0cf6(0x205)]) return;
        this[_0x3a0cf6(0x3b6)]();
        if (this[_0x3a0cf6(0x2da)]()) {
            if (_0x3a0cf6(0x259) === _0x3a0cf6(0x259))
                (this[_0x3a0cf6(0x1f5)](), this[_0x3a0cf6(0x32c)](), this[_0x3a0cf6(0x214)]());
            else {
                const _0x24e9e9 = new _0x2b850e();
                this[_0x3a0cf6(0x410)][_0x3a0cf6(0x248)](_0x24e9e9);
                const _0x592fdc = (_0x3c46e9 + 0x1) / (_0x411258 + 0x1),
                    _0x18548b = _0x541303[_0x3a0cf6(0x23d)](_0x592fdc * this[_0x3a0cf6(0x155)]);
                (_0x24e9e9[_0x3a0cf6(0x29f)](_0x18548b, _0x34f32f),
                    _0x24e9e9[_0x3a0cf6(0x30c)](_0x185658));
            }
        }
        this[_0x3a0cf6(0x14f)]();
    }),
    (Window_DiceSprite[_0x457895(0x428)]['isAnyDiceAnimating'] = function () {
        const _0x1a9bf7 = _0x457895,
            _0x3c53f8 = this[_0x1a9bf7(0x410)][_0x1a9bf7(0x233)];
        return _0x3c53f8[_0x1a9bf7(0x27d)](_0x532470 => _0x532470[_0x1a9bf7(0x3c0)]());
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x3b6)] = function () {
        const _0x4dbb99 = _0x457895;
        if (
            ['prepare', _0x4dbb99(0x38a), _0x4dbb99(0x237)][_0x4dbb99(0x313)](
                this[_0x4dbb99(0x35f)]
            )
        )
            return;
        if (this[_0x4dbb99(0x3b0)]()) return;
        switch (this[_0x4dbb99(0x35f)]) {
            case _0x4dbb99(0x3f5):
                this[_0x4dbb99(0x322)]();
                break;
            case 'modifiers':
                this[_0x4dbb99(0x288)]();
                break;
            case _0x4dbb99(0x337):
                this[_0x4dbb99(0x305)]();
                break;
            case _0x4dbb99(0x20f):
                this[_0x4dbb99(0x2c8)]();
                break;
        }
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0xf3)] = function () {
        const _0x353e4d = _0x457895;
        this[_0x353e4d(0x35f)] = _0x353e4d(0x3f5);
        const _0x3c2ef9 = this[_0x353e4d(0x410)][_0x353e4d(0x233)][_0x353e4d(0x121)]();
        VisuMZ[_0x353e4d(0x201)][_0x353e4d(0x186)](_0x3c2ef9);
        let _0x447b50 = 0x0;
        for (const _0x338c60 of _0x3c2ef9) {
            if (_0x353e4d(0x1b0) !== 'RNhWq') (_0x338c60['startDiceRoll'](_0x447b50), _0x447b50++);
            else return _0x473311[_0x353e4d(0x37f)][_0x353e4d(0x415)];
        }
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x186)] = function (_0x244a95) {
        const _0x189767 = _0x457895;
        var _0x2982b5, _0x4708e7, _0x8fd987;
        for (_0x8fd987 = _0x244a95[_0x189767(0x1a1)] - 0x1; _0x8fd987 > 0x0; _0x8fd987--) {
            ((_0x2982b5 = Math['floor'](Math['random']() * (_0x8fd987 + 0x1))),
                (_0x4708e7 = _0x244a95[_0x8fd987]),
                (_0x244a95[_0x8fd987] = _0x244a95[_0x2982b5]),
                (_0x244a95[_0x2982b5] = _0x4708e7));
        }
        return _0x244a95;
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x322)] = function () {
        const _0x14e7af = _0x457895;
        (this[_0x14e7af(0x3d7)](), this[_0x14e7af(0x439)]());
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x3d7)] = function () {
        const _0x6279a = _0x457895,
            _0x3be68b = SceneManager[_0x6279a(0x15c)] || {},
            _0x37dba8 = _0x3be68b[_0x6279a(0xfa)] || '',
            _0x13698c = Math[_0x6279a(0x23d)](this[_0x6279a(0x155)] / 0x2),
            _0x3fcfc0 = this[_0x6279a(0x410)][_0x6279a(0x233)],
            _0x474b47 = _0x3fcfc0[_0x6279a(0x3fe)](_0x1e889e => _0x1e889e[_0x6279a(0x3ae)]());
        let _0x1164fd = null;
        if ([_0x6279a(0x359), _0x6279a(0x25a)][_0x6279a(0x313)](_0x37dba8)) {
            _0x1164fd = _0x3fcfc0[Math[_0x6279a(0x3dd)](_0x3fcfc0[_0x6279a(0x1a1)])];
            for (const _0x2e9305 of _0x3fcfc0) {
                _0x2e9305[_0x6279a(0x24d)](_0x13698c);
                if (_0x2e9305 === _0x1164fd) {
                    if (_0x6279a(0x223) !== _0x6279a(0x223))
                        return _0x4cc079[_0x6279a(0x201)][_0x6279a(0x3d0)]['call'](this);
                    else {
                        let _0x4b78f9 = _0x474b47[_0x6279a(0x133)](
                            (_0x819cf9, _0x440c27) => _0x819cf9 + _0x440c27,
                            0x0
                        );
                        (_0x37dba8 === _0x6279a(0x25a) &&
                            (_0x4b78f9 = Math['round'](_0x4b78f9 / _0x474b47[_0x6279a(0x1a1)])),
                            _0x2e9305['startNumberShift'](_0x4b78f9, !![]));
                    }
                } else {
                    if (_0x6279a(0x2ee) !== _0x6279a(0x434)) _0x2e9305[_0x6279a(0x37a)]();
                    else {
                        const _0x805226 = _0x3d5813[_0x6279a(0x37f)],
                            _0x53796b = this['diceSettings']();
                        ((_0x3cf8fb = _0x5295e4 || 0x0),
                            (_0x3ca60b += _0x805226[_0x6279a(0x152)] || 0x0),
                            (_0x33ceda += _0x53796b[_0x6279a(0x101)] || 0x0));
                        if (this['x'] === _0x560432) return;
                        ((this[_0x6279a(0x1ec)] = _0x27d6f4),
                            (this[_0x6279a(0x371)] = _0x805226['moveDuration'] || 0x1));
                    }
                }
            }
        } else {
            if ([_0x6279a(0x235), _0x6279a(0x1a8)]['includes'](_0x37dba8)) {
                const _0x58c888 =
                        _0x37dba8 === _0x6279a(0x235)
                            ? Math['max'](..._0x474b47)
                            : Math[_0x6279a(0xf6)](..._0x474b47),
                    _0x173fa4 = _0x474b47[_0x6279a(0x2be)](_0x58c888);
                _0x1164fd = _0x3fcfc0[_0x173fa4];
            } else {
                if (_0x3be68b['type'] === _0x6279a(0x1c7)) {
                    if ('dbMDY' !== _0x6279a(0x23a)) return this[_0x6279a(0x1ac)];
                    else {
                        const _0x482a45 = _0x3be68b[_0x6279a(0x373)] >= 0x0;
                        let _0x57ba07 = 0x0;
                        if (['aboveTarget', _0x6279a(0x17f)][_0x6279a(0x313)](_0x37dba8)) {
                            if ('BDULP' !== _0x6279a(0x3e1))
                                _0x57ba07 = _0x482a45
                                    ? Math[_0x6279a(0x137)](..._0x474b47)
                                    : Math['min'](..._0x474b47);
                            else {
                                const _0x4b5af5 = this[_0x6279a(0x40f)](_0x34be1a),
                                    _0x240f44 = _0x6279a(0x1c2),
                                    _0x201387 = this[_0x6279a(0x16b)](_0x576038),
                                    _0x483828 = _0x598d40;
                                this[_0x6279a(0x32e)](_0x4b5af5, _0x240f44, _0x201387, _0x483828);
                            }
                        } else
                            _0x57ba07 = _0x482a45
                                ? Math['min'](..._0x474b47)
                                : Math['max'](..._0x474b47);
                        if (this[_0x6279a(0x135)]()) {
                            if (_0x6279a(0x408) !== 'fVfdI')
                                _0x5b34b9 =
                                    _0x40d67b[
                                        _0xd1db45[_0x6279a(0x413)] || _0x38f7b2[_0x6279a(0x342)]
                                    ];
                            else {
                                const _0x3b8081 = VisuMZ['DiceRollsRngSeeds'][_0x6279a(0x302)]();
                                if (_0x57ba07 === 0x1) {
                                    if (_0x6279a(0x15f) === 'EdbQE') this['_naturalRoll'] = !![];
                                    else return _0x30a187(_0x4b350e, !![], ![]);
                                } else _0x57ba07 === _0x3b8081 && (this[_0x6279a(0x158)] = !![]);
                            }
                        }
                        const _0x41cdb7 = _0x474b47['indexOf'](_0x57ba07);
                        _0x1164fd = _0x3fcfc0[_0x41cdb7];
                    }
                }
            }
        }
        _0x1164fd &&
            (_0x6279a(0x178) === 'oUBKS'
                ? (this['processRollingPhase'](), this[_0x6279a(0x439)]())
                : (_0x1164fd[_0x6279a(0x24d)](_0x13698c),
                  _0x1164fd['changeScaleTo'](0x1),
                  this[_0x6279a(0x43e)](_0x1164fd)));
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x439)] = function () {
        const _0x561641 = _0x457895;
        ((this[_0x561641(0x35f)] = _0x561641(0x3cb)), (this[_0x561641(0x40c)] = 0x0));
        if (this[_0x561641(0x158)]) return;
        const _0x4c1471 = SceneManager['_diceRollSettings'] || {};
        if (_0x4c1471[_0x561641(0x3cb)] === 0x0 && _0x4c1471['modRand'] === 0x0) return;
        this[_0x561641(0x40c)] += Sprite_DiceCheck[_0x561641(0x37f)][_0x561641(0x3f9)] || 0x1;
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x288)] = function () {
        const _0x484063 = _0x457895;
        if (this[_0x484063(0x40c)] > 0x0) return this['_rollingDelay']--;
        (this['processModifiersPhase'](), this[_0x484063(0x2c1)]());
    }),
    (Window_DiceSprite[_0x457895(0x428)]['processModifiersPhase'] = function () {
        const _0x28c331 = _0x457895,
            _0x4834d1 = SceneManager[_0x28c331(0x15c)] || {},
            _0x6c5ab5 = _0x4834d1[_0x28c331(0xfa)] || '',
            _0x4f1ff8 = this[_0x28c331(0x410)][_0x28c331(0x233)][0x0],
            _0x337eb3 = TextManager[_0x28c331(0x3dc)]['colors'];
        if (this[_0x28c331(0x158)]) {
            if (_0x28c331(0x3f0) !== _0x28c331(0x327)) {
                const _0x31c056 = SceneManager[_0x28c331(0x353)]['_diceRoll_SubtitleWindow'];
                if (_0x4f1ff8[_0x28c331(0x3ae)]() === 0x1) {
                    if ('eBaNI' !== _0x28c331(0x1d7)) {
                        if ([_0x28c331(0x1bc), _0x28c331(0x17f)]['includes'](_0x6c5ab5)) {
                            if (_0x28c331(0x2f3) === _0x28c331(0x2f3)) {
                                _0x4f1ff8[_0x28c331(0x2f2)](_0x337eb3[_0x28c331(0x2c4)]);
                                if (_0x31c056) {
                                    if (_0x28c331(0x1b7) === _0x28c331(0x2c5)) {
                                        this[_0x28c331(0x35c)] === _0x1a672c &&
                                            this[_0x28c331(0x261)]();
                                        const _0x34be26 = this[_0x28c331(0x161)](
                                            _0x51d499,
                                            _0x5e5ec7,
                                            _0x31f57b
                                        );
                                        return (
                                            this['_randomNumberSeeds'][_0x34be26] === _0x55205e &&
                                                this[_0x28c331(0x423)](_0x34be26),
                                            this[_0x28c331(0x35c)][_0x34be26]
                                        );
                                    } else
                                        _0x31c056['drawCenteredText'](
                                            TextManager[_0x28c331(0x3dc)][_0x28c331(0x37b)][
                                                _0x28c331(0x361)
                                            ]
                                        );
                                }
                            } else
                                _0xb4001c['prototype'][_0x28c331(0x31e)][_0x28c331(0x2a3)](
                                    this,
                                    _0x859aa6
                                );
                        } else
                            (_0x4f1ff8[_0x28c331(0x2f2)](_0x337eb3[_0x28c331(0x138)]),
                                _0x31c056 &&
                                    _0x31c056[_0x28c331(0x391)](
                                        TextManager[_0x28c331(0x3dc)][_0x28c331(0x37b)][
                                            _0x28c331(0x28a)
                                        ]
                                    ));
                        return;
                    } else {
                        _0x37cf57['ConvertParams'](_0x540cbf, _0x4e604b);
                        const _0x1a4c4a = _0x17fd56(_0x24c8ff[_0x28c331(0x35a)]) || '-',
                            _0x5165fd = _0x40465f[_0x28c331(0x245)],
                            _0x2ee107 = _0x570448[_0x28c331(0x171)];
                        _0x149e47[_0x28c331(0x303)](_0x1a4c4a, _0x5165fd, _0x2ee107);
                    }
                } else {
                    if (
                        _0x4f1ff8[_0x28c331(0x3ae)]() ===
                        VisuMZ[_0x28c331(0x201)][_0x28c331(0x302)]()
                    ) {
                        if ([_0x28c331(0x1bc), 'aboveEqualTarget'][_0x28c331(0x313)](_0x6c5ab5)) {
                            if (_0x28c331(0x1d5) === 'vBhtV')
                                (_0x4f1ff8[_0x28c331(0x2f2)](_0x337eb3[_0x28c331(0x138)]),
                                    _0x31c056 &&
                                        (_0x28c331(0x1ba) !== _0x28c331(0x3c7)
                                            ? _0x31c056[_0x28c331(0x391)](
                                                  TextManager[_0x28c331(0x3dc)][_0x28c331(0x37b)][
                                                      _0x28c331(0x28a)
                                                  ]
                                              )
                                            : this['initRandomNumberSeeds']()));
                            else {
                                const _0x349baa = 'Cannot\x20roll\x20dice\x20during\x20a\x20QTE.';
                                _0x16cd6f[_0x28c331(0x293)](_0x349baa);
                            }
                        } else {
                            if (_0x28c331(0x3bd) !== _0x28c331(0x3bd)) {
                                const _0x7e9194 = this[_0x28c331(0x410)]['children'][0x0];
                                (this[_0x28c331(0x410)][_0x28c331(0x34a)](_0x7e9194),
                                    _0x7e9194[_0x28c331(0x3aa)]());
                            } else
                                (_0x4f1ff8[_0x28c331(0x2f2)](_0x337eb3[_0x28c331(0x2c4)]),
                                    _0x31c056 &&
                                        _0x31c056[_0x28c331(0x391)](
                                            TextManager[_0x28c331(0x3dc)][_0x28c331(0x37b)][
                                                _0x28c331(0x361)
                                            ]
                                        ));
                        }
                        return;
                    }
                }
            } else {
                if (_0x30d287['DiceRollsRngSeeds']['Settings'][_0x28c331(0x3e5)][_0x28c331(0x1bb)])
                    return _0x27291f[_0x28c331(0x201)][_0x28c331(0x42e)]['Window'][
                        _0x28c331(0x1bb)
                    ][_0x28c331(0x2a3)](this);
                const _0x598547 = _0x1532dd[_0x28c331(0xf6)](
                        _0x884a51[_0x28c331(0x23d)](_0x9b3d01[_0x28c331(0x155)] * 0.8),
                        0x2cc
                    ),
                    _0x5ec27c = this[_0x28c331(0x21a)](0x1, ![]),
                    _0x4d4841 = _0x4e5f12[_0x28c331(0x23d)](
                        (_0x7f71ed[_0x28c331(0x155)] - _0x598547) / 0x2
                    ),
                    _0x56cfb7 =
                        _0x464256[_0x28c331(0xf8)] -
                        this['calcWindowHeight'](0x4, !![]) -
                        _0x5ec27c;
                return new _0x4e369c(_0x4d4841, _0x56cfb7, _0x598547, _0x5ec27c);
            }
        }
        const _0x1397f8 = _0x4834d1[_0x28c331(0x3cb)] || 0x0,
            _0x34a506 = _0x4834d1[_0x28c331(0x173)] || 0x0,
            _0x2a3907 = _0x4f1ff8['number']() + _0x1397f8,
            _0x18480e = _0x2a3907 + _0x34a506;
        let _0x4efda3 = _0x2a3907;
        if (_0x2a3907 !== _0x18480e) {
            if (_0x28c331(0x3e7) === _0x28c331(0x3cc)) this['x'] = this[_0x28c331(0x1ec)];
            else {
                const _0x1b9fc7 = _0x4834d1[_0x28c331(0x2ea)] || {};
                if (_0x1b9fc7['Seed'] && _0x1b9fc7[_0x28c331(0x35a)] !== 'none') {
                    if (_0x28c331(0x2b0) === _0x28c331(0x2b0)) {
                        const _0x52b485 = _0x1b9fc7[_0x28c331(0x35a)],
                            _0x3abadd = _0x1b9fc7[_0x28c331(0x245)],
                            _0x4b8d76 = _0x1b9fc7['ApplyUnique'];
                        _0x4efda3 = $gameSystem[_0x28c331(0x282)](
                            _0x2a3907,
                            _0x18480e,
                            _0x52b485,
                            _0x3abadd,
                            _0x4b8d76
                        );
                    } else {
                        const _0x225b98 = _0x1c7c94['DiceRollsRngSeeds'];
                        let _0xd5d3a2 = this[_0x28c331(0x169)](_0x773852, _0x132631, _0x2964c6);
                        const _0x3eaf28 = _0x225b98['m'];
                        _0xd5d3a2 = (_0x225b98['a'] * _0xd5d3a2 + _0x225b98['c']) % _0x3eaf28;
                        const _0x5ab26d = this['getRandomNumberSeedKey'](
                            _0x44f780,
                            _0x1d58fe,
                            _0x285de9
                        );
                        return (
                            (this['_randomNumberSeeds'][_0x5ab26d] = _0xd5d3a2),
                            this['_randomNumberSeeds'][_0x5ab26d]
                        );
                    }
                } else _0x4efda3 = _0x2a3907 + Math[_0x28c331(0x3dd)](_0x34a506 + 0x1);
            }
        }
        if (_0x4efda3 === _0x4f1ff8['number']()) return;
        (_0x4f1ff8[_0x28c331(0x2d0)](_0x4efda3),
            _0x4efda3 > _0x4f1ff8[_0x28c331(0x3ae)]()
                ? _0x28c331(0x271) === 'nQwej'
                    ? (_0x22f0b8[_0x28c331(0x105)](),
                      this[_0x28c331(0x34e)](_0x3e2c65),
                      _0x139cce[_0x28c331(0x201)]['Game_Action_apply']['call'](this, _0xecf0dd),
                      _0x150767['clearRngAspects']())
                    : _0x4f1ff8['setNumberColor'](_0x337eb3[_0x28c331(0x138)])
                : _0x28c331(0x33e) !== 'segJO'
                  ? (_0x42d176 = _0x28c331(0x20c)[_0x28c331(0x253)](_0x173ce3, _0x34a876))
                  : _0x4f1ff8[_0x28c331(0x2f2)](_0x337eb3['modDownColor']));
    }),
    (Window_DiceSprite[_0x457895(0x428)]['allowNaturalRolls'] = function () {
        const _0x228596 = _0x457895,
            _0x697518 = SceneManager[_0x228596(0x15c)] || {};
        return _0x697518[_0x228596(0x22a)];
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x2c1)] = function () {
        const _0x16ecd5 = _0x457895;
        this[_0x16ecd5(0x35f)] = _0x16ecd5(0x337);
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x305)] = function () {
        const _0x579d6e = _0x457895;
        (this['processRecordingPhase'](), this[_0x579d6e(0x185)]());
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x272)] = function () {
        const _0x55e2f5 = _0x457895,
            _0x543826 = SceneManager[_0x55e2f5(0x15c)] || {},
            _0x42a0de = _0x543826['ResultType'] || '',
            _0x53abd8 = this['_mainContainer'][_0x55e2f5(0x233)][0x0],
            _0x12b468 = _0x53abd8[_0x55e2f5(0x3ae)](),
            _0x1d5271 = _0x543826[_0x55e2f5(0x35b)] || 0x0;
        let _0x226238 = ![];
        if (_0x543826[_0x55e2f5(0x22a)] && this[_0x55e2f5(0x158)]) {
            if (_0x55e2f5(0x104) !== _0x55e2f5(0x104)) return [];
            else {
                if ([_0x55e2f5(0x1bc), _0x55e2f5(0x17f)][_0x55e2f5(0x313)](_0x42a0de)) {
                    if (_0x55e2f5(0x25e) === _0x55e2f5(0x2fe)) {
                        if (!_0x41471a['value'](_0x1b7658[_0x55e2f5(0x12b)])) return ![];
                    } else _0x226238 = _0x12b468 !== 0x1;
                } else _0x226238 = _0x12b468 === 0x1;
            }
        } else {
            if (_0x42a0de === 'aboveTarget') _0x226238 = _0x12b468 > _0x1d5271;
            else {
                if (_0x42a0de === _0x55e2f5(0x17f)) {
                    if (_0x55e2f5(0x304) !== _0x55e2f5(0x304)) {
                        const _0x2a4455 = _0x237058[_0x55e2f5(0x3dc)][_0x55e2f5(0x284)];
                        let _0x3c6696 = _0x305fa5[_0x55e2f5(0x428)][_0x55e2f5(0x1d2)][
                            _0x55e2f5(0x2a3)
                        ](this, _0x307b3b);
                        if (_0x2a4455[_0x55e2f5(0x170)]) {
                            const _0x296a27 = _0x348022[_0x55e2f5(0x295)] || 0x0,
                                _0xe17651 = _0x3a5247['MaxUses'] || 0x1;
                            if (_0x296a27 >= _0xe17651)
                                return _0x2a4455['usedUp'][_0x55e2f5(0x1ea)]();
                        }
                        if (_0x35ffbd['PreCostText']) {
                            const _0x3163c9 = _0x2a4455['costTextFmt'];
                            ((_0x3c6696 = _0x3163c9['format'](_0x1434ed['PreCostText'], _0x3c6696)),
                                (_0x3c6696 = _0x3c6696[_0x55e2f5(0x1ea)]()));
                        }
                        if (_0x24c56a['PostCostText']) {
                            const _0x59334d = _0x2a4455['costTextFmt'];
                            ((_0x3c6696 = _0x59334d[_0x55e2f5(0x253)](
                                _0x3c6696,
                                _0x2ee81c[_0x55e2f5(0x1c3)]
                            )),
                                (_0x3c6696 = _0x3c6696[_0x55e2f5(0x1ea)]()));
                        }
                        return _0x3c6696;
                    } else _0x226238 = _0x12b468 >= _0x1d5271;
                } else {
                    if (_0x42a0de === _0x55e2f5(0x348)) _0x226238 = _0x12b468 <= _0x1d5271;
                    else _0x42a0de === 'belowTarget' && (_0x226238 = _0x12b468 < _0x1d5271);
                }
            }
        }
        _0x543826[_0x55e2f5(0x3ed)] &&
            $gameSwitches[_0x55e2f5(0x176)](_0x543826[_0x55e2f5(0x3ed)], _0x226238);
        _0x543826['VariableID'] &&
            $gameVariables[_0x55e2f5(0x176)](_0x543826[_0x55e2f5(0x154)], _0x12b468);
        if (_0x543826['type'] === _0x55e2f5(0x1c7)) {
            if (_0x55e2f5(0x36a) === _0x55e2f5(0x431)) {
                const _0x45bb7a = _0x594f35[_0x55e2f5(0x2df)] || [];
                _0x30df52 = _0x45bb7a['map'](_0x422305 => _0xa62280[_0x55e2f5(0x1b6)](_0x422305))[
                    _0x55e2f5(0x398)
                ](_0x4ab3db => _0x2eaff5[_0x55e2f5(0x210)]()[_0x55e2f5(0x313)](_0x4ab3db));
            } else {
                if (_0x226238) SoundManager[_0x55e2f5(0x1dd)](this[_0x55e2f5(0x158)]);
                else {
                    if (_0x55e2f5(0x28f) !== 'GRskV') {
                        if (
                            _0x4779aa['DiceRollsRngSeeds'][_0x55e2f5(0x42e)]['Window'][
                                _0x55e2f5(0x2fb)
                            ]
                        )
                            return _0x3d292f[_0x55e2f5(0x201)][_0x55e2f5(0x42e)][_0x55e2f5(0x3e5)][
                                _0x55e2f5(0x2fb)
                            ][_0x55e2f5(0x2a3)](this);
                        const _0x370ade = _0x1015c5['min'](_0x30db83[_0x55e2f5(0x155)], 0x3f8),
                            _0x234034 = this[_0x55e2f5(0x21a)](0x4, !![]),
                            _0x45351 = _0x158790[_0x55e2f5(0x23d)](
                                (_0x559b6a[_0x55e2f5(0x155)] - _0x370ade) / 0x2
                            ),
                            _0x15e5b1 = _0x1af19a[_0x55e2f5(0xf8)] - _0x234034;
                        return new _0x1fa157(_0x45351, _0x15e5b1, _0x370ade, _0x234034);
                    } else SoundManager['playDiceFailure'](this[_0x55e2f5(0x158)]);
                }
            }
        }
        (_0x53abd8[_0x55e2f5(0x37a)](!![]),
            (this[_0x55e2f5(0x255)] =
                _0x53abd8['_finalizeDuration'] + _0x53abd8['_fadeDuration'] || 0x1));
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x185)] = function () {
        const _0x1baf66 = _0x457895;
        this[_0x1baf66(0x35f)] = _0x1baf66(0x20f);
    }),
    (Window_DiceSprite['prototype']['startEndingPhase'] = function () {
        const _0x5cbcc7 = _0x457895;
        if (this[_0x5cbcc7(0x255)] > 0x0) return this['_finalizeDuration']--;
        (this[_0x5cbcc7(0x153)](), this[_0x5cbcc7(0x3b1)]());
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x153)] = function () {
        const _0x54568e = _0x457895,
            _0x10c53c = this['_mainContainer'][_0x54568e(0x233)][0x0];
        (this[_0x54568e(0x410)][_0x54568e(0x34a)](_0x10c53c), _0x10c53c[_0x54568e(0x3aa)]());
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x3b1)] = function () {
        const _0x220e99 = _0x457895;
        ((this['_phase'] = _0x220e99(0x237)), SceneManager[_0x220e99(0x353)][_0x220e99(0x13e)]());
        const _0x5d2d2a = Math[_0x220e99(0x215)]((0xc / 0x3c) * 0x3e8);
        setTimeout(SceneManager[_0x220e99(0x307)][_0x220e99(0x323)](SceneManager), _0x5d2d2a);
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x2da)] = function () {
        const _0x1147e5 = _0x457895;
        if (this['_phase'] !== _0x1147e5(0x3ff)) return ![];
        const _0x1f0d87 = VisuMZ[_0x1147e5(0x201)][_0x1147e5(0x363)]();
        if (
            this[_0x1147e5(0x410)] &&
            this[_0x1147e5(0x410)][_0x1147e5(0x233)][_0x1147e5(0x1a1)] !== _0x1f0d87
        )
            return !![];
        if (this['_diceSides'] !== VisuMZ[_0x1147e5(0x201)][_0x1147e5(0x302)]()) return !![];
        return ![];
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x1f5)] = function () {
        const _0x20d26b = _0x457895;
        while (this['_mainContainer']['children'][_0x20d26b(0x1a1)] > 0x0) {
            const _0x51e6ee = this[_0x20d26b(0x410)][_0x20d26b(0x233)][0x0];
            this[_0x20d26b(0x3ce)](_0x51e6ee);
        }
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x43e)] = function (_0xceaa98) {
        const _0x156d0c = _0x457895,
            _0x214287 = this[_0x156d0c(0x410)]['children'],
            _0x5a328b = _0x214287[_0x156d0c(0x121)]();
        _0x5a328b[_0x156d0c(0x350)](_0xceaa98);
        while (_0x5a328b[_0x156d0c(0x1a1)] > 0x0) {
            const _0x27262d = _0x5a328b[_0x156d0c(0x1bd)]();
            this[_0x156d0c(0x3ce)](_0x27262d);
        }
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x3ce)] = function (_0x3bf069) {
        const _0x5d7630 = _0x457895;
        (_0x3bf069['startFadeOut'](),
            this[_0x5d7630(0x410)]['removeChild'](_0x3bf069),
            this['_subContainer'][_0x5d7630(0x248)](_0x3bf069));
    }),
    (Window_DiceSprite[_0x457895(0x428)][_0x457895(0x214)] = function () {
        const _0x51426a = _0x457895,
            _0x24f45c = VisuMZ[_0x51426a(0x201)]['TotalDiceToRoll'](),
            _0x29e0df = 0x0,
            _0x53e2f9 = 0xa0,
            _0x5e4164 = Math['floor'](this[_0x51426a(0x155)] / _0x24f45c),
            _0x2d4b09 = (_0x5e4164 / _0x53e2f9)[_0x51426a(0x2bd)](0.2, 0x1);
        for (let _0x23a903 = 0x0; _0x23a903 < _0x24f45c; _0x23a903++) {
            const _0x595b56 = new Sprite_DiceCheck();
            this[_0x51426a(0x410)]['addChild'](_0x595b56);
            const _0x2696c5 = (_0x23a903 + 0x1) / (_0x24f45c + 0x1),
                _0x119d71 = Math[_0x51426a(0x23d)](_0x2696c5 * this[_0x51426a(0x155)]);
            (_0x595b56[_0x51426a(0x29f)](_0x119d71, _0x29e0df),
                _0x595b56[_0x51426a(0x30c)](_0x2d4b09));
        }
    }),
    (Window_DiceSprite['prototype'][_0x457895(0x14f)] = function () {
        const _0x44850c = _0x457895,
            _0x29a0f1 = [];
        for (const _0x5d72ee of this[_0x44850c(0x205)]['children']) {
            if (_0x5d72ee[_0x44850c(0x29d)] <= 0x0) _0x29a0f1[_0x44850c(0x3af)](_0x5d72ee);
        }
        for (const _0x1d45d1 of _0x29a0f1) {
            (this[_0x44850c(0x205)]['removeChild'](_0x1d45d1), _0x1d45d1[_0x44850c(0x3aa)]());
        }
    }));
function Window_DiceChoice() {
    const _0x56eaec = _0x457895;
    this[_0x56eaec(0x31e)](...arguments);
}
((Window_DiceChoice[_0x457895(0x428)] = Object[_0x457895(0x3cd)](Window_Command['prototype'])),
    (Window_DiceChoice[_0x457895(0x428)][_0x457895(0x2ed)] = Window_DiceChoice),
    (Window_DiceChoice[_0x457895(0x37f)] = {
        textAlign:
            VisuMZ['DiceRollsRngSeeds']['Settings'][_0x457895(0x3e5)][_0x457895(0x289)] ??
            _0x457895(0x12a),
        showEffects:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)]['CommandShowEffects'] ??
            !![],
        showBonuses:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x160)] ?? !![],
    }),
    (Window_DiceChoice[_0x457895(0x428)]['initialize'] = function (_0x4bb59e) {
        const _0x375a31 = _0x457895;
        (Window_Command[_0x375a31(0x428)][_0x375a31(0x31e)][_0x375a31(0x2a3)](this, _0x4bb59e),
            (this[_0x375a31(0x250)] = 0x0),
            this[_0x375a31(0x13d)]());
    }),
    (Window_DiceChoice[_0x457895(0x428)][_0x457895(0x12e)] = function () {
        const _0x534840 = _0x457895;
        (this[_0x534840(0x219)](), this[_0x534840(0x2b2)](), this[_0x534840(0x34c)]());
    }),
    (Window_DiceChoice['prototype'][_0x457895(0x219)] = function () {
        const _0x1a600c = _0x457895,
            _0x544771 = TextManager[_0x1a600c(0x3dc)][_0x1a600c(0x244)][_0x1a600c(0x26d)];
        this[_0x1a600c(0x32e)](_0x544771, _0x1a600c(0x26d), !![]);
    }),
    (Window_DiceChoice[_0x457895(0x428)][_0x457895(0x2b2)] = function () {
        const _0x2e5078 = _0x457895;
        if (!VisuMZ[_0x2e5078(0x201)][_0x2e5078(0x324)]()) return;
        const _0x583ef2 = TextManager['DICE_ROLL'][_0x2e5078(0x244)][_0x2e5078(0x2bb)],
            _0x410b5b = SceneManager[_0x2e5078(0x15c)] || {};
        ((_0x410b5b['effects'] = _0x410b5b[_0x2e5078(0x2bb)] || []),
            this[_0x2e5078(0x32e)](
                _0x583ef2,
                _0x2e5078(0x2bb),
                _0x410b5b[_0x2e5078(0x2bb)][_0x2e5078(0x1a1)] > 0x0
            ));
    }),
    (Window_DiceChoice[_0x457895(0x428)][_0x457895(0x34c)] = function () {
        const _0x2b0064 = _0x457895;
        if (!VisuMZ['DiceRollsRngSeeds'][_0x2b0064(0x151)]()) return;
        const _0x53b8ab = TextManager[_0x2b0064(0x3dc)][_0x2b0064(0x244)][_0x2b0064(0x284)];
        this['addCommand'](_0x53b8ab, 'bonus', !![]);
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x25d)] = function () {
        const _0x39e6b1 = _0x457895;
        let _0x3698e6 = 0x1;
        VisuMZ[_0x39e6b1(0x201)][_0x39e6b1(0x324)]() &&
            ('DgPQv' !== _0x39e6b1(0x1b2)
                ? (_0x3698e6 += 0x1)
                : this[_0x39e6b1(0x32b)](_0x39113b ? _0x39e6b1(0x2e8) : _0x39e6b1(0x191)));
        if (VisuMZ[_0x39e6b1(0x201)][_0x39e6b1(0x151)]()) {
            if (_0x39e6b1(0x14b) === _0x39e6b1(0x14b)) _0x3698e6 += 0x1;
            else {
                const _0x297950 = _0x5e8be0[_0x39e6b1(0x295)] || 0x0,
                    _0x48f0fc = _0x134a01[_0x39e6b1(0x3d4)] || 0x1;
                if (_0x48f0fc < 0xf4240 && _0x297950 >= _0x48f0fc) return ![];
            }
        }
        return _0x3698e6;
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x324)] = function () {
        const _0x45fb32 = _0x457895;
        if (!Window_DiceChoice['SETTINGS'][_0x45fb32(0x1cf)]) return ![];
        if (this[_0x45fb32(0x151)]()) return !![];
        const _0x52870b = SceneManager[_0x45fb32(0x15c)] || {};
        return (
            (_0x52870b[_0x45fb32(0x2bb)] = _0x52870b['effects'] || []),
            _0x52870b[_0x45fb32(0x2bb)]['length'] > 0x0
        );
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x151)] = function () {
        const _0x531009 = _0x457895;
        if (!Window_DiceChoice[_0x531009(0x37f)][_0x531009(0x194)]) return ![];
        const _0x3c9fa = SceneManager[_0x531009(0x15c)] || {};
        return (
            (_0x3c9fa[_0x531009(0x346)] = _0x3c9fa[_0x531009(0x346)] || []),
            _0x3c9fa['bonusEffects'][_0x531009(0x1a1)] > 0x0
        );
    }),
    (Window_DiceChoice[_0x457895(0x428)]['itemTextAlign'] = function () {
        const _0x21e6fd = _0x457895;
        return Window_DiceChoice[_0x21e6fd(0x37f)][_0x21e6fd(0x415)];
    }),
    (Window_DiceChoice[_0x457895(0x428)][_0x457895(0x3a5)] = function (_0x25b862) {
        const _0x52aa61 = _0x457895,
            _0x18db1d = this[_0x52aa61(0x3ec)](_0x25b862),
            _0x30b302 = this['commandName'](_0x25b862),
            _0x43bf08 = this['textSizeEx'](_0x30b302)['width'];
        this[_0x52aa61(0x1e9)](this[_0x52aa61(0x1b8)](_0x25b862));
        const _0x61b333 = this['itemTextAlign']();
        if (_0x61b333 === _0x52aa61(0x195))
            this[_0x52aa61(0x3f2)](
                _0x30b302,
                _0x18db1d['x'] + _0x18db1d[_0x52aa61(0x155)] - _0x43bf08,
                _0x18db1d['y'],
                _0x43bf08
            );
        else {
            if (_0x61b333 === _0x52aa61(0x12a)) {
                const _0x59c476 =
                    _0x18db1d['x'] + Math['floor']((_0x18db1d['width'] - _0x43bf08) / 0x2);
                this[_0x52aa61(0x3f2)](_0x30b302, _0x59c476, _0x18db1d['y'], _0x43bf08);
            } else {
                if (_0x52aa61(0x3e2) !== _0x52aa61(0x3e2)) {
                    const _0x162378 = _0x4d35a1[_0x52aa61(0x33f)];
                    ((_0x49c396 = _0x162378[_0x52aa61(0x253)](
                        _0x2562bc,
                        _0x206084[_0x52aa61(0x1c3)]
                    )),
                        (_0x4720e5 = _0x498d24['trim']()));
                } else this[_0x52aa61(0x3f2)](_0x30b302, _0x18db1d['x'], _0x18db1d['y'], _0x43bf08);
            }
        }
    }));
function Window_DiceListBase() {
    const _0x56ca67 = _0x457895;
    this[_0x56ca67(0x31e)](...arguments);
}
((Window_DiceListBase['prototype'] = Object[_0x457895(0x3cd)](Window_Command[_0x457895(0x428)])),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x2ed)] = Window_DiceListBase),
    (Window_DiceListBase[_0x457895(0x37f)] = {
        columns:
            VisuMZ['DiceRollsRngSeeds'][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x39c)] ??
            0x2,
        colSpacing:
            VisuMZ[_0x457895(0x201)][_0x457895(0x42e)][_0x457895(0x3e5)][_0x457895(0x147)] ?? 0x10,
    }),
    (Window_DiceListBase[_0x457895(0x428)]['initialize'] = function (_0x1fea6e) {
        const _0x3a3ecc = _0x457895;
        (Window_Command[_0x3a3ecc(0x428)]['initialize'][_0x3a3ecc(0x2a3)](this, _0x1fea6e),
            (this[_0x3a3ecc(0x250)] = 0x0),
            this[_0x3a3ecc(0x2b7)]());
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x283)] = function () {
        const _0x1ba03e = _0x457895;
        return Window_DiceListBase[_0x1ba03e(0x37f)][_0x1ba03e(0x30e)] || 0x1;
    }),
    (Window_DiceListBase[_0x457895(0x428)]['colSpacing'] = function () {
        const _0x3769f6 = _0x457895;
        return Window_DiceListBase[_0x3769f6(0x37f)]['colSpacing'] || 0x0;
    }),
    (Window_DiceListBase['prototype'][_0x457895(0x12e)] = function () {
        const _0x28a9cf = _0x457895,
            _0x45bd73 = this['effectsList']();
        for (const _0x461fd8 of _0x45bd73) {
            if ('oBVOV' !== 'krDaj') {
                if (!this[_0x28a9cf(0x1b1)](_0x461fd8)) return;
                this[_0x28a9cf(0x341)](_0x461fd8);
            } else _0x48ad5b = _0x28a9cf(0x1de);
        }
    }),
    (Window_DiceListBase['prototype']['effectsList'] = function () {
        return [];
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x1b1)] = function (_0x59d262) {
        if (!_0x59d262) return ![];
        return !![];
    }),
    (Window_DiceListBase['prototype'][_0x457895(0x341)] = function (_0x328dd8) {
        const _0x456619 = _0x457895,
            _0x3563f4 = this[_0x456619(0x40f)](_0x328dd8),
            _0x212284 = _0x456619(0x1c2),
            _0xf99bdb = this['isEnabled'](_0x328dd8),
            _0x454de2 = _0x328dd8;
        this[_0x456619(0x32e)](_0x3563f4, _0x212284, _0xf99bdb, _0x454de2);
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x40f)] = function (_0x527bfb) {
        const _0x42e6ae = _0x457895;
        if (_0x527bfb[_0x42e6ae(0x20e)] || _0x527bfb[_0x42e6ae(0x124)]) {
            let _0x1004f8 = _0x527bfb[_0x42e6ae(0x20e)] || '',
                _0x28217d = _0x527bfb['Icon'] || 0x0,
                _0xd48b10 = null;
            (_0x527bfb[_0x42e6ae(0x413)] || _0x527bfb[_0x42e6ae(0x342)]) &&
                (_0xd48b10 = $dataItems[_0x527bfb['ItemReqID'] || _0x527bfb[_0x42e6ae(0x342)]]);
            if (_0x527bfb[_0x42e6ae(0x3ac)] || _0x527bfb[_0x42e6ae(0x343)]) {
                if (_0x42e6ae(0x402) === _0x42e6ae(0x10a)) {
                    const _0x151af1 = _0x3f25b0[_0x42e6ae(0x2e3)];
                    _0x13cee8 = _0x151af1[_0x42e6ae(0x253)](_0x5ed401, _0x1e773f)[
                        _0x42e6ae(0x1ea)
                    ]();
                } else
                    _0xd48b10 =
                        $dataWeapons[_0x527bfb[_0x42e6ae(0x3ac)] || _0x527bfb['WeaponCostID']];
            }
            (_0x527bfb['ArmorReqID'] || _0x527bfb[_0x42e6ae(0x11c)]) &&
                (_0xd48b10 = $dataArmors[_0x527bfb['ArmorReqID'] || _0x527bfb['ArmorCostID']]);
            (_0x527bfb['SkillReqID'] || _0x527bfb['SkillCostID']) &&
                (_0xd48b10 =
                    $dataSkills[_0x527bfb[_0x42e6ae(0x1e7)] || _0x527bfb[_0x42e6ae(0x369)]]);
            if (_0x1004f8[_0x42e6ae(0x1d3)]()['trim']() === 'AUTONAME') {
                _0x1004f8 = _0xd48b10 ? _0xd48b10['name'] : '';
                if (Imported[_0x42e6ae(0x36b)] && _0xd48b10) {
                    if ('XUqhH' === _0x42e6ae(0x336)) {
                        if (_0xd48b10['note'][_0x42e6ae(0x20b)](/<COLOR:[ ](\d+)>/i)) {
                            const _0x10a35b = Number(RegExp['$1']);
                            _0x1004f8 = _0x42e6ae(0x2e5)[_0x42e6ae(0x253)](
                                _0x10a35b,
                                _0x1004f8[_0x42e6ae(0x1ea)]()
                            );
                        } else {
                            if (
                                Imported[_0x42e6ae(0x177)] &&
                                _0xd48b10['note'][_0x42e6ae(0x20b)](/<COLOR:[ ]#(.*)>/i)
                            ) {
                                if (_0x42e6ae(0x162) === _0x42e6ae(0x162)) {
                                    const _0x2cec11 = '#' + String(RegExp['$1']);
                                    _0x1004f8 = _0x42e6ae(0x192)['format'](
                                        _0x2cec11,
                                        _0x1004f8[_0x42e6ae(0x1ea)]()
                                    );
                                } else {
                                    if (
                                        _0x318a87[_0x42e6ae(0x201)][_0x42e6ae(0x42e)][
                                            _0x42e6ae(0x3e5)
                                        ][_0x42e6ae(0x229)]
                                    )
                                        return _0x36e8f3[_0x42e6ae(0x201)]['Settings'][
                                            _0x42e6ae(0x3e5)
                                        ][_0x42e6ae(0x229)][_0x42e6ae(0x2a3)](this);
                                    const _0x566474 = _0x332184[_0x42e6ae(0xf6)](
                                            _0x5da2fa['round'](_0x1e8a32[_0x42e6ae(0x155)] * 0.9),
                                            0x330
                                        ),
                                        _0x4b212d = _0x336d05[_0x42e6ae(0x23d)](
                                            (_0x361799[_0x42e6ae(0x155)] - _0x566474) / 0x2
                                        ),
                                        _0x537347 =
                                            this[_0x42e6ae(0x21a)](0x2, ![]) +
                                            this[_0x42e6ae(0x21a)](0x1, ![]),
                                        _0x46ebaa =
                                            _0x49c3e0[_0x42e6ae(0xf8)] -
                                            _0x537347 -
                                            this[_0x42e6ae(0x21a)](0x4, !![]) -
                                            this[_0x42e6ae(0x21a)](0x1, ![]);
                                    return new _0x21c11e(
                                        _0x4b212d,
                                        _0x537347,
                                        _0x566474,
                                        _0x46ebaa
                                    );
                                }
                            }
                        }
                    } else {
                        const _0x3c2f89 = this[_0x42e6ae(0x410)][_0x42e6ae(0x233)],
                            _0x28639c = _0x3c2f89[_0x42e6ae(0x121)]();
                        _0x28639c[_0x42e6ae(0x350)](_0x3c390c);
                        while (_0x28639c[_0x42e6ae(0x1a1)] > 0x0) {
                            const _0x440e87 = _0x28639c[_0x42e6ae(0x1bd)]();
                            this[_0x42e6ae(0x3ce)](_0x440e87);
                        }
                    }
                }
                _0x1004f8 = _0x1004f8[_0x42e6ae(0x1ea)]();
            }
            if (_0x28217d >= 0xf4240) {
                if (_0x42e6ae(0x3ad) === _0x42e6ae(0x3ad))
                    _0x28217d = _0xd48b10 ? _0xd48b10[_0x42e6ae(0x298)] : 0x0;
                else {
                    const _0x5a2ca3 = _0x497fe1[_0x42e6ae(0x278)] || '',
                        _0x2086c6 = _0x4d06a5[_0x42e6ae(0x2bc)] || ![],
                        _0x54710b = _0xf62eb6[_0x42e6ae(0x421)] || ![];
                    var _0x3dbf22, _0x1a7231, _0x298623;
                    for (
                        _0x298623 = _0x578f6c[_0x42e6ae(0x1a1)] - 0x1;
                        _0x298623 > 0x0;
                        _0x298623--
                    ) {
                        const _0x12fe13 = _0x286373(_0x5a2ca3, _0x2086c6, _0x54710b);
                        ((_0x3dbf22 = _0x44b2e0[_0x42e6ae(0x3f3)](_0x12fe13 * (_0x298623 + 0x1))),
                            (_0x1a7231 = _0x129308[_0x298623]),
                            (_0x40b4d7[_0x298623] = _0x2db372[_0x3dbf22]),
                            (_0x281bd6[_0x3dbf22] = _0x1a7231));
                    }
                    return _0x30e1fa;
                }
            }
            return _0x42e6ae(0x2fa)[_0x42e6ae(0x253)](
                _0x1004f8 || _0x42e6ae(0x1c6),
                _0x28217d || 0x0
            );
        }
        return '\x5cI[16]Undefined';
    }),
    (Window_DiceListBase['prototype'][_0x457895(0x16b)] = function (_0x5adaf5) {
        if (!_0x5adaf5) return ![];
        return !![];
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x3a5)] = function (_0x17837d) {
        const _0x4de649 = _0x457895,
            _0x104be4 = this['itemLineRect'](_0x17837d);
        (this['resetFontSettings'](), this[_0x4de649(0x1e9)](this[_0x4de649(0x1b8)](_0x17837d)));
        const _0x605abe = this[_0x4de649(0x1bf)][_0x17837d][_0x4de649(0x16e)] || {},
            _0x1216a0 = this[_0x4de649(0x3a7)](_0x605abe, _0x17837d)[_0x4de649(0x1ea)]();
        (this['drawTextEx'](_0x1216a0, _0x104be4['x'], _0x104be4['y'], _0x104be4[_0x4de649(0x155)]),
            this[_0x4de649(0x1b3)](_0x605abe, _0x104be4));
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x3a7)] = function (_0x3a3067, _0x3b65fd) {
        const _0x316abd = _0x457895,
            _0x30349c = this[_0x316abd(0x367)](_0x3b65fd)[_0x316abd(0x1ea)]();
        let _0x422451 = this[_0x316abd(0x1d2)](_0x3a3067)[_0x316abd(0x1ea)]();
        const _0x3cf052 = TextManager[_0x316abd(0x3dc)]['bonus'][_0x316abd(0x3c4)];
        return _0x30349c + '\x20' + _0x3cf052[_0x316abd(0x253)](_0x422451);
    }),
    (Window_DiceListBase['prototype'][_0x457895(0x1d2)] = function (_0x369c96) {
        const _0x6bde51 = _0x457895,
            _0x505d65 = TextManager['DICE_ROLL'][_0x6bde51(0x284)];
        let _0x4736bc = this[_0x6bde51(0x2b5)](_0x369c96);
        if (_0x369c96[_0x6bde51(0x264)]) {
            const _0xc789f9 = $gameActors[_0x6bde51(0x1b6)](_0x369c96[_0x6bde51(0x29b)]);
            if (_0xc789f9) {
                const _0x4a325e = _0x505d65[_0x6bde51(0x262)],
                    _0x174fdf = _0x4a325e[_0x6bde51(0x253)](_0xc789f9[_0x6bde51(0x366)]())[
                        _0x6bde51(0x1ea)
                    ]();
                if (_0x4736bc[_0x6bde51(0x1a1)] > 0x0) {
                    const _0x5600eb = _0x505d65[_0x6bde51(0x2e3)];
                    _0x4736bc = _0x5600eb[_0x6bde51(0x253)](_0x174fdf, _0x4736bc)['trim']();
                } else _0x4736bc += _0x174fdf;
            }
            _0x4736bc = _0x4736bc['trim']();
        }
        return _0x4736bc;
    }),
    (Window_DiceListBase[_0x457895(0x428)][_0x457895(0x2b5)] = function (_0x148059) {
        return '';
    }),
    (Window_DiceListBase[_0x457895(0x428)]['drawEffects'] = function (_0x3b46ef, _0x4e28ca) {
        const _0x4a2d37 = _0x457895;
        let _0x5264bc = this[_0x4a2d37(0x118)](_0x3b46ef)[_0x4a2d37(0x1ea)]();
        const _0x2c5d45 = TextManager['DICE_ROLL'][_0x4a2d37(0x13a)];
        if (_0x3b46ef[_0x4a2d37(0x432)]) {
            const _0xe4fa9c = _0x2c5d45[_0x4a2d37(0x202)];
            ((_0x5264bc = _0xe4fa9c[_0x4a2d37(0x253)](_0x3b46ef[_0x4a2d37(0x432)], _0x5264bc)),
                (_0x5264bc = _0x5264bc[_0x4a2d37(0x1ea)]()));
        }
        if (_0x3b46ef['PostEffectText']) {
            const _0x10696d = _0x2c5d45[_0x4a2d37(0x202)];
            ((_0x5264bc = _0x10696d['format'](_0x5264bc, _0x3b46ef[_0x4a2d37(0x143)])),
                (_0x5264bc = _0x5264bc[_0x4a2d37(0x1ea)]()));
        }
        _0x5264bc = _0x2c5d45[_0x4a2d37(0x3c4)][_0x4a2d37(0x253)](_0x5264bc)[_0x4a2d37(0x1ea)]();
        const _0x15d9b0 = this[_0x4a2d37(0x3a1)](_0x5264bc)['width'];
        this[_0x4a2d37(0x3f2)](
            _0x5264bc,
            _0x4e28ca['x'] + _0x4e28ca[_0x4a2d37(0x155)] - _0x15d9b0,
            _0x4e28ca['y']
        );
    }),
    (Window_DiceListBase['prototype']['getEffectText'] = function (_0x224e61) {
        const _0x4ad7c4 = _0x457895,
            _0x2ab2d3 = TextManager[_0x4ad7c4(0x3dc)][_0x4ad7c4(0x284)];
        let _0x80c0e6 = TextManager[_0x4ad7c4(0x2d3)](_0x224e61);
        return _0x80c0e6;
    }));
function Window_DiceEffectsList() {
    const _0x43ff3d = _0x457895;
    this[_0x43ff3d(0x31e)](...arguments);
}
function _0x1786() {
    const _0x25fa46 = [
        'ItemCostID',
        'WeaponCostID',
        'scale',
        'createDiceGraphic_D8',
        'bonusEffects',
        'nEJfS',
        'belowEqualTarget',
        'ShowUses',
        'removeChild',
        'Interweave',
        'addBonusCommand',
        'rldzF',
        'applyRngAspects',
        'registerCommand',
        'remove',
        'anchor',
        'uniqueSeed',
        '_scene',
        'ItemCostValue',
        'beginPath',
        'lineTo',
        '(×%2-%1)',
        'weaponCostFmt',
        'total',
        'Seed',
        'TargetValue',
        '_randomNumberSeeds',
        '_cache_createDiceGraphic_D4',
        'drawNumber',
        '_phase',
        'qJOkY',
        'criticalFailure',
        'fontSize',
        'TotalDiceToRoll',
        'darkenColor',
        'EPgDR',
        'name',
        'commandName',
        '_cache_createDiceGraphic_D8',
        'SkillCostID',
        'RVhFH',
        'VisuMZ_1_ItemsEquipsCore',
        'createBlurFilter',
        '\x5cC[%2]%1\x5cC[0]',
        'lineJoin',
        'vbRdK',
        'VisuMZ_ShuffleArray',
        '_moveDuration',
        'setupDiceAutoEffects',
        'advantage',
        '_numberText',
        'modifier',
        'randomizeNumber',
        'xkcmg',
        'SubtitleCritFailure',
        'playDiceBonusUse',
        'startFadeOut',
        'subtitle',
        'isSceneBattle',
        'Yabyp',
        'Cannot\x20roll\x20multiple\x20dice\x20at\x20the\x20same\x20time.',
        'SETTINGS',
        'lineHeight',
        'choiceBgType',
        'rgBBb',
        'CostDisplayFormat',
        '_diceRoll_RankWindow',
        '\x5cC[16]Dice\x20Rank:',
        'totalDiceRollChoices',
        'CostPrePostFormat',
        'd12',
        'setDataType',
        'animation',
        'RngSeed',
        'jiZwH',
        'initMembers',
        'DataDiceCount',
        'Min',
        'Roll\x20for\x20\x5cC[27]lowest\x5cC[0]\x20number',
        'drawCenteredText',
        'DiceOrderFmt',
        '_delayRollDuration',
        'ShowVariableCost',
        'multiplier',
        'modifierBgType',
        'color2',
        'filter',
        'Game_Interpreter_updateWaitMode',
        'GbKfh',
        'Game_Interpreter_PluginCommand',
        'ListColumns',
        'VariableReqID',
        'NsNnB',
        'hEXKH',
        'tpA',
        'textSizeEx',
        'diceDownColor',
        'value',
        'createSkillEffectDupes',
        'drawItem',
        'updateWaitMode',
        'getNameText',
        'dgKHX',
        'slice',
        'destroy',
        'oNWAs',
        'WeaponReqID',
        'MXvSP',
        'number',
        'push',
        'isAnyDiceAnimating',
        'endEndingPhase',
        'QQhwc',
        'increment',
        '\x5c}%1\x5c{',
        'ArmorEffects',
        'updatePhase',
        'Dice_TargetValue',
        'Dice%1',
        'WeaponEffects',
        'ShuffleSeededArray',
        'angle',
        'isDead',
        'KRSpE',
        '[%1]',
        'mlbdW',
        'isAnimating',
        'TetSW',
        'MessageCore',
        'setBackgroundType',
        'displayFmt',
        'loseItem',
        'BonusList_BgType',
        'GqyTa',
        '_diceRoll_ModifierWindow',
        'bitmap',
        '%1_name',
        'modifiers',
        'smvpW',
        'create',
        'moveSpriteToSubContainer',
        'ARRAYJSON',
        'Math_random',
        'Roll\x20for\x20\x5cC[23]total\x5cC[0]\x20number',
        'createDiceGraphic_D20',
        'ColorBorderRatio',
        'MaxUses',
        'ecvoY',
        '\x5cC[5]Difficulty\x20Class:\x5cC[0]\x20Above\x20\x5cC[24]%1\x5cC[0]',
        'processRollingPhase',
        '_wholeRollDuration',
        'random',
        'HkYWC',
        'fontOffsetX',
        'DICE_ROLL',
        'randomInt',
        'anySpecificActor',
        'AutoMods',
        'diceRollModifierWindowRect',
        'okkQH',
        'SriOw',
        'CostUseTimesFmt',
        'lineWidth',
        'Window',
        'diceRollEffectsWindowRect',
        'lmIWM',
        'numItems',
        'drawLabelText',
        'canPaySkillCost',
        'Adv%1',
        'itemLineRect',
        'SwitchID',
        'isAnyDiceVisible',
        'CommandRoll',
        'HuGBw',
        'STR',
        'drawTextEx',
        'floor',
        'Cannot\x20roll\x20dice\x20during\x20Evolution\x20Matrix\x20Skills.',
        'rolling',
        'isSkill',
        'DataDisadvantage',
        'MHNkj',
        'rollModDelay',
        'CostVarFormat',
        '2579658sgflwa',
        'ShowItemCost',
        'startFadeIn',
        'map',
        'prepare',
        'dvWbU',
        'Rgbrq',
        'VUXjR',
        'lineCap',
        'ItemEffects',
        'isRolling',
        '2308YiihTI',
        'Subtitle_BgType',
        'fVfdI',
        'setHandler',
        'cancel',
        'unlimitedUse',
        '_rollingDelay',
        'rotateSpeed',
        'modulus',
        'makeEffectName',
        '_mainContainer',
        'createDiceRollEffectsWindow',
        'createDiceRollModifierWindow',
        'ItemReqID',
        'VisuMZ_1_SkillsStatesCore',
        'textAlign',
        '%1\x20%2',
        'createDiceGraphic_D10',
        'qzWpc',
        'applyDiceEffect',
        'addChildToBack',
        'createAllDiceRollWindows',
        'Mdpuy',
        'parse',
        'useCurrentDiceBonusEffect',
        'playBuzzerSound',
        'clear',
        '_rngSaveUnique',
        '_diceRoll_BonusWindow',
        'createNewRandomNumberSeed',
        'MAX_DICE',
        'EFFECT_LIST_KEYS',
        '_cache_createDiceGraphic_D20',
        'diceUpColor',
        'prototype',
        'QoKdy',
        'createAllSkillCostText',
        '_scaleTarget',
        'DiceModifierRand',
        'SubtitleAboveEqualTarget',
        'Settings',
        '241owjOuY',
        'isDiceBonusEffectIncluded',
        'EQVHN',
        'PreEffectText',
        'BKuUV',
        'jxisS',
        '32iTHOfT',
        'isPlaytest',
        '\x5cC[27]Disadvantage:',
        'rgba(0,\x200,\x200,\x200.9)',
        'endRollingPhase',
        'Enaws',
        '_smooth',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'TotalRolls',
        'moveSpriteToSubContainerExcept',
        'abs',
        'numberDuration',
        'createDiceGraphic_D12',
        'EnableSwitchID',
        'numberColorShift',
        'startDiceRoll',
        'setWaitMode',
        'Sound',
        'min',
        'DiceModifier',
        'height',
        'PreCostText',
        'ResultType',
        'ECAWE',
        'getRandomNumberSeedUniqueKey',
        '_baseX',
        'd20',
        'InjectedRngSeed',
        'NThzo',
        'positionOffsetX',
        'GlRCz',
        'HLOrK',
        'GBpPj',
        'clearRngAspects',
        'DiceSides',
        'createDiceGraphic_D6',
        'ARRAYFUNC',
        'WzkNz',
        'gVgno',
        'Game_Action_apply',
        'note',
        'Command_RectJS',
        'itemPadding',
        'save',
        'owIyk',
        'DmHEY',
        'activate',
        'mpA',
        'DICE_ROLL_RATIOS',
        'isActor',
        '_lastAdvantage',
        'UDSuT',
        'getEffectText',
        '_diceRoll_ChoiceWindow',
        'rollDelay',
        '_lastModifier',
        'ArmorCostID',
        'createDiceGraphic_D4',
        'charCodeAt',
        '1123150NcUEdn',
        'isLearnedSkill',
        'clone',
        'setupDiceRoll',
        'AnimationID',
        'Icon',
        'meetsDiceAutoEffectConditions',
        'DicePrePostFormat',
        'effectsBgType',
        '_numberColor',
        'VisuMZ_3_ActiveChainSkills',
        'center',
        'ShowSwitchID',
        'diceRollAdvantageWindowRect',
        'onDiceRollBonusUseEffect',
        'makeCommandList',
        'finalizeDelay',
        'onDiceRollBonusCancel',
        'description',
        'OGjNO',
        'reduce',
        'filters',
        'allowNaturalRolls',
        'DataModifiers',
        'max',
        'modUpColor',
        'PJCiW',
        'effectList',
        'loadDicePicture',
        '_dataType',
        'open',
        'startClosingDiceWindows',
        'showUseMax1',
        'getFullYear',
        '_baseY',
        'ARRAYSTRUCT',
        'PostEffectText',
        'Title',
        'CostSkillUserFmt',
        'innerWidth',
        'ListColSpacing',
        'Title_RectJS',
        'apply',
        'everyBattleMember',
        'jHbbP',
        'find',
        'BonusMods',
        'needsRefresh',
        'updateSubContainer',
        'Color2Ratio',
        'AllowDiceBonuses',
        'offsetX',
        'processEndingPhase',
        'VariableID',
        'width',
        'CurrentDiceRank',
        'qTupX',
        '_naturalRoll',
        'createSpriteContainers',
        'Cannot\x20roll\x20dice\x20during\x20Active\x20Chain\x20Skills.',
        '_settingsKey',
        '_diceRollSettings',
        'dailySeed',
        'plusSign',
        'EdbQE',
        'CommandShowBonus',
        'getRandomNumberSeedKey',
        'aVhuK',
        '_waitMode',
        'WFdsN',
        'item',
        'cacheData',
        'ytQmK',
        'kGFWj',
        'getRandomNumberSeeds',
        'rankAdjust',
        'isEnabled',
        'playDiceFailure',
        'colors',
        'ext',
        'positionOffsetY',
        'usedUp',
        'ApplyUnique',
        'createDiceRollSubtitleWindow',
        'modRand',
        '_cache_createDiceGraphic_D12',
        'rankFmt',
        'setValue',
        'VisuMZ_1_MessageCore',
        'ZhagZ',
        'MaxDiceCount',
        'subject',
        'WsvTS',
        'VariableCostValue',
        'ConvertParams',
        'disadvantageFmt',
        'aboveEqualTarget',
        'DataDiceRank',
        'D20',
        'exit',
        'effectsList',
        'commandDiceRoll',
        'endRecordingPhase',
        'ShuffleArray',
        'EvFKZ',
        'refresh',
        'mPmqb',
        'Bjmiz',
        'updateRoll',
        'zHJVJ',
        'ItemReqValue',
        '-actor-%1',
        'getDate',
        'SetupDiceRoll',
        'success',
        '\x5cHEXCOLOR<%1>%2\x5cC[0]',
        'disadvantage',
        'showBonuses',
        'right',
        'updateRotation',
        'USED!',
        'everyMember',
        'npPOn',
        'spdke',
        'iSyfp',
        '439082BoXSEd',
        'close',
        'HUivX',
        'armorCostFmt',
        'Variable_RngRange',
        'length',
        'Cannot\x20roll\x20dice\x20during\x20a\x20QTE.',
        '\x5cC[2]CRITICAL\x20FAILURE!\x5cC[0]',
        'border',
        'D10',
        'Pyeug',
        'Dice_RollValue',
        'lowest',
        'LtXaf',
        'JSON',
        'numberFontFace',
        '_lastPluginCommandInterpreter',
        'fadeInDuration',
        '_diceRoll_TitleWindow',
        'Max',
        'jbBkf',
        'isIncluded',
        'kieCu',
        'drawEffects',
        'wSJPM',
        'setupShuffleChoices',
        'actor',
        'FLHCj',
        'isCommandEnabled',
        'DataCount_RectJS',
        'AXhij',
        'Subtitle_RectJS',
        'aboveTarget',
        'pop',
        'createDiceGraphic',
        '_list',
        'AlMPj',
        'FUNC',
        'effect',
        'PostCostText',
        'kXsEy',
        'RVyox',
        'Undefined',
        'targetValue',
        'favYq',
        '_context',
        '_baseTexture',
        'rngSeed',
        'playDiceIncrement',
        'getDiceRollSubtitle',
        'QWMoC',
        'showEffects',
        'RegExp',
        'HDlTI',
        'getNameExtra',
        'toUpperCase',
        '_evoMatrixSkillMode',
        'vBhtV',
        'rollDuration',
        'YtDzb',
        'onDiceRollEffectCancel',
        'diceRollSpriteWindowRect',
        '\x5cC[16]Modifiers:',
        'DiceDisadvantage',
        'Roll\x20for\x20\x5cC[24]highest\x5cC[0]\x20number',
        'playDiceSuccess',
        'count',
        'pqBPV',
        'rank%1',
        'advantageBgType',
        'mainFontSize',
        'SkillUser',
        '_blurFilter',
        'Title_BgType',
        'Vocab',
        'SkillReqID',
        'version',
        'changePaintOpacity',
        'trim',
        'CostItemFormat',
        '_moveTargetX',
        'failure',
        'VariableCostID',
        'CostSkillUserName',
        'VisuMZ_2_QTE_TriggerSys',
        'createBitmap',
        'ShowJS',
        'updateScaleEffect',
        'tick',
        'removeOldDice',
        'rank',
        'color-%1',
        'vs-',
        'isActiveChainSkillsUiVisible',
        'rankUpColor',
        'tFaKE',
        'fontOffsetY',
        'getMonth',
        'meNsU',
        'hasSkill',
        'ARRAYSTR',
        'DiceRollsRngSeeds',
        'effectTextFmt',
        '_diceRoll_SpriteWindow',
        'APXMV',
        '_subContainer',
        'VariableReqValue',
        'isPlayingQTE',
        'rollValue',
        'DataAdvantage',
        'ActivateJS',
        'match',
        '%1~%2',
        'update',
        'Name',
        'ending',
        'allMembers',
        'diceRollChoiceWindowRect',
        'isRollingDice',
        '7067187ctClSk',
        'createNewDice',
        'ceil',
        'JkXDG',
        'createNumberSprite',
        '\x5cC[5]Difficulty\x20Class:\x5cC[0]\x20Below\x20\x5cC[24]%1\x5cC[0]',
        'addRollCommand',
        'calcWindowHeight',
        'currentExt',
        'diceRollRankWindowRect',
        'getColor',
        '_fadeTarget',
        'WeaponIncludeEquip',
        '651IsKxgD',
        'Color3Ratio',
        'DataRank_BgType',
        'ItXmj',
        'join',
        'DJaUo',
        'createDiceRollBonusWindow',
        'Advantage',
        'BlurFilter',
        'Container_RectJS',
        'NaturalRolls',
        'NzECu',
        'DefaultRngSeed',
        'bezierCurveTo',
        'diceRollSubtitleWindowRect',
        'createDiceRollSpriteWindow',
        'SlNKf',
        'DjEYA',
        'canRollDice',
        'children',
        'DataRank5',
        'highest',
        'butt',
        'final',
        '_numberSprite',
        '_scaleDuration',
        'dbMDY',
        'type',
        'actorId',
        'round',
        'hasItem',
        'meImM',
        'DicePlusSigns',
        'Rank%1',
        'setupDiceEffects',
        'oQmaF',
        'choices',
        'ApplyDaily',
        'DiceAdvantage',
        'enemyId',
        'addChild',
        'ShowArmorCost',
        'Roll%1',
        'vhIpy',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'startMoveToX',
        'Color1Ratio',
        'restore',
        'openness',
        '_randomNumberSeedUniqueKey',
        '%1_pitch',
        'format',
        'miter',
        '_finalizeDuration',
        'useTimesFmt',
        'eCjat',
        'sLkHH',
        'oRqSZ',
        'average',
        'status',
        '_inputComboSkillMode',
        'TotalDiceChoices',
        'RjCMg',
        'Roll\x20Dice',
        'diceRollTitleWindowRect',
        'initRandomNumberSeeds',
        'skillUserFmt',
        'updateBlurEffect',
        'SkillListUserName',
        'sjISS',
        'battleMembers',
        'playDiceThrow',
        'isChangingScale',
        'playOkSound',
        '_cache_createDiceGraphic_D10',
        'WOHgx',
        'gSmFU',
        'roll',
        'anyBattleMember',
        'addDiceBonusEffects',
        'DataRank_RectJS',
        'zGDnq',
        'processRecordingPhase',
        '_lastRank',
        'ArmorReqID',
        'SubtitleHighest',
        '-item-%1',
        'itfPG',
        '_rngSeed',
        '_choices',
        'everySpecificActor',
        '_numberShiftTarget',
        'GetDailyKey',
        'some',
        'textColor',
        'modifierFmt',
        'CostArmorFormat',
        'JmZVe',
        'getNextRangeRandomNumberFromSeed',
        'maxCols',
        'bonus',
        'Reset_RngSeed',
        'filenames',
        'pVTAi',
        'startModifiersPhase',
        'Command_TextAlign',
        'criticalSuccess',
        'Command_BgType',
        'maxNumberDuration',
        'unknownName',
        'xOdif',
        'GRskV',
        'oFBHb',
        'fadeOutDuration',
        'moveTo',
        'log',
        '%1_pan',
        'CurrentUses',
        '\x5cC[5]Difficulty\x20Class:\x5cC[0]\x20At\x20least\x20\x5cC[24]%1\x5cC[0]',
        'createDiceRollChoiceWindow',
        'iconIndex',
        'changeScaleTo',
        '_numberShiftDuration',
        'SkillUserActorID',
        'getImageFilenames',
        'opacity',
        'padStart',
        'setBaseXy',
        'diceCountFmt',
        'CostUnlimitedUse',
        'hhtEK',
        'call',
        'scaleDuration',
        'crit_failure',
        'playSe',
        'SIBOq',
        'diceRoll',
        '_lastModRand',
        'setupDiceBonusEffects',
        'moveDuration',
        'Game_System_initialize',
        '_fadeDuration',
        'BxVGq',
        'dOHMz',
        'JzBTm',
        'color1',
        'addEffectsCommand',
        'AdbvZ',
        'fill',
        'getNameCost',
        'SkillEffects',
        'deactivate',
        '%1_filenames',
        '(×%1/%2)',
        'rankDownColor',
        'effects',
        '_rngDaily',
        'clamp',
        'indexOf',
        'CommandEffects',
        '_cache_createDiceGraphic_D6',
        'endModifiersPhase',
        'SlzBZ',
        '_logWindow',
        'modDownColor',
        'WYSQY',
        '_rollDuration',
        '_diceRoll_EffectsWindow',
        'startEndingPhase',
        'ConditionJS',
        'evnmQ',
        'drawDataText',
        'QOnnH',
        'isNumberShifting',
        '_customModified',
        '_numberValue',
        'startNumberShift',
        '_diceRoll_SubtitleWindow',
        'commandDiceEffects',
        'getDiceRollEffectsText',
        'pow',
        'DAfqw',
        'toString',
        'createDiceRollRankWindow',
        '1494mxvbaJ',
        'EwRyd',
        'needsNewDiceBatch',
        'kiRWC',
        'DataRank6',
        'huvwh',
        'AZBaV',
        'SpecificActorIDs',
        'evMRO',
        '\x5cC[29]%1\x20%2',
        'isMoving',
        'skillUserCostFmt',
        '%1\x20%2\x20%3',
        '\x5cC[%1]%2\x5cC[0]',
        'OaAOE',
        'getImageColors',
        'crit_success',
        'SkillUserAlive',
        'DiceSeed',
        'd10',
        'variableCostFmt',
        'constructor',
        'XQsdB',
        'fillStyle',
        '_diceRoll',
        'auto',
        'setNumberColor',
        'EKVKz',
        'QueeV',
        'index',
        'Cannot\x20roll\x20dice\x20during\x20Input\x20Combo\x20Skills.',
        'blur',
        'diceSettings',
        'yqCrg',
        '\x5cI[%2]%1',
        'List_RectJS',
        'ShowWeaponCost',
        'getLastPluginCommandInterpreter',
        'kSJXZ',
        'skillMpCost',
        '_diceRoll_AdvantageWindow',
        'ArmorCostValue',
        'CurrentDiceSides',
        'resetRandomNumberSeed',
        'ykBZK',
        'startRecordingPhase',
        'dailyUniqueSeed',
        'endDiceRoll',
        'replace',
        'createDiceRollTitleWindow',
        'DbTIA',
        'createDiceRollAdvantageWindow',
        'setBaseScale',
        'belowTarget',
        'columns',
        'Dice',
        'WeaponCostValue',
        'uLLdc',
        'ossfL',
        'includes',
        'removeAllDiceRollWindows',
        'updateOpacity',
        'TCLoz',
        'none',
        'xpHBk',
        'NlccC',
        'skillTpCost',
        'commandDiceBonus',
        'getNextFloatRandomNumberFromSeed',
        'bonusBgType',
        'initialize',
        'updateNumberShift',
        'orderFmt',
        'leader',
        'startRollingPhase',
        'bind',
        'AllowDiceEffects',
        'DataMod_RectJS',
        'EffectList_BgType',
        'EFvaW',
        'getNextIntRandomNumberFromSeed',
        'setupShuffleChoiceSeed',
        'command357',
        'playDiceSound',
        'updateCacheInfo',
        'CommandBonus',
        'addCommand',
        'DiceRank',
        'setLastPluginCommandInterpreter',
        'NUM',
        '3325015bJMrYY',
        '\x5cC[23]%1\x20%2',
        'eZjBX',
        'xgwFl',
        'XUqhH',
        'recording',
        'advantageFmt',
        'innerHeight',
        'color3',
        'SkillLearnedOnly',
        'anyMember',
        'Game_Message_setupShuffleChoices',
        'segJO',
        'costTextFmt',
        '_diceSides',
        'makeEffectCommand',
    ];
    _0x1786 = function () {
        return _0x25fa46;
    };
    return _0x1786();
}
function _0x3a35(_0x1cfe9f, _0xda924d) {
    const _0x1786b1 = _0x1786();
    return (
        (_0x3a35 = function (_0x3a3561, _0xebfef1) {
            _0x3a3561 = _0x3a3561 - 0xef;
            let _0x35ca9f = _0x1786b1[_0x3a3561];
            return _0x35ca9f;
        }),
        _0x3a35(_0x1cfe9f, _0xda924d)
    );
}
((Window_DiceEffectsList[_0x457895(0x428)] = Object[_0x457895(0x3cd)](
    Window_DiceListBase[_0x457895(0x428)]
)),
    (Window_DiceEffectsList[_0x457895(0x428)]['constructor'] = Window_DiceEffectsList),
    (Window_DiceEffectsList[_0x457895(0x428)]['initialize'] = function (_0x450ae3) {
        const _0x32e092 = _0x457895;
        Window_DiceListBase[_0x32e092(0x428)][_0x32e092(0x31e)][_0x32e092(0x2a3)](this, _0x450ae3);
    }),
    (Window_DiceEffectsList[_0x457895(0x428)][_0x457895(0x269)] = function () {}),
    (Window_DiceEffectsList[_0x457895(0x428)][_0x457895(0x41f)] = function () {}),
    (Window_DiceEffectsList[_0x457895(0x428)][_0x457895(0x183)] = function () {
        const _0x5579e7 = _0x457895,
            _0x17b058 = SceneManager[_0x5579e7(0x15c)] || {},
            _0x2d90f1 = _0x17b058['effects'] || [];
        return _0x2d90f1;
    }));
function Window_DiceBonusList() {
    const _0x1dd838 = _0x457895;
    this[_0x1dd838(0x31e)](...arguments);
}
((Window_DiceBonusList['prototype'] = Object['create'](Window_DiceListBase[_0x457895(0x428)])),
    (Window_DiceBonusList['prototype'][_0x457895(0x2ed)] = Window_DiceBonusList),
    (Window_DiceBonusList[_0x457895(0x428)]['initialize'] = function (_0x2b25cf) {
        const _0x1edae4 = _0x457895;
        Window_DiceListBase[_0x1edae4(0x428)][_0x1edae4(0x31e)]['call'](this, _0x2b25cf);
    }),
    (Window_DiceBonusList[_0x457895(0x428)][_0x457895(0x269)] = function () {
        const _0xe29690 = _0x457895;
        SoundManager[_0xe29690(0x379)]();
    }),
    (Window_DiceBonusList[_0x457895(0x428)][_0x457895(0x183)] = function () {
        const _0x20bb16 = _0x457895,
            _0x24d6f5 = SceneManager[_0x20bb16(0x15c)] || {},
            _0x33e104 = _0x24d6f5[_0x20bb16(0x346)] || [];
        return _0x33e104;
    }),
    (Window_DiceBonusList[_0x457895(0x428)][_0x457895(0x16b)] = function (_0x397a43) {
        const _0x4c0d89 = _0x457895;
        if (!_0x397a43) return ![];
        if (_0x397a43['VariableCostID']) {
            const _0x3d48c3 = $gameVariables[_0x4c0d89(0x3a3)](_0x397a43[_0x4c0d89(0x1ee)]),
                _0x40915d = Math['max'](_0x397a43[_0x4c0d89(0x17c)] || 0x1, 0x1);
            if (_0x40915d > _0x3d48c3) return ![];
        }
        if (_0x397a43[_0x4c0d89(0x342)]) {
            const _0x4ffabf = $dataItems[_0x397a43[_0x4c0d89(0x342)]],
                _0x56faee = Math['max'](_0x397a43[_0x4c0d89(0x354)] || 0x1, 0x1),
                _0x42718c = $gameParty[_0x4c0d89(0x3e8)](_0x4ffabf);
            if (_0x56faee > _0x42718c) return ![];
        }
        if (_0x397a43[_0x4c0d89(0x343)]) {
            if (_0x4c0d89(0x167) !== _0x4c0d89(0x167)) return !![];
            else {
                const _0x50af67 = $dataWeapons[_0x397a43[_0x4c0d89(0x343)]],
                    _0x148a5e = Math[_0x4c0d89(0x137)](_0x397a43['WeaponCostValue'] || 0x1, 0x1),
                    _0x504ac8 = $gameParty[_0x4c0d89(0x3e8)](_0x50af67);
                if (_0x148a5e > _0x504ac8) return ![];
            }
        }
        if (_0x397a43[_0x4c0d89(0x11c)]) {
            const _0x47e174 = $dataArmors[_0x397a43[_0x4c0d89(0x11c)]],
                _0x3ef6c7 = Math['max'](_0x397a43[_0x4c0d89(0x301)] || 0x1, 0x1),
                _0x340850 = $gameParty['numItems'](_0x47e174);
            if (_0x3ef6c7 > _0x340850) return ![];
        }
        if (_0x397a43['SkillCostID']) {
            if (_0x4c0d89(0x100) === _0x4c0d89(0x100)) {
                const _0x152668 = $gameActors[_0x4c0d89(0x1b6)](_0x397a43[_0x4c0d89(0x29b)]),
                    _0x33708a = $dataSkills[_0x397a43['SkillCostID']];
                if (!_0x152668['canPaySkillCost'](_0x33708a)) return ![];
            } else this[_0x4c0d89(0x2cf)] = this[_0x4c0d89(0x27b)];
        }
        if (_0x397a43[_0x4c0d89(0x3d4)]) {
            const _0x40136d = _0x397a43[_0x4c0d89(0x295)] || 0x0,
                _0x5c7539 = _0x397a43[_0x4c0d89(0x3d4)] || 0x1;
            if (_0x5c7539 < 0xf4240 && _0x40136d >= _0x5c7539) return ![];
        }
        if (_0x397a43[_0x4c0d89(0xf1)]) {
            if (!$gameSwitches[_0x4c0d89(0x3a3)](_0x397a43[_0x4c0d89(0xf1)])) return ![];
        }
        if (_0x397a43['EnableJS']) {
            if (!_0x397a43['EnableJS']()) return ![];
        }
        return !![];
    }),
    (Window_DiceBonusList[_0x457895(0x428)][_0x457895(0x1d2)] = function (_0x9f278a) {
        const _0x27ca78 = _0x457895,
            _0x11ec64 = TextManager[_0x27ca78(0x3dc)][_0x27ca78(0x284)];
        let _0x1c1780 = Window_DiceListBase[_0x27ca78(0x428)]['getNameExtra'][_0x27ca78(0x2a3)](
            this,
            _0x9f278a
        );
        if (_0x11ec64[_0x27ca78(0x170)]) {
            if (_0x27ca78(0x1b4) !== _0x27ca78(0x1b4))
                (_0xc9e8b8[_0x27ca78(0x428)][_0x27ca78(0x31e)][_0x27ca78(0x2a3)](this),
                    this[_0x27ca78(0x38d)](),
                    this[_0x27ca78(0x1f1)](),
                    this[_0x27ca78(0x217)](),
                    this[_0x27ca78(0x36c)]());
            else {
                const _0x27873d = _0x9f278a[_0x27ca78(0x295)] || 0x0,
                    _0x549e67 = _0x9f278a[_0x27ca78(0x3d4)] || 0x1;
                if (_0x27873d >= _0x549e67) return _0x11ec64[_0x27ca78(0x170)][_0x27ca78(0x1ea)]();
            }
        }
        if (_0x9f278a['PreCostText']) {
            const _0x1a37ee = _0x11ec64[_0x27ca78(0x33f)];
            ((_0x1c1780 = _0x1a37ee[_0x27ca78(0x253)](_0x9f278a[_0x27ca78(0xf9)], _0x1c1780)),
                (_0x1c1780 = _0x1c1780['trim']()));
        }
        if (_0x9f278a['PostCostText']) {
            if ('TetSW' !== _0x27ca78(0x3c1)) {
                this[_0x27ca78(0x35f)] = _0x27ca78(0x3f5);
                const _0x3785a2 = this[_0x27ca78(0x410)]['children']['clone']();
                _0x45adb3[_0x27ca78(0x201)][_0x27ca78(0x186)](_0x3785a2);
                let _0x24b39d = 0x0;
                for (const _0xd5de0b of _0x3785a2) {
                    (_0xd5de0b[_0x27ca78(0xf3)](_0x24b39d), _0x24b39d++);
                }
            } else {
                const _0x19ddb1 = _0x11ec64[_0x27ca78(0x33f)];
                ((_0x1c1780 = _0x19ddb1[_0x27ca78(0x253)](_0x1c1780, _0x9f278a[_0x27ca78(0x1c3)])),
                    (_0x1c1780 = _0x1c1780[_0x27ca78(0x1ea)]()));
            }
        }
        return _0x1c1780;
    }),
    (Window_DiceBonusList[_0x457895(0x428)]['getNameCost'] = function (_0x50b2c8) {
        const _0x5d8989 = _0x457895,
            _0x41d81f = TextManager[_0x5d8989(0x3dc)][_0x5d8989(0x284)];
        let _0x180e0f = Window_DiceListBase[_0x5d8989(0x428)][_0x5d8989(0x2b5)][_0x5d8989(0x2a3)](
            this,
            _0x50b2c8
        );
        if (_0x50b2c8[_0x5d8989(0x349)]) {
            const _0x5eaa4f = _0x50b2c8[_0x5d8989(0x295)] || 0x0,
                _0x5dc153 = _0x50b2c8['MaxUses'] || 0x1,
                _0xd8bb33 = _0x5dc153 - _0x5eaa4f,
                _0xffe5b7 = _0x41d81f[_0x5d8989(0x256)];
            if (_0x5dc153 >= 0xf4240) {
                if (_0x5d8989(0x39a) !== _0x5d8989(0x39a)) {
                    _0x5d7dfc = _0x3190fb || 0x0;
                    let _0x21eb10 = _0x447b0a[_0x5d8989(0x21d)](_0x278770),
                        _0x470065 = _0x647742(_0x21eb10['slice'](0x1, 0x3), 0x10),
                        _0x3c4482 = _0x359595(_0x21eb10[_0x5d8989(0x3a9)](0x3, 0x5), 0x10),
                        _0x28a8f9 = _0x2624d3(_0x21eb10[_0x5d8989(0x3a9)](0x5, 0x7), 0x10);
                    return (
                        (_0x470065 = _0x48c3b0[_0x5d8989(0x137)](
                            0x0,
                            _0x3bc321[_0x5d8989(0xf6)](
                                0xff,
                                _0x4ed357[_0x5d8989(0x3f3)](_0x470065 * _0x4bacbe)
                            )
                        )[_0x5d8989(0x2d6)](0x10)),
                        (_0x3c4482 = _0x4b731a[_0x5d8989(0x137)](
                            0x0,
                            _0x21f515[_0x5d8989(0xf6)](
                                0xff,
                                _0x44e4ab[_0x5d8989(0x3f3)](_0x3c4482 * _0xf8051b)
                            )
                        )[_0x5d8989(0x2d6)](0x10)),
                        (_0x28a8f9 = _0x4a9df6[_0x5d8989(0x137)](
                            0x0,
                            _0x209ef1['min'](
                                0xff,
                                _0x552a4d[_0x5d8989(0x3f3)](_0x28a8f9 * _0x2e7b46)
                            )
                        )[_0x5d8989(0x2d6)](0x10)),
                        '#' +
                            _0x470065[_0x5d8989(0x29e)](0x2, '0') +
                            _0x3c4482['padStart'](0x2, '0') +
                            _0x28a8f9[_0x5d8989(0x29e)](0x2, '0')
                    );
                } else _0x180e0f += '\x20' + _0x41d81f[_0x5d8989(0x40b)];
            } else {
                if (_0x5dc153 >= 0x2 || (_0x5dc153 === 0x1 && _0x41d81f[_0x5d8989(0x13f)])) {
                    if ('hEXKH' !== _0x5d8989(0x39f)) {
                        const _0x5aef26 = _0xe1a8b1['CurrentUses'] || 0x0,
                            _0x40feb3 = _0x46daae[_0x5d8989(0x3d4)] || 0x1,
                            _0x4e38c1 = _0x40feb3 - _0x5aef26,
                            _0x1911e0 = _0x15c76a[_0x5d8989(0x256)];
                        if (_0x40feb3 >= 0xf4240) _0x4b3a2c += '\x20' + _0x5c3d1f['unlimitedUse'];
                        else
                            (_0x40feb3 >= 0x2 ||
                                (_0x40feb3 === 0x1 && _0x5bb811[_0x5d8989(0x13f)])) &&
                                (_0x163a72 +=
                                    '\x20' +
                                    _0x1911e0[_0x5d8989(0x253)](_0x5aef26, _0x40feb3, _0x4e38c1));
                        _0x55e423 = _0x391a21[_0x5d8989(0x1ea)]();
                    } else
                        _0x180e0f +=
                            '\x20' + _0xffe5b7[_0x5d8989(0x253)](_0x5eaa4f, _0x5dc153, _0xd8bb33);
                }
            }
            _0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]();
        }
        if (_0x50b2c8[_0x5d8989(0x1ee)] && _0x50b2c8[_0x5d8989(0x394)]) {
            const _0x14740a = $gameVariables[_0x5d8989(0x3a3)](_0x50b2c8[_0x5d8989(0x1ee)]),
                _0x5cb8c5 = Math[_0x5d8989(0x137)](_0x50b2c8[_0x5d8989(0x17c)] || 0x1, 0x1),
                _0x5b67ca = _0x41d81f[_0x5d8989(0x2ec)];
            ((_0x180e0f += '\x20' + _0x5b67ca[_0x5d8989(0x253)](_0x5cb8c5, _0x14740a)),
                (_0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]()));
        }
        if (_0x50b2c8[_0x5d8989(0x342)] && _0x50b2c8[_0x5d8989(0x3fc)]) {
            const _0x31627e = $dataItems[_0x50b2c8[_0x5d8989(0x342)]],
                _0x293775 = Math[_0x5d8989(0x137)](_0x50b2c8[_0x5d8989(0x354)] || 0x1, 0x1),
                _0x5a842c = $gameParty[_0x5d8989(0x3e8)](_0x31627e),
                _0x3a69a5 = _0x41d81f['itemCostFmt'];
            ((_0x180e0f += '\x20' + _0x3a69a5[_0x5d8989(0x253)](_0x293775, _0x5a842c)),
                (_0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]()));
        }
        if (_0x50b2c8[_0x5d8989(0x343)] && _0x50b2c8[_0x5d8989(0x2fc)]) {
            if (_0x5d8989(0x318) === _0x5d8989(0x164)) {
                if (this[_0x5d8989(0x405)]()) return !![];
                if (this[_0x5d8989(0x2e2)]()) return !![];
                if (this[_0x5d8989(0x2cd)]()) return !![];
                if (this['isChangingScale']()) return !![];
                return ![];
            } else {
                const _0x3c955b = $dataWeapons[_0x50b2c8[_0x5d8989(0x343)]],
                    _0x310907 = Math[_0x5d8989(0x137)](_0x50b2c8['WeaponCostValue'] || 0x1, 0x1),
                    _0x371c99 = $gameParty[_0x5d8989(0x3e8)](_0x3c955b),
                    _0x5e4e0f = _0x41d81f[_0x5d8989(0x358)];
                ((_0x180e0f += '\x20' + _0x5e4e0f[_0x5d8989(0x253)](_0x310907, _0x371c99)),
                    (_0x180e0f = _0x180e0f['trim']()));
            }
        }
        if (_0x50b2c8['ArmorCostID'] && _0x50b2c8[_0x5d8989(0x249)]) {
            if (_0x5d8989(0x377) !== _0x5d8989(0x377))
                (this[_0x5d8989(0x119)][_0x5d8989(0x19d)](),
                    this[_0x5d8989(0x422)][_0x5d8989(0x13d)](),
                    this['_diceRoll_BonusWindow']['activate'](),
                    this[_0x5d8989(0x422)][_0x5d8989(0x188)]());
            else {
                const _0x55c710 = $dataArmors[_0x50b2c8['ArmorCostID']],
                    _0x460ab = Math[_0x5d8989(0x137)](_0x50b2c8[_0x5d8989(0x301)] || 0x1, 0x1),
                    _0x287272 = $gameParty['numItems'](_0x55c710),
                    _0x24948f = _0x41d81f[_0x5d8989(0x19f)];
                ((_0x180e0f += '\x20' + _0x24948f[_0x5d8989(0x253)](_0x460ab, _0x287272)),
                    (_0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]()));
            }
        }
        if (_0x50b2c8[_0x5d8989(0x369)] && _0x50b2c8['ShowSkillCost']) {
            if (_0x5d8989(0x2f9) !== _0x5d8989(0x281)) {
                const _0x56aca4 = $gameActors['actor'](_0x50b2c8[_0x5d8989(0x29b)]);
                if (_0x56aca4) {
                    const _0x3ef919 = $dataSkills[_0x50b2c8[_0x5d8989(0x369)]];
                    if (Imported[_0x5d8989(0x414)]) {
                        let _0x2fe2c4 = this[_0x5d8989(0x42a)](_0x56aca4, _0x3ef919);
                        ((_0x2fe2c4 = _0x2fe2c4[_0x5d8989(0x308)](/\\FS\[(\d+)\]/gi, '')),
                            (_0x2fe2c4 = _0x2fe2c4[_0x5d8989(0x308)](/\\{/gi, '')),
                            (_0x2fe2c4 = _0x2fe2c4['replace'](/\\}/gi, '')),
                            (_0x180e0f += '\x20' + _0x2fe2c4['trim']()),
                            (_0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]()));
                    } else {
                        if (_0x5d8989(0x1df) === 'UewrT') this[_0x5d8989(0x31e)](...arguments);
                        else {
                            const _0x4ad329 = _0x56aca4[_0x5d8989(0x2ff)](_0x3ef919);
                            if (_0x4ad329 > 0x0) {
                                if ('ZsnCr' === _0x5d8989(0x22b)) {
                                    ((this[_0x5d8989(0xfd)] = _0x3d4e05),
                                        (this['_baseY'] = _0x5f6f83));
                                    {
                                        const _0x203724 = _0x2df274[_0x5d8989(0x37f)];
                                        ((this[_0x5d8989(0xfd)] += _0x203724[_0x5d8989(0x152)]),
                                            (this[_0x5d8989(0x141)] += _0x203724['offsetY']));
                                    }
                                    {
                                        const _0x12fafe = this[_0x5d8989(0x2f8)]();
                                        ((this['_baseX'] += _0x12fafe[_0x5d8989(0x101)]),
                                            (this[_0x5d8989(0x141)] +=
                                                _0x12fafe['positionOffsetY']));
                                    }
                                    ((this['x'] = this[_0x5d8989(0xfd)]),
                                        (this['y'] = this['_baseY']),
                                        this[_0x5d8989(0x3fd)]());
                                } else
                                    ((_0x180e0f +=
                                        '\x20' +
                                        _0x5d8989(0x333)['format'](
                                            _0x4ad329,
                                            TextManager[_0x5d8989(0x113)]
                                        )),
                                        (_0x180e0f = _0x180e0f['trim']()));
                            }
                            const _0x22ef73 = _0x56aca4[_0x5d8989(0x31a)](_0x3ef919);
                            _0x22ef73 > 0x0 &&
                                ((_0x180e0f +=
                                    '\x20' +
                                    _0x5d8989(0x2e1)[_0x5d8989(0x253)](
                                        _0x22ef73,
                                        TextManager[_0x5d8989(0x3a0)]
                                    )),
                                (_0x180e0f = _0x180e0f[_0x5d8989(0x1ea)]()));
                        }
                    }
                }
            } else {
                const _0x1c6b13 = _0x4c0ef6[_0x5d8989(0x206)] ?? 0x1;
                if (_0x2dfb59[_0x5d8989(0x3a3)](_0x2129e9[_0x5d8989(0x39d)]) < _0x1c6b13)
                    return ![];
            }
        }
        return _0x180e0f;
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x424)] =
        VisuMZ[_0x457895(0x201)]['Settings'][_0x457895(0x30f)][_0x457895(0x179)] ?? 0x5),
    (VisuMZ[_0x457895(0x201)]['TotalDiceToRoll'] = function () {
        const _0x43dbbc = _0x457895;
        if (!SceneManager[_0x43dbbc(0x212)]()) return 0x0;
        const _0x46d43 = SceneManager['_diceRollSettings'] || {};
        let _0x13d151 = _0x46d43[_0x43dbbc(0x43d)] || 0x1;
        if (_0x46d43[_0x43dbbc(0x23b)] === 'rollValue') _0x13d151 += _0x46d43['advantage'] || 0x0;
        else
            _0x46d43[_0x43dbbc(0x23b)] === _0x43dbbc(0x1c7) &&
                (_0x43dbbc(0x1ce) !== 'QWMoC'
                    ? this[_0x43dbbc(0x430)](_0x255e3c) && this[_0x43dbbc(0x26f)](_0x167cc6)
                    : (_0x13d151 += Math[_0x43dbbc(0x43f)](_0x46d43['advantage'] || 0x0)));
        return (
            (_0x13d151 = _0x13d151['clamp'](0x1, VisuMZ[_0x43dbbc(0x201)][_0x43dbbc(0x424)])),
            _0x13d151
        );
    }),
    (VisuMZ['DiceRollsRngSeeds'][_0x457895(0x156)] = function () {
        const _0xd16d6c = _0x457895;
        if (!SceneManager[_0xd16d6c(0x212)]()) return 0x0;
        const _0x25d76e = SceneManager['_diceRollSettings'] || {};
        let _0x535cac = _0x25d76e[_0xd16d6c(0x32f)];
        return (
            (_0x535cac += _0x25d76e['rankAdjust'] || 0x0),
            (_0x535cac = _0x535cac[_0xd16d6c(0x2bd)](0x1, 0x6)),
            _0x535cac
        );
    }),
    (VisuMZ[_0x457895(0x201)][_0x457895(0x302)] = function () {
        const _0x50b928 = _0x457895,
            _0x52c9e2 = this[_0x50b928(0x156)]();
        return [0x0, 0x4, 0x6, 0x8, 0xa, 0xc, 0x14][_0x52c9e2];
    }));
var $rngSeed = function (_0xdb3d5e, _0x160291, _0x580b8c) {
        const _0x35409f = _0x457895;
        if (_0xdb3d5e === undefined) return Math[_0x35409f(0x3d9)]();
        return $gameSystem[_0x35409f(0x31c)](_0xdb3d5e, _0x160291, _0x580b8c);
    },
    $resetRngSeed = function (_0x1a15c9, _0x1799f8, _0x3a7bbf) {
        const _0x4e9cb6 = _0x457895;
        $gameSystem[_0x4e9cb6(0x303)](_0x1a15c9, _0x1799f8, _0x3a7bbf);
    },
    $dailyRngSeed = function (_0x1871a7) {
        return $rngSeed(_0x1871a7, !![], ![]);
    },
    $resetDailyRngSeed = function (_0x6d0cb) {
        $resetRngSeed(_0x6d0cb, !![], ![]);
    },
    $uniqueRngSeed = function (_0x27a209) {
        return $rngSeed(_0x27a209, ![], !![]);
    },
    $resetUniqueRngSeed = function (_0x5a0fa3) {
        $resetRngSeed(_0x5a0fa3, ![], !![]);
    },
    $dailyUniqueRngSeed = function (_0x5cbeac) {
        return $rngSeed(_0x5cbeac, !![], !![]);
    },
    $resetDailyUniqueRngSeed = function (_0x1e9c50) {
        $resetRngSeed(_0x1e9c50, !![], !![]);
    },
    $addDiceCount = function (_0x17683d) {
        const _0x252aa6 = _0x457895,
            _0x552ffe = SceneManager[_0x252aa6(0x15c)] || {};
        if (_0x552ffe[_0x252aa6(0x23b)] === 'rollValue') {
            if (_0x252aa6(0x38c) === _0x252aa6(0x38c))
                ((_0x552ffe[_0x252aa6(0x373)] = _0x552ffe['advantage'] || 0x0),
                    (_0x552ffe[_0x252aa6(0x373)] += _0x17683d),
                    (_0x552ffe[_0x252aa6(0x373)] = Math[_0x252aa6(0x23d)](
                        _0x552ffe[_0x252aa6(0x373)]
                    )));
            else {
                const _0x4292bf = _0x56ba8b['_rngSeed'] || '',
                    _0x5075b6 = _0x3e4926[_0x252aa6(0x2bc)] || ![],
                    _0x32f45c = _0x24b591[_0x252aa6(0x421)] || ![];
                return _0x3ba860(_0x4292bf, _0x5075b6, _0x32f45c);
            }
        }
    },
    $addDiceAdvantage = function (_0x1f2135) {
        const _0x47af8b = _0x457895,
            _0x1e2ae0 = SceneManager[_0x47af8b(0x15c)] || {};
        _0x1e2ae0[_0x47af8b(0x23b)] === 'targetValue' &&
            (_0x47af8b(0x2e0) !== 'FXSpB'
                ? ((_0x1e2ae0[_0x47af8b(0x373)] = _0x1e2ae0[_0x47af8b(0x373)] || 0x0),
                  (_0x1e2ae0['advantage'] += _0x1f2135),
                  (_0x1e2ae0[_0x47af8b(0x373)] = Math['round'](_0x1e2ae0[_0x47af8b(0x373)])))
                : (_0x3ab890 =
                      _0x2fb79c[_0x2fe57d[_0x47af8b(0x1e7)] || _0x2d1ada[_0x47af8b(0x369)]]));
    },
    $addDiceDisadvantage = function (_0xf80e07) {
        $addDiceAdvantage(-_0xf80e07);
    },
    $addDiceRank = function (_0x44b7bd) {
        const _0x47ab9a = _0x457895,
            _0x3fc9b3 = SceneManager[_0x47ab9a(0x15c)] || {};
        ((_0x3fc9b3[_0x47ab9a(0x16a)] = _0x3fc9b3[_0x47ab9a(0x16a)] || 0x1),
            (_0x3fc9b3[_0x47ab9a(0x16a)] += _0x44b7bd),
            (_0x3fc9b3[_0x47ab9a(0x16a)] = Math[_0x47ab9a(0x23d)](_0x3fc9b3['rankAdjust'])));
    },
    $addDiceModifier = function (_0x592959) {
        const _0x4eafb3 = _0x457895,
            _0x467517 = SceneManager[_0x4eafb3(0x15c)] || {};
        ((_0x467517['modifiers'] = _0x467517[_0x4eafb3(0x3cb)] || 0x0),
            (_0x467517['modifiers'] += _0x592959),
            (_0x467517[_0x4eafb3(0x3cb)] = Math['round'](_0x467517[_0x4eafb3(0x3cb)])));
    },
    $addDiceModRand = function (_0xa03227) {
        const _0x3488be = _0x457895,
            _0x50bfad = SceneManager[_0x3488be(0x15c)] || {};
        ((_0x50bfad['modRand'] = _0x50bfad['modRand'] || 0x0),
            (_0x50bfad[_0x3488be(0x173)] += _0xa03227),
            (_0x50bfad[_0x3488be(0x173)] = Math[_0x3488be(0x23d)](_0x50bfad['modRand'])));
    };
