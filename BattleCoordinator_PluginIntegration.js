/*:
 * @target MZ
 * @plugindesc v1.0.0 Battle Coordinator Plugin Integration
 * <AUTHOR> Optimizer
 * @orderAfter BattleCoordinator
 * @orderAfter ComboSystem
 * @orderAfter StaggerGauge2
 * @orderAfter MagicDamageReduction
 * @orderAfter DisplayTotalDamage2
 * @help BattleCoordinator_PluginIntegration.js
 *
 * ============================================================================
 * Battle Coordinator Plugin Integration
 * ============================================================================
 *
 * This plugin modifies existing battle plugins to work with the Battle
 * Coordinator system, eliminating conflicts and optimizing performance.
 *
 * It patches:
 * - ComboSystem.js
 * - StaggerGauge2.js  
 * - MagicDamageReduction.js
 * - DisplayTotalDamage2.js
 *
 * ============================================================================
 * Installation:
 * ============================================================================
 * 
 * 1. Place BattleCoordinator.js BEFORE your battle plugins
 * 2. Place this file AFTER all battle plugins
 * 3. Existing plugins will automatically be coordinated
 *
 */

(() => {
    'use strict';

    // Wait for coordinator to be available
    if (!window.BattleCoordinator) {
        console.error('[BattleCoordinator_PluginIntegration] BattleCoordinator not found! Make sure BattleCoordinator.js loads first.');
        return;
    }

    const coordinator = window.BattleCoordinator;
    const EVENTS = window.BATTLE_EVENTS;

    //=============================================================================
    // ComboSystem Integration
    //=============================================================================

    if (typeof ComboManager !== 'undefined') {
        console.log('[BattleCoordinator] Integrating ComboSystem...');

        // Remove original Game_Action.apply hook
        if (Game_Action.prototype.processCombo) {
            delete Game_Action.prototype.processCombo;
            delete Game_Action.prototype.processBonusAction;
        }

        // Register with coordinator
        coordinator.addEventListener(EVENTS.ACTION_APPLICATION, (context) => {
            const { action, target } = context;

            // Only process combos for successful hits that deal actual damage
            if (target.isEnemy() && action.subject().isActor()) {
                const result = target.result();
                const damageDealt = result.hpDamage || 0;

                // Check if attack hit and dealt positive damage (not missed, evaded, or blocked)
                if (result.isHit() && !result.missed && !result.evaded && !result.blocked && damageDealt > 0) {
                    const scene = SceneManager._scene;
                    if (scene.comboManager && scene.comboDisplay) {
                        // Process combo through coordinator
                        scene.comboManager.updateCombo(damageDealt);
                        
                        const starCount = scene.comboManager.getStarCount();
                        const bonusChance = starCount * 0.05; // DEFAULT_CONFIG.bonusChancePerStar
                        
                        if (Math.random() < bonusChance) {
                            // Schedule bonus action through coordinator
                            coordinator.updateScheduler.scheduleUpdate(() => {
                                const halfDamage = Math.ceil(target.result().hpDamage / 2);
                                target.gainHp(-halfDamage);
                                target.startDamagePopup();
                                target.performDamage();
                                if (target.isDead()) target.performCollapse();
                            }, 10, 1); // High priority, immediate execution
                            
                            scene.comboDisplay.showBonusText();
                        }
                    }
                }
            }
        }, 100); // High priority

        // Register combo system with coordinator
        coordinator.registerPlugin('comboSystem', { 
            active: true,
            type: 'damage_processor'
        });
    }

    //=============================================================================
    // StaggerGauge2 Integration  
    //=============================================================================

    if (typeof Game_Enemy !== 'undefined' && Game_Enemy.prototype.initStaggerGauge) {
        console.log('[BattleCoordinator] Integrating StaggerGauge2...');

        // Register damage calculation hook
        coordinator.addEventListener(EVENTS.DAMAGE_CALCULATION, (context) => {
            const { action, target, finalValue } = context;
            
            // Apply stagger damage multiplier
            if (target.isEnemy() && target._isStaggered) {
                const cascadeMeter = SceneManager._scene && SceneManager._scene._cascadeMeter;
                target.updateStaggerMultiplier(cascadeMeter);
                context.finalValue = finalValue * target._staggerDamageMultiplier;
                context.modifications.push({
                    plugin: 'StaggerGauge2',
                    type: 'stagger_multiplier',
                    multiplier: target._staggerDamageMultiplier
                });
            }
            
            return context;
        }, 90); // High priority, before other damage modifiers

        // Register action application hook
        coordinator.addEventListener(EVENTS.ACTION_APPLICATION, (context) => {
            const { action, target } = context;

            // Only apply stagger if the attack hit and dealt actual damage
            if (target.isEnemy() && action.isDamage()) {
                const result = target.result();
                const actualDamage = result.hpDamage || 0;

                // Check if attack hit and dealt positive damage (not missed, evaded, or blocked)
                if (result.isHit() && !result.missed && !result.evaded && !result.blocked && actualDamage > 0) {
                    const staggerValue = Math.min(Math.floor(actualDamage * 0.02), 30);
                    const exploitWeakness = action.calcElementRate(target) > 1;

                    target.gainStagger(staggerValue, exploitWeakness);

                    // Handle sprite effects through coordinator
                    const sprite = BattleManager.getEnemySprite(target);
                    if (sprite && sprite._staggerGaugeSprite) {
                        coordinator.updateScheduler.scheduleUpdate(() => {
                            sprite._staggerGaugeSprite.scale.set(2.0);
                            sprite._staggerGaugeSprite.pulseScaling = true;

                            if (target._isStaggered) {
                                sprite._staggerGaugeSprite.hitWhileStaggered = true;
                                sprite._staggerGaugeSprite.hitWhileStaggeredDuration = 20;
                            } else {
                                sprite._staggerGaugeSprite.glowEffect = true;
                                sprite._staggerGaugeSprite.glowEffectDuration = 30;
                            }
                        }, 80, 1); // Medium priority
                    }
                }
            }
        }, 80);

        coordinator.registerPlugin('staggerGauge', { 
            active: true,
            type: 'damage_modifier'
        });
    }

    //=============================================================================
    // MagicDamageReduction Integration
    //=============================================================================

    if (typeof StatReactions !== 'undefined') {
        console.log('[BattleCoordinator] Integrating MagicDamageReduction...');

        // Register damage calculation hook (highest priority - modifies base damage)
        coordinator.addEventListener(EVENTS.DAMAGE_CALCULATION, (context) => {
            const { action, target, finalValue } = context;
            
            // Only process for actors taking positive damage
            if (target.isActor() && finalValue > 0) {
                let modifiedValue = finalValue;
                
                // Process LUK lucky damage first (affects all damage types)
                const lukResult = StatReactions.processLukLucky(target, modifiedValue);
                if (lukResult !== modifiedValue) {
                    context.modifications.push({
                        plugin: 'MagicDamageReduction',
                        type: 'luk_lucky',
                        originalValue: modifiedValue,
                        newValue: lukResult
                    });
                    modifiedValue = lukResult;
                }
                
                // Process DEF/MDF absorption based on damage type
                if (action.isPhysical()) {
                    const defResult = StatReactions.processDefAbsorption(target, modifiedValue);
                    if (defResult !== modifiedValue) {
                        context.modifications.push({
                            plugin: 'MagicDamageReduction',
                            type: 'def_absorption',
                            originalValue: modifiedValue,
                            newValue: defResult
                        });
                        modifiedValue = defResult;
                    }
                } else if (action.isMagical()) {
                    const mdfResult = StatReactions.processMdfAbsorption(target, modifiedValue);
                    if (mdfResult !== modifiedValue) {
                        context.modifications.push({
                            plugin: 'MagicDamageReduction',
                            type: 'mdf_absorption',
                            originalValue: modifiedValue,
                            newValue: mdfResult
                        });
                        modifiedValue = mdfResult;
                    }
                }
                
                context.finalValue = modifiedValue;
            }
            
            return context;
        }, 100); // Highest priority - modifies base damage

        // Register action application hook for effects
        coordinator.addEventListener(EVENTS.ACTION_APPLICATION, (context) => {
            const { target } = context;
            
            // Apply reaction effects
            StatReactions.applyReactionEffects(target);
            
            // Handle popup creation through coordinator
            if (target._statReactionType) {
                coordinator.updateScheduler.scheduleUpdate(() => {
                    // Use shared sprite pool for popups
                    const popup = coordinator.getSpriteFromPool('statReactionPopup', () => {
                        return new Sprite(); // Create new popup sprite
                    });
                    
                    // Configure and show popup
                    // (popup configuration code would go here)
                    
                }, 70, 1); // Medium priority
                
                target._statReactionType = null;
            }
        }, 70);

        coordinator.registerPlugin('magicDamageReduction', { 
            active: true,
            type: 'damage_modifier'
        });
    }

    //=============================================================================
    // DisplayTotalDamage2 Integration
    //=============================================================================

    if (typeof TotalDamageSprite !== 'undefined') {
        console.log('[BattleCoordinator] Integrating DisplayTotalDamage2...');

        // Register action application hook for damage display
        coordinator.addEventListener(EVENTS.ACTION_APPLICATION, (context) => {
            const { action, target } = context;
            const value = target.result().hpDamage;
            
            if (SceneManager._scene instanceof Scene_Battle && SceneManager._scene._totalDamageSprite) {
                const isHealing = value < 0;
                const exploitWeakness = action.isDrain() || action.item().damage.elementId < 0 
                    ? false 
                    : target.result().critical || action.calcElementRate(target) > 1;
                
                // Use coordinator's performance-aware scheduling
                coordinator.updateScheduler.scheduleUpdate(() => {
                    SceneManager._scene._totalDamageSprite.addDamage(value, isHealing, exploitWeakness);
                }, 60, coordinator.performanceMode === 'aggressive' ? 2 : 1); // Adaptive throttling
            }
        }, 60);

        coordinator.registerPlugin('displayTotalDamage', { 
            active: true,
            type: 'visual_effect'
        });
    }

    //=============================================================================
    // Performance Optimization Events
    //=============================================================================

    // Handle performance mode changes
    coordinator.addEventListener(EVENTS.PERFORMANCE_MODE_CHANGED, (data) => {
        const { newMode, currentFPS } = data;
        
        console.log(`[BattleCoordinator] Performance mode: ${newMode} (${currentFPS} FPS)`);
        
        // Adjust plugin behaviors based on performance
        if (newMode === 'aggressive') {
            // Reduce visual effect frequency
            // Increase update throttling
            // Disable non-essential animations
        } else if (newMode === 'normal') {
            // Restore full visual effects
            // Normal update frequency
        }
    });

    console.log('[BattleCoordinator] Plugin integration complete');
})();
