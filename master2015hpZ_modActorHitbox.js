//===============================================================================
// master2015hpZ_modActorHitbox.js
// by master2015hp
// 2022.06.03
//===============================================================================
/*:
 * @plugindesc allow you to modify an actor hitbox
 * <AUTHOR>
 *
 * @help
░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░
░▒░░▒▒▒░░░▒▒░░░░▒▒▒▒░░▒▒▒░░▒░░░░░▒▒░░░░
░▒░▒▒░░░░░▒░▒░░░▒░░▒▒░░░▒▒░▒░░░░░▒░▒░░░
░▒░▒░░░░░▒▒░▒░░░▒░░░▒░░░░▒░▒░░░░▒▒░▒░░░
░▒░▒▒░░░░▒░░░▒░░▒░░▒▒░░░░▒░▒░░░░▒░░░▒░░
░▒░░▒▒░░░▒▒▒▒▒░░▒▒▒▒░░▒▒▒▒░▒░░░░▒▒▒▒▒░░
░▒░░░░▒░▒▒░░░▒░░▒░░▒▒░░░░▒░▒░░░▒▒░░░▒░░
░▒░░░░▒░▒░░░░▒▒░▒░░▒▒░░░░▒░▒░░░▒░░░░▒▒░
░▒░▒▒▒░░▒░░░░░▒░▒▒▒▒░░▒▒▒░░▒▒▒░▒░░░░░▒░
░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░░

 * -------------------------------------------------------------------------------
 * ✧ REQUIRE
 * ===============================================================================
 *
 * ► Actor Notetag ◄
<uHitbox: width, height>
 * - modify the actor's hitbox. The base point (x/y) always remain intact though
 * 
 * -------------------------------------------------------------------------------
 * ✧ FEATURES
 * ===============================================================================
 * 
 * -------------------------------------------------------------------------------
 * ✧ TERMS OF USE
 * ===============================================================================
 * - You must buy a license before using this plugin for any commercial purposes
 * - License must be obtained BEFORE you start selling your game.
 * - NOTE: Games with micro-transactions and/or advertising incomes are considred
 *   commercial use of this plugin!
 * - Edits are allowed as long as "Terms of Use" is not changed in any way.
 *
 * DO NOT COPY, RESELL, REDISTRIBUTE, REPUBLISH OR CLAIM ANY PIECE OF
 * THIS SOFTWARE AS YOUR OWN!
 * Copyright (c) 2022, Isabella Ava
 * Contact me at gmail: master2015hp
 *
 * -------------------------------------------------------------------------------
 * Version History
 * ===============================================================================
 * 2022/06/03 v1.0.0 - Initial release
 *
 */
var master2015hp = master2015hp || {};
master2015hp.modAcHitbox = master2015hp.modAcHitbox || {};
master2015hp.modAcHitbox.b = [];
master2015hp.modAcHitbox.b[0] = DataManager.isDatabaseLoaded;

var ms2015hp_dtmng_isdatabaseloaded = DataManager.isDatabaseLoaded;
DataManager.isDatabaseLoaded = function () {
    if (!master2015hp.modAcHitbox.b[0].call(this)) return false;
    if (!this._loadedActorHitbox) {
        this.processActorHitbox($dataActors);
        this._loadedActorHitbox = true;
    }
    return true;
};

DataManager.processActorHitbox = function (data) {
    for (var i = 1; i < data.length; i++) {
        var obj = data[i];
        // if (obj.name == '') continue;
        obj._uHitbox = [];
        var notedata = obj.note.split(/[\r\n]+/);
        for (var j = 0; j < notedata.length; j++) {
            var line = notedata[j];
            //<uHitbox: width, height>
            if (line.match(/<uHitbox[ ]?:[ ]?(\w+)[ ]?,[ ]?(\w+)>/i)) {
                obj._uHitbox = [Number(RegExp.$1), Number(RegExp.$2)];
                break;
            }
        }
    }
};

Sprite_Actor.prototype.hitTest = function (x, y) {
    var b = this._battler;
    var w = this.width;
    var h = this.height;
    if (b && b.isActor()) {
        var ubox = b.actor()._uHitbox;
        if (ubox.length > 0) {
            w = ubox[0];
            h = ubox[1];
        }
    }
    const rect = new Rectangle(-this.anchor.x * w, -this.anchor.y * h, w, h);
    return rect.contains(x, y);
};
