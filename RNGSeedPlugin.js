/*:
 * @target MZ
 * @plugindesc Ensures consistent RNG results by saving and loading the RNG seed with the game data.
 * @help This plugin prevents save scumming by saving and loading the RNG seed.
 */

(() => {
    // Initialize the RNG seed if it doesn't exist
    if (!window._rngSeed) {
        window._rngSeed = Date.now();
    }

    // Save the RNG seed with the game data
    const originalMakeSaveContents = DataManager.makeSaveContents;
    DataManager.makeSaveContents = function () {
        const contents = originalMakeSaveContents.call(this);
        contents._rngSeed = window._rngSeed;
        return contents;
    };

    // Load the RNG seed with the game data
    const originalExtractSaveContents = DataManager.extractSaveContents;
    DataManager.extractSaveContents = function (contents) {
        originalExtractSaveContents.call(this, contents);
        window._rngSeed = contents._rngSeed || Date.now();
    };
})();
