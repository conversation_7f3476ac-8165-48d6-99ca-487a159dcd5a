/*:
 * @plugindesc Runs a Common Event immediately after loading a game
 * <AUTHOR> Name Here
 *
 * @help This plugin runs a specific common event immediately after a game load.
 *
 */

(function () {
    const _Scene_Load_onLoadSuccess = Scene_Load.prototype.onLoadSuccess;
    Scene_Load.prototype.onLoadSuccess = function () {
        _Scene_Load_onLoadSuccess.call(this);
        $gameTemp.reserveCommonEvent(242); // Replace 1 with your common event ID
    };
})();
