/*:
 * @plugindesc Enhanced overkill system with visual effects, chain bonuses, and more.
 * <AUTHOR> by <PERSON>
 *
 * @param Overkill Threshold
 * @desc Damage multiplier required for overkill (e.g., 1.5 = 150% of max HP)
 * @default 1.0
 *
 * @param Flash Color
 * @desc Screen flash color for overkill [R,G,B,A]
 * @default [255, 0, 0, 155]
 *
 * @param Flash Duration
 * @desc Duration of screen flash in frames
 * @default 30
 *
 * @param Text Size
 * @desc Size of OVERKILL text
 * @default 28
 *
 * @param Text Color
 * @desc Color of OVERKILL text (CSS format)
 * @default #ff0000
 *
 * @param Popup Duration
 * @desc How long the "OVERKILL!" text stays on screen, in frames.
 * @type number
 * @default 60
 *
 * @param Experience Bonus
 * @desc Base experience multiplier on overkill.
 * @default 1.5
 *
 * @param Gold Bonus
 * @desc Base gold multiplier on overkill.
 * @default 1.25
 *
 * @param Drop Bonus
 * @desc Base chance of extra drops on overkill (0.5 = 50%)
 * @default 0.5
 *
 * @param ---Sound---
 * @default
 *
 * @param Enable Sound
 * @desc Play a sound effect on overkill.
 * @type boolean
 * @default true
 *
 * @param SE Filename
 * @desc Sound effect to play on overkill
 * @default Explosion1
 * @require 1
 * @dir audio/se/
 * @type file
 *
 * @param SE Volume
 * @desc Volume of the sound effect
 * @default 90
 *
 * @param SE Pitch
 * @desc Pitch of the sound effect
 * @default 100
 *
 * @param ---Overkill Chains---
 * @default
 *
 * @param Enable Chains
 * @desc Enable overkill chains for escalating rewards.
 * @type boolean
 * @default true
 *
 * @param Max Chain Count
 * @desc The maximum number the overkill chain can reach.
 * @type number
 * @default 3
 *
 * @param Chain Text
 * @desc Text format for the overkill chain counter. %c will be replaced by the chain count.
 * @default OVERKILL x%c
 *
 * @param Chain Exp Bonus
 * @desc Additional EXP multiplier per kill in a chain. Stacks with the base bonus.
 * @default 0.25
 *
 * @param Chain Gold Bonus
 * @desc Additional Gold multiplier per kill in a chain. Stacks with the base bonus.
 * @default 0.25
 *
 * @param Chain Drop Bonus
 * @desc Additional Drop Rate bonus per kill in a chain. Stacks with the base bonus.
 * @default 0.1
 *
 * @help
 * Enhanced Overkill System
 * -----------------------
 * Adds an overkill system that triggers when dealing massive damage.
 *
 * Features:
 * - Overkill Chains: Chain consecutive overkills for escalating rewards.
 *   The chain only breaks when an enemy is killed without an overkill.
 * - Configurable damage threshold for overkill.
 * - Visual effects (screen flash, animated floating text).
 * - Sound effects.
 * - Bonus rewards (exp, gold, drops) that stack with chains.
 * - Fully customizable colors, bonuses, and effects.
 */

(function () {
    var parameters = PluginManager.parameters('Overkill');

    // Base Parameters
    var threshold = Number(parameters['Overkill Threshold'] || 1.0);
    var flashColor = JSON.parse(parameters['Flash Color'] || '[255, 0, 0, 155]');
    var flashDuration = Number(parameters['Flash Duration'] || 30);
    var textSize = Number(parameters['Text Size'] || 28);
    var textColor = String(parameters['Text Color'] || '#ff0000');
    var popupDuration = Number(parameters['Popup Duration'] || 60);
    var expBonus = Number(parameters['Experience Bonus'] || 1.5);
    var goldBonus = Number(parameters['Gold Bonus'] || 1.25);
    var dropBonus = Number(parameters['Drop Bonus'] || 0.5);

    // Sound Parameters
    var enableSound = (parameters['Enable Sound'] || 'true') === 'true';
    var seFilename = String(parameters['SE Filename'] || 'Explosion1');
    var seVolume = Number(parameters['SE Volume'] || 90);
    var sePitch = Number(parameters['SE Pitch'] || 100);

    // Chain Parameters
    var enableChains = (parameters['Enable Chains'] || 'true') === 'true';
    var maxChain = parseInt(parameters['Max Chain Count'] || 3);
    var chainText = String(parameters['Chain Text'] || 'OVERKILL x%c');
    var chainExpBonus = Number(parameters['Chain Exp Bonus'] || 0.25);
    var chainGoldBonus = Number(parameters['Chain Gold Bonus'] || 0.25);
    var chainDropBonus = Number(parameters['Chain Drop Bonus'] || 0.1);

    // -- Game_Troop --
    // Initialize Overkill Chain counter for the troop at the start of battle.
    var _Game_Troop_initialize = Game_Troop.prototype.initialize;
    Game_Troop.prototype.initialize = function () {
        _Game_Troop_initialize.call(this);
        this._overkillChain = 0;
    };

    // -- Game_Enemy --
    // Initialize overkill flags for each enemy.
    var _Game_Enemy_initialize = Game_Enemy.prototype.initialize;
    Game_Enemy.prototype.initialize = function (enemyId, x, y) {
        _Game_Enemy_initialize.call(this, enemyId, x, y);
        this._damageBuffer = 0;
        this._damageBufferTimer = null;
        this._overkill = false;
        this._overkillTriggered = false;
        this._displayChainCount = 0;
    };

    // Trigger overkill check when damage is dealt.
    var _Game_Enemy_gainHp = Game_Enemy.prototype.gainHp;
    Game_Enemy.prototype.gainHp = function (value) {
        if (value < 0) {
            this._damageBuffer += Math.abs(value);

            clearTimeout(this._damageBufferTimer);
            this._damageBufferTimer = setTimeout(
                enemy => {
                    enemy._damageBuffer = 0;
                },
                1000,
                this
            );
        }

        if (
            this._hp > 0 &&
            this._hp + value <= 0 &&
            this._damageBuffer >= this.mhp * threshold &&
            !this._overkillTriggered
        ) {
            this._overkill = true;
            this._overkillTriggered = true;
        }

        _Game_Enemy_gainHp.call(this, value);
    };

    // Handle the Overkill Chain logic when an enemy dies.
    var _Game_Enemy_performCollapse = Game_Enemy.prototype.performCollapse;
    Game_Enemy.prototype.performCollapse = function () {
        if (this.isAppeared() && enableChains) {
            if (this._overkillTriggered) {
                $gameTroop._overkillChain++;
                $gameTroop._overkillChain = Math.min($gameTroop._overkillChain, maxChain);
                this._displayChainCount = $gameTroop._overkillChain;
            } else {
                $gameTroop._overkillChain = 0;
            }
        }
        _Game_Enemy_performCollapse.call(this);
    };

    // Apply overkill bonus to EXP.
    var _Game_Enemy_exp = Game_Enemy.prototype.exp;
    Game_Enemy.prototype.exp = function () {
        var exp = _Game_Enemy_exp.call(this);
        if (this._overkillTriggered) {
            var bonus = expBonus;
            if (enableChains && $gameTroop._overkillChain > 1) {
                bonus += ($gameTroop._overkillChain - 1) * chainExpBonus;
            }
            exp = Math.floor(exp * bonus);
        }
        return exp;
    };

    // Apply overkill bonus to Gold.
    var _Game_Enemy_gold = Game_Enemy.prototype.gold;
    Game_Enemy.prototype.gold = function () {
        var gold = _Game_Enemy_gold.call(this);
        if (this._overkillTriggered) {
            var bonus = goldBonus;
            if (enableChains && $gameTroop._overkillChain > 1) {
                bonus += ($gameTroop._overkillChain - 1) * chainGoldBonus;
            }
            gold = Math.floor(gold * bonus);
        }
        return gold;
    };

    // Apply overkill bonus to drops.
    var _Game_Enemy_makeDropItems = Game_Enemy.prototype.makeDropItems;
    Game_Enemy.prototype.makeDropItems = function () {
        if (this._overkillTriggered) {
            const dropItems = [];
            for (const drop of this.enemy().dropItems) {
                if (drop.kind > 0) {
                    let rate = this.dropItemRate();
                    if (enableChains && $gameTroop._overkillChain > 1) {
                        rate += ($gameTroop._overkillChain - 1) * chainDropBonus;
                    }
                    if (Math.random() < rate || Math.random() < dropBonus) {
                        dropItems.push(this.itemObject(drop.kind, drop.dataId));
                    }
                }
            }
            return dropItems;
        } else {
            return _Game_Enemy_makeDropItems.call(this);
        }
    };

    // -- Sprite_Enemy --
    // Trigger the overkill visual effects.
    var _Sprite_Enemy_update = Sprite_Enemy.prototype.update;
    Sprite_Enemy.prototype.update = function () {
        _Sprite_Enemy_update.call(this);
        if (this._enemy._overkill) {
            this.showOverkill();
            this._enemy._overkill = false;

            if (enableSound) {
                AudioManager.playSe({ name: seFilename, volume: seVolume, pitch: sePitch });
            }
            $gameScreen.startFlash(flashColor, flashDuration);
        }
    };

    // Display the animated "OVERKILL" text.
    Sprite_Enemy.prototype.showOverkill = function () {
        let text = 'OVERKILL!';
        const chainCount = this._enemy._displayChainCount || 0;
        if (enableChains && chainCount > 1) {
            text = chainText.replace('%c', chainCount);
        }

        const bitmap = new Bitmap(200, 50);
        bitmap.fontFace = $gameSystem.mainFontFace();
        bitmap.fontSize = textSize;
        bitmap.textColor = textColor;
        bitmap.outlineColor = 'rgba(0, 0, 0, 0.8)';
        bitmap.outlineWidth = 4;
        bitmap.drawText(text, 0, 0, 200, 50, 'center');

        const sprite = new Sprite(bitmap);
        sprite.x = this.x;
        sprite.y = this.y - this.height / 2;
        sprite.anchor.x = 0.5;
        sprite.anchor.y = 1;

        this.parent.addChild(sprite);

        let duration = popupDuration;
        let y = sprite.y;

        function animate() {
            duration--;
            if (duration > 0) {
                sprite.y -= 1;
                requestAnimationFrame(animate);
            } else {
                sprite.parent.removeChild(sprite);
            }
        }
        animate();
    };
})();
