//=============================================================================
// VisuStella MZ - Event Chain Reactions
// VisuMZ_3_EventChainReact.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_EventChainReact = true;
var VisuMZ = VisuMZ || {};
VisuMZ.EventChainReact = VisuMZ.EventChainReact || {};
VisuMZ.EventChainReact.version = 1.05;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.05] [EventChainReact]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Event_Chain_Reactions_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_EventsMoveCore
 * @orderAfter VisuMZ_1_EventsMoveCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Making events react to anything but the player requires so much work in RPG
 * Maker MZ. This plugin will change that by adding in many different ways to
 * cause event changes to take place. This includes streamlined forms of push
 * and pull, catalysts that cause reactions, pressure plates and heavy objects,
 * chargers and conductors, a form of timed decay, and objects that submerge
 * when moved into the water.
 *
 * Features include all (but not limited to) the following:
 *
 * * Plugin Command added to let you push events with more efficiency and less
 *   headache, allowing them to only move and play sound effects when possible.
 * * The ability to pull objects, an absolute headache to event, is now also
 *   added through the form of a Plugin Command for ease of use.
 * * Events can react to calaytic events that spread energy outward. When
 *   reacting, the target event(s) can turn on/off switches and self switches.
 * * Plugin Commands added to help you create catalysts at specific coordinates
 *   so you can fire off your own chain reactions.
 * * Objects can be assigned as a heavy weight and placed on top of pressure
 *   plate events to turn on switches when there's a heavy object on top or
 *   turn off switches when there isn't one.
 * * Events can produce charges and others can conduct them. When events have a
 *   conduction current running through them, they can become powered up and
 *   turn on switches. When they don't, those switches are turned off.
 * * Events can decay over time. When the decay timer runs out, switches can be
 *   turned on or off.
 * * Events can submerge into the water if pushed into it. When done, they can
 *   become bridges for the player or other events to walk over.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_1_EventsMoveCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Event Mechanics - Pushing and Pulling
 * ============================================================================
 *
 * While push and pull mechanics can be achieved through common event systems,
 * sometimes, hiccups can occur that makes life a little bit more difficult,
 * such as determining if an event has successfully moved in order to decide if
 * a sound effect should be played or not.
 *
 * This plugin streamlines the push and pull mechanics by incorporating them
 * into simplistic Plugin Commands for you to use as you wish.
 *
 * ---
 *
 * ==== Push ====
 *
 * Pushing an event requires the player to be located adjacent to the event
 * (meaning next to the event and not diagonally). The direction the player is
 * facing will determine the direction the event will go. If there is a viable
 * tile for the event to move, the event will be "pushed" in that direction.
 * Pushing can only travel in the down, left, right, up directions.
 *
 * Through the push-related Plugin Commands, you can decide if you want the
 * player to move forward alongside the event. If you do, both the player's and
 * the event's speeds will become synchronized to whoever has the lower move
 * speed. Otherwise, if the player does not move with the event, then the speed
 * at which the event is pushed will be its own.
 *
 * The player cannot push an event onto a ladder tile nor can the player push
 * while on a ladder tile.
 *
 * Pushing an event can have a sound effect. This sound effect will only play
 * if the event is pushed successfully.
 *
 * ---
 *
 * ==== Pull ====
 *
 * Pulling an event requires the player to be located adjacent to the event
 * (meaning next to the event and not diagonally). There must be an empty tile
 * behind the player for the player to back into. If there is a viable tile for
 * the player to move to, the player will move backwards into that tile while
 * "pulling" the event into the player's previous position. Pulling can only
 * travel in the down, left, right, up directions.
 *
 * Unlike the push-related Plugin Commands, the pull-related Plugin Commands do
 * not have an option to determine if the player can or cannot move. The player
 * character will always move backwards while pulling the event backwards. Both
 * the player's and event's move speeds will be synchronized to whoever has the
 * lower move speed.
 *
 * Pulling an event can have a sound effect. This sound effect will only play
 * if the event is pulled successfully.
 *
 * ---
 *
 * Pushing and pulling event objects do not inherently have chain reaction
 * effects. However, there's a lot of position-based chain reactions for events
 * meaning that being able to push or pull the events into position will cause
 * a potential chain reaction to occur.
 *
 * ---
 *
 * ============================================================================
 * Event Mechanics - Catalysts and Reactors
 * ============================================================================
 *
 * A new type of event interaction added through this plugin comes in the form
 * of catalysts and reactors. These are things like events that are sources of
 * fire that when put near flammable events will make those flammable events
 * burst aflame.
 *
 * ---
 *
 * ==== Catalysts ====
 *
 * A catalyst event is one that spreads a reactionary source like a "flame".
 * The flame's spread direction can be determined through notetags. If there
 * are any nearby events that would react to the catalyst, those events will
 * change switches and/or self switches as instructed, allowing a chain
 * reaction to occur.
 *
 * For example, a torch is considered a "flame" source and therefore serves as
 * a catalyst for the "flame" reactor type. If placed near flammable objects
 * like dried plants, those dried plants can react to it by burning up.
 *
 * A catalyst event will spread this reactionary source in timed intervals.
 * The reason why they're not immediate or instant is to prevent lag as well as
 * some catalysts work slower than others, even in real life scenarios. This is
 * to mimic the nature of how catalysts work in ways that are familiar to
 * players experience the chain reactions.
 *
 * Catalysts don't have to come from events. They can be manually started with
 * Plugin Commands at specific coordinates.
 *
 * ---
 *
 * ==== Reactors ====
 *
 * A reactor event is one that, upon exposure to a specific type of catalyst,
 * like a "flame", will react a certain way, by either turning ON or OFF a
 * switch, multiple switches, or self switches.
 *
 * For example, dried plants will react to a "flame" type catalyst. When they
 * are near a "flame" source like a torch, the dried plant event will turn on
 * a self switch to change it into a burning flame sprite, indicating that it's
 * been set on fire.
 *
 * Reactor events can also trigger from non-event catalysts through Plugin
 * Commands, assuming that the Plugin Command catalyst aims at the reactor
 * event with a matching catalyst type.
 *
 * ---
 *
 * ==== Types ====
 *
 * An event chain reaction is based off a "type". The catalyst will have a type
 * that it spreads while the target event will have a type that it reacts to.
 * These catalyst types can be named to your needs as the game developer.
 *
 * Catalyst events can generate multiple types and reactor events can react to
 * multiple types as well. Some events can spread one type as a catalyst while
 * reacting to another type. Mix and match them as needed.
 *
 * ---
 *
 * Knowing how to utilize catalysts and reactors will allow you to create a
 * very interactable environment in your game. Dried plants can be set aflame
 * and spread to other nearby dried plants. When on fire, they can be doused
 * with water creating puddles. The puddles can conduct electricity and more.
 *
 * ---
 *
 * ============================================================================
 * Event Mechanics - Pressure Plates and Heavy Objects
 * ============================================================================
 *
 * A common mechanic you see in RPG puzzles is the pressure plate. When a heavy
 * object is on top of it, something will turn on or open. When there is no
 * heavy object on top, something will turn off or close. The pressure plate
 * chain reaction works similarly here.
 *
 * ---
 *
 * ==== Pressure Plates ====
 *
 * Pressure plate events are events that will trigger a switch or self switch
 * to the ON position if there is a heavy object located on top of it. If there
 * isn't, then the linked switch or self switches will be set to OFF.
 *
 * Once an event is declared a pressure plate, it will automatically change its
 * priority type to "Below characters", allowing the player and other events
 * to travel on top of it.
 *
 * A common example of a pressure plate event used would be a button on the
 * floor linked to a normally closed gate. Both the pressure plate button and
 * the gate are linked to a switch. If that switch is ON, then the gate opens
 * allowing the player to pass through it. A common solution to this puzzle
 * would be that the player needs to find a heavy object to hold down the
 * pressure plate and pass through the gate.
 *
 * ---
 *
 * ==== Heavy Objects ====
 *
 * Events marked as heavy objects are capable of activating pressure plates on
 * the map. They just simply have to share the same coordinates as the pressure
 * plates they're on top of to activate them.
 *
 * When events are marked as heavy objects, their priority level becomes
 * "Same as characters" in order to be able to register the connection between
 * heavy objects and pressure plates.
 *
 * The Plugin Parameters allow you to determine if the player and the player's
 * followers are considered heavy objects. This means that the player is
 * capable of activating pressure plates if he/she is considered heavy. However
 * this can ruin certain types of games so there is an option to turn that off.
 *
 * The Plugin Parameters also allow you to set events with "Same as Characters"
 * priority level to automatically be heavy objects. This allows wandering
 * NPC's to set on pressure plates and activate them as well. Events that are
 * "Below characters" or "Above characters" will be exempt from this automatic
 * assignment by default.
 *
 * ---
 *
 * ==== Switches and Self Switches ====
 *
 * Unlike catalysts and reactors, decay, and submerging, the switches and self
 * switches here will only turn ON and OFF under specific conditions. If a
 * heavy object is on top of the pressure plate, the assigned switch(es) will
 * turn ON. If there is no heavy object on top of the pressure plate, then the
 * assigned switch(es) will turn OFF.
 *
 * ---
 *
 * Heavy objects are the key to making pressure plates work. Therefore, it's
 * important to be able to move around the heavy objects, too. Pushing and
 * pulling can make this a reality. By assigning the ability to push or pull
 * the heavy objects, pressure plate utility becomes very accessible.
 *
 * ---
 *
 * ============================================================================
 * Event Mechanics - Chargers and Conductors
 * ============================================================================
 *
 * Conduction is a new event mechanic added through this plugin. Some events
 * can produce a type of current while another event conducts it. Conducting
 * events will have a switch, multiple switches, and/or self switches that turn
 * ON or OFF depending on the events' conduction state.
 *
 * ---
 *
 * ==== Chargers ====
 *
 * Charger events can emit a "current" of a specific type like "electricity".
 * This electricity is emitted in the specified direction as a current. Any
 * nearby events that conduct this type of current will have their linked
 * switch(es) turned ON or OFF.
 *
 * An event charger is always emitting the current instantaneously. Therefore,
 * anything that conducts the matching current type will also be set to the
 * appropriate conduction state a frame or two later and remain that way.
 *
 * Event chargers do not interact with reactors or catalysts inherently (though
 * you can make them do so with catalyst and/or reactor notetags).
 *
 * ---
 *
 * ==== Conductors ====
 *
 * Conductor events can receive a current and change its conduction state. They
 * can take something like "electricity", conduct it, and pass it to other
 * conductor events. Conductors will only conduct the specified current "type"
 * marked by its notetag(s) and/or comment tag(s).
 *
 * Once conducted, any linked switch(es) or self switch(es) to the conductor
 * event will turn ON. If there is no matching current running through it,
 * those linked switch(es) will turn OFF.
 *
 * Event chargers do not interact with reactors or catalysts inherently (though
 * you can make them do so with catalyst and/or reactor notetags).
 *
 * ---
 *
 * ==== Direction ====
 *
 * Both chargers and conductors have a direction in which the current travels.
 * By default, if no notetags or comment tags are used to control this, then
 * the current will emit to adjacent conductor events. Otherwise, you can
 * control the flow of a current to go left and right, up and down, etc.
 *
 * The same notetag and comment tag is used for both chargers and conductors.
 *
 * ---
 *
 * ==== Types ====
 *
 * Event chargers and conductors are based off a "type" of current. Conductors
 * can only receive and pass on matching current types from chargers and/or
 * other conductors. These current types can be named to your needs as the
 * game developer.
 *
 * Charger events can generate multiple types of currents and the conductor
 * events can conduct to multiple types as well. Some events can charge on type
 * of current while conducting another. Mix and match them as needed.
 *
 * ---
 *
 * ==== Switches and Self Switches ====
 *
 * Unlike catalysts and reactors, decay, and submerging, the switches and self
 * switches here will only turn ON and OFF under specific conditions. If a
 * conductor has a current running through it, the assigned switch(es) will
 * turn ON. If there is no current going through the conductor, then the
 * assigned switch(es) will turn OFF.
 *
 * ---
 *
 * Chargers and conductors allow you to create environmental interactions that
 * differ from catalysts and reactions. Where catalysts and reactions typically
 * have a one directional change, the chargers and conductors utilize the
 * current and conduction system to produce a toggleable change capable of
 * shifting back and forth.
 *
 * This can be used for a system where the player can choose which door to
 * temporarily open or close depending on where the current goes based off the
 * conductors on the map.
 *
 * ---
 *
 * ============================================================================
 * Event Mechanics - Decay
 * ============================================================================
 *
 * Not all events need to interact with one another to produce a reaction. An
 * event with decay mechanic will simply react to itself existing for a set
 * amount of time on the map.
 *
 * ---
 *
 * ==== Decay Timing ====
 *
 * A decaying event can be set to decay in a preset amount of frames or a
 * custom amount of frames (your choice as the developer). Once the decay timer
 * is set, it automatically starts counting down. Once it reaches 0, then any
 * linked switch(es) or self switch(es) will turn ON or OFF.
 *
 * The event does not necessarily have to remove itself once the decay timer
 * has reached 0. It can if it wants to, but all the same, the event can remain
 * in its current state if it doesn't.
 *
 * If the event page changes while a decay countdown is happening, the
 * countdown timer will reset to whatever is the newest countdown timer on the
 * event page's comments. If an event notetag is used to encompass the global
 * countdown timer, then it will reset to that timer each time the page changes
 * forward.
 *
 * ---
 *
 * This can be used for a number of things, ranging from bombs to ever so
 * slightly transforming events. It removes the need to utilize a parallel
 * process event to make a change.
 *
 * ---
 *
 * ============================================================================
 * Event Mechanics - Submerge
 * ============================================================================
 *
 * Events can now interact with water tiles by submerging into them. If an
 * event is submerged, any linked switch(es) and/or self switch(es) will turn
 * ON or OFF. These can be used to form temporary bridges and the like.
 *
 * ---
 *
 * ==== Submersive ====
 *
 * When an object is submersive, it can be moved into a water tile. Immediate
 * contact with the water tile will cause it to submerge and linked switch(es)
 * and/or self switch(es) will be turned ON or OFF depending on the notetags
 * and/or comment tags used.
 *
 * Once an event is moved into the water and submerges, it can no longer be
 * pushed or pulled.
 *
 * ---
 *
 * ==== Bridge ====
 *
 * A common usage for submersive events is to change them into a temporary
 * bridge that lets the player and other events to travel over what was
 * otherwise intraversable waters. Though this normally could be done with
 * events that use graphics from the tileset with passabilities, becoming a
 * bridge was not available to events that used graphics from other sources.
 *
 * The notetag/comment tag allows the event to become a bridge and allowing it
 * to become passable no matter from which direction. This ignores the tileset
 * graphic source requirement and binds it to a tag instead.
 *
 * ---
 *
 * This feature is best used with the push/pull mechanics. Submersive objects
 * forming temporary land bridges allows for a more interactable environment.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * Due to the nature of this plugin, the VisuStella MZ Core Engine's plugin
 * parameter "Smart Event Collision" will be automatically turned on in order
 * for the features provided by this plugin to properly work.
 *
 * ---
 *
 * ============================================================================
 * Warning! RPG Maker MZ Version 1.5.0+ Water-Tile Bug!
 * ============================================================================
 *
 * It seems like there's a new bug that occurs if you create a tileset from
 * scratch in RPG Maker MZ version 1.5.0+ and version 1.6.0+! What this bug
 * does is it causes many tiles to become water tiles without intending to.
 * You can find this out by turning off all the plugins in your project,
 * putting a Ship or Boat on what are normally ground tiles, and then seeing
 * the Ship or Boat traverse through it.
 *
 * Naturally, this causes problems with the Event Chain Reactions plugin as the
 * water tiles are important for submerging reactions.
 *
 * There are two ways to fix this. We cannot fix it through code in this plugin
 * as it's a problem that involves the tileset json data there are ways to work
 * around it so that you can get the proper water-flags to go where they need
 * to be at.
 *
 * ---
 *
 * 1. Copy a working un-bugged tileset onto the currently bugged one and
 *    reapply the tile features like passability, terrain tags, etc. This will
 *    make sure the water-passability tiles get copied over correctly.
 *
 * 2. If you're on RPG Maker MZ version 1.5.0 or above, select a working
 *    un-bugged tileset (usually a pre-existing tileset when a new project is
 *    made), click the "Copy Page" button, go to the bugged tileset and press
 *    "Paste Page". You'll have to reapply any different properties like
 *    passabilities and terrain tags, but the water tile flags should now be
 *    working properly.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Positioning-Related Notetags ===
 *
 * ---
 *
 * <Push>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Allows this event to be pushed when using the Plugin Command:
 *   "Positioning: Push Player Front".
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Pull>
 *
 * - Used for: Actor, Class, Skill, Item, Weapon, Armor, Enemy, State Notetags
 * - Allows this event to be pushed when using the Plugin Command:
 *   "Positioning: Pull Player Front".
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Push Pull>
 * <Pull Push>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Allows this event to be pushed when using the Plugin Commands:
 *   "Positioning: Push Player Front" and "Positioning: Pull Player Front".
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Catalyst and Reactor-Related Notetags ===
 *
 * ---
 *
 * <type speed Catalyst: range>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes this event into a 'type' catalyst source that spreads at 'speed'
 *   intervals onto events within 'range'.
 * - Replace 'type' with a string representing the reaction type to be spread
 *   to other event reactors of the same 'type'.
 *   - This is NOT case sensitive.
 * - Replace 'speed' with any of the following text to represent the repeating
 *   interval at which the catalyst event spreads:
 *   - 'tick', 'fast', 'quick', 'short', 'average', 'slow', 'long', 'late'
 *   - Do NOT include the quotes.
 *   - Faster speeds can potentially cause lag, so use slower speeds to ensure
 *     FPS stability.
 * - Replace 'range' with any of the following to determine the range:
 *   - 'exact' - Coordinates must equal the catalyst event's X, Y position.
 *   - 'front' - Coordinates equal the tile in front of the catalyst.
 *   - 'back' - Coordinates equal the tile behind the catalyst.
 *   - 'cw' - Coordinates equal the tile clockwise from catalyst.
 *   - 'ccw' - Coordinates equal the tile counterclockwise from catalyst.
 *   - 'adjacent' - Any of the 4 tiles surrounding the catalyst.
 *   - 'near' - Any of the 8 tiles surrounding the catalyst.
 *   - 'down' - Coordinates equal the tile below the catalyst on the map.
 *   - 'left' - Coordinates equal the tile left of the catalyst on the map.
 *   - 'right' - Coordinates equal the tile right of the catalyst on the map.
 *   - 'up' - Coordinates equal the tile above the catalyst on the map.
 *   - 'lower left' - Coordinates equal the tile to catalyst's lower left.
 *   - 'lower right' - Coordinates equal the tile to catalyst's lower right.
 *   - 'upper left' - Coordinates equal the tile to catalyst's upper left.
 *   - 'upper right' - Coordinates equal the tile to catalyst's upper right.
 *   - Do NOT include the quotes.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <type x Frames Catalyst: range>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes this event into a 'type' catalyst source that spreads at 'x' frame
 *   intervals onto events within 'range'.
 * - Replace 'type' with a string representing the reaction type to be spread
 *   to other event reactors of the same 'type'.
 *   - This is NOT case sensitive.
 * - Replace 'x' with a number representing the number of frames that determine
 *   the ongoing intervals the catalysts cycle through.
 *   - Faster speeds can potentially cause lag, so use slower speeds to ensure
 *     FPS stability.
 * - Replace 'range' with any of the following to determine the range:
 *   - 'exact' - Coordinates must equal the catalyst event's X, Y position.
 *   - 'front' - Coordinates equal the tile in front of the catalyst.
 *   - 'back' - Coordinates equal the tile behind the catalyst.
 *   - 'cw' - Coordinates equal the tile clockwise from catalyst.
 *   - 'ccw' - Coordinates equal the tile counterclockwise from catalyst.
 *   - 'adjacent' - Any of the 4 tiles surrounding the catalyst.
 *   - 'near' - Any of the 8 tiles surrounding the catalyst.
 *   - 'down' - Coordinates equal the tile below the catalyst on the map.
 *   - 'left' - Coordinates equal the tile left of the catalyst on the map.
 *   - 'right' - Coordinates equal the tile right of the catalyst on the map.
 *   - 'up' - Coordinates equal the tile above the catalyst on the map.
 *   - 'lower left' - Coordinates equal the tile to catalyst's lower left.
 *   - 'lower right' - Coordinates equal the tile to catalyst's lower right.
 *   - 'upper left' - Coordinates equal the tile to catalyst's upper left.
 *   - 'upper right' - Coordinates equal the tile to catalyst's upper right.
 *   - Do NOT include the quotes.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <type Reactor Switch On: x>
 * <type Reactor Switches On: x, x, x>
 *
 * <type Reactor Switch Off: x>
 * <type Reactor Switches Off: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Makes this event into a 'type' reactor that upon a catalyst creating a
 *   reaction will turn any linked switch(es) or self switch(es) ON or OFF.
 *   - The ON variant will turn the linked switch ON when a reaction occurs.
 *   - The OFF variant will turn the linked switch OFF when a reaction occurs.
 * - Replace 'type' with a string representing the reaction type to be react
 *   to from catalysts of the same 'type'.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon a successful reaction.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Pressure Plate and Heavy Object-Related Notetags ===
 *
 * ---
 *
 * <Pressure Plate Switch: x>
 * <Pressure Plate Switches: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Turns this event into a pressure plate that upon having a heavy object on
 *   it will trigger any linked switch(es) and self switch(es) to turn ON and
 *   upon stepping off will also turn them OFF.
 * - Using this notetag will set the event's priority to "Below characters".
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon a successful activation.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Heavy>
 * <Heavy Object>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a heavy object that can stand on top of and activate
 *   pressure plate events.
 * - Using this notetag will set the event's priority to "Same as characters".
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Not Heavy>
 * <Not Heavy Object>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as an object not heavy enough to activate pressure
 *   plates events.
 * - This is used to offset the Plugin Parameter that sets "Same as characters"
 *   events as heavy objects.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <All Pressure Plates Switch: x>
 * <All Pressure Plates Switches: x, x, x>
 *
 * - Used for: Map Notetags
 * - If all native pressure plate events on the map has been stepped on, turn
 *   ON the switch(es). Otherwise, set the switch(es) to OFF.
 * - Erased and spawned pressure plate events do not count.
 * - Replace 'x' with a number to represent a numeric global switch.
 *   - If a numeric switch value happens to be a map switch or map self due to
 *     VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *
 * ---
 *
 * === Charger and Conductor-Related Notetags ===
 *
 * ---
 *
 * <type Charger>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a charger event that produces a 'type' current that
 *   can be received by same 'type' conductors.
 * - Replace 'type' with a string representing the current type released from
 *   this charger event to other conductor events of the same 'type'.
 *   - This is NOT case sensitive.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <type Conductor Switch: x>
 * <type Conductor Switches: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a conductor event that receives a 'type' current and
 *   will turn on any linked switch(es) or self switch(es) based on its current
 *   conduction state.
 * - Conductor events will also transfer its current to other conductors of the
 *   same 'type'.
 * - Replace 'type' with a string representing the current type released from
 *   this charger event to other conductor events of the same 'type'.
 *   - This is NOT case sensitive.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to apply multiple switches or self switches
 *     based on the conduction state.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Current Direction: range>
 * <Charge Direction: range>
 * <Conduct Direction: range>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Used for charger events and conductor events to determine which direction
 *   the current will travel.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'range' with any of the following to determine the range:
 *   - 'exact' - Coordinates must equal the catalyst event's X, Y position.
 *   - 'front' - Coordinates equal the tile in front of the catalyst.
 *   - 'back' - Coordinates equal the tile behind the catalyst.
 *   - 'cw' - Coordinates equal the tile clockwise from catalyst.
 *   - 'ccw' - Coordinates equal the tile counterclockwise from catalyst.
 *   - 'adjacent' - Any of the 4 tiles surrounding the catalyst.
 *   - 'near' - Any of the 8 tiles surrounding the catalyst.
 *   - 'down' - Coordinates equal the tile below the catalyst on the map.
 *   - 'left' - Coordinates equal the tile left of the catalyst on the map.
 *   - 'right' - Coordinates equal the tile right of the catalyst on the map.
 *   - 'up' - Coordinates equal the tile above the catalyst on the map.
 *   - 'lower left' - Coordinates equal the tile to catalyst's lower left.
 *   - 'lower right' - Coordinates equal the tile to catalyst's lower right.
 *   - 'upper left' - Coordinates equal the tile to catalyst's upper left.
 *   - 'upper right' - Coordinates equal the tile to catalyst's upper right.
 *   - Do NOT include the quotes.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Decay-Related Notetags ===
 *
 * ---
 *
 * <speed Decay Switch On: x>
 * <speed Decay Switches On: x, x, x>
 *
 * <speed Decay Switch Off: x>
 * <speed Decay Switches On: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as one that upon availability, will start decaying and
 *   start a countdown timer, where upon reaching 0, any linked switch(es) and
 *   self switch(es) will have their values changed.
 *   - The ON variant will turn the linked switch ON when the decay occurs.
 *   - The OFF variant will turn the linked switch OFF when the decay occurs.
 * - Replace 'speed' with any of the following text to represent the duration
 *   time of the decay countdown:
 *   - 'tick', 'fast', 'quick', 'short', 'average', 'slow', 'long', 'late'
 *   - Do NOT include the quotes.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon the decay countdown reaching 0.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <x Frames Decay Switch On: id>
 * <x Frames Decay Switches On: id, id, id>
 *
 * <x Frames Decay Switch Off: id>
 * <x Frames Decay Switches Off: id, id, id>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as one that upon availability, will start decaying and
 *   after x frames, any linked switch(es) and self switch(es) will have their
 *   values changed.
 *   - The ON variant will turn the linked switch ON when the decay occurs.
 *   - The OFF variant will turn the linked switch OFF when the decay occurs.
 * - Replace 'x' with a number representing the number of frames that determine
 *   the total frames before the decay occurs.
 *   - Faster speeds can potentially cause lag, so use slower speeds to ensure
 *     FPS stability.
 * - Replace 'id' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'id' values to change multiple switches or self switches
 *     upon the decay countdown reaching 0.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Submerge-Related Notetags ===
 *
 * ---
 *
 * <Submerge Switch On: x>
 * <Submerge Switches On: x, x, x>
 *
 * <Submerge Switch Off: x>
 * <Submerge Switches Off: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a submersive event, which upon being on top of a water
 *   tile, will instantly alter any linked switch(es) and self switch(es).
 *   - The ON variant will turn the linked switch ON when the decay occurs.
 *   - The OFF variant will turn the linked switch OFF when the decay occurs.
 * - This notetag variant will treat both shallow water and deep water tiles
 *   the same as one another.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon being submerged into water.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Shallow Submerge Switch On: x>
 * <Shallow Submerge Switches On: x, x, x>
 *
 * <Shallow Submerge Switch Off: x>
 * <Shallow Submerge Switches Off: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a submersive event, which upon being on top of a water
 *   tile, will instantly alter any linked switch(es) and self switch(es).
 *   - The ON variant will turn the linked switch ON when the decay occurs.
 *   - The OFF variant will turn the linked switch OFF when the decay occurs.
 * - This notetag variant will only trigger on shallow water tiles and not
 *   deep water tiles.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon being submerged into shallow water.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Deep Submerge Switch On: x>
 * <Deep Submerge Switches On: x, x, x>
 *
 * <Deep Submerge Switch Off: x>
 * <Deep Submerge Switches Off: x, x, x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks this event as a submersive event, which upon being on top of a water
 *   tile, will instantly alter any linked switch(es) and self switch(es).
 *   - The ON variant will turn the linked switch ON when the decay occurs.
 *   - The OFF variant will turn the linked switch OFF when the decay occurs.
 * - This notetag variant will only trigger on deep water tiles and not
 *   shallow water tiles.
 * - Replace 'x' with either a number to represent a numeric global switch or
 *   a letter to represent a native RPG Maker MZ self switch.
 *   - If a numeric switch value happens to be a self switch or map switch due
 *     to VisuMZ_1_EventsMoveCore, then it will be treated as such.
 *   - Insert multiple 'x' values to change multiple switches or self switches
 *     upon being submerged into deep water.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Submerged Effect>
 *
 * <Submerged Effect: x>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Adds a visual effect to the event, making it bob up and down like at the
 *   surface of the water.
 * - Use the <Submerged Effect: x> variant to visually submerge the event
 *   further, making it appear heavier and deeper in the water.
 * - Replace 'x' with a number representing how many pixels deep to submerge
 *   the event visually.
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * <Bridge>
 *
 * - Used for: Event Notetags and Event Page Comment Tags
 * - Marks the event as a bridge, allowing the player and other events to walk
 *   on top of it, even if it is over water.
 * - Using this notetag will set the event's priority to "Below characters".
 * - If this is placed inside a page's comment, the effect will only occur
 *   if that event page is currently active.
 *
 * ---
 *
 * === Useful-Related Notetags ===
 *
 * ---
 *
 * <Exit Reset Self Data>
 *
 * - Used for: Event Notetags ONLY
 * - When the player leaves the current map, all Self Switches and Self
 *   Variables related to this event will be reset.
 * - This notetag is a part of VisuMZ_1_EventsMoveCore but is recommended to be
 *   listed here with this plugin.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Catalyst Plugin Commands ===
 *
 * ---
 *
 * Catalyst: Create Catalyst at Event Location
 * - Creates a catalyst for a spreadable event chain reaction at a target
 *   event's relative coordinates.
 *
 *   Event ID:
 *   - The ID of the event to start catalyst from.
 *   - Use 0 for current event.
 *   - You may use JavaScript code.
 *
 *   Reaction Type:
 *   - What is the reaction type's string?
 *   - Case does not matter.
 *
 *   Relative Location:
 *   - What is the location of the reaction relative to the event?
 *
 * ---
 *
 * Catalyst: Create Catalyst at Player Location
 * - Creates a catalyst for a spreadable event chain reaction at the player's
 *   relative coordinates.
 *
 *   Reaction Type:
 *   - What is the reaction type's string?
 *   - Case does not matter.
 *
 *   Relative Location:
 *   - What is the location of the reaction relative to the player?
 *
 * ---
 *
 * Catalyst: Create Catalyst at X, Y
 * - Creates a catalyst for a spreadable event chain reaction at
 *   X, Y coordinates.
 *
 *   Reaction Type:
 *   - What is the reaction type's string?
 *   - Case does not matter.
 *
 *   X Coordinate:
 *   Y Coordinate:
 *   - Target X/Y coordinate to create catalyst at.
 *   - You may use JavaScript code.
 *
 * ---
 *
 * === Positioning Plugin Commands ===
 *
 * ---
 *
 * Positioning: Pull Player Front
 * - If an event in front the player can be pulled, do so.
 * - Event requires <Pull> or <Pull Push> notetag.
 *
 *   Check Touch Triggers?:
 *   - Check triggers after moving and pulling?
 *
 *   Sound Effect:
 *   - Play this sound effect if the event can be pulled.
 *
 * ---
 *
 * Positioning: Pull This Event
 * - Pulls this event backward by one tile if possible.
 * - Requires the player to align up adjacently.
 *
 *   Check Touch Triggers?:
 *   - Check triggers after moving and pulling?
 *
 *   Sound Effect:
 *   - Play this sound effect if the event can be pulled.
 *
 * ---
 *
 * Positioning: Push Player Front
 * - If an event in front the player can be pushed, do so.
 * - Event requires <Push> or <Push Pull> notetag.
 *
 *   Move Player Forward?:
 *   - Move player forward while pushing the event?
 *
 *     Check Touch Triggers?:
 *     - Check triggers after moving and pushing?
 *
 *   Sound Effect:
 *   - Play this sound effect if the event can be pushed.
 *
 * ---
 *
 * Positioning: Push This Event
 * - Pushes this event forward by one tile if possible.
 * - Requires the player to align up adjacently.
 *
 *   Move Player Forward?:
 *   - Move player forward while pushing the event?
 *
 *     Check Touch Triggers?:
 *     - Check triggers after moving and pushing?
 *
 *   Sound Effect:
 *   - Play this sound effect if the event can be pushed.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Pressure Plate Settings
 * ============================================================================
 *
 * Pressure Plate-related settings used by this plugin.
 *
 * ---
 *
 * Settings
 *
 *   Heavy Player?:
 *   - Make the player character a "heavy object" capable of activating
 *     pressure plates?
 *
 *   Heavy Followers?:
 *   - Make the player followers "heavy objects" capable of activating
 *     pressure plates?
 *
 *   Heavy "Same" Events?:
 *   - Make events with "Same as Characters" priority capable of
 *     activating pressure plates?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Speed Settings
 * ============================================================================
 *
 * Speed-related settings for specific notetags used by this plugin.
 *
 * ---
 *
 * Settings
 *
 *   Tick:
 *   Fast:
 *   Quick:
 *   Short:
 *   Average:
 *   Slow:
 *   Long:
 *   Late:
 *   - How many frames to pass when using this speed for an Event Chain
 *     Reaction notetag?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Submerge Settings
 * ============================================================================
 *
 * Settings related to the submerge function for this plugin.
 *
 * ---
 *
 * Sound Effect
 *
 *   Filename:
 *   - Filename of the sound effect played.
 *
 *   Volume:
 *   - Volume of the sound effect played.
 *
 *   Pitch:
 *   - Pitch of the sound effect played.
 *
 *   Pan:
 *   - Pan of the sound effect played.
 *
 * ---
 *
 * Visual
 *
 *   Submerge Speed:
 *   - What speed should objects submerge at?
 *   - Lower numbers are faster.
 *   - Higher numbers are slower.
 *
 *   Oscillation Distance:
 *   - What is the maximum oscillation distance?
 *   - Lower numbers are closer.
 *   - Higher numbers are further.
 *
 *   Submerge Rate:
 *   - What rate should objects oscillate at?
 *   - Lower numbers are slower.
 *   - Higher numbers are faster.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Irina
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.05: April 18, 2024
 * * Bug Fixes!
 * ** Fixed a bug where if player would still be considered a heavy object
 *    after pushing a heavy object off a pressure plate if Plugin Parameters
 *    have set the player as non-Heavy. Fix made by Arisu.
 *
 * Version 1.04: October 12, 2023
 * * Documentation Update!
 * ** It appears we've left out some notetags/comment tags that could be used:
 * *** <x Frames Decay Switch On: id>
 * *** <x Frames Decay Switches On: id, id, id>
 * *** <x Frames Decay Switch Off: id>
 * *** <x Frames Decay Switches Off: id, id, id>
 * ** These should now appear in the help file.
 *
 * Version 1.03: August 17, 2023
 * * Bug Fixes!
 * ** Fixed an error that would cause a crash upon using the "Return to Title
 *    Screen" event command with the "Event Title Screen" plugin installed. Fix
 *    made by Irina.
 *
 * Version 1.02: February 16, 2023
 * * Bug Fixes!
 * ** Fixed a bug that would cause certain notetags to not work properly when
 *    they have another notetag in front of them. Fix made by Arisu.
 *
 * Version 1.01: December 15, 2022
 * * Documentation Update!
 * ** Added new section: "Warning! RPG Maker MZ Version 1.5.0+ Water-Tile Bug!"
 * *** It seems like there's a new bug that occurs if you create a tileset from
 *     scratch in RPG Maker MZ version 1.5.0+ and version 1.6.0+! What this bug
 *     does is it causes many tiles to become water tiles without intending to.
 *     You can find this out by turning off all the plugins in your project,
 *     putting a Ship or Boat on what are normally ground tiles, and then
 *     seeing the Ship or Boat traverse through it.
 * *** Naturally, this causes problems with the Event Chain Reactions plugin as
 *     the water tiles are important for submerging reactions.
 * *** There are two ways to fix this. We cannot fix it through code in this
 *     plugin as it's a problem that involves the tileset json data there are
 *     ways to work around it so that you can get the proper water-flags to go
 *     where they need to be at.
 * **** 1. Copy a working un-bugged tileset onto the currently bugged one and
 *      reapply the tile features like passability, terrain tags, etc. This
 *      will make sure the water-passability tiles get copied over correctly.
 * **** 2. If you're on RPG Maker MZ version 1.5.0 or above, select a working
 *      un-bugged tileset (usually a pre-existing tileset when a new project is
 *      made), click the "Copy Page" button, go to the bugged tileset and press
 *      "Paste Page". You'll have to reapply any different properties like
 *      passabilities and terrain tags, but the water tile flags should now be
 *      working properly.
 *
 * Version 1.00 Official Release Date: January 4, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CatalystCreateAtEvent
 * @text Catalyst: Create Catalyst at Event Location
 * @desc Creates a catalyst for a spreadable event chain reaction
 * at a target event's relative coordinates.
 *
 * @arg EventID:eval
 * @text Event ID
 * @desc The ID of the event to start catalyst from.
 * Use 0 for current event. You may use JavaScript code.
 * @default 0
 *
 * @arg Type:str
 * @text Reaction Type
 * @desc What is the reaction type's string?
 * Case does not matter.
 * @default >>>ATTENTION<<<
 *
 * @arg Location:str
 * @text Relative Location
 * @type select
 * @option -
 * @option exact
 * @option -
 * @option front
 * @option back
 * @option cw
 * @option ccw
 * @option -
 * @option adjacent
 * @option nearby
 * @option -
 * @option down
 * @option left
 * @option right
 * @option up
 * @option -
 * @option lower left
 * @option lower right
 * @option upper left
 * @option upper right
 * @option -
 * @desc What is the location of the reaction relative to the event?
 * @default exact
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CatalystCreateAtPlayer
 * @text Catalyst: Create Catalyst at Player Location
 * @desc Creates a catalyst for a spreadable event chain reaction
 * at the player's relative coordinates.
 *
 * @arg Type:str
 * @text Reaction Type
 * @desc What is the reaction type's string?
 * Case does not matter.
 * @default >>>ATTENTION<<<
 *
 * @arg Location:str
 * @text Relative Location
 * @type select
 * @option -
 * @option exact
 * @option -
 * @option front
 * @option back
 * @option cw
 * @option ccw
 * @option -
 * @option adjacent
 * @option nearby
 * @option -
 * @option down
 * @option left
 * @option right
 * @option up
 * @option -
 * @option lower left
 * @option lower right
 * @option upper left
 * @option upper right
 * @option -
 * @desc What is the location of the reaction relative to the player?
 * @default front
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CatalystCreateAtXy
 * @text Catalyst: Create Catalyst at X, Y
 * @desc Creates a catalyst for a spreadable event chain reaction
 * at X, Y coordinates.
 *
 * @arg Type:str
 * @text Reaction Type
 * @desc What is the reaction type's string?
 * Case does not matter.
 * @default >>>ATTENTION<<<
 *
 * @arg PosX:eval
 * @text X Coordinate
 * @type combo
 * @option 0
 * @option $gamePlayer.frontX()
 * @option $gamePlayer.backX()
 * @option Math.randomInt($gameMap.width())
 * @desc Target X coordinate to create catalyst at.
 * You may use JavaScript code.
 * @default 0
 *
 * @arg PosY:eval
 * @text Y Coordinate
 * @type combo
 * @option 0
 * @option $gamePlayer.frontY()
 * @option $gamePlayer.backY()
 * @option Math.randomInt($gameMap.height())
 * @desc Target Y coordinate to create catalyst at.
 * You may use JavaScript code.
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Positioning
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PositioningPullPlayerFront
 * @text Positioning: Pull Player Front
 * @desc If an event in front the player can be pulled, do so.
 * Event requires <Pull> or <Pull Push> notetag.
 *
 * @arg CheckTriggers:eval
 * @text Check Touch Triggers?
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Check triggers after moving and pulling?
 * @default true
 *
 * @arg Sound:struct
 * @text Sound Effect
 * @type struct<Sound>
 * @desc Play this sound effect if the event can be pulled.
 * @default {"name:str":"Earth3","volume:num":"60","pitch:num":"150","pan:num":"0"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PositioningPullThisEvent
 * @text Positioning: Pull This Event
 * @desc Pulls this event backward by one tile if possible.
 * Requires the player to align up adjacently.
 *
 * @arg CheckTriggers:eval
 * @text Check Touch Triggers?
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Check triggers after moving and pulling?
 * @default true
 *
 * @arg Sound:struct
 * @text Sound Effect
 * @type struct<Sound>
 * @desc Play this sound effect if the event can be pulled.
 * @default {"name:str":"Earth3","volume:num":"60","pitch:num":"150","pan:num":"0"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PositioningPushPlayerFront
 * @text Positioning: Push Player Front
 * @desc If an event in front the player can be pushed, do so.
 * Event requires <Push> or <Push Pull> notetag.
 *
 * @arg MoveForward:eval
 * @text Move Player Forward?
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Move player forward while pushing the event?
 * @default true
 *
 * @arg CheckTriggers:eval
 * @text Check Touch Triggers?
 * @parent MoveForward:eval
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Check triggers after moving and pushing?
 * @default true
 *
 * @arg Sound:struct
 * @text Sound Effect
 * @type struct<Sound>
 * @desc Play this sound effect if the event can be pushed.
 * @default {"name:str":"Earth3","volume:num":"60","pitch:num":"150","pan:num":"0"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command PositioningPushThisEvent
 * @text Positioning: Push This Event
 * @desc Pushes this event forward by one tile if possible.
 * Requires the player to align up adjacently.
 *
 * @arg MoveForward:eval
 * @text Move Player Forward?
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Move player forward while pushing the event?
 * @default true
 *
 * @arg CheckTriggers:eval
 * @text Check Touch Triggers?
 * @parent MoveForward:eval
 * @type boolean
 * @on Move
 * @off Don't Move
 * @desc Check triggers after moving and pushing?
 * @default true
 *
 * @arg Sound:struct
 * @text Sound Effect
 * @type struct<Sound>
 * @desc Play this sound effect if the event can be pushed.
 * @default {"name:str":"Earth3","volume:num":"60","pitch:num":"150","pan:num":"0"}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param EventChainReact
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param PressurePlate:struct
 * @text Pressure Plate Settings
 * @type struct<PressurePlate>
 * @desc Pressure Plate-related settings used by this plugin.
 * @default {"HeavyPlayerCharacter:eval":"true","HeavyFollowers:eval":"true","HeavySameEvents:eval":"true"}
 *
 * @param Speed:struct
 * @text Speed Settings
 * @type struct<Speed>
 * @desc Speed-related settings for specific notetags used by this plugin.
 * @default {"tick:num":"1","fast:num":"8","quick:num":"15","short:num":"20","avg:num":"30","slow:num":"40","long:num":"60","late:num":"120"}
 *
 * @param Submerge:struct
 * @text Submerge Settings
 * @type struct<Submerge>
 * @desc Settings related to the submerge function for this plugin.
 * @default {"Sound":"","soundName:str":"Water1","soundVolume:num":"50","soundPitch:num":"60","soundPan:num":"0","Visual":"","updateFrequency:num":"4","oscillationDistance:num":"8","oscillationSpeed:num":"0.05"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * PressurePlate Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~PressurePlate:
 *
 * @param HeavyPlayerCharacter:eval
 * @text Heavy Player?
 * @type boolean
 * @on Heavy
 * @off Not Heavy
 * @desc Make the player character a "heavy object"
 * capable of activating pressure plates?
 * @default true
 *
 * @param HeavyFollowers:eval
 * @text Heavy Followers?
 * @type boolean
 * @on Heavy
 * @off Not Heavy
 * @desc Make the player followers "heavy objects"
 * capable of activating pressure plates?
 * @default true
 *
 * @param HeavySameEvents:eval
 * @text Heavy "Same" Events?
 * @type boolean
 * @on Heavy
 * @off Not Heavy
 * @desc Make events with "Same as Characters" priority
 * capable of activating pressure plates?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Speed Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Speed:
 *
 * @param tick:num
 * @text Tick
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 1
 *
 * @param fast:num
 * @text Fast
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 8
 *
 * @param quick:num
 * @text Quick
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 15
 *
 * @param short:num
 * @text Short
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 20
 *
 * @param avg:num
 * @text Average
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 30
 *
 * @param slow:num
 * @text Slow
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 40
 *
 * @param long:num
 * @text Long
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 60
 *
 * @param late:num
 * @text Late
 * @type number
 * @min 1
 * @desc How many frames to pass when using this speed for an
 * Event Chain Reaction notetag?
 * @default 120
 *
 */
/* ----------------------------------------------------------------------------
 * Submerge Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Submerge:
 *
 * @param Sound
 * @text Sound Effect
 *
 * @param soundName:str
 * @text Filename
 * @parent Sound
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Water1
 *
 * @param soundVolume:num
 * @text Volume
 * @parent Sound
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 50
 *
 * @param soundPitch:num
 * @text Pitch
 * @parent Sound
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 60
 *
 * @param soundPan:num
 * @text Pan
 * @parent Sound
 * @desc Pan of the sound effect played.
 * @default 0
 *
 * @param Visual
 *
 * @param updateFrequency:num
 * @text Submerge Speed
 * @parent Visual
 * @type number
 * @min 1
 * @desc What speed should objects submerge at?
 * Lower numbers are faster. Higher numbers are slower.
 * @default 4
 *
 * @param oscillationDistance:num
 * @text Oscillation Distance
 * @parent Visual
 * @type number
 * @min 1
 * @desc What is the maximum oscillation distance?
 * Lower numbers are closer. Higher numbers are further.
 * @default 8
 *
 * @param oscillationSpeed:num
 * @text Submerge Rate
 * @parent Visual
 * @desc What rate should objects oscillate at?
 * Lower numbers are slower. Higher numbers are faster.
 * @default 0.05
 *
 */
/* ----------------------------------------------------------------------------
 * Sound Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Sound:
 *
 * @param name:str
 * @text Filename
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Earth3
 *
 * @param volume:num
 * @text Volume
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param pitch:num
 * @text Pitch
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param pan:num
 * @text Pan
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
//=============================================================================
var tier = tier || 0x0;
var dependencies = ['VisuMZ_0_CoreEngine', 'VisuMZ_1_EventsMoveCore'];
var pluginData = $plugins.filter(function (_0x43f1b3) {
    return _0x43f1b3.status && _0x43f1b3.description.includes('[EventChainReact]');
})[0x0];
VisuMZ.EventChainReact.Settings = VisuMZ.EventChainReact.Settings || {};
VisuMZ.ConvertParams = function (_0x476170, _0x1df09f) {
    for (const _0x2d46ed in _0x1df09f) {
        if (_0x2d46ed.match(/(.*):(.*)/i)) {
            const _0x27b1e2 = String(RegExp.$1);
            const _0x3554d5 = String(RegExp.$2).toUpperCase().trim();
            let _0x33d673;
            let _0x378265;
            let _0x1987ea;
            switch (_0x3554d5) {
                case 'NUM':
                    _0x33d673 = _0x1df09f[_0x2d46ed] !== '' ? Number(_0x1df09f[_0x2d46ed]) : 0x0;
                    break;
                case 'ARRAYNUM':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x53b6ae => Number(_0x53b6ae));
                    break;
                case 'EVAL':
                    _0x33d673 = _0x1df09f[_0x2d46ed] !== '' ? eval(_0x1df09f[_0x2d46ed]) : null;
                    break;
                case 'ARRAYEVAL':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x3c0eb9 => eval(_0x3c0eb9));
                    break;
                case 'JSON':
                    _0x33d673 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : '';
                    break;
                case 'ARRAYJSON':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x2a03b3 => JSON.parse(_0x2a03b3));
                    break;
                case 'FUNC':
                    _0x33d673 =
                        _0x1df09f[_0x2d46ed] !== ''
                            ? new Function(JSON.parse(_0x1df09f[_0x2d46ed]))
                            : new Function('return 0');
                    break;
                case 'ARRAYFUNC':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x5517fc => new Function(JSON.parse(_0x5517fc)));
                    break;
                case 'STR':
                    _0x33d673 = _0x1df09f[_0x2d46ed] !== '' ? String(_0x1df09f[_0x2d46ed]) : '';
                    break;
                case 'ARRAYSTR':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x23e19d => String(_0x23e19d));
                    break;
                case 'STRUCT':
                    _0x1987ea = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : {};
                    _0x33d673 = VisuMZ.ConvertParams({}, _0x1987ea);
                    break;
                case 'ARRAYSTRUCT':
                    _0x378265 = _0x1df09f[_0x2d46ed] !== '' ? JSON.parse(_0x1df09f[_0x2d46ed]) : [];
                    _0x33d673 = _0x378265.map(_0x2119c6 =>
                        VisuMZ.ConvertParams({}, JSON.parse(_0x2119c6))
                    );
                    break;
                default:
                    continue;
            }
            _0x476170[_0x27b1e2] = _0x33d673;
        }
    }
    return _0x476170;
};
(_0x5d9801 => {
    const _0x4fbd29 = _0x5d9801.name;
    for (const _0x173c1d of dependencies) {
        if (!Imported[_0x173c1d]) {
            alert(
                '%1 is missing a required plugin.\nPlease install %2 into the Plugin Manager.'.format(
                    _0x4fbd29,
                    _0x173c1d
                )
            );
            SceneManager.exit();
            break;
        }
    }
    const _0x5e36b9 = _0x5d9801.description;
    if (_0x5e36b9.match(/\[Version[ ](.*?)\]/i)) {
        const _0x119d55 = Number(RegExp.$1);
        if (_0x119d55 !== VisuMZ.EventChainReact.version) {
            alert(
                "%1's version does not match plugin's. Please update it in the Plugin Manager.".format(
                    _0x4fbd29,
                    _0x119d55
                )
            );
            SceneManager.exit();
        }
    }
    if (_0x5e36b9.match(/\[Tier[ ](\d+)\]/i)) {
        const _0x4e7376 = Number(RegExp.$1);
        if (_0x4e7376 < tier) {
            alert(
                '%1 is incorrectly placed on the plugin list.\nIt is a Tier %2 plugin placed over other Tier %3 plugins.\nPlease reorder the plugin list from smallest to largest tier numbers.'.format(
                    _0x4fbd29,
                    _0x4e7376,
                    tier
                )
            );
            SceneManager.exit();
        } else {
            tier = Math.max(_0x4e7376, tier);
        }
    }
    VisuMZ.ConvertParams(VisuMZ.EventChainReact.Settings, _0x5d9801.parameters);
})(pluginData);
if (VisuMZ.EventsMoveCore.version < 1.42) {
    let text = '';
    text += 'VisuMZ_1_EventsMoveCore needs to be updated ';
    text += 'in order for VisuMZ_3_EventChainReact to work.';
    alert(text);
    SceneManager.exit();
}
PluginManager.registerCommand(pluginData.name, 'CatalystCreateAtEvent', _0x3039af => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    if (!$gameMap) {
        return;
    }
    VisuMZ.ConvertParams(_0x3039af, _0x3039af);
    const _0x10a94d = $gameTemp.getLastPluginCommandInterpreter();
    const _0x1c6441 = _0x3039af.EventID || _0x10a94d.eventId();
    const _0x12f642 = _0x3039af.Type || '';
    const _0x3186f9 = _0x3039af.Location || 'exact';
    const _0x17f03e = $gameMap.event(_0x1c6441);
    $gameMap.spreadChainReactionFromTarget(_0x17f03e, _0x12f642, _0x3186f9);
});
PluginManager.registerCommand(pluginData.name, 'CatalystCreateAtPlayer', _0x4c8a39 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    if (!$gameMap) {
        return;
    }
    VisuMZ.ConvertParams(_0x4c8a39, _0x4c8a39);
    const _0x88ade3 = _0x4c8a39.Type || '';
    const _0x5b8aee = _0x4c8a39.Location || 'exact';
    $gameMap.spreadChainReactionFromTarget($gamePlayer, _0x88ade3, _0x5b8aee);
});
PluginManager.registerCommand(pluginData.name, 'CatalystCreateAtXy', _0x58ac04 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    if (!$gameMap) {
        return;
    }
    VisuMZ.ConvertParams(_0x58ac04, _0x58ac04);
    const _0x4a7fd1 = _0x58ac04.Type || '';
    const _0x30bd79 = _0x58ac04.PosX || 0x0;
    const _0x125b1c = _0x58ac04.PosY || 0x0;
    $gameMap.createChainReactionCatalystAtXy(_0x4a7fd1, _0x30bd79, _0x125b1c);
});
PluginManager.registerCommand(pluginData.name, 'PositioningPullPlayerFront', _0x5e41f9 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x5e41f9, _0x5e41f9);
    const _0x3ed650 = $gameMap
        .eventsXy($gamePlayer.frontX(), $gamePlayer.frontY())
        .filter(_0x17b9eb => _0x17b9eb && _0x17b9eb.canBePulled());
    if (_0x3ed650.length <= 0x0) {
        return;
    }
    const _0x46fde6 = _0x3ed650[0x0];
    const _0xda958 = {
        name: _0x5e41f9.Sound.name || '',
        volume: _0x5e41f9.Sound.volume || 0x5a,
        pitch: _0x5e41f9.Sound.pitch || 0x64,
        pan: _0x5e41f9.Sound.pan || 0x0,
    };
    const _0x14176d = _0x5e41f9.CheckTriggers ?? true;
    $gamePlayer.pullTargetEvent(_0x46fde6, _0xda958, _0x14176d);
});
PluginManager.registerCommand(pluginData.name, 'PositioningPullThisEvent', _0x1104b5 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x1104b5, _0x1104b5);
    const _0x1f1c6b = $gameTemp.getLastPluginCommandInterpreter();
    const _0x251797 = $gameMap.event(_0x1f1c6b.eventId());
    if (!_0x251797) {
        return;
    }
    const _0x1216a5 = {
        name: _0x1104b5.Sound.name || '',
        volume: _0x1104b5.Sound.volume || 0x5a,
        pitch: _0x1104b5.Sound.pitch || 0x64,
        pan: _0x1104b5.Sound.pan || 0x0,
    };
    const _0x580382 = _0x1104b5.CheckTriggers ?? true;
    $gamePlayer.pullTargetEvent(_0x251797, _0x1216a5, _0x580382);
});
PluginManager.registerCommand(pluginData.name, 'PositioningPushPlayerFront', _0x3003f4 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x3003f4, _0x3003f4);
    const _0x2d68d1 = $gameMap
        .eventsXy($gamePlayer.frontX(), $gamePlayer.frontY())
        .filter(_0x1da5c9 => _0x1da5c9 && _0x1da5c9.canBePushed());
    if (_0x2d68d1.length <= 0x0) {
        return;
    }
    const _0x298bf6 = _0x2d68d1[0x0];
    const _0x18fa87 = {
        name: _0x3003f4.Sound.name || '',
        volume: _0x3003f4.Sound.volume || 0x5a,
        pitch: _0x3003f4.Sound.pitch || 0x64,
        pan: _0x3003f4.Sound.pan || 0x0,
    };
    const _0x3b25b6 = _0x3003f4.CheckTriggers ?? true;
    $gamePlayer.pushTargetEvent(_0x298bf6, _0x18fa87, _0x3003f4.MoveForward, _0x3b25b6);
});
PluginManager.registerCommand(pluginData.name, 'PositioningPushThisEvent', _0x2e6f07 => {
    if (!SceneManager.isSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x2e6f07, _0x2e6f07);
    const _0x16008d = $gameTemp.getLastPluginCommandInterpreter();
    const _0x53d383 = $gameMap.event(_0x16008d.eventId());
    if (!_0x53d383) {
        return;
    }
    const _0x3c3534 = {
        name: _0x2e6f07.Sound.name || '',
        volume: _0x2e6f07.Sound.volume || 0x5a,
        pitch: _0x2e6f07.Sound.pitch || 0x64,
        pan: _0x2e6f07.Sound.pan || 0x0,
    };
    const _0x277f44 = _0x2e6f07.CheckTriggers ?? true;
    $gamePlayer.pushTargetEvent(_0x53d383, _0x3c3534, _0x2e6f07.MoveForward, _0x277f44);
});
VisuMZ.EventChainReact.RegExp = {
    CanPush: /<(?:PUSH|REPOSITION|PUSH PULL|PULL PUSH)>/i,
    CanPull: /<(?:PULL|REPOSITION|PUSH PULL|PULL PUSH)>/i,
    EventReactOn: /<(.*?) (?:REACT|REACTION|REACTOR) (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/gi,
    EventReactOff: /<(.*?) (?:REACT|REACTION|REACTOR) (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/gi,
    SpreadReactSpeed:
        /<(.*?)[ ](TICK|FAST|QUICK|SHORT|AVG|AVERAGE|SLOW|LONG|LATE)[ ](?:SPREAD|SPREADS|CATALYST):[ ](.*?)>/gi,
    SpreadReactFrame: /<(.*?)[ ](\d+)[ ](?:FRAME|FRAMES)[ ](?:SPREAD|SPREADS|CATALYST):[ ](.*?)>/gi,
    HeavyObj: /<(?:HEAVY OBJECT|HEAVY OBJ|HEAVY)>/i,
    NotHeavyObj: /<NOT (?:HEAVY OBJECT|HEAVY OBJ|HEAVY)>/i,
    PressurePlate: /<(?:PRESSURE PLATE|PLATE) (?:SW|SWITCH|SWITCHES):[ ](.*?)>/i,
    AllWeightSwitch: /<ALL (?:PRESSURE PLATES|PLATES) (?:SW|SWITCH|SWITCHES):[ ](.*?)>/i,
    Charger: /<(.*?)[ ](?:CHARGER|CHARGE)>/gi,
    Conductor: /<(.*?)[ ](?:CONDUCTOR|CONDUCT) (?:SW|SWITCH|SWITCHES):[ ](.*?)>/gi,
    ChargeDir:
        /<(?:CHARGER|CHARGE|CONDUCTOR|CONDUCT|CURRENT)[ ](?:DIR|DIRECTION|DIRECTIONS):[ ](.*?)>/i,
    DecaySpeedOn:
        /<(TICK|FAST|QUICK|SHORT|AVG|AVERAGE|SLOW|LONG|LATE) DECAY (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/i,
    DecaySpeedOff:
        /<(TICK|FAST|QUICK|SHORT|AVG|AVERAGE|SLOW|LONG|LATE) DECAY (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/i,
    DecayFrameOn: /<(\d+)[ ](?:FRAME|FRAMES) DECAY (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/i,
    DecayFrameOff: /<(\d+)[ ](?:FRAME|FRAMES) DECAY (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/i,
    ShallowSubmergeOn: /<(?:SHALLOW SUBMERGE|SUBMERGE) (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/i,
    ShallowSubmergeOff: /<(?:SHALLOW SUBMERGE|SUBMERGE) (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/i,
    DeepSubmergeOn: /<(?:DEEP SUBMERGE|SUBMERGE) (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/i,
    DeepSubmergeOff: /<(?:DEEP SUBMERGE|SUBMERGE) (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/i,
    ResurfaceOn: /<(?:SURFACE|RESURFACE) (?:SW|SWITCH|SWITCHES) ON:[ ](.*?)>/i,
    ResurfaceOff: /<(?:SURFACE|RESURFACE) (?:SW|SWITCH|SWITCHES) OFF:[ ](.*?)>/i,
    SubmergedEffect: /<SUBMERGED EFFECT>/i,
    SubmergedEffectOffset: /<SUBMERGED EFFECT:[ ](\d+)>/i,
    Bridge: /<BRIDGE>/i,
};
SoundManager.playSubmerge = function () {
    const _0x141caa = VisuMZ.EventChainReact.Settings.Submerge;
    const _0x49f1bc = {
        name: _0x141caa.soundName || '',
        volume: _0x141caa.soundVolume || 0x5a,
        pitch: _0x141caa.soundPitch || 0x64,
        pan: _0x141caa.soundPan || 0x0,
    };
    AudioManager.playSe(_0x49f1bc);
};
SceneManager.isSceneMap = function () {
    return this._scene && this._scene.constructor === Scene_Map;
};
Game_Temp.prototype.clearEventChainReactionCache = function () {
    this._submergedEventsXy = {};
    this._bridgeEventsXy = {};
};
Game_Temp.prototype.submergedEventsXy = function (_0x9ec9e6, _0x8b82e0) {
    this._submergedEventsXy = this._submergedEventsXy || {};
    const _0x4e0569 = '%1,%2'.format(_0x9ec9e6, _0x8b82e0);
    if (this._submergedEventsXy[_0x4e0569]) {
        return this._submergedEventsXy[_0x4e0569];
    }
    this._submergedEventsXy[_0x4e0569] = $gameMap
        .eventsXy(_0x9ec9e6, _0x8b82e0)
        .filter(_0x3f8c4e => _0x3f8c4e && _0x3f8c4e.submergedEffect());
    return this._submergedEventsXy[_0x4e0569];
};
Game_Temp.prototype.bridgeEventsXy = function (_0x183496, _0xc9175a) {
    this._bridgeEventsXy = this._bridgeEventsXy || {};
    const _0x127e6b = '%1,%2'.format(_0x183496, _0xc9175a);
    if (this._bridgeEventsXy[_0x127e6b]) {
        return this._bridgeEventsXy[_0x127e6b];
    }
    this._bridgeEventsXy[_0x127e6b] = $gameMap
        .eventsXy(_0x183496, _0xc9175a)
        .filter(_0x424aff => _0x424aff && _0x424aff.isBridge());
    return this._bridgeEventsXy[_0x127e6b];
};
VisuMZ.EventChainReact.Game_Map_setup = Game_Map.prototype.setup;
Game_Map.prototype.setup = function (_0x1f38e0) {
    VisuMZ.EventChainReact.Game_Map_setup.call(this, _0x1f38e0);
    this.setupEventChainReactNotetags();
    this.refreshEventChainReact();
};
Game_Map.prototype.setupEventChainReactNotetags = function () {
    this.setupPressurePlateNotetags();
};
VisuMZ.EventChainReact.Game_Map_refreshIfNeeded = Game_Map.prototype.refreshIfNeeded;
Game_Map.prototype.refreshIfNeeded = function () {
    VisuMZ.EventChainReact.Game_Map_refreshIfNeeded.call(this);
    this.refreshEventChainReact();
};
Game_Map.prototype.refreshEventChainReact = function () {
    if (this._requestRefreshPressurePlate) {
        this.refreshPressurePlate();
    }
    if (this._requestRefreshConduction) {
        this.refreshConduction();
    }
};
VisuMZ.EventChainReact.Game_Map_refresh = Game_Map.prototype.refresh;
Game_Map.prototype.refresh = function () {
    VisuMZ.EventChainReact.Game_Map_refresh.call(this);
    $gameTemp.clearEventChainReactionCache();
};
VisuMZ.EventChainReact.RelativeCoordinates = function (_0x26c125, _0x3ebb43) {
    const _0x51f4e1 = _0x26c125.x;
    const _0x425848 = _0x26c125.y;
    const _0x473c90 = [];
    switch (_0x3ebb43.toLowerCase().trim()) {
        case 'exact':
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848,
            });
            break;
        case 'front':
            _0x473c90.push({
                x: _0x26c125.frontX(),
                y: _0x26c125.frontY(),
            });
            break;
        case 'back':
            _0x473c90.push({
                x: _0x26c125.backX(),
                y: _0x26c125.backY(),
            });
            break;
        case 'cw':
        case 'clockwise':
            _0x473c90.push({
                x: _0x26c125.cwX(),
                y: _0x26c125.cwY(),
            });
            break;
        case 'ccw':
        case 'counterclockwise':
            _0x473c90.push({
                x: _0x26c125.ccwX(),
                y: _0x26c125.ccwY(),
            });
            break;
        case 'adjacent':
        case 'next':
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 - 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 + 0x1,
            });
            break;
        case 'near':
        case 'nearby':
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848 - 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848 + 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 - 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 + 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848 - 0x1,
            });
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848,
            });
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848 + 0x1,
            });
            break;
        case 'down':
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 + 0x1,
            });
            break;
        case 'left':
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848,
            });
            break;
        case 'right':
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848,
            });
            break;
        case 'up':
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848 - 0x1,
            });
            break;
        case 'lower left':
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848 + 0x1,
            });
            break;
        case 'lower right':
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848 + 0x1,
            });
            break;
        case 'upper left':
            _0x473c90.push({
                x: _0x51f4e1 - 0x1,
                y: _0x425848 - 0x1,
            });
            break;
        case 'upper right':
            _0x473c90.push({
                x: _0x51f4e1 + 0x1,
                y: _0x425848 - 0x1,
            });
            break;
        default:
            _0x473c90.push({
                x: _0x51f4e1,
                y: _0x425848,
            });
            break;
    }
    return _0x473c90;
};
Game_Map.prototype.createChainReactionCatalystAtXy = function (
    _0x449a93,
    _0x4e00c9,
    _0x500e5d,
    _0x49dbeb
) {
    const _0x517094 = this.eventsXy(_0x4e00c9, _0x500e5d).filter(
        _0x210526 => _0x210526 && _0x210526 !== _0x49dbeb
    );
    for (const _0x55b4b2 of _0x517094) {
        if (_0x55b4b2 && !_0x55b4b2._erased) {
            _0x55b4b2.reactToType(_0x449a93);
        }
    }
};
Game_Map.prototype.spreadChainReactionFromTarget = function (_0x150a28, _0x27034d, _0x17e267) {
    if (!_0x150a28) {
        return;
    }
    const _0x4ee61b = VisuMZ.EventChainReact.RelativeCoordinates(_0x150a28, _0x17e267);
    const _0x53f2e3 = _0x150a28 === $gamePlayer ? 0x0 : -_0x150a28._addedHitbox.left;
    const _0x930e56 = _0x150a28 === $gamePlayer ? 0x0 : _0x150a28._addedHitbox.right;
    const _0x1e9d16 = _0x150a28 === $gamePlayer ? 0x0 : -_0x150a28._addedHitbox.up;
    const _0x174a0d = _0x150a28 === $gamePlayer ? 0x0 : _0x150a28._addedHitbox.down;
    for (let _0x54fb42 = _0x53f2e3; _0x54fb42 <= _0x930e56; _0x54fb42++) {
        for (let _0x45c5b6 = _0x1e9d16; _0x45c5b6 <= _0x174a0d; _0x45c5b6++) {
            for (const _0x37a28b of _0x4ee61b) {
                const _0x42381e = _0x37a28b.x ?? -0x1;
                const _0x73bc2 = _0x37a28b.y ?? -0x1;
                this.createChainReactionCatalystAtXy(
                    _0x27034d,
                    _0x42381e + _0x54fb42,
                    _0x73bc2 + _0x45c5b6,
                    this
                );
            }
        }
    }
};
Game_Map.prototype.setupPressurePlateNotetags = function () {
    this._allPressurePlateSwitches = [];
    const _0x2023e1 = VisuMZ.EventChainReact.RegExp;
    const _0x3a13e9 = $dataMap ? $dataMap.note || '' : '';
    if (_0x3a13e9.match(_0x2023e1.AllWeightSwitch)) {
        this._allPressurePlateSwitches = String(RegExp.$1)
            .split(',')
            .map(_0x1ab2d7 => Number(_0x1ab2d7) || 0x0);
    }
};
Game_Map.prototype.requestRefreshPressurePlate = function () {
    this._requestRefreshPressurePlate = true;
};
Game_Map.prototype.refreshPressurePlate = function () {
    this._requestRefreshPressurePlate = false;
    const _0x524236 = this.events().filter(_0x171eb4 => _0x171eb4 && _0x171eb4.isPressurePlate());
    for (const _0x3301fe of _0x524236) {
        if (_0x3301fe) {
            _0x3301fe.processPressurePlateChanges();
        }
    }
    if (this._allPressurePlateSwitches === undefined) {
        this.setupPressurePlateNotetags();
    }
    if (this._allPressurePlateSwitches.length > 0x0) {
        const _0x32445b = _0x524236.every(
            _0x4cd305 =>
                _0x4cd305.anyHeavyObjOnThisTile() &&
                !_0x4cd305.isSpawnedEvent() &&
                !_0x4cd305._erased
        );
        for (const _0x2da193 of this._allPressurePlateSwitches) {
            $gameSwitches.setValue(_0x2da193, _0x32445b);
        }
    }
};
Game_Map.prototype.requestRefreshCondution = function () {
    this._requestRefreshConduction = true;
};
Game_Map.prototype.refreshConduction = function () {
    this._requestRefreshConduction = false;
    const _0x24c5aa = this.events().filter(_0x2404b7 => _0x2404b7 && _0x2404b7.isConductor());
    for (const _0xdbf343 of _0x24c5aa) {
        _0xdbf343.turnOffAllConductions();
    }
    const _0x28e494 = this.events().filter(_0xfd4079 => _0xfd4079 && _0xfd4079.hasCharge());
    for (const _0x1c7cbe of _0x28e494) {
        _0x1c7cbe.spreadCharges();
    }
    for (const _0x11478e of _0x24c5aa) {
        _0x11478e.synchConductionStates();
    }
};
Game_Map.prototype.spreadChargeAtCoordinate = function (_0x573eb3, _0xb9b7b3, _0x34a43c) {
    const _0xa8d2d7 = this.eventsXy(_0xb9b7b3, _0x34a43c).filter(
        _0x4b4c83 =>
            _0x4b4c83 &&
            _0x4b4c83.isConductor() &&
            _0x4b4c83.canConductType(_0x573eb3) &&
            _0x4b4c83.getConductionState(_0x573eb3) === false
    );
    for (const _0x5dbe23 of _0xa8d2d7) {
        _0x5dbe23.setConductionState(_0x573eb3, true);
        _0x5dbe23.spreadChargeType(_0x573eb3);
        _0x5dbe23.update();
    }
};
VisuMZ.EventChainReact.Game_Map_checkPassage = Game_Map.prototype.checkPassage;
Game_Map.prototype.checkPassage = function (_0x19d872, _0x47956d, _0xb1f61) {
    const _0x51c72e = $gameTemp.bridgeEventsXy(_0x19d872, _0x47956d);
    if (_0x51c72e.length > 0x0) {
        if (_0xb1f61 === 0x200) {
            return false;
        }
        if (_0xb1f61 === 0x400) {
            return false;
        }
        return true;
    } else {
        return VisuMZ.EventChainReact.Game_Map_checkPassage.call(
            this,
            _0x19d872,
            _0x47956d,
            _0xb1f61
        );
    }
};
Game_Map.prototype.isWaterTile = function (_0x518767, _0x70300a) {
    return (
        this.isShallowSubmersible(_0x518767, _0x70300a) ||
        this.isDeepSubmersible(_0x518767, _0x70300a)
    );
};
Game_Map.prototype.isShallowSubmersible = function (_0x34a6eb, _0x1c3dd1) {
    return this.checkPassageNoEvents(_0x34a6eb, _0x1c3dd1, 0x200);
};
Game_Map.prototype.isDeepSubmersible = function (_0x28fab6, _0x33be58) {
    return this.checkPassageNoEvents(_0x28fab6, _0x33be58, 0x400);
};
Game_Map.prototype.checkPassageNoEvents = function (_0xebfa4a, _0xd8130f, _0x57a552) {
    const _0x5b2b92 = this.tilesetFlags();
    const _0x9bba28 = this.layeredTiles(_0xebfa4a, _0xd8130f);
    for (const _0x3e0e90 of _0x9bba28) {
        const _0x4257f3 = _0x5b2b92[_0x3e0e90];
        if (_0x4257f3 === undefined || _0x4257f3 === null) {
            let _0x395005 = 'Current tileset has incomplete flag data.\n';
            _0x395005 += 'Click "Copy Page" from another tileset\'s pages\n';
            _0x395005 += 'and add it onto this one.';
            alert(_0x395005);
            SceneManager.exit();
        }
        if ((_0x4257f3 & 0x10) !== 0x0) {
            continue;
        }
        if ((_0x4257f3 & _0x57a552) === 0x0) {
            return true;
        }
        if ((_0x4257f3 & _0x57a552) === _0x57a552) {
            return false;
        }
    }
    return false;
};
VisuMZ.EventChainReact.Game_CharacterBase_update = Game_CharacterBase.prototype.update;
Game_CharacterBase.prototype.update = function () {
    this.updateEventChainReact();
    VisuMZ.EventChainReact.Game_CharacterBase_update.call(this);
};
Game_CharacterBase.prototype.updateEventChainReact = function () {
    if (this.needsUpdatePressurePlate()) {
        this.requestRefreshPressurePlate();
    }
};
Game_CharacterBase.prototype.needsUpdatePressurePlate = function () {
    if (!this.isHeavyObject()) {
        return false;
    }
    this._heavyObjCache = this._heavyObjCache || {};
    if (this._heavyObjCache.lastX !== this.x) {
        return true;
    }
    if (this._heavyObjCache.lastY !== this.y) {
        return true;
    }
    if (this._heavyObjCache.jumpHeight !== this.jumpHeight()) {
        return true;
    }
    if (this._heavyObjCache._pageIndex !== this._pageIndex) {
        return true;
    }
    return false;
};
Game_CharacterBase.prototype.isHeavyObject = function () {
    return false;
};
Game_CharacterBase.prototype.requestRefreshPressurePlate = function () {
    this._heavyObjCache = this._heavyObjCache || {};
    this._heavyObjCache.lastX = this.x;
    this._heavyObjCache.lastY = this.y;
    this._heavyObjCache.jumpHeight = this.jumpHeight();
    this._heavyObjCache._pageIndex = this._pageIndex;
    $gameMap.requestRefreshPressurePlate();
};
if (!Game_CharacterBase.prototype.ccwX) {
    Game_CharacterBase.prototype.ccwX = function () {
        const _0x3df297 = [0x0, 0x3, 0x6, 0x9, 0x2, 0x5, 0x8, 0x1, 0x4, 0x7][this.direction()];
        return $gameMap.roundXWithDirection(this.x, _0x3df297);
    };
    Game_CharacterBase.prototype.ccwY = function () {
        const _0x756b04 = [0x0, 0x3, 0x6, 0x9, 0x2, 0x5, 0x8, 0x1, 0x4, 0x7][this.direction()];
        return $gameMap.roundYWithDirection(this.y, _0x756b04);
    };
    Game_CharacterBase.prototype.cwX = function () {
        const _0x38521d = [0x0, 0x7, 0x4, 0x1, 0x8, 0x5, 0x2, 0x9, 0x6, 0x3][this.direction()];
        return $gameMap.roundXWithDirection(this.x, _0x38521d);
    };
    Game_CharacterBase.prototype.cwY = function () {
        const _0x8b7792 = [0x0, 0x7, 0x4, 0x1, 0x8, 0x5, 0x2, 0x9, 0x6, 0x3][this.direction()];
        return $gameMap.roundYWithDirection(this.y, _0x8b7792);
    };
}
Game_CharacterBase.prototype.submergedEffect = function () {
    return this._submergeEffect;
};
Game_CharacterBase.prototype.submergedEffectOffset = function () {
    return this._submergeEffectOffset || 0x0;
};
Game_Player.prototype.pullTargetEvent = function (_0x40bda2, _0x1a4623, _0x3f4439) {
    if (!_0x40bda2) {
        return;
    }
    if (_0x40bda2.isMoving()) {
        return;
    }
    if (_0x40bda2.inWaterTile(_0x40bda2.x, _0x40bda2.y)) {
        return;
    }
    const _0x11e4e3 = $gameMap.distance(this.x, this.y, _0x40bda2.x, _0x40bda2.y);
    if (_0x11e4e3 > 0x1) {
        return;
    }
    if (_0x40bda2.isOnLadder()) {
        return;
    }
    if (this.isOnLadder()) {
        return;
    }
    const _0x2c8c63 = 0xa - this.direction();
    if (_0x2c8c63 % 0x2 !== 0x0) {
        return;
    }
    if (!this.canPass(this.x, this.y, _0x2c8c63)) {
        return;
    }
    const _0xce5436 = this._through;
    this._through = true;
    if (!_0x40bda2.canPass(_0x40bda2.x, _0x40bda2.y, _0x2c8c63)) {
        return;
    }
    this._through = _0xce5436;
    const _0x2fd136 = $gameMap.roundXWithDirection(this.x, _0x2c8c63);
    const _0x182d4f = $gameMap.roundYWithDirection(this.y, _0x2c8c63);
    if ($gameMap.isLadder(_0x2fd136, _0x182d4f)) {
        return;
    }
    let _0x1b296a = 0x0;
    if (_0x2c8c63 === 0x2) {
        _0x1b296a = Game_Character.ROUTE_MOVE_DOWN;
    }
    if (_0x2c8c63 === 0x4) {
        _0x1b296a = Game_Character.ROUTE_MOVE_LEFT;
    }
    if (_0x2c8c63 === 0x6) {
        _0x1b296a = Game_Character.ROUTE_MOVE_RIGHT;
    }
    if (_0x2c8c63 === 0x8) {
        _0x1b296a = Game_Character.ROUTE_MOVE_UP;
    }
    const _0x276bbb = _0x40bda2.moveSpeed();
    const _0x3c6140 = $gamePlayer.moveSpeed();
    const _0x5cc968 = Math.min(_0x276bbb, _0x3c6140);
    {
        const _0x7ef815 = {
            list: [],
            repeat: false,
            skippable: true,
            wait: true,
        };
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_SCRIPT,
            parameters: ['this.gatherFollowersAndWait()'],
            indent: null,
        });
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_SCRIPT,
            parameters: ['this.pullFollowers(true)'],
            indent: null,
        });
        if (_0x1a4623 && _0x1a4623.name !== '') {
            _0x7ef815.list.push({
                code: Game_Character.ROUTE_PLAY_SE,
                parameters: [_0x1a4623],
                indent: null,
            });
        }
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_DIR_FIX_ON,
            indent: null,
        });
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x5cc968],
            indent: null,
        });
        _0x7ef815.list.push({
            code: _0x1b296a,
            indent: null,
        });
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x3c6140],
            indent: null,
        });
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_DIR_FIX_OFF,
            indent: null,
        });
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_SCRIPT,
            parameters: ['this.pullFollowers(false)'],
            indent: null,
        });
        if (_0x3f4439) {
            _0x7ef815.list.push({
                code: Game_Character.ROUTE_SCRIPT,
                parameters: ['this.checkEventTriggerHere([1, 2])'],
                indent: null,
            });
        }
        _0x7ef815.list.push({
            code: Game_Character.ROUTE_END,
        });
        this.forceMoveRoute(_0x7ef815);
    }
    {
        const _0x58328e = {
            list: [],
            repeat: false,
            skippable: true,
            wait: true,
        };
        _0x58328e.list.push({
            code: Game_Character.ROUTE_SCRIPT,
            parameters: ['this._gatherWait = true'],
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_THROUGH_ON,
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x5cc968],
            indent: null,
        });
        _0x58328e.list.push({
            code: _0x1b296a,
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x276bbb],
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_THROUGH_OFF,
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_SCRIPT,
            parameters: ['this._gatherWait = false'],
            indent: null,
        });
        _0x58328e.list.push({
            code: Game_Character.ROUTE_END,
        });
        _0x40bda2.forceMoveRoute(_0x58328e);
    }
    const _0x40ce0b = $gameTemp.getLastPluginCommandInterpreter();
    _0x40ce0b._characterId = _0x40bda2.eventId();
    _0x40ce0b.setWaitMode('route');
};
Game_Player.prototype.pushTargetEvent = function (_0x5c255b, _0x5ec774, _0x1039ac, _0x35d4f1) {
    if (!_0x5c255b) {
        return;
    }
    if (_0x5c255b.isMoving()) {
        return;
    }
    if (_0x5c255b.inWaterTile(_0x5c255b.x, _0x5c255b.y)) {
        return;
    }
    const _0x4a40c3 = $gameMap.distance(this.x, this.y, _0x5c255b.x, _0x5c255b.y);
    if (_0x4a40c3 > 0x1) {
        return;
    }
    if (_0x5c255b.isOnLadder()) {
        return;
    }
    if (this.isOnLadder()) {
        return;
    }
    const _0xf60628 = this.direction();
    if (_0xf60628 % 0x2 !== 0x0) {
        return;
    }
    if (!_0x5c255b.canPass(_0x5c255b.x, _0x5c255b.y, _0xf60628)) {
        return;
    }
    const _0x25fba0 = $gameMap.roundXWithDirection(_0x5c255b.x, _0xf60628);
    const _0x458faf = $gameMap.roundYWithDirection(_0x5c255b.y, _0xf60628);
    if ($gameMap.isLadder(_0x25fba0, _0x458faf)) {
        return;
    }
    if (_0x5ec774 && _0x5ec774.name !== '') {
        AudioManager.playSe(_0x5ec774);
    }
    let _0x4b55d7 = 0x0;
    if (_0xf60628 === 0x2) {
        _0x4b55d7 = Game_Character.ROUTE_MOVE_DOWN;
    }
    if (_0xf60628 === 0x4) {
        _0x4b55d7 = Game_Character.ROUTE_MOVE_LEFT;
    }
    if (_0xf60628 === 0x6) {
        _0x4b55d7 = Game_Character.ROUTE_MOVE_RIGHT;
    }
    if (_0xf60628 === 0x8) {
        _0x4b55d7 = Game_Character.ROUTE_MOVE_UP;
    }
    const _0x2e36f4 = _0x5c255b.moveSpeed();
    const _0x31cb37 = _0x1039ac ? $gamePlayer.moveSpeed() : _0x2e36f4;
    const _0x47d5a8 = Math.min(_0x2e36f4, _0x31cb37);
    {
        const _0x3354f2 = {
            list: [],
            repeat: false,
            skippable: true,
            wait: true,
        };
        _0x3354f2.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x47d5a8],
            indent: null,
        });
        _0x3354f2.list.push({
            code: _0x4b55d7,
            indent: null,
        });
        _0x3354f2.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x2e36f4],
            indent: null,
        });
        _0x3354f2.list.push({
            code: Game_Character.ROUTE_END,
        });
        _0x5c255b.forceMoveRoute(_0x3354f2);
    }
    if (_0x1039ac) {
        const _0x2aee49 = {
            list: [],
            repeat: false,
            skippable: true,
            wait: true,
        };
        _0x2aee49.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x47d5a8],
            indent: null,
        });
        _0x2aee49.list.push({
            code: _0x4b55d7,
            indent: null,
        });
        _0x2aee49.list.push({
            code: Game_Character.ROUTE_CHANGE_SPEED,
            parameters: [_0x31cb37],
            indent: null,
        });
        _0x2aee49.list.push({
            code: Game_Character.ROUTE_DIR_FIX_OFF,
            indent: null,
        });
        if (_0x35d4f1) {
            _0x2aee49.list.push({
                code: Game_Character.ROUTE_SCRIPT,
                parameters: ['this.checkEventTriggerHere([1, 2])'],
                indent: null,
            });
        }
        _0x2aee49.list.push({
            code: Game_Character.ROUTE_END,
        });
        this.forceMoveRoute(_0x2aee49);
    }
    const _0x1d8f83 = $gameTemp.getLastPluginCommandInterpreter();
    _0x1d8f83._characterId = _0x5c255b.eventId();
    _0x1d8f83.setWaitMode('route');
};
Game_Player.prototype.gatherFollowersAndWait = function () {
    this.gatherFollowers();
    this._gatherWait = true;
};
VisuMZ.EventChainReact.Game_Character_updateRoutineMove =
    Game_Character.prototype.updateRoutineMove;
Game_Character.prototype.updateRoutineMove = function () {
    if (this._gatherWait && $gamePlayer.areFollowersGathering()) {
        return;
    }
    VisuMZ.EventChainReact.Game_Character_updateRoutineMove.call(this);
};
Game_Player.prototype.pullFollowers = function (_0x356314) {
    this._followers.pullFollowers(_0x356314);
    if (!_0x356314) {
        this._gatherWait = false;
    }
};
Game_Player.prototype.isHeavyObject = function () {
    return VisuMZ.EventChainReact.Settings.PressurePlate.HeavyPlayerCharacter;
};
Game_Follower.prototype.isHeavyObject = function () {
    if (!VisuMZ.EventChainReact.Settings.PressurePlate.HeavyFollowers) {
        return false;
    }
    return this.isVisible() && this.characterName() !== '';
};
Game_Followers.prototype.pullFollowers = function (_0x3b19f7) {
    for (let _0x43c2d5 = this._data.length - 0x1; _0x43c2d5 >= 0x0; _0x43c2d5--) {
        this._data[_0x43c2d5]._pullingFollowers = _0x3b19f7;
        if (this._data[_0x43c2d5].isMoving()) {
            continue;
        }
        if (_0x3b19f7) {
            this._data[_0x43c2d5].chasePull($gamePlayer);
        }
    }
};
Game_Follower.prototype.chasePull = function (_0xc70922) {
    this.chasePullXy(_0xc70922.backX(), _0xc70922.backY(), _0xc70922.direction());
};
Game_Follower.prototype.chasePullXy = function (_0x25988c, _0x229391, _0x22a3bf) {
    const _0x407d46 = this.deltaXFrom(_0x25988c);
    const _0x5a1ba7 = this.deltaYFrom(_0x229391);
    if (_0x407d46 !== 0x0 || _0x5a1ba7 !== 0x0) {
        if (_0x407d46 !== 0x0 && _0x5a1ba7 !== 0x0) {
            this.moveDiagonally(_0x407d46 > 0x0 ? 0x4 : 0x6, _0x5a1ba7 > 0x0 ? 0x8 : 0x2);
        } else {
            if (_0x407d46 !== 0x0) {
                this.moveStraight(_0x407d46 > 0x0 ? 0x4 : 0x6);
            } else if (_0x5a1ba7 !== 0x0) {
                this.moveStraight(_0x5a1ba7 > 0x0 ? 0x8 : 0x2);
            }
        }
    }
    this.setDirection(_0x22a3bf);
    this.setMoveSpeed($gamePlayer.realMoveSpeed());
};
VisuMZ.EventChainReact.Game_Follower_chaseCharacter = Game_Follower.prototype.chaseCharacter;
Game_Follower.prototype.chaseCharacter = function (_0x706cf) {
    if (this._pullingFollowers) {
        return;
    }
    VisuMZ.EventChainReact.Game_Follower_chaseCharacter.call(this, _0x706cf);
};
Game_Event.EVENT_CHAIN_REACT_SPEED = {
    tick: VisuMZ.EventChainReact.Settings.Speed.tick || 0x1,
    fast: VisuMZ.EventChainReact.Settings.Speed.fast || 0x8,
    quick: VisuMZ.EventChainReact.Settings.Speed.quick || 0xf,
    short: VisuMZ.EventChainReact.Settings.Speed.short || 0x14,
    avg: VisuMZ.EventChainReact.Settings.Speed.avg || 0x1e,
    average: VisuMZ.EventChainReact.Settings.Speed.avg || 0x1e,
    slow: VisuMZ.EventChainReact.Settings.Speed.slow || 0x28,
    long: VisuMZ.EventChainReact.Settings.Speed.long || 0x3c,
    late: VisuMZ.EventChainReact.Settings.Speed.late || 0x78,
};
VisuMZ.EventChainReact.Game_Event_clearPageSettings = Game_Event.prototype.clearPageSettings;
Game_Event.prototype.clearPageSettings = function () {
    VisuMZ.EventChainReact.Game_Event_clearPageSettings.call(this);
    this.initEventChainReactEffects();
};
VisuMZ.EventChainReact.Game_Event_setupPageSettings = Game_Event.prototype.setupPageSettings;
Game_Event.prototype.setupPageSettings = function () {
    VisuMZ.EventChainReact.Game_Event_setupPageSettings.call(this);
    this.setupEventChainReactEffects();
};
Game_Event.prototype.setupEventChainReactEffects = function () {
    if (!this.event()) {
        return;
    }
    this.initEventChainReactEffects();
    this.setupEventChainReactNotetags();
    this.setupEventChainReactCommentTags();
};
Game_Event.prototype.setupEventChainReactNotetags = function () {
    let _0x409134 = this.event().note;
    if (_0x409134 === '') {
        return;
    }
    _0x409134 = _0x409134.replace(/>[ ]*</i, '>\n<');
    this.checkEventChainReactStringTags(_0x409134);
};
Game_Event.prototype.setupEventChainReactCommentTags = function () {
    if (!this.page()) {
        return;
    }
    const _0x5aeca1 = this.list();
    let _0x33df0f = '';
    for (const _0x5a4833 of _0x5aeca1) {
        if ([0x6c, 0x198].includes(_0x5a4833.code)) {
            if (_0x33df0f !== '') {
                _0x33df0f += '\n';
            }
            _0x33df0f += _0x5a4833.parameters[0x0];
        }
    }
    this.checkEventChainReactStringTags(_0x33df0f);
};
Game_Event.prototype.isSmartEventCollisionOn = function () {
    return true;
};
Game_Event.prototype.initEventChainReactEffects = function () {
    this._canPush = false;
    this._canPull = false;
    this._chainReactions = {};
    this._chainSpreads = [];
    this._pressurePlateSwitches = [];
    this._heavyObject = false;
    if (this._priorityType === 0x1) {
        this._heavyObject = VisuMZ.EventChainReact.Settings.PressurePlate.HeavySameEvents;
    }
    this._chargeTypes = [];
    this._conductTypes = {};
    this._conductDir = ['adjacent'];
    this._decayOnDuration = -0x1;
    this._decayOffDuration = -0x1;
    this._decaySwitchOn = [];
    this._decaySwitchOff = [];
    this._submergeShallowOn = [];
    this._submergeShallowOff = [];
    this._submergeDeepOn = [];
    this._submergeDeepOff = [];
    this._resurfaceOn = [];
    this._resurfaceOff = [];
    this._submergeEffect = false;
    this._submergeEffectOffset = 0x0;
    this._bridge = false;
};
Game_Event.prototype.initEventChainReactType = function (_0xe88715) {
    if (this._chainReactions === undefined) {
        this.initEventChainReactEffects();
    }
    _0xe88715 = _0xe88715.toLowerCase().trim();
    this._chainReactions[_0xe88715] = this._chainReactions[_0xe88715] || {};
    this._chainReactions[_0xe88715].reactSwitchOn = [];
    this._chainReactions[_0xe88715].reactSwitchOff = [];
};
Game_Event.prototype.checkEventChainReactStringTags = function (_0xee8e33) {
    const _0x4e817c = VisuMZ.EventChainReact.RegExp;
    if (_0xee8e33.match(_0x4e817c.CanPush)) {
        this._canPush = true;
        this._priorityType = 0x1;
    }
    if (_0xee8e33.match(_0x4e817c.CanPull)) {
        this._canPull = true;
        this._priorityType = 0x1;
    }
    {
        const _0x13d521 = _0x4e817c.EventReactOn;
        const _0x3f300b = _0xee8e33.match(_0x13d521);
        if (_0x3f300b) {
            for (const _0x14daa3 of _0x3f300b) {
                _0x14daa3.match(_0x13d521);
                const _0x2ba801 = String(RegExp.$1).toLowerCase().trim();
                const _0x566879 = String(RegExp.$2)
                    .split(',')
                    .map(_0x505e12 => _0x505e12.trim());
                this.initEventChainReactType(_0x2ba801);
                const _0x5f3adc = this._chainReactions[_0x2ba801].reactSwitchOn;
                VisuMZ.EventChainReact.AddEntries(_0x5f3adc, _0x566879);
            }
        }
    }
    {
        const _0x2483c2 = _0x4e817c.EventReactOff;
        const _0x3da5a7 = _0xee8e33.match(_0x2483c2);
        if (_0x3da5a7) {
            for (const _0xff3ef of _0x3da5a7) {
                _0xff3ef.match(_0x2483c2);
                const _0x4bed1f = String(RegExp.$1).toLowerCase().trim();
                const _0x2d63d7 = String(RegExp.$2)
                    .split(',')
                    .map(_0x413954 => _0x413954.trim());
                this.initEventChainReactType(_0x4bed1f);
                const _0x45b789 = this._chainReactions[_0x4bed1f].reactSwitchOff;
                VisuMZ.EventChainReact.AddEntries(_0x45b789, _0x2d63d7);
            }
        }
    }
    {
        const _0x45bb41 = _0x4e817c.SpreadReactSpeed;
        const _0x38945e = _0xee8e33.match(_0x45bb41);
        if (_0x38945e) {
            for (const _0x1e762c of _0x38945e) {
                _0x1e762c.match(_0x45bb41);
                const _0x5cea3d = String(RegExp.$1).toLowerCase().trim();
                const _0x4455b0 = String(RegExp.$2).toLowerCase().trim();
                const _0xc93de9 = String(RegExp.$3)
                    .split(',')
                    .map(_0x53442a => _0x53442a.toLowerCase().trim());
                const _0x69ada2 = Game_Event.EVENT_CHAIN_REACT_SPEED;
                const _0x41136b = _0x69ada2[_0x4455b0] || _0x69ada2.fast;
                for (const _0xd6ec9b of _0xc93de9) {
                    const _0xa4b438 = [_0x41136b, _0x5cea3d, _0xd6ec9b];
                    this._chainSpreads.push(_0xa4b438);
                }
            }
        }
    }
    {
        const _0xad8b25 = _0x4e817c.SpreadReactFrame;
        const _0x51e4c4 = _0xee8e33.match(_0xad8b25);
        if (_0x51e4c4) {
            for (const _0x554071 of _0x51e4c4) {
                _0x554071.match(_0xad8b25);
                const _0x5233c4 = String(RegExp.$1).toLowerCase().trim();
                const _0x496898 = Number(RegExp.$2) || 0x1;
                const _0x571f58 = String(RegExp.$3)
                    .split(',')
                    .map(_0x5c1b7f => _0x5c1b7f.toLowerCase().trim());
                for (const _0x39d76f of _0x571f58) {
                    const _0x5487d1 = [_0x496898, _0x5233c4, _0x39d76f];
                    this._chainSpreads.push(_0x5487d1);
                }
            }
        }
    }
    if (_0xee8e33.match(_0x4e817c.PressurePlate)) {
        this._pressurePlateSwitches = String(RegExp.$1)
            .split(',')
            .map(_0x1661bc => _0x1661bc.toUpperCase().trim());
        this._priorityType = 0x0;
        this._heavyObject = false;
        this._canPush = false;
        this._canPull = false;
    }
    if (_0xee8e33.match(_0x4e817c.HeavyObj)) {
        if (this._pressurePlateSwitches.length <= 0x0) {
            this._priorityType = 0x1;
            this._heavyObject = true;
        }
    }
    if (_0xee8e33.match(_0x4e817c.NotHeavyObj)) {
        this._heavyObject = false;
    }
    {
        const _0x2bb99d = _0x4e817c.Charger;
        const _0x5e45b4 = _0xee8e33.match(_0x2bb99d);
        if (_0x5e45b4) {
            for (const _0x44a1e7 of _0x5e45b4) {
                _0x44a1e7.match(_0x2bb99d);
                const _0x255b78 = String(RegExp.$1).toLowerCase().trim();
                if (!this._chargeTypes.includes(_0x255b78)) {
                    this._chargeTypes.push(_0x255b78);
                }
            }
        }
    }
    {
        const _0x54d4ac = _0x4e817c.Conductor;
        const _0x7ca945 = _0xee8e33.match(_0x54d4ac);
        if (_0x7ca945) {
            for (const _0x32f788 of _0x7ca945) {
                _0x32f788.match(_0x54d4ac);
                const _0xdc528a = String(RegExp.$1).toLowerCase().trim();
                const _0x1267dd = String(RegExp.$2)
                    .split(',')
                    .map(_0xdef1fa => _0xdef1fa.trim());
                this._conductTypes[_0xdc528a] = _0x1267dd;
            }
        }
    }
    if (_0xee8e33.match(_0x4e817c.ChargeDir)) {
        this._conductDir = String(RegExp.$1)
            .split(',')
            .map(_0x3b2c69 => _0x3b2c69.toLowerCase().trim());
    }
    if (_0xee8e33.match(_0x4e817c.DecayFrameOn)) {
        this._decayOnDuration = Math.max(Number(RegExp.$1), 0x1);
        this._decaySwitchOn = String(RegExp.$2)
            .split(',')
            .map(_0x11e6e5 => _0x11e6e5.trim());
    } else {
        if (_0xee8e33.match(_0x4e817c.DecaySpeedOn)) {
            const _0x10a130 = Game_Event.EVENT_CHAIN_REACT_SPEED;
            const _0x2c7a8e = String(RegExp.$1).toLowerCase().trim();
            this._decayOnDuration = _0x10a130[_0x2c7a8e] || _0x10a130.fast;
            this._decaySwitchOn = String(RegExp.$2)
                .split(',')
                .map(_0x149f38 => _0x149f38.trim());
        }
    }
    if (_0xee8e33.match(_0x4e817c.DecayFrameOff)) {
        this._decayOffDuration = Math.max(Number(RegExp.$1), 0x1);
        this._decaySwitchOff = String(RegExp.$2)
            .split(',')
            .map(_0x4c17b2 => _0x4c17b2.trim());
    } else {
        if (_0xee8e33.match(_0x4e817c.DecaySpeedOff)) {
            const _0x250f62 = Game_Event.EVENT_CHAIN_REACT_SPEED;
            const _0x184823 = String(RegExp.$1).toLowerCase().trim();
            this._decayOffDuration = _0x250f62[_0x184823] || _0x250f62.fast;
            this._decaySwitchOff = String(RegExp.$2)
                .split(',')
                .map(_0x2a57d0 => _0x2a57d0.trim());
        }
    }
    if (_0xee8e33.match(_0x4e817c.ShallowSubmergeOn)) {
        this._submergeShallowOn = String(RegExp.$1)
            .split(',')
            .map(_0x1e770d => _0x1e770d.trim());
    }
    if (_0xee8e33.match(_0x4e817c.ShallowSubmergeOff)) {
        this._submergeShallowOff = String(RegExp.$1)
            .split(',')
            .map(_0x5ec0f7 => _0x5ec0f7.trim());
    }
    if (_0xee8e33.match(_0x4e817c.DeepSubmergeOn)) {
        this._submergeDeepOn = String(RegExp.$1)
            .split(',')
            .map(_0x566dc2 => _0x566dc2.trim());
    }
    if (_0xee8e33.match(_0x4e817c.DeepSubmergeOff)) {
        this._submergeDeepOff = String(RegExp.$1)
            .split(',')
            .map(_0x1deafb => _0x1deafb.trim());
    }
    if (_0xee8e33.match(_0x4e817c.ResurfaceOn)) {
        this._resurfaceOn = String(RegExp.$1)
            .split(',')
            .map(_0x265451 => _0x265451.trim());
    }
    if (_0xee8e33.match(_0x4e817c.ResurfaceOff)) {
        this._resurfaceOff = String(RegExp.$1)
            .split(',')
            .map(_0x4a8a4c => _0x4a8a4c.trim());
    }
    if (_0xee8e33.match(_0x4e817c.SubmergedEffect)) {
        this._submergeEffect = true;
    }
    if (_0xee8e33.match(_0x4e817c.SubmergedEffectOffset)) {
        this._submergeEffect = true;
        this._submergeEffectOffset = Number(RegExp.$1);
    }
    if (_0xee8e33.match(_0x4e817c.Bridge)) {
        this._bridge = true;
        this._priorityType = 0x0;
        this._heavyObject = false;
        this._canPush = false;
        this._canPull = false;
    }
};
VisuMZ.EventChainReact.AddEntries = function (_0x227e89, _0x52b96e) {
    for (const _0x378e84 of _0x52b96e) {
        const _0x5180ab = /^\d+$/.test(_0x378e84);
        _0x227e89.push(_0x5180ab ? Number(_0x378e84) : String(_0x378e84).toUpperCase().trim());
    }
};
Game_Event.prototype.eventChainReactSwitchChange = function (_0xeb186b, _0x3851ef) {
    const _0x360941 = $gameMap._mapId;
    const _0x111577 = this._eventId || 0x0;
    for (const _0xc4a505 of _0xeb186b) {
        let _0x5b0918 = '';
        const _0x1a64c8 = /^\d+$/.test(_0xc4a505);
        if (_0x1a64c8) {
            const _0x17b1da = Number(_0xc4a505);
            if (DataManager.isSelfSwitch(_0x17b1da)) {
                _0x5b0918 = '%1,%2,Self Switch %3'.format(_0x360941, _0x111577, _0x17b1da);
            } else {
                if (DataManager.isMapSwitch(_0x17b1da)) {
                    const _0x531f9a = 'Map %1 Switch %2'.format(_0x360941, _0x17b1da);
                    const _0x5b3ef5 = !!$gameSelfSwitches._data[_0x531f9a];
                    if (_0x5b3ef5 !== _0x3851ef) {
                        $gameSwitches.setValue(_0x17b1da, _0x3851ef);
                    }
                } else {
                    const _0x44277b = !!$gameSwitches._data[_0x17b1da];
                    if (_0x44277b !== _0x3851ef) {
                        $gameSwitches.setValue(_0x17b1da, _0x3851ef);
                    }
                }
            }
        } else {
            _0x5b0918 = '%1,%2,%3'.format(_0x360941, _0x111577, String(_0xc4a505));
        }
        if (_0x5b0918 !== '') {
            const _0x282ec6 = !!$gameSelfSwitches._data[_0x5b0918];
            if (_0x282ec6 !== _0x3851ef) {
                $gameSelfSwitches.setValue(_0x5b0918, _0x3851ef);
            }
        }
    }
};
VisuMZ.EventChainReact.Game_Event_erase = Game_Event.prototype.erase;
Game_Event.prototype.erase = function () {
    if (this.isHeavyObject()) {
        this.requestRefreshPressurePlate();
    }
    VisuMZ.EventChainReact.Game_Event_erase.call(this);
};
Game_Event.prototype.updateEventChainReact = function () {
    Game_Character.prototype.updateEventChainReact.call(this);
    if (this.canSpreadEventChainReact()) {
        this.updateEventChainReactSpread();
    }
    if (this.needsUpdateConduction()) {
        this.requestRefreshConduction();
    }
    if (this.needsUpdateDecay()) {
        this.updateDecay();
    }
    if (this.needsUpdateSubmerge()) {
        this.updateSubmerge();
    }
};
Game_Event.prototype.canBePushed = function () {
    if (this._erased) {
        return false;
    }
    if (this.isMoving()) {
        return false;
    }
    if (this.inWaterTile(this.x, this.y)) {
        return false;
    }
    if (this.isOnLadder()) {
        return false;
    }
    if ($gamePlayer.isOnLadder()) {
        return false;
    }
    return this._canPush;
};
Game_Event.prototype.canBePulled = function () {
    if (this._erased) {
        return false;
    }
    if (this.isMoving()) {
        return false;
    }
    if (this.inWaterTile(this.x, this.y)) {
        return false;
    }
    if (this.isOnLadder()) {
        return false;
    }
    if ($gamePlayer.isOnLadder()) {
        return false;
    }
    return this._canPull;
};
Game_Event.prototype.reactToType = function (_0x32ed49) {
    if (!this._chainReactions) {
        return;
    }
    _0x32ed49 = _0x32ed49.toLowerCase().trim();
    if (this._chainReactions === undefined) {
        return;
    }
    if (this._chainReactions[_0x32ed49] === undefined) {
        return;
    }
    const _0xfc5682 = this._chainReactions[_0x32ed49].reactSwitchOn;
    const _0x35be3b = this._chainReactions[_0x32ed49].reactSwitchOff;
    this.eventChainReactSwitchChange(_0xfc5682, true);
    this.eventChainReactSwitchChange(_0x35be3b, false);
};
Game_Event.prototype.canSpreadEventChainReact = function () {
    if (!this._chainSpreads) {
        return false;
    }
    if ($gameMessage.isBusy()) {
        return false;
    }
    if (this.isMoving()) {
        return false;
    }
    if (Imported.VisuMZ_2_FurnitureSystem && $gameMap.isFurnitureSystemMode()) {
        return false;
    }
    return this._chainSpreads.length > 0x0;
};
Game_Event.prototype.updateEventChainReactSpread = function () {
    for (const _0x52f5d0 of this._chainSpreads) {
        const _0xa26c49 = _0x52f5d0[0x0];
        if (Graphics.frameCount % _0xa26c49 !== 0x0) {
            continue;
        }
        const _0x395cb2 = _0x52f5d0[0x1];
        const _0x244456 = _0x52f5d0[0x2];
        $gameMap.spreadChainReactionFromTarget(this, _0x395cb2, _0x244456);
    }
};
Game_Event.prototype.isHeavyObject = function () {
    if (this._erased) {
        return false;
    }
    if (this._heavyObject !== undefined) {
        return this._heavyObject;
    }
    return this._heavyObject || [];
};
Game_Event.prototype.isPressurePlate = function () {
    return this.getPressurePlateSwitches().length > 0x0;
};
Game_Event.prototype.getPressurePlateSwitches = function () {
    if (this._erased) {
        return [];
    }
    return this._pressurePlateSwitches || [];
};
Game_Event.prototype.processPressurePlateChanges = function () {
    let _0x3ed4a2 = this.anyHeavyObjOnThisTile();
    this.applyPressurePlateSwitchChange(_0x3ed4a2);
};
Game_Event.prototype.anyHeavyObjOnThisTile = function () {
    const _0x248cb9 = -this._addedHitbox.left;
    const _0x4cecd7 = this._addedHitbox.right;
    const _0x43d8c1 = -this._addedHitbox.up;
    const _0x250eaa = this._addedHitbox.down;
    for (let _0x49a76e = _0x248cb9; _0x49a76e <= _0x4cecd7; _0x49a76e++) {
        for (let _0x404d41 = _0x43d8c1; _0x404d41 <= _0x250eaa; _0x404d41++) {
            if ($gamePlayer.isHeavyObject()) {
                if (
                    $gamePlayer.pos(this.x + _0x49a76e, this.y + _0x404d41) &&
                    !$gamePlayer.isInAirship() &&
                    !$gamePlayer.isJumping()
                ) {
                    return true;
                }
            }
            if (
                $gamePlayer.followers().isVisible() &&
                !$gamePlayer.isInAirship() &&
                !$gamePlayer.isJumping()
            ) {
                const _0x4e00a8 = $gamePlayer
                    .followers()
                    ._data.filter(
                        _0xdac5ae =>
                            _0xdac5ae.isVisible() &&
                            _0xdac5ae.characterName() !== '' &&
                            !_0xdac5ae.isJumping()
                    );
                if (
                    _0x4e00a8.some(_0x3c9f91 =>
                        _0x3c9f91.pos(this.x + _0x49a76e, this.y + _0x404d41)
                    )
                ) {
                    return true;
                }
            }
            for (const _0xabc012 of $gameMap.vehicles()) {
                if (!_0xabc012) {
                    continue;
                }
                if (_0xabc012._mapId !== $gameMap.mapId()) {
                    continue;
                }
                if (!_0xabc012.isLowest()) {
                    continue;
                }
                if (_0xabc012.pos(this.x + _0x49a76e, this.y + _0x404d41)) {
                    return true;
                }
            }
            const _0x121b4a = $gameMap
                .eventsXy(this.x + _0x49a76e, this.y + _0x404d41)
                .filter(_0xdbc047 => _0xdbc047.isHeavyObject() && !_0xdbc047.isJumping());
            if (_0x121b4a.length > 0x0) {
                return true;
            }
        }
    }
    return false;
};
Game_Event.prototype.applyPressurePlateSwitchChange = function (_0x44562b) {
    const _0x36648a = this.getPressurePlateSwitches();
    this.eventChainReactSwitchChange(_0x36648a, _0x44562b);
};
Game_Event.prototype.isConductor = function () {
    return this.getConductionTypes().length > 0x0;
};
Game_Event.prototype.getConductionTypes = function () {
    if (this._erased) {
        return [];
    }
    return Object.keys(this._conductTypes || {});
};
Game_Event.prototype.canConductType = function (_0x4a16dc) {
    _0x4a16dc = _0x4a16dc.toLowerCase().trim();
    return this.getConductionTypes().includes(_0x4a16dc);
};
Game_Event.prototype.clearConductionState = function () {
    this._conductionState = {};
};
Game_Event.prototype.setConductionState = function (_0x10cce2, _0x1184a6) {
    _0x10cce2 = _0x10cce2.toLowerCase().trim();
    this._conductionState = this._conductionState || {};
    this._conductionState[_0x10cce2] = _0x1184a6;
};
Game_Event.prototype.getConductionState = function (_0x4de372) {
    _0x4de372 = _0x4de372.toLowerCase().trim();
    this._conductionState = this._conductionState || {};
    return this._conductionState[_0x4de372] || false;
};
Game_Event.prototype.hasCharge = function () {
    return this.getChargeTypes().length > 0x0;
};
Game_Event.prototype.getChargeTypes = function () {
    if (this._erased) {
        return [];
    }
    return this._chargeTypes || [];
};
Game_Event.prototype.getConductDirections = function () {
    if (this._erased) {
        return [];
    }
    return this._conductDir || [];
};
Game_Event.prototype.needsUpdateConduction = function () {
    if (!this.isConductor() && !this.hasCharge()) {
        return false;
    }
    this._conductCache = this._conductCache || {};
    if (this._conductCache.lastX !== this.x) {
        return true;
    }
    if (this._conductCache.lastY !== this.y) {
        return true;
    }
    if (this._conductCache.jumpHeight !== this.jumpHeight()) {
        return true;
    }
    if (this._conductCache._pageIndex !== this._pageIndex) {
        return true;
    }
    return false;
};
Game_Event.prototype.requestRefreshConduction = function () {
    this._conductCache = this._conductCache || {};
    this._conductCache.lastX = this.x;
    this._conductCache.lastY = this.y;
    this._conductCache.jumpHeight = this.jumpHeight();
    this._conductCache._pageIndex = this._pageIndex;
    $gameMap.requestRefreshCondution();
};
Game_Event.prototype.turnOffAllConductions = function () {
    this.clearConductionState();
};
Game_Event.prototype.spreadCharges = function () {
    const _0x5cb1a9 = this.getChargeTypes();
    for (const _0xab2dd7 of _0x5cb1a9) {
        this.spreadChargeType(_0xab2dd7);
    }
};
Game_Event.prototype.spreadChargeType = function (_0x1c5d61) {
    const _0x130605 = this.getConductDirections();
    for (const _0x19f9f7 of _0x130605) {
        this.spreadChargeAtLocation(_0x19f9f7, _0x1c5d61);
    }
};
Game_Event.prototype.spreadChargeAtLocation = function (_0x21b88d, _0x49375e) {
    const _0x47eafe = VisuMZ.EventChainReact.RelativeCoordinates(this, _0x21b88d);
    const _0x3d8a4a = -this._addedHitbox.left;
    const _0x310c4d = this._addedHitbox.right;
    const _0x189030 = -this._addedHitbox.up;
    const _0x42142d = this._addedHitbox.down;
    for (let _0xffb3d1 = _0x3d8a4a; _0xffb3d1 <= _0x310c4d; _0xffb3d1++) {
        for (let _0x100589 = _0x189030; _0x100589 <= _0x42142d; _0x100589++) {
            for (const _0x572e23 of _0x47eafe) {
                const _0x516f8c = (_0x572e23.x ?? -0x1) + _0xffb3d1;
                const _0x4ddf6c = (_0x572e23.y ?? -0x1) + _0x100589;
                $gameMap.spreadChargeAtCoordinate(_0x49375e, _0x516f8c, _0x4ddf6c);
            }
        }
    }
};
Game_Event.prototype.synchConductionStates = function () {
    const _0x3c03ac = this.getConductionTypes();
    for (const _0x3f67aa of _0x3c03ac) {
        const _0x1ae9b4 = this._conductTypes[_0x3f67aa] || [];
        const _0x5e0ddb = this.getConductionState(_0x3f67aa);
        this.eventChainReactSwitchChange(_0x1ae9b4, _0x5e0ddb);
    }
    this.clearConductionState();
    const _0x47be96 = SceneManager._scene;
    const _0x53648f = _0x47be96 ? _0x47be96._spriteset : null;
    if (_0x53648f) {
        const _0x41af31 = _0x53648f.findTargetSprite(this);
        if (_0x41af31) {
            _0x41af31.update();
        }
    }
};
Game_Event.prototype.needsUpdateDecay = function () {
    if ($gameMessage.isBusy()) {
        return false;
    }
    if (Imported.VisuMZ_2_FurnitureSystem && $gameMap.isFurnitureSystemMode()) {
        return false;
    }
    return this._decayOnDuration > 0x0 || this._decayOffDuration > 0x0;
};
Game_Event.prototype.updateDecay = function () {
    this.updateDecayOn();
    this.updateDecayOff();
};
Game_Event.prototype.updateDecayOn = function () {
    if (this._decayOnDuration <= 0x0) {
        return;
    }
    this._decayOnDuration--;
    if (this._decayOnDuration <= 0x0) {
        const _0x313cda = this._decaySwitchOn;
        this.eventChainReactSwitchChange(_0x313cda, true);
    }
};
Game_Event.prototype.updateDecayOff = function () {
    if (this._decayOffDuration <= 0x0) {
        return;
    }
    this._decayOffDuration--;
    if (this._decayOffDuration <= 0x0) {
        const _0x2f637b = this._decaySwitchOff;
        this.eventChainReactSwitchChange(_0x2f637b, false);
    }
};
Game_Event.prototype.canSubmergeShallow = function () {
    if (this._erased) {
        return false;
    }
    return this._submergeShallowOn.length > 0x0 || this._submergeShallowOff.length > 0x0;
};
Game_Event.prototype.canSubmergeDeep = function () {
    if (this._erased) {
        return false;
    }
    return this._submergeDeepOn.length > 0x0 || this._submergeDeepOff.length > 0x0;
};
Game_Event.prototype.canResurface = function () {
    if (this._erased) {
        return false;
    }
    return this._resurfaceOn.length > 0x0 || this._resurfaceOff.length > 0x0;
};
VisuMZ.EventChainReact.Game_Event_isMapPassable = Game_Event.prototype.isMapPassable;
Game_Event.prototype.isMapPassable = function (_0x8d18ba, _0x974962, _0x19e4af) {
    if (
        this.hasMoveOnlyRegions() &&
        !this.isMoveOnlyRegionPassable(_0x8d18ba, _0x974962, _0x19e4af)
    ) {
        return false;
    }
    if ($gameMap.isRegionAllowPass(_0x8d18ba, _0x974962, _0x19e4af, 'event')) {
        return true;
    }
    if ($gameMap.isRegionForbidPass(_0x8d18ba, _0x974962, _0x19e4af, 'event')) {
        return false;
    }
    if (this.isSubmergePassable(_0x8d18ba, _0x974962, _0x19e4af)) {
        return true;
    }
    return VisuMZ.EventChainReact.Game_Event_isMapPassable.call(
        this,
        _0x8d18ba,
        _0x974962,
        _0x19e4af
    );
};
Game_Event.prototype.isSubmergePassable = function (_0xb30bc0, _0x255a45, _0x59be3d) {
    const _0x5c1049 = $gameMap.roundXWithDirection(_0xb30bc0, _0x59be3d);
    const _0x2fbf63 = $gameMap.roundYWithDirection(_0x255a45, _0x59be3d);
    if ($gameMap.isBoatPassable(_0x5c1049, _0x2fbf63) && this.canSubmergeShallow()) {
        return true;
    }
    if (
        $gameMap.isShipPassable(_0x5c1049, _0x2fbf63) &&
        !$gameMap.isBoatPassable(_0x5c1049, _0x2fbf63) &&
        this.canSubmergeDeep()
    ) {
        return true;
    }
    return false;
};
Game_Event.prototype.needsUpdateSubmerge = function () {
    if (this._erased) {
        return false;
    }
    if (!this.canSubmergeShallow() && !this.canSubmergeDeep() && !this.canResurface()) {
        return false;
    }
    this._submergeCache = this._submergeCache || {};
    if (this._submergeCache.lastX !== this.x) {
        return true;
    }
    if (this._submergeCache.lastY !== this.y) {
        return true;
    }
    if (this._submergeCache.jumpHeight !== this.jumpHeight()) {
        return true;
    }
    if (this._submergeCache._pageIndex !== this._pageIndex) {
        return true;
    }
    return false;
};
Game_Event.prototype.updateSubmerge = function () {
    const _0x2796b6 = this.isSubmerged(
        this._submergeCache.lastX ?? this.x,
        this._submergeCache.lastY ?? this.y
    );
    const _0x3ff1db = this.inWaterTile(
        this._submergeCache.lastX ?? this.x,
        this._submergeCache.lastY ?? this.y
    );
    this._submergeCache.lastX = this.x;
    this._submergeCache.lastY = this.y;
    this._submergeCache.jumpHeight = this.jumpHeight();
    this._submergeCache._pageIndex = this._pageIndex;
    if ($gameMap.isBoatPassable(this.x, this.y) && this.canSubmergeShallow()) {
        this.eventChainReactSwitchChange(this._submergeShallowOn || [], true);
        this.eventChainReactSwitchChange(this._submergeShallowOff || [], false);
    }
    if (
        $gameMap.isShipPassable(this.x, this.y) &&
        !$gameMap.isBoatPassable(this.x, this.y) &&
        this.canSubmergeDeep()
    ) {
        this.eventChainReactSwitchChange(this._submergeDeepOn || [], true);
        this.eventChainReactSwitchChange(this._submergeDeepOff || [], false);
    }
    if (!_0x2796b6 && this.isSubmerged(this.x, this.y)) {
        SoundManager.playSubmerge();
    }
    if (_0x3ff1db && !this.isSubmerged(this.x, this.y)) {
        this.eventChainReactSwitchChange(this._resurfaceOn || [], true);
        this.eventChainReactSwitchChange(this._resurfaceOff || [], false);
        SoundManager.playSubmerge();
    }
};
Game_Event.prototype.isSubmerged = function (_0x393398, _0xa18c3d) {
    if (!this.canSubmergeShallow() && !this.canSubmergeDeep()) {
        return false;
    }
    return (
        $gameMap.isBoatPassable(_0x393398, _0xa18c3d) ||
        $gameMap.isShipPassable(_0x393398, _0xa18c3d)
    );
};
Game_Event.prototype.inWaterTile = function (_0x259126, _0x30866b) {
    return $gameMap.isWaterTile(_0x259126, _0x30866b) && this.isSubmerged(_0x259126, _0x30866b);
};
Game_Event.prototype.isBridge = function () {
    if (this._erased) {
        return false;
    }
    return this._bridge;
};
Sprite_Character.SUBMERGE = {
    updateFrequency: VisuMZ.EventChainReact.Settings.Submerge.updateFrequency || 0x4,
    oscillationDistance: VisuMZ.EventChainReact.Settings.Submerge.oscillationDistance || 0x8,
    oscillationSpeed: VisuMZ.EventChainReact.Settings.Submerge.oscillationSpeed || 0.05,
};
VisuMZ.EventChainReact.Sprite_Character_initialize = Sprite_Character.prototype.initialize;
Sprite_Character.prototype.initialize = function (_0x485a78) {
    VisuMZ.EventChainReact.Sprite_Character_initialize.call(this, _0x485a78);
    this.initSubmerged();
};
Sprite_Character.prototype.initSubmerged = function () {
    if (!this._character) {
        return;
    }
    if (this._character._erased) {
        return;
    }
    if (!this.canUpdateSubmergeFrame()) {
        return;
    }
    this._submergeRng = Math.randomInt(0xf4240);
    this._submergeHeight = this._character.submergedEffectOffset();
    this._submergeDistance = Sprite_Character.SUBMERGE.oscillationDistance;
};
VisuMZ.EventChainReact.Sprite_Character_updateFrame = Sprite_Character.prototype.updateFrame;
Sprite_Character.prototype.updateFrame = function () {
    VisuMZ.EventChainReact.Sprite_Character_updateFrame.call(this);
    if (this.canUpdateSubmergeFrame()) {
        this.updateSubmergeFrame();
    } else {
        this.clearSubmergeData();
    }
};
Sprite_Character.prototype.canUpdateSubmergeFrame = function () {
    return this._character && this._character.submergedEffect();
};
Sprite_Character.prototype.clearSubmergeData = function () {
    this._submergeRng = 0x0;
    this._submergeHeight = 0x0;
    this._submergeDistance = 0x0;
    this._submergeOffsetY = 0x0;
};
Sprite_Character.prototype.updateSubmergeFrame = function () {
    const _0x4cb959 = this._frame;
    this._submergeRng = this._submergeRng || Math.randomInt(0xf4240);
    const _0x3fe1a9 = this._submergeRng + Graphics.frameCount;
    this._submergeHeight = this._submergeHeight || 0x0;
    if (
        Graphics.frameCount % Sprite_Character.SUBMERGE.updateFrequency === 0x0 &&
        !this._character.isMoving()
    ) {
        if (this._character.submergedEffectOffset() > this._submergeHeight) {
            this._submergeHeight++;
        } else {
            if (this._character.submergedEffectOffset() < this._submergeHeight) {
                this._submergeHeight--;
            } else {
                this._submergeDistance = this._submergeDistance || 0x0;
                this._submergeDistance = Math.min(
                    this._submergeDistance + 0x1,
                    Sprite_Character.SUBMERGE.oscillationDistance
                );
            }
        }
    }
    let _0xd431a3 = _0x4cb959.height;
    _0xd431a3 -= this._submergeHeight || 0x0;
    if (this._character.submergedEffectOffset() === this._submergeHeight) {
        const _0x413d08 = Sprite_Character.SUBMERGE.oscillationSpeed;
        const _0x254df2 = Math.round((this._submergeDistance || 0x0) / 0x2);
        this._submergeOffsetY = Math.floor(Math.cos(_0x3fe1a9 * _0x413d08) * _0x254df2 + _0x254df2);
        _0xd431a3 -= this._submergeOffsetY;
    }
    this.setFrame(_0x4cb959.x, _0x4cb959.y, _0x4cb959.width, _0xd431a3);
};
VisuMZ.EventChainReact.Sprite_Character_updatePosition = Sprite_Character.prototype.updatePosition;
Sprite_Character.prototype.updatePosition = function () {
    VisuMZ.EventChainReact.Sprite_Character_updatePosition.call(this);
    if (this.canUpdateSubmergedOffset()) {
        this.updateSubmergedOffset();
    }
};
Sprite_Character.prototype.canUpdateSubmergedOffset = function () {
    if (SceneManager._scene) {
        return false;
    }
    if (SceneManager._scene._spriteset) {
        return false;
    }
    if (!this._character) {
        return false;
    }
    if (!this._character.isNormalPriority()) {
        return false;
    }
    const _0x93b4af = this._character.x;
    const _0x4b6cac = this._character.y;
    return $gameMap.isWaterTile(_0x93b4af, _0x4b6cac);
};
Sprite_Character.prototype.updateSubmergedOffset = function () {
    const _0xfc63bf = this._character.x;
    const _0x39ffe7 = this._character.y;
    const _0x19f9d8 = $gameTemp.submergedEventsXy(_0xfc63bf, _0x39ffe7);
    if (_0x19f9d8.length <= 0x0) {
        return;
    }
    const _0x565fc9 = SceneManager._scene._spriteset;
    if (!_0x565fc9) {
        return;
    }
    const _0x17966e = _0x19f9d8.map(_0x2852b3 => _0x565fc9.findTargetSprite(_0x2852b3));
    if (_0x17966e.length <= 0x0) {
        return;
    }
    const _0x4acf47 = Math.max(
        ..._0x17966e.map(_0x15b84d => _0x15b84d._submergeOffsetY || 0x0),
        0x0
    );
    this.y += _0x4acf47 - this._character.shiftY();
};
