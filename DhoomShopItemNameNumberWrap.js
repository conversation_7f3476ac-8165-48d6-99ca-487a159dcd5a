//=============================================================================
// DhoomShopItemNameNumberWrap.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_ShopItemNameNumberWrap = '1.0';

var Dhoom = Dhoom || {};
Dhoom.ShopItemNameNumberWrap = Dhoom.ShopItemNameNumberWrap || {};
/*:
 * @plugindesc Dhoom ShopItemNameNumberWrap v1.0 - 03/09/2024
 * <AUTHOR> - drd-workshop.blogspot.com | DrDhoom#8315
 *
 * @help Wrap item name text on Window_ShopNumber.
 */

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom ShopItemNameNumberWrap/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.ShopItemNameNumberWrap.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.ShopItemNameNumberWrap[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.ShopItemNameNumberWrap.loadParameters();

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ShopNumber
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_drawMoreCurrenciesItem =
    Window_ShopNumber.prototype.drawMoreCurrenciesItem;
Window_ShopNumber.prototype.drawMoreCurrenciesItem = function () {
    this._drawingMoreCurrenciesItem = true;
    Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_drawMoreCurrenciesItem.apply(this, arguments);
    this._drawingMoreCurrenciesItem = false;
};

Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_drawItemName =
    Window_ShopNumber.prototype.drawItemName;
Window_ShopNumber.prototype.drawItemName = function (item, x, y, width) {
    if (this._drawingMoreCurrenciesItem) {
        const ip = this.itemPadding();
        width = ip * 2 + Math.ceil((this.innerWidth - ip * 5) / 3) + 52;
    }
    this._wrapTextEx = true;
    Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_drawItemName.call(this, item, x, y, width);
    this._wrapTextEx = false;
};

Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_textSizeEx = Window_ShopNumber.prototype.textSizeEx;
Window_ShopNumber.prototype.textSizeEx = function (text) {
    const temp = this._wrapTextEx;
    this._wrapTextEx = false;
    var result = Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_textSizeEx.call(this, text);
    this._wrapTextEx = temp;
    return result;
};

Window_ShopNumber.prototype.drawTextEx = function (text, x, y, width) {
    if (this._wrapTextEx) {
        text = this.convertEscapeCharacters(text);
        this.resetFontSettings();
        var texts = this.sliceText(text, width);
        this.resetFontSettings();
        this._isDrawingCustomText = true;
        var lh = this.contents.fontSize + 2;
        var spacing = 0;
        var sy = 0;
        for (var i = 0; i < texts.length; i++) {
            var sx = this.contents.outlineWidth;
            Window_Base.prototype.drawTextEx.call(this, texts[i], x + sx, y + sy);
            sy += lh + spacing;
        }
        this._isDrawingCustomText = false;
    } else {
        Window_Base.prototype.drawTextEx.call(this, x, y, width);
    }
};

Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_resetFontSettings =
    Window_ShopNumber.prototype.resetFontSettings;
Window_ShopNumber.prototype.resetFontSettings = function () {
    if (this._isDrawingCustomText) return;
    Dhoom.ShopItemNameNumberWrap.Window_ShopNumber_resetFontSettings.call(this);
};

Window_ShopNumber.prototype.sliceText = function (text, width) {
    width = width || this.contents.width;
    var result = [];
    var texts = text.split(' ');
    var t = '';
    var s = null;
    while (texts.length) {
        var c = texts.shift();
        if (c.contains('\n')) {
            s = c.split('\n');
            c = s[0];
        }
        if (this.textSizeEx((t + ' ' + c).trim()).width <= width) {
            t = (t + ' ' + c).trim();
        } else if (t) {
            result.push(t);
            t = c;
        }
        if (s) {
            result.push(t);
            t = s[1];
            s = null;
        }
    }
    if (t) result.push(t);
    if (result.length === 0) result.push(text);
    return result;
};
