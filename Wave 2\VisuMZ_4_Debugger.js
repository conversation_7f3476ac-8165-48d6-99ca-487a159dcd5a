//=============================================================================
// VisuStella MZ - Debugger
// VisuMZ_4_Debugger.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_Debugger = true;

var VisuMZ = VisuMZ || {};
VisuMZ.Debugger = VisuMZ.Debugger || {};
VisuMZ.Debugger.version = 1.02;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.02] [Debugger]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Debugger_VisuStella_MZ
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The RPG Maker MZ default debug menu (accessible through the F9 key during a
 * play test session) only has switch and variable manipulation options, which
 * might not be enough for some game developers. This plugin adds in a new
 * debug menu with access to launching Common Events on the go, teleporting to
 * any desired map, quick options, initiating battles, modifying quantities for
 * items, weapons, armors, altering self switches, and applying buffs, debuffs,
 * and states. Furthermore, this plugin also makes the debug menu accessible
 * from anywhere.
 *
 * Features include all (but not limited to) the following:
 *
 * - Access the Debug Menu (F9 key) from anywhere instead of just from the
 *   map scene.
 * - Switches and Variables are color-coded to make it easier to spot which
 *   ones are on, off, unnamed, and on while unnamed.
 * - Debug Menu's Common Event option allows you to launch any desired Common
 *   Event while on the map or in battle.
 * - The "Teleport" option lets you traverse through the game's various maps
 *   with ease.
 * - The "Quick" command lets you access a variety of options that may come in
 *   handy during play testing.
 * - "Battle" option allows you to initiate a battle with preemptive modifiers,
 *   surprise attack, or neither.
 * - The item, weapon, and armor menus allow you to adjust their quantities
 *   held by the party easily.
 * - Adjust Self Switches A, B, C, D through the "Map Events" command.
 * - "Buffs & States" command allows you to apply or remove buffs, debuffs, and
 *   states while also altering their turns, stacks, and more.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * F9 Button
 *
 * - The F9 button will now call the new debug menu. It's now accessible from
 * any scene. The old debug menu still exists but is now unaccessible normally
 * through the F9 button as it's been reassigned.
 *
 * ---
 *
 * ============================================================================
 * Debugger Menu: Switches
 * ============================================================================
 *
 * This is the standard Switch adjustment menu you're used to in default RPG
 * Maker MZ. Use this menu to turn specific switches ON or OFF mid-game.
 *
 * - The main differences here are that the menu is color-coded to show
 *   [ON] and [OFF] states better.
 * - Unnamed switches that are turned [ON] will have their names displayed as
 *   "! ATTENTION !" to draw attention. This is in case you may have been using
 *   a specific switch that you've forgotten to name, and you wouldn't want to
 *   override its properties in a future event.
 *
 * ============================================================================
 * Debugger Menu: Variables
 * ============================================================================
 *
 * This is the standard Variables adjustment menu you're used to in default RPG
 * Maker MZ. Use this menu to change the value stored inside variables.
 *
 * - Unlike the default RPG Maker MZ Debug Menu's variable controller, here
 *   you can alter the variable values in larger increments than 1 or 10.
 * - You can even select the variable and type in the number you want it to be
 *   to allow for quicker adjustments.
 * - Unnamed variables that have non-0 values will have their names displayed
 *   as "! ATTENTION !" to draw attention. This is in case you may have been
 *   using a specific variable that you've forgotten to name, and you wouldn't
 *   want to override its properties in a future event.
 *
 * ============================================================================
 * Debugger Menu: Common Events
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - This option accessible from the map scene only.
 * - This menu allows you to launch specific common events.
 * - TIP: You can use this to create your own debug commands that involve
 *   eventing.
 *
 * ============================================================================
 * Debugger Menu: Teleport
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - This option accessible from the map scene only.
 * - Select a map to teleport to, then select the place to put down the player
 *   character on that map.
 * - The left column lists the maps in the order shown in the MZ editor.
 * - The right column lists the maps in numeric order by Map ID.
 *
 * ============================================================================
 * Debugger Menu: Quick
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - Options found in this menu will vary depending on which scene you're
 *   currently located in.
 * - Select from a variety of JavaScript coded commands that enable faster
 *   testing for your game.
 * - Commands can be added/removed/altered from the Plugin Parameters.
 *
 * ============================================================================
 * Debugger Menu: Battle
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - This option accessible from the map scene only.
 * - Enter a battle with the selected Troop.
 * - You can choose to start the battle with a Preemptive bonus, a Surprise
 *   Attack penalty, or neither.
 *
 * ============================================================================
 * Debugger Menu: Items, Weapons, Armors
 * ============================================================================
 *
 * These new menus added by this plugin that were not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - Adjust the quantity of items, weapons, and/or armors found in the game
 *   project.
 * - Unnamed items and items with ----- in their names are automatically
 *   filtered from the list to reduce bloat.
 * - You can alter the amount of gold the party is holding, too.
 * - When hovering over the central gold, item, weapon, or armor option, you
 *   can type in the exact number of that object you wish.
 * - For weapons and armors, the numbers available represent the amount of
 *   the object currently being carried by the party. Any equipped weapons or
 *   armors do not count towards the quantity.
 *
 * ============================================================================
 * Debugger Menu: Map Events
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - This option accessible from the map scene only.
 * - This menu allows you to adjust the A, B, C, D Self Switches an event has.
 * - You can also erase events from this menu.
 * - This does not give you control over the Self Switches and Self Variables
 *   added through other plugins due to how limitless they are.
 *
 * ============================================================================
 * Debugger Menu: Buffs & States
 * ============================================================================
 *
 * This is a new menu added by this plugin that was not originally available in
 * the default RPG Maker MZ Debug Menu.
 *
 * - Lets you select a specific party member (or enemy if in battle) to alter
 *   their current buffs, debuffs, and/or states.
 * - For buffs, debuffs, and states, you can alter how many turns are remaining
 *   if the battler is currently affected by it.
 * - You can change the amount of buff or debuff stacks a battler has.
 * - If using the VisuStella MZ Skills & States Core, you can alter the custom
 *   value assigned to the state if it's a numeric value.
 *
 * ============================================================================
 * Plugin Parameters: Quick Commands
 * ============================================================================
 *
 * A list of commands and their JS code to add to the debug menu's
 * Quick option. You can add in any command you need here.
 *
 * ---
 *
 * Quick Command
 *
 *   Name:
 *   - Command name for the Quick command.
 *
 *   Icon:
 *   - Icon index used for the Quick command.
 *
 *   Help:
 *   - Help description displayed for this Quick command.
 *
 *   Close Debugger on Select:
 *   - Close the debugger upon running the action?
 *
 *   JS: Visibility:
 *   - JavaScript code to determine the visibility of this command.
 *
 *   JS: Action:
 *   - JavaScript code that runs upon selecting this command.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * - Yanfly
 * - Arisu
 * - Olivia
 * - Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.02: January 8, 2021
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.01: August 30, 2020
 * * Bug Fixes!
 * ** Deleted maps will have their ID's sorted out. Fix made by Shaz.
 *
 * Version 1.00: August 21, 2020
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Debugger
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Quick:arraystruct
 * @text Quick Commands
 * @type struct<Quick>[]
 * @desc A list of commands and their JS code to add to the debug menu's Quick option.
 * @default ["{\"Name:str\":\"Open Main Menu\",\"Icon:num\":\"75\",\"Help:json\":\"\\\"Forces open the Main Menu.\\\\nWARNING: This may disrupt any events going on currently.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneMainMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nSceneManager.push(Scene_Menu);\\\"\"}","{\"Name:str\":\"Enable Main Menu\",\"Icon:num\":\"73\",\"Help:json\":\"\\\"Enables Main Menu access.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneMainMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\n$gameSystem.enableMenu();\\\"\"}","{\"Name:str\":\"Disable Main Menu\",\"Icon:num\":\"74\",\"Help:json\":\"\\\"Disable Main Menu access.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneMainMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\n$gameSystem.disableMenu();\\\"\"}","{\"Name:str\":\"Open Save Menu\",\"Icon:num\":\"75\",\"Help:json\":\"\\\"Forces open the Save Menu.\\\\nWARNING: This may disrupt any events going on currently.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneSaveMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nSceneManager.push(Scene_Save);\\\"\"}","{\"Name:str\":\"Enable Save Menu\",\"Icon:num\":\"73\",\"Help:json\":\"\\\"Enables Save Menu access.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneSaveMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\n$gameSystem.enableSave();\\\\nif (SceneManager.isSceneMainMenu()) {\\\\n    SceneManager._scene._commandWindow.refresh();\\\\n}\\\"\"}","{\"Name:str\":\"Disable Save Menu\",\"Icon:num\":\"74\",\"Help:json\":\"\\\"Disable Save Menu access.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn !SceneManager.isSceneBattle() && !SceneManager.isSceneSaveMenu();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\n$gameSystem.disableSave();\\\\nif (SceneManager.isSceneMainMenu()) {\\\\n    SceneManager._scene._commandWindow.refresh();\\\\n}\\\"\"}","{\"Name:str\":\"Defeat Enemies\",\"Icon:num\":\"78\",\"Help:json\":\"\\\"Defeat all enemies by bringing their HP to 0!\\\\nEnemies with \\\\\\\"Immortal\\\\\\\" won't die, however.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameTroop.aliveMembers()) {\\\\n    member.setHp(0);\\\\n    if (member.isDead()) member.performCollapse();\\\\n}\\\\nBattleManager.checkBattleEnd();\\\"\"}","{\"Name:str\":\"Enemies to 1 HP\",\"Icon:num\":\"85\",\"Help:json\":\"\\\"Bring all alive enemies to 1 HP.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameTroop.aliveMembers()) {\\\\n    member.setHp(1);\\\\n}\\\"\"}","{\"Name:str\":\"Enemies to Full HP\",\"Icon:num\":\"84\",\"Help:json\":\"\\\"Restores all enemies to their full HP.\\\\nThis will revive dead enemies, too.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameTroop.members()) {\\\\n    member.setHp(member.mhp);\\\\n}\\\"\"}","{\"Name:str\":\"Defeat Allies\",\"Icon:num\":\"1\",\"Help:json\":\"\\\"Defeat all allies by bringing their HP to 0!\\\\nAllies with \\\\\\\"Immortal\\\\\\\" won't die, however.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.aliveMembers()) {\\\\n    member.setHp(0);\\\\n    if (member.isDead()) member.performCollapse();\\\\n    member.requestMotionRefresh();\\\\n}\\\\nBattleManager.checkBattleEnd();\\\"\"}","{\"Name:str\":\"Allies to 1 HP\",\"Icon:num\":\"48\",\"Help:json\":\"\\\"Bring all alive allies to 1 HP.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.aliveMembers()) {\\\\n    member.setHp(1);\\\\n    member.requestMotionRefresh();\\\\n}\\\"\"}","{\"Name:str\":\"Allies Status Recovery\",\"Icon:num\":\"72\",\"Help:json\":\"\\\"Clean all allies of their status effects.\\\\nThis will revive allies, too.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn SceneManager.isSceneBattle();\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.members()) {\\\\n    member.clearStates();\\\\n    member.requestMotionRefresh();\\\\n}\\\"\"}","{\"Name:str\":\"Allies to Full HP\",\"Icon:num\":\"84\",\"Help:json\":\"\\\"Restores all allies to their full HP.\\\\nThis will revive dead allies, too.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.members()) {\\\\n    member.setHp(member.mhp);\\\\n    member.requestMotionRefresh();\\\\n}\\\"\"}","{\"Name:str\":\"Allies to Full MP\",\"Icon:num\":\"79\",\"Help:json\":\"\\\"Restores all allies to their full MP.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.members()) {\\\\n    member.setMp(member.mmp);\\\\n}\\\"\"}","{\"Name:str\":\"Allies to Full TP\",\"Icon:num\":\"77\",\"Help:json\":\"\\\"Restores all allies to their full TP.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.members()) {\\\\n    member.setTp(member.maxTp());\\\\n}\\\"\"}","{\"Name:str\":\"+1 All Items\",\"Icon:num\":\"176\",\"Help:json\":\"\\\"Add x1 of every item except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataItems) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 1);\\\\n}\\\\nSoundManager.playUseItem();\\\"\"}","{\"Name:str\":\"+1 All Weapons\",\"Icon:num\":\"97\",\"Help:json\":\"\\\"Add x1 of every weapons except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataWeapons) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 1);\\\\n}\\\\nSoundManager.playEquip();\\\"\"}","{\"Name:str\":\"+1 All Armors\",\"Icon:num\":\"137\",\"Help:json\":\"\\\"Add x1 of every armor except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataArmors) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 1);\\\\n}\\\\nSoundManager.playMiss();\\\"\"}","{\"Name:str\":\"+10 All Items\",\"Icon:num\":\"176\",\"Help:json\":\"\\\"Add x10 of every item except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataItems) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 10);\\\\n}\\\\nSoundManager.playUseItem();\\\"\"}","{\"Name:str\":\"+10 All Weapons\",\"Icon:num\":\"97\",\"Help:json\":\"\\\"Add x10 of every weapons except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataWeapons) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 10);\\\\n}\\\\nSoundManager.playEquip();\\\"\"}","{\"Name:str\":\"+10 All Armors\",\"Icon:num\":\"137\",\"Help:json\":\"\\\"Add x10 of every armor except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataArmors) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, 10);\\\\n}\\\\nSoundManager.playMiss();\\\"\"}","{\"Name:str\":\"+Max All Items\",\"Icon:num\":\"176\",\"Help:json\":\"\\\"Add max of every item except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataItems) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playUseItem();\\\"\"}","{\"Name:str\":\"+Max All Weapons\",\"Icon:num\":\"97\",\"Help:json\":\"\\\"Add max of every weapons except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataWeapons) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playEquip();\\\"\"}","{\"Name:str\":\"+Max All Armors\",\"Icon:num\":\"137\",\"Help:json\":\"\\\"Add max of every armor except for those without names\\\\nor those named ----- serving as database placeholders.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataArmors) {\\\\n    if (!obj) continue;\\\\n    if (obj.name.trim() === '') continue;\\\\n    if (obj.name.match(/-----/i)) continue;\\\\n    $gameParty.gainItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playMiss();\\\"\"}","{\"Name:str\":\"Remove All Items\",\"Icon:num\":\"176\",\"Help:json\":\"\\\"Remove all items from the party's inventory.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataItems) {\\\\n    if (!obj) continue;\\\\n    $gameParty.loseItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playUseItem();\\\"\"}","{\"Name:str\":\"Remove All Weapons\",\"Icon:num\":\"97\",\"Help:json\":\"\\\"Remove all weapons from the party's inventory.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataWeapons) {\\\\n    if (!obj) continue;\\\\n    $gameParty.loseItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playEquip();\\\"\"}","{\"Name:str\":\"Remove All Armors\",\"Icon:num\":\"137\",\"Help:json\":\"\\\"Remove all armors from the party's inventory.\\\"\",\"CloseOut:eval\":\"false\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const obj of $dataArmors) {\\\\n    if (!obj) continue;\\\\n    $gameParty.loseItem(obj, $gameParty.maxItems(obj));\\\\n}\\\\nSoundManager.playMiss();\\\"\"}","{\"Name:str\":\"Party Level +1\",\"Icon:num\":\"73\",\"Help:json\":\"\\\"Raises all current party members' levels by 1.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(member.level + 1, false);\\\\n}\\\"\"}","{\"Name:str\":\"Party Level +10\",\"Icon:num\":\"73\",\"Help:json\":\"\\\"Raises all current party members' levels by 10.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(member.level + 10, false);\\\\n}\\\"\"}","{\"Name:str\":\"Party Level Max\",\"Icon:num\":\"73\",\"Help:json\":\"\\\"Raises all current party members' levels to maximum.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(member.maxLevel(), false);\\\\n}\\\"\"}","{\"Name:str\":\"Party Level -1\",\"Icon:num\":\"74\",\"Help:json\":\"\\\"Lowers all current party members' levels by 1.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(member.level - 1, false);\\\\n}\\\"\"}","{\"Name:str\":\"Party Level -10\",\"Icon:num\":\"74\",\"Help:json\":\"\\\"Lowers all current party members' levels by 10.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(member.level - 10, false);\\\\n}\\\"\"}","{\"Name:str\":\"Party Level To 1\",\"Icon:num\":\"74\",\"Help:json\":\"\\\"Lowers all current party members' levels to 1.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.allMembers()) {\\\\n    if (!member) continue;\\\\n    member.changeLevel(1, false);\\\\n}\\\"\"}","{\"Name:str\":\"Add All Skills\",\"Icon:num\":\"79\",\"Help:json\":\"\\\"Adds all skills for all party members.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.aliveMembers()) {\\\\n    if (!member) continue;\\\\n    for (const skill of $dataSkills) {\\\\n        if (!skill) continue;\\\\n        if (skill.name.trim() === '') continue;\\\\n        if (skill.name.match(/-----/i)) continue;\\\\n        member.learnSkill(skill.id);\\\\n    }\\\\n}\\\"\"}","{\"Name:str\":\"Add Typed Skills\",\"Icon:num\":\"79\",\"Help:json\":\"\\\"Adds all skills with matching skill types for each party member.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.aliveMembers()) {\\\\n    if (!member) continue;\\\\n    for (const skill of $dataSkills) {\\\\n        if (!skill) continue;\\\\n        if (skill.name.trim() === '') continue;\\\\n        if (skill.name.match(/-----/i)) continue;\\\\n        if (!member.skillTypes().includes(skill.stypeId)) continue;\\\\n        member.learnSkill(skill.id);\\\\n    }\\\\n}\\\"\"}","{\"Name:str\":\"Remove All Skills\",\"Icon:num\":\"79\",\"Help:json\":\"\\\"Adds all skills for all party members.\\\"\",\"CloseOut:eval\":\"true\",\"Visibility:func\":\"\\\"//Return Boolean\\\\nreturn true;\\\"\",\"Action:func\":\"\\\"//Perform Action\\\\nfor (const member of $gameParty.aliveMembers()) {\\\\n    if (!member) continue;\\\\n    for (const skill of $dataSkills) {\\\\n        if (!skill) continue;\\\\n        member.forgetSkill(skill.id);\\\\n    }\\\\n}\\\"\"}"]
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Struct Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Quick:
 *
 * @param Name:str
 * @text Name
 * @desc Command name for the Quick command.
 * @default Untitled
 *
 * @param Icon:num
 * @text Icon
 * @desc Icon index used for the Quick command.
 * @default 0
 *
 * @param Help:json
 * @text Help
 * @type note
 * @desc Help description displayed for this Quick command.
 * @default "Text1\nText2"
 *
 * @param CloseOut:eval
 * @text Close Debugger on Select
 * @type boolean
 * @on Close
 * @off Keep Open
 * @desc Close the debugger upon running the action?
 * @default true
 *
 * @param Visibility:func
 * @text JS: Visibility
 * @type note
 * @desc JavaScript code to determine the visibility of this command.
 * @default "//Return Boolean\nreturn true;"
 *
 * @param Action:func
 * @text JS: Action
 * @type note
 * @desc JavaScript code that runs upon selecting this command.
 * @default "//Perform Action\n"
 *
 */
//=============================================================================

const _0x3aad = [
    'processStateDisplayChange',
    'Changes\x20the\x20current\x20gold\x20to\x20maximum.',
    'armors',
    'stateDisplayChange',
    'Window_initialize',
    'initialize',
    'innerHeight',
    'Scene_Map_updateMain',
    'updateCallDebug',
    'open',
    'CoreEngine',
    'attention',
    'Window_Base_open',
    'battleActors',
    'test',
    'eventId',
    '\x5cI[%1]\x5cC[4]%2',
    'updateTouchInputControls',
    '487155mLkofw',
    'armor',
    '\x5cC[4]E%1:\x5cC[0]%2',
    'StackBuffMax',
    'processObjectHandling',
    'map',
    'StatesMain',
    'gold',
    'updateDebugHelp',
    'iconIndex',
    'weapon',
    ',\x20Y:\x20',
    'createDebugHelpWindow',
    'textSizeEx',
    '\x5cC[4]T%1:\x5cC[%4]\x5cI[%2]%3',
    'Help',
    'menu',
    'processGoldHandling',
    '_buffTurns',
    'Item',
    'toggleSwitch',
    'drawCurrentVariableValue',
    '_preemptive',
    'keyMapper',
    'includes',
    'Type\x20using\x20the\x20keyboard\x20the\x20turns\x20you\x20want\x20for\x20this\x20debuff.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'smoothSelect',
    'makeBuffCommandName',
    'symbol',
    'debuffName',
    'updateDebugChildren',
    'textColor',
    'processStateTurnsChange',
    'push',
    'Stacks:\x20%1',
    'switchId',
    '\x5cC[2]Surprised',
    'debuggerRegisterCurrentLocation',
    'lineHeight',
    'changeTextColor',
    'Runs\x20this\x20comment\x20event\x20at\x20the\x20next\x20available\x20opportunity.\x0aCommon\x20Events\x20without\x20any\x20events\x20are\x20excluded\x20from\x20the\x20listing.',
    'locate',
    'Relocates\x20you\x20to\x20in\x20front\x20of\x20this\x20event\x20on\x20the\x20map.',
    'Debugger',
    'Play\x20a\x20common\x20event\x20of\x20your\x20choice.',
    'debuffTurnsType',
    'clearRect',
    'Changes\x20the\x20current\x20gold\x20by\x20this\x20amount.',
    'parentId',
    'addState',
    '\x5cC[4]T%1:\x5cC[0]%2',
    'isStateAffected',
    'Icon',
    'A\x20set\x20of\x20quick\x20debug\x20actions.\x0aYou\x20can\x20modify\x20these\x20in\x20the\x20Plugin\x20Parameters.',
    'quick',
    'number',
    'itemWidth',
    'Apply\x20buffs,\x20debuffs,\x20and/or\x20states\x20to\x20actors\x20and/or\x20enemies.',
    'makeCommonEventName',
    'stateDisplayType',
    'Turns:\x20%1',
    '3djSZJG',
    'autorunCommonEvent',
    '10FEJsIG',
    'processVariableHandling',
    'OFF',
    'stateTurnsChange',
    'makeSwitchName',
    'Changes\x20the\x20current\x20weapon\x20quantity\x20to\x20maximum.',
    '+100',
    '_debugChildren',
    'drawItem',
    'stateName',
    'right',
    'createDisplayObjects',
    'processHandling',
    'updateLocation',
    '\x5cC[4]W%1:\x5cI[%2]\x5cC[0]%3',
    'createDebugMainWindow',
    'addNewState',
    'callDebugger',
    'commandName',
    'Name',
    'create',
    'down',
    'setTone',
    'Modify\x20weapon\x20quantities.',
    'makeObjectName',
    'Lets\x20you\x20manually\x20adjust\x20variables.',
    'zeroGold',
    '-10',
    'Starts\x20a\x20battle\x20with\x20this\x20troop.',
    'stateBattler',
    'isDebugCalled',
    'getOldValuesForTyping',
    '_debuggerTeleport',
    'getItemText',
    '16667nSorcW',
    'Window_Base_updateTone',
    'updateOpacity',
    'changeGold',
    'contentsOpacity',
    'Change',
    '556793bwzdxu',
    'makeMapIdName',
    '\x5cC[4]P%1:\x5cC[%5]\x5cI[%2]%3\x20%4',
    '_battler',
    'isPressed',
    'buffStackType',
    'Action',
    '\x5cI[82]Exit',
    'concat',
    'makeCommandList',
    'isRepeated',
    'removeChild',
    'resetFontSettings',
    '\x5cC[4]V%1:%2',
    'maxObject',
    'addCommand',
    'statesMain',
    'setText',
    'Settings',
    '_buttonType',
    'isOptionValid',
    'GoldIcon',
    'parameters',
    'goldChange',
    'Window_Base_close',
    'All\x20Actors',
    'buffIconIndex',
    'Numeric\x20Order\x20by\x20Map\x20ID',
    'debuggerHelp',
    'resetTextColor',
    'openBattlerWindow',
    'commandSymbol',
    'Quickly\x20teleport\x20to\x20a\x20map\x20you\x20like.',
    '_surprise',
    'CloseOut',
    '_active',
    'buffName',
    'update',
    'Parallel\x20Switch:\x20%1',
    'isPlaytest',
    'debuggerResume',
    'buffStackChange',
    'isMaxBuffAffected',
    'floor',
    'processBuffToggle',
    '\x5cI[83]Common\x20Events',
    'Starts\x20a\x20battle\x20with\x20this\x20troop,\x20with\x20the\x20player\x20party\x20having\x20turn\x20advantage.',
    'switch',
    'description',
    'debuggerOpenMain',
    'mapId',
    'deactivate',
    'makeBattleName',
    '\x5cI[210]Map\x20Events',
    'variables',
    'gainGold',
    '\x5cC[4]A%1:\x5cI[%2]\x5cC[0]%3',
    'STRUCT',
    'selfSwitchName',
    'clearCommandList',
    'debuggerRestoreKeyMapper',
    'cancel',
    'reserveTransfer',
    'makeEventName',
    '\x5cC[8][Erase]',
    'left',
    'VisuMZ_1_SkillsStatesCore',
    'processQuickAction',
    '_debugActive',
    'event',
    'debuggerPrepareKeyMapper',
    'isTriggered',
    'item',
    'trim',
    'debuggerRefreshAll',
    'SkillsStatesCore',
    'Changes\x20the\x20current\x20item\x20quantity\x20by\x20this\x20amount.',
    'debuffTurnsChange',
    'Erases\x20this\x20event\x20on\x20the\x20map.',
    '\x5cI[176]Items',
    'processDebuffTurnsChange',
    'quickCommand',
    'database',
    'makeVariableName',
    'Battle\x20Members',
    'Change\x20the\x20stack\x20quantity\x20for\x20this\x20debuff.',
    'shouldAutosave',
    'updateDebugger',
    'Switch',
    '\x5cC[%1]%2',
    'Sprite_Button_update',
    'Changes\x20the\x20current\x20variable\x20amount\x20by\x20this\x20amount.',
    'itemHeight',
    'itemLineRect',
    'VisuMZ_0_CoreEngine',
    'debuffStackType',
    'maxGold',
    'Type\x20using\x20the\x20keyboard\x20the\x20quantity\x20you\x20want\x20for\x20the\x20item.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'setHandlers',
    'trigger',
    'allMembers',
    'parse',
    '_opacityCounter',
    'numItems',
    'increaseBuff',
    'reserveCommonEvent',
    'changeItemColor',
    '\x5cC[%1][%2:%3]',
    '\x5cC[4]I%1:\x5cI[%2]\x5cC[0]%3',
    '_stateTurns',
    'direction',
    'max',
    'addDebugWindow',
    'items',
    'none',
    'findExt',
    'activate',
    'isMaxDebuffAffected',
    'exit',
    'weapons',
    'prototype',
    'prepareMapTeleport',
    'buffTurnsType',
    'padZero',
    'setOldValuesForTyping',
    'drawText',
    '\x5cI[%1]%2',
    'processBuffsStatesKeyboardHandling',
    'changePaintOpacity',
    'drawCurrencyQuantity',
    'playOk',
    'itemRectWithPadding',
    'index',
    'runCommonEvent',
    'setup',
    '_debugPressCount',
    'removeState',
    'unnamed',
    'Change\x20the\x20stack\x20quantity\x20for\x20this\x20buff.',
    'inBattle',
    'debuggerClose',
    'Toggle\x20the\x20debuff\x20on/off.',
    'Max',
    'Armor',
    'useDigitGrouping',
    '_debugKeyMapperRestore',
    'children',
    'Sprite_Button_initialize',
    'Window_BattleEnemy',
    'isSceneMainMenu',
    'teleport',
    'Done\x20with\x20the\x20debugger?',
    'getCoordinates',
    'Quick',
    'maxItems',
    'decreaseBuff',
    'removeBuff',
    'makeStateCommandName',
    'changeObject',
    'commonEvents',
    '_isDebugWindow',
    'createDebugWindows',
    '<Unnamed>',
    'delete',
    'JSON',
    'Toggle\x20the\x20state\x20on/off.',
    'active',
    'list',
    'isDebuffAffected',
    'playCursor',
    '\x5cI[137]Armors',
    'setHelpWindow',
    'center',
    'ext',
    'requestRefresh',
    'toUpperCase',
    'opacity',
    'Changes\x20the\x20current\x20armor\x20quantity\x20to\x20zero.',
    'EVAL',
    'setValue',
    'Debuff',
    'mapEditorOrder',
    'backspace',
    'currentSymbol',
    'deadMembers',
    'makeQuickName',
    'close',
    'debuggerOpenWindow',
    'playCancel',
    'isDebuggerChildExcluded',
    'processDebuffToggle',
    'ToMax',
    'aliveEnemies',
    'updateKeyInputControls',
    'addSpacingCommandText',
    'Gold',
    '\x5cI[97]Weapons',
    'drawTextEx',
    'updateChildren',
    'battle',
    'debuggerStop',
    '_debug%1Window',
    'updateMain',
    'switches',
    '-100',
    'autoRemovalTiming',
    'ARRAYSTR',
    'Alive\x20Enemies',
    'ARRAYNUM',
    'Enter\x20a\x20battle\x20of\x20your\x20choice.',
    '_debugStatesMainWindow',
    'changeVariable',
    'Changes\x20the\x20current\x20weapon\x20quantity\x20to\x20zero.',
    '_debugStatesBattlerWindow',
    'getStateDisplay',
    'refresh',
    'preemptiveBattle',
    'startBattle',
    'Lets\x20you\x20manually\x20adjust\x20switches.',
    'X:\x20',
    'Type\x20using\x20the\x20keyboard\x20the\x20quantity\x20you\x20want\x20for\x20gold.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'isCommandEnabled',
    'setStateDisplay',
    'Type\x20using\x20the\x20keyboard\x20the\x20turns\x20you\x20want\x20for\x20this\x20buff.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'isBuffAffected',
    'createDebugTypeWindow',
    'Change\x20the\x20turns\x20remaining\x20for\x20this\x20buff.',
    'width',
    'maxCols',
    'string',
    'allActors',
    'value',
    'length',
    'call',
    'openness',
    'modifyEvent',
    'StackDebuffMax',
    'debuffStackChange',
    'param',
    'setDebug',
    'Game_Picture_opacity',
    '_helpWindow',
    'isSceneBattle',
    'states',
    'cursorLeft',
    'version',
    'format',
    'gainItem',
    'processStateToggle',
    'currencyUnit',
    'Change\x20the\x20turns\x20remaining\x20for\x20this\x20state.',
    '991DgvGfM',
    'stateTurnsType',
    '\x5cI[2]Buffs\x20&\x20States',
    'refreshIndex',
    '[ON]',
    'updateCommandName',
    'Autorun\x20Switch:\x20%1',
    'eraseEvent',
    'contents',
    'finishProcess',
    'isSceneSaveMenu',
    'debuggerOpen',
    'Variable',
    'Dead\x20Enemies',
    'bind',
    'updateHelp',
    'makeStatesCommandList',
    'processDebuffStackChange',
    '1RHqJDk',
    'battleMembers',
    'relocatePlayerToCenter',
    'itemTextAlign',
    'members',
    'Buffs',
    'abs',
    'setBattler',
    'makeDebuffsCommandList',
    '\x5cC[0]%1',
    'processBuffTurnsChange',
    'itemRect',
    'Weapon',
    'substring',
    'Type\x20using\x20the\x20keyboard\x20the\x20custom\x20value\x20you\x20want\x20for\x20this\x20state.\x0aRequires\x20VisuMZ_1_SkillsStatesCore!\x20BACKSPACE\x20to\x20remove\x20last\x20number.',
    '\x5cC[4]I%1:\x5cC[0]%2',
    'filter',
    'preemptive',
    'clear',
    'ConvertParams',
    '\x5cI[87]Quick',
    'ARRAYFUNC',
    '_debugMainWindow',
    'clamp',
    'n/a',
    'constructor',
    '256354TGHtOQ',
    'CommonEvent',
    'mapID',
    'cancelLocation',
    '241081ryhIlI',
    'updateTone',
    'makeBuffsCommandList',
    'confirmAcceptLocation',
    '\x5cC[4]%1',
    'deadEnemies',
    'innerWidth',
    '\x5cC[5]',
    'ARRAYJSON',
    '552340wlJDpL',
    'selfSwitch',
    'varChange',
    'Type\x20using\x20the\x20keyboard\x20the\x20turns\x20you\x20want\x20for\x20this\x20state.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    '\x5cC[4]CE%1',
    '-----',
    '614daLaki',
    '_scene',
    '_list',
    'addWindow',
    'cursorRight',
    'variable',
    '\x5cC[4]MAP%1:\x5cC[0]%2',
    'drawCurrentObjectValue',
    'isDebug',
    'goToEvent',
    'None',
    '\x5cI[77]Battle',
    'name',
    '\x5cC[8]%1',
    'ARRAYSTRUCT',
    'debugger',
    '_debugCoordinatesWindow',
    'Scene_Base_updateChildren',
    '+10',
    'Type\x20using\x20the\x20keyboard\x20the\x20quantity\x20you\x20want\x20for\x20the\x20armor.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'Type\x20using\x20the\x20keyboard\x20the\x20stacks\x20you\x20want\x20for\x20this\x20debuff.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    'makeGoldCommand',
    'surprise',
    'height',
    'currentExt',
    'apply',
    '_buffs',
    'buffTurnsChange',
    '_debugHelpWindow',
    'processBuffStackChange',
    'Scene_Base_update',
    'colSpacing',
    'canvasToMapY',
    'Changes\x20the\x20current\x20item\x20quantity\x20to\x20zero.',
    'Scene_Base_initialize',
    '!\x20ATTENTION\x20!',
    'fittingHeight',
    'match',
    '_index',
    'setHandler',
    'addEntry',
    'findSymbol',
    'Type\x20using\x20the\x20keyboard\x20the\x20value\x20you\x20want\x20for\x20the\x20variable.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
    '\x5cC[29]Preemptive',
    'commonEvent',
    'isSceneMap',
];
const _0x10d0 = function (_0x287b80, _0xbf0416) {
    _0x287b80 = _0x287b80 - 0x185;
    let _0x3aadf1 = _0x3aad[_0x287b80];
    return _0x3aadf1;
};
const _0x273d22 = _0x10d0;
(function (_0x2cac37, _0x168b6e) {
    const _0x1a9be9 = _0x10d0;
    while (!![]) {
        try {
            const _0x25f42a =
                parseInt(_0x1a9be9(0x305)) +
                parseInt(_0x1a9be9(0x29e)) +
                parseInt(_0x1a9be9(0x24b)) +
                parseInt(_0x1a9be9(0x24f)) * parseInt(_0x1a9be9(0x231)) +
                parseInt(_0x1a9be9(0x2dd)) * -parseInt(_0x1a9be9(0x2ff)) +
                parseInt(_0x1a9be9(0x21f)) * parseInt(_0x1a9be9(0x25e)) +
                -parseInt(_0x1a9be9(0x2db)) * parseInt(_0x1a9be9(0x258));
            if (_0x25f42a === _0x168b6e) break;
            else _0x2cac37['push'](_0x2cac37['shift']());
        } catch (_0x52bf5a) {
            _0x2cac37['push'](_0x2cac37['shift']());
        }
    }
})(_0x3aad, 0x4fa17);
if (Utils[_0x273d22(0x319)](_0x273d22(0x29a))) {
    var label = _0x273d22(0x2c9),
        tier = tier || 0x0,
        dependencies = [],
        pluginData = $plugins[_0x273d22(0x241)](function (_0x5c54fc) {
            const _0x190e8a = _0x273d22;
            return (
                _0x5c54fc['status'] &&
                _0x5c54fc[_0x190e8a(0x335)][_0x190e8a(0x2b6)]('[' + label + ']')
            );
        })[0x0];
    ((VisuMZ[label][_0x273d22(0x317)] = VisuMZ[label][_0x273d22(0x317)] || {}),
        (VisuMZ[_0x273d22(0x244)] = function (_0x9490e4, _0x1a1e52) {
            const _0x2ec848 = _0x273d22;
            for (const _0x2fb73 in _0x1a1e52) {
                if (_0x2fb73[_0x2ec848(0x283)](/(.*):(.*)/i)) {
                    const _0x23a718 = String(RegExp['$1']),
                        _0x5a21ae = String(RegExp['$2'])[_0x2ec848(0x1d3)]()[_0x2ec848(0x34e)]();
                    let _0x1b9a16, _0x6ed501, _0x392768;
                    switch (_0x5a21ae) {
                        case 'NUM':
                            _0x1b9a16 =
                                _0x1a1e52[_0x2fb73] !== '' ? Number(_0x1a1e52[_0x2fb73]) : 0x0;
                            break;
                        case _0x2ec848(0x1f4):
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON['parse'](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501[_0x2ec848(0x2a3)](_0x485c9b =>
                                    Number(_0x485c9b)
                                )));
                            break;
                        case _0x2ec848(0x1d6):
                            _0x1b9a16 =
                                _0x1a1e52[_0x2fb73] !== '' ? eval(_0x1a1e52[_0x2fb73]) : null;
                            break;
                        case 'ARRAYEVAL':
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON[_0x2ec848(0x189)](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501[_0x2ec848(0x2a3)](_0x5b3cc2 =>
                                    eval(_0x5b3cc2)
                                )));
                            break;
                        case _0x2ec848(0x1c8):
                            _0x1b9a16 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON[_0x2ec848(0x189)](_0x1a1e52[_0x2fb73])
                                    : '';
                            break;
                        case _0x2ec848(0x257):
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON['parse'](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501['map'](_0x474786 =>
                                    JSON[_0x2ec848(0x189)](_0x474786)
                                )));
                            break;
                        case 'FUNC':
                            _0x1b9a16 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? new Function(JSON[_0x2ec848(0x189)](_0x1a1e52[_0x2fb73]))
                                    : new Function('return\x200');
                            break;
                        case _0x2ec848(0x246):
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON[_0x2ec848(0x189)](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501['map'](
                                    _0x41288f => new Function(JSON['parse'](_0x41288f))
                                )));
                            break;
                        case 'STR':
                            _0x1b9a16 =
                                _0x1a1e52[_0x2fb73] !== '' ? String(_0x1a1e52[_0x2fb73]) : '';
                            break;
                        case _0x2ec848(0x1f2):
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON['parse'](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501[_0x2ec848(0x2a3)](_0x1dd4f6 =>
                                    String(_0x1dd4f6)
                                )));
                            break;
                        case _0x2ec848(0x33e):
                            ((_0x392768 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON['parse'](_0x1a1e52[_0x2fb73])
                                    : {}),
                                (_0x1b9a16 = VisuMZ[_0x2ec848(0x244)]({}, _0x392768)));
                            break;
                        case _0x2ec848(0x26c):
                            ((_0x6ed501 =
                                _0x1a1e52[_0x2fb73] !== ''
                                    ? JSON[_0x2ec848(0x189)](_0x1a1e52[_0x2fb73])
                                    : []),
                                (_0x1b9a16 = _0x6ed501[_0x2ec848(0x2a3)](_0xc29d5b =>
                                    VisuMZ[_0x2ec848(0x244)]({}, JSON[_0x2ec848(0x189)](_0xc29d5b))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x9490e4[_0x23a718] = _0x1b9a16;
                }
            }
            return _0x9490e4;
        }),
        (_0x36d61f => {
            const _0x194d2b = _0x273d22,
                _0x2ee7bb = _0x36d61f[_0x194d2b(0x26a)];
            for (const _0x3e2249 of dependencies) {
                if (!Imported[_0x3e2249]) {
                    (alert(
                        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.'[
                            _0x194d2b(0x21a)
                        ](_0x2ee7bb, _0x3e2249)
                    ),
                        SceneManager[_0x194d2b(0x19a)]());
                    break;
                }
            }
            const _0x186c5b = _0x36d61f['description'];
            if (_0x186c5b[_0x194d2b(0x283)](/\[Version[ ](.*?)\]/i)) {
                const _0x5abfc5 = Number(RegExp['$1']);
                _0x5abfc5 !== VisuMZ[label][_0x194d2b(0x219)] &&
                    (alert(
                        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                            _0x194d2b(0x21a)
                        ](_0x2ee7bb, _0x5abfc5)
                    ),
                    SceneManager[_0x194d2b(0x19a)]());
            }
            if (_0x186c5b[_0x194d2b(0x283)](/\[Tier[ ](\d+)\]/i)) {
                const _0x145bed = Number(RegExp['$1']);
                _0x145bed < tier
                    ? (alert(
                          '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.'[
                              _0x194d2b(0x21a)
                          ](_0x2ee7bb, _0x145bed, tier)
                      ),
                      SceneManager[_0x194d2b(0x19a)]())
                    : (tier = Math['max'](_0x145bed, tier));
            }
            VisuMZ['ConvertParams'](VisuMZ[label][_0x194d2b(0x317)], _0x36d61f[_0x194d2b(0x31b)]);
        })(pluginData),
        (SceneManager[_0x273d22(0x216)] = function () {
            const _0x315b8b = _0x273d22;
            return (
                this[_0x315b8b(0x25f)] && this[_0x315b8b(0x25f)][_0x315b8b(0x24a)] === Scene_Battle
            );
        }),
        (SceneManager[_0x273d22(0x28b)] = function () {
            const _0x3085bc = _0x273d22;
            return this['_scene'] && this[_0x3085bc(0x25f)][_0x3085bc(0x24a)] === Scene_Map;
        }),
        (SceneManager[_0x273d22(0x1b9)] = function () {
            const _0x3b2973 = _0x273d22;
            return (
                this[_0x3b2973(0x25f)] && this[_0x3b2973(0x25f)][_0x3b2973(0x24a)] === Scene_Menu
            );
        }),
        (SceneManager[_0x273d22(0x229)] = function () {
            const _0xe77fa = _0x273d22;
            return this['_scene'] && this['_scene'][_0xe77fa(0x24a)] === Scene_Save;
        }),
        (TextManager['debuggerHelp'] = {
            switches: _0x273d22(0x1fe),
            variables: _0x273d22(0x2f6),
            commonEvents: _0x273d22(0x2ca),
            teleport: _0x273d22(0x325),
            quick: _0x273d22(0x2d3),
            battle: _0x273d22(0x1f5),
            items: 'Modify\x20item\x20quantities.',
            weapons: _0x273d22(0x2f4),
            armors: 'Modify\x20armor\x20quantities.',
            events: 'Modify\x20the\x20events\x20on\x20this\x20map.',
            states: _0x273d22(0x2d7),
            cancel: _0x273d22(0x1bb),
            switch: 'Toggles\x20this\x20Switch\x20between\x20ON\x20or\x20OFF.',
            variable: _0x273d22(0x288),
            varChange: _0x273d22(0x360),
            commonEvent: _0x273d22(0x2c6),
            map: 'Teleport\x20to\x20this\x20map.',
            startBattle: _0x273d22(0x2f9),
            preemptiveBattle: _0x273d22(0x333),
            surpriseBattle:
                'Starts\x20a\x20battle\x20with\x20this\x20troop,\x20with\x20the\x20enemy\x20party\x20having\x20turn\x20advantage.',
            gold: _0x273d22(0x200),
            goldChange: _0x273d22(0x2cd),
            goldToZero: 'Changes\x20the\x20current\x20gold\x20to\x20zero.',
            goldToMax: _0x273d22(0x28d),
            item: _0x273d22(0x185),
            itemChange: _0x273d22(0x351),
            itemToZero: _0x273d22(0x27f),
            itemToMax: 'Changes\x20the\x20current\x20item\x20quantity\x20to\x20maximum.',
            weapon: 'Type\x20using\x20the\x20keyboard\x20the\x20quantity\x20you\x20want\x20for\x20the\x20weapon.\x0aUse\x20BACKSPACE\x20to\x20remove\x20the\x20last\x20number.',
            weaponChange:
                'Changes\x20the\x20current\x20weapon\x20quantity\x20by\x20this\x20amount.',
            weaponToZero: _0x273d22(0x1f8),
            weaponToMax: _0x273d22(0x2e2),
            armor: _0x273d22(0x271),
            armorChange: 'Changes\x20the\x20current\x20armor\x20quantity\x20by\x20this\x20amount.',
            armorToZero: _0x273d22(0x1d5),
            armorToMax: 'Changes\x20the\x20current\x20armor\x20quantity\x20to\x20maximum.',
            goToEvent: _0x273d22(0x2c8),
            selfSwitch:
                'Toggles\x20this\x20self\x20switch\x20for\x20this\x20event\x20on\x20the\x20map.',
            eraseEvent: _0x273d22(0x353),
            stateBattler:
                'Select\x20this\x20battler\x20to\x20modify\x20buffs,\x20debuffs,\x20and/or\x20states\x20for.',
            buffTurnsChange: _0x273d22(0x206),
            buffTurnsType: _0x273d22(0x203),
            buffName: 'Toggle\x20the\x20buff\x20on/off.',
            buffStackChange: _0x273d22(0x1ae),
            buffStackType: _0x273d22(0x272),
            debuffTurnsChange: 'Change\x20the\x20turns\x20remaining\x20for\x20this\x20debuff.',
            debuffTurnsType: _0x273d22(0x2b7),
            debuffName: _0x273d22(0x1b1),
            debuffStackChange: _0x273d22(0x35a),
            debuffStackType: _0x273d22(0x272),
            stateTurnsChange: _0x273d22(0x21e),
            stateTurnsType: _0x273d22(0x25b),
            stateName: _0x273d22(0x1c9),
            stateDisplayChange:
                'Change\x20the\x20custom\x20value\x20for\x20this\x20state.\x0aRequires\x20VisuMZ_1_SkillsStatesCore!',
            stateDisplayType: _0x273d22(0x23f),
        }),
        (TextManager[_0x273d22(0x26d)] = {
            attention: _0x273d22(0x281),
            unnamed: _0x273d22(0x1c6),
            autorunCommonEvent: _0x273d22(0x225),
            parallelCommonEvent: _0x273d22(0x32b),
            mapEditorOrder: 'Editor\x20Order',
            mapNumericOrder: _0x273d22(0x320),
            preemptive: _0x273d22(0x289),
            surprise: _0x273d22(0x2c2),
            eraseEvent: _0x273d22(0x345),
            allActors: _0x273d22(0x31e),
            battleActors: _0x273d22(0x359),
            aliveEnemies: _0x273d22(0x1f3),
            deadEnemies: _0x273d22(0x22c),
        }),
        (Game_Temp[_0x273d22(0x19c)][_0x273d22(0x2c3)] = function () {
            const _0x1b9ac3 = _0x273d22;
            this[_0x1b9ac3(0x2fd)] = {
                x: $gamePlayer['x'],
                y: $gamePlayer['y'],
                mapID: $gameMap[_0x1b9ac3(0x337)](),
            };
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x214)] = Game_Picture[_0x273d22(0x19c)]['opacity']),
        (Game_Picture['prototype'][_0x273d22(0x1d4)] = function () {
            const _0x250eb3 = _0x273d22;
            if (SceneManager['_scene'][_0x250eb3(0x24a)] === Scene_DebugMapTeleport) return 0x0;
            return VisuMZ[_0x250eb3(0x2c9)][_0x250eb3(0x214)][_0x250eb3(0x20d)](this);
        }),
        (VisuMZ[_0x273d22(0x2c9)]['Scene_Base_initialize'] =
            Scene_Base[_0x273d22(0x19c)][_0x273d22(0x291)]),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x5c486f = _0x273d22;
            (VisuMZ[_0x5c486f(0x2c9)][_0x5c486f(0x280)][_0x5c486f(0x20d)](this),
                (this[_0x5c486f(0x349)] = ![]),
                (this[_0x5c486f(0x2e4)] = []));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x1c5)] = function () {
            const _0x4011bc = _0x273d22;
            (this[_0x4011bc(0x2aa)](),
                this['createDebugMainWindow'](),
                this[_0x4011bc(0x205)](_0x4011bc(0x1ef), _0x4011bc(0x35d), Window_DebugSwitches),
                this[_0x4011bc(0x205)](_0x4011bc(0x33b), _0x4011bc(0x22b), Window_DebugVariables),
                this[_0x4011bc(0x205)]('commonEvents', _0x4011bc(0x24c), Window_DebugCommonEvents),
                this['createDebugTypeWindow'](_0x4011bc(0x1ba), 'Teleport', Window_DebugTeleport),
                this[_0x4011bc(0x205)](_0x4011bc(0x2d4), _0x4011bc(0x1bd), Window_DebugQuick),
                this[_0x4011bc(0x205)]('battle', 'Battle', Window_DebugBattle),
                this[_0x4011bc(0x205)](_0x4011bc(0x195), _0x4011bc(0x2b1), Window_DebugItems),
                this['createDebugTypeWindow'](
                    _0x4011bc(0x19b),
                    _0x4011bc(0x23d),
                    Window_DebugWeapons
                ),
                this['createDebugTypeWindow'](
                    _0x4011bc(0x28e),
                    _0x4011bc(0x1b3),
                    Window_DebugArmors
                ),
                this[_0x4011bc(0x205)]('events', 'Events', Window_DebugMapEvents),
                this['createDebugTypeWindow'](
                    _0x4011bc(0x217),
                    'StatesBattler',
                    Window_DebugBuffsStatesBattler
                ),
                this[_0x4011bc(0x205)](
                    _0x4011bc(0x315),
                    _0x4011bc(0x2a4),
                    Window_DebugBuffsStatesMain
                ));
        }),
        (Scene_Base['prototype']['addDebugWindow'] = function (_0x59e6b0) {
            const _0x49f186 = _0x273d22;
            (this[_0x49f186(0x2e4)][_0x49f186(0x2bf)](_0x59e6b0), this['addChild'](_0x59e6b0));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x2aa)] = function () {
            const _0x567029 = _0x273d22,
                _0x5f2112 = new Rectangle(
                    0x0,
                    0x0,
                    Graphics[_0x567029(0x207)],
                    Window_Base[_0x567029(0x19c)][_0x567029(0x282)](0x2)
                );
            ((this[_0x567029(0x27a)] = new Window_Help(_0x5f2112)),
                this[_0x567029(0x27a)]['setDebug'](),
                this[_0x567029(0x194)](this[_0x567029(0x27a)]));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x2ec)] = function () {
            const _0x5f1ddd = _0x273d22,
                _0x508dfa = new Window_DebugMain();
            (this[_0x5f1ddd(0x194)](_0x508dfa),
                _0x508dfa['setDebug'](),
                _0x508dfa[_0x5f1ddd(0x1cf)](this[_0x5f1ddd(0x27a)]),
                _0x508dfa['setHandler']('cancel', this[_0x5f1ddd(0x1b0)]['bind'](this)),
                (this[_0x5f1ddd(0x247)] = _0x508dfa));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x205)] = function (
            _0xa0e8af,
            _0x3c10f6,
            _0x4d0b06
        ) {
            const _0x33ce77 = _0x273d22,
                _0x288c99 = new _0x4d0b06();
            (this[_0x33ce77(0x194)](_0x288c99),
                _0x288c99['setDebug'](),
                _0x288c99[_0x33ce77(0x1cf)](this['_debugHelpWindow']),
                _0x288c99[_0x33ce77(0x285)](
                    _0x33ce77(0x342),
                    this[_0x33ce77(0x336)][_0x33ce77(0x22d)](this, _0x288c99)
                ),
                (this[_0x33ce77(0x1ed)[_0x33ce77(0x21a)](_0x3c10f6)] = _0x288c99),
                this['_debugMainWindow']['setHandler'](
                    _0xa0e8af,
                    this[_0x33ce77(0x1df)][_0x33ce77(0x22d)](this, _0x3c10f6)
                ));
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x27c)] =
            Scene_Base[_0x273d22(0x19c)][_0x273d22(0x32a)]),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x32a)] = function () {
            const _0x11804d = _0x273d22;
            (VisuMZ['Debugger'][_0x11804d(0x27c)]['call'](this),
                this[_0x11804d(0x2fb)]() && this[_0x11804d(0x2ee)]());
        }),
        (Scene_Base[_0x273d22(0x19c)]['isDebugCalled'] = function () {
            const _0x52d5ec = _0x273d22;
            return Input['isTriggered']('debug') && $gameTemp[_0x52d5ec(0x32c)]() && $gameSystem;
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x26f)] =
            Scene_Base[_0x273d22(0x19c)][_0x273d22(0x1ea)]),
        (Scene_Base[_0x273d22(0x19c)]['updateChildren'] = function () {
            const _0x1b5e41 = _0x273d22;
            this[_0x1b5e41(0x349)]
                ? this[_0x1b5e41(0x2bc)]()
                : VisuMZ[_0x1b5e41(0x2c9)][_0x1b5e41(0x26f)][_0x1b5e41(0x20d)](this);
        }),
        (Scene_Base['prototype'][_0x273d22(0x2bc)] = function () {
            const _0xbb739 = _0x273d22;
            for (const _0x644c0d of this[_0xbb739(0x2e4)]) {
                if (_0x644c0d[_0xbb739(0x32a)]) _0x644c0d[_0xbb739(0x32a)]();
            }
        }),
        (Scene_Base[_0x273d22(0x19c)]['callDebugger'] = function () {
            const _0x5774be = _0x273d22;
            this[_0x5774be(0x2e4)][_0x5774be(0x20c)] > 0x0
                ? (SoundManager[_0x5774be(0x1e0)](), this[_0x5774be(0x1b0)]())
                : this[_0x5774be(0x22a)]();
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x22a)] = function () {
            const _0x113c7c = _0x273d22;
            this[_0x113c7c(0x2e4)][_0x113c7c(0x20c)] <= 0x0 && this[_0x113c7c(0x1c5)]();
            (this['debuggerStop'](), this[_0x113c7c(0x34b)]());
            for (const _0x495887 of this[_0x113c7c(0x2e4)]) {
                this['addChild'](_0x495887);
            }
            (this[_0x113c7c(0x27a)][_0x113c7c(0x295)](),
                this[_0x113c7c(0x27a)]['activate'](),
                this['_debugMainWindow'][_0x113c7c(0x1fb)](),
                this[_0x113c7c(0x336)]());
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x34b)] = function () {
            const _0x2d44f9 = _0x273d22;
            if (this[_0x2d44f9(0x1b5)]) return;
            ((this[_0x2d44f9(0x1b5)] = JsonEx['makeDeepCopy'](Input[_0x2d44f9(0x2b5)])),
                (Input[_0x2d44f9(0x2b5)][0x8] = _0x2d44f9(0x1da)),
                (Input[_0x2d44f9(0x2b5)][0x2e] = 'delete'),
                (Input['keyMapper'][0x30] = '0'),
                (Input[_0x2d44f9(0x2b5)][0x60] = '0'),
                (Input[_0x2d44f9(0x2b5)][0x31] = '1'),
                (Input[_0x2d44f9(0x2b5)][0x61] = '1'),
                (Input[_0x2d44f9(0x2b5)][0x32] = '2'),
                (Input[_0x2d44f9(0x2b5)][0x62] = '2'),
                (Input['keyMapper'][0x33] = '3'),
                (Input[_0x2d44f9(0x2b5)][0x63] = '3'),
                (Input[_0x2d44f9(0x2b5)][0x34] = '4'),
                (Input['keyMapper'][0x64] = '4'),
                (Input[_0x2d44f9(0x2b5)][0x35] = '5'),
                (Input[_0x2d44f9(0x2b5)][0x65] = '5'),
                (Input['keyMapper'][0x36] = '6'),
                (Input[_0x2d44f9(0x2b5)][0x66] = '6'),
                (Input['keyMapper'][0x37] = '7'),
                (Input[_0x2d44f9(0x2b5)][0x67] = '7'),
                (Input['keyMapper'][0x38] = '8'),
                (Input['keyMapper'][0x68] = '8'),
                (Input[_0x2d44f9(0x2b5)][0x39] = '9'),
                (Input[_0x2d44f9(0x2b5)][0x69] = '9'),
                (Input[_0x2d44f9(0x2b5)][0x6b] = '+'),
                (Input[_0x2d44f9(0x2b5)][0xbb] = '+'),
                (Input['keyMapper'][0x6d] = '-'),
                (Input[_0x2d44f9(0x2b5)][0xbd] = '-'));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x1ec)] = function () {
            const _0x310d2e = _0x273d22;
            ((this[_0x310d2e(0x328)] = ![]), (this[_0x310d2e(0x349)] = !![]));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x336)] = function (_0x406e09) {
            const _0x140527 = _0x273d22;
            (this[_0x140527(0x247)][_0x140527(0x295)](),
                this['_debugMainWindow'][_0x140527(0x198)]());
            if (!_0x406e09) return;
            (_0x406e09['close'](), _0x406e09[_0x140527(0x338)]());
        }),
        (Scene_Base[_0x273d22(0x19c)]['debuggerClose'] = function () {
            const _0xa438fe = _0x273d22;
            (this[_0xa438fe(0x32d)](), this[_0xa438fe(0x341)]());
            for (const _0x13b1d6 of this[_0xa438fe(0x2e4)]) {
                (_0x13b1d6[_0xa438fe(0x1de)](), _0x13b1d6[_0xa438fe(0x338)]());
            }
            if ($gamePlayer) $gamePlayer[_0xa438fe(0x1fb)]();
            if ($gameMap) $gameMap[_0xa438fe(0x1d2)]();
            if ($gameParty)
                for (const _0x4bb26e of $gameParty[_0xa438fe(0x235)]()) {
                    _0x4bb26e[_0xa438fe(0x1fb)]();
                }
            if ($gameTroop)
                for (const _0x513dda of $gameTroop[_0xa438fe(0x235)]()) {
                    _0x513dda[_0xa438fe(0x1fb)]();
                }
            for (const _0x50e858 of this[_0xa438fe(0x2e4)]) {
                if (!_0x50e858) continue;
                (_0x50e858[_0xa438fe(0x338)](),
                    _0x50e858['close'](),
                    this[_0xa438fe(0x310)](_0x50e858));
            }
            ((this[_0xa438fe(0x2e4)] = []), this[_0xa438fe(0x34f)](this));
        }),
        (Scene_Base['prototype'][_0x273d22(0x32d)] = function () {
            const _0x24d1e8 = _0x273d22;
            ((this['_active'] = !![]), (this[_0x24d1e8(0x349)] = ![]));
        }),
        (Scene_Base[_0x273d22(0x19c)][_0x273d22(0x341)] = function () {
            const _0x2ddc85 = _0x273d22;
            ((Input[_0x2ddc85(0x2b5)] = this[_0x2ddc85(0x1b5)]),
                (this[_0x2ddc85(0x1b5)] = undefined));
        }),
        (Scene_Base['prototype']['debuggerRefreshAll'] = function (_0x3a9dc0) {
            const _0x2f494c = _0x273d22;
            for (const _0x281793 of _0x3a9dc0[_0x2f494c(0x1b6)]) {
                if (!_0x281793) continue;
                if (this[_0x2f494c(0x1e1)](_0x281793)) continue;
                if (_0x281793['refresh']) _0x281793['refresh']();
                this[_0x2f494c(0x34f)](_0x281793);
            }
        }),
        (Scene_Base[_0x273d22(0x19c)]['isDebuggerChildExcluded'] = function (_0x3dac7f) {
            const _0x3d6490 = _0x273d22,
                _0x2ebc59 = [_0x3d6490(0x1b8)];
            if (_0x2ebc59['includes'](_0x3dac7f[_0x3d6490(0x24a)][_0x3d6490(0x26a)])) return !![];
            return ![];
        }),
        (Scene_Base[_0x273d22(0x19c)]['debuggerOpenWindow'] = function (_0x51d0ac) {
            const _0x355164 = _0x273d22,
                _0x2bf2b1 = this[_0x355164(0x1ed)[_0x355164(0x21a)](_0x51d0ac)];
            _0x2bf2b1 &&
                (_0x2bf2b1[_0x355164(0x295)](),
                _0x2bf2b1[_0x355164(0x198)](),
                _0x2bf2b1[_0x355164(0x1fb)](),
                this[_0x355164(0x247)][_0x355164(0x1de)]());
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x293)] = Scene_Map[_0x273d22(0x19c)]['updateMain']),
        (Scene_Map['prototype'][_0x273d22(0x1ee)] = function () {
            const _0x1bffea = _0x273d22;
            this[_0x1bffea(0x349)]
                ? $gameScreen[_0x1bffea(0x32a)]()
                : VisuMZ[_0x1bffea(0x2c9)][_0x1bffea(0x293)][_0x1bffea(0x20d)](this);
        }),
        (Scene_Map[_0x273d22(0x19c)][_0x273d22(0x294)] = function () {}));
    function Scene_DebugMapTeleport() {
        const _0xfdddc = _0x273d22;
        this['initialize'][_0xfdddc(0x277)](this, arguments);
    }
    ((Scene_DebugMapTeleport[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Scene_Map[_0x273d22(0x19c)]
    )),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)]['constructor'] = Scene_DebugMapTeleport),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)][_0x273d22(0x2e8)] = function () {
            const _0x2f8f1 = _0x273d22;
            (Scene_Map[_0x2f8f1(0x19c)][_0x2f8f1(0x2e8)][_0x2f8f1(0x20d)](this),
                this[_0x2f8f1(0x233)](),
                this['createDebugCoordinatesWindow']());
        }),
        (Scene_DebugMapTeleport['prototype'][_0x273d22(0x35b)] = function () {
            return ![];
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)]['relocatePlayerToCenter'] = function () {
            const _0x17d6e5 = _0x273d22;
            var _0x1a6727 = Math['floor']($dataMap['width'] / 0x2),
                _0x35df15 = Math['floor']($dataMap[_0x17d6e5(0x275)] / 0x2);
            $gamePlayer[_0x17d6e5(0x2c7)](_0x1a6727, _0x35df15);
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)]['createDebugCoordinatesWindow'] = function () {
            const _0x2df859 = _0x273d22;
            ((this['_debugCoordinatesWindow'] = new Window_DebugCoordinates()),
                this[_0x2df859(0x261)](this[_0x2df859(0x26e)]));
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)][_0x273d22(0x32a)] = function () {
            const _0xd35c6c = _0x273d22;
            (Scene_Base[_0xd35c6c(0x19c)][_0xd35c6c(0x32a)][_0xd35c6c(0x20d)](this),
                this[_0xd35c6c(0x29d)](),
                this[_0xd35c6c(0x1e5)]());
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)][_0x273d22(0x29d)] = function () {
            const _0x29039d = _0x273d22;
            if (!TouchInput[_0x29039d(0x34c)]()) return;
            var _0x3fbf57 = $gameMap['canvasToMapX'](TouchInput['x']),
                _0x45ff96 = $gameMap[_0x29039d(0x27e)](TouchInput['y']);
            _0x3fbf57 === $gamePlayer['x'] && _0x45ff96 === $gamePlayer['y']
                ? this[_0x29039d(0x252)]()
                : $gamePlayer[_0x29039d(0x2c7)](_0x3fbf57, _0x45ff96);
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)]['updateKeyInputControls'] = function () {
            const _0x4ad248 = _0x273d22;
            if (Input[_0x4ad248(0x30f)](_0x4ad248(0x2f2))) this['updateLocation'](0x0, 0x1);
            if (Input['isRepeated']('left')) this['updateLocation'](-0x1, 0x0);
            if (Input[_0x4ad248(0x30f)](_0x4ad248(0x2e7))) this[_0x4ad248(0x2ea)](0x1, 0x0);
            if (Input['isRepeated']('up')) this['updateLocation'](0x0, -0x1);
            if (Input[_0x4ad248(0x34c)]('ok')) this[_0x4ad248(0x252)]();
            if (Input[_0x4ad248(0x34c)](_0x4ad248(0x342))) this[_0x4ad248(0x24e)]();
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)][_0x273d22(0x2ea)] = function (
            _0xd15d5e,
            _0x2d0812
        ) {
            const _0x2c9ad5 = _0x273d22;
            var _0x15443f = ($gamePlayer['x'] + _0xd15d5e)[_0x2c9ad5(0x248)](
                    0x0,
                    $dataMap[_0x2c9ad5(0x207)] - 0x1
                ),
                _0x153fa5 = ($gamePlayer['y'] + _0x2d0812)[_0x2c9ad5(0x248)](
                    0x0,
                    $dataMap[_0x2c9ad5(0x275)] - 0x1
                );
            $gamePlayer[_0x2c9ad5(0x2c7)](_0x15443f, _0x153fa5);
        }),
        (Scene_DebugMapTeleport[_0x273d22(0x19c)][_0x273d22(0x252)] = function () {
            const _0x465503 = _0x273d22;
            (SoundManager[_0x465503(0x1a6)](), SceneManager['push'](Scene_Map));
        }),
        (Scene_DebugMapTeleport['prototype'][_0x273d22(0x24e)] = function () {
            const _0x22994b = _0x273d22;
            SoundManager['playCancel']();
            const _0x29110f = $gameTemp[_0x22994b(0x2fd)];
            var _0x3d54fa = _0x29110f['x'],
                _0x5e7a61 = _0x29110f['y'],
                _0x347de3 = _0x29110f[_0x22994b(0x24d)];
            ($gamePlayer[_0x22994b(0x343)](_0x347de3, _0x3d54fa, _0x5e7a61, 0x2, 0x0),
                SceneManager[_0x22994b(0x2bf)](Scene_Map));
        }),
        (Scene_DebugMapTeleport['prototype'][_0x273d22(0x22a)] = function () {}),
        (VisuMZ['Debugger'][_0x273d22(0x1b7)] = Sprite_Button[_0x273d22(0x19c)]['initialize']),
        (Sprite_Button[_0x273d22(0x19c)][_0x273d22(0x291)] = function (_0x334e00) {
            const _0x3acd9c = _0x273d22;
            (VisuMZ[_0x3acd9c(0x2c9)][_0x3acd9c(0x1b7)][_0x3acd9c(0x20d)](this, _0x334e00),
                (this[_0x3acd9c(0x1ab)] = 0x0));
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x35f)] = Sprite_Button[_0x273d22(0x19c)]['update']),
        (Sprite_Button['prototype'][_0x273d22(0x32a)] = function () {
            const _0x19da37 = _0x273d22;
            (VisuMZ[_0x19da37(0x2c9)][_0x19da37(0x35f)][_0x19da37(0x20d)](this),
                this[_0x19da37(0x35c)]());
        }),
        (Sprite_Button[_0x273d22(0x19c)]['updateDebugger'] = function () {
            const _0xfd95de = _0x273d22;
            if (this[_0xfd95de(0x318)] !== _0xfd95de(0x2ae)) return;
            if (!$gameTemp['isPlaytest']()) return;
            if (!SceneManager[_0xfd95de(0x28b)]()) return;
            this[_0xfd95de(0x309)]()
                ? (this['_debugPressCount']++,
                  this[_0xfd95de(0x1ab)] > 0xb4 &&
                      ((this[_0xfd95de(0x1ab)] = 0x0),
                      SceneManager[_0xfd95de(0x25f)][_0xfd95de(0x22a)]()))
                : (this[_0xfd95de(0x1ab)] = 0x0);
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x290)] = Window['prototype'][_0x273d22(0x291)]),
        (Window[_0x273d22(0x19c)]['initialize'] = function () {
            const _0x1d5549 = _0x273d22;
            ((this[_0x1d5549(0x1c4)] = ![]),
                VisuMZ[_0x1d5549(0x2c9)][_0x1d5549(0x290)][_0x1d5549(0x20d)](this));
        }),
        (Window[_0x273d22(0x19c)][_0x273d22(0x213)] = function () {
            const _0x1e613a = _0x273d22;
            ((this[_0x1e613a(0x1c4)] = !![]),
                (this[_0x1e613a(0x20e)] = 0x0),
                (this[_0x1e613a(0x1ca)] = ![]));
        }),
        (Window[_0x273d22(0x19c)][_0x273d22(0x266)] = function () {
            const _0x265efe = _0x273d22;
            return this[_0x265efe(0x1c4)];
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x298)] = Window_Base['prototype'][_0x273d22(0x295)]),
        (Window_Base[_0x273d22(0x19c)][_0x273d22(0x295)] = function () {
            const _0x1c0715 = _0x273d22;
            VisuMZ[_0x1c0715(0x2c9)][_0x1c0715(0x298)][_0x1c0715(0x20d)](this);
            if (this[_0x1c0715(0x1c4)]) this['openness'] = 0xff;
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x31d)] = Window_Base['prototype']['close']),
        (Window_Base[_0x273d22(0x19c)][_0x273d22(0x1de)] = function () {
            const _0x53bdf6 = _0x273d22;
            VisuMZ[_0x53bdf6(0x2c9)][_0x53bdf6(0x31d)]['call'](this);
            if (this[_0x53bdf6(0x1c4)]) this[_0x53bdf6(0x20e)] = 0x0;
        }),
        (VisuMZ[_0x273d22(0x2c9)][_0x273d22(0x300)] = Window_Base['prototype'][_0x273d22(0x250)]),
        (Window_Base[_0x273d22(0x19c)]['updateTone'] = function () {
            const _0x45799a = _0x273d22;
            this['isDebug']()
                ? this[_0x45799a(0x2f3)](-0x32, -0x32, -0x32)
                : VisuMZ[_0x45799a(0x2c9)][_0x45799a(0x300)][_0x45799a(0x20d)](this);
        }));
    function Window_DebugCommand() {
        const _0x439217 = _0x273d22;
        this[_0x439217(0x291)](...arguments);
    }
    ((Window_DebugCommand[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_Command['prototype']
    )),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugCommand),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0xedb815 = _0x273d22,
                _0x530470 = Window_Base[_0xedb815(0x19c)][_0xedb815(0x282)](0x2),
                _0x123ca0 = new Rectangle(
                    0x0,
                    _0x530470,
                    Graphics[_0xedb815(0x207)],
                    Graphics[_0xedb815(0x275)] - _0x530470
                );
            (Window_Selectable['prototype']['initialize'][_0xedb815(0x20d)](this, _0x123ca0),
                this[_0xedb815(0x340)]());
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x1b4)] = function () {
            return !![];
        }),
        (Window_DebugCommand['prototype']['useDigitGroupingEx'] = function () {
            return ![];
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x234)] = function () {
            const _0x386e9b = _0x273d22;
            return _0x386e9b(0x1d0);
        }),
        (Window_DebugCommand['prototype'][_0x273d22(0x2e5)] = function (_0xe444ce) {
            const _0xc7c273 = _0x273d22,
                _0x238a5a = this['itemLineRect'](_0xe444ce);
            this[_0xc7c273(0x227)][_0xc7c273(0x2cc)](
                _0x238a5a['x'],
                _0x238a5a['y'],
                _0x238a5a[_0xc7c273(0x207)],
                _0x238a5a[_0xc7c273(0x275)]
            );
            const _0x3b040a = this[_0xc7c273(0x2ef)](_0xe444ce),
                _0x2881d6 = this[_0xc7c273(0x2ab)](_0x3b040a)[_0xc7c273(0x207)];
            this['changePaintOpacity'](this[_0xc7c273(0x201)](_0xe444ce));
            let _0x13ca57 = this[_0xc7c273(0x234)]();
            if (_0x13ca57 === _0xc7c273(0x2e7))
                this[_0xc7c273(0x1e9)](
                    _0x3b040a,
                    _0x238a5a['x'] + _0x238a5a[_0xc7c273(0x207)] - _0x2881d6,
                    _0x238a5a['y'],
                    _0x2881d6
                );
            else {
                if (_0x13ca57 === _0xc7c273(0x1d0)) {
                    const _0xb8743 =
                        _0x238a5a['x'] +
                        Math['floor']((_0x238a5a[_0xc7c273(0x207)] - _0x2881d6) / 0x2);
                    this[_0xc7c273(0x1e9)](_0x3b040a, _0xb8743, _0x238a5a['y'], _0x2881d6);
                } else this[_0xc7c273(0x1e9)](_0x3b040a, _0x238a5a['x'], _0x238a5a['y'], _0x2881d6);
            }
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x22e)] = function () {
            const _0x3d824d = _0x273d22;
            (Window_Command['prototype'][_0x3d824d(0x22e)][_0x3d824d(0x20d)](this),
                this['updateDebugHelp']());
        }),
        (Window_DebugCommand[_0x273d22(0x19c)]['updateDebugHelp'] = function () {
            const _0x490188 = _0x273d22;
            if (this[_0x490188(0x215)]) {
                const _0x4bb4f1 = TextManager[_0x490188(0x321)][this['currentSymbol']()] || '';
                this[_0x490188(0x215)][_0x490188(0x316)](_0x4bb4f1);
            }
        }),
        (Window_DebugCommand['prototype'][_0x273d22(0x1fb)] = function () {
            const _0x45d450 = _0x273d22;
            (Window_Command['prototype']['refresh'][_0x45d450(0x20d)](this),
                this['setHandlers'](),
                this['smoothSelect'](this['refreshIndex']()));
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {}),
        (Window_DebugCommand[_0x273d22(0x19c)]['refreshIndex'] = function () {
            return 0x0;
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x262)] = function (_0x2a39d5) {
            const _0x966cc2 = _0x273d22,
                _0x11cd14 = this[_0x966cc2(0x208)]();
            if (this['_index'] % _0x11cd14 < _0x11cd14 - 0x1) this[_0x966cc2(0x284)]++;
            else _0x2a39d5 && (this[_0x966cc2(0x284)] -= _0x11cd14 - 0x1);
            this[_0x966cc2(0x2b8)](this[_0x966cc2(0x284)]);
        }),
        (Window_DebugCommand[_0x273d22(0x19c)][_0x273d22(0x218)] = function (_0x2a49ac) {
            const _0x252b2f = _0x273d22,
                _0x2ae964 = this[_0x252b2f(0x208)]();
            if (this[_0x252b2f(0x284)] % _0x2ae964 > 0x0) this[_0x252b2f(0x284)]--;
            else _0x2a49ac && (this['_index'] += _0x2ae964 - 0x1);
            this[_0x252b2f(0x2b8)](this['_index']);
        }));
    function Window_DebugMain() {
        this['initialize'](...arguments);
    }
    ((Window_DebugMain[_0x273d22(0x19c)] = Object['create'](Window_DebugCommand[_0x273d22(0x19c)])),
        (Window_DebugMain['prototype']['constructor'] = Window_DebugMain),
        (Window_DebugMain[_0x273d22(0x19c)]['initialize'] = function () {
            const _0x2c2bfc = _0x273d22;
            Window_DebugCommand['prototype'][_0x2c2bfc(0x291)][_0x2c2bfc(0x20d)](this);
        }),
        (Window_DebugMain[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x3;
        }),
        (Window_DebugMain[_0x273d22(0x19c)][_0x273d22(0x361)] = function () {
            const _0x8b089 = _0x273d22;
            return Math['floor'](this[_0x8b089(0x292)] / 0x4);
        }),
        (Window_DebugMain[_0x273d22(0x19c)]['makeCommandList'] = function () {
            const _0x1d7813 = _0x273d22;
            (this[_0x1d7813(0x314)]('\x5cI[83]Switches', _0x1d7813(0x1ef)),
                this['addCommand']('\x5cI[83]Variables', _0x1d7813(0x33b)),
                this[_0x1d7813(0x314)](_0x1d7813(0x332), _0x1d7813(0x1c3)),
                this[_0x1d7813(0x314)](
                    '\x5cI[72]Teleport',
                    _0x1d7813(0x1ba),
                    SceneManager[_0x1d7813(0x28b)]()
                ),
                this['addCommand'](_0x1d7813(0x245), 'quick'),
                this[_0x1d7813(0x314)](
                    _0x1d7813(0x269),
                    _0x1d7813(0x1eb),
                    !SceneManager[_0x1d7813(0x216)]()
                ),
                this[_0x1d7813(0x314)](_0x1d7813(0x354), _0x1d7813(0x195)),
                this['addCommand'](_0x1d7813(0x1e8), _0x1d7813(0x19b)),
                this[_0x1d7813(0x314)](_0x1d7813(0x1ce), 'armors'),
                this['addCommand'](_0x1d7813(0x33a), 'events'),
                this[_0x1d7813(0x314)](_0x1d7813(0x221), 'states'),
                this['addCommand'](_0x1d7813(0x30c), _0x1d7813(0x342)));
        }),
        (Window_DebugMain[_0x273d22(0x19c)][_0x273d22(0x222)] = function () {
            const _0x25efdc = _0x273d22;
            return this[_0x25efdc(0x287)](_0x25efdc(0x2d4));
        }));
    function Window_DebugSwitches() {
        const _0x42c14f = _0x273d22;
        this[_0x42c14f(0x291)](...arguments);
    }
    ((Window_DebugSwitches[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugSwitches[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugSwitches),
        (Window_DebugSwitches['prototype'][_0x273d22(0x291)] = function () {
            const _0xd90433 = _0x273d22;
            Window_DebugCommand[_0xd90433(0x19c)]['initialize'][_0xd90433(0x20d)](this);
        }),
        (Window_DebugSwitches[_0x273d22(0x19c)][_0x273d22(0x234)] = function () {
            const _0x3e65e1 = _0x273d22;
            return _0x3e65e1(0x346);
        }),
        (Window_DebugSwitches[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x56e821 = _0x273d22,
                _0xe6506d = $dataSystem[_0x56e821(0x1ef)]['length'];
            for (let _0x431757 = 0x1; _0x431757 < _0xe6506d; _0x431757++) {
                this[_0x56e821(0x314)](
                    this[_0x56e821(0x2e1)](_0x431757),
                    'switch',
                    !![],
                    _0x431757
                );
            }
        }),
        (Window_DebugSwitches[_0x273d22(0x19c)]['makeSwitchName'] = function (_0x5e78fe) {
            const _0xd79f71 = _0x273d22;
            let _0x2ec4ee = $dataSystem['switches'][_0x5e78fe][_0xd79f71(0x34e)]();
            const _0x145321 = _0x2ec4ee[_0xd79f71(0x20c)] > 0x0,
                _0xfad35d = TextManager[_0xd79f71(0x26d)];
            $gameSwitches['value'](_0x5e78fe)
                ? (_0x2ec4ee = _0xd79f71(0x35e)['format'](
                      _0x145321 ? 0x11 : 0x12,
                      _0x145321 ? _0x2ec4ee : _0xfad35d['attention']
                  ))
                : (_0x2ec4ee = _0xd79f71(0x26b)[_0xd79f71(0x21a)](
                      _0x145321 ? _0x2ec4ee : _0xfad35d[_0xd79f71(0x1ad)]
                  ));
            let _0x2a5e71 = '\x5cC[4]S%1:%2'[_0xd79f71(0x21a)](
                _0x5e78fe['padZero'](0x4),
                _0x2ec4ee
            );
            return _0x2a5e71;
        }),
        (Window_DebugSwitches[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x5575db = _0x273d22;
            this[_0x5575db(0x285)](
                _0x5575db(0x334),
                this[_0x5575db(0x2b2)][_0x5575db(0x22d)](this)
            );
        }),
        (Window_DebugSwitches[_0x273d22(0x19c)]['drawItem'] = function (_0x575b60) {
            const _0x29951a = _0x273d22;
            ((this[_0x29951a(0x260)][_0x575b60][_0x29951a(0x26a)] = this[_0x29951a(0x2e1)](
                this[_0x29951a(0x260)][_0x575b60][_0x29951a(0x1d1)]
            )),
                Window_DebugCommand['prototype'][_0x29951a(0x2e5)][_0x29951a(0x20d)](
                    this,
                    _0x575b60
                ));
            const _0x7e7626 = this[_0x29951a(0x362)](_0x575b60),
                _0x536b81 = this[_0x29951a(0x260)][_0x575b60][_0x29951a(0x1d1)] || 0x0,
                _0x5d0dc7 = this[_0x29951a(0x2ab)](name)[_0x29951a(0x207)];
            ((_0x7e7626[_0x29951a(0x207)] /= 0x2),
                (_0x7e7626['x'] += _0x7e7626[_0x29951a(0x207)]),
                (_0x7e7626[_0x29951a(0x207)] /= 0x2),
                this['changeTextColor'](
                    ColorManager[_0x29951a(0x2bd)](
                        $gameSwitches[_0x29951a(0x20b)](_0x536b81) ? 0x11 : 0x0
                    )
                ),
                this[_0x29951a(0x1a1)](
                    _0x29951a(0x223),
                    _0x7e7626['x'],
                    _0x7e7626['y'],
                    _0x7e7626[_0x29951a(0x207)],
                    _0x29951a(0x1d0)
                ),
                (_0x7e7626['x'] += _0x7e7626[_0x29951a(0x207)]),
                this[_0x29951a(0x2c5)](
                    ColorManager[_0x29951a(0x2bd)](
                        $gameSwitches[_0x29951a(0x20b)](_0x536b81) ? 0x0 : 0x11
                    )
                ),
                this[_0x29951a(0x1a1)](
                    '[OFF]',
                    _0x7e7626['x'],
                    _0x7e7626['y'],
                    _0x7e7626[_0x29951a(0x207)],
                    _0x29951a(0x1d0)
                ),
                this[_0x29951a(0x311)]());
        }),
        (Window_DebugSwitches['prototype'][_0x273d22(0x2b2)] = function () {
            const _0x40ddef = _0x273d22,
                _0x32b320 = this[_0x40ddef(0x276)]();
            ($gameSwitches[_0x40ddef(0x1d7)](_0x32b320, !$gameSwitches['value'](_0x32b320)),
                (this[_0x40ddef(0x260)][this['index']()][_0x40ddef(0x26a)] =
                    this[_0x40ddef(0x2e1)](_0x32b320)),
                this[_0x40ddef(0x2e5)](this[_0x40ddef(0x1a8)]()),
                this['activate']());
        }));
    function Window_DebugVariables() {
        const _0x33f79c = _0x273d22;
        this[_0x33f79c(0x291)](...arguments);
    }
    ((Window_DebugVariables[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugVariables),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x1ce95f = _0x273d22;
            Window_DebugCommand[_0x1ce95f(0x19c)][_0x1ce95f(0x291)]['call'](this);
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x234)] = function () {
            return 'left';
        }),
        (Window_DebugVariables['prototype'][_0x273d22(0x208)] = function () {
            return 0x7;
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x2d6)] = function () {
            const _0x25ff46 = _0x273d22;
            return Math['floor'](this[_0x25ff46(0x255)] / 0xc);
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x23c)] = function (_0x4fc42c) {
            const _0x5d88eb = _0x273d22,
                _0x3c5436 = Window_DebugCommand[_0x5d88eb(0x19c)]['itemRect'][_0x5d88eb(0x20d)](
                    this,
                    _0x4fc42c
                );
            if (_0x4fc42c % 0x7 === 0x3)
                ((_0x3c5436[_0x5d88eb(0x207)] = this[_0x5d88eb(0x2d6)]() * 0x6),
                    (_0x3c5436['x'] = this[_0x5d88eb(0x2d6)]() * 0x3));
            else
                _0x4fc42c % 0x7 > 0x3 &&
                    (_0x3c5436['x'] =
                        this[_0x5d88eb(0x2d6)]() * (0x5 + (_0x4fc42c % 0x7)) +
                        this['colSpacing']());
            return _0x3c5436;
        }),
        (Window_DebugVariables['prototype'][_0x273d22(0x30e)] = function () {
            const _0x7f4036 = _0x273d22,
                _0x2d68dd = $dataSystem[_0x7f4036(0x33b)]['length'];
            for (let _0x4c13d2 = 0x1; _0x4c13d2 < _0x2d68dd; _0x4c13d2++) {
                (this[_0x7f4036(0x314)](_0x7f4036(0x1f0), _0x7f4036(0x25a), !![], [
                    _0x4c13d2,
                    -0x64,
                ]),
                    this['addCommand']('-10', _0x7f4036(0x25a), !![], [_0x4c13d2, -0xa]),
                    this[_0x7f4036(0x314)]('-1', _0x7f4036(0x25a), !![], [_0x4c13d2, -0x1]),
                    this[_0x7f4036(0x314)](
                        this[_0x7f4036(0x358)](_0x4c13d2),
                        _0x7f4036(0x263),
                        !![],
                        _0x4c13d2
                    ),
                    this[_0x7f4036(0x314)]('+1', 'varChange', !![], [_0x4c13d2, 0x1]),
                    this[_0x7f4036(0x314)](_0x7f4036(0x270), _0x7f4036(0x25a), !![], [
                        _0x4c13d2,
                        0xa,
                    ]),
                    this['addCommand'](_0x7f4036(0x2e3), 'varChange', !![], [_0x4c13d2, 0x64]));
            }
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x358)] = function (_0x1c5d07) {
            const _0x403f5d = _0x273d22;
            let _0x440313 = $dataSystem[_0x403f5d(0x33b)][_0x1c5d07][_0x403f5d(0x34e)]();
            const _0x1c9055 = _0x440313[_0x403f5d(0x20c)] > 0x0,
                _0x2ada31 = TextManager[_0x403f5d(0x26d)];
            $gameVariables[_0x403f5d(0x20b)](_0x1c5d07) !== 0x0
                ? (_0x440313 = _0x403f5d(0x35e)[_0x403f5d(0x21a)](
                      _0x1c9055 ? 0x11 : 0x12,
                      _0x1c9055 ? _0x440313 : _0x2ada31[_0x403f5d(0x297)]
                  ))
                : (_0x440313 = _0x403f5d(0x26b)[_0x403f5d(0x21a)](
                      _0x1c9055 ? _0x440313 : _0x2ada31[_0x403f5d(0x1ad)]
                  ));
            let _0x7366ff = _0x403f5d(0x312)[_0x403f5d(0x21a)](
                _0x1c5d07[_0x403f5d(0x19f)](0x4),
                _0x440313
            );
            return _0x7366ff;
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0xd75579 = _0x273d22;
            this[_0xd75579(0x285)](
                _0xd75579(0x25a),
                this[_0xd75579(0x1f7)][_0xd75579(0x22d)](this)
            );
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x222)] = function () {
            return this['findSymbol']('variable');
        }),
        (Window_DebugVariables['prototype'][_0x273d22(0x2e5)] = function (_0x37af30) {
            const _0x37d132 = _0x273d22;
            if (this[_0x37d132(0x324)](_0x37af30) === 'variable')
                ((this[_0x37d132(0x260)][_0x37af30][_0x37d132(0x26a)] = this[_0x37d132(0x358)](
                    this[_0x37d132(0x260)][_0x37af30][_0x37d132(0x1d1)]
                )),
                    Window_DebugCommand[_0x37d132(0x19c)][_0x37d132(0x2e5)][_0x37d132(0x20d)](
                        this,
                        _0x37af30
                    ),
                    this[_0x37d132(0x2b3)](_0x37af30));
            else {
                const _0xe1ccd9 = this[_0x37d132(0x362)](_0x37af30),
                    _0x3b4ba7 = _0x37d132(0x1d0);
                (this['resetTextColor'](),
                    this['changePaintOpacity'](this[_0x37d132(0x201)](_0x37af30)),
                    this[_0x37d132(0x1a1)](
                        this[_0x37d132(0x2ef)](_0x37af30),
                        _0xe1ccd9['x'],
                        _0xe1ccd9['y'],
                        _0xe1ccd9['width'],
                        _0x3b4ba7
                    ));
            }
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x2b3)] = function (_0xe8f06e) {
            const _0x9c0216 = _0x273d22,
                _0x165f8e = this['_list'][_0xe8f06e][_0x9c0216(0x1d1)],
                _0x595aa9 = $gameVariables[_0x9c0216(0x20b)](_0x165f8e),
                _0xb657c2 = this['itemLineRect'](_0xe8f06e);
            (this['resetTextColor'](),
                this[_0x9c0216(0x1a1)](
                    _0x595aa9,
                    _0xb657c2['x'],
                    _0xb657c2['y'],
                    _0xb657c2[_0x9c0216(0x207)],
                    _0x9c0216(0x2e7)
                ));
        }),
        (Window_DebugVariables[_0x273d22(0x19c)]['changeVariable'] = function () {
            const _0x36878c = _0x273d22,
                _0x16980a = this[_0x36878c(0x276)](),
                _0x48221d = _0x16980a[0x0],
                _0x369f92 = _0x16980a[0x1] + Number($gameVariables['value'](_0x48221d));
            $gameVariables[_0x36878c(0x1d7)](_0x48221d, _0x369f92);
            const _0x1be6a1 = this[_0x36878c(0x197)](_0x48221d);
            (this[_0x36878c(0x2e5)](_0x1be6a1), this[_0x36878c(0x198)]());
        }),
        (Window_DebugVariables['prototype'][_0x273d22(0x2e9)] = function () {
            const _0x1701ca = _0x273d22;
            if (this[_0x1701ca(0x1db)]() === 'variable') {
                if (this[_0x1701ca(0x2de)]()) return;
            }
            Window_DebugCommand[_0x1701ca(0x19c)][_0x1701ca(0x2e9)][_0x1701ca(0x20d)](this);
        }),
        (Window_DebugVariables[_0x273d22(0x19c)][_0x273d22(0x2de)] = function () {
            const _0x3cf94c = _0x273d22,
                _0x3f7192 = this[_0x3cf94c(0x276)](),
                _0x5a9f61 = $gameVariables[_0x3cf94c(0x20b)](_0x3f7192);
            let _0x1b434f = $gameVariables['value'](_0x3f7192);
            if (Input['isTriggered'](_0x3cf94c(0x1c7))) _0x1b434f = 0x0;
            else {
                if (Input[_0x3cf94c(0x30f)](_0x3cf94c(0x1da)))
                    ((_0x1b434f = String(_0x1b434f)),
                        (_0x1b434f = _0x1b434f[_0x3cf94c(0x23e)](
                            0x0,
                            _0x1b434f[_0x3cf94c(0x20c)] - 0x1
                        )),
                        (_0x1b434f = Number(_0x1b434f)));
                else {
                    if (Input[_0x3cf94c(0x30f)]('-'))
                        _0x1b434f = -0x1 * Math[_0x3cf94c(0x237)](_0x1b434f);
                    else {
                        if (Input[_0x3cf94c(0x30f)]('+'))
                            _0x1b434f = Math[_0x3cf94c(0x237)](_0x1b434f);
                        else
                            for (let _0x1a763f = 0x0; _0x1a763f < 0xa; _0x1a763f++) {
                                Input[_0x3cf94c(0x30f)](String(_0x1a763f)) &&
                                    (_0x1b434f = Number(String(_0x1b434f) + String(_0x1a763f)));
                            }
                    }
                }
            }
            if (_0x5a9f61 === _0x1b434f) return ![];
            return (
                SoundManager[_0x3cf94c(0x1cd)](),
                $gameVariables[_0x3cf94c(0x1d7)](_0x3f7192, _0x1b434f),
                this['drawItem'](this[_0x3cf94c(0x1a8)]()),
                !![]
            );
        }));
    function Window_DebugCommonEvents() {
        const _0x116b84 = _0x273d22;
        this[_0x116b84(0x291)](...arguments);
    }
    ((Window_DebugCommonEvents['prototype'] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugCommonEvents[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugCommonEvents),
        (Window_DebugCommonEvents[_0x273d22(0x19c)]['initialize'] = function () {
            const _0x24c89d = _0x273d22;
            Window_DebugCommand['prototype']['initialize'][_0x24c89d(0x20d)](this);
        }),
        (Window_DebugCommonEvents[_0x273d22(0x19c)]['maxCols'] = function () {
            return 0x3;
        }),
        (Window_DebugCommonEvents[_0x273d22(0x19c)][_0x273d22(0x361)] = function () {
            const _0x2f8b1b = _0x273d22;
            return (
                Window_Scrollable['prototype'][_0x2f8b1b(0x361)][_0x2f8b1b(0x20d)](this) * 0x3 + 0x8
            );
        }),
        (Window_DebugCommonEvents['prototype'][_0x273d22(0x30e)] = function () {
            const _0x4ec12c = _0x273d22,
                _0x53b29e = $dataCommonEvents[_0x4ec12c(0x20c)];
            for (let _0x208527 = 0x1; _0x208527 < _0x53b29e; _0x208527++) {
                if (!this[_0x4ec12c(0x2b6)]($dataCommonEvents[_0x208527])) continue;
                this['addCommand'](
                    this[_0x4ec12c(0x2d8)](_0x208527),
                    _0x4ec12c(0x28a),
                    !![],
                    _0x208527
                );
            }
        }),
        (Window_DebugCommonEvents['prototype'][_0x273d22(0x2b6)] = function (_0x27b0b4) {
            const _0x4d3fd4 = _0x273d22;
            if (!_0x27b0b4) return ![];
            return _0x27b0b4[_0x4d3fd4(0x1cb)][_0x4d3fd4(0x20c)] > 0x2;
        }),
        (Window_DebugCommonEvents[_0x273d22(0x19c)][_0x273d22(0x2d8)] = function (_0x50ff9c) {
            const _0x131226 = _0x273d22;
            return $dataCommonEvents[_0x50ff9c][_0x131226(0x26a)];
        }),
        (Window_DebugCommonEvents[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x189aa8 = _0x273d22;
            this[_0x189aa8(0x285)]('commonEvent', this[_0x189aa8(0x1a9)]['bind'](this));
        }),
        (Window_DebugCommonEvents['prototype'][_0x273d22(0x2e5)] = function (_0x3a1459) {
            const _0x1a9ac0 = _0x273d22;
            Window_DebugCommand[_0x1a9ac0(0x19c)][_0x1a9ac0(0x2e5)][_0x1a9ac0(0x20d)](
                this,
                _0x3a1459
            );
            const _0x1f0734 = this[_0x1a9ac0(0x1a7)](_0x3a1459),
                _0x161602 = this['_list'][_0x3a1459]['ext'] || 0x0,
                _0x56593f = $dataCommonEvents[_0x161602],
                _0x599481 = this['textSizeEx'](name)[_0x1a9ac0(0x207)],
                _0x51f268 = _0x1a9ac0(0x25c)[_0x1a9ac0(0x21a)](_0x161602[_0x1a9ac0(0x19f)](0x4));
            this['drawTextEx'](_0x51f268, _0x1f0734['x'], _0x1f0734['y']);
            if (_0x56593f[_0x1a9ac0(0x187)] > 0x0) {
                const _0x56d62b =
                        TextManager[_0x1a9ac0(0x26d)][
                            _0x56593f[_0x1a9ac0(0x187)] === 0x1
                                ? _0x1a9ac0(0x2dc)
                                : 'parallelCommonEvent'
                        ],
                    _0x3d6a6c =
                        _0x1a9ac0(0x256) + _0x56d62b[_0x1a9ac0(0x21a)](_0x56593f[_0x1a9ac0(0x2c1)]),
                    _0xaa5770 =
                        _0x1f0734['x'] +
                        (_0x1f0734[_0x1a9ac0(0x207)] -
                            this[_0x1a9ac0(0x2ab)](_0x3d6a6c)[_0x1a9ac0(0x207)]) /
                            0x2;
                this[_0x1a9ac0(0x1e9)](
                    _0x3d6a6c,
                    _0xaa5770,
                    _0x1f0734['y'] + this[_0x1a9ac0(0x2c4)]() * 0x2
                );
            }
        }),
        (Window_DebugCommonEvents[_0x273d22(0x19c)]['runCommonEvent'] = function () {
            const _0x28fd01 = _0x273d22,
                _0x1589e5 = this[_0x28fd01(0x276)]();
            ($gameTemp[_0x28fd01(0x18d)](_0x1589e5), SceneManager['_scene'][_0x28fd01(0x1b0)]());
        }));
    function Window_DebugTeleport() {
        const _0x509913 = _0x273d22;
        this[_0x509913(0x291)](...arguments);
    }
    ((Window_DebugTeleport['prototype'] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand['prototype']
    )),
        (Window_DebugTeleport[_0x273d22(0x19c)]['constructor'] = Window_DebugTeleport),
        (Window_DebugTeleport[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x21051c = _0x273d22;
            Window_DebugCommand['prototype'][_0x21051c(0x291)]['call'](this);
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)]['itemTextAlign'] = function () {
            const _0x501ba5 = _0x273d22;
            return _0x501ba5(0x346);
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x2;
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x13738e = _0x273d22;
            (this[_0x13738e(0x314)](
                TextManager[_0x13738e(0x26d)][_0x13738e(0x1d9)],
                _0x13738e(0x1d0)
            ),
                this[_0x13738e(0x314)](TextManager[_0x13738e(0x26d)]['mapNumericOrder'], 'center'));
            const _0x4fdeef = [],
                _0x54c47c = [];
            for (const _0x446d9a of $dataMapInfos) {
                if (!_0x446d9a) continue;
                (_0x4fdeef[_0x13738e(0x2bf)](_0x446d9a['id']),
                    (_0x54c47c[_0x446d9a['order']] = _0x446d9a['id']));
            }
            const _0x5311ae = _0x4fdeef[_0x13738e(0x20c)];
            for (let _0x268e00 = 0x0; _0x268e00 < _0x5311ae; _0x268e00++) {
                const _0xd1db50 = _0x4fdeef[_0x268e00],
                    _0x493159 = _0x54c47c[_0x268e00 + 0x1];
                (this[_0x13738e(0x314)](
                    this[_0x13738e(0x306)](_0x493159),
                    _0x13738e(0x2a3),
                    !![],
                    _0x493159
                ),
                    this[_0x13738e(0x314)](
                        this['makeMapIdName'](_0xd1db50),
                        'map',
                        !![],
                        _0xd1db50
                    ));
            }
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)]['makeMapIdName'] = function (_0xc68d7e) {
            const _0x3a8df1 = _0x273d22;
            return _0x3a8df1(0x264)[_0x3a8df1(0x21a)](
                _0xc68d7e[_0x3a8df1(0x19f)](0x3),
                $dataMapInfos[_0xc68d7e][_0x3a8df1(0x26a)]
            );
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x36916e = _0x273d22;
            this['setHandler'](_0x36916e(0x2a3), this[_0x36916e(0x19d)][_0x36916e(0x22d)](this));
        }),
        (Window_DebugTeleport['prototype'][_0x273d22(0x2e5)] = function (_0x9d6e36) {
            const _0x59171c = _0x273d22,
                _0x20fc86 = this[_0x59171c(0x362)](_0x9d6e36);
            this[_0x59171c(0x227)][_0x59171c(0x2cc)](
                _0x20fc86['x'],
                _0x20fc86['y'],
                _0x20fc86['width'],
                _0x20fc86['height']
            );
            const _0x3283fb = this['commandSymbol'](_0x9d6e36),
                _0x55fe95 = this[_0x59171c(0x260)][_0x9d6e36][_0x59171c(0x1d1)];
            let _0x23868d = this[_0x59171c(0x2ef)](_0x9d6e36);
            if (_0x9d6e36 % 0x2 === 0x0 && _0x3283fb === _0x59171c(0x2a3)) {
                let _0x163ccd = _0x55fe95,
                    _0x17cb11 = $dataMapInfos[_0x55fe95],
                    _0x5633cc = 0x5;
                _0x17cb11['parentId'] !== 0x0 && (_0x23868d = '└' + _0x23868d);
                while (!![]) {
                    if (_0x17cb11[_0x59171c(0x2ce)] === 0x0) break;
                    if (_0x5633cc <= 0x0) break;
                    ((_0x23868d = '\x20\x20' + _0x23868d),
                        (_0x17cb11 = $dataMapInfos[_0x17cb11[_0x59171c(0x2ce)]]),
                        _0x5633cc--);
                }
            }
            const _0x5f5a2d = this['textSizeEx'](_0x23868d)['width'];
            this['changePaintOpacity'](this[_0x59171c(0x201)](_0x9d6e36));
            let _0x42e785 = _0x3283fb === 'center' ? 'center' : this['itemTextAlign']();
            if (_0x42e785 === _0x59171c(0x2e7))
                this[_0x59171c(0x1e9)](
                    _0x23868d,
                    _0x20fc86['x'] + _0x20fc86['width'] - _0x5f5a2d,
                    _0x20fc86['y'],
                    _0x5f5a2d
                );
            else {
                if (_0x42e785 === 'center') {
                    const _0x3a3075 =
                        _0x20fc86['x'] +
                        Math[_0x59171c(0x330)]((_0x20fc86[_0x59171c(0x207)] - _0x5f5a2d) / 0x2);
                    this['drawTextEx'](_0x23868d, _0x3a3075, _0x20fc86['y'], _0x5f5a2d);
                } else this[_0x59171c(0x1e9)](_0x23868d, _0x20fc86['x'], _0x20fc86['y'], _0x5f5a2d);
            }
        }),
        (Window_DebugTeleport[_0x273d22(0x19c)][_0x273d22(0x19d)] = function () {
            const _0x26392a = _0x273d22,
                _0x324cee = this[_0x26392a(0x276)]();
            ($gameTemp[_0x26392a(0x2c3)](),
                SceneManager[_0x26392a(0x25f)][_0x26392a(0x1b0)](),
                SceneManager['push'](Scene_DebugMapTeleport),
                $gamePlayer['reserveTransfer'](_0x324cee, 0x0, 0x0, 0x2, 0x0));
        }));
    function Window_DebugCoordinates() {
        const _0xabbdfe = _0x273d22;
        this[_0xabbdfe(0x291)][_0xabbdfe(0x277)](this, arguments);
    }
    ((Window_DebugCoordinates['prototype'] = Object[_0x273d22(0x2f1)](
        Window_Base[_0x273d22(0x19c)]
    )),
        (Window_DebugCoordinates[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugCoordinates),
        (Window_DebugCoordinates[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x6925cb = _0x273d22,
                _0x3d5444 = new Rectangle(
                    0x0,
                    0x0,
                    Graphics['boxWidth'],
                    this[_0x6925cb(0x282)](0x2)
                );
            (Window_Base[_0x6925cb(0x19c)][_0x6925cb(0x291)][_0x6925cb(0x20d)](this, _0x3d5444),
                (this[_0x6925cb(0x18a)] = 0x3c),
                (this['_x'] = 0x0),
                (this['_y'] = 0x0));
        }),
        (Window_DebugCoordinates[_0x273d22(0x19c)][_0x273d22(0x1fb)] = function () {
            const _0x3e69c3 = _0x273d22;
            (this[_0x3e69c3(0x227)][_0x3e69c3(0x243)](),
                (this['_x'] = $gamePlayer['x']),
                (this['_y'] = $gamePlayer['y']),
                (this[_0x3e69c3(0x18a)] = 0x3c),
                (this['opacity'] = 0xff),
                (this[_0x3e69c3(0x303)] = 0xff),
                SoundManager['playCursor']());
            var _0x531bff = $dataMapInfos[$gameMap[_0x3e69c3(0x337)]()][_0x3e69c3(0x26a)];
            (this[_0x3e69c3(0x1a1)](
                _0x531bff,
                0x0,
                0x0,
                this[_0x3e69c3(0x227)][_0x3e69c3(0x207)],
                'center'
            ),
                (_0x531bff = this[_0x3e69c3(0x1bc)]()),
                this[_0x3e69c3(0x1a1)](
                    _0x531bff,
                    0x0,
                    this[_0x3e69c3(0x2c4)](),
                    this[_0x3e69c3(0x227)][_0x3e69c3(0x207)],
                    _0x3e69c3(0x1d0)
                ));
        }),
        (Window_DebugCoordinates[_0x273d22(0x19c)][_0x273d22(0x1bc)] = function () {
            const _0x4a5753 = _0x273d22;
            var _0x5d2e8e = _0x4a5753(0x1ff) + this['_x'];
            return ((_0x5d2e8e += _0x4a5753(0x2a9) + this['_y']), _0x5d2e8e);
        }),
        (Window_DebugCoordinates['prototype'][_0x273d22(0x32a)] = function () {
            const _0x4bbffe = _0x273d22;
            (Window_Base[_0x4bbffe(0x19c)][_0x4bbffe(0x32a)][_0x4bbffe(0x20d)](this),
                this[_0x4bbffe(0x301)](),
                (this['_x'] !== $gamePlayer['x'] || this['_y'] !== $gamePlayer['y']) &&
                    this[_0x4bbffe(0x1fb)]());
        }),
        (Window_DebugCoordinates[_0x273d22(0x19c)]['updateOpacity'] = function () {
            const _0x1593c3 = _0x273d22;
            --this[_0x1593c3(0x18a)] < 0x0 &&
                ((this[_0x1593c3(0x1d4)] -= 0x2), (this[_0x1593c3(0x303)] -= 0x2));
        }));
    function Window_DebugQuick() {
        const _0x4f7b2a = _0x273d22;
        this[_0x4f7b2a(0x291)](...arguments);
    }
    ((Window_DebugQuick['prototype'] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugQuick[_0x273d22(0x19c)]['constructor'] = Window_DebugQuick),
        (Window_DebugQuick[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x400bc6 = _0x273d22;
            Window_DebugCommand[_0x400bc6(0x19c)][_0x400bc6(0x291)][_0x400bc6(0x20d)](this);
        }),
        (Window_DebugQuick[_0x273d22(0x19c)]['itemTextAlign'] = function () {
            const _0x341b6a = _0x273d22;
            return _0x341b6a(0x1d0);
        }),
        (Window_DebugQuick['prototype'][_0x273d22(0x208)] = function () {
            return 0x3;
        }),
        (Window_DebugQuick[_0x273d22(0x19c)]['itemHeight'] = function () {
            const _0x4628a4 = _0x273d22;
            return this[_0x4628a4(0x2c4)]() * 0x2;
        }),
        (Window_DebugQuick[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x20b588 = _0x273d22,
                _0x5ca848 = VisuMZ[_0x20b588(0x2c9)][_0x20b588(0x317)][_0x20b588(0x1bd)];
            for (const _0x2c7fe5 of _0x5ca848) {
                if (!_0x2c7fe5) continue;
                if (!_0x2c7fe5['Visibility'][_0x20b588(0x20d)](this)) continue;
                const _0xe6a80f = this[_0x20b588(0x1dd)](_0x2c7fe5);
                this[_0x20b588(0x314)](_0xe6a80f, _0x20b588(0x356), !![], _0x2c7fe5);
            }
        }),
        (Window_DebugQuick['prototype']['makeQuickName'] = function (_0xaf37d) {
            const _0x50dec9 = _0x273d22;
            return _0xaf37d[_0x50dec9(0x2d2)]
                ? _0x50dec9(0x1a2)[_0x50dec9(0x21a)](
                      _0xaf37d[_0x50dec9(0x2d2)],
                      _0xaf37d[_0x50dec9(0x2f0)]
                  )
                : _0xaf37d[_0x50dec9(0x2f0)];
        }),
        (Window_DebugQuick[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x567353 = _0x273d22;
            this[_0x567353(0x285)](
                _0x567353(0x356),
                this[_0x567353(0x348)][_0x567353(0x22d)](this)
            );
        }),
        (Window_DebugQuick['prototype'][_0x273d22(0x348)] = function () {
            const _0x342248 = _0x273d22,
                _0x5e7724 = this[_0x342248(0x276)]();
            try {
                _0x5e7724[_0x342248(0x30b)][_0x342248(0x20d)](this);
            } catch (_0x33c984) {
                if ($gameTemp[_0x342248(0x32c)]()) console['log'](_0x33c984);
            }
            _0x5e7724[_0x342248(0x327)]
                ? SceneManager['_scene']['debuggerClose']()
                : this[_0x342248(0x198)]();
        }),
        (Window_DebugQuick[_0x273d22(0x19c)][_0x273d22(0x2a6)] = function () {
            const _0x2cdb8c = _0x273d22;
            if (this[_0x2cdb8c(0x215)]) {
                const _0x3535bb = this[_0x2cdb8c(0x276)](),
                    _0x5d3562 = _0x3535bb ? _0x3535bb[_0x2cdb8c(0x2ad)] : '';
                this[_0x2cdb8c(0x215)][_0x2cdb8c(0x316)](_0x5d3562);
            }
        }));
    function Window_DebugBattle() {
        this['initialize'](...arguments);
    }
    ((Window_DebugBattle[_0x273d22(0x19c)] = Object['create'](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugBattle),
        (Window_DebugBattle['prototype'][_0x273d22(0x291)] = function () {
            const _0x128e8b = _0x273d22;
            Window_DebugCommand[_0x128e8b(0x19c)][_0x128e8b(0x291)][_0x128e8b(0x20d)](this);
        }),
        (Window_DebugBattle['prototype']['itemTextAlign'] = function () {
            const _0x12cb59 = _0x273d22;
            return _0x12cb59(0x346);
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x3;
        }),
        (Window_DebugBattle[_0x273d22(0x19c)]['itemWidth'] = function () {
            const _0x3bf36b = _0x273d22;
            return Math[_0x3bf36b(0x330)](this[_0x3bf36b(0x255)] / 0x4);
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x23c)] = function (_0x1ecd3b) {
            const _0x4d54f1 = _0x273d22,
                _0x3749d8 = Window_DebugCommand[_0x4d54f1(0x19c)][_0x4d54f1(0x23c)][
                    _0x4d54f1(0x20d)
                ](this, _0x1ecd3b);
            if (_0x1ecd3b % 0x3 === 0x0) _0x3749d8['width'] = this[_0x4d54f1(0x2d6)]() * 0x2;
            else
                _0x1ecd3b % 0x3 > 0x0 &&
                    (_0x3749d8['x'] =
                        this[_0x4d54f1(0x2d6)]() * (0x1 + (_0x1ecd3b % 0x3)) +
                        this[_0x4d54f1(0x27d)]());
            return _0x3749d8;
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x3518ad = _0x273d22,
                _0x3f9837 = TextManager[_0x3518ad(0x26d)];
            for (const _0x147e04 of $dataTroops) {
                if (!_0x147e04) continue;
                if (_0x147e04['name'][_0x3518ad(0x34e)]() === '') continue;
                if (_0x147e04[_0x3518ad(0x26a)][_0x3518ad(0x283)](/-----/i)) continue;
                const _0x42bfbe = this[_0x3518ad(0x339)](_0x147e04['id']);
                (this[_0x3518ad(0x314)](_0x42bfbe, 'startBattle', !![], _0x147e04),
                    this[_0x3518ad(0x314)](
                        _0x3f9837[_0x3518ad(0x242)],
                        _0x3518ad(0x1fc),
                        !![],
                        _0x147e04
                    ),
                    this['addCommand'](
                        _0x3f9837[_0x3518ad(0x274)],
                        'surpriseBattle',
                        !![],
                        _0x147e04
                    ));
            }
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x339)] = function (_0x7b048b) {
            const _0x5e78bf = _0x273d22;
            let _0x38e382 = _0x5e78bf(0x2d0)[_0x5e78bf(0x21a)](
                _0x7b048b['padZero'](0x4),
                $dataTroops[_0x7b048b][_0x5e78bf(0x26a)]
            );
            return _0x38e382;
        }),
        (Window_DebugBattle['prototype']['setHandlers'] = function () {
            const _0x896a3c = _0x273d22;
            (this[_0x896a3c(0x285)](
                _0x896a3c(0x1fd),
                this[_0x896a3c(0x1fd)][_0x896a3c(0x22d)](this, 0x0)
            ),
                this['setHandler'](
                    _0x896a3c(0x1fc),
                    this[_0x896a3c(0x1fd)][_0x896a3c(0x22d)](this, 0x1)
                ),
                this[_0x896a3c(0x285)](
                    'surpriseBattle',
                    this[_0x896a3c(0x1fd)][_0x896a3c(0x22d)](this, 0x2)
                ));
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x2e5)] = function (_0x4147e4) {
            const _0x3a1413 = _0x273d22,
                _0x5a6803 = this[_0x3a1413(0x362)](_0x4147e4);
            this[_0x3a1413(0x227)][_0x3a1413(0x2cc)](
                _0x5a6803['x'],
                _0x5a6803['y'],
                _0x5a6803[_0x3a1413(0x207)],
                _0x5a6803['height']
            );
            const _0x11cee2 = this[_0x3a1413(0x2ef)](_0x4147e4),
                _0x485d88 = this['textSizeEx'](_0x11cee2)[_0x3a1413(0x207)];
            this[_0x3a1413(0x1a4)](this['isCommandEnabled'](_0x4147e4));
            let _0x3bef31 =
                this['commandSymbol'](_0x4147e4) === _0x3a1413(0x1fd)
                    ? _0x3a1413(0x346)
                    : _0x3a1413(0x1d0);
            if (_0x3bef31 === _0x3a1413(0x2e7))
                this[_0x3a1413(0x1e9)](
                    _0x11cee2,
                    _0x5a6803['x'] + _0x5a6803[_0x3a1413(0x207)] - _0x485d88,
                    _0x5a6803['y'],
                    _0x485d88
                );
            else {
                if (_0x3bef31 === _0x3a1413(0x1d0)) {
                    const _0x2dbc0d =
                        _0x5a6803['x'] +
                        Math['floor']((_0x5a6803[_0x3a1413(0x207)] - _0x485d88) / 0x2);
                    this['drawTextEx'](_0x11cee2, _0x2dbc0d, _0x5a6803['y'], _0x485d88);
                } else this[_0x3a1413(0x1e9)](_0x11cee2, _0x5a6803['x'], _0x5a6803['y'], _0x485d88);
            }
        }),
        (Window_DebugBattle[_0x273d22(0x19c)][_0x273d22(0x1fd)] = function (_0x335a59) {
            const _0x3aff77 = _0x273d22,
                _0x3b1db7 = this['currentExt']();
            BattleManager[_0x3aff77(0x1aa)](_0x3b1db7['id'], !![], ![]);
            if (_0x335a59 === 0x1) BattleManager[_0x3aff77(0x2b4)] = !![];
            else _0x335a59 === 0x2 && (BattleManager[_0x3aff77(0x326)] = !![]);
            (SceneManager['_scene'][_0x3aff77(0x1b0)](),
                SceneManager[_0x3aff77(0x2bf)](Scene_Battle));
        }));
    function Window_DebugObjects() {
        const _0x5bb55b = _0x273d22;
        this[_0x5bb55b(0x291)](...arguments);
    }
    ((Window_DebugObjects['prototype'] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand['prototype']
    )),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugObjects),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x1e1cfa = _0x273d22;
            Window_DebugCommand[_0x1e1cfa(0x19c)][_0x1e1cfa(0x291)][_0x1e1cfa(0x20d)](this);
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x234)] = function () {
            const _0xc14aa1 = _0x273d22;
            return _0xc14aa1(0x346);
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x7;
        }),
        (Window_DebugObjects['prototype']['itemWidth'] = function () {
            const _0x483df7 = _0x273d22;
            return Math[_0x483df7(0x330)](this['innerWidth'] / 0xc);
        }),
        (Window_DebugObjects['prototype'][_0x273d22(0x23c)] = function (_0x225134) {
            const _0x241b16 = _0x273d22,
                _0x2b1530 = Window_DebugCommand[_0x241b16(0x19c)][_0x241b16(0x23c)][
                    _0x241b16(0x20d)
                ](this, _0x225134);
            if (_0x225134 % 0x7 === 0x3)
                ((_0x2b1530[_0x241b16(0x207)] = this['itemWidth']() * 0x6),
                    (_0x2b1530['x'] = this[_0x241b16(0x2d6)]() * 0x3));
            else
                _0x225134 % 0x7 > 0x3 &&
                    (_0x2b1530['x'] =
                        this['itemWidth']() * (0x5 + (_0x225134 % 0x7)) + this[_0x241b16(0x27d)]());
            return _0x2b1530;
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x357)] = function () {
            return $dataItems;
        }),
        (Window_DebugObjects[_0x273d22(0x19c)]['symbol'] = function () {
            return 'item';
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x4f128a = _0x273d22;
            (this[_0x4f128a(0x314)]('None', 'goldToZero', !![], [0x0, _0x4f128a(0x268)]),
                this['addCommand']('-100', _0x4f128a(0x31c), !![], [0x0, -0x64]),
                this['addCommand']('-1', _0x4f128a(0x31c), !![], [0x0, -0x1]),
                this[_0x4f128a(0x314)](this[_0x4f128a(0x273)](), 'gold'),
                this['addCommand']('+1', 'goldChange', !![], [0x0, 0x1]),
                this[_0x4f128a(0x314)]('+100', _0x4f128a(0x31c), !![], [0x0, 0x64]),
                this[_0x4f128a(0x314)]('Max', 'goldToMax', !![], [0x0, _0x4f128a(0x1b2)]));
            const _0x19d569 = this[_0x4f128a(0x357)]()[_0x4f128a(0x20c)],
                _0x23c451 = this[_0x4f128a(0x2ba)](),
                _0x116529 = this[_0x4f128a(0x2ba)]() + _0x4f128a(0x304),
                _0x4da9f1 = this[_0x4f128a(0x2ba)]() + 'ToZero',
                _0x3f2012 = this[_0x4f128a(0x2ba)]() + 'ToMax';
            for (let _0x1ed5ec = 0x1; _0x1ed5ec < _0x19d569; _0x1ed5ec++) {
                const _0x24658c = this[_0x4f128a(0x357)]()[_0x1ed5ec];
                if (!_0x24658c) continue;
                if (_0x24658c[_0x4f128a(0x26a)]['trim']() === '') continue;
                if (_0x24658c[_0x4f128a(0x26a)][_0x4f128a(0x283)](/-----/i)) continue;
                (this[_0x4f128a(0x314)]('None', _0x4da9f1, !![], [_0x1ed5ec, _0x4f128a(0x268)]),
                    this[_0x4f128a(0x314)](_0x4f128a(0x2f8), _0x116529, !![], [_0x1ed5ec, -0xa]),
                    this[_0x4f128a(0x314)]('-1', _0x116529, !![], [_0x1ed5ec, -0x1]),
                    this[_0x4f128a(0x314)](
                        this[_0x4f128a(0x2f5)](_0x1ed5ec),
                        _0x23c451,
                        !![],
                        _0x1ed5ec
                    ),
                    this[_0x4f128a(0x314)]('+1', _0x116529, !![], [_0x1ed5ec, 0x1]),
                    this['addCommand'](_0x4f128a(0x270), _0x116529, !![], [_0x1ed5ec, 0xa]),
                    this[_0x4f128a(0x314)](_0x4f128a(0x1b2), _0x3f2012, !![], [_0x1ed5ec, 'Max']));
            }
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x273)] = function () {
            const _0x189370 = _0x273d22,
                _0x5cde51 = Imported[_0x189370(0x363)]
                    ? VisuMZ[_0x189370(0x296)]['Settings'][_0x189370(0x1e7)][_0x189370(0x31a)]
                    : 0x13a;
            return _0x189370(0x29c)[_0x189370(0x21a)](_0x5cde51, TextManager[_0x189370(0x21d)]);
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x2f5)] = function (_0x44dff2) {
            const _0x482d36 = _0x273d22;
            let _0x16dd88 = _0x482d36(0x240)[_0x482d36(0x21a)](
                _0x44dff2[_0x482d36(0x19f)](0x4),
                name
            );
            return _0x16dd88;
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x2170b8 = _0x273d22;
            (this[_0x2170b8(0x285)]('goldChange', this[_0x2170b8(0x302)][_0x2170b8(0x22d)](this)),
                this[_0x2170b8(0x285)]('goldToZero', this['zeroGold'][_0x2170b8(0x22d)](this)),
                this[_0x2170b8(0x285)](
                    'goldToMax',
                    this[_0x2170b8(0x365)][_0x2170b8(0x22d)](this)
                ));
            const _0x303247 = this[_0x2170b8(0x2ba)](),
                _0x45c17d = this[_0x2170b8(0x2ba)]() + _0x2170b8(0x304),
                _0x41cee8 = this[_0x2170b8(0x2ba)]() + 'ToZero',
                _0x52c6ef = this[_0x2170b8(0x2ba)]() + _0x2170b8(0x1e3);
            (this['setHandler'](_0x45c17d, this[_0x2170b8(0x1c2)][_0x2170b8(0x22d)](this)),
                this[_0x2170b8(0x285)](_0x41cee8, this['zeroObject'][_0x2170b8(0x22d)](this)),
                this[_0x2170b8(0x285)](_0x52c6ef, this[_0x2170b8(0x313)][_0x2170b8(0x22d)](this)));
        }),
        (Window_DebugObjects[_0x273d22(0x19c)]['refreshIndex'] = function () {
            const _0x58dd12 = _0x273d22;
            return this[_0x58dd12(0x287)](this[_0x58dd12(0x2ba)]());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x2e5)] = function (_0xb1cabc) {
            const _0x514caa = _0x273d22;
            if (this[_0x514caa(0x324)](_0xb1cabc) === _0x514caa(0x2a5))
                (Window_DebugCommand['prototype'][_0x514caa(0x2e5)]['call'](this, _0xb1cabc),
                    this[_0x514caa(0x1a5)](_0xb1cabc));
            else {
                if (this[_0x514caa(0x324)](_0xb1cabc) === this[_0x514caa(0x2ba)]())
                    ((this[_0x514caa(0x260)][_0xb1cabc][_0x514caa(0x26a)] = this['makeObjectName'](
                        this[_0x514caa(0x260)][_0xb1cabc][_0x514caa(0x1d1)]
                    )),
                        Window_DebugCommand[_0x514caa(0x19c)][_0x514caa(0x2e5)][_0x514caa(0x20d)](
                            this,
                            _0xb1cabc
                        ),
                        this[_0x514caa(0x265)](_0xb1cabc));
                else {
                    const _0xaf47ba = this[_0x514caa(0x362)](_0xb1cabc),
                        _0x56d469 = 'center';
                    (this['resetTextColor'](),
                        this['changePaintOpacity'](this[_0x514caa(0x201)](_0xb1cabc)),
                        this[_0x514caa(0x1a1)](
                            this['commandName'](_0xb1cabc),
                            _0xaf47ba['x'],
                            _0xaf47ba['y'],
                            _0xaf47ba[_0x514caa(0x207)],
                            _0x56d469
                        ));
                }
            }
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x1a5)] = function (_0x4f3237) {
            const _0x11b00d = _0x273d22;
            this[_0x11b00d(0x311)]();
            const _0x56919f = this[_0x11b00d(0x362)](_0x4f3237),
                _0x25b943 = $gameParty[_0x11b00d(0x2a5)]();
            this[_0x11b00d(0x1a1)](
                _0x25b943,
                _0x56919f['x'],
                _0x56919f['y'],
                _0x56919f['width'],
                _0x11b00d(0x2e7)
            );
        }),
        (Window_DebugObjects[_0x273d22(0x19c)]['drawCurrentObjectValue'] = function (_0x1f0d79) {
            const _0x5efe5e = _0x273d22,
                _0x356d9e = this[_0x5efe5e(0x260)][_0x1f0d79][_0x5efe5e(0x1d1)],
                _0x2228e1 = this[_0x5efe5e(0x357)]()[_0x356d9e],
                _0x418264 = '×' + $gameParty[_0x5efe5e(0x18b)](_0x2228e1),
                _0x55cd53 = this['itemLineRect'](_0x1f0d79);
            (this[_0x5efe5e(0x322)](),
                this[_0x5efe5e(0x1a1)](
                    _0x418264,
                    _0x55cd53['x'],
                    _0x55cd53['y'],
                    _0x55cd53[_0x5efe5e(0x207)],
                    'right'
                ));
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x302)] = function () {
            const _0x7fce8e = _0x273d22,
                _0x363f3f = this[_0x7fce8e(0x276)](),
                _0x401788 = _0x363f3f[0x0],
                _0x1c8148 = _0x363f3f[0x1];
            $gameParty[_0x7fce8e(0x33c)](_0x1c8148);
            const _0x55c1f0 = this[_0x7fce8e(0x287)](_0x7fce8e(0x2a5));
            (this[_0x7fce8e(0x2e5)](_0x55c1f0), this['activate']());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x2f7)] = function () {
            const _0x26e8c4 = _0x273d22,
                _0x28c152 = this[_0x26e8c4(0x276)](),
                _0x166488 = _0x28c152[0x0],
                _0x1070b9 = -0x1 * $gameParty[_0x26e8c4(0x365)]();
            $gameParty[_0x26e8c4(0x33c)](_0x1070b9);
            const _0x37e35b = this[_0x26e8c4(0x287)](_0x26e8c4(0x2a5));
            (this['drawItem'](_0x37e35b), this[_0x26e8c4(0x198)]());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x365)] = function () {
            const _0x241012 = _0x273d22,
                _0x4886e6 = this['currentExt'](),
                _0x46e44e = _0x4886e6[0x0],
                _0x59f35c = $gameParty[_0x241012(0x365)]();
            $gameParty[_0x241012(0x33c)](_0x59f35c);
            const _0x5b24a7 = this[_0x241012(0x287)]('gold');
            (this['drawItem'](_0x5b24a7), this[_0x241012(0x198)]());
        }),
        (Window_DebugObjects['prototype'][_0x273d22(0x1c2)] = function () {
            const _0x2b65af = _0x273d22,
                _0x514c6b = this['currentExt'](),
                _0xc11fb5 = _0x514c6b[0x0],
                _0x2c3bb7 = this[_0x2b65af(0x357)]()[_0xc11fb5],
                _0x2aa317 = _0x514c6b[0x1];
            $gameParty[_0x2b65af(0x21b)](_0x2c3bb7, _0x2aa317);
            const _0x66227d = this[_0x2b65af(0x197)](_0xc11fb5);
            (this['drawItem'](_0x66227d), this[_0x2b65af(0x198)]());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)]['zeroObject'] = function () {
            const _0x307d8f = _0x273d22,
                _0x1e5e06 = this[_0x307d8f(0x276)](),
                _0x32c8c3 = _0x1e5e06[0x0],
                _0x54e24f = this['database']()[_0x32c8c3],
                _0x522831 = -0x1 * $gameParty[_0x307d8f(0x1be)](_0x54e24f);
            $gameParty[_0x307d8f(0x21b)](_0x54e24f, _0x522831);
            const _0x163e61 = this[_0x307d8f(0x197)](_0x32c8c3);
            (this[_0x307d8f(0x2e5)](_0x163e61), this[_0x307d8f(0x198)]());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)]['maxObject'] = function () {
            const _0x19d04d = _0x273d22,
                _0xf69809 = this[_0x19d04d(0x276)](),
                _0x3dc91d = _0xf69809[0x0],
                _0x247dd1 = this[_0x19d04d(0x357)]()[_0x3dc91d],
                _0x2703e8 = $gameParty[_0x19d04d(0x1be)](_0x247dd1);
            $gameParty['gainItem'](_0x247dd1, _0x2703e8);
            const _0x537a4c = this[_0x19d04d(0x197)](_0x3dc91d);
            (this[_0x19d04d(0x2e5)](_0x537a4c), this[_0x19d04d(0x198)]());
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x2e9)] = function () {
            const _0x499f25 = _0x273d22;
            if (this['currentSymbol']() === _0x499f25(0x2a5)) {
                if (this['processGoldHandling']()) return;
            } else {
                if (this['currentSymbol']() === this[_0x499f25(0x2ba)]()) {
                    if (this['processObjectHandling']()) return;
                }
            }
            Window_DebugCommand[_0x499f25(0x19c)][_0x499f25(0x2e9)]['call'](this);
        }),
        (Window_DebugObjects['prototype'][_0x273d22(0x2af)] = function () {
            const _0x27621e = _0x273d22,
                _0x3f9f10 = $gameParty[_0x27621e(0x2a5)]();
            let _0x2c0348 = $gameParty[_0x27621e(0x2a5)]();
            if (Input[_0x27621e(0x34c)]('delete')) _0x2c0348 = 0x0;
            else {
                if (Input[_0x27621e(0x30f)](_0x27621e(0x1da)))
                    ((_0x2c0348 = String(_0x2c0348)),
                        (_0x2c0348 = _0x2c0348[_0x27621e(0x23e)](
                            0x0,
                            _0x2c0348[_0x27621e(0x20c)] - 0x1
                        )),
                        (_0x2c0348 = Number(_0x2c0348)));
                else {
                    if (Input[_0x27621e(0x30f)]('-'))
                        _0x2c0348 = -0x1 * Math[_0x27621e(0x237)](_0x2c0348);
                    else {
                        if (Input[_0x27621e(0x30f)]('+'))
                            _0x2c0348 = Math[_0x27621e(0x237)](_0x2c0348);
                        else
                            for (let _0x165292 = 0x0; _0x165292 < 0xa; _0x165292++) {
                                Input[_0x27621e(0x30f)](String(_0x165292)) &&
                                    (_0x2c0348 = Number(String(_0x2c0348) + String(_0x165292)));
                            }
                    }
                }
            }
            if (_0x3f9f10 === _0x2c0348) return ![];
            return (
                SoundManager['playCursor'](),
                $gameParty['gainGold'](_0x2c0348 - _0x3f9f10),
                this[_0x27621e(0x2e5)](this[_0x27621e(0x1a8)]()),
                !![]
            );
        }),
        (Window_DebugObjects[_0x273d22(0x19c)][_0x273d22(0x2a2)] = function () {
            const _0x35155d = _0x273d22,
                _0x12c5bd = this[_0x35155d(0x276)](),
                _0x399065 = this['database']()[_0x12c5bd],
                _0x56b094 = $gameParty['numItems'](_0x399065);
            let _0x5e0172 = $gameParty[_0x35155d(0x18b)](_0x399065);
            if (Input['isTriggered'](_0x35155d(0x1c7))) _0x5e0172 = 0x0;
            else {
                if (Input[_0x35155d(0x30f)](_0x35155d(0x1da)))
                    ((_0x5e0172 = String(_0x5e0172)),
                        (_0x5e0172 = _0x5e0172[_0x35155d(0x23e)](
                            0x0,
                            _0x5e0172[_0x35155d(0x20c)] - 0x1
                        )),
                        (_0x5e0172 = Number(_0x5e0172)));
                else {
                    if (Input[_0x35155d(0x30f)]('-')) _0x5e0172 = -0x1 * Math['abs'](_0x5e0172);
                    else {
                        if (Input[_0x35155d(0x30f)]('+'))
                            _0x5e0172 = Math[_0x35155d(0x237)](_0x5e0172);
                        else
                            for (let _0x42e5ae = 0x0; _0x42e5ae < 0xa; _0x42e5ae++) {
                                Input[_0x35155d(0x30f)](String(_0x42e5ae)) &&
                                    (_0x5e0172 = Number(String(_0x5e0172) + String(_0x42e5ae)));
                            }
                    }
                }
            }
            if (_0x56b094 === _0x5e0172) return ![];
            return (
                SoundManager[_0x35155d(0x1cd)](),
                $gameParty[_0x35155d(0x21b)](_0x399065, _0x5e0172 - _0x56b094),
                this[_0x35155d(0x2e5)](this[_0x35155d(0x1a8)]()),
                !![]
            );
        }));
    function Window_DebugItems() {
        this['initialize'](...arguments);
    }
    ((Window_DebugItems[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugObjects[_0x273d22(0x19c)]
    )),
        (Window_DebugItems['prototype'][_0x273d22(0x24a)] = Window_DebugItems),
        (Window_DebugItems[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x39369c = _0x273d22;
            Window_DebugObjects[_0x39369c(0x19c)][_0x39369c(0x291)][_0x39369c(0x20d)](this);
        }),
        (Window_DebugItems['prototype']['database'] = function () {
            return $dataItems;
        }),
        (Window_DebugItems[_0x273d22(0x19c)][_0x273d22(0x2ba)] = function () {
            const _0x4c14eb = _0x273d22;
            return _0x4c14eb(0x34d);
        }),
        (Window_DebugItems['prototype'][_0x273d22(0x2f5)] = function (_0x5baf2a) {
            const _0x58d800 = _0x273d22,
                _0x4cf2ee = $dataItems[_0x5baf2a],
                _0x608aae = _0x4cf2ee[_0x58d800(0x2a7)],
                _0x404be9 = _0x4cf2ee[_0x58d800(0x26a)];
            let _0x400ec6 = _0x58d800(0x190)[_0x58d800(0x21a)](
                _0x5baf2a[_0x58d800(0x19f)](0x4),
                _0x608aae,
                _0x404be9
            );
            return _0x400ec6;
        }));
    function Window_DebugWeapons() {
        const _0x32503b = _0x273d22;
        this[_0x32503b(0x291)](...arguments);
    }
    ((Window_DebugWeapons[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugObjects[_0x273d22(0x19c)]
    )),
        (Window_DebugWeapons['prototype'][_0x273d22(0x24a)] = Window_DebugWeapons),
        (Window_DebugWeapons['prototype'][_0x273d22(0x291)] = function () {
            const _0x49003c = _0x273d22;
            Window_DebugObjects['prototype'][_0x49003c(0x291)][_0x49003c(0x20d)](this);
        }),
        (Window_DebugWeapons[_0x273d22(0x19c)][_0x273d22(0x357)] = function () {
            return $dataWeapons;
        }),
        (Window_DebugWeapons['prototype'][_0x273d22(0x2ba)] = function () {
            const _0x80920b = _0x273d22;
            return _0x80920b(0x2a8);
        }),
        (Window_DebugWeapons[_0x273d22(0x19c)][_0x273d22(0x2f5)] = function (_0x48958a) {
            const _0x460f4c = _0x273d22,
                _0x18bfbb = $dataWeapons[_0x48958a],
                _0x28a051 = _0x18bfbb[_0x460f4c(0x2a7)],
                _0xc80844 = _0x18bfbb[_0x460f4c(0x26a)];
            let _0x13bc15 = _0x460f4c(0x2eb)[_0x460f4c(0x21a)](
                _0x48958a[_0x460f4c(0x19f)](0x4),
                _0x28a051,
                _0xc80844
            );
            return _0x13bc15;
        }));
    function Window_DebugArmors() {
        const _0x53655e = _0x273d22;
        this[_0x53655e(0x291)](...arguments);
    }
    ((Window_DebugArmors[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugObjects[_0x273d22(0x19c)]
    )),
        (Window_DebugArmors[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugArmors),
        (Window_DebugArmors[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x4184f5 = _0x273d22;
            Window_DebugObjects[_0x4184f5(0x19c)][_0x4184f5(0x291)][_0x4184f5(0x20d)](this);
        }),
        (Window_DebugArmors['prototype'][_0x273d22(0x357)] = function () {
            return $dataArmors;
        }),
        (Window_DebugArmors[_0x273d22(0x19c)]['symbol'] = function () {
            const _0x342f2b = _0x273d22;
            return _0x342f2b(0x29f);
        }),
        (Window_DebugArmors[_0x273d22(0x19c)][_0x273d22(0x2f5)] = function (_0xa4644b) {
            const _0x3bf25d = _0x273d22,
                _0x1de3a9 = $dataArmors[_0xa4644b],
                _0x5739c0 = _0x1de3a9[_0x3bf25d(0x2a7)],
                _0x440163 = _0x1de3a9[_0x3bf25d(0x26a)];
            let _0x488b63 = _0x3bf25d(0x33d)['format'](
                _0xa4644b['padZero'](0x4),
                _0x5739c0,
                _0x440163
            );
            return _0x488b63;
        }));
    function Window_DebugMapEvents() {
        const _0x24b3da = _0x273d22;
        this[_0x24b3da(0x291)](...arguments);
    }
    ((Window_DebugMapEvents[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x24a)] = Window_DebugMapEvents),
        (Window_DebugMapEvents[_0x273d22(0x19c)]['initialize'] = function () {
            const _0x4621f7 = _0x273d22;
            Window_DebugCommand[_0x4621f7(0x19c)][_0x4621f7(0x291)][_0x4621f7(0x20d)](this);
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x6;
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x2d6)] = function () {
            const _0x4965d2 = _0x273d22;
            return Math[_0x4965d2(0x330)](this['innerWidth'] / 0x8);
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x23c)] = function (_0xf9e6c) {
            const _0x16dbaf = _0x273d22,
                _0x36be58 = Window_DebugCommand['prototype'][_0x16dbaf(0x23c)][_0x16dbaf(0x20d)](
                    this,
                    _0xf9e6c
                );
            if (_0xf9e6c % 0x6 === 0x0)
                _0x36be58[_0x16dbaf(0x207)] = this[_0x16dbaf(0x2d6)]() * 0x3;
            else
                _0xf9e6c % 0x6 > 0x0 &&
                    (_0x36be58['x'] =
                        this[_0x16dbaf(0x2d6)]() * (0x2 + (_0xf9e6c % 0x6)) +
                        this[_0x16dbaf(0x27d)]());
            return _0x36be58;
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)]['makeCommandList'] = function () {
            const _0x1ad326 = _0x273d22;
            for (const _0x286000 of $gameMap['events']()) {
                if (!_0x286000) continue;
                const _0x505830 = this[_0x1ad326(0x344)](_0x286000);
                (this[_0x1ad326(0x314)](_0x505830, _0x1ad326(0x267), !![], _0x286000),
                    this[_0x1ad326(0x314)](
                        this[_0x1ad326(0x33f)](_0x286000, 'A'),
                        _0x1ad326(0x259),
                        !![],
                        [_0x286000, 'A']
                    ),
                    this[_0x1ad326(0x314)](
                        this[_0x1ad326(0x33f)](_0x286000, 'B'),
                        _0x1ad326(0x259),
                        !![],
                        [_0x286000, 'B']
                    ),
                    this[_0x1ad326(0x314)](
                        this[_0x1ad326(0x33f)](_0x286000, 'C'),
                        _0x1ad326(0x259),
                        !![],
                        [_0x286000, 'C']
                    ),
                    this[_0x1ad326(0x314)](
                        this[_0x1ad326(0x33f)](_0x286000, 'D'),
                        'selfSwitch',
                        !![],
                        [_0x286000, 'D']
                    ),
                    this[_0x1ad326(0x314)](
                        TextManager[_0x1ad326(0x26d)]['eraseEvent'],
                        _0x1ad326(0x226),
                        !![],
                        _0x286000
                    ));
            }
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x344)] = function (_0x59f935) {
            const _0x472838 = _0x273d22;
            let _0x38cd11 = _0x472838(0x2a0)[_0x472838(0x21a)](
                _0x59f935[_0x472838(0x29b)]()[_0x472838(0x19f)](0x3),
                _0x59f935[_0x472838(0x34a)]()['name']
            );
            return _0x38cd11;
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x33f)] = function (
            _0x6137dc,
            _0x1ccc8d
        ) {
            const _0x5054f8 = _0x273d22,
                _0x5c4772 = [$gameMap['mapId'](), _0x6137dc[_0x5054f8(0x29b)](), _0x1ccc8d],
                _0x1a00cd = $gameSelfSwitches[_0x5054f8(0x20b)](_0x5c4772) ? 0x11 : 0x0,
                _0x5ab810 = $gameSelfSwitches['value'](_0x5c4772) ? 'ON' : _0x5054f8(0x2df);
            return _0x5054f8(0x18f)[_0x5054f8(0x21a)](_0x1a00cd, _0x1ccc8d, _0x5ab810);
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x186)] = function () {
            const _0x1282b7 = _0x273d22;
            (this[_0x1282b7(0x285)](
                _0x1282b7(0x267),
                this[_0x1282b7(0x267)][_0x1282b7(0x22d)](this)
            ),
                this[_0x1282b7(0x285)](
                    _0x1282b7(0x259),
                    this[_0x1282b7(0x20f)][_0x1282b7(0x22d)](this)
                ),
                this[_0x1282b7(0x285)](_0x1282b7(0x226), this[_0x1282b7(0x226)]['bind'](this)));
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x2e5)] = function (_0x26648f) {
            const _0x42590d = _0x273d22,
                _0x507551 = this['itemLineRect'](_0x26648f);
            this['contents'][_0x42590d(0x2cc)](
                _0x507551['x'],
                _0x507551['y'],
                _0x507551[_0x42590d(0x207)],
                _0x507551['height']
            );
            const _0x236ea4 = this[_0x42590d(0x2ef)](_0x26648f),
                _0x3e3d5f = this[_0x42590d(0x2ab)](_0x236ea4)[_0x42590d(0x207)];
            this['changePaintOpacity'](this[_0x42590d(0x201)](_0x26648f));
            let _0x2234cf =
                this[_0x42590d(0x324)](_0x26648f) === 'goToEvent'
                    ? _0x42590d(0x346)
                    : _0x42590d(0x1d0);
            if (_0x2234cf === 'right')
                this[_0x42590d(0x1e9)](
                    _0x236ea4,
                    _0x507551['x'] + _0x507551[_0x42590d(0x207)] - _0x3e3d5f,
                    _0x507551['y'],
                    _0x3e3d5f
                );
            else {
                if (_0x2234cf === _0x42590d(0x1d0)) {
                    const _0x149b76 =
                        _0x507551['x'] +
                        Math[_0x42590d(0x330)]((_0x507551[_0x42590d(0x207)] - _0x3e3d5f) / 0x2);
                    this[_0x42590d(0x1e9)](_0x236ea4, _0x149b76, _0x507551['y'], _0x3e3d5f);
                } else this['drawTextEx'](_0x236ea4, _0x507551['x'], _0x507551['y'], _0x3e3d5f);
            }
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)]['goToEvent'] = function () {
            const _0x135699 = _0x273d22,
                _0x54fd73 = this['currentExt']();
            let _0x2ad35c = _0x54fd73['x'],
                _0x199dea = _0x54fd73['y'];
            switch ($gamePlayer[_0x135699(0x192)]()) {
                case 0x1:
                case 0x4:
                case 0x7:
                    _0x2ad35c += 0x1;
                    break;
                case 0x3:
                case 0x6:
                case 0x9:
                    _0x2ad35c -= 0x1;
                    break;
            }
            switch ($gamePlayer[_0x135699(0x192)]()) {
                case 0x1:
                case 0x2:
                case 0x3:
                    _0x199dea -= 0x1;
                    break;
                case 0x7:
                case 0x8:
                case 0x9:
                    _0x199dea += 0x1;
                    break;
            }
            ((_0x2ad35c = _0x2ad35c[_0x135699(0x248)](0x0, $gameMap[_0x135699(0x207)]() - 0x1)),
                (_0x199dea = _0x199dea['clamp'](0x0, $gameMap[_0x135699(0x275)]() - 0x1)),
                $gamePlayer[_0x135699(0x2c7)](_0x2ad35c, _0x199dea),
                SceneManager[_0x135699(0x25f)][_0x135699(0x1b0)]());
        }),
        (Window_DebugMapEvents['prototype']['modifyEvent'] = function () {
            const _0x31c72e = _0x273d22,
                _0x3a22b3 = this[_0x31c72e(0x276)](),
                _0x31c8bc = _0x3a22b3[0x0],
                _0x462a0e = _0x3a22b3[0x1],
                _0x26a403 = [$gameMap['mapId'](), _0x31c8bc[_0x31c72e(0x29b)](), _0x462a0e];
            ($gameSelfSwitches[_0x31c72e(0x1d7)](_0x26a403, !$gameSelfSwitches['value'](_0x26a403)),
                (this[_0x31c72e(0x260)][this['index']()][_0x31c72e(0x26a)] = this['selfSwitchName'](
                    _0x31c8bc,
                    _0x462a0e
                )),
                this[_0x31c72e(0x2e5)](this[_0x31c72e(0x1a8)]()),
                this[_0x31c72e(0x198)]());
        }),
        (Window_DebugMapEvents[_0x273d22(0x19c)][_0x273d22(0x226)] = function () {
            const _0x226572 = _0x273d22,
                _0x46d80f = this[_0x226572(0x276)]();
            (_0x46d80f['erase'](), SceneManager['_scene'][_0x226572(0x1b0)]());
        }));
    function Window_DebugBuffsStatesBattler() {
        this['initialize'](...arguments);
    }
    ((Window_DebugBuffsStatesBattler[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugBuffsStatesBattler['prototype'][_0x273d22(0x24a)] =
            Window_DebugBuffsStatesBattler),
        (Window_DebugBuffsStatesBattler[_0x273d22(0x19c)][_0x273d22(0x291)] = function () {
            const _0x3578b9 = _0x273d22;
            Window_DebugCommand[_0x3578b9(0x19c)][_0x3578b9(0x291)][_0x3578b9(0x20d)](this);
        }),
        (Window_DebugBuffsStatesBattler[_0x273d22(0x19c)][_0x273d22(0x234)] = function () {
            const _0x52a188 = _0x273d22;
            return _0x52a188(0x1d0);
        }),
        (Window_DebugBuffsStatesBattler[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x2;
        }),
        (Window_DebugBuffsStatesBattler['prototype'][_0x273d22(0x30e)] = function () {
            const _0x2a2948 = _0x273d22,
                _0xec52f3 = TextManager[_0x2a2948(0x26d)];
            let _0x3e2aa7 = [_0xec52f3[_0x2a2948(0x20a)]][_0x2a2948(0x30d)](
                    $gameParty[_0x2a2948(0x188)]()
                ),
                _0xf5ea82 = [_0xec52f3[_0x2a2948(0x299)]][_0x2a2948(0x30d)](
                    $gameParty[_0x2a2948(0x232)]()
                );
            $gameParty[_0x2a2948(0x1af)]() &&
                ((_0xf5ea82 = _0xf5ea82[_0x2a2948(0x30d)](
                    ['', _0xec52f3[_0x2a2948(0x1e4)]],
                    $gameTroop['aliveMembers']()
                )),
                $gameTroop['deadMembers']()['length'] > 0x0 &&
                    (_0xf5ea82 = _0xf5ea82[_0x2a2948(0x30d)](
                        ['', _0xec52f3[_0x2a2948(0x254)]],
                        $gameTroop[_0x2a2948(0x1dc)]()
                    )));
            const _0x21e516 = Math[_0x2a2948(0x193)](
                _0x3e2aa7[_0x2a2948(0x20c)],
                _0xf5ea82[_0x2a2948(0x20c)]
            );
            for (let _0x3dc3ab = 0x0; _0x3dc3ab < _0x21e516; _0x3dc3ab++) {
                if (SceneManager[_0x2a2948(0x216)]())
                    this['addEntry'](_0xf5ea82[_0x3dc3ab] || null);
                this[_0x2a2948(0x286)](_0x3e2aa7[_0x3dc3ab] || null);
                if (!SceneManager[_0x2a2948(0x216)]())
                    this[_0x2a2948(0x286)](_0xf5ea82[_0x3dc3ab] || null);
            }
        }),
        (Window_DebugBuffsStatesBattler['prototype'][_0x273d22(0x286)] = function (_0x5924a5) {
            const _0x24ca18 = _0x273d22;
            if (!_0x5924a5) this[_0x24ca18(0x314)]('', _0x24ca18(0x196));
            else
                typeof _0x5924a5 === 'string'
                    ? this[_0x24ca18(0x314)](_0x24ca18(0x253)['format'](_0x5924a5), 'none')
                    : this['addCommand'](
                          _0x24ca18(0x23a)[_0x24ca18(0x21a)](_0x5924a5[_0x24ca18(0x26a)]()),
                          _0x24ca18(0x2fa),
                          !![],
                          _0x5924a5
                      );
        }),
        (Window_DebugBuffsStatesBattler['prototype']['setHandlers'] = function () {
            const _0x3efa85 = _0x273d22;
            this[_0x3efa85(0x285)](
                _0x3efa85(0x2fa),
                this['prepareBattler'][_0x3efa85(0x22d)](this)
            );
        }),
        (Window_DebugBuffsStatesBattler['prototype']['prepareBattler'] = function () {
            const _0x1f4449 = _0x273d22,
                _0x51fb55 = this[_0x1f4449(0x276)](),
                _0x200849 = SceneManager[_0x1f4449(0x25f)][_0x1f4449(0x1f6)];
            _0x200849
                ? (_0x200849[_0x1f4449(0x238)](_0x51fb55),
                  _0x200849[_0x1f4449(0x295)](),
                  _0x200849['activate'](),
                  _0x200849[_0x1f4449(0x1fb)](),
                  this[_0x1f4449(0x338)](),
                  this[_0x1f4449(0x1de)]())
                : this['activate']();
        }));
    function Window_DebugBuffsStatesMain() {
        const _0x28a557 = _0x273d22;
        this[_0x28a557(0x291)](...arguments);
    }
    ((Window_DebugBuffsStatesMain[_0x273d22(0x19c)] = Object[_0x273d22(0x2f1)](
        Window_DebugCommand[_0x273d22(0x19c)]
    )),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['constructor'] =
            Window_DebugBuffsStatesMain),
        (Window_DebugBuffsStatesMain['prototype']['initialize'] = function () {
            const _0x27ce5a = _0x273d22;
            (Window_DebugCommand[_0x27ce5a(0x19c)][_0x27ce5a(0x291)][_0x27ce5a(0x20d)](this),
                (this[_0x27ce5a(0x308)] = null));
        }),
        (Window_DebugBuffsStatesMain['prototype'][_0x273d22(0x234)] = function () {
            const _0x2318e1 = _0x273d22;
            return _0x2318e1(0x346);
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x208)] = function () {
            return 0x7;
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['itemWidth'] = function () {
            const _0x21f2eb = _0x273d22;
            return Math[_0x21f2eb(0x330)](this[_0x21f2eb(0x255)] / 0xc);
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x27d)] = function () {
            return 0x0;
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x23c)] = function (_0x13d063) {
            const _0x5772e7 = _0x273d22,
                _0x42f7e1 = Window_DebugCommand[_0x5772e7(0x19c)][_0x5772e7(0x23c)][
                    _0x5772e7(0x20d)
                ](this, _0x13d063),
                _0x50ad1c = _0x13d063 % 0x7;
            switch (_0x50ad1c) {
                case 0x1:
                case 0x5:
                    _0x42f7e1[_0x5772e7(0x207)] = this[_0x5772e7(0x2d6)]() * 0x2;
                    break;
                case 0x3:
                    _0x42f7e1[_0x5772e7(0x207)] = this['itemWidth']() * 0x4;
                    break;
            }
            if (_0x50ad1c >= 0x2) {
                const _0x148900 = [0x0, 0x0, 0x3, 0x4, 0x8, 0x9, 0xb];
                _0x42f7e1['x'] = this['itemWidth']() * _0x148900[_0x50ad1c];
            }
            return _0x42f7e1;
        }),
        (Window_DebugBuffsStatesMain['prototype']['setBattler'] = function (_0x25f01b) {
            const _0x2eaac6 = _0x273d22;
            this[_0x2eaac6(0x308)] = _0x25f01b;
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x30e)] = function () {
            const _0x15f68d = _0x273d22;
            if (!this[_0x15f68d(0x308)]) return;
            (this[_0x15f68d(0x251)](), this[_0x15f68d(0x239)](), this[_0x15f68d(0x22f)]());
        }),
        (Window_DebugBuffsStatesMain['prototype']['addSpacingCommandText'] = function () {
            const _0x3740dd = _0x273d22;
            let _0x2526d8 = 0x7;
            while (_0x2526d8--) this[_0x3740dd(0x314)]('', '');
        }),
        (Window_DebugBuffsStatesMain['prototype'][_0x273d22(0x251)] = function () {
            const _0x559d97 = _0x273d22;
            for (let _0x183948 = 0x0; _0x183948 < 0x8; _0x183948++) {
                (this[_0x559d97(0x314)]('<<', 'buffTurnsChange', !![], [_0x183948, -0x1]),
                    this['addCommand']('', _0x559d97(0x19e), !![], [_0x183948, 0x0]),
                    this[_0x559d97(0x314)]('>>', _0x559d97(0x279), !![], [_0x183948, 0x1]),
                    this['addCommand'](_0x559d97(0x26a), _0x559d97(0x329), !![], [_0x183948, 0x0]),
                    this[_0x559d97(0x314)]('<<', _0x559d97(0x32e), !![], [_0x183948, -0x1]),
                    this[_0x559d97(0x314)]('', _0x559d97(0x30a), !![], [_0x183948, 0x0]),
                    this[_0x559d97(0x314)]('>>', _0x559d97(0x32e), !![], [_0x183948, 0x1]));
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x239)] = function () {
            const _0x1b8603 = _0x273d22;
            this['addSpacingCommandText']();
            for (let _0x5095b5 = 0x0; _0x5095b5 < 0x8; _0x5095b5++) {
                (this[_0x1b8603(0x314)]('<<', _0x1b8603(0x352), !![], [_0x5095b5, -0x1]),
                    this[_0x1b8603(0x314)]('', 'debuffTurnsType', !![], [_0x5095b5, 0x0]),
                    this[_0x1b8603(0x314)]('>>', _0x1b8603(0x352), !![], [_0x5095b5, 0x1]),
                    this[_0x1b8603(0x314)](_0x1b8603(0x26a), _0x1b8603(0x2bb), !![], [
                        _0x5095b5,
                        0x0,
                    ]),
                    this[_0x1b8603(0x314)]('<<', _0x1b8603(0x211), !![], [_0x5095b5, -0x1]),
                    this['addCommand']('', _0x1b8603(0x364), !![], [_0x5095b5, 0x0]),
                    this[_0x1b8603(0x314)]('>>', _0x1b8603(0x211), !![], [_0x5095b5, 0x1]));
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x2b9)] = function (
            _0x5ad948,
            _0x418b81
        ) {
            const _0x5de271 = _0x273d22,
                _0x354a92 = _0x418b81 === 'Buff' ? 0x1 : -0x1;
            let _0x22c192 = this[_0x5de271(0x308)][_0x5de271(0x31f)](_0x354a92, _0x5ad948);
            const _0x40a564 = TextManager[_0x5de271(0x212)](_0x5ad948);
            let _0x5afe3d = 0x0;
            if (_0x418b81 === 'Buff') {
                if (this['_battler'][_0x5de271(0x204)](_0x5ad948)) _0x5afe3d = 0x18;
                if (this[_0x5de271(0x308)]['isDebuffAffected'](_0x5ad948)) _0x5afe3d = 0x19;
            } else {
                if (_0x418b81 === _0x5de271(0x1d8)) {
                    if (this[_0x5de271(0x308)][_0x5de271(0x204)](_0x5ad948)) _0x5afe3d = 0x19;
                    if (this['_battler']['isDebuffAffected'](_0x5ad948)) _0x5afe3d = 0x18;
                }
            }
            return _0x5de271(0x307)[_0x5de271(0x21a)](
                _0x5ad948[_0x5de271(0x19f)](0x4),
                _0x22c192,
                _0x40a564,
                _0x418b81,
                _0x5afe3d
            );
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['makeStatesCommandList'] = function () {
            const _0x84c91f = _0x273d22;
            this[_0x84c91f(0x1e6)]();
            for (const _0x5a9ac7 of $dataStates) {
                if (!_0x5a9ac7) continue;
                if (_0x5a9ac7[_0x84c91f(0x26a)][_0x84c91f(0x34e)]() === '') continue;
                if (_0x5a9ac7[_0x84c91f(0x26a)]['match'](/-----/i)) continue;
                const _0x15201c = _0x5a9ac7['id'];
                (this[_0x84c91f(0x314)]('<<', _0x84c91f(0x2e0), !![], [_0x15201c, -0x1]),
                    this[_0x84c91f(0x314)]('', _0x84c91f(0x220), !![], [_0x15201c, 0x0]),
                    this[_0x84c91f(0x314)]('>>', _0x84c91f(0x2e0), !![], [_0x15201c, 0x1]),
                    this['addCommand']('name', _0x84c91f(0x2e6), !![], [_0x15201c, 0x0]),
                    this[_0x84c91f(0x314)]('<<', _0x84c91f(0x28f), !![], [_0x15201c, -0x1]),
                    this[_0x84c91f(0x314)]('', _0x84c91f(0x2d9), !![], [_0x15201c, 0x0]),
                    this[_0x84c91f(0x314)]('>>', _0x84c91f(0x28f), !![], [_0x15201c, 0x1]));
            }
        }),
        (Window_DebugBuffsStatesMain['prototype'][_0x273d22(0x1c1)] = function (_0x264fb9) {
            const _0x4bf0a4 = _0x273d22,
                _0x552362 = _0x264fb9['id'],
                _0x1af4b8 = _0x264fb9[_0x4bf0a4(0x2a7)] || 0x0,
                _0x36423c = _0x264fb9[_0x4bf0a4(0x26a)];
            let _0x4c2cd5 = this[_0x4bf0a4(0x308)][_0x4bf0a4(0x217)]()[_0x4bf0a4(0x2b6)](_0x264fb9)
                ? 0x11
                : 0x0;
            return _0x4bf0a4(0x2ac)['format'](
                _0x264fb9['id'][_0x4bf0a4(0x19f)](0x4),
                _0x1af4b8,
                _0x36423c,
                _0x4c2cd5
            );
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['setHandlers'] = function () {
            const _0x4be472 = _0x273d22;
            (this[_0x4be472(0x285)]('cancel', this[_0x4be472(0x323)][_0x4be472(0x22d)](this)),
                this['setHandler'](
                    _0x4be472(0x329),
                    this[_0x4be472(0x331)][_0x4be472(0x22d)](this)
                ),
                this[_0x4be472(0x285)](
                    'buffTurnsChange',
                    this[_0x4be472(0x23b)][_0x4be472(0x22d)](this)
                ),
                this[_0x4be472(0x285)](
                    'buffStackChange',
                    this[_0x4be472(0x27b)][_0x4be472(0x22d)](this)
                ),
                this[_0x4be472(0x285)](_0x4be472(0x2bb), this['processDebuffToggle']['bind'](this)),
                this['setHandler'](
                    'debuffTurnsChange',
                    this[_0x4be472(0x355)][_0x4be472(0x22d)](this)
                ),
                this['setHandler']('debuffStackChange', this[_0x4be472(0x230)]['bind'](this)),
                this[_0x4be472(0x285)](_0x4be472(0x2e6), this[_0x4be472(0x21c)]['bind'](this)),
                this[_0x4be472(0x285)](
                    'stateTurnsChange',
                    this[_0x4be472(0x2be)][_0x4be472(0x22d)](this)
                ),
                this[_0x4be472(0x285)](
                    _0x4be472(0x28f),
                    this[_0x4be472(0x28c)][_0x4be472(0x22d)](this)
                ));
        }),
        (Window_DebugBuffsStatesMain['prototype']['refreshIndex'] = function () {
            const _0x34c886 = _0x273d22;
            return this[_0x34c886(0x287)](_0x34c886(0x329));
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x2e5)] = function (_0x23b52e) {
            const _0x4b7ea1 = _0x273d22,
                _0x553be3 = this['commandSymbol'](_0x23b52e) || '';
            if (_0x553be3['match'](/name/i))
                (this[_0x4b7ea1(0x224)](_0x23b52e),
                    Window_DebugCommand[_0x4b7ea1(0x19c)][_0x4b7ea1(0x2e5)][_0x4b7ea1(0x20d)](
                        this,
                        _0x23b52e
                    ));
            else {
                const _0x3d333a = this[_0x4b7ea1(0x362)](_0x23b52e);
                this[_0x4b7ea1(0x227)][_0x4b7ea1(0x2cc)](
                    _0x3d333a['x'],
                    _0x3d333a['y'],
                    _0x3d333a[_0x4b7ea1(0x207)],
                    _0x3d333a[_0x4b7ea1(0x275)]
                );
                const _0x3f3a10 = _0x4b7ea1(0x1d0);
                (this['changeItemColor'](_0x23b52e),
                    this['changePaintOpacity'](this[_0x4b7ea1(0x201)](_0x23b52e)),
                    this[_0x4b7ea1(0x1a1)](
                        this[_0x4b7ea1(0x2fe)](_0x23b52e),
                        _0x3d333a['x'],
                        _0x3d333a['y'],
                        _0x3d333a[_0x4b7ea1(0x207)],
                        _0x3f3a10
                    ));
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['updateCommandName'] = function (_0x560c9c) {
            const _0x166826 = _0x273d22,
                _0x358d39 = this[_0x166826(0x324)](_0x560c9c),
                _0x5a1a56 = this[_0x166826(0x260)][_0x560c9c][_0x166826(0x1d1)];
            if (_0x358d39 === _0x166826(0x329))
                this[_0x166826(0x260)][_0x560c9c][_0x166826(0x26a)] = this[_0x166826(0x2b9)](
                    _0x5a1a56[0x0],
                    'Buff'
                );
            else {
                if (_0x358d39 === _0x166826(0x2bb))
                    this['_list'][_0x560c9c][_0x166826(0x26a)] = this[_0x166826(0x2b9)](
                        _0x5a1a56[0x0],
                        _0x166826(0x1d8)
                    );
                else
                    _0x358d39 === 'stateName' &&
                        (this[_0x166826(0x260)][_0x560c9c][_0x166826(0x26a)] = this[
                            _0x166826(0x1c1)
                        ]($dataStates[_0x5a1a56[0x0]]));
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x18e)] = function (_0x497501) {
            const _0x32bc66 = _0x273d22;
            this['resetTextColor']();
            const _0x41a49d = this[_0x32bc66(0x324)](_0x497501),
                _0x4f22ae = this[_0x32bc66(0x260)][_0x497501]['ext'];
            if (_0x41a49d[_0x32bc66(0x283)](/state/i)) {
                const _0x29bdde = $dataStates[_0x4f22ae[0x0]],
                    _0x26040d = this[_0x32bc66(0x308)][_0x32bc66(0x217)]()['includes'](_0x29bdde)
                        ? 0x11
                        : 0x0;
                this[_0x32bc66(0x2c5)](ColorManager[_0x32bc66(0x2bd)](_0x26040d));
            } else {
                if (_0x41a49d[_0x32bc66(0x283)](/buff/i)) {
                    const _0x5b1b79 = _0x4f22ae[0x0];
                    let _0x37e470 = 0x0;
                    if (_0x41a49d[_0x32bc66(0x283)](/debuff/i)) {
                        if (this[_0x32bc66(0x308)][_0x32bc66(0x1cc)](_0x5b1b79)) _0x37e470 = 0x18;
                        if (this[_0x32bc66(0x308)][_0x32bc66(0x204)](_0x5b1b79)) _0x37e470 = 0x19;
                    } else {
                        if (this['_battler'][_0x32bc66(0x1cc)](_0x5b1b79)) _0x37e470 = 0x19;
                        if (this['_battler'][_0x32bc66(0x204)](_0x5b1b79)) _0x37e470 = 0x18;
                    }
                    this['changeTextColor'](ColorManager[_0x32bc66(0x2bd)](_0x37e470));
                }
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x2fe)] = function (_0x4b4215) {
            const _0x364250 = _0x273d22,
                _0x55fc07 = this[_0x364250(0x324)](_0x4b4215),
                _0x3bfac5 = this[_0x364250(0x260)][_0x4b4215][_0x364250(0x1d1)];
            if (_0x55fc07[_0x364250(0x283)](/buffTurnsType/i)) {
                const _0x30ef21 = _0x3bfac5[0x0],
                    _0x10a4b9 = this[_0x364250(0x308)]['_buffTurns'][_0x30ef21];
                return _0x364250(0x2da)[_0x364250(0x21a)](_0x10a4b9);
            } else {
                if (_0x55fc07[_0x364250(0x283)](/buffStackType/i)) {
                    const _0x2c1af3 = _0x3bfac5[0x0],
                        _0xd083ca = Math[_0x364250(0x237)](
                            this[_0x364250(0x308)][_0x364250(0x278)][_0x2c1af3]
                        );
                    return _0x364250(0x2c0)[_0x364250(0x21a)](_0xd083ca);
                } else {
                    if (_0x55fc07[_0x364250(0x283)](/stateTurnsType/i)) {
                        const _0x4f4549 = _0x3bfac5[0x0],
                            _0x488930 = $dataStates[_0x4f4549];
                        let _0x43a8df = this[_0x364250(0x308)][_0x364250(0x191)][_0x4f4549] || 0x0;
                        if (_0x488930[_0x364250(0x1f1)] === 0x0) _0x43a8df = '∞';
                        return _0x364250(0x2da)['format'](_0x43a8df);
                    } else {
                        if (_0x55fc07[_0x364250(0x283)](/stateDisplayType/i)) {
                            if (Imported[_0x364250(0x347)]) {
                                const _0x43b2a1 = _0x3bfac5[0x0];
                                let _0x33fec5 = this[_0x364250(0x308)][_0x364250(0x1fa)](_0x43b2a1);
                                if (typeof _0x33fec5 !== 'number' && !_0x33fec5)
                                    return _0x364250(0x25d);
                                return _0x33fec5;
                            } else return '-----';
                        }
                    }
                }
            }
            return this['commandName'](_0x4b4215);
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x323)] = function (_0x4b3fa8) {
            const _0x3e9212 = _0x273d22,
                _0x144816 = SceneManager[_0x3e9212(0x25f)][_0x3e9212(0x1f9)];
            _0x144816
                ? (_0x144816[_0x3e9212(0x295)](),
                  _0x144816[_0x3e9212(0x198)](),
                  _0x144816['refresh'](),
                  this['deactivate'](),
                  this['close']())
                : SceneManager[_0x3e9212(0x25f)][_0x3e9212(0x1b0)]();
        }),
        (Window_DebugBuffsStatesMain['prototype']['finishProcess'] = function () {
            const _0x94450c = _0x273d22;
            this[_0x94450c(0x198)]();
            const _0x49fc71 = this[_0x94450c(0x208)]();
            let _0x110e98 = Math['floor'](this['index']() / _0x49fc71) * _0x49fc71;
            for (let _0x2a660a = _0x110e98; _0x2a660a < _0x110e98 + _0x49fc71; _0x2a660a++) {
                this[_0x94450c(0x2e5)](_0x2a660a);
            }
            const _0x4ef74b = this[_0x94450c(0x1db)]();
            if (_0x4ef74b[_0x94450c(0x283)](/debuff/i)) {
                _0x110e98 -= 0x9 * _0x49fc71;
                for (let _0x3f9ae9 = _0x110e98; _0x3f9ae9 < _0x110e98 + _0x49fc71; _0x3f9ae9++) {
                    this[_0x94450c(0x2e5)](_0x3f9ae9);
                }
            } else {
                if (_0x4ef74b[_0x94450c(0x283)](/buff/i)) {
                    _0x110e98 += 0x9 * _0x49fc71;
                    for (
                        let _0x40511c = _0x110e98;
                        _0x40511c < _0x110e98 + _0x49fc71;
                        _0x40511c++
                    ) {
                        this[_0x94450c(0x2e5)](_0x40511c);
                    }
                }
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x331)] = function () {
            const _0x4a305a = _0x273d22,
                _0x1a3a34 = this[_0x4a305a(0x260)][this[_0x4a305a(0x1a8)]()][_0x4a305a(0x1d1)],
                _0x480df6 = _0x1a3a34[0x0];
            (this['_battler']['isBuffAffected'](_0x480df6)
                ? this['_battler'][_0x4a305a(0x32f)](_0x480df6)
                    ? this[_0x4a305a(0x308)]['removeBuff'](_0x480df6)
                    : this['_battler'][_0x4a305a(0x18c)](_0x480df6)
                : (this[_0x4a305a(0x308)]['removeBuff'](_0x480df6),
                  this[_0x4a305a(0x308)]['addBuff'](_0x480df6, 0xa)),
                this['finishProcess']());
        }),
        (Window_DebugBuffsStatesMain['prototype']['processBuffTurnsChange'] = function () {
            const _0x55c736 = _0x273d22,
                _0x4eac60 = this['_list'][this[_0x55c736(0x1a8)]()][_0x55c736(0x1d1)],
                _0x72cc84 = _0x4eac60[0x0],
                _0xf9ee2e = _0x4eac60[0x1];
            ((this[_0x55c736(0x308)][_0x55c736(0x2b0)][_0x72cc84] =
                this[_0x55c736(0x308)]['_buffTurns'][_0x72cc84] || 0x0),
                (this[_0x55c736(0x308)][_0x55c736(0x2b0)][_0x72cc84] += _0xf9ee2e),
                (this[_0x55c736(0x308)]['_buffTurns'][_0x72cc84] = Math[_0x55c736(0x193)](
                    0x0,
                    this['_battler'][_0x55c736(0x2b0)][_0x72cc84]
                )));
            if (this[_0x55c736(0x308)][_0x55c736(0x2b0)][_0x72cc84] === 0x0)
                this[_0x55c736(0x308)][_0x55c736(0x1c0)](_0x72cc84);
            else
                this[_0x55c736(0x308)]['_buffs'][_0x72cc84] === 0x0 &&
                    this[_0x55c736(0x308)][_0x55c736(0x18c)](_0x72cc84);
            this[_0x55c736(0x228)]();
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['processBuffStackChange'] = function () {
            const _0x355606 = _0x273d22,
                _0x4d4841 = this['_list'][this[_0x355606(0x1a8)]()][_0x355606(0x1d1)],
                _0x55306d = _0x4d4841[0x0],
                _0x4d174f = _0x4d4841[0x1];
            (_0x4d174f > 0x0
                ? this['_battler'][_0x355606(0x18c)](_0x55306d)
                : this['_battler'][_0x355606(0x1bf)](_0x55306d),
                this[_0x355606(0x308)]['_buffs'][_0x55306d] === 0x0 &&
                    this[_0x355606(0x308)][_0x355606(0x1c0)](_0x55306d),
                this[_0x355606(0x228)]());
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x1e2)] = function () {
            const _0x4eeb85 = _0x273d22,
                _0x546432 = this[_0x4eeb85(0x260)][this[_0x4eeb85(0x1a8)]()]['ext'],
                _0x2465c1 = _0x546432[0x0];
            (this[_0x4eeb85(0x308)][_0x4eeb85(0x1cc)](_0x2465c1)
                ? this[_0x4eeb85(0x308)][_0x4eeb85(0x199)](_0x2465c1)
                    ? this[_0x4eeb85(0x308)][_0x4eeb85(0x1c0)](_0x2465c1)
                    : this[_0x4eeb85(0x308)][_0x4eeb85(0x1bf)](_0x2465c1)
                : (this[_0x4eeb85(0x308)][_0x4eeb85(0x1c0)](_0x2465c1),
                  this[_0x4eeb85(0x308)]['addDebuff'](_0x2465c1, 0xa)),
                this[_0x4eeb85(0x228)]());
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x355)] = function () {
            const _0x1f296f = _0x273d22,
                _0x13d316 = this[_0x1f296f(0x260)][this[_0x1f296f(0x1a8)]()][_0x1f296f(0x1d1)],
                _0x255f98 = _0x13d316[0x0],
                _0x3e8f4e = _0x13d316[0x1];
            ((this[_0x1f296f(0x308)][_0x1f296f(0x2b0)][_0x255f98] =
                this[_0x1f296f(0x308)][_0x1f296f(0x2b0)][_0x255f98] || 0x0),
                (this['_battler'][_0x1f296f(0x2b0)][_0x255f98] += _0x3e8f4e),
                (this['_battler']['_buffTurns'][_0x255f98] = Math['max'](
                    0x0,
                    this[_0x1f296f(0x308)]['_buffTurns'][_0x255f98]
                )));
            if (this[_0x1f296f(0x308)][_0x1f296f(0x2b0)][_0x255f98] === 0x0)
                this[_0x1f296f(0x308)][_0x1f296f(0x1c0)](_0x255f98);
            else
                this[_0x1f296f(0x308)][_0x1f296f(0x278)][_0x255f98] === 0x0 &&
                    this[_0x1f296f(0x308)][_0x1f296f(0x1bf)](_0x255f98);
            this[_0x1f296f(0x228)]();
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['processDebuffStackChange'] = function () {
            const _0x1b4668 = _0x273d22,
                _0x4e9560 = this['_list'][this[_0x1b4668(0x1a8)]()][_0x1b4668(0x1d1)],
                _0x5b06af = _0x4e9560[0x0],
                _0xed28cc = _0x4e9560[0x1];
            (_0xed28cc > 0x0
                ? this[_0x1b4668(0x308)][_0x1b4668(0x1bf)](_0x5b06af)
                : this[_0x1b4668(0x308)]['increaseBuff'](_0x5b06af),
                this[_0x1b4668(0x308)][_0x1b4668(0x278)][_0x5b06af] === 0x0 &&
                    this[_0x1b4668(0x308)][_0x1b4668(0x1c0)](_0x5b06af),
                this[_0x1b4668(0x228)]());
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x21c)] = function () {
            const _0x16fb31 = _0x273d22,
                _0x31d15 = this[_0x16fb31(0x260)][this['index']()][_0x16fb31(0x1d1)],
                _0x1b80ac = _0x31d15[0x0];
            (this[_0x16fb31(0x308)][_0x16fb31(0x2d1)](_0x1b80ac)
                ? this[_0x16fb31(0x308)][_0x16fb31(0x1ac)](_0x1b80ac)
                : this['_battler']['addState'](_0x1b80ac),
                this[_0x16fb31(0x228)]());
        }),
        (Window_DebugBuffsStatesMain['prototype'][_0x273d22(0x2be)] = function () {
            const _0x423e99 = _0x273d22,
                _0x1c42e4 = this[_0x423e99(0x260)][this[_0x423e99(0x1a8)]()][_0x423e99(0x1d1)],
                _0x3037b5 = _0x1c42e4[0x0],
                _0x4b0377 = _0x1c42e4[0x1];
            if ($dataStates[_0x3037b5][_0x423e99(0x1f1)] === 0x0) return this['finishProcess']();
            (!this[_0x423e99(0x308)]['isStateAffected'](_0x3037b5) &&
                this['_battler'][_0x423e99(0x2ed)](_0x3037b5),
                (this['_battler'][_0x423e99(0x191)][_0x3037b5] =
                    this[_0x423e99(0x308)][_0x423e99(0x191)][_0x3037b5] || 0x0),
                (this[_0x423e99(0x308)]['_stateTurns'][_0x3037b5] += _0x4b0377),
                (this['_battler'][_0x423e99(0x191)][_0x3037b5] = Math['max'](
                    0x0,
                    this[_0x423e99(0x308)][_0x423e99(0x191)][_0x3037b5]
                )),
                this[_0x423e99(0x308)][_0x423e99(0x191)][_0x3037b5] <= 0x0 &&
                    this[_0x423e99(0x308)]['eraseState'](_0x3037b5),
                this[_0x423e99(0x228)]());
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x28c)] = function () {
            const _0x4c1206 = _0x273d22,
                _0x27d213 = this[_0x4c1206(0x260)][this['index']()][_0x4c1206(0x1d1)],
                _0x27cfed = _0x27d213[0x0],
                _0x336244 = _0x27d213[0x1];
            let _0x4dd3ae = this[_0x4c1206(0x308)][_0x4c1206(0x1fa)](_0x27cfed);
            (typeof _0x4dd3ae === _0x4c1206(0x2d5) &&
                (!this['_battler'][_0x4c1206(0x2d1)](_0x27cfed) &&
                    (this['_battler'][_0x4c1206(0x2ed)](_0x27cfed),
                    (this['_battler'][_0x4c1206(0x191)][_0x27cfed] =
                        this[_0x4c1206(0x308)]['_stateTurns'][_0x27cfed] || 0x1)),
                (_0x4dd3ae += _0x336244),
                this[_0x4c1206(0x308)][_0x4c1206(0x202)](_0x27cfed, _0x4dd3ae)),
                this[_0x4c1206(0x228)]());
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)]['processHandling'] = function () {
            const _0x399e80 = _0x273d22,
                _0x2f8097 = this[_0x399e80(0x1db)]() || '';
            if (_0x2f8097[_0x399e80(0x283)](/type/i)) {
                if (this['processBuffsStatesKeyboardHandling']()) return;
            }
            Window_DebugCommand[_0x399e80(0x19c)][_0x399e80(0x2e9)]['call'](this);
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x1a3)] = function () {
            const _0x3510f3 = _0x273d22;
            let _0x1f9549 = this['getOldValuesForTyping'](),
                _0x110659 = _0x1f9549;
            if (typeof _0x110659 === _0x3510f3(0x209)) return ![];
            if (Input['isTriggered'](_0x3510f3(0x1c7))) _0x110659 = 0x0;
            else {
                if (Input[_0x3510f3(0x30f)]('backspace'))
                    ((_0x110659 = String(_0x110659)),
                        (_0x110659 = _0x110659[_0x3510f3(0x23e)](
                            0x0,
                            _0x110659[_0x3510f3(0x20c)] - 0x1
                        )),
                        (_0x110659 = Number(_0x110659)));
                else {
                    if (Input[_0x3510f3(0x30f)]('-'))
                        _0x110659 = -0x1 * Math[_0x3510f3(0x237)](_0x110659);
                    else {
                        if (Input[_0x3510f3(0x30f)]('+')) _0x110659 = Math['abs'](_0x110659);
                        else
                            for (let _0x5b1502 = 0x0; _0x5b1502 < 0xa; _0x5b1502++) {
                                Input['isRepeated'](String(_0x5b1502)) &&
                                    (_0x110659 = Number(String(_0x110659) + String(_0x5b1502)));
                            }
                    }
                }
            }
            if (_0x1f9549 === _0x110659) return ![];
            return (
                SoundManager['playCursor'](),
                this[_0x3510f3(0x1a0)](_0x110659),
                this[_0x3510f3(0x228)](),
                !![]
            );
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x2fc)] = function () {
            const _0x44c91c = _0x273d22,
                _0x45dfc4 = this['currentSymbol'](),
                _0x5ae04d = this[_0x44c91c(0x276)](),
                _0x4b2732 = _0x5ae04d[0x0];
            switch (_0x45dfc4) {
                case 'buffTurnsType':
                case _0x44c91c(0x2cb):
                    return this[_0x44c91c(0x308)][_0x44c91c(0x2b0)][_0x4b2732] || 0x0;
                    break;
                case _0x44c91c(0x30a):
                case _0x44c91c(0x364):
                    return Math['abs'](this[_0x44c91c(0x308)][_0x44c91c(0x278)][_0x4b2732] || 0x0);
                    break;
                case _0x44c91c(0x220):
                    if ($dataStates[_0x4b2732][_0x44c91c(0x1f1)] === 0x0) return 'n/a';
                    return this[_0x44c91c(0x308)][_0x44c91c(0x191)][_0x4b2732] || 0x0;
                    break;
                case _0x44c91c(0x2d9):
                    return this[_0x44c91c(0x308)][_0x44c91c(0x1fa)](_0x4b2732) || _0x44c91c(0x249);
                    break;
            }
        }),
        (Window_DebugBuffsStatesMain[_0x273d22(0x19c)][_0x273d22(0x1a0)] = function (_0x325713) {
            const _0x436bbe = _0x273d22,
                _0x43a415 = this[_0x436bbe(0x1db)](),
                _0x5bf164 = this[_0x436bbe(0x276)](),
                _0x43ec73 = _0x5bf164[0x0];
            switch (_0x43a415) {
                case _0x436bbe(0x19e):
                case 'debuffTurnsType':
                    this['_battler'][_0x436bbe(0x2b0)][_0x43ec73] = Math['max'](0x0, _0x325713);
                    break;
                case _0x436bbe(0x30a):
                case _0x436bbe(0x364):
                    const _0x399386 = Imported[_0x436bbe(0x347)],
                        _0x58e44c = _0x399386
                            ? VisuMZ[_0x436bbe(0x350)][_0x436bbe(0x317)][_0x436bbe(0x236)]
                            : {},
                        _0x1fcc7b = _0x399386 ? _0x58e44c[_0x436bbe(0x2a1)] : 0x2,
                        _0x58b192 = _0x399386 ? -_0x58e44c[_0x436bbe(0x210)] : -0x2;
                    if (_0x43a415[_0x436bbe(0x283)](/debuff/i)) _0x325713 *= -0x1;
                    this[_0x436bbe(0x308)][_0x436bbe(0x278)][_0x43ec73] = _0x325713['clamp'](
                        _0x58b192,
                        _0x1fcc7b
                    );
                    break;
                case _0x436bbe(0x220):
                    this['_battler']['_stateTurns'][_0x43ec73] = Math['max'](0x0, _0x325713);
                    break;
                case _0x436bbe(0x2d9):
                    this[_0x436bbe(0x308)]['setStateDisplay'](_0x43ec73, _0x325713);
                    break;
            }
            switch (_0x43a415) {
                case _0x436bbe(0x19e):
                case _0x436bbe(0x2cb):
                    this[_0x436bbe(0x308)][_0x436bbe(0x2b0)][_0x43ec73] === 0x0 &&
                        this[_0x436bbe(0x308)][_0x436bbe(0x1c0)](_0x43ec73);
                    break;
                case _0x436bbe(0x30a):
                case _0x436bbe(0x364):
                    this[_0x436bbe(0x308)][_0x436bbe(0x278)][_0x43ec73] !== 0x0
                        ? (this[_0x436bbe(0x308)][_0x436bbe(0x2b0)][_0x43ec73] = Math['max'](
                              0x1,
                              this['_battler'][_0x436bbe(0x2b0)][_0x43ec73]
                          ))
                        : (this[_0x436bbe(0x308)]['removeBuff'](_0x43ec73),
                          (this[_0x436bbe(0x308)][_0x436bbe(0x2b0)][_0x43ec73] = 0x0));
                    break;
                case _0x436bbe(0x220):
                    this[_0x436bbe(0x308)]['_stateTurns'][_0x43ec73] === 0x0
                        ? this[_0x436bbe(0x308)][_0x436bbe(0x1ac)](_0x43ec73)
                        : this[_0x436bbe(0x308)][_0x436bbe(0x2cf)](_0x43ec73);
                    break;
            }
        }));
}
