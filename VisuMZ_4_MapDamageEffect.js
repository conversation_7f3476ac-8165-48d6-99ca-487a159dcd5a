//=============================================================================
// VisuStella MZ - Map Damage Effect
// VisuMZ_4_MapDamageEffect.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_MapDamageEffect = true;

var VisuMZ = VisuMZ || {};
VisuMZ.MapDamageEffect = VisuMZ.MapDamageEffect || {};
VisuMZ.MapDamageEffect.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.00] [MapDamageEffect]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Map_Damage_Effect_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin changes the cheap-looking red flash when taking floor damage or
 * slip damage on the map to something more elegant looking or allows you to
 * utilize a custom image in place of it. The effect can vary depending on the
 * region the player is currently standing on to the slip state the effect is
 * originating from.
 *
 * Features include all (but not limited to) the following:
 *
 * * Pre-rendered damage gradient that is easier on the eyes for those not
 *   using any custom images for their map effects.
 * * Custom images can be used for a more personal touch.
 * * Map Damage Effects can vary depending on the regions the player is
 *   currently standing when taking floor damage.
 * * Map Damage Effects can vary depending on the states damaging the player
 *   when taking damage from them on the map.
 * * Use Plugin Commands to artificially prompt the Map Damage Effect for
 *   custom created damage events.
 * * Certain region-marked tiles can inflict states on the player's party by
 *   simply stepping on them.
 * * Utilize custom floor damage formulas for region-marked tiles to make some
 *   tiles more dangerous than others.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Basic Floor Damage Calculations
 *
 * Any changes made to floor damage calculations are overwritten in favor of
 * this plugin's ability to allow custom floor damage based on regions by using
 * the notetags available through this plugin.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * The "Once Parallel" feature of the Core Engine can be used and be triggered
 * upon taking Floor Damage or Slip Damage while on the map scene. Once
 * Parallel events will run in the background like a parallel process, except
 * that it does not repeat after finishing.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Custom Tile Effect-Related Notetags ===
 *
 * ---
 *
 * <Floor State Region x: id>
 * <Floor State Region x: id, id, id>
 * <Floor State Region x: name>
 * <Floor State Region x: name, name, name>
 *
 * - Used for: Map Notetags
 * - When stepping onto a tile marked by region 'x', afflict all party members
 *   with the associated state(s).
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'id' with a number representing the ID of the state to afflict
 *   upon all party members.
 * - Replace 'name' with the name of the state(s) to afflict all party members.
 *
 * ---
 *
 * === JavaScript Notetags: Custom Floor Damage ===
 *
 * ---
 *
 * <Floor Damage Formula Region x>
 *  code
 *  code
 *  damage = code
 * </Floor Damage Formula Region x>
 *
 * - Used for: Map Notetags
 * - Determines the amount of damage to deal to each actor when this Map Damage
 *   Effect triggers. Also changes any tile marked by region 'x' into a damage
 *   tile regardless of its setting in the database's tileset.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - The 'damage' variable is returned to determine how much damage the actor
 *   will receive as floor damage (before being affected by the actor's FDR).
 * - Replace 'code' with JavaScript code used to calculate the 'damage'.
 * - The 'a' variable represents the actor receiving the damage.
 * - The 'user' variable represents the actor receiving the damage.
 *
 * ---
 *
 * === Floor Damage Effect-Related Notetags ===
 *
 * ---
 *
 * <Map Damage Effect Region x Color: #rrggbb>
 * <Damage Region x Color: #rrggbb>
 *
 * - Used for: Map Notetags
 * - When taking damage from tiles marked by 'x' region, play a custom color.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'rr' with a hexadecimal value for red.
 * - Replace 'gg' with a hexadecimal value for green.
 * - Replace 'bb' with a hexadecimal value for blue.
 * - Leave the '#' in place.
 * - If you are unsure of what hexadecimal value your color is, please use an
 *   online site like: https://htmlcolorcodes.com/
 * - When using this notetag, custom images won't be used.
 *
 * ---
 *
 * <Map Damage Effect Region x Gradient Length: y>
 * <Damage Region x Gradient Length: y>
 *
 * - Used for: Map Notetags
 * - Changes the gradient length of the pre-render effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'y' with a number representing in pixels how long the gradient
 *   length is.
 *
 * ---
 *
 * <Map Damage Effect Region x Image: filename>
 * <Damage Region x Image: filename>
 *
 * - Used for: Map Notetags
 * - Uses a custom image found in the img/pictures/ folder of your game project
 *   to use when taking floor damage on these region-marked tiles.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'filename' with a picture found within your game project's
 *   img/pictures/ folder. Filenames are case sensitive. Leave out the filename
 *   extension from the notetag.
 * - If this notetag is used, ignore the hex color and gradient notetags.
 *
 * ---
 *
 * <Map Damage Effect Region x Opacity: y>
 * <Damage Region x Opacity: y>
 *
 * - Used for: Map Notetags
 * - Adjusts the starting opacity to 'y' for this region-marked-tile's
 *   Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'y' with a number (0 to 255) representing the starting opacity
 *   used by the region-marked-tile's Map Damage Effect.
 *
 * ---
 *
 * <Map Damage Effect Region x Opacity: y%>
 * <Damage Region x Opacity: y%>
 *
 * - Used for: Map Notetags
 * - Adjusts the starting opacity to 'y%' for this region-marked-tile's
 *   Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'y' with a percent (0% to 100%) representing the starting opacity
 *   used by the region-marked-tile's Map Damage Effect.
 *
 * ---
 *
 * <Map Damage Effect Region x Duration: y>
 * <Damage Region x Duration: y>
 *
 * - Used for: Map Notetags
 * - Alters the duration for this region-marked-tile's Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'y' with a number representing the duration of the Map Damage
 *   Effect in frames where 60 frames is equal to 1 second.
 *
 * ---
 *
 * <Map Damage Effect Region x Blend Mode: Normal>
 * <Map Damage Effect Region x Blend Mode: Additive>
 * <Map Damage Effect Region x Blend Mode: Multiply>
 * <Map Damage Effect Region x Blend Mode: Screen>
 * <Damage Region x Blend Mode: Normal>
 * <Damage Region x Blend Mode: Additive>
 * <Damage Region x Blend Mode: Multiply>
 * <Damage Region x Blend Mode: Screen>
 *
 * - Used for: Map Notetags
 * - Changes the blend mode used for the region-marked-tile's Map Damage Effect
 *   sprite to mesh with the map screen differently.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 *
 * ---
 *
 * <Map Damage Effect Region x Once Parallel: y>
 * <Damage Region x Once Parallel: y>
 *
 * - Used for: Map Notetags
 * - Requires VisuMZ_0_CoreEngine!
 * - Causes a region-marked-tile's Map Damage Effect to also launch a Once
 *   Parallel from the VisuMZ Core Engine.
 *   - A Once Parallel is a Common Event that occurs as a one-time parallel
 *     process event that does not loop.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a number (0 to 255) representing the Region ID used to
 *   mark the floor damage tiles with this Map Damage Effect.
 * - Replace 'y' with a number representing the ID of the Common Event to run
 *   as a Once Parallel.
 *
 * ---
 *
 * === State Damage-Related Notetags ===
 *
 * ---
 *
 * <Map Damage Effect Color: #rrggbb>
 * <Damage Color: #rrggbb>
 *
 * - Used for: State Notetags
 * - When taking damage from states on the map, play a custom color.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'rr' with a hexadecimal value for red.
 * - Replace 'gg' with a hexadecimal value for green.
 * - Replace 'bb' with a hexadecimal value for blue.
 * - Leave the '#' in place.
 * - If you are unsure of what hexadecimal value your color is, please use an
 *   online site like: https://htmlcolorcodes.com/
 * - When using this notetag, custom images won't be used.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Gradient Length: y>
 * <Damage Gradient Length: y>
 *
 * - Used for: State Notetags
 * - Changes the gradient length of the pre-render effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'y' with a number representing in pixels how long the gradient
 *   length is.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Image: filename>
 * <Damage Image: filename>
 *
 * - Used for: State Notetags
 * - Uses a custom image found in the img/pictures/ folder of your game project
 *   to use when taking damage from states on the map.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'filename' with a picture found within your game project's
 *   img/pictures/ folder. Filenames are case sensitive. Leave out the filename
 *   extension from the notetag.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 * - If this notetag is used, ignore the hex color and gradient notetags.
 *
 * ---
 *
 * <Map Damage Effect Opacity: y>
 * <Damage Opacity: y>
 *
 * - Used for: State Notetags
 * - Adjusts the starting opacity to 'y' for this damage state's
 *   Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'y' with a number (0 to 255) representing the starting opacity
 *   used by the region-marked-tile's Map Damage Effect.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Opacity: y%>
 * <Damage Opacity: y%>
 *
 * - Used for: State Notetags
 * - Adjusts the starting opacity to 'y%' for this damage state's
 *   Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'y' with a percent (0% to 100%) representing the starting opacity
 *   used by the region-marked-tile's Map Damage Effect.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Duration: y>
 * <Damage Duration: y>
 *
 * - Used for: State Notetags
 * - Alters the duration for this damage state's Map Damage Effect.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'y' with a number representing the duration of the Map Damage
 *   Effect in frames where 60 frames is equal to 1 second.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Blend Mode: Normal>
 * <Map Damage Effect Blend Mode: Additive>
 * <Map Damage Effect Blend Mode: Multiply>
 * <Map Damage Effect Blend Mode: Screen>
 * <Damage Blend Mode: Normal>
 * <Damage Blend Mode: Additive>
 * <Damage Blend Mode: Multiply>
 * <Damage Blend Mode: Screen>
 *
 * - Used for: State Notetags
 * - Changes the blend mode used for the damage state's Map Damage Effect
 *   sprite to mesh with the map screen differently.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * <Map Damage Effect Once Parallel: y>
 * <Damage Once Parallel: y>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_0_CoreEngine!
 * - Causes the damage state's Map Damage Effect to also launch a Once
 *   Parallel from the VisuMZ Core Engine.
 *   - A Once Parallel is a Common Event that occurs as a one-time parallel
 *     process event that does not loop.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'y' with a number representing the ID of the Common Event to run
 *   as a Once Parallel.
 * - If an actor has multiple damage states, the damage state with a Map Damage
 *   Effect and the highest priority will take effect.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Visual Plugin Commands ===
 *
 * ---
 *
 * Visual: Map Damage Effect
 * - Produces the visual Map Damage Effect without any damage tied to it.
 *
 *   Damage Appearance:
 *
 *     Hex Color:
 *     - Use #rrggbb for custom colors.
 *
 *       Gradient Length:
 *       - What is the length of the gradient effect?
 *
 *     Image Filename:
 *     - Filename of the map damage effect.
 *     - Using this will ignore "Hex Color" and "Gradient Length".
 *
 *   Damage Animation
 *
 *     Opacity:
 *     - What is the starting opacity before fading?
 *
 *     Total Duration:
 *     - How many frames should the animation be played?
 *
 *     Blend Mode:
 *     - The blend mode used for map damage effect.
 *
 *     Once Parallel:
 *     - Plays this Common Event as a "Once Parallel" upon trigger.
 *     - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * These settings found in the Plugin Parameters will determine the default
 * settings used across Floor-based Map Damage Effect types and Slip
 * State-based Map Damage Effect types.
 *
 * ---
 *
 * Floor
 *
 *   Damage Appearance:
 *
 *     Hex Color:
 *     - Use #rrggbb for custom colors.
 *
 *       Gradient Length:
 *       - What is the length of the gradient effect?
 *
 *     Image Filename:
 *     - Filename of the map damage effect.
 *     - Using this will ignore "Hex Color" and "Gradient Length".
 *
 *   Damage Animation
 *
 *     Opacity:
 *     - What is the starting opacity before fading?
 *
 *     Total Duration:
 *     - How many frames should the animation be played?
 *
 *     Blend Mode:
 *     - The blend mode used for map damage effect.
 *
 *     Once Parallel:
 *     - Plays this Common Event as a "Once Parallel" upon trigger.
 *     - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Slip-State
 *
 *   Damage Appearance:
 *
 *     Hex Color:
 *     - Use #rrggbb for custom colors.
 *
 *       Gradient Length:
 *       - What is the length of the gradient effect?
 *
 *     Image Filename:
 *     - Filename of the map damage effect.
 *     - Using this will ignore "Hex Color" and "Gradient Length".
 *
 *   Damage Animation
 *
 *     Opacity:
 *     - What is the starting opacity before fading?
 *
 *     Total Duration:
 *     - How many frames should the animation be played?
 *
 *     Blend Mode:
 *     - The blend mode used for map damage effect.
 *
 *     Once Parallel:
 *     - Plays this Common Event as a "Once Parallel" upon trigger.
 *     - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: November 4, 2022
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command VisualMapDamageEffect
 * @text Visual: Map Damage Effect
 * @desc Produces the visual Map Damage Effect without any damage tied to it.
 *
 * @arg Appearance
 * @text Damage Appearance
 *
 * @arg HexColor:str
 * @text Hex Color
 * @parent Appearance
 * @desc Use #rrggbb for custom colors.
 * @default #ed1c24
 *
 * @arg GradientLength:num
 * @text Gradient Length
 * @parent HexColor:str
 * @type number
 * @min 1
 * @desc What is the length of the gradient effect?
 * @default 128
 *
 * @arg ImageFilename:str
 * @text Image Filename
 * @parent Appearance
 * @type file
 * @dir img/pictures/
 * @require 1
 * @desc Filename of the map damage effect.
 * Using this will ignore "Hex Color" and "Gradient Length".
 * @default
 *
 * @arg Animation
 * @text Damage Animation
 *
 * @arg Opacity:num
 * @text Opacity
 * @parent Animation
 * @type number
 * @min 1
 * @max 255
 * @desc What is the starting opacity before fading?
 * @default 192
 *
 * @arg Duration:num
 * @text Total Duration
 * @parent Animation
 * @type number
 * @min 1
 * @desc How many frames should the animation be played?
 * @default 64
 *
 * @arg BlendMode:num
 * @text Blend Mode
 * @parent Animation
 * @type select
 * @option Normal
 * @value 0
 * @option Additive
 * @value 1
 * @option Multiply
 * @value 2
 * @option Screen
 * @value 3
 * @desc The blend mode used for map damage effect.
 * @default 0
 *
 * @arg OnceParallel:num
 * @text Once Parallel
 * @parent Animation
 * @type common_event
 * @desc Plays this Common Event as a "Once Parallel" upon trigger.
 * Requires VisuMZ_0_CoreEngine!
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MapDamageEffect
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param FloorAppearance
 * @text Floor Damage Appearance
 *
 * @param FloorHexColor:str
 * @text Hex Color
 * @parent FloorAppearance
 * @desc Use #rrggbb for custom colors.
 * @default #ed1c24
 *
 * @param FloorGradientLength:num
 * @text Gradient Length
 * @parent FloorHexColor:str
 * @type number
 * @min 1
 * @desc What is the length of the gradient effect?
 * @default 128
 *
 * @param FloorImageFilename:str
 * @text Image Filename
 * @parent FloorAppearance
 * @type file
 * @dir img/pictures/
 * @require 1
 * @desc Filename of the map damage effect.
 * Using this will ignore "Hex Color" and "Gradient Length".
 * @default
 *
 * @param FloorAnimation
 * @text Floor Damage Animation
 *
 * @param FloorOpacity:num
 * @text Start Opacity
 * @parent FloorAnimation
 * @type number
 * @min 1
 * @max 255
 * @desc What is the starting opacity before fading?
 * @default 192
 *
 * @param FloorDuration:num
 * @text Total Duration
 * @parent FloorAnimation
 * @type number
 * @min 1
 * @desc How many frames should the animation be played?
 * @default 64
 *
 * @param FloorBlendMode:num
 * @text Blend Mode
 * @parent FloorAnimation
 * @type select
 * @option Normal
 * @value 0
 * @option Additive
 * @value 1
 * @option Multiply
 * @value 2
 * @option Screen
 * @value 3
 * @desc The blend mode used for map damage effect.
 * @default 0
 *
 * @param FloorOnceParallel:num
 * @text Once Parallel
 * @parent FloorAnimation
 * @type common_event
 * @desc Plays this Common Event as a "Once Parallel" upon trigger.
 * Requires VisuMZ_0_CoreEngine!
 * @default 0
 *
 * @param SlipAppearance
 * @text Slip Damage Appearance
 *
 * @param SlipHexColor:str
 * @text Hex Color
 * @parent SlipAppearance
 * @desc Use #rrggbb for custom colors.
 * @default #8560a8
 *
 * @param SlipGradientLength:num
 * @text Gradient Length
 * @parent SlipHexColor:str
 * @type number
 * @min 1
 * @desc What is the length of the gradient effect?
 * @default 128
 *
 * @param SlipImageFilename:str
 * @text Image Filename
 * @parent SlipAppearance
 * @type file
 * @dir img/pictures/
 * @require 1
 * @desc Filename of the map damage effect.
 * Using this will ignore "Hex Color" and "Gradient Length".
 * @default
 *
 * @param SlipAnimation
 * @text Slip Damage Animation
 *
 * @param SlipOpacity:num
 * @text Start Opacity
 * @parent SlipAnimation
 * @type number
 * @min 1
 * @max 255
 * @desc What is the starting opacity before fading?
 * @default 192
 *
 * @param SlipDuration:num
 * @text Total Duration
 * @parent SlipAnimation
 * @type number
 * @min 1
 * @desc How many frames should the animation be played?
 * @default 64
 *
 * @param SlipBlendMode:num
 * @text Blend Mode
 * @parent SlipAnimation
 * @type select
 * @option Normal
 * @value 0
 * @option Additive
 * @value 1
 * @option Multiply
 * @value 2
 * @option Screen
 * @value 3
 * @desc The blend mode used for map damage effect.
 * @default 0
 *
 * @param SlipOnceParallel:num
 * @text Once Parallel
 * @parent SlipAnimation
 * @type common_event
 * @desc Plays this Common Event as a "Once Parallel" upon trigger.
 * Requires VisuMZ_0_CoreEngine!
 * @default 0
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================

const _0x4ed325 = _0xb2a0;
function _0x1f40() {
    const _0x3e777c = [
        'clearMapDamageEffect',
        'BLEND_MODES',
        'Game_Screen_onBattleStart',
        'length',
        'FloorDuration',
        '_spriteset',
        'Game_Actor_checkFloorEffect',
        '_mapDamageEffectRegions',
        '1484991zMdCCV',
        'push',
        'clamp',
        'Game_Actor_executeFloorDamage',
        'setupCustomFloorDamageFormula',
        'registerCommand',
        'imgFilename',
        'split',
        'MAP_DAMAGE_EFFECT_CONSTANT',
        'KQHxK',
        'OnceParallel',
        'note',
        'prototype',
        'additive',
        'fillRect',
        'ARRAYJSON',
        'isOnStateFloor',
        'isPlaySlipMapDamageEffect',
        'JnhWJ',
        '#ffffff',
        'setup',
        'jCalg',
        'EVAL',
        'Game_Actor_basicFloorDamage',
        'TRfAT',
        'includes',
        'blendModeParser',
        '_scene',
        'turnEndOnMap',
        'gradientLength',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'executeFloorStates',
        'ARRAYFUNC',
        'BlendMode',
        '_mapDamageRegionID',
        'isPlayFloorMapDamageEffect',
        'hexToRgba',
        '_mapDamageOpacity',
        '3840520XaenkN',
        'getFloorStatesAtRegion',
        '7962aYiybm',
        'setupMapDamageEffectImage',
        'DUFsA',
        '_mapDamageSprite',
        'width',
        'loadPicture',
        'onBattleStart',
        'startFlashForDamage',
        'regionId',
        'soYvD',
        '_mapDamageStateID',
        'toUpperCase',
        'HexColor',
        'states',
        'executeFloorDamage',
        '#8560a8',
        'prepareMapDamageEffectsBitmap',
        'Game_Screen_startFlashForDamage',
        'uuepB',
        'Spriteset_Map_update',
        'damage',
        'getFirstSlipStateWithMapDamageEffect',
        'MAP_DAMAGE_EFFECT',
        'updateMapDamageEffect',
        '_lastMapDamageStateID',
        'FloorOpacity',
        'call',
        'FloorHexColor',
        'opacityRate',
        'trim',
        'onceParallel',
        'test',
        'SlipImageFilename',
        '_executeFloorDamage',
        'customState',
        'status',
        'setupFloorStateEffects',
        'prepareMapDamageEffectsBlendMode',
        'TBvPa',
        'pMlKZ',
        'NORMAL',
        'tyFnO',
        'SCREEN',
        'isPlaytest',
        'ARRAYNUM',
        'Opacity',
        'FloorGradientLength',
        'rgba(0,0,0,0)',
        'PPIjO',
        'Game_Map_setup',
        'pop',
        'add',
        'toLowerCase',
        'gradientFillRect',
        'updateMapDamageEffectOpacity',
        'Game_Actor_turnEndOnMap',
        'startFloorMapDamageEffect',
        'setupMapDamageEffects',
        'FloorBlendMode',
        'isStateResist',
        'height',
        'slip',
        'getCustomFloorDamageAtRegion',
        'clear',
        'VXeGj',
        'match',
        'updateMapDamageEffectImage',
        'SLIP_DAMAGE_EFFECT',
        'VisualMapDamageEffect',
        'isOnDamageFloor',
        'ZSebM',
        'uCKct',
        'Game_Screen_update',
        'Game_Screen_clear',
        'createLowerLayer',
        'Vndxo',
        'constructor',
        'mCtRO',
        'JSON',
        'initMapDamageEffects',
        'parseMapDamageNotetagValue',
        'OpXyh',
        'join',
        '_mapDamageStallDuration',
        'map',
        'KilAm',
        'stringify',
        '18471204YfLVxQ',
        'Spriteset_Map_createLowerLayer',
        'blendMode',
        'opacity',
        'hexColor',
        'RegExp',
        'addChild',
        'Duration',
        '371YHcxRA',
        '_floorStateRegions',
        '_lastMapDamageRegion',
        'substring',
        'STR',
        'vZkeX',
        'MapDamageEffect',
        'max',
        'parse',
        'startSlipMapDamageEffect',
        'GradientLength',
        '_stateIDs',
        '4pksNoP',
        'pPgOJ',
        'BlOOA',
        'exit',
        '_mapDamageFormulaRegions',
        'SlipGradientLength',
        'SlipBlendMode',
        'basicFloorDamage',
        'filter',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'createMapDamageEffect',
        '#ed1c24',
        'log',
        'format',
        'FTosD',
        'WMaJk',
        'performCollapse',
        'agdxn',
        'normal',
        'customDmgFormula',
        'duration',
        'SlipDuration',
        'SlipOnceParallel',
        '_mapDamageDuration',
        'startCustomMapDamageEffect',
        'SlipHexColor',
        'bitmap',
        'ImageFilename',
        'allMembers',
        'description',
        'rzYlk',
        '_turnEndOnMap',
        'FiVBW',
        'ConvertParams',
        'any',
        'rxBXq',
        'ADD',
        'STRUCT',
        'Game_Player_isOnDamageFloor',
        'Settings',
        'screen',
        'CHdvy',
        'getStateIdWithName',
        'needsUpdateMapDamageEffectImage',
        'isSceneMap',
        'dIRxh',
        'clearResult',
        'parameters',
        'iabAT',
        'ARRAYEVAL',
        'ARRAYSTRUCT',
        'name',
        '530254uBiKuh',
        '1889335BnwQnI',
        'dQoYM',
        '822494dBeAQA',
        'isDead',
        'update',
        'ceil',
        'setMapDamageEffectSetting',
        'getMapDamageEffectSettings',
    ];
    _0x1f40 = function () {
        return _0x3e777c;
    };
    return _0x1f40();
}
(function (_0xccfc58, _0x36472d) {
    const _0x4b6143 = _0xb2a0,
        _0x59c69c = _0xccfc58();
    while (!![]) {
        try {
            const _0x2d4fd1 =
                parseInt(_0x4b6143(0x1af)) / 0x1 +
                parseInt(_0x4b6143(0x1b2)) / 0x2 +
                (-parseInt(_0x4b6143(0x1c0)) / 0x3) * (-parseInt(_0x4b6143(0x17b)) / 0x4) +
                parseInt(_0x4b6143(0x1b0)) / 0x5 +
                (parseInt(_0x4b6143(0x1e8)) / 0x6) * (parseInt(_0x4b6143(0x16f)) / 0x7) +
                parseInt(_0x4b6143(0x1e6)) / 0x8 +
                -parseInt(_0x4b6143(0x167)) / 0x9;
            if (_0x2d4fd1 === _0x36472d) break;
            else _0x59c69c['push'](_0x59c69c['shift']());
        } catch (_0x49be7a) {
            _0x59c69c['push'](_0x59c69c['shift']());
        }
    }
})(_0x1f40, 0x4c455);
var label = 'MapDamageEffect',
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x4ed325(0x183)](function (_0xaa978) {
        const _0x464cda = _0x4ed325;
        return (
            _0xaa978[_0x464cda(0x20b)] &&
            _0xaa978['description'][_0x464cda(0x1d9)]('[' + label + ']')
        );
    })[0x0];
function _0xb2a0(_0x2213cb, _0x1587bf) {
    const _0x1f400d = _0x1f40();
    return (
        (_0xb2a0 = function (_0xb2a04b, _0x1aeb26) {
            _0xb2a04b = _0xb2a04b - 0x159;
            let _0xae5331 = _0x1f400d[_0xb2a04b];
            return _0xae5331;
        }),
        _0xb2a0(_0x2213cb, _0x1587bf)
    );
}
((VisuMZ[label][_0x4ed325(0x1a2)] = VisuMZ[label][_0x4ed325(0x1a2)] || {}),
    (VisuMZ['ConvertParams'] = function (_0x42dd43, _0x489ef8) {
        const _0x18dd92 = _0x4ed325;
        for (const _0x8744d in _0x489ef8) {
            if (_0x8744d['match'](/(.*):(.*)/i)) {
                const _0x374740 = String(RegExp['$1']),
                    _0x116eed = String(RegExp['$2'])[_0x18dd92(0x1f3)]()[_0x18dd92(0x205)]();
                let _0x459748, _0xe6a7ea, _0x469dca;
                switch (_0x116eed) {
                    case 'NUM':
                        _0x459748 = _0x489ef8[_0x8744d] !== '' ? Number(_0x489ef8[_0x8744d]) : 0x0;
                        break;
                    case _0x18dd92(0x214):
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== ''
                                ? JSON[_0x18dd92(0x177)](_0x489ef8[_0x8744d])
                                : []),
                            (_0x459748 = _0xe6a7ea[_0x18dd92(0x164)](_0x23b82c =>
                                Number(_0x23b82c)
                            )));
                        break;
                    case _0x18dd92(0x1d6):
                        _0x459748 = _0x489ef8[_0x8744d] !== '' ? eval(_0x489ef8[_0x8744d]) : null;
                        break;
                    case _0x18dd92(0x1ac):
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== '' ? JSON['parse'](_0x489ef8[_0x8744d]) : []),
                            (_0x459748 = _0xe6a7ea[_0x18dd92(0x164)](_0x37a3ef =>
                                eval(_0x37a3ef)
                            )));
                        break;
                    case _0x18dd92(0x15e):
                        _0x459748 =
                            _0x489ef8[_0x8744d] !== ''
                                ? JSON[_0x18dd92(0x177)](_0x489ef8[_0x8744d])
                                : '';
                        break;
                    case _0x18dd92(0x1cf):
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== '' ? JSON['parse'](_0x489ef8[_0x8744d]) : []),
                            (_0x459748 = _0xe6a7ea[_0x18dd92(0x164)](_0x33a770 =>
                                JSON[_0x18dd92(0x177)](_0x33a770)
                            )));
                        break;
                    case 'FUNC':
                        _0x459748 =
                            _0x489ef8[_0x8744d] !== ''
                                ? new Function(JSON['parse'](_0x489ef8[_0x8744d]))
                                : new Function('return\x200');
                        break;
                    case _0x18dd92(0x1e0):
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== ''
                                ? JSON[_0x18dd92(0x177)](_0x489ef8[_0x8744d])
                                : []),
                            (_0x459748 = _0xe6a7ea[_0x18dd92(0x164)](
                                _0x15dff5 => new Function(JSON[_0x18dd92(0x177)](_0x15dff5))
                            )));
                        break;
                    case _0x18dd92(0x173):
                        _0x459748 = _0x489ef8[_0x8744d] !== '' ? String(_0x489ef8[_0x8744d]) : '';
                        break;
                    case 'ARRAYSTR':
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== ''
                                ? JSON[_0x18dd92(0x177)](_0x489ef8[_0x8744d])
                                : []),
                            (_0x459748 = _0xe6a7ea['map'](_0x5bd2f8 => String(_0x5bd2f8))));
                        break;
                    case _0x18dd92(0x1a0):
                        ((_0x469dca =
                            _0x489ef8[_0x8744d] !== '' ? JSON['parse'](_0x489ef8[_0x8744d]) : {}),
                            (_0x459748 = VisuMZ[_0x18dd92(0x19c)]({}, _0x469dca)));
                        break;
                    case _0x18dd92(0x1ad):
                        ((_0xe6a7ea =
                            _0x489ef8[_0x8744d] !== '' ? JSON['parse'](_0x489ef8[_0x8744d]) : []),
                            (_0x459748 = _0xe6a7ea[_0x18dd92(0x164)](_0x5d4df4 =>
                                VisuMZ[_0x18dd92(0x19c)]({}, JSON[_0x18dd92(0x177)](_0x5d4df4))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x42dd43[_0x374740] = _0x459748;
            }
        }
        return _0x42dd43;
    }),
    (_0xbf8e7c => {
        const _0x49bae3 = _0x4ed325,
            _0x2c96b9 = _0xbf8e7c['name'];
        for (const _0x28f229 of dependencies) {
            if (!Imported[_0x28f229]) {
                if (_0x49bae3(0x22f) !== 'uCKct') {
                    if (this[_0x49bae3(0x1b3)]()) this['performCollapse']();
                    this['clearResult']();
                } else {
                    (alert(
                        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.'[
                            _0x49bae3(0x188)
                        ](_0x2c96b9, _0x28f229)
                    ),
                        SceneManager[_0x49bae3(0x17e)]());
                    break;
                }
            }
        }
        const _0x435a60 = _0xbf8e7c[_0x49bae3(0x198)];
        if (_0x435a60[_0x49bae3(0x229)](/\[Version[ ](.*?)\]/i)) {
            if (_0x49bae3(0x19e) === _0x49bae3(0x19e)) {
                const _0xe877d6 = Number(RegExp['$1']);
                if (_0xe877d6 !== VisuMZ[label]['version']) {
                    if ('kfTyy' === _0x49bae3(0x20f)) {
                        if (_0x157b45['onceParallel']) _0x1bef12(_0xedc222['onceParallel']);
                    } else
                        (alert(_0x49bae3(0x184)[_0x49bae3(0x188)](_0x2c96b9, _0xe877d6)),
                            SceneManager[_0x49bae3(0x17e)]());
                }
            } else
                ((this[_0x49bae3(0x1bf)] = {}),
                    (this[_0x49bae3(0x1bf)][0x0] = _0x1a463c[_0x49bae3(0x177)](
                        _0x242191[_0x49bae3(0x166)](_0x402e16[_0x49bae3(0x1fe)])
                    )),
                    _0x5d9827[_0x49bae3(0x1fe)][_0x49bae3(0x1c6)] !== '' &&
                        _0x3930b3[_0x49bae3(0x1ed)](
                            _0x5cf055[_0x49bae3(0x1fe)][_0x49bae3(0x1c6)][_0x49bae3(0x205)]()
                        ),
                    (this[_0x49bae3(0x170)] = {}),
                    (this[_0x49bae3(0x170)][0x0] = []),
                    (this[_0x49bae3(0x17f)] = {}));
        }
        if (_0x435a60['match'](/\[Tier[ ](\d+)\]/i)) {
            if (_0x49bae3(0x22e) === 'evCaQ') {
                if (_0x10518f[_0x49bae3(0x213)]()) _0x16bd21[_0x49bae3(0x187)](_0x4d00eb);
                return _0x42d8c4;
            } else {
                const _0x15a754 = Number(RegExp['$1']);
                if (_0x15a754 < tier)
                    _0x49bae3(0x199) !== _0x49bae3(0x199)
                        ? ((this[_0x49bae3(0x1e2)] = -0x1),
                          (this[_0x49bae3(0x1f2)] = -0x1),
                          (this['_mapDamageOpacity'] = 0x0),
                          (this[_0x49bae3(0x192)] = 0x0),
                          (this[_0x49bae3(0x163)] = 0x0))
                        : (alert(_0x49bae3(0x1de)[_0x49bae3(0x188)](_0x2c96b9, _0x15a754, tier)),
                          SceneManager[_0x49bae3(0x17e)]());
                else {
                    if (_0x49bae3(0x1d8) === _0x49bae3(0x161)) {
                        const _0x475280 = this['_mapDamageDuration'];
                        (this[_0x49bae3(0x163)]++ > _0xec8a3b[_0x49bae3(0x1c8)] &&
                            ((this[_0x49bae3(0x1e5)] *= (_0x475280 - 0x1) / _0x475280),
                            this[_0x49bae3(0x192)]--),
                            this[_0x49bae3(0x192)] <= 0x0 &&
                                ((this[_0x49bae3(0x1e2)] = -0x1),
                                (this[_0x49bae3(0x1f2)] = -0x1),
                                (this[_0x49bae3(0x1e5)] = 0x0),
                                (this[_0x49bae3(0x163)] = 0x0)));
                    } else tier = Math['max'](_0x15a754, tier);
                }
            }
        }
        VisuMZ[_0x49bae3(0x19c)](VisuMZ[label][_0x49bae3(0x1a2)], _0xbf8e7c[_0x49bae3(0x1aa)]);
    })(pluginData),
    PluginManager[_0x4ed325(0x1c5)](pluginData['name'], _0x4ed325(0x22c), _0x596788 => {
        const _0x5109c8 = _0x4ed325;
        if (!SceneManager[_0x5109c8(0x1a7)]()) return;
        VisuMZ['ConvertParams'](_0x596788, _0x596788);
        const _0x5a3e68 = {
            hexColor: _0x596788[_0x5109c8(0x1f4)] || _0x5109c8(0x186),
            gradientLength: _0x596788['GradientLength'] || 0x1,
            imgFilename: _0x596788[_0x5109c8(0x196)] || '',
            opacity: _0x596788[_0x5109c8(0x215)] || 0x1,
            duration: _0x596788[_0x5109c8(0x16e)] || 0x1,
            blendMode: _0x596788[_0x5109c8(0x1e1)] || 0x0,
            onceParallel: _0x596788['OnceParallel'] || 0x0,
        };
        $gameScreen['startCustomMapDamageEffect'](_0x5a3e68);
        const _0x3b6b2b = SceneManager['_scene'][_0x5109c8(0x1bd)];
        _0x3b6b2b && _0x3b6b2b[_0x5109c8(0x1e9)](_0x5a3e68);
    }),
    (VisuMZ[_0x4ed325(0x175)]['RegExp'] = {
        floor: {
            hexColor:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:COLOR|HEX COLOR):[ ]#(.*)>/gi,
            gradientLength:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:GRADIENT LENGTH|LENGTH):[ ](\d+)>/gi,
            imgFilename:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:IMAGE FILENAME|IMAGE|IMG|FILENAME):[ ](.*)>/gi,
            opacity: /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:OPACITY|OP):[ ](\d+)>/gi,
            opacityRate:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:OPACITY|OPACITY RATE):[ ](\d+)([%％])>/gi,
            duration:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:DURATION|DUR):[ ](\d+)>/gi,
            blendMode:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:BLEND MODE|BLEND|BLENDMODE):[ ](.*)>/gi,
            onceParallel:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)REGION (\d+) (?:ONCE|ONCE PARALLEL|COMMON EVENT):[ ](\d+)>/gi,
        },
        slip: {
            any: /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(.*)>/gi,
            hexColor: /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:COLOR|HEX COLOR):[ ]#(.*)>/gi,
            gradientLength:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:GRADIENT LENGTH|LENGTH):[ ](\d+)>/gi,
            imgFilename:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:IMAGE FILENAME|IMAGE|IMG|FILENAME):[ ](.*)>/gi,
            opacity: /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:OPACITY|OP):[ ](\d+)>/gi,
            opacityRate:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:OPACITY|OPACITY RATE):[ ](\d+)([%％])>/gi,
            duration: /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:DURATION|DUR):[ ](\d+)>/gi,
            blendMode:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:BLEND MODE|BLEND|BLENDMODE):[ ](.*)>/gi,
            onceParallel:
                /<(?:MAP |)(?:DMG|DAMAGE) (?:EFFECT |)(?:ONCE|ONCE PARALLEL|COMMON EVENT):[ ](\d+)>/gi,
        },
        customState: /<FLOOR (?:STATE|STATES) REGION (\d+):[ ](.*)>/gi,
        customDmgFormula:
            /<(?:FLOOR |)(?:DMG|DAMAGE) FORMULA REGION (\d+)>\s*([\s\S]*?)\s*<\/(?:MAP |)(?:DMG|DAMAGE) FORMULA REGION (\d+)>/gi,
    }),
    (DataManager['getStateIdWithName'] = function (_0x4c5812) {
        const _0x3e5cf7 = _0x4ed325;
        ((_0x4c5812 = _0x4c5812[_0x3e5cf7(0x1f3)]()['trim']()),
            (this[_0x3e5cf7(0x17a)] = this[_0x3e5cf7(0x17a)] || {}));
        if (this[_0x3e5cf7(0x17a)][_0x4c5812]) return this[_0x3e5cf7(0x17a)][_0x4c5812];
        for (const _0x11cd30 of $dataStates) {
            if (!_0x11cd30) continue;
            this[_0x3e5cf7(0x17a)][_0x11cd30[_0x3e5cf7(0x1ae)][_0x3e5cf7(0x1f3)]()['trim']()] =
                _0x11cd30['id'];
        }
        return this[_0x3e5cf7(0x17a)][_0x4c5812] || 0x0;
    }),
    (DataManager['getMapDamageEffectSettings'] = function (_0x6a2d63) {
        const _0xcf0de2 = _0x4ed325,
            _0x33ba0f = JSON[_0xcf0de2(0x177)](
                JSON[_0xcf0de2(0x166)](Game_Actor[_0xcf0de2(0x22b)])
            ),
            _0x2f2c69 = $dataStates[_0x6a2d63];
        if (_0x2f2c69) {
            const _0x1dc0d5 = _0x2f2c69['note'] || '',
                _0xcca9dc = VisuMZ[_0xcf0de2(0x175)]['RegExp'][_0xcf0de2(0x225)],
                _0x5e30ea = [
                    _0xcf0de2(0x16b),
                    _0xcf0de2(0x1dd),
                    _0xcf0de2(0x1c6),
                    'opacity',
                    _0xcf0de2(0x204),
                    _0xcf0de2(0x18f),
                    'blendMode',
                    _0xcf0de2(0x206),
                ];
            for (const _0x101d26 of _0x5e30ea) {
                if (_0x1dc0d5[_0xcf0de2(0x229)](_0xcca9dc[_0x101d26])) {
                    const _0x55ac70 = DataManager[_0xcf0de2(0x160)](_0x101d26, RegExp['$1']);
                    (_0x101d26 === _0xcf0de2(0x16b) && (_0x33ba0f[_0xcf0de2(0x1c6)] = ''),
                        (_0x33ba0f[_0x101d26] = _0x55ac70));
                }
            }
        }
        return _0x33ba0f;
    }),
    (DataManager[_0x4ed325(0x160)] = function (_0x3458d5, _0x527abb) {
        const _0x1b2f76 = _0x4ed325;
        if (_0x3458d5 === _0x1b2f76(0x16b)) _0x527abb = '#' + _0x527abb;
        if (_0x3458d5 === _0x1b2f76(0x1dd)) _0x527abb = Math['max'](0x1, Number(_0x527abb));
        _0x3458d5 === _0x1b2f76(0x204) &&
            (_0x1b2f76(0x15b) === _0x1b2f76(0x1a4)
                ? (_0xa92d4a = _0x4eb73a(_0x3ba12b))
                : ((_0x527abb = Math['ceil'](Number(_0x527abb) * 0.01)),
                  (_0x3458d5 = _0x1b2f76(0x16a))));
        if (_0x3458d5 === _0x1b2f76(0x16a)) _0x527abb = Number(_0x527abb)['clamp'](0x0, 0xff);
        if (_0x3458d5 === _0x1b2f76(0x18f))
            _0x527abb = Math[_0x1b2f76(0x176)](0x1, Number(_0x527abb));
        return (
            _0x3458d5 === _0x1b2f76(0x169) &&
                (_0x527abb = ColorManager[_0x1b2f76(0x1da)](_0x527abb) || 0x0),
            _0x3458d5 === _0x1b2f76(0x206) &&
                (_0x527abb = Math[_0x1b2f76(0x176)](0x0, Number(_0x527abb))),
            _0x3458d5 === _0x1b2f76(0x1c6) &&
                ImageManager[_0x1b2f76(0x1ed)](_0x527abb[_0x1b2f76(0x205)]()),
            _0x527abb
        );
    }),
    (ImageManager['createMapDamageEffect'] = function (_0x172bef, _0x2ab755) {
        const _0x3d4f1f = _0x4ed325,
            _0x14088e = ColorManager[_0x3d4f1f(0x1e4)](_0x172bef, 0x1),
            _0x5d4ff0 = ColorManager[_0x3d4f1f(0x1e4)](_0x172bef, 0x0),
            _0x16af04 = ColorManager[_0x3d4f1f(0x1e4)](_0x172bef, 0.2),
            _0x286d4c = new Bitmap(Graphics[_0x3d4f1f(0x1ec)], Graphics[_0x3d4f1f(0x224)]);
        return (
            _0x286d4c[_0x3d4f1f(0x21d)](
                0x0,
                0x0,
                _0x2ab755,
                Graphics[_0x3d4f1f(0x224)],
                _0x14088e,
                _0x5d4ff0,
                ![]
            ),
            _0x286d4c[_0x3d4f1f(0x21d)](
                Graphics['width'] - _0x2ab755,
                0x0,
                _0x2ab755,
                Graphics[_0x3d4f1f(0x224)],
                _0x5d4ff0,
                _0x14088e,
                ![]
            ),
            _0x286d4c[_0x3d4f1f(0x21d)](
                0x0,
                0x0,
                Graphics['width'],
                _0x2ab755,
                _0x14088e,
                _0x5d4ff0,
                !![]
            ),
            _0x286d4c[_0x3d4f1f(0x21d)](
                0x0,
                Graphics[_0x3d4f1f(0x224)] - _0x2ab755,
                Graphics[_0x3d4f1f(0x1ec)],
                _0x2ab755,
                _0x5d4ff0,
                _0x14088e,
                !![]
            ),
            _0x286d4c[_0x3d4f1f(0x1ce)](
                0x0,
                0x0,
                Graphics[_0x3d4f1f(0x1ec)],
                Graphics[_0x3d4f1f(0x224)],
                _0x16af04
            ),
            _0x286d4c
        );
    }),
    (ColorManager[_0x4ed325(0x1da)] = function (_0x4799f6) {
        const _0x34c830 = _0x4ed325;
        _0x4799f6 = _0x4799f6[_0x34c830(0x21c)]();
        switch (_0x4799f6) {
            case _0x34c830(0x18d):
                return PIXI[_0x34c830(0x1b9)][_0x34c830(0x210)];
            case _0x34c830(0x21b):
            case _0x34c830(0x1cd):
                return PIXI[_0x34c830(0x1b9)][_0x34c830(0x19f)];
            case 'multiply':
                return PIXI[_0x34c830(0x1b9)]['MULTIPLY'];
            case _0x34c830(0x1a3):
                return PIXI[_0x34c830(0x1b9)][_0x34c830(0x212)];
        }
    }),
    (ColorManager['hexToRgba'] = function (_0x5544f2, _0x24a2af) {
        const _0x2c402f = _0x4ed325;
        let _0x55080a = '';
        if (/^#([A-Fa-f0-9]{3}){1,2}$/[_0x2c402f(0x207)](_0x5544f2)) {
            _0x55080a = _0x5544f2[_0x2c402f(0x172)](0x1)['split']('');
            _0x55080a['length'] === 0x3 &&
                (_0x2c402f(0x18a) === _0x2c402f(0x1d2)
                    ? (this[_0x2c402f(0x1eb)][_0x2c402f(0x16a)] =
                          _0x1f5c55[_0x2c402f(0x1e5)] || 0x0)
                    : (_0x55080a = [
                          _0x55080a[0x0],
                          _0x55080a[0x0],
                          _0x55080a[0x1],
                          _0x55080a[0x1],
                          _0x55080a[0x2],
                          _0x55080a[0x2],
                      ]));
            while (_0x55080a[_0x2c402f(0x1bb)] > 0x6) _0x55080a[_0x2c402f(0x21a)]();
            return (
                (_0x55080a = '0x' + _0x55080a[_0x2c402f(0x162)]('')),
                'rgba(' +
                    [
                        ((_0x55080a >> 0x10) & 0xff)['clamp'](0x0, 0xff),
                        ((_0x55080a >> 0x8) & 0xff)[_0x2c402f(0x1c2)](0x0, 0xff),
                        (_0x55080a & 0xff)[_0x2c402f(0x1c2)](0x0, 0xff),
                    ]['join'](',') +
                    ',' +
                    _0x24a2af[_0x2c402f(0x1c2)](0x0, 0x1) +
                    ')'
            );
        } else return _0x2c402f(0x217);
    }),
    (SceneManager['isSceneMap'] = function () {
        const _0x3e1ba3 = _0x4ed325;
        return this[_0x3e1ba3(0x1db)] && this[_0x3e1ba3(0x1db)][_0x3e1ba3(0x15c)] === Scene_Map;
    }),
    (Game_Screen[_0x4ed325(0x1c8)] = 0x14),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x159)] = Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x227)]),
    (Game_Screen['prototype'][_0x4ed325(0x227)] = function () {
        const _0x21c8a3 = _0x4ed325;
        (VisuMZ[_0x21c8a3(0x175)][_0x21c8a3(0x159)][_0x21c8a3(0x202)](this),
            this[_0x21c8a3(0x1b8)]());
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1ba)] = Game_Screen['prototype']['onBattleStart']),
    (Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x1ee)] = function () {
        const _0x240d1e = _0x4ed325;
        (VisuMZ[_0x240d1e(0x175)][_0x240d1e(0x1ba)][_0x240d1e(0x202)](this),
            this[_0x240d1e(0x1b8)]());
    }),
    (VisuMZ['MapDamageEffect'][_0x4ed325(0x230)] = Game_Screen[_0x4ed325(0x1cc)]['update']),
    (Game_Screen[_0x4ed325(0x1cc)]['update'] = function () {
        const _0x4e00ec = _0x4ed325;
        (VisuMZ[_0x4e00ec(0x175)][_0x4e00ec(0x230)][_0x4e00ec(0x202)](this),
            this['updateMapDamageEffect']());
    }),
    (Game_Screen[_0x4ed325(0x1cc)]['clearMapDamageEffect'] = function () {
        const _0x4bf349 = _0x4ed325;
        ((this[_0x4bf349(0x1e2)] = -0x1),
            (this['_mapDamageStateID'] = -0x1),
            (this[_0x4bf349(0x1e5)] = 0x0),
            (this['_mapDamageDuration'] = 0x0),
            (this[_0x4bf349(0x163)] = 0x0));
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1f9)] = Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x1ef)]),
    (Game_Screen[_0x4ed325(0x1cc)]['startFlashForDamage'] = function () {
        const _0x2cd935 = _0x4ed325;
        if (this[_0x2cd935(0x1e3)]()) this[_0x2cd935(0x220)]();
        else
            this[_0x2cd935(0x1d1)]()
                ? this['startSlipMapDamageEffect']()
                : VisuMZ[_0x2cd935(0x175)]['Game_Screen_startFlashForDamage'][_0x2cd935(0x202)](
                      this
                  );
    }),
    (Game_Screen[_0x4ed325(0x1cc)]['updateMapDamageEffect'] = function () {
        const _0x4f98f5 = _0x4ed325;
        if (this[_0x4f98f5(0x192)] > 0x0) {
            const _0x3301ce = this[_0x4f98f5(0x192)];
            (this[_0x4f98f5(0x163)]++ > Game_Screen[_0x4f98f5(0x1c8)] &&
                ((this[_0x4f98f5(0x1e5)] *= (_0x3301ce - 0x1) / _0x3301ce),
                this[_0x4f98f5(0x192)]--),
                this[_0x4f98f5(0x192)] <= 0x0 &&
                    ((this[_0x4f98f5(0x1e2)] = -0x1),
                    (this[_0x4f98f5(0x1f2)] = -0x1),
                    (this[_0x4f98f5(0x1e5)] = 0x0),
                    (this[_0x4f98f5(0x163)] = 0x0)));
        }
    }),
    (Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x1e3)] = function () {
        const _0x2ad046 = _0x4ed325;
        if (!SceneManager[_0x2ad046(0x1a7)]()) return ![];
        if (!$gameMap) return ![];
        const _0x5dc066 = $gamePlayer[_0x2ad046(0x1f0)](),
            _0x567893 = $gameMap[_0x2ad046(0x1b7)](_0x5dc066);
        if (_0x567893['hexColor'] === '' && _0x567893[_0x2ad046(0x1c6)] === '') return ![];
        return $gameTemp['_executeFloorDamage'];
    }),
    (Game_Screen['prototype'][_0x4ed325(0x220)] = function () {
        const _0xe7167e = _0x4ed325,
            _0x4da18f = $gamePlayer[_0xe7167e(0x1f0)](),
            _0x393a12 = $gameMap['getMapDamageEffectSettings'](_0x4da18f);
        ((this[_0xe7167e(0x1e2)] = _0x4da18f),
            (this[_0xe7167e(0x1f2)] = -0x1),
            (this[_0xe7167e(0x1e5)] = _0x393a12[_0xe7167e(0x16a)]),
            (this['_mapDamageDuration'] = _0x393a12[_0xe7167e(0x18f)]));
        this[_0xe7167e(0x192)] >= Game_Screen[_0xe7167e(0x1c8)]
            ? ((this[_0xe7167e(0x192)] -= Game_Screen['MAP_DAMAGE_EFFECT_CONSTANT']),
              (this[_0xe7167e(0x163)] = 0x0))
            : (this[_0xe7167e(0x163)] = Game_Screen[_0xe7167e(0x1c8)]);
        if (Imported['VisuMZ_0_CoreEngine'] && $onceParallel) {
            if (_0x393a12[_0xe7167e(0x206)]) $onceParallel(_0x393a12[_0xe7167e(0x206)]);
        }
    }),
    (Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x1d1)] = function () {
        const _0x15e86e = _0x4ed325;
        if (!SceneManager[_0x15e86e(0x1a7)]()) return ![];
        if (!$gameMap) return ![];
        return $gameTemp[_0x15e86e(0x19a)];
    }),
    (Game_Screen['prototype']['startSlipMapDamageEffect'] = function () {
        const _0x1ba560 = _0x4ed325,
            _0x4c28d1 = $gameParty[_0x1ba560(0x1fd)](),
            _0x15429b = _0x4c28d1 ? _0x4c28d1['id'] : 0xa455,
            _0x33e110 = DataManager['getMapDamageEffectSettings'](_0x15429b);
        ((this['_mapDamageRegionID'] = -0x1),
            (this[_0x1ba560(0x1f2)] = _0x15429b),
            (this[_0x1ba560(0x1e5)] = _0x33e110[_0x1ba560(0x16a)]),
            (this[_0x1ba560(0x192)] = _0x33e110[_0x1ba560(0x18f)]));
        this[_0x1ba560(0x192)] >= Game_Screen[_0x1ba560(0x1c8)]
            ? ((this[_0x1ba560(0x192)] -= Game_Screen[_0x1ba560(0x1c8)]),
              (this[_0x1ba560(0x163)] = 0x0))
            : _0x1ba560(0x1fa) !== _0x1ba560(0x1fa)
              ? this[_0x1ba560(0x178)]()
              : (this[_0x1ba560(0x163)] = Game_Screen['MAP_DAMAGE_EFFECT_CONSTANT']);
        if (Imported['VisuMZ_0_CoreEngine'] && $onceParallel) {
            if ('KQHxK' !== _0x1ba560(0x1c9)) {
                if (_0x34ffec['onceParallel']) _0x3a81bb(_0xf40c86[_0x1ba560(0x206)]);
            } else {
                if (_0x33e110[_0x1ba560(0x206)]) $onceParallel(_0x33e110[_0x1ba560(0x206)]);
            }
        }
    }),
    (Game_Screen[_0x4ed325(0x1cc)][_0x4ed325(0x193)] = function (_0x19beab) {
        const _0x5cf4b0 = _0x4ed325;
        ((this[_0x5cf4b0(0x1e2)] = -0x1),
            (this[_0x5cf4b0(0x1f2)] = -0x1),
            (this[_0x5cf4b0(0x1e5)] = _0x19beab[_0x5cf4b0(0x16a)]),
            (this[_0x5cf4b0(0x192)] = _0x19beab['duration']));
        if (this[_0x5cf4b0(0x192)] >= Game_Screen['MAP_DAMAGE_EFFECT_CONSTANT']) {
            if (_0x5cf4b0(0x20e) === _0x5cf4b0(0x228)) {
                if (!_0x3a38a1[_0x5cf4b0(0x1a7)]()) return;
                _0x591090[_0x5cf4b0(0x19c)](_0x46b355, _0xd5873c);
                const _0x3a267e = {
                    hexColor: _0x43c339['HexColor'] || _0x5cf4b0(0x186),
                    gradientLength: _0x595b76[_0x5cf4b0(0x179)] || 0x1,
                    imgFilename: _0x270c44[_0x5cf4b0(0x196)] || '',
                    opacity: _0x1be4cf[_0x5cf4b0(0x215)] || 0x1,
                    duration: _0x3020e5['Duration'] || 0x1,
                    blendMode: _0x18ad1a[_0x5cf4b0(0x1e1)] || 0x0,
                    onceParallel: _0x3f9e6a[_0x5cf4b0(0x1ca)] || 0x0,
                };
                _0x33957f[_0x5cf4b0(0x193)](_0x3a267e);
                const _0x16d4e4 = _0x8cc9e3[_0x5cf4b0(0x1db)][_0x5cf4b0(0x1bd)];
                _0x16d4e4 && _0x16d4e4[_0x5cf4b0(0x1e9)](_0x3a267e);
            } else
                ((this[_0x5cf4b0(0x192)] -= Game_Screen[_0x5cf4b0(0x1c8)]),
                    (this['_mapDamageStallDuration'] = 0x0));
        } else this[_0x5cf4b0(0x163)] = Game_Screen['MAP_DAMAGE_EFFECT_CONSTANT'];
        if (Imported['VisuMZ_0_CoreEngine'] && $onceParallel) {
            if (_0x19beab[_0x5cf4b0(0x206)]) $onceParallel(_0x19beab['onceParallel']);
        }
    }),
    (Game_Map[_0x4ed325(0x1fe)] = {
        hexColor: VisuMZ['MapDamageEffect'][_0x4ed325(0x1a2)][_0x4ed325(0x203)] || _0x4ed325(0x186),
        gradientLength: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x216)] || 0x1,
        imgFilename: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)]['FloorImageFilename'] || '',
        opacity: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x201)] || 0x1,
        duration: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x1bc)] || 0x1,
        blendMode: VisuMZ[_0x4ed325(0x175)]['Settings'][_0x4ed325(0x222)] || 0x0,
        onceParallel: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)]['FloorOnceParallel'] || 0x0,
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x219)] = Game_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1d4)]),
    (Game_Map['prototype'][_0x4ed325(0x1d4)] = function (_0x11d5e1) {
        const _0x7d952d = _0x4ed325;
        (VisuMZ[_0x7d952d(0x175)]['Game_Map_setup']['call'](this, _0x11d5e1),
            this[_0x7d952d(0x15f)](),
            this['setupMapDamageEffects'](),
            this['setupFloorStateEffects'](),
            this['setupCustomFloorDamageFormula']());
    }),
    (Game_Map[_0x4ed325(0x1cc)][_0x4ed325(0x15f)] = function () {
        const _0x1a82d9 = _0x4ed325;
        ((this[_0x1a82d9(0x1bf)] = {}),
            (this['_mapDamageEffectRegions'][0x0] = JSON[_0x1a82d9(0x177)](
                JSON['stringify'](Game_Map[_0x1a82d9(0x1fe)])
            )));
        if (Game_Map['MAP_DAMAGE_EFFECT'][_0x1a82d9(0x1c6)] !== '') {
            if ('KilAm' !== _0x1a82d9(0x165))
                for (const _0x2ed212 of _0x21e815) {
                    _0x2ed212[_0x1a82d9(0x229)](_0x321598);
                    const _0xa26786 = _0x4560e3(_0x26c69c['$1'])[_0x1a82d9(0x1c2)](0x0, 0xff),
                        _0x5e5956 = _0xf8a2b7(_0x343c79['$2']);
                    this['_mapDamageFormulaRegions'][_0xa26786] = _0x5e5956;
                }
            else
                ImageManager[_0x1a82d9(0x1ed)](
                    Game_Map[_0x1a82d9(0x1fe)][_0x1a82d9(0x1c6)][_0x1a82d9(0x205)]()
                );
        }
        ((this[_0x1a82d9(0x170)] = {}),
            (this[_0x1a82d9(0x170)][0x0] = []),
            (this[_0x1a82d9(0x17f)] = {}));
    }),
    (Game_Map['prototype'][_0x4ed325(0x221)] = function () {
        const _0x4e8cd3 = _0x4ed325,
            _0x4fb80b = $dataMap ? $dataMap['note'] || '' : '';
        if (_0x4fb80b[_0x4e8cd3(0x1bb)] <= 0x0) return;
        const _0x49623a = VisuMZ[_0x4e8cd3(0x175)][_0x4e8cd3(0x16c)]['floor'],
            _0x58ba33 = [
                _0x4e8cd3(0x16b),
                _0x4e8cd3(0x1dd),
                _0x4e8cd3(0x1c6),
                'opacity',
                'opacityRate',
                _0x4e8cd3(0x18f),
                'blendMode',
                'onceParallel',
            ];
        for (const _0x4ebf85 of _0x58ba33) {
            const _0x3c684a = _0x4fb80b[_0x4e8cd3(0x229)](_0x49623a[_0x4ebf85]);
            if (_0x3c684a) {
                if (_0x4e8cd3(0x174) !== _0x4e8cd3(0x174)) {
                    const _0x1c49be = _0x3df850 ? _0x5e1804[_0x4e8cd3(0x1cb)] || '' : '';
                    if (_0x1c49be[_0x4e8cd3(0x1bb)] <= 0x0) return;
                    const _0x821600 = _0x275d11[_0x4e8cd3(0x175)][_0x4e8cd3(0x16c)]['floor'],
                        _0x5601c3 = [
                            _0x4e8cd3(0x16b),
                            'gradientLength',
                            _0x4e8cd3(0x1c6),
                            'opacity',
                            _0x4e8cd3(0x204),
                            _0x4e8cd3(0x18f),
                            _0x4e8cd3(0x169),
                            _0x4e8cd3(0x206),
                        ];
                    for (const _0x2e286f of _0x5601c3) {
                        const _0x7eecba = _0x1c49be[_0x4e8cd3(0x229)](_0x821600[_0x2e286f]);
                        if (_0x7eecba)
                            for (const _0x5641da of _0x7eecba) {
                                _0x5641da[_0x4e8cd3(0x229)](_0x821600[_0x2e286f]);
                                const _0x4ee465 = _0x3703a9(_0x5f51db['$1'])[_0x4e8cd3(0x1c2)](
                                        0x0,
                                        0xff
                                    ),
                                    _0x45ca65 = _0x40cf40['$2'];
                                this['setMapDamageEffectSetting'](_0x4ee465, _0x2e286f, _0x45ca65);
                            }
                    }
                } else
                    for (const _0x5a8666 of _0x3c684a) {
                        if (_0x4e8cd3(0x1d5) === 'jCalg') {
                            _0x5a8666[_0x4e8cd3(0x229)](_0x49623a[_0x4ebf85]);
                            const _0x18e476 = Number(RegExp['$1'])[_0x4e8cd3(0x1c2)](0x0, 0xff),
                                _0x382560 = RegExp['$2'];
                            this[_0x4e8cd3(0x1b6)](_0x18e476, _0x4ebf85, _0x382560);
                        } else
                            ((_0x4b5160[_0x4e8cd3(0x209)] = !![]),
                                _0x3dfa5c[_0x4e8cd3(0x175)]['Game_Actor_executeFloorDamage'][
                                    'call'
                                ](this),
                                (_0x4c47a1[_0x4e8cd3(0x209)] = ![]));
                    }
            }
        }
    }),
    (Game_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1b6)] = function (_0x448883, _0x111805, _0x3861d1) {
        const _0x18b946 = _0x4ed325,
            _0x17fdb4 = this[_0x18b946(0x1b7)](_0x448883);
        ((_0x3861d1 = DataManager['parseMapDamageNotetagValue'](_0x111805, _0x3861d1)),
            _0x111805 === 'hexColor' &&
                (_0x18b946(0x15d) === _0x18b946(0x15d)
                    ? (_0x17fdb4[_0x18b946(0x1c6)] = '')
                    : ((_0x389b95 = _0x2f83c6[_0x18b946(0x1b5)](_0x3298d6(_0x5a2a5d) * 0.01)),
                      (_0xc80575 = _0x18b946(0x16a)))),
            (_0x17fdb4[_0x111805] = _0x3861d1));
    }),
    (Game_Map['prototype'][_0x4ed325(0x1b7)] = function (_0x2cd042) {
        const _0x2fbe09 = _0x4ed325;
        if (this[_0x2fbe09(0x1bf)] === undefined) this[_0x2fbe09(0x15f)]();
        return (
            this[_0x2fbe09(0x1bf)][_0x2cd042] === undefined &&
                (_0x2fbe09(0x218) === _0x2fbe09(0x218)
                    ? (this[_0x2fbe09(0x1bf)][_0x2cd042] = JSON[_0x2fbe09(0x177)](
                          JSON['stringify'](Game_Map['MAP_DAMAGE_EFFECT'])
                      ))
                    : this[_0x2fbe09(0x1df)]()),
            this['_mapDamageEffectRegions'][_0x2cd042]
        );
    }),
    (Game_Map['prototype'][_0x4ed325(0x20c)] = function () {
        const _0x31b934 = _0x4ed325,
            _0x1b46b2 = $dataMap ? $dataMap['note'] || '' : '';
        if (_0x1b46b2[_0x31b934(0x1bb)] <= 0x0) return;
        const _0x1eb093 = VisuMZ[_0x31b934(0x175)][_0x31b934(0x16c)],
            _0x4ab95b = _0x1eb093[_0x31b934(0x20a)],
            _0x43fd5f = _0x1b46b2[_0x31b934(0x229)](_0x4ab95b);
        if (_0x43fd5f) {
            if (_0x31b934(0x19b) === _0x31b934(0x1a8))
                ((this['_mapDamageDuration'] -= _0x570876[_0x31b934(0x1c8)]),
                    (this[_0x31b934(0x163)] = 0x0));
            else
                for (const _0x2975cf of _0x43fd5f) {
                    _0x2975cf[_0x31b934(0x229)](_0x4ab95b);
                    const _0x134e62 = Number(RegExp['$1'])['clamp'](0x0, 0xff),
                        _0x1e05e8 = String(RegExp['$2'])
                            [_0x31b934(0x1c7)](',')
                            ['map'](_0x383d54 => _0x383d54['trim']());
                    for (const _0x3922d6 of _0x1e05e8) {
                        const _0x29bef1 = /^\d+$/[_0x31b934(0x207)](_0x3922d6);
                        let _0x3ce452 = 0x0;
                        if (_0x29bef1) {
                            if ('dQoYM' !== _0x31b934(0x1b1)) {
                                if (!_0x4877b4[_0x31b934(0x1a7)]()) return ![];
                                if (!_0x44c84a) return ![];
                                const _0xe673b3 = _0x48ec86[_0x31b934(0x1f0)](),
                                    _0xf8f3b4 = _0x375990['getMapDamageEffectSettings'](_0xe673b3);
                                if (_0xf8f3b4['hexColor'] === '' && _0xf8f3b4['imgFilename'] === '')
                                    return ![];
                                return _0x1e848c[_0x31b934(0x209)];
                            } else _0x3ce452 = Number(_0x3922d6);
                        } else _0x3ce452 = DataManager[_0x31b934(0x1a5)](_0x3922d6);
                        _0x3ce452 &&
                            ((this[_0x31b934(0x170)][_0x134e62] =
                                this['_floorStateRegions'][_0x134e62] || []),
                            this[_0x31b934(0x170)][_0x134e62][_0x31b934(0x1c1)](_0x3ce452));
                    }
                }
        }
    }),
    (Game_Map['prototype']['getFloorStatesAtRegion'] = function (_0x392465) {
        const _0x26fdc2 = _0x4ed325;
        if (this[_0x26fdc2(0x170)] === undefined) this['initMapDamageEffects']();
        return (
            (this['_floorStateRegions'][_0x392465] = this[_0x26fdc2(0x170)][_0x392465] || []),
            this[_0x26fdc2(0x170)][_0x392465]
        );
    }),
    (Game_Map['prototype'][_0x4ed325(0x1c4)] = function () {
        const _0x116080 = _0x4ed325,
            _0x5c5c51 = $dataMap ? $dataMap['note'] || '' : '';
        if (_0x5c5c51[_0x116080(0x1bb)] <= 0x0) return;
        const _0x301365 = VisuMZ['MapDamageEffect'][_0x116080(0x16c)],
            _0x471059 = _0x301365[_0x116080(0x18e)],
            _0x4dc980 = _0x5c5c51[_0x116080(0x229)](_0x471059);
        if (_0x4dc980) {
            if (_0x116080(0x17c) !== _0x116080(0x17c)) {
                if (this['_lastMapDamageRegion'] >= 0x0) {
                    const _0x158034 = this[_0x116080(0x171)];
                    return _0x56da88[_0x116080(0x1b7)](_0x158034);
                } else {
                    if (this[_0x116080(0x200)] >= 0x0) {
                        const _0x129765 = this[_0x116080(0x200)];
                        return _0x5aa585['getMapDamageEffectSettings'](_0x129765);
                    }
                }
                return {};
            } else
                for (const _0x1953a3 of _0x4dc980) {
                    if (_0x116080(0x1ab) !== _0x116080(0x189)) {
                        _0x1953a3[_0x116080(0x229)](_0x471059);
                        const _0x34191e = Number(RegExp['$1'])[_0x116080(0x1c2)](0x0, 0xff),
                            _0x239091 = String(RegExp['$2']);
                        this[_0x116080(0x17f)][_0x34191e] = _0x239091;
                    } else {
                        const _0x41919c = _0x2f53d5['hexToRgba'](_0x396dd6, 0x1),
                            _0x30c84c = _0x1521f5[_0x116080(0x1e4)](_0x2a28e1, 0x0),
                            _0x2b0e67 = _0x31b061[_0x116080(0x1e4)](_0x15e3e2, 0.2),
                            _0x4306d6 = new _0x4f2763(
                                _0x15f00f[_0x116080(0x1ec)],
                                _0x54d9eb[_0x116080(0x224)]
                            );
                        return (
                            _0x4306d6[_0x116080(0x21d)](
                                0x0,
                                0x0,
                                _0x4b7975,
                                _0x12e847[_0x116080(0x224)],
                                _0x41919c,
                                _0x30c84c,
                                ![]
                            ),
                            _0x4306d6[_0x116080(0x21d)](
                                _0x1f8ceb[_0x116080(0x1ec)] - _0x2eb9d7,
                                0x0,
                                _0x5d31e7,
                                _0x5bcdb1[_0x116080(0x224)],
                                _0x30c84c,
                                _0x41919c,
                                ![]
                            ),
                            _0x4306d6[_0x116080(0x21d)](
                                0x0,
                                0x0,
                                _0x496854[_0x116080(0x1ec)],
                                _0x2b25ac,
                                _0x41919c,
                                _0x30c84c,
                                !![]
                            ),
                            _0x4306d6[_0x116080(0x21d)](
                                0x0,
                                _0x5df9fa['height'] - _0x2e5119,
                                _0x4f3040[_0x116080(0x1ec)],
                                _0x538c7e,
                                _0x30c84c,
                                _0x41919c,
                                !![]
                            ),
                            _0x4306d6['fillRect'](
                                0x0,
                                0x0,
                                _0x1f6fc7[_0x116080(0x1ec)],
                                _0x5e4ff4[_0x116080(0x224)],
                                _0x2b0e67
                            ),
                            _0x4306d6
                        );
                    }
                }
        }
    }),
    (Game_Map[_0x4ed325(0x1cc)]['getCustomFloorDamageAtRegion'] = function (_0x300cdd) {
        const _0x54fa27 = _0x4ed325;
        if (this[_0x54fa27(0x17f)] === undefined) this['initMapDamageEffects']();
        return this[_0x54fa27(0x17f)][_0x300cdd];
    }),
    (Game_Actor[_0x4ed325(0x22b)] = {
        hexColor: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x194)] || _0x4ed325(0x1f7),
        gradientLength: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x180)] || 0x1,
        imgFilename: VisuMZ[_0x4ed325(0x175)]['Settings'][_0x4ed325(0x208)] || '',
        opacity: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)]['SlipOpacity'] || 0x1,
        duration: VisuMZ['MapDamageEffect'][_0x4ed325(0x1a2)][_0x4ed325(0x190)] || 0x1,
        blendMode: VisuMZ['MapDamageEffect'][_0x4ed325(0x1a2)][_0x4ed325(0x181)] || 0x0,
        onceParallel: VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a2)][_0x4ed325(0x191)] || 0x0,
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x21f)] = Game_Actor[_0x4ed325(0x1cc)][_0x4ed325(0x1dc)]),
    (Game_Actor[_0x4ed325(0x1cc)][_0x4ed325(0x1dc)] = function () {
        const _0x2cc293 = _0x4ed325;
        (($gameTemp[_0x2cc293(0x19a)] = !![]),
            VisuMZ[_0x2cc293(0x175)][_0x2cc293(0x21f)][_0x2cc293(0x202)](this),
            ($gameTemp[_0x2cc293(0x19a)] = ![]));
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1c3)] = Game_Actor['prototype'][_0x4ed325(0x1f6)]),
    (Game_Actor[_0x4ed325(0x1cc)][_0x4ed325(0x1f6)] = function () {
        const _0x4cbd24 = _0x4ed325;
        (($gameTemp[_0x4cbd24(0x209)] = !![]),
            VisuMZ[_0x4cbd24(0x175)][_0x4cbd24(0x1c3)]['call'](this),
            ($gameTemp[_0x4cbd24(0x209)] = ![]));
    }),
    (VisuMZ['MapDamageEffect'][_0x4ed325(0x1be)] = Game_Actor['prototype']['checkFloorEffect']),
    (Game_Actor['prototype']['checkFloorEffect'] = function () {
        const _0x5467ac = _0x4ed325;
        (VisuMZ[_0x5467ac(0x175)]['Game_Actor_checkFloorEffect']['call'](this),
            $gamePlayer[_0x5467ac(0x1d0)]() && this[_0x5467ac(0x1df)](),
            this['refresh']());
    }),
    (Game_Player[_0x4ed325(0x1cc)]['isOnStateFloor'] = function () {
        const _0x3f4056 = _0x4ed325,
            _0x505f63 = this[_0x3f4056(0x1f0)]();
        return $gameMap[_0x3f4056(0x1e7)](_0x505f63)[_0x3f4056(0x1bb)] > 0x0;
    }),
    (Game_Actor['prototype'][_0x4ed325(0x1df)] = function () {
        const _0x220e04 = _0x4ed325;
        if (this[_0x220e04(0x1b3)]()) return;
        const _0x20dd21 = $gamePlayer[_0x220e04(0x1f0)](),
            _0x3ed01e = $gameMap['getFloorStatesAtRegion'](_0x20dd21);
        let _0x9522b7 = ![];
        for (const _0x79750d of _0x3ed01e) {
            if (!$dataStates[_0x79750d]) continue;
            if (this[_0x220e04(0x223)](_0x79750d)) continue;
            (this['addState'](_0x79750d), (_0x9522b7 = !![]));
        }
        if (_0x9522b7) {
            if (_0x220e04(0x17d) !== _0x220e04(0x17d))
                (_0x223766(
                    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                        _0x220e04(0x188)
                    ](_0x536c7e, _0x8a9db2)
                ),
                    _0x10d51f[_0x220e04(0x17e)]());
            else {
                if (this[_0x220e04(0x1b3)]()) this[_0x220e04(0x18b)]();
                this[_0x220e04(0x1a9)]();
            }
        }
    }),
    (VisuMZ['MapDamageEffect'][_0x4ed325(0x1d7)] = Game_Actor[_0x4ed325(0x1cc)][_0x4ed325(0x182)]),
    (Game_Actor[_0x4ed325(0x1cc)][_0x4ed325(0x182)] = function () {
        const _0x198d76 = _0x4ed325,
            _0x40878e = $gamePlayer[_0x198d76(0x1f0)](),
            _0x1baff9 = $gameMap[_0x198d76(0x226)](_0x40878e) || '',
            _0xa3f282 = VisuMZ[_0x198d76(0x175)][_0x198d76(0x1d7)][_0x198d76(0x202)](this);
        if (_0x1baff9[_0x198d76(0x1bb)] > 0x0) {
            if (_0x198d76(0x1f1) !== _0x198d76(0x1f1)) {
                if (this['_mapDamageSprite']) return;
                ((this[_0x198d76(0x1eb)] = new _0x3d722a()),
                    this[_0x198d76(0x16d)](this[_0x198d76(0x1eb)]),
                    (this[_0x198d76(0x171)] = -0x1),
                    (this[_0x198d76(0x200)] = -0x1));
                if (_0x23688c) _0x182f85[_0x198d76(0x1e2)] = -0x1;
                if (_0xd710c1) _0x9620d0[_0x198d76(0x1f2)] = -0x1;
            } else {
                window['damage'] = _0xa3f282;
                const _0xbeca87 =
                    '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20a\x20=\x20this;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20b\x20=\x20this;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20v\x20=\x20$gameVariables._data;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20user\x20=\x20this;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20target\x20=\x20this;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%2;\x0a\x20\x20\x20\x20\x20\x20\x20\x20'[
                        'format'
                    ](_0xa3f282, _0x1baff9);
                try {
                    eval(_0xbeca87);
                    const _0xd0aff9 = window[_0x198d76(0x1fc)];
                    return ((window[_0x198d76(0x1fc)] = undefined), _0xd0aff9);
                } catch (_0x3e18da) {
                    if ($gameTemp['isPlaytest']()) console[_0x198d76(0x187)](_0x3e18da);
                    return _0xa3f282;
                }
            }
        } else return _0xa3f282;
    }),
    (VisuMZ[_0x4ed325(0x175)][_0x4ed325(0x1a1)] = Game_Player['prototype'][_0x4ed325(0x22d)]),
    (Game_Player[_0x4ed325(0x1cc)][_0x4ed325(0x22d)] = function () {
        const _0x2995eb = _0x4ed325;
        if (VisuMZ[_0x2995eb(0x175)][_0x2995eb(0x1a1)][_0x2995eb(0x202)](this)) return !![];
        const _0x37b6ce = this[_0x2995eb(0x1f0)](),
            _0x362ce2 = $gameMap[_0x2995eb(0x226)](_0x37b6ce) || '';
        return _0x362ce2[_0x2995eb(0x1bb)] > 0x0;
    }),
    (Game_Party[_0x4ed325(0x1cc)][_0x4ed325(0x1fd)] = function () {
        const _0x4deab0 = _0x4ed325,
            _0x4b973c =
                VisuMZ[_0x4deab0(0x175)][_0x4deab0(0x16c)][_0x4deab0(0x225)][_0x4deab0(0x19d)];
        for (const _0x463b23 of this[_0x4deab0(0x197)]()) {
            if (!_0x463b23) continue;
            for (const _0xa2a9ad of _0x463b23[_0x4deab0(0x1f5)]()) {
                if (_0x4deab0(0x211) !== _0x4deab0(0x211)) {
                    const _0xe369f2 = this['_lastMapDamageRegion'];
                    return _0x477f0a['getMapDamageEffectSettings'](_0xe369f2);
                } else {
                    if (!_0xa2a9ad) continue;
                    if (_0xa2a9ad[_0x4deab0(0x1cb)][_0x4deab0(0x229)](_0x4b973c)) return _0xa2a9ad;
                }
            }
        }
        return null;
    }),
    (VisuMZ['MapDamageEffect'][_0x4ed325(0x168)] =
        Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x15a)]),
    (Spriteset_Map[_0x4ed325(0x1cc)]['createLowerLayer'] = function () {
        const _0x1e3dd6 = _0x4ed325;
        (VisuMZ[_0x1e3dd6(0x175)][_0x1e3dd6(0x168)][_0x1e3dd6(0x202)](this),
            this['createMapDamageEffect']());
    }),
    (VisuMZ['MapDamageEffect']['Spriteset_Map_update'] =
        Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1b4)]),
    (Spriteset_Map[_0x4ed325(0x1cc)]['update'] = function () {
        const _0xc8af9d = _0x4ed325;
        (VisuMZ[_0xc8af9d(0x175)][_0xc8af9d(0x1fb)][_0xc8af9d(0x202)](this),
            this[_0xc8af9d(0x1ff)]());
    }),
    (Spriteset_Map['prototype'][_0x4ed325(0x185)] = function () {
        const _0x5b1ba3 = _0x4ed325;
        if (this[_0x5b1ba3(0x1eb)]) return;
        ((this[_0x5b1ba3(0x1eb)] = new Sprite()),
            this['addChild'](this[_0x5b1ba3(0x1eb)]),
            (this[_0x5b1ba3(0x171)] = -0x1),
            (this[_0x5b1ba3(0x200)] = -0x1));
        if ($gameScreen) $gameScreen['_mapDamageRegionID'] = -0x1;
        if ($gameScreen) $gameScreen[_0x5b1ba3(0x1f2)] = -0x1;
    }),
    (Spriteset_Map['prototype'][_0x4ed325(0x1ff)] = function () {
        const _0x44930b = _0x4ed325;
        if (!this[_0x44930b(0x1eb)]) return;
        (this[_0x44930b(0x22a)](), this[_0x44930b(0x21e)]());
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)]['updateMapDamageEffectImage'] = function () {
        const _0x31834b = _0x4ed325;
        this[_0x31834b(0x1a6)]() && this[_0x31834b(0x1e9)]();
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1a6)] = function () {
        const _0x1951c0 = _0x4ed325;
        if (this[_0x1951c0(0x171)] !== $gameScreen['_mapDamageRegionID']) return !![];
        if (this[_0x1951c0(0x200)] !== $gameScreen[_0x1951c0(0x1f2)]) return !![];
        return ![];
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1e9)] = function (_0x18c53b) {
        const _0x4dd0d1 = _0x4ed325;
        this['cacheMapDamageEffects']();
        if (!_0x18c53b) _0x18c53b = this[_0x4dd0d1(0x1b7)]();
        (this[_0x4dd0d1(0x1f8)](_0x18c53b), this[_0x4dd0d1(0x20d)](_0x18c53b));
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)]['cacheMapDamageEffects'] = function () {
        const _0x51533b = _0x4ed325;
        ((this[_0x51533b(0x171)] = $gameScreen['_mapDamageRegionID']),
            (this[_0x51533b(0x200)] = $gameScreen[_0x51533b(0x1f2)]));
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)]['getMapDamageEffectSettings'] = function () {
        const _0x1fe2d7 = _0x4ed325;
        if (this['_lastMapDamageRegion'] >= 0x0) {
            const _0x3ad1fe = this['_lastMapDamageRegion'];
            return $gameMap[_0x1fe2d7(0x1b7)](_0x3ad1fe);
        } else {
            if (this[_0x1fe2d7(0x200)] >= 0x0) {
                if (_0x1fe2d7(0x18c) !== _0x1fe2d7(0x1ea)) {
                    const _0x4e5dd4 = this[_0x1fe2d7(0x200)];
                    return DataManager[_0x1fe2d7(0x1b7)](_0x4e5dd4);
                } else this[_0x1fe2d7(0x163)] = _0x3abef9[_0x1fe2d7(0x1c8)];
            }
        }
        return {};
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x1f8)] = function (_0x5ede70) {
        const _0x27a356 = _0x4ed325;
        if (_0x5ede70['imgFilename'] && _0x5ede70['imgFilename'] !== '')
            this[_0x27a356(0x1eb)][_0x27a356(0x195)] = ImageManager['loadPicture'](
                _0x5ede70['imgFilename']
            );
        else {
            const _0x35d865 = _0x5ede70[_0x27a356(0x16b)] || _0x27a356(0x1d3),
                _0x354ed5 = _0x5ede70[_0x27a356(0x1dd)] || 0x1;
            this[_0x27a356(0x1eb)][_0x27a356(0x195)] = ImageManager[_0x27a356(0x185)](
                _0x35d865,
                _0x354ed5
            );
        }
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)]['prepareMapDamageEffectsBlendMode'] = function (_0x2de7e9) {
        const _0x345f29 = _0x4ed325;
        this[_0x345f29(0x1eb)]['blendMode'] = _0x2de7e9['blendMode'] || 0x0;
    }),
    (Spriteset_Map[_0x4ed325(0x1cc)][_0x4ed325(0x21e)] = function () {
        const _0x517d80 = _0x4ed325;
        this[_0x517d80(0x1eb)][_0x517d80(0x16a)] = $gameScreen['_mapDamageOpacity'] || 0x0;
    }));
