//=============================================================================
// VisuStella MZ - Encounter Effects Patch Plugin
//=============================================================================
/*:
 * @target MZ
 * @plugindesc [Patch] Modifies alert response behavior to prevent movement while allowing custom teleport logic.
 * @help
 * ==========================================================================
 * Introduction
 * ==========================================================================
 * This plugin modifies the <Alert Response: Stay> behavior in the VisuStella MZ
 * Encounter Effects plugin to prevent movement while allowing custom teleport logic.
 *
 * ==========================================================================
 * Modified Behavior
 * ==========================================================================
 * <Alert Response: Stay> - Prevents the event from moving, allowing teleportation
 * to be controlled through common events or other custom logic.
 * ==========================================================================
 */
(() => {
    // Override <Alert Response: Stay> to prevent movement and allow teleport only
    const _Game_Event_updateEncounterEffect = Game_Event.prototype.updateEncounterEffect;
    Game_Event.prototype.updateEncounterEffect = function () {
        if (this.event().note.includes('<Alert Response: Stay>')) {
            // Prevent the default fleeing or chasing behavior
            return;
        }
        _Game_Event_updateEncounterEffect.call(this);
    };
})();
