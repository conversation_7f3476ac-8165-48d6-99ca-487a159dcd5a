//=============================================================================
// VisuStella MZ - Event Title Scene
// VisuMZ_4_EventTitleScene.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_EventTitleScene = true;

var VisuMZ = VisuMZ || {};
VisuMZ.EventTitleScene = VisuMZ.EventTitleScene || {};
VisuMZ.EventTitleScene.version = 1.01;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.01] [EventTitleScene]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Main_Page
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * For those who feel compelled to create their own custom title scene using
 * in-game maps and events, this plugin will replace Scene_Title with a new
 * dedicated map scene to allow such a thing to happen. Customize it however
 * you can within your abilities and utilize the full power of RPG Maker MZ's
 * eventing system. Just don't forget to use an Autorun event to kick things
 * off, alright?
 *
 * Features include all (but not limited to) the following:
 *
 * * Dedicated map scene to use for custom map title scenes.
 * * Going to the Game End screen to return to the title screen will take the
 *   player back to the dedicated map scene.
 * * Customize which map to use and where on the map to display.
 * * Determine the player's position, visibility, facing direction, and whether
 *   or not followers are shown, too.
 * * Disable or enable movement on the title scene if you want.
 * * Plugin Commands that facilitate the New Game, Continue, and Options
 *   command as seen before in the title screen.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Autosave
 *
 * - Autosaving is disabled on the event title scene map. This is to prevent
 * any instances of the player loading into an unintended map by accident.
 *
 * ---
 *
 * Movement Disable
 *
 * - Through the Plugin Parameters, you can disable input and mouse movement
 * from the player for the dedicated event title scene.
 *
 * ---
 *
 * Menu and Debug Disable
 *
 * - On the dedicated event title scene, calling the Main Menu and debug menu
 * is disabled to prevent errors.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * Those using the VisuStella MZ Core Engine will now have the "Title Picture
 * Buttons" imported into the Event Title Scene. They can be interacted the
 * same way. The picture buttons will appear above all else so keep that in
 * mind for how you position them.
 *
 * ---
 *
 * ============================================================================
 * Available Text Codes
 * ============================================================================
 *
 * The following are text codes that you may use with this plugin.
 *
 * === Continue-Related Text Codes ===
 *
 * ---
 *
 * ------------------   -------------------------------------------------------
 * Text Code            Effect (Show Choice Text Only)
 * ------------------   -------------------------------------------------------
 *
 * <Continue>           Put this text code inside of a Show Choice and it will
 *                      enable that choice if there is a save file available.
 *                      It will disable that choice if there are no saves.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === System-Type Plugin Commands ===
 *
 * ---
 *
 * System: Start New Game
 * - Leaves the current scene and starts a new game.
 *
 *   Slow Fade Out?:
 *   - Use a slow fade out transition to the next scene?
 *
 * ---
 *
 * System: Open Load Scene
 * - Leaves the current scene and opens the load game scene.
 *
 *   Slow Fade Out?:
 *   - Use a slow fade out transition to the next scene?
 *
 * ---
 *
 * System: Open Options Scene
 * - Leaves the current scene and opens the options scene.
 *
 *   Slow Fade Out?:
 *   - Use a slow fade out transition to the next scene?
 *
 * ---
 *
 * ============================================================================
 * Script Calls
 * ============================================================================
 *
 * The following are Script Calls that can be used with this plugin. These are
 * made for JavaScript proficient users. We are not responsible if you use them
 * incorrectly or for unintended usage.
 *
 * ---
 *
 * === Continue-Related Script Calls ===
 *
 * ---
 *
 * DataManager.isAnySavefileExists()
 *
 * - Use this in a 'Conditional Branch' event command script check.
 * - This will return 'true' if there are save files to load from.
 * - This will return 'false' if there are no save files to load from.
 * - This code is available outside of this plugin.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * These are the settings available through the plugin parameters to adjust how
 * the map title scene plays out.
 *
 * ---
 *
 * Title Scene Coordinates
 *
 *   Map ID:
 *   - Select the map used for the evented title scene.
 *
 *   Map X:
 *   - Select the X coordinate for the evented title scene.
 *
 *   Map Y:
 *   - Select the Y coordinate for the evented title scene.
 *
 *   Face Direction:
 *   - What direction will the player face on the title scene?
 *   - This is assuming the player is visible.
 *
 * ---
 *
 * Player Character
 *
 *   Transparent?:
 *   - Make the player transparent on the title scene?
 *
 *   Can Input Move?:
 *   - Can the player move while on the title scene?
 *
 *   Show Followers?:
 *   - Show player followers on the title scene?
 *   - This is assuming the player is visible.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Irina
 * * Arisu
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.01: June 25, 2021
 * * Documentation Update!
 * ** Added section for VisuStella compatibility.
 * *** Those using the VisuStella MZ Core Engine will now have the "Title
 *     Picture Buttons" imported into the Event Title Scene. They can be
 *     interacted the same way. The picture buttons will appear above all else
 *     so keep that in mind for how you position them.
 * * Compatibility Update!
 * ** This plugin is now compatible with the VisuMZ Core Engine's Title Picture
 *    Buttons and will have them displayed on the same scene. Update by Arisu.
 *
 * Version 1.00 Official Release Date: July 9, 2021
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command NewGame
 * @text System: Start New Game
 * @desc Leaves the current scene and starts a new game.
 *
 * @arg SlowFade:eval
 * @text Slow Fade Out?
 * @type boolean
 * @on Slow
 * @off Normal
 * @desc Use a slow fade out transition to the next scene?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command LoadScreen
 * @text System: Open Load Scene
 * @desc Leaves the current scene and opens the load game scene.
 *
 * @arg SlowFade:eval
 * @text Slow Fade Out?
 * @type boolean
 * @on Slow
 * @off Normal
 * @desc Use a slow fade out transition to the next scene?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Options
 * @text System: Open Options Scene
 * @desc Leaves the current scene and opens the options scene.
 *
 * @arg SlowFade:eval
 * @text Slow Fade Out?
 * @type boolean
 * @on Slow
 * @off Normal
 * @desc Use a slow fade out transition to the next scene?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param EventTitleScene
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Coordinates
 * @text Title Scene Coordinates
 *
 * @param MapID:num
 * @text Map ID
 * @parent Coordinates
 * @type number
 * @min 1
 * @max 999
 * @desc Select the map used for the evented title scene.
 * @default 1
 *
 * @param MapX:num
 * @text Map X
 * @parent Coordinates
 * @type number
 * @min 0
 * @max 255
 * @desc Select the X coordinate for the evented title scene.
 * @default 10
 *
 * @param MapY:num
 * @text Map Y
 * @parent Coordinates
 * @type number
 * @min 0
 * @max 255
 * @desc Select the Y coordinate for the evented title scene.
 * @default 10
 *
 * @param FaceDirection:num
 * @text Face Direction
 * @parent Coordinates
 * @type select
 * @option Down Left
 * @value 1
 * @option Down
 * @value 2
 * @option Down Right
 * @value 3
 * @option Left
 * @value 4
 * @option Right
 * @value 6
 * @option Up Left
 * @value 7
 * @option Up
 * @value 8
 * @option Up Right
 * @value 9
 * @desc What direction will the player face on the title scene?
 * This is assuming the player is visible.
 * @default 2
 *
 * @param Player
 * @text Player Character
 *
 * @param PlayerTransparent:eval
 * @text Transparent?
 * @parent Player
 * @type boolean
 * @on Transparent
 * @off Opaque
 * @desc Make the player transparent on the title scene?
 * @default true
 *
 * @param CanInputMove:eval
 * @text Can Input Move?
 * @parent Player
 * @type boolean
 * @on Allow
 * @off Disallow
 * @desc Can the player move while on the title scene?
 * @default false
 *
 * @param ShowFollowers:eval
 * @text Show Followers?
 * @parent Player
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show player followers on the title scene?
 * This is assuming the player is visible.
 * @default false
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================

const _0x3c6c = [
    'name',
    'makeCommandList',
    '589057lHyBiv',
    'SQFRP',
    'registerCommand',
    '37oXdNgg',
    'format',
    'callMenu',
    'createTitleButtons',
    'pictureButtons',
    'FaceDirection',
    'STRUCT',
    'replace',
    'trim',
    'getInputDirection',
    'FuEua',
    'ARRAYSTRUCT',
    'Options',
    'prepareEventedTitleScreen',
    'lZxcV',
    '2707xBXeIr',
    'JSON',
    'FsAET',
    'isSceneMap',
    'ssAII',
    'reserveTransfer',
    'call',
    'toUpperCase',
    'isSceneTitleMap',
    'isMenuCalled',
    'prototype',
    '25mEEWHt',
    'isAutosaveEnabled',
    'enableContinueTextTag',
    'setTransparent',
    'constructor',
    'DHFgS',
    'start',
    '53251FTpmIe',
    'ConvertParams',
    'isMapTouchOk',
    'parameters',
    '_scene',
    'ARRAYFUNC',
    '44956UXqWkx',
    'SlowFade',
    '13eazCCC',
    'FAFcx',
    'ShowFollowers',
    'initMembers',
    '_list',
    'CanInputMove',
    'hideFollowers',
    'match',
    'map',
    'ARRAYJSON',
    'Settings',
    'ARRAYEVAL',
    'FUNC',
    'MapY',
    'EventTitleScene',
    'NewGame',
    'EVAL',
    'push',
    'initialize',
    '452818FrZBua',
    'return\x200',
    'PlayerTransparent',
    'version',
    'Game_Player_getInputDirection',
    'create',
    '458846OxqJzJ',
    'fadeOutAll',
    'ARRAYSTR',
    'dRZMp',
    'filter',
    'addChild',
    'goto',
    'isAnySavefileExists',
    'isDebugCalled',
    'exit',
    'updateCallDebug',
    'neBFi',
    'parse',
    '2251EKguYm',
    '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
    'kzrHV',
    'jqkmk',
    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
    '205cAjYzh',
    'max',
    'STR',
];
const _0x1f85c6 = _0x1e44;
(function (_0x346226, _0x3ba08f) {
    const _0x179e37 = _0x1e44;
    while (!![]) {
        try {
            const _0x4333d7 =
                parseInt(_0x179e37(0x14f)) +
                -parseInt(_0x179e37(0x14a)) * parseInt(_0x179e37(0x161)) +
                -parseInt(_0x179e37(0x132)) +
                parseInt(_0x179e37(0x138)) +
                parseInt(_0x179e37(0x152)) * -parseInt(_0x179e37(0x145)) +
                -parseInt(_0x179e37(0x173)) * parseInt(_0x179e37(0x17b)) +
                -parseInt(_0x179e37(0x16c)) * -parseInt(_0x179e37(0x179));
            if (_0x4333d7 === _0x3ba08f) break;
            else _0x346226['push'](_0x346226['shift']());
        } catch (_0x57139d) {
            _0x346226['push'](_0x346226['shift']());
        }
    }
})(_0x3c6c, 0x5ed94);
var label = _0x1f85c6(0x189),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x1f85c6(0x13c)](function (_0x2d2b23) {
        return _0x2d2b23['status'] && _0x2d2b23['description']['includes']('[' + label + ']');
    })[0x0];
((VisuMZ[label][_0x1f85c6(0x185)] = VisuMZ[label][_0x1f85c6(0x185)] || {}),
    (VisuMZ['ConvertParams'] = function (_0x299d12, _0x596e43) {
        const _0x2e37d8 = _0x1f85c6;
        for (const _0x3581f4 in _0x596e43) {
            if (_0x2e37d8(0x143) !== 'neBFi') return ![];
            else {
                if (_0x3581f4['match'](/(.*):(.*)/i)) {
                    if (_0x2e37d8(0x150) === _0x2e37d8(0x150)) {
                        const _0x44e0d6 = String(RegExp['$1']),
                            _0x4563fe = String(RegExp['$2'])
                                [_0x2e37d8(0x168)]()
                                [_0x2e37d8(0x15a)]();
                        let _0x15ba76, _0x430e2f, _0x232092;
                        switch (_0x4563fe) {
                            case 'NUM':
                                _0x15ba76 =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? Number(_0x596e43[_0x3581f4])
                                        : 0x0;
                                break;
                            case 'ARRAYNUM':
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON[_0x2e37d8(0x144)](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f['map'](_0x4b823d => Number(_0x4b823d))));
                                break;
                            case _0x2e37d8(0x18b):
                                _0x15ba76 =
                                    _0x596e43[_0x3581f4] !== '' ? eval(_0x596e43[_0x3581f4]) : null;
                                break;
                            case _0x2e37d8(0x186):
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON['parse'](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f['map'](_0x4ea8c4 => eval(_0x4ea8c4))));
                                break;
                            case _0x2e37d8(0x162):
                                _0x15ba76 =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON[_0x2e37d8(0x144)](_0x596e43[_0x3581f4])
                                        : '';
                                break;
                            case _0x2e37d8(0x184):
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON[_0x2e37d8(0x144)](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f[_0x2e37d8(0x183)](_0x4dde13 =>
                                        JSON['parse'](_0x4dde13)
                                    )));
                                break;
                            case _0x2e37d8(0x187):
                                _0x15ba76 =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? new Function(JSON['parse'](_0x596e43[_0x3581f4]))
                                        : new Function(_0x2e37d8(0x133));
                                break;
                            case _0x2e37d8(0x178):
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON['parse'](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f['map'](
                                        _0x4a59f3 => new Function(JSON[_0x2e37d8(0x144)](_0x4a59f3))
                                    )));
                                break;
                            case _0x2e37d8(0x14c):
                                _0x15ba76 =
                                    _0x596e43[_0x3581f4] !== '' ? String(_0x596e43[_0x3581f4]) : '';
                                break;
                            case _0x2e37d8(0x13a):
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON[_0x2e37d8(0x144)](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f[_0x2e37d8(0x183)](_0x316ef7 =>
                                        String(_0x316ef7)
                                    )));
                                break;
                            case _0x2e37d8(0x158):
                                ((_0x232092 =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON['parse'](_0x596e43[_0x3581f4])
                                        : {}),
                                    (_0x15ba76 = VisuMZ['ConvertParams']({}, _0x232092)));
                                break;
                            case _0x2e37d8(0x15d):
                                ((_0x430e2f =
                                    _0x596e43[_0x3581f4] !== ''
                                        ? JSON[_0x2e37d8(0x144)](_0x596e43[_0x3581f4])
                                        : []),
                                    (_0x15ba76 = _0x430e2f[_0x2e37d8(0x183)](_0x31bbd9 =>
                                        VisuMZ['ConvertParams'](
                                            {},
                                            JSON[_0x2e37d8(0x144)](_0x31bbd9)
                                        )
                                    )));
                                break;
                            default:
                                continue;
                        }
                        _0x299d12[_0x44e0d6] = _0x15ba76;
                    } else _0x7f102e['_scene']['fadeOutAll']();
                }
            }
        }
        return _0x299d12;
    }),
    (_0x3c012a => {
        const _0xb40e39 = _0x1f85c6,
            _0x76e808 = _0x3c012a[_0xb40e39(0x14d)];
        for (const _0x4dd946 of dependencies) {
            if (!Imported[_0x4dd946]) {
                if (_0xb40e39(0x17c) !== _0xb40e39(0x171)) {
                    (alert(
                        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.'[
                            'format'
                        ](_0x76e808, _0x4dd946)
                    ),
                        SceneManager[_0xb40e39(0x141)]());
                    break;
                } else return ![];
            }
        }
        const _0xd0dffc = _0x3c012a['description'];
        if (_0xd0dffc['match'](/\[Version[ ](.*?)\]/i)) {
            if (_0xb40e39(0x147) !== _0xb40e39(0x160)) {
                const _0x284902 = Number(RegExp['$1']);
                _0x284902 !== VisuMZ[label][_0xb40e39(0x135)] &&
                    (alert(_0xb40e39(0x149)[_0xb40e39(0x153)](_0x76e808, _0x284902)),
                    SceneManager[_0xb40e39(0x141)]());
            } else this[_0xb40e39(0x18d)](...arguments);
        }
        if (_0xd0dffc[_0xb40e39(0x182)](/\[Tier[ ](\d+)\]/i)) {
            const _0x5c406d = Number(RegExp['$1']);
            _0x5c406d < tier
                ? (alert(_0xb40e39(0x146)['format'](_0x76e808, _0x5c406d, tier)),
                  SceneManager['exit']())
                : (tier = Math[_0xb40e39(0x14b)](_0x5c406d, tier));
        }
        VisuMZ['ConvertParams'](VisuMZ[label][_0xb40e39(0x185)], _0x3c012a[_0xb40e39(0x176)]);
    })(pluginData),
    PluginManager[_0x1f85c6(0x151)](pluginData[_0x1f85c6(0x14d)], _0x1f85c6(0x18a), _0x1bf662 => {
        const _0x5e9e4a = _0x1f85c6;
        VisuMZ[_0x5e9e4a(0x174)](_0x1bf662, _0x1bf662);
        const _0x4467af = _0x1bf662[_0x5e9e4a(0x17a)];
        (_0x4467af && SceneManager['_scene'][_0x5e9e4a(0x139)](),
            SceneManager[_0x5e9e4a(0x18c)](Scene_TitleTransition));
    }),
    PluginManager[_0x1f85c6(0x151)](pluginData[_0x1f85c6(0x14d)], 'LoadScreen', _0x1ffb7a => {
        const _0x5c3b1e = _0x1f85c6;
        VisuMZ[_0x5c3b1e(0x174)](_0x1ffb7a, _0x1ffb7a);
        const _0x5ceecf = _0x1ffb7a[_0x5c3b1e(0x17a)];
        (_0x5ceecf &&
            (_0x5c3b1e(0x163) !== _0x5c3b1e(0x163)
                ? this[_0x5c3b1e(0x155)]()
                : SceneManager[_0x5c3b1e(0x177)][_0x5c3b1e(0x139)]()),
            SceneManager[_0x5c3b1e(0x18c)](Scene_Load));
    }),
    PluginManager[_0x1f85c6(0x151)](pluginData['name'], _0x1f85c6(0x15e), _0x47fc78 => {
        const _0x2f8e1e = _0x1f85c6;
        VisuMZ['ConvertParams'](_0x47fc78, _0x47fc78);
        const _0x160a2f = _0x47fc78[_0x2f8e1e(0x17a)];
        (_0x160a2f &&
            (_0x2f8e1e(0x15c) === _0x2f8e1e(0x148)
                ? _0x2f9703[_0x2f8e1e(0x16b)][_0x2f8e1e(0x18d)][_0x2f8e1e(0x167)](this)
                : SceneManager['_scene']['fadeOutAll']()),
            SceneManager[_0x2f8e1e(0x18c)](Scene_Options));
    }),
    (DataManager[_0x1f85c6(0x15f)] = function () {
        const _0xdec918 = _0x1f85c6;
        this['setupNewGame']();
        const _0x460163 = VisuMZ['EventTitleScene'][_0xdec918(0x185)]['MapID'],
            _0x49b02e = VisuMZ[_0xdec918(0x189)]['Settings']['MapX'],
            _0x3482ab = VisuMZ['EventTitleScene'][_0xdec918(0x185)][_0xdec918(0x188)],
            _0x32d853 = VisuMZ[_0xdec918(0x189)]['Settings'][_0xdec918(0x157)];
        $gamePlayer[_0xdec918(0x166)](_0x460163, _0x49b02e, _0x3482ab, _0x32d853, 0x0);
    }),
    (SceneManager[_0x1f85c6(0x164)] = function () {
        const _0x25dd95 = _0x1f85c6;
        return this[_0x25dd95(0x177)] && this[_0x25dd95(0x177)][_0x25dd95(0x170)] === Scene_Map;
    }),
    (SceneManager[_0x1f85c6(0x169)] = function () {
        const _0x2a3e55 = _0x1f85c6;
        return this['_scene'] && this['_scene'][_0x2a3e55(0x170)] === Scene_EventedTitleMap;
    }),
    (VisuMZ['EventTitleScene']['SceneManager_goto'] = SceneManager[_0x1f85c6(0x13e)]),
    (SceneManager[_0x1f85c6(0x13e)] = function (_0x1049bf) {
        const _0x335031 = _0x1f85c6;
        (_0x1049bf === Scene_Title &&
            (DataManager[_0x335031(0x15f)](), (_0x1049bf = Scene_EventedTitleMap)),
            VisuMZ[_0x335031(0x189)]['SceneManager_goto'][_0x335031(0x167)](this, _0x1049bf));
    }),
    (VisuMZ[_0x1f85c6(0x189)]['SceneManager_push'] = SceneManager[_0x1f85c6(0x18c)]),
    (SceneManager[_0x1f85c6(0x18c)] = function (_0x1cf8de) {
        const _0x12e6fd = _0x1f85c6;
        (_0x1cf8de === Scene_Title &&
            (DataManager[_0x12e6fd(0x15f)](), (_0x1cf8de = Scene_EventedTitleMap)),
            VisuMZ['EventTitleScene']['SceneManager_push'][_0x12e6fd(0x167)](this, _0x1cf8de));
    }),
    (VisuMZ[_0x1f85c6(0x189)][_0x1f85c6(0x136)] =
        Game_Player[_0x1f85c6(0x16b)]['getInputDirection']),
    (Game_Player[_0x1f85c6(0x16b)][_0x1f85c6(0x15b)] = function () {
        const _0x4be4ce = _0x1f85c6;
        if (
            !VisuMZ[_0x4be4ce(0x189)][_0x4be4ce(0x185)][_0x4be4ce(0x180)] &&
            SceneManager[_0x4be4ce(0x169)]()
        ) {
            if ('ObKBz' === _0x4be4ce(0x165)) _0x2e41e5[_0x4be4ce(0x177)][_0x4be4ce(0x139)]();
            else return 0x0;
        }
        return VisuMZ[_0x4be4ce(0x189)][_0x4be4ce(0x136)]['call'](this);
    }));
function _0x1e44(_0x3b4d65, _0x36b1d1) {
    return (
        (_0x1e44 = function (_0x3c6c41, _0x1e448b) {
            _0x3c6c41 = _0x3c6c41 - 0x132;
            let _0x1ff0a6 = _0x3c6c[_0x3c6c41];
            return _0x1ff0a6;
        }),
        _0x1e44(_0x3b4d65, _0x36b1d1)
    );
}
function Scene_EventedTitleMap() {
    const _0xa650e6 = _0x1f85c6;
    this[_0xa650e6(0x18d)](...arguments);
}
((Scene_EventedTitleMap['prototype'] = Object['create'](Scene_Map[_0x1f85c6(0x16b)])),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)]['constructor'] = Scene_EventedTitleMap),
    (Scene_EventedTitleMap['prototype'][_0x1f85c6(0x18d)] = function () {
        const _0x18d1e6 = _0x1f85c6;
        (Scene_Map[_0x18d1e6(0x16b)][_0x18d1e6(0x18d)][_0x18d1e6(0x167)](this),
            this[_0x18d1e6(0x17e)]());
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x17e)] = function () {
        const _0x19470c = _0x1f85c6;
        $gamePlayer[_0x19470c(0x16f)](VisuMZ[_0x19470c(0x189)][_0x19470c(0x185)][_0x19470c(0x134)]);
        if (VisuMZ['EventTitleScene'][_0x19470c(0x185)][_0x19470c(0x17d)]) {
            if (_0x19470c(0x13b) === _0x19470c(0x13b)) $gamePlayer['showFollowers']();
            else {
                const _0x47857a = _0x355a6b(_0x241d52['$1']);
                _0x47857a !== _0x11ad5b[_0x423fef][_0x19470c(0x135)] &&
                    (_0x57fad2(_0x19470c(0x149)['format'](_0x4d4fc9, _0x47857a)),
                    _0x151e92[_0x19470c(0x141)]());
            }
        } else $gamePlayer[_0x19470c(0x181)]();
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x16d)] = function () {
        return ![];
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x175)] = function () {
        const _0x4a74d7 = _0x1f85c6;
        return VisuMZ['EventTitleScene'][_0x4a74d7(0x185)]['CanInputMove'];
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)]['updateEncounter'] = function () {}),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x16a)] = function () {
        return ![];
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x154)] = function () {}),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x142)] = function () {}),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)][_0x1f85c6(0x140)] = function () {
        return ![];
    }),
    (Scene_EventedTitleMap['prototype']['start'] = function () {
        const _0x26782a = _0x1f85c6;
        (Scene_Map[_0x26782a(0x16b)][_0x26782a(0x172)][_0x26782a(0x167)](this),
            Imported['VisuMZ_0_CoreEngine'] && this['createTitleButtons']());
    }),
    (Scene_EventedTitleMap[_0x1f85c6(0x16b)]['createTitleButtons'] = function () {
        const _0x548b98 = _0x1f85c6;
        for (const _0x493f43 of Scene_Title[_0x548b98(0x156)]) {
            const _0x2e81c2 = new Sprite_TitlePictureButton(_0x493f43);
            this[_0x548b98(0x13d)](_0x2e81c2);
        }
    }));
function Scene_TitleTransition() {
    const _0x2f7841 = _0x1f85c6;
    this[_0x2f7841(0x18d)](...arguments);
}
((Scene_TitleTransition[_0x1f85c6(0x16b)] = Object[_0x1f85c6(0x137)](Scene_Base[_0x1f85c6(0x16b)])),
    (Scene_TitleTransition[_0x1f85c6(0x16b)]['constructor'] = Scene_TitleTransition),
    (Scene_TitleTransition[_0x1f85c6(0x16b)][_0x1f85c6(0x18d)] = function () {
        const _0x8988f7 = _0x1f85c6;
        Scene_Base[_0x8988f7(0x16b)][_0x8988f7(0x18d)][_0x8988f7(0x167)](this);
    }),
    (Scene_TitleTransition['prototype']['start'] = function () {
        const _0x4b42d8 = _0x1f85c6;
        (Scene_Base[_0x4b42d8(0x16b)]['start']['call'](this),
            DataManager['setupNewGame'](),
            SceneManager[_0x4b42d8(0x13e)](Scene_Map));
    }),
    (VisuMZ['EventTitleScene']['Window_ChoiceList_makeCommandList'] =
        Window_ChoiceList[_0x1f85c6(0x16b)]['makeCommandList']),
    (Window_ChoiceList[_0x1f85c6(0x16b)][_0x1f85c6(0x14e)] = function () {
        const _0x3e0965 = _0x1f85c6;
        (VisuMZ[_0x3e0965(0x189)]['Window_ChoiceList_makeCommandList']['call'](this),
            this[_0x3e0965(0x16e)]());
    }),
    (Window_ChoiceList[_0x1f85c6(0x16b)][_0x1f85c6(0x16e)] = function () {
        const _0x1becf3 = _0x1f85c6;
        for (const _0x1c3d4a of this[_0x1becf3(0x17f)]) {
            if (!_0x1c3d4a) continue;
            if (!_0x1c3d4a['name'][_0x1becf3(0x182)](/<CONTINUE>/i)) continue;
            ((_0x1c3d4a[_0x1becf3(0x14d)] = _0x1c3d4a[_0x1becf3(0x14d)]
                [_0x1becf3(0x159)](/<CONTINUE>/gi, '')
                ['trim']()),
                (_0x1c3d4a['enabled'] = DataManager[_0x1becf3(0x13f)]()));
        }
    }));
