/*:
 * @target MZ
 * @plugindesc Peglin Pachinko Game v1.0.0
 * <AUTHOR> Assistant
 *
 * @command StartPeglin
 * @text Start Peglin Game
 * @desc Start the Peglin pachinko minigame
 *
 * @help Peglin.js
 *
 * A simple pachinko-style game where you shoot balls at pegs.
 * Press OK/Enter to aim and fire, ESC to exit.
 */

(() => {
    'use strict';

    const pluginName = 'Peglin';

    // Sound effect system
    const SoundEffects = {
        // Create audio context for sound generation
        audioContext: null,

        init() {
            if (!this.audioContext) {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            }
        },

        // Generate a beep sound
        beep(frequency = 440, duration = 0.1, type = 'sine', volume = 0.3, pitchMultiplier = 1.0) {
            if (!gameData.soundEnabled) return;
            this.init();

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();

            // Much softer low-pass filter
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(800, this.audioContext.currentTime); // Reduced from 2000
            filter.Q.setValueAtTime(0.3, this.audioContext.currentTime); // Reduced from 0.5

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // Cap frequency to prevent harsh high sounds
            const cappedFreq = Math.min(frequency * pitchMultiplier, 600);
            oscillator.frequency.setValueAtTime(cappedFreq, this.audioContext.currentTime);
            oscillator.type = 'triangle'; // Softer than sine

            // Moderate volume - not too loud, not too soft
            const moderateVolume = volume * 0.4; // Reduce volume by 60% (was 90%)
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(
                moderateVolume,
                this.audioContext.currentTime + 0.02
            );
            gainNode.gain.exponentialRampToValueAtTime(
                0.001,
                this.audioContext.currentTime + duration + 0.05
            );

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        },

        // Generate a noise sound (for impacts)
        noise(duration = 0.1, volume = 0.2) {
            if (!gameData.soundEnabled) return;
            this.init();

            const bufferSize = this.audioContext.sampleRate * duration;
            const buffer = this.audioContext.createBuffer(
                1,
                bufferSize,
                this.audioContext.sampleRate
            );
            const output = buffer.getChannelData(0);

            // Create softer noise with more low frequencies
            for (let i = 0; i < bufferSize; i++) {
                output[i] = (Math.random() * 2 - 1) * 0.3; // Reduced amplitude
            }

            const noise = this.audioContext.createBufferSource();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();

            // Add low-pass filter to soften noise
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(400, this.audioContext.currentTime);
            filter.Q.setValueAtTime(0.2, this.audioContext.currentTime);

            noise.buffer = buffer;
            noise.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // Moderate volume - not too loud, not too soft
            const moderateVolume = volume * 0.25; // Reduce volume by 75% (was 95%)
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(
                moderateVolume,
                this.audioContext.currentTime + 0.01
            );
            gainNode.gain.exponentialRampToValueAtTime(
                0.001,
                this.audioContext.currentTime + duration + 0.05
            );

            noise.start(this.audioContext.currentTime);
            noise.stop(this.audioContext.currentTime + duration);
        },

        // Generate a sweep sound (for special effects)
        sweep(startFreq = 200, endFreq = 800, duration = 0.3, volume = 0.2, pitchMultiplier = 1.0) {
            if (!gameData.soundEnabled) return;
            this.init();

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            const filter = this.audioContext.createBiquadFilter();

            // Much softer low-pass filter
            filter.type = 'lowpass';
            filter.frequency.setValueAtTime(600, this.audioContext.currentTime); // Reduced from 1500
            filter.Q.setValueAtTime(0.2, this.audioContext.currentTime); // Reduced from 0.3

            oscillator.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            // Cap frequencies to prevent harsh sounds
            const cappedStartFreq = Math.min(startFreq * pitchMultiplier, 400);
            const cappedEndFreq = Math.min(endFreq * pitchMultiplier, 500);

            oscillator.frequency.setValueAtTime(cappedStartFreq, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(
                cappedEndFreq,
                this.audioContext.currentTime + duration
            );
            oscillator.type = 'triangle'; // Softer than sine

            // Moderate volume - not too loud, not too soft
            const moderateVolume = volume * 0.35; // Reduce volume by 65% (was 92%)
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(
                moderateVolume,
                this.audioContext.currentTime + 0.02
            );
            gainNode.gain.exponentialRampToValueAtTime(
                0.001,
                this.audioContext.currentTime + duration + 0.05
            );

            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        },

        // Generate a chord sound (for combos)
        chord(frequencies = [440, 554, 659], duration = 0.2, volume = 0.2, pitchMultiplier = 1.0) {
            if (!gameData.soundEnabled) return;
            this.init();

            frequencies.forEach(freq => {
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                // Much softer low-pass filter
                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(700, this.audioContext.currentTime); // Reduced from 1800
                filter.Q.setValueAtTime(0.2, this.audioContext.currentTime); // Reduced from 0.4

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.audioContext.destination);

                // Cap frequency to prevent harsh sounds
                const cappedFreq = Math.min(freq * pitchMultiplier, 500);
                oscillator.frequency.setValueAtTime(cappedFreq, this.audioContext.currentTime);
                oscillator.type = 'triangle'; // Softer than sine

                // Moderate volume - not too loud, not too soft
                const moderateVolume = (volume * 0.3) / frequencies.length; // Reduce volume by 70% (was 94%)
                gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
                gainNode.gain.linearRampToValueAtTime(
                    moderateVolume,
                    this.audioContext.currentTime + 0.02
                );
                gainNode.gain.exponentialRampToValueAtTime(
                    0.001,
                    this.audioContext.currentTime + duration + 0.05
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + duration);
            });
        },

        // Game-specific sound effects
        playShoot() {
            this.beep(437, 0.1, 'sine', 0.02); // Atonal frequency
        },

        playPegHit() {
            // Record hit time for dynamic volume
            gameData.lastHitTimes.push(Date.now());

            const volume = this.getDynamicVolume('normal');
            this.beep(523, 0.1, 'sine', volume); // Atonal frequency
        },

        // Dynamic volume calculation
        getDynamicVolume(pegType = 'normal') {
            const now = Date.now();

            // Clean up old hit times (older than 500ms)
            gameData.lastHitTimes = gameData.lastHitTimes.filter(time => now - time < 500);

            // Count recent hits
            const recentHits = gameData.lastHitTimes.length;

            // Calculate volume based on hit frequency
            let volume = gameData.baseVolume;

            if (recentHits > 5) {
                // Rapid hits - reduce volume to prevent fatigue
                volume = gameData.minVolume;
            } else if (recentHits > 2) {
                // Moderate hits - slight volume reduction
                volume = gameData.baseVolume * 0.7;
            }

            // Boost volume for rare/valuable pegs
            if (pegType === 'diamond') {
                volume = gameData.maxVolume;
            } else if (pegType === 'platinum') {
                volume = gameData.baseVolume * 1.3;
            } else if (pegType === 'gold') {
                volume = gameData.baseVolume * 1.1;
            }

            return volume;
        },

        playSpecialPegHit(type) {
            // Record hit time for dynamic volume
            gameData.lastHitTimes.push(Date.now());

            const volume = this.getDynamicVolume(type);

            switch (type) {
                case 'bomb':
                    this.sweep(180, 95, 0.1, volume); // Atonal sweep
                    break;
                case 'multiball':
                    this.beep(647, 0.1, 'sine', volume); // Atonal frequency
                    break;
                case 'chainlightning':
                    this.sweep(287, 743, 0.1, volume); // Atonal sweep
                    break;
                case 'gravityflip':
                    this.sweep(163, 437, 0.1, volume); // Atonal sweep
                    break;
                case 'teleport':
                    this.sweep(423, 187, 0.1, volume); // Atonal sweep
                    break;
                case 'sticky':
                    this.beep(311, 0.1, 'sine', volume); // Atonal frequency
                    break;
                case 'shield':
                    this.beep(673, 0.1, 'sine', volume); // Atonal frequency
                    break;
                case 'gold':
                    this.beep(587, 0.1, 'sine', volume); // Atonal frequency
                    break;
                case 'platinum':
                    // Single note for platinum
                    this.beep(719, 0.1, 'sine', volume); // Atonal frequency
                    break;
                case 'diamond':
                    // Single note for diamond
                    this.beep(823, 0.1, 'sine', volume); // Atonal frequency
                    break;
                default:
                    this.playPegHit();
            }
        },

        playTargetHit(value) {
            if (value >= 200) {
                this.beep(587, 0.1, 'sine', 0.04); // Atonal frequency
            } else {
                this.beep(523, 0.1, 'sine', 0.03); // Atonal frequency
            }
        },

        playCombo(multiplier) {
            if (multiplier >= 2.0) {
                this.beep(673, 0.1, 'sine', 0.04); // Atonal frequency
            } else if (multiplier >= 1.5) {
                this.beep(587, 0.1, 'sine', 0.035); // Atonal frequency
            } else {
                this.beep(523, 0.1, 'sine', 0.03); // Atonal frequency
            }
        },

        playMultishot() {
            this.sweep(287, 647, 0.1, 0.06); // Atonal sweep
        },

        playSpecialBall(type) {
            switch (type) {
                case 'warp':
                    this.sweep(263, 647, 0.1, 0.05); // Atonal sweep
                    break;

                case 'colorizer':
                    this.sweep(287, 719, 0.1, 0.05); // Atonal sweep
                    break;
                case 'splitter':
                    this.beep(571, 0.1, 'sine', 0.05); // Atonal frequency
                    break;
            }
        },

        playGameWin() {
            this.beep(523, 0.1, 'sine', 0.05); // Atonal frequency
            setTimeout(() => this.beep(587, 0.1, 'sine', 0.05), 100); // Atonal frequency
            setTimeout(() => this.beep(673, 0.1, 'sine', 0.05), 200); // Atonal frequency
        },

        playGameLose() {
            this.sweep(287, 163, 0.1, 0.06); // Atonal sweep
            setTimeout(() => this.sweep(187, 95, 0.1, 0.05), 100); // Atonal sweep
        },

        playBallLost() {
            this.beep(163, 0.1, 'sine', 0.02); // Atonal frequency
        },

        playWallBounce() {
            this.beep(311, 0.1, 'sine', 0.02); // Atonal frequency
        },

        playGravityFlip() {
            this.sweep(163, 437, 0.1, 0.05); // Atonal sweep
            setTimeout(() => this.sweep(437, 163, 0.1, 0.05), 100); // Atonal sweep
        },

        // Musical pitch scaling using major scale
        getMusicalPitch(comboMultiplier) {
            // Base frequency (C major scale starting from C4)
            const baseFreq = 600; // C4 = 261.63Hz, but we'll use 600Hz as our base

            // Major scale intervals (C D E F G A B C)
            // Each step is a ratio from the previous note
            const majorScaleRatios = [
                1.0, // C (root)
                1.125, // D (major 2nd)
                1.25, // E (major 3rd)
                1.333, // F (perfect 4th)
                1.5, // G (perfect 5th)
                1.667, // A (major 6th)
                1.875, // B (major 7th)
                2.0, // C (octave)
            ];

            // Map combo multiplier to scale position
            // 1.0x = C, 1.5x = D, 2.0x = E, 2.5x = F, 3.0x = G, 3.5x = A, 4.0x = B, 4.5x+ = C (octave)
            const scaleIndex = Math.floor((comboMultiplier - 1.0) * 2);
            const clampedIndex = Math.min(scaleIndex, majorScaleRatios.length - 1);

            return majorScaleRatios[clampedIndex];
        },

        // Layered sound system for rich audio
        playLayeredSound(layers) {
            // layers is an array of {frequency, duration, type, volume, delay}
            layers.forEach(layer => {
                const delay = layer.delay || 0;
                setTimeout(() => {
                    this.beep(layer.frequency, layer.duration, layer.type || 'sine', layer.volume);
                }, delay);
            });
        },

        // Enhanced peg hit with layered sounds
        playEnhancedPegHit(pegType) {
            const baseVolume = this.getDynamicVolume(pegType);

            switch (pegType) {
                case 'diamond':
                    // Diamond gets a rich layered sound
                    this.playLayeredSound([
                        { frequency: 823, duration: 0.1, volume: baseVolume * 0.8 }, // Main tone - atonal
                        { frequency: 1237, duration: 0.1, volume: baseVolume * 0.4, delay: 20 }, // Harmonic - atonal
                        { frequency: 437, duration: 0.1, volume: baseVolume * 0.3, delay: 40 }, // Bass - atonal
                    ]);
                    break;
                case 'platinum':
                    // Platinum gets a medium layered sound
                    this.playLayeredSound([
                        { frequency: 719, duration: 0.1, volume: baseVolume * 0.9 }, // Main tone - atonal
                        { frequency: 1079, duration: 0.1, volume: baseVolume * 0.3, delay: 15 }, // Harmonic - atonal
                    ]);
                    break;
                case 'gold':
                    // Gold gets a simple layered sound
                    this.playLayeredSound([
                        { frequency: 587, duration: 0.1, volume: baseVolume * 0.9 }, // Main tone - atonal
                        { frequency: 881, duration: 0.1, volume: baseVolume * 0.2, delay: 10 }, // Harmonic - atonal
                    ]);
                    break;
                default:
                    // Regular pegs just use the single beep
                    this.beep(523, 0.1, 'sine', baseVolume);
            }
        },
    };

    // Enhanced game state
    let gameData = {
        score: 0,
        balls: 10,
        pegs: [],
        totalPegs: 0,
        pegsHit: 0,
        targetScore: 10000, // Win by reaching this score (increases with level)
        gameWon: false,
        gameLost: false,
        level: 1,
        gravityFlipped: false,
        gravityFlipTimeout: null,
        comboMultiplier: 1, // For combo system
        lastHitTime: 0, // For combo timing
        pegPositionsAll: [],
        pegVacantPositions: [],
        comboMeter: 0,
        comboMeterMax: 100,
        multishotReady: false, // Flag for multishot ready state
        soundEnabled: true, // Sound toggle
        // Dynamic volume system
        rapidHitCount: 0, // Count of hits in last 500ms
        lastHitTimes: [], // Array of recent hit timestamps
        baseVolume: 0.025, // Reduced base volume for peg hits
        maxVolume: 0.05, // Reduced maximum volume for rare events
        minVolume: 0.01, // Reduced minimum volume during rapid hits
        pendingRespawnPegs: [], // Pegs waiting to respawn
    };

    // Beautiful Ball class with trails and effects
    class Ball extends Sprite {
        constructor(startX, startY, velocityX, velocityY, type = 'normal') {
            super();
            this.bitmap = new Bitmap(20, 20);
            this.anchor.set(0.5, 0.5);
            this.x = startX || Graphics.width / 2;
            this.y = startY || 100;
            this.vx = velocityX || 0;
            this.vy = velocityY || 2;
            this.active = true;
            this.hitPegs = new Set();
            this.trail = []; // For particle trail
            this.spinRotation = 0;
            this.type = type;
            if (this.type === 'warp') this.warpsLeft = 3;
            if (this.type === 'rainbow') this._rainbowPhase = Math.random();
            this.radius = 8; // Default radius
            this.maxRadius = 64; // Maximum radius (much larger for chaos sandbox)
            this.createBallGraphics();
        }

        createBallGraphics() {
            const radius = this.radius || 8;
            // Resize bitmap if needed
            const size = Math.ceil((radius + 4) * 2);
            if (this.bitmap.width !== size || this.bitmap.height !== size) {
                this.bitmap = new Bitmap(size, size);
                this.anchor.set(0.5, 0.5);
            } else {
                this.bitmap.clear();
            }
            const context = this.bitmap.context;
            const centerX = size / 2;
            const centerY = size / 2;

            // Set colors based on ball type
            let outerGlowColor, highlightColor, lightColor, mainColor, shadowColor;

            switch (this.type) {
                case 'warp':
                    outerGlowColor = 'rgba(0, 255, 255, 0.4)';
                    highlightColor = '#ffffff';
                    lightColor = '#e6ffff';
                    mainColor = '#00ffff';
                    shadowColor = '#008b8b';
                    break;
                case 'colorizer':
                    outerGlowColor = 'rgba(255, 215, 0, 0.4)';
                    highlightColor = '#ffffff';
                    lightColor = '#fffef0';
                    mainColor = '#ffd700';
                    shadowColor = '#b8860b';
                    break;
                case 'splitter':
                    outerGlowColor = 'rgba(255, 136, 0, 0.4)';
                    highlightColor = '#ffffff';
                    lightColor = '#fff8f0';
                    mainColor = '#ff8800';
                    shadowColor = '#cc6600';
                    break;
                case 'rainbow':
                    // Draw a rainbow gradient ball
                    const phase = this._rainbowPhase || 0;
                    const grad = context.createLinearGradient(0, 0, size, size);
                    for (let i = 0; i <= 6; i++) {
                        grad.addColorStop(
                            i / 6,
                            `hsl(${Math.floor((phase * 360 + i * 60) % 360)},100%,60%)`
                        );
                    }
                    context.fillStyle = grad;
                    context.beginPath();
                    context.arc(centerX, centerY, radius, 0, Math.PI * 2);
                    context.fill();
                    // White highlight
                    context.fillStyle = 'rgba(255,255,255,0.7)';
                    context.beginPath();
                    context.arc(
                        centerX - radius / 4,
                        centerY - radius / 4,
                        Math.max(3, radius / 3),
                        0,
                        Math.PI * 2
                    );
                    context.fill();
                    return;
                default: // normal ball
                    outerGlowColor = 'rgba(173, 216, 230, 0.4)';
                    highlightColor = '#ffffff';
                    lightColor = '#e6f3ff';
                    mainColor = '#87ceeb';
                    shadowColor = '#4682b4';
            }

            // Outer glow
            const outerGlow = context.createRadialGradient(
                centerX,
                centerY,
                0,
                centerX,
                centerY,
                radius + 4
            );
            outerGlow.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            outerGlow.addColorStop(0.5, outerGlowColor);
            outerGlow.addColorStop(1, 'rgba(135, 206, 250, 0)');

            context.fillStyle = outerGlow;
            context.beginPath();
            context.arc(centerX, centerY, radius + 4, 0, Math.PI * 2);
            context.fill();

            // Main ball gradient
            const ballGradient = context.createRadialGradient(
                centerX - radius / 4,
                centerY - radius / 4,
                0,
                centerX,
                centerY,
                radius
            );
            ballGradient.addColorStop(0, highlightColor);
            ballGradient.addColorStop(0.3, lightColor);
            ballGradient.addColorStop(0.7, mainColor);
            ballGradient.addColorStop(1, shadowColor);

            context.fillStyle = ballGradient;
            context.beginPath();
            context.arc(centerX, centerY, radius, 0, Math.PI * 2);
            context.fill();

            // Highlight shine
            context.fillStyle = 'rgba(255, 255, 255, 0.7)';
            context.beginPath();
            context.arc(
                centerX - radius / 4,
                centerY - radius / 4,
                Math.max(3, radius / 3),
                0,
                Math.PI * 2
            );
            context.fill();
        }

        update() {
            if (!this.active) return;

            // Don't update if game is over
            if (gameData.gameWon || gameData.gameLost) return;

            // Store trail positions (optimized)
            this.trail.push({ x: this.x, y: this.y, life: 10 });
            if (this.trail.length > 6) this.trail.shift(); // Reduced trail length

            // Update trail life (optimized)
            for (let i = 0; i < this.trail.length; i++) {
                this.trail[i].life--;
            }
            // Remove dead trail points
            const aliveTrail = [];
            for (let i = 0; i < this.trail.length; i++) {
                if (this.trail[i].life > 0) {
                    aliveTrail.push(this.trail[i]);
                }
            }
            this.trail = aliveTrail;

            // Apply gravity
            this.vy += gameData.gravityFlipped ? -0.09 : 0.18;

            // Apply velocity cap
            const currentSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
            const maxSpeed = 20; // Maximum velocity cap
            if (currentSpeed > maxSpeed) {
                this.vx = (this.vx / currentSpeed) * maxSpeed;
                this.vy = (this.vy / currentSpeed) * maxSpeed;
            }

            // Apply movement
            this.x += this.vx;
            this.y += this.vy;

            // Spin animation based on velocity
            this.spinRotation += (Math.abs(this.vx) + Math.abs(this.vy)) * 0.02;
            this.rotation = this.spinRotation;

            // Check peg collisions with optimized physics
            const ballX = this.x;
            const ballY = this.y;
            for (let i = 0; i < gameData.pegs.length; i++) {
                const peg = gameData.pegs[i];
                if (!peg.visible || this.hitPegs.has(peg)) continue;
                const pegRadius = peg.radius || 15; // 15 matches visual size
                const hitRadius = (this.radius || 18) + pegRadius;
                const hitRadiusSquared = hitRadius * hitRadius;
                const dx = ballX - peg.x;
                const dy = ballY - peg.y;
                const distanceSquared = dx * dx + dy * dy;
                if (distanceSquared < hitRadiusSquared) {
                    this.hitPeg(peg);
                    break; // Only hit one peg per frame to prevent multiple collisions
                }
            }

            // Check target collisions
            if (gameData.targets) {
                gameData.targets.forEach(target => {
                    if (
                        target.active &&
                        this.x > target.x &&
                        this.x < target.x + target.bitmap.width &&
                        this.y > target.y &&
                        this.y < target.y + target.bitmap.height
                    ) {
                        // Hit target!
                        gameData.score += target.value;
                        target.visible = false;
                        target.active = false;
                        this.active = false;
                        this.visible = false;

                        // No bonus balls for hitting targets - limited ball system

                        // Create big impact effect for targets
                        if (this.scene && this.scene.createImpactEffect) {
                            this.scene.createImpactEffect(
                                target.x + target.bitmap.width / 2,
                                target.y + 30,
                                '#FF4500',
                                true
                            );
                        }

                        // Play target hit sound
                        SoundEffects.playTargetHit(target.value);
                    }
                });
            }

            // Boundary collisions
            if (this.x < 8) {
                this.x = 8;
                this.vx = Math.abs(this.vx) * 0.8;
                SoundEffects.playWallBounce();
            } else if (this.x > Graphics.width - 8) {
                this.x = Graphics.width - 8;
                this.vx = -Math.abs(this.vx) * 0.8;
                SoundEffects.playWallBounce();
            }

            // Remove if off screen
            if (this.y > Graphics.height + 50) {
                this.active = false;
                this.visible = false;
                SoundEffects.playBallLost();
                // Remove from activeBalls if present
                if (gameData.activeBalls && Array.isArray(gameData.activeBalls)) {
                    const idx = gameData.activeBalls.indexOf(this);
                    if (idx !== -1) gameData.activeBalls.splice(idx, 1);
                }
            }

            // Trail color for special balls
            let trailColor = null;
            if (this.type === 'warp') trailColor = '#00FFFF';
            else if (this.type === 'colorizer') trailColor = '#FFD700';
            else if (this.type === 'splitter') trailColor = '#FF8800';
            if (trailColor && this.trail.length > 0) {
                for (let i = 0; i < this.trail.length; i++) {
                    this.trail[i].color = trailColor;
                }
            }

            // Rainbow Ball color cycling
            if (this.type === 'rainbow') {
                this._rainbowPhase = (this._rainbowPhase || 0) + 0.012;
                if (this._rainbowPhase > 1) this._rainbowPhase -= 1;
                this.createBallGraphics(); // Animate color cycling
            }
        }

        hitPeg(peg, chainActivated = new Set()) {
            if (peg.type === 'obstacle') {
                // Obstacle pegs are unbreakable and unscored
                return;
            }
            if (chainActivated.has(peg)) return; // Prevent infinite loops
            chainActivated.add(peg);
            this.hitPegs.add(peg);
            // Ball grows when hitting a peg
            this.radius = Math.min(this.radius + 1, this.maxRadius);
            this.createBallGraphics();
            // Calculate bounce direction (only for direct hits)
            if (!chainActivated.direct) {
                const dx = this.x - peg.x;
                const dy = this.y - peg.y;
                const distance = Math.sqrt(dx * dx + dy * dy);
                if (distance > 0) {
                    const nx = dx / distance;
                    const ny = dy / distance;
                    const dotProduct = this.vx * nx + this.vy * ny;
                    this.vx = this.vx - 2 * dotProduct * nx;
                    this.vy = this.vy - 2 * dotProduct * ny;
                    if (this.type === 'splitter') {
                        // Splitter balls have a 5% chance to split into two copies when hitting pegs
                        if (Math.random() < 0.05) {
                            // Create a copy of this ball
                            const splitBall = new Ball(
                                this.x,
                                this.y,
                                this.vx,
                                this.vy,
                                'splitter'
                            );
                            splitBall.scene = this.scene;
                            splitBall.hitPegs = new Set(this.hitPegs); // Copy hit pegs to prevent re-hitting

                            // Give the split ball a slightly different trajectory
                            const angleChange = (Math.random() - 0.5) * 0.4;
                            const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                            const currentAngle = Math.atan2(this.vy, this.vx);
                            const newAngle = currentAngle + angleChange;
                            splitBall.vx = Math.cos(newAngle) * speed;
                            splitBall.vy = Math.sin(newAngle) * speed;

                            // Add the split ball to the scene and active balls
                            if (this.scene) this.scene.addChild(splitBall);
                            gameData.activeBalls = gameData.activeBalls || [];
                            gameData.activeBalls.push(splitBall);

                            // Create a visual effect for the split
                            if (this.scene && this.scene.createImpactEffect) {
                                this.scene.createImpactEffect(this.x, this.y, '#FF8800', true);
                            }
                        }
                    } else {
                        this.vx *= 0.8;
                        this.vy *= 0.8;
                    }
                    this.x = peg.x + nx * 18;
                    this.y = peg.y + ny * 18;
                }
            }
            // Enhanced Warp Ball effect - now more reliable and beneficial
            if (this.type === 'warp' && this.warpsLeft > 0 && Math.random() < 0.4) {
                this.warpsLeft--;
                // Find highest visible peg
                const visiblePegs = gameData.pegs.filter(p => p.visible);
                if (visiblePegs.length > 0) {
                    let highestPeg = visiblePegs[0];
                    for (const p of visiblePegs) {
                        if (p.y < highestPeg.y) highestPeg = p;
                    }
                    // Teleport to random X above highest peg, Y just above peg
                    const minX = Math.max(32, highestPeg.x - 60);
                    const maxX = Math.min(Graphics.width - 32, highestPeg.x + 60);
                    this.x = Math.random() * (maxX - minX) + minX;
                    this.y = Math.max(highestPeg.y - 60, 0);
                    this.vy -= 4; // Stronger upward boost
                    // Add a small speed boost
                    const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                    if (speed < 15) {
                        this.vx *= 1.2;
                        this.vy *= 1.2;
                    }
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(this.x, this.y, '#00FFFF', true);
                    }
                }
            }
            // Special peg effects
            if (peg.type === 'bomb' && peg.visible) {
                SoundEffects.playSpecialPegHit('bomb');
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(peg.x, peg.y, '#FF2222', true);
                }
                gameData.pegs.forEach(otherPeg => {
                    if (
                        otherPeg.visible &&
                        otherPeg !== peg &&
                        Math.abs(otherPeg.x - peg.x) < 110 &&
                        Math.abs(otherPeg.y - peg.y) < 110
                    ) {
                        const d = Math.sqrt((otherPeg.x - peg.x) ** 2 + (otherPeg.y - peg.y) ** 2);
                        if (d <= 100) {
                            // Chain activate if special
                            if (
                                (otherPeg.type === 'bomb' ||
                                    otherPeg.type === 'multiball' ||
                                    otherPeg.type === 'chainlightning') &&
                                !chainActivated.has(otherPeg)
                            ) {
                                this.hitPeg(otherPeg, chainActivated);
                            } else if (otherPeg.type !== 'obstacle') {
                                otherPeg.visible = false;
                                gameData.pegsHit++;
                                if (this.scene && this.scene.createImpactEffect) {
                                    this.scene.createImpactEffect(
                                        otherPeg.x,
                                        otherPeg.y,
                                        '#FF8888'
                                    );
                                }
                            }
                        }
                    }
                });
            } else if (peg.type === 'multiball' && peg.visible) {
                SoundEffects.playSpecialPegHit('multiball');
                for (let i = 0; i < 2; i++) {
                    const angle = Math.random() * Math.PI * 2;
                    const speed = 7 + Math.random() * 3;
                    const vx = Math.cos(angle) * speed;
                    const vy = Math.sin(angle) * speed;
                    const newBall = new Ball(this.x, this.y, vx, vy);
                    newBall.scene = this.scene;
                    if (this.scene) this.scene.addChild(newBall);
                    gameData.activeBalls = gameData.activeBalls || [];
                    gameData.activeBalls.push(newBall);
                }
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(peg.x, peg.y, '#00BFFF', true);
                }
            } else if (peg.type === 'chainlightning' && peg.visible) {
                SoundEffects.playSpecialPegHit('chainlightning');
                let source = peg;
                let zapped = [peg];
                let chainPath = [{ x: peg.x, y: peg.y }];
                for (let jump = 0; jump < 6; jump++) {
                    let nearest = null;
                    let nearestDist = 99999;
                    for (const other of gameData.pegs) {
                        if (
                            other.visible &&
                            !zapped.includes(other) &&
                            Math.abs(other.x - source.x) < 210 &&
                            Math.abs(other.y - source.y) < 210
                        ) {
                            const d = Math.sqrt(
                                (other.x - source.x) ** 2 + (other.y - source.y) ** 2
                            );
                            if (d < 200 && d < nearestDist) {
                                nearest = other;
                                nearestDist = d;
                            }
                        }
                    }
                    if (nearest) {
                        chainPath.push({ x: nearest.x, y: nearest.y });
                        // Chain activate if special
                        if (
                            (nearest.type === 'bomb' ||
                                nearest.type === 'multiball' ||
                                nearest.type === 'chainlightning') &&
                            !chainActivated.has(nearest)
                        ) {
                            this.hitPeg(nearest, chainActivated);
                        } else if (nearest.type !== 'obstacle') {
                            nearest.visible = false;
                            gameData.pegsHit++;
                        }
                        zapped.push(nearest);
                        source = nearest;
                    } else {
                        break;
                    }
                }
                if (this.scene && this.scene.drawLightningChain) {
                    this.scene.drawLightningChain(chainPath);
                }
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(peg.x, peg.y, '#00FFFF', true);
                }
            } else if (peg.type === 'gravityflip' && peg.visible) {
                // Gravity flip: reverse gravity for 0.7 second
                if (!gameData.gravityFlipped) {
                    gameData.gravityFlipped = true;
                    SoundEffects.playGravityFlip();
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(peg.x, peg.y, '#C71585', true);
                    }
                    if (this.scene && this.scene.showGravityFlipEffect) {
                        this.scene.showGravityFlipEffect();
                    }
                    if (gameData.gravityFlipTimeout) clearTimeout(gameData.gravityFlipTimeout);
                    gameData.gravityFlipTimeout = setTimeout(() => {
                        gameData.gravityFlipped = false;
                    }, 700);
                } else {
                    // If already flipped, reset timer
                    if (gameData.gravityFlipTimeout) clearTimeout(gameData.gravityFlipTimeout);
                    gameData.gravityFlipTimeout = setTimeout(() => {
                        gameData.gravityFlipped = false;
                    }, 700);
                }
            }
            if (peg.type === 'teleport' && peg.visible) {
                // Teleport to another teleport peg
                SoundEffects.playSpecialPegHit('teleport');
                const teleports = gameData.pegs.filter(
                    p => p.type === 'teleport' && p.visible && p !== peg
                );
                if (teleports.length > 0) {
                    const dest = teleports[Math.floor(Math.random() * teleports.length)];
                    this.x = dest.x;
                    this.y = dest.y;
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(dest.x, dest.y, '#A020F0', true);
                    }
                }
            }
            if (peg.type === 'sticky' && peg.visible) {
                // Sticky pegs now provide a beneficial effect: brief pause then powerful launch
                SoundEffects.playSpecialPegHit('sticky');
                this.vx = 0;
                this.vy = 0;
                this.active = false;
                // Create a visual effect to show the ball is charging
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(peg.x, peg.y, '#39FF14', true);
                }
                setTimeout(() => {
                    // Launch with increased power and toward nearest visible peg
                    let nearest = null,
                        nearestDist = 99999;
                    for (const otherPeg of gameData.pegs) {
                        if (otherPeg.visible && otherPeg !== peg) {
                            const d = Math.hypot(otherPeg.x - peg.x, otherPeg.y - peg.y);
                            if (d < nearestDist) {
                                nearest = otherPeg;
                                nearestDist = d;
                            }
                        }
                    }

                    let angle,
                        speed = 12; // Increased speed
                    if (nearest) {
                        // Aim toward nearest peg
                        angle = Math.atan2(nearest.y - peg.y, nearest.x - peg.x);
                    } else {
                        // Random direction if no visible pegs
                        angle = Math.random() * Math.PI * 2;
                    }

                    this.vx = Math.cos(angle) * speed;
                    this.vy = Math.sin(angle) * speed;
                    this.active = true;

                    // Add a speed boost effect
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(this.x, this.y, '#39FF14', false);
                    }
                }, 300); // Reduced delay for better gameplay
            }
            if (peg.type === 'shield' && peg.visible) {
                // Absorb one hit, then become normal
                if (!peg._shieldHit) {
                    peg._shieldHit = true;
                    SoundEffects.playSpecialPegHit('shield');
                    // Change visual to normal peg
                    peg.type = 'normal';
                    peg.createPegGraphics();
                    return; // Don't destroy peg on first hit
                }
            }
            if (peg.type === 'duplicator' && peg.visible) {
                // Duplicator peg: creates a copy of the ball that hit it
                SoundEffects.playSpecialPegHit('duplicator');
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(peg.x, peg.y, '#00BFFF', true);
                }
                // Create a duplicate ball with same type and velocity
                const duplicateBall = new Ball(this.x, this.y, this.vx, this.vy, this.type);
                duplicateBall.scene = this.scene;
                if (this.scene) this.scene.addChild(duplicateBall);
                gameData.activeBalls = gameData.activeBalls || [];
                gameData.activeBalls.push(duplicateBall);
            }
            // Remove peg and add score with visual effect
            peg.visible = false;
            gameData.pegVacantPositions = gameData.pegVacantPositions || [];
            gameData.pegVacantPositions.push({ x: peg.x, y: peg.y });
            // Add to pending respawn list (for any peg that disappears and is not already pending)
            if (peg.visible === false) {
                gameData.pendingRespawnPegs = gameData.pendingRespawnPegs || [];
                if (!gameData.pendingRespawnPegs.some(p => p.x === peg.x && p.y === peg.y)) {
                    gameData.pendingRespawnPegs.push({
                        x: peg.x,
                        y: peg.y,
                        type: peg.type,
                        value: peg.value,
                        respawn: 6,
                    });
                }
            }
            // Combo system - increase multiplier for quick successive hits
            const currentTime = Date.now();
            if (currentTime - gameData.lastHitTime < 1000) {
                // Within 1 second
                gameData.comboMultiplier = gameData.comboMultiplier + 0.1; // No cap
            } else {
                gameData.comboMultiplier = 1.0; // Reset combo
            }
            gameData.lastHitTime = currentTime;

            // Calculate score with combo multiplier
            const baseScore = peg.value || 10;
            const finalScore = Math.floor(baseScore * gameData.comboMultiplier);
            gameData.score += finalScore;
            gameData.pegsHit++;

            // Play enhanced layered sound effects for special pegs
            if (peg.type === 'diamond' || peg.type === 'platinum' || peg.type === 'gold') {
                SoundEffects.playEnhancedPegHit(peg.type);
            } else {
                SoundEffects.playSpecialPegHit(peg.type);
            }
            if (gameData.comboMultiplier > 1.0) {
                SoundEffects.playCombo(gameData.comboMultiplier);
            }

            // Show score popup
            if (this.scene && this.scene.showScorePopup) {
                this.scene.showScorePopup(peg.x, peg.y, finalScore, gameData.comboMultiplier, peg);
            }

            // Festive firework colors for special pegs
            const fireworkColors = {
                bomb: '#FF2222', // Red
                multiball: '#1976D2', // Blue
                chainlightning: '#00FFFF', // Cyan
                gravityflip: '#C71585', // Magenta
                teleport: '#A020F0', // Purple
                sticky: '#39FF14', // Green
                shield: '#00BFFF', // Blue
                duplicator: '#00BFFF', // Sky Blue
                gold: '#FFD700', // Gold
                platinum: '#E5E4E2', // Platinum
                diamond: '#B9F2FF', // Diamond
            };
            const specialEffectTypes = [
                'bomb',
                'multiball',
                'chainlightning',
                'gravityflip',
                'teleport',
                'sticky',
                'shield',
                'duplicator',
            ];
            let impactColor = '#FFD700'; // Default gold
            if (peg.type in fireworkColors) impactColor = fireworkColors[peg.type];
            if (this.scene && this.scene.createImpactEffect) {
                const isBig = specialEffectTypes.includes(peg.type);
                this.scene.createImpactEffect(peg.x, peg.y, impactColor, isBig);
            }

            if (this.type === 'duplicator') {
                // Enhanced duplicator: creates multiple pegs and has a chance for bonus effects
                gameData.pegVacantPositions = gameData.pegVacantPositions || [];
                let createdCount = 0;

                // Track occupied positions to prevent overlapping pegs
                const occupiedPositions = new Set();
                gameData.pegs.forEach(p => {
                    if (p.visible) {
                        occupiedPositions.add(`${p.x},${p.y}`);
                    }
                });

                // Create 1-3 pegs based on available positions
                const maxPegs = Math.min(3, gameData.pegVacantPositions.length);
                for (let i = 0; i < maxPegs; i++) {
                    if (gameData.pegVacantPositions.length > 0) {
                        // Find a position that's not already occupied
                        let spot = null;
                        let attempts = 0;
                        const maxAttempts = gameData.pegVacantPositions.length * 2; // Prevent infinite loop

                        while (attempts < maxAttempts && gameData.pegVacantPositions.length > 0) {
                            const idx = Math.floor(
                                Math.random() * gameData.pegVacantPositions.length
                            );
                            const candidateSpot = gameData.pegVacantPositions[idx];
                            const positionKey = `${candidateSpot.x},${candidateSpot.y}`;

                            if (!occupiedPositions.has(positionKey)) {
                                spot = gameData.pegVacantPositions.splice(idx, 1)[0];
                                occupiedPositions.add(positionKey);
                                break;
                            }
                            attempts++;
                        }

                        if (spot) {
                            // 50% chance to upgrade the peg type
                            let pegType = peg.type;
                            if (Math.random() < 0.5) {
                                if (pegType === 'normal') pegType = 'gold';
                                else if (pegType === 'gold') pegType = 'platinum';
                                else if (pegType === 'platinum') pegType = 'diamond';
                            }

                            const newPeg = new Peg(spot.x, spot.y, pegType);
                            gameData.pegs.push(newPeg);
                            gameData.totalPegs++;
                            createdCount++;
                            if (this.scene) this.scene.addChild(newPeg);
                            if (this.scene && this.scene.createImpactEffect) {
                                this.scene.createImpactEffect(spot.x, spot.y, '#00BFFF', true);
                            }
                        }
                    }
                }

                // Bonus effect: if we created multiple pegs, add a small combo boost
                if (createdCount >= 2) {
                    gameData.comboMultiplier = Math.min(gameData.comboMultiplier + 0.1, 5.0);
                }
            }
            if (this.type === 'colorizer') {
                // Enhanced colorizer: upgrades more pegs and has a chance to create bonus effects
                let upgradedCount = 0;
                for (const other of gameData.pegs) {
                    if (other.visible && Math.hypot(other.x - peg.x, other.y - peg.y) < 80) {
                        // Increased range
                        let upgraded = false;
                        if (other.type === 'normal') {
                            other.type = 'gold';
                            other.value = 50; // Update value to match gold peg
                            upgraded = true;
                        } else if (other.type === 'gold') {
                            other.type = 'platinum';
                            other.value = 100; // Update value to match platinum peg
                            upgraded = true;
                        } else if (other.type === 'platinum') {
                            other.type = 'diamond';
                            other.value = 250; // Update value to match diamond peg
                            upgraded = true;
                        }
                        if (upgraded) {
                            upgradedCount++;
                            // Force a complete redraw of the peg graphics
                            other.bitmap.clear();
                            other.createPegGraphics();
                            if (this.scene && this.scene.createImpactEffect) {
                                this.scene.createImpactEffect(other.x, other.y, '#FFD700', false);
                            }
                        }
                    }
                }

                // Bonus effect: if we upgraded 3+ pegs, add a small combo boost
                if (upgradedCount >= 3) {
                    gameData.comboMultiplier = Math.min(gameData.comboMultiplier + 0.1, 5.0);
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(peg.x, peg.y, '#FFD700', true);
                    }
                }
            }
            gameData.comboMeter = Math.min(gameData.comboMeter + 1, gameData.comboMeterMax);

            // Check if combo meter just reached max (multishot ready)
            if (gameData.comboMeter === gameData.comboMeterMax && !gameData.multishotReady) {
                gameData.multishotReady = true;
                SoundEffects.playMultishot();
                // Show popup
                const popup = new Sprite();
                popup.bitmap = new Bitmap(340, 56);
                const ctx = popup.bitmap.context;
                ctx.font = 'bold 28px ' + $gameSystem.mainFontFace();
                ctx.textAlign = 'center';
                ctx.fillStyle = '#00FFAA';
                ctx.strokeStyle = '#003344';
                ctx.lineWidth = 4;
                ctx.strokeText('MULTISHOT READY!', 170, 44);
                ctx.fillText('MULTISHOT READY!', 170, 44);
                popup.x = Graphics.width / 2 - 170;
                popup.y = 180;
                this.scene.addChild(popup);
                setTimeout(() => {
                    this.scene.removeChild(popup);
                }, 900);
            }

            // Rainbow Ball: one random effect per hit
            if (this.type === 'rainbow') {
                const effects = ['warp', 'colorizer', 'splitter', 'bomb', 'multiball', 'teleport'];
                const effect = effects[Math.floor(Math.random() * effects.length)];
                const effectColors = {
                    warp: '#00FFFF',
                    colorizer: '#FFD700',
                    splitter: '#FF8800',
                    bomb: '#FF2222',
                    multiball: '#1976D2',
                    teleport: '#A020F0',
                };
                if (this.scene && this.scene.createImpactEffect) {
                    this.scene.createImpactEffect(
                        this.x,
                        this.y,
                        effectColors[effect] || '#FF00FF',
                        true
                    );
                }
                if (effect === 'warp' && (this.warpsLeft === undefined || this.warpsLeft > 0)) {
                    this.warpsLeft = (this.warpsLeft || 3) - 1;
                    const visiblePegs = gameData.pegs.filter(p => p.visible);
                    if (visiblePegs.length > 0) {
                        let highestPeg = visiblePegs[0];
                        for (const p of visiblePegs) {
                            if (p.y < highestPeg.y) highestPeg = p;
                        }
                        const minX = Math.max(32, highestPeg.x - 60);
                        const maxX = Math.min(Graphics.width - 32, highestPeg.x + 60);
                        this.x = Math.random() * (maxX - minX) + minX;
                        this.y = Math.max(highestPeg.y - 60, 0);
                        this.vy -= 4;
                        const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                        if (speed < 15) {
                            this.vx *= 1.2;
                            this.vy *= 1.2;
                        }
                        if (this.scene && this.scene.createImpactEffect) {
                            this.scene.createImpactEffect(this.x, this.y, '#00FFFF', true);
                        }
                    }
                } else if (effect === 'colorizer') {
                    let upgradedCount = 0;
                    for (const other of gameData.pegs) {
                        if (other.visible && Math.hypot(other.x - peg.x, other.y - peg.y) < 80) {
                            let upgraded = false;
                            if (other.type === 'normal') {
                                other.type = 'gold';
                                other.value = 50;
                                upgraded = true;
                            } else if (other.type === 'gold') {
                                other.type = 'platinum';
                                other.value = 100;
                                upgraded = true;
                            } else if (other.type === 'platinum') {
                                other.type = 'diamond';
                                other.value = 250;
                                upgraded = true;
                            }
                            if (upgraded) {
                                upgradedCount++;
                                other.bitmap.clear();
                                other.createPegGraphics();
                                if (this.scene && this.scene.createImpactEffect) {
                                    this.scene.createImpactEffect(
                                        other.x,
                                        other.y,
                                        '#FFD700',
                                        false
                                    );
                                }
                            }
                        }
                    }
                    if (upgradedCount >= 3) {
                        gameData.comboMultiplier = Math.min(gameData.comboMultiplier + 0.1, 5.0);
                        if (this.scene && this.scene.createImpactEffect) {
                            this.scene.createImpactEffect(peg.x, peg.y, '#FFD700', true);
                        }
                    }
                } else if (effect === 'splitter') {
                    if (Math.random() < 0.05) {
                        const splitBall = new Ball(this.x, this.y, this.vx, this.vy, 'splitter');
                        splitBall.scene = this.scene;
                        splitBall.hitPegs = new Set(this.hitPegs);
                        const angleChange = (Math.random() - 0.5) * 0.4;
                        const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                        const currentAngle = Math.atan2(this.vy, this.vx);
                        const newAngle = currentAngle + angleChange;
                        splitBall.vx = Math.cos(newAngle) * speed;
                        splitBall.vy = Math.sin(newAngle) * speed;
                        if (this.scene) this.scene.addChild(splitBall);
                        gameData.activeBalls = gameData.activeBalls || [];
                        gameData.activeBalls.push(splitBall);
                        if (this.scene && this.scene.createImpactEffect) {
                            this.scene.createImpactEffect(this.x, this.y, '#FF8800', true);
                        }
                    }
                } else if (effect === 'bomb') {
                    SoundEffects.playSpecialPegHit('bomb');
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(this.x, this.y, '#FF2222', true);
                    }
                    gameData.pegs.forEach(otherPeg => {
                        if (
                            otherPeg.visible &&
                            otherPeg !== peg &&
                            Math.abs(otherPeg.x - this.x) < 110 &&
                            Math.abs(otherPeg.y - this.y) < 110
                        ) {
                            const d = Math.sqrt(
                                (otherPeg.x - this.x) ** 2 + (otherPeg.y - this.y) ** 2
                            );
                            if (d <= 100) {
                                otherPeg.visible = false;
                                gameData.pegsHit++;
                                if (this.scene && this.scene.createImpactEffect) {
                                    this.scene.createImpactEffect(
                                        otherPeg.x,
                                        otherPeg.y,
                                        '#FF8888'
                                    );
                                }
                            }
                        }
                    });
                } else if (effect === 'multiball') {
                    SoundEffects.playSpecialPegHit('multiball');
                    for (let i = 0; i < 2; i++) {
                        const angle = Math.random() * Math.PI * 2;
                        const speed = 7 + Math.random() * 3;
                        const vx = Math.cos(angle) * speed;
                        const vy = Math.sin(angle) * speed;
                        const newBall = new Ball(this.x, this.y, vx, vy);
                        newBall.scene = this.scene;
                        if (this.scene) this.scene.addChild(newBall);
                        gameData.activeBalls = gameData.activeBalls || [];
                        gameData.activeBalls.push(newBall);
                    }
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(this.x, this.y, '#1976D2', true);
                    }
                } else if (effect === 'teleport') {
                    SoundEffects.playSpecialPegHit('teleport');
                    if (this.scene && this.scene.createImpactEffect) {
                        this.scene.createImpactEffect(this.x, this.y, '#A020F0', true);
                    }
                    this.x = Math.random() * (Graphics.width - 64) + 32;
                    this.y = Math.random() * (Graphics.height / 2) + 32;
                    this.vx = (Math.random() - 0.5) * 8;
                    this.vy = (Math.random() - 0.5) * 8;
                }
            }
        }

        distanceTo(other) {
            const dx = this.x - other.x;
            const dy = this.y - other.y;
            return Math.sqrt(dx * dx + dy * dy);
        }
    }

    // Beautiful Peg class with gradients and glow
    class Peg extends Sprite {
        constructor(x, y, type = 'normal', value = 10) {
            super();
            this.bitmap = new Bitmap(42, 42);
            this.anchor.set(0.5, 0.5);
            this.x = x;
            this.y = y;
            this.type = type;
            this.value = value;
            this.hit = false;
            this.glowPhase = Math.random() * Math.PI * 2; // For pulsing glow
            this.glowSpeed = Math.random() * 0.02 + 0.005; // Random speed between 0.005 and 0.025 (much slower)
            this.glowIntensity = Math.random() * 0.3 + 0.1; // Random intensity between 0.1 and 0.4
            this.createPegGraphics();
        }

        createPegGraphics() {
            const context = this.bitmap.context;
            const centerX = 21;
            const centerY = 21;
            const radius = 15;
            let mainColor = '#D4AF37';
            let glowColor = 'rgba(255, 215, 0, 0.3)';
            if (this.type === 'bomb') {
                mainColor = '#D32F2F';
                glowColor = 'rgba(255, 50, 50, 0.4)';
            } else if (this.type === 'multiball') {
                mainColor = '#1976D2';
                glowColor = 'rgba(50, 150, 255, 0.4)';
            } else if (this.type === 'chainlightning') {
                mainColor = '#00FFFF';
                glowColor = 'rgba(0,255,255,0.4)';
            } else if (this.type === 'gravityflip') {
                mainColor = '#C71585'; // MediumVioletRed
                glowColor = 'rgba(199,21,133,0.4)';
            } else if (this.type === 'gold') {
                mainColor = '#FFD700';
                glowColor = 'rgba(255, 215, 0, 0.5)';
            } else if (this.type === 'platinum') {
                mainColor = '#E5E4E2';
                glowColor = 'rgba(229, 228, 226, 0.5)';
            } else if (this.type === 'diamond') {
                mainColor = '#B9F2FF';
                glowColor = 'rgba(185, 242, 255, 0.6)';
            } else if (this.type === 'duplicator') {
                mainColor = '#00BFFF';
                glowColor = 'rgba(0, 191, 255, 0.4)';
            }

            if (this.type === 'shield') {
                // Shield icon
                context.save();
                context.translate(centerX, centerY);
                context.strokeStyle = '#00BFFF';
                context.lineWidth = 3;
                context.beginPath();
                context.arc(0, 0, 10, 0, Math.PI * 2);
                context.stroke();
                context.font = 'bold 16px ' + $gameSystem.mainFontFace();
                context.fillStyle = '#00BFFF';
                context.textAlign = 'center';
                context.fillText('🛡️', 0, 6);
                context.restore();
            }
            const glowGradient = context.createRadialGradient(
                centerX,
                centerY,
                0,
                centerX,
                centerY,
                radius + 9
            );
            glowGradient.addColorStop(0, glowColor);
            glowGradient.addColorStop(0.7, glowColor);
            glowGradient.addColorStop(1, 'rgba(0,0,0,0)');
            context.fillStyle = glowGradient;
            context.beginPath();
            context.arc(centerX, centerY, radius + 9, 0, Math.PI * 2);
            context.fill();
            const pegGradient = context.createRadialGradient(
                centerX - 4.5,
                centerY - 4.5,
                0,
                centerX,
                centerY,
                radius
            );
            pegGradient.addColorStop(0, mainColor);
            pegGradient.addColorStop(0.7, '#8B7355');
            pegGradient.addColorStop(1, '#654321');
            context.fillStyle = pegGradient;
            context.beginPath();
            context.arc(centerX, centerY, radius, 0, Math.PI * 2);
            context.fill();
            context.strokeStyle = '#FFD700';
            context.lineWidth = 1.5;
            context.globalAlpha = 0.6;
            context.beginPath();
            context.arc(centerX, centerY, radius - 1.5, 0, Math.PI * 2);
            context.stroke();
            context.globalAlpha = 1;
            // Draw icon for sticky pegs on top (after main graphics)
            if (this.type === 'sticky') {
                // Gooey green blob with outline and highlight
                context.save();
                context.translate(centerX, centerY);
                // Main goo blob
                context.beginPath();
                context.arc(0, 0, 8, 0, Math.PI * 2);
                context.fillStyle = '#39FF14';
                context.shadowColor = '#39FF14';
                context.shadowBlur = 8;
                context.fill();
                context.shadowBlur = 0;
                // Dark green outline
                context.lineWidth = 2.5;
                context.strokeStyle = '#006400';
                context.stroke();
                // White highlight
                context.beginPath();
                context.arc(-3, -3, 2, 0, Math.PI * 2);
                context.fillStyle = '#ffffff';
                context.globalAlpha = 0.7;
                context.fill();
                context.globalAlpha = 1;
                context.restore();
            }
            // Draw icon for teleport pegs on top (after main graphics)
            if (this.type === 'teleport') {
                // Purple swirling portal
                context.save();
                context.translate(centerX, centerY);
                context.strokeStyle = '#A020F0';
                context.lineWidth = 3;
                context.beginPath();
                context.arc(0, 0, 10, 0, Math.PI * 2);
                context.stroke();
                context.font = 'bold 16px ' + $gameSystem.mainFontFace();
                context.fillStyle = '#A020F0';
                context.textAlign = 'center';
                context.fillText('⭮', 0, 6);
                context.restore();
            }
            // Draw icon for special pegs
            if (this.type === 'bomb') {
                context.save();
                context.beginPath();
                context.arc(centerX, centerY, 6, 0, Math.PI * 2);
                context.fillStyle = '#222';
                context.fill();
                context.strokeStyle = '#FFD700';
                context.lineWidth = 2;
                context.beginPath();
                context.moveTo(centerX + 4, centerY - 4);
                context.lineTo(centerX + 10, centerY - 10);
                context.stroke();
                context.restore();
            } else if (this.type === 'multiball') {
                context.save();
                context.translate(centerX, centerY);
                context.rotate(-Math.PI / 10);
                context.beginPath();
                for (let i = 0; i < 5; i++) {
                    context.lineTo(0, -7);
                    context.rotate(Math.PI / 5);
                    context.lineTo(0, -3);
                    context.rotate(Math.PI / 5);
                }
                context.closePath();
                context.fillStyle = '#fff';
                context.globalAlpha = 0.85;
                context.fill();
                context.globalAlpha = 1;
                context.restore();
            } else if (this.type === 'chainlightning') {
                // Lightning bolt icon
                context.save();
                context.strokeStyle = '#00FFFF';
                context.lineWidth = 3;
                context.beginPath();
                context.moveTo(centerX - 3, centerY - 7);
                context.lineTo(centerX + 1, centerY);
                context.lineTo(centerX - 1, centerY);
                context.lineTo(centerX + 3, centerY + 7);
                context.stroke();
                context.restore();
            } else if (this.type === 'gravityflip') {
                // Up/down arrow icon
                context.save();
                context.strokeStyle = '#fff';
                context.lineWidth = 3;
                // Up arrow
                context.beginPath();
                context.moveTo(centerX, centerY - 7);
                context.lineTo(centerX, centerY + 7);
                context.stroke();
                context.beginPath();
                context.moveTo(centerX - 4, centerY - 3);
                context.lineTo(centerX, centerY - 7);
                context.lineTo(centerX + 4, centerY - 3);
                context.stroke();
                // Down arrow
                context.beginPath();
                context.moveTo(centerX - 4, centerY + 3);
                context.lineTo(centerX, centerY + 7);
                context.lineTo(centerX + 4, centerY + 3);
                context.stroke();
                context.restore();
            } else if (this.type === 'gold') {
                // Gold coin icon
                context.save();
                context.fillStyle = '#FFD700';
                context.beginPath();
                context.arc(centerX, centerY, 8, 0, Math.PI * 2);
                context.fill();
                context.strokeStyle = '#B8860B';
                context.lineWidth = 2;
                context.stroke();
                context.fillStyle = '#B8860B';
                context.font = 'bold 12px ' + $gameSystem.mainFontFace();
                context.textAlign = 'center';
                context.fillText('$', centerX, centerY + 4);
                context.restore();
            } else if (this.type === 'platinum') {
                // Platinum star icon
                context.save();
                context.translate(centerX, centerY); // Center the drawing
                context.fillStyle = '#E5E4E2';
                context.beginPath();
                for (let i = 0; i < 5; i++) {
                    context.lineTo(0, -8);
                    context.rotate(Math.PI / 5);
                    context.lineTo(0, -3);
                    context.rotate(Math.PI / 5);
                }
                context.closePath();
                context.fill();
                context.restore();
            } else if (this.type === 'diamond') {
                // Diamond gem icon
                context.save();
                context.fillStyle = '#B9F2FF';
                context.beginPath();
                context.moveTo(centerX, centerY - 8);
                context.lineTo(centerX + 6, centerY - 2);
                context.lineTo(centerX + 6, centerY + 2);
                context.lineTo(centerX, centerY + 8);
                context.lineTo(centerX - 6, centerY + 2);
                context.lineTo(centerX - 6, centerY - 2);
                context.closePath();
                context.fill();
                context.strokeStyle = '#87CEEB';
                context.lineWidth = 2;
                context.stroke();
                context.restore();
            } else if (this.type === 'duplicator') {
                // Duplicator icon - two overlapping circles
                context.save();
                context.fillStyle = '#00BFFF';
                context.globalAlpha = 0.8;
                // First circle
                context.beginPath();
                context.arc(centerX - 3, centerY, 5, 0, Math.PI * 2);
                context.fill();
                // Second circle (overlapping)
                context.beginPath();
                context.arc(centerX + 3, centerY, 5, 0, Math.PI * 2);
                context.fill();
                // Overlap highlight
                context.globalAlpha = 1;
                context.fillStyle = '#ffffff';
                context.beginPath();
                context.arc(centerX, centerY, 2, 0, Math.PI * 2);
                context.fill();
                context.restore();
            }
            if (this.type === 'obstacle') {
                // Solid dark gray with thick lighter outline, no icon, no glow
                context.save();
                context.shadowColor = 'rgba(0,0,0,0)'; // No glow
                context.fillStyle = '#444444';
                context.beginPath();
                context.arc(centerX, centerY, 10, 0, Math.PI * 2);
                context.fill();
                context.lineWidth = 5;
                context.strokeStyle = '#888888';
                context.stroke();
                context.restore();
            }
        }

        update() {
            super.update();
            // Randomized pulsing glow effect
            this.glowPhase += this.glowSpeed;
            const baseAlpha = 0.8;
            const glowIntensity = baseAlpha + Math.sin(this.glowPhase) * this.glowIntensity;
            this.alpha = glowIntensity;
        }
    }

    // Simple working scene
    class Scene_Peglin extends Scene_Base {
        create() {
            super.create();
            this.createBackground();
            this.createPegs();
            this.createUI();
            // Set target score for this level
            gameData.targetScore = 10000 + (gameData.level - 1) * 2000;
        }

        createBackground() {
            this._backgroundSprite = new Sprite();
            this._backgroundSprite.bitmap = new Bitmap(Graphics.width, Graphics.height);

            // Create beautiful gradient background
            const context = this._backgroundSprite.bitmap.context;
            const gradient = context.createLinearGradient(0, 0, 0, Graphics.height);
            gradient.addColorStop(0, '#1a1a2e'); // Dark blue top
            gradient.addColorStop(0.3, '#16213e'); // Medium blue
            gradient.addColorStop(0.7, '#0f3460'); // Deeper blue
            gradient.addColorStop(1, '#533483'); // Purple bottom

            context.fillStyle = gradient;
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            // Create twinkling starfield
            this._stars = [];
            this._shootingStars = [];
            for (let i = 0; i < 150; i++) {
                const starType = Math.random();
                let color, size, speed, minAlpha, maxAlpha;

                if (starType < 0.7) {
                    // Regular white stars (70%)
                    color = '#ffffff';
                    size = Math.random() * 2 + 0.5;
                    speed = Math.random() * 0.015 + 0.008;
                    minAlpha = Math.random() * 0.2 + 0.1;
                    maxAlpha = Math.random() * 0.7 + 0.3;
                } else if (starType < 0.85) {
                    // Blue stars (15%)
                    color = '#87CEEB';
                    size = Math.random() * 1.5 + 0.8;
                    speed = Math.random() * 0.02 + 0.01;
                    minAlpha = Math.random() * 0.3 + 0.2;
                    maxAlpha = Math.random() * 0.8 + 0.4;
                } else if (starType < 0.95) {
                    // Yellow stars (10%)
                    color = '#FFD700';
                    size = Math.random() * 1.8 + 1;
                    speed = Math.random() * 0.025 + 0.015;
                    minAlpha = Math.random() * 0.4 + 0.2;
                    maxAlpha = Math.random() * 0.9 + 0.5;
                } else {
                    // Red stars (5%) - rare
                    color = '#FF6B6B';
                    size = Math.random() * 2.5 + 1.2;
                    speed = Math.random() * 0.03 + 0.02;
                    minAlpha = Math.random() * 0.5 + 0.3;
                    maxAlpha = Math.random() * 1.0 + 0.6;
                }

                this._stars.push({
                    x: Math.random() * Graphics.width,
                    y: Math.random() * Graphics.height,
                    size: size,
                    phase: Math.random() * Math.PI * 2,
                    speed: speed,
                    minAlpha: minAlpha,
                    maxAlpha: maxAlpha,
                    color: color,
                });
            }

            // Draw initial stars
            this.drawStars();

            // Start shooting star timer
            this._shootingStarTimer = 0;

            this.addChild(this._backgroundSprite);
        }

        drawStars() {
            const context = this._backgroundSprite.bitmap.context;

            // Clear and redraw background gradient
            const gradient = context.createLinearGradient(0, 0, 0, Graphics.height);
            gradient.addColorStop(0, '#1a1a2e'); // Dark blue top
            gradient.addColorStop(0.3, '#16213e'); // Medium blue
            gradient.addColorStop(0.7, '#0f3460'); // Deeper blue
            gradient.addColorStop(1, '#533483'); // Purple bottom

            context.fillStyle = gradient;
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            // Draw twinkling stars
            for (const star of this._stars) {
                const alpha =
                    star.minAlpha +
                    (star.maxAlpha - star.minAlpha) * (Math.sin(star.phase) * 0.5 + 0.5);
                context.globalAlpha = alpha;
                context.fillStyle = star.color;
                context.fillRect(star.x, star.y, star.size, star.size);
            }

            // Draw shooting stars
            for (const star of this._shootingStars) {
                const alpha = (star.life / 100) * 0.8;
                context.globalAlpha = alpha;
                context.fillStyle = star.color;

                // Draw shooting star with trail
                for (let i = 0; i < 5; i++) {
                    const trailX = star.x - star.vx * i * 2;
                    const trailY = star.y - star.vy * i * 2;
                    const trailAlpha = alpha * (1 - i * 0.2);
                    context.globalAlpha = trailAlpha;
                    context.fillRect(trailX, trailY, star.size - i * 0.5, star.size - i * 0.5);
                }
            }
            context.globalAlpha = 1;
        }

        createShootingStar() {
            const startX = Math.random() * Graphics.width;
            const startY = Math.random() * Graphics.height * 0.3; // Start in top third
            const endX = startX + (Math.random() - 0.5) * 200; // Random direction
            const endY = startY + Math.random() * Graphics.height * 0.7 + 100; // Go down

            const dx = endX - startX;
            const dy = endY - startY;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const speed = 3 + Math.random() * 2;

            this._shootingStars.push({
                x: startX,
                y: startY,
                vx: (dx / distance) * speed,
                vy: (dy / distance) * speed,
                life: 60 + Math.random() * 40,
                size: 2 + Math.random() * 3,
                color: Math.random() < 0.7 ? '#ffffff' : '#87CEEB',
            });
        }

        createPegs() {
            gameData.pegs = [];
            gameData.totalPegs = 0;
            gameData.pegsHit = 0;
            // --- Dynamic peg grid to fill most of the screen ---
            const topMargin = 264; // Increased from 160 to skip top 2 rows (160 + 2*52 = 264)
            const bottomMargin = 160;
            const leftMargin = 40;
            const rightMargin = 40;
            const availableHeight = Graphics.height - topMargin - bottomMargin;
            const availableWidth = Graphics.width - leftMargin - rightMargin;
            const pegSpacingY = 52;
            const pegSpacingX = 60;
            const pegRows = Math.floor(availableHeight / pegSpacingY);
            const maxPegsPerRow = Math.floor(availableWidth / pegSpacingX);
            // First, build a list of all peg positions
            let pegPositions = [];
            for (let row = 0; row < pegRows; row++) {
                let pegsInRow = maxPegsPerRow;
                const rowWidth = pegsInRow * pegSpacingX;
                const startX = (Graphics.width - rowWidth) / 2 + pegSpacingX / 2;
                const y = topMargin + row * pegSpacingY;
                for (let col = 0; col < pegsInRow; col++) {
                    let x = startX + col * pegSpacingX;
                    if (row % 2 === 1) x += pegSpacingX / 2;
                    pegPositions.push({ x, y });
                }
            }
            // Shuffle positions
            for (let i = pegPositions.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [pegPositions[i], pegPositions[j]] = [pegPositions[j], pegPositions[i]];
            }
            // Assign special types and values
            let pegTypes = Array(pegPositions.length).fill('normal');
            let pegValues = Array(pegPositions.length).fill(10); // Default value

            // Always add all extra score pegs
            for (let i = 0; i < 10; i++) pegTypes[i] = 'gold';
            for (let i = 10; i < 20; i++) pegTypes[i] = 'platinum';
            for (let i = 20; i < 30; i++) pegTypes[i] = 'diamond';
            // Only add special effect pegs progressively
            const allSpecialTypes = [
                'bomb',
                'multiball',
                'chainlightning',
                'gravityflip',
                'teleport',
                'sticky',
                'shield',
                'duplicator',
            ];
            // Shuffle the array for this level
            const shuffledSpecialTypes = allSpecialTypes.slice();
            for (let i = shuffledSpecialTypes.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [shuffledSpecialTypes[i], shuffledSpecialTypes[j]] = [
                    shuffledSpecialTypes[j],
                    shuffledSpecialTypes[i],
                ];
            }
            const specialTypesThisLevel = shuffledSpecialTypes.slice(
                0,
                Math.min(2 + Math.floor(gameData.level / 2), shuffledSpecialTypes.length)
            );
            const specialPegsPerType = Math.max(3, 5 + Math.floor(gameData.level / 3));
            let pegIdx = 30;
            for (const type of specialTypesThisLevel) {
                for (let j = 0; j < specialPegsPerType; j++) {
                    if (pegIdx < pegTypes.length) pegTypes[pegIdx++] = type;
                }
            }

            // Assign values based on type
            for (let i = 0; i < pegTypes.length; i++) {
                switch (pegTypes[i]) {
                    case 'normal':
                        pegValues[i] = 10;
                        break;
                    case 'gold':
                        pegValues[i] = 50;
                        break;
                    case 'platinum':
                        pegValues[i] = 100;
                        break;
                    case 'diamond':
                        pegValues[i] = 250;
                        break;
                    case 'bomb':
                        pegValues[i] = 25;
                        break;
                    case 'multiball':
                        pegValues[i] = 30;
                        break;
                    case 'chainlightning':
                        pegValues[i] = 40;
                        break;
                    case 'gravityflip':
                        pegValues[i] = 35;
                        break;
                    case 'duplicator':
                        pegValues[i] = 45;
                        break;
                }
            }

            // Shuffle types and values together to randomize placement
            for (let i = pegTypes.length - 1; i > 0; i--) {
                const j = Math.floor(Math.random() * (i + 1));
                [pegTypes[i], pegTypes[j]] = [pegTypes[j], pegTypes[i]];
                [pegValues[i], pegValues[j]] = [pegValues[j], pegValues[i]];
            }
            // Create pegs
            for (let i = 0; i < pegPositions.length; i++) {
                const { x, y } = pegPositions[i];
                const type = pegTypes[i];
                const value = pegValues[i];
                const peg = new Peg(x, y, type, value);
                gameData.pegs.push(peg);
                gameData.totalPegs++;
                this.addChild(peg);
            }
            gameData.pegPositionsAll = pegPositions.slice();
            gameData.pegVacantPositions = [];
            this.createTargets();
        }

        createTargets() {
            gameData.targets = [];
            const targetY = Graphics.height - 100;
            const targetCount = 7;
            const targetWidth = Graphics.width / targetCount;
            const values = [50, 100, 200, 500, 200, 100, 50];
            const colors = [
                '#CD853F',
                '#DAA520',
                '#FFD700',
                '#FF4500',
                '#FFD700',
                '#DAA520',
                '#CD853F',
            ];

            for (let i = 0; i < targetCount; i++) {
                const target = new Sprite();
                target.bitmap = new Bitmap(targetWidth - 4, 60);
                const context = target.bitmap.context;

                // Create gradient background
                const gradient = context.createLinearGradient(0, 0, 0, 60);
                gradient.addColorStop(0, colors[i]);
                gradient.addColorStop(0.5, this.lightenColor(colors[i], 30));
                gradient.addColorStop(1, this.darkenColor(colors[i], 20));

                context.fillStyle = gradient;
                context.fillRect(0, 0, targetWidth - 4, 60);

                // Add border glow
                context.strokeStyle = values[i] === 500 ? '#FF6347' : '#FFA500';
                context.lineWidth = values[i] === 500 ? 3 : 2;
                context.strokeRect(1, 1, targetWidth - 6, 58);

                // Add inner border
                context.strokeStyle = 'rgba(255, 255, 255, 0.5)';
                context.lineWidth = 1;
                context.strokeRect(3, 3, targetWidth - 10, 54);

                // Draw text with shadow
                context.fillStyle = '#000000';
                context.font = 'bold 28px ' + $gameSystem.mainFontFace(); // Increased from 18px to 28px
                context.textAlign = 'center';
                context.fillText(values[i].toString(), (targetWidth - 4) / 2 + 2, 42); // Adjusted shadow position

                context.fillStyle = values[i] === 500 ? '#FFFFFF' : '#FFFF00';
                context.fillText(values[i].toString(), (targetWidth - 4) / 2, 40); // Adjusted main text position

                target.x = i * targetWidth + 2;
                target.y = targetY;
                target.value = values[i];
                target.active = true;
                target.originalAlpha = 1;
                target.glowPhase = Math.random() * Math.PI * 2;
                target.glowSpeed = Math.random() * 0.015 + 0.008; // Random speed between 0.008 and 0.023 (much slower)
                target.glowIntensity = Math.random() * 0.15 + 0.05; // Random intensity between 0.05 and 0.20
                gameData.targets.push(target);
                this.addChild(target);
            }
        }

        lightenColor(color, percent) {
            const num = parseInt(color.replace('#', ''), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = ((num >> 8) & 0x00ff) + amt;
            const B = (num & 0x0000ff) + amt;
            const clampedR = R < 255 ? (R < 1 ? 0 : R) : 255;
            const clampedG = G < 255 ? (G < 1 ? 0 : G) : 255;
            const clampedB = B < 255 ? (B < 1 ? 0 : B) : 255;
            return (
                '#' +
                (0x1000000 + clampedR * 0x10000 + clampedG * 0x100 + clampedB).toString(16).slice(1)
            );
        }

        darkenColor(color, percent) {
            const num = parseInt(color.replace('#', ''), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) - amt;
            const G = ((num >> 8) & 0x00ff) - amt;
            const B = (num & 0x0000ff) - amt;
            const clampedR = R > 255 ? 255 : R < 0 ? 0 : R;
            const clampedG = G > 255 ? 255 : G < 0 ? 0 : G;
            const clampedB = B > 255 ? 255 : B < 0 ? 0 : B;
            return (
                '#' +
                (0x1000000 + clampedR * 0x10000 + clampedG * 0x100 + clampedB).toString(16).slice(1)
            );
        }

        createUI() {
            // Create translucent score window (top left)
            this._scoreWindow = new Window_Base(new Rectangle(10, 10, 320, 90));
            this._scoreWindow.opacity = 0; // Make fully transparent
            this.addChild(this._scoreWindow);
            // Create combo window (top right)
            this._comboWindow = new Window_Base(new Rectangle(Graphics.width - 340, 10, 400, 120));
            this._comboWindow.opacity = 0;
            this.addChild(this._comboWindow);

            this.updateScore();

            // Create launcher/cannon at top center
            this.createLauncher();
        }

        createLauncher() {
            // Create beautiful cannon
            this._launcher = new Sprite();
            this._launcher.bitmap = new Bitmap(50, 80);
            const context = this._launcher.bitmap.context;

            // Cannon barrel gradient
            const barrelGradient = context.createLinearGradient(10, 0, 40, 0);
            barrelGradient.addColorStop(0, '#2F4F4F'); // Dark slate gray
            barrelGradient.addColorStop(0.3, '#708090'); // Slate gray
            barrelGradient.addColorStop(0.7, '#4682B4'); // Steel blue
            barrelGradient.addColorStop(1, '#191970'); // Midnight blue

            // Draw cannon barrel
            context.fillStyle = barrelGradient;
            context.fillRect(15, 10, 20, 50);

            // Cannon base gradient
            const baseGradient = context.createRadialGradient(25, 65, 0, 25, 65, 20);
            baseGradient.addColorStop(0, '#8B7355');
            baseGradient.addColorStop(0.5, '#654321');
            baseGradient.addColorStop(1, '#2F1B14');

            // Draw cannon base
            context.fillStyle = baseGradient;
            context.beginPath();
            context.arc(25, 65, 18, 0, Math.PI * 2);
            context.fill();

            // Add metallic highlights
            context.strokeStyle = '#C0C0C0';
            context.lineWidth = 2;
            context.strokeRect(15, 10, 20, 50);

            // Add cannon mouth highlight
            context.fillStyle = '#000000';
            context.fillRect(18, 8, 14, 4);

            this._launcher.anchor.set(0.5, 0.9);
            this._launcher.x = Graphics.width / 2;
            this._launcher.y = 224; // Moved down from 120 to compensate for removed top rows (120 + 104 = 224)
            this.addChild(this._launcher);

            // Create glowing ball indicator
            this._ballIndicator = new Sprite();
            this._ballIndicator.bitmap = new Bitmap(24, 24);
            const ballContext = this._ballIndicator.bitmap.context;

            // Glow effect
            const glowGradient = ballContext.createRadialGradient(12, 12, 0, 12, 12, 12);
            glowGradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
            glowGradient.addColorStop(0.5, 'rgba(173, 216, 230, 0.6)');
            glowGradient.addColorStop(1, 'rgba(135, 206, 250, 0)');

            ballContext.fillStyle = glowGradient;
            ballContext.beginPath();
            ballContext.arc(12, 12, 12, 0, Math.PI * 2);
            ballContext.fill();

            // Ball itself
            const ballGradient = ballContext.createRadialGradient(9, 9, 0, 12, 12, 8);
            ballGradient.addColorStop(0, '#ffffff');
            ballGradient.addColorStop(0.7, '#87ceeb');
            ballGradient.addColorStop(1, '#4682b4');

            ballContext.fillStyle = ballGradient;
            ballContext.beginPath();
            ballContext.arc(12, 12, 8, 0, Math.PI * 2);
            ballContext.fill();

            this._ballIndicator.anchor.set(0.5, 0.5);
            this._ballIndicator.x = Graphics.width / 2;
            this._ballIndicator.y = 189; // Moved down from 85 to match new cannon position (85 + 104 = 189)
            this._ballIndicator.pulsePhase = 0;
            this.addChild(this._ballIndicator);
        }

        updateScore() {
            if (this._scoreWindow) {
                this._scoreWindow.contents.clear();
                this._scoreWindow.contents.fontSize = 16;
                this._scoreWindow.contents.drawText(`Level: ${gameData.level}`, 10, 10, 280, 20);
                this._scoreWindow.contents.drawText(`Balls: ${gameData.balls}`, 10, 30, 280, 20);
                this._scoreWindow.contents.drawText(
                    `Progress: ${gameData.score}/${gameData.targetScore}`,
                    10,
                    50,
                    280,
                    20
                );
            }
            if (this._comboWindow) {
                this._comboWindow.contents.clear();
                this._comboWindow.contents.fontSize = 16;
                if (gameData.comboMultiplier > 1.0) {
                    this._comboWindow.contents.textColor = '#FFD700';
                    this._comboWindow.contents.drawText(
                        `COMBO: ${gameData.comboMultiplier.toFixed(1)}x!`,
                        10,
                        10,
                        300,
                        20,
                        'right'
                    );
                    this._comboWindow.contents.textColor = '#ffffff';
                } else {
                    this._comboWindow.contents.drawText(`COMBO: 1.0x`, 10, 10, 300, 20, 'right');
                }
                // Add an empty line for spacing
                this._comboWindow.contents.drawText('', 10, 30, 300, 20, 'right');
                // Combo meter bar
                const meterWidth = 200,
                    meterHeight = 16,
                    meterX = 110,
                    meterY = 65;
                this._comboWindow.contents.fontSize = 14;
                this._comboWindow.contents.textColor = '#00FFAA';
                this._comboWindow.contents.drawText(
                    `Combo Meter: ${gameData.comboMeter}/${gameData.comboMeterMax}`,
                    meterX,
                    meterY - 18,
                    meterWidth,
                    18,
                    'right'
                );
                this._comboWindow.contents.textColor = '#ffffff';
                this._comboWindow.contents.fillRect(
                    meterX,
                    meterY,
                    meterWidth,
                    meterHeight,
                    '#222'
                );
                const fill = Math.floor(
                    (gameData.comboMeter / gameData.comboMeterMax) * meterWidth
                );
                this._comboWindow.contents.fillRect(
                    meterX + meterWidth - fill,
                    meterY,
                    fill,
                    meterHeight,
                    '#00FFAA'
                );
                this._comboWindow.contents.strokeStyle = '#00FFAA';
                this._comboWindow.contents.lineWidth = 2;
                this._comboWindow.contents.strokeRect(meterX, meterY, meterWidth, meterHeight);
            }
        }

        update() {
            super.update();

            // Animate twinkling stars (optimized)
            if (this._stars) {
                for (let i = 0; i < this._stars.length; i++) {
                    this._stars[i].phase += this._stars[i].speed;
                }

                // Update shooting stars
                this._shootingStarTimer++;
                if (this._shootingStarTimer > 300 && Math.random() < 0.02) {
                    // Every ~5 seconds, 2% chance
                    this.createShootingStar();
                    this._shootingStarTimer = 0;
                }

                // Update existing shooting stars (optimized)
                for (let i = this._shootingStars.length - 1; i >= 0; i--) {
                    const star = this._shootingStars[i];
                    star.x += star.vx;
                    star.y += star.vy;
                    star.life--;

                    if (
                        star.life <= 0 ||
                        star.x < -50 ||
                        star.x > Graphics.width + 50 ||
                        star.y < -50 ||
                        star.y > Graphics.height + 50
                    ) {
                        this._shootingStars.splice(i, 1);
                    }
                }

                // Only redraw stars occasionally for better performance
                if (this._starRedrawTimer === undefined) this._starRedrawTimer = 0;
                this._starRedrawTimer++;
                if (this._starRedrawTimer > 5) {
                    // Redraw every 5 frames instead of every frame
                    this.drawStars();
                    this._starRedrawTimer = 0;
                }
            }

            if (Input.isTriggered('cancel')) {
                SceneManager.pop();
                return;
            }

            // Handle win/lose screen inputs
            if (gameData.gameWon || gameData.gameLost) {
                if (Input.isTriggered('ok')) {
                    this.restartGame();
                    return;
                }
                return; // Don't process other inputs when game is over
            }

            // Only allow aiming and shooting if all balls are gone
            let allBallsGone = !gameData.activeBalls || gameData.activeBalls.length === 0;
            if (allBallsGone) {
                if (Input.isPressed('left')) {
                    gameData.aimAngle = (gameData.aimAngle || 0) - 0.01;
                    gameData.aimAngle = Math.max(gameData.aimAngle, -0.5); // Limit angle
                }
                if (Input.isPressed('right')) {
                    gameData.aimAngle = (gameData.aimAngle || 0) + 0.01;
                    gameData.aimAngle = Math.min(gameData.aimAngle, 0.5); // Limit angle
                }
                if (Input.isTriggered('ok') && gameData.balls > 0) {
                    this.shootBall();
                }
            }

            // Update main ball and trail
            // if (gameData.ball) {
            //   gameData.ball.update();
            //   this.renderTrail();
            //   if (!gameData.ball.active) {
            //     this.removeChild(gameData.ball);
            //     gameData.ball = null;
            //     // Do NOT set gameData.firing = false here
            //     this._ballIndicator.visible = true; // Show ball indicator again
            //     if (this._trailSprite) {
            //       this.removeChild(this._trailSprite);
            //       this._trailSprite = null;
            //     }
            //   }
            // }
            // Update all balls (main and multiballs)
            if (gameData.activeBalls && Array.isArray(gameData.activeBalls)) {
                for (let i = 0; i < gameData.activeBalls.length; i++) {
                    const b = gameData.activeBalls[i];
                    if (b && b.active) b.update();
                }
                // Remove inactive or off-screen balls (optimized)
                const activeBalls = [];
                for (let i = 0; i < gameData.activeBalls.length; i++) {
                    const b = gameData.activeBalls[i];
                    if (b && b.active && b.y <= Graphics.height + 50) {
                        activeBalls.push(b);
                    }
                }
                gameData.activeBalls = activeBalls;
            }
            // Show/hide launcher and indicator based on allBallsGone
            allBallsGone = !gameData.activeBalls || gameData.activeBalls.length === 0;
            if (this._launcher) this._launcher.visible = true;
            if (this._ballIndicator) this._ballIndicator.visible = allBallsGone;
            if (allBallsGone) {
                // Peg respawn system: decrement respawn timers and respawn pegs (once per round when cannon gets control back)
                if (
                    gameData.pendingRespawnPegs &&
                    gameData.pendingRespawnPegs.length > 0 &&
                    !gameData._respawnProcessed
                ) {
                    gameData._respawnProcessed = true; // Prevent multiple decrements per round
                    for (let i = gameData.pendingRespawnPegs.length - 1; i >= 0; i--) {
                        gameData.pendingRespawnPegs[i].respawn--;
                        if (gameData.pendingRespawnPegs[i].respawn <= 0) {
                            const info = gameData.pendingRespawnPegs[i];
                            // Only respawn if no peg currently at this position
                            const occupied = gameData.pegs.some(
                                p => p.visible && p.x === info.x && p.y === info.y
                            );
                            if (!occupied) {
                                const newPeg = new Peg(info.x, info.y, info.type, info.value);
                                newPeg._wasRespawned = true;
                                gameData.pegs.push(newPeg);
                                gameData.totalPegs++;
                                if (this.addChild) this.addChild(newPeg);
                                // Pop-in effect
                                newPeg.alpha = 0;
                                const popIn = () => {
                                    newPeg.alpha += 0.08;
                                    if (newPeg.alpha < 1) requestAnimationFrame(popIn);
                                    else newPeg.alpha = 1;
                                };
                                requestAnimationFrame(popIn);
                            }
                            gameData.pendingRespawnPegs.splice(i, 1);
                        }
                    }
                }

                maybeRefillPegs(this);
                // Update launcher rotation for aiming
                this._launcher.rotation = gameData.aimAngle || 0;
                // Update ball indicator pulsing
                if (this._ballIndicator && this._launcher) {
                    const angle = gameData.aimAngle || 0;
                    const baseX = this._launcher.x;
                    const baseY = this._launcher.y;
                    const barrelLength = 62;
                    const indicatorX = baseX + Math.sin(angle) * barrelLength;
                    const indicatorY = baseY - Math.cos(angle) * barrelLength;
                    this._ballIndicator.x = indicatorX;
                    this._ballIndicator.y = indicatorY;
                    this._ballIndicator.alpha = 0.7 + 0.3 * Math.sin(performance.now() / 400);
                }
            }

            // Update peg glow effects (optimized)
            for (let i = 0; i < gameData.pegs.length; i++) {
                const peg = gameData.pegs[i];
                if (peg.update) peg.update();
            }

            // Update target glow effects (optimized)
            if (gameData.targets) {
                for (let i = 0; i < gameData.targets.length; i++) {
                    const target = gameData.targets[i];
                    if (target.active) {
                        target.glowPhase += target.glowSpeed;
                        const baseAlpha = 0.9;
                        const glow = baseAlpha + Math.sin(target.glowPhase) * target.glowIntensity;
                        target.alpha = glow;
                    }
                }
            }

            this.updateScore();
            this.drawTrajectoryIndicator();
            this.checkWinLoseConditions();

            // Only set firing = false when all balls are gone
            if (gameData.firing && allBallsGone) {
                gameData.firing = false;
            }
        }

        checkWinLoseConditions() {
            if (gameData.gameWon || gameData.gameLost) return;
            // Win condition: Reach target score
            if (gameData.score >= gameData.targetScore) {
                gameData.gameWon = true;
                SoundEffects.playGameWin();
                this.showWinScreen();
                return;
            }
            // Lose condition: No balls left and haven't won
            if (
                gameData.balls <= 0 &&
                (!gameData.activeBalls || gameData.activeBalls.length === 0)
            ) {
                gameData.gameLost = true;
                SoundEffects.playGameLose();
                this.showLoseScreen();
                return;
            }
        }

        renderTrail() {
            // Only render trail for the first active ball (main ball)
            if (!gameData.activeBalls || gameData.activeBalls.length === 0) return;
            const mainBall = gameData.activeBalls[0];
            if (!mainBall.trail || mainBall.trail.length === 0) return;
            if (!this._trailSprite) {
                this._trailSprite = new Sprite();
                this._trailSprite.bitmap = new Bitmap(Graphics.width, Graphics.height);
                this.addChildAt(this._trailSprite, 1);
            }
            // Only redraw if trail has changed
            if (this._lastTrailLength === mainBall.trail.length) return;
            this._lastTrailLength = mainBall.trail.length;
            this._trailSprite.bitmap.clear();
            const context = this._trailSprite.bitmap.context;
            // Optimize trail rendering with fewer gradient calculations
            for (let i = 0; i < mainBall.trail.length; i++) {
                const point = mainBall.trail[i];
                const alpha = (point.life / 10) * 0.6;
                const size = (i / mainBall.trail.length) * 6 + 2; // Reduced max size
                // Use simpler rendering for better performance
                if (point.color) {
                    context.fillStyle = point.color;
                } else {
                    context.fillStyle = `rgba(135, 206, 250, ${alpha})`;
                }
                context.beginPath();
                context.arc(point.x, point.y, size, 0, Math.PI * 2);
                context.fill();
            }
        }

        shootBall() {
            if (gameData.firing) return;
            let multishot = false;
            if (gameData.multishotReady) {
                multishot = true;
                gameData.comboMeter = 0;
                gameData.multishotReady = false;
            }
            gameData.firing = true;
            gameData._respawnProcessed = false; // Reset respawn processing flag for new round
            gameData.balls--;
            this._ballIndicator.visible = false;
            const angle = gameData.aimAngle || 0;
            const power = 9;
            const baseX = this._launcher.x;
            const baseY = this._launcher.y;
            const barrelLength = 62;
            const tipX = baseX + Math.sin(angle) * barrelLength;
            const tipY = baseY - Math.cos(angle) * barrelLength;
            const velocityX = Math.sin(angle) * power;
            const velocityY = -Math.cos(angle) * power;
            // Ball firing logic
            const fireBall = (fireAngle, index = 0) => {
                // 20% chance for each special ball, rest normal
                let ballType = 'normal';
                let popupText = '',
                    popupColor = '#00FFFF';
                let popupSymbol = '';
                const r = Math.random();
                // 60% normal, 40% special (10% each for 4 special types)
                if (r < 0.6) {
                    ballType = 'normal';
                } else if (r < 0.7) {
                    ballType = 'warp';
                    popupText = 'WARP BALL!';
                    popupColor = '#00FFFF';
                    popupSymbol = '⇧';
                } else if (r < 0.8) {
                    ballType = 'colorizer';
                    popupText = 'COLORIZER BALL!';
                    popupColor = '#FFD700';
                    popupSymbol = '✹';
                } else if (r < 0.9) {
                    ballType = 'splitter';
                    popupText = 'SPLITTER BALL!';
                    popupColor = '#FF8800';
                    popupSymbol = '⚡';
                } else {
                    ballType = 'rainbow';
                    popupText = 'RAINBOW BALL!';
                    popupColor = '#FF00FF';
                    popupSymbol = '🌈';
                }
                const vx = Math.sin(fireAngle) * power;
                const vy = -Math.cos(fireAngle) * power;
                const ball = new Ball(tipX, tipY, vx, vy, ballType);
                ball.type = ballType;
                ball.scene = this;
                gameData.activeBalls = gameData.activeBalls || [];
                gameData.activeBalls.push(ball);
                this.addChild(ball);

                // Play sound effects
                SoundEffects.playShoot();
                if (ballType !== 'normal') {
                    SoundEffects.playSpecialBall(ballType);
                }

                // Show indicator if special ball
                if (ballType !== 'normal') {
                    const popup = new Sprite();
                    popup.bitmap = new Bitmap(260, 44);
                    const ctx = popup.bitmap.context;
                    ctx.font = 'bold 22px ' + $gameSystem.mainFontFace();
                    ctx.textAlign = 'center';
                    ctx.fillStyle = popupColor;
                    ctx.strokeStyle = '#003344';
                    ctx.lineWidth = 3;
                    ctx.strokeText(`${popupSymbol} ${popupText}`, 130, 32);
                    ctx.fillText(`${popupSymbol} ${popupText}`, 130, 32);
                    popup.x = tipX - 130;
                    popup.y = tipY - 60 + index * 32;
                    this.addChild(popup);
                    setTimeout(() => {
                        this.removeChild(popup);
                    }, 700);
                }
                return ball;
            };
            if (multishot) {
                // Fire 3 balls in a spread
                const spread = 0.15;
                fireBall(angle - spread, 0);
                fireBall(angle, 1);
                fireBall(angle + spread, 2);
            } else {
                fireBall(angle, 0);
            }
        }

        createImpactEffect(x, y, color = '#FFD700', isBig = false) {
            // Limit impact effects for better performance
            if (this._lastImpactTime && Date.now() - this._lastImpactTime < 50) return; // Max 20 effects per second
            this._lastImpactTime = Date.now();

            // Exciting firework burst for special pegs
            let particleCount, maxDistance, minSize, maxSize, fadeTime;
            if (isBig) {
                particleCount = 24 + Math.floor(Math.random() * 9); // 24-32
                maxDistance = 60;
                minSize = 8;
                maxSize = 14;
                fadeTime = 88;
            } else {
                particleCount = 4;
                maxDistance = 12;
                minSize = 3;
                maxSize = 5;
                fadeTime = 14;
            }
            for (let i = 0; i < particleCount; i++) {
                // Randomize angle and speed for firework effect
                const angle = Math.random() * Math.PI * 2;
                const distance = Math.random() * maxDistance + 8;
                const speed = distance / (8 + Math.random() * 4);
                const particle = new Sprite();
                const size = minSize + Math.random() * (maxSize - minSize);
                particle.bitmap = new Bitmap(size, size);
                // Sparkle: randomize color brightness
                let sparkleColor = color;
                if (isBig) {
                    // Wide hue variation for fireworks
                    const c = color.replace('#', '');
                    if (c.length === 6) {
                        let [h, s, l] = hexToHsl(color);
                        h = (h + (Math.random() - 0.5) * 100) % 360; // ±50 deg hue shift
                        if (h < 0) h += 360;
                        sparkleColor = hslToRgbStr(h / 360, s, l);
                    }
                }
                const context = particle.bitmap.context;
                context.fillStyle = sparkleColor;
                context.beginPath();
                context.arc(size / 2, size / 2, size / 2, 0, Math.PI * 2);
                context.fill();
                particle.anchor.set(0.5, 0.5);
                particle.x = x;
                particle.y = y;
                particle.vx = Math.cos(angle) * speed;
                particle.vy = Math.sin(angle) * speed;
                particle.life = fadeTime;
                particle.maxLife = fadeTime;
                this.addChild(particle);
                // Animate particle
                const animateParticle = () => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    if (isBig) {
                        // Add a little sparkle motion
                        particle.vx *= 0.97 + Math.random() * 0.03;
                        particle.vy *= 0.97 + Math.random() * 0.03;
                        if (Math.random() < 0.15) particle.vx += (Math.random() - 0.5) * 0.6;
                        if (Math.random() < 0.15) particle.vy += (Math.random() - 0.5) * 0.6;
                    }
                    particle.vy += 0.18; // Gravity
                    particle.life--;
                    particle.alpha = particle.life / particle.maxLife;
                    if (particle.life <= 0) {
                        this.removeChild(particle);
                    } else {
                        requestAnimationFrame(animateParticle);
                    }
                };
                requestAnimationFrame(animateParticle);
            }
        }

        showWinScreen() {
            // Immediately cull all balls and stop all ball updates
            this.cullAllBalls();

            // Stop any ongoing ball-related timers or effects
            if (gameData.gravityFlipTimeout) {
                clearTimeout(gameData.gravityFlipTimeout);
                gameData.gravityFlipTimeout = null;
            }

            // Clear any remaining impact effects
            this.clearAllImpactEffects();
            // Create victory overlay
            this._victoryOverlay = new Sprite();
            this._victoryOverlay.bitmap = new Bitmap(Graphics.width, Graphics.height);
            const context = this._victoryOverlay.bitmap.context;

            // Semi-transparent background
            context.fillStyle = 'rgba(0, 100, 0, 0.8)';
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            // Victory text
            context.fillStyle = '#FFD700';
            context.strokeStyle = '#000000';
            context.lineWidth = 4;
            context.font = 'bold 48px ' + $gameSystem.mainFontFace();
            context.textAlign = 'center';

            const victoryText = 'VICTORY!';
            const scoreText = `Final Score: ${gameData.score}`;
            const continueText = 'Press ENTER to continue or ESC to exit';

            context.strokeText(victoryText, Graphics.width / 2, Graphics.height / 2 - 50);
            context.fillText(victoryText, Graphics.width / 2, Graphics.height / 2 - 50);

            context.font = 'bold 24px ' + $gameSystem.mainFontFace();
            context.strokeText(scoreText, Graphics.width / 2, Graphics.height / 2 + 20);
            context.fillText(scoreText, Graphics.width / 2, Graphics.height / 2 + 20);

            context.font = '18px ' + $gameSystem.mainFontFace();
            context.fillStyle = '#FFFFFF';
            context.fillText(continueText, Graphics.width / 2, Graphics.height / 2 + 80);

            this.addChild(this._victoryOverlay);
        }

        showLoseScreen() {
            // Immediately cull all balls and stop all ball updates
            this.cullAllBalls();

            // Stop any ongoing ball-related timers or effects
            if (gameData.gravityFlipTimeout) {
                clearTimeout(gameData.gravityFlipTimeout);
                gameData.gravityFlipTimeout = null;
            }

            // Clear any remaining impact effects
            this.clearAllImpactEffects();
            // Create game over overlay
            this._gameOverOverlay = new Sprite();
            this._gameOverOverlay.bitmap = new Bitmap(Graphics.width, Graphics.height);
            const context = this._gameOverOverlay.bitmap.context;

            // Semi-transparent background
            context.fillStyle = 'rgba(100, 0, 0, 0.8)';
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            // Game Over text
            context.fillStyle = '#FF6347';
            context.strokeStyle = '#000000';
            context.lineWidth = 4;
            context.font = 'bold 48px ' + $gameSystem.mainFontFace();
            context.textAlign = 'center';

            const gameOverText = 'GAME OVER';
            const scoreText = `Final Score: ${gameData.score}`;
            const pegText = `Pegs Hit: ${gameData.pegsHit}/${gameData.totalPegs} (${Math.floor((gameData.pegsHit / gameData.totalPegs) * 100)}%)`;
            const continueText = 'Press ENTER to try again or ESC to exit';

            context.strokeText(gameOverText, Graphics.width / 2, Graphics.height / 2 - 50);
            context.fillText(gameOverText, Graphics.width / 2, Graphics.height / 2 - 50);

            context.font = 'bold 24px ' + $gameSystem.mainFontFace();
            context.strokeText(scoreText, Graphics.width / 2, Graphics.height / 2 + 10);
            context.fillText(scoreText, Graphics.width / 2, Graphics.height / 2 + 10);

            context.font = '20px ' + $gameSystem.mainFontFace();
            context.fillStyle = '#FFFF00';
            context.fillText(pegText, Graphics.width / 2, Graphics.height / 2 + 40);

            context.font = '18px ' + $gameSystem.mainFontFace();
            context.fillStyle = '#FFFFFF';
            context.fillText(continueText, Graphics.width / 2, Graphics.height / 2 + 80);

            this.addChild(this._gameOverOverlay);
        }

        restartGame() {
            // Always reset gravity to normal at the start of a round
            gameData.gravityFlipped = false;
            if (gameData.gravityFlipTimeout) {
                clearTimeout(gameData.gravityFlipTimeout);
                gameData.gravityFlipTimeout = null;
            }
            // If the player just won, increment the level
            if (gameData.gameWon) {
                gameData.level += 1;
            }
            // Clean up existing game elements
            if (this._victoryOverlay) {
                this.removeChild(this._victoryOverlay);
                this._victoryOverlay = null;
            }
            if (this._gameOverOverlay) {
                this.removeChild(this._gameOverOverlay);
                this._gameOverOverlay = null;
            }
            // Use the thorough culling method
            this.cullAllBalls();

            // Remove all pegs and targets
            gameData.pegs.forEach(peg => this.removeChild(peg));
            if (gameData.targets) {
                gameData.targets.forEach(target => this.removeChild(target));
            }

            // Reset game state
            gameData.score = 0;
            gameData.balls = 10;
            gameData.firing = false;
            gameData.aimAngle = 0;
            gameData.totalPegs = 0;
            gameData.pegsHit = 0;
            gameData.gameWon = false;
            gameData.gameLost = false;
            gameData.pegs = [];
            gameData.targets = [];
            gameData.comboMultiplier = 1;
            gameData.lastHitTime = 0;
            gameData.pegVacantPositions = [];

            // Recreate game elements
            this.createPegs();
            // Set target score for this level
            gameData.targetScore = 10000 + (gameData.level - 1) * 2000;
            this._ballIndicator.visible = true;
        }

        drawTrajectoryIndicator() {
            let allBallsGone = !gameData.activeBalls || gameData.activeBalls.length === 0;
            if (!this._launcher || !this._ballIndicator || gameData.firing || !allBallsGone) return;
            if (this._trajectorySprite) {
                this.removeChild(this._trajectorySprite);
                this._trajectorySprite = null;
            }
            this._trajectorySprite = new Sprite();
            this._trajectorySprite.bitmap = new Bitmap(Graphics.width, Graphics.height);
            this.addChildAt(this._trajectorySprite, 2);
            const ctx = this._trajectorySprite.bitmap.context;
            ctx.save();
            ctx.globalAlpha = 0.5;
            ctx.strokeStyle = '#00BFFF';
            ctx.setLineDash([8, 8]);
            ctx.lineWidth = 3;
            const angle = gameData.aimAngle || 0;
            const baseX = this._launcher.x;
            const baseY = this._launcher.y;
            const barrelLength = 62 - 15; // Pull inwards by 15px
            let x = baseX + Math.sin(angle) * barrelLength;
            let y = baseY - Math.cos(angle) * barrelLength;
            let vx = Math.sin(angle) * 9; // Match cannon power
            let vy = -Math.cos(angle) * 9; // Match cannon power
            ctx.beginPath();
            ctx.moveTo(x, y);
            let steps = 0;
            let bounces = 0;
            while (steps < 180 && bounces < 4 && y < Graphics.height) {
                vy += gameData.gravityFlipped ? -0.09 : 0.18;
                x += vx;
                y += vy;
                if (x < 8 || x > Graphics.width - 8) {
                    vx *= -1;
                    x = Math.max(8, Math.min(Graphics.width - 8, x));
                    bounces++;
                }
                ctx.lineTo(x, y);
                steps++;
            }
            ctx.stroke();
            ctx.setLineDash([]);
            ctx.restore();
        }

        drawLightningChain(chainPath) {
            if (!chainPath || chainPath.length < 2) return;
            if (!this._lightningSprite) {
                this._lightningSprite = new Sprite();
                this._lightningSprite.bitmap = new Bitmap(Graphics.width, Graphics.height);
                this.addChildAt(this._lightningSprite, 3);
            }
            const ctx = this._lightningSprite.bitmap.context;
            this._lightningSprite.bitmap.clear();
            ctx.save();
            ctx.strokeStyle = '#00FFFF';
            ctx.shadowColor = '#00FFFF';
            ctx.shadowBlur = 16;
            ctx.lineWidth = 4;
            ctx.beginPath();
            ctx.moveTo(chainPath[0].x, chainPath[0].y);
            for (let i = 1; i < chainPath.length; i++) {
                // Draw jagged segments between each pair
                const x1 = chainPath[i - 1].x,
                    y1 = chainPath[i - 1].y;
                const x2 = chainPath[i].x,
                    y2 = chainPath[i].y;
                const segments = 8;
                for (let s = 1; s <= segments; s++) {
                    const t = s / segments;
                    const nx = x1 + (x2 - x1) * t + (Math.random() - 0.5) * 18;
                    const ny = y1 + (y2 - y1) * t + (Math.random() - 0.5) * 18;
                    ctx.lineTo(nx, ny);
                }
            }
            ctx.stroke();
            ctx.restore();
            // Remove the lightning after 300ms
            setTimeout(() => {
                if (this._lightningSprite) this._lightningSprite.bitmap.clear();
            }, 300);
        }

        showGravityFlipEffect() {
            if (!this._gravityFlipSprite) {
                this._gravityFlipSprite = new Sprite();
                this._gravityFlipSprite.bitmap = new Bitmap(Graphics.width, Graphics.height);
                this.addChildAt(this._gravityFlipSprite, 4);
            }
            const ctx = this._gravityFlipSprite.bitmap.context;
            this._gravityFlipSprite.bitmap.clear();
            ctx.save();
            ctx.globalAlpha = 0.25;
            ctx.fillStyle = 'magenta';
            ctx.fillRect(0, 0, Graphics.width, Graphics.height);
            ctx.restore();
            setTimeout(() => {
                if (this._gravityFlipSprite) this._gravityFlipSprite.bitmap.clear();
            }, 300);
        }

        cullAllBalls() {
            // Immediately stop and remove all active balls
            if (gameData.activeBalls && Array.isArray(gameData.activeBalls)) {
                for (let i = 0; i < gameData.activeBalls.length; i++) {
                    const ball = gameData.activeBalls[i];
                    if (ball) {
                        ball.visible = false;
                        ball.active = false;
                        ball.trail = [];
                        if (this.children.includes(ball)) {
                            this.removeChild(ball);
                        }
                    }
                }
                gameData.activeBalls = [];
            }
            // Extra: forcibly remove any stray Ball instances from the scene
            for (let i = this.children.length - 1; i >= 0; i--) {
                const child = this.children[i];
                if (child && child.constructor && child.constructor.name === 'Ball') {
                    child.visible = false;
                    child.active = false;
                    child.trail = [];
                    this.removeChild(child);
                }
            }
            // Clear trail sprite
            if (this._trailSprite) {
                this.removeChild(this._trailSprite);
                this._trailSprite = null;
            }
            // Reset firing state
            gameData.firing = false;
            // TODO: If any ball-related timeouts exist, clear them here
        }

        clearAllImpactEffects() {
            // Remove all impact effect sprites from the scene
            for (let i = this.children.length - 1; i >= 0; i--) {
                const child = this.children[i];
                if (
                    child &&
                    child.bitmap &&
                    child.bitmap.width <= 10 &&
                    child.bitmap.height <= 10
                ) {
                    // Likely an impact effect particle
                    this.removeChild(child);
                }
            }
        }

        showScorePopup(x, y, score, multiplier, peg) {
            const popup = new Sprite();
            popup.bitmap = new Bitmap(120, 60);
            const context = popup.bitmap.context;
            // Determine symbol and color by peg type
            let symbol = '',
                color = '#FFFFFF';
            switch (peg?.type) {
                case 'gold':
                    symbol = '★';
                    color = '#FFD700';
                    break;
                case 'platinum':
                    symbol = '☆';
                    color = '#E5E4E2';
                    break;
                case 'diamond':
                    symbol = '◆';
                    color = '#00EFFF';
                    break;
                case 'bomb':
                    symbol = '💣';
                    color = '#FF2222';
                    break;
                case 'multiball':
                    symbol = '≡';
                    color = '#1976D2';
                    break;
                case 'chainlightning':
                    symbol = '⚡';
                    color = '#00FFFF';
                    break;
                case 'gravityflip':
                    symbol = '⇅';
                    color = '#C71585';
                    break;
                default:
                    symbol = '';
                    color = '#FFFFFF';
                    break;
            }
            // Score line
            context.fillStyle = color;
            context.font = 'bold 20px ' + $gameSystem.mainFontFace();
            context.textAlign = 'center';
            const scoreText = symbol
                ? `${symbol} +${peg?.value || score}`
                : `+${peg?.value || score}`;
            context.fillText(scoreText, 60, 25);
            // Combo line
            if (multiplier > 1.0) {
                context.font = 'bold 14px ' + $gameSystem.mainFontFace();
                context.fillStyle = '#00FF00';
                context.fillText(`x${multiplier.toFixed(1)} Combo!`, 60, 45);
            }
            popup.x = x - 60;
            popup.y = y - 30;
            this.addChild(popup);
            // Animate popup
            let startY = popup.y;
            let startTime = Date.now();
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / 1000, 1);
                popup.y = startY - progress * 40;
                popup.alpha = 1 - progress;
                if (progress < 1) {
                    requestAnimationFrame(animate);
                } else {
                    this.removeChild(popup);
                }
            };
            animate();
        }
    }

    // Plugin commands
    PluginManager.registerCommand(pluginName, 'StartPeglin', () => {
        // Reset game state
        gameData.score = 0;
        gameData.balls = 10;
        gameData.activeBalls = [];
        gameData.firing = false;
        gameData.aimAngle = 0;
        gameData.pegs = [];
        gameData.targets = [];
        gameData.totalPegs = 0;
        gameData.pegsHit = 0;
        gameData.gameWon = false;
        gameData.gameLost = false;

        SceneManager.push(Scene_Peglin);
    });

    // Global test function
    window.startPeglin = () => {
        gameData.score = 0;
        gameData.balls = 10;
        gameData.activeBalls = [];
        gameData.firing = false;
        gameData.aimAngle = 0;
        gameData.pegs = [];
        gameData.targets = [];
        gameData.totalPegs = 0;
        gameData.pegsHit = 0;
        gameData.gameWon = false;
        gameData.gameLost = false;
        SceneManager.push(Scene_Peglin);
    };

    window.Scene_Peglin = Scene_Peglin;

    // Helper: Convert hex to HSL
    function hexToHsl(hex) {
        hex = hex.replace('#', '');
        let r = parseInt(hex.substring(0, 2), 16) / 255;
        let g = parseInt(hex.substring(2, 4), 16) / 255;
        let b = parseInt(hex.substring(4, 6), 16) / 255;
        let max = Math.max(r, g, b),
            min = Math.min(r, g, b);
        let h,
            s,
            l = (max + min) / 2;
        if (max == min) {
            h = s = 0;
        } else {
            let d = max - min;
            s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
            switch (max) {
                case r:
                    h = (g - b) / d + (g < b ? 6 : 0);
                    break;
                case g:
                    h = (b - r) / d + 2;
                    break;
                case b:
                    h = (r - g) / d + 4;
                    break;
            }
            h /= 6;
        }
        return [h * 360, s, l];
    }
    // Helper: Convert HSL to RGB string
    function hslToRgbStr(h, s, l) {
        let r, g, b;
        if (s == 0) {
            r = g = b = l;
        } else {
            function hue2rgb(p, q, t) {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1 / 6) return p + (q - p) * 6 * t;
                if (t < 1 / 2) return q;
                if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
                return p;
            }
            let q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            let p = 2 * l - q;
            r = hue2rgb(p, q, h + 1 / 3);
            g = hue2rgb(p, q, h);
            b = hue2rgb(p, q, h - 1 / 3);
        }
        return `rgb(${Math.round(r * 255)},${Math.round(g * 255)},${Math.round(b * 255)})`;
    }

    // Refill pegs if needed (called between rounds)
    function maybeRefillPegs(scene) {
        const minPegs = 8;
        const refillCount = 8;
        const visiblePegs = gameData.pegs.filter(p => p.visible).length;
        if (
            visiblePegs < minPegs &&
            gameData.pegVacantPositions &&
            gameData.pegVacantPositions.length > 0
        ) {
            let pegsToRefill = Math.min(
                refillCount,
                gameData.pegVacantPositions.length,
                minPegs - visiblePegs
            );
            for (let i = 0; i < pegsToRefill; i++) {
                const idx = Math.floor(Math.random() * gameData.pegVacantPositions.length);
                const spot = gameData.pegVacantPositions.splice(idx, 1)[0];
                if (spot) {
                    const newPeg = new Peg(spot.x, spot.y, 'normal', 10);
                    newPeg._wasRespawned = true;
                    gameData.pegs.push(newPeg);
                    gameData.totalPegs++;
                    if (scene && scene.addChild) scene.addChild(newPeg);
                    // Pop-in + white flash effect
                    newPeg.alpha = 0;
                    let flash = 1;
                    const popIn = () => {
                        newPeg.alpha += 0.08;
                        if (flash > 0) {
                            newPeg.bitmap.context.globalAlpha = 1;
                            newPeg.bitmap.context.fillStyle = 'rgba(255,255,255,' + flash + ')';
                            newPeg.bitmap.context.beginPath();
                            newPeg.bitmap.context.arc(
                                newPeg.bitmap.width / 2,
                                newPeg.bitmap.height / 2,
                                newPeg.radius + 4,
                                0,
                                Math.PI * 2
                            );
                            newPeg.bitmap.context.fill();
                            flash -= 0.18;
                        }
                        if (newPeg.alpha < 1) requestAnimationFrame(popIn);
                        else newPeg.alpha = 1;
                    };
                    requestAnimationFrame(popIn);
                }
            }
        }
    }
})();
