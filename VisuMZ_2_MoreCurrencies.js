//=============================================================================
// VisuStella MZ - More Shop Currencies
// VisuMZ_2_MoreCurrencies.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_2_MoreCurrencies = true;

var VisuMZ = VisuMZ || {};
VisuMZ.MoreCurrencies = VisuMZ.MoreCurrencies || {};
VisuMZ.MoreCurrencies.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 2] [Version 1.00] [MoreCurrencies]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/More_Currencies_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_ItemsEquipsCore
 * @orderAfter VisuMZ_1_ItemsEquipsCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin expands the shop scene's functionality by allowing the game dev
 * to create items that can be sold for items and/or variables instead of gold.
 * Or you know what? Throw gold in there, too. Any combination of the them! By
 * doing so, gold no longer becomes the default currency for every shop, as
 * some special shops may require a different type of trade.
 *
 * Features include all (but not limited to) the following:
 *
 * * Items can be bought using items, weapons, armors, variables, gold, or any
 *   of the combinations listed.
 * * Sell items this way, too!
 * * Sold item listing window will now show the amount the player can get back
 *   per unit sold.
 * * Shop scene's calculation window is now updated to show the transaction
 *   details from how much the player owns to how much will be spent to what
 *   kind of result there will be.
 * * Proxy system support allows for shops to sell the same items but using
 *   different types of currencies.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_0_CoreEngine
 * * VisuMZ_1_ItemsEquipsCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 2 ------
 *
 * This plugin is a Tier 2 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Window_ShopNumber
 *
 * The visual contents of this window have been completely overhauled to show
 * the details of what transactions are happening. This includes how much or
 * many of a resource the player owns, how much will be involved in the actual
 * transaction, and the net outcome after the transaction has taken place.
 *
 * Naturally, this means that things will have to shift around in order for the
 * space to make any sense.
 *
 * ---
 *
 * Proxy Items
 *
 * Proxy Items are temporary substitutes for another. When they are acquired
 * through shopping, they will turn into the item, weapon, or armor they are a
 * proxy for. Only the icon, name, help description, and status details will
 * match up. Everything else will remain separate such as the notetag data and
 * the trading list. This allows you to effectively have multiple ways to
 * trade the same item using different item combinations.
 *
 * For more details, look inside of the Notetags section for Proxy items.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Cost-Related Notetags ===
 *
 * ---
 *
 * <Item id Buy Cost: x>
 * <Item name Buy Cost: x>
 *
 * <Item id Sell Cost: x>
 * <Item name Sell Cost: x>
 *
 * <Item id Cost: x>
 * <Item name Cost: x>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - The "buy" variant determines the item and quantity needed to purchase this
 *   object in the shop.
 * - The "sell" variant determines the item and quantity acquired when selling
 *   this object in the shop.
 * - The neither variant will determine both buy/sell transactions related to
 *   the item and quantities when selling.
 *   - Selling the object will yield a lower quantity determined by the sell
 *     rate found in Plugin Parameters > General > Automatic Sell Rate.
 *   - This variant cannot be used with the Buy/Sell notetag variants. If
 *     either the buy or sell notetag variants are detected, this doesn't work.
 * - Replace 'id' with a number representing the ID of the item to be taken
 *   (when bought) or acquired (when sold).
 * - Replace 'name' with the name of the item to be taken (when bought) or
 *   acquired (when sold).
 * - Replace 'x' with the quantity of the item that will be taken (when bought)
 *   or acquired (when sold).
 * - Insert multiple copies of these notetags to add more item costs.
 *
 * ---
 *
 * <Weapon id Buy Cost: x>
 * <Weapon name Buy Cost: x>
 *
 * <Weapon id Sell Cost: x>
 * <Weapon name Sell Cost: x>
 *
 * <Weapon id Cost: x>
 * <Weapon name Cost: x>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - The "buy" variant determines the weapon and quantity needed to purchase
 *   this object in the shop.
 * - The "sell" variant determines the weapon and quantity acquired when
 *   selling this object in the shop.
 * - The neither variant will determine both buy/sell transactions related to
 *   the weapon and quantities when selling.
 *   - Selling the object will yield a lower quantity determined by the sell
 *     rate found in Plugin Parameters > General > Automatic Sell Rate.
 *   - This variant cannot be used with the Buy/Sell notetag variants. If
 *     either the buy or sell notetag variants are detected, this doesn't work.
 * - Replace 'id' with a number representing the ID of the weapon to be taken
 *   (when bought) or acquired (when sold).
 * - Replace 'name' with the name of the weapon to be taken (when bought) or
 *   acquired (when sold).
 * - Replace 'x' with the quantity of the weapon that will be taken (when
 *   bought) or acquired (when sold).
 * - Insert multiple copies of these notetags to add more weapon costs.
 *
 * ---
 *
 * <Armor id Buy Cost: x>
 * <Armor name Buy Cost: x>
 *
 * <Armor id Sell Cost: x>
 * <Armor name Sell Cost: x>
 *
 * <Armor id Cost: x>
 * <Armor name Cost: x>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - The "buy" variant determines the armor and quantity needed to purchase
 *   this object in the shop.
 * - The "sell" variant determines the armor and quantity acquired when
 *   selling this object in the shop.
 * - The neither variant will determine both buy/sell transactions related to
 *   the armor and quantities when selling.
 *   - Selling the object will yield a lower quantity determined by the sell
 *     rate found in Plugin Parameters > General > Automatic Sell Rate.
 *   - This variant cannot be used with the Buy/Sell notetag variants. If
 *     either the buy or sell notetag variants are detected, this doesn't work.
 * - Replace 'id' with a number representing the ID of the armor to be taken
 *   (when bought) or acquired (when sold).
 * - Replace 'name' with the name of the armor to be taken (when bought) or
 *   acquired (when sold).
 * - Replace 'x' with the quantity of the armor that will be taken (when
 *   bought) or acquired (when sold).
 * - Insert multiple copies of these notetags to add more armor costs.
 *
 * ---
 *
 * === Proxy Notetags ===
 *
 * ---
 *
 * <Proxy: id>
 * <Proxy: name>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - REQUIRES the most up to date VisuMZ Items and Equips Core!
 * - Turns this item, weapon, or armor into a proxy for another item, allowing
 *   you to create trades with different components using the above notetag
 *   contents and yield the same item.
 * - The proxy item itself will take on the name, icon, and description of the
 *   original item it is supposed to represent.
 * - No other properties are carried over from the original.
 * - When viewed through the Window_ShopStatus window, the contents will
 *   reference the original item and not the proxy item.
 * - Proxy items themselves cannot be acquired. This includes event commands,
 *   item drops, or equips.
 * - When bought, the item yielded won't be the proxy item but the item it is
 *   a proxy for.
 * - Replace 'id' with a number representing the item, weapon, or armor ID of
 *   the same item type. If the proxy is an item, this will reference an item.
 *   If the proxy is a weapon, this will reference a weapon. Same for armors.
 * - Replace 'name' with text representing the item, weapon, or armor's name.
 *   The referenced item needs to be the same item type as the proxy. Item for
 *   item, weapon for weapon, armor for armor.
 * - Insert multiple copies of these notetags to add more variables costs.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * Default settings for More Currencies.
 *
 * ---
 *
 * General
 *
 *   Automatic Sell Rate:
 *   - When using the plain "Cost" notetags, use this sell rate.
 *
 * ---
 *
 * Vocabulary
 *
 *   Owned:
 *   - Text used for how much of an item is owned.
 *
 *   Shift:
 *   - Text used for the change in value.
 *
 *   Net:
 *   - Text used for the net result.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Listing Settings
 * ============================================================================
 *
 * Settings for the currency listings.
 *
 * ---
 *
 * Listing
 *
 *   Listing Order:
 *   - Determines the order the trade components are listed.
 *
 *   Show Sell Window:
 *   - Show listed items in the sell window?
 *
 *   List Font Size:
 *   - Font size used for listed items.
 *
 *   List Padding:
 *   - Pixel padding between listed items.
 *
 * ---
 *
 * Text Format
 *
 *   Item Format:
 *   Weapon Format:
 *   Armor Format:
 *   Variable Format:
 *   - Text format used for listed items.
 *   - %1 - Cost, %2 - Owned, %3 - Icon, %4 - Name
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: March 7, 2022
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MoreCurrencies
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc Default settings for More Currencies.
 * @default {"General":"","AutoSellRate:num":"0.50","Vocab":"","NumWindowOwned:str":"Owned","NumWindowShift:str":"Change","NumWindowNet:str":"Net"}
 *
 * @param Listing:struct
 * @text Listing Settings
 * @type struct<Listing>
 * @desc Settings for the currency listings.
 * @default {"Listing":"","ListOrder:arraystr":"[\"item\",\"weapon\",\"armor\",\"variable\",\"gold\"]","ShowSell:eval":"true","BuyFontSize:num":"22","BuyPadding:num":"16","Format":"","ItemBuyFmt:str":"%1%3","WeaponBuyFmt:str":"%1%3","ArmorsBuyFmt:str":"%1%3","VariableBuyFmt:str":"%1%4"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param General
 *
 * @param AutoSellRate:num
 * @text Automatic Sell Rate
 * @parent General
 * @desc When using the plain "Cost" notetags, use this sell rate.
 * @default 0.50
 *
 * @param Vocab
 * @text Vocabulary
 *
 * @param NumWindowOwned:str
 * @text Owned
 * @parent Vocab
 * @desc Text used for how much of an item is owned.
 * @default Owned
 *
 * @param NumWindowShift:str
 * @text Shift
 * @parent Vocab
 * @desc Text used for the change in value.
 * @default Change
 *
 * @param NumWindowNet:str
 * @text Net
 * @parent Vocab
 * @desc Text used for the net result.
 * @default Net
 *
 */
/* ----------------------------------------------------------------------------
 * Listing Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Listing:
 *
 * @param Listing
 *
 * @param ListOrder:arraystr
 * @text Listing Order
 * @parent Listing
 * @type select[]
 * @option item
 * @option weapon
 * @option armor
 * @option variable
 * @option gold
 * @desc Determines the order the trade components are listed.
 * @default ["item","weapon","armor","variable","gold"]
 *
 * @param ShowSell:eval
 * @text Show Sell Window
 * @parent Listing
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show listed items in the sell window?
 * @default true
 *
 * @param BuyFontSize:num
 * @text List Font Size
 * @parent Listing
 * @type number
 * @min 1
 * @desc Font size used for listed items.
 * @default 22
 *
 * @param BuyPadding:num
 * @text List Padding
 * @parent Listing
 * @type number
 * @min 1
 * @desc Pixel padding between listed items.
 * @default 16
 *
 * @param Format
 * @text Text Format
 *
 * @param ItemBuyFmt:str
 * @text Item Format
 * @parent Format
 * @desc Text format used for listed items.
 * %1 - Cost, %2 - Owned, %3 - Icon, %4 - Name
 * @default %1%3
 *
 * @param WeaponBuyFmt:str
 * @text Weapon Format
 * @parent Format
 * @desc Text format used for listed weapons.
 * %1 - Cost, %2 - Owned, %3 - Icon, %4 - Name
 * @default %1%3
 *
 * @param ArmorsBuyFmt:str
 * @text Armors Format
 * @parent Format
 * @desc Text format used for listed armors.
 * %1 - Cost, %2 - Owned, %3 - Icon, %4 - Name
 * @default %1%3
 *
 * @param VariableBuyFmt:str
 * @text Variable Format
 * @parent Format
 * @desc Text format used for listed variables.
 * %1 - Cost, %2 - Owned, %3 - Icon, %4 - Name
 * @default %1%4
 *
 */
//=============================================================================

function _0xa327(_0x334f75, _0x15959a) {
    const _0x59f026 = _0x59f0();
    return (
        (_0xa327 = function (_0xa32762, _0x342de7) {
            _0xa32762 = _0xa32762 - 0x15b;
            let _0x15cca9 = _0x59f026[_0xa32762];
            return _0x15cca9;
        }),
        _0xa327(_0x334f75, _0x15959a)
    );
}
const _0x22ffed = _0xa327;
(function (_0x4b841c, _0x3de902) {
    const _0x46f3c8 = _0xa327,
        _0x383180 = _0x4b841c();
    while (!![]) {
        try {
            const _0x163b4d =
                (-parseInt(_0x46f3c8(0x258)) / 0x1) * (parseInt(_0x46f3c8(0x170)) / 0x2) +
                (parseInt(_0x46f3c8(0x238)) / 0x3) * (parseInt(_0x46f3c8(0x18d)) / 0x4) +
                parseInt(_0x46f3c8(0x21d)) / 0x5 +
                (-parseInt(_0x46f3c8(0x191)) / 0x6) * (-parseInt(_0x46f3c8(0x225)) / 0x7) +
                (-parseInt(_0x46f3c8(0x20d)) / 0x8) * (parseInt(_0x46f3c8(0x1bd)) / 0x9) +
                -parseInt(_0x46f3c8(0x192)) / 0xa +
                -parseInt(_0x46f3c8(0x1a7)) / 0xb;
            if (_0x163b4d === _0x3de902) break;
            else _0x383180['push'](_0x383180['shift']());
        } catch (_0x331537) {
            _0x383180['push'](_0x383180['shift']());
        }
    }
})(_0x59f0, 0x7e94d);
var label = _0x22ffed(0x1bb),
    tier = tier || 0x0,
    dependencies = ['VisuMZ_0_CoreEngine', _0x22ffed(0x199)],
    pluginData = $plugins[_0x22ffed(0x16a)](function (_0x467d0e) {
        const _0x46391d = _0x22ffed;
        return _0x467d0e['status'] && _0x467d0e['description'][_0x46391d(0x179)]('[' + label + ']');
    })[0x0];
((VisuMZ[label][_0x22ffed(0x175)] = VisuMZ[label][_0x22ffed(0x175)] || {}),
    (VisuMZ[_0x22ffed(0x162)] = function (_0x4d9346, _0x1a119a) {
        const _0x5ca023 = _0x22ffed;
        for (const _0x221e2a in _0x1a119a) {
            if (_0x221e2a['match'](/(.*):(.*)/i)) {
                if (_0x5ca023(0x19c) !== _0x5ca023(0x19c)) {
                    const _0x34c11d = _0x5ca023(0x234),
                        _0x20cd76 =
                            _0xb0f70f['CoreEngine']['Settings'][_0x5ca023(0x1cf)][_0x5ca023(0x1d8)],
                        _0xdc713f = _0x25b820[_0x5ca023(0x1be)];
                    return _0x34c11d['format'](
                        _0xf40998,
                        _0x20cd76 > 0x0 ? _0x5ca023(0x23e)[_0x5ca023(0x1b7)](_0x20cd76) : '',
                        _0x20cd76 > 0x0 ? '' : _0xdc713f
                    );
                } else {
                    const _0xa27dd7 = String(RegExp['$1']),
                        _0xd627ca = String(RegExp['$2'])['toUpperCase']()[_0x5ca023(0x1d4)]();
                    let _0x2cf1d4, _0x1547b8, _0x132a55;
                    switch (_0xd627ca) {
                        case 'NUM':
                            _0x2cf1d4 =
                                _0x1a119a[_0x221e2a] !== '' ? Number(_0x1a119a[_0x221e2a]) : 0x0;
                            break;
                        case _0x5ca023(0x1fe):
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](_0x1751e1 =>
                                    Number(_0x1751e1)
                                )));
                            break;
                        case _0x5ca023(0x1e6):
                            _0x2cf1d4 =
                                _0x1a119a[_0x221e2a] !== '' ? eval(_0x1a119a[_0x221e2a]) : null;
                            break;
                        case _0x5ca023(0x1e1):
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](_0x4d6f23 =>
                                    eval(_0x4d6f23)
                                )));
                            break;
                        case _0x5ca023(0x24b):
                            _0x2cf1d4 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : '';
                            break;
                        case 'ARRAYJSON':
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](_0xe6184a =>
                                    JSON[_0x5ca023(0x189)](_0xe6184a)
                                )));
                            break;
                        case 'FUNC':
                            _0x2cf1d4 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? new Function(JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a]))
                                    : new Function(_0x5ca023(0x1c3));
                            break;
                        case _0x5ca023(0x1c5):
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](
                                    _0x3ae7eb => new Function(JSON[_0x5ca023(0x189)](_0x3ae7eb))
                                )));
                            break;
                        case _0x5ca023(0x18a):
                            _0x2cf1d4 =
                                _0x1a119a[_0x221e2a] !== '' ? String(_0x1a119a[_0x221e2a]) : '';
                            break;
                        case _0x5ca023(0x1e5):
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](_0x32196d =>
                                    String(_0x32196d)
                                )));
                            break;
                        case _0x5ca023(0x249):
                            ((_0x132a55 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : {}),
                                (_0x2cf1d4 = VisuMZ[_0x5ca023(0x162)]({}, _0x132a55)));
                            break;
                        case _0x5ca023(0x1a0):
                            ((_0x1547b8 =
                                _0x1a119a[_0x221e2a] !== ''
                                    ? JSON[_0x5ca023(0x189)](_0x1a119a[_0x221e2a])
                                    : []),
                                (_0x2cf1d4 = _0x1547b8[_0x5ca023(0x1d5)](_0x144de5 =>
                                    VisuMZ[_0x5ca023(0x162)]({}, JSON[_0x5ca023(0x189)](_0x144de5))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x4d9346[_0xa27dd7] = _0x2cf1d4;
                }
            }
        }
        return _0x4d9346;
    }),
    (_0x32b2a5 => {
        const _0x28f50f = _0x22ffed,
            _0x299550 = _0x32b2a5[_0x28f50f(0x244)];
        for (const _0x20588b of dependencies) {
            if (!Imported[_0x20588b]) {
                (alert(_0x28f50f(0x1f6)[_0x28f50f(0x1b7)](_0x299550, _0x20588b)),
                    SceneManager[_0x28f50f(0x257)]());
                break;
            }
        }
        const _0x49b0e8 = _0x32b2a5[_0x28f50f(0x224)];
        if (_0x49b0e8[_0x28f50f(0x1c4)](/\[Version[ ](.*?)\]/i)) {
            const _0x4c3a0d = Number(RegExp['$1']);
            _0x4c3a0d !== VisuMZ[label][_0x28f50f(0x178)] &&
                (_0x28f50f(0x251) === _0x28f50f(0x1a2)
                    ? (_0x78cb90(
                          _0x28f50f(0x1d6)[_0x28f50f(0x1b7)](_0x3e6348, _0x565594, _0x20177d)
                      ),
                      _0xdd5f4c[_0x28f50f(0x257)]())
                    : (alert(_0x28f50f(0x200)[_0x28f50f(0x1b7)](_0x299550, _0x4c3a0d)),
                      SceneManager[_0x28f50f(0x257)]()));
        }
        if (_0x49b0e8[_0x28f50f(0x1c4)](/\[Tier[ ](\d+)\]/i)) {
            const _0x30f50f = Number(RegExp['$1']);
            if (_0x30f50f < tier)
                (alert(_0x28f50f(0x1d6)[_0x28f50f(0x1b7)](_0x299550, _0x30f50f, tier)),
                    SceneManager[_0x28f50f(0x257)]());
            else {
                if (_0x28f50f(0x1b8) !== _0x28f50f(0x15f))
                    tier = Math[_0x28f50f(0x212)](_0x30f50f, tier);
                else {
                    const _0xcc5d78 = this[_0x28f50f(0x1f0)]();
                    let _0x4f72f9 = _0xcc5d78 * 0x2;
                    const _0x2c2a38 = this[_0x28f50f(0x1b3)] - _0x4f72f9 - _0xcc5d78 * 0x3,
                        _0x1ccca6 = _0x4f72f9 + _0x3b1ac7[_0x28f50f(0x23b)](_0x2c2a38 / 0x3),
                        _0x2a581f = this[_0x28f50f(0x15b)](),
                        _0x4d490a = _0x42c9e2[_0x28f50f(0x1b5)]((_0x2c2a38 * 0x2) / 0x3 / 0x3),
                        _0x1e3239 = _0x439462['max'](
                            this[_0x28f50f(0x161)](_0x28f50f(0x231)),
                            this[_0x28f50f(0x161)](_0x28f50f(0x232))
                        ),
                        _0x3bbedc =
                            this[_0x28f50f(0x185)]?.[_0x28f50f(0x1fa)] > 0x0
                                ? _0x1418e6[_0x28f50f(0x217)]
                                : 0x0,
                        _0x39c717 = this[_0x28f50f(0x1dd)](),
                        _0x1c434b = new _0x199614(
                            _0x56319c['floor'](
                                _0x1ccca6 +
                                    _0x4d490a * 0x2 -
                                    this[_0x28f50f(0x1dd)]() -
                                    _0x3bbedc +
                                    this[_0x28f50f(0x1f0)]() / 0x2 -
                                    0x2
                            ),
                            _0x2a581f,
                            this[_0x28f50f(0x1dd)](),
                            this[_0x28f50f(0x240)]()
                        );
                    return _0x1c434b;
                }
            }
        }
        VisuMZ[_0x28f50f(0x162)](VisuMZ[label]['Settings'], _0x32b2a5['parameters']);
    })(pluginData));
if (VisuMZ[_0x22ffed(0x25c)][_0x22ffed(0x178)] < 1.37) {
    let text = '';
    ((text += _0x22ffed(0x1ec)),
        (text += _0x22ffed(0x254)),
        alert(text),
        SceneManager[_0x22ffed(0x257)]());
}
((VisuMZ[_0x22ffed(0x1bb)]['RegExp'] = {
    SubCost: /<(ITEM|WEAPON|ARMOR|VARIABLE)[ ](.*?)[ ]COST:[ ](\d+)>/gi,
    SubBuyCost: /<(ITEM|WEAPON|ARMOR|VARIABLE)[ ](.*?)[ ]BUY COST:[ ](\d+)>/gi,
    SubSellCost: /<(ITEM|WEAPON|ARMOR|VARIABLE)[ ](.*?)[ ]SELL COST:[ ](\d+)>/gi,
}),
    (VisuMZ[_0x22ffed(0x1bb)]['Scene_Boot_onDatabaseLoaded'] =
        Scene_Boot[_0x22ffed(0x1ad)][_0x22ffed(0x24f)]),
    (Scene_Boot['prototype'][_0x22ffed(0x24f)] = function () {
        const _0x5d3bb0 = _0x22ffed;
        (DataManager[_0x5d3bb0(0x256)](),
            VisuMZ[_0x5d3bb0(0x1bb)][_0x5d3bb0(0x206)]['call'](this),
            this[_0x5d3bb0(0x22d)]());
    }),
    (Scene_Boot[_0x22ffed(0x1ad)][_0x22ffed(0x22d)] = function () {
        this['process_VisuMZ_MoreCurrencies_Notetags']();
    }),
    (Scene_Boot[_0x22ffed(0x1ad)]['process_VisuMZ_MoreCurrencies_Notetags'] = function () {
        const _0x1d56ac = _0x22ffed;
        if (VisuMZ[_0x1d56ac(0x1a1)]) return;
        const _0x339edf = [$dataItems, $dataWeapons, $dataArmors];
        for (const _0x36224e of _0x339edf) {
            for (const _0x3d1599 of _0x36224e) {
                if (!_0x3d1599) continue;
                VisuMZ[_0x1d56ac(0x1bb)]['ParseNotetagCosts'](_0x3d1599);
            }
        }
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x233)] = VisuMZ['ParseItemNotetags']),
    (VisuMZ['ParseItemNotetags'] = function (_0x2a1320) {
        const _0x11608c = _0x22ffed;
        (VisuMZ['MoreCurrencies'][_0x11608c(0x233)][_0x11608c(0x23d)](this, _0x2a1320),
            VisuMZ['MoreCurrencies'][_0x11608c(0x1e2)](_0x2a1320));
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x18f)] = VisuMZ[_0x22ffed(0x18f)]),
    (VisuMZ['ParseWeaponNotetags'] = function (_0x5191f9) {
        const _0x211d27 = _0x22ffed;
        (VisuMZ[_0x211d27(0x1bb)][_0x211d27(0x18f)][_0x211d27(0x23d)](this, _0x5191f9),
            VisuMZ[_0x211d27(0x1bb)][_0x211d27(0x1e2)](_0x5191f9));
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x21f)] = VisuMZ['ParseArmorNotetags']),
    (VisuMZ[_0x22ffed(0x21f)] = function (_0xb09cc6) {
        const _0x834155 = _0x22ffed;
        (VisuMZ[_0x834155(0x1bb)][_0x834155(0x21f)]['call'](this, _0xb09cc6),
            VisuMZ[_0x834155(0x1bb)][_0x834155(0x1e2)](_0xb09cc6));
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x1e2)] = function (_0x407ab8) {
        const _0x5d4a09 = _0x22ffed;
        if (!_0x407ab8) return;
        const _0x35b0da = VisuMZ[_0x5d4a09(0x1bb)][_0x5d4a09(0x229)],
            _0xf98ec = _0x407ab8[_0x5d4a09(0x166)];
        if (
            !_0xf98ec['match'](_0x35b0da['SubCost']) &&
            !_0xf98ec[_0x5d4a09(0x1c4)](_0x35b0da[_0x5d4a09(0x230)]) &&
            !_0xf98ec[_0x5d4a09(0x1c4)](_0x35b0da[_0x5d4a09(0x1c7)])
        )
            return;
        const _0x50a23f = DataManager[_0x5d4a09(0x1a8)](_0x407ab8);
        ((_0x50a23f[_0x407ab8['id']] = _0x50a23f[_0x407ab8['id']] || {}),
            _0xf98ec[_0x5d4a09(0x1c4)](_0x35b0da['SubBuyCost']) ||
            _0xf98ec['match'](_0x35b0da['SubSellCost'])
                ? (this['ParseNotetagSubCosts'](
                      _0x407ab8,
                      _0x50a23f[_0x407ab8['id']],
                      _0x5d4a09(0x230)
                  ),
                  this[_0x5d4a09(0x1c2)](_0x407ab8, _0x50a23f[_0x407ab8['id']], 'SubSellCost'))
                : this[_0x5d4a09(0x1c2)](_0x407ab8, _0x50a23f[_0x407ab8['id']], 'SubCost'));
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x1c2)] = function (_0x62993a, _0x417777, _0x59543d) {
        const _0x5d1e25 = _0x22ffed,
            _0x58640f = VisuMZ[_0x5d1e25(0x1bb)][_0x5d1e25(0x229)],
            _0x1234a7 = _0x62993a[_0x5d1e25(0x166)],
            _0x53c9e8 = _0x1234a7[_0x5d1e25(0x1c4)](_0x58640f[_0x59543d]);
        if (_0x53c9e8)
            for (const _0x3fae37 of _0x53c9e8) {
                if (_0x5d1e25(0x1e4) === _0x5d1e25(0x1f2)) {
                    const _0x556288 = _0x13c617(_0x127307['$1']);
                    _0x556288 !== _0x1fd5d6[_0x378ff1][_0x5d1e25(0x178)] &&
                        (_0x598382(
                            '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                                _0x5d1e25(0x1b7)
                            ](_0x37db09, _0x556288)
                        ),
                        _0xc789[_0x5d1e25(0x257)]());
                } else this['ParseNotetagLineSubCosts'](_0x3fae37, _0x417777, _0x59543d);
            }
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x173)] = function (_0x330cd1, _0x2eff02, _0xb22628) {
        const _0x11d72f = _0x22ffed,
            _0x4639e7 = VisuMZ[_0x11d72f(0x1bb)]['RegExp'];
        _0x330cd1[_0x11d72f(0x1c4)](_0x4639e7[_0xb22628]);
        const _0x32dffb = ['SubCost', _0x11d72f(0x230)][_0x11d72f(0x179)](_0xb22628),
            _0x496629 = ['SubCost', _0x11d72f(0x1c7)][_0x11d72f(0x179)](_0xb22628),
            _0x3ea516 = _0xb22628 === 'SubCost',
            _0x2c3d07 = DataManager[_0x11d72f(0x25d)],
            _0x4809bf = String(RegExp['$1'])[_0x11d72f(0x1d1)]()['trim'](),
            _0x513c6e = String(RegExp['$2'])[_0x11d72f(0x1d4)](),
            _0x129950 = Number(RegExp['$3']) || 0x0,
            _0x676075 = /^\d+$/[_0x11d72f(0x259)](_0x513c6e);
        if (_0x4809bf === _0x11d72f(0x1db)) {
            const _0xcf5e45 = _0x676075 ? _0xcf5e45 : DataManager[_0x11d72f(0x18e)](_0x513c6e);
            if (!_0xcf5e45) return;
            (_0x32dffb &&
                ((_0x2eff02[_0x11d72f(0x169)] = _0x2eff02['buyItemCosts'] || {}),
                (_0x2eff02[_0x11d72f(0x169)][_0xcf5e45] = _0x129950)),
                _0x496629 &&
                    ((_0x2eff02[_0x11d72f(0x198)] = _0x2eff02[_0x11d72f(0x198)] || {}),
                    (_0x2eff02[_0x11d72f(0x198)][_0xcf5e45] = Math[_0x11d72f(0x1b5)](
                        _0x129950 * (_0x3ea516 ? _0x2c3d07 : 0x1)
                    ))));
        } else {
            if (_0x4809bf === _0x11d72f(0x210)) {
                const _0x2ada12 = _0x676075
                    ? _0x2ada12
                    : DataManager['getWeaponIdWithName'](_0x513c6e);
                if (!_0x2ada12) return;
                if (_0x32dffb) {
                    if (_0x11d72f(0x168) !== _0x11d72f(0x168)) {
                        const _0x3d6a36 = _0xa10fab[_0x11d72f(0x207)][_0x11d72f(0x218)],
                            _0x1085a9 = _0x3d6a36['visualGoldDisplayPadding'](),
                            _0xae9b84 = _0x3d6a36[_0x11d72f(0x226)]();
                        _0x161498['push'](
                            _0x575f24[_0x11d72f(0x23f)][_0x11d72f(0x19e)](0x0, _0x1085a9, _0xae9b84)
                        );
                    } else
                        ((_0x2eff02['buyWeaponCosts'] = _0x2eff02[_0x11d72f(0x1e7)] || {}),
                            (_0x2eff02[_0x11d72f(0x1e7)][_0x2ada12] = _0x129950));
                }
                _0x496629 &&
                    ((_0x2eff02[_0x11d72f(0x1ae)] = _0x2eff02['sellWeaponCosts'] || {}),
                    (_0x2eff02[_0x11d72f(0x1ae)][_0x2ada12] = Math[_0x11d72f(0x1b5)](
                        _0x129950 * (_0x3ea516 ? _0x2c3d07 : 0x1)
                    )));
            } else {
                if (_0x4809bf === _0x11d72f(0x1b0)) {
                    const _0x162a64 = _0x676075
                        ? _0x162a64
                        : DataManager[_0x11d72f(0x214)](_0x513c6e);
                    if (!_0x162a64) return;
                    (_0x32dffb &&
                        ((_0x2eff02[_0x11d72f(0x1b2)] = _0x2eff02[_0x11d72f(0x1b2)] || {}),
                        (_0x2eff02[_0x11d72f(0x1b2)][_0x162a64] = _0x129950)),
                        _0x496629 &&
                            ((_0x2eff02[_0x11d72f(0x17e)] = _0x2eff02[_0x11d72f(0x17e)] || {}),
                            (_0x2eff02['sellArmorCosts'][_0x162a64] = Math['floor'](
                                _0x129950 * (_0x3ea516 ? _0x2c3d07 : 0x1)
                            ))));
                } else {
                    if (_0x4809bf === _0x11d72f(0x1eb)) {
                        const _0x1598fc = Number(_0x513c6e);
                        if (!_0x1598fc) return;
                        (_0x32dffb &&
                            ((_0x2eff02[_0x11d72f(0x183)] = _0x2eff02[_0x11d72f(0x183)] || {}),
                            (_0x2eff02[_0x11d72f(0x183)][_0x1598fc] = _0x129950)),
                            _0x496629 &&
                                ((_0x2eff02[_0x11d72f(0x19a)] = _0x2eff02[_0x11d72f(0x19a)] || {}),
                                (_0x2eff02['sellVariableCosts'][_0x1598fc] = Math[_0x11d72f(0x1b5)](
                                    _0x129950 * (_0x3ea516 ? _0x2c3d07 : 0x1)
                                ))));
                    }
                }
            }
        }
    }),
    (DataManager[_0x22ffed(0x25d)] =
        VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c9)][_0x22ffed(0x172)]),
    (DataManager[_0x22ffed(0x18e)] = function (_0x3e7bda) {
        const _0x5018e5 = _0x22ffed;
        ((_0x3e7bda = _0x3e7bda['toUpperCase']()[_0x5018e5(0x1d4)]()),
            (this['_itemIDs'] = this[_0x5018e5(0x1f9)] || {}));
        if (this[_0x5018e5(0x1f9)][_0x3e7bda]) return this[_0x5018e5(0x1f9)][_0x3e7bda];
        for (const _0x24875b of $dataItems) {
            if (_0x5018e5(0x190) === _0x5018e5(0x190)) {
                if (!_0x24875b) continue;
                this[_0x5018e5(0x1f9)][_0x24875b['name'][_0x5018e5(0x1d1)]()['trim']()] =
                    _0x24875b['id'];
            } else
                ((_0x311bd8[_0x5018e5(0x183)] = _0x131453['buyVariableCosts'] || {}),
                    (_0x2d40d4[_0x5018e5(0x183)][_0x568705] = _0x1ec18d));
        }
        return this[_0x5018e5(0x1f9)][_0x3e7bda] || 0x0;
    }),
    (DataManager[_0x22ffed(0x252)] = function (_0x4e2763) {
        const _0x5248e5 = _0x22ffed;
        ((_0x4e2763 = _0x4e2763[_0x5248e5(0x1d1)]()[_0x5248e5(0x1d4)]()),
            (this[_0x5248e5(0x17d)] = this[_0x5248e5(0x17d)] || {}));
        if (this[_0x5248e5(0x17d)][_0x4e2763]) return this[_0x5248e5(0x17d)][_0x4e2763];
        for (const _0x16dfba of $dataWeapons) {
            if (!_0x16dfba) continue;
            this[_0x5248e5(0x17d)][
                _0x16dfba[_0x5248e5(0x244)][_0x5248e5(0x1d1)]()[_0x5248e5(0x1d4)]()
            ] = _0x16dfba['id'];
        }
        return this['_weaponIDs'][_0x4e2763] || 0x0;
    }),
    (DataManager[_0x22ffed(0x214)] = function (_0x441c1a) {
        const _0x1a64d1 = _0x22ffed;
        ((_0x441c1a = _0x441c1a[_0x1a64d1(0x1d1)]()[_0x1a64d1(0x1d4)]()),
            (this['_armorIDs'] = this[_0x1a64d1(0x219)] || {}));
        if (this[_0x1a64d1(0x219)][_0x441c1a]) return this[_0x1a64d1(0x219)][_0x441c1a];
        for (const _0x3014f7 of $dataArmors) {
            if ('KgznM' === _0x1a64d1(0x1f5)) {
                const _0x4aeee1 = _0x2cf25c[0x0];
                this['resetFontSettings']();
                const _0x184035 = _0x3150cd[_0x1a64d1(0x207)][_0x1a64d1(0x215)]['currentSymbol'](),
                    _0x36c354 = _0x184035 === 'buy';
                this['drawMoreCurrenciesMathMarks'](_0x2b4845, _0x36c354 ? '-' : '+');
                if (_0x4aeee1 === _0x1a64d1(0x1f8))
                    this[_0x1a64d1(0x1dc)](_0x42fe9b, _0x1b2946, _0x36c354);
                else
                    _0x4aeee1 === _0x1a64d1(0x1b9)
                        ? this[_0x1a64d1(0x16c)](_0x1387dc, _0x2162c5, _0x36c354)
                        : this[_0x1a64d1(0x1fc)](_0x5db0d4, _0x2b6667, _0x36c354);
            } else {
                if (!_0x3014f7) continue;
                this[_0x1a64d1(0x219)][
                    _0x3014f7[_0x1a64d1(0x244)]['toUpperCase']()[_0x1a64d1(0x1d4)]()
                ] = _0x3014f7['id'];
            }
        }
        return this[_0x1a64d1(0x219)][_0x441c1a] || 0x0;
    }),
    (DataManager[_0x22ffed(0x256)] = function () {
        const _0x289965 = _0x22ffed;
        this[_0x289965(0x1d7)] = this['_moreCurrencyCosts'] || {
            items: {},
            weapons: {},
            armors: {},
        };
    }),
    (DataManager[_0x22ffed(0x1a8)] = function (_0x4b1aff) {
        const _0x183633 = _0x22ffed;
        if (DataManager[_0x183633(0x16e)](_0x4b1aff)) {
            if (_0x183633(0x1b6) === _0x183633(0x1fb)) {
                const _0xa7ab3d = this[_0x183633(0x1f0)]();
                let _0x2d7e4f = _0xa7ab3d * 0x2;
                const _0x2979d5 = this[_0x183633(0x1b3)] - _0x2d7e4f - _0xa7ab3d * 0x3,
                    _0x33fb44 = _0x2d7e4f + _0x555049['ceil'](_0x2979d5 / 0x3),
                    _0x128355 = _0x28c26e[_0x183633(0x1b5)]((_0x2979d5 * 0x2) / 0x3 / 0x3),
                    _0x43c374 = _0x12813a[_0x183633(0x212)](
                        this['textWidth'](_0x183633(0x231)),
                        this[_0x183633(0x161)](_0x183633(0x232))
                    ),
                    _0x51b2f0 = _0x29e211[0x0],
                    _0x141d29 = _0x551269[0x1],
                    _0x52f6fb = _0x4dbd4c[0x2],
                    _0x3e346b = _0x141d29 * this[_0x183633(0x25a)];
                let _0xc78295 = 0x0;
                const _0x392891 = _0x21b582['variables'][_0x52f6fb];
                _0x392891[_0x183633(0x1c4)](/\\I\[(\d+)\]/i) &&
                    (_0xc78295 = _0xf999eb(_0x24cfcc['$1']));
                const _0x31a5b5 = _0xc78295 > 0x0 ? _0x2f2fc2[_0x183633(0x217)] + 0x4 : 0x0;
                this[_0x183633(0x1cc)](
                    _0x392891,
                    _0x2d7e4f,
                    _0x150722,
                    _0x2979d5,
                    _0x183633(0x187)
                );
                const _0x6720e1 = _0x33fb44 + _0x128355 * 0x0,
                    _0x288a58 = _0x128355 - _0x31a5b5,
                    _0x1daa1a = _0x5f2db4['value'](_0x52f6fb);
                (this[_0x183633(0x1bc)](
                    _0x1daa1a,
                    _0x6720e1,
                    _0x1f4a1c,
                    _0x288a58,
                    _0x183633(0x208)
                ),
                    this[_0x183633(0x1a9)](_0xc78295, _0x6720e1 + _0x288a58 + 0x4, _0x36439e));
                const _0x43bd81 = _0x33fb44 + _0x128355 * 0x1 + _0x43c374,
                    _0x5af3af = _0x128355 - _0x43c374 - _0x31a5b5;
                (this[_0x183633(0x1bc)](
                    _0x3e346b,
                    _0x43bd81,
                    _0x539477,
                    _0x5af3af,
                    _0x183633(0x208)
                ),
                    this[_0x183633(0x1a9)](_0xc78295, _0x43bd81 + _0x5af3af + 0x4, _0x5db352));
                const _0x59e8e5 = _0x33fb44 + _0x128355 * 0x2 + _0x43c374,
                    _0x4189a1 = _0x128355 - _0x43c374 - _0x31a5b5,
                    _0x316ffb = _0x1daa1a + _0x3e346b * (_0x5ede02 ? -0x1 : 0x1);
                (this['drawText'](_0x316ffb, _0x59e8e5, _0x2e49c8, _0x4189a1, 'right'),
                    this[_0x183633(0x1a9)](_0xc78295, _0x59e8e5 + _0x4189a1 + 0x4, _0x4145aa));
            } else return this[_0x183633(0x1d7)][_0x183633(0x209)];
        } else {
            if (DataManager[_0x183633(0x22b)](_0x4b1aff))
                return this[_0x183633(0x1d7)][_0x183633(0x253)];
            else {
                if (DataManager[_0x183633(0x165)](_0x4b1aff)) {
                    if ('JNOBZ' !== _0x183633(0x180))
                        this['drawMoreCurrenciesItem'](_0x3f1fd9, _0x29a9c3, _0x279131);
                    else return this[_0x183633(0x1d7)][_0x183633(0x250)];
                } else return {};
            }
        }
    }),
    (TextManager[_0x22ffed(0x193)] =
        VisuMZ['MoreCurrencies']['Settings'][_0x22ffed(0x1c8)][_0x22ffed(0x1f4)]),
    (TextManager[_0x22ffed(0x186)] = {
        item: VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c8)][_0x22ffed(0x1df)],
        weapon: VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c8)][_0x22ffed(0x184)],
        armor: VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c8)][_0x22ffed(0x23a)],
        variable: VisuMZ[_0x22ffed(0x1bb)]['Settings'][_0x22ffed(0x1c8)][_0x22ffed(0x181)],
    }),
    (TextManager[_0x22ffed(0x19b)] = {
        owned:
            VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c9)][_0x22ffed(0x20c)] ||
            _0x22ffed(0x239),
        shift:
            VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c9)]['NumWindowShift'] ||
            'Change',
        net: VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)]['General'][_0x22ffed(0x1aa)] || 'Net',
    }),
    (SceneManager['isSceneShop'] = function () {
        const _0x2166ba = _0x22ffed;
        return this[_0x2166ba(0x207)] && this['_scene'][_0x2166ba(0x22e)] === Scene_Shop;
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x1fd)] = Scene_Shop['prototype'][_0x22ffed(0x1bf)]),
    (Scene_Shop[_0x22ffed(0x1ad)][_0x22ffed(0x1bf)] = function () {
        const _0x55f498 = _0x22ffed;
        let _0x262212 = [VisuMZ['MoreCurrencies']['Scene_Shop_maxBuy'][_0x55f498(0x23d)](this)];
        return (
            ($gameTemp[_0x55f498(0x202)] = !![]),
            (item = this['_buyWindow']['item']()),
            (_0x262212 = _0x262212['concat'](VisuMZ[_0x55f498(0x1bb)][_0x55f498(0x24a)](item))),
            ($gameTemp[_0x55f498(0x202)] = ![]),
            Math[_0x55f498(0x245)](..._0x262212)
        );
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x24a)] = function (_0x191531) {
        const _0x3cc21c = _0x22ffed;
        if (!_0x191531) return [];
        const _0x3ec4ce = DataManager['getMoreCurrenciesObjLibrary'](_0x191531),
            _0x5c2373 = _0x3ec4ce[_0x191531['id']];
        if (!_0x5c2373) return [];
        const _0x55fdbf = [];
        for (const _0x3d3226 in _0x5c2373[_0x3cc21c(0x169)]) {
            const _0xb496fb = Number(_0x3d3226) || 0x0;
            if (!_0xb496fb) continue;
            const _0x55c6a1 = $dataItems[_0xb496fb];
            if (!_0x55c6a1) continue;
            const _0x27abb4 = _0x5c2373[_0x3cc21c(0x169)][_0x3d3226] || 0x1,
                _0x558a40 = $gameParty[_0x3cc21c(0x23c)](_0x55c6a1),
                _0x2fb230 = Math[_0x3cc21c(0x1b5)](_0x558a40 / _0x27abb4);
            _0x55fdbf[_0x3cc21c(0x1e8)](_0x2fb230);
        }
        for (const _0x3dc537 in _0x5c2373[_0x3cc21c(0x1e7)]) {
            if ('PIYFE' === _0x3cc21c(0x20a)) {
                const _0x49222b = Number(_0x3dc537) || 0x0;
                if (!_0x49222b) continue;
                const _0x44dd32 = $dataWeapons[_0x49222b];
                if (!_0x44dd32) continue;
                const _0xef98eb = _0x5c2373[_0x3cc21c(0x1e7)][_0x3dc537] || 0x1,
                    _0x586aeb = $gameParty['numItems'](_0x44dd32),
                    _0x34ac7a = Math[_0x3cc21c(0x1b5)](_0x586aeb / _0xef98eb);
                _0x55fdbf[_0x3cc21c(0x1e8)](_0x34ac7a);
            } else
                ((_0x1a36ca[_0x3cc21c(0x17e)] = _0xd43a8a[_0x3cc21c(0x17e)] || {}),
                    (_0x206cff[_0x3cc21c(0x17e)][_0x44dd07] = _0x415288[_0x3cc21c(0x1b5)](
                        _0x460520 * (_0x18d293 ? _0x35fb57 : 0x1)
                    )));
        }
        for (const _0x5e1096 in _0x5c2373['buyArmorCosts']) {
            if (_0x3cc21c(0x1d0) !== _0x3cc21c(0x1d0))
                return this[_0x3cc21c(0x1d7)][_0x3cc21c(0x250)];
            else {
                const _0x231cd8 = Number(_0x5e1096) || 0x0;
                if (!_0x231cd8) continue;
                const _0x1ee69c = $dataArmors[_0x231cd8];
                if (!_0x1ee69c) continue;
                const _0x1bedd3 = _0x5c2373['buyArmorCosts'][_0x5e1096] || 0x1,
                    _0x223028 = $gameParty[_0x3cc21c(0x23c)](_0x1ee69c),
                    _0xc91a3b = Math[_0x3cc21c(0x1b5)](_0x223028 / _0x1bedd3);
                _0x55fdbf['push'](_0xc91a3b);
            }
        }
        for (const _0x28f17a in _0x5c2373['buyVariableCosts']) {
            const _0x111f95 = Number(_0x28f17a) || 0x0;
            if (!_0x111f95) continue;
            const _0xa5e305 = _0x5c2373[_0x3cc21c(0x183)][_0x28f17a] || 0x1,
                _0x5402cf = $gameVariables[_0x3cc21c(0x15e)](_0x111f95),
                _0x15cfec = Math[_0x3cc21c(0x1b5)](_0x5402cf / _0xa5e305);
            _0x55fdbf[_0x3cc21c(0x1e8)](_0x15cfec);
        }
        return _0x55fdbf;
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x222)] = Scene_Shop[_0x22ffed(0x1ad)]['doBuy']),
    (Scene_Shop[_0x22ffed(0x1ad)][_0x22ffed(0x1c0)] = function (_0x4fc72b) {
        const _0x2d3384 = _0x22ffed;
        VisuMZ[_0x2d3384(0x1bb)][_0x2d3384(0x222)][_0x2d3384(0x23d)](this, _0x4fc72b);
        if (_0x4fc72b <= 0x0) return;
        (($gameTemp[_0x2d3384(0x202)] = !![]),
            (item = this['_buyWindow'][_0x2d3384(0x177)]()),
            ($gameTemp['_bypassProxy'] = ![]),
            VisuMZ[_0x2d3384(0x1bb)][_0x2d3384(0x248)](item, -_0x4fc72b));
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x262)] = Scene_Shop[_0x22ffed(0x1ad)][_0x22ffed(0x1d2)]),
    (Scene_Shop['prototype'][_0x22ffed(0x1d2)] = function (_0xfbb8d3) {
        const _0x1be76f = _0x22ffed;
        VisuMZ[_0x1be76f(0x1bb)][_0x1be76f(0x262)]['call'](this, _0xfbb8d3);
        if (_0xfbb8d3 <= 0x0) return;
        (($gameTemp[_0x1be76f(0x202)] = !![]),
            (item = this['_sellWindow'][_0x1be76f(0x177)]()),
            ($gameTemp[_0x1be76f(0x202)] = ![]),
            VisuMZ[_0x1be76f(0x1bb)][_0x1be76f(0x248)](item, _0xfbb8d3));
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x248)] = function (_0x31248c, _0x1b1658) {
        const _0x2f67bd = _0x22ffed;
        if (!_0x31248c) return [];
        const _0x4ec33b = DataManager[_0x2f67bd(0x1a8)](_0x31248c),
            _0x221638 = _0x4ec33b[_0x31248c['id']];
        if (!_0x221638) return [];
        let _0x211cba = {};
        _0x211cba = _0x1b1658 < 0x0 ? _0x221638[_0x2f67bd(0x169)] : _0x221638[_0x2f67bd(0x198)];
        for (const _0x436fb2 in _0x211cba) {
            const _0x36fcf3 = Number(_0x436fb2) || 0x0;
            if (!_0x36fcf3) continue;
            const _0x36df34 = $dataItems[_0x36fcf3];
            if (!_0x36df34) continue;
            const _0x17724c = _0x211cba[_0x436fb2] || 0x1,
                _0x2af60d = _0x17724c * _0x1b1658;
            $gameParty[_0x2f67bd(0x1ea)](_0x36df34, _0x2af60d);
        }
        _0x211cba = _0x1b1658 < 0x0 ? _0x221638[_0x2f67bd(0x1e7)] : _0x221638['sellWeaponCosts'];
        for (const _0x133e45 in _0x211cba) {
            const _0x1259aa = Number(_0x133e45) || 0x0;
            if (!_0x1259aa) continue;
            const _0x506341 = $dataWeapons[_0x1259aa];
            if (!_0x506341) continue;
            const _0x476283 = _0x211cba[_0x133e45] || 0x1,
                _0xa1954e = _0x476283 * _0x1b1658;
            $gameParty[_0x2f67bd(0x1ea)](_0x506341, _0xa1954e);
        }
        _0x211cba = _0x1b1658 < 0x0 ? _0x221638['buyArmorCosts'] : _0x221638[_0x2f67bd(0x17e)];
        for (const _0x2f6888 in _0x211cba) {
            if (_0x2f67bd(0x16f) !== _0x2f67bd(0x16f))
                this['process_VisuMZ_MoreCurrencies_Notetags']();
            else {
                const _0x577abd = Number(_0x2f6888) || 0x0;
                if (!_0x577abd) continue;
                const _0x1c466b = $dataArmors[_0x577abd];
                if (!_0x1c466b) continue;
                const _0x8c048b = _0x211cba[_0x2f6888] || 0x1,
                    _0x3a9f85 = _0x8c048b * _0x1b1658;
                $gameParty[_0x2f67bd(0x1ea)](_0x1c466b, _0x3a9f85);
            }
        }
        _0x211cba = _0x1b1658 < 0x0 ? _0x221638[_0x2f67bd(0x183)] : _0x221638['sellVariableCosts'];
        for (const _0xe87af in _0x211cba) {
            const _0x35284f = Number(_0xe87af) || 0x0;
            if (!_0x35284f) continue;
            const _0x251f48 = _0x211cba[_0xe87af] || 0x1,
                _0x21c39f = _0x251f48 * _0x1b1658,
                _0x33c491 = $gameVariables[_0x2f67bd(0x15e)](_0x35284f) + _0x21c39f;
            $gameVariables['setValue'](_0x35284f, _0x33c491);
        }
    }),
    (Window_Base[_0x22ffed(0x21b)] =
        VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c8)]['BuyPadding']),
    (Window_Base[_0x22ffed(0x20b)] =
        VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)]['Listing'][_0x22ffed(0x1cb)]),
    (Window_Base['prototype'][_0x22ffed(0x1c6)] = function (
        _0x589564,
        _0x315a85,
        _0x21fb49,
        _0x45f4d7
    ) {
        const _0x203126 = _0x22ffed;
        _0x21fb49 = _0x21fb49 || ![];
        let _0x1204ba = [];
        for (const _0x470148 of Window_Base[_0x203126(0x20b)]) {
            if (_0x203126(0x242) === 'etBNH') {
                const _0x158659 = VisuMZ['MoreCurrencies'][_0x203126(0x201)](
                    _0x589564,
                    _0x470148,
                    _0x21fb49,
                    _0x45f4d7
                );
                if (_0x158659) _0x1204ba = _0x1204ba['concat'](_0x158659);
            } else {
                const _0x15c7a4 =
                    _0x4b432c + (this[_0x203126(0x240)]() - _0x3967bd[_0x203126(0x17f)]) / 0x2;
                this['drawIcon'](_0x535643, _0x57e599, _0x15c7a4);
                const _0x4d4e96 = _0x34ce11[_0x203126(0x217)] + 0x4;
                _0x4b0148 += _0x4d4e96;
            }
        }
        const _0xc2932 = _0x203126(0x16d)['format'](TextManager['MoreCurrenciesFontSize']),
            _0x1ef3f4 = '\x5cFS[%1]'[_0x203126(0x1b7)]($gameSystem[_0x203126(0x204)]());
        (_0x1204ba[_0x203126(0x25e)](''),
            (_0x1204ba = _0x1204ba[_0x203126(0x1d5)](
                _0xd58d81 => _0xc2932 + _0xd58d81 + _0x1ef3f4
            )));
        if (_0x1204ba[_0x203126(0x1e3)] === 0x0) {
            if (_0x203126(0x196) !== _0x203126(0x196)) {
                const _0x13613d = this[_0x203126(0x1f0)]();
                let _0x3beade = _0x13613d * 0x2;
                const _0xc88055 = this[_0x203126(0x1b3)] - _0x3beade - _0x13613d * 0x3,
                    _0x5c833c = _0x3beade + _0x22c083['ceil'](_0xc88055 / 0x3),
                    _0x4c99fa = _0x305512[_0x203126(0x1b5)]((_0xc88055 * 0x2) / 0x3 / 0x3);
                ((_0x317cbd = _0x203126(0x188)['format'](_0x4110fd)),
                    this['drawText'](
                        _0x134142,
                        _0x5c833c + _0x4c99fa * 0x1,
                        _0x39c0c1,
                        _0x4c99fa,
                        _0x203126(0x187)
                    ),
                    this[_0x203126(0x1bc)](
                        '\x20=',
                        _0x5c833c + _0x4c99fa * 0x2,
                        _0x2199c6,
                        _0x4c99fa,
                        _0x203126(0x187)
                    ));
            } else {
                if (Imported[_0x203126(0x20f)]) {
                    if (_0x203126(0x1f7) !== _0x203126(0x255)) {
                        const _0x5e03ef = SceneManager['_scene']['_buyWindow'],
                            _0x105ee6 = _0x5e03ef[_0x203126(0x15c)](),
                            _0x578d93 = _0x5e03ef[_0x203126(0x226)]();
                        _0x1204ba[_0x203126(0x1e8)](
                            VisuMZ[_0x203126(0x23f)]['CreateVisualGoldText'](
                                0x0,
                                _0x105ee6,
                                _0x578d93
                            )
                        );
                    } else {
                        const _0x3e01d6 = _0x5bb4da[_0x203126(0x15c)](),
                            _0x4d99c3 = _0x569d89[_0x203126(0x226)]();
                        return _0x4a0074[_0x203126(0x23f)][_0x203126(0x19e)](
                            _0x1f4317,
                            _0x3e01d6,
                            _0x4d99c3
                        );
                    }
                } else {
                    if (_0x203126(0x25b) !== _0x203126(0x25b)) {
                        const _0x534180 =
                            _0x19b272[_0x203126(0x207)][_0x203126(0x1ba)][_0x203126(0x1e9)];
                        return [_0x203126(0x1f8), _0x534180];
                    } else _0x1204ba['push'](VisuMZ[_0x203126(0x1bb)][_0x203126(0x19f)](0x0));
                }
            }
        }
        _0x1204ba[_0x203126(0x15d)]();
        for (const _0xfe8904 of _0x1204ba) {
            if (_0xfe8904 === '') continue;
            this[_0x203126(0x260)]();
            const _0x5d0e39 = this[_0x203126(0x1ff)](_0xfe8904)[_0x203126(0x203)],
                _0x1c50be = _0x315a85['x'] + _0x315a85[_0x203126(0x203)] - _0x5d0e39,
                _0x3c3089 = _0x315a85['y'];
            (this[_0x203126(0x1cc)](_0xfe8904, _0x1c50be, _0x3c3089, _0x5d0e39),
                (_0x315a85[_0x203126(0x203)] -= _0x5d0e39 + Window_Base[_0x203126(0x21b)]));
        }
        this['resetFontSettings']();
    }),
    (VisuMZ[_0x22ffed(0x1bb)]['CreateSubCostText'] = function (
        _0xdd10c0,
        _0x54deae,
        _0x5d26a9,
        _0xed237e
    ) {
        const _0x594e9d = _0x22ffed;
        _0x54deae = _0x54deae[_0x594e9d(0x235)]()[_0x594e9d(0x1d4)]();
        switch (_0x54deae) {
            case _0x594e9d(0x177):
            case _0x594e9d(0x182):
            case 'armor':
                return this[_0x594e9d(0x17c)](_0xdd10c0, _0x54deae, _0x5d26a9, _0xed237e);
            case _0x594e9d(0x1b9):
                return this['CreateSubVariableCostTexts'](
                    _0xdd10c0,
                    _0x54deae,
                    _0x5d26a9,
                    _0xed237e
                );
            case 'gold':
                return [this[_0x594e9d(0x195)](_0xdd10c0, _0x54deae, _0x5d26a9, _0xed237e)];
            default:
                return [];
        }
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x17c)] = function (
        _0xeb31c3,
        _0x569a16,
        _0xde51b2,
        _0x2a8933
    ) {
        const _0x3695db = _0x22ffed,
            _0xa98825 = DataManager[_0x3695db(0x1a8)](_0xeb31c3),
            _0x25100b = _0xa98825[_0xeb31c3['id']];
        if (!_0x25100b) return [];
        const _0x22ea85 = _0xde51b2 ? 'sell' : _0x3695db(0x1f1),
            _0x13fedb = _0x3695db(0x174)[_0x3695db(0x1b7)](
                _0x22ea85,
                _0x569a16[_0x3695db(0x220)](0x0)[_0x3695db(0x1d1)]() +
                    _0x569a16[_0x3695db(0x21e)](0x1)
            );
        if (!_0x25100b[_0x13fedb]) return [];
        let _0x5c79de = [];
        if (_0x569a16 === _0x3695db(0x177)) _0x5c79de = $dataItems;
        if (_0x569a16 === 'weapon') _0x5c79de = $dataWeapons;
        if (_0x569a16 === _0x3695db(0x1de)) _0x5c79de = $dataArmors;
        const _0x458a33 = TextManager[_0x3695db(0x186)][_0x569a16],
            _0x3c960e = [];
        for (const _0x5a03b5 in _0x25100b[_0x13fedb]) {
            const _0x4d3afa = Number(_0x5a03b5),
                _0x4dc7cc = _0x5c79de[_0x4d3afa];
            if (!_0x4dc7cc) continue;
            const _0x139e4c = _0x25100b[_0x13fedb][_0x5a03b5] * _0x2a8933,
                _0x179476 = $gameParty[_0x3695db(0x23c)](_0x4dc7cc),
                _0x5b3ece = _0x4dc7cc[_0x3695db(0x1fa)]
                    ? _0x3695db(0x23e)['format'](_0x4dc7cc['iconIndex'])
                    : '',
                _0x3f318e = _0x4dc7cc[_0x3695db(0x244)],
                _0x2de251 = _0x458a33[_0x3695db(0x1b7)](_0x139e4c, _0x179476, _0x5b3ece, _0x3f318e);
            _0x3c960e[_0x3695db(0x1e8)](_0x2de251);
        }
        return _0x3c960e;
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x16b)] = function (
        _0x23a60e,
        _0x46e23f,
        _0x19cddf,
        _0x4d8411
    ) {
        const _0x274484 = _0x22ffed,
            _0x10c972 = DataManager[_0x274484(0x1a8)](_0x23a60e),
            _0x4f5665 = _0x10c972[_0x23a60e['id']];
        if (!_0x4f5665) return [];
        const _0x45e47a = _0x19cddf ? _0x274484(0x1ab) : 'buy',
            _0x1be46c = _0x274484(0x174)[_0x274484(0x1b7)](
                _0x45e47a,
                _0x46e23f[_0x274484(0x220)](0x0)[_0x274484(0x1d1)]() +
                    _0x46e23f[_0x274484(0x21e)](0x1)
            );
        if (!_0x4f5665[_0x1be46c]) return [];
        const _0x32bf13 = TextManager[_0x274484(0x186)][_0x46e23f],
            _0x3718fb = [];
        for (const _0x501f5c in _0x4f5665[_0x1be46c]) {
            if (_0x274484(0x227) !== _0x274484(0x227)) {
                const _0x50cb57 = this['itemPadding']();
                let _0x283cec = _0x50cb57 * 0x2;
                const _0x44af22 = this[_0x274484(0x1b3)] - _0x283cec - _0x50cb57 * 0x3,
                    _0x203785 = _0x283cec + _0x3e3c4a[_0x274484(0x23b)](_0x44af22 / 0x3),
                    _0x1af62e = _0x262227[_0x274484(0x1b5)]((_0x44af22 * 0x2) / 0x3 / 0x3),
                    _0x38774e = _0x1b3ae6[_0x274484(0x212)](
                        this['textWidth']('\x20+\x20'),
                        this[_0x274484(0x161)](_0x274484(0x232))
                    );
                (this[_0x274484(0x260)](), this[_0x274484(0x21a)](_0x576f39[_0x274484(0x1ef)]()));
                const _0x302533 = [_0x274484(0x1cd), 'shift', 'net'];
                for (let _0x5d1bf8 = 0x0; _0x5d1bf8 < 0x3; _0x5d1bf8++) {
                    const _0x5aa2ef = _0x302533[_0x5d1bf8],
                        _0x5958fc = _0x1ee208['MoreCurrenciesNumberWindow'][_0x5aa2ef];
                    this[_0x274484(0x1bc)](
                        _0x5958fc,
                        _0x203785 + _0x1af62e * _0x5d1bf8 + _0x38774e,
                        _0x968958,
                        _0x1af62e - _0x38774e,
                        _0x274484(0x1a4)
                    );
                }
            } else {
                const _0x204219 = Number(_0x501f5c);
                if ($dataSystem[_0x274484(0x261)]['length'] <= _0x204219) continue;
                const _0x2d1a94 = _0x4f5665[_0x1be46c][_0x501f5c] * _0x4d8411,
                    _0x5ee574 = $gameVariables[_0x274484(0x15e)](_0x204219);
                let _0x588ce4 = '',
                    _0x55a969 = $dataSystem[_0x274484(0x261)][_0x204219];
                _0x55a969[_0x274484(0x1c4)](/\\I\[(\d+)\]/i) &&
                    (_0x274484(0x1c1) !== _0x274484(0x1d3)
                        ? (_0x588ce4 = '\x5cI[%1]'[_0x274484(0x1b7)](Number(RegExp['$1'])))
                        : (_0x59f687 = _0x274484(0x23e)[_0x274484(0x1b7)](
                              _0x108db0(_0x1ad6ca['$1'])
                          )));
                _0x55a969 = _0x55a969[_0x274484(0x17a)](/<(.*)>/gi, '');
                const _0x3ee9d8 = _0x32bf13['format'](_0x2d1a94, _0x5ee574, _0x588ce4, _0x55a969);
                _0x3718fb['push'](_0x3ee9d8);
            }
        }
        return _0x3718fb;
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x195)] = function (
        _0x3f976c,
        _0x183e3b,
        _0x30db49,
        _0x17cf63
    ) {
        const _0x3655e1 = _0x22ffed,
            _0x5bcb8b = SceneManager['_scene'][_0x3655e1(0x218)],
            _0x442ca9 = _0x5bcb8b['price'](_0x3f976c),
            _0x110ab4 = SceneManager['_scene'][_0x3655e1(0x18b)](_0x3f976c),
            _0x2c847d = Math[_0x3655e1(0x223)]((_0x30db49 ? _0x110ab4 : _0x442ca9) * _0x17cf63);
        if (_0x2c847d === 0x0) return '';
        if (Imported[_0x3655e1(0x20f)]) {
            const _0x290b31 = _0x5bcb8b[_0x3655e1(0x15c)](),
                _0x2ca75f = _0x5bcb8b['visualGoldDisplayNoCost']();
            return VisuMZ['VisualGoldDisplay'][_0x3655e1(0x19e)](_0x2c847d, _0x290b31, _0x2ca75f);
        } else return this[_0x3655e1(0x19f)](_0x2c847d);
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x19f)] = function (_0x2ef6f2) {
        const _0x36a262 = _0x22ffed,
            _0x105823 = _0x36a262(0x234),
            _0x5724c2 =
                VisuMZ[_0x36a262(0x25f)][_0x36a262(0x175)][_0x36a262(0x1cf)][_0x36a262(0x1d8)],
            _0x2a0ef4 = TextManager['currencyUnit'];
        return _0x105823[_0x36a262(0x1b7)](
            _0x2ef6f2,
            _0x5724c2 > 0x0 ? _0x36a262(0x23e)[_0x36a262(0x1b7)](_0x5724c2) : '',
            _0x5724c2 > 0x0 ? '' : _0x2a0ef4
        );
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x1ce)] =
        Window_ShopBuy[_0x22ffed(0x1ad)][_0x22ffed(0x160)]),
    (Window_ShopBuy[_0x22ffed(0x1ad)]['drawItemCost'] = function (_0x4c74a8, _0x24f5ae) {
        const _0x84b112 = _0x22ffed;
        if (!_0x4c74a8) return;
        this[_0x84b112(0x1c6)](_0x4c74a8, _0x24f5ae, ![], 0x1);
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x237)] = Window_ShopBuy[_0x22ffed(0x1ad)]['isEnabled']),
    (Window_ShopBuy[_0x22ffed(0x1ad)][_0x22ffed(0x228)] = function (_0x356f74) {
        const _0x4adf1f = _0x22ffed;
        if (!VisuMZ[_0x4adf1f(0x1bb)][_0x4adf1f(0x237)][_0x4adf1f(0x23d)](this, _0x356f74))
            return ![];
        return VisuMZ[_0x4adf1f(0x1bb)][_0x4adf1f(0x164)](_0x356f74);
    }),
    (VisuMZ[_0x22ffed(0x1bb)]['CheckMeetBuyRequirements'] = function (_0x592c42) {
        const _0x304ee5 = _0x22ffed;
        if (!_0x592c42) return ![];
        const _0x121af4 = DataManager[_0x304ee5(0x1a8)](_0x592c42),
            _0x3e906f = _0x121af4[_0x592c42['id']];
        if (!_0x3e906f) return !![];
        for (const _0x29c042 in _0x3e906f[_0x304ee5(0x169)]) {
            const _0x2c4aec = Number(_0x29c042) || 0x0;
            if (!_0x2c4aec) continue;
            const _0x13b9db = $dataItems[_0x2c4aec];
            if (!_0x13b9db) continue;
            const _0x3b950d = _0x3e906f[_0x304ee5(0x169)][_0x29c042];
            if (_0x3b950d > $gameParty[_0x304ee5(0x23c)](_0x13b9db)) return ![];
        }
        for (const _0x1ee955 in _0x3e906f[_0x304ee5(0x1e7)]) {
            if (_0x304ee5(0x243) === 'mbWpT')
                this[_0x304ee5(0x247)](_0x3cdc53, _0x44634c, _0x5799f8, _0x19bf68);
            else {
                const _0x369f74 = Number(_0x1ee955) || 0x0;
                if (!_0x369f74) continue;
                const _0x1c3dc1 = $dataWeapons[_0x369f74];
                if (!_0x1c3dc1) continue;
                const _0x4c9caa = _0x3e906f[_0x304ee5(0x1e7)][_0x1ee955];
                if (_0x4c9caa > $gameParty['numItems'](_0x1c3dc1)) return ![];
            }
        }
        for (const _0x3d2221 in _0x3e906f[_0x304ee5(0x1b2)]) {
            const _0x25d2c9 = Number(_0x3d2221) || 0x0;
            if (!_0x25d2c9) continue;
            const _0x366cda = $dataArmors[_0x25d2c9];
            if (!_0x366cda) continue;
            const _0x3fbbc5 = _0x3e906f[_0x304ee5(0x1b2)][_0x3d2221];
            if (_0x3fbbc5 > $gameParty[_0x304ee5(0x23c)](_0x366cda)) return ![];
        }
        for (const _0xacc383 in _0x3e906f[_0x304ee5(0x183)]) {
            if (_0x304ee5(0x221) !== _0x304ee5(0x211)) {
                const _0x465482 = Number(_0xacc383) || 0x0;
                if (!_0x465482) continue;
                const _0x252d46 = _0x3e906f['buyVariableCosts'][_0xacc383];
                if (_0x252d46 > $gameVariables[_0x304ee5(0x15e)](_0x465482)) return ![];
            } else
                this[_0x304ee5(0x1d7)] = this[_0x304ee5(0x1d7)] || {
                    items: {},
                    weapons: {},
                    armors: {},
                };
        }
        return !![];
    }),
    (Window_ShopSell[_0x22ffed(0x1a3)] =
        VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x175)][_0x22ffed(0x1c8)][_0x22ffed(0x17b)]),
    (VisuMZ[_0x22ffed(0x1bb)]['Window_ItemList_drawItemNumber'] =
        Window_ItemList[_0x22ffed(0x1ad)][_0x22ffed(0x241)]),
    (Window_ItemList[_0x22ffed(0x1ad)][_0x22ffed(0x241)] = function (
        _0x2a1ad1,
        _0x596c82,
        _0x2510a8,
        _0x85526
    ) {
        const _0x16ff19 = _0x22ffed;
        (VisuMZ[_0x16ff19(0x1bb)][_0x16ff19(0x1b4)][_0x16ff19(0x23d)](
            this,
            _0x2a1ad1,
            _0x596c82,
            _0x2510a8,
            _0x85526
        ),
            this[_0x16ff19(0x1ee)] &&
                this['showMoreCurrenciesSellValue']() &&
                this[_0x16ff19(0x247)](_0x2a1ad1, _0x596c82, _0x2510a8, _0x85526));
    }),
    (Window_ItemList[_0x22ffed(0x1ad)][_0x22ffed(0x1ee)] = function () {
        const _0x50f255 = _0x22ffed;
        return this[_0x50f255(0x22e)] === Window_ShopSell && Window_ShopSell[_0x50f255(0x1a3)];
    }),
    (Window_ShopSell[_0x22ffed(0x1ad)][_0x22ffed(0x247)] = function (
        _0x543b78,
        _0x39efd6,
        _0x155ff5,
        _0x4f647c
    ) {
        const _0x28af97 = _0x22ffed,
            _0x95fdeb = VisuMZ[_0x28af97(0x25c)][_0x28af97(0x175)][_0x28af97(0x24d)],
            _0x20c8c5 = _0x95fdeb[_0x28af97(0x1da)],
            _0x5c93f8 = _0x20c8c5[_0x28af97(0x1b7)]($gameParty[_0x28af97(0x176)](_0x543b78));
        this[_0x28af97(0x1e0)][_0x28af97(0x213)] = _0x95fdeb[_0x28af97(0x1a5)];
        const _0x375b2a = this[_0x28af97(0x161)](_0x5c93f8);
        _0x4f647c -= _0x375b2a + Window_Base[_0x28af97(0x21b)];
        const _0x9d729e = new Rectangle(_0x39efd6, _0x155ff5, _0x4f647c, this[_0x28af97(0x240)]());
        this[_0x28af97(0x1c6)](_0x543b78, _0x9d729e, !![], 0x1);
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x15b)] = function () {
        const _0x1f886a = _0x22ffed;
        return Math['floor'](this[_0x1f886a(0x171)]() + this[_0x1f886a(0x240)]() * 0x2);
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x171)] = function () {
        const _0x5820a8 = _0x22ffed;
        return Math['floor'](this[_0x5820a8(0x1a6)] - this[_0x5820a8(0x240)]() * 6.5);
    }),
    (Window_ShopNumber['prototype'][_0x22ffed(0x1b1)] = function () {
        const _0x3104ad = _0x22ffed;
        return Math[_0x3104ad(0x1b5)](this[_0x3104ad(0x15b)]() + this[_0x3104ad(0x240)]() * 0x2);
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x24c)] = function () {
        const _0x39a0a3 = _0x22ffed,
            _0x52c2e6 = VisuMZ[_0x39a0a3(0x1bb)][_0x39a0a3(0x1ac)](this[_0x39a0a3(0x185)]);
        let _0x5b247e = this[_0x39a0a3(0x171)]();
        ((_0x5b247e -= this[_0x39a0a3(0x240)]() * _0x52c2e6[_0x39a0a3(0x1e3)]),
            this['drawCategories'](_0x5b247e));
        for (const _0x2a762d of _0x52c2e6) {
            if (_0x39a0a3(0x1d9) !== _0x39a0a3(0x1d9)) {
                let _0x45f440 = [
                    _0x3b59f7[_0x39a0a3(0x1bb)][_0x39a0a3(0x1fd)][_0x39a0a3(0x23d)](this),
                ];
                return (
                    (_0x571b15[_0x39a0a3(0x202)] = !![]),
                    (_0xe9c4c9 = this[_0x39a0a3(0x218)]['item']()),
                    (_0x45f440 = _0x45f440[_0x39a0a3(0x18c)](
                        _0x5b9685['MoreCurrencies']['GetMaxBuysForObj'](_0x175dcc)
                    )),
                    (_0x76c650['_bypassProxy'] = ![]),
                    _0x51fbe7[_0x39a0a3(0x245)](..._0x45f440)
                );
            } else {
                _0x5b247e += this['lineHeight']();
                if (!_0x2a762d) continue;
                this['drawMoreCurrenciesPriceData'](_0x2a762d, _0x5b247e);
            }
        }
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x1af)] = function (_0x2e722d) {
        const _0x496048 = _0x22ffed,
            _0x234057 = this[_0x496048(0x1f0)]();
        let _0x573535 = _0x234057 * 0x2;
        const _0xb4d3ee = this[_0x496048(0x1b3)] - _0x573535 - _0x234057 * 0x3,
            _0x241b23 = _0x573535 + Math[_0x496048(0x23b)](_0xb4d3ee / 0x3),
            _0x28648c = Math['floor']((_0xb4d3ee * 0x2) / 0x3 / 0x3),
            _0x49c6f2 = Math[_0x496048(0x212)](
                this[_0x496048(0x161)](_0x496048(0x231)),
                this[_0x496048(0x161)](_0x496048(0x232))
            );
        (this[_0x496048(0x260)](), this[_0x496048(0x21a)](ColorManager[_0x496048(0x1ef)]()));
        const _0xabc841 = [_0x496048(0x1cd), 'shift', _0x496048(0x167)];
        for (let _0x192792 = 0x0; _0x192792 < 0x3; _0x192792++) {
            if (_0x496048(0x205) !== 'XHhmH') {
                const _0x33015d = _0xabc841[_0x192792],
                    _0x2cdb97 = TextManager['MoreCurrenciesNumberWindow'][_0x33015d];
                this[_0x496048(0x1bc)](
                    _0x2cdb97,
                    _0x241b23 + _0x28648c * _0x192792 + _0x49c6f2,
                    _0x2e722d,
                    _0x28648c - _0x49c6f2,
                    _0x496048(0x1a4)
                );
            } else {
                const _0x518bd3 = _0x5a75cf[_0x496048(0x1bb)][_0x496048(0x201)](
                    _0x23f52f,
                    _0xd09a42,
                    _0x237a2d,
                    _0x4e9fe7
                );
                if (_0x518bd3) _0x2fec60 = _0x168cd7[_0x496048(0x18c)](_0x518bd3);
            }
        }
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['drawMoreCurrenciesMathMarks'] = function (
        _0x5eddcd,
        _0x5499f7
    ) {
        const _0x34523d = _0x22ffed,
            _0x5c4c04 = this['itemPadding']();
        let _0x323779 = _0x5c4c04 * 0x2;
        const _0xfbb53d = this[_0x34523d(0x1b3)] - _0x323779 - _0x5c4c04 * 0x3,
            _0x37eb38 = _0x323779 + Math[_0x34523d(0x23b)](_0xfbb53d / 0x3),
            _0xe1508e = Math[_0x34523d(0x1b5)]((_0xfbb53d * 0x2) / 0x3 / 0x3);
        ((_0x5499f7 = _0x34523d(0x188)['format'](_0x5499f7)),
            this[_0x34523d(0x1bc)](
                _0x5499f7,
                _0x37eb38 + _0xe1508e * 0x1,
                _0x5eddcd,
                _0xe1508e,
                _0x34523d(0x187)
            ),
            this[_0x34523d(0x1bc)](
                '\x20=',
                _0x37eb38 + _0xe1508e * 0x2,
                _0x5eddcd,
                _0xe1508e,
                'left'
            ));
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['drawMoreCurrenciesPriceData'] = function (
        _0x438564,
        _0x256144
    ) {
        const _0x2eaf74 = _0x22ffed,
            _0x58e210 = _0x438564[0x0];
        this['resetFontSettings']();
        const _0x5afb2f = SceneManager['_scene']['_commandWindow'][_0x2eaf74(0x22a)](),
            _0x3952b8 = _0x5afb2f === _0x2eaf74(0x1f1);
        this[_0x2eaf74(0x1ed)](_0x256144, _0x3952b8 ? '-' : '+');
        if (_0x58e210 === 'gold') this[_0x2eaf74(0x1dc)](_0x438564, _0x256144, _0x3952b8);
        else
            _0x58e210 === 'variable'
                ? this[_0x2eaf74(0x16c)](_0x438564, _0x256144, _0x3952b8)
                : this['drawMoreCurrenciesItem'](_0x438564, _0x256144, _0x3952b8);
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x19d)] = function () {
        return !![];
    }),
    (Window_ShopNumber['prototype']['visualGoldDisplayNoCost'] = function () {
        return ![];
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['drawMoreCurrenciesGold'] = function (
        _0x2bf3e8,
        _0x2b8f01,
        _0x49c982
    ) {
        const _0x1ec351 = _0x22ffed,
            _0x54441b = this[_0x1ec351(0x1f0)]();
        let _0x4b02a4 = _0x54441b * 0x2;
        const _0x5c6497 = this[_0x1ec351(0x1b3)] - _0x4b02a4 - _0x54441b * 0x3,
            _0xde872d = _0x4b02a4 + Math['ceil'](_0x5c6497 / 0x3),
            _0x27fb73 = Math['floor']((_0x5c6497 * 0x2) / 0x3 / 0x3),
            _0x37f670 = Math['max'](
                this[_0x1ec351(0x161)](_0x1ec351(0x231)),
                this[_0x1ec351(0x161)](_0x1ec351(0x232))
            ),
            _0x1f225a = _0x2bf3e8[0x0],
            _0xa216a1 = _0x2bf3e8[0x1],
            _0x360fd3 = _0xa216a1 * this['_number'],
            _0x41a2ab =
                VisuMZ[_0x1ec351(0x25f)][_0x1ec351(0x175)][_0x1ec351(0x1cf)][_0x1ec351(0x1d8)];
        if (_0x41a2ab > 0x0) {
            if ('xXxXT' !== _0x1ec351(0x163)) {
                const _0x54e9b7 = [this[_0x1ec351(0x185)], 0x1],
                    _0x534c2a = this[_0x1ec351(0x15b)](),
                    _0x10b37b = _0x1cba43[_0x1ec351(0x207)][_0x1ec351(0x215)]['currentSymbol'](),
                    _0x311f2b = _0x10b37b === _0x1ec351(0x1f1);
                this[_0x1ec351(0x1fc)](_0x54e9b7, _0x534c2a, !_0x311f2b);
                const _0x58641b = _0x311f2b ? '+' : '-';
                this[_0x1ec351(0x1ed)](_0x534c2a, _0x58641b);
            } else {
                const _0x3c903d =
                    _0x2b8f01 + (this['lineHeight']() - ImageManager[_0x1ec351(0x17f)]) / 0x2;
                this[_0x1ec351(0x1a9)](_0x41a2ab, _0x4b02a4, _0x3c903d);
                const _0x40bd00 = ImageManager['iconWidth'] + 0x4;
                _0x4b02a4 += _0x40bd00;
            }
        }
        (this[_0x1ec351(0x21a)](ColorManager[_0x1ec351(0x1ef)]()),
            this['drawText'](
                TextManager[_0x1ec351(0x1be)],
                _0x4b02a4,
                _0x2b8f01,
                _0x27fb73,
                'left'
            ));
        const _0x16e302 = $gameParty[_0x1ec351(0x1f8)]();
        this['drawCurrencyValue'](
            _0x16e302,
            TextManager[_0x1ec351(0x1be)],
            _0xde872d,
            _0x2b8f01,
            _0x27fb73
        );
        const _0x397f94 = _0xde872d + _0x27fb73 * 0x1 + _0x37f670,
            _0x2baf08 = _0x27fb73 - _0x37f670;
        this[_0x1ec351(0x20e)](
            _0x360fd3,
            TextManager[_0x1ec351(0x1be)],
            _0x397f94,
            _0x2b8f01,
            _0x2baf08
        );
        const _0xfed28a = _0xde872d + _0x27fb73 * 0x2 + _0x37f670,
            _0x57505c = _0x27fb73 - _0x37f670,
            _0xa6bfac = Math['min'](
                _0x16e302 + _0x360fd3 * (_0x49c982 ? -0x1 : 0x1),
                $gameParty['maxGold']()
            );
        this[_0x1ec351(0x20e)](
            _0xa6bfac,
            TextManager[_0x1ec351(0x1be)],
            _0xfed28a,
            _0x2b8f01,
            _0x57505c
        );
    }),
    (Window_ShopNumber['prototype']['drawMoreCurrenciesVariable'] = function (
        _0x39963f,
        _0x1cd93d,
        _0x293855
    ) {
        const _0x2ed7fa = _0x22ffed,
            _0x5c8c18 = this['itemPadding']();
        let _0x6427ee = _0x5c8c18 * 0x2;
        const _0x738ddf = this[_0x2ed7fa(0x1b3)] - _0x6427ee - _0x5c8c18 * 0x3,
            _0xe79b7c = _0x6427ee + Math[_0x2ed7fa(0x23b)](_0x738ddf / 0x3),
            _0x2a49ba = Math['floor']((_0x738ddf * 0x2) / 0x3 / 0x3),
            _0x300fce = Math[_0x2ed7fa(0x212)](
                this['textWidth']('\x20+\x20'),
                this[_0x2ed7fa(0x161)](_0x2ed7fa(0x232))
            ),
            _0x19bf80 = _0x39963f[0x0],
            _0x181c49 = _0x39963f[0x1],
            _0x103605 = _0x39963f[0x2],
            _0x1b80c7 = _0x181c49 * this[_0x2ed7fa(0x25a)];
        let _0x558a8d = 0x0;
        const _0x41b586 = $dataSystem[_0x2ed7fa(0x261)][_0x103605];
        _0x41b586[_0x2ed7fa(0x1c4)](/\\I\[(\d+)\]/i) && (_0x558a8d = Number(RegExp['$1']));
        const _0x1e6aad = _0x558a8d > 0x0 ? ImageManager[_0x2ed7fa(0x217)] + 0x4 : 0x0;
        this[_0x2ed7fa(0x1cc)](_0x41b586, _0x6427ee, _0x1cd93d, _0x738ddf, _0x2ed7fa(0x187));
        const _0x15de13 = _0xe79b7c + _0x2a49ba * 0x0,
            _0x4224fb = _0x2a49ba - _0x1e6aad,
            _0x30e8f8 = $gameVariables['value'](_0x103605);
        (this[_0x2ed7fa(0x1bc)](_0x30e8f8, _0x15de13, _0x1cd93d, _0x4224fb, _0x2ed7fa(0x208)),
            this[_0x2ed7fa(0x1a9)](_0x558a8d, _0x15de13 + _0x4224fb + 0x4, _0x1cd93d));
        const _0x3d75ab = _0xe79b7c + _0x2a49ba * 0x1 + _0x300fce,
            _0x576fe5 = _0x2a49ba - _0x300fce - _0x1e6aad;
        (this[_0x2ed7fa(0x1bc)](_0x1b80c7, _0x3d75ab, _0x1cd93d, _0x576fe5, _0x2ed7fa(0x208)),
            this[_0x2ed7fa(0x1a9)](_0x558a8d, _0x3d75ab + _0x576fe5 + 0x4, _0x1cd93d));
        const _0x5bdcd0 = _0xe79b7c + _0x2a49ba * 0x2 + _0x300fce,
            _0x9a0188 = _0x2a49ba - _0x300fce - _0x1e6aad,
            _0x18cd3b = _0x30e8f8 + _0x1b80c7 * (_0x293855 ? -0x1 : 0x1);
        (this['drawText'](_0x18cd3b, _0x5bdcd0, _0x1cd93d, _0x9a0188, _0x2ed7fa(0x208)),
            this[_0x2ed7fa(0x1a9)](_0x558a8d, _0x5bdcd0 + _0x9a0188 + 0x4, _0x1cd93d));
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['drawMoreCurrenciesItem'] = function (
        _0xdab4e3,
        _0x5413c9,
        _0x4492ba
    ) {
        const _0x22301a = _0x22ffed,
            _0x269bfa = this[_0x22301a(0x1f0)]();
        let _0x436149 = _0x269bfa * 0x2;
        const _0x7ab74 = this[_0x22301a(0x1b3)] - _0x436149 - _0x269bfa * 0x3,
            _0xffd849 = _0x436149 + Math['ceil'](_0x7ab74 / 0x3),
            _0x16c708 = Math[_0x22301a(0x1b5)]((_0x7ab74 * 0x2) / 0x3 / 0x3),
            _0x323a49 = Math[_0x22301a(0x212)](
                this[_0x22301a(0x161)](_0x22301a(0x231)),
                this[_0x22301a(0x161)]('\x20=\x20')
            ),
            _0x1b58db = _0xdab4e3[0x0],
            _0x1f8004 = _0xdab4e3[0x1],
            _0x3a4e9a = _0x1f8004 * this[_0x22301a(0x25a)];
        let _0x5c0644 = _0x1b58db[_0x22301a(0x1fa)];
        const _0x112c84 = _0x5c0644 > 0x0 ? ImageManager[_0x22301a(0x217)] + 0x4 : 0x0;
        this[_0x22301a(0x1f3)](_0x1b58db, _0x436149, _0x5413c9, _0x7ab74);
        const _0x554df1 = _0xffd849 + _0x16c708 * 0x0,
            _0x185c4a = _0x16c708 - _0x112c84,
            _0x588a57 = $gameParty[_0x22301a(0x23c)](_0x1b58db);
        (this[_0x22301a(0x1bc)](_0x588a57, _0x554df1, _0x5413c9, _0x185c4a, 'right'),
            this['drawIcon'](_0x5c0644, _0x554df1 + _0x185c4a + 0x4, _0x5413c9));
        const _0x586085 = _0xffd849 + _0x16c708 * 0x1 + _0x323a49,
            _0xcb64b0 = _0x16c708 - _0x323a49 - _0x112c84;
        (this[_0x22301a(0x1bc)](_0x3a4e9a, _0x586085, _0x5413c9, _0xcb64b0, _0x22301a(0x208)),
            this[_0x22301a(0x1a9)](_0x5c0644, _0x586085 + _0xcb64b0 + 0x4, _0x5413c9));
        const _0x420e3a = _0xffd849 + _0x16c708 * 0x2 + _0x323a49,
            _0x1ce9cd = _0x16c708 - _0x323a49 - _0x112c84,
            _0x5963b9 = _0x588a57 + _0x3a4e9a * (_0x4492ba ? -0x1 : 0x1);
        (this[_0x22301a(0x1bc)](_0x5963b9, _0x420e3a, _0x5413c9, _0x1ce9cd, _0x22301a(0x208)),
            this['drawIcon'](_0x5c0644, _0x420e3a + _0x1ce9cd + 0x4, _0x5413c9));
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x194)] = function () {
        const _0x143ebb = _0x22ffed,
            _0x12a897 = [this[_0x143ebb(0x185)], 0x1],
            _0x30e562 = this[_0x143ebb(0x15b)](),
            _0x1e5e18 = SceneManager[_0x143ebb(0x207)][_0x143ebb(0x215)]['currentSymbol'](),
            _0x2c8d5a = _0x1e5e18 === _0x143ebb(0x1f1);
        this[_0x143ebb(0x1fc)](_0x12a897, _0x30e562, !_0x2c8d5a);
        const _0x9e8e62 = _0x2c8d5a ? '+' : '-';
        this[_0x143ebb(0x1ed)](_0x30e562, _0x9e8e62);
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['drawMultiplicationSign'] = function () {}),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x216)] = function () {}),
    (Window_ShopNumber[_0x22ffed(0x1ad)][_0x22ffed(0x21c)] = function () {
        const _0xd08eaa = _0x22ffed;
        if (!this['_item']) return 0x1;
        let _0xf8e212 = String($gameParty[_0xd08eaa(0x176)](this[_0xd08eaa(0x185)]));
        if (this[_0xd08eaa(0x197)]()) {
            if (_0xd08eaa(0x24e) === 'ajmzi') return this[_0xd08eaa(0x1d7)]['items'];
            else _0xf8e212 = VisuMZ[_0xd08eaa(0x22c)](_0xf8e212);
        }
        return _0xf8e212[_0xd08eaa(0x1e3)];
    }),
    (Window_ShopNumber[_0x22ffed(0x1ad)]['itemRect'] = function () {
        const _0xc6c90 = _0x22ffed,
            _0x75139a = this[_0xc6c90(0x1f0)]();
        let _0x13acf4 = _0x75139a * 0x2;
        const _0x31b1ad = this[_0xc6c90(0x1b3)] - _0x13acf4 - _0x75139a * 0x3,
            _0x10500c = _0x13acf4 + Math[_0xc6c90(0x23b)](_0x31b1ad / 0x3),
            _0x32b924 = this[_0xc6c90(0x15b)](),
            _0x3654f9 = Math[_0xc6c90(0x1b5)]((_0x31b1ad * 0x2) / 0x3 / 0x3),
            _0x28f659 = Math[_0xc6c90(0x212)](
                this[_0xc6c90(0x161)](_0xc6c90(0x231)),
                this['textWidth']('\x20=\x20')
            ),
            _0x1f2100 =
                this[_0xc6c90(0x185)]?.['iconIndex'] > 0x0 ? ImageManager[_0xc6c90(0x217)] : 0x0,
            _0x37d3b4 = this[_0xc6c90(0x1dd)](),
            _0x533437 = new Rectangle(
                Math[_0xc6c90(0x1b5)](
                    _0x10500c +
                        _0x3654f9 * 0x2 -
                        this[_0xc6c90(0x1dd)]() -
                        _0x1f2100 +
                        this[_0xc6c90(0x1f0)]() / 0x2 -
                        0x2
                ),
                _0x32b924,
                this[_0xc6c90(0x1dd)](),
                this[_0xc6c90(0x240)]()
            );
        return _0x533437;
    }),
    (VisuMZ[_0x22ffed(0x1bb)]['MakeShopNumberIngredients'] = function (_0x115559) {
        const _0x7bb23f = _0x22ffed;
        let _0xacbf0b = [];
        const _0x1c331b =
            SceneManager[_0x7bb23f(0x207)][_0x7bb23f(0x215)][_0x7bb23f(0x22a)]() ===
            _0x7bb23f(0x1ab);
        for (const _0x502ce8 of Window_Base[_0x7bb23f(0x20b)]) {
            const _0x5c2840 = this[_0x7bb23f(0x246)](_0x115559, _0x502ce8, _0x1c331b);
            if (
                _0x502ce8 === _0x7bb23f(0x1f8) &&
                SceneManager[_0x7bb23f(0x207)][_0x7bb23f(0x1ba)][_0x7bb23f(0x1e9)] <= 0x0
            )
                continue;
            if (_0x5c2840) _0xacbf0b = _0xacbf0b[_0x7bb23f(0x18c)](_0x5c2840);
        }
        return (
            _0xacbf0b[_0x7bb23f(0x1e3)] === 0x0 &&
                _0xacbf0b[_0x7bb23f(0x1e8)]([_0x7bb23f(0x1f8), 0x0]),
            _0xacbf0b
        );
    }),
    (VisuMZ['MoreCurrencies'][_0x22ffed(0x246)] = function (_0x1660f0, _0x208d3f, _0x3521cd) {
        const _0x1102a1 = _0x22ffed;
        _0x208d3f = _0x208d3f[_0x1102a1(0x235)]()[_0x1102a1(0x1d4)]();
        switch (_0x208d3f) {
            case _0x1102a1(0x177):
            case _0x1102a1(0x182):
            case _0x1102a1(0x1de):
                return this['GetShopNumberIngredientItems'](_0x1660f0, _0x208d3f, _0x3521cd);
            case _0x1102a1(0x1b9):
                return this[_0x1102a1(0x22f)](_0x1660f0, _0x208d3f, _0x3521cd);
            case 'gold':
                return [this[_0x1102a1(0x236)]()];
        }
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x1ca)] = function (_0x2d39cc, _0x34b1cd, _0xadad3) {
        const _0x373ef4 = _0x22ffed,
            _0x212b2b = DataManager[_0x373ef4(0x1a8)](_0x2d39cc),
            _0x5743dc = _0x212b2b[_0x2d39cc['id']];
        if (!_0x5743dc) return [];
        const _0x2af990 = _0xadad3 ? _0x373ef4(0x1ab) : 'buy',
            _0x194442 = '%1%2Costs'[_0x373ef4(0x1b7)](
                _0x2af990,
                _0x34b1cd[_0x373ef4(0x220)](0x0)[_0x373ef4(0x1d1)]() +
                    _0x34b1cd[_0x373ef4(0x21e)](0x1)
            );
        if (!_0x5743dc[_0x194442]) return [];
        let _0x7092b8 = [];
        if (_0x34b1cd === 'item') _0x7092b8 = $dataItems;
        if (_0x34b1cd === _0x373ef4(0x182)) _0x7092b8 = $dataWeapons;
        if (_0x34b1cd === 'armor') _0x7092b8 = $dataArmors;
        const _0x402176 = [];
        for (const _0x42cce4 in _0x5743dc[_0x194442]) {
            const _0xeb0f23 = Number(_0x42cce4),
                _0x1e06b7 = _0x7092b8[_0xeb0f23];
            if (!_0x1e06b7) continue;
            const _0x2b8bba = [_0x1e06b7];
            (_0x2b8bba[_0x373ef4(0x1e8)](_0x5743dc[_0x194442][_0x42cce4]),
                _0x402176[_0x373ef4(0x1e8)](_0x2b8bba));
        }
        return _0x402176;
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x22f)] = function (_0x1e720d, _0x318843, _0x4908b8) {
        const _0x5d2384 = _0x22ffed,
            _0x373296 = DataManager[_0x5d2384(0x1a8)](_0x1e720d),
            _0xe8dc74 = _0x373296[_0x1e720d['id']];
        if (!_0xe8dc74) return [];
        const _0x39c916 = _0x4908b8 ? _0x5d2384(0x1ab) : _0x5d2384(0x1f1),
            _0x5e5ef1 = _0x5d2384(0x174)[_0x5d2384(0x1b7)](
                _0x39c916,
                _0x318843[_0x5d2384(0x220)](0x0)[_0x5d2384(0x1d1)]() + _0x318843['slice'](0x1)
            );
        if (!_0xe8dc74[_0x5e5ef1]) return [];
        const _0x4a32a5 = [];
        for (const _0x291f6b in _0xe8dc74[_0x5e5ef1]) {
            const _0x4aad6d = Number(_0x291f6b);
            if ($dataSystem['variables'][_0x5d2384(0x1e3)] <= _0x4aad6d) continue;
            const _0x5c2a56 = ['variable'];
            (_0x5c2a56['push'](_0xe8dc74[_0x5e5ef1][_0x291f6b]),
                _0x5c2a56[_0x5d2384(0x1e8)](_0x4aad6d),
                _0x4a32a5[_0x5d2384(0x1e8)](_0x5c2a56));
        }
        return _0x4a32a5;
    }),
    (VisuMZ[_0x22ffed(0x1bb)][_0x22ffed(0x236)] = function () {
        const _0x149660 = _0x22ffed,
            _0x1c002e = SceneManager[_0x149660(0x207)][_0x149660(0x1ba)]['_price'];
        return [_0x149660(0x1f8), _0x1c002e];
    }));
function _0x59f0() {
    const _0x47ad6e = [
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        '_moreCurrencyCosts',
        'GoldIcon',
        'mXszw',
        'ItemQuantityFmt',
        'ITEM',
        'drawMoreCurrenciesGold',
        'cursorWidth',
        'armor',
        'ItemBuyFmt',
        'contents',
        'ARRAYEVAL',
        'ParseNotetagCosts',
        'length',
        'cRQCq',
        'ARRAYSTR',
        'EVAL',
        'buyWeaponCosts',
        'push',
        '_price',
        'gainItem',
        'VARIABLE',
        'VisuMZ_1_ItemsEquipsCore\x20needs\x20to\x20be\x20updated\x20',
        'drawMoreCurrenciesMathMarks',
        'showMoreCurrenciesSellValue',
        'systemColor',
        'itemPadding',
        'buy',
        'RDvup',
        'drawItemName',
        'BuyFontSize',
        'WBysF',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'Ywing',
        'gold',
        '_itemIDs',
        'iconIndex',
        'NtfYG',
        'drawMoreCurrenciesItem',
        'Scene_Shop_maxBuy',
        'ARRAYNUM',
        'textSizeEx',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'CreateSubCostText',
        '_bypassProxy',
        'width',
        'mainFontSize',
        'rSbyw',
        'Scene_Boot_onDatabaseLoaded',
        '_scene',
        'right',
        'items',
        'PIYFE',
        'MORE_CURRENCIES_ORDER',
        'NumWindowOwned',
        '3283016SDoDkG',
        'drawCurrencyValue',
        'VisuMZ_3_VisualGoldDisplay',
        'WEAPON',
        'hsMvi',
        'max',
        'fontSize',
        'getArmorIdWithName',
        '_commandWindow',
        'drawNumber',
        'iconWidth',
        '_buyWindow',
        '_armorIDs',
        'changeTextColor',
        'MORE_CURRENCIES_PADDING',
        'maxDigits',
        '4188365GdqQai',
        'slice',
        'ParseArmorNotetags',
        'charAt',
        'BnQht',
        'Scene_Shop_doBuy',
        'round',
        'description',
        '7TxFxql',
        'visualGoldDisplayNoCost',
        'UqcwU',
        'isEnabled',
        'RegExp',
        'currentSymbol',
        'isWeapon',
        'GroupDigits',
        'process_VisuMZ_MoreCurrencies',
        'constructor',
        'GetShopNumberIngredientVariables',
        'SubBuyCost',
        '\x20+\x20',
        '\x20=\x20',
        'ParseItemNotetags',
        '%1%2%3',
        'toLowerCase',
        'GetShopNumberIngredientGold',
        'Window_ShopBuy_isEnabled',
        '276LLmSja',
        'Owned',
        'ArmorsBuyFmt',
        'ceil',
        'numItems',
        'call',
        '\x5cI[%1]',
        'VisualGoldDisplay',
        'lineHeight',
        'drawItemNumber',
        'etBNH',
        'kwiyv',
        'name',
        'min',
        'GetShopNumberIngredientType',
        'drawSellPrice',
        'ChangeQuantityForObj',
        'STRUCT',
        'GetMaxBuysForObj',
        'JSON',
        'drawTotalPrice',
        'ItemScene',
        'yyGpN',
        'onDatabaseLoaded',
        'armors',
        'JCsPq',
        'getWeaponIdWithName',
        'weapons',
        'in\x20order\x20for\x20VisuMZ_3_OneTimePurchase\x20to\x20work.',
        'nsOum',
        'prepareMoreCurrenciesObj',
        'exit',
        '2QUnVpu',
        'test',
        '_number',
        'ZiFJV',
        'ItemsEquipsCore',
        'MORE_CURRENCIES_DEFAULT_SELL_RATE',
        'remove',
        'CoreEngine',
        'resetFontSettings',
        'variables',
        'Scene_Shop_doSell',
        'itemNameY',
        'visualGoldDisplayPadding',
        'reverse',
        'value',
        'fSXya',
        'drawItemCost',
        'textWidth',
        'ConvertParams',
        'xXxXT',
        'CheckMeetBuyRequirements',
        'isArmor',
        'note',
        'net',
        'TdXhC',
        'buyItemCosts',
        'filter',
        'CreateSubVariableCostTexts',
        'drawMoreCurrenciesVariable',
        '\x5cFS[%1]',
        'isItem',
        'hDEDo',
        '73064iMQToA',
        'totalPriceY',
        'AutoSellRate',
        'ParseNotetagLineSubCosts',
        '%1%2Costs',
        'Settings',
        'maxItems',
        'item',
        'version',
        'includes',
        'replace',
        'ShowSell',
        'CreateSubItemCostTexts',
        '_weaponIDs',
        'sellArmorCosts',
        'iconHeight',
        'JNOBZ',
        'VariableBuyFmt',
        'weapon',
        'buyVariableCosts',
        'WeaponBuyFmt',
        '_item',
        'MoreCurrenciesFmt',
        'left',
        '\x20%1',
        'parse',
        'STR',
        'sellPriceOfItem',
        'concat',
        '29592OuLhQq',
        'getItemIdWithName',
        'ParseWeaponNotetags',
        'tcMVJ',
        '3587586TtkWOk',
        '199930skoekV',
        'MoreCurrenciesFontSize',
        'drawCurrentItemName',
        'CreateSubGoldCostText',
        'sPgTh',
        'useDigitGrouping',
        'sellItemCosts',
        'VisuMZ_1_ItemsEquipsCore',
        'sellVariableCosts',
        'MoreCurrenciesNumberWindow',
        'kzazk',
        'visualGoldDisplayAutosize',
        'CreateVisualGoldText',
        'CreateGoldCostText',
        'ARRAYSTRUCT',
        'ParseAllNotetags',
        'MZgpC',
        'MORE_CURRENCIES_SHOW_SELL_VALUE',
        'center',
        'ItemQuantityFontSize',
        'innerHeight',
        '7523252YUfUPa',
        'getMoreCurrenciesObjLibrary',
        'drawIcon',
        'NumWindowNet',
        'sell',
        'MakeShopNumberIngredients',
        'prototype',
        'sellWeaponCosts',
        'drawCategories',
        'ARMOR',
        'buttonY',
        'buyArmorCosts',
        'innerWidth',
        'Window_ItemList_drawItemNumber',
        'floor',
        'XHjgC',
        'format',
        'GeKSL',
        'variable',
        '_numberWindow',
        'MoreCurrencies',
        'drawText',
        '18TnvTVa',
        'currencyUnit',
        'maxBuy',
        'doBuy',
        'Htnei',
        'ParseNotetagSubCosts',
        'return\x200',
        'match',
        'ARRAYFUNC',
        'drawItemMoreCurrencies',
        'SubSellCost',
        'Listing',
        'General',
        'GetShopNumberIngredientItems',
        'ListOrder',
        'drawTextEx',
        'owned',
        'Window_ShopBuy_drawItemCost',
        'Gold',
        'aqVZu',
        'toUpperCase',
        'doSell',
        'KlfIc',
        'trim',
        'map',
    ];
    _0x59f0 = function () {
        return _0x47ad6e;
    };
    return _0x59f0();
}
