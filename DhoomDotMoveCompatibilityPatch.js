//=============================================================================
// DhoomDotMoveCompatibilityPatch.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_DotMoveCompatibilityPatch = '1.4';

var Dhoom = Dhoom || {};
Dhoom.DotMoveCompatibilityPatch = Dhoom.DotMoveCompatibilityPatch || {};
/*:
 * @plugindesc Dhoom DotMoveCompatibilityPatch v1.4 - 22/10/2024
 * <AUTHOR> - drd-workshop.blogspot.com | DrDhoom#8315
 * @url drd-workshop.blogspot.com
 *
 * @help Compatibility fixes for DotMoveSystem, VisuMZ_EventsMoveCore and
 * VisuMZ_4_EncounterEffects.
 *
 * Place DotMoveSystem below VisuMZ_EventsMoveCore, and this plugin below
 * DotMoveSystem.
 */

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom DotMoveCompatibilityPatch/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.DotMoveCompatibilityPatch.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.DotMoveCompatibilityPatch[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.DotMoveCompatibilityPatch.loadParameters();

if (Imported.VisuMZ_1_EventsMoveCore) {
    DotMoveSystem.DotMoveUtils.direction2Axis = function (direction4) {
        if ([1, 3, 7, 9].contains(direction4)) {
            console.log('direction2Axis has diagonal direction! ' + direction4);
            console.log(new Error().stack);
            const { horz, vert } = DotMoveSystem.DotMoveUtils.direction2HorzAndVert(direction4);
            direction4 = Math.randomInt(2) === 0 ? horz : vert;
        }
        if (direction4 === 4 || direction4 === 6) {
            return 'x';
        } else if (direction4 === 8 || direction4 === 2) {
            return 'y';
        } else {
            throw new Error(`${direction4} is not found`);
        }
    };

    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // Game_CharacterBase
    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_moveStraight =
        Game_CharacterBase.prototype.moveStraight;
    Game_CharacterBase.prototype.moveStraight = function (direction) {
        if (!direction) return;
        Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_moveStraight.call(this, direction);
    };

    Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_moveDiagonally =
        Game_CharacterBase.prototype.moveDiagonally;
    Game_CharacterBase.prototype.moveDiagonally = function (horz, vert) {
        Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_moveDiagonally.call(this, horz, vert);
        if (this.isSpriteVS8dir()) {
            this.setDiagonalDirection(horz, vert);
        }
    };

    Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_isCollidedWithCharacters =
        Game_CharacterBase.prototype.isCollidedWithCharacters;
    Game_CharacterBase.prototype.isCollidedWithCharacters = function (x, y, d = this.direction()) {
        if ([1, 3, 7, 9].contains(d)) {
            const { horz, vert } = DotMoveSystem.DotMoveUtils.direction2HorzAndVert(d);
            if (
                Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_isCollidedWithCharacters.call(
                    this,
                    x,
                    y,
                    horz
                )
            )
                return true;
            return Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_isCollidedWithCharacters.call(
                this,
                x,
                y,
                vert
            );
        } else {
            return Dhoom.DotMoveCompatibilityPatch.Game_CharacterBase_isCollidedWithCharacters.call(
                this,
                x,
                y,
                d
            );
        }
    };

    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // Game_Character
    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    Dhoom.DotMoveCompatibilityPatch.Game_Character_dotMoveByDeg =
        Game_Character.prototype.dotMoveByDeg;
    Game_Character.prototype.dotMoveByDeg = function (deg) {
        Dhoom.DotMoveCompatibilityPatch.Game_Character_dotMoveByDeg.call(this, deg);
        if (this.isSpriteVS8dir()) {
            let dir = [8, 9, 6, 3, 2, 1, 4, 7, 8][Math.round(deg / 45)];
            if (dir) {
                let horz = 0;
                if ([1, 4, 7].contains(dir)) horz = 4;
                if ([3, 6, 9].contains(dir)) horz = 6;
                let vert = 0;
                if ([1, 2, 3].contains(dir)) vert = 2;
                if ([7, 8, 9].contains(dir)) vert = 8;
                this.setDiagonalDirection(horz, vert);
            }
        }
    };

    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // Game_Event
    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    Dhoom.DotMoveCompatibilityPatch.Game_Event_setupEventsMoveCoreEffects =
        Game_Event.prototype.setupEventsMoveCoreEffects;
    Game_Event.prototype.setupEventsMoveCoreEffects = function (notes) {
        Dhoom.DotMoveCompatibilityPatch.Game_Event_setupEventsMoveCoreEffects.call(this, notes);
        this.processDotMoveEventsMoveCoreHitbox();
    };

    Game_Event.prototype.processDotMoveEventsMoveCoreHitbox = function () {
        if (this._collisionRealXOffset) {
            this._realX += this._collisionRealXOffset;
            this._collisionRealXOffset = undefined;
        }
        if (this._collisionRealYOffset) {
            this._realY += this._collisionRealYOffset;
            this._collisionRealYOffset = undefined;
        }
        let width = 1;
        width += this._addedHitbox.left;
        width += this._addedHitbox.right;
        let height = 1;
        height += this._addedHitbox.up;
        height += this._addedHitbox.down;
        if (width > 1) {
            this.setWidth(width);
            if (this._addedHitbox.left) {
                this.setOffsetX(this._addedHitbox.left);
                this._collisionRealXOffset = this._addedHitbox.left;
                this._realX -= this._addedHitbox.left;
            }
        }
        if (height > 1) {
            this.setHeight(height);
            if (this._addedHitbox.up) {
                this.setOffsetY(this._addedHitbox.up);
                this._realY -= this._addedHitbox.up;
                this._collisionRealYOffset = this._addedHitbox.up;
            }
        }
    };

    if (Imported.VisuMZ_4_EncounterEffects) {
        Dhoom.DotMoveCompatibilityPatch.Game_Event_checkEventTriggerTouchFront =
            Game_Event.prototype.checkEventTriggerTouchFront;
        Game_Event.prototype.checkEventTriggerTouchFront = function (d) {
            Dhoom.DotMoveCompatibilityPatch.Game_Event_checkEventTriggerTouchFront.call(this, d);
            this.checkEventFollowerTriggerTouchDot(d);
        };

        Game_Event.prototype.checkEventFollowerTriggerTouchDot = function (d) {
            if (
                this._EncounterEffectsFollowerTrigger &&
                !$gameMap.isEventRunning() &&
                this._trigger === 2 &&
                !this.isJumping() &&
                this.isNormalPriority()
            ) {
                const axis = DotMoveSystem.DotMoveUtils.direction2Axis(this._direction);
                const axisLen = this.distancePerFrame() * 0.75;
                const otherAxis = axis === 'y' ? 'x' : 'y';
                const followers = $gamePlayer._followers._data;
                const eventWidthOrHeightArea =
                    otherAxis === 'x' ? this.widthArea() : this.heightArea();
                for (let follower of followers) {
                    let result = this.mover().checkCharacterStepDir(
                        this._realX,
                        this._realY,
                        d,
                        follower
                    );
                    if (result) {
                        let minTouchWidthOrHeight =
                            otherAxis === 'x'
                                ? follower.minTouchWidth()
                                : follower.minTouchHeight();
                        let widthOrHeightArea = Math.min(
                            minTouchWidthOrHeight,
                            eventWidthOrHeightArea
                        );
                        if (
                            result.getCollisionLength(otherAxis) >= widthOrHeightArea &&
                            result.getCollisionLength(axis) >= axisLen
                        ) {
                            this.dotMoveTempData().eventTouchToPlayer = true;
                            this.start();
                            break;
                        }
                    }
                }
            }
        };
    }

    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // Game_Player
    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    Dhoom.DotMoveCompatibilityPatch.Game_Player_executeMove = Game_Player.prototype.executeMove;
    Game_Player.prototype.executeMove = function (direction) {
        Dhoom.DotMoveCompatibilityPatch.Game_Player_executeMove.call(this, direction);
        if (this.isSpriteVS8dir()) {
            let horz = 0;
            if ([1, 4, 7].contains(direction)) horz = 4;
            if ([3, 6, 9].contains(direction)) horz = 6;
            let vert = 0;
            if ([1, 2, 3].contains(direction)) vert = 2;
            if ([7, 8, 9].contains(direction)) vert = 8;
            this.setDiagonalDirection(horz, vert);
        }
    };

    Dhoom.DotMoveCompatibilityPatch.Game_Player_startMapEventFront =
        Game_Player.prototype.startMapEventFront;
    Game_Player.prototype.startMapEventFront = function (x, y, d, triggers, normal, isTouch) {
        if (d % 2 !== 0) {
            this.startMapEventDiagonal(x, y, d, triggers, normal, isTouch);
            return;
        }
        Dhoom.DotMoveCompatibilityPatch.Game_Player_startMapEventFront.call(
            this,
            x,
            y,
            d,
            triggers,
            normal,
            isTouch
        );
    };

    Game_Player.prototype.startMapEventDiagonal = function (x, y, d, triggers, normal, isTouch) {
        if ($gameMap.isEventRunning()) return;
        if (isTouch && (this.isThrough() || this.isDebugThrough())) return;
        for (const result of this.mover().checkHitCharactersStepDir(x, y, d, Game_Event)) {
            const event = result.targetObject;
            if (event.isTriggerIn(triggers) && event.isNormalPriority() === normal) {
                if (isTouch && event.isThrough()) continue;
                event.start();
            }
        }
    };

    Game_Player.prototype.triggerTouchAction = function () {
        if ($gameTemp.isDestinationValid()) {
            const direction = this.direction();
            const x1 = this.x;
            const y1 = this.y;
            const x2 = $gameMap.roundXWithDirection(x1, direction);
            const y2 = $gameMap.roundYWithDirection(y1, direction);
            const destX = $gameTemp.destinationX();
            const destY = $gameTemp.destinationY();
            if (destX === x1 && destY === y1) {
                return this.triggerTouchActionD1(x1, y1);
            } else if (destX === x2 && destY === y2) {
                return this.triggerTouchActionD2(x2, y2);
            } else {
                if ([1, 3, 7, 9].contains(direction)) {
                    if (destX !== x1 && destY !== y1) return false;
                } else {
                    const axis = DotMoveSystem.DotMoveUtils.direction2Axis(direction);
                    if (axis === 'x') {
                        if (destX !== x1) return false;
                    } else {
                        if (destY !== y1) return false;
                    }
                }
                const dest = new DotMoveSystem.DotMovePoint(destX, destY);
                const prev = DotMoveSystem.DotMoveUtils.prevPointWithDirection(dest, direction);
                return this.triggerTouchActionD3(prev.x, prev.y);
            }
        }
        return false;
    };

    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
    // Game_Event
    //vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv\
    Dhoom.DotMoveCompatibilityPatch.Game_Follower_updateStop = Game_Follower.prototype.updateStop;
    Game_Follower.prototype.updateStop = function () {
        let moving = this._actuallyMoving;
        Dhoom.DotMoveCompatibilityPatch.Game_Follower_updateStop.call(this);
        this._actuallyMoving = moving;
        if (this._stopCount > 8) this._actuallyMoving = false;
    };

    Dhoom.DotMoveCompatibilityPatch.Game_Follower_chaseCharacter =
        Game_Follower.prototype.chaseCharacter;
    Game_Follower.prototype.chaseCharacter = function (character) {
        if (this._chaseOff) return;
        if ($gameSystem.isStopFollowerChasing()) return;
        Dhoom.DotMoveCompatibilityPatch.Game_Follower_chaseCharacter.call(this, character);
        this._actuallyMoving = true;
    };
}
