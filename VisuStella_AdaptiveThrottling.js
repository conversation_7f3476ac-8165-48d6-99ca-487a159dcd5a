/*:
 * @target MZ
 * @plugindesc v2.0.0 VisuStella Adaptive Throttling System
 * <AUTHOR> Performance Optimizer
 * @orderAfter VisuStella_UnifiedPerformanceSystem
 * @orderAfter VisuStella_EnhancedPatches
 * @help VisuStella_AdaptiveThrottling.js
 *
 * ============================================================================
 * VisuStella Adaptive Throttling System
 * ============================================================================
 *
 * This system provides intelligent, dynamic performance scaling that adapts
 * to current game conditions and hardware performance in real-time.
 *
 * Features:
 * - Real-time FPS monitoring and adaptation
 * - Scene-specific optimization profiles
 * - Hardware capability detection
 * - Gradual performance scaling (no sudden changes)
 * - User preference integration
 * - Emergency performance mode for low-end devices
 *
 * @param enableAdaptiveScaling
 * @text Enable Adaptive Scaling
 * @desc Automatically adjust performance based on FPS
 * @type boolean
 * @default true
 *
 * @param targetFPS
 * @text Target FPS
 * @desc Desired frame rate to maintain
 * @type number
 * @min 30
 * @max 60
 * @default 55
 *
 * @param emergencyFPS
 * @text Emergency FPS Threshold
 * @desc Activate emergency mode below this FPS
 * @type number
 * @min 20
 * @max 40
 * @default 25
 *
 * @param adaptationSpeed
 * @text Adaptation Speed
 * @desc How quickly to adapt (1=slow, 5=fast)
 * @type number
 * @min 1
 * @max 5
 * @default 3
 *
 * @param enableSceneProfiles
 * @text Enable Scene Profiles
 * @desc Use different settings for different scenes
 * @type boolean
 * @default true
 */

(() => {
    'use strict';

    const parameters = PluginManager.parameters('VisuStella_AdaptiveThrottling');
    const enableAdaptiveScaling = parameters['enableAdaptiveScaling'] !== 'false';
    const targetFPS = parseInt(parameters['targetFPS']) || 55;
    const emergencyFPS = parseInt(parameters['emergencyFPS']) || 25;
    const adaptationSpeed = parseInt(parameters['adaptationSpeed']) || 3;
    const enableSceneProfiles = parameters['enableSceneProfiles'] !== 'false';

    if (!enableAdaptiveScaling) return;

    // Check for required systems
    if (!window.VisuStellaPerformanceManager) {
        console.error('[VisuStella_AdaptiveThrottling] VisuStella_UnifiedPerformanceSystem not found!');
        return;
    }

    //=============================================================================
    // Adaptive Throttling Manager
    //=============================================================================

    class AdaptiveThrottlingManager {
        constructor() {
            this.initialize();
        }

        initialize() {
            // Performance monitoring
            this.fpsHistory = [];
            this.maxFPSHistory = 300; // 5 seconds at 60fps
            this.currentFPS = 60;
            this.averageFPS = 60;
            this.fpsStability = 100; // Percentage

            // Throttling state
            this.currentProfile = 'normal';
            this.targetProfile = 'normal';
            this.adaptationProgress = 0;
            this.lastAdaptationTime = Date.now();

            // Scene-specific profiles
            this.sceneProfiles = {
                map: {
                    normal: { events: 5, lights: 2, particles: 2, core: 2 },
                    throttled: { events: 8, lights: 3, particles: 3, core: 3 },
                    aggressive: { events: 12, lights: 5, particles: 4, core: 4 },
                    emergency: { events: 20, lights: 8, particles: 6, core: 6 }
                },
                battle: {
                    normal: { events: 3, lights: 2, particles: 2, core: 1 },
                    throttled: { events: 5, lights: 3, particles: 3, core: 2 },
                    aggressive: { events: 8, lights: 4, particles: 4, core: 3 },
                    emergency: { events: 15, lights: 6, particles: 5, core: 5 }
                },
                menu: {
                    normal: { events: 10, lights: 4, particles: 4, core: 3 },
                    throttled: { events: 15, lights: 6, particles: 6, core: 4 },
                    aggressive: { events: 20, lights: 8, particles: 8, core: 6 },
                    emergency: { events: 30, lights: 12, particles: 10, core: 8 }
                }
            };

            // Hardware capability detection
            this.hardwareCapability = this.detectHardwareCapability();
            
            // Integration with existing systems
            this.setupIntegration();

            console.log(`[AdaptiveThrottling] Initialized - Hardware: ${this.hardwareCapability}, Target FPS: ${targetFPS}`);
        }

        // Detect hardware capability
        detectHardwareCapability() {
            // Basic capability detection based on initial performance
            const startTime = performance.now();
            
            // Simple performance test
            let operations = 0;
            const testDuration = 50; // 50ms test
            
            while (performance.now() - startTime < testDuration) {
                Math.random() * Math.random();
                operations++;
            }

            // Classify hardware based on operations per millisecond
            const opsPerMs = operations / testDuration;
            
            if (opsPerMs > 10000) return 'high';
            if (opsPerMs > 5000) return 'medium';
            return 'low';
        }

        // Setup integration with existing systems
        setupIntegration() {
            // Hook into Battle Coordinator if available
            if (window.BattleCoordinator) {
                window.BattleCoordinator.addEventListener('coordinatedUpdate', (data) => {
                    this.updatePerformanceMetrics(data.currentFPS);
                    this.adaptThrottling();
                });
            }

            // Hook into VisuStella Performance Manager
            if (window.VisuStellaPerformanceManager) {
                const originalGetThrottleSettings = window.VisuStellaPerformanceManager.getCurrentThrottleSettings;
                window.VisuStellaPerformanceManager.getCurrentThrottleSettings = () => {
                    return this.getCurrentAdaptiveSettings();
                };
            }
        }

        // Update performance metrics
        updatePerformanceMetrics(currentFPS) {
            this.currentFPS = currentFPS;
            this.fpsHistory.push(currentFPS);

            // Maintain history size
            if (this.fpsHistory.length > this.maxFPSHistory) {
                this.fpsHistory.shift();
            }

            // Calculate average and stability
            if (this.fpsHistory.length > 30) { // At least 0.5 seconds of data
                this.averageFPS = this.fpsHistory.reduce((a, b) => a + b, 0) / this.fpsHistory.length;
                
                // Calculate stability (lower variance = higher stability)
                const variance = this.fpsHistory.reduce((acc, fps) => {
                    return acc + Math.pow(fps - this.averageFPS, 2);
                }, 0) / this.fpsHistory.length;
                
                this.fpsStability = Math.max(0, 100 - Math.sqrt(variance));
            }
        }

        // Determine target profile based on performance
        determineTargetProfile() {
            const avgFPS = this.averageFPS;
            const stability = this.fpsStability;

            // Emergency mode for very low FPS
            if (avgFPS < emergencyFPS) {
                return 'emergency';
            }

            // Consider both FPS and stability
            const performanceScore = (avgFPS / targetFPS) * (stability / 100);

            if (performanceScore >= 0.95) {
                return 'normal';
            } else if (performanceScore >= 0.8) {
                return 'throttled';
            } else {
                return 'aggressive';
            }
        }

        // Adapt throttling gradually
        adaptThrottling() {
            const now = Date.now();
            const timeSinceLastAdaptation = now - this.lastAdaptationTime;

            // Only adapt every 500ms to avoid rapid changes
            if (timeSinceLastAdaptation < 500) return;

            this.targetProfile = this.determineTargetProfile();

            // Gradual adaptation
            if (this.currentProfile !== this.targetProfile) {
                this.adaptationProgress += adaptationSpeed * 0.1;

                if (this.adaptationProgress >= 1.0) {
                    this.currentProfile = this.targetProfile;
                    this.adaptationProgress = 0;
                    this.lastAdaptationTime = now;

                    console.log(`[AdaptiveThrottling] Profile changed to: ${this.currentProfile} (FPS: ${this.averageFPS.toFixed(1)}, Stability: ${this.fpsStability.toFixed(1)}%)`);

                    // Notify other systems
                    if (window.BattleCoordinator) {
                        window.BattleCoordinator.dispatchEvent('adaptiveThrottlingChanged', {
                            profile: this.currentProfile,
                            fps: this.averageFPS,
                            stability: this.fpsStability
                        });
                    }
                }
            } else {
                this.adaptationProgress = 0;
            }
        }

        // Get current scene type
        getCurrentSceneType() {
            if (!SceneManager._scene) return 'menu';

            if (SceneManager._scene instanceof Scene_Map) return 'map';
            if (SceneManager._scene instanceof Scene_Battle) return 'battle';
            return 'menu';
        }

        // Get current adaptive settings
        getCurrentAdaptiveSettings() {
            const sceneType = this.getCurrentSceneType();
            const profile = this.currentProfile;

            if (enableSceneProfiles && this.sceneProfiles[sceneType]) {
                return this.sceneProfiles[sceneType][profile] || this.sceneProfiles[sceneType]['normal'];
            }

            // Fallback to map settings
            return this.sceneProfiles.map[profile] || this.sceneProfiles.map['normal'];
        }

        // Get performance statistics
        getPerformanceStats() {
            return {
                currentFPS: this.currentFPS,
                averageFPS: this.averageFPS,
                fpsStability: this.fpsStability,
                currentProfile: this.currentProfile,
                targetProfile: this.targetProfile,
                adaptationProgress: this.adaptationProgress,
                hardwareCapability: this.hardwareCapability,
                sceneType: this.getCurrentSceneType()
            };
        }

        // Manual profile override (for testing or user preference)
        setProfileOverride(profile) {
            if (['normal', 'throttled', 'aggressive', 'emergency'].includes(profile)) {
                this.currentProfile = profile;
                this.targetProfile = profile;
                this.adaptationProgress = 0;
                console.log(`[AdaptiveThrottling] Manual override to: ${profile}`);
            }
        }

        // Reset to automatic adaptation
        resetToAutomatic() {
            this.targetProfile = this.determineTargetProfile();
            console.log('[AdaptiveThrottling] Reset to automatic adaptation');
        }
    }

    //=============================================================================
    // Global Adaptive Throttling Manager
    //=============================================================================

    window.AdaptiveThrottlingManager = new AdaptiveThrottlingManager();

    // Export helper functions
    window.AdaptiveThrottlingHelpers = {
        getCurrentProfile: () => window.AdaptiveThrottlingManager.currentProfile,
        getPerformanceStats: () => window.AdaptiveThrottlingManager.getPerformanceStats(),
        setProfileOverride: (profile) => window.AdaptiveThrottlingManager.setProfileOverride(profile),
        resetToAutomatic: () => window.AdaptiveThrottlingManager.resetToAutomatic()
    };

    // Hook into Graphics.render for FPS monitoring
    const _Graphics_render = Graphics.render;
    Graphics.render = function() {
        const startTime = performance.now();
        _Graphics_render.call(this);
        const renderTime = performance.now() - startTime;
        
        // Estimate FPS from render time
        const estimatedFPS = Math.min(60, 1000 / Math.max(16.67, renderTime));
        
        if (window.AdaptiveThrottlingManager) {
            window.AdaptiveThrottlingManager.updatePerformanceMetrics(estimatedFPS);
            window.AdaptiveThrottlingManager.adaptThrottling();
        }
    };

    console.log('[VisuStella_AdaptiveThrottling] Adaptive throttling system loaded and active');
})();
