//=============================================================================
// VisuStella MZ - Skill Learn System
// VisuMZ_2_SkillLearnSystem.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_2_SkillLearnSystem = true;

var VisuMZ = VisuMZ || {};
VisuMZ.SkillLearnSystem = VisuMZ.SkillLearnSystem || {};
VisuMZ.SkillLearnSystem.version = 1.12;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 2] [Version 1.12] [SkillLearnSystem]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Skill_Learn_System_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin lets your game's actors have an alternative way of learning
 * skills aside from leveling up. Instead, they can learn skills through the
 * in-game skill menu, where they can trade gold, items, or the brand new
 * resources made available by this plugin: Ability Points and/or Skill Points.
 *
 * Ability Points and Skill Points are new resources provided by this plugin
 * that can be acquired in a variety of ways, of which, you can set through its
 * mechanical settings in the Plugin Parameters. These can be through leveling
 * up, performing actions, and/or defeating enemies.
 *
 * When learning skills through this plugin's in-game system, skills can have
 * a variety of costs and requirements. These requirements can come in the form
 * of needing to be at a certain level, having specific skills learned, and/or
 * having certain switches on.
 *
 * Features include all (but not limited to) the following:
 *
 * * Actors can now learn new skills from the in-game skill menu under the
 *   new "Learn" command.
 * * In this new menu, actors can spend various resources to learn new skills.
 * * These resources can be Ability Points, Skill Points, items, and more.
 * * Ability Points and Skill Points are brand new resources added through this
 *   plugin which can be acquired through a variety a means ranging from
 *   participating in battle, defeating enemies, and/or leveling up.
 * * Learnable skills may have requirements that need to be first met even if
 *   the actor has the available resources.
 * * Skill learning requirements can include levels, having other skills
 *   learned, and/or enabled switches.
 * * Play animations upon learning a new skill inside the menu.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 2 ------
 *
 * This plugin is a Tier 2 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * Battle Test
 *
 * When doing a battle test through the database, all of an actor's learnable
 * skills through the Skill Learn System's notetags will become available for
 * the test battle to reduce the need to manually add them.
 *
 * ---
 *
 * VisuMZ_3_VictoryAftermath
 *
 * If VisuStella MZ's Victory Aftermath plugin is installed, the amount of
 * Skill Points and Ability Points earned can be visibly shown in the rewards
 * window.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Ability Points-Related Notetags ===
 *
 * ---
 *
 * <Starting AP: x>
 *
 * - Used for: Actor Notetags
 * - Determines the amount of Ability Points the actor starts with in his/her
 *   starting class.
 * - Replace 'x' with a numeric value representing the amount of Ability Points
 *   to start out with.
 *
 * ---
 *
 * <Class id Starting AP: x>
 * <Class name Starting AP: x>
 *
 * - Used for: Actor Notetags
 * - Determines the amount of Ability Points the actor starts with in a
 *   specific class if Ability Points aren't shared across all classes.
 * - Replace 'x' with a numeric value representing the amount of Ability Points
 *   to start out with.
 * - Replace 'id' with the ID of the class to set starting Ability Points for.
 * - Replace 'name' with the name of the class to set starting Ability
 *   Points for.
 *
 * ---
 *
 * <AP Gain: x>
 * <User AP Gain: x>
 *
 * - Used for: Skill, Item Notetags
 * - When this skill/item is used in battle, the user will acquire 'x' amount
 *   of Ability Points.
 * - Replace 'x' with a number representing the amount of Ability Points for
 *   the user to earn upon usage.
 * - This effect will trigger each time per "hit".
 * - This effect will take over the "Per Action Hit" Ability Points gain from
 *   the Plugin Parameters.
 *
 * ---
 *
 * <Target AP Gain: x>
 *
 * - Used for: Skill, Item Notetags
 * - When this skill/item is used in battle, the target will acquire 'x' amount
 *   of Ability Points.
 * - Replace 'x' with a number representing the amount of Ability Points for
 *   the target to earn upon usage.
 * - This effect will trigger each time per "hit".
 *
 * ---
 *
 * <AP: x>
 *
 * - Used for: Enemy Notetags
 * - Determines the amount of Ability Points the enemy will give the player's
 *   party upon being defeated.
 * - Replace 'x' with a number representing the amount of Ability Points to
 *   grant the player's party each.
 * - This effect will take over the "Per Enemy" Ability Points gain from the
 *   Plugin Parameters.
 *
 * ---
 *
 * <AP Rate: x%>
 *
 * - Used for: Actor, Class, Weapon, Armor, State Notetags
 * - Increases the amount of Ability Points the affected battler will gain by a
 *   percentile value.
 * - Replace 'x' with a percentage number representing the amount of Ability
 *   Points that will be acquired.
 * - This stacks multiplicatively with each other.
 * - This does not apply when Ability Points are directly added, lost, or set.
 *
 * ---
 *
 * === Skill Points-Related Notetags ===
 *
 * ---
 *
 * <Starting SP: x>
 *
 * - Used for: Actor Notetags
 * - Determines the amount of Skill Points the actor starts with in his/her
 *   starting class.
 * - Replace 'x' with a numeric value representing the amount of Skill Points
 *   to start out with.
 *
 * ---
 *
 * <Class id Starting SP: x>
 * <Class name Starting SP: x>
 *
 * - Used for: Actor Notetags
 * - Determines the amount of Skill Points the actor starts with in a specific
 *   class if Skill Points aren't shared across all classes.
 * - Replace 'x' with a numeric value representing the amount of Skill Points
 *   to start out with.
 * - Replace 'id' with the ID of the class to set starting Skill Points for.
 * - Replace 'name' with the name of the class to set starting Skill
 *   Points for.
 *
 * ---
 *
 * <SP Gain: x>
 * <User SP Gain: x>
 *
 * - Used for: Skill, Item Notetags
 * - When this skill/item is used in battle, the user will acquire 'x' amount
 *   of Skill Points.
 * - Replace 'x' with a number representing the amount of Skill Points for the
 *   user to earn upon usage.
 * - This effect will trigger each time per "hit".
 * - This effect will take over the "Per Action Hit" Skill Points gain from the
 *   Plugin Parameters.
 *
 * ---
 *
 * <Target SP Gain: x>
 *
 * - Used for: Skill, Item Notetags
 * - When this skill/item is used in battle, the target will acquire 'x' amount
 *   of Skill Points.
 * - Replace 'x' with a number representing the amount of Skill Points for the
 *   target to earn upon usage.
 * - This effect will trigger each time per "hit".
 *
 * ---
 *
 * <SP: x>
 *
 * - Used for: Enemy Notetags
 * - Determines the amount of Skill Points the enemy will give the player's
 *   party upon being defeated.
 * - Replace 'x' with a number representing the amount of Skill Points to grant
 *   the player's party each.
 * - This effect will take over the "Per Enemy" Skill Points gain from the
 *   Plugin Parameters.
 *
 * ---
 *
 * <SP Rate: x%>
 *
 * - Used for: Actor, Class, Weapon, Armor, State Notetags
 * - Increases the amount of Skill Points the affected battler will gain by a
 *   percentile value.
 * - Replace 'x' with a percentage number representing the amount of Skill
 *   Points that will be acquired.
 * - This stacks multiplicatively with each other.
 * - This does not apply when Skill Points are directly added, lost, or set.
 *
 * ---
 *
 * === Learnable Skills-Related Notetags ===
 *
 * ---
 *
 * <Learn Skill: id>
 * <Learn Skills: id, id, id>
 *
 * <Learn Skill: name>
 * <Learn Skills: name, name, name>
 *
 * - Used for: Class Notetags
 * - Determines what skills the class can learn through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the skill that can be
 *   learned through the Skill Learn System menu.
 * - Replace 'name' with the name of the skill that can be learned through the
 *   Skill Learn System menu.
 * - Multiple entries are permited.
 *
 * ---
 *
 * <Learn Skills>
 *  id
 *  id
 *  id
 *  name
 *  name
 *  name
 * </Learn Skills>
 *
 * - Used for: Class
 * - Determines what skills the class can learn through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the skill that can be
 *   learned through the Skill Learn System menu.
 * - Replace 'name' with the name of the skill that can be learned through the
 *   Skill Learn System menu.
 * - Multiple middle entries are permited.
 *
 * ---
 *
 * === Skill Learn Cost-Related Notetags ===
 *
 * ---
 *
 * <Learn AP Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the Ability Point cost needed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Ability Points needed
 *   to learn this skill.
 * - If this notetag is not used, then the Ability Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn CP Cost: x>
 *
 * - Used for: Skill Notetags
 * - Requires VisuMZ_2_ClassChangeSystem
 * - Determines the Class Point cost needed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this skill.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn JP Cost: x>
 *
 * - Used for: Skill Notetags
 * - Requires VisuMZ_2_ClassChangeSystem
 * - Determines the Job Point cost needed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this skill.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn SP Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the Skill Point cost needed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this skill.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn Item id Cost: x>
 * <Learn Item name Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the items needed to be consumed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the item needed to be
 *   consumed.
 * - Replace 'name' with the name of the item needed to be consumed.
 * - Replace 'x' with a number representing the amount of the item needed
 *   to learn this skill.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Weapon id Cost: x>
 * <Learn Weapon name Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the weapons needed to be consumed for an actor to learn the
 *   skill through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the weapon needed to be
 *   consumed.
 * - Replace 'name' with the name of the weapon needed to be consumed.
 * - Replace 'x' with a number representing the amount of the weapon needed
 *   to learn this skill.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Armor id Cost: x>
 * <Learn Armor name Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the armors needed to be consumed for an actor to learn the
 *   skill through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the armor needed to be
 *   consumed.
 * - Replace 'name' with the name of the armor needed to be consumed.
 * - Replace 'x' with a number representing the amount of the armor needed
 *   to learn this skill.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Gold Cost: x>
 *
 * - Used for: Skill Notetags
 * - Determines the gold cost needed for an actor to learn the skill through
 *   the Skill Learn System.
 * - Replace 'x' with a number representing the amount of gold needed to learn
 *   this skill.
 * - If this notetag is not used, then the gold cost will default to the value
 *   found in the settings.
 *
 * ---
 *
 * <Learn Skill Costs>
 *  AP: x
 *
 *  SP: x
 *
 *  Item id: x
 *  Item name: x
 *
 *  Weapon id: x
 *  Weapon name: x
 *
 *  Armor id: x
 *  Armor name: x
 *
 *  Gold: x
 * </Learn Skill Costs>
 *
 * - Used for: Skill Notetags
 * - Determines a group of resources needed for an actor to learn the skill
 *   through the Skill Learn System.
 * - Replace 'id' with the ID's of items, weapons, armors to be consumed.
 * - Replace 'name' with the names of items, weapons, armors to be consumed.
 * - Replace 'x' with the quantities of the designated resource to be consumed.
 * - Insert multiple entries of items, weapons, and armors inside the notetags
 *   to add more resource entries.
 *
 * ---
 *
 * === JavaScript Notetags: Skill Costs ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * create dynamic Ability Point and Skill Point costs.
 *
 * ---
 *
 * <JS Learn AP Cost>
 *  code
 *  code
 *  cost = code;
 * </JS Learn AP Cost>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create dynamically calculated cost
 *   for the required Ability Points in order to learn this skill.
 * - The 'cost' variable will be returned to determine the finalized Ability
 *   Points cost to learn this skill.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - If the <Learn AP Cost: x> is present, this notetag will be ignored.
 *
 * ---
 *
 * <JS Learn CP Cost>
 *  code
 *  code
 *  cost = code;
 * </JS Learn CP Cost>
 *
 * - Used for: Skill Notetags
 * - Requires VisuMZ_2_ClassChangeSystem
 * - Replace 'code' with JavaScript code to create dynamically calculated cost
 *   for the required Class Points in order to learn this skill.
 * - The 'cost' variable will be returned to determine the finalized Skill
 *   Points cost to learn this skill.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - If the <Learn CP Cost: x> is present, this notetag will be ignored.
 *
 * ---
 *
 * <JS Learn JP Cost>
 *  code
 *  code
 *  cost = code;
 * </JS Learn JP Cost>
 *
 * - Used for: Skill Notetags
 * - Requires VisuMZ_2_ClassChangeSystem
 * - Replace 'code' with JavaScript code to create dynamically calculated cost
 *   for the required Job Points in order to learn this skill.
 * - The 'cost' variable will be returned to determine the finalized Skill
 *   Points cost to learn this skill.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - If the <Learn JP Cost: x> is present, this notetag will be ignored.
 *
 * ---
 *
 * <JS Learn SP Cost>
 *  code
 *  code
 *  cost = code;
 * </JS Learn SP Cost>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create dynamically calculated cost
 *   for the required Skill Points in order to learn this skill.
 * - The 'cost' variable will be returned to determine the finalized Skill
 *   Points cost to learn this skill.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - If the <Learn SP Cost: x> is present, this notetag will be ignored.
 *
 * ---
 *
 * === Show Condition-Related Notetags ===
 *
 * ---
 *
 * <Learn Show Level: x>
 *
 * - Used for: Skill Notetags
 * - Actors must be at least the required level in order for the skill to even
 *   appear visibly in the Skill Learn System menu.
 * - Replace 'x' with a number representing the required level for the actor
 *   in order for the skill to visibly appear.
 *
 * ---
 *
 * <Learn Show Skill: id>
 * <Learn Show Skill: name>
 *
 * <Learn Show All Skills: id, id, id>
 * <Learn Show All Skills: name, name, name>
 *
 * <Learn Show Any Skills: id, id, id>
 * <Learn Show Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - The actor must have already learned the above skills in order for the
 *   learnable skill to appear visibly in the Skill Learn System menu.
 * - Replace 'id' with a number representing the ID of the skill required to be
 *   known by the actor in order to appear visibly in the menu.
 * - Replace 'name' with the name of the skill required to be known by the
 *   actor in order to appear visibly in the menu.
 * - The 'All' notetag variant requires all of the listed skills to be known
 *   before the learnable skill will appear visibly in the menu.
 * - The 'Any' notetag variant requires any of the listed skills to be known
 *   before the learnable skill will appear visibly in the menu.
 *
 * ---
 *
 * <Learn Show Switch: x>
 *
 * <Learn Show All Switches: x, x, x>
 *
 * <Learn Show Any Switches: x, x, x>
 *
 * - Used for: Skill Notetags
 * - The switches must be in the ON position in order for the learnable skill
 *   to appear visibly in the Skill Learn System menu.
 * - Replace 'x' with a number representing the ID of the switch required to be
 *   in the ON position in order to appear visibly in the menu.
 * - The 'All' notetag variant requires all of the switches to be in the ON
 *   position before the learnable skill will appear visibly in the menu.
 * - The 'Any' notetag variant requires any of the switches to be in the ON
 *   position before the learnable skill will appear visibly in the menu.
 *
 * ---
 *
 * === JavaScript Notetags: Show Conditions ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * create dynamic determined show conditions.
 *
 * ---
 *
 * <JS Learn Show>
 *  code
 *  code
 *  visible = code;
 * </JS Learn Show>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to determine if the skill will be
 *   visibly shown in the Skill Learn System menu.
 * - The 'visible' variable must result in a 'true' or 'false' value to
 *   determine if the skill will be visible.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - Any other show conditions must be met, too.
 *
 * ---
 *
 * <JS Learn Show List Text>
 *  code
 *  code
 *  text = code;
 * </JS Learn Show List Text>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create custom text that will be
 *   displayed when the skill is shown in the Skill Learn System skill list.
 * - The 'text' variable will determine the text to be shown if it is a string.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 *
 * ---
 *
 * <JS Learn Show Detail Text>
 *  code
 *  code
 *  text = code;
 * </JS Learn Show Detail Text>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create custom text that will be
 *   displayed when the skill is selected and the Detailed Skill Learn System
 *   resource cost window is opened.
 * - The 'text' variable will determine the text to be shown if it is a string.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 *
 * ---
 *
 * === Require Condition-Related Notetags ===
 *
 * ---
 *
 * <Learn Require Level: x>
 *
 * - Used for: Skill Notetags
 * - Actors must be at least the required level in order for the skill to be
 *   enabled in the Skill Learn System menu.
 * - Replace 'x' with a number representing the required level for the actor
 *   in order for the skill to visibly appear.
 *
 * ---
 *
 * <Learn Require Skill: id>
 * <Learn Require Skill: name>
 *
 * <Learn Require All Skills: id, id, id>
 * <Learn Require All Skills: name, name, name>
 *
 * <Learn Require Any Skills: id, id, id>
 * <Learn Require Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - The actor must have already learned the above skills in order for the
 *   learnable skill to be enabled in the Skill Learn System menu.
 * - Replace 'id' with a number representing the ID of the skill required to be
 *   known by the actor in order to be enabled in the menu.
 * - Replace 'name' with the name of the skill required to be known by the
 *   actor in order to be enabled in the menu.
 * - The 'All' notetag variant requires all of the listed skills to be known
 *   before the learnable skill will be enabled in the menu.
 * - The 'Any' notetag variant requires any of the listed skills to be known
 *   before the learnable skill will be enabled in the menu.
 *
 * ---
 *
 * <Learn Require Switch: x>
 *
 * <Learn Require All Switches: x, x, x>
 *
 * <Learn Require Any Switches: x, x, x>
 *
 * - Used for: Skill Notetags
 * - The switches must be in the ON position in order for the learnable skill
 *   to be enabled in the Skill Learn System menu.
 * - Replace 'x' with a number representing the ID of the switch required to be
 *   in the ON position in order to be enabled in the menu.
 * - The 'All' notetag variant requires all of the switches to be in the ON
 *   position before the learnable skill will be enabled in the menu.
 * - The 'Any' notetag variant requires any of the switches to be in the ON
 *   position before the learnable skill will be enabled in the menu.
 *
 * ---
 *
 * === JavaScript Notetags: Requirement Conditions ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * create dynamic determined learning requirement conditions.
 *
 * ---
 *
 * <JS Learn Requirements>
 *  code
 *  code
 *  enabled = code;
 * </JS Learn Requirements>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to determine if the skill will be
 *   enabled for learning in the Skill Learn System menu.
 * - The 'enabled' variable must result in a 'true' or 'false' value to
 *   determine if the skill will be enabled.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 * - Any other requirement conditions must be met, too.
 *
 * ---
 *
 * <JS Learn Requirements List Text>
 *  code
 *  code
 *  text = code;
 * </JS Learn Requirements List Text>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create custom text that will be
 *   displayed when the skill is shown in the Skill Learn System skill list
 *   as long as the requirements have to be met.
 * - The 'text' variable will determine the text to be shown if it is a string.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 *
 * ---
 *
 * <JS Learn Requirements Detail Text>
 *  code
 *  code
 *  text = code;
 * </JS Learn Requirements Detail Text>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to create custom text that will be
 *   displayed when the skill is selected and the Detailed Skill Learn System
 *   resource cost window is opened as long as the requirements have to be met.
 * - The 'text' variable will determine the text to be shown if it is a string.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 *
 * ---
 *
 * === Animation-Related Notetags ===
 *
 * ---
 *
 * <Learn Skill Animation: id>
 * <Learn Skill Animation: id, id, id>
 *
 * - Used for: Skill Notetags
 * - Plays the animation(s) when this skill is learned through the Skill Learn
 *   System's menu.
 * - This will override the default animation settings found in the plugin
 *   parameters and use the unique one set through notetags instead.
 * - Replace 'id' with the ID of the animation you wish to play.
 * - If multiple ID's are found, then each animation will play one by one in
 *   the order they are listed.
 *
 * ---
 *
 * <Learn Skill Fade Speed: x>
 *
 * - Used for: Skill Notetags
 * - This determines the speed at which the skill's icon fades in during the
 *   skill learning animation.
 * - Replace 'x' with a number value to determine how fast the icon fades in.
 * - Use lower numbers for slower fade speeds and higher numbers for faster
 *   fade speeds.
 *
 * ---
 *
 * <Learn Skill Picture: filename>
 * <Picture: filename>
 *
 * - Used for: Skill Notetags
 * - Uses a picture from your project's /img/pictures/ folder instead of the
 *   skill's icon during learning instead.
 * - Replace 'filename' with the filename of the image.
 *   - Do not include the file extension.
 * - Scaling will not apply to the picture.
 * - Use the <Picture: filename> version for any other plugins that may be
 *   using this as an image outside of learning skills, too.
 * - The size used for the image will vary based on your game's resolution.
 *
 * ---
 *
 * === JavaScript Notetags: On Learning Conditions ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * produce special effects when the skill is learned.
 *
 * ---
 *
 * <JS On Learn Skill>
 *  code
 *  code
 *  code
 * </JS On Learn Skill>
 *
 * - Used for: Skill Notetags
 * - Replace 'code' with JavaScript code to perform the desired actions when
 *   the skill is learned.
 * - This will apply to any time the skill is learned by an actor, even if it
 *   is through natural leveling or through the Skill Learn System menu.
 * - The 'user' variable can be used to reference the actor who will be
 *   learning the skill.
 * - The 'skill' variable can be used to reference the skill that will be
 *   learned by the actor.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Ability Points Plugin Commands ===
 *
 * ---
 *
 * Ability Points: Gain
 * - The target actor(s) gains Ability Points.
 * - Gained amounts are affected by Ability Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to gain Ability Points for.
 *   - Use "0" for the current class.
 *
 *   Ability Points:
 *   - Determine how many Ability Points will be gained.
 *   - You may use code.
 *
 * ---
 *
 * Ability Points: Add
 * - The target actor(s) receives Ability Points.
 * - Received amounts are NOT affected by Ability Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to receive Ability Points for.
 *   - Use "0" for the current class.
 *
 *   Ability Points:
 *   - Determine how many Ability Points will be added.
 *   - You may use code.
 *
 * ---
 *
 * Ability Points: Lose
 * - The target actor(s) loses Ability Points.
 * - Lost amounts are NOT affected by Ability Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to lose Ability Points for.
 *   - Use "0" for the current class.
 *
 *   Ability Points:
 *   - Determine how many Ability Points will be lost.
 *   - You may use code.
 *
 * ---
 *
 * Ability Points: Set
 * - Changes the exact Ability Points for the target actor(s).
 * - Changed amounts are NOT affected by Ability Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to change Ability Points for.
 *   - Use "0" for the current class.
 *
 *   Ability Points:
 *   - Determine how many Ability Points will be set exactly to.
 *   - You may use code.
 *
 * ---
 *
 * === Skill Points Plugin Commands ===
 *
 * ---
 *
 * Skill Points: Gain
 * - The target actor(s) gains Skill Points.
 * - Gained amounts are affected by Skill Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to gain Skill Points for.
 *   - Use "0" for the current class.
 *
 *   Skill Points:
 *   - Determine how many Skill Points will be gained.
 *   - You may use code.
 *
 * ---
 *
 * Skill Points: Add
 * - The target actor(s) receives Skill Points.
 * - Received amounts are NOT affected by Skill Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to receive Skill Points for.
 *   - Use "0" for the current class.
 *
 *   Skill Points:
 *   - Determine how many Skill Points will be added.
 *   - You may use code.
 *
 * ---
 *
 * Skill Points: Lose
 * - The target actor(s) loses Skill Points.
 * - Lost amounts are NOT affected by Skill Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to lose Skill Points for.
 *   - Use "0" for the current class.
 *
 *   Skill Points:
 *   - Determine how many Skill Points will be lost.
 *   - You may use code.
 *
 * ---
 *
 * Skill Points: Set
 * - Changes the exact Skill Points for the target actor(s).
 * - Changed amounts are NOT affected by Skill Point bonus rates.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Class ID(s):
 *   - Select which Class ID(s) to change Skill Points for.
 *   - Use "0" for the current class.
 *
 *   Skill Points:
 *   - Determine how many Skill Points will be set exactly to.
 *   - You may use code.
 *
 * ---
 *
 * === System Plugin Commands ===
 *
 * ---
 *
 * System: Show Skill Learn in Skill Menu?
 * - Shows/hides Skill Learn inside the skill menu.
 *
 *   Show/Hide?:
 *   - Shows/hides Skill Learn inside the skill menu.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * General settings for the Skill Learn System. These determine the settings
 * that are used for the Skill Learn System menu's main screen.
 *
 * ---
 *
 * Visual
 *
 *   Displayed Costs:
 *   - Select which cost types to display in the skill entry.
 *   - This also determines the order they are displayed.
 *     - AP - Ability Points
 *     - SP - Skill Points
 *     - Item - Item Costs
 *     - Weapon - Weapon Costs
 *     - Armor - Armor Costs
 *     - Gold - Gold Costs
 *
 *   JS: Draw Status:
 *   - JavaScript code used to draw in Window_SkillStatus when the Skill Learn
 *     System is active.
 *
 * ---
 *
 * Vocabulary
 *
 *   Learned Text:
 *   - This is the text that appears if the skill has been learned.
 *   - You may use text codes.
 *
 *   Requirements
 *
 *     Requirement Header:
 *     - Header for requirements.
 *     - %1 - Requirements (all of them)
 *
 *     Separation Format:
 *     - This determines how the requirements are separated.
 *     - %1 - Previous Requirement, %2 - Second Requirement
 *
 *     Level Format:
 *     - This how level is displayed.
 *     - %1 - Level, %2 - Full Level Term, %3 - Abbr Level Term
 *
 *     Skill Format:
 *     - This how required skills are displayed.
 *     - %1 - Icon, %2 - Skill Name
 *
 *     Switch Format:
 *     - This how required switches are displayed.
 *     - %1 - Switch Name
 *
 *   Costs
 *
 *     Separation Format:
 *     - This determines how the costs are separated from one another.
 *     - %1 - Previous Cost, %2 - Second Cost
 *
 *     Item Format:
 *     - Determine how items are displayed as a cost.
 *     - %1 - Quantity, %2 - Icon, %3 - Item Name
 *
 *     Weapon Format:
 *     - Determine how weapons are displayed as a cost.
 *     - %1 - Quantity, %2 - Icon, %3 - Weapon Name
 *
 *     Armor Format:
 *     - Determine how armors are displayed as a cost.
 *     - %1 - Quantity, %2 - Icon, %3 - Armor Name
 *
 *     Gold Format:
 *     - Determine how gold is displayed as a cost.
 *     - %1 - Quantity, %2 - Icon, %3 - Currency Vocabulary
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Main Access Settings
 * ============================================================================
 *
 * Menu Access settings for Skill Learn System. The Skill Learn System is
 * accessible normally through the in-game Skill menu.
 *
 * ---
 *
 * Main Access Settings
 *
 *   Command Name:
 *   - Name of the 'Skill Learn' option in the Menu.
 *
 *   Icon:
 *   - What is the icon you want to use to represent Skill Learn?
 *
 *   Show in Menu?:
 *   - Add the 'Skill Learn' option to the Menu by default?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Animation Settings
 * ============================================================================
 *
 * Animation settings for the Skill Learn System. By default, an animation will
 * be played upon learning a skill through the Skill Learn System's menu in
 * order to provide player feedback about learning the said skill.
 *
 * ---
 *
 * General
 *
 *   Show Animations?:
 *   - Show animations when learning a skill?
 *
 *   Show Windows?:
 *   - Show windows during a skill learn animation?
 *
 *   Default Animations:
 *   - Default animation(s) do you want to play when learning.
 *
 * ---
 *
 * Skill Sprite
 *
 *   Scale:
 *   - How big do you want the skill sprite to be on screen?
 *
 *   Fade Speed:
 *   - How fast do you want the icon to fade in?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Sound Settings
 * ============================================================================
 *
 * Settings for the sound effect played when learning a new skill through the
 * Skill Learn System.
 *
 * ---
 *
 * Settings
 *
 *   Filename:
 *   - Filename of the sound effect played.
 *
 *   Volume:
 *   - Volume of the sound effect played.
 *
 *   Pitch:
 *   - Pitch of the sound effect played.
 *
 *   Pan:
 *   - Pan of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Window Settings
 * ============================================================================
 *
 * Window settings for the Skill Learn System. There are two new windows added
 * into the Skill menu through this plugin: the Detail Window and the Confirm
 * Window.
 *
 * The Detail Window will list the required costs of learning a skill in detail
 * in case the icons provided are not clear enough to show what's needed.
 *
 * The Confirm Window is a window that appears towards the bottom to let the
 * player make a confirmation before deciding to learn the skill.
 *
 * ---
 *
 * Detail Window
 *
 *   Requirements
 *
 *     Requirement Title:
 *     - Text used when drawing the learning requirements.
 *     - %1 - Skill Icon, %2 - Skill Name
 *
 *     Requirement Met:
 *     - This how met requirements look.
 *     - %1 - Requirement Text
 *
 *     Requirement Not Met:
 *     - This how met requirements look.
 *     - %1 - Requirement Text
 *
 *     Requirement Level:
 *     - This how level is displayed.
 *     - %1 - Level, %2 - Full Level Term, %3 - Abbr Level Term
 *
 *     Requirement Skill:
 *     - This how required skills are displayed.
 *     - %1 - Icon, %2 - Skill Name
 *
 *     Requirement Switch:
 *     - This how required switches are displayed.
 *     - %1 - Switch Name
 *
 *   Costs
 *
 *     Cost Title:
 *     - Text used when drawing the learning costs.
 *     - %1 - Skill Icon, %2 - Skill Name
 *
 *     Cost Name:
 *     - Text used to label the resource being consumed.
 *
 *     Cost Quantity:
 *     - Text used to label the cost of the resource.
 *
 *     Cost of Owned:
 *     - Text used to label the amount of the resource in possession.
 *
 *   Background Type:
 *   - Select background type for this window.
 *     - 0 - Window
 *     - 1 - Dim
 *     - 2 - Transparent
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Confirm Window
 *
 *   Confirm Text:
 *   - Text used for the Confirm command.
 *   - Text codes can be used.
 *
 *   Cancel Text:
 *   - Text used for the Cancel command.
 *   - Text codes can be used.
 *
 *   Background Type:
 *   - Select background type for this window.
 *     - 0 - Window
 *     - 1 - Dim
 *     - 2 - Transparent
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Ability Points Settings
 * ============================================================================
 *
 * Ability Points are an actor-only resource used as a currency for this
 * plugin. You can determine how they appear in-game, how they're earned, and
 * what kind of mechanics are involved with them. Ability Points can also be
 * used in other VisuStella plugins.
 *
 * ---
 *
 * Mechanics
 *
 *   Shared Ability Points:
 *   - Do you want Ability Points to be shared across all classes?
 *   - Or do you want all classes to have their own?
 *
 *   Maximum:
 *   - What's the maximum amount of Ability Points an actor can have?
 *   - Use 0 for unlimited Ability Points.
 *
 * ---
 *
 * Visual
 *
 *   Show In Menus?:
 *   - Do you wish to show Ability Points in menus that allow them?
 *
 *   Icon:
 *   - What is the icon you want to use to represent Ability Points?
 *
 * ---
 *
 * Vocabulary
 *
 *   Full Text:
 *   - The full text of how Ability Points appears in-game.
 *
 *   Abbreviated Text:
 *   - The abbreviation of how Ability Points appears in-game.
 *
 *   Menu Text Format:
 *   - What is the text format for it to be displayed in windows.
 *   - %1 - Value, %2 - Abbr, %3 - Icon, %4 - Full Text
 *
 * ---
 *
 * Gain
 *
 *   Per Action Hit:
 *   - How many Ability Points should an actor gain per action?
 *   - You may use code.
 *
 *   Per Level Up:
 *   - How many Ability Points should an actor gain per level up?
 *   - You may use code.
 *
 *   Per Enemy Defeated:
 *   - How many Ability Points should an actor gain per enemy?
 *   - You may use code.
 *
 *     Alive Actors?:
 *     - Do actors have to be alive to receive Ability Points from
 *       defeated enemies?
 *
 * ---
 *
 * Victory
 *
 *   Show During Victory?:
 *   - Show how much AP an actor has earned in battle during the victory phase?
 *
 *   Victory Text:
 *   - For no Victory Aftermath, this is the text that appears.
 *   - %1 - Actor, %2 - Earned, %3 - Abbr, %4 - Full Text
 *
 *   Aftermath Display?:
 *   - Requires VisuMZ_3_VictoryAftermath.
 *   - Show Ability Points as the main acquired resource in the actor windows?
 *
 *   Aftermath Text:
 *   - For no Victory Aftermath, this is the text that appears.
 *   - %1 - Earned, %2 - Abbr, %3 - Full Text
 *
 * ---
 *
 * For those who wish to display how many Ability Points an actor has for a
 * specific class, you can use the following JavaScript code inside of a
 * window object.
 *
 *   this.drawAbilityPoints(value, x, y, width, align);
 *   - The 'value' variable refers to the number you wish to display.
 *   - The 'x' variable refers to the x coordinate to draw at.
 *   - The 'y' variable refers to the y coordinate to draw at.
 *   - The 'width' variable refers to the width of the data area.
 *   - Replace 'align' with a string saying 'left', 'center', or 'right' to
 *     determine how you want the data visibly aligned.
 *
 *   this.drawActorAbilityPoints(actor, classID, x, y, width, align);
 *   - The 'actor' variable references the actor to get data from.
 *   - The 'classID' variable is the class to get data from.
 *     - Use 0 if Ability Points aren't shared or if you want the Ability
 *       Points from the actor's current class.
 *   - The 'x' variable refers to the x coordinate to draw at.
 *   - The 'y' variable refers to the y coordinate to draw at.
 *   - The 'width' variable refers to the width of the data area.
 *   - Replace 'align' with a string saying 'left', 'center', or 'right' to
 *     determine how you want the data visibly aligned.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Skill Points Settings
 * ============================================================================
 *
 * Skill Points are an actor-only resource used as a currency for this plugin.
 * You can determine how they appear in-game, how they're earned, and what kind
 * of mechanics are involved with them. Skill Points can also be used in other
 * VisuStella plugins.
 *
 * ---
 *
 * Mechanics
 *
 *   Shared Skill Points:
 *   - Do you want Skill Points to be shared across all classes?
 *   - Or do you want all classes to have their own?
 *
 *   Maximum:
 *   - What's the maximum amount of Skill Points an actor can have?
 *   - Use 0 for unlimited Skill Points.
 *
 * ---
 *
 * Visual
 *
 *   Show In Menus?:
 *   - Do you wish to show Skill Points in menus that allow them?
 *
 *   Icon:
 *   - What is the icon you want to use to represent Skill Points?
 *
 * ---
 *
 * Vocabulary
 *
 *   Full Text:
 *   - The full text of how Skill Points appears in-game.
 *
 *   Abbreviated Text:
 *   - The abbreviation of how Skill Points appears in-game.
 *
 *   Menu Text Format:
 *   - What is the text format for it to be displayed in windows.
 *   - %1 - Value, %2 - Abbr, %3 - Icon, %4 - Full Text
 *
 * ---
 *
 * Gain
 *
 *   Per Action Hit:
 *   - How many Skill Points should an actor gain per action?
 *   - You may use code.
 *
 *   Per Level Up:
 *   - How many Skill Points should an actor gain per level up?
 *   - You may use code.
 *
 *   Per Enemy Defeated:
 *   - How many Skill Points should an actor gain per enemy?
 *   - You may use code.
 *
 *     Alive Actors?:
 *     - Do actors have to be alive to receive Skill Points from
 *       defeated enemies?
 *
 * ---
 *
 * Victory
 *
 *   Show During Victory?:
 *   - Show how much SP an actor has earned in battle during the victory phase?
 *
 *   Victory Text:
 *   - For no Victory Aftermath, this is the text that appears.
 *   - %1 - Actor, %2 - Earned, %3 - Abbr, %4 - Full Text
 *
 *   Aftermath Display?:
 *   - Requires VisuMZ_3_VictoryAftermath.
 *   - Show Skill Points as the main acquired resource in the actor windows?
 *
 *   Aftermath Text:
 *   - For no Victory Aftermath, this is the text that appears.
 *   - %1 - Earned, %2 - Abbr, %3 - Full Text
 *
 * ---
 *
 * For those who wish to display how many Skill Points an actor has for a
 * specific class, you can use the following JavaScript code inside of a
 * window object.
 *
 *   this.drawSkillPoints(value, x, y, width, align);
 *   - The 'value' variable refers to the number you wish to display.
 *   - The 'x' variable refers to the x coordinate to draw at.
 *   - The 'y' variable refers to the y coordinate to draw at.
 *   - The 'width' variable refers to the width of the data area.
 *   - Replace 'align' with a string saying 'left', 'center', or 'right' to
 *     determine how you want the data visibly aligned.
 *
 *   this.drawActorSkillPoints(actor, classID, x, y, width, align);
 *   - The 'actor' variable references the actor to get data from.
 *   - The 'classID' variable is the class to get data from.
 *     - Use 0 if Skill Points aren't shared or if you want the Skill
 *       Points from the actor's current class.
 *   - The 'x' variable refers to the x coordinate to draw at.
 *   - The 'y' variable refers to the y coordinate to draw at.
 *   - The 'width' variable refers to the width of the data area.
 *   - Replace 'align' with a string saying 'left', 'center', or 'right' to
 *     determine how you want the data visibly aligned.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.12: November 16, 2023
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.11: May 18, 2023
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.10: December 15, 2022
 * * Bug Fixes!
 * ** Fixed a visual listing bug effect when 'CP' and 'JP' are listed under
 *    costs but the VisuMZ Class Change System plugin isn't present. Fix made
 *    by Olivia.
 *
 * Version 1.09: June 9, 2022
 * * Compatibility Update
 * ** Plugins should be more compatible with one another.
 *
 * Version 1.08: March 24, 2022
 * * Documentation Update!
 * ** Fixed a typo for missing a "/" in the <Learn Skills> group notetag.
 *
 * Version 1.07: February 10, 2022
 * * Bug Fixes!
 * ** Costs for CP and JP will have better fail safes to not automatically
 *    reduce to 0 under specific conditions when learning skills. Fix by Arisu.
 *
 * Version 1.06: July 9, 2021
 * * Compatibility Update
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.05: December 25, 2020
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New notetag added by Yanfly.
 * *** <Learn Skill Picture: filename> and <Picture: filename>
 * **** Uses a picture from your project's /img/pictures/ folder instead of the
 *      skill's icon during learning instead.
 *
 * Version 1.04: December 18, 2020
 * * Bug Fixes!
 * ** Notetags that utilize multiple numeric ID's instead of skill names should
 *    now be working properly. Fix made by Yanfly.
 *
 * Version 1.03: December 11, 2020
 * * Compatibility Update
 * ** Added compatibility functionality for future plugins.
 * * Documentation Update!
 * ** Help file updated for new features.
 * * Feature Updates!
 * ** The Plugin Parameter for "Displayed Costs" have been updated to contain
 *    compatibility for a future plugin.
 * ** The Plugin Parameter for "JS: Draw Status" has been updated to contain
 *    compatibility for a future plugin.
 * *** To quickly acquire the new changes for the above Plugin Parameters,
 *     delete the "General" settings from the main Plugin Parameters page, then
 *     open them up again. These settings will be defaulted to the new
 *     additions added for the plugin. Warning! Old settings will be lost.
 * * New Features!
 * ** Added <Learn CP Cost: x>, <Learn JP Cost: x>, <JS Learn CP Cost>,
 *    <JS Learn JP Cost> notetags. Added by Arisu.
 *
 * Version 1.02: November 29, 2020
 * * Bug Fixes!
 * ** The plugin should no longer be dependent on Skills & States Core. Fix
 *    made by Arisu.
 *
 * Version 1.01: November 22, 2020
 * * Bug Fixes!
 * ** Game no longer crashes when displaying AP/SP rewards for those without
 *    the Victory Aftermath plugin. Fix made by Yanfly.
 *
 * Version 1.00: November 30, 2020
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command AbilityPointsGain
 * @text Ability Points: Gain
 * @desc The target actor(s) gains Ability Points.
 * Gained amounts are affected by Ability Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to gain Ability Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Ability Points
 * @desc Determine how many Ability Points will be gained.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command AbilityPointsAdd
 * @text Ability Points: Add
 * @desc The target actor(s) receives Ability Points.
 * Received amounts are NOT affected by Ability Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to receive Ability Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Ability Points
 * @desc Determine how many Ability Points will be added.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command AbilityPointsLose
 * @text Ability Points: Lose
 * @desc The target actor(s) loses Ability Points.
 * Lost amounts are NOT affected by Ability Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to lose Ability Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Ability Points
 * @desc Determine how many Ability Points will be lost.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command AbilityPointsSet
 * @text Ability Points: Set
 * @desc Changes the exact Ability Points for the target actor(s).
 * Changed amounts are NOT affected by Ability Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to change Ability Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Ability Points
 * @desc Determine how many Ability Points will be set exactly to.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillPointsGain
 * @text Skill Points: Gain
 * @desc The target actor(s) gains Skill Points.
 * Gained amounts are affected by Skill Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to gain Skill Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Skill Points
 * @desc Determine how many Skill Points will be gained.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillPointsAdd
 * @text Skill Points: Add
 * @desc The target actor(s) receives Skill Points.
 * Received amounts are NOT affected by Skill Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to receive Skill Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Skill Points
 * @desc Determine how many Skill Points will be added.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillPointsLose
 * @text Skill Points: Lose
 * @desc The target actor(s) loses Skill Points.
 * Lost amounts are NOT affected by Skill Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to lose Skill Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Skill Points
 * @desc Determine how many Skill Points will be lost.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillPointsSet
 * @text Skill Points: Set
 * @desc Changes the exact Skill Points for the target actor(s).
 * Changed amounts are NOT affected by Skill Point bonus rates.
 *
 * @arg Actors:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg Classes:arraynum
 * @text Class ID(s)
 * @type class[]
 * @desc Select which Class ID(s) to change Skill Points for.
 * Use "0" for the current class.
 * @default ["0"]
 *
 * @arg Points:eval
 * @text Skill Points
 * @desc Determine how many Skill Points will be set exactly to.
 * You may use code.
 * @default 100
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemShowSkillLearnSystemMenu
 * @text System: Show Skill Learn in Skill Menu?
 * @desc Shows/hides Skill Learn inside the skill menu.
 *
 * @arg Show:eval
 * @text Show/Hide?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Shows/hides Skill Learn inside the skill menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param SkillLearnSystem
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Scene_SkillLearn
 *
 * @param General:struct
 * @text General Settings
 * @parent Scene_SkillLearn
 * @type struct<General>
 * @desc General settings for the Skill Learn System.
 * @default {"Visual":"","DisplayedCosts:arraystr":"[\"AP\",\"SP\",\"Item\",\"Weapon\",\"Armor\",\"Gold\"]","StatusWindowDrawJS:func":"\"// Draw Face\\nconst fx = this.colSpacing() / 2;\\nconst fh = this.innerHeight;\\nconst fy = fh / 2 - this.lineHeight() * 1.5;\\nthis.drawActorFace(this._actor, fx + 1, 0, 144, fh);\\nthis.drawActorSimpleStatus(this._actor, fx + 180, fy);\\n\\n// Return if Window Size is Too Small\\nlet sx = (this.colSpacing() / 2) + 180 + 180 + 180;\\nlet sw = this.innerWidth - sx - 2;\\nif (sw < 300) return;\\n\\n// Draw Costs\\n// Compatibility Target\\nconst costs = this.getSkillLearnDisplayedCosts();\\nconst maxEntries = Math.floor(this.innerHeight / this.lineHeight());\\nconst maxCol = Math.ceil(costs.length / maxEntries);\\nlet cx = sx;\\nlet cy = Math.max(Math.round((this.innerHeight - (this.lineHeight() * Math.ceil(costs.length / maxCol))) / 2), 0);\\nconst by = cy;\\nlet cw = (this.innerWidth - cx - (this.itemPadding() * 2 * maxCol)) / maxCol;\\nif (maxCol === 1) {\\n    cw = Math.min(ImageManager.faceWidth, cw);\\n    cx += Math.round((this.innerWidth - cx - (this.itemPadding() * 2) - cw) / 2);\\n}\\nfor (const cost of costs) {\\n    switch (cost) {\\n\\n        case 'AP':\\n            this.drawActorAbilityPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\\n            break;\\n\\n        case 'CP':\\n            if (Imported.VisuMZ_2_ClassChangeSystem) {\\n                this.drawActorClassPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\\n            }\\n            break;\\n\\n        case 'JP':\\n            if (Imported.VisuMZ_2_ClassChangeSystem) {\\n                this.drawActorJobPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\\n            }\\n            break;\\n\\n        case 'SP':\\n            this.drawActorSkillPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\\n            break;\\n\\n        case 'Gold':\\n            this.drawCurrencyValue($gameParty.gold(), TextManager.currencyUnit, cx, cy, cw);\\n            break;\\n\\n        default:\\n            continue;\\n    }\\n    cy += this.lineHeight();\\n    if (cy + this.lineHeight() > this.innerHeight) {\\n        cy = by;\\n        cx += cw + (this.itemPadding() * 2);\\n    }\\n}\"","Vocabulary":"","Learned:str":"Learned","Requirements":"","RequireFmt:str":"Requires %1","ReqSeparateFmt:str":"%1, %2","ReqLevelFmt:str":"\\C[16]%3\\C[0]%1","ReqSkillFmt:str":"%1\\C[16]%2\\C[0]","ReqSwitchFmt:str":"\\C[16]%1\\C[0]","Costs":"","SeparationFmt:str":"%1  %2","ItemFmt:str":"×%1%2","WeaponFmt:str":"×%1%2","ArmorFmt:str":"×%1%2","GoldFmt:str":"×%1%2"}
 *
 * @param MenuAccess:struct
 * @text Menu Access Settings
 * @parent Scene_SkillLearn
 * @type struct<MenuAccess>
 * @desc Menu Access settings for Skill Learn System.
 * @default {"Name:str":"Learn","Icon:num":"87","ShowMenu:eval":"true"}
 *
 * @param Animation:struct
 * @text Animation Settings
 * @parent Scene_SkillLearn
 * @type struct<Animation>
 * @desc Animation settings for the Skill Learn System.
 * @default {"General":"","ShowAnimations:eval":"true","ShowWindows:eval":"true","Animations:arraynum":"[\"40\",\"48\"]","Sprite":"","Scale:num":"8.0","FadeSpeed:num":"4"}
 *
 * @param Sound:struct
 * @text Learn Sound Effect
 * @parent Scene_SkillLearn
 * @type struct<Sound>
 * @desc Settings for the sound effect played when learning a new skill through the Skill Learn System.
 * @default {"name:str":"Skill3","volume:num":"90","pitch:num":"100","pan:num":"0"}
 *
 * @param Window:struct
 * @text Window Settings
 * @parent Scene_SkillLearn
 * @type struct<Window>
 * @desc Window settings for the Skill Learn System.
 * @default {"DetailWindow":"","Requirements":"","RequirementTitle:str":"\\C[16]%1%2 Requirements\\C[0]","ReqMetFmt:str":"\\C[24]✔ %1\\C[0]","ReqNotMetFmt:str":"\\C[0]✘ %1\\C[0]","ReqLevelFmt:str":"\\I[87]%2 %1 Reached","ReqSkillFmt:str":"%1%2 Learned","ReqSwitchFmt:str":"\\I[160]%1","Costs":"","LearningTitle:str":"\\C[16]Learning\\C[0] %1%2","IngredientName:str":"\\C[16]Resource\\C[0]","IngredientCost:str":"\\C[16]Cost\\C[0]","IngredientOwned:str":"\\C[16]Owned\\C[0]","DetailWindow_BgType:num":"0","DetailWindow_RectJS:func":"\"const skillWindowRect = this.itemWindowRect();\\nconst wx = skillWindowRect.x;\\nconst wy = skillWindowRect.y;\\nconst ww = skillWindowRect.width;\\nconst wh = skillWindowRect.height - this.calcWindowHeight(2, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","ConfirmWindow":"","ConfirmCmd:str":"\\I[164]Learn","CancelCmd:str":"\\I[168]Cancel","ConfirmWindow_BgType:num":"0","ConfirmWindow_RectJS:func":"\"const skillWindowRect = this.itemWindowRect();\\nconst ww = skillWindowRect.width;\\nconst wh = this.calcWindowHeight(2, false);\\nconst wx = skillWindowRect.x;\\nconst wy = skillWindowRect.y + skillWindowRect.height - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param Resources
 *
 * @param AbilityPoints:struct
 * @text Ability Points Settings
 * @parent Resources
 * @type struct<AbilityPoints>
 * @desc Settings for Ability Points and how they work in-game.
 * @default {"Mechanics":"","SharedResource:eval":"true","DefaultCost:num":"0","MaxResource:num":"0","Visual":"","ShowInMenus:eval":"true","Icon:num":"78","Vocabulary":"","FullText:str":"Ability Points","AbbrText:str":"AP","TextFmt:str":"%1 \\c[5]%2\\c[0]%3","Gain":"","PerAction:str":"10 + Math.randomInt(5)","PerLevelUp:str":"0","PerEnemy:str":"50 + Math.randomInt(10)","AliveActors:eval":"true","Victory":"","ShowVictory:eval":"true","VictoryText:str":"%1 gains %2 %3!","AftermathActorDisplay:eval":"true","AftermathText:str":"+%1 %2"}
 *
 * @param SkillPoints:struct
 * @text Skill Points Settings
 * @parent Resources
 * @type struct<SkillPoints>
 * @desc Settings for Skill Points and how they work in-game.
 * @default {"Mechanics":"","SharedResource:eval":"false","DefaultCost:num":"1","MaxResource:num":"0","Visual":"","ShowInMenus:eval":"true","Icon:num":"79","Vocabulary":"","FullText:str":"Skill Points","AbbrText:str":"SP","TextFmt:str":"%1 \\c[5]%2\\c[0]%3","Gain":"","PerAction:str":"0","PerLevelUp:str":"100","PerEnemy:str":"0","AliveActors:eval":"true","Victory":"","ShowVictory:eval":"false","VictoryText:str":"%1 gains %2 %3!","AftermathActorDisplay:eval":"false","AftermathText:str":"+%1 %2"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param Visual
 *
 * @param DisplayedCosts:arraystr
 * @text Displayed Costs
 * @parent Visual
 * @type select[]
 * @option AP - Ability Points
 * @value AP
 * @option CP - Class Points (Requires VisuMZ_2_ClassChangeSystem)
 * @value CP
 * @option JP - Job Points (Requires VisuMZ_2_ClassChangeSystem)
 * @value JP
 * @option SP - Skill Points
 * @value SP
 * @option Item - Item Costs
 * @value Item
 * @option Weapon - Weapon Costs
 * @value Weapon
 * @option Armor - Armor Costs
 * @value Armor
 * @option Gold - Gold Costs
 * @value Gold
 * @desc Select which cost types to display in the skill entry.
 * This also determines the order they are displayed.
 * @default ["AP","SP","Item","Weapon","Armor","Gold"]
 *
 * @param StatusWindowDrawJS:func
 * @text JS: Draw Status
 * @parent Visual
 * @type note
 * @desc JavaScript code used to draw in Window_SkillStatus when the Skill Learn System is active.
 * @default "// Draw Face\nconst fx = this.colSpacing() / 2;\nconst fh = this.innerHeight;\nconst fy = fh / 2 - this.lineHeight() * 1.5;\nthis.drawActorFace(this._actor, fx + 1, 0, 144, fh);\nthis.drawActorSimpleStatus(this._actor, fx + 180, fy);\n\n// Return if Window Size is Too Small\nlet sx = (this.colSpacing() / 2) + 180 + 180 + 180;\nlet sw = this.innerWidth - sx - 2;\nif (sw < 300) return;\n\n// Draw Costs\n// Compatibility Target\nconst costs = this.getSkillLearnDisplayedCosts();\nconst maxEntries = Math.floor(this.innerHeight / this.lineHeight());\nconst maxCol = Math.ceil(costs.length / maxEntries);\nlet cx = sx;\nlet cy = Math.max(Math.round((this.innerHeight - (this.lineHeight() * Math.ceil(costs.length / maxCol))) / 2), 0);\nconst by = cy;\nlet cw = (this.innerWidth - cx - (this.itemPadding() * 2 * maxCol)) / maxCol;\nif (maxCol === 1) {\n    cw = Math.min(ImageManager.faceWidth, cw);\n    cx += Math.round((this.innerWidth - cx - (this.itemPadding() * 2) - cw) / 2);\n}\nfor (const cost of costs) {\n    switch (cost) {\n\n        case 'AP':\n            this.drawActorAbilityPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\n            break;\n\n        case 'CP':\n            if (Imported.VisuMZ_2_ClassChangeSystem) {\n                this.drawActorClassPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\n            }\n            break;\n\n        case 'JP':\n            if (Imported.VisuMZ_2_ClassChangeSystem) {\n                this.drawActorJobPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\n            }\n            break;\n\n        case 'SP':\n            this.drawActorSkillPoints(this._actor, this._actor.currentClass().id, cx, cy, cw, 'right');\n            break;\n\n        case 'Gold':\n            this.drawCurrencyValue($gameParty.gold(), TextManager.currencyUnit, cx, cy, cw);\n            break;\n\n        default:\n            continue;\n    }\n    cy += this.lineHeight();\n    if (cy + this.lineHeight() > this.innerHeight) {\n        cy = by;\n        cx += cw + (this.itemPadding() * 2);\n    }\n}"
 *
 * @param Vocabulary
 *
 * @param Learned:str
 * @text Learned Text
 * @parent Vocabulary
 * @desc This is the text that appears if the skill has been
 * learned. You may use text codes.
 * @default Learned
 *
 * @param Requirements
 * @parent Vocabulary
 *
 * @param RequireFmt:str
 * @text Requirement Header
 * @parent Requirements
 * @desc Header for requirements.
 * %1 - Requirements (all of them)
 * @default Requires %1
 *
 * @param ReqSeparateFmt:str
 * @text Separation Format
 * @parent Requirements
 * @desc This determines how the requirements are separated.
 * %1 - Previous Requirement, %2 - Second Requirement
 * @default %1, %2
 *
 * @param ReqLevelFmt:str
 * @text Level Format
 * @parent Requirements
 * @desc This how level is displayed.
 * %1 - Level, %2 - Full Level Term, %3 - Abbr Level Term
 * @default \C[16]%3\C[0]%1
 *
 * @param ReqSkillFmt:str
 * @text Skill Format
 * @parent Requirements
 * @desc This how required skills are displayed.
 * %1 - Icon, %2 - Skill Name
 * @default %1\C[16]%2\C[0]
 *
 * @param ReqSwitchFmt:str
 * @text Switch Format
 * @parent Requirements
 * @desc This how required switches are displayed.
 * %1 - Switch Name
 * @default \C[16]%1\C[0]
 *
 * @param Costs
 * @parent Vocabulary
 *
 * @param SeparationFmt:str
 * @text Separation Format
 * @parent Costs
 * @desc This determines how the costs are separated from one another.
 * %1 - Previous Cost, %2 - Second Cost
 * @default %1  %2
 *
 * @param ItemFmt:str
 * @text Item Format
 * @parent Costs
 * @desc Determine how items are displayed as a cost.
 * %1 - Quantity, %2 - Icon, %3 - Item Name
 * @default ×%1%2
 *
 * @param WeaponFmt:str
 * @text Weapon Format
 * @parent Costs
 * @desc Determine how weapons are displayed as a cost.
 * %1 - Quantity, %2 - Icon, %3 - Weapon Name
 * @default ×%1%2
 *
 * @param ArmorFmt:str
 * @text Armor Format
 * @parent Costs
 * @desc Determine how armors are displayed as a cost.
 * %1 - Quantity, %2 - Icon, %3 - Armor Name
 * @default ×%1%2
 *
 * @param GoldFmt:str
 * @text Gold Format
 * @parent Costs
 * @desc Determine how gold is displayed as a cost.
 * %1 - Quantity, %2 - Icon, %3 - Currency Vocabulary
 * @default ×%1%2
 *
 */
/* ----------------------------------------------------------------------------
 * MenuAccess Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~MenuAccess:
 *
 * @param Name:str
 * @text Command Name
 * @desc Name of the 'Skill Learn' option in the Menu.
 * @default Learn
 *
 * @param Icon:num
 * @text Icon
 * @desc What is the icon you want to use to represent Skill Learn?
 * @default 87
 *
 * @param ShowMenu:eval
 * @text Show in Menu?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Add the 'Skill Learn' option to the Menu by default?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Animation Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Animation:
 *
 * @param General
 *
 * @param ShowAnimations:eval
 * @text Show Animations?
 * @parent General
 * @type boolean
 * @on Show
 * @off Skip
 * @desc Show animations when learning a skill?
 * @default true
 *
 * @param ShowWindows:eval
 * @text Show Windows?
 * @parent General
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show windows during a skill learn animation?
 * @default false
 *
 * @param Animations:arraynum
 * @text Default Animations
 * @parent General
 * @type animation[]
 * @desc Default animation(s) do you want to play when learning.
 * @default ["40","48"]
 *
 * @param Sprite
 * @text Skill Sprite
 *
 * @param Scale:num
 * @text Scale
 * @parent Sprite
 * @desc How big do you want the skill sprite to be on screen?
 * @default 8.0
 *
 * @param FadeSpeed:num
 * @text Fade Speed
 * @parent Sprite
 * @type number
 * @min 1
 * @desc How fast do you want the icon to fade in?
 * @default 4
 *
 */
/* ----------------------------------------------------------------------------
 * Sound Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Sound:
 *
 * @param name:str
 * @text Filename
 * @type file
 * @dir audio/se/
 * @desc Filename of the sound effect played.
 * @default Skill3
 *
 * @param volume:num
 * @text Volume
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param pitch:num
 * @text Pitch
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 100
 *
 * @param pan:num
 * @text Pan
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Window Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Window:
 *
 * @param DetailWindow
 * @text Detail Window
 *
 * @param Requirements
 * @parent DetailWindow
 *
 * @param RequirementTitle:str
 * @text Requirement Title
 * @parent Requirements
 * @desc Text used when drawing the learning requirements.
 * %1 - Skill Icon, %2 - Skill Name
 * @default \C[16]%1%2 Requirements\C[0]
 *
 * @param ReqMetFmt:str
 * @text Requirement Met
 * @parent Requirements
 * @desc This how met requirements look.
 * %1 - Requirement Text
 * @default \C[24]✔ %1\C[0]
 *
 * @param ReqNotMetFmt:str
 * @text Requirement Not Met
 * @parent Requirements
 * @desc This how met requirements look.
 * %1 - Requirement Text
 * @default \C[0]✘ %1\C[0]
 *
 * @param ReqLevelFmt:str
 * @text Requirement Level
 * @parent Requirements
 * @desc This how level is displayed.
 * %1 - Level, %2 - Full Level Term, %3 - Abbr Level Term
 * @default \I[87]%2 %1 Reached
 *
 * @param ReqSkillFmt:str
 * @text Requirement Skill
 * @parent Requirements
 * @desc This how required skills are displayed.
 * %1 - Icon, %2 - Skill Name
 * @default %1%2 Learned
 *
 * @param ReqSwitchFmt:str
 * @text Requirement Switch
 * @parent Requirements
 * @desc This how required switches are displayed.
 * %1 - Switch Name
 * @default \I[160]%1
 *
 * @param Costs
 * @parent DetailWindow
 *
 * @param LearningTitle:str
 * @text Cost Title
 * @parent Costs
 * @desc Text used when drawing the learning costs.
 * %1 - Skill Icon, %2 - Skill Name
 * @default \C[16]Learning\C[0] %1%2
 *
 * @param IngredientName:str
 * @text Cost Name
 * @parent Costs
 * @desc Text used to label the resource being consumed.
 * @default \C[16]Resource\C[0]
 *
 * @param IngredientCost:str
 * @text Cost Quantity
 * @parent Costs
 * @desc Text used to label the cost of the resource.
 * @default \C[16]Cost\C[0]
 *
 * @param IngredientOwned:str
 * @text Cost of Owned
 * @parent Costs
 * @desc Text used to label the amount of the resource in possession.
 * @default \C[16]Owned\C[0]
 *
 * @param DetailWindow_BgType:num
 * @text Background Type
 * @parent DetailWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DetailWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent DetailWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const skillWindowRect = this.itemWindowRect();\nconst wx = skillWindowRect.x;\nconst wy = skillWindowRect.y;\nconst ww = skillWindowRect.width;\nconst wh = skillWindowRect.height - this.calcWindowHeight(2, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param ConfirmWindow
 * @text Confirm Window
 *
 * @param ConfirmCmd:str
 * @text Confirm Text
 * @parent ConfirmWindow
 * @desc Text used for the Confirm command.
 * Text codes can be used.
 * @default \I[164]Learn
 *
 * @param CancelCmd:str
 * @text Cancel Text
 * @parent ConfirmWindow
 * @desc Text used for the Cancel command.
 * Text codes can be used.
 * @default \I[168]Cancel
 *
 * @param ConfirmWindow_BgType:num
 * @text Background Type
 * @parent ConfirmWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param ConfirmWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent ConfirmWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const skillWindowRect = this.itemWindowRect();\nconst ww = skillWindowRect.width;\nconst wh = this.calcWindowHeight(2, false);\nconst wx = skillWindowRect.x;\nconst wy = skillWindowRect.y + skillWindowRect.height - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Ability Points Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~AbilityPoints:
 *
 * @param Mechanics
 *
 * @param SharedResource:eval
 * @text Shared Ability Points
 * @parent Mechanics
 * @type boolean
 * @on Shared Across Classes
 * @off Classes Separate
 * @desc Do you want Ability Points to be shared across all classes?
 * Or do you want all classes to have their own?
 * @default true
 *
 * @param DefaultCost:num
 * @text Default Cost
 * @parent Mechanics
 * @type number
 * @desc What's the default AP cost of a skill when trying to learn
 * it through the Skill Learn System?
 * @default 0
 *
 * @param MaxResource:num
 * @text Maximum
 * @parent Mechanics
 * @type number
 * @desc What's the maximum amount of Ability Points an actor can have?
 * Use 0 for unlimited Ability Points.
 * @default 0
 *
 * @param Visual
 *
 * @param ShowInMenus:eval
 * @text Show In Menus?
 * @parent Visual
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Do you wish to show Ability Points in menus that allow them?
 * @default true
 *
 * @param Icon:num
 * @text Icon
 * @parent Visual
 * @desc What is the icon you want to use to represent Ability Points?
 * @default 78
 *
 * @param Vocabulary
 *
 * @param FullText:str
 * @text Full Text
 * @parent Vocabulary
 * @desc The full text of how Ability Points appears in-game.
 * @default Ability Points
 *
 * @param AbbrText:str
 * @text Abbreviated Text
 * @parent Vocabulary
 * @desc The abbreviation of how Ability Points appears in-game.
 * @default AP
 *
 * @param TextFmt:str
 * @text Menu Text Format
 * @parent Vocabulary
 * @desc What is the text format for it to be displayed in windows.
 * %1 - Value, %2 - Abbr, %3 - Icon, %4 - Full Text
 * @default %1 \c[5]%2\c[0]%3
 *
 * @param Gain
 *
 * @param PerAction:str
 * @text Per Action Hit
 * @parent Gain
 * @desc How many Ability Points should an actor gain per action?
 * You may use code.
 * @default 10 + Math.randomInt(5)
 *
 * @param PerLevelUp:str
 * @text Per Level Up
 * @parent Gain
 * @desc How many Ability Points should an actor gain per level up?
 * You may use code.
 * @default 0
 *
 * @param PerEnemy:str
 * @text Per Enemy Defeated
 * @parent Gain
 * @desc How many Ability Points should an actor gain per enemy?
 * You may use code.
 * @default 50 + Math.randomInt(10)
 *
 * @param AliveActors:eval
 * @text Alive Actors?
 * @parent PerEnemy:str
 * @type boolean
 * @on Alive Requirement
 * @off No Requirement
 * @desc Do actors have to be alive to receive Ability Points from
 * defeated enemies?
 * @default true
 *
 * @param Victory
 *
 * @param ShowVictory:eval
 * @text Show During Victory?
 * @parent Victory
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how much AP an actor has earned in battle during the
 * victory phase?
 * @default true
 *
 * @param VictoryText:str
 * @text Victory Text
 * @parent Victory
 * @desc For no Victory Aftermath, this is the text that appears.
 * %1 - Actor, %2 - Earned, %3 - Abbr, %4 - Full Text
 * @default %1 gains %2 %3!
 *
 * @param AftermathActorDisplay:eval
 * @text Aftermath Display?
 * @parent Victory
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Requires VisuMZ_3_VictoryAftermath. Show Ability Points as
 * the main acquired resource in the actor windows?
 * @default true
 *
 * @param AftermathText:str
 * @text Aftermath Text
 * @parent Victory
 * @desc For no Victory Aftermath, this is the text that appears.
 * %1 - Earned, %2 - Abbr, %3 - Full Text
 * @default +%1 %2
 *
 */
/* ----------------------------------------------------------------------------
 * Skill Points Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SkillPoints:
 *
 * @param Mechanics
 *
 * @param SharedResource:eval
 * @text Shared Skill Points
 * @parent Mechanics
 * @type boolean
 * @on Shared Across Classes
 * @off Classes Separate
 * @desc Do you want Skill Points to be shared across all classes?
 * Or do you want all classes to have their own?
 * @default false
 *
 * @param DefaultCost:num
 * @text Default Cost
 * @parent Mechanics
 * @type number
 * @desc What's the default SP cost of a skill when trying to learn
 * it through the Skill Learn System?
 * @default 1
 *
 * @param MaxResource:num
 * @text Maximum
 * @parent Mechanics
 * @type number
 * @desc What's the maximum amount of Skill Points an actor can have?
 * Use 0 for unlimited Skill Points.
 * @default 0
 *
 * @param Visual
 *
 * @param ShowInMenus:eval
 * @text Show In Menus?
 * @parent Visual
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Do you wish to show Skill Points in menus that allow them?
 * @default true
 *
 * @param Icon:num
 * @text Icon
 * @parent Visual
 * @desc What is the icon you want to use to represent Skill Points?
 * @default 79
 *
 * @param Vocabulary
 *
 * @param FullText:str
 * @text Full Text
 * @parent Vocabulary
 * @desc The full text of how Skill Points appears in-game.
 * @default Skill Points
 *
 * @param AbbrText:str
 * @text Abbreviated Text
 * @parent Vocabulary
 * @desc The abbreviation of how Skill Points appears in-game.
 * @default SP
 *
 * @param TextFmt:str
 * @text Menu Text Format
 * @parent Vocabulary
 * @desc What is the text format for it to be displayed in windows.
 * %1 - Value, %2 - Abbr, %3 - Icon, %4 - Full Text
 * @default %1 \c[4]%2\c[0]%3
 *
 * @param Gain
 *
 * @param PerAction:str
 * @text Per Action Hit
 * @parent Gain
 * @desc How many Skill Points should an actor gain per action?
 * You may use code.
 * @default 0
 *
 * @param PerLevelUp:str
 * @text Per Level Up
 * @parent Gain
 * @desc How many Skill Points should an actor gain per level up?
 * You may use code.
 * @default 100
 *
 * @param PerEnemy:str
 * @text Per Enemy Defeated
 * @parent Gain
 * @desc How many Skill Points should an actor gain per enemy?
 * You may use code.
 * @default 0
 *
 * @param AliveActors:eval
 * @text Alive Actors?
 * @parent PerEnemy:str
 * @type boolean
 * @on Alive Requirement
 * @off No Requirement
 * @desc Do actors have to be alive to receive Skill Points from
 * defeated enemies?
 * @default true
 *
 * @param Victory
 *
 * @param ShowVictory:eval
 * @text Show During Victory?
 * @parent Victory
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show how much SP an actor has earned in battle during the
 * victory phase?
 * @default false
 *
 * @param VictoryText:str
 * @text Victory Text
 * @parent Victory
 * @desc For no Victory Aftermath, this is the text that appears.
 * %1 - Actor, %2 - Earned, %3 - Abbr, %4 - Full Text
 * @default %1 gains %2 %3!
 *
 * @param AftermathActorDisplay:eval
 * @text Aftermath Display?
 * @parent Victory
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Requires VisuMZ_3_VictoryAftermath. Show Skill Points as
 * the main acquired resource in the actor windows?
 * @default false
 *
 * @param AftermathText:str
 * @text Aftermath Text
 * @parent Victory
 * @desc For no Victory Aftermath, this is the text that appears.
 * %1 - Earned, %2 - Abbr, %3 - Full Text
 * @default +%1 %2
 *
 */
//=============================================================================

const _0x3591d3 = _0x58d2;
(function (_0x505770, _0x228cfb) {
    const _0x2c75ab = _0x58d2,
        _0x99b53 = _0x505770();
    while (!![]) {
        try {
            const _0x236119 =
                parseInt(_0x2c75ab(0x213)) / 0x1 +
                parseInt(_0x2c75ab(0x18e)) / 0x2 +
                (parseInt(_0x2c75ab(0x246)) / 0x3) * (parseInt(_0x2c75ab(0x327)) / 0x4) +
                parseInt(_0x2c75ab(0xcf)) / 0x5 +
                parseInt(_0x2c75ab(0x180)) / 0x6 +
                parseInt(_0x2c75ab(0x334)) / 0x7 +
                -parseInt(_0x2c75ab(0x15b)) / 0x8;
            if (_0x236119 === _0x228cfb) break;
            else _0x99b53['push'](_0x99b53['shift']());
        } catch (_0x5908b1) {
            _0x99b53['push'](_0x99b53['shift']());
        }
    }
})(_0x3ca6, 0x4798f);
function _0x3ca6() {
    const _0x373de3 = [
        'oeBuh',
        'drawAbilityPoints',
        'makeRewards',
        'fjara',
        'gainAbilityPointsForMulticlasses',
        'qNniC',
        'SKILLS',
        'skill',
        'return\x200',
        'rMFzh',
        'destroySkillLearnAnimationSprite',
        'ConfirmCmd',
        'LearnWeaponCost',
        'ItemFmt',
        'VisuMZ_2_ClassChangeSystem',
        'FadeSpeed',
        'skillLearnConfirmWindow',
        'learnEquippedPassive',
        'registerCommand',
        'skillLearnReqListSkill',
        'isTriggered',
        'jWwkc',
        'uzudU',
        'isBattleMember',
        'SWITCHES',
        'createSkillLearnSystemWindows',
        '_SkillLearnSystem_preventLevelUpGain',
        'Show',
        'uEYOG',
        'applyItemSkillLearnSystemUserEffect',
        'createConditionJS',
        'reduce',
        'drawItem',
        'xyOzQ',
        'QPUDb',
        'Game_Battler_onBattleStart',
        'refresh',
        'skillLearnReqSeparatorFmt',
        'createSkillLearnConfirmWindow',
        'playSkillLearn',
        'dSuHV',
        'makeCommandList',
        'DetailWindow_RectJS',
        'cNCrA',
        'LearnShowSkillsAll',
        'drawSkillPoints',
        'skillLearnItemFmt',
        'Points',
        'addWindow',
        '_skillLearnIngredientsWindow',
        'IlEwB',
        'abilityPointsVisible',
        'Armor-%1-%2',
        'ReqSwitchFmt',
        'Scene_Skill_update',
        'gmVsr',
        'skillLearningName',
        'onDatabaseLoaded',
        'Game_Actor_changeClass',
        'Class-%1-%2',
        'rUEWf',
        'Animation',
        'initAbilityPoints',
        'eWcDg',
        'LearnArmorCost',
        'updateSkillLearnAnimationSprite',
        'getWeaponIdWithName',
        'createKeyJS',
        'pabyQ',
        'skillLearnSeparationFmt',
        'LearnReqSwitchesAny',
        'WlOKT',
        '_scene',
        'setAbilityPoints',
        'gnqrw',
        'SystemShowSkillLearnSystemMenu',
        'jsLearnReq',
        'displayRewardsSkillPoints',
        'loseClassPoints',
        'IngredientOwned',
        '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20enabled\x20=\x20true;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Condition\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20enabled;\x0a\x20\x20\x20\x20\x20\x20\x20\x20',
        'createSkillLearnIngredientsWindow',
        'ARRAYJSON',
        'skillPointsIcon',
        'Nbwve',
        '6404BFbSaM',
        'hTFwN',
        'getSkillLearnSkillsFromClass',
        'skillLearnGoldFmt',
        'indexOf',
        'skillLearnWeaponFmt',
        'actor',
        'LearnReqSkillsAll',
        'learnSkill',
        'shift',
        'drawSkillLearnRequirements',
        'fbIXD',
        'Window_SkillList_isEnabled',
        '3436062HLvQrv',
        'lVGaM',
        'mPTMd',
        'kyBfn',
        'gmOfr',
        'oRQrq',
        'applySkillPoints',
        '_skillLearnIconSpriteOpacitySpeed',
        'applyItemUserEffect',
        'abilityPointsRate',
        'xtBpC',
        'isMVAnimation',
        'skillPointsFmt',
        'sjppq',
        'commandName',
        'ztiNL',
        'PerAction',
        'text',
        'loadSystem',
        'width',
        'FuEgv',
        'ReqNotMetFmt',
        'ReqSkillFmt',
        'uEVEj',
        'CUSTOM',
        'Window_SkillList_includes',
        'select',
        'gnbJa',
        'updateSkillLearnAnimation',
        'setFrame',
        'eeWrr',
        'MaxResource',
        '_actor',
        'jobPointsFull',
        'max',
        'setup',
        'createSkillLearnSkillSprite',
        'classPointsIcon',
        'gainRewardsSkillPoints',
        'Game_Action_applyItemUserEffect',
        'getSkillLearnRequirementText',
        '\x5cI[%1]',
        'LEVEL',
        'meetRequirementsForSkillLearnSystem',
        'jxOQr',
        'QkTCj',
        '_abilityPoints',
        'sKlod',
        '_weaponIDs',
        'textSizeEx',
        'jsLearnReqDetailTxt',
        'right',
        'createSkillLearnAnimation',
        'JXLsH',
        'addCommand',
        'skillLearnIcon',
        'EkaRy',
        'fOdDJ',
        'skillPointsRate',
        'kCsck',
        'PbGon',
        'AbilityPointsSet',
        'svXmb',
        'PJwfP',
        'General',
        'tcWEB',
        'RegExp',
        'NWOQe',
        'setBackgroundType',
        'ShiMT',
        'uqxIF',
        'DChiB',
        'show',
        '_learnPicture',
        'LearnReqSwitchesAll',
        'test',
        'shouldDrawSkillLearnRequirements',
        'setSkillLearnSystemMenuAccess',
        'clear',
        'gainStartingAbilityPoints',
        'changePaintOpacity',
        'JylRE',
        '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20cost\x20=\x200;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Cost\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20cost;\x0a\x20\x20\x20\x20\x20\x20\x20\x20',
        'Adymi',
        'STRUCT',
        'RWNGk',
        'AbbrText',
        'itemLineRect',
        'ShowAnimations',
        'ExPna',
        'initialize',
        'BattleManager_displayRewards',
        'Game_Actor_learnSkill',
        'sort',
        '_stypeId',
        'getSkillLearnClassPointCost',
        '_earnedAbilityPoints',
        'rPwzU',
        'getSkillLearnAbilityPointCost',
        '_skillLearnBitmapSprite',
        'nSOHc',
        'setSkillLearnSkillSpritePosition',
        'makeRewardsSkillPoints',
        'Meslh',
        'parse',
        'LearnReqSkillsAny',
        'drawActorFace',
        '_skillLearnAnimationIDs',
        'PqsxD',
        'round',
        'CoreEngine',
        'earnedAbilityPoints',
        'ConfirmWindow_RectJS',
        'LLmkH',
        'clamp',
        'create',
        'EnemyAbilityPoints',
        '2672070EcxOwo',
        'drawJobPoints',
        'ZzMhw',
        'isSkillLearnEnabled',
        'newPage',
        'SGbiH',
        'Window_SkillStatus_refresh',
        'isAlive',
        'Parse_Notetags_CreateJS',
        'resetFontSettings',
        'concat',
        'Game_Actor_setup',
        'maxCols',
        'IDIto',
        'Actors',
        'icJEj',
        'JNXXx',
        'drawActorClassPoints',
        'isEnabled',
        'getSkillLearnWeaponCost',
        'Icon',
        'qFmBM',
        'toUpperCase',
        'ceil',
        'UXxiR',
        'getSkillLearnSkillPointCost',
        'LearnReqLevel',
        'SkillPointsRate',
        'enemy',
        'jsLearnCpCost',
        '_windowLayer',
        'level',
        'Scene_Skill_create',
        'skillLearnReqNotMet',
        'createActionJS',
        'levelUpGainAbilityPoints',
        'cnUfZ',
        'KeFHi',
        'ozEhF',
        'classPointsFmt',
        'skillTypes',
        'GoldFmt',
        'startSkillLearnAnimation',
        'UserGainSkillPoints',
        'drawActorJobPoints',
        'TargetGainSkillPoints',
        'SkillLearnSystem',
        'abilityPoints',
        'isLearnedEquippedPassive',
        'joryr',
        'canPayForSkillLearnSystem',
        'LPifH',
        'cFnfb',
        'KcBfd',
        'AvYsi',
        'skillLearnReqHeaderFmt',
        'members',
        'value',
        'ReqSeparateFmt',
        'ITEM',
        'Classes',
        'ILNzN',
        'animationIDs',
        'YewwI',
        'UcWTk',
        'LHQQL',
        'RequireFmt',
        '_skillPoints',
        'LearnApCost',
        'smooth',
        'cssTr',
        'Xmdut',
        'STR',
        'processFinishSkillLearnAnimation',
        'LvPvp',
        'SkillPoints',
        'PpyZI',
        'showVisualGoldDisplay',
        'fDHlV',
        '_SkillLearnSystem_MenuAccess',
        'skillLearningCost',
        'skillLearnReqListLevel',
        'isReleased',
        'WTWTo',
        'RznZW',
        'jobPointsAbbr',
        'abilityPointsAbbr',
        'LearnJpCost',
        'PhYEB',
        'qyDoO',
        'playStaticSe',
        'createSkillLearnAnimationIDs',
        'oaRFg',
        'vwWdS',
        '_data',
        'kbxSM',
        'abilityPointsFull',
        'setHandler',
        'rEYYp',
        'WEAPON',
        'AbilityPointsAdd',
        'SkillPointsLose',
        'DetailWindow_BgType',
        'center',
        'GVGzo',
        'kOZhM',
        'maxTurns',
        'commandStyle',
        '_armorIDs',
        'rDUWx',
        'classPointsAbbr',
        'AbilityPoints',
        'iconWidth',
        'constructor',
        'scale',
        'removeChild',
        'applySkillLearnSystemUserEffect',
        'earnedSkillPoints',
        'skillPointsTotal',
        'gainMulticlassRewardPoints',
        'skillLearningTitle',
        'note',
        'filter',
        'remove',
        'SkillPointsSet',
        'OkwME',
        'parameters',
        'wQwPi',
        'StartClassSkillPoints',
        'setStypeId',
        'LearnItemCost',
        'getSkillLearnGoldCost',
        'Uzrpk',
        'isCommandEnabled',
        'makeRewardsAbilityPoints',
        'innerHeight',
        'abilityPointsIcon',
        'levelUp',
        'Hvouo',
        'skillLearnArmorFmt',
        '13815400ijNQIc',
        'LearnCpCost',
        'skillPoints',
        'SkillPointsAdd',
        'min',
        'loseGold',
        'match',
        '_itemIDs',
        'addSkillPoints',
        'RFCzU',
        'skillPointsFull',
        'onLoadBattleTestSkillLearnSystem',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'split',
        'LearnSkillA',
        'jsLearnSpCost',
        'isPlaying',
        'RequirementTitle',
        'onSkillLearnItemOk',
        'ClassChangeSystem',
        'NlOPn',
        'gOTBF',
        'cIVAS',
        'PHzQa',
        'jsLearnShow',
        'bRQMZ',
        'hide',
        'bLwPf',
        'BattleManager_makeRewards',
        'Armor',
        'drawRequirements',
        'displayRewards',
        'StKMJ',
        'FullText',
        '_statusWindow',
        'getItemIdWithName',
        'qvVmB',
        '1338690ajtVyT',
        'Game_Actor_levelUp',
        'skillLearn',
        'jsLearnShowListTxt',
        'makeSkillLearnList',
        'setupBattleTestMembers',
        'bitmap',
        'getSkillLearnJobPointCost',
        'YNekW',
        'loseSkillPoints',
        'Window_SkillList_setStypeId',
        'drawTextExCenterAlign',
        'getSkillLearnDisplayedCosts',
        'DisplayedCosts',
        '39900xKNOPM',
        'processPayForSkillLearnSystem',
        'fPHmh',
        'loseItem',
        'LearnCostBatch',
        'Scale',
        '_rewards',
        'VisuMZ_1_SkillsStatesCore',
        'onBattleStart',
        'ZWCNj',
        '_skillIDs',
        'getArmorIdWithName',
        'abilityPointsTotal',
        'isPlaytest',
        'createVisibleJS',
        'itemWindowRect',
        'Jbdsd',
        'oIUKm',
        '_earnedSkillPoints',
        'bpufu',
        'allMembers',
        'length',
        '\x5cI[%1]%2',
        'ARMOR',
        'YDaDB',
        'log',
        'FlVWJ',
        'getSkillIdWithName',
        'drawCurrencyValue',
        'calcWindowHeight',
        'getSkillLearnArmorCost',
        'prototype',
        'ShowWindows',
        'PZrzT',
        'JobPoints',
        'FryHh',
        '_skillLearnAnimationWait',
        'VisuMZ_2_EquipPassiveSys',
        'inBattle',
        'makeSkillLearnPassivesList',
        'VmNzM',
        'refreshSkillLearnSystem',
        'shouldDrawRequirements',
        'Weapon-%1-%2',
        'VKNeW',
        'UlJtp',
        'traitObjects',
        'MAX_SAFE_INTEGER',
        'jsLearnShowDetailTxt',
        'displayRewardsAbilityPoints',
        'Scene_Skill_onItemOk',
        'AApKJ',
        'levelA',
        'wZVxm',
        'edafB',
        'Window_SkillList_maxCols',
        'bind',
        'aKKpJ',
        'EVAL',
        'OKIyH',
        'loseAbilityPoints',
        'gainSkillPoints',
        'map',
        'ParseSkillNotetags',
        'Window_SkillList_drawSkillCost',
        'CancelCmd',
        'GBYyx',
        'call',
        'setSkillPoints',
        'JKWeC',
        'skillLearnReqSkillFmt',
        'dlutY',
        'skillLearnCmd',
        'gainSkillPointsForMulticlasses',
        'LearnGoldCost',
        'drawActorAbilityPoints',
        'oWrEV',
        'Window',
        'changeClass',
        'gainStartingSkillPoints',
        'drawTextEx',
        '_skillLearnConfirmWindow',
        'ZQDec',
        'NUM',
        'BGtum',
        'rtGJn',
        'Custom',
        '_skillLearnAnimationPlaying',
        'initSkillPoints',
        'GNnnA',
        'contents',
        'classPointsFull',
        'IconSet',
        'PerEnemy',
        '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x20\x20\x20\x20\x20\x20\x20\x20',
        'process_VisuMZ_SkillLearnSystem_Notetags',
        'ShowMenu',
        'status',
        'HIgnC',
        'tdGNu',
        'onSkillLearnConfirmCancel',
        'innerWidth',
        'LearnSkillB',
        'skillLearnReqTitle',
        'applyAbilityPoints',
        'TextFmt',
        'RjKMR',
        'DGvmR',
        'Skill',
        'IzJNb',
        'StartingAbilityPoints',
        'ANrWa',
        'SharedResource',
        'getClassIdWithName',
        'UcsNE',
        'itemPadding',
        'isSkillLearnMode',
        'isActor',
        'getClassPoints',
        'ConfirmWindow_BgType',
        'HNzXt',
        'Settings',
        'VisuMZ_0_CoreEngine',
        'ReqLevelFmt',
        'skillLearnConfirmCmd',
        'jsLearnReqListTxt',
        'tIUNI',
        'vAqcC',
        'State-%1-%2',
        'PerLevelUp',
        'name',
        'AwNpz',
        'gpiIe',
        '173882YmwnUC',
        'exit',
        'drawTextExRightAlign',
        'user',
        'playOkSound',
        'skillLearnIngredientsWindowRect',
        'setupBattleTestMembersSkillLearnSystem',
        'DefaultCost',
        'Scene_Boot_onDatabaseLoaded',
        'TaCIY',
        'Gold',
        'getAbilityPoints',
        'skillLearnAlreadyLearned',
        'LearnSpCost',
        'makeItemList',
        'jsLearnApCost',
        '_skillLearnIconSprite',
        'yBDNR',
        'opacity',
        'destroySkillLearnSprite',
        'skillLearningOwned',
        'LearnShowSkillsAny',
        'fTuls',
        'currentClass',
        'AliveActors',
        'xzIDa',
        'isSkill',
        'SaEdN',
        'addAbilityPoints',
        'left',
        'gold',
        'STASf',
        'MFpyp',
        'anchor',
        'iEcZT',
        'numItems',
        'VictoryText',
        'oZBbU',
        'AYytw',
        'Learned',
        'dQitf',
        'cancel',
        'destroy',
        'skillPointsAbbr',
        'update',
        '_classIDs',
        'UutdB',
        'format',
        'cagNZ',
        'Skill-%1-%2',
        'TpQda',
        '1083DAtWTh',
        'getSkillLearnItemCost',
        'createSkillLearnCostText',
        'ParseAllNotetags',
        'colSpacing',
        'description',
        'Window_SkillList_makeItemList',
        'skillLearnReqSwitchFmt',
        'DhOxX',
        'sIpRf',
        'CRdgb',
        'TMnWh',
        'UserGainAbilityPoints',
        'uMWXa',
        'abilityPointsFmt',
        'onItemOk',
        'process_VisuMZ_SkillLearnSystem_JS',
        'ARRAYNUM',
        'ARRAYSTR',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'trim',
        'fvLjZ',
        'NRyfV',
        'currencyUnit',
        'Window_SkillType_makeCommandList',
        'finishSkillLearnAnimation',
        'Game_Party_setupBattleTestMembers',
        'drawClassPoints',
        'createTextJS',
        'Window_SkillList_drawItem',
        'YsptF',
        'LearnShowLevel',
        'drawSkillLearnCost',
        'ALZez',
        'subject',
        'SkuCY',
        'ConvertParams',
        'SkgoJ',
        'TNAGU',
        'replace',
        'makeDeepCopy',
        'TFrxy',
        'cnniz',
        'optExtraExp',
        'AbilityPointsGain',
        'aAxJl',
        'ifpis',
        'setSkillLearnSkillSpriteOpacity',
        '_skillLearnSystem_drawItemMode',
        'getSkillPoints',
        'lcJMx',
        'drawActorSkillPoints',
        'Ability',
        'qgMUN',
        'lDjPh',
        'MDCMJ',
        'XdeBa',
        'ShowVictory',
        'LearnShowSwitchesAny',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'eLztx',
        'MenuAccess',
        'item',
        'autoRemovalTiming',
        'BGpOt',
        'zPjWK',
        'drawSkillCost',
        'zeknb',
        'jsLearnJpCost',
        'getJobPoints',
        'AbilityPointsRate',
        'isLearnedSkill',
        'Game_System_initialize',
        'skillPointsVisible',
        'pwSpA',
        'addSkillLearnSystemCommand',
        'includes',
        'avFRO',
        'add',
        'version',
        'gainRewardsAbilityPoints',
        'getEquipPassiveIcon',
        'skillLearnReqLevelFmt',
        'WGXMT',
        'isFinishedSkillLearnAnimating',
        'isState',
        'drawActorSimpleStatus',
        'switches',
        'skillLearnReqListSwitch',
        'akqPg',
        'height',
        'StatusWindowDrawJS',
        'setSkillLearnSkillSpriteFrame',
        'Weapon',
        'Enemy-%1-%2',
        'drawItemName',
        'oKhzI',
        'addChild',
        'jobPointsIcon',
        '_skillLearnAnimationSprite',
        'createCostJS',
        'ACuhZ',
        'visible',
        'initSkillLearnSystemMenuAccess',
        'eXARz',
        'qiskn',
        'iconIndex',
        '_itemWindow',
        'floor',
        'lineHeight',
        'gainAbilityPoints',
        'push',
        'faceWidth',
        'jsOnLearn',
        'MIalW',
        'FAjRc',
        'onSkillLearnConfirmOk',
        'skillLearnSystemCommandName',
        'quantity',
        'activate',
        'skillLearnCancelCmd',
        'opacitySpeed',
        'itemHeight',
        'iconHeight',
        'xsvKE',
        'StartingSkillPoints',
        'updateSkillLearnSpriteOpacity',
        'NNFAX',
        '%1%2',
        'gMbJj',
        'TargetGainAbilityPoints',
        'levelUpGainSkillPoints',
        'bigPicture',
        'SeparationFmt',
        'zBqis',
        'deadMembers',
        'SkillPointsGain',
        'jobPointsFmt',
        'nCCkG',
        'isSkillLearnSystemMenuAccess',
    ];
    _0x3ca6 = function () {
        return _0x373de3;
    };
    return _0x3ca6();
}
var label = _0x3591d3(0xfd),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x3591d3(0x149)](function (_0x340530) {
        const _0x1c289a = _0x3591d3;
        return (
            _0x340530[_0x1c289a(0x1ef)] &&
            _0x340530[_0x1c289a(0x24b)][_0x1c289a(0x292)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label]['Settings'] = VisuMZ[label][_0x3591d3(0x207)] || {}),
    (VisuMZ[_0x3591d3(0x26a)] = function (_0x91bfea, _0x15b00b) {
        const _0x549000 = _0x3591d3;
        for (const _0x41b9c2 in _0x15b00b) {
            if (_0x41b9c2[_0x549000(0x161)](/(.*):(.*)/i)) {
                const _0x398f7d = String(RegExp['$1']),
                    _0x4d9d83 = String(RegExp['$2'])[_0x549000(0xe5)]()['trim']();
                let _0x43d686, _0x32f7b1, _0x5df561;
                switch (_0x4d9d83) {
                    case _0x549000(0x1e1):
                        _0x43d686 =
                            _0x15b00b[_0x41b9c2] !== '' ? Number(_0x15b00b[_0x41b9c2]) : 0x0;
                        break;
                    case _0x549000(0x257):
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2])
                                : []),
                            (_0x43d686 = _0x32f7b1['map'](_0x4c1f42 => Number(_0x4c1f42))));
                        break;
                    case _0x549000(0x1c8):
                        _0x43d686 = _0x15b00b[_0x41b9c2] !== '' ? eval(_0x15b00b[_0x41b9c2]) : null;
                        break;
                    case 'ARRAYEVAL':
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2])
                                : []),
                            (_0x43d686 = _0x32f7b1[_0x549000(0x1cc)](_0x5107a1 =>
                                eval(_0x5107a1)
                            )));
                        break;
                    case 'JSON':
                        _0x43d686 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2])
                                : '';
                        break;
                    case _0x549000(0x324):
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2])
                                : []),
                            (_0x43d686 = _0x32f7b1[_0x549000(0x1cc)](_0x5d1288 =>
                                JSON['parse'](_0x5d1288)
                            )));
                        break;
                    case 'FUNC':
                        _0x43d686 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? new Function(JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2]))
                                : new Function(_0x549000(0x2da));
                        break;
                    case 'ARRAYFUNC':
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== '' ? JSON['parse'](_0x15b00b[_0x41b9c2]) : []),
                            (_0x43d686 = _0x32f7b1[_0x549000(0x1cc)](
                                _0x5ccfe6 => new Function(JSON['parse'](_0x5ccfe6))
                            )));
                        break;
                    case _0x549000(0x117):
                        _0x43d686 = _0x15b00b[_0x41b9c2] !== '' ? String(_0x15b00b[_0x41b9c2]) : '';
                        break;
                    case _0x549000(0x258):
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== '' ? JSON['parse'](_0x15b00b[_0x41b9c2]) : []),
                            (_0x43d686 = _0x32f7b1[_0x549000(0x1cc)](_0x27cf9e =>
                                String(_0x27cf9e)
                            )));
                        break;
                    case _0x549000(0xae):
                        ((_0x5df561 =
                            _0x15b00b[_0x41b9c2] !== '' ? JSON['parse'](_0x15b00b[_0x41b9c2]) : {}),
                            (_0x43d686 = VisuMZ[_0x549000(0x26a)]({}, _0x5df561)));
                        break;
                    case 'ARRAYSTRUCT':
                        ((_0x32f7b1 =
                            _0x15b00b[_0x41b9c2] !== ''
                                ? JSON[_0x549000(0xc2)](_0x15b00b[_0x41b9c2])
                                : []),
                            (_0x43d686 = _0x32f7b1[_0x549000(0x1cc)](_0x55f969 =>
                                VisuMZ['ConvertParams']({}, JSON['parse'](_0x55f969))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x91bfea[_0x398f7d] = _0x43d686;
            }
        }
        return _0x91bfea;
    }),
    (_0x2083a8 => {
        const _0x230f95 = _0x3591d3,
            _0x2ebf5b = _0x2083a8[_0x230f95(0x210)];
        for (const _0x44d91f of dependencies) {
            if (!Imported[_0x44d91f]) {
                (alert(_0x230f95(0x167)[_0x230f95(0x242)](_0x2ebf5b, _0x44d91f)),
                    SceneManager['exit']());
                break;
            }
        }
        const _0x3b9de4 = _0x2083a8['description'];
        if (_0x3b9de4[_0x230f95(0x161)](/\[Version[ ](.*?)\]/i)) {
            if (_0x230f95(0x2db) !== 'XnFdb') {
                const _0x280065 = Number(RegExp['$1']);
                _0x280065 !== VisuMZ[label][_0x230f95(0x295)] &&
                    (_0x230f95(0x1a8) === _0x230f95(0x1c1)
                        ? this[_0x230f95(0x22f)](-_0x7d201f, _0x5b162f)
                        : (alert(_0x230f95(0x259)[_0x230f95(0x242)](_0x2ebf5b, _0x280065)),
                          SceneManager[_0x230f95(0x214)]()));
            } else {
                const _0x2d0fb5 = _0x43f65e[_0x230f95(0x26e)](
                    _0x66aea0[_0x230f95(0xfd)][_0x230f95(0x207)]['General']['DisplayedCosts']
                );
                return (
                    !_0x19450b[_0x230f95(0x2e0)] &&
                        (_0x2d0fb5[_0x230f95(0x14a)]('CP'), _0x2d0fb5[_0x230f95(0x14a)]('JP')),
                    _0x2d0fb5[_0x230f95(0x14a)]('Item')
                        [_0x230f95(0x14a)](_0x230f95(0x2a3))
                        [_0x230f95(0x14a)](_0x230f95(0x178))
                );
            }
        }
        if (_0x3b9de4[_0x230f95(0x161)](/\[Tier[ ](\d+)\]/i)) {
            if (_0x230f95(0x105) === 'AvYsi') {
                const _0x1ef82d = Number(RegExp['$1']);
                _0x1ef82d < tier
                    ? (alert(
                          '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.'[
                              _0x230f95(0x242)
                          ](_0x2ebf5b, _0x1ef82d, tier)
                      ),
                      SceneManager[_0x230f95(0x214)]())
                    : (tier = Math[_0x230f95(0x356)](_0x1ef82d, tier));
            } else this[_0x230f95(0x118)]();
        }
        VisuMZ['ConvertParams'](VisuMZ[label]['Settings'], _0x2083a8[_0x230f95(0x14d)]);
    })(pluginData),
    PluginManager[_0x3591d3(0x2e4)](pluginData[_0x3591d3(0x210)], _0x3591d3(0x272), _0x4d44de => {
        const _0x42c91b = _0x3591d3;
        VisuMZ[_0x42c91b(0x26a)](_0x4d44de, _0x4d44de);
        const _0x3eb2f6 = _0x4d44de[_0x42c91b(0xdd)]['map'](_0x1251ac =>
                $gameActors[_0x42c91b(0x32d)](_0x1251ac)
            ),
            _0x2705db = _0x4d44de[_0x42c91b(0x10b)],
            _0x2ee5ad = _0x4d44de['Points'];
        for (const _0x56b562 of _0x3eb2f6) {
            if (!_0x56b562) continue;
            for (const _0x53cb92 of _0x2705db) {
                _0x56b562['gainAbilityPoints'](_0x2ee5ad, _0x53cb92);
            }
        }
    }),
    PluginManager['registerCommand'](pluginData[_0x3591d3(0x210)], _0x3591d3(0x133), _0x30335a => {
        const _0x32eedb = _0x3591d3;
        VisuMZ[_0x32eedb(0x26a)](_0x30335a, _0x30335a);
        const _0x511d05 = _0x30335a[_0x32eedb(0xdd)][_0x32eedb(0x1cc)](_0x51a179 =>
                $gameActors['actor'](_0x51a179)
            ),
            _0x24fc99 = _0x30335a[_0x32eedb(0x10b)],
            _0xa21b8 = _0x30335a[_0x32eedb(0x301)];
        for (const _0x49fb76 of _0x511d05) {
            if (_0x32eedb(0x116) === 'Xmdut') {
                if (!_0x49fb76) continue;
                for (const _0x14269e of _0x24fc99) {
                    _0x49fb76[_0x32eedb(0x22f)](_0xa21b8, _0x14269e);
                }
            } else {
                if (
                    !_0x4d0815[_0x32eedb(0xfd)]['JS'][_0x41f1c7][_0x32eedb(0x1d1)](
                        this,
                        this[_0x32eedb(0x354)],
                        _0x1dac96
                    )
                )
                    return ![];
            }
        }
    }),
    PluginManager[_0x3591d3(0x2e4)](
        pluginData[_0x3591d3(0x210)],
        'AbilityPointsLose',
        _0x42254b => {
            const _0x176fc6 = _0x3591d3;
            VisuMZ['ConvertParams'](_0x42254b, _0x42254b);
            const _0x4f89e2 = _0x42254b['Actors'][_0x176fc6(0x1cc)](_0x4f6aa9 =>
                    $gameActors['actor'](_0x4f6aa9)
                ),
                _0x23846a = _0x42254b[_0x176fc6(0x10b)],
                _0x6b4099 = _0x42254b[_0x176fc6(0x301)];
            for (const _0xb149b5 of _0x4f89e2) {
                if (!_0xb149b5) continue;
                for (const _0x235df7 of _0x23846a) {
                    _0xb149b5[_0x176fc6(0x1ca)](_0x6b4099, _0x235df7);
                }
            }
        }
    ),
    PluginManager['registerCommand'](pluginData[_0x3591d3(0x210)], _0x3591d3(0x371), _0x4374d1 => {
        const _0x56886b = _0x3591d3;
        VisuMZ[_0x56886b(0x26a)](_0x4374d1, _0x4374d1);
        const _0x33d3ab = _0x4374d1[_0x56886b(0xdd)][_0x56886b(0x1cc)](_0x4836e7 =>
                $gameActors['actor'](_0x4836e7)
            ),
            _0x380fa3 = _0x4374d1[_0x56886b(0x10b)],
            _0x26048d = _0x4374d1[_0x56886b(0x301)];
        for (const _0x1d0526 of _0x33d3ab) {
            if (!_0x1d0526) continue;
            for (const _0x3d746a of _0x380fa3) {
                'YewwI' !== _0x56886b(0x10e)
                    ? (_0x3df1e7 = _0x214e1b(_0x2b488e[_0x56886b(0x20f)]))
                    : _0x1d0526[_0x56886b(0x31b)](_0x26048d, _0x3d746a);
            }
        }
    }),
    PluginManager[_0x3591d3(0x2e4)](pluginData[_0x3591d3(0x210)], _0x3591d3(0x2ce), _0x315e7a => {
        const _0x4a7a74 = _0x3591d3;
        VisuMZ[_0x4a7a74(0x26a)](_0x315e7a, _0x315e7a);
        const _0x2f4bd2 = _0x315e7a['Actors'][_0x4a7a74(0x1cc)](_0x23cbd3 =>
                $gameActors['actor'](_0x23cbd3)
            ),
            _0x2867d2 = _0x315e7a[_0x4a7a74(0x10b)],
            _0x55290f = _0x315e7a[_0x4a7a74(0x301)];
        for (const _0x57813f of _0x2f4bd2) {
            if (!_0x57813f) continue;
            for (const _0x4263f8 of _0x2867d2) {
                _0x57813f[_0x4a7a74(0x1cb)](_0x55290f, _0x4263f8);
            }
        }
    }),
    PluginManager[_0x3591d3(0x2e4)](pluginData[_0x3591d3(0x210)], _0x3591d3(0x15e), _0x2937e6 => {
        const _0x34b69c = _0x3591d3;
        VisuMZ[_0x34b69c(0x26a)](_0x2937e6, _0x2937e6);
        const _0x56991c = _0x2937e6['Actors'][_0x34b69c(0x1cc)](_0x1b12b9 =>
                $gameActors[_0x34b69c(0x32d)](_0x1b12b9)
            ),
            _0x3311a0 = _0x2937e6[_0x34b69c(0x10b)],
            _0x105566 = _0x2937e6[_0x34b69c(0x301)];
        for (const _0x4223e5 of _0x56991c) {
            if (!_0x4223e5) continue;
            for (const _0x23c891 of _0x3311a0) {
                _0x4223e5[_0x34b69c(0x163)](_0x105566, _0x23c891);
            }
        }
    }),
    PluginManager[_0x3591d3(0x2e4)](pluginData['name'], _0x3591d3(0x134), _0x30430c => {
        const _0x2222fb = _0x3591d3;
        VisuMZ['ConvertParams'](_0x30430c, _0x30430c);
        const _0x410c61 = _0x30430c['Actors'][_0x2222fb(0x1cc)](_0x4ec6a7 =>
                $gameActors['actor'](_0x4ec6a7)
            ),
            _0x39d34c = _0x30430c[_0x2222fb(0x10b)],
            _0x4577bb = _0x30430c['Points'];
        for (const _0x2e117c of _0x410c61) {
            if (!_0x2e117c) continue;
            for (const _0x40c653 of _0x39d34c) {
                _0x2e117c['loseSkillPoints'](_0x4577bb, _0x40c653);
            }
        }
    }),
    PluginManager[_0x3591d3(0x2e4)](pluginData[_0x3591d3(0x210)], _0x3591d3(0x14b), _0x7da6d1 => {
        const _0x452ac9 = _0x3591d3;
        VisuMZ[_0x452ac9(0x26a)](_0x7da6d1, _0x7da6d1);
        const _0x4153d0 = _0x7da6d1[_0x452ac9(0xdd)][_0x452ac9(0x1cc)](_0x3efb64 =>
                $gameActors[_0x452ac9(0x32d)](_0x3efb64)
            ),
            _0x42acac = _0x7da6d1[_0x452ac9(0x10b)],
            _0x40e438 = _0x7da6d1[_0x452ac9(0x301)];
        for (const _0x416219 of _0x4153d0) {
            if (!_0x416219) continue;
            for (const _0x235864 of _0x42acac) {
                if (_0x452ac9(0x25b) === _0x452ac9(0x1fb)) {
                    const _0xdf42dc = _0x267fa7(_0x3a927a['$1']);
                    if (_0xdf42dc > this['_actor'][_0x452ac9(0xee)]) return ![];
                } else _0x416219[_0x452ac9(0x1d2)](_0x40e438, _0x235864);
            }
        }
    }),
    PluginManager['registerCommand'](pluginData[_0x3591d3(0x210)], _0x3591d3(0x31d), _0x336dfc => {
        const _0xc6b6d7 = _0x3591d3;
        (VisuMZ['ConvertParams'](_0x336dfc, _0x336dfc),
            $gameSystem[_0xc6b6d7(0xa7)](_0x336dfc[_0xc6b6d7(0x2ed)]));
    }),
    (VisuMZ[_0x3591d3(0xfd)]['Scene_Boot_onDatabaseLoaded'] =
        Scene_Boot['prototype'][_0x3591d3(0x30b)]),
    (Scene_Boot[_0x3591d3(0x1ad)]['onDatabaseLoaded'] = function () {
        const _0x9bdc3d = _0x3591d3;
        (VisuMZ[_0x9bdc3d(0xfd)][_0x9bdc3d(0x21b)][_0x9bdc3d(0x1d1)](this),
            this[_0x9bdc3d(0x1ed)]());
    }),
    (Scene_Boot['prototype'][_0x3591d3(0x1ed)] = function () {
        const _0x2f65c4 = _0x3591d3;
        if (VisuMZ[_0x2f65c4(0x249)]) return;
        this[_0x2f65c4(0x256)]();
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x376)] = {
        StartingAbilityPoints: /<STARTING (?:ABILITY POINTS|AP):[ ](.*)>/i,
        StartClassAbilityPoints: /<CLASS (.*) STARTING (?:ABILITY POINTS|AP):[ ](.*)>/gi,
        UserGainAbilityPoints: /<(?:ABILITY POINTS|AP|USER ABILITY POINTS|USER AP) GAIN:[ ](.*)>/i,
        TargetGainAbilityPoints: /<TARGET (?:ABILITY POINTS|AP) GAIN:[ ](.*)>/i,
        EnemyAbilityPoints: /<(?:ABILITY POINTS|AP):[ ](.*)>/i,
        AbilityPointsRate: /<(?:ABILITY POINTS|AP) RATE:[ ](\d+)([%％])>/i,
        StartingSkillPoints: /<STARTING (?:SKILL POINTS|SP):[ ](.*)>/i,
        StartClassSkillPoints: /<CLASS (.*) STARTING (?:SKILL POINTS|SP):[ ](.*)>/gi,
        UserGainSkillPoints: /<(?:SKILL POINTS|SP|USER SKILL POINTS|USER SP) GAIN:[ ](.*)>/i,
        TargetGainSkillPoints: /<TARGET (?:SKILL POINTS|SP) GAIN:[ ](.*)>/i,
        EnemySkillPoints: /<(?:SKILL POINTS|SP):[ ](.*)>/i,
        SkillPointsRate: /<(?:SKILL POINTS|SP) RATE:[ ](\d+)([%％])>/i,
        LearnSkillA: /<LEARN SKILL(?:|S):[ ](.*)>/gi,
        LearnSkillB: /<LEARN SKILL(?:|S)>\s*([\s\S]*)\s*<\/LEARN SKILL(?:|S)>/i,
        LearnSkillPassiveA: /<LEARN SKILL PASSIVE(?:|S):[ ](.*)>/gi,
        LearnSkillPassiveB:
            /<LEARN SKILL PASSIVE(?:|S)>\s*([\s\S]*)\s*<\/LEARN SKILL PASSIVE(?:|S)>/i,
        LearnApCost: /<LEARN (?:ABILITY POINTS|AP) COST:[ ](\d+)>/i,
        LearnCpCost: /<LEARN (?:CLASS POINTS|CP) COST:[ ](\d+)>/i,
        LearnJpCost: /<LEARN (?:JOB POINTS|JP) COST:[ ](\d+)>/i,
        LearnSpCost: /<LEARN (?:SKILL POINTS|SP) COST:[ ](\d+)>/i,
        LearnItemCost: /<LEARN ITEM (.*) COST:[ ](\d+)>/gi,
        LearnWeaponCost: /<LEARN WEAPON (.*) COST:[ ](\d+)>/gi,
        LearnArmorCost: /<LEARN ARMOR (.*) COST:[ ](\d+)>/gi,
        LearnGoldCost: /<LEARN GOLD COST:[ ](\d+)>/i,
        LearnCostBatch:
            /<LEARN SKILL (?:COST|COSTS)>\s*([\s\S]*)\s*<\/LEARN SKILL (?:COST|COSTS)>/i,
        LearnShowLevel: /<LEARN SHOW LEVEL:[ ](\d+)>/i,
        LearnShowSkillsAll: /<LEARN SHOW (?:SKILL|SKILLS|ALL SKILL|ALL SKILLS):[ ](.*)>/i,
        LearnShowSkillsAny: /<LEARN SHOW ANY (?:SKILL|SKILLS):[ ](.*)>/i,
        LearnShowSwitchesAll: /<LEARN SHOW (?:SWITCH|SWITCHES|ALL SWITCH|ALL SWITCHES):[ ](.*)>/i,
        LearnShowSwitchesAny: /<LEARN SHOW ANY (?:SWITCH|SWITCHES):[ ](.*)>/i,
        LearnReqLevel: /<LEARN REQUIRE LEVEL:[ ](\d+)>/i,
        LearnReqSkillsAll: /<LEARN REQUIRE (?:SKILL|SKILLS|ALL SKILL|ALL SKILLS):[ ](.*)>/i,
        LearnReqSkillsAny: /<LEARN REQUIRE ANY (?:SKILL|SKILLS):[ ](.*)>/i,
        LearnReqSwitchesAll: /<LEARN REQUIRE (?:SWITCH|SWITCHES|ALL SWITCH|ALL SWITCHES):[ ](.*)>/i,
        LearnReqSwitchesAny: /<LEARN REQUIRE ANY (?:SWITCH|SWITCHES):[ ](.*)>/i,
        animationIDs: /<LEARN SKILL (?:ANIMATION|ANIMATIONS|ANI):[ ](.*)>/i,
        opacitySpeed: /<LEARN SKILL FADE SPEED:[ ](\d+)>/i,
        learnPicture: /<LEARN SKILL (?:PICTURE|FILENAME):[ ](.*)>/i,
        bigPicture: /<PICTURE:[ ](.*)>/i,
        jsLearnApCost:
            /<JS LEARN (?:ABILITY POINTS|AP) COST>\s*([\s\S]*)\s*<\/JS LEARN (?:ABILITY POINTS|AP) COST>/i,
        jsLearnCpCost:
            /<JS LEARN (?:CLASS POINTS|CP) COST>\s*([\s\S]*)\s*<\/JS LEARN (?:CLASS POINTS|CP) COST>/i,
        jsLearnJpCost:
            /<JS LEARN (?:JOB POINTS|JP) COST>\s*([\s\S]*)\s*<\/JS LEARN (?:JOB POINTS|JP) COST>/i,
        jsLearnSpCost:
            /<JS LEARN (?:SKILL POINTS|SP) COST>\s*([\s\S]*)\s*<\/JS LEARN (?:SKILL POINTS|SP) COST>/i,
        jsLearnShow: /<JS LEARN (?:SHOW|VISIBLE)>\s*([\s\S]*)\s*<\/JS LEARN (?:SHOW|VISIBLE)>/i,
        jsLearnShowListTxt:
            /<JS LEARN (?:SHOW|VISIBLE) LIST TEXT>\s*([\s\S]*)\s*<\/JS LEARN (?:SHOW|VISIBLE) LIST TEXT>/i,
        jsLearnShowDetailTxt:
            /<JS LEARN (?:SHOW|VISIBLE) DETAIL TEXT>\s*([\s\S]*)\s*<\/JS LEARN (?:SHOW|VISIBLE) DETAIL TEXT>/i,
        jsLearnReq:
            /<JS LEARN (?:REQUIREMENT|REQUIREMENTS)>\s*([\s\S]*)\s*<\/JS LEARN (?:REQUIREMENT|REQUIREMENTS)>/i,
        jsLearnReqListTxt:
            /<JS LEARN (?:REQUIREMENT|REQUIREMENTS) LIST TEXT>\s*([\s\S]*)\s*<\/JS LEARN (?:REQUIREMENT|REQUIREMENTS) LIST TEXT>/i,
        jsLearnReqDetailTxt:
            /<JS LEARN (?:REQUIREMENT|REQUIREMENTS) DETAIL TEXT>\s*([\s\S]*)\s*<\/JS LEARN (?:REQUIREMENT|REQUIREMENTS) DETAIL TEXT>/i,
        jsOnLearn: /<JS ON LEARN SKILL>\s*([\s\S]*)\s*<\/JS ON LEARN SKILL>/i,
    }),
    (VisuMZ['SkillLearnSystem']['JS'] = {}),
    (Scene_Boot['prototype'][_0x3591d3(0x256)] = function () {
        const _0x3c4365 = _0x3591d3,
            _0x18a1f1 = $dataActors[_0x3c4365(0xd9)]($dataSkills);
        for (const _0x32f841 of _0x18a1f1) {
            if (!_0x32f841) continue;
            VisuMZ[_0x3c4365(0xfd)][_0x3c4365(0xd7)](_0x32f841);
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x1cd)] = VisuMZ[_0x3591d3(0x1cd)]),
    (VisuMZ['ParseSkillNotetags'] = function (_0xa7f0d8) {
        const _0x1b4d8f = _0x3591d3;
        (VisuMZ[_0x1b4d8f(0xfd)][_0x1b4d8f(0x1cd)]['call'](this, _0xa7f0d8),
            VisuMZ[_0x1b4d8f(0xfd)]['Parse_Notetags_CreateJS'](_0xa7f0d8));
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0xd7)] = function (_0xf34833) {
        const _0x85ce5d = _0x3591d3,
            _0x517a62 = VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x376)];
        (VisuMZ[_0x85ce5d(0xfd)]['createCostJS'](
            _0xf34833,
            _0x85ce5d(0x222),
            _0x517a62[_0x85ce5d(0x222)]
        ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x2aa)](
                _0xf34833,
                _0x85ce5d(0xec),
                _0x517a62[_0x85ce5d(0xec)]
            ),
            VisuMZ[_0x85ce5d(0xfd)]['createCostJS'](
                _0xf34833,
                _0x85ce5d(0x28a),
                _0x517a62[_0x85ce5d(0x28a)]
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x2aa)](
                _0xf34833,
                _0x85ce5d(0x16a),
                _0x517a62['jsLearnSpCost']
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x19c)](
                _0xf34833,
                'jsLearnShow',
                _0x517a62[_0x85ce5d(0x173)]
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x2f0)](
                _0xf34833,
                _0x85ce5d(0x31e),
                _0x517a62['jsLearnReq']
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x262)](
                _0xf34833,
                _0x85ce5d(0x183),
                _0x517a62[_0x85ce5d(0x183)]
            ),
            VisuMZ[_0x85ce5d(0xfd)]['createTextJS'](
                _0xf34833,
                _0x85ce5d(0x1be),
                _0x517a62[_0x85ce5d(0x1be)]
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x262)](
                _0xf34833,
                _0x85ce5d(0x20b),
                _0x517a62[_0x85ce5d(0x20b)]
            ),
            VisuMZ[_0x85ce5d(0xfd)][_0x85ce5d(0x262)](
                _0xf34833,
                _0x85ce5d(0x366),
                _0x517a62[_0x85ce5d(0x366)]
            ),
            VisuMZ[_0x85ce5d(0xfd)]['createActionJS'](
                _0xf34833,
                _0x85ce5d(0x2b7),
                _0x517a62[_0x85ce5d(0x2b7)]
            ));
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x2aa)] = function (_0x2a0169, _0x4472f0, _0x2cb0e1) {
        const _0x53a8b5 = _0x3591d3,
            _0x316c36 = _0x2a0169[_0x53a8b5(0x148)];
        if (_0x316c36['match'](_0x2cb0e1)) {
            if (_0x53a8b5(0x36c) !== _0x53a8b5(0x36c))
                (_0x3b73a5[_0x53a8b5(0xfd)][_0x53a8b5(0x177)][_0x53a8b5(0x1d1)](this),
                    this[_0x53a8b5(0x155)](),
                    this[_0x53a8b5(0x296)](),
                    this[_0x53a8b5(0xc0)](),
                    this[_0x53a8b5(0x35a)]());
            else {
                const _0x232fd2 = String(RegExp['$1']),
                    _0x21918b = _0x53a8b5(0xac)[_0x53a8b5(0x242)](_0x232fd2),
                    _0x471da9 = VisuMZ[_0x53a8b5(0xfd)][_0x53a8b5(0x315)](_0x2a0169, _0x4472f0);
                VisuMZ[_0x53a8b5(0xfd)]['JS'][_0x471da9] = new Function(_0x21918b);
            }
        }
    }),
    (VisuMZ['SkillLearnSystem'][_0x3591d3(0x19c)] = function (_0x45b3fa, _0x4597b5, _0x284f6f) {
        const _0x1168d3 = _0x3591d3,
            _0x1e7f7f = _0x45b3fa[_0x1168d3(0x148)];
        if (_0x1e7f7f[_0x1168d3(0x161)](_0x284f6f)) {
            const _0x27f0fa = String(RegExp['$1']),
                _0x23dd45 =
                    '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20visible\x20=\x20true;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Visible\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20visible;\x0a\x20\x20\x20\x20\x20\x20\x20\x20'[
                        _0x1168d3(0x242)
                    ](_0x27f0fa),
                _0x4de736 = VisuMZ[_0x1168d3(0xfd)][_0x1168d3(0x315)](_0x45b3fa, _0x4597b5);
            VisuMZ[_0x1168d3(0xfd)]['JS'][_0x4de736] = new Function(_0x23dd45);
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x2f0)] = function (_0x2b2203, _0x4acd6d, _0x5ecba0) {
        const _0x3d7e6d = _0x3591d3,
            _0x37acaa = _0x2b2203[_0x3d7e6d(0x148)];
        if (_0x37acaa[_0x3d7e6d(0x161)](_0x5ecba0)) {
            const _0x3e0e60 = String(RegExp['$1']),
                _0x5e3e51 = _0x3d7e6d(0x322)[_0x3d7e6d(0x242)](_0x3e0e60),
                _0x4cf608 = VisuMZ['SkillLearnSystem'][_0x3d7e6d(0x315)](_0x2b2203, _0x4acd6d);
            VisuMZ[_0x3d7e6d(0xfd)]['JS'][_0x4cf608] = new Function(_0x5e3e51);
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x262)] = function (_0x5ae9d5, _0x334d95, _0x2cccb8) {
        const _0x20a0c3 = _0x3591d3,
            _0x5170f5 = _0x5ae9d5[_0x20a0c3(0x148)];
        if (_0x5170f5[_0x20a0c3(0x161)](_0x2cccb8)) {
            if ('kgSyw' !== _0x20a0c3(0x19f)) {
                const _0x344838 = String(RegExp['$1']),
                    _0x1b9805 =
                        '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20text\x20=\x20\x27\x27;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Text\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20text;\x0a\x20\x20\x20\x20\x20\x20\x20\x20'[
                            _0x20a0c3(0x242)
                        ](_0x344838),
                    _0x41660f = VisuMZ[_0x20a0c3(0xfd)][_0x20a0c3(0x315)](_0x5ae9d5, _0x334d95);
                VisuMZ[_0x20a0c3(0xfd)]['JS'][_0x41660f] = new Function(_0x1b9805);
            } else {
                const _0x115736 = _0x23a0df[_0x2f6427],
                    _0x1786a9 = _0x575b8b[_0x20a0c3(0xfd)][_0x20a0c3(0x315)](
                        _0x115736,
                        _0x20a0c3(0x2b7)
                    );
                _0x236848[_0x20a0c3(0xfd)]['JS'][_0x1786a9] &&
                    _0xc6f7e0['SkillLearnSystem']['JS'][_0x1786a9][_0x20a0c3(0x1d1)](
                        this,
                        this,
                        _0x115736
                    );
            }
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0xf1)] = function (_0x4a43eb, _0x15fe74, _0x493907) {
        const _0x34baff = _0x3591d3,
            _0x5cf941 = _0x4a43eb[_0x34baff(0x148)];
        if (_0x5cf941['match'](_0x493907)) {
            if (_0x34baff(0x10c) === _0x34baff(0x123)) _0x5a1e8e = _0x1daca5;
            else {
                const _0x11a90c = String(RegExp['$1']),
                    _0x58aaff = _0x34baff(0x1ec)[_0x34baff(0x242)](_0x11a90c),
                    _0x26a677 = VisuMZ[_0x34baff(0xfd)][_0x34baff(0x315)](_0x4a43eb, _0x15fe74);
                VisuMZ[_0x34baff(0xfd)]['JS'][_0x26a677] = new Function(_0x58aaff);
            }
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)]['createKeyJS'] = function (_0x1f6ac4, _0x31f5ae) {
        const _0x2afeca = _0x3591d3;
        if (VisuMZ['createKeyJS']) return VisuMZ[_0x2afeca(0x315)](_0x1f6ac4, _0x31f5ae);
        let _0x3e61ac = '';
        if ($dataActors[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = 'Actor-%1-%2'[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataClasses[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = _0x2afeca(0x30d)['format'](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataSkills['includes'](_0x1f6ac4))
            _0x3e61ac = 'Skill-%1-%2'[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataItems[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = 'Item-%1-%2'[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataWeapons['includes'](_0x1f6ac4))
            _0x3e61ac = _0x2afeca(0x1b9)['format'](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataArmors[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = _0x2afeca(0x306)[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataEnemies[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = _0x2afeca(0x2a4)[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        if ($dataStates[_0x2afeca(0x292)](_0x1f6ac4))
            _0x3e61ac = _0x2afeca(0x20e)[_0x2afeca(0x242)](_0x1f6ac4['id'], _0x31f5ae);
        return _0x3e61ac;
    }),
    (DataManager[_0x3591d3(0x29b)] = function (_0x1bbd34) {
        const _0x118b52 = _0x3591d3;
        if (!_0x1bbd34) return ![];
        return (
            _0x1bbd34[_0x118b52(0x285)] !== undefined && _0x1bbd34[_0x118b52(0x139)] !== undefined
        );
    }),
    (DataManager[_0x3591d3(0x1ff)] = function (_0x5bfa6d) {
        const _0x4a0a74 = _0x3591d3;
        ((_0x5bfa6d = _0x5bfa6d[_0x4a0a74(0xe5)]()[_0x4a0a74(0x25a)]()),
            (this[_0x4a0a74(0x240)] = this['_classIDs'] || {}));
        if (this[_0x4a0a74(0x240)][_0x5bfa6d]) return this[_0x4a0a74(0x240)][_0x5bfa6d];
        for (const _0x37a303 of $dataClasses) {
            if (!_0x37a303) continue;
            let _0x2de478 = _0x37a303[_0x4a0a74(0x210)];
            ((_0x2de478 = _0x2de478[_0x4a0a74(0x26d)](/\x1I\[(\d+)\]/gi, '')),
                (_0x2de478 = _0x2de478[_0x4a0a74(0x26d)](/\\I\[(\d+)\]/gi, '')),
                (this[_0x4a0a74(0x240)][_0x2de478[_0x4a0a74(0xe5)]()[_0x4a0a74(0x25a)]()] =
                    _0x37a303['id']));
        }
        return this[_0x4a0a74(0x240)][_0x5bfa6d] || 0x0;
    }),
    (DataManager[_0x3591d3(0x1a9)] = function (_0x167715) {
        const _0x34b771 = _0x3591d3;
        ((_0x167715 = _0x167715[_0x34b771(0xe5)]()[_0x34b771(0x25a)]()),
            (this['_skillIDs'] = this[_0x34b771(0x198)] || {}));
        if (this[_0x34b771(0x198)][_0x167715]) return this['_skillIDs'][_0x167715];
        for (const _0x19fd01 of $dataSkills) {
            if (!_0x19fd01) continue;
            this[_0x34b771(0x198)][
                _0x19fd01[_0x34b771(0x210)][_0x34b771(0xe5)]()[_0x34b771(0x25a)]()
            ] = _0x19fd01['id'];
        }
        return this[_0x34b771(0x198)][_0x167715] || 0x0;
    }),
    (DataManager[_0x3591d3(0x17e)] = function (_0x256c26) {
        const _0x1822b1 = _0x3591d3;
        ((_0x256c26 = _0x256c26[_0x1822b1(0xe5)]()[_0x1822b1(0x25a)]()),
            (this[_0x1822b1(0x162)] = this[_0x1822b1(0x162)] || {}));
        if (this[_0x1822b1(0x162)][_0x256c26]) return this[_0x1822b1(0x162)][_0x256c26];
        for (const _0x4b7b38 of $dataItems) {
            if (_0x1822b1(0x30e) === 'Oarvs')
                _0x351a3d['id'] = _0x4146e0[_0x1822b1(0x314)](_0x31b7e7);
            else {
                if (!_0x4b7b38) continue;
                this[_0x1822b1(0x162)][
                    _0x4b7b38[_0x1822b1(0x210)][_0x1822b1(0xe5)]()[_0x1822b1(0x25a)]()
                ] = _0x4b7b38['id'];
            }
        }
        return this[_0x1822b1(0x162)][_0x256c26] || 0x0;
    }),
    (DataManager['getWeaponIdWithName'] = function (_0x14935f) {
        const _0x498bc4 = _0x3591d3;
        ((_0x14935f = _0x14935f['toUpperCase']()[_0x498bc4(0x25a)]()),
            (this['_weaponIDs'] = this[_0x498bc4(0x364)] || {}));
        if (this['_weaponIDs'][_0x14935f]) return this[_0x498bc4(0x364)][_0x14935f];
        for (const _0x585891 of $dataWeapons) {
            if (_0x498bc4(0x27c) !== _0x498bc4(0x27c))
                ((this['_skillLearnIconSpriteOpacitySpeed'] =
                    _0x2c7946['SkillLearnSystem']['Settings'][_0x498bc4(0x30f)][_0x498bc4(0x2e1)] ||
                    0x1),
                    this[_0x498bc4(0x284)]()[_0x498bc4(0x148)][_0x498bc4(0x161)](
                        _0x52b380['SkillLearnSystem'][_0x498bc4(0x376)][_0x498bc4(0x2bf)]
                    ) &&
                        (this['_skillLearnIconSpriteOpacitySpeed'] = _0x58d480[_0x498bc4(0x356)](
                            _0x1ebb0a(_0x57b723['$1']),
                            0x1
                        )),
                    (this[_0x498bc4(0x223)][_0x498bc4(0x225)] = 0x0));
            else {
                if (!_0x585891) continue;
                this[_0x498bc4(0x364)][_0x585891['name']['toUpperCase']()[_0x498bc4(0x25a)]()] =
                    _0x585891['id'];
            }
        }
        return this[_0x498bc4(0x364)][_0x14935f] || 0x0;
    }),
    (DataManager[_0x3591d3(0x199)] = function (_0x360203) {
        const _0x34b45c = _0x3591d3;
        ((_0x360203 = _0x360203[_0x34b45c(0xe5)]()[_0x34b45c(0x25a)]()),
            (this[_0x34b45c(0x13b)] = this['_armorIDs'] || {}));
        if (this[_0x34b45c(0x13b)][_0x360203]) return this['_armorIDs'][_0x360203];
        for (const _0x229dd6 of $dataArmors) {
            if ('RFCzU' !== _0x34b45c(0x164)) {
                const _0x3b2af6 = this['itemLineRect'](_0x1bd5ea);
                (this['resetTextColor'](),
                    this[_0x34b45c(0xaa)](this[_0x34b45c(0x154)](_0x17ab96)));
                const _0x5402c4 = this[_0x34b45c(0x342)](_0x407c2e),
                    _0x5738c6 = this[_0x34b45c(0x365)](_0x5402c4)[_0x34b45c(0x347)];
                ((_0x3b2af6['x'] += _0x5016b0[_0x34b45c(0xc7)](
                    (_0x3b2af6[_0x34b45c(0x347)] - _0x5738c6) / 0x2
                )),
                    this[_0x34b45c(0x1de)](_0x5402c4, _0x3b2af6['x'], _0x3b2af6['y'], _0x5738c6));
            } else {
                if (!_0x229dd6) continue;
                this[_0x34b45c(0x13b)][
                    _0x229dd6[_0x34b45c(0x210)][_0x34b45c(0xe5)]()[_0x34b45c(0x25a)]()
                ] = _0x229dd6['id'];
            }
        }
        return this[_0x34b45c(0x13b)][_0x360203] || 0x0;
    }),
    (DataManager[_0x3591d3(0x329)] = function (_0x310795) {
        const _0x2878dd = _0x3591d3;
        if (!$dataClasses[_0x310795]) return [];
        const _0xa45d91 = [],
            _0xa2c8ce = $dataClasses[_0x310795][_0x2878dd(0x148)],
            _0x289d09 = VisuMZ['SkillLearnSystem'][_0x2878dd(0x376)],
            _0x433875 = _0xa2c8ce[_0x2878dd(0x161)](_0x289d09[_0x2878dd(0x169)]);
        if (_0x433875)
            for (const _0x1b623e of _0x433875) {
                if (_0x2878dd(0x311) === _0x2878dd(0x311)) {
                    if (!_0x1b623e) continue;
                    _0x1b623e['match'](_0x289d09['LearnSkillA']);
                    const _0x3dcac5 = String(RegExp['$1'])
                        [_0x2878dd(0x168)](',')
                        [_0x2878dd(0x1cc)](_0x4f118d => _0x4f118d[_0x2878dd(0x25a)]());
                    for (let _0xaf681c of _0x3dcac5) {
                        if (_0x2878dd(0x1f8) === _0x2878dd(0x1f8)) {
                            _0xaf681c = (String(_0xaf681c) || '')[_0x2878dd(0x25a)]();
                            const _0x343a4e = /^\d+$/[_0x2878dd(0xa5)](_0xaf681c);
                            _0x343a4e
                                ? _0xa45d91['push'](Number(_0xaf681c))
                                : _0xa45d91[_0x2878dd(0x2b5)](
                                      DataManager[_0x2878dd(0x1a9)](_0xaf681c)
                                  );
                        } else
                            return (
                                this[_0x2878dd(0x354)] &&
                                !this['_actor']['meetRequirementsForSkillLearnSystem'](_0x57d2ac)
                            );
                    }
                } else _0x16ca34 = _0x535fb8[_0x2878dd(0x356)](_0x3a530c, _0x17d7ad);
            }
        const _0x4b90b3 = _0xa2c8ce[_0x2878dd(0x161)](_0x289d09[_0x2878dd(0x1f4)]);
        if (_0x4b90b3)
            for (const _0x1ac635 of _0x4b90b3) {
                if (_0x2878dd(0x12b) !== _0x2878dd(0x372)) {
                    if (!_0x1ac635) continue;
                    _0x1ac635[_0x2878dd(0x161)](_0x289d09[_0x2878dd(0x1f4)]);
                    const _0x1c8961 = String(RegExp['$1'])[_0x2878dd(0x168)](/[\r\n]+/);
                    for (let _0x39f748 of _0x1c8961) {
                        _0x39f748 = (String(_0x39f748) || '')[_0x2878dd(0x25a)]();
                        const _0x5c224c = /^\d+$/['test'](_0x39f748);
                        _0x5c224c
                            ? _0xa45d91[_0x2878dd(0x2b5)](Number(_0x39f748))
                            : _0xa45d91[_0x2878dd(0x2b5)](DataManager[_0x2878dd(0x1a9)](_0x39f748));
                    }
                } else this[_0x2878dd(0x2ad)]();
            }
        return _0xa45d91[_0x2878dd(0xb7)]((_0x3c3393, _0x298313) => _0x3c3393 - _0x298313)[
            _0x2878dd(0x149)
        ](
            (_0x12b9aa, _0x569daf, _0x59fdb1) =>
                _0x59fdb1[_0x2878dd(0x32b)](_0x12b9aa) === _0x569daf
        );
    }),
    (DataManager[_0x3591d3(0xbc)] = function (_0x371ceb) {
        const _0x1658fd = _0x3591d3;
        if (!_0x371ceb) return 0x0;
        if (!DataManager[_0x1658fd(0x22d)](_0x371ceb) && !DataManager[_0x1658fd(0x29b)](_0x371ceb))
            return 0x0;
        const _0x193cda = VisuMZ[_0x1658fd(0xfd)]['RegExp'],
            _0x282c81 = _0x371ceb[_0x1658fd(0x148)];
        if (_0x282c81['match'](_0x193cda[_0x1658fd(0x113)])) {
            if ('UQBWi' !== 'UQBWi') _0x487ccc[_0x1658fd(0x2b5)](_0x6a4da);
            else return Number(RegExp['$1']);
        }
        if (_0x282c81[_0x1658fd(0x161)](_0x193cda[_0x1658fd(0x192)])) {
            if (_0x1658fd(0x2b8) === _0x1658fd(0x2ab)) this[_0x1658fd(0x1e6)]();
            else {
                const _0x35e590 = String(RegExp['$1'])['split'](/[\r\n]+/);
                for (const _0x2dd9b5 of _0x35e590) {
                    if (_0x2dd9b5[_0x1658fd(0x161)](/(?:ABILITY POINTS|AP):[ ](\d+)/gi))
                        return Number(RegExp['$1']);
                }
            }
        }
        const _0x69661 = VisuMZ['SkillLearnSystem'][_0x1658fd(0x315)](_0x371ceb, _0x1658fd(0x222));
        if (VisuMZ[_0x1658fd(0xfd)]['JS'][_0x69661]) {
            const _0x308bd0 = SceneManager[_0x1658fd(0x31a)][_0x1658fd(0x216)]();
            return VisuMZ[_0x1658fd(0xfd)]['JS'][_0x69661]['call'](this, _0x308bd0, _0x371ceb);
        }
        return VisuMZ[_0x1658fd(0xfd)][_0x1658fd(0x207)][_0x1658fd(0x13e)][_0x1658fd(0x21a)] || 0x0;
    }),
    (DataManager['getSkillLearnClassPointCost'] = function (_0x174655) {
        const _0x91140f = _0x3591d3;
        if (!_0x174655) return 0x0;
        if (!DataManager[_0x91140f(0x22d)](_0x174655) && !DataManager[_0x91140f(0x29b)](_0x174655))
            return 0x0;
        const _0x217c17 = VisuMZ['SkillLearnSystem'][_0x91140f(0x376)],
            _0x510083 = _0x174655[_0x91140f(0x148)];
        if (_0x510083[_0x91140f(0x161)](_0x217c17[_0x91140f(0x15c)])) {
            if (_0x91140f(0x290) === _0x91140f(0x1da))
                ((_0x53ad5d = _0x2b3052[_0x91140f(0xfd)]['JS'][_0xf503a1]['call'](
                    this,
                    this[_0x91140f(0x354)],
                    _0x665d5c
                )),
                    _0x32937d['length'] > 0x0 &&
                        (_0x406361 !== ''
                            ? (_0x554e6a = _0x12d9de[_0x91140f(0x242)](_0xa56aea, _0x23275f))
                            : (_0x334791 = _0x38dc54)));
            else return Number(RegExp['$1']);
        }
        if (_0x510083[_0x91140f(0x161)](_0x217c17['LearnCostBatch'])) {
            const _0xb46c9f = String(RegExp['$1'])[_0x91140f(0x168)](/[\r\n]+/);
            for (const _0x2b5d29 of _0xb46c9f) {
                if (_0x91140f(0x2c5) !== _0x91140f(0x190)) {
                    if (_0x2b5d29[_0x91140f(0x161)](/(?:CLASS POINTS|CP):[ ](\d+)/gi))
                        return Number(RegExp['$1']);
                } else _0x28d8b0 = _0x472edb(_0x438eea);
            }
        }
        const _0x20037d = VisuMZ['SkillLearnSystem'][_0x91140f(0x315)](_0x174655, _0x91140f(0xec));
        if (VisuMZ[_0x91140f(0xfd)]['JS'][_0x20037d]) {
            if (_0x91140f(0x1fd) === 'ANrWa') {
                const _0x13863b = SceneManager[_0x91140f(0x31a)][_0x91140f(0x216)]();
                return (
                    VisuMZ[_0x91140f(0xfd)]['JS'][_0x20037d][_0x91140f(0x1d1)](
                        this,
                        _0x13863b,
                        _0x174655
                    ) || 0x0
                );
            } else {
                const _0x25d4df = this[_0x91140f(0x365)](_0x29e104)['width'],
                    _0x3cb7fc =
                        _0x297cea + _0x17995a[_0x91140f(0xc7)]((_0x571110 - _0x25d4df) / 0x2);
                this['drawTextEx'](_0x15b406, _0x3cb7fc, _0x367917);
            }
        }
        return VisuMZ[_0x91140f(0x16e)][_0x91140f(0x207)]['ClassPoints'][_0x91140f(0x21a)] || 0x0;
    }),
    (DataManager[_0x3591d3(0x187)] = function (_0x45e695) {
        const _0x552cac = _0x3591d3;
        if (!_0x45e695) return 0x0;
        if (!DataManager[_0x552cac(0x22d)](_0x45e695) && !DataManager[_0x552cac(0x29b)](_0x45e695))
            return 0x0;
        const _0x28fedb = VisuMZ[_0x552cac(0xfd)][_0x552cac(0x376)],
            _0x456586 = _0x45e695[_0x552cac(0x148)];
        if (_0x456586[_0x552cac(0x161)](_0x28fedb[_0x552cac(0x126)])) {
            if (_0x552cac(0x360) !== _0x552cac(0x377)) return Number(RegExp['$1']);
            else
                ((this[_0x552cac(0x1e5)] = !![]),
                    (this['_skillLearnAnimationWait'] = 0x14),
                    (this[_0x552cac(0xed)][_0x552cac(0x2ac)] =
                        _0x407e56['SkillLearnSystem'][_0x552cac(0x207)]['Animation'][
                            _0x552cac(0x1ae)
                        ] || ![]),
                    this[_0x552cac(0x358)]());
        }
        if (_0x456586[_0x552cac(0x161)](_0x28fedb[_0x552cac(0x192)])) {
            const _0x1b10d5 = String(RegExp['$1'])['split'](/[\r\n]+/);
            for (const _0x1db0da of _0x1b10d5) {
                if (_0x1db0da[_0x552cac(0x161)](/(?:JOB POINTS|JP):[ ](\d+)/gi))
                    return _0x552cac(0x31c) === _0x552cac(0x31c)
                        ? Number(RegExp['$1'])
                        : _0x3f139b * (_0x585212(_0x40b660['$1']) * 0.01);
            }
        }
        const _0x23ffd8 = VisuMZ[_0x552cac(0xfd)][_0x552cac(0x315)](_0x45e695, 'jsLearnJpCost');
        if (VisuMZ[_0x552cac(0xfd)]['JS'][_0x23ffd8]) {
            if (_0x552cac(0x188) === _0x552cac(0x188)) {
                const _0x406f44 = SceneManager[_0x552cac(0x31a)][_0x552cac(0x216)]();
                return VisuMZ[_0x552cac(0xfd)]['JS'][_0x23ffd8][_0x552cac(0x1d1)](
                    this,
                    _0x406f44,
                    _0x45e695
                );
            } else return this['_stypeId'] === 'skillLearn';
        }
        return VisuMZ[_0x552cac(0x16e)]['Settings'][_0x552cac(0x1b0)]['DefaultCost'] || 0x0;
    }),
    (DataManager[_0x3591d3(0xe8)] = function (_0xa6fc30) {
        const _0x23d99a = _0x3591d3;
        if (!_0xa6fc30) return 0x0;
        if (!DataManager[_0x23d99a(0x22d)](_0xa6fc30) && !DataManager['isState'](_0xa6fc30))
            return 0x0;
        const _0x4d3c67 = VisuMZ['SkillLearnSystem'][_0x23d99a(0x376)],
            _0x1f55ee = _0xa6fc30['note'];
        if (_0x1f55ee['match'](_0x4d3c67[_0x23d99a(0x220)])) {
            if (_0x23d99a(0xad) !== _0x23d99a(0xad))
                _0x290523[_0x23d99a(0xfd)]['Window_SkillStatus_refresh'][_0x23d99a(0x1d1)](this);
            else return Number(RegExp['$1']);
        }
        if (_0x1f55ee['match'](_0x4d3c67['LearnCostBatch'])) {
            if (_0x23d99a(0x1f0) === _0x23d99a(0x2d2)) _0x4c580b = _0x3e1f4e;
            else {
                const _0xd48873 = String(RegExp['$1'])[_0x23d99a(0x168)](/[\r\n]+/);
                for (const _0x4a40c1 of _0xd48873) {
                    if (_0x4a40c1[_0x23d99a(0x161)](/(?:SKILL POINTS|SP):[ ](\d+)/gi)) {
                        if (_0x23d99a(0x338) !== _0x23d99a(0x338)) this[_0x23d99a(0x1b7)]();
                        else return Number(RegExp['$1']);
                    }
                }
            }
        }
        const _0x591da9 = VisuMZ[_0x23d99a(0xfd)][_0x23d99a(0x315)](_0xa6fc30, 'jsLearnSpCost');
        if (VisuMZ[_0x23d99a(0xfd)]['JS'][_0x591da9]) {
            if (_0x23d99a(0x29f) !== _0x23d99a(0xe7)) {
                const _0x1d1373 = SceneManager['_scene'][_0x23d99a(0x216)]();
                return VisuMZ[_0x23d99a(0xfd)]['JS'][_0x591da9][_0x23d99a(0x1d1)](
                    this,
                    _0x1d1373,
                    _0xa6fc30
                );
            } else
                (_0x1efb8f[_0x23d99a(0xfd)][_0x23d99a(0x260)][_0x23d99a(0x1d1)](this),
                    this[_0x23d99a(0x219)]());
        }
        return VisuMZ[_0x23d99a(0xfd)][_0x23d99a(0x207)][_0x23d99a(0x11a)][_0x23d99a(0x21a)] || 0x0;
    }),
    (DataManager[_0x3591d3(0x247)] = function (_0x39e8d8) {
        const _0x2778e7 = _0x3591d3;
        if (!_0x39e8d8) return [];
        if (!DataManager['isSkill'](_0x39e8d8) && !DataManager[_0x2778e7(0x29b)](_0x39e8d8))
            return [];
        const _0x3066f5 = VisuMZ[_0x2778e7(0xfd)][_0x2778e7(0x376)],
            _0x297bdb = _0x39e8d8[_0x2778e7(0x148)],
            _0x2aa6c1 = [],
            _0x5bd728 = _0x297bdb[_0x2778e7(0x161)](_0x3066f5['LearnItemCost']);
        if (_0x5bd728) {
            if (_0x2778e7(0x343) === _0x2778e7(0x343))
                for (const _0x518535 of _0x5bd728) {
                    if (_0x2778e7(0x137) !== 'Xqjje') {
                        if (!_0x518535) continue;
                        _0x518535[_0x2778e7(0x161)](_0x3066f5[_0x2778e7(0x151)]);
                        const _0x5b91a4 = String(RegExp['$1']),
                            _0x59f846 = { id: 0x0, quantity: Number(RegExp['$2']) },
                            _0x51bbce = /^\d+$/['test'](_0x5b91a4);
                        if (_0x51bbce) {
                            if (_0x2778e7(0x128) === 'qyDoO') _0x59f846['id'] = Number(_0x5b91a4);
                            else {
                                const _0x3365bd = _0x5eacc4[_0x2778e7(0x29d)][_0x41f039],
                                    _0x19897e = _0xb4d2dc[_0x2778e7(0x108)](_0x48038d)
                                        ? _0xc93867
                                        : _0x3edeff;
                                _0x4d4432 += _0x19897e['format'](_0x3365bd) + '\x0a';
                            }
                        } else _0x59f846['id'] = DataManager[_0x2778e7(0x17e)](_0x5b91a4);
                        _0x59f846['id'] > 0x0 &&
                            (_0x2778e7(0x11b) === _0x2778e7(0x11b)
                                ? _0x2aa6c1[_0x2778e7(0x2b5)](_0x59f846)
                                : (_0x262f0b['id'] = _0x2fd9d1['getArmorIdWithName'](_0x9cf872)));
                    } else
                        (_0x2f5c84(
                            _0x2778e7(0x281)[_0x2778e7(0x242)](_0x1133ab, _0x4d286d, _0x2baaa5)
                        ),
                            _0x2632de[_0x2778e7(0x214)]());
                }
            else _0x37bf52 = _0x45a5a0;
        }
        if (_0x297bdb['match'](_0x3066f5[_0x2778e7(0x192)])) {
            if (_0x2778e7(0x119) === 'EsaMn') {
                if (!this[_0x2778e7(0x223)]) return;
                (this[_0x2778e7(0x142)](this[_0x2778e7(0x223)]),
                    this[_0x2778e7(0x223)][_0x2778e7(0x23d)](),
                    (this[_0x2778e7(0x223)] = _0x179cab));
            } else {
                const _0x5dda7d = String(RegExp['$1'])[_0x2778e7(0x168)](/[\r\n]+/);
                for (const _0x5e31cf of _0x5dda7d) {
                    if (_0x5e31cf['match'](/ITEM[ ](.*):[ ](\d+)/gi)) {
                        if (_0x2778e7(0x2ae) !== _0x2778e7(0x1d0)) {
                            const _0x11d897 = String(RegExp['$1']),
                                _0x3ea823 = { id: 0x0, quantity: Number(RegExp['$2']) },
                                _0x16962a = /^\d+$/[_0x2778e7(0xa5)](_0x11d897);
                            if (_0x16962a) {
                                if ('ewPOm' !== 'ewPOm') {
                                    const _0x395e40 =
                                        _0x175672[_0x2778e7(0x31a)][_0x2778e7(0x216)]();
                                    return _0x102e22[_0x2778e7(0xfd)]['JS'][_0x575a3e][
                                        _0x2778e7(0x1d1)
                                    ](this, _0x395e40, _0x37e1b4);
                                } else _0x3ea823['id'] = Number(_0x11d897);
                            } else _0x3ea823['id'] = DataManager[_0x2778e7(0x17e)](_0x11d897);
                            _0x3ea823['id'] > 0x0 && _0x2aa6c1[_0x2778e7(0x2b5)](_0x3ea823);
                        } else _0x110f6b *= this[_0x2778e7(0x33d)]();
                    }
                }
            }
        }
        return _0x2aa6c1;
    }),
    (DataManager[_0x3591d3(0xe2)] = function (_0x1424c) {
        const _0x2bf9ab = _0x3591d3;
        if (!_0x1424c) return [];
        if (!DataManager[_0x2bf9ab(0x22d)](_0x1424c) && !DataManager[_0x2bf9ab(0x29b)](_0x1424c))
            return [];
        const _0x221838 = VisuMZ[_0x2bf9ab(0xfd)][_0x2bf9ab(0x376)],
            _0xb9f49a = _0x1424c[_0x2bf9ab(0x148)],
            _0x328e80 = [],
            _0x153dfb = _0xb9f49a['match'](_0x221838['LearnWeaponCost']);
        if (_0x153dfb)
            for (const _0x39cde2 of _0x153dfb) {
                if (_0x2bf9ab(0x375) === 'tcWEB') {
                    if (!_0x39cde2) continue;
                    _0x39cde2[_0x2bf9ab(0x161)](_0x221838[_0x2bf9ab(0x2de)]);
                    const _0x3eedbc = String(RegExp['$1']),
                        _0x125319 = { id: 0x0, quantity: Number(RegExp['$2']) },
                        _0x1d1add = /^\d+$/[_0x2bf9ab(0xa5)](_0x3eedbc);
                    (_0x1d1add
                        ? _0x2bf9ab(0xc6) === _0x2bf9ab(0xa1)
                            ? (_0x2ef438[_0x2bf9ab(0xfd)][_0x2bf9ab(0x25e)][_0x2bf9ab(0x1d1)](this),
                              this[_0x2bf9ab(0x291)]())
                            : (_0x125319['id'] = Number(_0x3eedbc))
                        : (_0x125319['id'] = DataManager['getWeaponIdWithName'](_0x3eedbc)),
                        _0x125319['id'] > 0x0 && _0x328e80[_0x2bf9ab(0x2b5)](_0x125319));
                } else
                    return this[_0x2bf9ab(0x1bc)]()['reduce']((_0x37b1a8, _0xeead8d) => {
                        const _0xc90912 = _0x2bf9ab;
                        return _0xeead8d &&
                            _0xeead8d['note'][_0xc90912(0x161)](
                                _0x47a616[_0xc90912(0xfd)]['RegExp'][_0xc90912(0xea)]
                            )
                            ? _0x37b1a8 * (_0x3e1fca(_0x471f47['$1']) * 0.01)
                            : _0x37b1a8;
                    }, 0x1);
            }
        if (_0xb9f49a['match'](_0x221838[_0x2bf9ab(0x192)])) {
            if (_0x2bf9ab(0x282) !== 'zFEzw') {
                const _0xc80d3a = String(RegExp['$1'])[_0x2bf9ab(0x168)](/[\r\n]+/);
                for (const _0xffe7d0 of _0xc80d3a) {
                    if (_0xffe7d0[_0x2bf9ab(0x161)](/WEAPON[ ](.*):[ ](\d+)/gi)) {
                        if (_0x2bf9ab(0x14c) === _0x2bf9ab(0x14c)) {
                            const _0x5addb6 = String(RegExp['$1']),
                                _0x20dd0e = { id: 0x0, quantity: Number(RegExp['$2']) },
                                _0xfd7553 = /^\d+$/[_0x2bf9ab(0xa5)](_0x5addb6);
                            (_0xfd7553
                                ? (_0x20dd0e['id'] = Number(_0x5addb6))
                                : (_0x20dd0e['id'] = DataManager['getWeaponIdWithName'](_0x5addb6)),
                                _0x20dd0e['id'] > 0x0 &&
                                    ('tXnNX' !== 'tXnNX'
                                        ? this[_0x2bf9ab(0x25f)]()
                                        : _0x328e80[_0x2bf9ab(0x2b5)](_0x20dd0e)));
                        } else this[_0x2bf9ab(0x1f6)]();
                    }
                }
            } else {
                const _0x743aed = _0x4ce213(_0xc3c06f['$1'])
                    [_0x2bf9ab(0x168)](',')
                    [_0x2bf9ab(0x1cc)](_0x3eac4c => _0x3eac4c['trim']());
                for (const _0x35141a of _0x743aed) {
                    let _0x435d7b = 0x0;
                    const _0x2ae826 = /^\d+$/['test'](_0x35141a);
                    _0x2ae826
                        ? (_0x435d7b = _0x499429(_0x35141a))
                        : (_0x435d7b = _0x9f3c5f[_0x2bf9ab(0x1a9)](_0x35141a));
                    if (!this[_0x2bf9ab(0x28d)](_0x435d7b)) return ![];
                }
            }
        }
        return _0x328e80;
    }),
    (DataManager[_0x3591d3(0x1ac)] = function (_0x4dc7e8) {
        const _0x4ba80d = _0x3591d3;
        if (!_0x4dc7e8) return [];
        if (!DataManager[_0x4ba80d(0x22d)](_0x4dc7e8) && !DataManager[_0x4ba80d(0x29b)](_0x4dc7e8))
            return [];
        const _0x3ef735 = VisuMZ[_0x4ba80d(0xfd)][_0x4ba80d(0x376)],
            _0x1ac0fb = _0x4dc7e8[_0x4ba80d(0x148)],
            _0x4c0ef6 = [],
            _0x470389 = _0x1ac0fb['match'](_0x3ef735[_0x4ba80d(0x312)]);
        if (_0x470389)
            for (const _0x20d89d of _0x470389) {
                if (_0x4ba80d(0x9f) === 'ShiMT') {
                    if (!_0x20d89d) continue;
                    _0x20d89d[_0x4ba80d(0x161)](_0x3ef735[_0x4ba80d(0x312)]);
                    const _0x3f1e72 = String(RegExp['$1']),
                        _0x277bb8 = { id: 0x0, quantity: Number(RegExp['$2']) },
                        _0x430384 = /^\d+$/['test'](_0x3f1e72);
                    (_0x430384
                        ? (_0x277bb8['id'] = Number(_0x3f1e72))
                        : _0x4ba80d(0x122) !== 'WTWTo'
                          ? _0x2c9b60['addSkillPoints'](_0x4ceda6, _0x73d434)
                          : (_0x277bb8['id'] = DataManager[_0x4ba80d(0x199)](_0x3f1e72)),
                        _0x277bb8['id'] > 0x0 && _0x4c0ef6[_0x4ba80d(0x2b5)](_0x277bb8));
                } else _0x3c63f1 = 0x0;
            }
        if (_0x1ac0fb[_0x4ba80d(0x161)](_0x3ef735[_0x4ba80d(0x192)])) {
            const _0x4959b0 = String(RegExp['$1'])[_0x4ba80d(0x168)](/[\r\n]+/);
            for (const _0x409f8d of _0x4959b0) {
                if (_0x4ba80d(0x13c) === _0x4ba80d(0x13c)) {
                    if (_0x409f8d[_0x4ba80d(0x161)](/ARMOR[ ](.*):[ ](\d+)/gi)) {
                        const _0x5e61b2 = String(RegExp['$1']),
                            _0x346bd5 = { id: 0x0, quantity: Number(RegExp['$2']) },
                            _0x196709 = /^\d+$/['test'](_0x5e61b2);
                        if (_0x196709) {
                            if (_0x4ba80d(0x337) === 'pJsgn') {
                                const _0x5997e9 = _0x5c719a[_0x4ba80d(0x31a)];
                                if (!_0x5997e9) return ![];
                                const _0x35b713 = _0x5997e9[_0x4ba80d(0x216)]();
                                if (!_0x35b713) return ![];
                                const _0x5837d7 = _0x5997e9['item']();
                                if (!_0x5837d7) return ![];
                                if (!_0x35b713['meetRequirementsForSkillLearnSystem'](_0x5837d7))
                                    return ![];
                                return _0x35b713[_0x4ba80d(0x101)](_0x5837d7);
                            } else _0x346bd5['id'] = Number(_0x5e61b2);
                        } else {
                            if (_0x4ba80d(0x328) === _0x4ba80d(0x2c7)) {
                                const _0x2f9ebd = !this['isLearnedSkill'](_0x5e3e3e);
                                _0x33834f[_0x4ba80d(0xfd)][_0x4ba80d(0xb6)][_0x4ba80d(0x1d1)](
                                    this,
                                    _0x297659
                                );
                                if (_0x2f9ebd && this[_0x4ba80d(0x28d)](_0x126df4)) {
                                    const _0x55542d = _0x1632a3[_0x2b7314],
                                        _0x54c660 = _0x51ede2['SkillLearnSystem'][_0x4ba80d(0x315)](
                                            _0x55542d,
                                            _0x4ba80d(0x2b7)
                                        );
                                    _0x1ddbbd['SkillLearnSystem']['JS'][_0x54c660] &&
                                        _0x2c8ad1['SkillLearnSystem']['JS'][_0x54c660][
                                            _0x4ba80d(0x1d1)
                                        ](this, this, _0x55542d);
                                }
                            } else _0x346bd5['id'] = DataManager[_0x4ba80d(0x199)](_0x5e61b2);
                        }
                        _0x346bd5['id'] > 0x0 && _0x4c0ef6[_0x4ba80d(0x2b5)](_0x346bd5);
                    }
                } else _0x5ad906 = 0x0;
            }
        }
        return _0x4c0ef6;
    }),
    (DataManager[_0x3591d3(0x152)] = function (_0x3a2fed) {
        const _0x17475c = _0x3591d3;
        if (!_0x3a2fed) return 0x0;
        if (!DataManager['isSkill'](_0x3a2fed) && !DataManager[_0x17475c(0x29b)](_0x3a2fed))
            return 0x0;
        const _0x2856fe = VisuMZ[_0x17475c(0xfd)][_0x17475c(0x376)],
            _0x22a4c0 = _0x3a2fed[_0x17475c(0x148)];
        if (_0x22a4c0[_0x17475c(0x161)](_0x2856fe[_0x17475c(0x1d8)])) return Number(RegExp['$1']);
        if (_0x22a4c0[_0x17475c(0x161)](_0x2856fe['LearnCostBatch'])) {
            if (_0x17475c(0x369) === _0x17475c(0x1c3))
                return (
                    (_0xdad8a8 = _0x4d4079[_0x17475c(0xf6)]),
                    _0x3ad75b[_0x17475c(0x242)](
                        _0xb41695,
                        _0x2c5059['classPointsAbbr'],
                        _0x17475c(0x35d)['format'](_0xb9e0bc[_0x17475c(0x359)]),
                        _0x4a80c5[_0x17475c(0x1e9)]
                    )
                );
            else {
                const _0x526ae7 = String(RegExp['$1'])['split'](/[\r\n]+/);
                for (const _0x4d9bda of _0x526ae7) {
                    if (_0x4d9bda[_0x17475c(0x161)](/GOLD:[ ](\d+)/gi)) {
                        if (_0x17475c(0x250) !== 'lgofX') return Number(RegExp['$1']);
                        else
                            ((this[_0x17475c(0xba)] = this['getAbilityPoints']()),
                                (this['_earnedSkillPoints'] = this[_0x17475c(0x277)]()));
                    }
                }
            }
        }
        return 0x0;
    }),
    (TextManager['skillLearnIcon'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x283)]['Icon']),
    (ImageManager[_0x3591d3(0x157)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x13e)][_0x3591d3(0xe3)]),
    (ImageManager['skillPointsIcon'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x11a)][_0x3591d3(0xe3)]),
    (SoundManager['playSkillLearn'] = function () {
        const _0xd894fc = _0x3591d3;
        AudioManager[_0xd894fc(0x129)](VisuMZ[_0xd894fc(0xfd)]['Settings']['Sound']);
    }),
    (TextManager[_0x3591d3(0x21f)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['General'][_0x3591d3(0x23a)]),
    (TextManager[_0x3591d3(0x106)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['General'][_0x3591d3(0x111)]),
    (TextManager[_0x3591d3(0x2f7)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['General'][_0x3591d3(0x109)]),
    (TextManager['skillLearnReqLevelFmt'] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x374)][_0x3591d3(0x209)]),
    (TextManager[_0x3591d3(0x1d4)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x374)]['ReqSkillFmt']),
    (TextManager[_0x3591d3(0x24d)] =
        VisuMZ[_0x3591d3(0xfd)]['Settings'][_0x3591d3(0x374)][_0x3591d3(0x307)]),
    (TextManager['skillLearnSeparationFmt'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x374)][_0x3591d3(0x2cb)]),
    (TextManager['skillLearnItemFmt'] =
        VisuMZ[_0x3591d3(0xfd)]['Settings'][_0x3591d3(0x374)][_0x3591d3(0x2df)]),
    (TextManager[_0x3591d3(0x32c)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['General']['WeaponFmt']),
    (TextManager['skillLearnArmorFmt'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x374)]['ArmorFmt']),
    (TextManager[_0x3591d3(0x32a)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x374)][_0x3591d3(0xf8)]),
    (TextManager[_0x3591d3(0x1d6)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['MenuAccess']['Name']),
    (TextManager[_0x3591d3(0x1f5)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)]['Window'][_0x3591d3(0x16c)]),
    (TextManager['skillLearnReqMet'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x1db)]['ReqMetFmt']),
    (TextManager[_0x3591d3(0xf0)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x1db)][_0x3591d3(0x349)]),
    (TextManager[_0x3591d3(0x120)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x1db)]['ReqLevelFmt']),
    (TextManager['skillLearnReqListSkill'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x1db)][_0x3591d3(0x34a)]),
    (TextManager[_0x3591d3(0x29e)] =
        VisuMZ[_0x3591d3(0xfd)]['Settings'][_0x3591d3(0x1db)][_0x3591d3(0x307)]),
    (TextManager[_0x3591d3(0x147)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x1db)]['LearningTitle']),
    (TextManager[_0x3591d3(0x30a)] =
        VisuMZ[_0x3591d3(0xfd)]['Settings'][_0x3591d3(0x1db)]['IngredientName']),
    (TextManager[_0x3591d3(0x11f)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x1db)]['IngredientCost']),
    (TextManager[_0x3591d3(0x227)] =
        VisuMZ[_0x3591d3(0xfd)]['Settings'][_0x3591d3(0x1db)][_0x3591d3(0x321)]),
    (TextManager['skillLearnConfirmCmd'] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x1db)][_0x3591d3(0x2dd)]),
    (TextManager[_0x3591d3(0x2be)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)]['Window'][_0x3591d3(0x1cf)]),
    (TextManager['abilityPointsFull'] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x13e)][_0x3591d3(0x17c)]),
    (TextManager[_0x3591d3(0x125)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x13e)][_0x3591d3(0xb0)]),
    (TextManager[_0x3591d3(0x254)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x13e)][_0x3591d3(0x1f7)]),
    (TextManager[_0x3591d3(0x165)] =
        VisuMZ['SkillLearnSystem'][_0x3591d3(0x207)][_0x3591d3(0x11a)]['FullText']),
    (TextManager[_0x3591d3(0x23e)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x11a)][_0x3591d3(0xb0)]),
    (TextManager[_0x3591d3(0x340)] =
        VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x207)][_0x3591d3(0x11a)][_0x3591d3(0x1f7)]),
    (VisuMZ[_0x3591d3(0xfd)]['BattleManager_makeRewards'] = BattleManager['makeRewards']),
    (BattleManager[_0x3591d3(0x2d4)] = function () {
        const _0x4e2173 = _0x3591d3;
        (VisuMZ[_0x4e2173(0xfd)][_0x4e2173(0x177)][_0x4e2173(0x1d1)](this),
            this[_0x4e2173(0x155)](),
            this[_0x4e2173(0x296)](),
            this[_0x4e2173(0xc0)](),
            this[_0x4e2173(0x35a)]());
    }),
    (VisuMZ['SkillLearnSystem']['BattleManager_displayRewards'] = BattleManager[_0x3591d3(0x17a)]),
    (BattleManager[_0x3591d3(0x17a)] = function () {
        const _0x226677 = _0x3591d3;
        (VisuMZ[_0x226677(0xfd)][_0x226677(0xb5)][_0x226677(0x1d1)](this),
            this[_0x226677(0x1bf)](),
            this[_0x226677(0x31f)]());
    }),
    (BattleManager[_0x3591d3(0x155)] = function () {
        const _0x5b3170 = _0x3591d3;
        this['_rewards'][_0x5b3170(0xfe)] = $gameTroop[_0x5b3170(0x19a)]();
    }),
    (BattleManager['displayRewardsAbilityPoints'] = function () {
        const _0x3f25fc = _0x3591d3;
        if (!this[_0x3f25fc(0x305)]()) return;
        $gameMessage[_0x3f25fc(0xd3)]();
        const _0x4db8e3 = $gameParty[_0x3f25fc(0x107)](),
            _0x1b6743 = VisuMZ[_0x3f25fc(0xfd)][_0x3f25fc(0x207)][_0x3f25fc(0x13e)],
            _0x14d33a = _0x1b6743[_0x3f25fc(0x237)];
        for (const _0x1bfeb2 of _0x4db8e3) {
            if (!_0x1bfeb2) continue;
            const _0x3d6dbc = _0x14d33a['format'](
                _0x1bfeb2[_0x3f25fc(0x210)](),
                _0x1bfeb2[_0x3f25fc(0xc9)](),
                TextManager['abilityPointsAbbr'],
                TextManager[_0x3f25fc(0x254)]
            );
            $gameMessage[_0x3f25fc(0x294)]('\x5c.' + _0x3d6dbc);
        }
    }),
    (BattleManager[_0x3591d3(0x296)] = function () {
        const _0x329b28 = _0x3591d3;
        this[_0x329b28(0x194)][_0x329b28(0xfe)] = this[_0x329b28(0x194)][_0x329b28(0xfe)] || 0x0;
        let _0x4cf76b = $gameParty[_0x329b28(0x1a2)]();
        VisuMZ[_0x329b28(0xfd)][_0x329b28(0x207)][_0x329b28(0x13e)][_0x329b28(0x22b)] &&
            ('EbnbM' !== _0x329b28(0x1b1)
                ? (_0x4cf76b = _0x4cf76b[_0x329b28(0x149)](_0x53b0a0 =>
                      _0x53b0a0[_0x329b28(0xd6)]()
                  ))
                : this[_0x329b28(0x310)]());
        for (const _0x183bdf of _0x4cf76b) {
            if (_0x329b28(0x1af) !== _0x329b28(0x1af)) {
                if (_0x56c198[_0x329b28(0x161)](/(?:JOB POINTS|JP):[ ](\d+)/gi))
                    return _0x3ccbb4(_0x32d459['$1']);
            } else {
                if (!_0x183bdf) continue;
                if (!$dataSystem[_0x329b28(0x271)] && !_0x183bdf[_0x329b28(0x2e9)]()) continue;
                (_0x183bdf[_0x329b28(0x2b4)](this[_0x329b28(0x194)][_0x329b28(0xfe)]),
                    _0x183bdf[_0x329b28(0x2d6)](this['_rewards'][_0x329b28(0xfe)]));
            }
        }
    }),
    (BattleManager[_0x3591d3(0x305)] = function () {
        const _0xfd093 = _0x3591d3;
        return VisuMZ[_0xfd093(0xfd)][_0xfd093(0x207)]['AbilityPoints']['ShowVictory'];
    }),
    (BattleManager[_0x3591d3(0xc0)] = function () {
        const _0x57ca35 = _0x3591d3;
        this[_0x57ca35(0x194)][_0x57ca35(0x15d)] = $gameTroop[_0x57ca35(0x145)]();
    }),
    (BattleManager[_0x3591d3(0x31f)] = function () {
        const _0x203e25 = _0x3591d3;
        if (!this[_0x203e25(0x28f)]()) return;
        $gameMessage[_0x203e25(0xd3)]();
        const _0x2aec6d = $gameParty[_0x203e25(0x107)](),
            _0x316df0 = VisuMZ[_0x203e25(0xfd)]['Settings']['SkillPoints'],
            _0x57c530 = _0x316df0[_0x203e25(0x237)];
        for (const _0x4752fd of _0x2aec6d) {
            if ('yBDNR' === _0x203e25(0x224)) {
                if (!_0x4752fd) continue;
                const _0xc6285c = _0x57c530[_0x203e25(0x242)](
                    _0x4752fd[_0x203e25(0x210)](),
                    _0x4752fd[_0x203e25(0x144)](),
                    TextManager[_0x203e25(0x23e)],
                    TextManager['skillPointsFmt']
                );
                $gameMessage[_0x203e25(0x294)]('\x5c.' + _0xc6285c);
            } else
                (_0xf53f9e[_0x203e25(0x26a)](_0x2abd37, _0x20fe46),
                    _0x324053[_0x203e25(0xa7)](_0x20b8fb[_0x203e25(0x2ed)]));
        }
    }),
    (BattleManager[_0x3591d3(0x35a)] = function () {
        const _0x4e7dd6 = _0x3591d3;
        this[_0x4e7dd6(0x194)][_0x4e7dd6(0x15d)] = this[_0x4e7dd6(0x194)][_0x4e7dd6(0x15d)] || 0x0;
        let _0x30ed7b = $gameParty['allMembers']();
        VisuMZ[_0x4e7dd6(0xfd)][_0x4e7dd6(0x207)][_0x4e7dd6(0x11a)][_0x4e7dd6(0x22b)] &&
            (_0x30ed7b = _0x30ed7b['filter'](_0x131521 => _0x131521[_0x4e7dd6(0xd6)]()));
        for (const _0x4eafdf of _0x30ed7b) {
            if (_0x4e7dd6(0x235) !== _0x4e7dd6(0x1a6)) {
                if (!_0x4eafdf) continue;
                if (!$dataSystem[_0x4e7dd6(0x271)] && !_0x4eafdf[_0x4e7dd6(0x2e9)]()) continue;
                (_0x4eafdf[_0x4e7dd6(0x1cb)](this[_0x4e7dd6(0x194)][_0x4e7dd6(0x15d)]),
                    _0x4eafdf[_0x4e7dd6(0x1d7)](this[_0x4e7dd6(0x194)][_0x4e7dd6(0x15d)]));
            } else return _0xf70cf[_0x4e7dd6(0xfd)][_0x4e7dd6(0x1c5)][_0x4e7dd6(0x1d1)](this);
        }
    }),
    (BattleManager['skillPointsVisible'] = function () {
        const _0x1234ac = _0x3591d3;
        return VisuMZ[_0x1234ac(0xfd)][_0x1234ac(0x207)]['SkillPoints'][_0x1234ac(0x27f)];
    }),
    (VisuMZ['SkillLearnSystem'][_0x3591d3(0x28e)] = Game_System[_0x3591d3(0x1ad)][_0x3591d3(0xb4)]),
    (Game_System['prototype']['initialize'] = function () {
        const _0x3ece4a = _0x3591d3;
        (VisuMZ[_0x3ece4a(0xfd)][_0x3ece4a(0x28e)][_0x3ece4a(0x1d1)](this),
            this[_0x3ece4a(0x2ad)]());
    }),
    (Game_System[_0x3591d3(0x1ad)][_0x3591d3(0x2ad)] = function () {
        const _0x453f7a = _0x3591d3;
        this[_0x453f7a(0x11e)] =
            VisuMZ['SkillLearnSystem']['Settings'][_0x453f7a(0x283)][_0x453f7a(0x1ee)];
    }),
    (Game_System[_0x3591d3(0x1ad)][_0x3591d3(0x2d1)] = function () {
        const _0x5ed105 = _0x3591d3;
        return (
            this[_0x5ed105(0x11e)] === undefined && this['initSkillLearnSystemMenuAccess'](),
            this['_SkillLearnSystem_MenuAccess']
        );
    }),
    (Game_System[_0x3591d3(0x1ad)]['setSkillLearnSystemMenuAccess'] = function (_0x41fcb2) {
        const _0xbfdbfa = _0x3591d3;
        if (this[_0xbfdbfa(0x11e)] === undefined) {
            if ('dQitf' !== _0xbfdbfa(0x23b)) {
                if (!_0xa5016f['inBattle']()) return;
                if (!this[_0xbfdbfa(0x268)]()[_0xbfdbfa(0x203)]()) return;
                const _0x106ec7 = _0x35d106[_0xbfdbfa(0xfd)][_0xbfdbfa(0x207)]['AbilityPoints'];
                let _0x4cbf4a = 0x0;
                try {
                    _0x4cbf4a = _0x945e34(_0x106ec7[_0xbfdbfa(0x344)]);
                } catch (_0x56679b) {
                    if (_0x1d6f4a[_0xbfdbfa(0x19b)]()) _0x15a5a1['log'](_0x56679b);
                }
                this[_0xbfdbfa(0x268)]()[_0xbfdbfa(0x2b4)](_0x4cbf4a);
            } else this[_0xbfdbfa(0x2ad)]();
        }
        this['_SkillLearnSystem_MenuAccess'] = _0x41fcb2;
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x35b)] = Game_Action[_0x3591d3(0x1ad)][_0x3591d3(0x33c)]),
    (Game_Action[_0x3591d3(0x1ad)][_0x3591d3(0x33c)] = function (_0x10b85f) {
        const _0x521288 = _0x3591d3;
        (VisuMZ[_0x521288(0xfd)]['Game_Action_applyItemUserEffect'][_0x521288(0x1d1)](
            this,
            _0x10b85f
        ),
            this[_0x521288(0x143)](_0x10b85f));
    }),
    (Game_Action['prototype']['applySkillLearnSystemUserEffect'] = function (_0x4eeb82) {
        const _0x2806f6 = _0x3591d3;
        if (this[_0x2806f6(0x284)]()) this[_0x2806f6(0x2ef)](_0x4eeb82);
    }),
    (Game_Action[_0x3591d3(0x1ad)][_0x3591d3(0x2ef)] = function (_0xf771c4) {
        const _0x168e80 = _0x3591d3,
            _0x4aeafd = VisuMZ[_0x168e80(0xfd)]['RegExp'],
            _0x3a5da6 = this[_0x168e80(0x284)]()[_0x168e80(0x148)];
        if ($gameParty[_0x168e80(0x1b4)]()) {
            if (
                this[_0x168e80(0x268)]()[_0x168e80(0x203)]() &&
                _0x3a5da6['match'](_0x4aeafd[_0x168e80(0x252)])
            ) {
                if (_0x168e80(0x2af) === _0x168e80(0x2af)) {
                    const _0x54d887 = eval(RegExp['$1']);
                    this['subject']()[_0x168e80(0x2b4)](_0x54d887);
                } else return _0x29a3d3(_0x975caa['$1']);
            } else {
                if (_0x168e80(0x332) === _0x168e80(0x332)) this['applyAbilityPoints']();
                else {
                    const _0x4a47b8 = _0x1ca3cc[_0x168e80(0x148)];
                    if (_0x4a47b8[_0x168e80(0x161)](_0x3da349)) {
                        const _0xddeaf0 = _0x4041e7(_0x3f3c98['$1']),
                            _0x2f70ec =
                                '\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20let\x20cost\x20=\x200;\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Cost\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20return\x20cost;\x0a\x20\x20\x20\x20\x20\x20\x20\x20'[
                                    'format'
                                ](_0xddeaf0),
                            _0x344cd9 = _0x49c4c5[_0x168e80(0xfd)][_0x168e80(0x315)](
                                _0x3cafc2,
                                _0x4dde55
                            );
                        _0x8c13af[_0x168e80(0xfd)]['JS'][_0x344cd9] = new _0x56a842(_0x2f70ec);
                    }
                }
            }
            if (
                _0xf771c4[_0x168e80(0x203)]() &&
                _0x3a5da6[_0x168e80(0x161)](_0x4aeafd['TargetGainAbilityPoints'])
            ) {
                const _0x259d4d = eval(RegExp['$1']);
                _0xf771c4[_0x168e80(0x2b4)](_0x259d4d);
            }
        }
        if ($gameParty['inBattle']()) {
            if (
                this[_0x168e80(0x268)]()[_0x168e80(0x203)]() &&
                _0x3a5da6[_0x168e80(0x161)](_0x4aeafd[_0x168e80(0xfa)])
            ) {
                if (_0x168e80(0x2d5) !== _0x168e80(0x24e)) {
                    const _0x4f4ded = eval(RegExp['$1']);
                    this['subject']()[_0x168e80(0x1cb)](_0x4f4ded);
                } else return _0x48d84d(_0x3d5f19['$1']);
            } else this[_0x168e80(0x33a)]();
            if (
                _0xf771c4[_0x168e80(0x203)]() &&
                _0x3a5da6[_0x168e80(0x161)](_0x4aeafd[_0x168e80(0xfc)])
            ) {
                if (_0x168e80(0xaf) !== _0x168e80(0x1f1)) {
                    const _0x1695d8 = eval(RegExp['$1']);
                    _0xf771c4[_0x168e80(0x1cb)](_0x1695d8);
                } else _0x4a6540 = 0x0;
            }
        }
        if (_0x3a5da6[_0x168e80(0x161)](/<NOTETAG>/i)) {
        }
    }),
    (Game_Action[_0x3591d3(0x1ad)][_0x3591d3(0x1f6)] = function () {
        const _0x35b065 = _0x3591d3;
        if (!$gameParty['inBattle']()) return;
        if (!this['subject']()[_0x35b065(0x203)]()) return;
        const _0x9c3563 = VisuMZ[_0x35b065(0xfd)]['Settings'][_0x35b065(0x13e)];
        let _0x388eb4 = 0x0;
        try {
            if (_0x35b065(0xbb) !== _0x35b065(0xbb)) {
                if (
                    _0x28609b['SkillLearnSystem'][_0x35b065(0x207)][_0x35b065(0x1db)][
                        _0x35b065(0x2fc)
                    ]
                )
                    return _0x382f7a[_0x35b065(0xfd)][_0x35b065(0x207)][_0x35b065(0x1db)][
                        _0x35b065(0x2fc)
                    ][_0x35b065(0x1d1)](this);
                const _0x2a326a = this[_0x35b065(0x19d)](),
                    _0x245517 = _0x2a326a['x'],
                    _0x2cf667 = _0x2a326a['y'],
                    _0x166b90 = _0x2a326a[_0x35b065(0x347)],
                    _0x543ad4 = _0x2a326a[_0x35b065(0x2a0)] - this['calcWindowHeight'](0x2, ![]);
                return new _0x4e014b(_0x245517, _0x2cf667, _0x166b90, _0x543ad4);
            } else _0x388eb4 = eval(_0x9c3563['PerAction']);
        } catch (_0x102cd2) {
            if ($gameTemp[_0x35b065(0x19b)]()) console[_0x35b065(0x1a7)](_0x102cd2);
        }
        this['subject']()['gainAbilityPoints'](_0x388eb4);
    }),
    (Game_Action[_0x3591d3(0x1ad)][_0x3591d3(0x33a)] = function () {
        const _0x29216d = _0x3591d3;
        if (!$gameParty['inBattle']()) return;
        if (!this[_0x29216d(0x268)]()[_0x29216d(0x203)]()) return;
        const _0x58d3c2 = VisuMZ['SkillLearnSystem'][_0x29216d(0x207)][_0x29216d(0x11a)];
        let _0x26aad1 = 0x0;
        try {
            if (_0x29216d(0x1e2) === _0x29216d(0xdf)) {
                if (_0x5e1ee8[_0x29216d(0x315)])
                    return _0x1185c7['createKeyJS'](_0x58fcd5, _0x872bda);
                let _0x4e4392 = '';
                if (_0x1ce9d3[_0x29216d(0x292)](_0x33600f))
                    _0x4e4392 = 'Actor-%1-%2'[_0x29216d(0x242)](_0x14fc0a['id'], _0x287535);
                if (_0x2a75af[_0x29216d(0x292)](_0xaf624f))
                    _0x4e4392 = _0x29216d(0x30d)['format'](_0x70efd1['id'], _0x3c802c);
                if (_0x3aca1c[_0x29216d(0x292)](_0x27cee7))
                    _0x4e4392 = _0x29216d(0x244)['format'](_0x18afc2['id'], _0x1a9a9e);
                if (_0x296e5e[_0x29216d(0x292)](_0x5a624c))
                    _0x4e4392 = 'Item-%1-%2'[_0x29216d(0x242)](_0x52f125['id'], _0xfd2bd3);
                if (_0x599725['includes'](_0x41159b))
                    _0x4e4392 = _0x29216d(0x1b9)['format'](_0x40229b['id'], _0x37aeaf);
                if (_0x179df7['includes'](_0x3d3fa8))
                    _0x4e4392 = _0x29216d(0x306)[_0x29216d(0x242)](_0x5e3a70['id'], _0x5abb31);
                if (_0x5b0188[_0x29216d(0x292)](_0x2ef9e5))
                    _0x4e4392 = _0x29216d(0x2a4)[_0x29216d(0x242)](_0x43d775['id'], _0x1ddfac);
                if (_0x2534d1[_0x29216d(0x292)](_0x10c65f))
                    _0x4e4392 = _0x29216d(0x20e)[_0x29216d(0x242)](_0x5a9d54['id'], _0x16e4e2);
                return _0x4e4392;
            } else _0x26aad1 = eval(_0x58d3c2[_0x29216d(0x344)]);
        } catch (_0x2be967) {
            if ($gameTemp[_0x29216d(0x19b)]()) console['log'](_0x2be967);
        }
        this[_0x29216d(0x268)]()[_0x29216d(0x1cb)](_0x26aad1);
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x2f5)] = Game_Battler['prototype'][_0x3591d3(0x196)]),
    (Game_Battler[_0x3591d3(0x1ad)][_0x3591d3(0x196)] = function (_0x488490) {
        const _0xdb4478 = _0x3591d3;
        (VisuMZ[_0xdb4478(0xfd)]['Game_Battler_onBattleStart'][_0xdb4478(0x1d1)](this, _0x488490),
            this[_0xdb4478(0x203)]() &&
                ((this['_earnedAbilityPoints'] = this[_0xdb4478(0x21e)]()),
                (this['_earnedSkillPoints'] = this[_0xdb4478(0x277)]())));
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0xda)] = Game_Actor[_0x3591d3(0x1ad)]['setup']),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x357)] = function (_0x456fe1) {
        const _0x108d92 = _0x3591d3;
        (VisuMZ[_0x108d92(0xfd)][_0x108d92(0xda)][_0x108d92(0x1d1)](this, _0x456fe1),
            this[_0x108d92(0x310)](),
            this[_0x108d92(0xa9)](),
            this[_0x108d92(0x1e6)](),
            this['gainStartingSkillPoints']());
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x30c)] = Game_Actor['prototype']['changeClass']),
    (Game_Actor['prototype'][_0x3591d3(0x1dc)] = function (_0x12e2bd, _0x47ba0d) {
        const _0x52db43 = _0x3591d3;
        ((this['_SkillLearnSystem_preventLevelUpGain'] = !![]),
            VisuMZ['SkillLearnSystem'][_0x52db43(0x30c)][_0x52db43(0x1d1)](
                this,
                _0x12e2bd,
                _0x47ba0d
            ),
            (this[_0x52db43(0x2ec)] = undefined));
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x181)] = Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x158)]),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x158)] = function () {
        const _0x1e50a1 = _0x3591d3;
        (VisuMZ[_0x1e50a1(0xfd)][_0x1e50a1(0x181)]['call'](this),
            this[_0x1e50a1(0xf2)](this['currentClass']()['id']),
            this[_0x1e50a1(0x2c9)](this[_0x1e50a1(0x22a)]()['id']));
    }),
    (Game_Actor['prototype'][_0x3591d3(0x310)] = function () {
        this['_abilityPoints'] = {};
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['gainStartingAbilityPoints'] = function () {
        const _0x2b30ee = _0x3591d3,
            _0x1608c8 = VisuMZ[_0x2b30ee(0xfd)]['RegExp'],
            _0x17498e = this['actor']()[_0x2b30ee(0x148)];
        if (_0x17498e[_0x2b30ee(0x161)](_0x1608c8[_0x2b30ee(0x1fc)])) {
            if ('uEYOG' === _0x2b30ee(0x2ee)) {
                const _0x387695 = eval(RegExp['$1']);
                this[_0x2b30ee(0x2b4)](_0x387695);
            } else this['learnSkill'](_0x27d060['id']);
        }
        const _0x1b3e51 = VisuMZ[_0x2b30ee(0xfd)][_0x2b30ee(0x207)]['AbilityPoints'];
        if (!_0x1b3e51[_0x2b30ee(0x1fe)]) return;
        const _0xb5cd11 = _0x17498e[_0x2b30ee(0x161)](_0x1608c8['StartClassAbilityPoints']);
        if (_0xb5cd11)
            for (const _0x5d8b4d of _0xb5cd11) {
                if (!_0x5d8b4d) continue;
                _0x5d8b4d[_0x2b30ee(0x161)](_0x1608c8['StartClassAbilityPoints']);
                const _0x252bad = String(RegExp['$1']),
                    _0x19752b = eval(RegExp['$2']),
                    _0x1316d2 = /^\d+$/['test'](_0x252bad);
                let _0x1ac025 = 0x0;
                if (_0x1316d2) _0x1ac025 = Number(_0x252bad);
                else {
                    if (_0x2b30ee(0x339) !== _0x2b30ee(0x339)) {
                        const _0x3e2d64 = _0x72a5a3(_0x2a66c7['$1']);
                        this['gainAbilityPoints'](_0x3e2d64);
                    } else _0x1ac025 = DataManager[_0x2b30ee(0x1ff)](_0x252bad);
                }
                this[_0x2b30ee(0x2b4)](_0x19752b, _0x1ac025);
            }
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x21e)] = function (_0x2d7ff2) {
        const _0x3e31ec = _0x3591d3;
        this[_0x3e31ec(0x362)] === undefined && this[_0x3e31ec(0x310)]();
        const _0x25f95a = VisuMZ[_0x3e31ec(0xfd)][_0x3e31ec(0x207)][_0x3e31ec(0x13e)];
        if (_0x25f95a['SharedResource']) {
            if (_0x3e31ec(0x1bb) === _0x3e31ec(0x1bb)) _0x2d7ff2 = 0x0;
            else
                return (
                    (this[_0x3e31ec(0xba)] = this[_0x3e31ec(0xba)] || 0x0),
                    this[_0x3e31ec(0x21e)]() - this[_0x3e31ec(0xba)]
                );
        } else {
            if (_0x3e31ec(0x361) === _0x3e31ec(0x1a1)) {
                const _0x3fbe4b = this[_0x3e31ec(0x202)]();
                _0x2ae1e6[_0x3e31ec(0xfd)][_0x3e31ec(0x18a)][_0x3e31ec(0x1d1)](this, _0x7f951a);
                if (_0x3fbe4b !== this['isSkillLearnMode']()) {
                    const _0x2f9174 = _0x96b119['_scene'];
                    if (!_0x2f9174) return;
                    const _0xe3d93c = _0x2f9174[_0x3e31ec(0x17d)];
                    if (_0xe3d93c) _0xe3d93c[_0x3e31ec(0x2f6)]();
                }
            } else _0x2d7ff2 = _0x2d7ff2 || this[_0x3e31ec(0x22a)]()['id'];
        }
        return (
            (this[_0x3e31ec(0x362)][_0x2d7ff2] = this[_0x3e31ec(0x362)][_0x2d7ff2] || 0x0),
            Math['round'](this[_0x3e31ec(0x362)][_0x2d7ff2])
        );
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x31b)] = function (_0x44c219, _0x4346dd) {
        const _0x4788be = _0x3591d3;
        this['_abilityPoints'] === undefined && this[_0x4788be(0x310)]();
        const _0x9ea81e = VisuMZ['SkillLearnSystem'][_0x4788be(0x207)][_0x4788be(0x13e)];
        if (_0x9ea81e[_0x4788be(0x1fe)]) {
            if ('SzdvM' === 'SzdvM') _0x4346dd = 0x0;
            else {
                const _0x462b60 = _0x26c619(_0x31bcfe['$1']);
                this[_0x4788be(0x268)]()['gainSkillPoints'](_0x462b60);
            }
        } else {
            if (_0x4788be(0x2d7) !== _0x4788be(0x12c))
                _0x4346dd = _0x4346dd || this['currentClass']()['id'];
            else {
                const _0x261540 = _0x726130[_0x4788be(0x31a)][_0x4788be(0x216)]();
                return (
                    _0x263cc0[_0x4788be(0xfd)]['JS'][_0x3b5f7a][_0x4788be(0x1d1)](
                        this,
                        _0x261540,
                        _0x2e9e1e
                    ) || 0x0
                );
            }
        }
        ((this[_0x4788be(0x362)][_0x4346dd] = this[_0x4788be(0x362)][_0x4346dd] || 0x0),
            (this['_abilityPoints'][_0x4346dd] = Math['round'](_0x44c219 || 0x0)));
        const _0xccb03b = _0x9ea81e[_0x4788be(0x353)] || Number['MAX_SAFE_INTEGER'];
        this[_0x4788be(0x362)][_0x4346dd] = this['_abilityPoints'][_0x4346dd][_0x4788be(0xcc)](
            0x0,
            _0xccb03b
        );
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x2b4)] = function (_0x145ead, _0x1183a3) {
        const _0x2b288b = _0x3591d3;
        (_0x145ead > 0x0 && (_0x145ead *= this[_0x2b288b(0x33d)]()),
            this[_0x2b288b(0x22f)](_0x145ead, _0x1183a3));
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x2d6)] = function (_0x29c3eb) {
        const _0x40c997 = _0x3591d3;
        if (!Imported['VisuMZ_2_ClassChangeSystem']) return;
        (_0x29c3eb > 0x0 &&
            (_0x40c997(0x200) !== _0x40c997(0x12e)
                ? (_0x29c3eb *= this[_0x40c997(0x33d)]())
                : (_0x42cb4e *= this[_0x40c997(0x36e)]())),
            this['gainMulticlassRewardPoints'](_0x29c3eb, _0x40c997(0x27a)));
    }),
    (Game_Actor['prototype'][_0x3591d3(0x22f)] = function (_0x1e56b3, _0x2eef2c) {
        const _0x387b49 = _0x3591d3,
            _0x3e4d18 = VisuMZ[_0x387b49(0xfd)][_0x387b49(0x207)][_0x387b49(0x13e)];
        (_0x3e4d18[_0x387b49(0x1fe)]
            ? (_0x2eef2c = 0x0)
            : (_0x2eef2c = _0x2eef2c || this[_0x387b49(0x22a)]()['id']),
            (_0x1e56b3 += this['getAbilityPoints'](_0x2eef2c)),
            this[_0x387b49(0x31b)](_0x1e56b3, _0x2eef2c));
    }),
    (Game_Actor['prototype'][_0x3591d3(0x1ca)] = function (_0x3ccf4a, _0x168295) {
        this['addAbilityPoints'](-_0x3ccf4a, _0x168295);
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['abilityPointsRate'] = function () {
        const _0x2e6785 = _0x3591d3;
        return this['traitObjects']()[_0x2e6785(0x2f1)]((_0xba1726, _0x3fbd3f) => {
            const _0x274df7 = _0x2e6785;
            if (
                _0x3fbd3f &&
                _0x3fbd3f[_0x274df7(0x148)][_0x274df7(0x161)](
                    VisuMZ[_0x274df7(0xfd)]['RegExp']['AbilityPointsRate']
                )
            ) {
                if (_0x274df7(0x2e7) === _0x274df7(0x2e7))
                    return _0xba1726 * (Number(RegExp['$1']) * 0.01);
                else {
                    const _0x4cebba = _0x1e9abf[_0x274df7(0xfd)][_0x274df7(0x376)];
                    (_0x10c7d6[_0x274df7(0xfd)]['createCostJS'](
                        _0x53d2b3,
                        _0x274df7(0x222),
                        _0x4cebba['jsLearnApCost']
                    ),
                        _0x5b2d4e[_0x274df7(0xfd)][_0x274df7(0x2aa)](
                            _0x30d9f8,
                            _0x274df7(0xec),
                            _0x4cebba['jsLearnCpCost']
                        ),
                        _0x182b3e[_0x274df7(0xfd)][_0x274df7(0x2aa)](
                            _0x5044a8,
                            _0x274df7(0x28a),
                            _0x4cebba['jsLearnJpCost']
                        ),
                        _0x322d74['SkillLearnSystem'][_0x274df7(0x2aa)](
                            _0x378f0f,
                            _0x274df7(0x16a),
                            _0x4cebba[_0x274df7(0x16a)]
                        ),
                        _0x17cfb1[_0x274df7(0xfd)]['createVisibleJS'](
                            _0x4ddea0,
                            _0x274df7(0x173),
                            _0x4cebba['jsLearnShow']
                        ),
                        _0x56a6ac['SkillLearnSystem']['createConditionJS'](
                            _0x12d262,
                            _0x274df7(0x31e),
                            _0x4cebba[_0x274df7(0x31e)]
                        ),
                        _0x39b326[_0x274df7(0xfd)][_0x274df7(0x262)](
                            _0x4731df,
                            _0x274df7(0x183),
                            _0x4cebba[_0x274df7(0x183)]
                        ),
                        _0x2b8832[_0x274df7(0xfd)][_0x274df7(0x262)](
                            _0x134c9f,
                            _0x274df7(0x1be),
                            _0x4cebba['jsLearnShowDetailTxt']
                        ),
                        _0x49a656[_0x274df7(0xfd)][_0x274df7(0x262)](
                            _0x8c039,
                            _0x274df7(0x20b),
                            _0x4cebba['jsLearnReqListTxt']
                        ),
                        _0x3cf337[_0x274df7(0xfd)][_0x274df7(0x262)](
                            _0x6ccf10,
                            _0x274df7(0x366),
                            _0x4cebba[_0x274df7(0x366)]
                        ),
                        _0x13ac24[_0x274df7(0xfd)]['createActionJS'](
                            _0x3011ec,
                            _0x274df7(0x2b7),
                            _0x4cebba['jsOnLearn']
                        ));
                }
            } else return _0x274df7(0x10f) === 'LlbAg' ? _0x5c3555(_0x451903['$1']) : _0xba1726;
        }, 0x1);
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0xf2)] = function (_0x11bfd8) {
        const _0x5cbe47 = _0x3591d3;
        if (this[_0x5cbe47(0x2ec)]) return;
        const _0x58ba38 = VisuMZ[_0x5cbe47(0xfd)][_0x5cbe47(0x207)][_0x5cbe47(0x13e)];
        let _0x12820a = 0x0;
        try {
            _0x12820a = eval(_0x58ba38[_0x5cbe47(0x20f)]);
        } catch (_0x1deb58) {
            if ($gameTemp[_0x5cbe47(0x19b)]()) console[_0x5cbe47(0x1a7)](_0x1deb58);
        }
        this[_0x5cbe47(0x2b4)](_0x12820a, _0x11bfd8);
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0xc9)] = function () {
        const _0x1590ce = _0x3591d3;
        return (
            (this[_0x1590ce(0xba)] = this[_0x1590ce(0xba)] || 0x0),
            this[_0x1590ce(0x21e)]() - this[_0x1590ce(0xba)]
        );
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['initSkillPoints'] = function () {
        const _0x58f483 = _0x3591d3;
        this[_0x58f483(0x112)] = {};
    }),
    (Game_Actor['prototype'][_0x3591d3(0x1dd)] = function () {
        const _0x4dec39 = _0x3591d3,
            _0x50a065 = VisuMZ[_0x4dec39(0xfd)][_0x4dec39(0x376)],
            _0x584fb8 = this[_0x4dec39(0x32d)]()[_0x4dec39(0x148)];
        if (_0x584fb8[_0x4dec39(0x161)](_0x50a065[_0x4dec39(0x2c3)])) {
            if ('TNAGU' !== _0x4dec39(0x26c))
                _0x461bd4 !== ''
                    ? (_0x391471 = _0x56a5b9[_0x4dec39(0x242)](_0x1cb2bb, _0x3c0c93))
                    : (_0x8d7b5d = _0x30c7f4);
            else {
                const _0x4857cf = eval(RegExp['$1']);
                this['gainSkillPoints'](_0x4857cf);
            }
        }
        const _0x4d0d6a = VisuMZ[_0x4dec39(0xfd)]['Settings'][_0x4dec39(0x11a)];
        if (!_0x4d0d6a['SharedResource']) return;
        const _0x28fe68 = _0x584fb8['match'](_0x50a065[_0x4dec39(0x14f)]);
        if (_0x28fe68) {
            if (_0x4dec39(0x264) !== 'LvSwr')
                for (const _0x35daf4 of _0x28fe68) {
                    if (!_0x35daf4) continue;
                    _0x35daf4[_0x4dec39(0x161)](_0x50a065[_0x4dec39(0x14f)]);
                    const _0x12b104 = String(RegExp['$1']),
                        _0x1165ca = eval(RegExp['$2']),
                        _0x425bb5 = /^\d+$/['test'](_0x12b104);
                    let _0x4c487a = 0x0;
                    (_0x425bb5
                        ? (_0x4c487a = Number(_0x12b104))
                        : (_0x4c487a = DataManager['getClassIdWithName'](_0x12b104)),
                        this[_0x4dec39(0x1cb)](_0x1165ca, _0x4c487a));
                }
            else _0x14b501[_0x4dec39(0x2b5)](_0x3a4398[_0x4dec39(0x1a9)](_0xbcae1d));
        }
    }),
    (Game_Actor['prototype']['getSkillPoints'] = function (_0xd27662) {
        const _0x311c89 = _0x3591d3;
        this['_skillPoints'] === undefined && this[_0x311c89(0x1e6)]();
        const _0x21129a = VisuMZ['SkillLearnSystem'][_0x311c89(0x207)]['SkillPoints'];
        return (
            _0x21129a[_0x311c89(0x1fe)]
                ? (_0xd27662 = 0x0)
                : 'LNuCG' === _0x311c89(0x352)
                  ? (_0x37165a = 0x0)
                  : (_0xd27662 = _0xd27662 || this[_0x311c89(0x22a)]()['id']),
            (this['_skillPoints'][_0xd27662] = this['_skillPoints'][_0xd27662] || 0x0),
            Math['round'](this[_0x311c89(0x112)][_0xd27662])
        );
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['setSkillPoints'] = function (_0x2f75b9, _0x14d5e1) {
        const _0x4d3798 = _0x3591d3;
        this[_0x4d3798(0x112)] === undefined && this[_0x4d3798(0x1e6)]();
        const _0xe259b1 = VisuMZ[_0x4d3798(0xfd)]['Settings'][_0x4d3798(0x11a)];
        if (_0xe259b1[_0x4d3798(0x1fe)]) {
            if (_0x4d3798(0x326) !== _0x4d3798(0x326)) {
                if (_0xcb9f1c[_0x4d3798(0x19b)]()) _0xf36739[_0x4d3798(0x1a7)](_0x57df2b);
                return 0x0;
            } else _0x14d5e1 = 0x0;
        } else _0x14d5e1 = _0x14d5e1 || this['currentClass']()['id'];
        ((this['_skillPoints'][_0x14d5e1] = this[_0x4d3798(0x112)][_0x14d5e1] || 0x0),
            (this[_0x4d3798(0x112)][_0x14d5e1] = Math['round'](_0x2f75b9 || 0x0)));
        const _0xb3503e = _0xe259b1[_0x4d3798(0x353)] || Number[_0x4d3798(0x1bd)];
        this[_0x4d3798(0x112)][_0x14d5e1] = this[_0x4d3798(0x112)][_0x14d5e1][_0x4d3798(0xcc)](
            0x0,
            _0xb3503e
        );
    }),
    (Game_Actor['prototype'][_0x3591d3(0x1cb)] = function (_0xea09fc, _0x4131e5) {
        const _0x2faa4d = _0x3591d3;
        (_0xea09fc > 0x0 && (_0xea09fc *= this[_0x2faa4d(0x36e)]()),
            this[_0x2faa4d(0x163)](_0xea09fc, _0x4131e5));
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['gainSkillPointsForMulticlasses'] = function (_0x86d43e) {
        const _0x479cee = _0x3591d3;
        if (!Imported[_0x479cee(0x2e0)]) return;
        if (_0x86d43e > 0x0) {
            if (_0x479cee(0x176) !== _0x479cee(0x176)) {
                if (!_0x2d5a77[_0x479cee(0x2e0)]) return;
                (_0x2d8287 > 0x0 && (_0x4caa00 *= this[_0x479cee(0x36e)]()),
                    this[_0x479cee(0x146)](_0x27ea9e, 'Skill'));
            } else _0x86d43e *= this[_0x479cee(0x36e)]();
        }
        this[_0x479cee(0x146)](_0x86d43e, _0x479cee(0x1fa));
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x163)] = function (_0x5da6b9, _0xa67df0) {
        const _0x5a9863 = _0x3591d3,
            _0x11ca48 = VisuMZ[_0x5a9863(0xfd)][_0x5a9863(0x207)]['SkillPoints'];
        if (_0x11ca48[_0x5a9863(0x1fe)]) {
            if (_0x5a9863(0x241) !== _0x5a9863(0x2f4)) _0xa67df0 = 0x0;
            else {
                const _0x48b1ff = _0x5f2c66[_0x5c6b52];
                ((_0x587331 = _0x1bfb66[_0x5a9863(0x1d4)][_0x5a9863(0x242)](
                    _0x5a9863(0x35d)[_0x5a9863(0x242)](_0x48b1ff[_0x5a9863(0x2b0)]),
                    _0x48b1ff[_0x5a9863(0x210)]
                )),
                    _0x27911e[_0x5a9863(0x1a3)] > 0x0 &&
                        (_0x44c6fa !== ''
                            ? (_0x2569ff = _0x43611b[_0x5a9863(0x242)](_0x1832e5, _0x3695d8))
                            : (_0x43365d = _0x47152b)));
            }
        } else {
            if (_0x5a9863(0x36d) === _0x5a9863(0x319)) return 0x2;
            else _0xa67df0 = _0xa67df0 || this[_0x5a9863(0x22a)]()['id'];
        }
        ((_0x5da6b9 += this['getSkillPoints'](_0xa67df0)),
            this[_0x5a9863(0x1d2)](_0x5da6b9, _0xa67df0));
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x189)] = function (_0x14133c, _0x1d7371) {
        const _0x21219a = _0x3591d3;
        this[_0x21219a(0x163)](-_0x14133c, _0x1d7371);
    }),
    (Game_Actor['prototype'][_0x3591d3(0x36e)] = function () {
        const _0x40953c = _0x3591d3;
        return this['traitObjects']()[_0x40953c(0x2f1)]((_0x56aac5, _0xae9d8f) => {
            const _0x22c3f6 = _0x40953c;
            return _0xae9d8f &&
                _0xae9d8f[_0x22c3f6(0x148)][_0x22c3f6(0x161)](
                    VisuMZ[_0x22c3f6(0xfd)][_0x22c3f6(0x376)][_0x22c3f6(0xea)]
                )
                ? _0x56aac5 * (Number(RegExp['$1']) * 0.01)
                : _0x56aac5;
        }, 0x1);
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x2c9)] = function (_0x407aef) {
        const _0x6d7763 = _0x3591d3;
        if (this[_0x6d7763(0x2ec)]) return;
        const _0xed1532 = VisuMZ[_0x6d7763(0xfd)]['Settings'][_0x6d7763(0x11a)];
        let _0x433e58 = 0x0;
        try {
            _0x433e58 = eval(_0xed1532[_0x6d7763(0x20f)]);
        } catch (_0x1435c1) {
            if ($gameTemp[_0x6d7763(0x19b)]()) console['log'](_0x1435c1);
        }
        this[_0x6d7763(0x1cb)](_0x433e58, _0x407aef);
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['earnedSkillPoints'] = function () {
        const _0xdf0d15 = _0x3591d3;
        return (
            (this[_0xdf0d15(0x1a0)] = this[_0xdf0d15(0x1a0)] || 0x0),
            this[_0xdf0d15(0x277)]() - this[_0xdf0d15(0x1a0)]
        );
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['meetRequirementsForSkillLearnSystem'] = function (_0x5b46fa) {
        const _0x2f3f4f = _0x3591d3;
        if (!_0x5b46fa) return ![];
        const _0x57dbb6 = VisuMZ[_0x2f3f4f(0xfd)]['createKeyJS'](_0x5b46fa, _0x2f3f4f(0x31e));
        if (VisuMZ['SkillLearnSystem']['JS'][_0x57dbb6]) {
            if (!VisuMZ[_0x2f3f4f(0xfd)]['JS'][_0x57dbb6][_0x2f3f4f(0x1d1)](this, this, _0x5b46fa))
                return ![];
        }
        const _0x4b0a3b = VisuMZ[_0x2f3f4f(0xfd)][_0x2f3f4f(0x376)],
            _0x12f8ca = _0x5b46fa['note'];
        if (_0x12f8ca[_0x2f3f4f(0x161)](_0x4b0a3b[_0x2f3f4f(0xe9)])) {
            const _0x5388ed = Number(RegExp['$1']);
            if (_0x5388ed > this[_0x2f3f4f(0xee)]) return ![];
        }
        if (_0x12f8ca['match'](_0x4b0a3b[_0x2f3f4f(0x32e)])) {
            const _0x48d725 = String(RegExp['$1'])
                [_0x2f3f4f(0x168)](',')
                [_0x2f3f4f(0x1cc)](_0x257071 => _0x257071[_0x2f3f4f(0x25a)]());
            for (const _0x1e38e7 of _0x48d725) {
                if ('yjmrR' === _0x2f3f4f(0x335)) {
                    const _0x2fc53b = _0x36e9a4['switches'][_0x470c76],
                        _0x406d5c = _0x4dae2c[_0x2f3f4f(0x108)](_0x208ffc) ? _0x32e777 : _0x250bdb;
                    _0x257e05 += _0x406d5c[_0x2f3f4f(0x242)](_0x2fc53b) + '\x0a';
                } else {
                    let _0x203917 = 0x0;
                    const _0x47883c = /^\d+$/['test'](_0x1e38e7);
                    if (_0x47883c) {
                        if (_0x2f3f4f(0x289) !== _0x2f3f4f(0x2fa)) _0x203917 = Number(_0x1e38e7);
                        else return _0x48bfb4(_0x1ae730['$1']);
                    } else {
                        if ('faCuW' === 'faCuW')
                            _0x203917 = DataManager[_0x2f3f4f(0x1a9)](_0x1e38e7);
                        else
                            return (
                                (this[_0x2f3f4f(0x1a0)] = this['_earnedSkillPoints'] || 0x0),
                                this[_0x2f3f4f(0x277)]() - this[_0x2f3f4f(0x1a0)]
                            );
                    }
                    if (!this[_0x2f3f4f(0x28d)](_0x203917)) return ![];
                }
            }
        }
        if (_0x12f8ca[_0x2f3f4f(0x161)](_0x4b0a3b[_0x2f3f4f(0xc3)])) {
            if (_0x2f3f4f(0x206) !== 'HNzXt')
                _0x12bb25 = _0x429e05[_0x2f3f4f(0x242)](_0x3a442e, _0x5bd39);
            else {
                const _0xa50694 = String(RegExp['$1'])
                    ['split'](',')
                    [_0x2f3f4f(0x1cc)](_0x51f685 => _0x51f685[_0x2f3f4f(0x25a)]());
                let _0x3d8a39 = ![];
                for (const _0x4b43d7 of _0xa50694) {
                    let _0x2bbb8c = 0x0;
                    const _0xe290c8 = /^\d+$/['test'](_0x4b43d7);
                    _0xe290c8
                        ? _0x2f3f4f(0x16f) === _0x2f3f4f(0x16f)
                            ? (_0x2bbb8c = Number(_0x4b43d7))
                            : (_0xbe65fa = _0x27d887[_0x2f3f4f(0x1a9)](_0x1a4e95))
                        : (_0x2bbb8c = DataManager[_0x2f3f4f(0x1a9)](_0x4b43d7));
                    if (this[_0x2f3f4f(0x28d)](_0x2bbb8c)) {
                        if (_0x2f3f4f(0x27e) !== _0x2f3f4f(0x27e))
                            this[_0x2f3f4f(0xa3)] = _0xa52752(_0xfbe871['$1']);
                        else {
                            _0x3d8a39 = !![];
                            break;
                        }
                    }
                }
                if (!_0x3d8a39) return ![];
            }
        }
        if (_0x12f8ca[_0x2f3f4f(0x161)](_0x4b0a3b[_0x2f3f4f(0xa4)])) {
            const _0x2d235e = String(RegExp['$1'])
                ['split'](',')
                [_0x2f3f4f(0x1cc)](_0x1a7f15 => Number(_0x1a7f15));
            for (const _0x5f5804 of _0x2d235e) {
                if (!$gameSwitches[_0x2f3f4f(0x108)](_0x5f5804)) return ![];
            }
        }
        if (_0x12f8ca[_0x2f3f4f(0x161)](_0x4b0a3b['LearnReqSwitchesAny'])) {
            const _0x1d9c37 = String(RegExp['$1'])
                ['split'](',')
                [_0x2f3f4f(0x1cc)](_0x259fba => Number(_0x259fba));
            let _0x104a2a = ![];
            for (const _0x23dd3a of _0x1d9c37) {
                if ($gameSwitches[_0x2f3f4f(0x108)](_0x23dd3a)) {
                    if (_0x2f3f4f(0x274) !== 'UGtSP') {
                        _0x104a2a = !![];
                        break;
                    } else {
                        const _0x53e2bb = _0x25bfc0(_0x18bba2['$1'])[_0x2f3f4f(0x168)](/[\r\n]+/);
                        for (const _0x59fca1 of _0x53e2bb) {
                            if (_0x59fca1['match'](/(?:SKILL POINTS|SP):[ ](\d+)/gi))
                                return _0x3a69a0(_0x18e218['$1']);
                        }
                    }
                }
            }
            if (!_0x104a2a) return ![];
        }
        return !![];
    }),
    (Game_Actor[_0x3591d3(0x1ad)]['canPayForSkillLearnSystem'] = function (_0x44c05b) {
        const _0x2dd0bb = _0x3591d3;
        if (!_0x44c05b) return ![];
        const _0xcd2a79 = DataManager[_0x2dd0bb(0xbc)](_0x44c05b);
        if (_0xcd2a79 > this[_0x2dd0bb(0x21e)]()) return ![];
        const _0x3af3ee = DataManager[_0x2dd0bb(0xe8)](_0x44c05b);
        if (_0x3af3ee > this[_0x2dd0bb(0x277)]()) return ![];
        const _0xbe1dac = DataManager[_0x2dd0bb(0x152)](_0x44c05b);
        if (_0xbe1dac > $gameParty[_0x2dd0bb(0x231)]()) return ![];
        if (Imported['VisuMZ_2_ClassChangeSystem']) {
            const _0x5ca9e4 = DataManager[_0x2dd0bb(0xb9)](_0x44c05b);
            if (_0x5ca9e4 > this['getClassPoints']()) return ![];
            const _0x555477 = DataManager[_0x2dd0bb(0x187)](_0x44c05b);
            if (_0x555477 > this[_0x2dd0bb(0x28b)]()) return ![];
        }
        const _0xa65aa7 = DataManager['getSkillLearnItemCost'](_0x44c05b);
        for (const _0x1cbc46 of _0xa65aa7) {
            if (!_0x1cbc46) continue;
            const _0x39dbfe = $dataItems[_0x1cbc46['id']];
            if (_0x39dbfe && _0x1cbc46[_0x2dd0bb(0x2bc)] > $gameParty[_0x2dd0bb(0x236)](_0x39dbfe))
                return ![];
        }
        const _0x6056e1 = DataManager[_0x2dd0bb(0xe2)](_0x44c05b);
        for (const _0x194a06 of _0x6056e1) {
            if ('iObMD' === 'iObMD') {
                if (!_0x194a06) continue;
                const _0x6bbde = $dataWeapons[_0x194a06['id']];
                if (
                    _0x6bbde &&
                    _0x194a06[_0x2dd0bb(0x2bc)] > $gameParty[_0x2dd0bb(0x236)](_0x6bbde)
                )
                    return ![];
            } else {
                if (this['_SkillLearnSystem_preventLevelUpGain']) return;
                const _0x6e6b56 = _0x209810['SkillLearnSystem'][_0x2dd0bb(0x207)][_0x2dd0bb(0x13e)];
                let _0x1d2180 = 0x0;
                try {
                    _0x1d2180 = _0x2ae3a5(_0x6e6b56[_0x2dd0bb(0x20f)]);
                } catch (_0x12fe01) {
                    if (_0x22361f['isPlaytest']()) _0x561f7a[_0x2dd0bb(0x1a7)](_0x12fe01);
                }
                this['gainAbilityPoints'](_0x1d2180, _0x4edbba);
            }
        }
        const _0x4b4364 = DataManager['getSkillLearnArmorCost'](_0x44c05b);
        for (const _0x2248ce of _0x4b4364) {
            if ('bcydf' === _0x2dd0bb(0x304))
                return _0x5c6510 &&
                    _0x1ab4ef['note'][_0x2dd0bb(0x161)](
                        _0x426c65[_0x2dd0bb(0xfd)]['RegExp'][_0x2dd0bb(0xea)]
                    )
                    ? _0x39a7a2 * (_0x2f81f6(_0x1a89f4['$1']) * 0.01)
                    : _0x5e0bd1;
            else {
                if (!_0x2248ce) continue;
                const _0x5b6d4a = $dataArmors[_0x2248ce['id']];
                if (_0x5b6d4a && _0x2248ce[_0x2dd0bb(0x2bc)] > $gameParty['numItems'](_0x5b6d4a))
                    return ![];
            }
        }
        return !![];
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x18f)] = function (_0x1a6d70) {
        const _0x521077 = _0x3591d3;
        if (!_0x1a6d70) return;
        const _0x683123 = DataManager[_0x521077(0xbc)](_0x1a6d70);
        this[_0x521077(0x1ca)](_0x683123);
        const _0x43eebd = DataManager['getSkillLearnSkillPointCost'](_0x1a6d70);
        this[_0x521077(0x189)](_0x43eebd);
        const _0x44cfe3 = DataManager[_0x521077(0x152)](_0x1a6d70);
        $gameParty[_0x521077(0x160)](_0x44cfe3);
        if (Imported[_0x521077(0x2e0)]) {
            if (_0x521077(0x373) === _0x521077(0x373)) {
                const _0x2c619b = DataManager[_0x521077(0xb9)](_0x1a6d70);
                this[_0x521077(0x320)](_0x2c619b);
                const _0x20be11 = DataManager['getSkillLearnJobPointCost'](_0x1a6d70);
                this['loseJobPoints'](_0x20be11);
            } else _0x269c34 = _0x5510f0[_0x521077(0x242)](_0x1c26a7, _0x112e32);
        }
        const _0x225ce0 = DataManager[_0x521077(0x247)](_0x1a6d70);
        for (const _0x536855 of _0x225ce0) {
            if (!_0x536855) continue;
            const _0x39f600 = $dataItems[_0x536855['id']],
                _0x2cdf6e = _0x536855[_0x521077(0x2bc)];
            $gameParty['loseItem'](_0x39f600, _0x2cdf6e);
        }
        const _0x411b15 = DataManager[_0x521077(0xe2)](_0x1a6d70);
        for (const _0x3e8fa4 of _0x411b15) {
            if (_0x521077(0x1c4) === 'vavQx') _0x975e49 += 0x0;
            else {
                if (!_0x3e8fa4) continue;
                const _0xde783a = $dataWeapons[_0x3e8fa4['id']],
                    _0x587113 = _0x3e8fa4[_0x521077(0x2bc)];
                $gameParty['loseItem'](_0xde783a, _0x587113);
            }
        }
        const _0x4d724d = DataManager['getSkillLearnArmorCost'](_0x1a6d70);
        for (const _0xbdf111 of _0x4d724d) {
            if (!_0xbdf111) continue;
            const _0x498b96 = $dataArmors[_0xbdf111['id']],
                _0x1ed549 = _0xbdf111[_0x521077(0x2bc)];
            $gameParty[_0x521077(0x191)](_0x498b96, _0x1ed549);
        }
        if (DataManager[_0x521077(0x22d)](_0x1a6d70)) {
            if (_0x521077(0x20d) !== _0x521077(0x1e3)) this[_0x521077(0x32f)](_0x1a6d70['id']);
            else
                return (
                    (_0x506674 = _0x45177c['skillLearnGoldFmt']),
                    _0x41503c[_0x521077(0x242)](
                        _0x4aef7c,
                        _0x2f0f9f[_0x521077(0x208)]
                            ? '\x5cI[%1]'[_0x521077(0x242)](
                                  _0x21938e[_0x521077(0xc8)]['Settings'][_0x521077(0x21d)][
                                      'GoldIcon'
                                  ]
                              )
                            : _0x19830f[_0x521077(0x25d)],
                        _0x569691[_0x521077(0x25d)]
                    )
                );
        } else {
            if (DataManager[_0x521077(0x29b)](_0x1a6d70) && Imported[_0x521077(0x1b3)]) {
                if (_0x521077(0x2d0) !== 'nCCkG')
                    return _0x15805e[_0x521077(0xfd)][_0x521077(0x207)][_0x521077(0x13e)][
                        _0x521077(0x27f)
                    ];
                else this[_0x521077(0x2e3)](_0x1a6d70, !![]);
            }
        }
        this[_0x521077(0x2f6)]();
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0xb6)] = Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x32f)]),
    (Game_Actor['prototype'][_0x3591d3(0x32f)] = function (_0x4e78e6) {
        const _0x1a080a = _0x3591d3,
            _0xd7ba39 = !this['isLearnedSkill'](_0x4e78e6);
        VisuMZ[_0x1a080a(0xfd)][_0x1a080a(0xb6)][_0x1a080a(0x1d1)](this, _0x4e78e6);
        if (_0xd7ba39 && this['isLearnedSkill'](_0x4e78e6)) {
            const _0x37c24d = $dataSkills[_0x4e78e6],
                _0xbefe60 = VisuMZ['SkillLearnSystem']['createKeyJS'](_0x37c24d, _0x1a080a(0x2b7));
            VisuMZ[_0x1a080a(0xfd)]['JS'][_0xbefe60] &&
                VisuMZ[_0x1a080a(0xfd)]['JS'][_0xbefe60][_0x1a080a(0x1d1)](this, this, _0x37c24d);
        }
    }),
    (Game_Actor[_0x3591d3(0x1ad)][_0x3591d3(0x166)] = function () {
        const _0xc708e2 = _0x3591d3,
            _0x45b8de = DataManager[_0xc708e2(0x329)](this['currentClass']()['id']);
        for (const _0x12f728 of _0x45b8de) {
            if (_0xc708e2(0x131) !== _0xc708e2(0x127)) {
                const _0x41c900 = $dataSkills[_0x12f728];
                if (!_0x41c900) continue;
                if (_0x41c900[_0xc708e2(0x210)][_0xc708e2(0x25a)]() === '') continue;
                if (_0x41c900[_0xc708e2(0x210)][_0xc708e2(0x161)](/-----/i)) continue;
                this[_0xc708e2(0x32f)](_0x12f728);
            } else _0x4f50f1 = _0x4ee219 || this[_0xc708e2(0x22a)]()['id'];
        }
    }),
    (Game_Enemy[_0x3591d3(0x1ad)]['abilityPoints'] = function () {
        const _0x15e83c = _0x3591d3,
            _0x5e65b6 = VisuMZ[_0x15e83c(0xfd)][_0x15e83c(0x207)]['AbilityPoints'],
            _0x208324 = VisuMZ['SkillLearnSystem'][_0x15e83c(0x376)],
            _0x102b04 = this[_0x15e83c(0xeb)]()[_0x15e83c(0x148)];
        if (_0x102b04[_0x15e83c(0x161)](_0x208324[_0x15e83c(0xce)]))
            try {
                return eval(RegExp['$1']);
            } catch (_0x2fd239) {
                if (_0x15e83c(0x336) === 'mPTMd') {
                    if ($gameTemp[_0x15e83c(0x19b)]()) console[_0x15e83c(0x1a7)](_0x2fd239);
                    return 0x0;
                } else {
                    const _0x2fdbc4 = _0x20beb1[_0x15e83c(0x277)](_0x3201c3);
                    this['drawSkillPoints'](_0x2fdbc4, _0x374d36, _0x4a0bfe, _0x4b1bda, _0x430d43);
                }
            }
        try {
            if (_0x15e83c(0x267) === _0x15e83c(0x267)) return eval(_0x5e65b6[_0x15e83c(0x1eb)]);
            else {
                let _0x4128a0 = _0x5100d5[_0x15e83c(0x1d6)];
                if (_0x4128a0[_0x15e83c(0x161)](/\\I\[(\d+)\]/i)) return _0x4128a0;
                if (!_0x224db4[_0x15e83c(0x195)]) return _0x4128a0;
                if (this[_0x15e83c(0x13a)]() === _0x15e83c(0x345)) return _0x4128a0;
                const _0x25f5b1 = _0x1df781[_0x15e83c(0x36b)];
                return _0x15e83c(0x1a4)[_0x15e83c(0x242)](_0x25f5b1, _0x4128a0);
            }
        } catch (_0x1c51e4) {
            if ($gameTemp[_0x15e83c(0x19b)]()) console[_0x15e83c(0x1a7)](_0x1c51e4);
            return 0x0;
        }
    }),
    (Game_Enemy[_0x3591d3(0x1ad)][_0x3591d3(0x15d)] = function () {
        const _0x594a53 = _0x3591d3,
            _0x2e4afa = VisuMZ[_0x594a53(0xfd)][_0x594a53(0x207)][_0x594a53(0x11a)],
            _0x4b5459 = VisuMZ[_0x594a53(0xfd)][_0x594a53(0x376)],
            _0xd53f26 = this[_0x594a53(0xeb)]()[_0x594a53(0x148)];
        if (_0xd53f26[_0x594a53(0x161)](_0x4b5459['EnemySkillPoints'])) {
            if (_0x594a53(0xb3) === _0x594a53(0x19e))
                return _0x12cc13['SkillLearnSystem']['Window_SkillList_includes'][_0x594a53(0x1d1)](
                    this,
                    _0x4d70ad
                );
            else
                try {
                    if (_0x594a53(0x17b) === _0x594a53(0x370)) this[_0x594a53(0x184)]();
                    else return eval(RegExp['$1']);
                } catch (_0x5f465d) {
                    if (_0x594a53(0x27b) === _0x594a53(0x27b)) {
                        if ($gameTemp[_0x594a53(0x19b)]()) console['log'](_0x5f465d);
                        return 0x0;
                    } else
                        this[_0x594a53(0x11e)] =
                            _0x38af4a[_0x594a53(0xfd)]['Settings'][_0x594a53(0x283)][
                                _0x594a53(0x1ee)
                            ];
                }
        }
        try {
            return eval(_0x2e4afa[_0x594a53(0x1eb)]);
        } catch (_0x154cd3) {
            if ($gameTemp[_0x594a53(0x19b)]()) console[_0x594a53(0x1a7)](_0x154cd3);
            return 0x0;
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x260)] = Game_Party[_0x3591d3(0x1ad)][_0x3591d3(0x185)]),
    (Game_Party[_0x3591d3(0x1ad)][_0x3591d3(0x185)] = function () {
        const _0x20bac7 = _0x3591d3;
        (VisuMZ['SkillLearnSystem'][_0x20bac7(0x260)]['call'](this), this[_0x20bac7(0x219)]());
    }),
    (Game_Party[_0x3591d3(0x1ad)][_0x3591d3(0x219)] = function () {
        const _0x377d26 = _0x3591d3;
        for (const _0x180657 of this[_0x377d26(0x1a2)]()) {
            if ('BgbAy' === 'BgbAy') {
                if (!_0x180657) continue;
                _0x180657[_0x377d26(0x166)]();
            } else _0x3fdef7 *= this[_0x377d26(0x33d)]();
        }
    }),
    (Game_Troop[_0x3591d3(0x1ad)]['abilityPointsTotal'] = function () {
        const _0x120aed = _0x3591d3;
        return this[_0x120aed(0x2cd)]()[_0x120aed(0x2f1)](
            (_0x39f29b, _0x441756) => _0x39f29b + _0x441756[_0x120aed(0xfe)](),
            0x0
        );
    }),
    (Game_Troop[_0x3591d3(0x1ad)][_0x3591d3(0x145)] = function () {
        const _0x3bda03 = _0x3591d3;
        return this['deadMembers']()[_0x3bda03(0x2f1)](
            (_0x467977, _0x123e35) => _0x467977 + _0x123e35[_0x3bda03(0x15d)](),
            0x0
        );
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0xef)] = Scene_Skill['prototype'][_0x3591d3(0xcd)]),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0xcd)] = function () {
        const _0x1578de = _0x3591d3;
        (VisuMZ[_0x1578de(0xfd)][_0x1578de(0xef)][_0x1578de(0x1d1)](this),
            this['createSkillLearnSystemWindows']());
    }),
    (Scene_Skill['prototype'][_0x3591d3(0x2eb)] = function () {
        const _0x5c202b = _0x3591d3;
        (this[_0x5c202b(0x323)](), this[_0x5c202b(0x2f8)]());
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x323)] = function () {
        const _0x145d10 = _0x3591d3,
            _0x5d47df = this['skillLearnIngredientsWindowRect']();
        ((this['_skillLearnIngredientsWindow'] = new Window_SkillLearnIngredients(_0x5d47df)),
            this[_0x145d10(0x302)](this[_0x145d10(0x303)]),
            this[_0x145d10(0x303)][_0x145d10(0x175)]());
        const _0x382667 = VisuMZ[_0x145d10(0xfd)]['Settings'][_0x145d10(0x1db)][_0x145d10(0x135)];
        this[_0x145d10(0x303)][_0x145d10(0x9e)](_0x382667);
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x218)] = function () {
        const _0xfb0f25 = _0x3591d3;
        if (VisuMZ[_0xfb0f25(0xfd)][_0xfb0f25(0x207)]['Window'][_0xfb0f25(0x2fc)])
            return VisuMZ[_0xfb0f25(0xfd)]['Settings']['Window']['DetailWindow_RectJS'][
                _0xfb0f25(0x1d1)
            ](this);
        const _0xf99d4c = this[_0xfb0f25(0x19d)](),
            _0x52c41e = _0xf99d4c['x'],
            _0x2c8331 = _0xf99d4c['y'],
            _0x18d6fb = _0xf99d4c['width'],
            _0x485f32 = _0xf99d4c['height'] - this['calcWindowHeight'](0x2, ![]);
        return new Rectangle(_0x52c41e, _0x2c8331, _0x18d6fb, _0x485f32);
    }),
    (Scene_Skill['prototype'][_0x3591d3(0x2f8)] = function () {
        const _0x5e83e3 = _0x3591d3,
            _0x920dac = this[_0x5e83e3(0x2e2)]();
        ((this[_0x5e83e3(0x1df)] = new Window_SkillLearnConfirm(_0x920dac)),
            this[_0x5e83e3(0x302)](this[_0x5e83e3(0x1df)]),
            this[_0x5e83e3(0x1df)][_0x5e83e3(0x130)](
                'ok',
                this[_0x5e83e3(0x2ba)][_0x5e83e3(0x1c6)](this)
            ),
            this['_skillLearnConfirmWindow']['setHandler'](
                'cancel',
                this['onSkillLearnConfirmCancel'][_0x5e83e3(0x1c6)](this)
            ),
            this['_skillLearnConfirmWindow'][_0x5e83e3(0x175)]());
        const _0x40ae67 =
            VisuMZ[_0x5e83e3(0xfd)][_0x5e83e3(0x207)][_0x5e83e3(0x1db)][_0x5e83e3(0x205)];
        this[_0x5e83e3(0x1df)][_0x5e83e3(0x9e)](_0x40ae67);
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x2e2)] = function () {
        const _0x2fa64f = _0x3591d3;
        if (VisuMZ['SkillLearnSystem'][_0x2fa64f(0x207)][_0x2fa64f(0x1db)][_0x2fa64f(0xca)])
            return VisuMZ['SkillLearnSystem'][_0x2fa64f(0x207)]['Window'][_0x2fa64f(0xca)][
                _0x2fa64f(0x1d1)
            ](this);
        const _0x54cd6f = this[_0x2fa64f(0x19d)](),
            _0x1b9fe0 = _0x54cd6f['width'],
            _0x596fc0 = this[_0x2fa64f(0x1ab)](0x2, ![]),
            _0xd17b5a = _0x54cd6f['x'],
            _0x5c2660 = _0x54cd6f['y'] + _0x54cd6f[_0x2fa64f(0x2a0)] - _0x596fc0;
        return new Rectangle(_0xd17b5a, _0x5c2660, _0x1b9fe0, _0x596fc0);
    }),
    (VisuMZ[_0x3591d3(0xfd)]['Scene_Skill_onItemOk'] = Scene_Skill['prototype'][_0x3591d3(0x255)]),
    (Scene_Skill['prototype']['onItemOk'] = function () {
        const _0x514f28 = _0x3591d3;
        this['_itemWindow'][_0x514f28(0x202)]()
            ? this[_0x514f28(0x16d)]()
            : VisuMZ[_0x514f28(0xfd)][_0x514f28(0x1c0)][_0x514f28(0x1d1)](this);
    }),
    (Scene_Skill['prototype'][_0x3591d3(0x16d)] = function () {
        const _0x1c1744 = _0x3591d3;
        (this[_0x1c1744(0x2b1)]['hide'](),
            this['_skillLearnIngredientsWindow'][_0x1c1744(0xa2)](),
            this[_0x1c1744(0x303)][_0x1c1744(0x2f6)](),
            this[_0x1c1744(0x1df)][_0x1c1744(0xa2)](),
            this[_0x1c1744(0x1df)][_0x1c1744(0x2f6)](),
            this[_0x1c1744(0x1df)][_0x1c1744(0x2bd)](),
            this[_0x1c1744(0x1df)][_0x1c1744(0x34e)](0x0));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)]['onSkillLearnConfirmOk'] = function () {
        const _0x330b6e = _0x3591d3;
        if (VisuMZ['SkillLearnSystem'][_0x330b6e(0x207)]['Animation'][_0x330b6e(0xb2)])
            'oZBbU' !== _0x330b6e(0x238)
                ? (this[_0x330b6e(0x33b)] = _0x4bb95d[_0x330b6e(0x356)](
                      _0x275984(_0x4dadab['$1']),
                      0x1
                  ))
                : this[_0x330b6e(0xf9)]();
        else {
            if (_0x330b6e(0x33e) === _0x330b6e(0x33e)) this[_0x330b6e(0x25f)]();
            else {
                if (_0x5e521b[_0x330b6e(0x19b)]()) _0x4dfa19[_0x330b6e(0x1a7)](_0x3c95ef);
            }
        }
    }),
    (Scene_Skill[_0x3591d3(0x1ad)]['onSkillLearnConfirmCancel'] = function () {
        const _0x19532f = _0x3591d3;
        (this[_0x19532f(0x2b1)][_0x19532f(0xa2)](),
            this[_0x19532f(0x2b1)]['activate'](),
            this[_0x19532f(0x303)]['hide'](),
            this['_skillLearnConfirmWindow'][_0x19532f(0x175)]());
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x25f)] = function () {
        const _0x1d7dbb = _0x3591d3;
        ((this[_0x1d7dbb(0xed)][_0x1d7dbb(0x2ac)] = !![]),
            (this['_skillLearnAnimationPlaying'] = ![]),
            SoundManager[_0x1d7dbb(0x2f9)](),
            this['user']()[_0x1d7dbb(0x18f)](this[_0x1d7dbb(0x284)]()),
            this[_0x1d7dbb(0x1f2)](),
            this[_0x1d7dbb(0x2b1)][_0x1d7dbb(0x2f6)](),
            this[_0x1d7dbb(0x17d)][_0x1d7dbb(0x2f6)]());
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x308)] = Scene_Skill['prototype'][_0x3591d3(0x23f)]),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x23f)] = function () {
        const _0x296045 = _0x3591d3;
        (VisuMZ['SkillLearnSystem'][_0x296045(0x308)]['call'](this), this[_0x296045(0x350)]());
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0xf9)] = function () {
        const _0x440f8a = _0x3591d3;
        ((this[_0x440f8a(0x1e5)] = !![]),
            (this['_skillLearnAnimationWait'] = 0x14),
            (this[_0x440f8a(0xed)]['visible'] =
                VisuMZ[_0x440f8a(0xfd)][_0x440f8a(0x207)][_0x440f8a(0x30f)][_0x440f8a(0x1ae)] ||
                ![]),
            this[_0x440f8a(0x358)]());
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x358)] = function () {
        const _0x3b6212 = _0x3591d3;
        ((this[_0x3b6212(0x223)] = new Sprite()),
            this[_0x3b6212(0x2a7)](this[_0x3b6212(0x223)]),
            this['setSkillLearnSkillSpriteBitmap'](),
            this[_0x3b6212(0x2a2)](),
            this[_0x3b6212(0xbf)](),
            this['setSkillLearnSkillSpriteOpacity'](),
            this['createSkillLearnAnimationIDs'](),
            this[_0x3b6212(0x368)](this[_0x3b6212(0xc5)]['shift']()));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)]['setSkillLearnSkillSpriteBitmap'] = function () {
        const _0x43da63 = _0x3591d3,
            _0x374174 = VisuMZ[_0x43da63(0xfd)][_0x43da63(0x376)],
            _0x2a452a = this['item']()['note'];
        this[_0x43da63(0xa3)] = '';
        if (_0x2a452a['match'](_0x374174['learnPicture']))
            'qlZwW' !== _0x43da63(0x110)
                ? (this[_0x43da63(0xa3)] = String(RegExp['$1']))
                : (_0x23ba88 > 0x0 && (_0x2c1512 *= this[_0x43da63(0x36e)]()),
                  this[_0x43da63(0x163)](_0x575d0f, _0x1103df));
        else
            _0x2a452a[_0x43da63(0x161)](_0x374174[_0x43da63(0x2ca)]) &&
                (_0x43da63(0x2a6) !== _0x43da63(0x2a6)
                    ? (_0x551f7c = _0x972d5e(_0xec1f35))
                    : (this[_0x43da63(0xa3)] = String(RegExp['$1'])));
        this[_0x43da63(0xbd)] = new Sprite();
        this[_0x43da63(0xa3)]
            ? (this[_0x43da63(0xbd)][_0x43da63(0x186)] = ImageManager['loadPicture'](
                  this[_0x43da63(0xa3)]
              ))
            : ((this[_0x43da63(0xbd)][_0x43da63(0x186)] = ImageManager[_0x43da63(0x346)](
                  _0x43da63(0x1ea)
              )),
              (this[_0x43da63(0xbd)][_0x43da63(0x186)][_0x43da63(0x114)] = ![]));
        ((this['_skillLearnBitmapSprite'][_0x43da63(0x234)]['x'] = 0.5),
            (this[_0x43da63(0xbd)][_0x43da63(0x234)]['y'] = 0.5));
        if (!this[_0x43da63(0xa3)]) {
            const _0x3bca0c =
                VisuMZ[_0x43da63(0xfd)][_0x43da63(0x207)][_0x43da63(0x30f)][_0x43da63(0x193)] ||
                0x8;
            ((this[_0x43da63(0xbd)][_0x43da63(0x141)]['x'] = _0x3bca0c),
                (this[_0x43da63(0xbd)][_0x43da63(0x141)]['y'] = _0x3bca0c));
        }
        this['_skillLearnIconSprite'][_0x43da63(0x2a7)](this[_0x43da63(0xbd)]);
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x2a2)] = function () {
        const _0x1e41ae = _0x3591d3;
        if (this[_0x1e41ae(0xa3)]) return;
        const _0x425b4f = this[_0x1e41ae(0x284)](),
            _0x3329ce = _0x425b4f[_0x1e41ae(0x2b0)],
            _0x3e0cd9 = ImageManager[_0x1e41ae(0x13f)],
            _0x3fde54 = ImageManager[_0x1e41ae(0x2c1)],
            _0x5a5dcd = (_0x3329ce % 0x10) * _0x3e0cd9,
            _0x251a05 = Math[_0x1e41ae(0x2b2)](_0x3329ce / 0x10) * _0x3fde54;
        this[_0x1e41ae(0xbd)][_0x1e41ae(0x351)](_0x5a5dcd, _0x251a05, _0x3e0cd9, _0x3fde54);
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0xbf)] = function () {
        const _0x168375 = _0x3591d3;
        this['_skillLearnIconSprite']['x'] = Math['round'](Graphics[_0x168375(0x347)] / 0x2);
        const _0x100ff0 = Math[_0x168375(0xc7)](
            ImageManager['iconHeight'] * this[_0x168375(0x223)]['scale']['y']
        );
        this['_skillLearnIconSprite']['y'] = Math[_0x168375(0xc7)](
            (Graphics[_0x168375(0x2a0)] + _0x100ff0) / 0x2
        );
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x275)] = function () {
        const _0x439343 = _0x3591d3;
        ((this[_0x439343(0x33b)] =
            VisuMZ[_0x439343(0xfd)][_0x439343(0x207)][_0x439343(0x30f)][_0x439343(0x2e1)] || 0x1),
            this['item']()[_0x439343(0x148)][_0x439343(0x161)](
                VisuMZ[_0x439343(0xfd)]['RegExp']['opacitySpeed']
            ) &&
                (_0x439343(0x251) === _0x439343(0x17f)
                    ? (_0x48ff1a[_0x439343(0xfd)][_0x439343(0x1cd)][_0x439343(0x1d1)](
                          this,
                          _0x56ec64
                      ),
                      _0x1bf572[_0x439343(0xfd)][_0x439343(0xd7)](_0x401e71))
                    : (this['_skillLearnIconSpriteOpacitySpeed'] = Math['max'](
                          Number(RegExp['$1']),
                          0x1
                      ))),
            (this[_0x439343(0x223)][_0x439343(0x225)] = 0x0));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x12a)] = function () {
        const _0x512bd9 = _0x3591d3;
        this['_skillLearnAnimationIDs'] = [];
        if (
            this[_0x512bd9(0x284)]()[_0x512bd9(0x148)][_0x512bd9(0x161)](
                VisuMZ['SkillLearnSystem'][_0x512bd9(0x376)][_0x512bd9(0x10d)]
            )
        ) {
            if (_0x512bd9(0xd1) !== _0x512bd9(0x341))
                this['_skillLearnAnimationIDs'] = RegExp['$1']
                    [_0x512bd9(0x168)](',')
                    ['map'](_0x270b59 => Number(_0x270b59));
            else return _0x2cc224(_0x32e91f['$1']);
        } else
            _0x512bd9(0x1f9) === _0x512bd9(0x1f9)
                ? (this[_0x512bd9(0xc5)] = this['_skillLearnAnimationIDs'][_0x512bd9(0xd9)](
                      VisuMZ[_0x512bd9(0xfd)][_0x512bd9(0x207)][_0x512bd9(0x30f)]['Animations']
                  ))
                : _0x2dbc0f['loseSkillPoints'](_0x4eae7b, _0x5ab847);
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x368)] = function (_0x2d2d6e) {
        const _0x15a330 = _0x3591d3,
            _0x582d95 = $dataAnimations[_0x2d2d6e];
        if (!_0x582d95) return;
        const _0x3b533c = this[_0x15a330(0x33f)](_0x582d95);
        this[_0x15a330(0x2a9)] = new (_0x3b533c ? Sprite_AnimationMV : Sprite_Animation)();
        const _0x5ba251 = [this['_skillLearnIconSprite']],
            _0x5ce770 = 0x0;
        (this[_0x15a330(0x2a9)][_0x15a330(0x357)](_0x5ba251, _0x582d95, ![], _0x5ce770, null),
            this[_0x15a330(0x2a7)](this['_skillLearnAnimationSprite']));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x33f)] = function (_0x289e91) {
        return !!_0x289e91['frames'];
    }),
    (Scene_Skill['prototype'][_0x3591d3(0x350)] = function () {
        const _0x9bbbd7 = _0x3591d3;
        if (!this['_skillLearnAnimationPlaying']) return;
        (this[_0x9bbbd7(0x2c4)](),
            this[_0x9bbbd7(0x313)](),
            this[_0x9bbbd7(0x29a)]() && this[_0x9bbbd7(0x118)]());
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x2c4)] = function () {
        const _0x3d55cd = _0x3591d3;
        this[_0x3d55cd(0x223)][_0x3d55cd(0x225)] += this[_0x3d55cd(0x33b)];
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x313)] = function () {
        const _0x497aac = _0x3591d3;
        if (!this[_0x497aac(0x2a9)]) return;
        if (this[_0x497aac(0x2a9)][_0x497aac(0x16b)]()) return;
        (this[_0x497aac(0x2dc)](),
            this[_0x497aac(0x368)](this[_0x497aac(0xc5)][_0x497aac(0x330)]()));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x2dc)] = function () {
        const _0x5da462 = _0x3591d3;
        if (!this[_0x5da462(0x2a9)]) return;
        (this[_0x5da462(0x142)](this['_skillLearnAnimationSprite']),
            this[_0x5da462(0x2a9)][_0x5da462(0x23d)](),
            (this[_0x5da462(0x2a9)] = undefined));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x226)] = function () {
        const _0x27b7d9 = _0x3591d3;
        if (!this[_0x27b7d9(0x223)]) return;
        (this['removeChild'](this[_0x27b7d9(0x223)]),
            this[_0x27b7d9(0x223)][_0x27b7d9(0x23d)](),
            (this[_0x27b7d9(0x223)] = undefined));
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x29a)] = function () {
        const _0x2051e9 = _0x3591d3;
        if (TouchInput[_0x2051e9(0x121)]()) return !![];
        if (Input['isTriggered']('ok')) return !![];
        if (Input[_0x2051e9(0x2e6)](_0x2051e9(0x23c))) return !![];
        if (this[_0x2051e9(0x223)][_0x2051e9(0x225)] < 0xff) return ![];
        if (this['_skillLearnAnimationSprite']) return ![];
        return this[_0x2051e9(0x1b2)]-- <= 0x0;
    }),
    (Scene_Skill[_0x3591d3(0x1ad)][_0x3591d3(0x118)] = function () {
        const _0xc8f580 = _0x3591d3;
        (this[_0xc8f580(0x2dc)](),
            this[_0xc8f580(0x226)](),
            this[_0xc8f580(0x25f)](),
            TouchInput['clear'](),
            Input['clear']());
    }),
    (Window_Base[_0x3591d3(0x1ad)][_0x3591d3(0x2d3)] = function (
        _0x253758,
        _0x3d8ec5,
        _0x3f3541,
        _0x15fadd,
        _0x554466
    ) {
        const _0x1b6cae = _0x3591d3;
        _0x554466 = _0x554466 || _0x1b6cae(0x230);
        const _0x2dd734 = _0x1b6cae(0x35d)[_0x1b6cae(0x242)](ImageManager[_0x1b6cae(0x157)]),
            _0x8f0e0 = TextManager[_0x1b6cae(0x254)],
            _0x445500 = _0x8f0e0[_0x1b6cae(0x242)](
                _0x253758,
                TextManager[_0x1b6cae(0x125)],
                _0x2dd734,
                TextManager[_0x1b6cae(0x12f)]
            ),
            _0x40062c = this[_0x1b6cae(0x365)](_0x445500)[_0x1b6cae(0x347)];
        if (_0x554466 === _0x1b6cae(0x230))
            'FEBUq' === 'FEBUq' ? (_0x3d8ec5 += 0x0) : this['initSkillPoints']();
        else
            _0x554466 === _0x1b6cae(0x136)
                ? _0x1b6cae(0x245) === _0x1b6cae(0x239)
                    ? _0x50f69c[_0x1b6cae(0x1ad)][_0x1b6cae(0xb4)][_0x1b6cae(0x1d1)](
                          this,
                          _0x53e6bc
                      )
                    : (_0x3d8ec5 += Math['round']((_0x15fadd - _0x40062c) / 0x2))
                : (_0x3d8ec5 += _0x15fadd - _0x40062c);
        this['drawTextEx'](_0x445500, _0x3d8ec5, _0x3f3541);
    }),
    (Window_Base['prototype']['drawActorAbilityPoints'] = function (
        _0x572b0e,
        _0x170d8d,
        _0x45a728,
        _0x53f0cd,
        _0x2ca74b,
        _0x170813
    ) {
        const _0x4b51d5 = _0x3591d3,
            _0x452283 = _0x572b0e['getAbilityPoints'](_0x170d8d);
        this[_0x4b51d5(0x2d3)](_0x452283, _0x45a728, _0x53f0cd, _0x2ca74b, _0x170813);
    }),
    (Window_Base['prototype']['drawSkillPoints'] = function (
        _0x283e69,
        _0x346a1c,
        _0x211a35,
        _0x177972,
        _0x92fb9
    ) {
        const _0x3f027d = _0x3591d3;
        _0x92fb9 = _0x92fb9 || _0x3f027d(0x230);
        const _0x324b6e = _0x3f027d(0x35d)['format'](ImageManager['skillPointsIcon']),
            _0x4df720 = TextManager[_0x3f027d(0x340)],
            _0x30e8b8 = _0x4df720[_0x3f027d(0x242)](
                _0x283e69,
                TextManager[_0x3f027d(0x23e)],
                _0x324b6e,
                TextManager[_0x3f027d(0x165)]
            ),
            _0x2eac00 = this[_0x3f027d(0x365)](_0x30e8b8)[_0x3f027d(0x347)];
        if (_0x92fb9 === _0x3f027d(0x230)) _0x346a1c += 0x0;
        else
            _0x92fb9 === _0x3f027d(0x136)
                ? (_0x346a1c += Math[_0x3f027d(0xc7)]((_0x177972 - _0x2eac00) / 0x2))
                : (_0x346a1c += _0x177972 - _0x2eac00);
        this[_0x3f027d(0x1de)](_0x30e8b8, _0x346a1c, _0x211a35);
    }),
    (Window_Base['prototype'][_0x3591d3(0x279)] = function (
        _0x1f513e,
        _0x3b201c,
        _0x354a62,
        _0x160460,
        _0x27c3d7,
        _0x588ad
    ) {
        const _0x5a641b = _0x3591d3,
            _0x5ea5f6 = _0x1f513e[_0x5a641b(0x277)](_0x3b201c);
        this[_0x5a641b(0x2ff)](_0x5ea5f6, _0x354a62, _0x160460, _0x27c3d7, _0x588ad);
    }),
    (VisuMZ['SkillLearnSystem'][_0x3591d3(0x25e)] =
        Window_SkillType[_0x3591d3(0x1ad)][_0x3591d3(0x2fb)]),
    (Window_SkillType[_0x3591d3(0x1ad)][_0x3591d3(0x2fb)] = function () {
        const _0xa5854c = _0x3591d3;
        (VisuMZ[_0xa5854c(0xfd)]['Window_SkillType_makeCommandList'][_0xa5854c(0x1d1)](this),
            this[_0xa5854c(0x291)]());
    }),
    (Window_SkillType[_0x3591d3(0x1ad)]['addSkillLearnSystemCommand'] = function () {
        const _0x2baf17 = _0x3591d3;
        if (!$gameSystem['isSkillLearnSystemMenuAccess']()) return;
        if (!this[_0x2baf17(0x354)]) return;
        let _0x46590f = this[_0x2baf17(0x2bb)]();
        const _0x36de04 = this['_actor'][_0x2baf17(0xf7)]()[0x0];
        this[_0x2baf17(0x36a)](_0x46590f, 'skill', !![], _0x2baf17(0x182));
    }),
    (Window_SkillType[_0x3591d3(0x1ad)]['skillLearnSystemCommandName'] = function () {
        const _0x3dbb8e = _0x3591d3;
        let _0x206639 = TextManager[_0x3dbb8e(0x1d6)];
        if (_0x206639[_0x3dbb8e(0x161)](/\\I\[(\d+)\]/i)) return _0x206639;
        if (!Imported[_0x3dbb8e(0x195)]) return _0x206639;
        if (this[_0x3dbb8e(0x13a)]() === _0x3dbb8e(0x345)) return _0x206639;
        const _0x7633af = TextManager['skillLearnIcon'];
        return '\x5cI[%1]%2'[_0x3dbb8e(0x242)](_0x7633af, _0x206639);
    }),
    (VisuMZ[_0x3591d3(0xfd)]['Window_SkillStatus_refresh'] =
        Window_SkillStatus[_0x3591d3(0x1ad)]['refresh']),
    (Window_SkillStatus[_0x3591d3(0x1ad)][_0x3591d3(0x2f6)] = function () {
        const _0x1b07bc = _0x3591d3;
        this[_0x1b07bc(0xd8)]();
        if (this[_0x1b07bc(0x202)]())
            'KcBfd' !== _0x1b07bc(0x104) ? this[_0x1b07bc(0x179)]() : this[_0x1b07bc(0x1b7)]();
        else {
            if (_0x1b07bc(0x253) === 'uMWXa')
                VisuMZ[_0x1b07bc(0xfd)][_0x1b07bc(0xd5)][_0x1b07bc(0x1d1)](this);
            else {
                const _0x2ff268 = _0x33436a['_scene'];
                if (!_0x2ff268) return;
                const _0x24b52e = _0x2ff268[_0x1b07bc(0x17d)];
                if (_0x24b52e) _0x24b52e[_0x1b07bc(0x2f6)]();
            }
        }
    }),
    (Window_SkillStatus['prototype'][_0x3591d3(0x202)] = function () {
        const _0xa054d5 = _0x3591d3,
            _0x5c1159 = SceneManager['_scene'];
        if (!_0x5c1159) return ![];
        const _0x19a846 = _0x5c1159['_itemWindow'];
        if (!_0x19a846) return ![];
        return _0x19a846[_0xa054d5(0x202)] && _0x19a846[_0xa054d5(0x202)]();
    }),
    (Window_SkillStatus[_0x3591d3(0x1ad)][_0x3591d3(0x1b7)] = function () {
        const _0x4a6677 = _0x3591d3;
        if (!this[_0x4a6677(0x354)]) return;
        Window_StatusBase[_0x4a6677(0x1ad)][_0x4a6677(0x2f6)][_0x4a6677(0x1d1)](this);
        if (VisuMZ['SkillLearnSystem']['Settings'][_0x4a6677(0x374)]['StatusWindowDrawJS']) {
            VisuMZ[_0x4a6677(0xfd)][_0x4a6677(0x207)][_0x4a6677(0x374)][_0x4a6677(0x2a1)]['call'](
                this
            );
            return;
        }
        const _0x2ad3fd = this[_0x4a6677(0x24a)]() / 0x2,
            _0x463eff = this[_0x4a6677(0x156)],
            _0x46f22b = _0x463eff / 0x2 - this[_0x4a6677(0x2b3)]() * 1.5;
        (this[_0x4a6677(0xc4)](this['_actor'], _0x2ad3fd + 0x1, 0x0, 0x90, _0x463eff),
            this[_0x4a6677(0x29c)](this['_actor'], _0x2ad3fd + 0xb4, _0x46f22b));
        let _0x34030a = this[_0x4a6677(0x24a)]() / 0x2 + 0xb4 + 0xb4 + 0xb4,
            _0x3db2fb = this[_0x4a6677(0x1f3)] - _0x34030a - 0x2;
        if (_0x3db2fb < 0x12c) return;
        const _0x21cf91 = this[_0x4a6677(0x18c)](),
            _0x3a77f7 = Math['floor'](this[_0x4a6677(0x156)] / this[_0x4a6677(0x2b3)]()),
            _0x3701c1 = Math[_0x4a6677(0xe6)](_0x21cf91[_0x4a6677(0x1a3)] / _0x3a77f7);
        let _0x4ef865 = _0x34030a,
            _0x3a277e = Math[_0x4a6677(0x356)](
                Math[_0x4a6677(0xc7)](
                    (this['innerHeight'] -
                        this[_0x4a6677(0x2b3)]() *
                            Math[_0x4a6677(0xe6)](_0x21cf91[_0x4a6677(0x1a3)] / _0x3701c1)) /
                        0x2
                ),
                0x0
            );
        const _0x34d463 = _0x3a277e;
        let _0x53dad8 =
            (this['innerWidth'] - _0x4ef865 - this['itemPadding']() * 0x2 * _0x3701c1) / _0x3701c1;
        _0x3701c1 === 0x1 &&
            ((_0x53dad8 = Math[_0x4a6677(0x15f)](ImageManager[_0x4a6677(0x2b6)], _0x53dad8)),
            (_0x4ef865 += Math[_0x4a6677(0xc7)](
                (this[_0x4a6677(0x1f3)] - _0x4ef865 - this['itemPadding']() * 0x2 - _0x53dad8) / 0x2
            )));
        for (const _0x3e7203 of _0x21cf91) {
            if (_0x4a6677(0x22c) !== _0x4a6677(0x348)) {
                switch (_0x3e7203) {
                    case 'AP':
                        this[_0x4a6677(0x1d9)](
                            this[_0x4a6677(0x354)],
                            this[_0x4a6677(0x354)]['currentClass']()['id'],
                            _0x4ef865,
                            _0x3a277e,
                            _0x53dad8,
                            _0x4a6677(0x367)
                        );
                        break;
                    case 'CP':
                        Imported[_0x4a6677(0x2e0)] &&
                            (_0x4a6677(0x243) !== _0x4a6677(0x24f)
                                ? this[_0x4a6677(0xe0)](
                                      this[_0x4a6677(0x354)],
                                      this[_0x4a6677(0x354)][_0x4a6677(0x22a)]()['id'],
                                      _0x4ef865,
                                      _0x3a277e,
                                      _0x53dad8,
                                      _0x4a6677(0x367)
                                  )
                                : (_0x6d5ea5['id'] = _0x563a98(_0x3e216c)));
                        break;
                    case 'JP':
                        Imported[_0x4a6677(0x2e0)] &&
                            this[_0x4a6677(0xfb)](
                                this['_actor'],
                                this['_actor'][_0x4a6677(0x22a)]()['id'],
                                _0x4ef865,
                                _0x3a277e,
                                _0x53dad8,
                                _0x4a6677(0x367)
                            );
                        break;
                    case 'SP':
                        this['drawActorSkillPoints'](
                            this[_0x4a6677(0x354)],
                            this[_0x4a6677(0x354)][_0x4a6677(0x22a)]()['id'],
                            _0x4ef865,
                            _0x3a277e,
                            _0x53dad8,
                            _0x4a6677(0x367)
                        );
                        break;
                    case 'Gold':
                        this[_0x4a6677(0x1aa)](
                            $gameParty['gold'](),
                            TextManager[_0x4a6677(0x25d)],
                            _0x4ef865,
                            _0x3a277e,
                            _0x53dad8
                        );
                        break;
                    default:
                        continue;
                }
                ((_0x3a277e += this[_0x4a6677(0x2b3)]()),
                    _0x3a277e + this[_0x4a6677(0x2b3)]() > this[_0x4a6677(0x156)] &&
                        ((_0x3a277e = _0x34d463),
                        (_0x4ef865 += _0x53dad8 + this['itemPadding']() * 0x2)));
            } else _0x29b848[_0x4a6677(0x2b5)](_0x5d540a);
        }
    }),
    (Window_SkillStatus[_0x3591d3(0x1ad)][_0x3591d3(0x18c)] = function () {
        const _0x51a333 = _0x3591d3,
            _0x1068c2 = JsonEx[_0x51a333(0x26e)](
                VisuMZ[_0x51a333(0xfd)][_0x51a333(0x207)]['General'][_0x51a333(0x18d)]
            );
        if (!Imported[_0x51a333(0x2e0)]) {
            if (_0x51a333(0x211) === _0x51a333(0x211))
                (_0x1068c2[_0x51a333(0x14a)]('CP'), _0x1068c2[_0x51a333(0x14a)]('JP'));
            else {
                if (_0x5d4141['isPlaytest']()) _0x38b10c[_0x51a333(0x1a7)](_0x382822);
                return 0x0;
            }
        }
        return _0x1068c2[_0x51a333(0x14a)]('Item')
            [_0x51a333(0x14a)](_0x51a333(0x2a3))
            [_0x51a333(0x14a)](_0x51a333(0x178));
    }),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x202)] = function () {
        const _0xb4b48b = _0x3591d3;
        return this[_0xb4b48b(0xb8)] === 'skillLearn';
    }),
    (VisuMZ['SkillLearnSystem'][_0x3591d3(0x18a)] =
        Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x150)]),
    (Window_SkillList['prototype'][_0x3591d3(0x150)] = function (_0x535809) {
        const _0x581b66 = _0x3591d3,
            _0x578133 = this[_0x581b66(0x202)]();
        VisuMZ['SkillLearnSystem'][_0x581b66(0x18a)]['call'](this, _0x535809);
        if (_0x578133 !== this[_0x581b66(0x202)]()) {
            if (_0x581b66(0x34f) !== _0x581b66(0x1d5)) {
                const _0x17cc5b = SceneManager[_0x581b66(0x31a)];
                if (!_0x17cc5b) return;
                const _0x354cd7 = _0x17cc5b[_0x581b66(0x17d)];
                if (_0x354cd7) _0x354cd7[_0x581b66(0x2f6)]();
            } else _0x2a2818[_0x581b66(0xfd)]['JS'][_0x4c0cfa]['call'](this, this, _0x18add6);
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x1c5)] =
        Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0xdb)]),
    (Window_SkillList[_0x3591d3(0x1ad)]['maxCols'] = function () {
        const _0x2ee337 = _0x3591d3;
        if (this[_0x2ee337(0x202)]()) return 0x1;
        else {
            if (_0x2ee337(0x172) === _0x2ee337(0xf4)) _0x3d3282 = _0x8771fe(_0x260d65);
            else return VisuMZ[_0x2ee337(0xfd)][_0x2ee337(0x1c5)][_0x2ee337(0x1d1)](this);
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x24c)] = Window_SkillList['prototype'][_0x3591d3(0x221)]),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x221)] = function () {
        const _0x243d7f = _0x3591d3;
        if (this[_0x243d7f(0x354)] && this[_0x243d7f(0x202)]()) this[_0x243d7f(0x184)]();
        else {
            if (_0x243d7f(0x232) !== _0x243d7f(0x232)) return _0x30377e(_0x1009dc['$1']);
            else VisuMZ[_0x243d7f(0xfd)][_0x243d7f(0x24c)][_0x243d7f(0x1d1)](this);
        }
    }),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x184)] = function () {
        const _0xad35fe = _0x3591d3,
            _0x20aee5 = DataManager[_0xad35fe(0x329)](this['_actor'][_0xad35fe(0x22a)]()['id']);
        this[_0xad35fe(0x12d)] = _0x20aee5['map'](_0x39e687 => $dataSkills[_0x39e687])[
            _0xad35fe(0x149)
        ](_0x3ddab6 => this[_0xad35fe(0x292)](_0x3ddab6));
        if (Imported[_0xad35fe(0x1b3)]) {
            if (_0xad35fe(0x278) === _0xad35fe(0xe4)) return _0x2211ef[_0xad35fe(0x21f)];
            else this[_0xad35fe(0x1b5)]();
        }
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x34d)] =
        Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x292)]),
    (Window_SkillList['prototype'][_0x3591d3(0x292)] = function (_0x3e1b76) {
        const _0x3ce290 = _0x3591d3;
        if (this['isSkillLearnMode']()) return this['skillLearnIncludes'](_0x3e1b76);
        else {
            if ('zDWFb' !== 'HawFb')
                return VisuMZ[_0x3ce290(0xfd)][_0x3ce290(0x34d)][_0x3ce290(0x1d1)](this, _0x3e1b76);
            else _0x52d176 += _0x28d8aa[_0x3ce290(0xc7)]((_0x1c6fc6 - _0x150207) / 0x2);
        }
    }),
    (Window_SkillList[_0x3591d3(0x1ad)]['skillLearnIncludes'] = function (_0x5ad047) {
        const _0x4ed10b = _0x3591d3;
        if (!_0x5ad047) return ![];
        if (_0x5ad047[_0x4ed10b(0x210)][_0x4ed10b(0x1a3)] <= 0x0) return ![];
        if (_0x5ad047['name']['match'](/-----/i)) return ![];
        const _0x534fea = VisuMZ['SkillLearnSystem'][_0x4ed10b(0x315)](_0x5ad047, _0x4ed10b(0x173));
        if (VisuMZ[_0x4ed10b(0xfd)]['JS'][_0x534fea]) {
            if (_0x4ed10b(0x286) !== _0x4ed10b(0xa0)) {
                if (
                    !VisuMZ[_0x4ed10b(0xfd)]['JS'][_0x534fea][_0x4ed10b(0x1d1)](
                        this,
                        this['_actor'],
                        _0x5ad047
                    )
                )
                    return ![];
            } else {
                if (_0x514108[_0x4ed10b(0x161)](/GOLD:[ ](\d+)/gi))
                    return _0x168ad4(_0x14677f['$1']);
            }
        }
        const _0x1746be = VisuMZ[_0x4ed10b(0xfd)][_0x4ed10b(0x376)],
            _0x480b6e = _0x5ad047[_0x4ed10b(0x148)];
        if (_0x480b6e['match'](_0x1746be[_0x4ed10b(0x265)])) {
            const _0x3156bb = Number(RegExp['$1']);
            if (_0x3156bb > this[_0x4ed10b(0x354)][_0x4ed10b(0xee)]) return ![];
        }
        if (_0x480b6e[_0x4ed10b(0x161)](_0x1746be[_0x4ed10b(0x2fe)])) {
            if ('KqBuM' === 'JptYR') return ![];
            else {
                const _0x1f7b9f = String(RegExp['$1'])
                    ['split'](',')
                    [_0x4ed10b(0x1cc)](_0x4ab77a => _0x4ab77a[_0x4ed10b(0x25a)]());
                for (const _0x44e115 of _0x1f7b9f) {
                    let _0x3c9c7c = 0x0;
                    const _0x40504d = /^\d+$/[_0x4ed10b(0xa5)](_0x44e115);
                    _0x40504d
                        ? (_0x3c9c7c = Number(_0x44e115))
                        : (_0x3c9c7c = DataManager[_0x4ed10b(0x1a9)](_0x44e115));
                    if (!this[_0x4ed10b(0x354)][_0x4ed10b(0x28d)](_0x3c9c7c)) return ![];
                }
            }
        }
        if (_0x480b6e[_0x4ed10b(0x161)](_0x1746be[_0x4ed10b(0x228)])) {
            const _0x47ca42 = String(RegExp['$1'])
                ['split'](',')
                ['map'](_0x2fb6f5 => _0x2fb6f5[_0x4ed10b(0x25a)]());
            let _0xa90c6 = ![];
            for (const _0x45b864 of _0x47ca42) {
                let _0x3a8982 = 0x0;
                const _0x2d0241 = /^\d+$/[_0x4ed10b(0xa5)](_0x45b864);
                _0x2d0241
                    ? _0x4ed10b(0x115) === 'cssTr'
                        ? (_0x3a8982 = Number(_0x45b864))
                        : this[_0x4ed10b(0xe0)](
                              this[_0x4ed10b(0x354)],
                              this[_0x4ed10b(0x354)][_0x4ed10b(0x22a)]()['id'],
                              _0x3bb3a2,
                              _0x58bc23,
                              _0x37a9fd,
                              _0x4ed10b(0x367)
                          )
                    : (_0x3a8982 = DataManager['getSkillIdWithName'](_0x45b864));
                if (this[_0x4ed10b(0x354)]['isLearnedSkill'](_0x3a8982)) {
                    _0xa90c6 = !![];
                    break;
                }
            }
            if (!_0xa90c6) return ![];
        }
        if (_0x480b6e[_0x4ed10b(0x161)](_0x1746be['LearnShowSwitchesAll'])) {
            if (_0x4ed10b(0x363) === _0x4ed10b(0x363)) {
                const _0x1caca8 = String(RegExp['$1'])
                    [_0x4ed10b(0x168)](',')
                    [_0x4ed10b(0x1cc)](_0x1cee9a => Number(_0x1cee9a));
                for (const _0x41be1c of _0x1caca8) {
                    if ('Uzrpk' !== _0x4ed10b(0x153))
                        _0x576d9c = _0x1db865 || this['currentClass']()['id'];
                    else {
                        if (!$gameSwitches['value'](_0x41be1c)) return ![];
                    }
                }
            } else {
                if (!_0xe06baf) return ![];
                return (
                    _0x274129[_0x4ed10b(0x285)] !== _0x191ba6 &&
                    _0x27a321[_0x4ed10b(0x139)] !== _0x499ddd
                );
            }
        }
        if (_0x480b6e[_0x4ed10b(0x161)](_0x1746be[_0x4ed10b(0x280)])) {
            const _0x4a5eb0 = String(RegExp['$1'])
                [_0x4ed10b(0x168)](',')
                [_0x4ed10b(0x1cc)](_0x5025b5 => Number(_0x5025b5));
            let _0x5ac1e6 = ![];
            for (const _0x46e035 of _0x4a5eb0) {
                if ($gameSwitches[_0x4ed10b(0x108)](_0x46e035)) {
                    if (_0x4ed10b(0x2b9) === 'FAjRc') {
                        _0x5ac1e6 = !![];
                        break;
                    } else {
                        const _0x4c3664 = _0x395030['makeDeepCopy'](
                            _0xdabffe[_0x4ed10b(0xfd)][_0x4ed10b(0x207)]['General'][
                                'DisplayedCosts'
                            ]
                        );
                        return (_0x4c3664[_0x4ed10b(0x2b5)](_0x4ed10b(0x1e4)), _0x4c3664);
                    }
                }
            }
            if (!_0x5ac1e6) return ![];
        }
        return _0x5ad047;
    }),
    (VisuMZ[_0x3591d3(0xfd)]['Window_SkillList_isEnabled'] =
        Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0xe1)]),
    (Window_SkillList[_0x3591d3(0x1ad)]['isEnabled'] = function (_0x5b7312) {
        const _0x543e0c = _0x3591d3;
        return this[_0x543e0c(0x354)] && this['isSkillLearnMode']()
            ? this[_0x543e0c(0xd2)](_0x5b7312)
            : VisuMZ[_0x543e0c(0xfd)][_0x543e0c(0x333)]['call'](this, _0x5b7312);
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x263)] = Window_SkillList['prototype'][_0x3591d3(0x2f2)]),
    (Window_SkillList[_0x3591d3(0x1ad)]['drawItem'] = function (_0x90cdde) {
        const _0x42fe03 = _0x3591d3;
        ((this['_skillLearnSystem_drawItemMode'] = this[_0x42fe03(0x202)]()),
            VisuMZ['SkillLearnSystem'][_0x42fe03(0x263)][_0x42fe03(0x1d1)](this, _0x90cdde),
            (this[_0x42fe03(0x276)] = ![]));
    }),
    (Window_SkillList[_0x3591d3(0x1ad)]['isSkillLearnEnabled'] = function (_0x53c010) {
        const _0x27299e = _0x3591d3;
        if (!_0x53c010) return ![];
        if (_0x53c010[_0x27299e(0x210)][_0x27299e(0x1a3)] <= 0x0) return ![];
        if (_0x53c010['name']['match'](/-----/i)) return ![];
        if (this[_0x27299e(0x354)][_0x27299e(0x28d)](_0x53c010['id'])) return ![];
        if (Imported[_0x27299e(0x1b3)] && DataManager['isState'](_0x53c010)) {
            if (this[_0x27299e(0x354)][_0x27299e(0xff)](_0x53c010)) return ![];
        }
        if (this[_0x27299e(0x276)]) {
            if (!this[_0x27299e(0x354)][_0x27299e(0x35f)](_0x53c010)) return ![];
            return this[_0x27299e(0x354)]['canPayForSkillLearnSystem'](_0x53c010);
        }
        return !![];
    }),
    (VisuMZ[_0x3591d3(0xfd)][_0x3591d3(0x1ce)] =
        Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x288)]),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x288)] = function (
        _0x2ad8b3,
        _0x1193ee,
        _0x2e5f80,
        _0x27e3c2
    ) {
        const _0x4c40df = _0x3591d3;
        if (this[_0x4c40df(0x202)]()) {
            if (this[_0x4c40df(0xa6)](_0x2ad8b3)) {
                if (_0x4c40df(0x269) !== _0x4c40df(0x269)) return _0x276eeb;
                else this['drawSkillLearnRequirements'](_0x2ad8b3, _0x1193ee, _0x2e5f80, _0x27e3c2);
            } else this[_0x4c40df(0x266)](_0x2ad8b3, _0x1193ee, _0x2e5f80, _0x27e3c2);
        } else
            VisuMZ[_0x4c40df(0xfd)][_0x4c40df(0x1ce)][_0x4c40df(0x1d1)](
                this,
                _0x2ad8b3,
                _0x1193ee,
                _0x2e5f80,
                _0x27e3c2
            );
    }),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0xa6)] = function (_0x448ea0) {
        const _0x57ad3e = _0x3591d3;
        return this[_0x57ad3e(0x354)] && !this[_0x57ad3e(0x354)][_0x57ad3e(0x35f)](_0x448ea0);
    }),
    (Window_SkillList[_0x3591d3(0x1ad)]['drawSkillLearnRequirements'] = function (
        _0xcaa595,
        _0x59de88,
        _0x1ffdc0,
        _0x1ec946
    ) {
        const _0x117d20 = _0x3591d3,
            _0x3521a0 = this['getSkillLearnRequirementText'](_0xcaa595),
            _0x3fcb75 = this[_0x117d20(0x365)](_0x3521a0)[_0x117d20(0x347)];
        ((_0x59de88 += _0x1ec946 - _0x3fcb75),
            this[_0x117d20(0x1de)](_0x3521a0, _0x59de88, _0x1ffdc0));
    }),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x35c)] = function (_0x70b608) {
        const _0x4bb7ac = _0x3591d3,
            _0x5d82ed = VisuMZ[_0x4bb7ac(0xfd)][_0x4bb7ac(0x207)]['General'],
            _0x1f61d2 = TextManager[_0x4bb7ac(0x2f7)],
            _0x1eb842 = VisuMZ[_0x4bb7ac(0xfd)][_0x4bb7ac(0x376)],
            _0x3f2118 = _0x70b608['note'];
        let _0x27217a = '',
            _0x2039ba = '';
        const _0x5598df = [_0x4bb7ac(0x35e), 'SKILLS', 'SWITCHES', _0x4bb7ac(0x34c)];
        for (const _0x2c6010 of _0x5598df) {
            if (_0x4bb7ac(0x100) !== _0x4bb7ac(0x2e8))
                switch (_0x2c6010) {
                    case _0x4bb7ac(0x35e):
                        if (_0x3f2118['match'](_0x1eb842[_0x4bb7ac(0xe9)])) {
                            const _0x4fe6e5 = Number(RegExp['$1']);
                            _0x2039ba = TextManager[_0x4bb7ac(0x298)]['format'](
                                _0x4fe6e5,
                                TextManager[_0x4bb7ac(0xee)],
                                TextManager[_0x4bb7ac(0x1c2)]
                            );
                            if (_0x2039ba['length'] > 0x0) {
                                if (_0x4bb7ac(0x26b) === _0x4bb7ac(0x26b)) {
                                    if (_0x27217a !== '')
                                        _0x4bb7ac(0x11d) !== _0x4bb7ac(0x212)
                                            ? (_0x27217a = _0x1f61d2['format'](
                                                  _0x27217a,
                                                  _0x2039ba
                                              ))
                                            : this[_0x4bb7ac(0x163)](-_0x36f0aa, _0x412e15);
                                    else {
                                        if (_0x4bb7ac(0x34b) === _0x4bb7ac(0x34b))
                                            _0x27217a = _0x2039ba;
                                        else {
                                            const _0x442b39 = _0x30f802(_0x558132['$1']),
                                                _0x2361a2 = {
                                                    id: 0x0,
                                                    quantity: _0x2f909b(_0x497f9f['$2']),
                                                },
                                                _0x3bba8b = /^\d+$/[_0x4bb7ac(0xa5)](_0x442b39);
                                            (_0x3bba8b
                                                ? (_0x2361a2['id'] = _0x2eb36a(_0x442b39))
                                                : (_0x2361a2['id'] =
                                                      _0x47c766['getItemIdWithName'](_0x442b39)),
                                                _0x2361a2['id'] > 0x0 &&
                                                    _0x1dd389[_0x4bb7ac(0x2b5)](_0x2361a2));
                                        }
                                    }
                                } else _0xa21365[_0x4bb7ac(0x1d2)](_0x54e776, _0x1a301b);
                            }
                        }
                        break;
                    case _0x4bb7ac(0x2d8):
                        if (_0x3f2118[_0x4bb7ac(0x161)](_0x1eb842[_0x4bb7ac(0x32e)])) {
                            if ('MDCMJ' !== _0x4bb7ac(0x27d))
                                return (
                                    (_0xc80618 = _0x25cb68[_0x4bb7ac(0x340)]),
                                    _0x329589[_0x4bb7ac(0x242)](
                                        _0x575e9e,
                                        _0x81cc22[_0x4bb7ac(0x23e)],
                                        _0x4bb7ac(0x35d)[_0x4bb7ac(0x242)](
                                            _0x33b23d[_0x4bb7ac(0x325)]
                                        ),
                                        _0x10739c[_0x4bb7ac(0x165)]
                                    )
                                );
                            else {
                                const _0x20c176 = String(RegExp['$1'])
                                    [_0x4bb7ac(0x168)](',')
                                    [_0x4bb7ac(0x1cc)](_0x56b3e3 => _0x56b3e3[_0x4bb7ac(0x25a)]());
                                for (const _0x33a93b of _0x20c176) {
                                    let _0x1bf4b6 = 0x0;
                                    const _0x11d34b = /^\d+$/[_0x4bb7ac(0xa5)](_0x33a93b);
                                    _0x11d34b
                                        ? (_0x1bf4b6 = Number(_0x33a93b))
                                        : (_0x1bf4b6 = DataManager[_0x4bb7ac(0x1a9)](_0x33a93b));
                                    if ($dataSkills[_0x1bf4b6]) {
                                        if (_0x4bb7ac(0x25c) !== _0x4bb7ac(0x25c)) {
                                            if (!this[_0x4bb7ac(0x2a9)]) return;
                                            (this[_0x4bb7ac(0x142)](this[_0x4bb7ac(0x2a9)]),
                                                this[_0x4bb7ac(0x2a9)][_0x4bb7ac(0x23d)](),
                                                (this['_skillLearnAnimationSprite'] = _0x3e7344));
                                        } else {
                                            const _0x1b6174 = $dataSkills[_0x1bf4b6];
                                            ((_0x2039ba = TextManager['skillLearnReqSkillFmt'][
                                                'format'
                                            ](
                                                _0x4bb7ac(0x35d)[_0x4bb7ac(0x242)](
                                                    _0x1b6174[_0x4bb7ac(0x2b0)]
                                                ),
                                                _0x1b6174['name']
                                            )),
                                                _0x2039ba[_0x4bb7ac(0x1a3)] > 0x0 &&
                                                    (_0x27217a !== ''
                                                        ? (_0x27217a = _0x1f61d2[_0x4bb7ac(0x242)](
                                                              _0x27217a,
                                                              _0x2039ba
                                                          ))
                                                        : (_0x27217a = _0x2039ba)));
                                        }
                                    }
                                }
                            }
                        }
                        if (_0x3f2118[_0x4bb7ac(0x161)](_0x1eb842[_0x4bb7ac(0xc3)])) {
                            const _0x280f1a = String(RegExp['$1'])
                                [_0x4bb7ac(0x168)](',')
                                [_0x4bb7ac(0x1cc)](_0x3c77ae => _0x3c77ae['trim']());
                            for (const _0x17bc3f of _0x280f1a) {
                                let _0x4b75e8 = 0x0;
                                const _0x2c594c = /^\d+$/['test'](_0x17bc3f);
                                _0x2c594c
                                    ? (_0x4b75e8 = Number(_0x17bc3f))
                                    : (_0x4b75e8 = DataManager['getSkillIdWithName'](_0x17bc3f));
                                if ($dataSkills[_0x4b75e8]) {
                                    if (_0x4bb7ac(0x273) === _0x4bb7ac(0x273)) {
                                        const _0x50b23b = $dataSkills[_0x4b75e8];
                                        _0x2039ba = TextManager[_0x4bb7ac(0x1d4)][_0x4bb7ac(0x242)](
                                            _0x4bb7ac(0x35d)[_0x4bb7ac(0x242)](
                                                _0x50b23b[_0x4bb7ac(0x2b0)]
                                            ),
                                            _0x50b23b[_0x4bb7ac(0x210)]
                                        );
                                        if (_0x2039ba['length'] > 0x0) {
                                            if (_0x4bb7ac(0x287) === _0x4bb7ac(0x287)) {
                                                if (_0x27217a !== '')
                                                    _0x27217a = _0x1f61d2['format'](
                                                        _0x27217a,
                                                        _0x2039ba
                                                    );
                                                else {
                                                    if (_0x4bb7ac(0x2c2) !== _0x4bb7ac(0x2c2)) {
                                                        _0x4415df[_0x4bb7ac(0xfd)][
                                                            _0x4bb7ac(0x207)
                                                        ][_0x4bb7ac(0x374)][_0x4bb7ac(0x2a1)][
                                                            'call'
                                                        ](this);
                                                        return;
                                                    } else _0x27217a = _0x2039ba;
                                                }
                                            } else {
                                                if (!_0xd41142[_0x4bb7ac(0x2d1)]()) return;
                                                if (!this[_0x4bb7ac(0x354)]) return;
                                                let _0x4016ac = this[_0x4bb7ac(0x2bb)]();
                                                const _0x521fab =
                                                    this[_0x4bb7ac(0x354)]['skillTypes']()[0x0];
                                                this[_0x4bb7ac(0x36a)](
                                                    _0x4016ac,
                                                    _0x4bb7ac(0x2d9),
                                                    !![],
                                                    _0x4bb7ac(0x182)
                                                );
                                            }
                                        }
                                    } else _0x2281b2['id'] = _0x22380b(_0x57b3c5);
                                }
                            }
                        }
                        break;
                    case _0x4bb7ac(0x2ea):
                        if (_0x3f2118[_0x4bb7ac(0x161)](_0x1eb842[_0x4bb7ac(0xa4)])) {
                            if (_0x4bb7ac(0xab) === 'JylRE') {
                                const _0x389df1 = String(RegExp['$1'])
                                    ['split'](',')
                                    [_0x4bb7ac(0x1cc)](_0x50b51a => _0x50b51a[_0x4bb7ac(0x25a)]());
                                for (const _0x59c060 of _0x389df1) {
                                    if ($dataSystem[_0x4bb7ac(0x29d)][_0x59c060]) {
                                        _0x2039ba = TextManager[_0x4bb7ac(0x24d)][_0x4bb7ac(0x242)](
                                            $dataSystem['switches'][_0x59c060] || ''
                                        );
                                        if (_0x2039ba[_0x4bb7ac(0x1a3)] > 0x0) {
                                            if (_0x4bb7ac(0x103) === 'cFnfb') {
                                                if (_0x27217a !== '')
                                                    _0x27217a = _0x1f61d2['format'](
                                                        _0x27217a,
                                                        _0x2039ba
                                                    );
                                                else {
                                                    if (_0x4bb7ac(0x171) === _0x4bb7ac(0x1c7)) {
                                                        const _0x37570d = _0x5ef6f7(
                                                                _0x6d3084['$1']
                                                            ),
                                                            _0x2bf6ed = {
                                                                id: 0x0,
                                                                quantity: _0x3aadc2(
                                                                    _0x10f9d1['$2']
                                                                ),
                                                            },
                                                            _0x53cd52 = /^\d+$/[_0x4bb7ac(0xa5)](
                                                                _0x37570d
                                                            );
                                                        (_0x53cd52
                                                            ? (_0x2bf6ed['id'] =
                                                                  _0x3c39bc(_0x37570d))
                                                            : (_0x2bf6ed['id'] =
                                                                  _0x56f24e['getWeaponIdWithName'](
                                                                      _0x37570d
                                                                  )),
                                                            _0x2bf6ed['id'] > 0x0 &&
                                                                _0x5cf77c['push'](_0x2bf6ed));
                                                    } else _0x27217a = _0x2039ba;
                                                }
                                            } else
                                                return this[_0x4bb7ac(0x2cd)]()[_0x4bb7ac(0x2f1)](
                                                    (_0x105b1b, _0x5084e4) =>
                                                        _0x105b1b + _0x5084e4[_0x4bb7ac(0x15d)](),
                                                    0x0
                                                );
                                        }
                                    }
                                }
                            } else _0x2a21ac = _0x586788['getSkillIdWithName'](_0x3f2a82);
                        }
                        if (_0x3f2118[_0x4bb7ac(0x161)](_0x1eb842['LearnReqSwitchesAny'])) {
                            if (_0x4bb7ac(0x26f) !== _0x4bb7ac(0x26f))
                                return this[_0x4bb7ac(0x1bc)]()[_0x4bb7ac(0x2f1)](
                                    (_0x23ab89, _0x136ddf) => {
                                        const _0x2fcbd4 = _0x4bb7ac;
                                        return _0x136ddf &&
                                            _0x136ddf[_0x2fcbd4(0x148)][_0x2fcbd4(0x161)](
                                                _0x4f2466['SkillLearnSystem']['RegExp'][
                                                    _0x2fcbd4(0x28c)
                                                ]
                                            )
                                            ? _0x23ab89 * (_0x44c548(_0x36b8f3['$1']) * 0.01)
                                            : _0x23ab89;
                                    },
                                    0x1
                                );
                            else {
                                const _0x484639 = String(RegExp['$1'])
                                    [_0x4bb7ac(0x168)](',')
                                    ['map'](_0x1855cd => _0x1855cd['trim']());
                                for (const _0x15db00 of _0x484639) {
                                    'hFLyr' !== _0x4bb7ac(0xde)
                                        ? $dataSystem['switches'][_0x15db00] &&
                                          ((_0x2039ba = TextManager[_0x4bb7ac(0x24d)][
                                              _0x4bb7ac(0x242)
                                          ]($dataSystem['switches'][_0x15db00] || '')),
                                          _0x2039ba[_0x4bb7ac(0x1a3)] > 0x0 &&
                                              (_0x4bb7ac(0xc1) === 'Meslh'
                                                  ? _0x27217a !== ''
                                                      ? _0x4bb7ac(0x2f3) !== _0x4bb7ac(0xdc)
                                                          ? (_0x27217a = _0x1f61d2[
                                                                _0x4bb7ac(0x242)
                                                            ](_0x27217a, _0x2039ba))
                                                          : (_0x1f5ce7 = _0xab83da(_0x141872))
                                                      : _0x4bb7ac(0x233) === _0x4bb7ac(0x233)
                                                        ? (_0x27217a = _0x2039ba)
                                                        : (_0x58fdfc = _0x1d4c95['format'](
                                                              _0x1f88b8,
                                                              _0x4bf455
                                                          ))
                                                  : (this['_skillLearnAnimationIDs'] = _0x264d25[
                                                        '$1'
                                                    ]
                                                        ['split'](',')
                                                        ['map'](_0x198e4a =>
                                                            _0x41b3bd(_0x198e4a)
                                                        ))))
                                        : _0x11b89e[_0x4bb7ac(0x1ca)](_0x233dc5, _0x28b18b);
                                }
                            }
                        }
                        break;
                    case _0x4bb7ac(0x34c):
                        const _0x374ee4 = VisuMZ['SkillLearnSystem'][_0x4bb7ac(0x315)](
                            _0x70b608,
                            _0x4bb7ac(0x20b)
                        );
                        if (VisuMZ[_0x4bb7ac(0xfd)]['JS'][_0x374ee4]) {
                            if (_0x4bb7ac(0x21c) !== _0x4bb7ac(0x22e))
                                ((_0x2039ba = VisuMZ[_0x4bb7ac(0xfd)]['JS'][_0x374ee4]['call'](
                                    this,
                                    this[_0x4bb7ac(0x354)],
                                    _0x70b608
                                )),
                                    _0x2039ba[_0x4bb7ac(0x1a3)] > 0x0 &&
                                        (_0x27217a !== ''
                                            ? _0x4bb7ac(0x20c) !== 'qXHIf'
                                                ? (_0x27217a = _0x1f61d2[_0x4bb7ac(0x242)](
                                                      _0x27217a,
                                                      _0x2039ba
                                                  ))
                                                : _0x1d4b6b[_0x4bb7ac(0xfd)][
                                                      'Scene_Skill_onItemOk'
                                                  ][_0x4bb7ac(0x1d1)](this)
                                            : (_0x27217a = _0x2039ba)));
                            else return ![];
                        }
                        break;
                }
            else _0x41f38b[_0x4bb7ac(0x1ad)][_0x4bb7ac(0x217)][_0x4bb7ac(0x1d1)](this);
        }
        return (
            (_0x27217a = TextManager['skillLearnReqHeaderFmt']['format'](_0x27217a)),
            _0x27217a[_0x4bb7ac(0x25a)]()
        );
    }),
    (Window_SkillList['prototype'][_0x3591d3(0x266)] = function (
        _0x2c9ddc,
        _0x377c3b,
        _0x589fec,
        _0x1be4e6
    ) {
        const _0x364e2e = _0x3591d3,
            _0x4017d2 = this['getSkillLearnCostText'](_0x2c9ddc),
            _0x5bd244 = this[_0x364e2e(0x365)](_0x4017d2)[_0x364e2e(0x347)];
        ((_0x377c3b += _0x1be4e6 - _0x5bd244), this['drawTextEx'](_0x4017d2, _0x377c3b, _0x589fec));
    }),
    (Window_SkillList[_0x3591d3(0x1ad)]['getSkillLearnCostText'] = function (_0xdc2279) {
        const _0x5563e2 = _0x3591d3;
        if (this['_actor'] && this[_0x5563e2(0x354)][_0x5563e2(0x28d)](_0xdc2279['id'])) {
            if (_0x5563e2(0x2cc) !== _0x5563e2(0x36f)) return TextManager[_0x5563e2(0x21f)];
            else _0x50cf88['id'] = _0x5b889f(_0x10c8a0);
        }
        const _0x5da847 = VisuMZ[_0x5563e2(0xfd)][_0x5563e2(0x207)][_0x5563e2(0x374)],
            _0x7d8a04 = TextManager[_0x5563e2(0x317)];
        let _0x65e637 = '';
        const _0x41b516 = JsonEx['makeDeepCopy'](_0x5da847[_0x5563e2(0x18d)]);
        _0x41b516[_0x5563e2(0x2b5)](_0x5563e2(0x1e4));
        for (const _0x3352fe of _0x41b516) {
            if (!_0x3352fe) continue;
            const _0xc668ad = this[_0x5563e2(0x248)](_0xdc2279, _0x3352fe)[_0x5563e2(0x25a)]();
            _0xc668ad['length'] > 0x0 &&
                (_0x65e637 !== ''
                    ? _0x5563e2(0x270) !== _0x5563e2(0x102)
                        ? (_0x65e637 = _0x7d8a04[_0x5563e2(0x242)](_0x65e637, _0xc668ad))
                        : (_0x4611c6 = _0x2472a2['filter'](_0x541b3f => _0x541b3f['isAlive']()))
                    : _0x5563e2(0x299) === _0x5563e2(0x299)
                      ? (_0x65e637 = _0xc668ad)
                      : this[_0x5563e2(0x33a)]());
        }
        return _0x65e637[_0x5563e2(0x25a)]();
    }),
    (Window_SkillList[_0x3591d3(0x1ad)][_0x3591d3(0x248)] = function (_0x530ed5, _0x355f02) {
        const _0x152e7c = _0x3591d3;
        let _0x391101 = 0x0,
            _0x5d9014 = '',
            _0x100675 = '';
        switch (_0x355f02[_0x152e7c(0xe5)]()[_0x152e7c(0x25a)]()) {
            case 'AP':
                _0x391101 = DataManager[_0x152e7c(0xbc)](_0x530ed5);
                if (_0x391101 > 0x0)
                    return (
                        (_0x5d9014 = TextManager[_0x152e7c(0x254)]),
                        _0x5d9014['format'](
                            _0x391101,
                            TextManager['abilityPointsAbbr'],
                            '\x5cI[%1]'['format'](ImageManager['abilityPointsIcon']),
                            TextManager[_0x152e7c(0x12f)]
                        )
                    );
                break;
            case 'SP':
                _0x391101 = DataManager[_0x152e7c(0xe8)](_0x530ed5);
                if (_0x391101 > 0x0)
                    return (
                        (_0x5d9014 = TextManager[_0x152e7c(0x340)]),
                        _0x5d9014[_0x152e7c(0x242)](
                            _0x391101,
                            TextManager['skillPointsAbbr'],
                            _0x152e7c(0x35d)[_0x152e7c(0x242)](ImageManager[_0x152e7c(0x325)]),
                            TextManager['skillPointsFull']
                        )
                    );
                break;
            case _0x152e7c(0x10a):
                ((_0x391101 = DataManager['getSkillLearnItemCost'](_0x530ed5)),
                    (_0x5d9014 = TextManager[_0x152e7c(0x300)]));
                for (const _0x65429a of _0x391101) {
                    if (!_0x65429a) continue;
                    const _0x7442d6 = $dataItems[_0x65429a['id']];
                    if (!_0x7442d6) continue;
                    const _0x1e5f4e = _0x5d9014[_0x152e7c(0x242)](
                        _0x65429a[_0x152e7c(0x2bc)],
                        '\x5cI[%1]'[_0x152e7c(0x242)](_0x7442d6['iconIndex']),
                        _0x7442d6[_0x152e7c(0x210)]
                    );
                    if (_0x100675 !== '') {
                        if ('lLOQP' !== 'lLOQP') {
                            if (this['_SkillLearnSystem_preventLevelUpGain']) return;
                            const _0x668511 =
                                _0xd3607c[_0x152e7c(0xfd)][_0x152e7c(0x207)][_0x152e7c(0x11a)];
                            let _0x1279ee = 0x0;
                            try {
                                _0x1279ee = _0x49a979(_0x668511['PerLevelUp']);
                            } catch (_0x5335b0) {
                                if (_0x5ad298['isPlaytest']())
                                    _0x48cb0a[_0x152e7c(0x1a7)](_0x5335b0);
                            }
                            this[_0x152e7c(0x1cb)](_0x1279ee, _0x3397f1);
                        } else
                            _0x100675 = TextManager[_0x152e7c(0x317)][_0x152e7c(0x242)](
                                _0x100675,
                                _0x1e5f4e
                            );
                    } else {
                        if (_0x152e7c(0x1ba) === _0x152e7c(0x1e7)) {
                            const _0x32160b =
                                _0x16589e[_0x152e7c(0xfd)][_0x152e7c(0x207)][_0x152e7c(0x11a)];
                            (_0x32160b[_0x152e7c(0x1fe)]
                                ? (_0xac4274 = 0x0)
                                : (_0x207cbc = _0x539d88 || this[_0x152e7c(0x22a)]()['id']),
                                (_0x15a979 += this['getSkillPoints'](_0x2df580)),
                                this[_0x152e7c(0x1d2)](_0x436486, _0x4843d1));
                        } else _0x100675 = _0x1e5f4e;
                    }
                }
                return _0x100675;
            case _0x152e7c(0x132):
                ((_0x391101 = DataManager[_0x152e7c(0xe2)](_0x530ed5)),
                    (_0x5d9014 = TextManager[_0x152e7c(0x32c)]));
                for (const _0x4a63fa of _0x391101) {
                    if (!_0x4a63fa) continue;
                    const _0x4ba48c = $dataWeapons[_0x4a63fa['id']];
                    if (!_0x4ba48c) continue;
                    const _0x20655b = _0x5d9014['format'](
                        _0x4a63fa['quantity'],
                        _0x152e7c(0x35d)[_0x152e7c(0x242)](_0x4ba48c[_0x152e7c(0x2b0)]),
                        _0x4ba48c[_0x152e7c(0x210)]
                    );
                    _0x100675 !== ''
                        ? (_0x100675 = TextManager[_0x152e7c(0x317)][_0x152e7c(0x242)](
                              _0x100675,
                              _0x20655b
                          ))
                        : (_0x100675 = _0x20655b);
                }
                return _0x100675;
            case _0x152e7c(0x1a5):
                ((_0x391101 = DataManager['getSkillLearnArmorCost'](_0x530ed5)),
                    (_0x5d9014 = TextManager[_0x152e7c(0x15a)]));
                for (const _0x3c48b3 of _0x391101) {
                    if (_0x152e7c(0x174) !== _0x152e7c(0x174)) {
                        const _0x1b32d5 = _0xcef0d2[_0x43e3c8];
                        ((_0x587df9 = _0x5c7ba1[_0x152e7c(0x1d4)]['format'](
                            _0x152e7c(0x35d)[_0x152e7c(0x242)](_0x1b32d5['iconIndex']),
                            _0x1b32d5['name']
                        )),
                            _0x28933d[_0x152e7c(0x1a3)] > 0x0 &&
                                (_0x13f664 !== ''
                                    ? (_0x5e5e0f = _0x4583ef[_0x152e7c(0x242)](
                                          _0x515cc0,
                                          _0x1b93f5
                                      ))
                                    : (_0x31be72 = _0x15901e)));
                    } else {
                        if (!_0x3c48b3) continue;
                        const _0x55925b = $dataArmors[_0x3c48b3['id']];
                        if (!_0x55925b) continue;
                        const _0x4c3da9 = _0x5d9014[_0x152e7c(0x242)](
                            _0x3c48b3[_0x152e7c(0x2bc)],
                            '\x5cI[%1]'[_0x152e7c(0x242)](_0x55925b[_0x152e7c(0x2b0)]),
                            _0x55925b[_0x152e7c(0x210)]
                        );
                        _0x100675 !== ''
                            ? (_0x100675 = TextManager[_0x152e7c(0x317)][_0x152e7c(0x242)](
                                  _0x100675,
                                  _0x4c3da9
                              ))
                            : (_0x100675 = _0x4c3da9);
                    }
                }
                return _0x100675;
            case 'GOLD':
                _0x391101 = DataManager[_0x152e7c(0x152)](_0x530ed5);
                if (_0x391101 > 0x0)
                    return (
                        (_0x5d9014 = TextManager['skillLearnGoldFmt']),
                        _0x5d9014[_0x152e7c(0x242)](
                            _0x391101,
                            Imported[_0x152e7c(0x208)]
                                ? _0x152e7c(0x35d)['format'](
                                      VisuMZ[_0x152e7c(0xc8)][_0x152e7c(0x207)][_0x152e7c(0x21d)][
                                          'GoldIcon'
                                      ]
                                  )
                                : TextManager[_0x152e7c(0x25d)],
                            TextManager[_0x152e7c(0x25d)]
                        )
                    );
                break;
            case _0x152e7c(0x34c):
                const _0x26ba5f = VisuMZ['SkillLearnSystem'][_0x152e7c(0x315)](
                    _0x530ed5,
                    _0x152e7c(0x183)
                );
                if (VisuMZ[_0x152e7c(0xfd)]['JS'][_0x26ba5f])
                    return VisuMZ[_0x152e7c(0xfd)]['JS'][_0x26ba5f][_0x152e7c(0x1d1)](
                        this,
                        this['_actor'],
                        _0x530ed5
                    );
                break;
            case 'CP':
                if (Imported['VisuMZ_2_ClassChangeSystem']) {
                    _0x391101 = DataManager['getSkillLearnClassPointCost'](_0x530ed5);
                    if (_0x391101 > 0x0)
                        return (
                            (_0x5d9014 = TextManager[_0x152e7c(0xf6)]),
                            _0x5d9014[_0x152e7c(0x242)](
                                _0x391101,
                                TextManager[_0x152e7c(0x13d)],
                                _0x152e7c(0x35d)[_0x152e7c(0x242)](ImageManager[_0x152e7c(0x359)]),
                                TextManager['classPointsFull']
                            )
                        );
                    break;
                }
            case 'JP':
                if (Imported[_0x152e7c(0x2e0)]) {
                    _0x391101 = DataManager[_0x152e7c(0x187)](_0x530ed5);
                    if (_0x391101 > 0x0) {
                        if (_0x152e7c(0x309) !== _0x152e7c(0x293))
                            return (
                                (_0x5d9014 = TextManager[_0x152e7c(0x2cf)]),
                                _0x5d9014['format'](
                                    _0x391101,
                                    TextManager[_0x152e7c(0x124)],
                                    _0x152e7c(0x35d)[_0x152e7c(0x242)](
                                        ImageManager[_0x152e7c(0x2a8)]
                                    ),
                                    TextManager[_0x152e7c(0x355)]
                                )
                            );
                        else _0x3214d9['id'] = _0x3ebc6e(_0xb86b6b);
                    }
                    break;
                }
        }
        return '';
    }),
    (Window_ActorCommand[_0x3591d3(0x1ad)][_0x3591d3(0x202)] = function () {
        return ![];
    }));
function Window_SkillLearnIngredients() {
    const _0x296835 = _0x3591d3;
    this[_0x296835(0xb4)](...arguments);
}
((Window_SkillLearnIngredients[_0x3591d3(0x1ad)] = Object[_0x3591d3(0xcd)](
    Window_Base[_0x3591d3(0x1ad)]
)),
    (Window_SkillLearnIngredients['prototype'][_0x3591d3(0x140)] = Window_SkillLearnIngredients),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0xb4)] = function (_0x110963) {
        const _0x1a65d9 = _0x3591d3;
        Window_Base[_0x1a65d9(0x1ad)][_0x1a65d9(0xb4)][_0x1a65d9(0x1d1)](this, _0x110963);
    }),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0x2f6)] = function () {
        const _0x39ea3f = _0x3591d3;
        (this[_0x39ea3f(0x1e8)]['clear'](), this[_0x39ea3f(0xd8)]());
        if (this[_0x39ea3f(0x1b8)]())
            _0x39ea3f(0x138) !== _0x39ea3f(0x138)
                ? (_0x372cf8 = _0x56a2cd)
                : this['drawRequirements']();
        else {
            if ('XAihp' === _0x39ea3f(0x1d3)) {
                if (
                    this['subject']()[_0x39ea3f(0x203)]() &&
                    _0xefa9d8[_0x39ea3f(0x161)](_0x761dfe['UserGainAbilityPoints'])
                ) {
                    const _0x2490b2 = _0xfa45d0(_0x2fb539['$1']);
                    this[_0x39ea3f(0x268)]()[_0x39ea3f(0x2b4)](_0x2490b2);
                } else this[_0x39ea3f(0x1f6)]();
                if (
                    _0x2aa0ed['isActor']() &&
                    _0x9c428c[_0x39ea3f(0x161)](_0x57cc3e[_0x39ea3f(0x2c8)])
                ) {
                    const _0x52d855 = _0x1d9f14(_0x1fe671['$1']);
                    _0x5bc8fd[_0x39ea3f(0x2b4)](_0x52d855);
                }
            } else this['drawIngredients']();
        }
    }),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0x18b)] = function (
        _0x8aca92,
        _0x434c0d,
        _0x472ed2,
        _0xb7df12
    ) {
        const _0x1719b0 = _0x3591d3,
            _0xba0abe = this[_0x1719b0(0x365)](_0x8aca92)[_0x1719b0(0x347)],
            _0x369339 = _0x434c0d + Math[_0x1719b0(0xc7)]((_0xb7df12 - _0xba0abe) / 0x2);
        this[_0x1719b0(0x1de)](_0x8aca92, _0x369339, _0x472ed2);
    }),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0x215)] = function (
        _0x9417c1,
        _0x2d2ca3,
        _0x3e4af9,
        _0x369605
    ) {
        const _0x334a51 = _0x3591d3,
            _0xc48823 = this[_0x334a51(0x365)](_0x9417c1)[_0x334a51(0x347)],
            _0x278224 = _0x2d2ca3 + Math[_0x334a51(0xc7)](_0x369605 - _0xc48823);
        this[_0x334a51(0x1de)](_0x9417c1, _0x278224, _0x3e4af9);
    }),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0x1b8)] = function () {
        const _0x51a9c9 = _0x3591d3,
            _0x59f7b0 = SceneManager[_0x51a9c9(0x31a)]['item'](),
            _0x4210c1 = SceneManager[_0x51a9c9(0x31a)][_0x51a9c9(0x216)]();
        return _0x4210c1 && !_0x4210c1[_0x51a9c9(0x35f)](_0x59f7b0);
    }),
    (Window_SkillLearnIngredients[_0x3591d3(0x1ad)][_0x3591d3(0x179)] = function () {
        const _0x32ee64 = _0x3591d3,
            _0x4e36bc = SceneManager['_scene'][_0x32ee64(0x284)](),
            _0x599f58 = VisuMZ[_0x32ee64(0xfd)]['RegExp'],
            _0x7ec193 = _0x4e36bc[_0x32ee64(0x148)],
            _0x4fdce3 = SceneManager['_scene'][_0x32ee64(0x216)](),
            _0x15d40c = this[_0x32ee64(0x2b3)](),
            _0x5778ce = TextManager['skillLearnReqMet'],
            _0x49d540 = TextManager[_0x32ee64(0xf0)];
        let _0x518493 = 0x0,
            _0x5da5aa = 0x0;
        const _0x2c2e0e = _0x32ee64(0x35d)['format'](_0x4e36bc[_0x32ee64(0x2b0)]),
            _0x1fdbbf = TextManager[_0x32ee64(0x1f5)][_0x32ee64(0x242)](
                _0x2c2e0e,
                _0x4e36bc[_0x32ee64(0x210)]
            );
        (this[_0x32ee64(0x18b)](_0x1fdbbf, _0x518493, _0x5da5aa, this['innerWidth']),
            (_0x5da5aa += Math['round'](_0x15d40c * 1.5)));
        let _0x44e940 = '';
        if (_0x7ec193[_0x32ee64(0x161)](_0x599f58['LearnReqLevel'])) {
            const _0x440413 = Number(RegExp['$1']),
                _0x26ee12 = TextManager['skillLearnReqListLevel'][_0x32ee64(0x242)](
                    _0x440413,
                    TextManager['level'],
                    TextManager[_0x32ee64(0x1c2)]
                ),
                _0x1aa7d9 = _0x4fdce3['level'] >= _0x440413 ? _0x5778ce : _0x49d540;
            _0x44e940 += _0x1aa7d9['format'](_0x26ee12) + '\x0a';
        }
        if (_0x7ec193[_0x32ee64(0x161)](_0x599f58[_0x32ee64(0x32e)])) {
            const _0x11fd2b = String(RegExp['$1'])
                [_0x32ee64(0x168)](',')
                [_0x32ee64(0x1cc)](_0x1da9e4 => _0x1da9e4[_0x32ee64(0x25a)]());
            for (const _0x498eb0 of _0x11fd2b) {
                if (_0x32ee64(0x159) !== _0x32ee64(0x14e)) {
                    let _0x4d19db = 0x0;
                    const _0xf4ee8d = /^\d+$/[_0x32ee64(0xa5)](_0x498eb0);
                    _0xf4ee8d
                        ? _0x32ee64(0xcb) !== _0x32ee64(0xcb)
                            ? (_0x4d0524 = _0x1f82af(_0x38650f[_0x32ee64(0x344)]))
                            : (_0x4d19db = Number(_0x498eb0))
                        : (_0x4d19db = DataManager[_0x32ee64(0x1a9)](_0x498eb0));
                    const _0x14aee7 = $dataSkills[_0x4d19db];
                    if (_0x14aee7) {
                        const _0xd6f2c6 = TextManager[_0x32ee64(0x2e5)]['format'](
                                '\x5cI[%1]'[_0x32ee64(0x242)](_0x14aee7[_0x32ee64(0x2b0)]),
                                _0x14aee7[_0x32ee64(0x210)]
                            ),
                            _0x39ac01 = _0x4fdce3[_0x32ee64(0x28d)](_0x4d19db)
                                ? _0x5778ce
                                : _0x49d540;
                        _0x44e940 += _0x39ac01['format'](_0xd6f2c6) + '\x0a';
                    }
                } else _0x3f77f9 = 0x0;
            }
        }
        if (_0x7ec193['match'](_0x599f58[_0x32ee64(0xc3)])) {
            const _0x2fd433 = String(RegExp['$1'])
                ['split'](',')
                [_0x32ee64(0x1cc)](_0x3237aa => _0x3237aa[_0x32ee64(0x25a)]());
            for (const _0x2d4b7b of _0x2fd433) {
                if (_0x32ee64(0x1c9) === _0x32ee64(0x1b6)) {
                    const _0x3ba159 = _0x48199f(_0x10ff28['$1']);
                    this['subject']()[_0x32ee64(0x2b4)](_0x3ba159);
                } else {
                    let _0x41fde3 = 0x0;
                    const _0x1c5e78 = /^\d+$/['test'](_0x2d4b7b);
                    _0x1c5e78
                        ? _0x32ee64(0xf3) === _0x32ee64(0x316)
                            ? (this[_0x32ee64(0x2dc)](),
                              this[_0x32ee64(0x226)](),
                              this['finishSkillLearnAnimation'](),
                              _0x148af[_0x32ee64(0xa8)](),
                              _0x3fc549[_0x32ee64(0xa8)]())
                            : (_0x41fde3 = Number(_0x2d4b7b))
                        : (_0x41fde3 = DataManager[_0x32ee64(0x1a9)](_0x2d4b7b));
                    const _0x59b2aa = $dataSkills[_0x41fde3];
                    if (_0x59b2aa) {
                        if (_0x32ee64(0x229) === _0x32ee64(0x229)) {
                            const _0x24d97a = TextManager[_0x32ee64(0x2e5)][_0x32ee64(0x242)](
                                    _0x32ee64(0x35d)['format'](_0x59b2aa['iconIndex']),
                                    _0x59b2aa[_0x32ee64(0x210)]
                                ),
                                _0xb2dff6 = _0x4fdce3['isLearnedSkill'](_0x41fde3)
                                    ? _0x5778ce
                                    : _0x49d540;
                            _0x44e940 += _0xb2dff6[_0x32ee64(0x242)](_0x24d97a) + '\x0a';
                        } else
                            ((this[_0x32ee64(0x276)] = this['isSkillLearnMode']()),
                                _0xaff95['SkillLearnSystem'][_0x32ee64(0x263)][_0x32ee64(0x1d1)](
                                    this,
                                    _0xea9c4b
                                ),
                                (this[_0x32ee64(0x276)] = ![]));
                    }
                }
            }
        }
        if (_0x7ec193[_0x32ee64(0x161)](_0x599f58['LearnReqSwitchesAll'])) {
            const _0xb41445 = String(RegExp['$1'])
                [_0x32ee64(0x168)](',')
                [_0x32ee64(0x1cc)](_0x757878 => Number(_0x757878));
            for (const _0x1b1f5d of _0xb41445) {
                const _0x1a60d4 = $dataSystem['switches'][_0x1b1f5d],
                    _0x46bb57 = $gameSwitches['value'](_0x1b1f5d) ? _0x5778ce : _0x49d540;
                _0x44e940 += _0x46bb57['format'](_0x1a60d4) + '\x0a';
            }
        }
        if (_0x7ec193['match'](_0x599f58[_0x32ee64(0x318)])) {
            const _0x2a6902 = String(RegExp['$1'])
                [_0x32ee64(0x168)](',')
                [_0x32ee64(0x1cc)](_0xb9177b => Number(_0xb9177b));
            for (const _0x4f2696 of _0x2a6902) {
                const _0x463a4f = $dataSystem[_0x32ee64(0x29d)][_0x4f2696],
                    _0x145ca0 = $gameSwitches[_0x32ee64(0x108)](_0x4f2696) ? _0x5778ce : _0x49d540;
                _0x44e940 += _0x145ca0[_0x32ee64(0x242)](_0x463a4f) + '\x0a';
            }
        }
        const _0x5f5580 = VisuMZ['SkillLearnSystem'][_0x32ee64(0x315)](_0x4e36bc, _0x32ee64(0x366));
        if (VisuMZ[_0x32ee64(0xfd)]['JS'][_0x5f5580]) {
            if ('OpEmI' !== 'OpEmI') _0x132a37[_0x32ee64(0x2b5)](_0x43102c);
            else {
                const _0x31cdeb = VisuMZ['SkillLearnSystem']['JS'][_0x5f5580]['call'](
                    this,
                    _0x4fdce3,
                    _0x4e36bc
                );
                _0x44e940 += _0x31cdeb + '\x0a';
            }
        }
        this[_0x32ee64(0x18b)](_0x44e940, _0x518493, _0x5da5aa, this[_0x32ee64(0x1f3)]);
    }),
    (Window_SkillLearnIngredients['prototype']['drawIngredients'] = function () {
        const _0x314383 = _0x3591d3,
            _0x3df946 = SceneManager[_0x314383(0x31a)]['item'](),
            _0x21374f = SceneManager[_0x314383(0x31a)][_0x314383(0x216)](),
            _0x45d1fd = this['getSkillLearnDisplayedCosts']();
        let _0x4171c9 = 0x0,
            _0x4fb27d = 0x0;
        const _0x2f42e6 = this[_0x314383(0x2b3)](),
            _0x28d3a1 = Math[_0x314383(0xc7)](this[_0x314383(0x1f3)] / 0x2),
            _0x1e12ab = Math[_0x314383(0xc7)](this[_0x314383(0x1f3)] / 0x4),
            _0x3d2d91 = 0x0,
            _0x533e6e = _0x28d3a1,
            _0x14acff = _0x28d3a1 + _0x1e12ab;
        let _0x30c418 = '\x5cI[%1]'[_0x314383(0x242)](_0x3df946[_0x314383(0x2b0)]),
            _0xb0f2a5 = _0x3df946['name'];
        Imported['VisuMZ_2_EquipPassiveSys'] &&
            DataManager[_0x314383(0x29b)](_0x3df946) &&
            ((_0x30c418 = _0x314383(0x35d)[_0x314383(0x242)](
                DataManager[_0x314383(0x297)](_0x3df946)
            )),
            (_0xb0f2a5 = DataManager['getEquipPassiveName'](_0x3df946)));
        let _0x3ad122 = TextManager[_0x314383(0x147)][_0x314383(0x242)](_0x30c418, _0xb0f2a5);
        (this[_0x314383(0x18b)](_0x3ad122, _0x4171c9, _0x4fb27d, this['innerWidth']),
            (_0x4fb27d += _0x2f42e6),
            this['drawTextExCenterAlign'](
                TextManager[_0x314383(0x30a)],
                _0x3d2d91,
                _0x4fb27d,
                _0x28d3a1
            ),
            this[_0x314383(0x18b)](TextManager[_0x314383(0x11f)], _0x533e6e, _0x4fb27d, _0x1e12ab),
            this[_0x314383(0x18b)](TextManager[_0x314383(0x227)], _0x14acff, _0x4fb27d, _0x1e12ab),
            (_0x4fb27d += _0x2f42e6));
        const _0x3cc2ef = _0x3d2d91 + this[_0x314383(0x201)]();
        for (const _0x418140 of _0x45d1fd) {
            if ('iDYqO' === 'IVeGw') _0x427eff = _0xf93d15;
            else {
                this['resetFontSettings']();
                let _0x1ea220 = '',
                    _0xa6cef5 = 0x0,
                    _0x8c7edf = 0x0,
                    _0x2dd925 = '';
                switch (_0x418140['toUpperCase']()[_0x314383(0x25a)]()) {
                    case 'AP':
                        _0xa6cef5 = DataManager[_0x314383(0xbc)](_0x3df946);
                        if (_0xa6cef5 <= 0x0) continue;
                        (this[_0x314383(0x2d3)](
                            _0xa6cef5,
                            _0x533e6e,
                            _0x4fb27d,
                            _0x1e12ab,
                            'right'
                        ),
                            (_0x1ea220 = _0x314383(0x1a4)[_0x314383(0x242)](
                                ImageManager['abilityPointsIcon'],
                                TextManager[_0x314383(0x12f)]
                            )),
                            this[_0x314383(0x1de)](_0x1ea220, _0x3cc2ef, _0x4fb27d),
                            (_0x8c7edf = _0x21374f[_0x314383(0x21e)]()),
                            this[_0x314383(0x2d3)](
                                _0x8c7edf,
                                _0x14acff,
                                _0x4fb27d,
                                _0x1e12ab - this[_0x314383(0x201)](),
                                'right'
                            ));
                        break;
                    case 'SP':
                        _0xa6cef5 = DataManager[_0x314383(0xe8)](_0x3df946);
                        if (_0xa6cef5 <= 0x0) continue;
                        (this[_0x314383(0x2ff)](
                            _0xa6cef5,
                            _0x533e6e,
                            _0x4fb27d,
                            _0x1e12ab,
                            _0x314383(0x367)
                        ),
                            (_0x1ea220 = '\x5cI[%1]%2'['format'](
                                ImageManager[_0x314383(0x325)],
                                TextManager['skillPointsFull']
                            )),
                            this['drawTextEx'](_0x1ea220, _0x3cc2ef, _0x4fb27d),
                            (_0x8c7edf = _0x21374f['getSkillPoints']()),
                            this[_0x314383(0x2ff)](
                                _0x8c7edf,
                                _0x14acff,
                                _0x4fb27d,
                                _0x1e12ab - this['itemPadding'](),
                                _0x314383(0x367)
                            ));
                        break;
                    case 'GOLD':
                        _0xa6cef5 = DataManager['getSkillLearnGoldCost'](_0x3df946);
                        if (_0xa6cef5 <= 0x0) continue;
                        this[_0x314383(0x1aa)](
                            _0xa6cef5,
                            TextManager[_0x314383(0x25d)],
                            _0x533e6e,
                            _0x4fb27d,
                            _0x1e12ab
                        );
                        const _0x444804 = Imported['VisuMZ_0_CoreEngine']
                            ? '\x5cI[%1]'['format'](
                                  VisuMZ[_0x314383(0xc8)]['Settings'][_0x314383(0x21d)]['GoldIcon']
                              )
                            : TextManager['currencyUnit'];
                        ((_0x1ea220 = _0x314383(0x2c6)[_0x314383(0x242)](
                            _0x444804,
                            TextManager[_0x314383(0x25d)]
                        )),
                            this[_0x314383(0x1de)](_0x1ea220, _0x3cc2ef, _0x4fb27d),
                            (_0x8c7edf = $gameParty[_0x314383(0x231)]()),
                            this['drawCurrencyValue'](
                                _0x8c7edf,
                                TextManager[_0x314383(0x25d)],
                                _0x14acff,
                                _0x4fb27d,
                                _0x1e12ab - this[_0x314383(0x201)]()
                            ));
                        break;
                    case 'ITEM':
                        const _0x233293 = DataManager[_0x314383(0x247)](_0x3df946);
                        if (_0x233293[_0x314383(0x1a3)] <= 0x0) continue;
                        for (const _0x22ad3e of _0x233293) {
                            if (!_0x22ad3e) continue;
                            const _0x3134bc = $dataItems[_0x22ad3e['id']];
                            ((_0x2dd925 = TextManager[_0x314383(0x300)]),
                                this[_0x314383(0x2a5)](
                                    _0x3134bc,
                                    _0x3cc2ef,
                                    _0x4fb27d,
                                    _0x28d3a1 - _0x3cc2ef
                                ),
                                (_0x1ea220 = _0x2dd925[_0x314383(0x242)](
                                    _0x22ad3e[_0x314383(0x2bc)],
                                    '\x5cI[%1]'[_0x314383(0x242)](_0x3134bc['iconIndex']),
                                    _0x3134bc[_0x314383(0x210)]
                                )),
                                this[_0x314383(0x215)](_0x1ea220, _0x533e6e, _0x4fb27d, _0x1e12ab),
                                (_0x1ea220 = _0x2dd925['format'](
                                    $gameParty[_0x314383(0x236)](_0x3134bc),
                                    '\x5cI[%1]'[_0x314383(0x242)](_0x3134bc[_0x314383(0x2b0)]),
                                    _0x3134bc[_0x314383(0x210)]
                                )),
                                this[_0x314383(0x215)](
                                    _0x1ea220,
                                    _0x14acff,
                                    _0x4fb27d,
                                    _0x1e12ab - this['itemPadding']()
                                ),
                                (_0x4fb27d += _0x2f42e6));
                            if (_0x4fb27d + _0x2f42e6 > this[_0x314383(0x156)]) return;
                        }
                        continue;
                        break;
                    case _0x314383(0x132):
                        const _0xc2fd73 = DataManager[_0x314383(0xe2)](_0x3df946);
                        if (_0xc2fd73[_0x314383(0x1a3)] <= 0x0) continue;
                        for (const _0x3c3bb4 of _0xc2fd73) {
                            if (_0x314383(0x170) === _0x314383(0x170)) {
                                if (!_0x3c3bb4) continue;
                                const _0x598231 = $dataWeapons[_0x3c3bb4['id']];
                                ((_0x2dd925 = TextManager[_0x314383(0x32c)]),
                                    this[_0x314383(0x2a5)](
                                        _0x598231,
                                        _0x3cc2ef,
                                        _0x4fb27d,
                                        _0x28d3a1 - _0x3cc2ef
                                    ),
                                    (_0x1ea220 = _0x2dd925[_0x314383(0x242)](
                                        _0x3c3bb4['quantity'],
                                        '\x5cI[%1]'[_0x314383(0x242)](_0x598231[_0x314383(0x2b0)]),
                                        _0x598231['name']
                                    )),
                                    this[_0x314383(0x215)](
                                        _0x1ea220,
                                        _0x533e6e,
                                        _0x4fb27d,
                                        _0x1e12ab
                                    ),
                                    (_0x1ea220 = _0x2dd925[_0x314383(0x242)](
                                        $gameParty['numItems'](_0x598231),
                                        _0x314383(0x35d)[_0x314383(0x242)](
                                            _0x598231[_0x314383(0x2b0)]
                                        ),
                                        _0x598231['name']
                                    )),
                                    this['drawTextExRightAlign'](
                                        _0x1ea220,
                                        _0x14acff,
                                        _0x4fb27d,
                                        _0x1e12ab - this[_0x314383(0x201)]()
                                    ),
                                    (_0x4fb27d += _0x2f42e6));
                                if (_0x4fb27d + _0x2f42e6 > this[_0x314383(0x156)]) return;
                            } else
                                return _0x32832e[_0x314383(0xfd)][_0x314383(0x207)][
                                    _0x314383(0x11a)
                                ][_0x314383(0x27f)];
                        }
                        continue;
                        break;
                    case _0x314383(0x1a5):
                        const _0x1a22cd = DataManager[_0x314383(0x1ac)](_0x3df946);
                        if (_0x1a22cd[_0x314383(0x1a3)] <= 0x0) continue;
                        for (const _0x15c283 of _0x1a22cd) {
                            if (!_0x15c283) continue;
                            const _0x4d1bed = $dataArmors[_0x15c283['id']];
                            ((_0x2dd925 = TextManager[_0x314383(0x15a)]),
                                this[_0x314383(0x2a5)](
                                    _0x4d1bed,
                                    _0x3cc2ef,
                                    _0x4fb27d,
                                    _0x28d3a1 - _0x3cc2ef
                                ),
                                (_0x1ea220 = _0x2dd925[_0x314383(0x242)](
                                    _0x15c283[_0x314383(0x2bc)],
                                    _0x314383(0x35d)[_0x314383(0x242)](_0x4d1bed[_0x314383(0x2b0)]),
                                    _0x4d1bed[_0x314383(0x210)]
                                )),
                                this['drawTextExRightAlign'](
                                    _0x1ea220,
                                    _0x533e6e,
                                    _0x4fb27d,
                                    _0x1e12ab
                                ),
                                (_0x1ea220 = _0x2dd925['format'](
                                    $gameParty['numItems'](_0x4d1bed),
                                    _0x314383(0x35d)[_0x314383(0x242)](_0x4d1bed[_0x314383(0x2b0)]),
                                    _0x4d1bed[_0x314383(0x210)]
                                )),
                                this[_0x314383(0x215)](
                                    _0x1ea220,
                                    _0x14acff,
                                    _0x4fb27d,
                                    _0x1e12ab - this[_0x314383(0x201)]()
                                ),
                                (_0x4fb27d += _0x2f42e6));
                            if (_0x4fb27d + _0x2f42e6 > this['innerHeight']) return;
                        }
                        continue;
                        break;
                    case 'CUSTOM':
                        const _0x595f4e = VisuMZ['SkillLearnSystem'][_0x314383(0x315)](
                            _0x3df946,
                            _0x314383(0x1be)
                        );
                        if (VisuMZ[_0x314383(0xfd)]['JS'][_0x595f4e])
                            _0x314383(0xd4) !== _0x314383(0xbe)
                                ? ((_0x1ea220 = VisuMZ[_0x314383(0xfd)]['JS'][_0x595f4e][
                                      _0x314383(0x1d1)
                                  ](this, _0x21374f, _0x3df946)),
                                  this[_0x314383(0x1de)](_0x1ea220, _0x3cc2ef, _0x4fb27d))
                                : (_0x103e35 += _0x25b7fc[_0x314383(0xc7)](
                                      (_0x95c357 - _0x2013fb) / 0x2
                                  ));
                        else {
                            if (_0x314383(0x2fd) === _0x314383(0x2fd)) continue;
                            else
                                return this[_0x314383(0x202)]()
                                    ? 0x1
                                    : _0x42843f[_0x314383(0xfd)]['Window_SkillList_maxCols'][
                                          'call'
                                      ](this);
                        }
                        break;
                    case 'CP':
                        if (Imported[_0x314383(0x2e0)]) {
                            if (_0x314383(0x197) === 'hlctT')
                                return this['deadMembers']()[_0x314383(0x2f1)](
                                    (_0x120f6f, _0x425c13) =>
                                        _0x120f6f + _0x425c13[_0x314383(0xfe)](),
                                    0x0
                                );
                            else {
                                _0xa6cef5 = DataManager[_0x314383(0xb9)](_0x3df946) || 0x0;
                                if (_0xa6cef5 <= 0x0) continue;
                                (this[_0x314383(0x261)](
                                    _0xa6cef5,
                                    _0x533e6e,
                                    _0x4fb27d,
                                    _0x1e12ab,
                                    'right'
                                ),
                                    (_0x1ea220 = _0x314383(0x1a4)[_0x314383(0x242)](
                                        ImageManager[_0x314383(0x359)],
                                        TextManager['classPointsFull']
                                    )),
                                    this[_0x314383(0x1de)](_0x1ea220, _0x3cc2ef, _0x4fb27d),
                                    (_0x8c7edf = _0x21374f[_0x314383(0x204)]()),
                                    this['drawClassPoints'](
                                        _0x8c7edf,
                                        _0x14acff,
                                        _0x4fb27d,
                                        _0x1e12ab - this['itemPadding'](),
                                        _0x314383(0x367)
                                    ));
                            }
                        } else continue;
                        break;
                    case 'JP':
                        if (Imported['VisuMZ_2_ClassChangeSystem']) {
                            if (_0x314383(0xf5) !== _0x314383(0xf5)) this[_0x314383(0x362)] = {};
                            else {
                                _0xa6cef5 =
                                    DataManager['getSkillLearnJobPointCost'](_0x3df946) || 0x0;
                                if (_0xa6cef5 <= 0x0) continue;
                                (this[_0x314383(0xd0)](
                                    _0xa6cef5,
                                    _0x533e6e,
                                    _0x4fb27d,
                                    _0x1e12ab,
                                    _0x314383(0x367)
                                ),
                                    (_0x1ea220 = '\x5cI[%1]%2'[_0x314383(0x242)](
                                        ImageManager[_0x314383(0x2a8)],
                                        TextManager['jobPointsFull']
                                    )),
                                    this[_0x314383(0x1de)](_0x1ea220, _0x3cc2ef, _0x4fb27d),
                                    (_0x8c7edf = _0x21374f[_0x314383(0x28b)]()),
                                    this['drawJobPoints'](
                                        _0x8c7edf,
                                        _0x14acff,
                                        _0x4fb27d,
                                        _0x1e12ab - this[_0x314383(0x201)](),
                                        _0x314383(0x367)
                                    ));
                            }
                        } else {
                            if (_0x314383(0x1e0) !== 'vGpPP') continue;
                            else
                                this[_0x314383(0xa6)](_0x494454)
                                    ? this[_0x314383(0x331)](
                                          _0xb1b665,
                                          _0x48a402,
                                          _0x1b2a01,
                                          _0xc4e1eb
                                      )
                                    : this[_0x314383(0x266)](
                                          _0x146a71,
                                          _0x50a5a4,
                                          _0x5ac22f,
                                          _0x5e43e1
                                      );
                        }
                        break;
                    default:
                        continue;
                }
                _0x4fb27d += _0x2f42e6;
                if (_0x4fb27d + _0x2f42e6 > this[_0x314383(0x156)]) return;
            }
        }
    }),
    (Window_SkillLearnIngredients['prototype']['getSkillLearnDisplayedCosts'] = function () {
        const _0x242c3c = _0x3591d3,
            _0x299bb3 = JsonEx['makeDeepCopy'](
                VisuMZ[_0x242c3c(0xfd)][_0x242c3c(0x207)][_0x242c3c(0x374)][_0x242c3c(0x18d)]
            );
        return (_0x299bb3[_0x242c3c(0x2b5)](_0x242c3c(0x1e4)), _0x299bb3);
    }),
    (Window_SkillLearnIngredients['prototype'][_0x3591d3(0x11c)] = function () {
        return ![];
    }));
function Window_SkillLearnConfirm() {
    const _0x30ea3a = _0x3591d3;
    this[_0x30ea3a(0xb4)](...arguments);
}
function _0x58d2(_0x3d66e1, _0x23843b) {
    const _0x3ca60f = _0x3ca6();
    return (
        (_0x58d2 = function (_0x58d291, _0x12d5fd) {
            _0x58d291 = _0x58d291 - 0x9e;
            let _0x24aead = _0x3ca60f[_0x58d291];
            return _0x24aead;
        }),
        _0x58d2(_0x3d66e1, _0x23843b)
    );
}
((Window_SkillLearnConfirm['prototype'] = Object['create'](Window_HorzCommand['prototype'])),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)][_0x3591d3(0x140)] = Window_SkillLearnConfirm),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)][_0x3591d3(0xb4)] = function (_0x5cd782) {
        const _0x2264c1 = _0x3591d3;
        Window_HorzCommand[_0x2264c1(0x1ad)][_0x2264c1(0xb4)][_0x2264c1(0x1d1)](this, _0x5cd782);
    }),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)][_0x3591d3(0xdb)] = function () {
        return 0x2;
    }),
    (Window_SkillLearnConfirm['prototype'][_0x3591d3(0x2c0)] = function () {
        const _0x488100 = _0x3591d3;
        return this[_0x488100(0x156)];
    }),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)][_0x3591d3(0x2fb)] = function () {
        const _0x5a8a26 = _0x3591d3;
        (this[_0x5a8a26(0x36a)](TextManager[_0x5a8a26(0x20a)], 'ok', this['isConfirmEnabled']()),
            this[_0x5a8a26(0x36a)](TextManager[_0x5a8a26(0x2be)], _0x5a8a26(0x23c)));
    }),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)]['isConfirmEnabled'] = function () {
        const _0x41e76e = _0x3591d3,
            _0x1c8158 = SceneManager[_0x41e76e(0x31a)];
        if (!_0x1c8158) return ![];
        const _0x56c07c = _0x1c8158[_0x41e76e(0x216)]();
        if (!_0x56c07c) return ![];
        const _0x2970d5 = _0x1c8158['item']();
        if (!_0x2970d5) return ![];
        if (!_0x56c07c[_0x41e76e(0x35f)](_0x2970d5)) return ![];
        return _0x56c07c['canPayForSkillLearnSystem'](_0x2970d5);
    }),
    (Window_SkillLearnConfirm[_0x3591d3(0x1ad)]['drawItem'] = function (_0x4fd3f1) {
        const _0xc6bacf = _0x3591d3,
            _0x467d1d = this[_0xc6bacf(0xb1)](_0x4fd3f1);
        (this['resetTextColor'](), this[_0xc6bacf(0xaa)](this[_0xc6bacf(0x154)](_0x4fd3f1)));
        const _0x188a08 = this[_0xc6bacf(0x342)](_0x4fd3f1),
            _0x52edf8 = this['textSizeEx'](_0x188a08)['width'];
        ((_0x467d1d['x'] += Math[_0xc6bacf(0xc7)]((_0x467d1d[_0xc6bacf(0x347)] - _0x52edf8) / 0x2)),
            this['drawTextEx'](_0x188a08, _0x467d1d['x'], _0x467d1d['y'], _0x52edf8));
    }),
    (Window_SkillLearnConfirm['prototype']['playOkSound'] = function () {
        const _0x1187d1 = _0x3591d3;
        if (this['currentSymbol']() === 'ok') {
        } else Window_HorzCommand[_0x1187d1(0x1ad)]['playOkSound']['call'](this);
    }));
