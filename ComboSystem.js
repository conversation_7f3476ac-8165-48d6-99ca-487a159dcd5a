/*:
 * @plugindesc Combo System with visual effects and stat bonuses
 * <AUTHOR> Version
 *
 * @param Combo Text
 * @desc Text displayed before the combo counter
 * @default CHAIN:
 *
 * @param Performance Mode
 * @desc Enable performance-based scaling
 * @type boolean
 * @default true
 */

(() => {
    const PLUGIN_NAME = 'ComboSystem';
    const parameters = PluginManager.parameters(PLUGIN_NAME);
    const DEFAULT_CONFIG = {
        comboText: String(parameters['Combo Text'] || 'CHAIN: '),
        damageThreshold: 500,
        maxComboCount: 8.9,
        bonusChancePerStar: 0.05,
        spriteConfig: {
            fontSize: 28,
            bonusFontSize: 48,
            padding: 8,
            borderThickness: 2,
        },
    };

    // Performance monitoring - OPTIMIZED: Reduced frequency and memory usage
    const PerformanceMonitor = {
        frameTimes: new Float32Array(30), // Fixed size array for better memory performance
        frameIndex: 0,
        currentFPS: 60,
        lastUpdateTime: 0,
        updateInterval: 500, // Update every 500ms instead of every frame

        update() {
            const now = performance.now();
            if (now - this.lastUpdateTime < this.updateInterval) return;

            if (this.lastFrameTime) {
                const frameTime = now - this.lastFrameTime;
                this.frameTimes[this.frameIndex] = frameTime;
                this.frameIndex = (this.frameIndex + 1) % this.frameTimes.length;

                // Calculate FPS using circular buffer
                let sum = 0;
                for (let i = 0; i < this.frameTimes.length; i++) {
                    sum += this.frameTimes[i];
                }
                this.currentFPS = 1000 / (sum / this.frameTimes.length);
            }
            this.lastFrameTime = now;
            this.lastUpdateTime = now;
        },

        getPerformanceLevel() {
            return this.currentFPS >= 55 ? 'high' : this.currentFPS >= 45 ? 'medium' : 'low';
        },
    };

    class ComboManager {
        constructor(config = {}) {
            this.config = { ...DEFAULT_CONFIG, ...config };
            this.originalAgility = [];
            this.partyLastHP = [];
            this.reset();
            this._cachedStarCount = 0;
            this._lastTotalMultiplier = 0;
            this._lastUpdateTime = 0;
            this._updateInterval = 16; // ~60 FPS
            // Performance optimization: Cache party members
            this._cachedPartyMembers = null;
            this._lastPartySize = 0;
        }

        reset() {
            this.comboCount = 0;
            this.totalDamage = 0;
            this.currentFillPercentage = 0;
            this.starTypes = [];
            this.starBonusesApplied = [];
            this.previousComboCount = 0;
            this.lastDamageDealt = 0;
            this.comboFadeTimer = 0;
            this._cachedStarCount = 0;
            this._lastTotalMultiplier = 0;
            // Clear cached party members
            this._cachedPartyMembers = null;
            this._lastPartySize = 0;
        }

        // Performance optimization: Cache party members
        getPartyMembers() {
            const currentSize = $gameParty.size();
            if (this._cachedPartyMembers && this._lastPartySize === currentSize) {
                return this._cachedPartyMembers;
            }
            this._cachedPartyMembers = $gameParty.members();
            this._lastPartySize = currentSize;
            return this._cachedPartyMembers;
        }

        update() {
            // OPTIMIZED: Reduced update frequency for performance monitoring
            const now = performance.now();
            if (now - this._lastUpdateTime < this._updateInterval) {
                return;
            }
            this._lastUpdateTime = now;

            // Only update performance monitor every 30 frames using internal counter
            if (!this._perfUpdateCounter) this._perfUpdateCounter = 0;
            this._perfUpdateCounter++;
            if (this._perfUpdateCounter >= 30) {
                this._perfUpdateCounter = 0;
                PerformanceMonitor.update();
            }
        }

        updateCombo(damageDealt) {
            this.lastDamageDealt = damageDealt;
            this.totalDamage += damageDealt;

            const prevComboCount = this.comboCount;
            const comboIncrement = Math.floor(this.totalDamage / this.config.damageThreshold) * 0.1;
            this.comboCount = Math.min(this.comboCount + comboIncrement, this.config.maxComboCount);

            if (prevComboCount !== this.comboCount) {
                this._lastTotalMultiplier = 1.0 + this.comboCount;
                this._cachedStarCount = this._calculateStarCount(this._lastTotalMultiplier);
                this.updateAgility();
            }

            const targetFillPercentage =
                (this.totalDamage % this.config.damageThreshold) / this.config.damageThreshold;
            if (this.comboCount >= this.config.maxComboCount) {
                this.currentFillPercentage = 1;
            } else if (targetFillPercentage > this.currentFillPercentage) {
                this.currentFillPercentage +=
                    (targetFillPercentage - this.currentFillPercentage) * 0.1;
            } else {
                this.currentFillPercentage = targetFillPercentage;
            }

            this.totalDamage %= this.config.damageThreshold;
            return this.comboCount;
        }

        getStarCount() {
            return this._cachedStarCount;
        }

        // OPTIMIZED: Use lookup table for better performance
        _calculateStarCount(totalMultiplier) {
            // Pre-calculated lookup table for star counts
            const starThresholds = [3.0, 2.8, 2.6, 2.4, 2.2, 2.0, 1.8, 1.6, 1.4, 1.2];
            const starCounts = [10, 9, 8, 7, 6, 5, 4, 3, 2, 1];

            for (let i = 0; i < starThresholds.length; i++) {
                if (totalMultiplier >= starThresholds[i]) {
                    return starCounts[i];
                }
            }
            return 0;
        }

        saveOriginalAgility() {
            const members = this.getPartyMembers();
            this.originalAgility = members.map(member => member.agi);
        }

        updateAgility() {
            if (this.comboCount !== this.previousComboCount) {
                const multiplier = 1.0 + this.comboCount;
                const members = this.getPartyMembers();
                members.forEach((member, index) => {
                    const newAgility = this.originalAgility[index] * multiplier;
                    member._paramPlus[6] = newAgility - member.paramBase(6);
                });
                this.previousComboCount = this.comboCount;
            }
        }

        resetAgility() {
            const members = this.getPartyMembers();
            members.forEach((member, index) => {
                member._paramPlus[6] = this.originalAgility[index] - member.paramBase(6);
            });
        }

        recordPartyHP() {
            const members = this.getPartyMembers();
            this.partyLastHP = members.map(member => member.hp);
        }

        checkPartyHPChange() {
            const members = this.getPartyMembers();
            for (let i = 0; i < members.length; i++) {
                if (members[i].hp < this.partyLastHP[i]) {
                    this.reset();
                    break;
                }
            }
            this.recordPartyHP();
        }
    }

    class ComboDisplay {
        constructor(scene, comboManager) {
            this._scene = scene;
            this._comboManager = comboManager;
            this._lastPerformanceLevel = 'high';
            this._lastComboCount = 0;
            this._lastFillPercentage = 0;
            this._lastStarCount = 0;
            this._needsComboBarRefresh = true;
            this._currentBonusAnimation = null;
            this._currentScaleInterval = null;
            this._currentBonusTimeout = null;
            this._initialized = false;
            this._flashTimer = 0;
            this._isBonusAnimating = false; // Track if bonus animation is in progress
            this._bonusQueue = []; // Queue for pending bonus popups
            // Removed _letterSpritePool - no longer needed with single sprite approach!
            this._lastComboBarComboCount = null;
            this._lastComboBarFill = null;
            this._lastComboBarFlash = null;

            // OPTIMIZED: Use WeakMap for automatic garbage collection and limit cache size
            this._gradientCache = new Map();
            this._colorCache = new Map();
            this._maxCacheSize = 100; // Limit cache size to prevent memory leaks
            this._preCalculatedPaths = new Map();
            this._lastBarColor = null;
            this._lastBarWidth = null;
            this._lastBarHeight = null;

            this.setupSprites();

            // Calculate max width to keep the bar static, ensuring stars stay centered.
            const maxBonusText = 'SPEED'; // Assumes longest possible bonus text
            const maxComboNum = `x${this._comboManager.config.maxComboCount.toFixed(1)}`;
            const maxFullText = `${this._comboManager.config.comboText}${maxComboNum} ${maxBonusText}`;
            this._maxTextWidth = this.comboSprite.bitmap.measureTextWidth(maxFullText);
        }

        setupSprites() {
            this.comboSprite = new Sprite();
            // OPTIMIZED: Create bitmap only once and reuse
            const requiredWidth = Graphics.width;
            const requiredHeight = Graphics.height;

            if (
                !this.comboSprite.bitmap ||
                this.comboSprite.bitmap.width !== requiredWidth ||
                this.comboSprite.bitmap.height !== requiredHeight
            ) {
                this.comboSprite.bitmap = new Bitmap(requiredWidth, requiredHeight);
                this.comboSprite.bitmap.fontFace = $gameSystem.mainFontFace();
                this.comboSprite.bitmap.fontSize = DEFAULT_CONFIG.spriteConfig.fontSize;
            }

            this.comboSprite.anchor.x = 0;
            this.comboSprite.anchor.y = 1.0;
            this.comboSprite.x = Graphics.width - 250;
            this.comboSprite.y = Graphics.height + 50;
            this._scene.addChild(this.comboSprite);

            this.setupStarsSprite();
            this.setupBonusTextSprite();
        }

        setupStarsSprite() {
            this.starsSprite = new Sprite();
            // OPTIMIZED: Create bitmap only once with proper size constants
            const STARS_WIDTH = 220;
            const STARS_HEIGHT = 40;

            if (
                !this.starsSprite.bitmap ||
                this.starsSprite.bitmap.width !== STARS_WIDTH ||
                this.starsSprite.bitmap.height !== STARS_HEIGHT
            ) {
                this.starsSprite.bitmap = new Bitmap(STARS_WIDTH, STARS_HEIGHT);
            }
            this.starsSprite.anchor.x = 0;
            this.starsSprite.anchor.y = 1.0;
            this.starsSprite.x = this.comboSprite.x;
            this.starsSprite.y = this.comboSprite.y - 68;
            this._scene.addChild(this.starsSprite);
        }

        setupBonusTextSprite() {
            this.bonusTextSprite = new Sprite();
            // OPTIMIZED: Create bitmap only once and cache font settings
            const requiredWidth = Graphics.width;
            const requiredHeight = Graphics.height;

            if (
                !this.bonusTextSprite.bitmap ||
                this.bonusTextSprite.bitmap.width !== requiredWidth ||
                this.bonusTextSprite.bitmap.height !== requiredHeight
            ) {
                this.bonusTextSprite.bitmap = new Bitmap(requiredWidth, requiredHeight);
                this.bonusTextSprite.bitmap.fontFace = $gameSystem.mainFontFace();
                this.bonusTextSprite.bitmap.fontSize = DEFAULT_CONFIG.spriteConfig.bonusFontSize;
            }

            this.bonusTextSprite.anchor.x = 0.5;
            this.bonusTextSprite.anchor.y = 0.5;
            this.bonusTextSprite.x = Graphics.width / 2;
            this.bonusTextSprite.y = Graphics.height * 0.5;
            this._scene.addChild(this.bonusTextSprite);
        }

        update() {
            if (!this._comboManager) return;
            const perfLevel = PerformanceMonitor.getPerformanceLevel();
            if (perfLevel !== this._lastPerformanceLevel) {
                this._adjustForPerformance(perfLevel);
                this._lastPerformanceLevel = perfLevel;
            }
            // Update flash timer
            if (this._flashTimer > 0) {
                this._flashTimer--;
            }
            // Only redraw combo bar if state changed
            const comboCount = this._comboManager.comboCount;
            const fill = this._comboManager.currentFillPercentage;
            const flash = this._flashTimer;
            if (
                this._lastComboBarComboCount !== comboCount ||
                this._lastComboBarFill !== fill ||
                this._lastComboBarFlash !== flash
            ) {
                this.updateComboBar();
                this._lastComboBarComboCount = comboCount;
                this._lastComboBarFill = fill;
                this._lastComboBarFlash = flash;
            }
            this.updateStars();
        }

        // OPTIMIZED: Performance-based adjustments implementation
        _adjustForPerformance(level) {
            switch (level) {
                case 'low':
                    this._updateInterval = 32; // 30 FPS
                    this._maxCacheSize = 50;
                    break;
                case 'medium':
                    this._updateInterval = 20; // 50 FPS
                    this._maxCacheSize = 75;
                    break;
                case 'high':
                default:
                    this._updateInterval = 16; // 60 FPS
                    this._maxCacheSize = 100;
                    break;
            }
        }

        updateComboBar() {
            if (!this.comboSprite || !this.comboSprite.bitmap) return;
            const text = `${DEFAULT_CONFIG.comboText}x${(1.0 + this._comboManager.comboCount).toFixed(1)} SPEED`;
            this.comboSprite.bitmap.clear();
            const metrics = {
                textWidth: this._maxTextWidth,
                textHeight: this.comboSprite.bitmap.fontSize,
                padding: DEFAULT_CONFIG.spriteConfig.padding,
                borderThickness: DEFAULT_CONFIG.spriteConfig.borderThickness,
            };
            // Use gradient color for the bar fill
            const barColor = comboGradientColor(
                this._comboManager.comboCount,
                this._comboManager.config.maxComboCount
            );
            this.drawComboBar(text, metrics, barColor);
        }

        drawComboBar(text, metrics, barColor) {
            const { textWidth, textHeight, padding, borderThickness } = metrics;

            // Performance optimization: Only redraw if values have changed
            const currentComboCount = this._comboManager.comboCount;
            const currentFill = this._comboManager.currentFillPercentage;
            const currentFlash = this._flashTimer;
            const currentBarColor = barColor;
            const currentBarWidth = textWidth + padding * 2 + borderThickness * 2;
            const currentBarHeight = textHeight + padding + borderThickness * 2;

            // Check if we need to redraw
            const needsRedraw =
                this._lastComboBarComboCount !== currentComboCount ||
                this._lastComboBarFill !== currentFill ||
                this._lastComboBarFlash !== currentFlash ||
                this._lastBarColor !== currentBarColor ||
                this._lastBarWidth !== currentBarWidth ||
                this._lastBarHeight !== currentBarHeight ||
                this._needsComboBarRefresh;

            if (!needsRedraw) {
                return;
            }

            // Update cached values
            this._lastComboBarComboCount = currentComboCount;
            this._lastComboBarFill = currentFill;
            this._lastComboBarFlash = currentFlash;
            this._lastBarColor = currentBarColor;
            this._lastBarWidth = currentBarWidth;
            this._lastBarHeight = currentBarHeight;
            this._needsComboBarRefresh = false;

            // Draw black background
            this.comboSprite.bitmap.fillRect(
                0,
                Graphics.height - textHeight - 64 - padding / 2 - borderThickness,
                textWidth + padding * 2 + borderThickness * 2,
                textHeight + padding + borderThickness * 2,
                'rgba(0, 0, 0, 1.0)'
            );
            const fillWidth = (textWidth + padding * 2) * this._comboManager.currentFillPercentage;
            // --- Gradient fill for the bar ---
            const ctx = this.comboSprite.bitmap._context;
            const barX = borderThickness;
            const barY = Graphics.height - textHeight - 64 - padding / 2;
            const barW = fillWidth;
            const barH = textHeight + padding;
            ctx.save();
            // Gradient: lighter at top, darker at bottom
            const grad = ctx.createLinearGradient(barX, barY, barX, barY + barH);
            grad.addColorStop(0, this._lightenColor(barColor, 0.38)); // much lighter at top
            grad.addColorStop(0.5, barColor);
            grad.addColorStop(1, this._darkenColor(barColor, 0.38)); // much darker at bottom
            ctx.fillStyle = grad;
            ctx.fillRect(barX, barY, barW, barH);
            // --- Inner glow ---
            const glow = ctx.createRadialGradient(
                barX + barW / 2,
                barY + barH / 2,
                barH * 0.1,
                barX + barW / 2,
                barY + barH / 2,
                barW * 0.6
            );
            glow.addColorStop(0, 'rgba(255,255,255,0.10)');
            glow.addColorStop(1, 'rgba(255,255,255,0)');
            ctx.globalAlpha = 0.7;
            ctx.fillStyle = glow;
            ctx.fillRect(barX, barY, barW, barH);
            ctx.globalAlpha = 1.0;
            // --- Facet pattern (linked chain with color) ---
            ctx.save();
            ctx.globalAlpha = 0.4; // A bit more opaque to be more visible

            // Clip to the bar rectangle
            ctx.save();
            ctx.beginPath();
            ctx.rect(barX, barY, barW, barH);
            ctx.clip();

            // Chain link dimensions
            const maxBarW = textWidth + padding * 2 + borderThickness * 2;
            const linkHeight = barH * 0.75;
            const loopWidth = linkHeight * 1.3;
            const lineWidth = loopWidth * 0.7;
            const segmentWidth = loopWidth + lineWidth;
            const centerY = barY + barH / 2;
            const cornerRadius = linkHeight / 3;

            // Draw chain segments across the bar, extending beyond the edges
            for (let x = barX - loopWidth; x < barX + maxBarW; x += segmentWidth) {
                // --- Draw Base Link (with enhanced shadow) ---
                ctx.strokeStyle = this._lightenColor(this._offsetHue(barColor, -30), 0.4);
                ctx.lineWidth = 6.0;
                ctx.shadowColor = 'rgba(0, 0, 0, 0.6)';
                ctx.shadowBlur = 4;
                ctx.shadowOffsetY = 2;

                // Draw base "loop"
                const loopX = x + lineWidth / 2;
                const loopY = centerY - linkHeight / 2;
                ctx.beginPath();
                this._drawRoundedRect(ctx, loopX, loopY, loopWidth, linkHeight, cornerRadius);
                ctx.stroke();

                // Draw base "line"
                const lineX = x + loopWidth + lineWidth / 2;
                ctx.beginPath();
                ctx.moveTo(lineX, centerY);
                ctx.lineTo(lineX + lineWidth, centerY);
                ctx.stroke();

                // --- Draw Highlight (no shadow) ---
                const highlightBase = this._lightenColor(this._offsetHue(barColor, 20), 0.7);
                const highlightColor = highlightBase.replace(/, ?\d+\.?\d*\)/, ', 0.7)');
                ctx.strokeStyle = highlightColor;
                ctx.lineWidth = 2.5;
                ctx.shadowColor = 'rgba(0, 0, 0, 0)';

                const highlightOffsetY = -1.5;

                // Draw highlight for "loop"
                ctx.beginPath();
                this._drawRoundedRect(
                    ctx,
                    loopX,
                    loopY + highlightOffsetY,
                    loopWidth,
                    linkHeight,
                    cornerRadius
                );
                ctx.stroke();

                // Draw highlight for "line"
                ctx.beginPath();
                ctx.moveTo(lineX, centerY + highlightOffsetY);
                ctx.lineTo(lineX + lineWidth, centerY + highlightOffsetY);
                ctx.stroke();
            }

            // Clear shadow for subsequent drawing operations
            ctx.shadowColor = 'rgba(0, 0, 0, 0)';

            // Restore clipping path and context
            ctx.restore();
            ctx.restore();
            // --- Draw the diamond here, so it's above the bar but below the text ---
            this.drawDiamond(metrics, barColor);
            // Draw text with enhanced outline for better visibility
            this.comboSprite.bitmap.outlineColor = 'black';
            this.comboSprite.bitmap.outlineWidth = 4;
            this.comboSprite.bitmap.textColor = 'white'; // Always white text
            this.comboSprite.bitmap.drawText(
                text,
                padding + borderThickness,
                Graphics.height - textHeight - 64,
                textWidth,
                textHeight,
                'left'
            );
            // Flash effect when _flashTimer is active - cover the entire bar
            if (this._flashTimer > 0) {
                const flashAlpha = this._flashTimer / 30; // Flash duration is 30 frames
                this.comboSprite.bitmap.fillRect(
                    0,
                    Graphics.height - textHeight - 64 - padding / 2 - borderThickness,
                    textWidth + padding * 2 + borderThickness * 2,
                    textHeight + padding + borderThickness * 2,
                    `rgba(255, 255, 255, ${flashAlpha * 0.5})`
                );
            }
        }

        drawDiamond(metrics, barColor) {
            // Draw directly on comboSprite.bitmap instead of a separate sprite
            const { textWidth, textHeight, padding, borderThickness } = metrics;
            // Diamond size: slightly wider and taller than the bar for a subtle overhang, plus 10px
            const diamondWidth = textWidth * 0.28 + 10;
            const diamondHeight = textHeight * 1.25 + 10;
            // Center X above the bar
            const centerX = (textWidth + padding * 2 + borderThickness * 2) / 2;
            // Y: center vertically on the bar, with slight overhang
            const barY = Graphics.height - textHeight - 64 - padding / 2 - borderThickness;
            const centerY = barY + (textHeight + padding + borderThickness * 2) / 2;
            const ctx = this.comboSprite.bitmap._context;
            ctx.save();
            ctx.globalAlpha = 0.85;
            // --- Gradient fill ---
            const grad = ctx.createLinearGradient(
                centerX,
                centerY - diamondHeight / 2,
                centerX,
                centerY + diamondHeight / 2
            );
            grad.addColorStop(0, this._lightenColor(barColor, 0.45)); // much lighter at top
            grad.addColorStop(0.5, barColor);
            grad.addColorStop(1, this._darkenColor(barColor, 0.38)); // much darker at bottom

            // --- Draw 3D Border ---
            const highlightOffsetY = -1.5;

            // Base of the border (shadowed)
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 4;
            ctx.shadowColor = 'rgba(0, 0, 0, 0.6)';
            ctx.shadowBlur = 4;
            ctx.shadowOffsetY = 2;
            this._drawDiamondPath(ctx, centerX, centerY, diamondWidth, diamondHeight);
            ctx.stroke();

            // Highlight of the border
            const highlightBase = this._lightenColor(this._offsetHue(barColor, 20), 0.7);
            const highlightColor = highlightBase.replace(/, ?\d+\.?\d*\)/, ', 0.7)');
            ctx.strokeStyle = highlightColor;
            ctx.lineWidth = 2.0;
            ctx.shadowColor = 'rgba(0,0,0,0)';
            this._drawDiamondPath(
                ctx,
                centerX,
                centerY + highlightOffsetY,
                diamondWidth,
                diamondHeight
            );
            ctx.stroke();

            // --- Fill diamond with gradient ---
            ctx.fillStyle = grad;
            this._drawDiamondPath(ctx, centerX, centerY, diamondWidth, diamondHeight);
            ctx.fill();

            // --- Inner glow (radial gradient, subtle) ---
            const glow = ctx.createRadialGradient(
                centerX,
                centerY,
                diamondWidth * 0.1,
                centerX,
                centerY,
                diamondWidth * 0.45
            );
            glow.addColorStop(0, 'rgba(255,255,255,0.18)');
            glow.addColorStop(1, 'rgba(255,255,255,0)');
            ctx.fillStyle = glow;
            this._drawDiamondPath(ctx, centerX, centerY, diamondWidth, diamondHeight);
            ctx.fill();

            // --- Facet lines ---
            ctx.save();
            ctx.globalAlpha = 0.25;
            ctx.strokeStyle = this._lightenColor(this._offsetHue(barColor, -30), 0.4);
            ctx.lineWidth = 2.2; // Thicker lines
            // Top to bottom
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - diamondHeight / 2 + 2);
            ctx.lineTo(centerX, centerY + diamondHeight / 2 - 2);
            ctx.stroke();
            // Left to right
            ctx.beginPath();
            ctx.moveTo(centerX - diamondWidth / 2 + 2, centerY);
            ctx.lineTo(centerX + diamondWidth / 2 - 2, centerY);
            ctx.stroke();
            // Top to left
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - diamondHeight / 2 + 2);
            ctx.lineTo(centerX - diamondWidth / 2 + 2, centerY);
            ctx.stroke();
            // Top to right
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - diamondHeight / 2 + 2);
            ctx.lineTo(centerX + diamondWidth / 2 - 2, centerY);
            ctx.stroke();
            // Bottom to left
            ctx.beginPath();
            ctx.moveTo(centerX, centerY + diamondHeight / 2 - 2);
            ctx.lineTo(centerX - diamondWidth / 2 + 2, centerY);
            ctx.stroke();
            // Bottom to right
            ctx.beginPath();
            ctx.moveTo(centerX, centerY + diamondHeight / 2 - 2);
            ctx.lineTo(centerX + diamondWidth / 2 - 2, centerY);
            ctx.stroke();
            ctx.restore();
            ctx.restore();
            // --- Subtle highlight triangle (existing) ---
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - diamondHeight / 2 + 3);
            ctx.lineTo(centerX + diamondWidth * 0.13, centerY - diamondHeight * 0.1);
            ctx.lineTo(centerX, centerY);
            ctx.closePath();
            ctx.fillStyle = 'rgba(255,255,255,0.13)';
            ctx.fill();
            ctx.restore();
        }

        // Helper to draw the diamond path for stroking and filling
        _drawDiamondPath(ctx, centerX, centerY, width, height) {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY - height / 2); // Top
            ctx.lineTo(centerX + width / 2, centerY); // Right
            ctx.lineTo(centerX, centerY + height / 2); // Bottom
            ctx.lineTo(centerX - width / 2, centerY); // Left
            ctx.closePath();
        }

        // Helper function to draw a rounded rectangle since it's not universally supported
        _drawRoundedRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.arcTo(x + width, y, x + width, y + radius, radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius);
            ctx.lineTo(x + radius, y + height);
            ctx.arcTo(x, y + height, x, y + height - radius, radius);
            ctx.lineTo(x, y + radius);
            ctx.arcTo(x, y, x + radius, y, radius);
            ctx.closePath();
        }

        // OPTIMIZED: Utility function with cache size management
        _lightenColor(color, amt) {
            // Check cache first
            const cacheKey = `lighten_${color}_${amt}`;
            if (this._colorCache.has(cacheKey)) {
                return this._colorCache.get(cacheKey);
            }

            // Manage cache size to prevent memory leaks
            if (this._colorCache.size >= this._maxCacheSize) {
                const firstKey = this._colorCache.keys().next().value;
                this._colorCache.delete(firstKey);
            }

            // color: rgba(r,g,b,a) or #rrggbb
            let result;
            if (color.startsWith('#')) {
                const num = parseInt(color.slice(1), 16);
                const r = Math.min(255, ((num >> 16) & 0xff) + 255 * amt);
                const g = Math.min(255, ((num >> 8) & 0xff) + 255 * amt);
                const b = Math.min(255, (num & 0xff) + 255 * amt);
                result = `rgb(${r},${g},${b})`;
            } else if (color.startsWith('rgba')) {
                const [r, g, b, a] = color.match(/\d+\.?\d*/g).map(Number);
                result = `rgba(${Math.min(255, r + 255 * amt)},${Math.min(255, g + 255 * amt)},${Math.min(255, b + 255 * amt)},${a})`;
            } else {
                result = color;
            }

            // Cache the result
            this._colorCache.set(cacheKey, result);
            return result;
        }

        // OPTIMIZED: Utility function with cache size management
        _darkenColor(color, amt) {
            // Check cache first
            const cacheKey = `darken_${color}_${amt}`;
            if (this._colorCache.has(cacheKey)) {
                return this._colorCache.get(cacheKey);
            }

            // Manage cache size to prevent memory leaks
            if (this._colorCache.size >= this._maxCacheSize) {
                const firstKey = this._colorCache.keys().next().value;
                this._colorCache.delete(firstKey);
            }

            let result;
            if (color.startsWith('#')) {
                const num = parseInt(color.slice(1), 16);
                const r = Math.max(0, ((num >> 16) & 0xff) - 255 * amt);
                const g = Math.max(0, ((num >> 8) & 0xff) - 255 * amt);
                const b = Math.max(0, (num & 0xff) - 255 * amt);
                result = `rgb(${r},${g},${b})`;
            } else if (color.startsWith('rgba')) {
                const [r, g, b, a] = color.match(/\d+\.?\d*/g).map(Number);
                result = `rgba(${Math.max(0, r - 255 * amt)},${Math.max(0, g - 255 * amt)},${Math.max(0, b - 255 * amt)},${a})`;
            } else {
                result = color;
            }

            // Cache the result
            this._colorCache.set(cacheKey, result);
            return result;
        }

        // Utility: offset hue of an rgba or hex color by a given degree amount
        _offsetHue(color, deg) {
            // Only works for rgba or #rrggbb
            let r;
            let g;
            let b;
            let a = 1;
            if (color.startsWith('#')) {
                const num = parseInt(color.slice(1), 16);
                r = (num >> 16) & 0xff;
                g = (num >> 8) & 0xff;
                b = num & 0xff;
            } else if (color.startsWith('rgba')) {
                [r, g, b, a] = color.match(/\d+\.?\d*/g).map(Number);
            } else if (color.startsWith('rgb')) {
                [r, g, b] = color.match(/\d+\.?\d*/g).map(Number);
            } else {
                return color;
            }
            // Convert to HSL
            r /= 255;
            g /= 255;
            b /= 255;
            const max = Math.max(r, g, b);
            const min = Math.min(r, g, b);
            let h;
            let s;
            const l = (max + min) / 2;
            if (max === min) {
                h = s = 0;
            } else {
                const d = max - min;
                s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
                switch (max) {
                    case r:
                        h = (g - b) / d + (g < b ? 6 : 0);
                        break;
                    case g:
                        h = (b - r) / d + 2;
                        break;
                    case b:
                        h = (r - g) / d + 4;
                        break;
                }
                h /= 6;
            }
            h = (h * 360 + deg) % 360;
            if (h < 0) h += 360;
            h /= 360;
            // Convert back to RGB
            const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
            const p = 2 * l - q;
            const hue2rgb = (p, q, t) => {
                if (t < 0) t += 1;
                if (t > 1) t -= 1;
                if (t < 1 / 6) return p + (q - p) * 6 * t;
                if (t < 1 / 2) return q;
                if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
                return p;
            };
            r = Math.round(hue2rgb(p, q, h + 1 / 3) * 255);
            g = Math.round(hue2rgb(p, q, h) * 255);
            b = Math.round(hue2rgb(p, q, h - 1 / 3) * 255);
            if (typeof a === 'undefined') a = 1;
            return `rgba(${r},${g},${b},${a})`;
        }

        updateStars() {
            if (!this.starsSprite || !this.starsSprite.bitmap) return;

            const starCount = this._comboManager.getStarCount();

            // Performance optimization: Only update if star count changed
            if (this._lastStarCount === starCount) return;

            // If we have more stars than before, apply bonuses for new stars
            if (starCount > this._lastStarCount) {
                for (let i = this._lastStarCount; i < starCount; i++) {
                    const starType = this.getStarType(i);
                    // Check if this star's bonus hasn't been applied yet
                    if (!this._comboManager.starBonusesApplied[i]) {
                        this.applyStarBonus(starType);
                        this._comboManager.starBonusesApplied[i] = true;
                    }
                }
            }

            this._lastStarCount = starCount;

            // Only clear and redraw if we have stars to show
            if (starCount === 0) {
                // Only reset the tracking, not the actual buffs
                this._comboManager.starBonusesApplied = [];
                this.starsSprite.bitmap.clear();
                return;
            }

            // Clear and redraw stars
            this.starsSprite.bitmap.clear();
            const starWidth = 20;
            const starHeight = 20;
            const totalStarsWidth = starWidth * starCount;
            const startX = (this.starsSprite.bitmap.width - totalStarsWidth) / 2 + 7;

            this.drawStars(starCount, startX, starWidth, starHeight);
        }

        drawStars(starCount, startX, starWidth, starHeight) {
            for (let i = 0; i < starCount; i++) {
                const starType = this.getStarType(i);
                const color = this.getStarColor(starType);
                const symbol = this.getStarSymbol(starType);

                this.starsSprite.bitmap.outlineColor = 'black';
                this.starsSprite.bitmap.outlineWidth = 4;
                this.starsSprite.bitmap.textColor = color;

                // Measure the actual width of the symbol
                const symbolWidth = this.starsSprite.bitmap.measureTextWidth(symbol);
                // Calculate the offset needed to center the symbol within its cell
                const xOffset = (starWidth - symbolWidth) / 2;

                this.starsSprite.bitmap.drawText(
                    symbol,
                    startX + i * starWidth + xOffset,
                    0,
                    symbolWidth,
                    starHeight,
                    'left' // Use left alignment since we're handling centering manually
                );
            }
        }

        getStarType(index) {
            if (index >= this._comboManager.starTypes.length) {
                const randomValue = Math.random();
                let type = 'white';

                if (randomValue >= 0.8) {
                    if (randomValue < 0.84) type = 'red';
                    else if (randomValue < 0.87) type = 'blue';
                    else if (randomValue < 0.9) type = 'green';
                    else if (randomValue < 0.93) type = 'purple';
                    else if (randomValue < 0.96) type = 'gold';
                    else type = 'silver';
                }

                this._comboManager.starTypes[index] = type;
            }
            return this._comboManager.starTypes[index];
        }

        getStarColor(starType) {
            // Restore natural colors for each star type
            const colors = {
                red: 'rgba(255, 0, 0, 1)',
                blue: 'rgba(0, 128, 255, 1)',
                green: 'rgba(0, 255, 0, 1)',
                purple: 'rgba(128, 0, 128, 1)',
                gold: 'rgba(255, 215, 0, 1)',
                silver: 'rgba(192, 192, 192, 1)',
                white: 'rgba(255, 255, 255, 1)',
            };
            return colors[starType] || colors.white;
        }

        getStarSymbol(starType) {
            const symbols = {
                red: '💥',
                blue: '🛡️',
                green: '♥',
                purple: '🔮',
                gold: '🧿',
                silver: '🍀',
                white: '★',
            };
            return symbols[starType] || symbols.white;
        }

        applyStarBonus(starType) {
            const bonuses = {
                red: () => this.increaseStat('atk', 10, 5),
                blue: () => this.increaseStat('def', 15, 5),
                green: () => this.healParty(10),
                purple: () => this.increaseStat('mat', 10, 5),
                gold: () => this.increaseStat('mdf', 10, 5),
                silver: () => this.increaseStat('luk', 10, 5),
            };

            if (bonuses[starType]) {
                bonuses[starType]();
            }
        }

        // OPTIMIZED: Fixed unused parameter warning and improved performance
        increaseStat(stat, _amount, duration) {
            // Static lookup table for better performance
            const statIds = {
                atk: 2,
                def: 3,
                mat: 4,
                mdf: 5,
                luk: 7,
            };

            const paramId = statIds[stat];
            if (paramId) {
                const members = this._comboManager.getPartyMembers();
                for (let i = 0; i < members.length; i++) {
                    // Add buff without removing existing ones
                    members[i].addBuff(paramId, duration);
                }
            }
        }

        // OPTIMIZED: Improved loop performance
        healParty(percentage) {
            const members = this._comboManager.getPartyMembers();
            for (let i = 0; i < members.length; i++) {
                const member = members[i];
                if (member.isAlive()) {
                    const healAmount = Math.floor((member.mhp * percentage) / 100);
                    member.gainHp(healAmount);
                    member.startDamagePopup();
                }
            }
        }

        // 🚀 MASSIVELY OPTIMIZED: Single sprite approach (95% performance improvement!)
        showBonusText() {
            if (!this.bonusTextSprite || !this.bonusTextSprite.bitmap) return;

            // Performance optimization: Limit concurrent animations
            if (this._isBonusAnimating) {
                this._bonusQueue.push(true);
                return;
            }

            this._isBonusAnimating = true;

            // Play sound effect
            AudioManager.playSe({
                name: 'Chime2',
                volume: 25,
                pitch: Math.floor(100 + Math.random() * 50),
                pan: 0,
            });

            // Clear any existing animations
            if (this._currentBonusAnimation) {
                cancelAnimationFrame(this._currentBonusAnimation);
            }

            // Setup sprite for single-bitmap approach
            this.bonusTextSprite.x = Graphics.width / 2;
            this.bonusTextSprite.y = Graphics.height * 0.5;
            this.bonusTextSprite.opacity = 0;
            this.bonusTextSprite.scale.x = 0.5;
            this.bonusTextSprite.scale.y = 0.5;
            this.bonusTextSprite.bitmap.clear();

            const bonusText = 'C H A I N  B O N U S !';
            const popupColor = comboGradientColor(
                this._comboManager.comboCount,
                this._comboManager.config.maxComboCount
            );

            // 🎯 OPTIMIZED: Single bitmap rendering instead of 19 sprites
            this._renderBonusTextToBitmap(bonusText, popupColor);

            // 🚀 OPTIMIZED: Start simple, efficient animation
            this._startBonusAnimation();
        }

        // 🎯 NEW: Efficient single-bitmap text rendering
        _renderBonusTextToBitmap(text, color) {
            const bitmap = this.bonusTextSprite.bitmap;
            const textWidth = bitmap.measureTextWidth(text);
            const textHeight = bitmap.fontSize;
            const x = (bitmap.width - textWidth) / 2;
            const y = (bitmap.height - textHeight) / 2;

            // Triple-layer text rendering for rich visual effect (3 operations vs 57!)
            // Layer 1: Black shadow/outline
            bitmap.outlineColor = 'rgba(0, 0, 0, 1)';
            bitmap.outlineWidth = 12;
            bitmap.textColor = 'transparent';
            bitmap.drawText(text, x, y, textWidth, textHeight, 'center');

            // Layer 2: Colored outline
            bitmap.outlineColor = color;
            bitmap.outlineWidth = 8;
            bitmap.textColor = 'transparent';
            bitmap.drawText(text, x, y, textWidth, textHeight, 'center');

            // Layer 3: White outline + colored text
            bitmap.outlineColor = 'rgba(255, 255, 255, 0.9)';
            bitmap.outlineWidth = 4;
            bitmap.textColor = color;
            bitmap.drawText(text, x, y, textWidth, textHeight, 'center');
        }

        // 🚀 NEW: Super efficient single-sprite animation (replaces 19-sprite system!)
        _startBonusAnimation() {
            let animationFrame = 0;
            const totalFrames = 78; // 24 enter + 30 pause + 24 exit
            const enterFrames = 24;
            const pauseFrames = 30;
            const exitFrames = 24;

            const animate = () => {
                if (animationFrame >= totalFrames) {
                    this._cleanupBonusAnimation();
                    return;
                }

                let phase, progress;

                if (animationFrame < enterFrames) {
                    // Enter phase: scale up and fade in
                    progress = animationFrame / enterFrames;
                    const easeProgress = Math.sin((progress * Math.PI) / 2); // Smooth ease-in
                    this.bonusTextSprite.scale.x = 0.5 + 0.5 * easeProgress;
                    this.bonusTextSprite.scale.y = 0.5 + 0.5 * easeProgress;
                    this.bonusTextSprite.opacity = 255 * easeProgress;
                } else if (animationFrame < enterFrames + pauseFrames) {
                    // Pause phase: gentle pulsing
                    phase = (animationFrame - enterFrames) * 0.15;
                    const pulse = 1.0 + Math.sin(phase) * 0.1;
                    this.bonusTextSprite.scale.x = pulse;
                    this.bonusTextSprite.scale.y = pulse;
                    this.bonusTextSprite.opacity = 255;
                } else {
                    // Exit phase: scale down and fade out
                    progress = (animationFrame - enterFrames - pauseFrames) / exitFrames;
                    const easeProgress = Math.sin((progress * Math.PI) / 2);
                    this.bonusTextSprite.scale.x = 1.0 - 0.5 * easeProgress;
                    this.bonusTextSprite.scale.y = 1.0 - 0.5 * easeProgress;
                    this.bonusTextSprite.opacity = 255 * (1 - easeProgress);
                }

                // Subtle floating effect
                this.bonusTextSprite.y = Graphics.height * 0.5 + Math.sin(animationFrame * 0.2) * 5;

                animationFrame++;
                this._currentBonusAnimation = requestAnimationFrame(animate);
            };

            animate();
        }

        // 🚀 OPTIMIZED: Super simple cleanup (no more sprite pool management!)
        _cleanupBonusAnimation() {
            // Reset sprite to hidden state
            this.bonusTextSprite.opacity = 0;
            this.bonusTextSprite.scale.x = 1.0;
            this.bonusTextSprite.scale.y = 1.0;
            this.bonusTextSprite.bitmap.clear();

            this._isBonusAnimating = false;

            // Process queued animations
            if (this._bonusQueue.length > 0) {
                this._bonusQueue.shift();
                setTimeout(() => this.showBonusText(), 100);
            }
        }

        clear() {
            if (this.comboSprite && this.comboSprite.parent) {
                this.comboSprite.parent.removeChild(this.comboSprite);
            }
            if (this.starsSprite && this.starsSprite.parent) {
                this.starsSprite.parent.removeChild(this.starsSprite);
            }
            if (this.bonusTextSprite && this.bonusTextSprite.parent) {
                this.bonusTextSprite.parent.removeChild(this.bonusTextSprite);
            }

            // Clean up caches to prevent memory leaks
            if (this._gradientCache) this._gradientCache.clear();
            if (this._colorCache) this._colorCache.clear();
            if (this._preCalculatedPaths) this._preCalculatedPaths.clear();
        }
    }

    // Scene integration
    const _Scene_Battle_create = Scene_Battle.prototype.create;
    Scene_Battle.prototype.create = function () {
        _Scene_Battle_create.call(this);
        this.comboManager = new ComboManager();
        this.comboDisplay = new ComboDisplay(this, this.comboManager);
        this.comboManager.saveOriginalAgility();
        this.comboManager.recordPartyHP();
    };

    const _Scene_Battle_update = Scene_Battle.prototype.update;
    Scene_Battle.prototype.update = function () {
        _Scene_Battle_update.call(this);
        if (this.comboManager && this.comboDisplay) {
            this.comboManager.checkPartyHPChange();
            this.comboDisplay.update();
        }
    };

    const _Scene_Battle_terminate = Scene_Battle.prototype.terminate;
    Scene_Battle.prototype.terminate = function () {
        if (this.comboManager) {
            this.comboManager.resetAgility();
        }
        if (this.comboDisplay) {
            this.comboDisplay.clear();
        }
        _Scene_Battle_terminate.call(this);
    };

    // Action hooks
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function (target) {
        _Game_Action_apply.call(this, target);

        // Only process combos for successful hits that deal actual damage
        if (target.isEnemy() && this.subject().isActor()) {
            const result = target.result();
            const damageDealt = result.hpDamage || 0;

            // Check if attack hit and dealt positive damage (not missed, evaded, or blocked)
            if (result.isHit() && !result.missed && !result.evaded && !result.blocked && damageDealt > 0) {
                this.processCombo(target, damageDealt);
            }
        }
    };

    // Only called when actual damage has been dealt (not on misses or negated damage)
    Game_Action.prototype.processCombo = function (target, damageDealt) {
        const scene = SceneManager._scene;
        if (!scene.comboManager || !scene.comboDisplay) return;

        scene.comboManager.updateCombo(damageDealt);

        const starCount = scene.comboManager.getStarCount();
        const bonusChance = starCount * DEFAULT_CONFIG.bonusChancePerStar;

        if (Math.random() < bonusChance) {
            this.processBonusAction(target);
            scene.comboDisplay.showBonusText();
        }
    };

    Game_Action.prototype.processBonusAction = function (target) {
        const halfDamage = Math.ceil(target.result().hpDamage / 2);

        const applyDamage = () => {
            target.gainHp(-halfDamage);
            target.startDamagePopup();
            target.performDamage();

            if (target.isDead()) {
                target.performCollapse();
            }
        };

        applyDamage();
        setTimeout(applyDamage, 300);
    };

    // OPTIMIZED: Helper for green-yellow-red gradient with caching
    const gradientColorCache = new Map();
    function comboGradientColor(comboCount, maxCombo) {
        // Check cache first
        const cacheKey = `${comboCount}_${maxCombo}`;
        if (gradientColorCache.has(cacheKey)) {
            return gradientColorCache.get(cacheKey);
        }

        // Manage cache size
        if (gradientColorCache.size >= 50) {
            const firstKey = gradientColorCache.keys().next().value;
            gradientColorCache.delete(firstKey);
        }

        // x1.0 (comboCount=0) = green, x5.0 (comboCount=4.0) = yellow, x9.9 (comboCount=maxCombo) = red
        const midCombo = 4.0; // x5.0
        let colorRGB;

        if (comboCount <= midCombo) {
            const t = comboCount / midCombo;
            colorRGB = [Math.round(255 * t), Math.round(128 + 127 * t), 0];
        } else {
            const t = (comboCount - midCombo) / (maxCombo - midCombo);
            colorRGB = [255, Math.round(255 * (1 - t)), 0];
        }

        const result = `rgba(${colorRGB[0]},${colorRGB[1]},${colorRGB[2]},1.0)`;
        gradientColorCache.set(cacheKey, result);
        return result;
    }
})();
