/*:
 * @plugindesc v2.0 - Enhanced Total Damage Display with Optimized Lightning Effects
 * <AUTHOR> of Cascadia
 * @version 2.0.0
 * @url
 * @help DisplayTotalDamage2.js
 *
 * Displays total damage with hit counter and realistic lightning visual effects.
 * Optimized for performance with advanced caching and natural lightning simulation.
 *
 * @param duration
 * @text Display Duration
 * @type number
 * @min 1
 * @max 10
 * @desc Duration in seconds for the damage display
 * @default 2
 *
 * @param yPosition
 * @text Y Position
 * @type number
 * @min 0.1
 * @max 0.9
 * @decimals 2
 * @desc Vertical position as a percentage of screen height (0.1 = 10% from top, 0.9 = 90% from top)
 * @default 0.35
 */

(() => {
    'use strict';

    // === PLUGIN CONSTANTS ===
    const PLUGIN_NAME = 'DisplayTotalDamage2';

    // Parse plugin parameters with validation
    const parameters = PluginManager.parameters(PLUGIN_NAME);
    const displayDuration = Math.max(1, parseInt(parameters.duration || 2)) * 60;
    const yPosition = 0.25;
    const baseFontSize = 40;
    const maxDamageCap = 10000;

    // === PERFORMANCE CONSTANTS ===
    const PERFORMANCE = {
        maxFPS: 60,
        minFrameTime: 16, // ~60fps
        lightningUpdateInterval: 2, // Update lightning every 2 frames
        cacheTimeout: 1000, // Cache timeout in ms
        // Adaptive performance scaling
        heavyLoadThreshold: 45, // FPS threshold for heavy load detection
        lightningLOD: {
            high: { segments: { min: 6, max: 12 }, branches: { min: 2, max: 4 } },
            medium: { segments: { min: 4, max: 8 }, branches: { min: 2, max: 3 } },
            low: { segments: { min: 3, max: 6 }, branches: { min: 1, max: 2 } },
        },
        damageGroupingWindow: 100, // ms to group rapid damage
    };

    // === MATH OPTIMIZATION CONSTANTS ===
    const MATH_CONSTANTS = {
        PI2: Math.PI * 2,
        PI_HALF: Math.PI / 2,
        PI_QUARTER: Math.PI / 4,
        PI_SIXTH: Math.PI / 6,
        // Pre-calculated timing multipliers
        HEALING_BREATHE_SPEED: 0.006,
        CRIT_PULSE_SPEED: 0.015,
        MILESTONE_PULSE_SPEED: 0.012,
        LIGHTNING_PULSE_SPEED: 0.01,
        HIT_COUNTER_PULSE_SPEED: 0.01,
        TEXT_PULSE_SPEED: 0.008,
        // Common fractions
        ONE_THIRD: 1 / 3,
        TWO_THIRDS: 2 / 3,
        QUARTER: 0.25,
        HALF: 0.5,
        THREE_QUARTERS: 0.75,
    };

    // === STRING OPTIMIZATION ===
    const STRING_CACHE = {
        DAMAGE: 'Damage',
        HEALING: 'Healing',
        HIT_PREFIX: 'Hit x',
        STAR_PREFIX: '★ Hit x',
        STAR_SUFFIX: ' ★',
        LIGHTNING_PREFIX: '⚡ Hit x',
        LIGHTNING_SUFFIX: ' ⚡',
        RGBA_PREFIX: 'rgba(',
        RGBA_SUFFIX: ', 1.0)',
    };

    // === ENHANCED LIGHTNING CONFIG ===
    const LIGHTNING_CONFIG = {
        // Main branch settings (more natural, fewer branches)
        mainBranches: { min: 2, max: 3 },
        branchLength: { min: 60, max: 150 },
        branchAngleSpread: Math.PI / 8, // 22.5 degrees (tighter spread)

        // Sub-branch settings (reduced for cleaner look)
        subBranches: { probability: 0.2, maxPerBranch: 1 },
        subBranchLength: { min: 15, max: 40 },
        subBranchAngle: Math.PI / 6, // 30 degrees (tighter)

        // Segment settings (more natural variation)
        segments: { min: 4, max: 8 },
        segmentDeviation: 12, // Slightly less jagged

        // Visual settings (more subtle and natural)
        thickness: { base: 2.0, taper: 0.9 },
        glow: { enabled: true, radius: 6, intensity: 0.4 },
        flicker: { enabled: true, intensity: 0.3, speed: 0.08 },

        // Color settings with intensity-based variations
        color: {
            base: { r: 255, g: 255, b: 100 },
            tip: { r: 255, g: 200, b: 255 },
            whiten: 0.4,
            // Intensity-based color themes (no performance cost)
            themes: {
                low: { base: { r: 200, g: 200, b: 255 }, tip: { r: 150, g: 150, b: 255 } }, // Blue for low damage
                medium: { base: { r: 255, g: 255, b: 100 }, tip: { r: 255, g: 200, b: 255 } }, // Yellow (default)
                high: { base: { r: 255, g: 150, b: 50 }, tip: { r: 255, g: 100, b: 100 } }, // Orange-red for high damage
                critical: { base: { r: 255, g: 50, b: 50 }, tip: { r: 255, g: 255, b: 255 } }, // Red-white for critical
            },
        },

        // Animation settings (more natural timing)
        lifetime: 6, // frames (shorter for less visual clutter)
        fadeOut: true,
    };

    // === UTILITY CLASSES ===

    /**
     * Optimized color utility class with caching
     */
    class ColorUtils {
        static _cache = new Map();

        /**
         * Convert RGBA string to RGB object with caching
         */
        static parseRGBA(rgbaString) {
            if (this._cache.has(rgbaString)) {
                return this._cache.get(rgbaString);
            }

            const match = rgbaString.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
            const result = match
                ? {
                      r: parseInt(match[1]),
                      g: parseInt(match[2]),
                      b: parseInt(match[3]),
                  }
                : { r: 255, g: 255, b: 255 };

            this._cache.set(rgbaString, result);
            return result;
        }

        /**
         * Interpolate between two colors
         */
        static lerp(color1, color2, t) {
            return {
                r: Math.round(color1.r + (color2.r - color1.r) * t),
                g: Math.round(color1.g + (color2.g - color1.g) * t),
                b: Math.round(color1.b + (color2.b - color1.b) * t),
            };
        }

        /**
         * Convert RGB object to RGBA string
         */
        static toRGBA(color, alpha = 1) {
            return `rgba(${color.r},${color.g},${color.b},${alpha})`;
        }

        /**
         * Shift hue of a color (optimized version)
         */
        static shiftHue(rgbColor, degrees) {
            const { r, g, b } = rgbColor;
            const max = Math.max(r, g, b) / 255;
            const min = Math.min(r, g, b) / 255;
            const delta = max - min;

            if (delta === 0) return rgbColor; // Grayscale, no hue to shift

            let h = 0;
            if (max === r / 255) h = ((g / 255 - b / 255) / delta) % 6;
            else if (max === g / 255) h = (b / 255 - r / 255) / delta + 2;
            else h = (r / 255 - g / 255) / delta + 4;

            h = ((h * 60 + degrees + 360) % 360) / 360;
            const s = delta / max;
            const l = (max + min) / 2;

            // Convert back to RGB
            const c = (1 - Math.abs(2 * l - 1)) * s;
            const x = c * (1 - Math.abs(((h * 6) % 2) - 1));
            const m = l - c / 2;

            let rNew, gNew, bNew;
            if (h < 1 / 6) [rNew, gNew, bNew] = [c, x, 0];
            else if (h < 2 / 6) [rNew, gNew, bNew] = [x, c, 0];
            else if (h < 3 / 6) [rNew, gNew, bNew] = [0, c, x];
            else if (h < 4 / 6) [rNew, gNew, bNew] = [0, x, c];
            else if (h < 5 / 6) [rNew, gNew, bNew] = [x, 0, c];
            else [rNew, gNew, bNew] = [c, 0, x];

            return {
                r: Math.round((rNew + m) * 255),
                g: Math.round((gNew + m) * 255),
                b: Math.round((bNew + m) * 255),
            };
        }
    }

    /**
     * Enhanced Performance monitoring utility with adaptive scaling
     */
    class PerformanceMonitor {
        static _frameTime = 0;
        static _lastFrame = 0;
        static _fps = 60;
        static _fpsHistory = [];
        static _heavyLoadMode = false;

        static update() {
            const now = performance.now();
            this._frameTime = now - this._lastFrame;
            this._lastFrame = now;
            this._fps = 1000 / this._frameTime;

            // Track FPS history for adaptive scaling
            this._fpsHistory.push(this._fps);
            if (this._fpsHistory.length > 10) this._fpsHistory.shift();

            // Detect heavy load conditions
            const avgFPS = this._fpsHistory.reduce((a, b) => a + b, 0) / this._fpsHistory.length;
            this._heavyLoadMode = avgFPS < PERFORMANCE.heavyLoadThreshold;
        }

        static shouldSkipFrame(interval = 1) {
            const adaptiveInterval = this._heavyLoadMode ? interval * 2 : interval;
            return (
                Graphics.frameCount %
                    Math.max(1, Math.ceil(adaptiveInterval * (60 / this._fps))) !==
                0
            );
        }

        static getFPS() {
            return this._fps;
        }

        static isHeavyLoad() {
            return this._heavyLoadMode;
        }

        static getLightningLOD() {
            if (this._fps > 50) return PERFORMANCE.lightningLOD.high;
            if (this._fps > 35) return PERFORMANCE.lightningLOD.medium;
            return PERFORMANCE.lightningLOD.low;
        }
    }
    /**
     * Object pool for lightning points to reduce garbage collection
     */
    class ObjectPool {
        static _pointPool = [];
        static _pathPool = [];

        static getPoint(x = 0, y = 0) {
            const point = this._pointPool.pop() || { x: 0, y: 0 };
            point.x = x;
            point.y = y;
            return point;
        }

        static releasePoint(point) {
            this._pointPool.push(point);
        }

        static getPath() {
            const path = this._pathPool.pop() || { points: [], thickness: 0 };
            path.points.length = 0;
            path.thickness = 0;
            return path;
        }

        static releasePath(path) {
            // Return points to pool
            for (const point of path.points) {
                this.releasePoint(point);
            }
            path.points.length = 0;
            this._pathPool.push(path);
        }
    }

    /**
     * Enhanced Lightning Generator with natural branching and object pooling
     */
    class LightningGenerator {
        static _pathCache = new Map();
        static _cacheTimeout = 100; // frames

        /**
         * Generate realistic lightning paths with branching
         */
        static generatePaths(centerX, centerY, intensity, targetAreas = []) {
            const cacheKey = `${centerX}_${centerY}_${intensity}_${Graphics.frameCount}`;

            // Use cached paths if available and recent
            if (this._pathCache.has(cacheKey)) {
                return this._pathCache.get(cacheKey);
            }

            // Use adaptive LOD based on performance
            const lodConfig = PerformanceMonitor.getLightningLOD();
            const config = { ...LIGHTNING_CONFIG, ...lodConfig };
            const paths = [];

            // Calculate number of main branches based on intensity and performance
            const branchCount = Math.floor(
                config.branches.min + (config.branches.max - config.branches.min) * intensity
            );

            // Generate main branches
            for (let i = 0; i < branchCount; i++) {
                const angle =
                    (Math.PI * 2 * i) / branchCount +
                    (Math.random() - 0.5) * config.branchAngleSpread;

                const branch = this._generateBranch(
                    centerX,
                    centerY,
                    angle,
                    intensity,
                    config,
                    targetAreas
                );

                if (branch.points.length > 1) {
                    paths.push(branch);

                    // Generate sub-branches for more natural look
                    if (Math.random() < config.subBranches.probability) {
                        const subBranches = this._generateSubBranches(branch, config, intensity);
                        paths.push(...subBranches);
                    }
                }
            }

            // Cache the result
            this._pathCache.set(cacheKey, paths);

            // Clean old cache entries
            if (this._pathCache.size > 50) {
                const oldKeys = Array.from(this._pathCache.keys()).slice(0, 10);
                oldKeys.forEach(key => this._pathCache.delete(key));
            }

            return paths;
        }

        /**
         * Generate a single lightning branch
         */
        static _generateBranch(startX, startY, angle, intensity, config, targetAreas) {
            const points = [ObjectPool.getPoint(startX, startY)];
            const maxLength =
                config.branchLength.min +
                (config.branchLength.max - config.branchLength.min) * intensity;
            const segments = Math.floor(
                config.segments.min + (config.segments.max - config.segments.min) * intensity
            );

            let currentX = startX;
            let currentY = startY;
            let currentAngle = angle;
            let remainingLength = maxLength;

            for (let i = 0; i < segments && remainingLength > 0; i++) {
                const segmentLength = Math.min(
                    remainingLength / (segments - i),
                    maxLength / segments
                );

                // Add natural deviation
                const deviation = (Math.random() - 0.5) * config.segmentDeviation;
                currentAngle += deviation * 0.1;

                // Calculate next point
                currentX += Math.cos(currentAngle) * segmentLength;
                currentY += Math.sin(currentAngle) * segmentLength;

                // Keep within screen bounds
                currentX = Math.max(10, Math.min(Graphics.width - 10, currentX));
                currentY = Math.max(10, Math.min(Graphics.height - 10, currentY));

                points.push(ObjectPool.getPoint(currentX, currentY));
                remainingLength -= segmentLength;

                // Attract to target areas if specified
                if (targetAreas.length > 0 && Math.random() < 0.3) {
                    const nearestTarget = this._findNearestTarget(currentX, currentY, targetAreas);
                    if (nearestTarget) {
                        const attractionAngle = Math.atan2(
                            nearestTarget.y - currentY,
                            nearestTarget.x - currentX
                        );
                        currentAngle = this._lerpAngle(currentAngle, attractionAngle, 0.3);
                    }
                }
            }

            return {
                points,
                thickness: config.thickness.base * (0.8 + intensity * 0.4),
                intensity: intensity,
                age: 0,
                maxAge: config.lifetime,
            };
        }

        /**
         * Generate sub-branches from a main branch
         */
        static _generateSubBranches(mainBranch, config, intensity) {
            const subBranches = [];
            const maxSubBranches = Math.floor(config.subBranches.maxPerBranch * intensity);

            for (let i = 0; i < maxSubBranches; i++) {
                // Pick a random point along the main branch (not too close to start/end)
                const branchPointIndex = Math.floor(
                    mainBranch.points.length * (0.3 + Math.random() * 0.4)
                );
                const branchPoint = mainBranch.points[branchPointIndex];

                // Calculate branch angle (perpendicular to main branch direction)
                const prevPoint = mainBranch.points[Math.max(0, branchPointIndex - 1)];
                const nextPoint =
                    mainBranch.points[Math.min(mainBranch.points.length - 1, branchPointIndex + 1)];
                const mainAngle = Math.atan2(nextPoint.y - prevPoint.y, nextPoint.x - prevPoint.x);
                const branchAngle =
                    mainAngle +
                    (Math.random() < 0.5 ? 1 : -1) *
                        (Math.PI / 4 + Math.random() * config.subBranchAngle);

                const subBranch = this._generateBranch(
                    branchPoint.x,
                    branchPoint.y,
                    branchAngle,
                    intensity * 0.6, // Sub-branches are less intense
                    {
                        ...config,
                        branchLength: config.subBranchLength,
                        segments: { min: 3, max: 6 },
                    },
                    []
                );

                if (subBranch.points.length > 1) {
                    subBranch.thickness *= 0.6; // Thinner sub-branches
                    subBranches.push(subBranch);
                }
            }

            return subBranches;
        }

        /**
         * Find nearest target area for lightning attraction
         */
        static _findNearestTarget(x, y, targetAreas) {
            let nearest = null;
            let minDistance = Infinity;

            for (const area of targetAreas) {
                const centerX = area.x + area.width / 2;
                const centerY = area.y + area.height / 2;
                const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);

                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = { x: centerX, y: centerY };
                }
            }

            return nearest;
        }

        /**
         * Interpolate between two angles
         */
        static _lerpAngle(angle1, angle2, t) {
            const diff = angle2 - angle1;
            const shortestDiff = ((diff + Math.PI) % (2 * Math.PI)) - Math.PI;
            return angle1 + shortestDiff * t;
        }
    }

    /**
     * Enhanced Lightning Renderer with realistic effects
     */
    class LightningRenderer {
        /**
         * Render lightning paths with advanced visual effects
         */
        static render(ctx, paths, baseColor, intensity) {
            if (!paths || paths.length === 0) return;

            PerformanceMonitor.update();

            const config = LIGHTNING_CONFIG;
            const parsedColor = ColorUtils.parseRGBA(baseColor);

            ctx.save();
            ctx.lineCap = 'round';
            ctx.lineJoin = 'round';
            ctx.globalCompositeOperation = 'screen'; // Additive blending for glow

            // Render each path
            for (let pathIndex = 0; pathIndex < paths.length; pathIndex++) {
                const path = paths[pathIndex];
                this._renderPath(ctx, path, parsedColor, intensity, config);
            }

            ctx.restore();
        }

        /**
         * Render a single lightning path
         */
        static _renderPath(ctx, path, baseColor, intensity, config) {
            if (path.points.length < 2) return;

            // Apply flicker effect
            const flickeredPoints = this._applyFlicker(path.points, intensity);

            // Render glow effect first (if enabled)
            if (config.glow.enabled) {
                this._renderGlow(ctx, flickeredPoints, baseColor, path.thickness, config.glow);
            }

            // Render main lightning bolt
            this._renderMainBolt(
                ctx,
                flickeredPoints,
                baseColor,
                path.thickness,
                intensity,
                config
            );
        }

        /**
         * Apply realistic flicker to lightning points
         */
        static _applyFlicker(points, intensity) {
            if (!LIGHTNING_CONFIG.flicker.enabled) return points;

            const flickerIntensity = LIGHTNING_CONFIG.flicker.intensity * intensity;
            const time = Date.now() * LIGHTNING_CONFIG.flicker.speed;

            return points.map((point, index) => {
                if (index === 0 || index === points.length - 1) {
                    return point; // Don't flicker start/end points
                }

                const noise = Math.sin(time + index * 0.5) * flickerIntensity;
                const randomOffset = (Math.random() - 0.5) * flickerIntensity * 10;

                return {
                    x: point.x + noise * 5 + randomOffset,
                    y: point.y + noise * 3 + randomOffset * 0.5,
                };
            });
        }

        /**
         * Render glow effect around lightning
         */
        static _renderGlow(ctx, points, baseColor, thickness, glowConfig) {
            ctx.save();
            ctx.globalAlpha = glowConfig.intensity;
            ctx.shadowColor = ColorUtils.toRGBA(baseColor, 0.8);
            ctx.shadowBlur = glowConfig.radius;
            ctx.strokeStyle = ColorUtils.toRGBA(baseColor, 0.3);
            ctx.lineWidth = thickness * 3;

            this._drawPath(ctx, points);

            ctx.restore();
        }

        /**
         * Render main lightning bolt with color gradient
         */
        static _renderMainBolt(ctx, points, baseColor, thickness, intensity, config) {
            const tipColor = ColorUtils.lerp(baseColor, config.color.tip, intensity);

            for (let i = 1; i < points.length; i++) {
                const t = i / (points.length - 1);
                const segmentColor = ColorUtils.lerp(baseColor, tipColor, t);
                const whiteColor = ColorUtils.lerp(
                    segmentColor,
                    { r: 255, g: 255, b: 255 },
                    config.color.whiten
                );

                // Calculate thickness with taper
                const segmentThickness =
                    thickness * (1 - t * config.thickness.taper) * (0.8 + Math.random() * 0.4);

                // Calculate alpha with fade
                const alpha = (1 - t * 0.3) * (0.9 + Math.random() * 0.1);

                ctx.strokeStyle = ColorUtils.toRGBA(whiteColor, alpha);
                ctx.lineWidth = segmentThickness;

                ctx.beginPath();
                ctx.moveTo(points[i - 1].x, points[i - 1].y);
                ctx.lineTo(points[i].x, points[i].y);
                ctx.stroke();
            }
        }

        /**
         * Helper to draw a path through points
         */
        static _drawPath(ctx, points) {
            if (points.length < 2) return;

            ctx.beginPath();
            ctx.moveTo(points[0].x, points[0].y);

            for (let i = 1; i < points.length; i++) {
                ctx.lineTo(points[i].x, points[i].y);
            }

            ctx.stroke();
        }
    }

    /**
     * Enhanced Total Damage Sprite with optimized lightning effects
     */
    class TotalDamageSprite extends Sprite {
        constructor() {
            super();
            this.initialize.apply(this, arguments);

            // Lightning system
            this._lightningActive = false;
            this._lightningPaths = [];
            this._lightningUpdateTimer = 0;
            this._lastLightningUpdate = 0;

            // Performance tracking
            this._lastFrameTime = 0;
            this._frameSkipCounter = 0;
        }

        initialize() {
            super.initialize();

            // Core damage tracking
            this._totalDamage = 0;
            this._displayedDamage = 0;
            this._duration = displayDuration;
            this._isHealing = false;
            this._exploitWeakness = false;
            this._hitCount = 0;

            // Visual effects
            this._lastMilestone = 0;
            this._milestoneEffect = 0;
            this._popEffect = 0;
            this._critFlashTimer = 0;

            // Smart damage grouping for performance
            this._pendingDamage = 0;
            this._lastDamageTime = 0;
            this._damageGroupTimer = 0;

            // Animation
            this._rollStartValue = 0;
            this._rollTargetValue = 0;
            this._rollFrame = 0;
            this._rollDuration = 120;

            // Display properties
            this._displayedBarWidth = 0;
            this._targetBarWidth = 0;

            // Screen tracking
            this._lastWidth = Graphics.width;
            this._lastHeight = Graphics.height;
            this._needsRefresh = true;
            this._lastRefreshTime = 0;

            // Positioning
            this.anchor.x = 0.5;
            this.anchor.y = 0.5;
            this.x = Graphics.width / 2;
            this.y = Graphics.height * yPosition;
            this.opacity = 0;
            this.visible = true;
            this.scale.x = 1.0;
            this.scale.y = 1.0;

            this.createBitmap();
        }

        createBitmap() {
            if (this.bitmap) {
                this.bitmap.destroy();
            }
            const width = Graphics.width;
            const extraLightningPad = 40;
            const verticalPadding = 18 + extraLightningPad;
            const height = 144 + verticalPadding * 2;
            this._verticalPadding = verticalPadding;
            this._extraLightningPad = extraLightningPad;
            this.bitmap = new Bitmap(width, height);
            this.bitmap.fontSize = baseFontSize;
            this.bitmap.fontFace = $gameSystem.mainFontFace();
            this.bitmap.smooth = true;
            // Enhanced caching system
            this._barCache = null;
            this._barCacheParams = { width: 0, height: 0, color: '' };
            this._textCache = null;
            this._textCacheParams = { text: '', fontSize: 0, color: '' };
            this._lastCacheFrame = -1;
        }

        update() {
            super.update();

            // Performance monitoring
            PerformanceMonitor.update();
            const now = performance.now();
            const deltaTime = now - this._lastFrameTime;
            this._lastFrameTime = now;

            // Handle screen resize
            if (this._lastWidth !== Graphics.width || this._lastHeight !== Graphics.height) {
                this._lastWidth = Graphics.width;
                this._lastHeight = Graphics.height;
                this.createBitmap();
                this._needsRefresh = true;
            }

            if (!this.bitmap) {
                this.createBitmap();
                return;
            }

            // Frame rate limiting for performance
            if (deltaTime < PERFORMANCE.minFrameTime) {
                this._frameSkipCounter++;
                if (this._frameSkipCounter < 2) return; // Skip every other frame if running too fast
            }
            this._frameSkipCounter = 0;

            // Process grouped damage
            if (this._damageGroupTimer > 0) {
                this._damageGroupTimer--;
                if (this._damageGroupTimer === 0 && this._pendingDamage > 0) {
                    // Apply grouped damage
                    this._totalDamage += this._pendingDamage;
                    this._pendingDamage = 0;
                    this._lightningActive = true;
                    this._updateLightningPaths();
                }
            }

            // Main update logic
            if (this._duration > 0 || this.opacity > 0) {
                if (this._duration > 0) {
                    this._duration--;
                }

                const diff = this._totalDamage - this._displayedDamage;
                let needsRefresh = false;

                // Optimized roll-up calculation with better performance
                if (Math.abs(diff) > 1) {
                    const step = Math.max(1, Math.ceil(Math.abs(diff) * 0.5)); // Faster roll-up
                    this._displayedDamage += Math.sign(diff) * step;
                    needsRefresh = true;

                    // Check for milestone effects
                    const currentMilestone = Math.floor(this._displayedDamage / 1000) * 1000;
                    if (currentMilestone > this._lastMilestone) {
                        this._lastMilestone = currentMilestone;
                        this._milestoneEffect = 8; // Longer effect
                    }
                } else if (diff !== 0) {
                    this._displayedDamage = this._totalDamage;
                    needsRefresh = true;
                    this._lightningActive = false;
                }

                // Update visual effects
                if (this._milestoneEffect > 0) {
                    this._milestoneEffect--;
                    needsRefresh = true;
                }

                if (this._popEffect > 0) {
                    this._popEffect--;
                    const progress = this._popEffect / 8; // Longer pop effect
                    const scale = 1.0 + 0.15 * Math.sin(progress * Math.PI); // Smoother scaling
                    this.scale.x = scale;
                    this.scale.y = scale;
                } else {
                    this.scale.x = 1.0;
                    this.scale.y = 1.0;
                }

                // Enhanced screen shake for critical hits
                if (this._exploitWeakness) {
                    const shakeIntensity = 2.0;
                    const shakeFreq = Date.now() * 0.01;
                    const shakeX = Math.sin(shakeFreq) * shakeIntensity;
                    const shakeY = Math.cos(shakeFreq * 1.3) * shakeIntensity * 0.5;
                    this.x = Math.round(Graphics.width / 2 + shakeX);
                    this.y = Math.round(Graphics.height * yPosition + shakeY);
                } else {
                    this.x = Math.round(Graphics.width / 2);
                    this.y = Math.round(Graphics.height * yPosition);
                }

                // Smooth fade out
                const fadeStartFrame = 45;
                if (this._duration <= fadeStartFrame) {
                    this.opacity = Math.max(0, Math.floor((this._duration / fadeStartFrame) * 255));
                } else {
                    this.opacity = 255;
                }

                // Ensure final value is shown
                if (this._duration <= 0 && this._displayedDamage !== this._totalDamage) {
                    this._displayedDamage = this._totalDamage;
                    this.refresh();
                }

                if (this._critFlashTimer > 0) {
                    this._critFlashTimer--;
                    needsRefresh = true;
                }

                // Smooth bar width animation
                if (this._targetBarWidth !== undefined) {
                    const diff = this._targetBarWidth - this._displayedBarWidth;
                    if (Math.abs(diff) > 1) {
                        this._displayedBarWidth += diff * 0.2; // Smooth animation
                    } else {
                        this._displayedBarWidth = this._targetBarWidth;
                    }
                }

                // Enhanced lightning system
                const rolling = this._displayedDamage !== this._totalDamage;
                if (rolling && this._totalDamage > 0) {
                    this._lightningActive = true;

                    // Update lightning paths less frequently for performance
                    // Use internal counter instead of Graphics.frameCount for better performance
                    if (!this._lightningUpdateCounter) this._lightningUpdateCounter = 0;
                    this._lightningUpdateCounter++;

                    if (this._lightningUpdateCounter >= PERFORMANCE.lightningUpdateInterval) {
                        this._lightningUpdateCounter = 0;
                        this._updateLightningPaths();
                    }
                } else {
                    this._lightningActive = false;
                    this._lightningPaths = [];
                }

                // Intelligent refresh logic
                const shouldRefresh =
                    needsRefresh ||
                    (this._lightningActive && !PerformanceMonitor.shouldSkipFrame(2));

                if (shouldRefresh) {
                    this.refresh();
                }

                this._lastRefreshTime = now;
            } else {
                // Fade out gracefully
                if (this.opacity > 0) {
                    this.opacity = Math.max(0, this.opacity - 12);
                } else {
                    if (this._pendingFade) {
                        this.resetCount();
                    }
                }
            }

            // Reset when fully faded
            if (this._pendingReset && this.opacity <= 0) {
                this.resetCount();
                this._pendingReset = false;
            }

            // Clean up when invisible
            if (this.opacity <= 0) {
                this._lightningPaths = [];
            }
        }

        /**
         * Update lightning paths for current damage state
         */
        _updateLightningPaths() {
            if (!this._lightningActive || this._totalDamage <= 0) {
                this._lightningPaths = [];
                return;
            }

            const intensity = Math.min(1, this._totalDamage / maxDamageCap);
            // Center lightning within the damage display rectangle
            const centerX = this.bitmap.width / 2;
            const padY = this._verticalPadding || 0;
            const centerY = padY + 42; // Center within the text/bar area

            // Lightning will use gradientBorderColor for consistency

            // Define target areas for lightning attraction (bitmap-relative)
            const targetAreas = [];
            if (this._displayedBarWidth > 0) {
                const barX = (this.bitmap.width - this._displayedBarWidth) / 2;
                const barY = padY;
                targetAreas.push({
                    x: barX,
                    y: barY,
                    width: this._displayedBarWidth,
                    height: 60,
                });
            }

            // Clean up old paths before generating new ones
            if (this._lightningPaths) {
                for (const path of this._lightningPaths) {
                    ObjectPool.releasePath(path);
                }
            }

            // Generate new lightning paths
            this._lightningPaths = LightningGenerator.generatePaths(
                centerX,
                centerY,
                intensity,
                targetAreas
            );
        }

        /**
         * Get lightning color theme based on damage intensity and type
         */
        _getLightningColorTheme(intensity) {
            const themes = LIGHTNING_CONFIG.color.themes;

            if (this._exploitWeakness) {
                return themes.critical;
            } else if (intensity > 0.8) {
                return themes.high;
            } else if (intensity > 0.4) {
                return themes.medium;
            } else {
                return themes.low;
            }
        }

        /**
         * Smart damage grouping for performance during heavy action
         */
        _shouldGroupDamage(damage) {
            const now = Date.now();
            const timeSinceLastDamage = now - this._lastDamageTime;

            // Group damage if it's within the grouping window and we're in heavy load
            return (
                timeSinceLastDamage < PERFORMANCE.damageGroupingWindow &&
                PerformanceMonitor.isHeavyLoad() &&
                Math.abs(damage) < 1000
            ); // Don't group large damage numbers
        }

        /**
         * Add damage to the display with enhanced effects and smart grouping
         */
        addDamage(damage, isHealing = false, exploitWeakness = false) {
            if (!this.bitmap) {
                this.createBitmap();
            }

            const absValue = Math.abs(damage);
            if (absValue === 0) return;

            const now = Date.now();

            // Smart damage grouping during heavy load
            if (this._shouldGroupDamage(damage) && !exploitWeakness) {
                this._pendingDamage += absValue;
                this._damageGroupTimer = 5; // Frames to wait before applying
                this._lastDamageTime = now;
                return;
            }

            // Apply any pending grouped damage
            const totalDamage = absValue + this._pendingDamage;
            this._pendingDamage = 0;
            this._lastDamageTime = now;

            // Update damage state
            this._isHealing = isHealing;
            this._exploitWeakness = exploitWeakness;
            this._totalDamage += totalDamage;
            this._hitCount = Math.max(1, this._hitCount + 1);
            this._duration = displayDuration;
            this.opacity = 255;
            this.y = Graphics.height * yPosition; // Use plugin parameter instead of hardcoded value
            this._needsRefresh = true;

            // Enhanced visual effects
            this._popEffect = 8; // Longer pop effect

            if (this._exploitWeakness) {
                this._critFlashTimer = 20; // Longer flash for critical hits
            }

            // Update roll animation
            if (this._rollTargetValue !== this._totalDamage) {
                this._rollStartValue = this._displayedDamage;
                this._rollTargetValue = this._totalDamage;
                this._rollFrame = 0;
            }

            // Instant display for small damage
            if (absValue < 10) {
                this._displayedDamage = this._totalDamage;
            } else if (this._totalDamage > 0) {
                this._displayedDamage = this._displayedDamage || 0;
            }

            // Trigger lightning effect
            this._lightningActive = true;
            this._updateLightningPaths();
        }

        resetCount() {
            this._totalDamage = 0;
            this._displayedDamage = 0;
            this._hitCount = 0;
            this._isHealing = false;
            this._exploitWeakness = false;
            this._lastMilestone = 0;
            this._milestoneEffect = 0;
            this._popEffect = 0;
            this.scale.x = 1.0;
            this.scale.y = 1.0;
        }

        getFontSize() {
            const scaleFactor = Math.min(1 + this._totalDamage / maxDamageCap, 1.3);
            return Math.floor(baseFontSize * scaleFactor);
        }

        refresh() {
            // Early exits for maximum performance
            if (!this.bitmap) return;
            if (this._totalDamage === 0 && this._hitCount === 0) return;

            // Skip refresh if nothing changed and not in active animation
            const currentFrame = Graphics.frameCount;
            if (
                currentFrame === this._lastCacheFrame &&
                !this._lightningActive &&
                this._milestoneEffect === 0 &&
                this._popEffect === 0 &&
                this._critFlashTimer === 0
            ) {
                return;
            }
            this._lastCacheFrame = currentFrame;

            this.bitmap.clear();

            const damageValue = Math.round(this._displayedDamage).toLocaleString();
            // Enhanced hit counter with streak indicators (optimized string building)
            let hitText = '';
            if (this._hitCount > 0) {
                if (this._hitCount >= 10) {
                    hitText =
                        STRING_CACHE.LIGHTNING_PREFIX +
                        this._hitCount +
                        STRING_CACHE.LIGHTNING_SUFFIX;
                } else if (this._hitCount >= 5) {
                    hitText = STRING_CACHE.STAR_PREFIX + this._hitCount + STRING_CACHE.STAR_SUFFIX;
                } else {
                    hitText = STRING_CACHE.HIT_PREFIX + this._hitCount;
                }
            }
            const scale = Math.min(Math.max(this._totalDamage / 2000, 1), 1.3);
            const mainFontSize = Math.round(baseFontSize * scale);
            const subFontSize = Math.round(baseFontSize * 0.75);
            this.bitmap.fontSize = mainFontSize;
            const valueWidth = this.bitmap.measureTextWidth(damageValue);
            const valueHeight = this.bitmap.fontSize;
            this.bitmap.fontSize = subFontSize;
            const labelText = this._isHealing ? STRING_CACHE.HEALING : STRING_CACHE.DAMAGE;
            const labelWidth = this.bitmap.measureTextWidth(labelText);
            const spacing = 12;
            const totalWidth = valueWidth + spacing + labelWidth;
            const startX = (Graphics.width - totalWidth) / 2;
            const padY = this._verticalPadding || 0;

            if (this._totalDamage > 0) {
                const barPaddingX = 36;
                const barPaddingY = 6;
                const barWidth = totalWidth + barPaddingX * 2;
                this._targetBarWidth = barWidth;
                if (!this._displayedBarWidth || this._displayedBarWidth === 0)
                    this._displayedBarWidth = barWidth;
                const barHeight = valueHeight + subFontSize + barPaddingY * 2;
                const barX = (Graphics.width - this._displayedBarWidth) / 2;
                const barY = padY;
                const ctx = this.bitmap.context;
                let barColor = 'rgba(0,0,0,0.4)';
                let pulseIntensity = 1.0;

                // Dynamic bar color with pulsing effect for maximum visual punch
                if (this._isHealing) {
                    barColor = 'rgba(40,120,40,0.4)';
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.008) * 0.2; // Gentle healing pulse
                } else if (this._exploitWeakness) {
                    barColor = 'rgba(200,40,40,0.4)';
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.015) * 0.4; // Intense critical pulse
                } else if (this._milestoneEffect > 0) {
                    const border = this.gradientBorderColor();
                    const rgb = border.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                    if (rgb) {
                        const r = Math.floor(parseInt(rgb[1]) * 0.5);
                        const g = Math.floor(parseInt(rgb[2]) * 0.5);
                        const b = Math.floor(parseInt(rgb[3]) * 0.5);
                        barColor = `rgba(${r},${g},${b},0.4)`;
                    }
                    pulseIntensity = 1.0 + Math.sin(Date.now() * 0.012) * 0.3; // Milestone celebration pulse
                } else if (this._lightningActive) {
                    // Synchronized pulse with lightning and text effects
                    const syncTime = Date.now() * 0.01;
                    pulseIntensity = 1.0 + Math.sin(syncTime) * 0.1;
                }

                // Apply pulse intensity to bar opacity (zero performance cost)
                const baseOpacity = 0.4;
                const pulsedOpacity = Math.min(0.8, baseOpacity * pulseIntensity);
                barColor = barColor.replace('0.4)', `${pulsedOpacity.toFixed(2)})`);
                // --- Optimized solid color bar with caching ---
                if (
                    !this._barCache ||
                    this._barCacheParams.width !== this._displayedBarWidth ||
                    this._barCacheParams.height !== barHeight ||
                    this._barCacheParams.color !== barColor
                ) {
                    // Create or update the offscreen canvas
                    this._barCache = document.createElement('canvas');
                    this._barCache.width = this._displayedBarWidth;
                    this._barCache.height = barHeight;
                    const barCtx = this._barCache.getContext('2d');
                    const radius = 12;
                    barCtx.beginPath();
                    barCtx.moveTo(radius, 0);
                    barCtx.lineTo(this._displayedBarWidth - radius, 0);
                    barCtx.quadraticCurveTo(
                        this._displayedBarWidth,
                        0,
                        this._displayedBarWidth,
                        radius
                    );
                    barCtx.lineTo(this._displayedBarWidth, barHeight - radius);
                    barCtx.quadraticCurveTo(
                        this._displayedBarWidth,
                        barHeight,
                        this._displayedBarWidth - radius,
                        barHeight
                    );
                    barCtx.lineTo(radius, barHeight);
                    barCtx.quadraticCurveTo(0, barHeight, 0, barHeight - radius);
                    barCtx.lineTo(0, radius);
                    barCtx.quadraticCurveTo(0, 0, radius, 0);
                    barCtx.closePath();
                    barCtx.fillStyle = barColor;
                    barCtx.fill();
                    // Add 1px solid border using gradientBorderColor
                    barCtx.strokeStyle = this.gradientBorderColor();
                    barCtx.lineWidth = 1;
                    barCtx.stroke();
                    this._barCacheParams = {
                        width: this._displayedBarWidth,
                        height: barHeight,
                        color: barColor,
                    };
                }
                // Blit the cached bar to the main context
                ctx.drawImage(this._barCache, barX, barY);
                // --- End optimized solid color bar ---
                // --- Bar border flash sync with lightning ---
                let barBorderColor = this.gradientBorderColor();
                // Make the border darker by default
                const rgbDark = barBorderColor.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgbDark) {
                    const r = Math.floor(parseInt(rgbDark[1]) * 0.5);
                    const g = Math.floor(parseInt(rgbDark[2]) * 0.5);
                    const b = Math.floor(parseInt(rgbDark[3]) * 0.5);
                    barBorderColor = `rgba(${r},${g},${b},1)`;
                }
                if (this._lightningActive && this._borderFlashActive) {
                    // Flash: border is bright when lightning is redrawn
                    const rgb = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                    if (rgb) {
                        const r = Math.min(255, Math.floor(parseInt(rgb[1]) * 1.8 + 100));
                        const g = Math.min(255, Math.floor(parseInt(rgb[2]) * 1.8 + 100));
                        const b = Math.min(255, Math.floor(parseInt(rgb[3]) * 1.8 + 100));
                        barBorderColor = `rgba(${r},${g},${b},1)`;
                    }
                }
                // Draw the bar border (1px solid)
                ctx.save();
                ctx.strokeStyle = barBorderColor;
                ctx.lineWidth = 1;
                const radius = 12;
                ctx.beginPath();
                ctx.moveTo(barX + radius, barY);
                ctx.lineTo(barX + this._displayedBarWidth - radius, barY);
                ctx.quadraticCurveTo(
                    barX + this._displayedBarWidth,
                    barY,
                    barX + this._displayedBarWidth,
                    barY + radius
                );
                ctx.lineTo(barX + this._displayedBarWidth, barY + barHeight - radius);
                ctx.quadraticCurveTo(
                    barX + this._displayedBarWidth,
                    barY + barHeight,
                    barX + this._displayedBarWidth - radius,
                    barY + barHeight
                );
                ctx.lineTo(barX + radius, barY + barHeight);
                ctx.quadraticCurveTo(barX, barY + barHeight, barX, barY + barHeight - radius);
                ctx.lineTo(barX, barY + radius);
                ctx.quadraticCurveTo(barX, barY, barX + radius, barY);
                ctx.closePath();
                ctx.stroke();
                ctx.restore();
                // --- End bar border flash ---
                // --- Enhanced Lightning Rendering ---
                if (this._lightningActive && this._lightningPaths.length > 0) {
                    const intensity = Math.min(1, this._totalDamage / maxDamageCap);

                    // Use gradient border color for lightning to match damage display
                    const lightningColor = this.gradientBorderColor();

                    // Render lightning with consistent color system
                    LightningRenderer.render(ctx, this._lightningPaths, lightningColor, intensity);

                    this._borderFlashActive = true;
                } else {
                    this._borderFlashActive = false;
                }

                // --- End Enhanced Lightning ---
                // Draw main text
                this.bitmap.fontSize = mainFontSize;
                this.drawOutlinedText(damageValue, startX, padY, valueWidth, valueHeight, 'left');
                // Draw label
                this.bitmap.fontSize = subFontSize;
                const labelY = padY + valueHeight - subFontSize - 3;
                this.drawOutlinedText(
                    labelText,
                    startX + valueWidth + spacing,
                    labelY,
                    labelWidth,
                    subFontSize,
                    'left'
                );
            }
            // Draw hit counter with streak color coding
            if (this._hitCount > 0) {
                this.bitmap.fontSize = subFontSize;
                const hitWidth = this.bitmap.measureTextWidth(hitText);
                const hitY = padY + valueHeight + 6;

                // Get hit counter color based on streak
                const hitColor = this._getHitCounterColor();

                this.drawOutlinedTextWithColor(
                    hitText,
                    (Graphics.width - hitWidth) / 2,
                    hitY,
                    hitWidth,
                    subFontSize,
                    'center',
                    hitColor
                );
            }
        }

        /**
         * Get hit counter color based on streak level (zero performance cost)
         */
        _getHitCounterColor() {
            if (this._hitCount >= 10) {
                // High streak - pulsing gold (optimized)
                const now = Date.now();
                const pulse = 1.0 + Math.sin(now * MATH_CONSTANTS.HIT_COUNTER_PULSE_SPEED) * 0.2;
                const intensity = Math.floor(255 * pulse);
                const goldIntensity = Math.floor(intensity * 0.8);
                return (
                    STRING_CACHE.RGBA_PREFIX +
                    Math.min(255, intensity) +
                    ', ' +
                    goldIntensity +
                    ', 0' +
                    STRING_CACHE.RGBA_SUFFIX
                );
            } else if (this._hitCount >= 5) {
                // Medium streak - bright cyan
                return 'rgba(100, 255, 255, 1.0)';
            } else if (this._hitCount >= 3) {
                // Small streak - light blue
                return 'rgba(180, 220, 255, 1.0)';
            } else {
                // Normal - white
                return 'rgba(255, 255, 255, 1.0)';
            }
        }

        /**
         * Enhanced drawOutlinedText with custom color support
         */
        drawOutlinedTextWithColor(text, x, y, maxWidth, maxHeight, align, customColor = null) {
            const originalTextColor = this.bitmap.textColor;
            if (customColor) {
                this.bitmap.textColor = customColor;
            }
            this.drawOutlinedText(text, x, y, maxWidth, maxHeight, align);
            this.bitmap.textColor = originalTextColor;
        }

        drawOutlinedText(text, x, y, maxWidth, maxHeight, align) {
            // Use the same flash logic as the border for the color outline
            let colorOutline;
            if (this._borderFlashActive) {
                // Bright flash color
                const rgb = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgb) {
                    const r = Math.min(255, Math.floor(parseInt(rgb[1]) * 1.8 + 100));
                    const g = Math.min(255, Math.floor(parseInt(rgb[2]) * 1.8 + 100));
                    const b = Math.min(255, Math.floor(parseInt(rgb[3]) * 1.8 + 100));
                    colorOutline = `rgba(${r},${g},${b},1)`;
                } else {
                    colorOutline = this.gradientBorderColor();
                }
            } else {
                // Dark color
                const rgbDark = this.gradientBorderColor().match(/rgba?\((\d+),\s*(\d+),\s*(\d+)/);
                if (rgbDark) {
                    const r = Math.floor(parseInt(rgbDark[1]) * 0.5);
                    const g = Math.floor(parseInt(rgbDark[2]) * 0.5);
                    const b = Math.floor(parseInt(rgbDark[3]) * 0.5);
                    colorOutline = `rgba(${r},${g},${b},1)`;
                } else {
                    colorOutline = this.gradientBorderColor();
                }
            }
            // Draw colored outline (5px)
            this.bitmap.outlineWidth = 5;
            this.bitmap.outlineColor = colorOutline;
            this.bitmap.textColor = this.textColor();
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
            // Draw black outline (3px)
            this.bitmap.outlineWidth = 3;
            this.bitmap.outlineColor = 'rgba(0, 0, 0, 1.0)';
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
            // Draw filled text
            this.bitmap.outlineWidth = 0;
            this.bitmap.drawText(text, x, y, maxWidth, maxHeight, align);
        }

        textColor() {
            if (this._isHealing) {
                // Healing with subtle breathing effect using pre-calculated constant
                const breathe =
                    1.0 + Math.sin(Date.now() * MATH_CONSTANTS.HEALING_BREATHE_SPEED) * 0.1;
                const green = Math.floor(255 * breathe);
                return `rgba(176, ${Math.min(255, green)}, 144, 1.0)`;
            } else if (this._critFlashTimer > 0) {
                // Enhanced critical flash with multiple color phases
                const flashPhase = this._critFlashTimer / 20;
                if (flashPhase > 0.7) {
                    return 'rgba(255, 255, 255, 1.0)'; // White flash peak
                } else if (flashPhase > 0.4) {
                    return 'rgba(255, 230, 64, 1.0)'; // Yellow flash
                } else {
                    return 'rgba(255, 180, 100, 1.0)'; // Orange fade
                }
            } else {
                // Dynamic color based on damage intensity with smooth transitions
                const intensity = Math.min(1, this._totalDamage / maxDamageCap);
                if (intensity >= 0.8) {
                    // High damage - pulsing red
                    const pulse =
                        1.0 + Math.sin(Date.now() * MATH_CONSTANTS.TEXT_PULSE_SPEED) * 0.15;
                    const red = Math.floor(255 * pulse);
                    return `rgba(${Math.min(255, red)}, 200, 200, 1.0)`;
                } else if (intensity >= 0.5) {
                    // Medium-high damage - warm orange
                    return 'rgba(255, 220, 180, 1.0)';
                } else if (intensity >= 0.2) {
                    // Medium damage - soft yellow
                    return 'rgba(255, 255, 220, 1.0)';
                } else {
                    // Low damage - cool white
                    return 'rgba(240, 245, 255, 1.0)';
                }
            }
        }

        gradientBorderColor() {
            const progress = Math.min(this._totalDamage / maxDamageCap, 1);
            const red = progress < 0.5 ? Math.floor(510 * progress) : 255;
            const green = progress < 0.5 ? 255 : Math.floor(255 - 510 * (progress - 0.5));

            // Enhanced magical pulsing effect
            if (this._exploitWeakness || this._milestoneEffect > 0) {
                const basePulse = Math.sin(Date.now() / 100) * 0.2 + 0.8; // Increased pulse range
                let totalBoost = 0;

                if (this._exploitWeakness) totalBoost += 0.2;
                if (this._milestoneEffect > 0) totalBoost += 0.2;

                // Cap the maximum boost
                totalBoost = Math.min(totalBoost, 0.3);

                return `rgba(${red},${green},0,${basePulse + totalBoost})`;
            }
            return `rgba(${red},${green},0,0.9)`; // Increased base opacity
        }

        /**
         * Enhanced destroy method with proper cleanup
         */

        destroy() {
            // Clean up bitmap
            if (this.bitmap) {
                this.bitmap.destroy();
                this.bitmap = null;
            }

            // Clean up cached resources
            if (this._barCache) {
                this._barCache = null;
            }

            // Clear lightning paths
            this._lightningPaths = [];

            // Remove from parent
            if (this.parent) {
                this.parent.removeChild(this);
            }
        }
    }

    const _Scene_Battle_createAllWindows = Scene_Battle.prototype.createAllWindows;
    Scene_Battle.prototype.createAllWindows = function () {
        _Scene_Battle_createAllWindows.call(this);
        this.createTotalDamageWindow();
    };

    Scene_Battle.prototype.createTotalDamageWindow = function () {
        this._totalDamageSprite = new TotalDamageSprite();
        this.addChild(this._totalDamageSprite);
    };

    const _Game_Action_executeHpDamage = Game_Action.prototype.executeHpDamage;
    Game_Action.prototype.executeHpDamage = function (target, value) {
        _Game_Action_executeHpDamage.call(this, target, value);
        if (SceneManager._scene instanceof Scene_Battle) {
            const isHealing = value < 0;
            const exploitWeakness =
                this.isDrain() || this.item().damage.elementId < 0
                    ? false
                    : target.result().critical || this.calcElementRate(target) > 1;
            SceneManager._scene._totalDamageSprite.addDamage(value, isHealing, exploitWeakness);
        }
    };

    const _BattleManager_startAction = BattleManager.startAction;
    BattleManager.startAction = function () {
        _BattleManager_startAction.call(this);
        if (SceneManager._scene instanceof Scene_Battle && SceneManager._scene._totalDamageSprite) {
            SceneManager._scene._totalDamageSprite.resetCount();
        }
    };
})();
