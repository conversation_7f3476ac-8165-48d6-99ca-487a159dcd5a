/*:
 * @target MZ
 * @plugindesc Applies a sway animation to the upper part of an event sprite while keeping the bottom fixed. Ideal for trees.
 * <AUTHOR>
 *
 * @help
 * This plugin adds a sway effect to the upper portion of an event's sprite.
 * Tag the event with <SwayTop> in a comment (first page only).
 *
 * -- How It Works --
 * The plugin offsets the event sprite's top portion using canvas transformation.
 *
 * Note: Best used with tall sprites where the sway should only affect the canopy.
 */

(() => {
    const swayTag = /<SwayTop>/i;

    const _Sprite_Character_initialize = Sprite_Character.prototype.initialize;
    Sprite_Character.prototype.initialize = function (character) {
        _Sprite_Character_initialize.call(this, character);
        this._swayAngle = 0;
        this._swaySpeed = 0.05 + Math.random() * 0.05;
    };

    const _Sprite_Character_update = Sprite_Character.prototype.update;
    Sprite_Character.prototype.update = function () {
        _Sprite_Character_update.call(this);
        this.updateSwayEffect();
    };

    Sprite_Character.prototype.updateSwayEffect = function () {
        const gameEvent = this._character.event;
        if (!gameEvent || !gameEvent.list) return;

        const hasSwayTag = gameEvent.list.some(
            command => command.code === 108 && swayTag.test(command.parameters[0])
        );

        if (!hasSwayTag) return;

        this._swayAngle += this._swaySpeed;
        const swayOffset = Math.sin(this._swayAngle) * 2;

        this._upperCanvas ??= document.createElement('canvas');
        this._upperContext ??= this._upperCanvas.getContext('2d');

        const bitmap = this.bitmap;
        if (!bitmap || !bitmap.isReady()) return;

        const height = this.patternHeight();
        const width = this.patternWidth();
        const swayHeight = Math.floor(height * 0.6); // sway top 60%
        const staticHeight = height - swayHeight;

        this._upperCanvas.width = width;
        this._upperCanvas.height = swayHeight;

        this._upperContext.clearRect(0, 0, width, swayHeight);
        this._upperContext.drawImage(
            bitmap._image,
            this._character.patternX() * width,
            this._character.patternY() * height,
            width,
            swayHeight,
            swayOffset,
            0,
            width,
            swayHeight
        );

        if (!this._swaySprite) {
            this._swaySprite = new Sprite();
            this._swaySprite.bitmap = new Bitmap(width, swayHeight);
            this.addChild(this._swaySprite);
        }

        this._swaySprite.x = -swayOffset;
        this._swaySprite.y = -height + swayHeight;
        this._swaySprite.bitmap.context.clearRect(0, 0, width, swayHeight);
        this._swaySprite.bitmap.context.drawImage(this._upperCanvas, 0, 0);
        this._swaySprite.bitmap._baseTexture.update();
    };
})();
