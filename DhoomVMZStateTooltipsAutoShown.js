//=============================================================================
// DhoomVMZStateTooltipsAutoShown.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_VMZStateTooltipsAutoShown = '1.0';

var Dhoom = Dhoom || {};
Dhoom.VMZStateTooltipsAutoShown = Dhoom.VMZStateTooltipsAutoShown || {};
/*:
 * @target MZ
 * @plugindesc Dhoom VMZStateTooltipsAutoShown v1.0 - 17/08/2024
 * <AUTHOR>
 * @url drd-workshop.blogspot.com
 * @base VisuMZ_3_StateTooltips
 * @orderAfter VisuMZ_3_StateTooltips
 *
 * @param Enable Switch
 * @desc Switch to enable this plugin. 0/None = Always enabled.
 * @type switch
 * @default 0
 *
 * @param Enemy Tooltips Position
 * @desc Tooltips position on selected battler.
 * @type struct<pos>
 * @default {"battlerAnchor":"2","tooltipAnchor":"8","x":"0","y":"32"}
 *
 * @param Actor Tooltips Position
 * @desc Tooltips position on selected battler.
 * @type struct<pos>
 * @default {"battlerAnchor":"2","tooltipAnchor":"8","x":"0","y":"0"}
 *
 * @help This plugin is for VisuMZ_3_StateTooltips.
 * Will show selected target battler state tooltips automatically.
 */

/*~struct~pos:
@param battlerAnchor
@text Battler Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 7

@param tooltipAnchor
@text Tooltip Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 7

@param x
@text X Position
@type number
@min -999999
@default 0

@param y
@text Y Position
@type number
@min -999999
@default 0
*/

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom VMZStateTooltipsAutoShown/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.VMZStateTooltipsAutoShown.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.VMZStateTooltipsAutoShown[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.VMZStateTooltipsAutoShown.loadParameters();
Dhoom.VMZStateTooltipsAutoShown.isEnabled = function () {
    return !this.enableSwitch || ($gameSwitches && $gameSwitches.value(this.enableSwitch));
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// SceneManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VMZStateTooltipsAutoShown.SceneManager_setStateTooltipBattler =
    SceneManager.setStateTooltipBattler;
SceneManager.setStateTooltipBattler = function (battler, static) {
    Dhoom.VMZStateTooltipsAutoShown.SceneManager_setStateTooltipBattler.call(this, battler);
    static = !!static && $gameParty.inBattle();
    const win = SceneManager._scene._stateTooltipWindow;
    if (!win || win._battler !== battler) return;
    win.setStaticPosition(static);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_StateTooltip
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Window_StateTooltip.prototype.setUsingMouse = function (value) {
    this._mouseMode = value;
};

Window_StateTooltip.prototype.setStaticPosition = function (value) {
    this._staticPosition = !!value;
};

Dhoom.VMZStateTooltipsAutoShown.Window_StateTooltip_updatePosition =
    Window_StateTooltip.prototype.updatePosition;
Window_StateTooltip.prototype.updatePosition = function () {
    if (this._staticPosition) {
        if (this._battler && this.visible) {
            const setting = this._battler.isActor()
                ? Dhoom.VMZStateTooltipsAutoShown.actorTooltipsPosition
                : Dhoom.VMZStateTooltipsAutoShown.enemyTooltipsPosition;
            let sprite = this._battler.battler();
            if (sprite) {
                let sx = sprite.x;
                let sy = sprite.y;
                const scale = sprite.scale;
                if (this._battler.isActor()) {
                    sprite = sprite._mainSprite;
                }
                if (this._battler.isEnemy()) {
                    if (this._battler.hasSvBattler()) {
                        sprite = sprite._svBattlerSprite._mainSprite;
                    } else {
                        sprite = sprite._mainSprite;
                    }
                }
                const sw = sprite.width * scale.x;
                const sh = sprite.height * scale.y;
                sx -= sw * sprite.anchor.x;
                sy -= sh * sprite.anchor.y;
                const ba = setting.battlerAnchor;
                if ([1, 2, 3].contains(ba)) sy += sh;
                if ([4, 5, 6].contains(ba)) sy += Math.round(sh / 2);
                if ([2, 5, 8].contains(ba)) sx += Math.round(sw / 2);
                if ([3, 6, 9].contains(ba)) sx += sw;
                let tx = 0;
                let ty = 0;
                const ta = setting.tooltipAnchor;
                const tw = this.width * this.scale.x;
                const th = this.height * this.scale.y;
                if ([1, 2, 3].contains(ta)) ty -= th;
                if ([4, 5, 6].contains(ta)) ty -= Math.round(th / 2);
                if ([2, 5, 8].contains(ta)) tx -= Math.round(tw / 2);
                if ([3, 6, 9].contains(ta)) tx -= tw;
                this.x = setting.x + sx + tx;
                this.y = setting.y + sy + ty;
                this.clampPosition();
            }
        }
    } else {
        Dhoom.VMZStateTooltipsAutoShown.Window_StateTooltip_updatePosition.call(this);
    }
};

Window_StateTooltip.prototype.battlerHasStateTooltipText = function (battler) {
    const tempBattler = this._battler;
    const tempText = this._text;
    this._battler = battler;
    this.setupText();
    const result = this._text.length > 0;
    this._battler = tempBattler;
    this._text = tempText;
    return result;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Sprite_Clickable
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VMZStateTooltipsAutoShown.Sprite_Clickable_onMouseEnterStateTooltips =
    Sprite_Clickable.prototype.onMouseEnterStateTooltips;
Sprite_Clickable.prototype.onMouseEnterStateTooltips = function () {
    const battler = this.getStateTooltipBattler();
    const win = SceneManager._scene._stateTooltipWindow;
    const tempBattler = win._battler;
    let setBattler = true;
    if (!win._mouseMode && battler) {
        if (!win.battlerHasStateTooltipText(battler)) setBattler = false;
    }
    Dhoom.VMZStateTooltipsAutoShown.Sprite_Clickable_onMouseEnterStateTooltips.call(this);
    if (setBattler) {
        if (battler && SceneManager.currentTooltipBattler() === battler) {
            win.setUsingMouse(true);
        }
    } else {
        SceneManager.setStateTooltipBattler(tempBattler, true);
        win.setUsingMouse(false);
    }
};

Dhoom.VMZStateTooltipsAutoShown.Sprite_Clickable_onMouseExitStateTooltips =
    Sprite_Clickable.prototype.onMouseExitStateTooltips;
Sprite_Clickable.prototype.onMouseExitStateTooltips = function () {
    const battler = this.getStateTooltipBattler();
    const win = SceneManager._scene._stateTooltipWindow;
    if (battler && SceneManager.currentTooltipBattler() === battler) {
        win.setUsingMouse(false);
    }
    Dhoom.VMZStateTooltipsAutoShown.Sprite_Clickable_onMouseExitStateTooltips.call(this);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Sprite_Battler
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VMZStateTooltipsAutoShown.Sprite_Battler_onMouseEnter = Sprite_Battler.prototype.onMouseEnter;
Sprite_Battler.prototype.onMouseEnter = function () {
    const battler = this.getStateTooltipBattler();
    const win = SceneManager._scene._stateTooltipWindow;
    let setBattler = true;
    const tempBattler = win._battler;
    if (!win._mouseMode && battler) {
        if (!win.battlerHasStateTooltipText(battler)) setBattler = false;
    }
    Dhoom.VMZStateTooltipsAutoShown.Sprite_Battler_onMouseEnter.call(this);
    if (setBattler) {
        if (battler && SceneManager.currentTooltipBattler() === battler) {
            win.setUsingMouse(true);
        }
    } else {
        SceneManager.setStateTooltipBattler(tempBattler, true);
        win.setUsingMouse(false);
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Scene_Battle
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VMZStateTooltipsAutoShown.Scene_Battle_update = Scene_Battle.prototype.update;
Scene_Battle.prototype.update = function () {
    Dhoom.VMZStateTooltipsAutoShown.Scene_Battle_update.call(this);
    this.updateStateTooltipsActionTarget();
};

Scene_Battle.prototype.updateStateTooltipsActionTarget = function () {
    const win = this._stateTooltipWindow;
    if (win && !win._mouseMode) {
        let found = false;
        if (BattleManager.isInputting() && Dhoom.VMZStateTooltipsAutoShown.isEnabled()) {
            BattleManager.allBattleMembers().forEach(battler => {
                if (battler.isAlive() && battler.isSelected()) {
                    SceneManager.setStateTooltipBattler(battler, true);
                    found = true;
                }
            });
        }
        if (!found) SceneManager.setStateTooltipBattler(null);
    }
};
