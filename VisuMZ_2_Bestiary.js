//=============================================================================
// VisuStella MZ - Bestiary
// VisuMZ_2_Bestiary.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_2_Bestiary = true;

var VisuMZ = VisuMZ || {};
VisuMZ.Bestiary = VisuMZ.Bestiary || {};
VisuMZ.Bestiary.version = 1.01;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 2] [Version 1.01] [Bestiary]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Bestiary_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin adds a new scene accessible through (but not limited to) the
 * Main Menu, the Bestiary. The Bestiary is a monster book/encyclopedia that
 * will update as the player plays the game. When an enemy is defeated, the
 * player can view that enemy through the Bestiary to see the enemy's stats,
 * elemental resistances and weaknesses, skills, rewards, and more.
 *
 * Features include all (but not limited to) the following:
 *
 * * Access the Bestiary through the Main Menu or through Plugin Commands.
 * * Enemies will automatically populate the Bestiary as they are seen in
 *   battle and defeated.
 * * The player can access the full information of an enemy after defeating it.
 * * Full information found in the bestiary includes the basic stats, elemental
 *   weaknesses and resistances, skills, rewards (EXP, Gold, Drops, etc.), and
 *   any added Lore.
 * * If the VisuStella MZ Elements and Status Menu Core is added, Traits are
 *   also added to the Bestiary.
 * * The VisuStella MZ Enemy Levels plugin gives functionality to view enemy
 *   stats at different levels.
 * * The VisuStella MZ Extra Enemy Drops will show any and all additional drops
 *   including conditional drops.
 * * The VisuStella MZ Class Change Core and Skill Learn System will show any
 *   AP, CP, JP, and SP rewards, too.
 * * Selected skills found in the Bestiary will have a help window appear that
 *   will also list what the skill does.
 * * The game dev can add in custom lore to an enemy's entry through notetags.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 2 ------
 *
 * This plugin is a Tier 2 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_1_ElementStatusCore
 *
 * When this plugin is used together with VisuStella MZ's Elements and Status
 * Menu Core plugin, the "Traits" data page becomes available. It lets the
 * player adjust the trait properties for the enemy being viewed so that the
 * player can view the changes when different traits are applied.
 *
 * ---
 *
 * VisuMZ_3_EnemyLevels
 *
 * When used together in the same project as VisuStella MZ's Enemy Levels
 * plugin, new commands will appear under the "Basic" parameters window,
 * allowing the player to adjust the level of the currently viewed enemy in
 * order to see their parameters across different levels.
 *
 * ---
 *
 * VisuMZ_4_ExtraEnemyDrops
 *
 * When used together in the same project as VisuStella MZ's Extra Enemy Drops
 * plugin, extended drops will be listed as well as conditional drops (although
 * the conditional drops will not display how they are acquired).
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * When used together in the same project as VisuStella MZ's Core Engine, this
 * plugin will display the Extended Parameters dictated by the Core Engine. The
 * icons assigned by the Core Engine will also be utilized, too.
 *
 * ---
 *
 * VisuMZ_1_BattleCore
 *
 * When used together in the same project as VisuStella MZ's Battle Core, the
 * notetags <Display Icon: x> and <Display Text: string> will be used on top of
 * displayed enemy skills to portray their displayed appearances.
 *
 * ---
 *
 * VisuMZ_1_ElementStatusCore
 *
 * When used together in the same project as VisuStella MZ's Elements and
 * Status Menu Core, any excluded elements found in that plugin's Plugin
 * Parameters will also be used here to exclude certain elements, too.
 *
 * ---
 *
 * VisuMZ_2_ClassChangeSystem
 *
 * When used together in the same project as VisuStella MZ's Class Change
 * System plugin, the CP and JP gains from enemies can be displayed under the
 * "Rewards" page as long as the rewards are intended to be shown in the
 * victory reward gains.
 *
 * ---
 *
 * VisuMZ_2_SkillLearnSystem
 *
 * When used together in the same project as VisuStella MZ's Skill Learn System
 * plugin, the AP and SP gains from enemies can be displayed under the"Rewards"
 * page as long as the rewards are intended to be shown in the victory reward
 * gains.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Bestiary-Related Notetags ===
 *
 * ---
 *
 * <Bestiary Category: x>
 * <Bestiary Categories: x, x, x>
 *
 * - Used for: Enemy Notetags
 * - Displays this enemy in the Bestiary category(ies) 'x'.
 * - Replace 'x' with the ID Key of the category or categories found in the
 *   Plugin Parameters.
 * - If this notetag is not used, use the default category determined by the
 *   Plugin Parameters.
 *
 * ---
 *
 * <Hide in Bestiary>
 *
 * - Used for: Enemy Notetags
 * - Prevents this enemy from being listed in the Bestiary.
 *
 * ---
 *
 * <Bestiary Custom Picture: filename>
 *
 * - Used for: Enemy Notetags
 * - Makes this enemy display a custom picture in the Bestiary instead of the
 *   battler graphic used in-game.
 * - Replace 'filename' with the name of the image file to pick from the game
 *   project's /img/pictures/ folder.
 *   - Filenames are case sensitive.
 *   - Leave out the filename extension from the notetag.
 *
 * ---
 *
 * <Bestiary Battleback 1: filename>
 * <Bestiary Battleback 2: filename>
 *
 * - Used for: Enemy Notetags
 * - Makes this enemy display a custom battleback background in the Bestiary
 *   instead of the default graphic determined by the Plugin Parameters.
 * - Replace 'filename' with the name of the image file to pick from the game
 *   project's /img/battlebacks1/ and /img/battlebacks2/ folders.
 *   - Filenames are case sensitive.
 *   - Leave out the filename extension from the notetag.
 * - If these notetags are not used, use the default settings found in the
 *   Plugin Parameters instead.
 *
 * ---
 *
 * <Bestiary Lore>
 *  text
 *  text
 *  text
 * </Bestiary Lore>
 *
 * - Used for: Enemy Notetags
 * - Inserts the written 'text' as the enemy's lore in the Bestiary.
 * - Replace 'text' with whatever you want as the enemy's lore.
 * - If this notetag is not used, then the text displayed will be the default
 *   settings found in the Plugin Parameters.
 *
 * ---
 *
 * <Hide Skill in Bestiary>
 *
 * - Used for: Skill Notetags
 * - Prevents this skill from being listed in the Bestiary.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Debug Plugin Commands ===
 *
 * ---
 *
 * Debug: Full Bestiary?
 * - For playtest only! Allows you to fully view Bestiary.
 * - Resets when the game client is closed.
 *
 *   Reveal?:
 *   - Fully reveals Bestiary for playtesting.
 *   - Resets when the game client is closed.
 *
 * ---
 *
 * === Scene Plugin Commands ===
 *
 * ---
 *
 * Scene: Open Bestiary
 * - Opens the Bestiary scene.
 * - Cannot be used in battle.
 *
 * ---
 *
 * === System Plugin Commands ===
 *
 * ---
 *
 * System: Enable Bestiary in Menu?
 * - Enables/disables Bestiary menu inside the main menu.
 *
 *   Enable/Disable?:
 *   - Enables/disables Bestiary menu inside the main menu.
 *
 * ---
 *
 * System: Show Bestiary in Menu?
 * - Shows/hides Bestiary menu inside the main menu.
 *
 *   Show/Hide?:
 *   - Shows/hides Bestiary menu inside the main menu.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Enemy Categories List Settings
 * ============================================================================
 *
 * List of categories that are used for the bestiary.
 *
 * By default, categories are hidden away until one enemy in that category has
 * been seen (not necessarily defeated). Once seen, the category is visible for
 * the player to browser through. This is to prevent spoilers based on the
 * category name (in case the game developer decides to name categories based
 * on regions for example).
 *
 * The "Default Category", however, will always be visible to the player
 * regardless of whether or not an enemy has been seen inside of it. Therefore,
 * it's best to use the "Default Category" as a category for commonly seen
 * enemies in the game.
 *
 * ---
 *
 * Category
 *
 *   ID Key:
 *   - This category's identification key.
 *   - Categories require unique keys for the plugin to differentiate them.
 *   - Used with <Bestiary Category: x> notetag.
 *
 *   Title:
 *   - This category's title.
 *   - You may use text codes.
 *
 * ---
 *
 * Plugin Parameters
 *
 *   Default Category:
 *   - Default enemy category when no notetag is used.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Main Menu Settings
 * ============================================================================
 *
 * Set up the main menu defaults.
 *
 * ---
 *
 * Main Menu Settings
 *
 *   Command Name:
 *   - Name of the 'Bestiary' option in the Main Menu.
 *
 *   Show in Main Menu?:
 *   - Add the 'Bestiary' option to the Main Menu by default?
 *
 *   Enable in Main Menu?:
 *   - Enable the 'Bestiary' option to the Main Menu by default?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Background Settings
 * ============================================================================
 *
 * Background settings for Scene_Bestiary.
 *
 * ---
 *
 * Background Settings
 *
 *   Snapshop Opacity:
 *   - Snapshot opacity for the scene.
 *
 *   Background 1:
 *   - Filename used for the bottom background image.
 *   - Leave empty if you don't wish to use one.
 *
 *   Background 2:
 *   - Filename used for the upper background image.
 *   - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Vocabulary Settings
 * ============================================================================
 *
 * These settings let you adjust the text displayed for this plugin.
 *
 * ---
 *
 * Button Assist Window
 *
 *   Collapse:
 *   - Text used to collapse a category.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 *   Expand:
 *   - Text used to expand a category.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 *   Scroll Fast:
 *   - Text used to scroll enemy lore quickly.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 *   Scroll Slow:
 *   - Text used to scroll enemy lore slowly.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 *   Switch Enemy:
 *   - Text used to switch an enemy.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 *   View:
 *   - Text used to view an enemy.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Main Windows > List Window
 *
 *   Category (Closed):
 *   Category (Opened):
 *   - Text format used for closed/open categories.
 *   - %1 - Category Name, %2 - Percent Complete
 *
 *     Decimal Places:
 *     - Decimal places for completion percentages.
 *
 *   Mask Character:
 *   - Text character used to mask unknown enemy names.
 *
 * ---
 *
 * Main Windows > Name Window
 *
 *   Category Text:
 *   - Text used when selecting an enemy.
 *
 * ---
 *
 * Main Windows > Sub Window
 *
 *   Completion Rate:
 *   - Text used to announce completion rate.
 *   - %1 - Percentage, %2 - Defeated, %3 - Total
 *
 *     Decimal Places:
 *     - Decimal places for completion percentage.
 *
 *   Defeated:
 *   - Text used to announce defeated monsters.
 *   - %1 - Defeated Number
 *
 *   Encountered:
 *   - Text used to announce encountered monsters.
 *   - %1 - Encountered Number
 *
 * ---
 *
 * Data Windows > Category Window
 *
 *   Basic Text:
 *   Elements Text:
 *   Skills Text:
 *   Rewards Text:
 *   Traits Text:
 *   Lore Text:
 *   - Text used for this command.
 *
 *     Icon:
 *     - Icon used for this command.
 *
 * ---
 *
 * Data Windows > Basic Window
 *
 *   Level Up To Max:
 *   Level Up By One:
 *   Level Down By One:
 *   Level Down To Min:
 *   - Text used for leveling.
 *   - Text codes allowed.
 *   - Requires VisuMZ_3_EnemyLevels!
 *   - %1 - Level Name
 *
 * ---
 *
 * Data Windows > Elements Window
 *
 *   Weak to Element:
 *   Neutral to Element:
 *   Resistant to Element:
 *   Immune to Element:
 *   Absorbs Element:
 *   - Text used with this elemental affinity.
 *   - Text codes allowed.
 *
 * ---
 *
 * Data Windows > Rewards Window
 *
 *   Drop Rate 100%:
 *   Drop Rate >= 50%:
 *   Drop Rate >= 20%:
 *   Drop Rate >= 10%:
 *   Drop Rate < 10%:
 *   Conditional Rate:
 *   - Text used for this kind of drop rate.
 *   - Text codes allowed.
 *
 * ---
 *
 * Data Windows > Traits Window
 *
 *   Category (Closed):
 *   Category (Opened):
 *   - Text format used for closed/open categories.
 *   - Text codes allowed.
 *   - %1 - Category Name
 *
 *   Help Description:
 *   - Help description used for trait categories.
 *   - Text codes allowed.
 *
 *   Null Help:
 *   - Help description used for no traits.
 *   - Text codes allowed.
 *
 * ---
 *
 * Data Windows > Lore Window
 *
 *   Default Lore:
 *   - Text when no lore is found.
 *   - Text codes allowed.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Window Settings
 * ============================================================================
 *
 * These settings let you adjust the windows displayed for this plugin.
 *
 * ---
 *
 * Help Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Scale Window:
 *   - Scale the help window to fit with the enemy preview window?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Main Windows > Image Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Blur Strength:
 *   - What is the blur strength used for unknown enemies?
 *
 *   Default Battleback 1:
 *   Default Battleback 2:
 *   - Default battleback 1 image used for enemies without
 *     <Bestiary Battleback 1: filename> and <Bestiary Battleback 2: filename>
 *     notetags.
 *
 *   Padding:
 *   - What is the padding value used for this window?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Main Windows > List Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Delay MS:
 *   - How many milliseconds (MS) to delay the preview update?
 *   - This is to prevent lag spikes.
 *
 *   Mask Unknown Enemies:
 *   - Apply a character mask to unknown enemies?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Main Windows > Name Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Main Windows > Sub Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Data Windows
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for all data windows.
 *
 * ---
 *
 * Data Windows > Category Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Category Order:
 *   - What order do you want the commands to appear in?
 *
 *   Style:
 *   - How do you wish to draw commands for this window?
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Data Windows > Basic Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Show Level Change:
 *   - Show level change commands?
 *   - Requires VisuMZ_3_EnemyLevels!
 *
 * ---
 *
 * Data Windows > Elements Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 * ---
 *
 * Data Windows > Skills Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 * ---
 *
 * Data Windows > Rewards Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Rewards Order:
 *   - What order do you want the rewards to appear in?
 *
 *   Reward EXP Icon:
 *   - Icon used for EXP reward.
 *
 *   Reward Gold Icon:
 *   - Icon used for Gold reward.
 *
 * ---
 *
 * Data Windows > Traits Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Show All Traits:
 *   - Show all traits? Including unused ones?
 *   - Requires VisuMZ_1_ElementStatusCore!
 *
 * ---
 *
 * Data Windows > Lore Window
 *
 *   Auto Word Wrap?:
 *   - Automatically enable word wrap?
 *   - Requires VisuMZ_1_MessageCore!
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Font Size:
 *   - Font size used for Lore Window.
 *
 *   Scrolling > Slow:
 *
 *     Scroll Speed:
 *     - What speed will Up/Down scroll the window at?
 *     - Lower is slower. Higher is faster.
 *
 *     Sound Frequency:
 *     - How frequent will Up/Down scrolling make sounds?
 *     - Lower is quicker. Higher is later.
 *
 *   Scrolling > Fast:
 *
 *     Scroll Speed:
 *     - What speed will Up/Down scroll the window at?
 *     - Lower is slower. Higher is faster.
 *
 *     Sound Frequency:
 *     - How frequent will Up/Down scrolling make sounds?
 *     - Lower is quicker. Higher is later.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Irina
 * * Arisu
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.01: March 16, 2023
 * * Bug Fixes!
 * ** Fixed a bug that allowed players to scroll to unrevealed enemies. Fix
 *    made by Irina.
 *
 * Version 1.00 Official Release Date: April 3, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command DebugFullBestiary
 * @text Debug: Full Bestiary?
 * @desc For playtest only! Allows you to fully view Bestiary.
 * Resets when the game client is closed.
 *
 * @arg Reveal:eval
 * @text Reveal?
 * @type boolean
 * @on Reveal
 * @off Normal
 * @desc Fully reveals Bestiary for playtesting.
 * Resets when the game client is closed.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Scene
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SceneOpenBestiary
 * @text Scene: Open Bestiary
 * @desc Opens the Bestiary scene.
 * Cannot be used in battle.
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_System
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemEnableBestiaryMenu
 * @text System: Enable Bestiary in Menu?
 * @desc Enables/disables Bestiary menu inside the main menu.
 *
 * @arg Enable:eval
 * @text Enable/Disable?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enables/disables Bestiary menu inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemShowBestiaryMenu
 * @text System: Show Bestiary in Menu?
 * @desc Shows/hides Bestiary menu inside the main menu.
 *
 * @arg Show:eval
 * @text Show/Hide?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Shows/hides Bestiary menu inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Bestiary
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Categories:arraystruct
 * @text Enemy Categories List
 * @type struct<Category>[]
 * @desc List of categories that are used for the bestiary.
 * @default ["{\"Key:str\":\"Common\",\"Title:str\":\"\\\\C[6]Common Monsters\"}","{\"Key:str\":\"Elite\",\"Title:str\":\"\\\\C[4]Elite Monsters\"}","{\"Key:str\":\"MiniBoss\",\"Title:str\":\"\\\\C[5]Mini-Bosses\"}","{\"Key:str\":\"Boss\",\"Title:str\":\"\\\\C[2]Bosses\"}"]
 *
 * @param DefaultCategory:str
 * @text Default Category
 * @parent Categories:arraystruct
 * @desc Default enemy category when no notetag is used.
 * @default Common
 *
 * @param MainMenu:struct
 * @text Main Menu Settings
 * @type struct<MainMenu>
 * @desc Main Menu settings for Bestiary.
 * @default {"Name:str":"Bestiary","ShowMainMenu:eval":"true","EnableMainMenu:eval":"true"}
 *
 * @param BgSettings:struct
 * @text Background Settings
 * @type struct<BgSettings>
 * @desc Background settings for Scene_Bestiary.
 * @default {"SnapshotOpacity:num":"192","BgFilename1:str":"","BgFilename2:str":""}
 *
 * @param Vocab:struct
 * @text Vocabulary Settings
 * @type struct<Vocab>
 * @desc These settings let you adjust the text displayed for this plugin.
 * @default {"ButtonAssist":"","buttonAssist_Collapse:str":"Collapse","buttonAssist_Expand:str":"Expand","buttonAssist_FastScroll:str":"Fast Scroll","buttonAssist_SlowScroll:str":"Scroll","buttonAssist_Switch:str":"Switch Monster","buttonAssist_View:str":"View","MainWindows":"","CategoryWindow":"","CategoryWindow_ClosedCategory:str":"+ %1 (%2%)","CategoryWindow_OpenCategory:str":"- %1 (%2%)","CategoryPercentFixedDigits:num":"2","CategoryWindow_MaskChar:str":"?","NameWindow":"","NameWindow_CategoryText:str":"Please select a monster to view.","SubWindow":"","SubWindow_Completion:str":"Bestiary Completion Rate: %1% (%2/%3 Monsters)","SubWindowCompleteFixedDigits:num":"2","SubWindow_Defeated:str":"Defeated: %1","SubWindow_Encountered:str":"Encountered: %1","DataWindows":"","DataCategoryWindow":"","BasicText:str":"Base","BasicIcon:str":"84","ElementsText:str":"Elements","ElementsIcon:str":"64","SkillsText:str":"Skills","SkillsIcon:str":"79","RewardsText:str":"Rewards","RewardsIcon:str":"87","TraitsText:str":"Properties","TraitsIcon:str":"83","LoreText:str":"Lore","LoreIcon:str":"80","BasicWindow":"","BasicWindow_LevelUpToMax:str":"\\I[73]Raise %1 Up to Maximum","BasicWindow_LevelUpByOne:str":"\\I[73]Raise %1 Up","BasicWindow_LevelDownByOne:str":"\\I[74]Lower %1 Down","BasicWindow_LevelDownToMin:str":"\\I[74]Lower %1 Down to Minimum","ElementsWindow":"","ElementsWindow_Weak:str":"\\C[24]Weak","ElementsWindow_Neutral:str":"\\C[0]Normal","ElementsWindow_Resist:str":"\\C[25]Resist","ElementsWindow_Immune:str":"\\C[7]Immune","ElementsWindow_Absorb:str":"\\C[27]Absorb","RewardsWindow":"","RewardsWindow_Chance100:str":"\\C[24]Guaranteed","RewardsWindow_Chance50:str":"\\C[21]Common","RewardsWindow_Chance20:str":"\\C[4]Uncommon","RewardsWindow_Chance10:str":"\\C[5]Rare","RewardsWindow_Chance0:str":"\\C[27]Super Rare","RewardsWindow_Conditional:str":"\\C[17]Conditional","TraitsWindow":"","TraitsWindow_ClosedCategory:str":"+ \\C[16]%1","TraitsWindow_OpenCategory:str":"- \\C[16]%1","TraitsWindow_CategoryHelpDesc:json":"\"This is the property type.\"","TraitsWindow_NullHelpDesc:json":"\"This monster has no special properties.\"","LoreWindow":"","LoreWindow_Default:json":"\"Little is known about this monster.\""}
 *
 * @param Window:struct
 * @text Window Settings
 * @type struct<Window>
 * @desc These settings let you adjust the windows displayed for this plugin.
 * @default {"HelpWindow":"","HelpWindow_BgType:num":"0","HelpWindow_ScaleRatio:eval":"true","HelpWindow_RectJS:func":"\"const imgRect = this.imageWindowRect();\\nconst ratio = this.helpWindowRatio();\\n\\nconst ww = Graphics.boxWidth;\\nconst wh = this.calcWindowHeight(2, false);\\nconst wx = imgRect.x;\\nconst wy = imgRect.y + (this.isBottomHelpMode() ? (imgRect.height - (wh * ratio)) : 0);\\nreturn new Rectangle(wx, wy, ww, wh);\"","MainWindows":"","ImageWindow":"","ImageWindow_BgType:num":"0","ImageWindow_BlurStrength:num":"8","ImageWindow_Battleback1:str":"Grassland","ImageWindow_Battleback2:str":"Grassland","ImageWindow_Padding:num":"4","ImageWindow_RectJS:func":"\"const ww = Graphics.boxWidth - Math.ceil(Graphics.boxWidth * 4/10);\\nconst wh = this.mainAreaHeight() - (this.calcWindowHeight(1, false) * 2);\\nconst wx = this.isRightInputMode() ? 0 : (Graphics.boxWidth - ww);\\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","ListWindow":"","ListWindow_BgType:num":"0","ListWindowDelayMS:num":"240","ListWindow_MaskUnknown:eval":"true","ListWindow_RectJS:func":"\"const ww = Math.ceil(Graphics.boxWidth * 4/10);\\nconst wh = this.mainAreaHeight() - (this.calcWindowHeight(1, false) * 2);\\nconst wx = this.isRightInputMode() ? (Graphics.boxWidth - ww) : 0;\\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","NameWindow":"","NameWindow_BgType:num":"0","NameWindow_RectJS:func":"\"const ww = Graphics.boxWidth;\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = 0;\\nconst wy = this.mainAreaTop();\\nreturn new Rectangle(wx, wy, ww, wh);\"","SubWindow":"","SubWindow_BgType:num":"0","SubWindow_RectJS:func":"\"const ww = Graphics.boxWidth;\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = 0;\\nconst wy = this.mainAreaBottom() - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\"","DataWindows":"","DataWindow_RectJS:func":"\"const ww = this.listWindowRect().width;\\nconst wh = this.mainAreaHeight() - this.calcWindowHeight(1, true) - (this.calcWindowHeight(1, false) * 2);\\nconst wx = this.listWindowRect().x;\\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false) + this.calcWindowHeight(1, true);\\nreturn new Rectangle(wx, wy, ww, wh);\"","CategoryWindow":"","CategoryWindow_BgType:num":"0","CategoryWindow_CommandOrder:arraystr":"[\"basic\",\"elements\",\"skills\",\"rewards\",\"traits\",\"lore\"]","CategoryWindow_Style:str":"auto","DataCategoriesWindow_RectJS:func":"\"const ww = this.listWindowRect().width;\\nconst wh = this.calcWindowHeight(1, true);\\nconst wx = this.listWindowRect().x;\\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\\nreturn new Rectangle(wx, wy, ww, wh);\"","BasicWindow":"","BasicWindow_BgType:num":"0","BasicWindow_ShowLevelChange:eval":"true","ElementsWindow":"","ElementsWindow_BgType:num":"0","SkillsWindow":"","SkillsWindow_BgType:num":"0","RewardsWindow":"","RewardsWindow_BgType:num":"0","RewardsWindow_RewardsOrder:arraystr":"[\"exp\",\"ap\",\"cp\",\"jp\",\"sp\",\"gold\",\"items\"]","EXP_Icon:num":"87","Gold_Icon:num":"314","TraitsWindow":"","TraitsWindow_BgType:num":"0","TraitsWindow_ShowAllTraits:eval":"false","LoreWindow":"","LoreWindow_AutoWordWrap:eval":"false","LoreWindow_BgType:num":"0","LoreWindow_FontSize:num":"22","Scrolling":"","Slow":"","SlowScrollSpeed:num":"8","SlowSoundFreq:num":"8","Fast":"","FastScrollSpeed:num":"32","FastSoundFreq:num":"4"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Category List Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Category:
 *
 * @param Key:str
 * @text ID Key
 * @desc This category's identification key. Categories require
 * unique keys for the plugin to differentiate them.
 * @default (Needs Key)
 *
 * @param Title:str
 * @text Title
 * @desc This category's title.
 * You may use text codes.
 * @default Untitled
 *
 */
/* ----------------------------------------------------------------------------
 * MainMenu Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~MainMenu:
 *
 * @param Name:str
 * @text Command Name
 * @parent Options
 * @desc Name of the 'Bestiary' option in the Main Menu.
 * @default Bestiary
 *
 * @param ShowMainMenu:eval
 * @text Show in Main Menu?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Add the 'Bestiary' option to the Main Menu by default?
 * @default true
 *
 * @param EnableMainMenu:eval
 * @text Enable in Main Menu?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable the 'Bestiary' option to the Main Menu by default?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Background Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~BgSettings:
 *
 * @param SnapshotOpacity:num
 * @text Snapshop Opacity
 * @type number
 * @min 0
 * @max 255
 * @desc Snapshot opacity for the scene.
 * @default 192
 *
 * @param BgFilename1:str
 * @text Background 1
 * @type file
 * @dir img/titles1/
 * @require 1
 * @desc Filename used for the bottom background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param BgFilename2:str
 * @text Background 2
 * @type file
 * @dir img/titles2/
 * @require 1
 * @desc Filename used for the upper background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 */
/* ----------------------------------------------------------------------------
 * Vocabulary Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Vocab:
 *
 * @param ButtonAssist
 * @text Button Assist Window
 *
 * @param buttonAssist_Collapse:str
 * @text Collapse
 * @parent ButtonAssist
 * @desc Text used to collapse a category.
 * Requires VisuMZ_0_CoreEngine!
 * @default Collapse
 *
 * @param buttonAssist_Expand:str
 * @text Expand
 * @parent ButtonAssist
 * @desc Text used to expand a category.
 * Requires VisuMZ_0_CoreEngine!
 * @default Expand
 *
 * @param buttonAssist_FastScroll:str
 * @text Scroll Fast
 * @parent ButtonAssist
 * @desc Text used to scroll enemy lore quickly.
 * Requires VisuMZ_0_CoreEngine!
 * @default Fast Scroll
 *
 * @param buttonAssist_SlowScroll:str
 * @text Scroll Slow
 * @parent ButtonAssist
 * @desc Text used to scroll enemy lore slowly.
 * Requires VisuMZ_0_CoreEngine!
 * @default Scroll
 *
 * @param buttonAssist_Switch:str
 * @text Switch Enemy
 * @parent ButtonAssist
 * @desc Text used to switch an enemy.
 * Requires VisuMZ_0_CoreEngine!
 * @default Switch Monster
 *
 * @param buttonAssist_View:str
 * @text View
 * @parent ButtonAssist
 * @desc Text used to view an enemy.
 * Requires VisuMZ_0_CoreEngine!
 * @default View
 *
 * @param MainWindows
 * @text Main Windows
 *
 * @param CategoryWindow
 * @text List Window
 * @parent MainWindows
 *
 * @param CategoryWindow_ClosedCategory:str
 * @text Category (Closed)
 * @parent CategoryWindow
 * @desc Text format used for closed categories.
 * %1 - Category Name, %2 - Percent Complete
 * @default + %1 (%2%)
 *
 * @param CategoryWindow_OpenCategory:str
 * @text Category (Opened)
 * @parent CategoryWindow
 * @desc Text format used for opened categories.
 * %1 - Category Name, %2 - Percent Complete
 * @default - %1 (%2%)
 *
 * @param CategoryPercentFixedDigits:num
 * @text Decimal Places
 * @parent CategoryWindow_OpenCategory:str
 * @type number
 * @desc Decimal places for completion percentages.
 * @default 2
 *
 * @param CategoryWindow_MaskChar:str
 * @text Mask Character
 * @parent CategoryWindow
 * @desc Text character used to mask unknown enemy names.
 * @default ?
 *
 * @param NameWindow
 * @text Name Window
 * @parent MainWindows
 *
 * @param NameWindow_CategoryText:str
 * @text Category Text
 * @parent NameWindow
 * @desc Text used when selecting an enemy.
 * @default Please select a monster to view.
 *
 * @param SubWindow
 * @text Sub Window
 * @parent MainWindows
 *
 * @param SubWindow_Completion:str
 * @text Completion Rate
 * @parent SubWindow
 * @desc Text used to announce completion rate.
 * %1 - Percentage, %2 - Defeated, %3 - Total
 * @default Bestiary Completion Rate: %1% (%2/%3 Monsters)
 *
 * @param SubWindowCompleteFixedDigits:num
 * @text Decimal Places
 * @parent SubWindow_Completion:str
 * @type number
 * @desc Decimal places for completion percentage.
 * @default 2
 *
 * @param SubWindow_Defeated:str
 * @text Defeated
 * @parent SubWindow
 * @desc Text used to announce defeated monsters.
 * %1 - Defeated Number
 * @default Defeated: %1
 *
 * @param SubWindow_Encountered:str
 * @text Encountered
 * @parent SubWindow
 * @desc Text used to announce encountered monsters.
 * %1 - Encountered Number
 * @default Encountered: %1
 *
 * @param DataWindows
 * @text Data Windows
 *
 * @param DataCategoryWindow
 * @text Category Window
 * @parent DataWindows
 *
 * @param BasicText:str
 * @text Basic Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Base
 *
 * @param BasicIcon:str
 * @text Icon
 * @parent BasicText:str
 * @desc Icon used for this command.
 * @default 84
 *
 * @param ElementsText:str
 * @text Elements Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Elements
 *
 * @param ElementsIcon:str
 * @text Icon
 * @parent ElementsText:str
 * @desc Icon used for this command.
 * @default 64
 *
 * @param SkillsText:str
 * @text Skills Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Skills
 *
 * @param SkillsIcon:str
 * @text Icon
 * @parent SkillsText:str
 * @desc Icon used for this command.
 * @default 79
 *
 * @param RewardsText:str
 * @text Rewards Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Rewards
 *
 * @param RewardsIcon:str
 * @text Icon
 * @parent RewardsText:str
 * @desc Icon used for this command.
 * @default 87
 *
 * @param TraitsText:str
 * @text Traits Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Properties
 *
 * @param TraitsIcon:str
 * @text Icon
 * @parent TraitsText:str
 * @desc Icon used for this command.
 * @default 83
 *
 * @param LoreText:str
 * @text Lore Text
 * @parent DataCategoryWindow
 * @desc Text used for this command.
 * @default Lore
 *
 * @param LoreIcon:str
 * @text Icon
 * @parent LoreText:str
 * @desc Icon used for this command.
 * @default 80
 *
 * @param BasicWindow
 * @text Basic Window
 * @parent DataWindows
 *
 * @param BasicWindow_LevelUpToMax:str
 * @text Level Up To Max
 * @parent BasicWindow
 * @desc Text used for leveling to max. Text codes allowed.
 * Requires VisuMZ_3_EnemyLevels! %1 - Level Name
 * @default \I[73]Raise %1 Up to Maximum
 *
 * @param BasicWindow_LevelUpByOne:str
 * @text Level Up By One
 * @parent BasicWindow
 * @desc Text used for leveling by one. Text codes allowed.
 * Requires VisuMZ_3_EnemyLevels! %1 - Level Name
 * @default \I[73]Raise %1 Up
 *
 * @param BasicWindow_LevelDownByOne:str
 * @text Level Down By One
 * @parent BasicWindow
 * @desc Text used for deleveling by one. Text codes allowed.
 * Requires VisuMZ_3_EnemyLevels! %1 - Level Name
 * @default \I[74]Lower %1 Down
 *
 * @param BasicWindow_LevelDownToMin:str
 * @text Level Down To Min
 * @parent BasicWindow
 * @desc Text used for deleveling to min. Text codes allowed.
 * Requires VisuMZ_3_EnemyLevels! %1 - Level Name
 * @default \I[74]Lower %1 Down to Minimum
 *
 * @param ElementsWindow
 * @text Elements Window
 * @parent DataWindows
 *
 * @param ElementsWindow_Weak:str
 * @text Weak to Element
 * @parent ElementsWindow
 * @desc Text used when weak to element.
 * Text codes allowed.
 * @default \C[24]Weak
 *
 * @param ElementsWindow_Neutral:str
 * @text Neutral to Element
 * @parent ElementsWindow
 * @desc Text used when neutral to element.
 * Text codes allowed.
 * @default \C[0]Normal
 *
 * @param ElementsWindow_Resist:str
 * @text Resistant to Element
 * @parent ElementsWindow
 * @desc Text used when resistant to element.
 * Text codes allowed.
 * @default \C[25]Resist
 *
 * @param ElementsWindow_Immune:str
 * @text Immune to Element
 * @parent ElementsWindow
 * @desc Text used when immune to element.
 * Text codes allowed.
 * @default \C[7]Immune
 *
 * @param ElementsWindow_Absorb:str
 * @text Absorbs Element
 * @parent ElementsWindow
 * @desc Text used when absorbs element.
 * Text codes allowed.
 * @default \C[27]Absorb
 *
 * @param RewardsWindow
 * @text Rewards Window
 * @parent DataWindows
 *
 * @param RewardsWindow_Chance100:str
 * @text Drop Rate 100%
 * @parent RewardsWindow
 * @desc Text used for 100% drop rates.
 * Text codes allowed.
 * @default \C[24]Guaranteed
 *
 * @param RewardsWindow_Chance50:str
 * @text Drop Rate >= 50%
 * @parent RewardsWindow
 * @desc Text used for greater than 50% drop rates.
 * Text codes allowed.
 * @default \C[21]Common
 *
 * @param RewardsWindow_Chance20:str
 * @text Drop Rate >= 20%
 * @parent RewardsWindow
 * @desc Text used for greater than 20% drop rates.
 * Text codes allowed.
 * @default \C[4]Uncommon
 *
 * @param RewardsWindow_Chance10:str
 * @text Drop Rate >= 10%
 * @parent RewardsWindow
 * @desc Text used for greater than 10% drop rates.
 * Text codes allowed.
 * @default \C[5]Rare
 *
 * @param RewardsWindow_Chance0:str
 * @text Drop Rate < 10%
 * @parent RewardsWindow
 * @desc Text used for less than 10% drop rates.
 * Text codes allowed.
 * @default \C[27]Super Rare
 *
 * @param RewardsWindow_Conditional:str
 * @text Conditional Rate
 * @parent RewardsWindow
 * @desc Text used for conditional drop rates.
 * Requires VisuMZ_4_ExtraEnemyDrops! Text codes allowed.
 * @default \C[17]Conditional
 *
 * @param TraitsWindow
 * @text Traits Window
 * @parent DataWindows
 *
 * @param TraitsWindow_ClosedCategory:str
 * @text Category (Closed)
 * @parent TraitsWindow
 * @desc Text format used for closed categories.
 * Text codes allowed. %1 - Category Name
 * @default + \C[16]%1
 *
 * @param TraitsWindow_OpenCategory:str
 * @text Category (Opened)
 * @parent TraitsWindow
 * @desc Text format used for opened categories.
 * Text codes allowed. %1 - Category Name
 * @default - \C[16]%1
 *
 * @param TraitsWindow_CategoryHelpDesc:json
 * @text Help Description
 * @parent TraitsWindow_OpenCategory:str
 * @type note
 * @desc Help description used for trait categories.
 * Text codes allowed.
 * @default "This is the property type."
 *
 * @param TraitsWindow_NullHelpDesc:json
 * @text Null Help
 * @parent TraitsWindow
 * @type note
 * @desc Help description used for no traits.
 * Text codes allowed.
 * @default "This monster has no special properties."
 *
 * @param LoreWindow
 * @text Lore Window
 * @parent DataWindows
 *
 * @param LoreWindow_Default:json
 * @text Default Lore
 * @parent LoreWindow
 * @type note
 * @desc Text when no lore is found.
 * Text codes allowed.
 * @default "Little is known about this monster."
 *
 */
/* ----------------------------------------------------------------------------
 * Window Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Window:
 *
 * @param HelpWindow
 * @text Help Window
 *
 * @param HelpWindow_BgType:num
 * @text Background Type
 * @parent HelpWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param HelpWindow_ScaleRatio:eval
 * @text Scale Window
 * @parent HelpWindow
 * @type boolean
 * @on ON
 * @off OFF
 * @desc Scale the help window to fit with the enemy preview window?
 * @default true
 *
 * @param HelpWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent HelpWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Graphics.boxWidth;\nconst wh = this.calcWindowHeight(2, false);\nconst wx = this.imageWindowRect().x;\nconst wy = this.imageWindowRect().y;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param MainWindows
 * @text Main Windows
 *
 * @param ImageWindow
 * @text Image Window
 * @parent MainWindows
 *
 * @param ImageWindow_BgType:num
 * @text Background Type
 * @parent ImageWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param ImageWindow_BlurStrength:num
 * @text Blur Strength
 * @parent ImageWindow
 * @type number
 * @desc What is the blur strength used for unknown enemies?
 * @default 8
 *
 * @param ImageWindow_Battleback1:str
 * @text Default Battleback 1
 * @parent ImageWindow
 * @type file
 * @dir img/battlebacks1/
 * @require 1
 * @desc Default battleback 1 image used for enemies
 * without <Bestiary Battleback 1: filename> notetag.
 * @default Grassland
 *
 * @param ImageWindow_Battleback2:str
 * @text Default Battleback 2
 * @parent ImageWindow
 * @type file
 * @dir img/battlebacks2/
 * @require 1
 * @desc Default battleback 2 image used for enemies
 * without <Bestiary Battleback 2: filename> notetag.
 * @default Grassland
 *
 * @param ImageWindow_Padding:num
 * @text Padding
 * @parent ImageWindow
 * @type number
 * @desc What is the padding value used for this window?
 * @default 4
 *
 * @param ImageWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent ImageWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Graphics.boxWidth - Math.ceil(Graphics.boxWidth * 4/10);\nconst wh = this.mainAreaHeight() - (this.calcWindowHeight(1, false) * 2);\nconst wx = this.isRightInputMode() ? 0 : (Graphics.boxWidth - ww);\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param ListWindow
 * @text List Window
 * @parent MainWindows
 *
 * @param ListWindow_BgType:num
 * @text Background Type
 * @parent ListWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param ListWindowDelayMS:num
 * @text Delay MS
 * @parent ListWindow
 * @type number
 * @min 1
 * @max 999
 * @desc How many milliseconds (MS) to delay the preview update?
 * This is to prevent lag spikes.
 * @default 240
 *
 * @param ListWindow_MaskUnknown:eval
 * @text Mask Unknown Enemies
 * @parent ListWindow
 * @type boolean
 * @on ON
 * @off OFF
 * @desc Apply a character mask to unknown enemies?
 * @default true
 *
 * @param ListWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent ListWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.ceil(Graphics.boxWidth * 4/10);\nconst wh = this.mainAreaHeight() - (this.calcWindowHeight(1, false) * 2);\nconst wx = this.isRightInputMode() ? (Graphics.boxWidth - ww) : 0;\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param NameWindow
 * @text Name Window
 * @parent MainWindows
 *
 * @param NameWindow_BgType:num
 * @text Background Type
 * @parent NameWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param NameWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent NameWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Graphics.boxWidth;\nconst wh = this.calcWindowHeight(1, false);\nconst wx = 0;\nconst wy = this.mainAreaTop();\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param SubWindow
 * @text Sub Window
 * @parent MainWindows
 *
 * @param SubWindow_BgType:num
 * @text Background Type
 * @parent SubWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param SubWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent SubWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Graphics.boxWidth;\nconst wh = this.calcWindowHeight(1, false);\nconst wx = 0;\nconst wy = this.mainAreaBottom() - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param DataWindows
 * @text Data Window
 *
 * @param DataWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent DataWindows
 * @type note
 * @desc Code used to determine the dimensions for all data windows.
 * @default "const ww = this.listWindowRect().width;\nconst wh = this.mainAreaHeight() - this.calcWindowHeight(1, true) - (this.calcWindowHeight(1, false) * 2);\nconst wx = 0;\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false) + this.calcWindowHeight(1, true);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param CategoryWindow
 * @text Category Window
 * @parent DataWindows
 *
 * @param CategoryWindow_BgType:num
 * @text Background Type
 * @parent CategoryWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param CategoryWindow_CommandOrder:arraystr
 * @text Command Order
 * @parent CategoryWindow
 * @type select[]
 * @option Basic - Basic parameter data
 * @value basic
 * @option Elements - Elemental resistances and weaknesses
 * @value elements
 * @option Skills - Usable skills in-battle
 * @value skills
 * @option Rewards - EXP, Gold, Drop Items
 * @value rewards
 * @option Traits - For VisuMZ_1_ElementStatusCore.js
 * @value traits
 * @option Lore - Background Information
 * @value lore
 * @desc What order do you want the commands to appear in?
 * @default ["basic","elements","skills","rewards","traits","lore"]
 *
 * @param CategoryWindow_Style:str
 * @text Style
 * @parent CategoryWindow
 * @type select
 * @option Text Only
 * @value text
 * @option Icon Only
 * @value icon
 * @option Icon + Text
 * @value iconText
 * @option Automatic
 * @value auto
 * @desc How do you wish to draw commands for this window?
 * @default auto
 *
 * @param DataCategoriesWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent CategoryWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = this.listWindowRect().width;\nconst wh = this.calcWindowHeight(1, true);\nconst wx = 0;\nconst wy = this.mainAreaTop() + this.calcWindowHeight(1, false);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param BasicWindow
 * @text Basic Window
 * @parent DataWindows
 *
 * @param BasicWindow_BgType:num
 * @text Background Type
 * @parent BasicWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param BasicWindow_ShowLevelChange:eval
 * @text Show Level Change
 * @parent BasicWindow
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show level change commands?
 * Requires VisuMZ_3_EnemyLevels!
 * @default true
 *
 * @param ElementsWindow
 * @text Elements Window
 * @parent DataWindows
 *
 * @param ElementsWindow_BgType:num
 * @text Background Type
 * @parent ElementsWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param SkillsWindow
 * @text Skills Window
 * @parent DataWindows
 *
 * @param SkillsWindow_BgType:num
 * @text Background Type
 * @parent SkillsWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param RewardsWindow
 * @text Rewards Window
 * @parent DataWindows
 *
 * @param RewardsWindow_BgType:num
 * @text Background Type
 * @parent RewardsWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param RewardsWindow_RewardsOrder:arraystr
 * @text Rewards Order
 * @parent RewardsWindow
 * @type select[]
 * @option EXP - Experience Points
 * @value exp
 * @option Gold - Gold Currency
 * @value gold
 * @option Drop Items - Enemy Drop Items
 * @value items
 * @option AP - For VisuMZ_2_SkillLearnSystem.js
 * @value ap
 * @option CP - For VisuMZ_2_ClassChangeSystem.js
 * @value cp
 * @option JP - For VisuMZ_2_ClassChangeSystem.js
 * @value jp
 * @option SP - For VisuMZ_2_SkillLearnSystem.js
 * @value sp
 * @desc What order do you want the rewards to appear in?
 * @default ["exp","ap","cp","jp","sp","gold","items"]
 *
 * @param EXP_Icon:num
 * @text Reward EXP Icon
 * @parent RewardsWindow
 * @desc Icon used for EXP reward.
 * @default 87
 *
 * @param Gold_Icon:num
 * @text Reward Gold Icon
 * @parent RewardsWindow
 * @desc Icon used for Gold reward.
 * @default 314
 *
 * @param TraitsWindow
 * @text Traits Window
 * @parent DataWindows
 *
 * @param TraitsWindow_BgType:num
 * @text Background Type
 * @parent TraitsWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param TraitsWindow_ShowAllTraits:eval
 * @text Show All Traits
 * @parent TraitsWindow
 * @type boolean
 * @on Include Unused
 * @off Show Only Used
 * @desc Show all traits? Including unused ones?
 * Requires VisuMZ_1_ElementStatusCore!
 * @default false
 *
 * @param LoreWindow
 * @text Lore Window
 * @parent DataWindows
 *
 * @param LoreWindow_AutoWordWrap:eval
 * @text Auto Word Wrap?
 * @parent LoreWindow
 * @type boolean
 * @on Word Wrap
 * @off Normal
 * @desc Automatically enable word wrap?
 * Requires VisuMZ_1_MessageCore!
 * @default false
 *
 * @param LoreWindow_BgType:num
 * @text Background Type
 * @parent LoreWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param LoreWindow_FontSize:num
 * @text Font Size
 * @parent LoreWindow
 * @desc Font size used for Lore Window.
 * @default 22
 *
 * @param Scrolling
 * @parent LoreWindow
 *
 * @param Slow
 * @parent Scrolling
 *
 * @param SlowScrollSpeed:num
 * @text Scroll Speed
 * @parent Slow
 * @type number
 * @min 1
 * @desc What speed will Up/Down scroll the window at?
 * Lower is slower. Higher is faster.
 * @default 8
 *
 * @param SlowSoundFreq:num
 * @text Sound Frequency
 * @parent Slow
 * @type number
 * @min 1
 * @desc How frequent will Up/Down scrolling make sounds?
 * Lower is quicker. Higher is later.
 * @default 8
 *
 * @param Fast
 * @parent Scrolling
 *
 * @param FastScrollSpeed:num
 * @text Scroll Speed
 * @parent Fast
 * @type number
 * @min 1
 * @desc What speed will PageUp/PageDn scroll the window at?
 * Lower is slower. Higher is faster.
 * @default 32
 *
 * @param FastSoundFreq:num
 * @text Sound Frequency
 * @parent Fast
 * @type number
 * @min 1
 * @desc How frequent will PageUp/PageDn scrolling make sounds?
 * Lower is quicker. Higher is later.
 * @default 4
 *
 */
//=============================================================================

const _0x249061 = _0x5e87;
(function (_0x1fbbe8, _0x5c126e) {
    const _0x152770 = _0x5e87,
        _0x42d212 = _0x1fbbe8();
    while (!![]) {
        try {
            const _0x202fcc =
                parseInt(_0x152770(0x224)) / 0x1 +
                parseInt(_0x152770(0x193)) / 0x2 +
                (-parseInt(_0x152770(0x2d1)) / 0x3) * (parseInt(_0x152770(0x145)) / 0x4) +
                (-parseInt(_0x152770(0x128)) / 0x5) * (parseInt(_0x152770(0x11f)) / 0x6) +
                parseInt(_0x152770(0x192)) / 0x7 +
                (parseInt(_0x152770(0x3a3)) / 0x8) * (parseInt(_0x152770(0x3c0)) / 0x9) +
                (-parseInt(_0x152770(0x176)) / 0xa) * (parseInt(_0x152770(0x36d)) / 0xb);
            if (_0x202fcc === _0x5c126e) break;
            else _0x42d212['push'](_0x42d212['shift']());
        } catch (_0x18401c) {
            _0x42d212['push'](_0x42d212['shift']());
        }
    }
})(_0x550e, 0x49c92);
var label = _0x249061(0x2c5),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x249061(0x2b0)](function (_0x3f12ff) {
        const _0x132a15 = _0x249061;
        return _0x3f12ff['status'] && _0x3f12ff[_0x132a15(0x1cf)]['includes']('[' + label + ']');
    })[0x0];
((VisuMZ[label][_0x249061(0x27d)] = VisuMZ[label][_0x249061(0x27d)] || {}),
    (VisuMZ[_0x249061(0x31c)] = function (_0x5c7b9a, _0xaef5c6) {
        const _0x211ca4 = _0x249061;
        for (const _0x467045 in _0xaef5c6) {
            if (_0x467045[_0x211ca4(0x11c)](/(.*):(.*)/i)) {
                const _0x3f8c59 = String(RegExp['$1']),
                    _0x42ddd9 = String(RegExp['$2'])[_0x211ca4(0x199)]()['trim']();
                let _0x3e02fb, _0x30ada4, _0xa6a752;
                switch (_0x42ddd9) {
                    case _0x211ca4(0x23e):
                        _0x3e02fb =
                            _0xaef5c6[_0x467045] !== '' ? Number(_0xaef5c6[_0x467045]) : 0x0;
                        break;
                    case _0x211ca4(0x33a):
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : []),
                            (_0x3e02fb = _0x30ada4['map'](_0x3d256d => Number(_0x3d256d))));
                        break;
                    case _0x211ca4(0x123):
                        _0x3e02fb = _0xaef5c6[_0x467045] !== '' ? eval(_0xaef5c6[_0x467045]) : null;
                        break;
                    case _0x211ca4(0x197):
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : []),
                            (_0x3e02fb = _0x30ada4[_0x211ca4(0x336)](_0x35d776 =>
                                eval(_0x35d776)
                            )));
                        break;
                    case _0x211ca4(0x29e):
                        _0x3e02fb =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : '';
                        break;
                    case _0x211ca4(0x23f):
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : []),
                            (_0x3e02fb = _0x30ada4[_0x211ca4(0x336)](_0x2f9127 =>
                                JSON[_0x211ca4(0x1c7)](_0x2f9127)
                            )));
                        break;
                    case _0x211ca4(0x312):
                        _0x3e02fb =
                            _0xaef5c6[_0x467045] !== ''
                                ? new Function(JSON['parse'](_0xaef5c6[_0x467045]))
                                : new Function('return\x200');
                        break;
                    case 'ARRAYFUNC':
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : []),
                            (_0x3e02fb = _0x30ada4[_0x211ca4(0x336)](
                                _0x396511 => new Function(JSON[_0x211ca4(0x1c7)](_0x396511))
                            )));
                        break;
                    case 'STR':
                        _0x3e02fb = _0xaef5c6[_0x467045] !== '' ? String(_0xaef5c6[_0x467045]) : '';
                        break;
                    case 'ARRAYSTR':
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== '' ? JSON['parse'](_0xaef5c6[_0x467045]) : []),
                            (_0x3e02fb = _0x30ada4['map'](_0x5a6d70 => String(_0x5a6d70))));
                        break;
                    case _0x211ca4(0x12f):
                        ((_0xa6a752 =
                            _0xaef5c6[_0x467045] !== ''
                                ? JSON[_0x211ca4(0x1c7)](_0xaef5c6[_0x467045])
                                : {}),
                            (_0x3e02fb = VisuMZ['ConvertParams']({}, _0xa6a752)));
                        break;
                    case 'ARRAYSTRUCT':
                        ((_0x30ada4 =
                            _0xaef5c6[_0x467045] !== '' ? JSON['parse'](_0xaef5c6[_0x467045]) : []),
                            (_0x3e02fb = _0x30ada4['map'](_0x3564fe =>
                                VisuMZ[_0x211ca4(0x31c)]({}, JSON[_0x211ca4(0x1c7)](_0x3564fe))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x5c7b9a[_0x3f8c59] = _0x3e02fb;
            }
        }
        return _0x5c7b9a;
    }),
    (_0x86c175 => {
        const _0x518d1c = _0x249061,
            _0x3523fa = _0x86c175[_0x518d1c(0x14d)];
        for (const _0x184958 of dependencies) {
            if (!Imported[_0x184958]) {
                (alert(
                    '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.'[
                        _0x518d1c(0x340)
                    ](_0x3523fa, _0x184958)
                ),
                    SceneManager[_0x518d1c(0x24b)]());
                break;
            }
        }
        const _0x3d0dfd = _0x86c175[_0x518d1c(0x1cf)];
        if (_0x3d0dfd['match'](/\[Version[ ](.*?)\]/i)) {
            const _0x59e982 = Number(RegExp['$1']);
            _0x59e982 !== VisuMZ[label][_0x518d1c(0x380)] &&
                (alert(_0x518d1c(0x1e4)[_0x518d1c(0x340)](_0x3523fa, _0x59e982)),
                SceneManager['exit']());
        }
        if (_0x3d0dfd[_0x518d1c(0x11c)](/\[Tier[ ](\d+)\]/i)) {
            const _0x328df1 = Number(RegExp['$1']);
            _0x328df1 < tier
                ? (alert(_0x518d1c(0x1ea)[_0x518d1c(0x340)](_0x3523fa, _0x328df1, tier)),
                  SceneManager['exit']())
                : (tier = Math['max'](_0x328df1, tier));
        }
        VisuMZ[_0x518d1c(0x31c)](VisuMZ[label][_0x518d1c(0x27d)], _0x86c175[_0x518d1c(0x182)]);
    })(pluginData),
    PluginManager[_0x249061(0x3ae)](
        pluginData[_0x249061(0x14d)],
        'DebugFullBestiary',
        _0x50d65a => {
            const _0x416c06 = _0x249061;
            if (!$gameTemp[_0x416c06(0x1e7)]()) return;
            (VisuMZ[_0x416c06(0x31c)](_0x50d65a, _0x50d65a),
                $gameTemp[_0x416c06(0x15b)](_0x50d65a[_0x416c06(0x38e)]));
        }
    ),
    PluginManager[_0x249061(0x3ae)](
        pluginData[_0x249061(0x14d)],
        'SceneOpenBestiary',
        _0x12f0e0 => {
            const _0x1a2984 = _0x249061;
            if ($gameParty['inBattle']()) return;
            if (SceneManager[_0x1a2984(0x222)]()) return;
            SceneManager[_0x1a2984(0x2a5)](Scene_Bestiary);
        }
    ),
    PluginManager[_0x249061(0x3ae)](pluginData['name'], _0x249061(0x236), _0x3e3898 => {
        const _0x1b390a = _0x249061;
        (VisuMZ[_0x1b390a(0x31c)](_0x3e3898, _0x3e3898),
            $gameSystem[_0x1b390a(0x2cc)](_0x3e3898[_0x1b390a(0x3ba)]));
    }),
    PluginManager[_0x249061(0x3ae)](pluginData[_0x249061(0x14d)], _0x249061(0x1ae), _0x57c3cf => {
        const _0xec7aed = _0x249061;
        (VisuMZ[_0xec7aed(0x31c)](_0x57c3cf, _0x57c3cf),
            $gameSystem[_0xec7aed(0x2ca)](_0x57c3cf[_0xec7aed(0x14a)]));
    }),
    (VisuMZ['Bestiary'][_0x249061(0x2bc)] = {
        category: /<BESTIARY (?:CATEGORY|CATEGORIES):[ ](.*)>/i,
        hideInBestiary: /<HIDE IN BESTIARY>/i,
        customPicture: /<BESTIARY CUSTOM (?:IMAGE|PICTURE):[ ](.*)>/i,
        battleback1: /<BESTIARY (?:BATTLEBACK|BACKGROUND) 1:[ ](.*)>/i,
        battleback2: /<BESTIARY (?:BATTLEBACK|BACKGROUND) 2:[ ](.*)>/i,
        lore: /<(?:BESTIARY |)LORE>\s*([\s\S]*)\s*<\/(?:BESTIARY |)LORE>/i,
        hideSkill: /<HIDE SKILL IN BESTIARY>/i,
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x207)] = Scene_Boot['prototype'][_0x249061(0x3a9)]),
    (Scene_Boot['prototype']['onDatabaseLoaded'] = function () {
        const _0x387ad0 = _0x249061;
        (VisuMZ[_0x387ad0(0x2c5)][_0x387ad0(0x207)][_0x387ad0(0x310)](this),
            this[_0x387ad0(0x116)]());
    }),
    (Scene_Boot[_0x249061(0x19a)]['process_VisuMZ_Bestiary'] = function () {
        const _0xed3267 = _0x249061;
        this[_0xed3267(0x394)]();
    }),
    (Scene_Boot[_0x249061(0x19a)][_0x249061(0x394)] = function () {
        const _0x2a3d4f = _0x249061;
        ((VisuMZ[_0x2a3d4f(0x2c5)][_0x2a3d4f(0x2e7)] = []),
            (VisuMZ[_0x2a3d4f(0x2c5)]['CategoryData'] = {}));
        const _0x2a548d = VisuMZ[_0x2a3d4f(0x2c5)][_0x2a3d4f(0x27d)][_0x2a3d4f(0x122)];
        for (const _0x2f977b of _0x2a548d) {
            const _0x1849a5 = (_0x2f977b['Key'] || '')['toLowerCase']()[_0x2a3d4f(0x1ec)]();
            if (_0x1849a5 === '') continue;
            if (_0x1849a5 === _0x2a3d4f(0x1d9)) continue;
            (VisuMZ[_0x2a3d4f(0x2c5)][_0x2a3d4f(0x2e7)]['push'](_0x1849a5),
                (VisuMZ[_0x2a3d4f(0x2c5)][_0x2a3d4f(0x1ee)][_0x1849a5] = _0x2f977b));
        }
    }),
    (VisuMZ['Bestiary']['Math_random'] = Math[_0x249061(0x184)]),
    (Math[_0x249061(0x184)] = function () {
        const _0x5e03ce = _0x249061;
        if (this[_0x5e03ce(0x23d)]) return 0.5;
        return VisuMZ[_0x5e03ce(0x2c5)][_0x5e03ce(0x24d)][_0x5e03ce(0x35b)](this, arguments);
    }),
    (DataManager[_0x249061(0x2e0)] = function (_0x1d75ca) {
        const _0x23b474 = _0x249061;
        if (!_0x1d75ca) return [];
        const _0x1c5282 = _0x1d75ca['id'];
        this[_0x23b474(0x26f)] = this[_0x23b474(0x26f)] || {};
        if (this[_0x23b474(0x26f)][_0x1c5282] !== undefined)
            return this[_0x23b474(0x26f)][_0x1c5282];
        this[_0x23b474(0x26f)][_0x1c5282] = [];
        const _0x4dd452 = VisuMZ[_0x23b474(0x2c5)][_0x23b474(0x2bc)],
            _0x179851 = _0x1d75ca['note'] || '';
        return (
            _0x179851['match'](_0x4dd452['category']) &&
                (this['_enemyBestiaryCategories'][_0x1c5282] = RegExp['$1']
                    [_0x23b474(0x2f7)](',')
                    [
                        _0x23b474(0x336)
                    ](_0x12079a => _0x12079a[_0x23b474(0x320)]()[_0x23b474(0x1ec)]())),
            this['_enemyBestiaryCategories'][_0x1c5282]['length'] <= 0x0 &&
                (this[_0x23b474(0x26f)][_0x1c5282] = [
                    Game_Enemy[_0x23b474(0x1f7)][_0x23b474(0x334)][_0x23b474(0x320)]()['trim'](),
                ]),
            this['_enemyBestiaryCategories'][_0x1c5282]
        );
    }),
    (DataManager[_0x249061(0x172)] = function (_0x44e62a) {
        const _0x71f727 = _0x249061;
        if (!_0x44e62a) return ![];
        if (_0x44e62a[_0x71f727(0x14d)][_0x71f727(0x1ec)]() === '') return ![];
        if (_0x44e62a['name'][_0x71f727(0x2a7)](_0x71f727(0x16d))) return ![];
        const _0x3f4f10 = _0x44e62a['id'];
        this[_0x71f727(0x285)] = this[_0x71f727(0x285)] || {};
        if (this[_0x71f727(0x285)][_0x3f4f10] !== undefined)
            return this[_0x71f727(0x285)][_0x3f4f10];
        let _0xb70ca7 = !![];
        const _0x4e280f = VisuMZ[_0x71f727(0x2c5)][_0x71f727(0x2bc)],
            _0x2e7727 = _0x44e62a[_0x71f727(0x274)] || '';
        if (_0x2e7727['match'](_0x4e280f[_0x71f727(0x158)])) _0xb70ca7 = ![];
        else
            _0x2e7727['match'](/<SWAP ENEMIES>\s*([\s\S]*)\s*<\/SWAP ENEMIES>/i) &&
                (_0xb70ca7 = ![]);
        return (
            (this[_0x71f727(0x285)][_0x3f4f10] = _0xb70ca7),
            this['_showEnemyInBestiary'][_0x3f4f10]
        );
    }),
    (DataManager[_0x249061(0x2f8)] = function (_0x24dae1) {
        const _0x52acdc = _0x249061,
            _0x53aa44 = this[_0x52acdc(0x149)](_0x24dae1);
        return _0x53aa44['map'](_0x4d43f6 => $dataEnemies[_0x4d43f6])
            [_0x52acdc(0x2c9)](undefined)
            [_0x52acdc(0x2c9)](null);
    }),
    (DataManager[_0x249061(0x149)] = function (_0x5ce1dc) {
        const _0x451b94 = _0x249061;
        this['_categoryEnemyIDs'] = this['_categoryEnemyIDs'] || {};
        if (this[_0x451b94(0x368)][_0x5ce1dc] !== undefined)
            return this[_0x451b94(0x368)][_0x5ce1dc];
        for (const _0x59facc of VisuMZ[_0x451b94(0x2c5)][_0x451b94(0x2e7)]) {
            this[_0x451b94(0x368)][_0x59facc] = [];
        }
        for (const _0x7fa055 of $dataEnemies) {
            if (!_0x7fa055) continue;
            if (!this[_0x451b94(0x172)](_0x7fa055)) continue;
            const _0x5e74bb = this[_0x451b94(0x2e0)](_0x7fa055);
            for (const _0x256c7e of _0x5e74bb) {
                ((this[_0x451b94(0x368)][_0x256c7e] = this['_categoryEnemyIDs'][_0x256c7e] || []),
                    this[_0x451b94(0x368)][_0x256c7e]['push'](_0x7fa055['id']));
            }
        }
        for (const _0x9357b7 in this[_0x451b94(0x368)]) {
            this['_categoryEnemyIDs'][_0x9357b7][_0x451b94(0x15a)](
                (_0x49fa31, _0x4282eb) => _0x49fa31 - _0x4282eb
            );
        }
        return this['_categoryEnemyIDs'][_0x5ce1dc];
    }),
    (DataManager['bestiaryTotalEnemies'] = function () {
        const _0x588a52 = _0x249061;
        if (this[_0x588a52(0x252)] !== undefined) return this[_0x588a52(0x252)];
        let _0x4412be = [];
        for (const _0x2e10dd of VisuMZ[_0x588a52(0x2c5)][_0x588a52(0x2e7)]) {
            const _0xc0cb59 = this['categoryEnemyIDs'](_0x2e10dd);
            _0x4412be = _0x4412be[_0x588a52(0x146)](_0xc0cb59);
        }
        return (
            (this[_0x588a52(0x252)] = _0x4412be['filter'](
                (_0x254fc3, _0x47def3, _0x515097) =>
                    _0x515097[_0x588a52(0x39c)](_0x254fc3) === _0x47def3
            )['length']),
            this[_0x588a52(0x252)]
        );
    }),
    (ImageManager['svActorHorzCells'] = ImageManager['svActorHorzCells'] || 0x9),
    (ImageManager['svActorVertCells'] = ImageManager[_0x249061(0x375)] || 0x6),
    (ImageManager[_0x249061(0x346)] = function (_0x41fc4c) {
        const _0x5b4358 = _0x249061,
            _0x214f00 = this[_0x5b4358(0x108)](_0x41fc4c)[0x0];
        return _0x214f00 === '' ? new Bitmap(0x1, 0x1) : this[_0x5b4358(0x12c)](_0x214f00);
    }),
    (ImageManager[_0x249061(0x392)] = function (_0xc11a5f) {
        const _0x374fb0 = _0x249061,
            _0x4760c3 = this['bestiaryEnemyBattlebackData'](_0xc11a5f)[0x1];
        return _0x4760c3 === '' ? new Bitmap(0x1, 0x1) : this[_0x374fb0(0x3b3)](_0x4760c3);
    }),
    (ImageManager[_0x249061(0x108)] = function (_0x44f90d) {
        const _0xf59561 = _0x249061,
            _0x194134 = $dataEnemies[_0x44f90d];
        if (!_0x194134) return ['', ''];
        this[_0xf59561(0x272)] = this[_0xf59561(0x272)] || {};
        if (this[_0xf59561(0x272)][_0x44f90d] !== undefined)
            return this[_0xf59561(0x272)][_0x44f90d];
        this[_0xf59561(0x272)][_0x44f90d] = ['', ''];
        const _0x342e5d = VisuMZ['Bestiary']['RegExp'],
            _0x13cec9 = _0x194134[_0xf59561(0x274)] || '';
        return (
            _0x13cec9['match'](_0x342e5d[_0xf59561(0x347)]) &&
                (this['_bestiaryEnemyBattlebackData'][_0x44f90d][0x0] = String(RegExp['$1'])[
                    _0xf59561(0x1ec)
                ]()),
            _0x13cec9[_0xf59561(0x11c)](_0x342e5d['battleback2']) &&
                (this[_0xf59561(0x272)][_0x44f90d][0x1] = String(RegExp['$1'])['trim']()),
            this[_0xf59561(0x272)][_0x44f90d][0x0] === '' &&
                this[_0xf59561(0x272)][_0x44f90d][0x1] === '' &&
                (this['_bestiaryEnemyBattlebackData'][_0x44f90d] = [
                    Window_BestiaryEnemyImage['SETTINGS'][_0xf59561(0x363)],
                    Window_BestiaryEnemyImage[_0xf59561(0x147)][_0xf59561(0x160)],
                ]),
            this[_0xf59561(0x272)][_0x44f90d]
        );
    }),
    (ImageManager[_0x249061(0x18e)] = function (_0x4545ad) {
        const _0x39379d = _0x249061,
            _0x193a0c = $dataEnemies[_0x4545ad];
        if (!_0x193a0c) return '';
        this[_0x39379d(0x2d4)] = this[_0x39379d(0x2d4)] || {};
        if (this[_0x39379d(0x2d4)][_0x4545ad] !== undefined)
            return this['_bestiaryEnemyCustomImageFilename'][_0x4545ad];
        this[_0x39379d(0x2d4)][_0x4545ad] = '';
        const _0x59b94a = VisuMZ[_0x39379d(0x2c5)][_0x39379d(0x2bc)],
            _0x39fa51 = _0x193a0c['note'] || '';
        return (
            _0x39fa51[_0x39379d(0x11c)](_0x59b94a[_0x39379d(0x2b8)]) &&
                (this[_0x39379d(0x2d4)][_0x4545ad] = String(RegExp['$1'])[_0x39379d(0x1ec)]()),
            this[_0x39379d(0x2d4)][_0x4545ad]
        );
    }),
    (TextManager['BestiaryMenuCommand'] =
        VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x227)][_0x249061(0x3b1)]),
    (TextManager[_0x249061(0x2c5)] = {
        buttonAssist: {
            view:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x39b)] ?? 'View',
            expand:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x204)] ??
                'Expand',
            collapse:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][_0x249061(0x39e)] ??
                _0x249061(0x25d),
            switch:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x364)] ??
                _0x249061(0x2d0),
            fastScrollLore:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][_0x249061(0x1eb)] ??
                _0x249061(0x3c5),
            slowScrollLore:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'buttonAssist_SlowScroll'
                ] ?? _0x249061(0x216),
        },
        categoryWindow: {
            maskChar: VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][_0x249061(0x3bb)] ?? '?',
            openCategoriesFmt:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x3c7)] ??
                _0x249061(0x3c1),
            closedCategoriesFmt:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'CategoryWindow_ClosedCategory'
                ] ?? _0x249061(0x1fb),
            fixedPercentage:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab']['CategoryPercentFixedDigits'] ??
                0x2,
        },
        nameWindow: {
            category:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x226)] ??
                'Please\x20select\x20a\x20monster\x20to\x20view.',
        },
        subWindow: {
            defeatedFmt:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x389)] ??
                _0x249061(0x1b1),
            seenFmt:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][_0x249061(0x393)] ??
                _0x249061(0x3bd),
            completionFmt:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x35c)] ??
                'Bestiary\x20Completion\x20Rate:\x20%1%\x20(%2/%3\x20Monsters)',
            fixedPercentage: VisuMZ['Bestiary']['Settings']['Vocab'][_0x249061(0x288)] ?? 0x2,
        },
        basicWindow: {
            levelUpToMax:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'BasicWindow_LevelUpToMax'
                ] ?? _0x249061(0x37e),
            levelUp:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab']['BasicWindow_LevelUpByOne'] ??
                _0x249061(0x133),
            levelDown:
                VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x2d2)] ??
                _0x249061(0x1a6),
            levelDownToMin:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'BasicWindow_LevelDownToMin'
                ] ?? '\x5cI[74]Lower\x20%1\x20Down\x20to\x20Minimum',
        },
        elementsWindow: {
            weak:
                VisuMZ[_0x249061(0x2c5)]['Settings']['Vocab'][_0x249061(0x178)] ?? '\x5cC[24]Weak',
            neutral:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'ElementsWindow_Neutral'
                ] ?? _0x249061(0x22c),
            resist:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x3b5)] ??
                _0x249061(0x1ac),
            immune:
                VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x2d7)] ??
                _0x249061(0x11a),
            absorb:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)]['ElementsWindow_Absorb'] ??
                _0x249061(0x386),
        },
        rewardsWindow: {
            chance100:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'RewardsWindow_Chance100'
                ] ?? _0x249061(0x275),
            chance50:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)]['RewardsWindow_Chance50'] ??
                '\x5cC[21]Common',
            chance20:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                    'RewardsWindow_Chance20'
                ] ?? _0x249061(0x143),
            chance10:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x25f)] ??
                _0x249061(0x3a5),
            chance0:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x159)] ??
                _0x249061(0x130),
            conditional:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x18c)] ??
                '\x5cC[17]Conditional',
        },
        traitsWindow: {
            openCategoriesFmt:
                VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x3a8)] ??
                _0x249061(0x325),
            closedCategoriesFmt:
                VisuMZ['Bestiary']['Settings'][_0x249061(0x388)][_0x249061(0x241)] ??
                _0x249061(0x1c8),
            traitHelp:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][
                    'TraitsWindow_CategoryHelpDesc'
                ] ?? 'This\x20is\x20the\x20property\x20type.',
            nullHelp:
                VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x331)] ??
                _0x249061(0x266),
        },
        loreWindow: {
            defaultLoreFmt:
                VisuMZ['Bestiary']['Settings'][_0x249061(0x388)]['LoreWindow_Default'] ??
                _0x249061(0x23b),
        },
    }),
    (TextManager[_0x249061(0x19f)] = function (_0xf703ad) {
        const _0x285f2f = _0x249061;
        if (!_0xf703ad) return '';
        const _0x559174 = _0xf703ad[_0x285f2f(0x2d9)]()['id'];
        this['_getBestiaryLore'] = this[_0x285f2f(0x165)] || {};
        if (this[_0x285f2f(0x165)][_0x559174] !== undefined)
            return this[_0x285f2f(0x165)][_0x559174];
        this[_0x285f2f(0x165)][_0x559174] = TextManager[_0x285f2f(0x2c5)][_0x285f2f(0x383)][
            _0x285f2f(0x10a)
        ][_0x285f2f(0x340)](_0xf703ad[_0x285f2f(0x2fe)]());
        const _0x46be29 = VisuMZ['Bestiary'][_0x285f2f(0x2bc)],
            _0xf09ea7 = _0xf703ad['enemy']()['note'] || '';
        return (
            _0xf09ea7['match'](_0x46be29['lore']) &&
                (this[_0x285f2f(0x165)][_0x559174] = String(RegExp['$1'])['trim']()),
            this[_0x285f2f(0x165)][_0x559174]
        );
    }),
    (ColorManager[_0x249061(0x25b)] = function (_0x560b72) {
        const _0x5eb6f8 = _0x249061;
        return (
            (_0x560b72 = String(_0x560b72)),
            _0x560b72['match'](/#(.*)/i)
                ? _0x5eb6f8(0x2d8)['format'](String(RegExp['$1']))
                : this['textColor'](Number(_0x560b72))
        );
    }),
    (SceneManager['isSceneBattle'] = function () {
        const _0x2e7916 = _0x249061;
        return this[_0x2e7916(0x12b)] && this[_0x2e7916(0x12b)]['constructor'] === Scene_Battle;
    }),
    (Game_Temp[_0x249061(0x19a)][_0x249061(0x1e1)] = function () {
        const _0x5c0196 = _0x249061;
        return this[_0x5c0196(0x1e7)]() && this[_0x5c0196(0x3a4)];
    }),
    (Game_Temp[_0x249061(0x19a)][_0x249061(0x15b)] = function (_0x3107f2) {
        const _0x3348e1 = _0x249061;
        this[_0x3348e1(0x3a4)] = _0x3107f2;
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x29c)] = Game_System['prototype']['initialize']),
    (Game_System[_0x249061(0x19a)][_0x249061(0x2f6)] = function () {
        const _0x498a1c = _0x249061;
        (VisuMZ[_0x498a1c(0x2c5)][_0x498a1c(0x29c)][_0x498a1c(0x310)](this),
            this[_0x498a1c(0x29b)](),
            this['initBestiarySettings']());
    }),
    (Game_System[_0x249061(0x19a)]['initBestiaryMainMenu'] = function () {
        const _0x42aa51 = _0x249061;
        this[_0x42aa51(0x345)] = {
            shown: VisuMZ[_0x42aa51(0x2c5)][_0x42aa51(0x27d)]['MainMenu'][_0x42aa51(0x136)],
            enabled: VisuMZ[_0x42aa51(0x2c5)][_0x42aa51(0x27d)][_0x42aa51(0x227)][_0x42aa51(0x1bd)],
        };
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x2ff)] = function () {
        const _0x4986a1 = _0x249061;
        if (this[_0x4986a1(0x345)] === undefined) this[_0x4986a1(0x29b)]();
        return this['_Bestiary_MainMenu'][_0x4986a1(0x242)];
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x2ca)] = function (_0x72acbe) {
        const _0x41ed94 = _0x249061;
        if (this['_Bestiary_MainMenu'] === undefined) this['initBestiaryMainMenu']();
        this[_0x41ed94(0x345)][_0x41ed94(0x242)] = _0x72acbe;
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x1f4)] = function () {
        const _0x243056 = _0x249061;
        if (this[_0x243056(0x345)] === undefined) this[_0x243056(0x29b)]();
        return this[_0x243056(0x345)]['enabled'];
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x2cc)] = function (_0x24e119) {
        const _0x2de417 = _0x249061;
        if (this[_0x2de417(0x345)] === undefined) this['initBestiaryMainMenu']();
        this['_Bestiary_MainMenu'][_0x2de417(0x20e)] = _0x24e119;
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x10f)] = function (_0x15858d) {
        const _0x7297f8 = _0x249061;
        return Imported[_0x7297f8(0x13e)]
            ? this[_0x7297f8(0x137)]()[_0x7297f8(0x2a7)](_0x15858d)
            : this[_0x7297f8(0x30d)](_0x15858d) > 0x0;
    }),
    (Game_System['prototype'][_0x249061(0x32e)] = function () {
        const _0xa9dece = _0x249061;
        ((this[_0xa9dece(0x26a)] = this[_0xa9dece(0x26a)] || {}),
            (this[_0xa9dece(0x28f)] = this['_timesEnemySeen'] || {}));
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x30d)] = function (_0x303a5a) {
        const _0x27e54a = _0x249061;
        if (this[_0x27e54a(0x26a)] === undefined) this['initBestiarySettings']();
        return (
            (this[_0x27e54a(0x26a)][_0x303a5a] = this['_timesEnemyDefeated'][_0x303a5a] || 0x0),
            this[_0x27e54a(0x26a)][_0x303a5a]
        );
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x2a3)] = function (_0x3eab31, _0x12d32e) {
        const _0x3cea0e = _0x249061;
        if (this[_0x3cea0e(0x26a)] === undefined) this[_0x3cea0e(0x32e)]();
        ((this[_0x3cea0e(0x26a)][_0x3eab31] = this[_0x3cea0e(0x26a)][_0x3eab31] || 0x0),
            (this[_0x3cea0e(0x26a)][_0x3eab31] += _0x12d32e || 0x1));
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x243)] = function () {
        const _0x4be127 = _0x249061;
        let _0x2e4ec8 = 0x0;
        for (const _0x6e14e8 of $dataEnemies) {
            if (!_0x6e14e8) continue;
            DataManager[_0x4be127(0x172)](_0x6e14e8) &&
                this['timesEnemyDefeated'](_0x6e14e8['id']) > 0x0 &&
                _0x2e4ec8++;
        }
        return _0x2e4ec8;
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x2a0)] =
        Game_BattlerBase[_0x249061(0x19a)]['addNewState']),
    (Game_BattlerBase[_0x249061(0x19a)][_0x249061(0x1c0)] = function (_0x4c3407) {
        const _0x59ac0b = _0x249061,
            _0x315ad5 = this['isAlive']();
        (VisuMZ['Bestiary'][_0x59ac0b(0x2a0)][_0x59ac0b(0x310)](this, _0x4c3407),
            this[_0x59ac0b(0x323)]() &&
                _0x315ad5 &&
                this[_0x59ac0b(0x38d)]() &&
                $gameSystem[_0x59ac0b(0x2a3)](this[_0x59ac0b(0x1fd)](), 0x1));
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x113)] = function (_0x382de8) {
        const _0x3d856a = _0x249061;
        if (this['_timesEnemyDefeated'] === undefined) this[_0x3d856a(0x32e)]();
        return (
            (this[_0x3d856a(0x26a)][_0x382de8] = this[_0x3d856a(0x26a)][_0x382de8] || 0x0),
            this['_timesEnemyDefeated'][_0x382de8]
        );
    }),
    (Game_System[_0x249061(0x19a)][_0x249061(0x156)] = function (_0x3d2384, _0x163060) {
        const _0x2c7420 = _0x249061;
        if (this[_0x2c7420(0x28f)] === undefined) this[_0x2c7420(0x32e)]();
        ((this['_timesEnemySeen'][_0x3d2384] = this[_0x2c7420(0x28f)][_0x3d2384] || 0x0),
            (this['_timesEnemySeen'][_0x3d2384] += _0x163060 || 0x1));
    }),
    (VisuMZ['Bestiary']['BattleManager_setup'] = BattleManager[_0x249061(0x362)]),
    (BattleManager[_0x249061(0x362)] = function (_0x44302e, _0x54e78f, _0x449167) {
        const _0x30e5e0 = _0x249061;
        VisuMZ[_0x30e5e0(0x2c5)][_0x30e5e0(0x37f)]['call'](this, _0x44302e, _0x54e78f, _0x449167);
        for (const _0x43fe7a of $gameTroop['members']()) {
            $gameSystem[_0x30e5e0(0x156)](_0x43fe7a['enemyId'](), 0x1);
        }
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x300)] =
        Game_BattlerBase[_0x249061(0x19a)][_0x249061(0x2ac)]),
    (Game_BattlerBase[_0x249061(0x19a)][_0x249061(0x2ac)] = function () {
        const _0x22894b = _0x249061;
        ((this[_0x22894b(0x317)] = {}),
            (this[_0x22894b(0x2c1)] = this[_0x22894b(0x2c1)]['clamp'](
                0x0,
                this[_0x22894b(0x1e2)]()
            )),
            VisuMZ[_0x22894b(0x2c5)][_0x22894b(0x300)][_0x22894b(0x310)](this));
    }),
    (Game_Enemy['BESTIARY'] = {
        defaultCategory:
            VisuMZ['Bestiary'][_0x249061(0x27d)]['DefaultCategory'] ?? _0x249061(0x168),
    }),
    (Game_Enemy[_0x249061(0x19a)][_0x249061(0x1df)] = function () {
        const _0x3c26df = _0x249061,
            _0x22f193 = [];
        for (const _0x5e7206 of this[_0x3c26df(0x2d9)]()[_0x3c26df(0x209)]) {
            const _0x54cb2e = $dataSkills[_0x5e7206[_0x3c26df(0x1a0)]];
            if (_0x54cb2e && !_0x22f193[_0x3c26df(0x2a7)](_0x54cb2e))
                _0x22f193[_0x3c26df(0x2a5)](_0x54cb2e);
        }
        return _0x22f193;
    }),
    (VisuMZ[_0x249061(0x2c5)]['PossibleEnemyTraits'] = function (_0x40b200, _0x3dc2bc) {
        const _0x59cf96 = _0x249061;
        let _0x1a8734 = [];
        const _0x27cb83 = _0x3dc2bc['enemy']()[_0x59cf96(0x274)] || '';
        (this[_0x59cf96(0x142)](_0x1a8734, _0x40b200, _0x27cb83),
            this[_0x59cf96(0x2c2)](_0x1a8734, _0x40b200, _0x27cb83),
            this[_0x59cf96(0x293)](_0x1a8734, _0x40b200, _0x27cb83));
        if (_0x1a8734[_0x59cf96(0x36f)] <= 0x0) {
            const _0x26fd2a = DataManager[_0x59cf96(0x2a6)](_0x40b200);
            if (_0x26fd2a['RandomizeEnemy']) {
                _0x26fd2a[_0x59cf96(0x327)]['RandomValid'] &&
                    _0x1a8734['push'](_0x26fd2a['Default'][_0x59cf96(0x3b1)]);
                for (const _0x48384b in _0x26fd2a['List']) {
                    _0x1a8734[_0x59cf96(0x2a5)](
                        _0x26fd2a[_0x59cf96(0x179)][_0x48384b][_0x59cf96(0x3b1)]
                    );
                }
                return _0x1a8734[_0x59cf96(0x336)](_0x1baecf =>
                    String(_0x1baecf)[_0x59cf96(0x199)]()[_0x59cf96(0x1ec)]()
                );
            }
        }
        return _0x1a8734[_0x59cf96(0x336)](_0x4b6efa =>
            String(_0x4b6efa)['toUpperCase']()[_0x59cf96(0x1ec)]()
        );
    }),
    (VisuMZ[_0x249061(0x2c5)]['PossibleMassTraitsFromNotetags'] = function (
        _0x1ba603,
        _0xec7301,
        _0x43a06d
    ) {
        const _0x21162a = _0x249061,
            _0x4cf89f = {
                ELEMENT: _0x21162a(0x357),
                SUBELEMENT: _0x21162a(0x254),
                GENDER: _0x21162a(0x297),
                RACE: _0x21162a(0x214),
                NATURE: _0x21162a(0x21f),
                ALIGNMENT: 'Alignment',
                BLESSING: _0x21162a(0x194),
                CURSE: 'Curse',
                ZODIAC: _0x21162a(0x22f),
                VARIANT: _0x21162a(0x3b6),
            };
        if (_0x43a06d[_0x21162a(0x11c)](/<TRAIT SETS>\s*([\s\S]*)\s*<\/TRAIT SETS>/i)) {
            const _0x315174 = String(RegExp['$1'])[_0x21162a(0x2f7)](/[\r\n]+/);
            for (const _0x101de1 of _0x315174) {
                if (_0x101de1[_0x21162a(0x11c)](/(.*):[ ](.*)/i)) {
                    const _0x55f712 = String(RegExp['$1'])['toUpperCase']()[_0x21162a(0x1ec)](),
                        _0x4d431e = String(RegExp['$2'])[_0x21162a(0x2f7)](','),
                        _0x5277e4 = _0x4cf89f[_0x55f712];
                    _0x5277e4 &&
                        _0x5277e4 === _0xec7301 &&
                        (_0x1ba603 = _0x1ba603[_0x21162a(0x146)](_0x4d431e));
                }
            }
        }
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x2c2)] = function (_0x6c41b5, _0x3ff221, _0x3a3814) {
        const _0x5541a3 = _0x249061,
            _0xd8e2fe = {
                Element: /<ELEMENT:[ ](.*)>/i,
                SubElement: /<SUBELEMENT:[ ](.*)>/i,
                Gender: /<GENDER:[ ](.*)>/i,
                Race: /<RACE:[ ](.*)>/i,
                Nature: /<NATURE:[ ](.*)>/i,
                Alignment: /<ALIGNMENT:[ ](.*)>/i,
                Blessing: /<BLESSING:[ ](.*)>/i,
                Curse: /<CURSE:[ ](.*)>/i,
                Zodiac: /<ZODIAC:[ ](.*)>/i,
                Variant: /<VARIANT:[ ](.*)>/i,
            },
            _0x41560a = _0xd8e2fe[_0x3ff221];
        if (!_0x41560a) return;
        if (_0x3a3814[_0x5541a3(0x11c)](/<ELEMENT:[ ](.*)\/(.*)>/i)) {
            if (_0x3ff221 === 'Element')
                _0x6c41b5[_0x5541a3(0x2a5)](String(RegExp['$1'])['trim']());
            if (_0x3ff221 === 'SubElement')
                _0x6c41b5[_0x5541a3(0x2a5)](String(RegExp['$2'])[_0x5541a3(0x1ec)]());
        } else {
            if (_0x3a3814[_0x5541a3(0x11c)](_0x41560a)) {
                const _0x483d78 = String(RegExp['$2'])[_0x5541a3(0x2f7)](',');
                _0x6c41b5 = _0x6c41b5[_0x5541a3(0x146)](_0x483d78);
            }
        }
    }),
    (VisuMZ['Bestiary'][_0x249061(0x293)] = function (_0xbdebb6, _0x4be081, _0x40a0ac) {
        const _0x3ebb0c = _0x249061,
            _0x1b8b5e = {
                Element: /<RANDOM ELEMENT>\s*([\s\S]*)\s*<\/RANDOM ELEMENT>/i,
                SubElement: /<RANDOM SUBELEMENT>\s*([\s\S]*)\s*<\/RANDOM SUBELEMENT>/i,
                Gender: /<RANDOM GENDER>\s*([\s\S]*)\s*<\/RANDOM GENDER>/i,
                Race: /<RANDOM RACE>\s*([\s\S]*)\s*<\/RANDOM RACE>/i,
                Nature: /<RANDOM NATURE>\s*([\s\S]*)\s*<\/RANDOM NATURE>/i,
                Alignment: /<RANDOM ALIGNMENT>\s*([\s\S]*)\s*<\/RANDOM ALIGNMENT>/i,
                Blessing: /<RANDOM BLESSING>\s*([\s\S]*)\s*<\/RANDOM BLESSING>/i,
                Curse: /<RANDOM CURSE>\s*([\s\S]*)\s*<\/RANDOM CURSE>/i,
                Zodiac: /<RANDOM ZODIAC>\s*([\s\S]*)\s*<\/RANDOM ZODIAC>/i,
                Variant: /<RANDOM VARIANT>\s*([\s\S]*)\s*<\/RANDOM VARIANT>/i,
            },
            _0x3145b2 = _0x1b8b5e[_0x4be081];
        if (!_0x3145b2) return;
        if (_0x40a0ac[_0x3ebb0c(0x11c)](_0x3145b2)) {
            const _0x37ab7e = String(RegExp['$1'])
                [_0x3ebb0c(0x2f7)](/[\r\n]+/)
                [_0x3ebb0c(0x2c9)]('');
            for (const _0x25c50d of _0x37ab7e) {
                _0x25c50d[_0x3ebb0c(0x11c)](/(.*):[ ](.*)/i) &&
                    _0xbdebb6['push'](RegExp['$1'][_0x3ebb0c(0x1ec)]());
            }
        }
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x2dd)] = Scene_Menu[_0x249061(0x19a)][_0x249061(0x397)]),
    (Scene_Menu[_0x249061(0x19a)][_0x249061(0x397)] = function () {
        const _0x566d6f = _0x249061;
        VisuMZ[_0x566d6f(0x2c5)][_0x566d6f(0x2dd)][_0x566d6f(0x310)](this);
        const _0x41b933 = this[_0x566d6f(0x1b5)];
        _0x41b933['setHandler'](_0x566d6f(0x1cd), this[_0x566d6f(0x2ee)][_0x566d6f(0x244)](this));
    }),
    (Scene_Menu['prototype'][_0x249061(0x2ee)] = function () {
        const _0x53ac73 = _0x249061;
        SceneManager[_0x53ac73(0x2a5)](Scene_Bestiary);
    }));
function Scene_Bestiary() {
    this['initialize'](...arguments);
}
((Scene_Bestiary['prototype'] = Object[_0x249061(0x2fa)](Scene_MenuBase[_0x249061(0x19a)])),
    (Scene_Bestiary['prototype'][_0x249061(0x28d)] = Scene_Bestiary),
    (Scene_Bestiary[_0x249061(0x147)] = {
        helpWindow_BgType:
            VisuMZ['Bestiary']['Settings'][_0x249061(0x144)][_0x249061(0x2e2)] ?? 0x0,
        scaleHelpWindow:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)]['HelpWindow_ScaleRatio'] ??
            !![],
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x2f6)] = function () {
        const _0x2a5af3 = _0x249061;
        Scene_MenuBase['prototype']['initialize'][_0x2a5af3(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x2ab)] = function () {
        return 0x0;
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x2b4)] = function () {
        return !![];
    }),
    (Scene_Bestiary['prototype']['createPageButtons'] = function () {
        const _0x5ea83c = _0x249061;
        (Scene_MenuBase[_0x5ea83c(0x19a)][_0x5ea83c(0x22b)]['call'](this),
            this[_0x5ea83c(0x28c)][_0x5ea83c(0x20f)](this['prevEnemy'][_0x5ea83c(0x244)](this)),
            this[_0x5ea83c(0x1aa)][_0x5ea83c(0x20f)](
                this[_0x5ea83c(0x3a1)][_0x5ea83c(0x244)](this)
            ));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x316)] = function () {
        const _0x1ae32f = _0x249061;
        return this[_0x1ae32f(0x25c)] && this['_dataCategoriesWindow'][_0x1ae32f(0x203)];
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['create'] = function () {
        const _0x453c32 = _0x249061;
        (Scene_MenuBase[_0x453c32(0x19a)][_0x453c32(0x2fa)][_0x453c32(0x310)](this),
            this[_0x453c32(0x356)](),
            this[_0x453c32(0x276)](),
            this[_0x453c32(0x185)]());
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x17f)] = function () {
        const _0x4b5b79 = _0x249061;
        if (ConfigManager[_0x4b5b79(0x127)] !== undefined) {
            if (ConfigManager[_0x4b5b79(0x127)]) return ConfigManager[_0x4b5b79(0x341)];
        }
        return Scene_MenuBase[_0x4b5b79(0x19a)][_0x4b5b79(0x17f)][_0x4b5b79(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x30c)] = function () {
        const _0x1adbfb = _0x249061;
        if (ConfigManager[_0x1adbfb(0x127)] !== undefined) {
            if (ConfigManager[_0x1adbfb(0x127)]) return ConfigManager[_0x1adbfb(0x270)];
        }
        return Scene_MenuBase[_0x1adbfb(0x19a)][_0x1adbfb(0x30c)][_0x1adbfb(0x310)](this);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x356)] = function () {
        this['_enemy'] = new Game_Enemy(0x1, 0x0, 0x0);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x2d9)] = function () {
        const _0x5de7f4 = _0x249061;
        return this[_0x5de7f4(0x352)];
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x21c)] = function (_0x45cf8b) {
        const _0x2209c4 = _0x249061;
        ((Math[_0x2209c4(0x23d)] = !![]),
            this[_0x2209c4(0x2d9)]()[_0x2209c4(0x362)](_0x45cf8b, 0x0, 0x0),
            (Math[_0x2209c4(0x23d)] = ![]));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x276)] = function () {
        const _0x4e8f3f = _0x249061;
        (this[_0x4e8f3f(0x219)](),
            this[_0x4e8f3f(0x18f)](),
            this[_0x4e8f3f(0x24f)](),
            this['createImageWindow'](),
            this[_0x4e8f3f(0x20c)](),
            this[_0x4e8f3f(0x170)](),
            this[_0x4e8f3f(0x105)](),
            this[_0x4e8f3f(0x3a2)](),
            this[_0x4e8f3f(0x306)](),
            this[_0x4e8f3f(0x1a4)](),
            this[_0x4e8f3f(0x282)](),
            this[_0x4e8f3f(0x235)]());
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['createHelpWindow'] = function () {
        const _0x41efa2 = _0x249061;
        Scene_MenuBase[_0x41efa2(0x19a)][_0x41efa2(0x170)]['call'](this);
        if (Scene_Bestiary[_0x41efa2(0x147)][_0x41efa2(0x157)]) {
            const _0x2cd652 = this[_0x41efa2(0x34f)]();
            this[_0x41efa2(0x369)][_0x41efa2(0x238)]['x'] = this['_helpWindow']['scale']['y'] =
                _0x2cd652;
        }
        (this[_0x41efa2(0x369)][_0x41efa2(0x32a)](
            Scene_Bestiary[_0x41efa2(0x147)][_0x41efa2(0x360)]
        ),
            this[_0x41efa2(0x369)][_0x41efa2(0x332)]());
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x34f)] = function () {
        const _0x43a6bf = _0x249061;
        if (!Scene_Bestiary[_0x43a6bf(0x147)]['scaleHelpWindow']) return 0x1;
        return this[_0x43a6bf(0x10b)]()[_0x43a6bf(0x171)] / Graphics[_0x43a6bf(0x351)];
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x314)] = function () {
        const _0x2e558b = _0x249061;
        if (VisuMZ[_0x2e558b(0x2c5)][_0x2e558b(0x27d)][_0x2e558b(0x144)][_0x2e558b(0x228)])
            return VisuMZ[_0x2e558b(0x2c5)][_0x2e558b(0x27d)]['Window'][_0x2e558b(0x228)][
                _0x2e558b(0x310)
            ](this);
        const _0x273c20 = this['imageWindowRect'](),
            _0x4e8fdc = this[_0x2e558b(0x34f)](),
            _0x43b347 = Graphics[_0x2e558b(0x351)],
            _0x6a2011 = this[_0x2e558b(0x261)](0x2, ![]),
            _0x445610 = _0x273c20['x'],
            _0x2a5349 =
                _0x273c20['y'] +
                (this[_0x2e558b(0x17f)]()
                    ? _0x273c20[_0x2e558b(0x379)] - _0x6a2011 * _0x4e8fdc
                    : 0x0);
        return new Rectangle(_0x445610, _0x2a5349, _0x43b347, _0x6a2011);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x219)] = function () {
        const _0x2bdbf3 = _0x249061,
            _0x59c6c8 = this[_0x2bdbf3(0x186)](),
            _0x10a6e7 = new Window_BestiaryName(_0x59c6c8);
        (_0x10a6e7[_0x2bdbf3(0x324)](),
            this[_0x2bdbf3(0x175)](_0x10a6e7),
            (this[_0x2bdbf3(0x1e3)] = _0x10a6e7),
            _0x10a6e7[_0x2bdbf3(0x32a)](Window_BestiaryName['SETTINGS'][_0x2bdbf3(0x361)]));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x186)] = function () {
        const _0x373fe6 = _0x249061;
        if (VisuMZ['Bestiary']['Settings']['Window'][_0x373fe6(0x125)])
            return VisuMZ[_0x373fe6(0x2c5)][_0x373fe6(0x27d)]['Window'][_0x373fe6(0x125)][
                _0x373fe6(0x310)
            ](this);
        const _0x4bcfe8 = Graphics[_0x373fe6(0x351)],
            _0x3e2289 = this['calcWindowHeight'](0x1, ![]),
            _0x3d7e6d = 0x0,
            _0x1bb89f = this['mainAreaTop']();
        return new Rectangle(_0x3d7e6d, _0x1bb89f, _0x4bcfe8, _0x3e2289);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x18f)] = function () {
        const _0x408211 = _0x249061,
            _0x51391e = this['subWindowRect'](),
            _0x528a96 = new Window_BestiarySub(_0x51391e);
        (this[_0x408211(0x175)](_0x528a96),
            (this[_0x408211(0x17a)] = _0x528a96),
            _0x528a96[_0x408211(0x32a)](Window_BestiarySub[_0x408211(0x147)][_0x408211(0x361)]));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x256)] = function () {
        const _0x2bc16d = _0x249061;
        if (VisuMZ[_0x2bc16d(0x2c5)][_0x2bc16d(0x27d)][_0x2bc16d(0x144)][_0x2bc16d(0x16c)])
            return VisuMZ[_0x2bc16d(0x2c5)][_0x2bc16d(0x27d)][_0x2bc16d(0x144)][_0x2bc16d(0x16c)][
                _0x2bc16d(0x310)
            ](this);
        const _0x1b7a0f = Graphics[_0x2bc16d(0x351)],
            _0x380a6f = this[_0x2bc16d(0x261)](0x1, ![]),
            _0x42916a = 0x0,
            _0x59d8a2 = this[_0x2bc16d(0x2f2)]() - _0x380a6f;
        return new Rectangle(_0x42916a, _0x59d8a2, _0x1b7a0f, _0x380a6f);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x24f)] = function () {
        const _0x421971 = _0x249061,
            _0x4c61ce = this['listWindowRect'](),
            _0x5471b4 = new Window_BestiaryEnemyList(_0x4c61ce);
        (_0x5471b4[_0x421971(0x14e)](this[_0x421971(0x17a)]),
            _0x5471b4[_0x421971(0x381)](
                _0x421971(0x107),
                this[_0x421971(0x26c)][_0x421971(0x244)](this)
            ),
            _0x5471b4[_0x421971(0x381)](
                _0x421971(0x2d9),
                this[_0x421971(0x333)][_0x421971(0x244)](this)
            ),
            _0x5471b4[_0x421971(0x381)]('cancel', this['popScene'][_0x421971(0x244)](this)),
            this['addWindow'](_0x5471b4),
            (this[_0x421971(0x232)] = _0x5471b4),
            _0x5471b4['setBackgroundType'](
                Window_BestiaryEnemyList[_0x421971(0x147)][_0x421971(0x361)]
            ));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x2ae)] = function () {
        const _0x543729 = _0x249061;
        if (VisuMZ[_0x543729(0x2c5)]['Settings'][_0x543729(0x144)][_0x543729(0x2c0)])
            return VisuMZ[_0x543729(0x2c5)][_0x543729(0x27d)][_0x543729(0x144)][_0x543729(0x2c0)][
                _0x543729(0x310)
            ](this);
        const _0x17fef5 = Math['ceil']((Graphics[_0x543729(0x351)] * 0x4) / 0xa),
            _0x259913 = this['mainAreaHeight']() - this[_0x543729(0x261)](0x1, ![]) * 0x2,
            _0x5c3ffe = this[_0x543729(0x30c)]() ? Graphics[_0x543729(0x351)] - _0x17fef5 : 0x0,
            _0x107d29 = this['mainAreaTop']() + this[_0x543729(0x261)](0x1, ![]);
        return new Rectangle(_0x5c3ffe, _0x107d29, _0x17fef5, _0x259913);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x141)] = function () {
        const _0x1c73e6 = _0x249061,
            _0x3efa9a = this[_0x1c73e6(0x10b)](),
            _0x4624b4 = new Window_BestiaryEnemyImage(_0x3efa9a);
        (this['_listWindow']['setImageWindow'](_0x4624b4),
            this[_0x1c73e6(0x175)](_0x4624b4),
            (this[_0x1c73e6(0x1f2)] = _0x4624b4),
            _0x4624b4[_0x1c73e6(0x32a)](
                Window_BestiaryEnemyImage[_0x1c73e6(0x147)][_0x1c73e6(0x361)]
            ));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x10b)] = function () {
        const _0x372c8b = _0x249061;
        if (VisuMZ[_0x372c8b(0x2c5)][_0x372c8b(0x27d)]['Window']['ImageWindow_RectJS'])
            return VisuMZ[_0x372c8b(0x2c5)][_0x372c8b(0x27d)][_0x372c8b(0x144)][_0x372c8b(0x33c)][
                _0x372c8b(0x310)
            ](this);
        const _0xd85bb4 =
                Graphics[_0x372c8b(0x351)] - Math['ceil']((Graphics['boxWidth'] * 0x4) / 0xa),
            _0x29bb6d = this[_0x372c8b(0x121)]() - this[_0x372c8b(0x261)](0x1, ![]) * 0x2,
            _0x3168a3 = this[_0x372c8b(0x30c)]() ? 0x0 : Graphics[_0x372c8b(0x351)] - _0xd85bb4,
            _0x302b0e = this[_0x372c8b(0x1ab)]() + this['calcWindowHeight'](0x1, ![]);
        return new Rectangle(_0x3168a3, _0x302b0e, _0xd85bb4, _0x29bb6d);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x20c)] = function () {
        const _0x8cfd2f = _0x249061,
            _0x3459c3 = this['dataCategoriesWindowRect'](),
            _0x21d880 = new Window_BestiaryDataCategories(_0x3459c3);
        (_0x21d880['setHandler'](_0x8cfd2f(0x328), this[_0x8cfd2f(0x164)][_0x8cfd2f(0x244)](this)),
            _0x21d880[_0x8cfd2f(0x381)](
                _0x8cfd2f(0x326),
                this[_0x8cfd2f(0x164)][_0x8cfd2f(0x244)](this)
            ),
            _0x21d880[_0x8cfd2f(0x381)](
                _0x8cfd2f(0x1df),
                this[_0x8cfd2f(0x164)][_0x8cfd2f(0x244)](this)
            ),
            _0x21d880[_0x8cfd2f(0x381)](_0x8cfd2f(0x31b), this[_0x8cfd2f(0x164)]['bind'](this)),
            _0x21d880[_0x8cfd2f(0x381)](_0x8cfd2f(0x387), this[_0x8cfd2f(0x164)]['bind'](this)),
            _0x21d880[_0x8cfd2f(0x381)](
                _0x8cfd2f(0x2c8),
                this[_0x8cfd2f(0x164)][_0x8cfd2f(0x244)](this)
            ),
            _0x21d880[_0x8cfd2f(0x381)](
                _0x8cfd2f(0x39f),
                this[_0x8cfd2f(0x167)][_0x8cfd2f(0x244)](this)
            ),
            _0x21d880['setHandler'](
                _0x8cfd2f(0x2b9),
                this[_0x8cfd2f(0x3a1)][_0x8cfd2f(0x244)](this)
            ),
            _0x21d880[_0x8cfd2f(0x381)](
                _0x8cfd2f(0x259),
                this['onDataCategoriesCancel'][_0x8cfd2f(0x244)](this)
            ),
            this[_0x8cfd2f(0x175)](_0x21d880),
            (this[_0x8cfd2f(0x25c)] = _0x21d880),
            _0x21d880[_0x8cfd2f(0x32a)](
                Window_BestiaryDataCategories['SETTINGS'][_0x8cfd2f(0x361)]
            ));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x1e5)] = function () {
        const _0xa63760 = _0x249061;
        if (VisuMZ[_0xa63760(0x2c5)]['Settings'][_0xa63760(0x144)]['DataCategoriesWindow_RectJS'])
            return VisuMZ[_0xa63760(0x2c5)]['Settings'][_0xa63760(0x144)][_0xa63760(0x17b)][
                _0xa63760(0x310)
            ](this);
        const _0x484b68 = this[_0xa63760(0x2ae)]()[_0xa63760(0x171)],
            _0x14245e = this[_0xa63760(0x261)](0x1, !![]),
            _0x289f4d = this['listWindowRect']()['x'],
            _0x24f3f6 = this['mainAreaTop']() + this['calcWindowHeight'](0x1, ![]);
        return new Rectangle(_0x289f4d, _0x24f3f6, _0x484b68, _0x14245e);
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['dataWindowRect'] = function () {
        const _0x4965c4 = _0x249061;
        if (VisuMZ[_0x4965c4(0x2c5)][_0x4965c4(0x27d)][_0x4965c4(0x144)][_0x4965c4(0x2dc)])
            return VisuMZ[_0x4965c4(0x2c5)][_0x4965c4(0x27d)][_0x4965c4(0x144)][_0x4965c4(0x2dc)][
                _0x4965c4(0x310)
            ](this);
        const _0x1f1b6b = this['listWindowRect']()[_0x4965c4(0x171)],
            _0x20b44b =
                this[_0x4965c4(0x121)]() -
                this[_0x4965c4(0x261)](0x1, !![]) -
                this[_0x4965c4(0x261)](0x1, ![]) * 0x2,
            _0x50afa9 = this[_0x4965c4(0x2ae)]()['x'],
            _0x2f5082 =
                this['mainAreaTop']() +
                this[_0x4965c4(0x261)](0x1, ![]) +
                this[_0x4965c4(0x261)](0x1, !![]);
        return new Rectangle(_0x50afa9, _0x2f5082, _0x1f1b6b, _0x20b44b);
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['createBasicDataWindow'] = function () {
        const _0x132fe5 = _0x249061,
            _0x3b3845 = this['dataWindowRect'](),
            _0x38e2da = new Window_BestiaryBasic(_0x3b3845);
        (this[_0x132fe5(0x25c)][_0x132fe5(0x3b7)](_0x38e2da, 'basic'),
            _0x38e2da[_0x132fe5(0x381)](
                'levelMax',
                this[_0x132fe5(0x2f0)]['bind'](this, _0x132fe5(0x2b7))
            ),
            _0x38e2da[_0x132fe5(0x381)](
                _0x132fe5(0x344),
                this['onBasicDataLevelChange'][_0x132fe5(0x244)](this, 'up')
            ),
            _0x38e2da[_0x132fe5(0x381)](
                _0x132fe5(0x10d),
                this[_0x132fe5(0x2f0)]['bind'](this, _0x132fe5(0x3b8))
            ),
            _0x38e2da[_0x132fe5(0x381)](
                _0x132fe5(0x21e),
                this['onBasicDataLevelChange'][_0x132fe5(0x244)](this, _0x132fe5(0x1b4))
            ),
            _0x38e2da[_0x132fe5(0x381)](_0x132fe5(0x259), this[_0x132fe5(0x229)]['bind'](this)),
            this[_0x132fe5(0x175)](_0x38e2da),
            (this[_0x132fe5(0x2d5)] = _0x38e2da),
            _0x38e2da['setBackgroundType'](
                Window_BestiaryBasic[_0x132fe5(0x147)][_0x132fe5(0x361)]
            ));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x3a2)] = function () {
        const _0x455f22 = _0x249061,
            _0x5d2133 = this[_0x455f22(0x138)](),
            _0x2fff7a = new Window_BestiaryElements(_0x5d2133);
        (this[_0x455f22(0x25c)][_0x455f22(0x3b7)](_0x2fff7a, 'elements'),
            _0x2fff7a['setHandler'](_0x455f22(0x259), this['onSymbolWindowCancel']['bind'](this)),
            this['addWindow'](_0x2fff7a),
            (this[_0x455f22(0x2fb)] = _0x2fff7a),
            _0x2fff7a[_0x455f22(0x32a)](
                Window_BestiaryElements[_0x455f22(0x147)][_0x455f22(0x361)]
            ));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x306)] = function () {
        const _0x2d8086 = _0x249061,
            _0xca24d = this[_0x2d8086(0x138)](),
            _0x30014d = new Window_BestiarySkills(_0xca24d);
        (_0x30014d[_0x2d8086(0x12d)](this[_0x2d8086(0x369)]),
            this['_dataCategoriesWindow'][_0x2d8086(0x3b7)](_0x30014d, _0x2d8086(0x1df)),
            _0x30014d[_0x2d8086(0x381)](_0x2d8086(0x259), this[_0x2d8086(0x229)]['bind'](this)),
            this[_0x2d8086(0x175)](_0x30014d),
            (this[_0x2d8086(0x1c3)] = _0x30014d),
            _0x30014d[_0x2d8086(0x32a)](Window_BestiarySkills[_0x2d8086(0x147)]['bgType']));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x1a4)] = function () {
        const _0x1de2ab = _0x249061,
            _0x5b76a0 = this['dataWindowRect'](),
            _0x1eceb2 = new Window_BestiaryRewards(_0x5b76a0);
        (this['_dataCategoriesWindow'][_0x1de2ab(0x3b7)](_0x1eceb2, _0x1de2ab(0x31b)),
            _0x1eceb2[_0x1de2ab(0x381)](
                _0x1de2ab(0x259),
                this['onSymbolWindowCancel']['bind'](this)
            ),
            this['addWindow'](_0x1eceb2),
            (this[_0x1de2ab(0x37b)] = _0x1eceb2),
            _0x1eceb2[_0x1de2ab(0x32a)](Window_BestiaryRewards['SETTINGS'][_0x1de2ab(0x361)]));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x282)] = function () {
        const _0x3eb981 = _0x249061;
        if (!Imported[_0x3eb981(0x1a3)]) return;
        const _0x34fc3f = this[_0x3eb981(0x138)](),
            _0x25b5ce = new Window_BestiaryTraits(_0x34fc3f);
        (_0x25b5ce[_0x3eb981(0x12d)](this['_helpWindow']),
            this['_dataCategoriesWindow'][_0x3eb981(0x3b7)](_0x25b5ce, 'traits'),
            _0x25b5ce[_0x3eb981(0x381)](
                _0x3eb981(0x107),
                this[_0x3eb981(0x2e5)][_0x3eb981(0x244)](this)
            ),
            _0x25b5ce[_0x3eb981(0x381)](
                _0x3eb981(0x2e8),
                this[_0x3eb981(0x34d)][_0x3eb981(0x244)](this)
            ),
            _0x25b5ce['setHandler'](_0x3eb981(0x259), this['onSymbolWindowCancel']['bind'](this)),
            this['addWindow'](_0x25b5ce),
            (this[_0x3eb981(0x378)] = _0x25b5ce),
            _0x25b5ce[_0x3eb981(0x32a)](Window_BestiaryTraits['SETTINGS']['bgType']));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x235)] = function () {
        const _0x41357c = _0x249061,
            _0x13de5c = this[_0x41357c(0x138)](),
            _0x373577 = new Window_BestiaryLore(_0x13de5c);
        (this['_dataCategoriesWindow'][_0x41357c(0x3b7)](_0x373577, _0x41357c(0x2c8)),
            _0x373577[_0x41357c(0x381)](_0x41357c(0x259), this[_0x41357c(0x229)]['bind'](this)),
            this[_0x41357c(0x175)](_0x373577),
            (this[_0x41357c(0x1ba)] = _0x373577),
            _0x373577[_0x41357c(0x32a)](Window_BestiaryLore['SETTINGS'][_0x41357c(0x361)]));
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['toggleEnemyCategory'] = function () {
        const _0x1c3bc2 = _0x249061;
        (this['_listWindow'][_0x1c3bc2(0x3b4)](), this[_0x1c3bc2(0x232)][_0x1c3bc2(0x32f)]());
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x333)] = function () {
        const _0x231bcf = _0x249061;
        (this[_0x231bcf(0x232)][_0x231bcf(0x26e)](this['_listWindow'][_0x231bcf(0x1b7)]()),
            this[_0x231bcf(0x232)]['hide'](),
            this[_0x231bcf(0x25c)][_0x231bcf(0x202)](),
            this[_0x231bcf(0x25c)][_0x231bcf(0x32f)]());
        const _0x2810b4 = this['_listWindow'][_0x231bcf(0x1f0)](),
            _0x5b40cf = this[_0x231bcf(0x2d9)]();
        (this['_nameWindow'][_0x231bcf(0x260)](_0x5b40cf),
            this[_0x231bcf(0x17a)][_0x231bcf(0x337)](_0x2810b4));
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x164)] = function () {
        const _0x5933cd = _0x249061;
        this['_dataCategoriesWindow'][_0x5933cd(0x225)]();
    }),
    (Scene_Bestiary['prototype']['nextEnemy'] = function () {
        const _0x154f69 = _0x249061;
        let _0x33d74d = this[_0x154f69(0x232)][_0x154f69(0x1b7)]();
        const _0x2d497c = this[_0x154f69(0x232)][_0x154f69(0x1f0)]();
        for (;;) {
            _0x33d74d >= this[_0x154f69(0x232)][_0x154f69(0x290)]() - 0x1
                ? (_0x33d74d = 0x0)
                : (_0x33d74d += 0x1);
            if (
                this['_listWindow'][_0x154f69(0x1c4)](_0x33d74d) &&
                this['_listWindow'][_0x154f69(0x2ad)](_0x33d74d) === _0x154f69(0x2d9)
            ) {
                (this[_0x154f69(0x232)][_0x154f69(0x1ce)](_0x33d74d),
                    this[_0x154f69(0x21c)](this['_listWindow'][_0x154f69(0x1f0)]()),
                    this[_0x154f69(0x232)][_0x154f69(0x26e)](this['_listWindow']['index']()));
                break;
            }
        }
        SoundManager[_0x154f69(0x12e)]();
        if (_0x2d497c !== this['_listWindow'][_0x154f69(0x1f0)]()) this[_0x154f69(0x3bf)]();
        this[_0x154f69(0x25c)]['activate']();
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x167)] = function () {
        const _0x551768 = _0x249061;
        let _0x517687 = this[_0x551768(0x232)][_0x551768(0x1b7)]();
        const _0x541f07 = this[_0x551768(0x232)]['currentExt']();
        for (;;) {
            _0x517687 <= 0x0
                ? (_0x517687 = this['_listWindow'][_0x551768(0x290)]() - 0x1)
                : (_0x517687 -= 0x1);
            if (
                this[_0x551768(0x232)][_0x551768(0x1c4)](_0x517687) &&
                this['_listWindow'][_0x551768(0x2ad)](_0x517687) === _0x551768(0x2d9)
            ) {
                (this[_0x551768(0x232)]['smoothSelect'](_0x517687),
                    this[_0x551768(0x21c)](this[_0x551768(0x232)][_0x551768(0x1f0)]()),
                    this[_0x551768(0x232)]['callUpdateImage'](
                        this[_0x551768(0x232)][_0x551768(0x1b7)]()
                    ));
                break;
            }
        }
        SoundManager[_0x551768(0x12e)]();
        if (_0x541f07 !== this[_0x551768(0x232)]['currentExt']()) this[_0x551768(0x3bf)]();
        this[_0x551768(0x25c)][_0x551768(0x32f)]();
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x3bf)] = function () {
        const _0x3ec29c = _0x249061;
        (this[_0x3ec29c(0x1e3)][_0x3ec29c(0x260)](this['enemy']()),
            this[_0x3ec29c(0x17a)][_0x3ec29c(0x337)](this[_0x3ec29c(0x2d9)]()));
    }),
    (Scene_Bestiary['prototype']['onDataCategoriesCancel'] = function () {
        const _0x2d63df = _0x249061;
        (this['_dataCategoriesWindow'][_0x2d63df(0x332)](),
            this[_0x2d63df(0x232)][_0x2d63df(0x202)](),
            this[_0x2d63df(0x232)][_0x2d63df(0x32f)](),
            this['_nameWindow'][_0x2d63df(0x324)](),
            this[_0x2d63df(0x17a)][_0x2d63df(0x337)](0x0));
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['onSymbolWindowCancel'] = function () {
        const _0x54c5d4 = _0x249061;
        (this[_0x54c5d4(0x25c)]['activate'](),
            this[_0x54c5d4(0x25c)][_0x54c5d4(0x3c3)](),
            this[_0x54c5d4(0x369)]['hide']());
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x2f0)] = function (_0x3e5a6c) {
        const _0x3206c3 = _0x249061;
        if (_0x3e5a6c === _0x3206c3(0x2b7))
            this[_0x3206c3(0x2d9)]()[_0x3206c3(0x3b0)](
                this[_0x3206c3(0x2d9)]()[_0x3206c3(0x30b)]()
            );
        else {
            if (_0x3e5a6c === 'up') this[_0x3206c3(0x2d9)]()['gainLevel'](0x1);
            else {
                if (_0x3e5a6c === _0x3206c3(0x3b8))
                    this[_0x3206c3(0x2d9)]()[_0x3206c3(0x299)](-0x1);
                else
                    _0x3e5a6c === _0x3206c3(0x1b4) &&
                        this[_0x3206c3(0x2d9)]()['setLevel'](
                            this[_0x3206c3(0x2d9)]()[_0x3206c3(0x139)]()
                        );
            }
        }
        (this[_0x3206c3(0x1e3)][_0x3206c3(0x260)](this[_0x3206c3(0x2d9)]()),
            this[_0x3206c3(0x2d5)][_0x3206c3(0x2ac)](),
            this['_basicDataWindow']['activate']());
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x2e5)] = function () {
        const _0x4b0a8b = _0x249061;
        (this['_traitsDataWindow'][_0x4b0a8b(0x3b4)](), this[_0x4b0a8b(0x378)]['activate']());
    }),
    (Scene_Bestiary['prototype']['changeEnemyTrait'] = function () {
        const _0x35a2d3 = _0x249061,
            _0x5a7c0e = this[_0x35a2d3(0x378)][_0x35a2d3(0x1f0)]();
        (this['enemy']()[_0x35a2d3(0x2e3)](_0x5a7c0e[0x0], _0x5a7c0e[0x1]),
            this[_0x35a2d3(0x3bf)](),
            this[_0x35a2d3(0x2d9)]()[_0x35a2d3(0x183)](),
            this[_0x35a2d3(0x1f2)][_0x35a2d3(0x2ac)](),
            this[_0x35a2d3(0x378)][_0x35a2d3(0x2ac)](),
            this[_0x35a2d3(0x378)][_0x35a2d3(0x32f)]());
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x3af)] = function () {
        const _0x8aa252 = _0x249061;
        return Scene_MenuBase[_0x8aa252(0x19a)][_0x8aa252(0x3af)][_0x8aa252(0x310)](this);
    }),
    (Scene_Bestiary['prototype']['buttonAssistKey2'] = function () {
        const _0x424a84 = _0x249061;
        return Scene_MenuBase['prototype'][_0x424a84(0x32b)][_0x424a84(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x309)] = function () {
        const _0x9354ca = _0x249061;
        if (this[_0x9354ca(0x1ba)] && this[_0x9354ca(0x1ba)]['active'])
            return TextManager['getInputMultiButtonStrings']('up', _0x9354ca(0x3b8));
        return Scene_MenuBase[_0x9354ca(0x19a)]['buttonAssistKey3']['call'](this);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x30e)] = function () {
        const _0x2c61b3 = _0x249061;
        return Scene_MenuBase[_0x2c61b3(0x19a)][_0x2c61b3(0x30e)][_0x2c61b3(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x27b)] = function () {
        const _0x440285 = _0x249061;
        return Scene_MenuBase['prototype'][_0x440285(0x27b)][_0x440285(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['buttonAssistText1'] = function () {
        const _0x5de932 = _0x249061;
        if (this[_0x5de932(0x28c)] && this['_pageupButton'][_0x5de932(0x2fd)]) {
            if (this[_0x5de932(0x25c)] && this[_0x5de932(0x25c)][_0x5de932(0x203)])
                return TextManager[_0x5de932(0x2c5)][_0x5de932(0x215)][_0x5de932(0x343)];
        } else {
            if (this[_0x5de932(0x1ba)] && this[_0x5de932(0x1ba)][_0x5de932(0x203)])
                return TextManager[_0x5de932(0x2c5)][_0x5de932(0x215)][_0x5de932(0x2cb)];
        }
        return Scene_MenuBase[_0x5de932(0x19a)][_0x5de932(0x10c)][_0x5de932(0x310)](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)][_0x249061(0x13a)] = function () {
        const _0xac0437 = _0x249061;
        return Scene_MenuBase[_0xac0437(0x19a)]['buttonAssistText2'][_0xac0437(0x310)](this);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x2b5)] = function () {
        const _0x1b1800 = _0x249061;
        if (this[_0x1b1800(0x1ba)] && this[_0x1b1800(0x1ba)][_0x1b1800(0x203)])
            return TextManager['Bestiary']['buttonAssist'][_0x1b1800(0x237)];
        return Scene_MenuBase[_0x1b1800(0x19a)]['buttonAssistText3']['call'](this);
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['buttonAssistText4'] = function () {
        const _0x1fdfec = _0x249061;
        if (this[_0x1fdfec(0x232)] && this['_listWindow'][_0x1fdfec(0x203)]) {
            const _0x1d439e = this[_0x1fdfec(0x232)][_0x1fdfec(0x27c)]();
            if (_0x1d439e === _0x1fdfec(0x2d9))
                return TextManager[_0x1fdfec(0x2c5)][_0x1fdfec(0x215)][_0x1fdfec(0x1d8)];
            else {
                if (_0x1d439e === _0x1fdfec(0x107)) {
                    const _0x162009 = this[_0x1fdfec(0x232)][_0x1fdfec(0x1f0)]();
                    return this[_0x1fdfec(0x232)][_0x1fdfec(0x208)](_0x162009)
                        ? TextManager[_0x1fdfec(0x2c5)][_0x1fdfec(0x215)][_0x1fdfec(0x267)]
                        : TextManager[_0x1fdfec(0x2c5)][_0x1fdfec(0x215)][_0x1fdfec(0x1f9)];
                }
            }
        } else {
            if (this['_basicDataWindow'] && this['_basicDataWindow'][_0x1fdfec(0x203)]) {
                const _0x5491c7 = this['_basicDataWindow'][_0x1fdfec(0x27c)]();
                if (_0x5491c7 !== 'param')
                    return Scene_MenuBase['prototype'][_0x1fdfec(0x2c4)][_0x1fdfec(0x310)](this);
            }
        }
        return '';
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['buttonAssistText5'] = function () {
        const _0x14c93b = _0x249061;
        return Scene_MenuBase[_0x14c93b(0x19a)][_0x14c93b(0x19b)][_0x14c93b(0x310)](this);
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x19d)] = function () {
        const _0x120d98 = _0x249061;
        (Scene_MenuBase[_0x120d98(0x19a)][_0x120d98(0x19d)]['call'](this),
            this[_0x120d98(0x281)](this[_0x120d98(0x3b2)]()),
            this[_0x120d98(0x1cc)]());
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x3b2)] = function () {
        const _0xc14b43 = _0x249061;
        return VisuMZ['Bestiary'][_0xc14b43(0x27d)][_0xc14b43(0x3c6)][_0xc14b43(0x18d)];
    }),
    (Scene_Bestiary[_0x249061(0x19a)]['createCustomBackgroundImages'] = function () {
        const _0x1a7008 = _0x249061,
            _0xd04c22 = VisuMZ['Bestiary'][_0x1a7008(0x27d)][_0x1a7008(0x3c6)];
        _0xd04c22 &&
            (_0xd04c22[_0x1a7008(0x3aa)] !== '' || _0xd04c22[_0x1a7008(0x205)] !== '') &&
            ((this[_0x1a7008(0x16b)] = new Sprite(
                ImageManager[_0x1a7008(0x17d)](_0xd04c22[_0x1a7008(0x3aa)])
            )),
            (this['_backSprite2'] = new Sprite(
                ImageManager[_0x1a7008(0x292)](_0xd04c22[_0x1a7008(0x205)])
            )),
            this[_0x1a7008(0x161)](this[_0x1a7008(0x16b)]),
            this[_0x1a7008(0x161)](this[_0x1a7008(0x1af)]),
            this['_backSprite1'][_0x1a7008(0x33d)]['addLoadListener'](
                this[_0x1a7008(0x1bc)]['bind'](this, this[_0x1a7008(0x16b)])
            ),
            this[_0x1a7008(0x1af)][_0x1a7008(0x33d)][_0x1a7008(0x2ce)](
                this[_0x1a7008(0x1bc)]['bind'](this, this['_backSprite2'])
            ));
    }),
    (Scene_Bestiary['prototype'][_0x249061(0x1bc)] = function (_0x399e8f) {
        const _0x4b4b87 = _0x249061;
        (this['scaleSprite'](_0x399e8f), this[_0x4b4b87(0x2ea)](_0x399e8f));
    }),
    (VisuMZ[_0x249061(0x2c5)][_0x249061(0x28e)] =
        Window_MenuCommand['prototype'][_0x249061(0x2b1)]),
    (Window_MenuCommand[_0x249061(0x19a)][_0x249061(0x2b1)] = function () {
        const _0x31a4f4 = _0x249061;
        (VisuMZ[_0x31a4f4(0x2c5)][_0x31a4f4(0x28e)]['call'](this), this[_0x31a4f4(0x384)]());
    }),
    (Window_MenuCommand[_0x249061(0x19a)][_0x249061(0x384)] = function () {
        const _0x3e1c9d = _0x249061;
        if (!this[_0x3e1c9d(0x15e)]()) return;
        if (!this[_0x3e1c9d(0x109)]()) return;
        const _0x256b80 = TextManager[_0x3e1c9d(0x1b9)],
            _0x3fc6c6 = this['isBestiaryCommandEnabled']();
        this[_0x3e1c9d(0x32d)](_0x256b80, 'bestiary', _0x3fc6c6);
    }),
    (Window_MenuCommand[_0x249061(0x19a)][_0x249061(0x15e)] = function () {
        return Imported['VisuMZ_1_MainMenuCore'] ? ![] : !![];
    }),
    (Window_MenuCommand[_0x249061(0x19a)][_0x249061(0x109)] = function () {
        return $gameSystem['isMainMenuBestiaryVisible']();
    }),
    (Window_MenuCommand[_0x249061(0x19a)][_0x249061(0x15d)] = function () {
        return $gameSystem['isMainMenuBestiaryEnabled']();
    }));
function Window_BestiaryName() {
    const _0x326e60 = _0x249061;
    this[_0x326e60(0x2f6)](...arguments);
}
((Window_BestiaryName[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](Window_Base[_0x249061(0x19a)])),
    (Window_BestiaryName[_0x249061(0x19a)]['constructor'] = Window_BestiaryName),
    (Window_BestiaryName[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x2c6)] ?? 0x0,
    }),
    (Window_BestiaryName[_0x249061(0x19a)]['initialize'] = function (_0x557d35) {
        const _0x506664 = _0x249061;
        (Window_Base[_0x506664(0x19a)][_0x506664(0x2f6)]['call'](this, _0x557d35),
            (this[_0x506664(0x17e)] = ''));
    }),
    (Window_BestiaryName['prototype'][_0x249061(0x377)] = function (_0x555857) {
        const _0x3e0e63 = _0x249061;
        this['_text'] !== _0x555857 &&
            ((this[_0x3e0e63(0x17e)] = _0x555857), this[_0x3e0e63(0x2ac)]());
    }),
    (Window_BestiaryName[_0x249061(0x19a)][_0x249061(0x260)] = function (_0x315959) {
        const _0x44b710 = _0x249061;
        this[_0x44b710(0x377)](_0x315959[_0x44b710(0x14d)]());
    }),
    (Window_BestiaryName[_0x249061(0x19a)][_0x249061(0x324)] = function () {
        const _0x4dde13 = _0x249061;
        this[_0x4dde13(0x377)](TextManager[_0x4dde13(0x2c5)][_0x4dde13(0x1a5)][_0x4dde13(0x107)]);
    }),
    (Window_BestiaryName[_0x249061(0x19a)][_0x249061(0x304)] = function () {
        return ![];
    }),
    (Window_BestiaryName[_0x249061(0x19a)]['refresh'] = function () {
        const _0x5d759e = _0x249061;
        this['contents'][_0x5d759e(0x20a)]();
        if (this[_0x5d759e(0x17e)] === '') return;
        const _0x32bd81 = this['baseTextRect'](),
            _0x2e1674 = this[_0x5d759e(0x2c3)](this[_0x5d759e(0x17e)])[_0x5d759e(0x171)],
            _0x2721bb =
                _0x32bd81['x'] +
                Math[_0x5d759e(0x2a4)]((_0x32bd81[_0x5d759e(0x171)] - _0x2e1674) / 0x2);
        this[_0x5d759e(0x1f8)](
            this[_0x5d759e(0x17e)],
            _0x2721bb,
            _0x32bd81['y'],
            _0x32bd81[_0x5d759e(0x171)]
        );
    }));
function Window_BestiarySub() {
    this['initialize'](...arguments);
}
((Window_BestiarySub[_0x249061(0x19a)] = Object['create'](Window_Base[_0x249061(0x19a)])),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiarySub),
    (Window_BestiarySub[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x163)] ?? 0x0,
    }),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x2f6)] = function (_0xd0e2b0) {
        const _0x5841da = _0x249061;
        (Window_Base[_0x5841da(0x19a)][_0x5841da(0x2f6)]['call'](this, _0xd0e2b0),
            (this[_0x5841da(0x3b9)] = null),
            this[_0x5841da(0x2ac)]());
    }),
    (Window_BestiarySub['prototype'][_0x249061(0x337)] = function (_0x452f70) {
        const _0x287a62 = _0x249061;
        this[_0x287a62(0x3b9)] !== _0x452f70 &&
            ((this[_0x287a62(0x3b9)] = _0x452f70), this[_0x287a62(0x2ac)]());
    }),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x304)] = function () {
        return ![];
    }),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x2ac)] = function () {
        const _0x52dda1 = _0x249061;
        (this['contents']['clear'](),
            this['_enemyID'] ? this[_0x52dda1(0x271)]() : this[_0x52dda1(0x111)]());
    }),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x271)] = function () {
        const _0x428f18 = _0x249061,
            _0x2741e6 = TextManager[_0x428f18(0x2c5)]['subWindow'],
            _0x225933 = this[_0x428f18(0x177)]() * 0x10,
            _0x3e38a8 = this[_0x428f18(0x311)] - _0x225933 * 0x2,
            _0x39e07b = _0x2741e6['seenFmt'],
            _0x4ebd2d = $gameSystem[_0x428f18(0x113)](this[_0x428f18(0x3b9)]),
            _0x1cd057 = _0x39e07b[_0x428f18(0x340)](_0x4ebd2d);
        this[_0x428f18(0x2ec)](_0x1cd057, _0x225933, 0x0, _0x3e38a8, _0x428f18(0x11b));
        const _0x5d1d86 = _0x2741e6[_0x428f18(0x2f5)],
            _0x541378 = $gameSystem[_0x428f18(0x30d)](this[_0x428f18(0x3b9)]),
            _0x44f3f4 = _0x5d1d86[_0x428f18(0x340)](_0x541378);
        this['drawText'](_0x44f3f4, _0x225933, 0x0, _0x3e38a8, _0x428f18(0x38c));
    }),
    (Window_BestiarySub[_0x249061(0x19a)][_0x249061(0x111)] = function () {
        const _0x591ea2 = _0x249061,
            _0x3a903b = TextManager[_0x591ea2(0x2c5)][_0x591ea2(0x1d7)],
            _0x25bb40 = this['itemPadding']() * 0x10,
            _0x124c4a = this['innerWidth'] - _0x25bb40 * 0x2,
            _0x18fe16 = _0x3a903b[_0x591ea2(0x16e)],
            _0x182dd0 = DataManager['bestiaryTotalEnemies'](),
            _0x20d6f1 = $gameSystem[_0x591ea2(0x243)](),
            _0x2ad976 = ((_0x20d6f1 / _0x182dd0) * 0x64)[_0x591ea2(0x2a2)](
                _0x3a903b['fixedPercentage']
            ),
            _0xf2f77a = _0x18fe16[_0x591ea2(0x340)](_0x2ad976, _0x20d6f1, _0x182dd0);
        this[_0x591ea2(0x2ec)](_0xf2f77a, _0x25bb40, 0x0, _0x124c4a, 'center');
    }));
function Window_BestiaryEnemyList() {
    this['initialize'](...arguments);
}
((Window_BestiaryEnemyList[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](
    Window_Command[_0x249061(0x19a)]
)),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiaryEnemyList),
    (Window_BestiaryEnemyList[_0x249061(0x147)] = {
        bgType: VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x2a8)] ?? 0x0,
        delayMs: VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x308)] ?? 0xf0,
        maskUndefeatedEnemyNames:
            VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x124)] ?? !![],
    }),
    (Window_BestiaryEnemyList['prototype']['initialize'] = function (_0x4dc197) {
        const _0x1014e8 = _0x249061;
        (this['initCategoryStatus'](),
            Window_Command[_0x1014e8(0x19a)][_0x1014e8(0x2f6)][_0x1014e8(0x310)](this, _0x4dc197));
    }),
    (Window_BestiaryEnemyList['prototype'][_0x249061(0x24c)] = function () {
        const _0x3a7b37 = _0x249061;
        this['_categoryStatus'] = {};
        const _0x2063cf = VisuMZ['Bestiary'][_0x3a7b37(0x2e7)];
        for (const _0x383f09 of _0x2063cf) {
            if (!this[_0x3a7b37(0x313)](_0x383f09)) continue;
            this['_categoryStatus'][_0x383f09[_0x3a7b37(0x320)]()[_0x3a7b37(0x1ec)]()] = !![];
        }
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x304)] = function () {
        return ![];
    }),
    (Window_BestiaryEnemyList['prototype'][_0x249061(0x131)] = function () {
        const _0x5a2a25 = _0x249061,
            _0x325f97 = VisuMZ['Bestiary']['CategoryOrder'];
        for (const _0x640bf4 of _0x325f97) {
            if (!this[_0x5a2a25(0x313)](_0x640bf4)) continue;
            this[_0x5a2a25(0x13b)](_0x640bf4);
        }
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)]['includeCategory'] = function (_0x48454a) {
        const _0x130ed4 = _0x249061;
        _0x48454a = _0x48454a[_0x130ed4(0x320)]()[_0x130ed4(0x1ec)]();
        const _0xe4ff2d = DataManager['categoryEnemies'](_0x48454a);
        if (_0xe4ff2d[_0x130ed4(0x36f)] <= 0x0) return ![];
        if ($gameTemp[_0x130ed4(0x1e1)]()) return !![];
        if (Game_Enemy['BESTIARY'][_0x130ed4(0x334)]['toLowerCase']() === _0x48454a) return !![];
        return _0xe4ff2d['some'](_0xba030c => $gameSystem['timesEnemySeen'](_0xba030c['id']) > 0x0);
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x208)] = function (_0x3a19f0) {
        const _0x35a3e4 = _0x249061;
        return (
            (_0x3a19f0 = _0x3a19f0['toLowerCase']()['trim']()),
            this[_0x35a3e4(0x115)][_0x3a19f0]
        );
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x13b)] = function (_0x1849eb) {
        const _0x15f637 = _0x249061;
        _0x1849eb = _0x1849eb[_0x15f637(0x320)]()[_0x15f637(0x1ec)]();
        const _0x42c3cf = VisuMZ[_0x15f637(0x2c5)][_0x15f637(0x1ee)][_0x1849eb];
        if (!_0x42c3cf) return;
        const _0x2c749b = this[_0x15f637(0x208)](_0x1849eb)
                ? TextManager[_0x15f637(0x2c5)][_0x15f637(0x245)][_0x15f637(0x258)]
                : TextManager[_0x15f637(0x2c5)][_0x15f637(0x245)][_0x15f637(0x376)],
            _0x891426 = DataManager[_0x15f637(0x2f8)](_0x1849eb),
            _0xdb2d24 = _0x891426[_0x15f637(0x36f)],
            _0x3e92a8 = _0x891426[_0x15f637(0x2b0)](_0x1a1286 =>
                $gameSystem['isEnemyDefeated'](_0x1a1286['id'])
            )[_0x15f637(0x36f)],
            _0x1f0536 = ((_0x3e92a8 / _0xdb2d24) * 0x64)
                [_0x15f637(0x126)](0x0, 0x64)
                [
                    _0x15f637(0x2a2)
                ](TextManager[_0x15f637(0x2c5)][_0x15f637(0x245)][_0x15f637(0x118)]),
            _0x3fc1e6 = _0x2c749b['format'](_0x42c3cf['Title'], _0x1f0536);
        (this[_0x15f637(0x32d)](_0x3fc1e6, _0x15f637(0x107), !![], _0x1849eb),
            this[_0x15f637(0x382)](_0x1849eb));
    }),
    (Window_BestiaryEnemyList['prototype'][_0x249061(0x3b4)] = function () {
        const _0x2562ae = _0x249061,
            _0x2ed8f1 = this[_0x2562ae(0x354)]();
        ((this[_0x2562ae(0x115)][_0x2ed8f1] = !this[_0x2562ae(0x115)][_0x2ed8f1]),
            this[_0x2562ae(0x2ac)]());
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x354)] = function () {
        const _0x1441ad = _0x249061;
        return this[_0x1441ad(0x27c)]() === _0x1441ad(0x107) ? this[_0x1441ad(0x1f0)]() : null;
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x382)] = function (_0x43b0ec) {
        const _0x413786 = _0x249061;
        if (!this['isCategoryOpen'](_0x43b0ec)) return;
        const _0x4705ec = DataManager[_0x413786(0x2f8)](_0x43b0ec);
        for (const _0x303c46 of _0x4705ec) {
            if (!this[_0x413786(0x263)](_0x303c46)) continue;
            this[_0x413786(0x1b2)](_0x303c46);
        }
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x263)] = function (_0x1609cd) {
        const _0x519b65 = _0x249061;
        return DataManager[_0x519b65(0x172)](_0x1609cd);
    }),
    (Window_BestiaryEnemyList['prototype'][_0x249061(0x153)] = function (_0x44014c) {
        const _0x1d9fe9 = _0x249061;
        if ($gameTemp[_0x1d9fe9(0x1e1)]()) return !![];
        return $gameSystem['isEnemyDefeated'](_0x44014c['id']);
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x1b2)] = function (_0x1bfbae) {
        const _0x421eef = _0x249061;
        let _0x41dae4 = _0x1bfbae[_0x421eef(0x14d)];
        (this['isEnemyNameMasked'](_0x1bfbae) &&
            (_0x41dae4 = Array(_0x1bfbae[_0x421eef(0x14d)][_0x421eef(0x36f)] + 0x1)[
                _0x421eef(0x2ba)
            ](TextManager[_0x421eef(0x2c5)][_0x421eef(0x245)][_0x421eef(0x22a)])),
            this[_0x421eef(0x32d)](
                '\x20\x20' + _0x41dae4,
                _0x421eef(0x2d9),
                this[_0x421eef(0x153)](_0x1bfbae),
                _0x1bfbae['id']
            ));
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x358)] = function (_0x220154) {
        const _0x4f6c46 = _0x249061;
        if ($gameTemp[_0x4f6c46(0x1e1)]()) return ![];
        if ($gameSystem['isEnemyDefeated'](_0x220154['id'])) return ![];
        if ($gameSystem['timesEnemySeen'](_0x220154['id']) > 0x0) return ![];
        return Window_BestiaryEnemyList[_0x4f6c46(0x147)][_0x4f6c46(0x3a7)];
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)]['itemTextAlign'] = function () {
        const _0x40bfd1 = _0x249061;
        return _0x40bfd1(0x11b);
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)]['drawItem'] = function (_0x2b34f3) {
        const _0xbe609b = _0x249061,
            _0x3cab4b = this[_0xbe609b(0x2eb)](_0x2b34f3),
            _0x11aa9e = this[_0xbe609b(0x180)](_0x2b34f3),
            _0x3aec44 = this[_0xbe609b(0x2c3)](_0x11aa9e)[_0xbe609b(0x171)];
        this[_0xbe609b(0x132)](this[_0xbe609b(0x1c4)](_0x2b34f3));
        const _0x7c0de3 = this['itemTextAlign']();
        if (_0x7c0de3 === 'right')
            this['drawTextEx'](
                _0x11aa9e,
                _0x3cab4b['x'] + _0x3cab4b[_0xbe609b(0x171)] - _0x3aec44,
                _0x3cab4b['y'],
                _0x3aec44
            );
        else {
            if (_0x7c0de3 === _0xbe609b(0x391)) {
                const _0x26a0df =
                    _0x3cab4b['x'] +
                    Math[_0xbe609b(0x2a4)]((_0x3cab4b[_0xbe609b(0x171)] - _0x3aec44) / 0x2);
                this[_0xbe609b(0x1f8)](_0x11aa9e, _0x26a0df, _0x3cab4b['y'], _0x3aec44);
            } else this[_0xbe609b(0x1f8)](_0x11aa9e, _0x3cab4b['x'], _0x3cab4b['y'], _0x3aec44);
        }
    }),
    (Window_BestiaryEnemyList['prototype'][_0x249061(0x218)] = function (_0xf24baf) {
        const _0x1b7303 = _0x249061;
        ((this[_0x1b7303(0x1f2)] = _0xf24baf), this[_0x1b7303(0x169)]());
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x14e)] = function (_0x6f5aa8) {
        const _0x20133b = _0x249061;
        ((this[_0x20133b(0x17a)] = _0x6f5aa8), this[_0x20133b(0x169)]());
    }),
    (Window_BestiaryEnemyList['prototype']['callUpdateHelp'] = function () {
        const _0x2faa4c = _0x249061;
        Window_Command[_0x2faa4c(0x19a)][_0x2faa4c(0x169)][_0x2faa4c(0x310)](this);
        const _0x2f1efe = this[_0x2faa4c(0x1b7)](),
            _0x3a4abb = Window_BestiaryEnemyList[_0x2faa4c(0x147)]['delayMs'];
        (this['_imageWindow'] &&
            setTimeout(this['callUpdateImage']['bind'](this, _0x2f1efe), _0x3a4abb),
            this[_0x2faa4c(0x17a)] &&
                setTimeout(this[_0x2faa4c(0x1b3)][_0x2faa4c(0x244)](this, _0x2f1efe), _0x3a4abb));
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)]['callUpdateImage'] = function (_0x373347) {
        const _0x3e33cf = _0x249061;
        if (_0x373347 !== this['index']()) return;
        if (this['_lastIndex'] === _0x373347) return;
        this[_0x3e33cf(0x150)] = _0x373347;
        const _0x8fcebe = this['currentSymbol']();
        _0x8fcebe === _0x3e33cf(0x2d9)
            ? this[_0x3e33cf(0x1f2)][_0x3e33cf(0x15c)](this[_0x3e33cf(0x1f0)]())
            : this[_0x3e33cf(0x1f2)][_0x3e33cf(0x15c)](0x0);
    }),
    (Window_BestiaryEnemyList[_0x249061(0x19a)][_0x249061(0x1b3)] = function (_0x22fe32) {
        const _0x330da4 = _0x249061;
        if (_0x22fe32 !== this[_0x330da4(0x1b7)]()) return;
        const _0x42d290 = this[_0x330da4(0x27c)]();
        _0x42d290 === 'enemy'
            ? this[_0x330da4(0x17a)][_0x330da4(0x337)](this[_0x330da4(0x1f0)]())
            : this[_0x330da4(0x17a)]['setEnemy'](0x0);
    }));
function Window_BestiaryEnemyImage() {
    const _0x4f3d5d = _0x249061;
    this[_0x4f3d5d(0x2f6)](...arguments);
}
((Window_BestiaryEnemyImage['prototype'] = Object[_0x249061(0x2fa)](Window_Base['prototype'])),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiaryEnemyImage),
    (Window_BestiaryEnemyImage[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x253)] ?? 0x0,
        blurFilterStrength:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x339)] ?? 0x8,
        defaultBattleback1:
            VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x144)]['ImageWindow_Battleback1'] ??
            _0x249061(0x399),
        defaultBattleback2:
            VisuMZ['Bestiary']['Settings']['Window'][_0x249061(0x233)] ?? _0x249061(0x399),
        padding: VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x35e)] ?? 0x4,
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x2f6)] = function (_0x2ce3a9) {
        const _0x1281fa = _0x249061;
        (Window_Base['prototype']['initialize'][_0x1281fa(0x310)](this, _0x2ce3a9),
            this['createBattlebackSprites'](),
            this[_0x1281fa(0x33b)](),
            this[_0x1281fa(0x3c4)](),
            this[_0x1281fa(0x3a6)]());
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)]['updatePadding'] = function () {
        const _0x319793 = _0x249061;
        this[_0x319793(0x1ef)] = Window_BestiaryEnemyImage[_0x319793(0x147)][_0x319793(0x1ef)];
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x3c2)] = function () {
        const _0x4fd688 = _0x249061;
        ((this[_0x4fd688(0x349)] = new Sprite()),
            (this[_0x4fd688(0x2f9)] = new Sprite()),
            this[_0x4fd688(0x329)](this[_0x4fd688(0x349)]),
            this[_0x4fd688(0x329)](this[_0x4fd688(0x2f9)]),
            (this[_0x4fd688(0x349)][_0x4fd688(0x15f)]['x'] = this[_0x4fd688(0x349)][
                _0x4fd688(0x15f)
            ]['y'] =
                0.5),
            (this[_0x4fd688(0x2f9)]['anchor']['x'] = this[_0x4fd688(0x2f9)][_0x4fd688(0x15f)]['y'] =
                0.5));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)]['createEnemySprite'] = function () {
        const _0x57d7cc = _0x249061;
        ((this[_0x57d7cc(0x1fc)] = new Sprite()),
            this[_0x57d7cc(0x329)](this[_0x57d7cc(0x1fc)]),
            (this[_0x57d7cc(0x1fc)][_0x57d7cc(0x15f)]['x'] = this[_0x57d7cc(0x1fc)][
                _0x57d7cc(0x15f)
            ]['y'] =
                0.5));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)]['createDragonbonesSprite'] = function () {
        const _0x44f2b9 = _0x249061;
        if (!Imported['VisuMZ_2_DragonbonesUnion']) return;
        ((this['_dragonbones'] = null),
            (this['_dragonbonesSpriteContainer'] = new Sprite()),
            this[_0x44f2b9(0x329)](this[_0x44f2b9(0x1f1)]));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x3a6)] = function () {
        const _0x23f6fb = _0x249061,
            _0x364600 = Window_BestiaryEnemyImage[_0x23f6fb(0x147)]['blurFilterStrength'];
        ((this['_blurFilter'] = new PIXI[_0x23f6fb(0x30f)][_0x23f6fb(0x262)](_0x364600)),
            (this[_0x23f6fb(0x1fc)][_0x23f6fb(0x30f)] = [this[_0x23f6fb(0x374)]]),
            this[_0x23f6fb(0x1f1)] &&
                (this[_0x23f6fb(0x1f1)][_0x23f6fb(0x30f)] = [this[_0x23f6fb(0x374)]]));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x15c)] = function (_0x1c7683) {
        const _0xb9dc88 = _0x249061;
        if (!SceneManager[_0xb9dc88(0x12b)][_0xb9dc88(0x21c)]) return;
        if (this['_enemyID'] !== _0x1c7683) {
            if (_0x1c7683 > 0x0) SceneManager[_0xb9dc88(0x12b)][_0xb9dc88(0x21c)](_0x1c7683);
            ((this[_0xb9dc88(0x3b9)] = _0x1c7683), this[_0xb9dc88(0x2ac)]());
        }
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x2ac)] = function () {
        const _0x5b749d = _0x249061;
        this[_0x5b749d(0x2c7)]();
        if (this[_0x5b749d(0x3b9)] <= 0x0) return;
        (this['updateBattlebackImages'](), this['updateEnemyImage']());
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x2c7)] = function () {
        const _0x1e9c0d = _0x249061;
        ((this[_0x1e9c0d(0x1fc)][_0x1e9c0d(0x2fd)] = this[_0x1e9c0d(0x3b9)] > 0x0),
            (this[_0x1e9c0d(0x349)]['visible'] = this[_0x1e9c0d(0x3b9)] > 0x0),
            (this[_0x1e9c0d(0x2f9)][_0x1e9c0d(0x2fd)] = this[_0x1e9c0d(0x3b9)] > 0x0),
            (this[_0x1e9c0d(0x1fc)]['x'] = Math[_0x1e9c0d(0x35f)](this[_0x1e9c0d(0x311)] / 0x2)),
            (this[_0x1e9c0d(0x1fc)]['y'] = Math['round'](this['innerHeight'] / 0x2)),
            (this[_0x1e9c0d(0x1fc)][_0x1e9c0d(0x238)]['x'] = Math[_0x1e9c0d(0x20b)](
                this[_0x1e9c0d(0x1fc)]['scale']['x']
            )),
            (this[_0x1e9c0d(0x349)]['x'] = this[_0x1e9c0d(0x2f9)]['x'] =
                Math['round'](this['innerWidth'] / 0x2)),
            (this[_0x1e9c0d(0x349)]['y'] = this['_battlebackSprite2']['y'] =
                Math[_0x1e9c0d(0x35f)](this[_0x1e9c0d(0x1a2)] / 0x2)));
    }),
    (Window_BestiaryEnemyImage['prototype'][_0x249061(0x151)] = function () {
        const _0x136df2 = _0x249061;
        ((this[_0x136df2(0x349)]['bitmap'] = ImageManager[_0x136df2(0x346)](
            this[_0x136df2(0x3b9)]
        )),
            (this[_0x136df2(0x2f9)][_0x136df2(0x33d)] = ImageManager[_0x136df2(0x392)](
                this[_0x136df2(0x3b9)]
            )));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x34c)] = function () {
        const _0x281b99 = _0x249061,
            _0x3e39ec = SceneManager[_0x281b99(0x12b)]['enemy'](),
            _0xf758d5 = _0x3e39ec[_0x281b99(0x3ad)]();
        this[_0x281b99(0x2e4)]();
        if (ImageManager[_0x281b99(0x18e)](this[_0x281b99(0x3b9)]) !== '') {
            const _0x4a328c = ImageManager['bestiaryEnemyCustomImageFilename'](
                this[_0x281b99(0x3b9)]
            );
            _0x4a328c['addLoadListener'](
                this['processFullEnemyImage'][_0x281b99(0x244)](this, _0x4a328c, 0x0)
            );
        } else {
            if (this[_0x281b99(0x1e0)]()) {
                const _0x460e2d = new Bitmap(0x1, 0x1);
                (this['processDragonbones'](), this[_0x281b99(0x36c)](_0x460e2d, 0x0));
            } else {
                if (this[_0x281b99(0x289)]()) {
                    const _0x2d2535 = this[_0x281b99(0x2b6)],
                        _0x139617 = ImageManager['loadSvActor'](_0x2d2535);
                    _0x139617[_0x281b99(0x2ce)](
                        this[_0x281b99(0x162)][_0x281b99(0x244)](this, _0x2d2535, _0x139617, 0x0)
                    );
                } else {
                    if ($gameSystem[_0x281b99(0x2bd)]()) {
                        const _0x29087a = ImageManager['loadSvEnemy'](
                            _0x3e39ec[_0x281b99(0x13c)]()
                        );
                        _0x29087a['addLoadListener'](
                            this[_0x281b99(0x36c)][_0x281b99(0x244)](this, _0x29087a, _0xf758d5)
                        );
                    } else {
                        const _0x5ec174 = ImageManager[_0x281b99(0x294)](
                            _0x3e39ec[_0x281b99(0x13c)]()
                        );
                        _0x5ec174[_0x281b99(0x2ce)](
                            this['processFullEnemyImage'][_0x281b99(0x244)](
                                this,
                                _0x5ec174,
                                _0xf758d5
                            )
                        );
                    }
                }
            }
        }
    }),
    (Window_BestiaryEnemyImage['prototype'][_0x249061(0x1e0)] = function () {
        const _0x2a044a = _0x249061;
        if (!Imported[_0x2a044a(0x174)]) return ![];
        const _0x558218 = SceneManager['_scene'][_0x2a044a(0x2d9)]();
        return _0x558218[_0x2a044a(0x305)]()[_0x2a044a(0x2e1)] !== ''
            ? ((this['_dragonbonesBattlerData'] = _0x558218[_0x2a044a(0x305)]()), !![])
            : ![];
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x289)] = function () {
        const _0x1962dc = _0x249061;
        if (!Imported[_0x1962dc(0x13e)]) return ![];
        const _0x4c8bc3 = SceneManager['_scene']['enemy']();
        return _0x4c8bc3[_0x1962dc(0x1e6)]()
            ? ((this['_svBattlerName'] = _0x4c8bc3[_0x1962dc(0x210)]()[_0x1962dc(0x14d)]), !![])
            : ![];
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x36c)] = function (
        _0x4a48ef,
        _0x549ea4
    ) {
        const _0x43a01f = _0x249061;
        ((this[_0x43a01f(0x1fc)][_0x43a01f(0x33d)] = _0x4a48ef),
            this[_0x43a01f(0x1fc)][_0x43a01f(0x30a)](_0x549ea4),
            this[_0x43a01f(0x1fc)][_0x43a01f(0x23c)](
                0x0,
                0x0,
                _0x4a48ef[_0x43a01f(0x171)],
                _0x4a48ef[_0x43a01f(0x379)]
            ),
            this['updateFilters'](),
            this[_0x43a01f(0x1fc)][_0x43a01f(0x31a)]());
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x162)] = function (
        _0x3b4441,
        _0x15c3b6,
        _0xb1155a
    ) {
        const _0x29944c = _0x249061;
        ((this[_0x29944c(0x1fc)][_0x29944c(0x33d)] = _0x15c3b6),
            this[_0x29944c(0x1fc)]['setHue'](_0xb1155a));
        const _0x2ba02a = Math[_0x29944c(0x2a4)](
                _0x15c3b6[_0x29944c(0x171)] / ImageManager['svActorHorzCells']
            ),
            _0x37e4a6 = Math[_0x29944c(0x2a4)](
                _0x15c3b6[_0x29944c(0x379)] / ImageManager['svActorVertCells']
            );
        (this[_0x29944c(0x1fc)][_0x29944c(0x23c)](0x0, 0x0, _0x2ba02a, _0x37e4a6),
            (this[_0x29944c(0x1fc)]['scale']['x'] *= -0x1),
            this[_0x29944c(0x14c)](),
            this[_0x29944c(0x1fc)]['update']());
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x2da)] = function () {
        const _0x3cef21 = _0x249061;
        this['disposeDragonbones']();
        const _0x5f272b = this['_dragonbonesBattlerData'];
        ((this[_0x3cef21(0x188)] = _0x5f272b[_0x3cef21(0x2e1)]),
            (armatureName = _0x5f272b[_0x3cef21(0x2e1)]),
            DragonbonesManager[_0x3cef21(0x3bc)](
                armatureName,
                this[_0x3cef21(0x19c)]['bind'](this)
            ));
        const _0x37dc0a = this['_dragonbonesSpriteContainer'];
        _0x37dc0a &&
            ((_0x37dc0a['x'] = Math[_0x3cef21(0x35f)](this[_0x3cef21(0x311)] / 0x2)),
            (_0x37dc0a['y'] = Math['round'](this[_0x3cef21(0x1a2)] / 0x2)),
            (_0x37dc0a['y'] += Math[_0x3cef21(0x35f)](_0x5f272b[_0x3cef21(0x379)] / 0x2)));
    }),
    (Window_BestiaryEnemyImage['prototype']['disposeDragonbones'] = function () {
        const _0x5c7047 = _0x249061;
        this[_0x5c7047(0x33e)] &&
            (this[_0x5c7047(0x1f1)] &&
                this['_dragonbonesSpriteContainer']['removeChild'](this[_0x5c7047(0x33e)]),
            this[_0x5c7047(0x2d6)](this[_0x5c7047(0x33e)]),
            this[_0x5c7047(0x33e)][_0x5c7047(0x278)](),
            delete this[_0x5c7047(0x33e)],
            delete this['_dragonbonesName']);
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)][_0x249061(0x19c)] = function () {
        const _0x2444f6 = _0x249061,
            _0x1f0a0a = this[_0x2444f6(0x1dd)];
        ((this[_0x2444f6(0x33e)] = DragonbonesManager[_0x2444f6(0x280)](
            _0x1f0a0a[_0x2444f6(0x2e1)]
        )),
            !this[_0x2444f6(0x1f1)] &&
                ((this[_0x2444f6(0x1f1)] = new Sprite()),
                (this['_dragonbonesSpriteContainer']['filters'] = [this[_0x2444f6(0x374)]])),
            this[_0x2444f6(0x1f1)]['addChild'](this[_0x2444f6(0x33e)]),
            this[_0x2444f6(0x2cd)](),
            (this[_0x2444f6(0x33e)]['x'] = _0x1f0a0a[_0x2444f6(0x29d)]),
            (this['_dragonbones']['y'] = _0x1f0a0a[_0x2444f6(0x1a8)]),
            (this[_0x2444f6(0x33e)]['scale']['x'] = _0x1f0a0a[_0x2444f6(0x1d3)]),
            (this[_0x2444f6(0x33e)][_0x2444f6(0x238)]['y'] = _0x1f0a0a['scaleY']));
    }),
    (Window_BestiaryEnemyImage['prototype'][_0x249061(0x2cd)] = function () {
        const _0x5d12dd = _0x249061,
            _0x52adc0 = _0x5d12dd(0x1ad),
            _0x1c9897 = this['_dragonbones'][_0x5d12dd(0x298)];
        _0x1c9897[_0x5d12dd(0x34e)][_0x52adc0] && _0x1c9897['play'](_0x52adc0);
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)]['updateFilters'] = function () {
        const _0xab4dda = _0x249061;
        this[_0xab4dda(0x365)]()
            ? ((this[_0xab4dda(0x374)]['enabled'] = ![]),
              this['_enemySprite'][_0xab4dda(0x248)]([0x0, 0x0, 0x0, 0x0]),
              this[_0xab4dda(0x1f1)] &&
                  this['_dragonbonesSpriteContainer'][_0xab4dda(0x248)]([0x0, 0x0, 0x0, 0x0]))
            : ((this['_blurFilter']['enabled'] = !![]),
              this[_0xab4dda(0x1fc)][_0xab4dda(0x248)]([-0xff, -0xff, -0xff, 0x0]),
              this[_0xab4dda(0x1f1)] &&
                  this[_0xab4dda(0x1f1)][_0xab4dda(0x248)]([-0xff, -0xff, -0xff, 0x0]));
    }),
    (Window_BestiaryEnemyImage[_0x249061(0x19a)]['isEnemyFullyVisible'] = function () {
        const _0x534134 = _0x249061;
        if ($gameTemp[_0x534134(0x1e1)]()) return !![];
        if ($gameSystem[_0x534134(0x10f)](this['_enemyID'])) return !![];
        if ($gameSystem[_0x534134(0x113)](this[_0x534134(0x3b9)]) > 0x0) return !![];
        return ![];
    }));
function Window_BestiaryDataCategories() {
    const _0x42a124 = _0x249061;
    this[_0x42a124(0x2f6)](...arguments);
}
((Window_BestiaryDataCategories[_0x249061(0x19a)] = Object['create'](
    Window_HorzCommand['prototype']
)),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x28d)] =
        Window_BestiaryDataCategories),
    (Window_BestiaryDataCategories['SETTINGS'] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)]['CategoryWindow_BgType'] ?? 0x0,
        commandStyle:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x1de)] ??
            _0x249061(0x148),
        commandOrder: VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x255)] ?? [
            _0x249061(0x328),
            _0x249061(0x326),
            _0x249061(0x1df),
            _0x249061(0x31b),
            _0x249061(0x387),
            'lore',
        ],
        commands: {
            basic: {
                show: !![],
                text:
                    VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x112)] ??
                    'Base',
                icon:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)]['BasicIcon'] ??
                    0x54,
            },
            elements: {
                show: !![],
                text:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                        _0x249061(0x31f)
                    ] ?? 'Elements',
                icon:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                        _0x249061(0x398)
                    ] ?? 0x40,
            },
            skills: {
                show: !![],
                text:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                        _0x249061(0x2a9)
                    ] ?? _0x249061(0x119),
                icon: VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab']['SkillsIcon'] ?? 0x4f,
            },
            rewards: {
                show: !![],
                text:
                    VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x388)]['RewardsText'] ??
                    _0x249061(0x37a),
                icon:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)][
                        _0x249061(0x201)
                    ] ?? 0x57,
            },
            traits: {
                show: !![],
                text:
                    VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x388)][_0x249061(0x2bb)] ??
                    _0x249061(0x38a),
                icon:
                    VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x388)][_0x249061(0x350)] ??
                    0x53,
            },
            lore: {
                show: !![],
                text:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Vocab'][_0x249061(0x234)] ??
                    _0x249061(0x315),
                icon:
                    VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x388)]['LoreIcon'] ??
                    0x50,
            },
        },
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)]['initialize'] = function (_0x5de247) {
        const _0x1e52eb = _0x249061;
        (Window_HorzCommand[_0x1e52eb(0x19a)][_0x1e52eb(0x2f6)][_0x1e52eb(0x310)](this, _0x5de247),
            this[_0x1e52eb(0x269)](_0x5de247),
            this[_0x1e52eb(0x247)](),
            this[_0x1e52eb(0x332)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x395)] = function () {
        const _0x204a13 = _0x249061;
        return this[_0x204a13(0x1a9)] ? this['_list']['length'] : 0x1;
    }),
    (Window_BestiaryDataCategories['prototype'][_0x249061(0x169)] = function () {
        const _0xfc21cb = _0x249061;
        (Window_HorzCommand[_0xfc21cb(0x19a)][_0xfc21cb(0x169)]['call'](this),
            this[_0xfc21cb(0x330)] && this[_0xfc21cb(0x371)](),
            this['active'] && this[_0xfc21cb(0x27f)] && this[_0xfc21cb(0x18b)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x202)] = function () {
        const _0x3185ba = _0x249061;
        (Window_HorzCommand[_0x3185ba(0x19a)][_0x3185ba(0x202)][_0x3185ba(0x310)](this),
            this[_0x3185ba(0x169)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x332)] = function () {
        const _0xee56ab = _0x249061;
        (Window_HorzCommand['prototype'][_0xee56ab(0x332)][_0xee56ab(0x310)](this),
            this[_0xee56ab(0x230)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x1ed)] = function () {
        return ![];
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x3b7)] = function (
        _0x58d3b4,
        _0xd6759e
    ) {
        const _0x47096f = _0x249061;
        ((this[_0x47096f(0x27f)] = this[_0x47096f(0x27f)] || {}),
            (this[_0x47096f(0x27f)][_0xd6759e] = _0x58d3b4),
            this[_0x47096f(0x169)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x18b)] = function () {
        const _0x148fbb = _0x249061;
        this[_0x148fbb(0x27f)] = this[_0x148fbb(0x27f)] || {};
        for (const _0x2173d8 in this['_symbolWindows']) {
            _0x2173d8 === this[_0x148fbb(0x27c)]()
                ? (this[_0x148fbb(0x27f)][_0x2173d8]['show'](),
                  this['_symbolWindows'][_0x2173d8][_0x148fbb(0x2ac)](),
                  this['_symbolWindows'][_0x2173d8]['deactivate']())
                : this[_0x148fbb(0x27f)][_0x2173d8][_0x148fbb(0x332)]();
        }
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)]['hideAllSymbolWindows'] = function () {
        const _0x34fde7 = _0x249061;
        this[_0x34fde7(0x27f)] = this['_symbolWindows'] || {};
        for (const _0x3deb28 in this[_0x34fde7(0x27f)]) {
            this[_0x34fde7(0x27f)][_0x3deb28]['hide']();
        }
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x225)] = function () {
        const _0x373e31 = _0x249061,
            _0x559296 = this[_0x373e31(0x27c)]();
        this['_symbolWindows'][_0x559296]
            ? this[_0x373e31(0x27f)][_0x559296][_0x373e31(0x370)]
                ? this[_0x373e31(0x27f)][_0x559296]['becomeActive']()
                : this[_0x373e31(0x27f)][_0x559296][_0x373e31(0x32f)]()
            : this['activate']();
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x3c3)] = function () {
        const _0x590112 = _0x249061,
            _0x2fe4c9 = this[_0x590112(0x27c)]();
        this[_0x590112(0x27f)][_0x2fe4c9]
            ? (this[_0x590112(0x27f)][_0x2fe4c9][_0x590112(0x247)](),
              this[_0x590112(0x27f)][_0x2fe4c9][_0x590112(0x240)](0x0),
              this['_symbolWindows'][_0x2fe4c9][_0x590112(0x295)](),
              this['_symbolWindows'][_0x2fe4c9][_0x590112(0x29a)](0x0, 0x0))
            : this[_0x590112(0x32f)]();
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x269)] = function (_0x512de7) {
        const _0x116508 = _0x249061,
            _0x435b73 = new Rectangle(
                0x0,
                0x0,
                _0x512de7[_0x116508(0x171)],
                _0x512de7[_0x116508(0x379)]
            );
        ((this[_0x116508(0x330)] = new Window_Base(_0x435b73)),
            (this[_0x116508(0x330)][_0x116508(0x1a7)] = 0x0),
            this[_0x116508(0x161)](this[_0x116508(0x330)]),
            this[_0x116508(0x371)]());
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x371)] = function () {
        const _0x44ed3e = _0x249061,
            _0x1b9d60 = this[_0x44ed3e(0x330)];
        _0x1b9d60[_0x44ed3e(0x21a)]['clear']();
        const _0x328a6b = this['commandStyleCheck'](this[_0x44ed3e(0x1b7)]());
        if (_0x328a6b === _0x44ed3e(0x246)) {
            const _0x523ddf = this['itemLineRect'](this[_0x44ed3e(0x1b7)]());
            let _0x26fca3 = this[_0x44ed3e(0x180)](this[_0x44ed3e(0x1b7)]());
            ((_0x26fca3 = _0x26fca3[_0x44ed3e(0x2d3)](/\\I\[(\d+)\]/gi, '')),
                _0x1b9d60[_0x44ed3e(0x13d)](),
                this[_0x44ed3e(0x155)](_0x26fca3, _0x523ddf),
                this[_0x44ed3e(0x2cf)](_0x26fca3, _0x523ddf),
                this['commandNameWindowCenter'](_0x26fca3, _0x523ddf));
        }
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x155)] = function (
        _0x34f695,
        _0x53d4a9
    ) {}),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x2cf)] = function (
        _0xb8b247,
        _0x1a105a
    ) {
        const _0x39008e = _0x249061,
            _0x6068fb = this[_0x39008e(0x330)];
        _0x6068fb[_0x39008e(0x2ec)](
            _0xb8b247,
            0x0,
            _0x1a105a['y'],
            _0x6068fb[_0x39008e(0x311)],
            _0x39008e(0x391)
        );
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x2f3)] = function (
        _0x30f921,
        _0x351cc7
    ) {
        const _0x3438c1 = _0x249061,
            _0xc68627 = this['_commandNameWindow'],
            _0x808360 = $gameSystem[_0x3438c1(0x24a)](),
            _0x2899fd =
                _0x351cc7['x'] +
                Math[_0x3438c1(0x2a4)](_0x351cc7[_0x3438c1(0x171)] / 0x2) +
                _0x808360;
        ((_0xc68627['x'] = _0xc68627['width'] / -0x2 + _0x2899fd),
            (_0xc68627['y'] = Math[_0x3438c1(0x2a4)](_0x351cc7[_0x3438c1(0x379)] / 0x2)));
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x131)] = function () {
        const _0x4bef3c = _0x249061;
        for (const _0x43c960 of Window_BestiaryDataCategories['SETTINGS'][_0x4bef3c(0x200)]) {
            this[_0x4bef3c(0x1ff)](_0x43c960);
        }
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x1ff)] = function (_0x3d8b7d) {
        const _0x3d7c7a = _0x249061,
            _0xf670ee =
                Window_BestiaryDataCategories[_0x3d7c7a(0x147)][_0x3d7c7a(0x396)][_0x3d8b7d];
        if (!this['isCustomCommandVisible'](_0xf670ee)) return;
        const _0x366cc9 = _0x3d8b7d,
            _0x1eafb4 = Number(_0xf670ee[_0x3d7c7a(0x246)]);
        let _0x18826f = _0xf670ee[_0x3d7c7a(0x120)];
        _0x1eafb4 > 0x0 &&
            this['commandStyle']() !== _0x3d7c7a(0x120) &&
            (_0x18826f = _0x3d7c7a(0x1f6)[_0x3d7c7a(0x340)](_0x1eafb4, _0x18826f));
        const _0x1d3297 = this[_0x3d7c7a(0x321)](_0xf670ee);
        this['addCommand'](_0x18826f, _0x366cc9, _0x1d3297);
    }),
    (Window_BestiaryDataCategories['prototype']['isCustomCommandVisible'] = function (_0x49cba3) {
        const _0x3b658b = _0x249061;
        if (
            _0x49cba3 ===
            Window_BestiaryDataCategories[_0x3b658b(0x147)][_0x3b658b(0x396)][_0x3b658b(0x387)]
        ) {
            if (!Imported['VisuMZ_1_ElementStatusCore']) return ![];
        }
        return _0x49cba3[_0x3b658b(0x202)];
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x321)] = function (_0x1df4b0) {
        return !![];
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x373)] = function () {
        const _0x525e24 = _0x249061;
        return _0x525e24(0x391);
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x1db)] = function (_0x4417e0) {
        const _0x5db2d3 = _0x249061,
            _0x25a9c5 = this[_0x5db2d3(0x1fa)](_0x4417e0);
        if (_0x25a9c5 === _0x5db2d3(0x2fc)) this[_0x5db2d3(0x16a)](_0x4417e0);
        else
            _0x25a9c5 === _0x5db2d3(0x246)
                ? this[_0x5db2d3(0x287)](_0x4417e0)
                : Window_Command[_0x5db2d3(0x19a)][_0x5db2d3(0x1db)][_0x5db2d3(0x310)](
                      this,
                      _0x4417e0
                  );
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x338)] = function () {
        const _0x2f680f = _0x249061;
        return Window_BestiaryDataCategories[_0x2f680f(0x147)][_0x2f680f(0x338)];
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)]['commandStyleCheck'] = function (_0x15de85) {
        const _0x27d733 = _0x249061;
        if (_0x15de85 < 0x0) return _0x27d733(0x120);
        const _0x467280 = this[_0x27d733(0x338)]();
        if (_0x467280 !== _0x27d733(0x148)) return _0x467280;
        else {
            if (this['maxItems']() > 0x0) {
                const _0x5c1627 = this[_0x27d733(0x180)](_0x15de85);
                if (_0x5c1627[_0x27d733(0x11c)](/\\I\[(\d+)\]/i)) {
                    const _0x26afb1 = this[_0x27d733(0x2eb)](_0x15de85),
                        _0x57e0a8 = this[_0x27d733(0x2c3)](_0x5c1627)[_0x27d733(0x171)];
                    return _0x57e0a8 <= _0x26afb1[_0x27d733(0x171)]
                        ? _0x27d733(0x2fc)
                        : _0x27d733(0x246);
                }
            }
        }
        return 'text';
    }),
    (Window_BestiaryDataCategories[_0x249061(0x19a)][_0x249061(0x16a)] = function (_0x2e2a9e) {
        const _0x49f6fb = _0x249061,
            _0x704d83 = this['itemLineRect'](_0x2e2a9e),
            _0x29f5ef = this['commandName'](_0x2e2a9e),
            _0xc91f41 = this[_0x49f6fb(0x2c3)](_0x29f5ef)[_0x49f6fb(0x171)];
        this[_0x49f6fb(0x132)](this[_0x49f6fb(0x1c4)](_0x2e2a9e));
        const _0x49f4e7 = this['itemTextAlign']();
        if (_0x49f4e7 === _0x49f6fb(0x38c))
            this['drawTextEx'](
                _0x29f5ef,
                _0x704d83['x'] + _0x704d83[_0x49f6fb(0x171)] - _0xc91f41,
                _0x704d83['y'],
                _0xc91f41
            );
        else {
            if (_0x49f4e7 === _0x49f6fb(0x391)) {
                const _0x13f8a0 =
                    _0x704d83['x'] +
                    Math[_0x49f6fb(0x2a4)]((_0x704d83[_0x49f6fb(0x171)] - _0xc91f41) / 0x2);
                this['drawTextEx'](_0x29f5ef, _0x13f8a0, _0x704d83['y'], _0xc91f41);
            } else this['drawTextEx'](_0x29f5ef, _0x704d83['x'], _0x704d83['y'], _0xc91f41);
        }
    }),
    (Window_BestiaryDataCategories['prototype']['drawItemStyleIcon'] = function (_0x46dec5) {
        const _0x531fcd = _0x249061;
        this[_0x531fcd(0x180)](_0x46dec5)[_0x531fcd(0x11c)](/\\I\[(\d+)\]/i);
        const _0x298674 = Number(RegExp['$1']) || 0x0,
            _0x3de691 = this[_0x531fcd(0x2eb)](_0x46dec5),
            _0x579074 =
                _0x3de691['x'] +
                Math[_0x531fcd(0x2a4)](
                    (_0x3de691[_0x531fcd(0x171)] - ImageManager[_0x531fcd(0x31d)]) / 0x2
                ),
            _0x4762dc = _0x3de691['y'] + (_0x3de691['height'] - ImageManager['iconHeight']) / 0x2;
        this[_0x531fcd(0x1bb)](_0x298674, _0x579074, _0x4762dc);
    }));
function Window_BestiaryBasic() {
    const _0x3854b1 = _0x249061;
    this[_0x3854b1(0x2f6)](...arguments);
}
((Window_BestiaryBasic[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](
    Window_Command[_0x249061(0x19a)]
)),
    (Window_BestiaryBasic['prototype']['constructor'] = Window_BestiaryBasic),
    (Window_BestiaryBasic[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x10e)] ?? 0x0,
        showLevelChange:
            VisuMZ['Bestiary'][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x2e9)] ?? !![],
    }),
    (Window_BestiaryBasic['prototype'][_0x249061(0x2f6)] = function (_0x1ccc09) {
        const _0x18af03 = _0x249061;
        (Window_Command[_0x18af03(0x19a)][_0x18af03(0x2f6)]['call'](this, _0x1ccc09),
            this[_0x18af03(0x247)](),
            this[_0x18af03(0x295)](),
            this[_0x18af03(0x332)]());
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x370)] = function () {
        const _0x35bca6 = _0x249061;
        (this[_0x35bca6(0x32f)](), this[_0x35bca6(0x240)](0x0), this['scrollTo'](0x0, 0x0));
    }),
    (Window_BestiaryBasic['prototype'][_0x249061(0x268)] = function () {
        const _0x267586 = _0x249061;
        if (this[_0x267586(0x27c)]() !== _0x267586(0x110))
            Window_Command[_0x267586(0x19a)]['playOkSound'][_0x267586(0x310)](this);
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)]['makeCommandList'] = function () {
        const _0x5be990 = _0x249061;
        for (const _0x4cfa86 of this[_0x5be990(0x279)]()) {
            this[_0x5be990(0x32d)](_0x4cfa86, 'param', !![], _0x4cfa86);
        }
        this[_0x5be990(0x191)]() && this['addLevelChangeCommands']();
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x279)] = function () {
        const _0x4b1910 = _0x249061;
        return Imported[_0x4b1910(0x249)]
            ? VisuMZ['CoreEngine'][_0x4b1910(0x27d)][_0x4b1910(0x1c1)][_0x4b1910(0x173)]
            : [0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7];
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x191)] = function () {
        const _0x520251 = _0x249061;
        return (
            Imported['VisuMZ_3_EnemyLevels'] &&
            Window_BestiaryBasic[_0x520251(0x147)]['showLevelChange']
        );
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)]['addLevelChangeCommands'] = function () {
        const _0x218908 = _0x249061,
            _0x2c1d45 = TextManager[_0x218908(0x2c5)]['basicWindow'],
            _0x4f4197 = SceneManager['_scene'][_0x218908(0x2d9)]();
        {
            const _0x1728dd = _0x2c1d45[_0x218908(0x2db)],
                _0x3c5c62 = _0x1728dd[_0x218908(0x340)](TextManager[_0x218908(0x34b)]),
                _0x8fe7e4 = _0x4f4197['level'] < _0x4f4197[_0x218908(0x30b)]();
            this['addCommand'](_0x3c5c62, _0x218908(0x2bf), _0x8fe7e4);
        }
        {
            const _0x36ca5f = _0x2c1d45[_0x218908(0x344)],
                _0x107bf6 = _0x36ca5f['format'](TextManager[_0x218908(0x34b)]),
                _0x1695de = _0x4f4197[_0x218908(0x34b)] < _0x4f4197[_0x218908(0x30b)]();
            this[_0x218908(0x32d)](_0x107bf6, _0x218908(0x344), _0x1695de);
        }
        {
            const _0xcb754d = _0x2c1d45['levelDown'],
                _0x38878c = _0xcb754d[_0x218908(0x340)](TextManager['level']),
                _0x3f3947 = _0x4f4197[_0x218908(0x34b)] > _0x4f4197[_0x218908(0x139)]();
            this[_0x218908(0x32d)](_0x38878c, _0x218908(0x10d), _0x3f3947);
        }
        {
            const _0x410e7c = _0x2c1d45['levelDownToMin'],
                _0x3e1e55 = _0x410e7c['format'](TextManager[_0x218908(0x34b)]),
                _0x3766f7 = _0x4f4197[_0x218908(0x34b)] > _0x4f4197[_0x218908(0x139)]();
            this[_0x218908(0x32d)](_0x3e1e55, _0x218908(0x21e), _0x3766f7);
        }
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x1db)] = function (_0x3ed079) {
        const _0x4c0142 = _0x249061,
            _0x5502d4 = this[_0x4c0142(0x2ad)](_0x3ed079);
        _0x5502d4 === _0x4c0142(0x110)
            ? this['drawParamItem'](_0x3ed079)
            : this['drawRegularItem'](_0x3ed079);
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x140)] = function () {
        const _0x2e9a5d = _0x249061;
        return (
            Imported['VisuMZ_0_CoreEngine'] &&
            VisuMZ[_0x2e9a5d(0x25e)][_0x2e9a5d(0x27d)][_0x2e9a5d(0x1c1)][_0x2e9a5d(0x303)]
        );
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x129)] = function (_0x188823) {
        const _0x52251f = _0x249061,
            _0x54028a = this[_0x52251f(0x2eb)](_0x188823),
            _0x1cac92 = String(this[_0x52251f(0x180)](_0x188823))
                ['toUpperCase']()
                [_0x52251f(0x1ec)](),
            _0x5dd309 = SceneManager[_0x52251f(0x12b)][_0x52251f(0x2d9)]();
        if (!_0x5dd309) return;
        (this[_0x52251f(0x13d)](),
            this['changePaintOpacity'](!![]),
            this[_0x52251f(0x1c2)](ColorManager['normalColor']()));
        if (Imported['VisuMZ_0_CoreEngine']) {
            if (this[_0x52251f(0x140)]()) {
                const _0x4836db = VisuMZ[_0x52251f(0x1bf)](_0x1cac92);
                (this[_0x52251f(0x1bb)](_0x4836db, _0x54028a['x'] + 0x2, _0x54028a['y'] + 0x2),
                    (_0x54028a['x'] += ImageManager[_0x52251f(0x31d)] + 0x4),
                    (_0x54028a[_0x52251f(0x171)] -= ImageManager[_0x52251f(0x31d)] + 0x4));
            }
            const _0x34ed88 = TextManager[_0x52251f(0x110)](_0x1cac92);
            this[_0x52251f(0x2ec)](
                _0x34ed88,
                _0x54028a['x'],
                _0x54028a['y'],
                _0x54028a[_0x52251f(0x171)],
                _0x52251f(0x11b)
            );
        } else {
            const _0x39c753 = TextManager[_0x52251f(0x110)](Number(_0x1cac92));
            this[_0x52251f(0x2ec)](
                _0x39c753,
                _0x54028a['x'],
                _0x54028a['y'],
                _0x54028a['width'],
                'left'
            );
        }
        (this[_0x52251f(0x13d)](), this[_0x52251f(0x1c2)](ColorManager[_0x52251f(0x37d)]()));
        if (Imported['VisuMZ_0_CoreEngine']) {
            const _0x45be9b = _0x5dd309[_0x52251f(0x1d5)](_0x1cac92, !![]);
            this['drawText'](
                _0x45be9b,
                _0x54028a['x'],
                _0x54028a['y'],
                _0x54028a[_0x52251f(0x171)],
                'right'
            );
        } else {
            const _0x5ba134 = _0x5dd309[_0x52251f(0x110)](Number(_0x1cac92));
            this[_0x52251f(0x2ec)](
                _0x5ba134,
                _0x54028a['x'],
                _0x54028a['y'],
                _0x54028a[_0x52251f(0x171)],
                _0x52251f(0x38c)
            );
        }
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)][_0x249061(0x373)] = function () {
        return 'left';
    }),
    (Window_BestiaryBasic[_0x249061(0x19a)]['drawRegularItem'] = function (_0x1206bd) {
        const _0x26ce30 = _0x249061,
            _0x18d3d7 = this[_0x26ce30(0x2eb)](_0x1206bd),
            _0x5765a2 = this['commandName'](_0x1206bd),
            _0x206269 = this[_0x26ce30(0x2c3)](_0x5765a2)[_0x26ce30(0x171)];
        this[_0x26ce30(0x132)](this['isCommandEnabled'](_0x1206bd));
        const _0x37e55d = this['itemTextAlign']();
        if (_0x37e55d === _0x26ce30(0x38c))
            this['drawTextEx'](
                _0x5765a2,
                _0x18d3d7['x'] + _0x18d3d7[_0x26ce30(0x171)] - _0x206269,
                _0x18d3d7['y'],
                _0x206269
            );
        else {
            if (_0x37e55d === 'center') {
                const _0xa3afff =
                    _0x18d3d7['x'] +
                    Math[_0x26ce30(0x2a4)]((_0x18d3d7[_0x26ce30(0x171)] - _0x206269) / 0x2);
                this[_0x26ce30(0x1f8)](_0x5765a2, _0xa3afff, _0x18d3d7['y'], _0x206269);
            } else this['drawTextEx'](_0x5765a2, _0x18d3d7['x'], _0x18d3d7['y'], _0x206269);
        }
    }));
function Window_BestiaryElements() {
    const _0x11256e = _0x249061;
    this[_0x11256e(0x2f6)](...arguments);
}
((Window_BestiaryElements[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](
    Window_Command[_0x249061(0x19a)]
)),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiaryElements),
    (Window_BestiaryElements[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x1b6)] ?? 0x0,
    }),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x2f6)] = function (_0x1b46ff) {
        const _0x18607b = _0x249061;
        (Window_Command[_0x18607b(0x19a)][_0x18607b(0x2f6)][_0x18607b(0x310)](this, _0x1b46ff),
            this[_0x18607b(0x247)](),
            this[_0x18607b(0x295)](),
            this[_0x18607b(0x332)]());
    }),
    (Window_BestiaryElements[_0x249061(0x19a)]['becomeActive'] = function () {
        const _0x2636e1 = _0x249061;
        (this[_0x2636e1(0x32f)](), this[_0x2636e1(0x240)](0x0), this[_0x2636e1(0x29a)](0x0, 0x0));
    }),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x268)] = function () {}),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x131)] = function () {
        const _0x251b36 = _0x249061;
        for (
            let _0x14253a = 0x1;
            _0x14253a < $dataSystem[_0x251b36(0x326)]['length'];
            _0x14253a++
        ) {
            if (this[_0x251b36(0x189)](_0x14253a)) continue;
            const _0x2f101e = $dataSystem['elements'][_0x14253a];
            this[_0x251b36(0x32d)](_0x2f101e, _0x251b36(0x326), !![], _0x14253a);
        }
    }),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x189)] = function (_0x27a959) {
        const _0x220d05 = _0x249061;
        if (_0x27a959 <= 0x0) return !![];
        if (Imported[_0x220d05(0x1a3)]) {
            if (
                VisuMZ[_0x220d05(0x355)]['Settings'][_0x220d05(0x16f)][_0x220d05(0x33f)][
                    _0x220d05(0x2a7)
                ](_0x27a959)
            )
                return !![];
        }
        return ![];
    }),
    (Window_BestiaryElements[_0x249061(0x19a)][_0x249061(0x1db)] = function (_0x429fdb) {
        const _0x2fc81e = _0x249061,
            _0x443ffc = this[_0x2fc81e(0x2eb)](_0x429fdb),
            _0x1dc3ce = this[_0x2fc81e(0x180)](_0x429fdb),
            _0x45964a = this['_list'][_0x429fdb][_0x2fc81e(0x3a0)];
        (this[_0x2fc81e(0x13d)](),
            this[_0x2fc81e(0x132)](this[_0x2fc81e(0x1c4)](_0x429fdb)),
            this[_0x2fc81e(0x1f8)](_0x1dc3ce, _0x443ffc['x'], _0x443ffc['y'], _0x443ffc['width']));
        const _0x26560a = SceneManager[_0x2fc81e(0x12b)][_0x2fc81e(0x2d9)](),
            _0x3b9b6d = _0x26560a['elementRate'](_0x45964a),
            _0x1b5e33 = TextManager[_0x2fc81e(0x2c5)][_0x2fc81e(0x213)];
        let _0x4452c3 = _0x1b5e33['neutral'];
        if (
            Imported[_0x2fc81e(0x1a3)] &&
            _0x26560a[_0x2fc81e(0x1d0)]()[_0x2fc81e(0x2a7)](_0x45964a)
        )
            _0x4452c3 = _0x1b5e33[_0x2fc81e(0x211)];
        else {
            if (_0x3b9b6d > 1.05) _0x4452c3 = _0x1b5e33[_0x2fc81e(0x1da)];
            else {
                if (_0x3b9b6d <= 0x0) _0x4452c3 = _0x1b5e33[_0x2fc81e(0x31e)];
                else _0x3b9b6d < 0.95 && (_0x4452c3 = _0x1b5e33[_0x2fc81e(0x36b)]);
            }
        }
        const _0x4c03b5 =
            _0x443ffc['x'] +
            _0x443ffc[_0x2fc81e(0x171)] -
            this[_0x2fc81e(0x2c3)](_0x4452c3)[_0x2fc81e(0x171)];
        this['drawTextEx'](_0x4452c3, _0x4c03b5, _0x443ffc['y'], _0x443ffc['width']);
    }));
function Window_BestiarySkills() {
    this['initialize'](...arguments);
}
((Window_BestiarySkills[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](
    Window_Command[_0x249061(0x19a)]
)),
    (Window_BestiarySkills[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiarySkills),
    (Window_BestiarySkills[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)]['SkillsWindow_BgType'] ?? 0x0,
    }),
    (Window_BestiarySkills[_0x249061(0x19a)]['initialize'] = function (_0x7edf1d) {
        const _0x2647d4 = _0x249061;
        (Window_Command[_0x2647d4(0x19a)][_0x2647d4(0x2f6)][_0x2647d4(0x310)](this, _0x7edf1d),
            this['deactivate'](),
            this[_0x2647d4(0x295)](),
            this[_0x2647d4(0x332)]());
    }),
    (Window_BestiarySkills[_0x249061(0x19a)]['becomeActive'] = function () {
        const _0x9a3fe7 = _0x249061;
        (this[_0x9a3fe7(0x32f)](),
            this[_0x9a3fe7(0x240)](0x0),
            this[_0x9a3fe7(0x29a)](0x0, 0x0),
            this['_helpWindow'] && this[_0x9a3fe7(0x369)][_0x9a3fe7(0x202)]());
    }),
    (Window_BestiarySkills[_0x249061(0x19a)]['playOkSound'] = function () {}),
    (Window_BestiarySkills[_0x249061(0x19a)][_0x249061(0x131)] = function () {
        const _0x5739da = _0x249061,
            _0x1558bf = SceneManager[_0x5739da(0x12b)][_0x5739da(0x2d9)](),
            _0x2bf1ae = _0x1558bf['skills']()
                ['sort']((_0x1a6e16, _0xb5cc6e) => _0x1a6e16['id'] - _0xb5cc6e['id'])
                [_0x5739da(0x2b0)](
                    (_0x42fd91, _0x38679d, _0xda8ab6) =>
                        _0xda8ab6[_0x5739da(0x39c)](_0x42fd91) === _0x38679d
                );
        for (const _0x4be8d8 of _0x2bf1ae) {
            if (this[_0x5739da(0x322)](_0x4be8d8)) continue;
            this[_0x5739da(0x32d)](_0x4be8d8['id'], _0x5739da(0x25a), !![], _0x4be8d8['id']);
        }
    }),
    (Window_BestiarySkills[_0x249061(0x19a)][_0x249061(0x322)] = function (_0x2ca46f) {
        const _0x1cf14b = _0x249061;
        if (!_0x2ca46f) return !![];
        const _0x2c0c62 = VisuMZ['Bestiary']['RegExp'],
            _0x31b8ce = _0x2ca46f[_0x1cf14b(0x274)] || '';
        if (_0x31b8ce[_0x1cf14b(0x11c)](_0x2c0c62[_0x1cf14b(0x39d)])) return !![];
        return ![];
    }),
    (Window_BestiarySkills[_0x249061(0x19a)][_0x249061(0x1db)] = function (_0x546dc4) {
        const _0x3a9595 = _0x249061,
            _0x528a7c = this[_0x3a9595(0x2eb)](_0x546dc4),
            _0x51419a = this['commandName'](_0x546dc4),
            _0x194d71 = this['getSkillName'](_0x51419a);
        (this[_0x3a9595(0x13d)](),
            this['changePaintOpacity'](this[_0x3a9595(0x1c4)](_0x546dc4)),
            this[_0x3a9595(0x1f8)](
                _0x194d71,
                _0x528a7c['x'],
                _0x528a7c['y'],
                _0x528a7c[_0x3a9595(0x171)]
            ));
    }),
    (Window_BestiarySkills['prototype'][_0x249061(0x296)] = function (_0x452bda) {
        const _0x12a90c = _0x249061,
            _0x3783a3 = $dataSkills[_0x452bda];
        let _0x372de4 = _0x3783a3[_0x12a90c(0x14d)],
            _0x56f70b = _0x3783a3[_0x12a90c(0x286)];
        if (Imported[_0x12a90c(0x13e)]) {
            const _0x1c2215 = _0x3783a3[_0x12a90c(0x274)] || '';
            (_0x1c2215[_0x12a90c(0x11c)](/<DISPLAY ICON: (\d+)>/i) &&
                (_0x56f70b = Number(RegExp['$1'])),
                _0x1c2215[_0x12a90c(0x11c)](/<DISPLAY TEXT: (.*)>/i) &&
                    (_0x372de4 = String(RegExp['$1'])));
        }
        return _0x12a90c(0x22d)[_0x12a90c(0x340)](_0x372de4, _0x56f70b);
    }),
    (Window_BestiarySkills['prototype'][_0x249061(0x35d)] = function () {
        const _0x3163d5 = _0x249061,
            _0x3debd9 = this['currentExt']() ? $dataSkills[this['currentExt']()] : null;
        this['_helpWindow'][_0x3163d5(0x11e)](_0x3debd9);
    }));
function Window_BestiaryRewards() {
    const _0x1ca2b8 = _0x249061;
    this[_0x1ca2b8(0x2f6)](...arguments);
}
((Window_BestiaryRewards[_0x249061(0x19a)] = Object['create'](Window_Command['prototype'])),
    (Window_BestiaryRewards['prototype']['constructor'] = Window_BestiaryRewards),
    (Window_BestiaryRewards['SETTINGS'] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x195)] ?? 0x0,
        rewardsOrder: VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][
            'RewardsWindow_RewardsOrder'
        ] ?? [_0x249061(0x2f1), 'ap', 'cp', 'jp', 'sp', _0x249061(0x301), _0x249061(0x152)],
        expIcon:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x367)] ?? 0x57,
        goldIcon: VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x264)] ?? 0x13a,
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)]['initialize'] = function (_0x4d172c) {
        const _0x5e7f14 = _0x249061;
        (Window_Command[_0x5e7f14(0x19a)][_0x5e7f14(0x2f6)][_0x5e7f14(0x310)](this, _0x4d172c),
            this[_0x5e7f14(0x247)](),
            this['deselect'](),
            this['hide']());
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)]['becomeActive'] = function () {
        const _0x1ff76d = _0x249061;
        (this[_0x1ff76d(0x32f)](), this['forceSelect'](0x0), this[_0x1ff76d(0x29a)](0x0, 0x0));
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x268)] = function () {}),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x131)] = function () {
        const _0xd361ce = _0x249061,
            _0x1339ed = Window_BestiaryRewards['SETTINGS']['rewardsOrder'];
        ((Math[_0xd361ce(0x23d)] = !![]),
            (SceneManager[_0xd361ce(0x12b)]['enemy']()[_0xd361ce(0x302)] = undefined));
        for (const _0x401baf of _0x1339ed) {
            if (_0x401baf === _0xd361ce(0x2f1)) this['addExpCommand']();
            if (_0x401baf === 'ap') this['addApCommand']();
            if (_0x401baf === 'cp') this[_0xd361ce(0x21b)]();
            if (_0x401baf === 'jp') this[_0xd361ce(0x1e8)]();
            if (_0x401baf === 'sp') this[_0xd361ce(0x28b)]();
            if (_0x401baf === _0xd361ce(0x301)) this[_0xd361ce(0x335)]();
            if (_0x401baf === _0xd361ce(0x152)) this[_0xd361ce(0x250)]();
        }
        Math[_0xd361ce(0x23d)] = ![];
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x366)] = function () {
        const _0x14e6f6 = _0x249061,
            _0x2a1333 = SceneManager[_0x14e6f6(0x12b)][_0x14e6f6(0x2d9)](),
            _0x56a1a5 = _0x2a1333[_0x14e6f6(0x2f1)](),
            _0x56e59f = TextManager[_0x14e6f6(0x35a)],
            _0x4e5df0 = Window_BestiaryRewards[_0x14e6f6(0x147)][_0x14e6f6(0x12a)];
        let _0x57ac34 =
            _0x4e5df0 > 0x0 ? _0x14e6f6(0x1f6)[_0x14e6f6(0x340)](_0x4e5df0, _0x56e59f) : _0x56e59f;
        this[_0x14e6f6(0x32d)](_0x57ac34, 'rewards', !![], _0x56a1a5);
    }),
    (Window_BestiaryRewards['prototype'][_0x249061(0x223)] = function () {
        const _0x5714fd = _0x249061;
        if (!Imported[_0x5714fd(0x3ab)]) return;
        if (!VisuMZ[_0x5714fd(0x14b)][_0x5714fd(0x27d)]['AbilityPoints'][_0x5714fd(0x1d1)]) return;
        const _0x3bf67d = SceneManager[_0x5714fd(0x12b)][_0x5714fd(0x2d9)](),
            _0x3b2d03 = _0x3bf67d[_0x5714fd(0x36e)](),
            _0x56b8c5 = TextManager['abilityPointsAbbr'],
            _0x3a4fcc = ImageManager[_0x5714fd(0x1cb)];
        let _0x286203 =
            _0x3a4fcc > 0x0 ? _0x5714fd(0x1f6)[_0x5714fd(0x340)](_0x3a4fcc, _0x56b8c5) : _0x56b8c5;
        this['addCommand'](_0x286203, 'rewards', !![], _0x3b2d03);
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x21b)] = function () {
        const _0x53644e = _0x249061;
        if (!Imported[_0x53644e(0x32c)]) return;
        if (!VisuMZ[_0x53644e(0x11d)][_0x53644e(0x27d)][_0x53644e(0x114)]['ShowVictory']) return;
        const _0x59ad64 = SceneManager[_0x53644e(0x12b)][_0x53644e(0x2d9)](),
            _0x5360ef = _0x59ad64['classPoints'](),
            _0x232a65 = TextManager[_0x53644e(0x273)],
            _0x28ea52 = ImageManager[_0x53644e(0x1d4)];
        let _0x19ea40 =
            _0x28ea52 > 0x0 ? _0x53644e(0x1f6)[_0x53644e(0x340)](_0x28ea52, _0x232a65) : _0x232a65;
        this[_0x53644e(0x32d)](_0x19ea40, 'rewards', !![], _0x5360ef);
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x1e8)] = function () {
        const _0x2daf0e = _0x249061;
        if (!Imported[_0x2daf0e(0x32c)]) return;
        if (!VisuMZ[_0x2daf0e(0x11d)][_0x2daf0e(0x27d)][_0x2daf0e(0x1b8)][_0x2daf0e(0x1d1)]) return;
        const _0x212961 = SceneManager['_scene']['enemy'](),
            _0x26bb4c = _0x212961['jobPoints'](),
            _0x32d959 = TextManager[_0x2daf0e(0x318)],
            _0x5d5289 = ImageManager[_0x2daf0e(0x2b2)];
        let _0x271f0d =
            _0x5d5289 > 0x0 ? _0x2daf0e(0x1f6)['format'](_0x5d5289, _0x32d959) : _0x32d959;
        this[_0x2daf0e(0x32d)](_0x271f0d, _0x2daf0e(0x31b), !![], _0x26bb4c);
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x28b)] = function () {
        const _0x296974 = _0x249061;
        if (!Imported[_0x296974(0x3ab)]) return;
        if (!VisuMZ[_0x296974(0x14b)][_0x296974(0x27d)]['SkillPoints'][_0x296974(0x1d1)]) return;
        const _0x55490a = SceneManager[_0x296974(0x12b)][_0x296974(0x2d9)](),
            _0x398b0f = _0x55490a[_0x296974(0x1d2)](),
            _0x3e4405 = TextManager['skillPointsAbbr'],
            _0x4e5316 = ImageManager[_0x296974(0x190)];
        let _0x590db0 =
            _0x4e5316 > 0x0 ? _0x296974(0x1f6)['format'](_0x4e5316, _0x3e4405) : _0x3e4405;
        this[_0x296974(0x32d)](_0x590db0, 'rewards', !![], _0x398b0f);
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x335)] = function () {
        const _0x1c7ce4 = _0x249061,
            _0x86cb5a = SceneManager[_0x1c7ce4(0x12b)][_0x1c7ce4(0x2d9)](),
            _0x5e9872 = _0x86cb5a[_0x1c7ce4(0x301)](),
            _0xd89170 = TextManager[_0x1c7ce4(0x251)],
            _0x5c8a4a = Window_BestiaryRewards[_0x1c7ce4(0x147)]['goldIcon'];
        let _0x4591bf =
            _0x5c8a4a > 0x0 ? '\x5cI[%1]%2'[_0x1c7ce4(0x340)](_0x5c8a4a, _0xd89170) : _0xd89170;
        this['addCommand'](_0x4591bf, _0x1c7ce4(0x31b), !![], _0x5e9872);
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x250)] = function () {
        const _0x2886c4 = _0x249061;
        (this[_0x2886c4(0x319)](),
            this[_0x2886c4(0x1ca)](),
            this[_0x2886c4(0x1b0)](),
            this['addSortedEnemyDrops']());
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)]['makeEmptyGroups'] = function () {
        const _0x50d29e = _0x249061;
        this[_0x50d29e(0x18a)] = {};
        const _0xdf28ef = [
                _0x50d29e(0x385),
                _0x50d29e(0x1c6),
                'chance20',
                _0x50d29e(0x2ef),
                'chance0',
                _0x50d29e(0x390),
            ],
            _0x1c571e = [_0x50d29e(0x152), _0x50d29e(0x348), _0x50d29e(0x36a)];
        for (const _0x44b545 of _0xdf28ef) {
            for (const _0x1885a9 of _0x1c571e) {
                ((this['_enemyDrops'][_0x44b545] = this[_0x50d29e(0x18a)][_0x44b545] || {}),
                    (this[_0x50d29e(0x18a)][_0x44b545][_0x1885a9] =
                        this[_0x50d29e(0x18a)][_0x44b545][_0x1885a9] || []));
            }
        }
    }),
    (VisuMZ['Bestiary'][_0x249061(0x1f3)] = function (_0x490099, _0x3b564a) {
        if (_0x490099 === 0x1) return $dataItems[_0x3b564a];
        if (_0x490099 === 0x2) return $dataWeapons[_0x3b564a];
        if (_0x490099 === 0x3) return $dataArmors[_0x3b564a];
        return null;
    }),
    (VisuMZ[_0x249061(0x2c5)]['GetDropRateText'] = function (_0x22d26b) {
        const _0x2f8117 = _0x249061,
            _0x3bddf5 = TextManager['Bestiary']['rewardsWindow'];
        if (_0x22d26b >= 0x1) return _0x3bddf5[_0x2f8117(0x385)];
        else {
            if (_0x22d26b >= 0.5) return _0x3bddf5['chance50'];
            else {
                if (_0x22d26b >= 0.2) return _0x3bddf5[_0x2f8117(0x26d)];
                else return _0x22d26b >= 0.1 ? _0x3bddf5[_0x2f8117(0x2ef)] : _0x3bddf5['chance0'];
            }
        }
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x198)] = function (_0x15d988, _0x246f94) {
        const _0x5961b3 = _0x249061;
        if (!_0x15d988) return;
        const _0x5d973f = TextManager[_0x5961b3(0x2c5)]['rewardsWindow'],
            _0x1c7998 = [
                _0x5961b3(0x385),
                _0x5961b3(0x1c6),
                _0x5961b3(0x26d),
                _0x5961b3(0x2ef),
                _0x5961b3(0x21d),
                'conditional',
            ];
        let _0x3815b5 = '';
        for (const _0x3b656c of _0x1c7998) {
            if (_0x246f94 === _0x5d973f[_0x3b656c]) _0x3815b5 = _0x3b656c;
        }
        let _0x3c2f2d = '';
        if (DataManager[_0x5961b3(0x231)](_0x15d988)) _0x3c2f2d = _0x5961b3(0x152);
        if (DataManager['isWeapon'](_0x15d988)) _0x3c2f2d = _0x5961b3(0x348);
        if (DataManager[_0x5961b3(0x206)](_0x15d988)) _0x3c2f2d = 'armors';
        this[_0x5961b3(0x18a)][_0x3815b5][_0x3c2f2d][_0x5961b3(0x2a5)](_0x15d988);
    }),
    (Window_BestiaryRewards['prototype'][_0x249061(0x1ca)] = function () {
        const _0x40bca1 = _0x249061,
            _0x540afb = SceneManager[_0x40bca1(0x12b)]['enemy'](),
            _0x291966 = _0x540afb[_0x40bca1(0x2d9)]()[_0x40bca1(0x1fe)];
        if (!_0x291966) return;
        for (const _0x4705a7 of _0x291966) {
            if (_0x4705a7[_0x40bca1(0x212)] <= 0x0) continue;
            const _0x13685c = 0x1 / Math[_0x40bca1(0x2b7)](_0x4705a7['denominator'], 0x1),
                _0x584e48 = VisuMZ['Bestiary'][_0x40bca1(0x1f3)](
                    _0x4705a7[_0x40bca1(0x212)],
                    _0x4705a7[_0x40bca1(0x1d6)]
                ),
                _0x2b8a73 = VisuMZ[_0x40bca1(0x2c5)]['GetDropRateText'](_0x13685c);
            this[_0x40bca1(0x198)](_0x584e48, _0x2b8a73);
        }
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)][_0x249061(0x1b0)] = function () {
        const _0xab203d = _0x249061;
        if (!Imported[_0xab203d(0x2af)]) return;
        const _0x3d1b1a = SceneManager[_0xab203d(0x12b)][_0xab203d(0x2d9)](),
            _0x323471 = _0x3d1b1a['enemy']()[_0xab203d(0x274)] || '',
            _0xa9a3b4 = _0x323471[_0xab203d(0x2f7)](/[\r\n]+/),
            _0x1547a3 = TextManager[_0xab203d(0x2c5)][_0xab203d(0x26b)]['conditional'];
        for (const _0x1a7177 of _0xa9a3b4) {
            if (
                _0x1a7177[_0xab203d(0x11c)](
                    /<CONDITIONAL (ITEM|WEAPON|ARMOR) (\d+)[ ](?:THROUGH|to)[ ](\d+) (?:DROP|DROPS)>/i
                )
            ) {
                const _0x3214fa = VisuMZ[_0xab203d(0x23a)]['getDatabase'](RegExp['$1']),
                    _0x4af131 = Number(RegExp['$2']),
                    _0x3acf30 = Number(RegExp['$3']);
                for (let _0x5ae546 = _0x4af131; _0x5ae546 <= _0x3acf30; _0x5ae546++) {
                    const _0x392380 = _0x3214fa[_0x5ae546] || null;
                    _0x392380 &&
                        _0x392380[_0xab203d(0x14d)][_0xab203d(0x1ec)]() !== '' &&
                        !_0x392380['name'][_0xab203d(0x11c)](/-----/i) &&
                        this[_0xab203d(0x198)](_0x392380, _0x1547a3);
                }
            }
            if (
                _0x1a7177[_0xab203d(0x11c)](
                    /<CONDITIONAL (ITEM|WEAPON|ARMOR) (\d+) (?:DROP|DROPS)>/i
                )
            ) {
                const _0x551488 = VisuMZ['ExtraEnemyDrops'][_0xab203d(0x14f)](RegExp['$1']),
                    _0x3ca980 = Number(RegExp['$2']),
                    _0x5f33a9 = _0x551488[_0x3ca980];
                this[_0xab203d(0x198)](_0x5f33a9, _0x1547a3);
            }
            if (
                _0x1a7177[_0xab203d(0x11c)](
                    /<CONDITIONAL (ITEM|WEAPON|ARMOR) (.*) (?:DROP|DROPS)>/i
                )
            ) {
                const _0x4d76d4 = VisuMZ[_0xab203d(0x23a)][_0xab203d(0x277)](
                    RegExp['$1'],
                    RegExp['$2']
                );
                this[_0xab203d(0x198)](_0x4d76d4, _0x1547a3);
            }
        }
    }),
    (Window_BestiaryRewards['prototype'][_0x249061(0x154)] = function (_0x9adf44, _0x12ee01) {
        const _0x1481a4 = _0x249061;
        if (!_0x9adf44) return;
        const _0x5bda50 = _0x9adf44[_0x1481a4(0x14d)],
            _0xd652 = _0x9adf44[_0x1481a4(0x286)];
        let _0x36f51f =
            _0xd652 > 0x0 ? _0x1481a4(0x1f6)[_0x1481a4(0x340)](_0xd652, _0x5bda50) : _0x5bda50;
        this['addCommand'](_0x36f51f, _0x1481a4(0x31b), !![], _0x12ee01);
    }),
    (Window_BestiaryRewards['prototype']['addSortedEnemyDrops'] = function () {
        const _0x4f1625 = _0x249061,
            _0x1b9295 = [
                'chance100',
                _0x4f1625(0x1c6),
                _0x4f1625(0x26d),
                _0x4f1625(0x2ef),
                _0x4f1625(0x21d),
                'conditional',
            ],
            _0x2622c9 = [_0x4f1625(0x152), _0x4f1625(0x348), 'armors'];
        for (const _0x2ab6e1 of _0x1b9295) {
            for (const _0xc74682 of _0x2622c9) {
                let _0x3e00fb = this[_0x4f1625(0x18a)][_0x2ab6e1][_0xc74682];
                _0x3e00fb = _0x3e00fb['sort'](
                    (_0x70f632, _0x578afa) => _0x70f632['id'] - _0x578afa['id']
                );
                for (const _0x407dd4 of _0x3e00fb) {
                    const _0x17c127 = TextManager[_0x4f1625(0x2c5)][_0x4f1625(0x26b)][_0x2ab6e1];
                    this[_0x4f1625(0x154)](_0x407dd4, _0x17c127);
                }
            }
        }
    }),
    (Window_BestiaryRewards[_0x249061(0x19a)]['drawItem'] = function (_0x24fa1e) {
        const _0x4642db = _0x249061,
            _0x1a3249 = this[_0x4642db(0x2eb)](_0x24fa1e),
            _0x21858b = this[_0x4642db(0x180)](_0x24fa1e),
            _0x363263 = String(this[_0x4642db(0x1a9)][_0x24fa1e]['ext']);
        (this[_0x4642db(0x13d)](),
            this['changePaintOpacity'](this[_0x4642db(0x1c4)](_0x24fa1e)),
            this['drawTextEx'](
                _0x21858b,
                _0x1a3249['x'],
                _0x1a3249['y'],
                _0x1a3249[_0x4642db(0x171)]
            ));
        const _0x46b47e =
            _0x1a3249['x'] +
            (_0x1a3249['width'] - this[_0x4642db(0x2c3)](_0x363263)[_0x4642db(0x171)]);
        this[_0x4642db(0x1f8)](_0x363263, _0x46b47e, _0x1a3249['y'], _0x1a3249['width']);
    }));
function Window_BestiaryTraits() {
    const _0x403076 = _0x249061;
    this[_0x403076(0x2f6)](...arguments);
}
function _0x5e87(_0x40117c, _0x3bbaa) {
    const _0x550e73 = _0x550e();
    return (
        (_0x5e87 = function (_0x5e87f7, _0x2d3a10) {
            _0x5e87f7 = _0x5e87f7 - 0x105;
            let _0xc05879 = _0x550e73[_0x5e87f7];
            return _0xc05879;
        }),
        _0x5e87(_0x40117c, _0x3bbaa)
    );
}
function _0x550e() {
    const _0x5a41e3 = [
        'setBackgroundOpacity',
        'createTraitsDataWindow',
        'fontSize',
        'end',
        '_showEnemyInBestiary',
        'iconIndex',
        'drawItemStyleIcon',
        'SubWindowCompleteFixedDigits',
        'hasAnimatedSvActorBattler',
        'origin',
        'addSpCommand',
        '_pageupButton',
        'constructor',
        'Window_MenuCommand_addOriginalCommands',
        '_timesEnemySeen',
        'maxItems',
        'FastSoundFreq',
        'loadTitle2',
        'PossibleRandomSingularTraitsFromNotetags',
        'loadEnemy',
        'deselect',
        'getSkillName',
        'Gender',
        'animation',
        'gainLevel',
        'scrollTo',
        'initBestiaryMainMenu',
        'Game_System_initialize',
        'offsetX',
        'JSON',
        'includesTrait',
        'Game_BattlerBase_addNewState',
        'traitSet',
        'toFixed',
        'addTimesEnemyDefeated',
        'floor',
        'push',
        'traitSetType',
        'includes',
        'ListWindow_BgType',
        'SkillsText',
        'SlowSoundFreq',
        'helpAreaHeight',
        'refresh',
        'commandSymbol',
        'listWindowRect',
        'VisuMZ_4_ExtraEnemyDrops',
        'filter',
        'addOriginalCommands',
        'jobPointsIcon',
        'autoWordWrap',
        'needsPageButtons',
        'buttonAssistText3',
        '_svBattlerName',
        'max',
        'customPicture',
        'pagedown',
        'join',
        'TraitsText',
        'RegExp',
        'isSideView',
        'traitsWindow',
        'levelMax',
        'ListWindow_RectJS',
        '_tp',
        'PossibleSingularTraitsFromNotetags',
        'textSizeEx',
        'buttonAssistText4',
        'Bestiary',
        'NameWindow_BgType',
        'updateSpriteVisibility',
        'lore',
        'remove',
        'setMainMenuBestiaryVisible',
        'fastScrollLore',
        'setMainMenuBestiaryEnabled',
        'playDragonbonesIdleAnimation',
        'addLoadListener',
        'commandNameWindowDrawText',
        'Switch\x20Monster',
        '123tivGrx',
        'SubWindowCompleteFiBasicWindow_LevelDownByOnexedDigits',
        'replace',
        '_bestiaryEnemyCustomImageFilename',
        '_basicDataWindow',
        'removeChild',
        'ElementsWindow_Immune',
        '#%1',
        'enemy',
        'processDragonbones',
        'levelUpToMax',
        'DataWindow_RectJS',
        'Scene_Menu_createCommandWindow',
        'drawAllText',
        'processCursorMove',
        'enemyBestiaryCategories',
        'battler',
        'HelpWindow_BgType',
        'setTraitSet',
        'disposeDragonbones',
        'toggleTraitsCategory',
        'displayAllTraitTypes',
        'CategoryOrder',
        'trait',
        'BasicWindow_ShowLevelChange',
        'centerSprite',
        'itemLineRect',
        'drawText',
        'isPressed',
        'commandBestiary',
        'chance10',
        'onBasicDataLevelChange',
        'exp',
        'mainAreaBottom',
        'commandNameWindowCenter',
        'isTriggered',
        'defeatedFmt',
        'initialize',
        'split',
        'categoryEnemies',
        '_battlebackSprite2',
        'create',
        '_elementsDataWindow',
        'iconText',
        'visible',
        'originalName',
        'isMainMenuBestiaryVisible',
        'Game_BattlerBase_refresh',
        'gold',
        '_visualDrops',
        'DrawIcons',
        'isAutoColorAffected',
        'dragonbonesData',
        'createSkillsDataWindow',
        '_allTextHeight',
        'ListWindowDelayMS',
        'buttonAssistKey3',
        'setHue',
        'maxLevel',
        'isRightInputMode',
        'timesEnemyDefeated',
        'buttonAssistKey4',
        'filters',
        'call',
        'innerWidth',
        'FUNC',
        'includeCategory',
        'helpWindowRect',
        'Lore',
        'arePageButtonsEnabled',
        '_cache',
        'jobPointsAbbr',
        'makeEmptyGroups',
        'update',
        'rewards',
        'ConvertParams',
        'iconWidth',
        'immune',
        'ElementsText',
        'toLowerCase',
        'isCustomCommandEnabled',
        'isSkillHidden',
        'isEnemy',
        'setNoEnemyText',
        '-\x20\x5cC[16]%1',
        'elements',
        'Default',
        'basic',
        'addInnerChild',
        'setBackgroundType',
        'buttonAssistKey2',
        'VisuMZ_2_ClassChangeSystem',
        'addCommand',
        'initBestiarySettings',
        'activate',
        '_commandNameWindow',
        'TraitsWindow_NullHelpDesc',
        'hide',
        'viewEnemy',
        'defaultCategory',
        'addGoldCommand',
        'map',
        'setEnemy',
        'commandStyle',
        'ImageWindow_BlurStrength',
        'ARRAYNUM',
        'createEnemySprite',
        'ImageWindow_RectJS',
        'bitmap',
        '_dragonbones',
        'ExcludeElements',
        'format',
        'uiHelpPosition',
        'LoreWindow_FontSize',
        'switch',
        'levelUp',
        '_Bestiary_MainMenu',
        'bestiaryEnemyBattleback1',
        'battleback1',
        'weapons',
        '_battlebackSprite1',
        'createContents',
        'level',
        'updateEnemyImage',
        'changeEnemyTrait',
        'animations',
        'helpWindowRatio',
        'TraitsIcon',
        'boxWidth',
        '_enemy',
        'processFastScroll',
        'currentCategory',
        'ElementStatusCore',
        'createEnemy',
        'Element',
        'isEnemyNameMasked',
        'downArrowVisible',
        'expA',
        'apply',
        'SubWindow_Completion',
        'updateHelp',
        'ImageWindow_Padding',
        'round',
        'helpWindow_BgType',
        'bgType',
        'setup',
        'defaultBattleback1',
        'buttonAssist_Switch',
        'isEnemyFullyVisible',
        'addExpCommand',
        'EXP_Icon',
        '_categoryEnemyIDs',
        '_helpWindow',
        'armors',
        'resist',
        'processFullEnemyImage',
        '11429OFpsPg',
        'abilityPoints',
        'length',
        'becomeActive',
        'updateCommandNameWindow',
        'Visible',
        'itemTextAlign',
        '_blurFilter',
        'svActorVertCells',
        'closedCategoriesFmt',
        'setText',
        '_traitsDataWindow',
        'height',
        'Rewards',
        '_rewardsDataWindow',
        'LoreWindow_BgType',
        'normalColor',
        '\x5cI[73]Raise\x20%1\x20Up\x20to\x20Maximum',
        'BattleManager_setup',
        'version',
        'setHandler',
        'makeEnemyList',
        'loreWindow',
        'addBestiaryCommand',
        'chance100',
        '\x5cC[27]Absorb',
        'traits',
        'Vocab',
        'SubWindow_Defeated',
        'Properties',
        'processSlowScroll',
        'right',
        'isDead',
        'Reveal',
        'calculateTextHeight',
        'conditional',
        'center',
        'bestiaryEnemyBattleback2',
        'SubWindow_Encountered',
        'process_VisuMZ_Bestiary_Categories',
        'maxCols',
        'commands',
        'createCommandWindow',
        'ElementsIcon',
        'Grassland',
        'home',
        'buttonAssist_View',
        'indexOf',
        'hideSkill',
        'buttonAssist_Collapse',
        'pageup',
        'ext',
        'nextEnemy',
        'createElementsDataWindow',
        '1753688bRZqfJ',
        '_debugViewBestiary',
        '\x5cC[5]Rare',
        'createFilters',
        'maskUndefeatedEnemyNames',
        'TraitsWindow_OpenCategory',
        'onDatabaseLoaded',
        'BgFilename1',
        'VisuMZ_2_SkillLearnSystem',
        'Display',
        'battlerHue',
        'registerCommand',
        'buttonAssistKey1',
        'setLevel',
        'Name',
        'getBackgroundOpacity',
        'loadBattleback2',
        'openCloseCurrentCategory',
        'ElementsWindow_Resist',
        'Variant',
        'setSymbolWindow',
        'down',
        '_enemyID',
        'Enable',
        'CategoryWindow_MaskChar',
        'loadArmature',
        'Encountered:\x20%1',
        'Description',
        'updateEnemy',
        '9octLFX',
        '-\x20%1\x20(%2%)',
        'createBattlebackSprites',
        'deactivateSymbolWindow',
        'createDragonbonesSprite',
        'Fast\x20Scroll',
        'BgSettings',
        'CategoryWindow_OpenCategory',
        'createBasicDataWindow',
        'TraitsWindow_ShowAllTraits',
        'category',
        'bestiaryEnemyBattlebackData',
        'isBestiaryCommandVisible',
        'defaultLoreFmt',
        'imageWindowRect',
        'buttonAssistText1',
        'levelDown',
        'BasicWindow_BgType',
        'isEnemyDefeated',
        'param',
        'drawBestiaryCompletionRate',
        'BasicText',
        'timesEnemySeen',
        'ClassPoints',
        '_categoryStatus',
        'process_VisuMZ_Bestiary',
        'addTraitCommand',
        'fixedPercentage',
        'Skills',
        '\x5cC[7]Immune',
        'left',
        'match',
        'ClassChangeSystem',
        'setItem',
        '144MfGgkX',
        'text',
        'mainAreaHeight',
        'Categories',
        'EVAL',
        'ListWindow_MaskUnknown',
        'NameWindow_RectJS',
        'clamp',
        'uiMenuStyle',
        '55025bkAyfz',
        'drawParamItem',
        'expIcon',
        '_scene',
        'loadBattleback1',
        'setHelpWindow',
        'playCursor',
        'STRUCT',
        '\x5cC[27]Super\x20Rare',
        'makeCommandList',
        'changePaintOpacity',
        '\x5cI[73]Raise\x20%1\x20Up',
        'resetWordWrap',
        'drawMessageText',
        'ShowMainMenu',
        'getDefeatedEnemies',
        'dataWindowRect',
        'minLevel',
        'buttonAssistText2',
        'addCategory',
        'battlerName',
        'resetFontSettings',
        'VisuMZ_1_BattleCore',
        'fastSoundFrequency',
        'shouldDrawIcons',
        'createImageWindow',
        'PossibleMassTraitsFromNotetags',
        '\x5cC[4]Uncommon',
        'Window',
        '9624ysacxX',
        'concat',
        'SETTINGS',
        'auto',
        'categoryEnemyIDs',
        'Show',
        'SkillLearnSystem',
        'updateFilters',
        'name',
        'setSubWindow',
        'getDatabase',
        '_lastIndex',
        'updateBattlebackImages',
        'items',
        'isEnabledEnemy',
        'addItemDropCommand',
        'commandNameWindowDrawBackground',
        'addTimesEnemySeen',
        'scaleHelpWindow',
        'hideInBestiary',
        'RewardsWindow_Chance0',
        'sort',
        'setDebugViewBestiary',
        'setEnemyID',
        'isBestiaryCommandEnabled',
        'addBestiaryCommandAutomatically',
        'anchor',
        'defaultBattleback2',
        'addChild',
        'processSvActorImage',
        'SubWindow_BgType',
        'onDataCategoriesOpen',
        '_getBestiaryLore',
        'scrollToBottom',
        'prevEnemy',
        'Common',
        'callUpdateHelp',
        'drawItemStyleIconText',
        '_backSprite1',
        'SubWindow_RectJS',
        '-----',
        'completionFmt',
        'StatusMenu',
        'createHelpWindow',
        'width',
        'showEnemyInBestiary',
        'ExtDisplayedParams',
        'VisuMZ_2_DragonbonesUnion',
        'addWindow',
        '3490UsHhHJ',
        'itemPadding',
        'ElementsWindow_Weak',
        'List',
        '_subWindow',
        'DataCategoriesWindow_RectJS',
        'fastScrollSpeed',
        'loadTitle1',
        '_text',
        'isBottomHelpMode',
        'commandName',
        'makeTraitList',
        'parameters',
        'createSpecialBattlers',
        'random',
        'updatePageButtons',
        'nameWindowRect',
        'Curse',
        '_dragonbonesName',
        'isElementIDExcluded',
        '_enemyDrops',
        'callUpdateSymbolWindow',
        'RewardsWindow_Conditional',
        'SnapshotOpacity',
        'bestiaryEnemyCustomImageFilename',
        'createSubWindow',
        'skillPointsIcon',
        'canAddLevelChange',
        '1381107rzlKDv',
        '37160eslytH',
        'Blessing',
        'RewardsWindow_BgType',
        'processEnemyLore',
        'ARRAYEVAL',
        'addItemToGroup',
        'toUpperCase',
        'prototype',
        'buttonAssistText5',
        'onLoadDragonbones',
        'createBackground',
        'Label',
        'getBestiaryLore',
        'skillId',
        'updateOrigin',
        'innerHeight',
        'VisuMZ_1_ElementStatusCore',
        'createRewardsDataWindow',
        'nameWindow',
        '\x5cI[74]Lower\x20%1\x20Down',
        'opacity',
        'offsetY',
        '_list',
        '_pagedownButton',
        'mainAreaTop',
        '\x5cC[25]Resist',
        'wait',
        'SystemShowBestiaryMenu',
        '_backSprite2',
        'addEnemyConditionalDrops',
        'Defeated:\x20%1',
        'addEnemy',
        'callUpdateSubWindow',
        'min',
        '_commandWindow',
        'ElementsWindow_BgType',
        'index',
        'JobPoints',
        'BestiaryMenuCommand',
        '_loreDataWindow',
        'drawIcon',
        'adjustSprite',
        'EnableMainMenu',
        'getTraitSet',
        'GetParamIcon',
        'addNewState',
        'Param',
        'changeTextColor',
        '_skillsDataWindow',
        'isCommandEnabled',
        'updateArrows',
        'chance50',
        'parse',
        '+\x20\x5cC[16]%1',
        'frameCount',
        'addEnemyDatabaseDrops',
        'abilityPointsIcon',
        'createCustomBackgroundImages',
        'bestiary',
        'smoothSelect',
        'description',
        'getAbsorbedElements',
        'ShowVictory',
        'skillPoints',
        'scaleX',
        'classPointsIcon',
        'paramValueByName',
        'dataId',
        'subWindow',
        'view',
        '(needs\x20key)',
        'weak',
        'drawItem',
        'LoreWindow_AutoWordWrap',
        '_dragonbonesBattlerData',
        'CategoryWindow_Style',
        'skills',
        'hasDragonbonesBattler',
        'canDebugViewBestiary',
        'maxTp',
        '_nameWindow',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'dataCategoriesWindowRect',
        'hasSvBattler',
        'isPlaytest',
        'addJpCommand',
        'contentsHeight',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'buttonAssist_FastScroll',
        'trim',
        'isUseModernControls',
        'CategoryData',
        'padding',
        'currentExt',
        '_dragonbonesSpriteContainer',
        '_imageWindow',
        'GetItemObj',
        'isMainMenuBestiaryEnabled',
        'Alignment',
        '\x5cI[%1]%2',
        'BESTIARY',
        'drawTextEx',
        'expand',
        'commandStyleCheck',
        '+\x20%1\x20(%2%)',
        '_enemySprite',
        'enemyId',
        'dropItems',
        'addCustomCommand',
        'commandOrder',
        'RewardsIcon',
        'show',
        'active',
        'buttonAssist_Expand',
        'BgFilename2',
        'isArmor',
        'Scene_Boot_onDatabaseLoaded',
        'isCategoryOpen',
        'actions',
        'clear',
        'abs',
        'createDataCategoriesWindow',
        'playOk',
        'enabled',
        'setClickHandler',
        'svBattlerData',
        'absorb',
        'kind',
        'elementsWindow',
        'Race',
        'buttonAssist',
        'Scroll',
        'playCursorSound',
        'setImageWindow',
        'createNameWindow',
        'contents',
        'addCpCommand',
        'updateEnemyID',
        'chance0',
        'levelMin',
        'Nature',
        'TraitsWindow_BgType',
        'slowScrollSpeed',
        'isSceneBattle',
        'addApCommand',
        '592511sdHEhv',
        'activateSymbolWindow',
        'NameWindow_CategoryText',
        'MainMenu',
        'HelpWindow_RectJS',
        'onSymbolWindowCancel',
        'maskChar',
        'createPageButtons',
        '\x5cC[0]Normal',
        '\x5cI[%2]%1',
        'VisuMZ_1_MessageCore',
        'Zodiac',
        'hideAllSymbolWindows',
        'isItem',
        '_listWindow',
        'ImageWindow_Battleback2',
        'LoreText',
        'createLoreDataWindow',
        'SystemEnableBestiaryMenu',
        'slowScrollLore',
        'scale',
        'makeTraitCommand',
        'ExtraEnemyDrops',
        'Little\x20is\x20known\x20about\x20this\x20monster.',
        'setFrame',
        '_noRandom',
        'NUM',
        'ARRAYJSON',
        'forceSelect',
        'TraitsWindow_ClosedCategory',
        'shown',
        'totalDefeatedEnemies',
        'bind',
        'categoryWindow',
        'icon',
        'deactivate',
        'setColorTone',
        'VisuMZ_0_CoreEngine',
        'windowPadding',
        'exit',
        'initCategoryStatus',
        'Math_random',
        'getTraitSetKeys',
        'createListWindow',
        'addItemsCommand',
        'currencyUnit',
        '_bestiaryTotalEnemies',
        'ImageWindow_BgType',
        'SubElement',
        'CategoryWindow_CommandOrder',
        'subWindowRect',
        'traitHelp',
        'openCategoriesFmt',
        'cancel',
        'skill',
        'getColor',
        '_dataCategoriesWindow',
        'Collapse',
        'CoreEngine',
        'RewardsWindow_Chance10',
        'setEnemyName',
        'calcWindowHeight',
        'BlurFilter',
        'includeEnemy',
        'Gold_Icon',
        'scrollToTop',
        'This\x20monster\x20has\x20no\x20special\x20properties.',
        'collapse',
        'playOkSound',
        'createCommandNameWindow',
        '_timesEnemyDefeated',
        'rewardsWindow',
        'toggleEnemyCategory',
        'chance20',
        'callUpdateImage',
        '_enemyBestiaryCategories',
        'uiButtonPosition',
        'drawEncounterData',
        '_bestiaryEnemyBattlebackData',
        'classPointsAbbr',
        'note',
        '\x5cC[24]Guaranteed',
        'createAllWindows',
        'getDatabaseItem',
        'dispose',
        'baseParams',
        'setScrollAccel',
        'buttonAssistKey5',
        'currentSymbol',
        'Settings',
        'FastScrollSpeed',
        '_symbolWindows',
        'createArmature',
    ];
    _0x550e = function () {
        return _0x5a41e3;
    };
    return _0x550e();
}
((Window_BestiaryTraits[_0x249061(0x19a)] = Object[_0x249061(0x2fa)](
    Window_Command[_0x249061(0x19a)]
)),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x28d)] = Window_BestiaryTraits),
    (Window_BestiaryTraits[_0x249061(0x147)] = {
        bgType: VisuMZ['Bestiary'][_0x249061(0x27d)]['Window'][_0x249061(0x220)] ?? 0x0,
        displayAllTraitTypes:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x106)] ?? ![],
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x2f6)] = function (_0x3c72f6) {
        const _0x4015e4 = _0x249061;
        (this[_0x4015e4(0x24c)](),
            Window_Command[_0x4015e4(0x19a)]['initialize']['call'](this, _0x3c72f6),
            this[_0x4015e4(0x247)](),
            this[_0x4015e4(0x295)](),
            this[_0x4015e4(0x332)]());
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x24c)] = function () {
        const _0x212a8a = _0x249061;
        this[_0x212a8a(0x115)] = {};
        const _0x50937b = [
            'Element',
            _0x212a8a(0x254),
            _0x212a8a(0x297),
            _0x212a8a(0x214),
            _0x212a8a(0x21f),
            _0x212a8a(0x1f5),
            _0x212a8a(0x194),
            _0x212a8a(0x187),
            _0x212a8a(0x22f),
            _0x212a8a(0x3b6),
        ];
        for (const _0x1074b9 of _0x50937b) {
            this[_0x212a8a(0x115)][_0x1074b9[_0x212a8a(0x320)]()['trim']()] = !![];
        }
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x304)] = function () {
        return ![];
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x370)] = function () {
        const _0x138109 = _0x249061;
        (this[_0x138109(0x32f)](),
            this[_0x138109(0x240)](0x0),
            this['scrollTo'](0x0, 0x0),
            this['_helpWindow'] && this[_0x138109(0x369)][_0x138109(0x202)]());
    }),
    (Window_BestiaryTraits['prototype'][_0x249061(0x268)] = function () {
        const _0x3cd305 = _0x249061;
        if (this[_0x3cd305(0x27c)]() === _0x3cd305(0x107)) SoundManager[_0x3cd305(0x20d)]();
        else {
            if (this[_0x3cd305(0x27c)]() === 'trait') {
                const _0xc65a2a = this[_0x3cd305(0x1f0)](),
                    _0x22c65f = SceneManager['_scene']['enemy']();
                enabled =
                    _0x22c65f[_0x3cd305(0x1be)](_0xc65a2a[0x0])
                        [_0x3cd305(0x199)]()
                        [_0x3cd305(0x1ec)]() ===
                    _0xc65a2a[0x1][_0x3cd305(0x199)]()[_0x3cd305(0x1ec)]();
                if (!enabled) SoundManager['playEquip']();
            }
        }
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x131)] = function () {
        const _0x2eccde = _0x249061,
            _0x358793 = SceneManager[_0x2eccde(0x12b)][_0x2eccde(0x2d9)]();
        if (!_0x358793) return;
        const _0x3f2f58 = _0x358793[_0x2eccde(0x24e)]();
        for (const _0x8154a2 of _0x3f2f58) {
            if (!this[_0x2eccde(0x29f)](_0x8154a2, _0x358793)) continue;
            (this[_0x2eccde(0x117)](_0x8154a2), this[_0x2eccde(0x181)](_0x8154a2));
        }
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x29f)] = function (_0xbda18f, _0x371184) {
        const _0x3c8977 = _0x249061,
            _0x428d0d = DataManager[_0x3c8977(0x2a6)](_0xbda18f);
        if (!_0x428d0d) return ![];
        if (!_0x428d0d[_0x3c8977(0x372)]) return ![];
        return Window_BestiaryTraits[_0x3c8977(0x147)][_0x3c8977(0x2e6)]
            ? !![]
            : _0x371184 && _0x371184[_0x3c8977(0x1be)](_0xbda18f) !== '';
    }),
    (Window_BestiaryTraits['prototype'][_0x249061(0x117)] = function (_0x36f4d8) {
        const _0x75d5a = _0x249061,
            _0x2ce345 = this['isCategoryOpen'](_0x36f4d8)
                ? TextManager['Bestiary'][_0x75d5a(0x2be)][_0x75d5a(0x258)]
                : TextManager['Bestiary'][_0x75d5a(0x2be)][_0x75d5a(0x376)],
            _0x5ad7ff = DataManager['traitSetType'](_0x36f4d8),
            _0x97a4ed = _0x2ce345[_0x75d5a(0x340)](_0x5ad7ff[_0x75d5a(0x19e)]);
        this[_0x75d5a(0x32d)](_0x97a4ed, 'category', !![], _0x36f4d8);
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x208)] = function (_0x3ecc7d) {
        const _0x272045 = _0x249061;
        return (
            (_0x3ecc7d = _0x3ecc7d[_0x272045(0x320)]()['trim']()),
            this[_0x272045(0x115)][_0x3ecc7d]
        );
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)]['openCloseCurrentCategory'] = function () {
        const _0x51d9ab = _0x249061,
            _0x39a2ab = this[_0x51d9ab(0x354)]()[_0x51d9ab(0x320)]()['trim']();
        ((this['_categoryStatus'][_0x39a2ab] = !this['_categoryStatus'][_0x39a2ab]),
            this[_0x51d9ab(0x2ac)]());
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x354)] = function () {
        const _0xd2d7bc = _0x249061;
        return this[_0xd2d7bc(0x27c)]() === _0xd2d7bc(0x107) ? this[_0xd2d7bc(0x1f0)]() : null;
    }),
    (Window_BestiaryTraits['prototype'][_0x249061(0x181)] = function (_0x109c08) {
        const _0x3ed845 = _0x249061;
        if (!this[_0x3ed845(0x208)](_0x109c08)) return;
        const _0x8a8206 = SceneManager[_0x3ed845(0x12b)][_0x3ed845(0x2d9)](),
            _0x5d9d8c = VisuMZ[_0x3ed845(0x2c5)]['PossibleEnemyTraits'](_0x109c08, _0x8a8206);
        if (_0x5d9d8c[_0x3ed845(0x36f)] <= 0x0) {
            const _0x188d4a = _0x8a8206[_0x3ed845(0x1be)](_0x109c08);
            this['makeTraitCommand'](_0x109c08, _0x188d4a, _0x8a8206);
        } else
            for (const _0x3c2d23 of _0x5d9d8c) {
                this['makeTraitCommand'](_0x109c08, _0x3c2d23, _0x8a8206);
            }
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x239)] = function (
        _0x15c3c9,
        _0x2a963e,
        _0x29f372
    ) {
        const _0x553cc6 = _0x249061,
            _0x2434b7 = DataManager[_0x553cc6(0x2a1)](_0x15c3c9, _0x2a963e);
        this[_0x553cc6(0x32d)]('\x20\x20' + _0x2434b7[_0x553cc6(0x3ac)], _0x553cc6(0x2e8), !![], [
            _0x15c3c9,
            _0x2a963e,
        ]);
    }),
    (Window_BestiaryTraits['prototype']['drawItem'] = function (_0x53dd5c) {
        const _0x40924a = _0x249061,
            _0x20ab4e = this['itemLineRect'](_0x53dd5c),
            _0x53fd28 = this['commandName'](_0x53dd5c);
        this[_0x40924a(0x13d)]();
        let _0x125010 = !![];
        if (this[_0x40924a(0x2ad)](_0x53dd5c) === _0x40924a(0x2e8)) {
            const _0x5a0cef = this['_list'][_0x53dd5c]['ext'],
                _0x3cd71c = SceneManager[_0x40924a(0x12b)]['enemy']();
            _0x125010 =
                _0x3cd71c[_0x40924a(0x1be)](_0x5a0cef[0x0])
                    [_0x40924a(0x199)]()
                    [_0x40924a(0x1ec)]() === _0x5a0cef[0x1][_0x40924a(0x199)]()[_0x40924a(0x1ec)]();
        }
        (this[_0x40924a(0x132)](_0x125010),
            this[_0x40924a(0x1f8)](
                _0x53fd28,
                _0x20ab4e['x'],
                _0x20ab4e['y'],
                _0x20ab4e[_0x40924a(0x171)]
            ));
    }),
    (Window_BestiaryTraits[_0x249061(0x19a)][_0x249061(0x35d)] = function () {
        const _0x6eaffa = _0x249061;
        if (this[_0x6eaffa(0x27c)]() === _0x6eaffa(0x107))
            this[_0x6eaffa(0x369)]['setText'](
                TextManager[_0x6eaffa(0x2c5)]['traitsWindow'][_0x6eaffa(0x257)]
            );
        else {
            if (this[_0x6eaffa(0x27c)]() === 'trait') {
                const _0x264f73 = this[_0x6eaffa(0x1f0)](),
                    _0x15bd44 = DataManager[_0x6eaffa(0x2a1)](_0x264f73[0x0], _0x264f73[0x1]);
                this[_0x6eaffa(0x369)][_0x6eaffa(0x377)](
                    _0x15bd44 ? _0x15bd44[_0x6eaffa(0x3be)] || '' : ''
                );
            } else
                this['currentSymbol']() === null &&
                    this[_0x6eaffa(0x369)][_0x6eaffa(0x377)](
                        TextManager[_0x6eaffa(0x2c5)][_0x6eaffa(0x2be)]['nullHelp']
                    );
        }
    }));
function Window_BestiaryLore() {
    const _0x9c0310 = _0x249061;
    this[_0x9c0310(0x2f6)](...arguments);
}
((Window_BestiaryLore[_0x249061(0x19a)] = Object['create'](Window_Selectable[_0x249061(0x19a)])),
    (Window_BestiaryLore['prototype'][_0x249061(0x28d)] = Window_BestiaryLore),
    (Window_BestiaryLore[_0x249061(0x147)] = {
        bgType:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x37c)] ?? 0x0,
        fontSize:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x342)] ?? 0x16,
        autoWordWrap:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x1dc)] ?? ![],
        slowScrollSpeed:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)]['SlowScrollSpeed'] ?? 0x8,
        fastScrollSpeed:
            VisuMZ[_0x249061(0x2c5)]['Settings'][_0x249061(0x144)][_0x249061(0x27e)] ?? 0x20,
        slowSoundFrequency:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)][_0x249061(0x144)][_0x249061(0x2aa)] ?? 0x8,
        fastSoundFrequency:
            VisuMZ[_0x249061(0x2c5)][_0x249061(0x27d)]['Window'][_0x249061(0x291)] ?? 0x4,
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x2f6)] = function (_0x2671c0) {
        const _0x22011b = _0x249061;
        ((this[_0x22011b(0x17e)] = ''),
            Window_Selectable['prototype'][_0x22011b(0x2f6)]['call'](this, _0x2671c0),
            this['deactivate'](),
            this[_0x22011b(0x295)](),
            this[_0x22011b(0x332)]());
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x13d)] = function () {
        const _0x27fa83 = _0x249061;
        (Window_Selectable[_0x27fa83(0x19a)]['resetFontSettings'][_0x27fa83(0x310)](this),
            (this[_0x27fa83(0x21a)][_0x27fa83(0x283)] =
                Window_BestiaryLore[_0x27fa83(0x147)][_0x27fa83(0x283)]));
    }),
    (Window_BestiaryLore[_0x249061(0x19a)]['refresh'] = function () {
        const _0x135af8 = _0x249061;
        (this[_0x135af8(0x196)](),
            this[_0x135af8(0x38f)](),
            this[_0x135af8(0x34a)](),
            this[_0x135af8(0x2de)]());
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x38f)] = function () {
        const _0x597394 = _0x249061,
            _0x4e8c68 = this[_0x597394(0x17e)];
        ((this[_0x597394(0x307)] = 0x0),
            (this['_allTextHeight'] = this[_0x597394(0x2c3)](_0x4e8c68)[_0x597394(0x379)]));
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x1e9)] = function () {
        const _0x285009 = _0x249061;
        return Math[_0x285009(0x2b7)](this[_0x285009(0x307)], 0x1);
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x370)] = function () {
        const _0x33e7df = _0x249061;
        (this[_0x33e7df(0x32f)](), this['scrollTo'](0x0, 0x0));
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x196)] = function () {
        const _0xb591e8 = _0x249061,
            _0x13b59d = SceneManager[_0xb591e8(0x12b)][_0xb591e8(0x2d9)](),
            _0x56fb67 = TextManager['getBestiaryLore'](_0x13b59d);
        this[_0xb591e8(0x377)](_0x56fb67);
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x377)] = function (_0x5e1998) {
        const _0x47283d = _0x249061;
        if (_0x5e1998 === this['_text']) return;
        (Imported[_0x47283d(0x22e)] &&
            Window_BestiaryLore[_0x47283d(0x147)][_0x47283d(0x2b3)] &&
            (_0x5e1998 = '<WordWrap>' + _0x5e1998),
            (this[_0x47283d(0x17e)] = _0x5e1998));
    }),
    (Window_BestiaryLore[_0x249061(0x19a)]['drawAllText'] = function () {
        const _0x2a55a9 = _0x249061,
            _0xa43899 = this[_0x2a55a9(0x17e)];
        (this[_0x2a55a9(0x13d)](), this[_0x2a55a9(0x135)](_0xa43899));
        if (Imported[_0x2a55a9(0x22e)]) this[_0x2a55a9(0x134)]();
        this[_0x2a55a9(0x265)]();
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x135)] = function (_0x42656b) {
        const _0xc620ae = _0x249061;
        this['drawTextEx'](_0x42656b, 0x0, 0x0, this[_0xc620ae(0x311)]);
    }),
    (Window_BestiaryLore['prototype'][_0x249061(0x1a1)] = function () {}),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x2df)] = function () {
        const _0x3511a2 = _0x249061;
        if (!this[_0x3511a2(0x203)]) return;
        if (Input[_0x3511a2(0x2ed)](_0x3511a2(0x3b8))) this[_0x3511a2(0x38b)](!![]);
        else {
            if (Input['isPressed']('up')) this['processSlowScroll'](![]);
            else {
                if (Input[_0x3511a2(0x2ed)](_0x3511a2(0x2b9))) this[_0x3511a2(0x353)](!![]);
                else {
                    if (Input[_0x3511a2(0x2ed)](_0x3511a2(0x39f))) this[_0x3511a2(0x353)](![]);
                    else {
                        if (Input['isTriggered'](_0x3511a2(0x39a))) this[_0x3511a2(0x265)](!![]);
                        else
                            Input[_0x3511a2(0x2f4)](_0x3511a2(0x284)) &&
                                this[_0x3511a2(0x166)](!![]);
                    }
                }
            }
        }
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x38b)] = function (_0x122517) {
        const _0x30cfb6 = _0x249061;
        let _0x495454 = this[_0x30cfb6(0x28a)]['y'];
        this[_0x30cfb6(0x28a)]['y'] +=
            (_0x122517 ? 0x1 : -0x1) * Window_BestiaryLore[_0x30cfb6(0x147)][_0x30cfb6(0x221)];
        let _0x4296bd = Math['max'](0x0, this[_0x30cfb6(0x307)] - this['innerHeight']);
        this[_0x30cfb6(0x28a)]['y'] = this[_0x30cfb6(0x28a)]['y']['clamp'](0x0, _0x4296bd);
        if (
            _0x495454 !== this[_0x30cfb6(0x28a)]['y'] &&
            Graphics[_0x30cfb6(0x1c9)] %
                Window_BestiaryLore[_0x30cfb6(0x147)]['slowSoundFrequency'] ===
                0x0
        )
            this[_0x30cfb6(0x217)]();
    }),
    (Window_BestiaryLore['prototype'][_0x249061(0x353)] = function (_0x2b3fa3) {
        const _0x31b83d = _0x249061;
        let _0xdb236a = this['origin']['y'];
        this[_0x31b83d(0x28a)]['y'] +=
            (_0x2b3fa3 ? 0x1 : -0x1) * Window_BestiaryLore['SETTINGS'][_0x31b83d(0x17c)];
        let _0x46945a = Math[_0x31b83d(0x2b7)](0x0, this[_0x31b83d(0x307)] - this['innerHeight']);
        this['origin']['y'] = this[_0x31b83d(0x28a)]['y'][_0x31b83d(0x126)](0x0, _0x46945a);
        if (
            _0xdb236a !== this[_0x31b83d(0x28a)]['y'] &&
            Graphics[_0x31b83d(0x1c9)] % Window_BestiaryLore['SETTINGS'][_0x31b83d(0x13f)] === 0x0
        )
            this[_0x31b83d(0x217)]();
    }),
    (Window_BestiaryLore['prototype'][_0x249061(0x265)] = function (_0x5cf0b7) {
        const _0x1a4bab = _0x249061;
        let _0x1a478f = this[_0x1a4bab(0x28a)]['y'];
        this[_0x1a4bab(0x28a)]['y'] = 0x0;
        if (_0x5cf0b7 && _0x1a478f !== this['origin']['y']) this[_0x1a4bab(0x217)]();
    }),
    (Window_BestiaryLore[_0x249061(0x19a)]['scrollToBottom'] = function (_0xdfcff7) {
        const _0x2ffaa5 = _0x249061;
        let _0x2bf2f0 = this['origin']['y'],
            _0x4ae354 = Math['max'](0x0, this[_0x2ffaa5(0x307)] - this[_0x2ffaa5(0x1a2)]);
        this[_0x2ffaa5(0x28a)]['y'] = _0x4ae354;
        if (_0xdfcff7 && _0x2bf2f0 !== this[_0x2ffaa5(0x28a)]['y']) this[_0x2ffaa5(0x217)]();
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x1c5)] = function () {
        const _0x410028 = _0x249061;
        ((this[_0x410028(0x359)] =
            this[_0x410028(0x28a)]['y'] < this[_0x410028(0x307)] - this[_0x410028(0x1a2)]),
            (this['upArrowVisible'] = this[_0x410028(0x28a)]['y'] > 0x0));
    }),
    (Window_BestiaryLore[_0x249061(0x19a)]['smoothScrollBy'] = function (_0x4227f7, _0x1227f4) {
        const _0x51fb2d = _0x249061;
        this[_0x51fb2d(0x28a)]['y'] += _0x1227f4;
        let _0x5cc413 = Math[_0x51fb2d(0x2b7)](
            0x0,
            this[_0x51fb2d(0x307)] - this[_0x51fb2d(0x1a2)]
        );
        this[_0x51fb2d(0x28a)]['y'] = this[_0x51fb2d(0x28a)]['y']['clamp'](0x0, _0x5cc413);
    }),
    (Window_BestiaryLore[_0x249061(0x19a)][_0x249061(0x27a)] = function (_0x21898a, _0xbb4499) {
        const _0xf38ffd = _0x249061;
        this['origin']['y'] += _0xbb4499;
        let _0x375677 = Math['max'](0x0, this[_0xf38ffd(0x307)] - this[_0xf38ffd(0x1a2)]);
        this[_0xf38ffd(0x28a)]['y'] = this[_0xf38ffd(0x28a)]['y']['clamp'](0x0, _0x375677);
    }));
