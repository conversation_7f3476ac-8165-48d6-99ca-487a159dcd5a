/*:
 * @target MZ
 * @plugindesc v1.0.0 Cross-Plugin Battle Coordinator
 * <AUTHOR> Optimizer
 * @orderBefore ComboSystem
 * @orderBefore StaggerGauge2
 * @orderBefore MagicDamageReduction
 * @orderBefore DisplayTotalDamage2
 * @orderBefore BattleOptimizer
 * @help BattleCoordinator.js
 *
 * ============================================================================
 * Cross-Plugin Battle Coordinator
 * ============================================================================
 *
 * This plugin coordinates all battle-related plugins to eliminate redundancies
 * and optimize performance. It provides:
 *
 * - Unified damage calculation pipeline
 * - Shared sprite pools and resource management
 * - Coordinated update scheduling
 * - Performance-aware throttling
 * - Centralized event system
 *
 * ============================================================================
 * Coordinated Plugins:
 * ============================================================================
 *
 * - ComboSystem.js
 * - StaggerGauge2.js  
 * - MagicDamageReduction.js
 * - DisplayTotalDamage2.js
 * - BattleOptimizer.js
 *
 * @param enableDebugMode
 * @text Enable Debug Mode
 * @desc Show coordination statistics in console
 * @type boolean
 * @default false
 *
 * @param performanceThreshold
 * @text Performance Threshold (FPS)
 * @desc Below this FPS, enable aggressive optimizations
 * @type number
 * @min 30
 * @max 60
 * @default 45
 *
 * @param updateThrottleLevel
 * @text Update Throttle Level
 * @desc Higher = more aggressive throttling (1-5)
 * @type number
 * @min 1
 * @max 5
 * @default 2
 */

(() => {
    'use strict';

    // Plugin parameters
    const pluginName = 'BattleCoordinator';
    const parameters = PluginManager.parameters(pluginName);
    const enableDebugMode = parameters['enableDebugMode'] === 'true';
    const performanceThreshold = parseInt(parameters['performanceThreshold']) || 45;
    const updateThrottleLevel = parseInt(parameters['updateThrottleLevel']) || 2;

    // Debug logging
    const debugLog = (message) => {
        if (enableDebugMode) {
            console.log(`[BattleCoordinator] ${message}`);
        }
    };

    //=============================================================================
    // Battle Coordinator Core System
    //=============================================================================

    class BattleCoordinator {
        constructor() {
            this.initialize();
        }

        initialize() {
            // Performance monitoring
            this.frameCounter = 0;
            this.currentFPS = 60;
            this.lastFPSCheck = Date.now();
            this.performanceMode = 'normal'; // normal, throttled, aggressive

            // Event system
            this.eventListeners = new Map();
            this.eventQueue = [];

            // Shared resources
            this.sharedSpritePool = new Map();
            this.damageCalculationCache = new Map();
            this.updateScheduler = new UpdateScheduler();

            // Plugin coordination flags
            this.pluginStates = {
                comboSystem: { active: false, manager: null, display: null },
                staggerGauge: { active: false, cascadeMeter: null },
                magicDamageReduction: { active: false },
                displayTotalDamage: { active: false, sprite: null },
                battleOptimizer: { active: false }
            };

            // Damage pipeline data
            this.currentDamageContext = null;

            debugLog('Battle Coordinator initialized');
        }

        // Event system for plugin coordination
        addEventListener(eventType, callback, priority = 0) {
            if (!this.eventListeners.has(eventType)) {
                this.eventListeners.set(eventType, []);
            }
            
            this.eventListeners.get(eventType).push({ callback, priority });
            this.eventListeners.get(eventType).sort((a, b) => b.priority - a.priority);
        }

        dispatchEvent(eventType, data) {
            if (!this.eventListeners.has(eventType)) return data;

            const listeners = this.eventListeners.get(eventType);
            let result = data;

            for (const listener of listeners) {
                try {
                    const newResult = listener.callback(result);
                    if (newResult !== undefined) {
                        result = newResult;
                    }
                } catch (error) {
                    debugLog(`Error in event listener for ${eventType}: ${error.message}`);
                }
            }

            return result;
        }

        // Performance monitoring and adaptive throttling
        updatePerformanceMetrics() {
            this.frameCounter++;
            
            if (this.frameCounter % 60 === 0) { // Check every second
                const now = Date.now();
                const timeDiff = now - this.lastFPSCheck;
                this.currentFPS = Math.round(60000 / timeDiff);
                this.lastFPSCheck = now;

                // Adjust performance mode based on FPS
                const oldMode = this.performanceMode;
                if (this.currentFPS < performanceThreshold - 10) {
                    this.performanceMode = 'aggressive';
                } else if (this.currentFPS < performanceThreshold) {
                    this.performanceMode = 'throttled';
                } else {
                    this.performanceMode = 'normal';
                }

                if (oldMode !== this.performanceMode) {
                    debugLog(`Performance mode changed: ${oldMode} → ${this.performanceMode} (FPS: ${this.currentFPS})`);
                    this.dispatchEvent('performanceModeChanged', {
                        oldMode,
                        newMode: this.performanceMode,
                        currentFPS: this.currentFPS
                    });
                }
            }
        }

        // Unified damage calculation pipeline
        processDamageCalculation(action, target, originalValue, critical) {
            // Create damage context for coordination
            this.currentDamageContext = {
                action,
                target,
                originalValue,
                critical,
                finalValue: originalValue,
                modifications: [],
                timestamp: Date.now()
            };

            // Dispatch to coordinated plugins in optimal order
            const result = this.dispatchEvent('damageCalculation', this.currentDamageContext);
            
            return result.finalValue;
        }

        // Unified action application pipeline  
        processActionApplication(action, target) {
            const context = {
                action,
                target,
                result: target.result(),
                damageDealt: target.result().hpDamage,
                timestamp: Date.now()
            };

            this.dispatchEvent('actionApplication', context);
        }

        // Shared sprite pool management
        getSpriteFromPool(type, createFunction) {
            if (!this.sharedSpritePool.has(type)) {
                this.sharedSpritePool.set(type, []);
            }

            const pool = this.sharedSpritePool.get(type);

            if (pool.length > 0) {
                const sprite = pool.pop();
                sprite._pooled = false;
                return sprite;
            } else {
                const sprite = createFunction();
                sprite._poolType = type;
                sprite._pooled = false;
                return sprite;
            }
        }

        returnSpriteToPool(type, sprite) {
            if (!this.sharedSpritePool.has(type)) {
                this.sharedSpritePool.set(type, []);
            }

            if (sprite._pooled) return; // Already pooled

            // Reset sprite properties
            sprite.visible = false;
            sprite.opacity = 255;
            sprite.scale.set(1, 1);
            sprite.rotation = 0;
            sprite.x = 0;
            sprite.y = 0;
            sprite.anchor.set(0, 0);
            sprite._pooled = true;

            const pool = this.sharedSpritePool.get(type);

            // Limit pool size to prevent memory bloat
            if (pool.length < 20) {
                pool.push(sprite);
            }
        }

        // Damage calculation caching
        getCachedDamageCalculation(cacheKey) {
            return this.damageCalculationCache.get(cacheKey);
        }

        setCachedDamageCalculation(cacheKey, result, ttl = 1000) {
            this.damageCalculationCache.set(cacheKey, {
                result,
                timestamp: Date.now(),
                ttl
            });

            // Clean old cache entries
            if (this.damageCalculationCache.size > 100) {
                this.cleanDamageCache();
            }
        }

        cleanDamageCache() {
            const now = Date.now();
            for (const [key, entry] of this.damageCalculationCache.entries()) {
                if (now - entry.timestamp > entry.ttl) {
                    this.damageCalculationCache.delete(key);
                }
            }
        }

        // Resource cleanup
        cleanup() {
            // Clear all sprite pools
            for (const [type, pool] of this.sharedSpritePool.entries()) {
                for (const sprite of pool) {
                    if (sprite.parent) {
                        sprite.parent.removeChild(sprite);
                    }
                }
                pool.length = 0;
            }

            // Clear caches
            this.damageCalculationCache.clear();

            // Clear event listeners
            this.eventListeners.clear();

            debugLog('Resources cleaned up');
        }

        // Plugin registration system
        registerPlugin(pluginName, pluginData) {
            if (this.pluginStates[pluginName]) {
                this.pluginStates[pluginName] = { ...this.pluginStates[pluginName], ...pluginData };
                this.pluginStates[pluginName].active = true;
                debugLog(`Registered plugin: ${pluginName}`);
            }
        }

        // Coordinated update system
        coordinatedUpdate() {
            this.updatePerformanceMetrics();

            // Adapt scheduler based on performance
            this.updateScheduler.adaptThrottling(this.performanceMode);

            // Process scheduled updates with performance monitoring
            this.updateScheduler.processScheduledUpdates();

            // Clean caches periodically
            if (this.frameCounter % 300 === 0) { // Every 5 seconds
                this.cleanDamageCache();
            }

            // Dispatch coordinated update event
            const updateData = {
                frameCounter: this.frameCounter,
                performanceMode: this.performanceMode,
                currentFPS: this.currentFPS,
                schedulerStats: this.updateScheduler.getPerformanceStats()
            };

            this.dispatchEvent('coordinatedUpdate', updateData);

            // Performance reporting
            if (enableDebugMode && this.frameCounter % 600 === 0) { // Every 10 seconds
                this.reportPerformanceStats();
            }
        }

        // Performance reporting
        reportPerformanceStats() {
            const stats = this.updateScheduler.getPerformanceStats();
            const poolStats = Array.from(this.sharedSpritePool.entries()).map(([type, pool]) =>
                `${type}: ${pool.length}`
            ).join(', ');

            debugLog(`Performance Stats:
                FPS: ${this.currentFPS}
                Mode: ${this.performanceMode}
                Scheduled Updates: ${stats.totalUpdates}
                Frame Time: ${stats.averageFrameTime.toFixed(2)}ms
                Budget Usage: ${stats.budgetUsage.toFixed(1)}%
                Sprite Pools: {${poolStats}}
                Cache Size: ${this.damageCalculationCache.size}`);
        }
    }

    //=============================================================================
    // Update Scheduler for Performance Optimization
    //=============================================================================

    class UpdateScheduler {
        constructor() {
            this.scheduledUpdates = [];
            this.frameCounter = 0;
            this.performanceBudget = 16; // 16ms budget per frame (60 FPS)
            this.currentFrameTime = 0;
        }

        scheduleUpdate(callback, priority = 0, throttle = 1, maxExecutionTime = 5) {
            this.scheduledUpdates.push({
                callback,
                priority,
                throttle,
                maxExecutionTime,
                lastExecuted: 0,
                averageExecutionTime: 0,
                executionCount: 0
            });

            // Sort by priority
            this.scheduledUpdates.sort((a, b) => b.priority - a.priority);
        }

        processScheduledUpdates() {
            this.frameCounter++;
            this.currentFrameTime = 0;
            const frameStartTime = performance.now();

            for (const update of this.scheduledUpdates) {
                // Check if we should execute this update
                if (this.frameCounter - update.lastExecuted < update.throttle) {
                    continue;
                }

                // Check performance budget
                if (this.currentFrameTime > this.performanceBudget * 0.8) {
                    break; // Stop processing to maintain frame rate
                }

                const updateStartTime = performance.now();

                try {
                    update.callback();
                    update.lastExecuted = this.frameCounter;

                    // Track execution time for future optimization
                    const executionTime = performance.now() - updateStartTime;
                    update.executionCount++;
                    update.averageExecutionTime =
                        (update.averageExecutionTime * (update.executionCount - 1) + executionTime) / update.executionCount;

                    this.currentFrameTime += executionTime;

                } catch (error) {
                    debugLog(`Error in scheduled update: ${error.message}`);
                }
            }

            // Clean up completed one-time updates
            this.scheduledUpdates = this.scheduledUpdates.filter(update =>
                update.throttle > 0 || this.frameCounter - update.lastExecuted < 60
            );
        }

        // Adaptive throttling based on performance
        adaptThrottling(performanceMode) {
            for (const update of this.scheduledUpdates) {
                if (performanceMode === 'aggressive') {
                    update.throttle = Math.max(update.throttle, 3);
                } else if (performanceMode === 'throttled') {
                    update.throttle = Math.max(update.throttle, 2);
                } else {
                    // Restore original throttling
                    update.throttle = Math.max(update.throttle, 1);
                }
            }
        }

        // Get performance statistics
        getPerformanceStats() {
            return {
                totalUpdates: this.scheduledUpdates.length,
                averageFrameTime: this.currentFrameTime,
                budgetUsage: (this.currentFrameTime / this.performanceBudget) * 100
            };
        }
    }

    //=============================================================================
    // Core Game Engine Integration
    //=============================================================================

    // Hook into Game_Action damage calculation pipeline
    const _Game_Action_makeDamageValue = Game_Action.prototype.makeDamageValue;
    Game_Action.prototype.makeDamageValue = function(target, critical) {
        const originalValue = _Game_Action_makeDamageValue.call(this, target, critical);

        // Use coordinator for unified damage processing
        return window.BattleCoordinator.processDamageCalculation(this, target, originalValue, critical);
    };

    // Hook into Game_Action application pipeline
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function(target) {
        _Game_Action_apply.call(this, target);

        // Use coordinator for unified action processing
        window.BattleCoordinator.processActionApplication(this, target);
    };

    // Hook into Scene_Battle for coordinated updates
    const _Scene_Battle_update = Scene_Battle.prototype.update;
    Scene_Battle.prototype.update = function() {
        // Coordinated update before individual plugin updates
        window.BattleCoordinator.coordinatedUpdate();

        _Scene_Battle_update.call(this);
    };

    // Hook into Scene_Battle creation for plugin registration
    const _Scene_Battle_create = Scene_Battle.prototype.create;
    Scene_Battle.prototype.create = function() {
        _Scene_Battle_create.call(this);

        // Initialize coordinator for this battle
        window.BattleCoordinator.initialize();

        // Store reference for plugins to access
        this.battleCoordinator = window.BattleCoordinator;

        // Dispatch battle start event
        window.BattleCoordinator.dispatchEvent(BATTLE_EVENTS.BATTLE_START, {
            scene: this,
            troop: $gameTroop,
            party: $gameParty
        });
    };

    // Hook into Scene_Battle termination for cleanup
    const _Scene_Battle_terminate = Scene_Battle.prototype.terminate;
    Scene_Battle.prototype.terminate = function() {
        // Dispatch battle end event
        if (window.BattleCoordinator) {
            window.BattleCoordinator.dispatchEvent(BATTLE_EVENTS.BATTLE_END, {
                scene: this,
                result: BattleManager._battleResult
            });

            // Cleanup coordinator resources
            window.BattleCoordinator.cleanup();
        }

        _Scene_Battle_terminate.call(this);
    };

    // Hook into Graphics.render for performance monitoring
    const _Graphics_render = Graphics.render;
    Graphics.render = function() {
        _Graphics_render.call(this);

        // Update coordinator performance metrics
        if (window.BattleCoordinator) {
            window.BattleCoordinator.updatePerformanceMetrics();
        }
    };

    //=============================================================================
    // Plugin Coordination Event Definitions
    //=============================================================================

    // Define standard events that plugins can subscribe to
    const BATTLE_EVENTS = {
        DAMAGE_CALCULATION: 'damageCalculation',
        ACTION_APPLICATION: 'actionApplication',
        COORDINATED_UPDATE: 'coordinatedUpdate',
        PERFORMANCE_MODE_CHANGED: 'performanceModeChanged',
        SPRITE_POOL_REQUEST: 'spritePoolRequest',
        BATTLE_START: 'battleStart',
        BATTLE_END: 'battleEnd'
    };

    // Export events for plugin use
    window.BATTLE_EVENTS = BATTLE_EVENTS;

    //=============================================================================
    // Global Battle Coordinator Instance
    //=============================================================================

    window.BattleCoordinator = new BattleCoordinator();

    debugLog('Battle Coordinator system loaded and ready');
})();
