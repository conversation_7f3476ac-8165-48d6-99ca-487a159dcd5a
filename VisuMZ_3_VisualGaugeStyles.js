//=============================================================================
// VisuStella MZ - Visual Gauge Styles
// VisuMZ_3_VisualGaugeStyles.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_VisualGaugeStyles = true;

var VisuMZ = VisuMZ || {};
VisuMZ.VisualGaugeStyles = VisuMZ.VisualGaugeStyles || {};
VisuMZ.VisualGaugeStyles.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.00] [VisualGaugeStyles]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Visual_Gauge_Styles_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_BattleCore
 * @base VisuMZ_1_SkillsStatesCore
 * @orderAfter VisuMZ_1_BattleCore
 * @orderAfter VisuMZ_1_SkillsStatesCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The Visual Gauge Styles plugin allows you to swap out the various gauges
 * found and used in the game to don a different appearance and aesthetic. The
 * aesthetics can be mixed and matched to your liking, going from more visual
 * polygon structure-like styles to enhance a feeling to more mechanical-like
 * styles to relay information better. As these styles are all pre-rendered,
 * there are no custom files used with this plugin.
 *
 * Features include all (but not limited to) the following:
 *
 * * No custom image files are needed for this plugin to utilize the various
 *   pre-rendered visual gauge styles.
 * * Mix and match from over 20+ choices to pick from for different types of
 *   gauges found in the game and from other VisuStella MZ plugins.
 * * Styles can have varying gauge heights, label offsets, and value offsets to
 *   add to the aesthetic differences.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_0_CoreEngine
 * * VisuMZ_1_BattleCore
 * * VisuMZ_1_SkillsStatesCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Sprite_Gauge Overwrite
 *
 * Naturally, since the visual gauge styles are altered, certain aspects have
 * to be overwritten as a whole. For the Sprite_Gauge class, this means the
 * functions related to drawing the gauges themselves are overwritten.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_1_BattleCore
 *
 * VisuMZ_2_AggroControlSys
 *
 * VisuMZ_2_BattleSystemATB
 *
 * VisuMZ_3_VictoryAftermath
 *
 * These plugins from the VisuStella MZ library contain sprite gauges used that
 * can be altered and have a different style from the rest. Mix and match them
 * to your liking.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_4_VariableGauges
 *
 * The updated version of VisuStella MZ's Variable Gauges can now utilize the
 * styles from this plugin. However, keep in mind that style settings like
 * adjusting gauge thickness will not be handled by the Visual Gauge Styles
 * plugin, but instead, handled by the Variable Gauges plugin.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Style Adjustment Settings
 * ============================================================================
 *
 * Adjust settings like label and value offsets for each style type.
 *
 * ---
 *
 * Structure-Styles
 *
 *   Normal:
 *   Arrow:
 *   Dipper:
 *   Flag:
 *   Growth:
 *   Lean:
 *   Quad:
 *   Stagger:
 *   Trapezoid:
 *   - Adjustment settings like gauge thickness, labels, values offsets values
 *     when this specific style is used.
 *
 * ---
 *
 * Step-Styles
 *
 *   Half Step:
 *   Third Step:
 *   Fourth Step:
 *   Fifth Step:
 *   Sixth Step:
 *   Eighth Step:
 *   Tenth Step:
 *   - Adjustment settings like gauge thickness, labels, values offsets values
 *     when this specific style is used.
 *
 * ---
 *
 * Section-Styles
 *
 *   Half Section:
 *   Third Section:
 *   Fourth Section:
 *   Fifth Section:
 *   Sixth Section:
 *   Eighth Section:
 *   Tenth Section:
 *   - Adjustment settings like gauge thickness, labels, values offsets values
 *     when this specific style is used.
 *   - These gauges will be separated in even sections based on their numeric
 *     value used for their style name.
 *
 * ---
 *
 * Segment-Styles
 *
 *   Segment By 10:
 *   Segment By 20:
 *   Segment By 25:
 *   Segment By 50:
 *   Segment By 100:
 *   Segment By 200:
 *   Segment By 250:
 *   Segment By 500:
 *   Segment By 1000:
 *   - Adjustment settings like gauge thickness, labels, values offsets values
 *     when this specific style is used.
 *   - These gauges will be separated in divided chunks based on the maximum
 *     value used to calculate the gauge. Their divison count is based on the
 *     numeric value used for their style name.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * Here, you can adjust the default settings for the various gauges used in the
 * game. If there are any future plugins that will utilize custom gauges, they
 * will be added here at a later date.
 *
 * ---
 *
 * Default
 *
 *   Default Horizontal Style:
 *   Default Vertical Style:
 *   - Select the gauge style to use for default horizontal/vertical gauges.
 *   - When 'Default' style is selected in the "Status Window" or "Battlers"
 *     Plugin Parameters, the style will then refer to the "Horizontal" or
 *     "Vertical" gauge styles set here.
 *
 * ---
 *
 * Status Window
 *
 *   Status: HP Style:
 *   Status: MP Style:
 *   Status: TP Style:
 *   Status: Time Style:
 *   Status: Aggro Style:
 *   - Select the gauge style to use for the status-related gauge.
 *
 * ---
 *
 * Battlers
 *
 *   Battler: HP Style:
 *   Battler: Aggro Style:
 *   Battler: ATB Style:
 *   Battler: EXP Style:
 *   - Select the gauge style to use for the battler-related gauges.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Irina
 * * Arisu
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: April 5, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param VisualGaugeStyles
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Styles:struct
 * @text Style Adjustment Settings
 * @type struct<Styles>
 * @desc Adjust settings like label and value offsets for each style type.
 * @default {"Structure":"","normal:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+0\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"+0\",\"valueOffsetY:num\":\"+0\"}","arrow:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-8\",\"valueOffsetY:num\":\"+0\"}","dipper:struct":"{\"gaugeThickness:num\":\"20\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","flag:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+0\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-8\",\"valueOffsetY:num\":\"+0\"}","growth:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","lean:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-8\",\"valueOffsetY:num\":\"+0\"}","quad:struct":"{\"gaugeThickness:num\":\"20\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","stagger:struct":"{\"gaugeThickness:num\":\"14\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-8\",\"valueOffsetY:num\":\"+0\"}","trapezoid:struct":"{\"gaugeThickness:num\":\"16\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","Steps":"","halfstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","thirdstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","fourthstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","fifthstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","sixthstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","eighthstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","tenthstep:struct":"{\"gaugeThickness:num\":\"24\",\"Label\":\"\",\"labelOffsetX:num\":\"+8\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-12\",\"valueOffsetY:num\":\"+0\"}","Section":"","halfsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","thirdsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","fourthsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","fifthsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","sixthsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","eighthsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","tenthsection:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","Segment":"","segmentby10:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby20:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby25:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby50:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby100:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby200:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby250:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby500:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}","segmentby1000:struct":"{\"gaugeThickness:num\":\"12\",\"Label\":\"\",\"labelOffsetX:num\":\"+4\",\"labelOffsetY:num\":\"+0\",\"Value\":\"\",\"valueOffsetX:num\":\"-4\",\"valueOffsetY:num\":\"+0\"}"}
 *
 * @param DefaultStyles
 * @text Default
 * @parent Styles:struct
 *
 * @param horzStyle:str
 * @text Default Horizontal Style
 * @parent DefaultStyles
 * @type select
 * @option -
 * @option Normal
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for default horizontal gauges.
 * @default Lean
 *
 * @param vertStyle:str
 * @text Default Vertical Style
 * @parent DefaultStyles
 * @type select
 * @option Normal
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for default vertical gauges.
 * @default Arrow
 *
 * @param StatusStyles
 * @text Status Window
 * @parent Styles:struct
 *
 * @param statusHpStyle:str
 * @text Status: HP Style
 * @parent StatusStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the status window HP.
 * @default Stagger
 *
 * @param statusMpStyle:str
 * @text Status: MP Style
 * @parent StatusStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the status window MP.
 * @default Stagger
 *
 * @param statusTpStyle:str
 * @text Status: TP Style
 * @parent StatusStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the status window TP.
 * @default Stagger
 *
 * @param statusTimeStyle:str
 * @text Status: Time Style
 * @parent StatusStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the status window time
 * gauge. Used for TPB and VisuMZ_2_BattleSystemATB.
 * @default Lean
 *
 * @param statusAggroStyle:str
 * @text Status: Aggro Style
 * @parent StatusStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the status aggro gauge.
 * Requires VisuMZ_2_AggroControlSys!
 * @default Lean
 *
 * @param BattlerStyles
 * @text Battlers
 * @parent Styles:struct
 *
 * @param battlerHpStyle:str
 * @text Battler: HP Style
 * @parent BattlerStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the battler HP gauges.
 * @default Lean
 *
 * @param battlerAggroStyle:str
 * @text Battler: Aggro Style
 * @parent BattlerStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the battler aggro gauge.
 * Requires VisuMZ_2_AggroControlSys!
 * @default Lean
 *
 * @param battlerAtbStyle:str
 * @text Battler: ATB Style
 * @parent BattlerStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the battler ATB gauges.
 * Requires VisuMZ_2_BattleSystemATB!
 * @default Lean
 *
 * @param battlerEXPStyle:str
 * @text Battler: EXP Style
 * @parent BattlerStyles
 * @type select
 * @option Normal
 * @option Default
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the battler EXP gauges.
 * Requires VisuMZ_3_VictoryAftermath!
 * @default Arrow
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Specific Style Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Styles:
 *
 * @param Structure
 * @text Structure-Styles
 *
 * @param normal:struct
 * @text Normal
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+0","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"+0","valueOffsetY:num":"+0"}
 *
 * @param arrow:struct
 * @text Arrow
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-8","valueOffsetY:num":"+0"}
 *
 * @param dipper:struct
 * @text Dipper
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"20","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param flag:struct
 * @text Flag
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+0","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-8","valueOffsetY:num":"+0"}
 *
 * @param growth:struct
 * @text Growth
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param lean:struct
 * @text Lean
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-8","valueOffsetY:num":"+0"}
 *
 * @param quad:struct
 * @text Quad
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"20","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param stagger:struct
 * @text Stagger
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"14","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-8","valueOffsetY:num":"+0"}
 *
 * @param trapezoid:struct
 * @text Trapezoid
 * @parent Structure
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"16","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param Steps
 * @text Step-Styles
 *
 * @param halfstep:struct
 * @text Half Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param thirdstep:struct
 * @text Third Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param fourthstep:struct
 * @text Fourth Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param fifthstep:struct
 * @text Fifth Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param sixthstep:struct
 * @text Sixth Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param eighthstep:struct
 * @text Eighth Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param tenthstep:struct
 * @text Tenth Step
 * @parent Steps
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"24","Label":"","labelOffsetX:num":"+8","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-12","valueOffsetY:num":"+0"}
 *
 * @param Section
 * @text Section-Styles
 *
 * @param halfsection:struct
 * @text Half Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param thirdsection:struct
 * @text Third Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param fourthsection:struct
 * @text Fourth Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param fifthsection:struct
 * @text Fifth Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param sixthsection:struct
 * @text Sixth Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param eighthsection:struct
 * @text Eighth Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param tenthsection:struct
 * @text Tenth Section
 * @parent Section
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param Segment
 * @text Segment-Styles
 *
 * @param segmentby10:struct
 * @text Segment By 10
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby20:struct
 * @text Segment By 20
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby25:struct
 * @text Segment By 25
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby50:struct
 * @text Segment By 50
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby100:struct
 * @text Segment By 100
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby200:struct
 * @text Segment By 200
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby250:struct
 * @text Segment By 250
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby500:struct
 * @text Segment By 500
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 * @param segmentby1000:struct
 * @text Segment By 1000
 * @parent Segment
 * @type struct<OffsetData>
 * @desc Adjustment settings like gauge thickness, labels, values
 * offsets values when this specific style is used.
 * @default {"gaugeThickness:num":"12","Label":"","labelOffsetX:num":"+4","labelOffsetY:num":"+0","Value":"","valueOffsetX:num":"-4","valueOffsetY:num":"+0"}
 *
 */
/* ----------------------------------------------------------------------------
 * Offset Data Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~OffsetData:
 *
 * @param gaugeThickness:num
 * @text Gauge Thickness
 * @type number
 * @min 1
 * @desc What is the gauge height/width when this style is used?
 * Horz Style: Adjusts height. Vert Style: Adjusts width.
 * @default 12
 *
 * @param Label
 * @text Label Offsets
 *
 * @param labelOffsetX:num
 * @text Offset X
 * @parent Label
 * @desc How many pixels to offset the label text?
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param labelOffsetY:num
 * @text Offset Y
 * @parent Label
 * @desc How many pixels to offset the label text?
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param Value
 * @text Value Offsets
 *
 * @param valueOffsetX:num
 * @text Offset X
 * @parent Value
 * @desc How many pixels to offset the value text?
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param valueOffsetY:num
 * @text Offset Y
 * @parent Value
 * @desc How many pixels to offset the value text?
 * Negative: up. Positive: down.
 * @default +0
 *
 */
//=============================================================================

function _0x5bf3() {
    const _0x11d6c2 = [
        'level',
        'description',
        'floor',
        'GetFlagPolygon',
        'Settings',
        'exit',
        'VisuMZ_2_BattleSystemATB',
        'SkillsStatesCore',
        'segmentby50',
        'ceil',
        'GetMultiStepPolygon',
        'vertStyle',
        'statusMpStyle',
        'strokeStyle',
        'VisuMZ_2_AggroControlSystem',
        '_tpGaugeBack',
        '_context',
        'gaugeBackColor',
        'VisualGaugeStyles',
        'moveTo',
        'drawVisualStyleGaugeFront',
        'GetArrowPolygon',
        'drawGaugeRectEnhancedTp',
        '163581BLBCXF',
        'segmentby100',
        '_battler',
        '2949720eCcRRs',
        'STR',
        'version',
        'statusAggroStyle',
        '_aggroGaugeSprite',
        '2072adufbf',
        'setupValueFont',
        'drawGauge',
        'ShowActorLevel',
        'GetStaggerPolygon',
        'GetMultiSegmentPolygon',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'statusTimeStyle',
        'bitmap',
        'segmentby10',
        'bitmapHeight',
        'Sprite_Gauge_redraw',
        'GetQuadPolygon',
        'fifthstep',
        'beginPath',
        'fourthstep',
        'segmentby500',
        'HorzStyle',
        'dipper',
        'number',
        'fifthsection',
        'drawGaugeRect',
        'getStyleName',
        '850IbdqQP',
        'parse',
        'stroke',
        'STRUCT',
        'VisuMZ_1_BattleCore',
        'currentMaxValue',
        'segmentby25',
        'halfstep',
        'call',
        'toLowerCase',
        'redraw',
        'format',
        'isBattlerAggroGauge',
        'GetGaugeHeight',
        '421180UKEblw',
        'drawValue',
        'ARRAYJSON',
        'contents',
        'tenthstep',
        '_visualGaugeStyleOffset',
        'fillStyle',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'stagger',
        'gaugeColor1',
        'GetGrowthPolygon',
        'clamp',
        '8eiNgLz',
        'restore',
        'slant',
        'isExpGaugeDrawn',
        'toUpperCase',
        'drawVisualStyleGauge',
        'Sprite_Gauge_gaugeHeight',
        'parameters',
        'segmentby200',
        'segmentby20',
        'drawActorExpGauge',
        'Sprite_Gauge_drawValue',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'arrow',
        'JSON',
        'name',
        'GetDefaultPolygon',
        'setupLabelFont',
        '22678nyTEkz',
        'VisuMZ_2_EnhancedTpSystem',
        'lineWidth',
        'drawFullGaugeEnhancedTp',
        'drawText',
        '1692nKEPtW',
        'save',
        'trim',
        'gaugeRate',
        'trapezoid',
        'drawLabel',
        'map',
        'Gauge',
        'drawVisualStyleGaugeBack',
        'drawVisualStyleGaugeRect',
        'push',
        'GetStyleData',
        'Sprite_Gauge_drawLabel',
        '1552aTgKHc',
        'GetPolygonStyle',
        'eighthstep',
        'GetLeanPolygon',
        'match',
        'isBattlerAtbGauge',
        'lineTo',
        'GetDipperPolygon',
        'Sprite_Gauge_setupLabelFont',
        'expGaugeColor2',
        'update',
        'valueOffsetX',
        'valueOffsetY',
        '_tpGaugeSprite',
        'gaugeHeight',
        'gaugeColor2',
        'Param',
        'GetTrapezoidPolygon',
        'sixthstep',
        'resetFontSettings',
        'SetValueOffset',
        '_maxValueSegment',
        'normal',
        '_statusType',
        'battlerAtbStyle',
        'horzStyle',
        'flag',
        'createTpGaugeBitmaps',
        'tenthsection',
        'Bitmap_drawText',
        'lineHeight',
        'VertStyle',
        'fontSize',
        'thirdsection',
        'return\x200',
        'numberFontFace',
        'ARRAYSTRUCT',
        'globalAlpha',
        'NUM',
        'time',
        'addColorStop',
        'max',
        'Sprite_Gauge_setupValueFont',
        '1324989SuvSPM',
        'battler',
        'GetMultiSectionPolygon',
        '_baseTexture',
        'FUNC',
        'changeTpCustomColor',
        '_atbGaugeSprite',
        'lean',
        'filter',
        'fontFace',
        'SetLabelOffset',
        'styleName',
        'prototype',
        'length',
        'segmentby1000',
        'ClearTextOffset',
        'default',
        'Styles',
        'right',
        'ConvertParams',
    ];
    _0x5bf3 = function () {
        return _0x11d6c2;
    };
    return _0x5bf3();
}
const _0x13ce85 = _0x2bad;
function _0x2bad(_0x128f8d, _0x484877) {
    const _0x5bf390 = _0x5bf3();
    return (
        (_0x2bad = function (_0x2bad33, _0x1573d3) {
            _0x2bad33 = _0x2bad33 - 0x135;
            let _0x32a3fa = _0x5bf390[_0x2bad33];
            return _0x32a3fa;
        }),
        _0x2bad(_0x128f8d, _0x484877)
    );
}
(function (_0x34e288, _0x3bee16) {
    const _0x53f11a = _0x2bad,
        _0xb7f388 = _0x34e288();
    while (!![]) {
        try {
            const _0x269256 =
                -parseInt(_0x53f11a(0x1c5)) / 0x1 +
                -parseInt(_0x53f11a(0x1a7)) / 0x2 +
                (-parseInt(_0x53f11a(0x17a)) / 0x3) * (-parseInt(_0x53f11a(0x1b3)) / 0x4) +
                (-parseInt(_0x53f11a(0x199)) / 0x5) * (-parseInt(_0x53f11a(0x1ca)) / 0x6) +
                (parseInt(_0x53f11a(0x182)) / 0x7) * (parseInt(_0x53f11a(0x1d7)) / 0x8) +
                -parseInt(_0x53f11a(0x14f)) / 0x9 +
                parseInt(_0x53f11a(0x17d)) / 0xa;
            if (_0x269256 === _0x3bee16) break;
            else _0xb7f388['push'](_0xb7f388['shift']());
        } catch (_0x22900d) {
            _0xb7f388['push'](_0xb7f388['shift']());
        }
    }
})(_0x5bf3, 0x1f785);
var label = _0x13ce85(0x175),
    tier = tier || 0x0,
    dependencies = ['VisuMZ_0_CoreEngine', _0x13ce85(0x19d), 'VisuMZ_1_SkillsStatesCore'],
    pluginData = $plugins[_0x13ce85(0x157)](function (_0x410263) {
        const _0x20be8e = _0x13ce85;
        return _0x410263['status'] && _0x410263[_0x20be8e(0x164)]['includes']('[' + label + ']');
    })[0x0];
((VisuMZ[label][_0x13ce85(0x167)] = VisuMZ[label]['Settings'] || {}),
    (VisuMZ[_0x13ce85(0x162)] = function (_0x19c2eb, _0x1661b7) {
        const _0xfa936e = _0x13ce85;
        for (const _0x395d34 in _0x1661b7) {
            if (_0x395d34['match'](/(.*):(.*)/i)) {
                const _0x2e8dee = String(RegExp['$1']),
                    _0x1c077c = String(RegExp['$2'])[_0xfa936e(0x1b7)]()['trim']();
                let _0x3f2fa3, _0x1432b0, _0x3c2959;
                switch (_0x1c077c) {
                    case _0xfa936e(0x14a):
                        _0x3f2fa3 =
                            _0x1661b7[_0x395d34] !== '' ? Number(_0x1661b7[_0x395d34]) : 0x0;
                        break;
                    case 'ARRAYNUM':
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : []),
                            (_0x3f2fa3 = _0x1432b0[_0xfa936e(0x1d0)](_0x5acf05 =>
                                Number(_0x5acf05)
                            )));
                        break;
                    case 'EVAL':
                        _0x3f2fa3 = _0x1661b7[_0x395d34] !== '' ? eval(_0x1661b7[_0x395d34]) : null;
                        break;
                    case 'ARRAYEVAL':
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : []),
                            (_0x3f2fa3 = _0x1432b0['map'](_0x43c823 => eval(_0x43c823))));
                        break;
                    case _0xfa936e(0x1c1):
                        _0x3f2fa3 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : '';
                        break;
                    case _0xfa936e(0x1a9):
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : []),
                            (_0x3f2fa3 = _0x1432b0['map'](_0x5bf9a8 => JSON['parse'](_0x5bf9a8))));
                        break;
                    case _0xfa936e(0x153):
                        _0x3f2fa3 =
                            _0x1661b7[_0x395d34] !== ''
                                ? new Function(JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34]))
                                : new Function(_0xfa936e(0x146));
                        break;
                    case 'ARRAYFUNC':
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : []),
                            (_0x3f2fa3 = _0x1432b0['map'](
                                _0x1acf3d => new Function(JSON[_0xfa936e(0x19a)](_0x1acf3d))
                            )));
                        break;
                    case _0xfa936e(0x17e):
                        _0x3f2fa3 = _0x1661b7[_0x395d34] !== '' ? String(_0x1661b7[_0x395d34]) : '';
                        break;
                    case 'ARRAYSTR':
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== '' ? JSON['parse'](_0x1661b7[_0x395d34]) : []),
                            (_0x3f2fa3 = _0x1432b0[_0xfa936e(0x1d0)](_0x4557df =>
                                String(_0x4557df)
                            )));
                        break;
                    case _0xfa936e(0x19c):
                        ((_0x3c2959 =
                            _0x1661b7[_0x395d34] !== ''
                                ? JSON[_0xfa936e(0x19a)](_0x1661b7[_0x395d34])
                                : {}),
                            (_0x3f2fa3 = VisuMZ[_0xfa936e(0x162)]({}, _0x3c2959)));
                        break;
                    case _0xfa936e(0x148):
                        ((_0x1432b0 =
                            _0x1661b7[_0x395d34] !== '' ? JSON['parse'](_0x1661b7[_0x395d34]) : []),
                            (_0x3f2fa3 = _0x1432b0[_0xfa936e(0x1d0)](_0x1397a7 =>
                                VisuMZ['ConvertParams']({}, JSON[_0xfa936e(0x19a)](_0x1397a7))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x19c2eb[_0x2e8dee] = _0x3f2fa3;
            }
        }
        return _0x19c2eb;
    }),
    (_0x5b9434 => {
        const _0x29857d = _0x13ce85,
            _0x35a3d = _0x5b9434[_0x29857d(0x1c2)];
        for (const _0x29b447 of dependencies) {
            if (!Imported[_0x29b447]) {
                (alert(_0x29857d(0x1bf)[_0x29857d(0x1a4)](_0x35a3d, _0x29b447)),
                    SceneManager['exit']());
                break;
            }
        }
        const _0x359dd7 = _0x5b9434[_0x29857d(0x164)];
        if (_0x359dd7[_0x29857d(0x1db)](/\[Version[ ](.*?)\]/i)) {
            const _0x16223a = Number(RegExp['$1']);
            _0x16223a !== VisuMZ[label][_0x29857d(0x17f)] &&
                (alert(_0x29857d(0x1ae)[_0x29857d(0x1a4)](_0x35a3d, _0x16223a)),
                SceneManager[_0x29857d(0x168)]());
        }
        if (_0x359dd7[_0x29857d(0x1db)](/\[Tier[ ](\d+)\]/i)) {
            const _0x2f139d = Number(RegExp['$1']);
            _0x2f139d < tier
                ? (alert(_0x29857d(0x188)[_0x29857d(0x1a4)](_0x35a3d, _0x2f139d, tier)),
                  SceneManager[_0x29857d(0x168)]())
                : (tier = Math['max'](_0x2f139d, tier));
        }
        VisuMZ[_0x29857d(0x162)](VisuMZ[label][_0x29857d(0x167)], _0x5b9434[_0x29857d(0x1ba)]);
    })(pluginData),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x193)] = function () {
        const _0x36d055 = _0x13ce85;
        return (VisuMZ[_0x36d055(0x175)]['Settings'][_0x36d055(0x13d)] ?? _0x36d055(0x13a))
            ['toLowerCase']()
            [_0x36d055(0x1cc)]();
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x143)] = function () {
        const _0x19e6fb = _0x13ce85;
        return (VisuMZ[_0x19e6fb(0x175)][_0x19e6fb(0x167)][_0x19e6fb(0x16e)] ?? 'normal')
            [_0x19e6fb(0x1a2)]()
            [_0x19e6fb(0x1cc)]();
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1d5)] = function (_0x5abc91) {
        const _0x1c7249 = _0x13ce85;
        return (
            (_0x5abc91 = _0x5abc91[_0x1c7249(0x1a2)]()[_0x1c7249(0x1cc)]()),
            VisuMZ[_0x1c7249(0x175)][_0x1c7249(0x167)][_0x1c7249(0x160)][_0x5abc91] ?? {}
        );
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x1a6)] = function (_0x58e99f, _0x5955ab) {
        const _0x39d710 = _0x13ce85,
            _0x2f434a = this[_0x39d710(0x1d5)](_0x58e99f);
        return _0x2f434a['gaugeThickness'] ?? 0xc;
    }),
    (VisuMZ[_0x13ce85(0x175)]['SetLabelOffset'] = function (_0x2b1a23, _0x82330f) {
        const _0x5e091f = _0x13ce85,
            _0xd07ac = this[_0x5e091f(0x1d5)](_0x2b1a23);
        $gameTemp[_0x5e091f(0x1ac)] = {
            x: _0xd07ac['labelOffsetX'] ?? 0x0,
            y: _0xd07ac['labelOffsetY'] ?? 0x0,
        };
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x138)] = function (_0x5f004e, _0xe5cd55) {
        const _0xdc3cbb = _0x13ce85,
            _0x24b87f = this[_0xdc3cbb(0x1d5)](_0x5f004e);
        $gameTemp[_0xdc3cbb(0x1ac)] = {
            x: _0x24b87f[_0xdc3cbb(0x1e2)] ?? 0x0,
            y: _0x24b87f[_0xdc3cbb(0x1e3)] ?? 0x0,
        };
    }),
    (VisuMZ[_0x13ce85(0x175)]['ClearTextOffset'] = function () {
        const _0x2f6dfc = _0x13ce85;
        $gameTemp[_0x2f6dfc(0x1ac)] = undefined;
    }),
    (Bitmap[_0x13ce85(0x15b)][_0x13ce85(0x1b8)] = function (
        _0x1c2090,
        _0x19a494,
        _0x3c2995,
        _0x61a951,
        _0x377d05,
        _0x32b7c8,
        _0x2b590f,
        _0x4d2739,
        _0x484221
    ) {
        const _0x3f044f = _0x13ce85;
        _0x1c2090 = String(_0x1c2090)[_0x3f044f(0x1a2)]()[_0x3f044f(0x1cc)]();
        let _0x222e15 = VisuMZ[_0x3f044f(0x175)][_0x3f044f(0x1d8)](
                _0x1c2090,
                _0x19a494,
                _0x3c2995,
                _0x61a951,
                _0x377d05,
                0x1,
                !![]
            ),
            _0x39695c = VisuMZ[_0x3f044f(0x175)][_0x3f044f(0x1d8)](
                _0x1c2090,
                _0x19a494,
                _0x3c2995,
                _0x61a951,
                _0x377d05,
                _0x32b7c8,
                ![]
            );
        this[_0x3f044f(0x1d2)](_0x222e15, _0x2b590f);
        const _0x396dc4 = _0x19a494 + _0x61a951,
            _0x421940 = _0x3c2995,
            _0x557bf3 = this[_0x3f044f(0x173)]['createLinearGradient'](
                _0x19a494,
                _0x3c2995,
                _0x396dc4,
                _0x421940
            );
        this[_0x3f044f(0x177)](_0x39695c, _0x4d2739, _0x484221, _0x557bf3);
    }),
    (Bitmap[_0x13ce85(0x15b)][_0x13ce85(0x1d2)] = function (_0x3fa21d, _0x4fd85a) {
        const _0x9f116 = _0x13ce85,
            _0xeb2e6f = this['_context'];
        (_0xeb2e6f[_0x9f116(0x1cb)](),
            _0xeb2e6f['beginPath'](),
            _0xeb2e6f[_0x9f116(0x176)](_0x3fa21d[0x0], _0x3fa21d[0x1]));
        for (var _0x5030a4 = 0x2; _0x5030a4 < _0x3fa21d[_0x9f116(0x15c)]; _0x5030a4 += 0x2) {
            _0xeb2e6f[_0x9f116(0x1dd)](_0x3fa21d[_0x5030a4], _0x3fa21d[_0x5030a4 + 0x1]);
        }
        (_0xeb2e6f[_0x9f116(0x1dd)](_0x3fa21d[0x0], _0x3fa21d[0x1]),
            (_0xeb2e6f['strokeStyle'] = _0x4fd85a),
            (_0xeb2e6f['lineWidth'] = 0x2),
            _0xeb2e6f['stroke'](),
            (_0xeb2e6f[_0x9f116(0x149)] = 0xff),
            (_0xeb2e6f[_0x9f116(0x1ad)] = _0x4fd85a),
            _0xeb2e6f['fill'](),
            (_0xeb2e6f['globalAlpha'] = 0x1),
            _0xeb2e6f['restore'](),
            this[_0x9f116(0x152)][_0x9f116(0x1e1)]());
    }),
    (Bitmap[_0x13ce85(0x15b)][_0x13ce85(0x177)] = function (
        _0x3e649b,
        _0x7784ae,
        _0x271f89,
        _0x21bc4c,
        _0x55903c
    ) {
        const _0x56aaf8 = _0x13ce85,
            _0x2fe1cb = this[_0x56aaf8(0x173)];
        (_0x21bc4c[_0x56aaf8(0x14c)](0x0, _0x7784ae),
            _0x21bc4c[_0x56aaf8(0x14c)](0x1, _0x271f89),
            _0x2fe1cb[_0x56aaf8(0x1cb)](),
            _0x2fe1cb[_0x56aaf8(0x190)](),
            _0x2fe1cb[_0x56aaf8(0x176)](_0x3e649b[0x0], _0x3e649b[0x1]));
        for (var _0x5124b8 = 0x2; _0x5124b8 < _0x3e649b[_0x56aaf8(0x15c)]; _0x5124b8 += 0x2) {
            _0x2fe1cb[_0x56aaf8(0x1dd)](_0x3e649b[_0x5124b8], _0x3e649b[_0x5124b8 + 0x1]);
        }
        (_0x2fe1cb[_0x56aaf8(0x1dd)](_0x3e649b[0x0], _0x3e649b[0x1]),
            _0x55903c &&
                ((_0x2fe1cb[_0x56aaf8(0x170)] = _0x55903c),
                (_0x2fe1cb[_0x56aaf8(0x1c7)] = 0x2),
                _0x2fe1cb[_0x56aaf8(0x19b)]()),
            (_0x2fe1cb[_0x56aaf8(0x1ad)] = _0x21bc4c),
            _0x2fe1cb['fill'](),
            (_0x2fe1cb[_0x56aaf8(0x149)] = 0x1),
            _0x2fe1cb[_0x56aaf8(0x1b4)](),
            this['_baseTexture'][_0x56aaf8(0x1e1)]());
    }),
    (VisuMZ['VisualGaugeStyles']['GetPolygonStyle'] = function (
        _0x4eaea2,
        _0xc949cd,
        _0x4fb2d3,
        _0x2154c0,
        _0x5acdcf,
        _0x208620,
        _0x156c9b
    ) {
        const _0xaf541f = _0x13ce85;
        ((_0x4eaea2 = _0x4eaea2['toLowerCase']()[_0xaf541f(0x1cc)]()),
            (_0x208620 = _0x208620[_0xaf541f(0x1b2)](0x0, 0x1)),
            (_0xc949cd += 0x1),
            (_0x4fb2d3 += 0x1),
            (_0x2154c0 -= 0x2),
            (_0x5acdcf -= 0x2));
        switch (_0x4eaea2) {
            case _0xaf541f(0x156):
                return this[_0xaf541f(0x1da)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1c0):
                return this[_0xaf541f(0x178)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case 'growth':
                return this[_0xaf541f(0x1b1)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1af):
                return this['GetStaggerPolygon'](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x194):
                return this['GetDipperPolygon'](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case 'quad':
                return this[_0xaf541f(0x18e)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1ce):
                return this[_0xaf541f(0x135)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x13e):
                return this['GetFlagPolygon'](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1a0):
                return this['GetMultiStepPolygon'](
                    0x2,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case 'thirdstep':
                return this['GetMultiStepPolygon'](
                    0x3,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x191):
                return this['GetMultiStepPolygon'](
                    0x4,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x18f):
                return this[_0xaf541f(0x16d)](
                    0x5,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x136):
                return this[_0xaf541f(0x16d)](
                    0x6,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1d9):
                return this[_0xaf541f(0x16d)](
                    0x8,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case _0xaf541f(0x1ab):
                return this['GetMultiStepPolygon'](
                    0xa,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
            case 'halfsection':
                return this['GetMultiSectionPolygon'](
                    0x2,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x145):
                return this[_0xaf541f(0x151)](
                    0x3,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case 'fourthsection':
                return this[_0xaf541f(0x151)](
                    0x4,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x196):
                return this['GetMultiSectionPolygon'](
                    0x5,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case 'sixthsection':
                return this[_0xaf541f(0x151)](
                    0x6,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case 'eighthsection':
                return this[_0xaf541f(0x151)](
                    0x8,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x140):
                return this[_0xaf541f(0x151)](
                    0xa,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x18b):
                return this['GetMultiSegmentPolygon'](
                    0xa,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x1bc):
                return this[_0xaf541f(0x187)](
                    0x14,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x19f):
                return this[_0xaf541f(0x187)](
                    0x19,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x16b):
                return this[_0xaf541f(0x187)](
                    0x32,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x17b):
                return this[_0xaf541f(0x187)](
                    0x64,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x1bb):
                return this[_0xaf541f(0x187)](
                    0xc8,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case 'segmentby250':
                return this[_0xaf541f(0x187)](
                    0xfa,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x192):
                return this[_0xaf541f(0x187)](
                    0x1f4,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            case _0xaf541f(0x15d):
                return this[_0xaf541f(0x187)](
                    0x3e8,
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620,
                    _0x156c9b
                );
            default:
                return this[_0xaf541f(0x1c3)](
                    _0xc949cd,
                    _0x4fb2d3,
                    _0x2154c0,
                    _0x5acdcf,
                    _0x208620
                );
        }
    }),
    (VisuMZ[_0x13ce85(0x175)]['GetDefaultPolygon'] = function (
        _0x5d1f55,
        _0x4410b6,
        _0x580a8c,
        _0x264af4,
        _0x34be82
    ) {
        const _0x2f0503 = _0x13ce85,
            _0x438867 = _0x264af4;
        return (
            (_0x580a8c = Math[_0x2f0503(0x165)](_0x580a8c * _0x34be82)),
            [
                _0x5d1f55,
                _0x4410b6,
                _0x5d1f55 + _0x580a8c,
                _0x4410b6,
                _0x5d1f55 + _0x580a8c,
                _0x4410b6 + _0x264af4,
                _0x5d1f55,
                _0x4410b6 + _0x264af4,
            ]
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1da)] = function (
        _0x34e33e,
        _0x27d8d9,
        _0x241a81,
        _0x4378a9,
        _0xfbfaaa
    ) {
        const _0x1ed86e = _0x13ce85,
            _0x39afc2 = [],
            _0x4bb471 = Math['ceil'](_0x4378a9 / 0x3);
        if (_0x241a81 < _0x4bb471 * 0x2)
            return this[_0x1ed86e(0x1c3)](_0x34e33e, _0x27d8d9, _0x241a81, _0x4378a9, _0xfbfaaa);
        return (
            (_0x241a81 = Math['floor']((_0x241a81 - _0x4bb471) * _0xfbfaaa)),
            _0x39afc2['push'](_0x34e33e + _0x4bb471, _0x27d8d9),
            (_0x39afc2[_0x1ed86e(0x1d4)](_0x34e33e + _0x4bb471 + _0x241a81, _0x27d8d9),
            _0x39afc2[_0x1ed86e(0x1d4)](_0x34e33e + _0x241a81, _0x27d8d9 + _0x4378a9),
            _0x39afc2[_0x1ed86e(0x1d4)](_0x34e33e, _0x27d8d9 + _0x4378a9)),
            _0x39afc2
        );
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x178)] = function (
        _0x5b01ff,
        _0x317586,
        _0xdbd527,
        _0x5c47ce,
        _0x47ed37
    ) {
        const _0x4e678e = _0x13ce85,
            _0x4a0b01 = [],
            _0xee73e3 = Math[_0x4e678e(0x16c)](_0x5c47ce / 0x3);
        if (_0xdbd527 < _0xee73e3 * 0x2)
            return this['GetDefaultPolygon'](_0x5b01ff, _0x317586, _0xdbd527, _0x5c47ce, _0x47ed37);
        return (
            (_0xdbd527 = Math[_0x4e678e(0x165)]((_0xdbd527 - _0xee73e3) * _0x47ed37)),
            _0x4a0b01[_0x4e678e(0x1d4)](_0x5b01ff, _0x317586),
            _0x4a0b01[_0x4e678e(0x1d4)](_0x5b01ff + _0xdbd527, _0x317586),
            _0x4a0b01[_0x4e678e(0x1d4)](
                _0x5b01ff + _0xdbd527 + _0xee73e3,
                _0x317586 + _0x5c47ce / 0x2
            ),
            _0x4a0b01[_0x4e678e(0x1d4)](_0x5b01ff + _0xdbd527, _0x317586 + _0x5c47ce),
            _0x4a0b01['push'](_0x5b01ff, _0x317586 + _0x5c47ce),
            _0x4a0b01['push'](_0x5b01ff + _0xee73e3, _0x317586 + _0x5c47ce / 0x2),
            _0x4a0b01
        );
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x1b1)] = function (
        _0x8f21a3,
        _0x22f546,
        _0x39cd1b,
        _0x3308b1,
        _0x4e9527
    ) {
        const _0x4482d4 = _0x13ce85,
            _0x183bfd = [],
            _0x578348 = Math[_0x4482d4(0x16c)](_0x3308b1 / 0x2);
        if (_0x39cd1b < _0x578348 * 0x2)
            return this[_0x4482d4(0x1c3)](_0x8f21a3, _0x22f546, _0x39cd1b, _0x3308b1, _0x4e9527);
        return (
            (_0x39cd1b = Math[_0x4482d4(0x165)](_0x39cd1b * _0x4e9527)),
            (hr = Math[_0x4482d4(0x165)](_0x3308b1 * _0x4e9527)),
            _0x183bfd[_0x4482d4(0x1d4)](_0x8f21a3, _0x22f546 + _0x3308b1),
            _0x183bfd['push'](_0x8f21a3 + _0x39cd1b, _0x22f546 + _0x3308b1 - hr),
            _0x183bfd['push'](
                _0x8f21a3 + Math['max'](_0x39cd1b - _0x578348 * _0x4e9527, 0x0),
                _0x22f546 + _0x3308b1
            ),
            _0x183bfd
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x186)] = function (
        _0x2c9dbe,
        _0x2d109b,
        _0x31b501,
        _0x526d0e,
        _0x42b700
    ) {
        const _0x14ab18 = _0x13ce85,
            _0x2298a3 = [],
            _0xe17dcc = Math[_0x14ab18(0x16c)](_0x526d0e / 0x2),
            _0x5a1e5c = _0xe17dcc / 0x2;
        if (_0x31b501 < _0xe17dcc * 0x2)
            return this[_0x14ab18(0x1c3)](_0x2c9dbe, _0x2d109b, _0x31b501, _0x526d0e, _0x42b700);
        _0x31b501 -= _0xe17dcc;
        const _0x21ed2 = _0x31b501 / 0x3;
        return (
            (_0x31b501 = Math[_0x14ab18(0x165)](_0x31b501 * _0x42b700)),
            _0x2298a3[_0x14ab18(0x1d4)](_0x2c9dbe + _0x5a1e5c, _0x2d109b + _0x526d0e / 0x2),
            _0x42b700 < 0x1 / 0x3
                ? _0x2298a3['push'](_0x2c9dbe + _0x5a1e5c + _0x31b501, _0x2d109b + _0x526d0e / 0x2)
                : (_0x2298a3[_0x14ab18(0x1d4)](
                      _0x2c9dbe + _0x21ed2 + _0x5a1e5c,
                      _0x2d109b + _0x526d0e / 0x2
                  ),
                  _0x2298a3[_0x14ab18(0x1d4)](_0x2c9dbe + _0x21ed2 + _0xe17dcc, _0x2d109b),
                  _0x2298a3[_0x14ab18(0x1d4)](_0x2c9dbe + _0x31b501 + _0xe17dcc, _0x2d109b)),
            _0x2298a3[_0x14ab18(0x1d4)](_0x2c9dbe + _0x31b501, _0x2d109b + _0x526d0e),
            _0x2298a3[_0x14ab18(0x1d4)](_0x2c9dbe, _0x2d109b + _0x526d0e),
            _0x2298a3
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1de)] = function (
        _0x175406,
        _0x5e0857,
        _0x78501,
        _0x4c2a0e,
        _0x40305d
    ) {
        const _0xc08423 = _0x13ce85,
            _0x5439ff = [],
            _0x59e5c7 = 0x1e;
        if (_0x78501 < _0x59e5c7 * 0x2) return;
        ((_0x78501 -= _0x59e5c7), (_0x78501 = Math['floor'](_0x78501 * _0x40305d)));
        const _0x55a759 = _0x4c2a0e / 0x2;
        return (
            _0x5439ff['push'](_0x175406, _0x5e0857 + _0x55a759),
            _0x5439ff['push'](
                _0x175406 + _0x78501 + _0x59e5c7 * _0x40305d,
                _0x5e0857 + (_0x55a759 - _0x55a759 * _0x40305d)
            ),
            _0x5439ff[_0xc08423(0x1d4)](
                _0x175406 + _0x78501,
                _0x5e0857 + (_0x55a759 + _0x55a759 * _0x40305d)
            ),
            _0x5439ff
        );
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x18e)] = function (
        _0x3dc12a,
        _0x51e2c7,
        _0x26aabd,
        _0x5b0838,
        _0x502758
    ) {
        const _0x51c8a1 = _0x13ce85,
            _0x4e4a81 = [],
            _0x5940e6 = _0x5b0838;
        ((_0x26aabd -= _0x5940e6), (_0x26aabd = Math[_0x51c8a1(0x165)](_0x26aabd * _0x502758)));
        const _0x507a14 = _0x5b0838 / 0x2;
        return (
            _0x4e4a81['push'](_0x3dc12a, _0x51e2c7 + _0x507a14),
            _0x4e4a81[_0x51c8a1(0x1d4)](
                _0x3dc12a + _0x5940e6 * _0x502758 + _0x26aabd,
                _0x51e2c7 + (_0x507a14 - _0x507a14 * _0x502758)
            ),
            _0x4e4a81[_0x51c8a1(0x1d4)](
                _0x3dc12a + _0x5940e6 / 0x2 + _0x26aabd,
                _0x51e2c7 + _0x5b0838
            ),
            _0x4e4a81[_0x51c8a1(0x1d4)](_0x3dc12a + _0x5940e6 / 0x2, _0x51e2c7 + _0x5b0838),
            _0x4e4a81
        );
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x135)] = function (
        _0x4557de,
        _0x39170f,
        _0x119deb,
        _0x3165ff,
        _0x586143
    ) {
        const _0xaff74 = _0x13ce85,
            _0x1164bc = [],
            _0x70fdf = Math['ceil'](_0x3165ff / 0x2),
            _0x4163c3 = Math[_0xaff74(0x165)](_0x119deb * _0x586143);
        return (
            (_0x119deb -= _0x70fdf * 0x2),
            (_0x119deb = Math[_0xaff74(0x165)](_0x119deb * _0x586143)),
            _0x1164bc[_0xaff74(0x1d4)](_0x4557de + _0x70fdf, _0x39170f),
            _0x1164bc[_0xaff74(0x1d4)](_0x4557de + _0x70fdf + _0x119deb, _0x39170f),
            _0x1164bc[_0xaff74(0x1d4)](_0x4557de + _0x4163c3, _0x39170f + _0x3165ff),
            _0x1164bc[_0xaff74(0x1d4)](_0x4557de, _0x39170f + _0x3165ff),
            _0x1164bc
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x166)] = function (
        _0x1fe25b,
        _0x239e61,
        _0x46099a,
        _0x38084a,
        _0x45bf3d
    ) {
        const _0x670177 = _0x13ce85,
            _0x19cabc = [],
            _0x1bf6e6 = Math['ceil'](_0x38084a / 0x3);
        _0x46099a = Math[_0x670177(0x165)](_0x46099a * _0x45bf3d);
        const _0x273708 = Math[_0x670177(0x14d)](_0x46099a - _0x1bf6e6, 0x0);
        return (
            _0x19cabc['push'](_0x1fe25b, _0x239e61),
            _0x19cabc['push'](_0x1fe25b + _0x273708, _0x239e61),
            _0x19cabc[_0x670177(0x1d4)](_0x1fe25b + _0x46099a, _0x239e61 + _0x38084a / 0x2),
            _0x19cabc[_0x670177(0x1d4)](_0x1fe25b + _0x273708, _0x239e61 + _0x38084a),
            _0x19cabc[_0x670177(0x1d4)](_0x1fe25b, _0x239e61 + _0x38084a),
            _0x19cabc
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x16d)] = function (
        _0x4c960f,
        _0x450abf,
        _0x41ba6b,
        _0x315465,
        _0x3ba240,
        _0xe128e8
    ) {
        const _0x307afd = _0x13ce85,
            _0xa23543 = [],
            _0x55b584 = Math['ceil'](_0x3ba240 / 0x2);
        if (_0x315465 < _0x55b584 * 0x2)
            return this[_0x307afd(0x1c3)](_0x450abf, _0x41ba6b, _0x315465, _0x3ba240, _0xe128e8);
        _0x315465 -= _0x55b584;
        const _0x5c718c = _0x315465;
        _0x315465 = Math[_0x307afd(0x165)](_0x315465 * _0xe128e8);
        let _0x1ad2a8 = 0x1;
        _0xa23543['push'](
            _0x450abf + (_0x55b584 * _0x1ad2a8) / _0x4c960f,
            _0x41ba6b + (_0x3ba240 * (_0x4c960f - _0x1ad2a8)) / _0x4c960f
        );
        while (_0x1ad2a8 <= _0x4c960f) {
            if (_0xe128e8 <= _0x1ad2a8 / _0x4c960f) {
                _0xa23543[_0x307afd(0x1d4)](
                    _0x450abf + (_0x55b584 * _0x1ad2a8) / _0x4c960f + _0x315465,
                    _0x41ba6b + (_0x3ba240 * (_0x4c960f - _0x1ad2a8)) / _0x4c960f
                );
                break;
            }
            (_0xa23543[_0x307afd(0x1d4)](
                _0x450abf +
                    (_0x55b584 * _0x1ad2a8) / _0x4c960f +
                    _0x5c718c * (_0x1ad2a8 / _0x4c960f),
                _0x41ba6b + (_0x3ba240 * (_0x4c960f - _0x1ad2a8)) / _0x4c960f
            ),
                (_0x1ad2a8 += 0x1),
                _0xa23543['push'](
                    _0x450abf +
                        (_0x55b584 * _0x1ad2a8) / _0x4c960f +
                        _0x5c718c * ((_0x1ad2a8 - 0x1) / _0x4c960f),
                    _0x41ba6b + (_0x3ba240 * (_0x4c960f - _0x1ad2a8)) / _0x4c960f
                ));
        }
        return (
            _0xa23543[_0x307afd(0x1d4)](_0x450abf + _0x315465, _0x41ba6b + _0x3ba240),
            _0xa23543['push'](_0x450abf, _0x41ba6b + _0x3ba240),
            _0xa23543
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x151)] = function (
        _0x131925,
        _0xbeee7a,
        _0x18d568,
        _0x2b6e28,
        _0x1c1011,
        _0x1841f2,
        _0x41609a
    ) {
        const _0x11516b = _0x13ce85,
            _0x5e566c = [],
            _0x3ff1bb = _0x2b6e28,
            _0x20ddfd = _0x1c1011 * 0.99;
        ((_0x2b6e28 = Math['floor'](_0x2b6e28 * _0x1841f2)),
            _0x5e566c[_0x11516b(0x1d4)](_0xbeee7a, _0x18d568));
        const _0x438dca = _0x3ff1bb / _0x131925,
            _0x29c417 = 0x1 / _0x131925,
            _0x2b3206 = 0.5;
        let _0x156bf4 = 0x1;
        while (_0x156bf4 <= _0x131925) {
            if (_0x1841f2 <= _0x29c417 * _0x156bf4 || _0x41609a) {
                _0x5e566c[_0x11516b(0x1d4)](_0xbeee7a + _0x2b6e28, _0x18d568);
                break;
            }
            (_0x5e566c[_0x11516b(0x1d4)](_0xbeee7a + _0x438dca * _0x156bf4, _0x18d568),
                _0x5e566c[_0x11516b(0x1d4)](
                    _0xbeee7a + _0x438dca * _0x156bf4,
                    _0x18d568 + _0x20ddfd
                ));
            if (_0x2b6e28 <= _0xbeee7a + _0x438dca * _0x156bf4 + _0x2b3206) {
                _0x5e566c['push'](_0xbeee7a + _0x2b6e28, _0x18d568 + _0x20ddfd);
                break;
            }
            (_0x5e566c[_0x11516b(0x1d4)](
                _0xbeee7a + _0x438dca * _0x156bf4 + _0x2b3206,
                _0x18d568 + _0x20ddfd
            ),
                _0x5e566c[_0x11516b(0x1d4)](
                    _0xbeee7a + _0x438dca * _0x156bf4 + _0x2b3206,
                    _0x18d568
                ),
                (_0x156bf4 += 0x1));
        }
        return (
            _0x5e566c[_0x11516b(0x1d4)](_0xbeee7a + _0x2b6e28, _0x18d568 + _0x1c1011),
            _0x5e566c['push'](_0xbeee7a, _0x18d568 + _0x1c1011),
            _0x5e566c
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x187)] = function (
        _0x23f19b,
        _0xddc3d4,
        _0x52ff7a,
        _0x1f442a,
        _0x2b9d67,
        _0x2b2785,
        _0x3a5c71
    ) {
        const _0x37916d = _0x13ce85,
            _0x3adace = [],
            _0xdb1119 = _0x1f442a,
            _0x291f27 = _0x2b9d67 * 0.99;
        ((_0x1f442a = Math['floor'](_0x1f442a * _0x2b2785)),
            _0x3adace['push'](_0xddc3d4, _0x52ff7a));
        const _0xf26cd1 = Math[_0x37916d(0x14d)]((this[_0x37916d(0x139)] || 0x64) / _0x23f19b, 0x1),
            _0x1d5d52 = _0xdb1119 / _0xf26cd1,
            _0xa41583 = 0x1 / _0xf26cd1,
            _0x52f713 = 0.5;
        let _0x54e0db = 0x1;
        while (_0x54e0db <= _0xf26cd1) {
            if (_0x2b2785 <= _0xa41583 * _0x54e0db || _0x3a5c71) {
                _0x3adace[_0x37916d(0x1d4)](_0xddc3d4 + _0x1f442a, _0x52ff7a);
                break;
            }
            (_0x3adace['push'](_0xddc3d4 + _0x1d5d52 * _0x54e0db, _0x52ff7a),
                _0x3adace[_0x37916d(0x1d4)](
                    _0xddc3d4 + _0x1d5d52 * _0x54e0db,
                    _0x52ff7a + _0x291f27
                ));
            if (_0x1f442a <= _0xddc3d4 + _0x1d5d52 * _0x54e0db + _0x52f713) {
                _0x3adace[_0x37916d(0x1d4)](_0xddc3d4 + _0x1f442a, _0x52ff7a + _0x291f27);
                break;
            }
            (_0x3adace[_0x37916d(0x1d4)](
                _0xddc3d4 + _0x1d5d52 * _0x54e0db + _0x52f713,
                _0x52ff7a + _0x291f27
            ),
                _0x3adace[_0x37916d(0x1d4)](
                    _0xddc3d4 + _0x1d5d52 * _0x54e0db + _0x52f713,
                    _0x52ff7a
                ),
                (_0x54e0db += 0x1));
            if (_0x54e0db > _0xf26cd1) {
                _0x3adace['push'](_0xddc3d4 + _0x1f442a, _0x52ff7a);
                break;
            }
        }
        return (
            _0x3adace['push'](_0xddc3d4 + _0x1f442a, _0x52ff7a + _0x2b9d67),
            _0x3adace[_0x37916d(0x1d4)](_0xddc3d4, _0x52ff7a + _0x2b9d67),
            _0x3adace
        );
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x15a)] = function () {
        const _0x402aed = _0x13ce85;
        if (!this[_0x402aed(0x17c)]) return VisuMZ[_0x402aed(0x175)][_0x402aed(0x193)]();
        const _0x43657a = this['getStyleName']()[_0x402aed(0x1a2)]()[_0x402aed(0x1cc)]();
        if (_0x43657a === _0x402aed(0x15f)) return VisuMZ[_0x402aed(0x175)][_0x402aed(0x193)]();
        return _0x43657a;
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x198)] = function () {
        const _0x3ae238 = _0x13ce85,
            _0xcc7959 = VisuMZ['VisualGaugeStyles']['Settings'];
        switch (this[_0x3ae238(0x13b)]) {
            case 'hp':
                return _0xcc7959['statusHpStyle'] ?? _0x3ae238(0x1af);
            case 'mp':
                return _0xcc7959[_0x3ae238(0x16f)] ?? _0x3ae238(0x1af);
            case 'tp':
                return _0xcc7959['statusTpStyle'] ?? _0x3ae238(0x1af);
            case _0x3ae238(0x14b):
                return this[_0x3ae238(0x1dc)]()
                    ? (_0xcc7959[_0x3ae238(0x13c)] ?? 'slant')
                    : (_0xcc7959[_0x3ae238(0x189)] ?? 'slant');
            case 'aggro':
                return this[_0x3ae238(0x1a5)]()
                    ? (_0xcc7959['battlerAggroStyle'] ?? _0x3ae238(0x1b5))
                    : (_0xcc7959[_0x3ae238(0x180)] ?? _0x3ae238(0x1b5));
        }
        return VisuMZ['VisualGaugeStyles'][_0x3ae238(0x193)]();
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x197)] = function (
        _0x3f966f,
        _0x10c3b7,
        _0xfb9cea,
        _0x3906c7
    ) {
        const _0x210f3a = _0x13ce85;
        if (Imported[_0x210f3a(0x1c6)] && this['_statusType'] === 'tp' && this['_tpGaugeSprite'])
            this[_0x210f3a(0x179)](_0x3f966f, _0x10c3b7, _0xfb9cea, _0x3906c7);
        else {
            const _0x4dc2c9 = this['gaugeColor1'](),
                _0x11e87c = this[_0x210f3a(0x1e6)]();
            this[_0x210f3a(0x1d3)](
                _0x4dc2c9,
                _0x11e87c,
                _0x3f966f,
                _0x10c3b7,
                _0xfb9cea,
                _0x3906c7
            );
        }
    }),
    (Sprite_Gauge['prototype']['drawFullGauge'] = function (
        _0x97a666,
        _0x33c18f,
        _0x278c97,
        _0x11425d,
        _0x204013,
        _0x5b621c
    ) {
        const _0x532b0e = _0x13ce85;
        Imported[_0x532b0e(0x1c6)] && this[_0x532b0e(0x13b)] === 'tp' && this[_0x532b0e(0x1e4)]
            ? this['drawFullGaugeEnhancedTp'](
                  _0x97a666,
                  _0x33c18f,
                  _0x278c97,
                  _0x11425d,
                  _0x204013,
                  _0x5b621c
              )
            : this[_0x532b0e(0x1d3)](
                  _0x97a666,
                  _0x33c18f,
                  _0x278c97,
                  _0x11425d,
                  _0x204013,
                  _0x5b621c
              );
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1d3)] = function (
        _0x1140c1,
        _0x4ef3de,
        _0x5709cd,
        _0x509d14,
        _0x180a78,
        _0x31e226
    ) {
        const _0x36e475 = _0x13ce85,
            _0x2f0fc9 = this['styleName'](),
            _0x180429 = this[_0x36e475(0x1cd)](),
            _0x56fb69 = this[_0x36e475(0x174)]();
        ((VisuMZ['VisualGaugeStyles']['_maxValueSegment'] = this[_0x36e475(0x19e)]() || 0x64),
            this['bitmap'][_0x36e475(0x1b8)](
                _0x2f0fc9,
                _0x5709cd,
                _0x509d14,
                _0x180a78,
                _0x31e226,
                _0x180429,
                _0x56fb69,
                _0x1140c1,
                _0x4ef3de
            ));
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x1b9)] =
        Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1e5)]),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1e5)] = function () {
        const _0x24538f = _0x13ce85,
            _0x256b25 = this[_0x24538f(0x15a)]();
        return (VisuMZ[_0x24538f(0x175)]['GetGaugeHeight'](_0x256b25) ?? 0xc)[_0x24538f(0x1b2)](
            0x1,
            this[_0x24538f(0x18c)]()
        );
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1df)] = Sprite_Gauge['prototype']['setupLabelFont']),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1c4)] = function () {
        const _0x47a26a = _0x13ce85;
        (VisuMZ[_0x47a26a(0x175)][_0x47a26a(0x1df)]['call'](this),
            VisuMZ[_0x47a26a(0x175)][_0x47a26a(0x159)](this['styleName']()));
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1d6)] = Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1cf)]),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1cf)] = function () {
        const _0x3966e1 = _0x13ce85;
        (VisuMZ[_0x3966e1(0x175)][_0x3966e1(0x1d6)][_0x3966e1(0x1a1)](this),
            VisuMZ['VisualGaugeStyles'][_0x3966e1(0x15e)]());
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x14e)] = Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x183)]),
    (Sprite_Gauge['prototype'][_0x13ce85(0x183)] = function () {
        const _0x346061 = _0x13ce85;
        (VisuMZ[_0x346061(0x175)][_0x346061(0x14e)]['call'](this),
            VisuMZ[_0x346061(0x175)]['SetValueOffset'](this['styleName']()));
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x1be)] = Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1a8)]),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1a8)] = function () {
        const _0x36c509 = _0x13ce85;
        (VisuMZ[_0x36c509(0x175)][_0x36c509(0x1be)][_0x36c509(0x1a1)](this),
            VisuMZ[_0x36c509(0x175)][_0x36c509(0x15e)]());
    }),
    (VisuMZ[_0x13ce85(0x175)][_0x13ce85(0x18d)] = Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1a3)]),
    (Sprite_Gauge['prototype'][_0x13ce85(0x1a3)] = function () {
        const _0x2900b5 = _0x13ce85;
        (VisuMZ[_0x2900b5(0x175)]['Sprite_Gauge_redraw'][_0x2900b5(0x1a1)](this),
            VisuMZ[_0x2900b5(0x175)][_0x2900b5(0x15e)]());
    }),
    (VisuMZ['VisualGaugeStyles'][_0x13ce85(0x141)] = Bitmap[_0x13ce85(0x15b)]['drawText']),
    (Bitmap[_0x13ce85(0x15b)][_0x13ce85(0x1c9)] = function (
        _0x2c9ceb,
        _0xb2d1f5,
        _0xcc4813,
        _0x18de3e,
        _0x1e75de,
        _0x3becb7
    ) {
        const _0x5919ea = _0x13ce85;
        ($gameTemp &&
            $gameTemp[_0x5919ea(0x1ac)] &&
            ((_0xb2d1f5 += $gameTemp['_visualGaugeStyleOffset']['x']),
            (_0xcc4813 += $gameTemp[_0x5919ea(0x1ac)]['y'])),
            VisuMZ['VisualGaugeStyles'][_0x5919ea(0x141)][_0x5919ea(0x1a1)](
                this,
                _0x2c9ceb,
                _0xb2d1f5,
                _0xcc4813,
                _0x18de3e,
                _0x1e75de,
                _0x3becb7
            ));
    }),
    (Sprite_HpGauge[_0x13ce85(0x15b)][_0x13ce85(0x1e5)] = function () {
        const _0x3a06c2 = _0x13ce85;
        return VisuMZ[_0x3a06c2(0x175)][_0x3a06c2(0x1b9)][_0x3a06c2(0x1a1)](this);
    }),
    (Sprite_HpGauge['prototype']['getStyleName'] = function () {
        const _0x4d7a96 = _0x13ce85,
            _0x4528d0 = VisuMZ[_0x4d7a96(0x175)][_0x4d7a96(0x167)];
        return _0x4528d0['battlerHpStyle'] ?? 'normal';
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)][_0x13ce85(0x1dc)] = function () {
        const _0x24e596 = _0x13ce85;
        if (!Imported[_0x24e596(0x169)]) return ![];
        if (!this['_battler']) return ![];
        if (!this[_0x24e596(0x17c)]['battler']()) return ![];
        return this === this[_0x24e596(0x17c)][_0x24e596(0x150)]()[_0x24e596(0x155)];
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)]['isBattlerAggroGauge'] = function () {
        const _0x11dba1 = _0x13ce85;
        if (!Imported[_0x11dba1(0x171)]) return ![];
        if (!this[_0x11dba1(0x17c)]) return ![];
        if (!this['_battler'][_0x11dba1(0x150)]()) return ![];
        return this === this[_0x11dba1(0x17c)][_0x11dba1(0x150)]()[_0x11dba1(0x181)];
    }),
    (Sprite_Gauge[_0x13ce85(0x15b)]['drawGaugeRectEnhancedTp'] = function (
        _0x483e66,
        _0x79a261,
        _0x592fa3,
        _0x259017
    ) {
        const _0x19317e = _0x13ce85,
            _0x12597d = this[_0x19317e(0x154)](this[_0x19317e(0x1b0)](), 0x1),
            _0x3bb6c9 = this[_0x19317e(0x154)](this['gaugeColor2'](), 0x2);
        this['drawFullGaugeEnhancedTp'](
            _0x12597d,
            _0x3bb6c9,
            _0x483e66,
            _0x79a261,
            _0x592fa3,
            _0x259017
        );
    }),
    (Sprite_Gauge['prototype'][_0x13ce85(0x1c8)] = function (
        _0x35bd80,
        _0x3c6590,
        _0x31aaf1,
        _0xadcb79,
        _0x588526,
        _0x396112
    ) {
        const _0x18c853 = _0x13ce85;
        this[_0x18c853(0x13f)](!![]);
        const _0x36ec34 = this[_0x18c853(0x15a)](),
            _0x216a3c = this[_0x18c853(0x1cd)](),
            _0x241f6c = this[_0x18c853(0x174)](),
            _0x52d4a0 = VisuMZ[_0x18c853(0x175)][_0x18c853(0x1d8)](
                _0x36ec34,
                _0x31aaf1,
                _0xadcb79,
                _0x588526,
                _0x396112,
                0x1,
                !![]
            );
        ((VisuMZ['VisualGaugeStyles'][_0x18c853(0x139)] = this[_0x18c853(0x19e)]() || 0x64),
            this[_0x18c853(0x172)][_0x18c853(0x18a)][_0x18c853(0x1d2)](_0x52d4a0, _0x241f6c));
        const _0x37a723 = VisuMZ[_0x18c853(0x175)][_0x18c853(0x1d8)](
                _0x36ec34,
                _0x31aaf1,
                _0xadcb79,
                _0x588526,
                _0x396112,
                _0x216a3c,
                ![]
            ),
            _0x329d45 = this[_0x18c853(0x1e4)][_0x18c853(0x18a)][_0x18c853(0x173)][
                'createLinearGradient'
            ](_0x31aaf1, _0xadcb79, _0x31aaf1 + _0x588526, _0xadcb79);
        ((VisuMZ[_0x18c853(0x175)][_0x18c853(0x139)] = this[_0x18c853(0x19e)]() || 0x64),
            this[_0x18c853(0x1e4)]['bitmap']['drawVisualStyleGaugeFront'](
                _0x37a723,
                _0x35bd80,
                _0x3c6590,
                _0x329d45
            ));
    }),
    (Window_Base['prototype'][_0x13ce85(0x184)] = function (
        _0x40476a,
        _0x310e51,
        _0xa4963b,
        _0x40ba73,
        _0xc62bde,
        _0x3f3211
    ) {
        const _0x515219 = _0x13ce85,
            _0x31587d = VisuMZ[_0x515219(0x175)]['HorzStyle'](),
            _0x5181fd = (VisuMZ['VisualGaugeStyles']['GetGaugeHeight'](_0x31587d) ?? 0xc)[
                _0x515219(0x1b2)
            ](0x1, 0x20),
            _0x116900 = _0x310e51 + this[_0x515219(0x142)]() - _0x5181fd - 0x2,
            _0x40cff7 = ColorManager['gaugeBackColor']();
        ((VisuMZ[_0x515219(0x175)][_0x515219(0x139)] = 0x64),
            this[_0x515219(0x1aa)][_0x515219(0x1b8)](
                _0x31587d,
                _0x40476a,
                _0x116900,
                _0xa4963b,
                _0x5181fd,
                _0x40ba73,
                _0x40cff7,
                _0xc62bde,
                _0x3f3211
            ));
    }),
    (Window_StatusBase[_0x13ce85(0x15b)]['drawActorLevel'] = function (
        _0x1e4c5e,
        _0x775bb1,
        _0x250a83
    ) {
        const _0x5d2eb0 = _0x13ce85;
        if (VisuMZ['CoreEngine'][_0x5d2eb0(0x167)][_0x5d2eb0(0x1e7)][_0x5d2eb0(0x185)] === ![])
            return;
        if (this[_0x5d2eb0(0x1b6)]()) this[_0x5d2eb0(0x1bd)](_0x1e4c5e, _0x775bb1, _0x250a83);
        this[_0x5d2eb0(0x137)]();
        const _0x42c866 = VisuMZ[_0x5d2eb0(0x175)][_0x5d2eb0(0x193)](),
            _0x22fb41 = VisuMZ[_0x5d2eb0(0x16a)][_0x5d2eb0(0x167)][_0x5d2eb0(0x1d1)],
            _0x5e9f17 = _0x22fb41['MatchLabelColor']
                ? ColorManager[_0x5d2eb0(0x1e0)]()
                : ColorManager['systemColor']();
        (this['changeTextColor'](_0x5e9f17),
            _0x22fb41['LabelFontMainType'] === _0x5d2eb0(0x195) &&
                ((this[_0x5d2eb0(0x1aa)][_0x5d2eb0(0x158)] = $gameSystem[_0x5d2eb0(0x147)]()),
                (this['contents'][_0x5d2eb0(0x144)] = $gameSystem['mainFontSize']())),
            VisuMZ[_0x5d2eb0(0x175)][_0x5d2eb0(0x159)](_0x42c866),
            this[_0x5d2eb0(0x1c9)](TextManager['levelA'], _0x775bb1, _0x250a83, 0x30),
            this[_0x5d2eb0(0x137)](),
            VisuMZ[_0x5d2eb0(0x175)]['SetValueOffset'](_0x42c866),
            this['drawText'](
                _0x1e4c5e[_0x5d2eb0(0x163)],
                _0x775bb1 + 0x54,
                _0x250a83,
                0x24,
                _0x5d2eb0(0x161)
            ),
            this[_0x5d2eb0(0x137)](),
            VisuMZ[_0x5d2eb0(0x175)][_0x5d2eb0(0x15e)]());
    }));
