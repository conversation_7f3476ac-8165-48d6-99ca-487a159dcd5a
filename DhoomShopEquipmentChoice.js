//=============================================================================
// DhoomShopEquipmentChoice.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_ShopEquipmentChoice = '1.0a';

var Dhoom = Dhoom || {};
Dhoom.ShopEquipmentChoice = Dhoom.ShopEquipmentChoice || {};
/*:
 * @plugindesc Dhoom ShopEquipmentChoice v1.0a - 02/09/2024
 * <AUTHOR>
 * @url drd-workshop.blogspot.com
 * @target MZ
 *
 * @param Show Choice Only On Equippable Actor
 * @desc Only show equip choice after buying equipment when there's any actor that can equip it?
 * @type boolean
 * @default true
 *
 * @param Choice Window Setting
 * @desc Choice window setting
 * @type struct<choiceWindowSetting>
 * @default {"x":"0","y":"460","width":"720","height":"252","padding":"12","opacity":"0","weapon":"\"Do you want to equip the new weapon?\"","armor":"\"Do you want to equip the new armor?\"","helpText":"{\"text\":\"%1\",\"x\":\"0\",\"y\":\"0\",\"width\":\"696\",\"height\":\"72\",\"style\":\"{\\\"name\\\":\\\"\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"true\",\"spacing\":\"3\",\"centerVertical\":\"true\"}","confirm":"{\"text\":\"Yes\",\"x\":\"24\",\"y\":\"72\",\"width\":\"108\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"false\",\"spacing\":\"3\",\"centerVertical\":\"false\"}","cancel":"{\"text\":\"No\",\"x\":\"24\",\"y\":\"116\",\"width\":\"108\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"false\",\"spacing\":\"3\",\"centerVertical\":\"false\"}"}
 *
 * @param Actor Window Setting
 * @desc Window for choosing which actor to equip the newly bought equipment.
 * @type struct<statusWindowSetting>
 * @default {"x":"0","y":"216","width":"720","height":"496","padding":"12","opacity":"255","itemHeight":"78","showAllActors":"false"}
 *
 * @help After buying an equipment, there will be a choice to immediately
 * equip the newly bought equipment to an actor.
 */

/*~struct~choiceWindowSetting:
@param x
@text X Position
@desc Window X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Window Y position.
@type number
@min -999999
@default 0

@param width
@text Window Width
@desc Window width.
@type number
@min 1
@default 1280

@param height
@text Window Height
@desc Window height.
@type number
@min 1
@default 760

@param padding
@text Window Padding
@desc Window padding.
@type number
@min 0
@default 12

@param opacity
@text Window Opacity
@desc Window opacity.
@type number
@min 0
@max 255
@default 0

@param weapon
@text Weapon Help Text
@desc %1 = Weapon name.
@type note
@default 

@param armor
@text Armor Help Text
@desc %1 = Armor name.
@type note

@param helpText
@text Help Text Setting
@desc %1 = Help text
@type struct<textSetting>
@default 

@param confirm
@text Confirm Text Setting
@type struct<textSetting>
@default 

@param cancel
@text Cancel Text Setting
@type struct<textSetting>
@default 
*/

/*~struct~statusWindowSetting:
@param x
@text X Position
@desc Window X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Window Y position.
@type number
@min -999999
@default 0

@param width
@text Window Width
@desc Window width.
@type number
@min 1
@default 1280

@param height
@text Window Height
@desc Window height.
@type number
@min 1
@default 760

@param padding
@text Window Padding
@desc Window padding.
@type number
@min 0
@default 12

@param opacity
@text Window Opacity
@desc Window opacity.
@type number
@min 0
@max 255
@default 0

@param itemHeight
@text Item Height
@type number
@min 1
@default 78

@param showAllActors
@text Show All Actors?
@desc When set to false, only actor that can equip the item will be listed.
@type boolean
@default false
*/

/*~struct~textSetting:
@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area.
@type number
@min 4
@default 1

@param height
@text Text Area Height
@desc Height of text area.
@type number
@min 4
@default 1

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param wordwrap
@text Word Wrap
@type boolean
@default true

@param spacing
@text Vertical Spacing
@type number
@min -999999
@default 3

@param centerVertical
@text Center Vertically?
@type boolean
@default true
*/

/*~struct~FontStyle:
@param name
@text Font Name
@desc Font name, leave empty if you want to use the default font.
@default 

@param size
@text Font Size
@desc Font size
@default 32
@type number
@min 1

@param color
@text Font Color
@desc Font color
@default #FFFFFF

@param outlineWidth
@text Font Outline Width
@desc Font outline width
@default 1
@type number

@param outlineColor
@text Font Outline Color
@desc Font outline color
@default #000000

@param shadowBlur
@text Shadow Blur
@desc Shadow blur strength.
@type number
@min 0
@default 0

@param shadowColor
@text Shadow color
@default #000000

@param bold
@text Font Bold
@desc Font bold
@default false
@type boolean

@param italic
@text Font Italic
@desc Font italic
@default false
@type boolean

@param spacing
@text Font Spacing
@type number
@min 0
@default 0

@param align
@text Text Alignment
@desc Text alignment
@default center
@type select
@option left
@option center
@option right

@param case
@text Text Case
@type select
@option unchanged
@option lowercase
@option UPPERCASE
@option Titlecase
@default unchanged
*/

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom ShopEquipmentChoice/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.ShopEquipmentChoice.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.ShopEquipmentChoice[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.ShopEquipmentChoice.loadParameters();

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ShopEquipStatus
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_ShopEquipStatus extends Window_PartyReserve {
    constructor() {
        super(...arguments);
    }

    initialize() {
        this._actors = [];
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
    }

    setting() {
        return Dhoom.ShopEquipmentChoice.actorWindowSetting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    itemHeight() {
        return this.setting().itemHeight;
    }

    maxItems() {
        return this._actors.length;
    }

    actor(index) {
        return this._actors[index];
    }

    currentActor() {
        return this.actor(this.index());
    }

    isEnabled(index) {
        return this._item && this.actor(index) && this.actor(index).canEquip(this._item);
    }

    drawItem(index) {
        this.changePaintOpacity(this.isEnabled(index));
        this._disableChangePaintOpacity = true;
        this.drawItemImage(index);
        var rect = this.itemLineRect(index);
        this.drawActorName(this.actor(index), rect.x + 184, rect.y, 168);
        this._disableChangePaintOpacity = false;
        this.changePaintOpacity(true);
    }

    setItem(item) {
        this._item = item;
        this._actors = $gameParty.members().filter(actor => actor.canEquip(item));
        this.refresh();
    }

    changePaintOpacity(value) {
        if (!this._disableChangePaintOpacity) super.changePaintOpacity(value);
    }

    setStatusWindow(wnd) {
        this._statusWindow = wnd;
        this.callUpdateHelp();
    }

    callUpdateHelp() {
        if (this.active && this._statusWindow)
            this._statusWindow.setEquipActor(this.currentActor());
    }

    select(index) {
        Window_StatusBase.prototype.select.call(this, index);
    }

    cursorUp(wrap) {
        Window_StatusBase.prototype.cursorUp.call(this, wrap);
    }

    cursorPagedown() {
        Window_StatusBase.prototype.cursorPagedown.call(this);
    }

    cursorPageup() {
        Window_StatusBase.prototype.cursorPageup.call(this);
    }

    reselect() {
        Window_StatusBase.prototype.reselect.call(this);
    }

    checkShiftSortShortcut() {}
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ShopBuyChoice
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_ShopBuyChoice extends Window_Selectable {
    constructor() {
        super(...arguments);
    }

    initialize() {
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
        this.refresh();
    }

    setting() {
        return Dhoom.ShopEquipmentChoice.choiceWindowSetting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    maxItems() {
        return 2;
    }

    itemRect(index) {
        var rect = new Rectangle();
        var setting = index === 1 ? this.setting().cancel : this.setting().confirm;
        rect.x = setting.x;
        rect.y = setting.y;
        rect.width = setting.width;
        rect.height = setting.height;
        return rect;
    }

    drawAllItems() {
        this.drawHelpText();
        super.drawAllItems();
    }

    drawHelpText() {
        var text = DataManager.isWeapon(this._item) ? this.setting().weapon : this.setting().armor;
        if (this._item) text = text.format(this._item.name);
        this.drawCustomText(0, 0, this.setting().helpText, [text]);
    }

    drawItem(index) {
        this.drawCustomText(
            this.itemPadding(),
            0,
            index === 0 ? this.setting().confirm : this.setting().cancel,
            [this._item ? this._item.name : '']
        );
    }

    drawCustomText(x, y, setting, replaces) {
        this._isDrawingCustomText = true;
        this._customTextSetting = setting;
        this.contents.changeTextStyle(setting.style);
        var text = setting.text;
        text = text.format.apply(text, replaces);
        text = this.convertEscapeCharacters(text);
        if (setting.wordwrap) {
            var texts = this.sliceText(text, setting.width);
        } else {
            var texts = [text];
        }
        this.contents.changeTextStyle(setting.style);
        var lh = this.contents.fontSize + 2;
        var spacing = setting.spacing;
        var width = setting.width;
        var sy = setting.centerVertical
            ? (setting.height - (texts.length * (lh + spacing) - spacing)) / 2
            : 0;
        for (var i = 0; i < texts.length; i++) {
            this.contents.changeTextStyle(setting.style);
            var cw = this.textSizeEx(texts[i]).width + this.contents.outlineWidth * 2;
            var sx = this.contents.outlineWidth;
            if (setting.style.align === 'center') {
                sx = (width - cw) / 2;
            }
            if (setting.style.align === 'right') {
                sx = width - cw;
            }
            this.contents.changeTextStyle(setting.style);
            this.drawTextEx(texts[i], x + sx + setting.x, y + sy + setting.y);
            sy += lh + spacing;
        }
        this._isDrawingCustomText = false;
    }

    setItem(item) {
        this._item = item;
        this.refresh();
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ShopNumber
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Window_ShopNumber.prototype.totalPriceY = function () {
    return this.lineHeight();
};

Window_ShopNumber.prototype.showButtons = function () {
    if (this._buttons) this._buttons.forEach(button => (button.visible = true));
};

Window_ShopNumber.prototype.hideButtons = function () {
    if (this._buttons) this._buttons.forEach(button => (button.visible = false));
};

Window_ShopNumber.prototype.activate = function () {
    Window_Selectable.prototype.activate.call(this);
    this.showButtons();
};

Window_ShopNumber.prototype.deactivate = function () {
    Window_Selectable.prototype.deactivate.call(this);
    this.hideButtons();
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ShopStatus
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Window_ShopStatus.prototype.setEquipMode = function (value) {
    this._equipMode = value;
    this.refresh();
};

Window_ShopStatus.prototype.setEquipActor = function (actor) {
    if (this._equipActor !== actor && this._equipMode) {
        this._equipActor = actor;
        this.refresh();
    }
};

Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawEquipData =
    Window_ShopStatus.prototype.drawEquipData;
Window_ShopStatus.prototype.drawEquipData = function () {
    this._maxParamName = 0;
    this._firstParamY = 9999999;
    Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawEquipData.call(this);
    if (this._equipMode) {
        this.drawCurrentActorEquipment();
        this.drawEquipActorParam();
    }
};

Window_ShopStatus.prototype.drawCurrentActorEquipment = function () {
    if (this._equipActor && this._item) {
        this.changePaintOpacity(true);
        const item = this._equipActor.equips()[this._item.etypeId - 1];
        this.drawText(
            'Current: ',
            this.itemPadding(),
            this.lineHeight() * 2,
            this.innerWidth - this.itemPadding() * 2
        );
        this.drawItemName(
            item,
            this.itemPadding(),
            this.lineHeight() * 3,
            this.innerWidth - this.itemPadding() * 2
        );
    }
};

Window_ShopStatus.prototype.drawEquipActorParam = function () {
    this.changePaintOpacity(true);
    const actor = this._equipActor;
    if (!actor) return;
    const params = this.actorParams();
    const width = (this.innerWidth - this._maxParamName) / 3;
    Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorCharacter.call(
        this,
        actor,
        this._maxParamName + width / 2,
        this._firstParamY - 4
    );
    let ay = this._firstParamY;
    const lh = this.gaugeLineHeight() + 8;
    for (const param of params) {
        const y = ay - (this.lineHeight() - lh) / 2 - 4;
        Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorParamDifference.call(
            this,
            actor,
            param,
            this._maxParamName,
            y,
            width
        );
        ay += lh;
    }
};

Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorCharacter =
    Window_ShopStatus.prototype.drawActorCharacter;
Window_ShopStatus.prototype.drawActorCharacter = function (actor, x, y) {
    if (!this._equipMode) {
        Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorCharacter.call(this, actor, x, y);
    }
};

Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorParamDifference =
    Window_ShopStatus.prototype.drawActorParamDifference;
Window_ShopStatus.prototype.drawActorParamDifference = function (actor, paramId, x, y, width) {
    if (!this._equipMode) {
        Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawActorParamDifference.call(
            this,
            actor,
            paramId,
            x,
            y,
            width
        );
    }
};

Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawParamName =
    Window_ShopStatus.prototype.drawParamName;
Window_ShopStatus.prototype.drawParamName = function (paramId, x, y, width) {
    var result = Dhoom.ShopEquipmentChoice.Window_ShopStatus_drawParamName.apply(this, arguments);
    this._firstParamY = Math.min(this._firstParamY, y);
    this._maxParamName = Math.max(this._maxParamName + 1, result);
    return result;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Scene_Shop
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.ShopEquipmentChoice.Scene_Shop_create = Scene_Shop.prototype.create;
Scene_Shop.prototype.create = function () {
    Dhoom.ShopEquipmentChoice.Scene_Shop_create.call(this);
    this.createBuyChoiceWindow();
};

Dhoom.ShopEquipmentChoice.Scene_Shop_createStatusWindow = Scene_Shop.prototype.createStatusWindow;
Scene_Shop.prototype.createStatusWindow = function () {
    Dhoom.ShopEquipmentChoice.Scene_Shop_createStatusWindow.call(this);
    this._equipStatusWindow = new Window_ShopEquipStatus();
    this._equipStatusWindow.setHandler('ok', this.onEquipStatusOk.bind(this));
    this._equipStatusWindow.setHandler('cancel', this.onEquipStatusCancel.bind(this));
    this._equipStatusWindow.hide();
    this._equipStatusWindow.setStatusWindow(this._statusWindow);
    this.addWindow(this._equipStatusWindow);
};

Scene_Shop.prototype.createBuyChoiceWindow = function () {
    this._buyChoiceWindow = new Window_ShopBuyChoice();
    this._buyChoiceWindow.setHandler('ok', this.onBuyChoiceOk.bind(this));
    this._buyChoiceWindow.setHandler('cancel', this.onBuyChoiceCancel.bind(this));
    this._buyChoiceWindow.hide();
    this.addWindow(this._buyChoiceWindow);
};

Dhoom.ShopEquipmentChoice.Scene_Shop_doBuy = Scene_Shop.prototype.doBuy;
Scene_Shop.prototype.doBuy = function (number) {
    if (
        !DataManager.isItem(this._item) &&
        (!this.afterBuyChoiceOnlyEquippable() || this.boughtItemHasEquippableActor())
    ) {
        this._buyChoiceAfter = true;
    }
    Dhoom.ShopEquipmentChoice.Scene_Shop_doBuy.call(this, number);
};

Scene_Shop.prototype.afterBuyChoiceOnlyEquippable = function () {
    return Dhoom.ShopEquipmentChoice.showChoiceOnlyOnEquippableActor;
};

Scene_Shop.prototype.boughtItemHasEquippableActor = function () {
    return $gameParty
        .members()
        .some(actor => actor.canEquip(this._item) && !actor.isEquipped(this._item), this);
};

Dhoom.ShopEquipmentChoice.Scene_Shop_endNumberInput = Scene_Shop.prototype.endNumberInput;
Scene_Shop.prototype.endNumberInput = function () {
    if (this._buyChoiceAfter) {
        this._buyChoiceAfter = false;
        this._numberWindow.deactivate();
        this._buyChoiceWindow.setItem(this._item);
        this._buyChoiceWindow.show();
        this._buyChoiceWindow.activate();
        this._buyChoiceWindow.select(0);
    } else {
        Dhoom.ShopEquipmentChoice.Scene_Shop_endNumberInput.call(this);
    }
};

Scene_Shop.prototype.onBuyChoiceOk = function () {
    if (this._buyChoiceWindow.index() === 0) {
        this._statusWindow.setEquipMode(true);
        this._buyChoiceWindow.hide();
        this._equipStatusWindow.setItem(this._item);
        this._equipStatusWindow.show();
        this._equipStatusWindow.activate();
        this._equipStatusWindow.select(0);
        this._numberWindow.hide();
    } else {
        this.onBuyChoiceCancel();
    }
};

Scene_Shop.prototype.onBuyChoiceCancel = function () {
    Dhoom.ShopEquipmentChoice.Scene_Shop_endNumberInput.call(this);
    this._buyChoiceWindow.hide();
};

Scene_Shop.prototype.onEquipStatusOk = function () {
    this.executeEquipChange();
    Dhoom.ShopEquipmentChoice.Scene_Shop_endNumberInput.call(this);
    this._equipStatusWindow.hide();
    this._statusWindow.setEquipMode(false);
};

Scene_Shop.prototype.onEquipStatusCancel = function () {
    Dhoom.ShopEquipmentChoice.Scene_Shop_endNumberInput.call(this);
    this._equipStatusWindow.hide();
    this._statusWindow.setEquipMode(false);
};

Scene_Shop.prototype.executeEquipChange = function () {
    SoundManager.playEquip();
    const actor = this._equipStatusWindow.currentActor();
    actor.changeEquipById(this._item.etypeId, this._item.id);
};
