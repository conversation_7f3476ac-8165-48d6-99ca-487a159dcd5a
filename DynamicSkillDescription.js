/*:
 * @target MZ
 * @plugindesc Dynamically updates skill descriptions with evaluated formulas.
 * @help This plugin allows you to dynamically update skill descriptions
 * with evaluated formulas using a specific notation in the description.
 *
 * Use the following notation in the skill description:
 * Heals for about \eval[300 + user.mat * 3.5]
 */

(() => {
    // Extend the drawItem method to dynamically update the skill description
    const _Window_SkillList_drawItem = Window_SkillList.prototype.drawItem;

    Window_SkillList.prototype.drawItem = function (index) {
        const skill = this.itemAt(index);
        if (skill) {
            // Get the current actor (user)
            const user = this._actor; // This references the current actor using the skill
            // Replace the \eval[...] notation with the evaluated formula
            skill.description = skill.description.replace(/\\eval\[(.*?)\]/g, (_, formula) => {
                try {
                    return eval(formula);
                } catch (e) {
                    return 0;
                }
            });
        }
        _Window_SkillList_drawItem.call(this, index);
    };
})();
