//=============================================================================
// VisuStella MZ - Message Log
// VisuMZ_3_MessageLog.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_MessageLog = true;

var VisuMZ = VisuMZ || {};
VisuMZ.MessageLog = VisuMZ.MessageLog || {};
VisuMZ.MessageLog.version = 1.02;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.02] [MessageLog]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Message_Log_VisuStella_MZ
 * @base VisuMZ_1_MessageCore
 * @orderAfter VisuMZ_1_MessageCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The Message Log plugin will take and record any Show Message entries played
 * on the map screen so that players can go back to them and review them at a
 * later point in time when needed. This is helpful for players who may have
 * missed important information that would have been displayed or those who
 * would like to review what was said previously. The Message Log will not
 * record any of the text displayed in the battle scene in order to preserve
 * the data to one specific scene.
 *
 * Features include all (but not limited to) the following:
 *
 * * Record messages written out in the "Show Text" command while the player is
 *   on the map screen.
 * * Players can access the Message Log through either the Main Menu or by a
 *   shortcut key whenever the Message Window is open.
 * * Faces and speaker names will also be recorded.
 * * Choice List selections, Number Inputs, and selected Event Items will also
 *   be recorded.
 * * Those using the Extended Message Functionality plugin can also bind this
 *   effect to the Button Console.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_1_MessageCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Replaced Message Text Codes
 *
 * Some text codes are not compatible with the Message Log when viewed such as
 * wait commands, showing the gold window, etc. When that happens, those text
 * codes will be removed from visibility in the Message Log in order to prevent
 * any problems. The following is a list of the Message Text Codes that will
 * not appear in the Message Log:
 *
 *   --------------------
 *   Default RPG Maker MZ
 *   --------------------
 *   \$
 *   \.
 *   \|
 *   \!
 *   \>
 *   \<
 *   \^
 *
 *   --------------------
 *   VisuMZ_1_MessageCore
 *   --------------------
 *   \Picture<x>
 *   \CenterPicture<x>
 *   \CommonEvent[x]
 *   \Wait[x]
 *   \NormalBG
 *   \DimBG
 *   \TransparentBG
 *   \WindowMoveTo: ?>
 *   \WindowMoveBy: ?>
 *   \WindowReset
 *   \TroopMember[x]
 *   \TroopNameMember[x]
 *   \ChangeFace<?>
 *   \FaceIndex[x]
 *   <Auto>
 *   <Auto Width>
 *   <Auto Height>
 *   <Auto Actor: x>
 *   <Auto Party: x>
 *   <Auto Enemy: x>
 *   <Auto Event: x>
 *   <Auto Player>
 *   <Show>
 *   <Show Switch: x>
 *   <Show All Switches: x,x,x>
 *   <Show Any Switches: x,x,x>
 *   <Hide>
 *   <Hide Switch: x>
 *   <Hide All Switches: x,x,x>
 *   <Hide Any Switches: x,x,x>
 *   <Enable>
 *   <Enable Switch: x>
 *   <Enable All Switches: x,x,x>
 *   <Enable Any Switches: x,x,x>
 *   <Disable>
 *   <Disable Switch: x>
 *   <Disable All Switches: x,x,x>
 *   <Disable Any Switches: x,x,x>
 *   <Position: ?>
 *   <Coordinates: ?>
 *   <Dimensions: ?>
 *
 *   -----------------------
 *   VisuMZ_2_ExtMessageFunc
 *   -----------------------
 *   <Hide Buttons>
 *
 *   -----------------------
 *   VisuMZ_2_PictureChoices
 *   -----------------------
 *   <Bind Picture: id>
 *   <Hide Choice Window>
 *
 *   ----------------------
 *   VisuMZ_3_ChoiceCmnEvts
 *   ----------------------
 *   <Choice Common Event: id>
 *
 *   -------------------
 *   VisuMZ_3_MessageLog
 *   -------------------
 *   <Bypass Message Log>
 *
 *   ----------------------
 *   VisuMZ_3_MessageSounds
 *   ----------------------
 *   <Letter Sound On>
 *   <Letter Sound Off>
 *   \LetterSoundName<filename>
 *   \LetterSoundVolume[x]
 *   \LetterSoundPitch[x]
 *   \LetterSoundPan[x]
 *   \LetterSoundVolumeVar[x]
 *   \LetterSoundPitchVar[x]
 *   \LetterSoundPanVar[x]
 *   \LSON
 *   \LSOFF
 *   \LSN<filename>
 *   \LSV[x]
 *   \LSPI[x]
 *   \LSPA[x]
 *   \LSVV[x]
 *   \LSPIV[x]
 *   \LSPAV[x]
 *
 *   ------------------------
 *   VisuMZ_4_EventTitleScene
 *   ------------------------
 *   <Continue>
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_2_ExtMessageFunc
 *
 * The Extended Message Functionality plugin enables the "Log" button found in
 * the Button Console to let the player go and review the text that has been
 * displayed in the map scene. This does not include the text found in battle
 * to avoid conflicting logged messages across different situations.
 *
 * ---
 *
 * ============================================================================
 * Available Text Codes
 * ============================================================================
 *
 * The following are text codes that you may use with this plugin.
 *
 * === Type-Related Text Codes ===
 *
 * ---
 *
 * --------------------   -----------------------------------------------------
 * Text Code              Effect
 * --------------------   -----------------------------------------------------
 *
 * <Bypass Message Log>   Prevents the specific "Show Text" window from being
 *                        recorded into the Message Log.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Bypass Plugin Commands ===
 *
 * ---
 *
 * Bypass: Message Logging?
 * - Bypass message logging until turned off.
 *
 *   Bypass?:
 *   - Bypasses Message Logging until turned off.
 *
 * ---
 *
 * === System Plugin Commands ===
 *
 * ---
 *
 * System: Enable Message Log in Menu?
 * - Enables/disables Message Log menu inside the main menu.
 *
 *   Enable/Disable?:
 *   - Enables/disables Message Log menu inside the main menu.
 *
 * ---
 *
 * System: Show Message Log in Menu?
 * - Shows/hides Message Log menu inside the main menu.
 *
 *   Show/Hide?:
 *   - Shows/hides Message Log menu inside the main menu.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * General Settings for the Message Log.
 *
 * ---
 *
 * Settings
 *
 *   Entry Limit:
 *   - How many message entries will be stored before the game will start
 *     trimming them?
 *
 *   Shortcut Key:
 *   - This is the key used for opening the Message Log scene.
 *   - Does not work in battle!
 *
 *   Show Faces?
 *   - Show face graphics in the Message Log?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Main Menu Settings
 * ============================================================================
 *
 * Main Menu settings for Message Log.
 *
 * ---
 *
 * Settings
 *
 *   Command Name:
 *   - Name of the 'Message Log' option in the Main Menu.
 *
 *   Show in Main Menu?:
 *   - Add the 'Message Log' option to the Main Menu by default?
 *
 *   Enable in Main Menu?:
 *   - Enable the 'Message Log' option to the Main Menu by default?
 *   - This will be automatically disabled if there are no entries available.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Background Settings
 * ============================================================================
 *
 * Background settings for Scene_MessageLog.
 *
 * ---
 *
 * Settings
 *
 *   Snapshop Opacity:
 *   - Snapshot opacity for the scene.
 *
 *   Background 1:
 *   - Filename used for the bottom background image.
 *   - Leave empty if you don't wish to use one.
 *
 *   Background 2:
 *   - Filename used for the upper background image.
 *   - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Vocabulary Settings
 * ============================================================================
 *
 * These settings let you adjust the text displayed for this plugin.
 *
 * ---
 *
 * ExtMessageFunc
 *
 *   Button Name:
 *   - How is this option's text displayed in-game?
 *   - Requires VisuMZ_2_ExtMessageFunc!
 *
 * ---
 *
 * Button Assist Window
 *
 *   Slow Scroll:
 *   - Text used for slow scrolling.
 *
 *   Fast Scroll:
 *   - Text used for fast scrolling.
 *
 * ---
 *
 * Choice Window Logging
 *
 *   Text Format:
 *   - Text format for logging the selected choice text.
 *   - %1 - Selected Choice Text
 *
 *   Cancel:
 *   - Text used when cancel branch is selected.
 *
 * ---
 *
 * Number Input Logging
 *
 *   Text Format:
 *   - Text format for logging the inputted number value.
 *   - %1 - Number Value
 *
 * ---
 *
 * Event Item Logging
 *
 *   Text Format:
 *   - Text format for logging the selected event Item.
 *   - %1 - Selected Event Item Text
 *
 *   Name Format:
 *   - Text format for how item names are displayed.
 *   - %1 - Item Icon, %2 - Item Name
 *
 *   No Item:
 *   - Text used when no item is selected.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Window Settings
 * ============================================================================
 *
 * Window settings for Scene_MessageLog.
 *
 * ---
 *
 * Message Log Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Appearance
 *
 *   Speaker Name X:
 *   - What X coordinate do you want the speaker name to appear at?
 *
 * ---
 *
 * Color Lock
 *
 *   Choices:
 *   - Color lock the logged choices?
 *
 *   Number Inputs:
 *   - Color lock the logged Number Inputs?
 *
 *   Event Item:
 *   - Color lock the logged selected Event Item?
 *
 * ---
 *
 * Scrolling > Slow
 *
 *   Scroll Speed:
 *   - What speed will Up/Down scroll the window at?
 *   - Lower is slower. Higher is faster.
 *
 *   Sound Frequency:
 *   - How frequent will Up/Down scrolling make sounds?
 *   - Lower is quicker. Higher is later.
 *
 * ---
 *
 * Scrolling > Fast
 *
 *   Scroll Speed:
 *   - What speed will PageUp/PageDn scroll the window at?
 *   - Lower is slower. Higher is faster.
 *
 *   Sound Frequency:
 *   - How frequent will PageUp/PageDn scrolling make sounds?
 *   - Lower is quicker. Higher is later.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Irina
 * * Trihan
 * * Arisu
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.02: September 3, 2021
 * * Bug Fixes!
 * ** Fixed a crash pertaining to specific message windows that haven't
 *    declared a speaker name from an older RPG Maker version. Fix by Irina.
 *
 * Version 1.01: August 6, 2021
 * * Documentation Update!
 * ** Plugin URL now updated to most recent one.
 *
 * Version 1.00 Official Release Date: August 4, 2021
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command BypassMessageLogging
 * @text Bypass: Message Logging?
 * @desc Bypass message logging until turned off.
 *
 * @arg Bypass:eval
 * @text Bypass?
 * @type boolean
 * @on Bypass
 * @off Enable
 * @desc Bypasses Message Logging until turned off.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemEnableMessageLogMenu
 * @text System: Enable Message Log in Menu?
 * @desc Enables/disables Message Log menu inside the main menu.
 *
 * @arg Enable:eval
 * @text Enable/Disable?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enables/disables Message Log menu inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemShowMessageLogMenu
 * @text System: Show Message Log in Menu?
 * @desc Shows/hides Message Log menu inside the main menu.
 *
 * @arg Show:eval
 * @text Show/Hide?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Shows/hides Message Log menu inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MessageLog
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc General Settings for the Message Log.
 * @default {"EntryLimit:num":"50","ShortcutKey:str":"pageup","ShowFaces:eval":"true"}
 *
 * @param MainMenu:struct
 * @text Main Menu Settings
 * @type struct<MainMenu>
 * @desc Main Menu settings for Message Log.
 * @default {"Name:str":"Message Log","ShowMainMenu:eval":"true","EnableMainMenu:eval":"true"}
 *
 * @param BgSettings:struct
 * @text Background Settings
 * @type struct<BgSettings>
 * @desc Background settings for Scene_MessageLog.
 * @default {"SnapshotOpacity:num":"192","BgFilename1:str":"","BgFilename2:str":""}
 *
 * @param Vocab:struct
 * @text Vocabulary Settings
 * @type struct<Vocab>
 * @desc These settings let you adjust the text displayed for this plugin.
 * @default {"ExtMessageFunc":"","ButtonName:str":"LOG","ButtonAssist":"","SlowScroll:str":"Scroll","FastScroll:str":"Fast Scroll","ChoiceLogging":"","ChoiceFmt:str":"\\C[4]Choice >\\C[0] %1","ChoiceCancel:str":"Cancel","NumberLogging":"","NumberFmt:str":"\\C[4]Amount >\\C[0] %1","EventItemLogging":"","ItemFmt:str":"\\C[4]Choice >\\C[0] %1","ItemNameFmt:str":"%1%2","NoItem:str":"Nothing"}
 *
 * @param Window:struct
 * @text Window Settings
 * @type struct<Window>
 * @desc Window settings for Scene_MessageLog.
 * @default {"MessageLogWindow":"","MessageLogMenu_BgType:num":"0","MessageLogMenu_RectJS:func":"\"const wx = 0;\\nconst wy = this.mainAreaTop();\\nconst ww = Graphics.boxWidth;\\nconst wh = this.mainAreaHeight();\\n\\nreturn new Rectangle(wx, wy, ww, wh);\"","Appearance":"","SpeakerNameX:num":"128","ColorLock":"","ColorLockChoice:eval":"false","ColorLockNumber:eval":"true","ColorLockItem:eval":"true","Scrolling":"","Slow":"","SlowScrollSpeed:num":"8","SlowSoundFreq:num":"8","Fast":"","FastScrollSpeed:num":"32","FastSoundFreq:num":"4"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param EntryLimit:num
 * @text Entry Limit
 * @parent General
 * @type Number
 * @min 1
 * @max 999
 * @desc How many message entries will be stored before the game
 * will start trimming them?
 * @default 50
 *
 * @param ShortcutKey:str
 * @text Shortcut Key
 * @parent General
 * @type combo
 * @option none
 * @option tab
 * @option shift
 * @option control
 * @option pageup
 * @option pagedown
 * @desc This is the key used for opening the Message Log scene.
 * Does not work in battle!
 * @default pageup
 *
 * @param ShowFaces:eval
 * @text Show Faces?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show face graphics in the Message Log?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * MainMenu Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~MainMenu:
 *
 * @param Name:str
 * @text Command Name
 * @parent Options
 * @desc Name of the 'Message Log' option in the Main Menu.
 * @default Message Log
 *
 * @param ShowMainMenu:eval
 * @text Show in Main Menu?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Add the 'Message Log' option to the Main Menu by default?
 * @default true
 *
 * @param EnableMainMenu:eval
 * @text Enable in Main Menu?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable the 'Message Log' option to the Main Menu by default?
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Background Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~BgSettings:
 *
 * @param SnapshotOpacity:num
 * @text Snapshop Opacity
 * @type number
 * @min 0
 * @max 255
 * @desc Snapshot opacity for the scene.
 * @default 192
 *
 * @param BgFilename1:str
 * @text Background 1
 * @type file
 * @dir img/titles1/
 * @require 1
 * @desc Filename used for the bottom background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param BgFilename2:str
 * @text Background 2
 * @type file
 * @dir img/titles2/
 * @require 1
 * @desc Filename used for the upper background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 */
/* ----------------------------------------------------------------------------
 * Vocabulary Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Vocab:
 *
 * @param ExtMessageFunc
 *
 * @param ButtonName:str
 * @text Button Name
 * @parent ExtMessageFunc
 * @desc How is this option's text displayed in-game?
 * Requires VisuMZ_2_ExtMessageFunc!
 * @default LOG
 *
 * @param ButtonAssist
 * @text Button Assist Window
 *
 * @param SlowScroll:str
 * @text Slow Scroll
 * @parent ButtonAssist
 * @desc Text used for slow scrolling.
 * @default Scroll
 *
 * @param FastScroll:str
 * @text Fast Scroll
 * @parent ButtonAssist
 * @desc Text used for fast scrolling.
 * @default Fast Scroll
 *
 * @param ChoiceLogging
 * @text Choice Window Logging
 *
 * @param ChoiceFmt:str
 * @text Text Format
 * @parent ChoiceLogging
 * @desc Text format for logging the selected choice text.
 * %1 - Selected Choice Text
 * @default \C[4]Choice >\C[0] %1
 *
 * @param ChoiceCancel:str
 * @text Cancel
 * @parent ChoiceLogging
 * @desc Text used when cancel branch is selected.
 * @default Cancel
 *
 * @param NumberLogging
 * @text Number Input Logging
 *
 * @param NumberFmt:str
 * @text Text Format
 * @parent NumberLogging
 * @desc Text format for logging the inputted number value.
 * %1 - Number Value
 * @default \C[4]Amount >\C[0] %1
 *
 * @param EventItemLogging
 * @text Event Item Logging
 *
 * @param ItemFmt:str
 * @text Text Format
 * @parent EventItemLogging
 * @desc Text format for logging the selected event Item.
 * %1 - Selected Event Item Text
 * @default \C[4]Choice >\C[0] %1
 *
 * @param ItemNameFmt:str
 * @text Name Format
 * @parent EventItemLogging
 * @desc Text format for how item names are displayed.
 * %1 - Item Icon, %2 - Item Name
 * @default %1%2
 *
 * @param NoItem:str
 * @text No Item
 * @parent EventItemLogging
 * @desc Text used when no item is selected.
 * @default Nothing
 *
 */
/* ----------------------------------------------------------------------------
 * Window Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Window:
 *
 * @param MessageLogWindow
 * @text Message Log Window
 *
 * @param MessageLogMenu_BgType:num
 * @text Background Type
 * @parent MessageLogWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param MessageLogMenu_RectJS:func
 * @text JS: X, Y, W, H
 * @parent MessageLogWindow
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const wx = 0;\nconst wy = this.mainAreaTop();\nconst ww = Graphics.boxWidth;\nconst wh = this.mainAreaHeight();\n\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Appearance
 *
 * @param SpeakerNameX:num
 * @text Speaker Name X
 * @parent Appearance
 * @type Number
 * @min 0
 * @desc What X coordinate do you want the speaker name to appear at?
 * @default 128
 *
 * @param ColorLock
 * @text Color Lock
 *
 * @param ColorLockChoice:eval
 * @text Choices
 * @parent ColorLock
 * @type boolean
 * @on Color Lock
 * @off Don't Color Lock
 * @desc Color lock the logged choices?
 * @default false
 *
 * @param ColorLockNumber:eval
 * @text Number Inputs
 * @parent ColorLock
 * @type boolean
 * @on Color Lock
 * @off Don't Color Lock
 * @desc Color lock the logged Number Inputs?
 * @default true
 *
 * @param ColorLockItem:eval
 * @text Event Item
 * @parent ColorLock
 * @type boolean
 * @on Color Lock
 * @off Don't Color Lock
 * @desc Color lock the logged selected Event Item?
 * @default true
 *
 * @param Scrolling
 *
 * @param Slow
 * @parent Scrolling
 *
 * @param SlowScrollSpeed:num
 * @text Scroll Speed
 * @parent Slow
 * @type Number
 * @min 1
 * @desc What speed will Up/Down scroll the window at?
 * Lower is slower. Higher is faster.
 * @default 8
 *
 * @param SlowSoundFreq:num
 * @text Sound Frequency
 * @parent Slow
 * @type Number
 * @min 1
 * @desc How frequent will Up/Down scrolling make sounds?
 * Lower is quicker. Higher is later.
 * @default 8
 *
 * @param Fast
 * @parent Scrolling
 *
 * @param FastScrollSpeed:num
 * @text Scroll Speed
 * @parent Fast
 * @type Number
 * @min 1
 * @desc What speed will PageUp/PageDn scroll the window at?
 * Lower is slower. Higher is faster.
 * @default 32
 *
 * @param FastSoundFreq:num
 * @text Sound Frequency
 * @parent Fast
 * @type Number
 * @min 1
 * @desc How frequent will PageUp/PageDn scrolling make sounds?
 * Lower is quicker. Higher is later.
 * @default 4
 *
 */
//=============================================================================

const _0x5c0eeb = _0x2447;
(function (_0x4b1666, _0x2a7481) {
    const _0x2b7fc4 = _0x2447,
        _0x5c4dca = _0x4b1666();
    while (!![]) {
        try {
            const _0x2a6de8 =
                (parseInt(_0x2b7fc4(0x15b)) / 0x1) * (-parseInt(_0x2b7fc4(0x236)) / 0x2) +
                (parseInt(_0x2b7fc4(0x248)) / 0x3) * (-parseInt(_0x2b7fc4(0x1c7)) / 0x4) +
                (-parseInt(_0x2b7fc4(0x1d7)) / 0x5) * (parseInt(_0x2b7fc4(0x1e9)) / 0x6) +
                parseInt(_0x2b7fc4(0x227)) / 0x7 +
                (parseInt(_0x2b7fc4(0x18d)) / 0x8) * (parseInt(_0x2b7fc4(0x17d)) / 0x9) +
                -parseInt(_0x2b7fc4(0x1e0)) / 0xa +
                (-parseInt(_0x2b7fc4(0x182)) / 0xb) * (parseInt(_0x2b7fc4(0x1bb)) / 0xc);
            if (_0x2a6de8 === _0x2a7481) break;
            else _0x5c4dca['push'](_0x5c4dca['shift']());
        } catch (_0x38ca53) {
            _0x5c4dca['push'](_0x5c4dca['shift']());
        }
    }
})(_0x4943, 0x860dc);
var label = _0x5c0eeb(0x220),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x5c0eeb(0x162)](function (_0x4751e3) {
        const _0x46fcc0 = _0x5c0eeb;
        return (
            _0x4751e3[_0x46fcc0(0x219)] &&
            _0x4751e3[_0x46fcc0(0x1b3)][_0x46fcc0(0x168)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label][_0x5c0eeb(0x23d)] = VisuMZ[label][_0x5c0eeb(0x23d)] || {}),
    (VisuMZ[_0x5c0eeb(0x1cf)] = function (_0x136e40, _0x493af0) {
        const _0x2cf856 = _0x5c0eeb;
        for (const _0xd40553 in _0x493af0) {
            if (_0x2cf856(0x15e) !== _0x2cf856(0x22f)) {
                if (_0xd40553[_0x2cf856(0x13c)](/(.*):(.*)/i)) {
                    if (_0x2cf856(0x1f0) === _0x2cf856(0x1f0)) {
                        const _0x286c9e = String(RegExp['$1']),
                            _0x45a820 = String(RegExp['$2'])
                                [_0x2cf856(0x1b5)]()
                                [_0x2cf856(0x14d)]();
                        let _0x21b7f7, _0x3333c9, _0x3df4d8;
                        switch (_0x45a820) {
                            case 'NUM':
                                _0x21b7f7 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? Number(_0x493af0[_0xd40553])
                                        : 0x0;
                                break;
                            case _0x2cf856(0x23e):
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9[_0x2cf856(0x1e4)](_0x3b647d =>
                                        Number(_0x3b647d)
                                    )));
                                break;
                            case _0x2cf856(0x190):
                                _0x21b7f7 =
                                    _0x493af0[_0xd40553] !== '' ? eval(_0x493af0[_0xd40553]) : null;
                                break;
                            case _0x2cf856(0x1e7):
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9['map'](_0x554e71 => eval(_0x554e71))));
                                break;
                            case 'JSON':
                                _0x21b7f7 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553])
                                        : '';
                                break;
                            case _0x2cf856(0x1b4):
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9['map'](_0x267afc =>
                                        JSON[_0x2cf856(0x1e6)](_0x267afc)
                                    )));
                                break;
                            case _0x2cf856(0x20f):
                                _0x21b7f7 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? new Function(JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553]))
                                        : new Function(_0x2cf856(0x212));
                                break;
                            case _0x2cf856(0x193):
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON[_0x2cf856(0x1e6)](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9['map'](
                                        _0x7238fd => new Function(JSON['parse'](_0x7238fd))
                                    )));
                                break;
                            case 'STR':
                                _0x21b7f7 =
                                    _0x493af0[_0xd40553] !== '' ? String(_0x493af0[_0xd40553]) : '';
                                break;
                            case 'ARRAYSTR':
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON['parse'](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9[_0x2cf856(0x1e4)](_0x39b719 =>
                                        String(_0x39b719)
                                    )));
                                break;
                            case _0x2cf856(0x161):
                                ((_0x3df4d8 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON['parse'](_0x493af0[_0xd40553])
                                        : {}),
                                    (_0x21b7f7 = VisuMZ[_0x2cf856(0x1cf)]({}, _0x3df4d8)));
                                break;
                            case 'ARRAYSTRUCT':
                                ((_0x3333c9 =
                                    _0x493af0[_0xd40553] !== ''
                                        ? JSON['parse'](_0x493af0[_0xd40553])
                                        : []),
                                    (_0x21b7f7 = _0x3333c9[_0x2cf856(0x1e4)](_0x37dbe0 =>
                                        VisuMZ[_0x2cf856(0x1cf)](
                                            {},
                                            JSON[_0x2cf856(0x1e6)](_0x37dbe0)
                                        )
                                    )));
                                break;
                            default:
                                continue;
                        }
                        _0x136e40[_0x286c9e] = _0x21b7f7;
                    } else return _0xe72648[_0x2cf856(0x177)](this[_0x2cf856(0x231)], 0x1);
                }
            } else
                (_0x496c6e['ConvertParams'](_0x38357c, _0x4aaf42),
                    _0x1ea457[_0x2cf856(0x134)](_0x11ed32['Enable']));
        }
        return _0x136e40;
    }),
    (_0x85f718 => {
        const _0x17d387 = _0x5c0eeb,
            _0x34757e = _0x85f718[_0x17d387(0x11e)];
        for (const _0x37c511 of dependencies) {
            if (!Imported[_0x37c511]) {
                if (_0x17d387(0x17f) === _0x17d387(0x17f)) {
                    (alert(_0x17d387(0x13b)['format'](_0x34757e, _0x37c511)),
                        SceneManager[_0x17d387(0x1c6)]());
                    break;
                } else {
                    const _0x3cbf97 = this['getLatestMessageLogEntry']();
                    ((_0x3cbf97[_0x17d387(0x20c)] = _0x5bb727 || ''),
                        (_0x3cbf97[_0x17d387(0x213)] = _0x44dbfb || 0x0));
                }
            }
        }
        const _0xabc215 = _0x85f718['description'];
        if (_0xabc215[_0x17d387(0x13c)](/\[Version[ ](.*?)\]/i)) {
            if (_0x17d387(0x1ea) !== _0x17d387(0x1d0)) {
                const _0x28dfbe = Number(RegExp['$1']);
                _0x28dfbe !== VisuMZ[label][_0x17d387(0x1dd)] &&
                    (_0x17d387(0x18f) !== 'XiPjv'
                        ? (alert(_0x17d387(0x197)[_0x17d387(0x140)](_0x34757e, _0x28dfbe)),
                          SceneManager['exit']())
                        : ((_0x5c7be5[_0x17d387(0x138)]() || !_0x270afc[_0x17d387(0x1ee)]) &&
                              this['addMessageLogEntry'](![]),
                          _0x4371a6[_0x17d387(0x220)][_0x17d387(0x20b)]['call'](this)));
            } else
                (_0x1cb593['MessageLog'][_0x17d387(0x1b2)][_0x17d387(0x1a0)](this, _0x37647d),
                    _0xdf1813[_0x17d387(0x1b1)](_0xbe021f));
        }
        if (_0xabc215[_0x17d387(0x13c)](/\[Tier[ ](\d+)\]/i)) {
            const _0x4f683c = Number(RegExp['$1']);
            if (_0x4f683c < tier) {
                if (_0x17d387(0x21b) === _0x17d387(0x247)) {
                    this[_0x17d387(0x1fe)]['y'] += _0x57d81d;
                    let _0x418d9b = _0x223b7c[_0x17d387(0x177)](
                        0x0,
                        this[_0x17d387(0x231)] - this[_0x17d387(0x1ba)]
                    );
                    this['origin']['y'] = this[_0x17d387(0x1fe)]['y'][_0x17d387(0x14e)](
                        0x0,
                        _0x418d9b
                    );
                } else
                    (alert(_0x17d387(0x1fc)[_0x17d387(0x140)](_0x34757e, _0x4f683c, tier)),
                        SceneManager[_0x17d387(0x1c6)]());
            } else tier = Math[_0x17d387(0x177)](_0x4f683c, tier);
        }
        VisuMZ[_0x17d387(0x1cf)](VisuMZ[label][_0x17d387(0x23d)], _0x85f718[_0x17d387(0x143)]);
    })(pluginData),
    PluginManager[_0x5c0eeb(0x1d9)](pluginData['name'], _0x5c0eeb(0x13e), _0x104f2f => {
        const _0x1fd615 = _0x5c0eeb;
        (VisuMZ[_0x1fd615(0x1cf)](_0x104f2f, _0x104f2f),
            $gameSystem[_0x1fd615(0x1bc)](_0x104f2f[_0x1fd615(0x15c)]));
    }),
    PluginManager[_0x5c0eeb(0x1d9)](
        pluginData[_0x5c0eeb(0x11e)],
        'SystemEnableMessageLogMenu',
        _0x5d9c6a => {
            const _0x555c55 = _0x5c0eeb;
            (VisuMZ['ConvertParams'](_0x5d9c6a, _0x5d9c6a),
                $gameSystem[_0x555c55(0x134)](_0x5d9c6a[_0x555c55(0x173)]));
        }
    ),
    PluginManager[_0x5c0eeb(0x1d9)](pluginData[_0x5c0eeb(0x11e)], _0x5c0eeb(0x1d3), _0x524185 => {
        const _0x3ccf27 = _0x5c0eeb;
        (VisuMZ[_0x3ccf27(0x1cf)](_0x524185, _0x524185),
            $gameSystem[_0x3ccf27(0x195)](_0x524185['Show']));
    }),
    (TextManager[_0x5c0eeb(0x180)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x1e5)][_0x5c0eeb(0x224)]),
    (TextManager['MessageLogButtonName'] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)]['Vocab'][_0x5c0eeb(0x18e)]),
    (TextManager[_0x5c0eeb(0x11f)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x132)][_0x5c0eeb(0x222)]),
    (TextManager['MessageLogFastScroll'] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x132)][_0x5c0eeb(0x172)]),
    (TextManager['MessageLogChoiceListFmt'] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings']['Vocab']['ChoiceFmt']),
    (TextManager[_0x5c0eeb(0x147)] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x132)]['ChoiceCancel']),
    (TextManager['MessageLogNumberInputFmt'] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x132)][_0x5c0eeb(0x21e)]),
    (TextManager[_0x5c0eeb(0x169)] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x132)][_0x5c0eeb(0x17b)]),
    (TextManager[_0x5c0eeb(0x139)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)]['Vocab'][_0x5c0eeb(0x1d1)]),
    (TextManager[_0x5c0eeb(0x1ca)] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x132)]['NoItem']));
function _0x2447(_0x24aa72, _0x199478) {
    const _0x4943f8 = _0x4943();
    return (
        (_0x2447 = function (_0x2447eb, _0x55013d) {
            _0x2447eb = _0x2447eb - 0x11e;
            let _0x2acbec = _0x4943f8[_0x2447eb];
            return _0x2acbec;
        }),
        _0x2447(_0x24aa72, _0x199478)
    );
}
TextManager[_0x5c0eeb(0x238)] &&
    ((VisuMZ['MessageLog'][_0x5c0eeb(0x1c5)] = TextManager[_0x5c0eeb(0x238)]),
    (TextManager['msgButtonConsole'] = function (_0x1a3c8b) {
        const _0x30eb8d = _0x5c0eeb;
        if (['backlog', 'log'][_0x30eb8d(0x168)](_0x1a3c8b)) {
            if (_0x30eb8d(0x185) === _0x30eb8d(0x185)) return TextManager[_0x30eb8d(0x201)];
            else _0x4d9e0c = _0x25c381[_0x30eb8d(0x140)](_0x5e6fb6[_0x30eb8d(0x147)]);
        }
        return VisuMZ[_0x30eb8d(0x220)][_0x30eb8d(0x1c5)]['call'](this, _0x1a3c8b);
    }));
function _0x4943() {
    const _0x5cf80a = [
        'ColorLockNumber',
        'buttonAssistKey4',
        'call',
        'dKsbf',
        'getBackgroundOpacity',
        'bind',
        'XFYwE',
        'centerSprite',
        'EnableMainMenu',
        'mbCln',
        'item',
        'Game_System_initialize',
        'pagedown',
        'wEIHz',
        'setSpeakerName',
        'addMessageLogCommandAutomatically',
        'commandMessageLog',
        'isTriggered',
        'faceHeight',
        'setSpeakerToMessageLog',
        'Game_Message_setSpeakerName',
        'description',
        'ARRAYJSON',
        'toUpperCase',
        'addChild',
        'frameCount',
        'SHORTCUT_KEY',
        'MessageCore',
        'innerHeight',
        '40920BLZwps',
        'setBypassMessageLogging',
        '_backSprite1',
        'MessageLogMenu_RectJS',
        'setFaceToMessageLog',
        'outputWidth',
        'BgSettings',
        'isBypassMessageLogging',
        'forceNameColor',
        'MessageLogFastScroll',
        'TextManager_msgButtonConsole',
        'exit',
        '84PSrYkP',
        'createNewLoggedMessageEntry',
        'needsCancelButton',
        'MessageLogItemNothing',
        'HOpxi',
        'drawFace',
        'convertMessageLogVariableTextCodes',
        'lastGainedObjectName',
        'ConvertParams',
        'ANTem',
        'ItemNameFmt',
        'calculateTextHeight',
        'SystemShowMessageLogMenu',
        'processFastScroll',
        'choices',
        'scrollToTop',
        '5BGMzka',
        'changeTextColor',
        'registerCommand',
        'VisuMZ_1_MainMenuCore',
        'iPVGE',
        'isMessageLogCommandEnabled',
        'version',
        'Window_ChoiceList_callOkHandler',
        'drawMessageText',
        '2107410tcetOV',
        'down',
        'callOkHandler',
        'prototype',
        'map',
        'MainMenu',
        'parse',
        'ARRAYEVAL',
        'drawAllText',
        '731502gcxbcG',
        'YFhwQ',
        'scaleSprite',
        'messageLogWindowRect',
        'shown',
        'SCENE_MAP_ONLY',
        'VlKRg',
        'sKIIN',
        'processCursorMove',
        'Cvnna',
        '_scene',
        'refresh',
        'tJOit',
        '_messageLogWindow',
        'popScene',
        'height',
        'hASDx',
        'callMessageLog',
        'updateOrigin',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'isPressed',
        'origin',
        'convertMessageLogTextRemoval',
        'Game_Interpreter_command101',
        'MessageLogButtonName',
        'General',
        'ENTRY_LIMIT',
        'initialize',
        'SPEAKER_NAME_X',
        'create',
        'BgFilename1',
        'convertMessageLogTextReplacement',
        'PtlVQ',
        'currentExt',
        'Window_ChoiceList_callCancelHandler',
        'faceName',
        'COLOR_LOCK_NUMBER',
        'HOzdx',
        'FUNC',
        'shift',
        'JpSxL',
        'return\x200',
        'faceIndex',
        'FKvil',
        'DnPVP',
        'getInputMultiButtonStrings',
        'end',
        'SceneManager_push',
        'status',
        'ZePua',
        'euupE',
        'MessageLogMenu_BgType',
        'getRemovedMessageLogTextCodes',
        'NumberFmt',
        'setScrollAccel',
        'MessageLog',
        'buttonAssistText3',
        'SlowScroll',
        'SHOW_FACES',
        'Name',
        'speaker',
        'vLfwa',
        '6936447TUSddv',
        'Window_Base_preConvertEscapeCharacters',
        'addTextToMessageLog',
        'Window_NumberInput_processOk',
        'FAST_SCROLL_SPEED',
        'activate',
        'scrollToBottom',
        'resetFontSettings',
        'LbGev',
        'buttonAssistKey3',
        '_allTextHeight',
        'drawHorzLine',
        'addNewLoggedMessageEntry',
        'faceWidth',
        'push',
        '76992dzriFC',
        '<ColorLock>%1</ColorLock>',
        'msgButtonConsole',
        'UANYP',
        'AaaBP',
        'isMessageLogCommandVisible',
        'bitmap',
        'Settings',
        'ARRAYNUM',
        'processAllText',
        'getLatestMessageLogEntry',
        'SLOW_SOUND_FREQUENCY',
        'Window_MenuCommand_addOriginalCommands',
        'processSlowScroll',
        'lineHeight',
        'ZXXdE',
        'setBackgroundType',
        'yQyED',
        '5367Lvaxjq',
        'home',
        '_lineY',
        'name',
        'MessageLogScroll',
        'processOk',
        'BpueM',
        'Window_EventItem_onOk',
        'bXVnM',
        'messageBody',
        'Window_EventItem_onCancel',
        'constructor',
        'getLoggedMessages',
        'mainAreaHeight',
        'choiceCancelType',
        'onCancel',
        'add',
        'members',
        'createContents',
        'Window_Message_isTriggered',
        'convertMessageLogTextCodes',
        '_MessageLog_Bypass',
        'gUTbn',
        'Vocab',
        'isMainMenuMessageLogVisible',
        'setMainMenuMessageLogEnabled',
        'LDqBV',
        'FAST_SOUND_FREQUENCY',
        'createMessageLogWindow',
        'isSceneMap',
        'MessageLogItemNameFmt',
        'boxWidth',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'match',
        'callCancelHandler',
        'BypassMessageLogging',
        'tunOZ',
        'format',
        'initMessageLogMainMenu',
        'ColorLockItem',
        'parameters',
        'SLOW_SCROLL_SPEED',
        'mainAreaTop',
        'HORZ_LINE_THICKNESS',
        'MessageLogChoiceCancel',
        'SlowSoundFreq',
        '_forcedNameColor',
        'stringify',
        'COLOR_LOCK_ITEM',
        'LYhZd',
        'trim',
        'clamp',
        'adjustSprite',
        'Game_Message_add',
        'MessageLogNumberInputFmt',
        'DsTDe',
        'buttonAssistKey1',
        'VGxLT',
        'canCallMessageLog',
        '_isHotKeyPressed',
        'COLOR_LOCK_CHOICE',
        'addMessageLogEntry',
        'drawRect',
        'EntryLimit',
        '4AkAsjN',
        'Bypass',
        'convertMessageLogNameRemoval',
        'RGiDb',
        'resetTextColor',
        'innerWidth',
        'STRUCT',
        'filter',
        '_MessageLog_MainMenu',
        'addCommand',
        'Window',
        'initMessageLogSettings',
        'messageLog',
        'includes',
        'MessageLogEventItemFmt',
        'resetWordWrap',
        'ZnIUc',
        'playOkSound',
        'replace',
        'addWindow',
        'command101',
        'BgFilename2',
        'SnapshotOpacity',
        'FastScroll',
        'Enable',
        'enabled',
        'createTextState',
        '_backSprite2',
        'max',
        'addLoadListener',
        '_newMessageLogEntry',
        'hRKlk',
        'ItemFmt',
        'numItems',
        '3286233uaQwlg',
        '_loggedMessages',
        'iHwNH',
        'MessageLogMenuCommand',
        'clearNameColor',
        '2090DPmgMm',
        'isMainMenuMessageLogEnabled',
        'addMessageLogCommand',
        'EUoxK',
        'ShowMainMenu',
        'playCursorSound',
        'addOriginalCommands',
        'iconIndex',
        'textSizeEx',
        'Game_Message_setFaceImage',
        'Qhqex',
        '16TpGxxg',
        'ButtonName',
        'DsUDK',
        'EVAL',
        'setFaceImage',
        'ShowFaces',
        'ARRAYFUNC',
        'createCommandWindow',
        'setMainMenuMessageLogVisible',
        'prepareMessageLogFaces',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'setHandler',
        'bjEbs',
        'Scene_Menu_createCommandWindow',
        'outputHeight',
        'NameBoxWindowDefaultColor',
        'length',
    ];
    _0x4943 = function () {
        return _0x5cf80a;
    };
    return _0x4943();
}
((SceneManager[_0x5c0eeb(0x138)] = function () {
    const _0x161343 = _0x5c0eeb;
    return this[_0x161343(0x1f3)] && this[_0x161343(0x1f3)]['constructor'] === Scene_Map;
}),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x218)] = SceneManager['push']),
    (SceneManager[_0x5c0eeb(0x235)] = function (_0x2fee3a) {
        const _0x1d3225 = _0x5c0eeb;
        (_0x2fee3a === Scene_MessageLog &&
            ('xfHlj' !== _0x1d3225(0x152)
                ? this[_0x1d3225(0x196)]()
                : (_0x4615a6 = this['innerWidth'] - (_0x595a12 + 0x4))),
            VisuMZ[_0x1d3225(0x220)][_0x1d3225(0x218)][_0x1d3225(0x1a0)](this, _0x2fee3a));
    }),
    (SceneManager[_0x5c0eeb(0x196)] = function () {
        const _0x431864 = _0x5c0eeb,
            _0x41e85d = $gameSystem[_0x431864(0x127)]();
        for (const _0x16715f of _0x41e85d) {
            if (!_0x16715f) continue;
            const _0x4fc497 = _0x16715f[_0x431864(0x20c)];
            _0x4fc497 !== '' && ImageManager['loadFace'](_0x4fc497);
        }
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x1a9)] = Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x204)]),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x204)] = function () {
        const _0x1d98f3 = _0x5c0eeb;
        (VisuMZ[_0x1d98f3(0x220)][_0x1d98f3(0x1a9)][_0x1d98f3(0x1a0)](this),
            this[_0x1d98f3(0x141)](),
            this[_0x1d98f3(0x166)]());
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x141)] = function () {
        const _0x3bb596 = _0x5c0eeb;
        ((this[_0x3bb596(0x163)] = {
            shown: VisuMZ[_0x3bb596(0x220)]['Settings']['MainMenu'][_0x3bb596(0x186)],
            enabled: VisuMZ[_0x3bb596(0x220)][_0x3bb596(0x23d)][_0x3bb596(0x1e5)][_0x3bb596(0x1a6)],
        }),
            (this[_0x3bb596(0x130)] = ![]));
    }),
    (Game_System['prototype'][_0x5c0eeb(0x133)] = function () {
        const _0x27c79f = _0x5c0eeb;
        if (this[_0x27c79f(0x163)] === undefined) this[_0x27c79f(0x141)]();
        return this[_0x27c79f(0x163)][_0x27c79f(0x1ed)];
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x195)] = function (_0x3761f4) {
        const _0x5a8cd9 = _0x5c0eeb;
        if (this['_MessageLog_MainMenu'] === undefined) this[_0x5a8cd9(0x141)]();
        this[_0x5a8cd9(0x163)]['shown'] = _0x3761f4;
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x183)] = function () {
        const _0x1fe04d = _0x5c0eeb;
        if (this[_0x1fe04d(0x163)] === undefined) this[_0x1fe04d(0x141)]();
        if (this[_0x1fe04d(0x127)]()[_0x1fe04d(0x19d)] <= 0x0) return ![];
        return this['_MessageLog_MainMenu'][_0x1fe04d(0x174)];
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x134)] = function (_0x539660) {
        const _0x493db0 = _0x5c0eeb;
        if (this[_0x493db0(0x163)] === undefined) this[_0x493db0(0x141)]();
        this[_0x493db0(0x163)][_0x493db0(0x174)] = _0x539660;
    }),
    (Game_System['prototype'][_0x5c0eeb(0x1c2)] = function () {
        const _0x173b28 = _0x5c0eeb;
        if (this[_0x173b28(0x130)] === undefined) this['initMessageLogMainMenu']();
        return this[_0x173b28(0x130)];
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1bc)] = function (_0x30aebc) {
        const _0x47865c = _0x5c0eeb;
        if (this[_0x47865c(0x130)] === undefined) this['initMessageLogMainMenu']();
        this[_0x47865c(0x130)] = _0x30aebc;
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x166)] = function () {
        const _0xfb4592 = _0x5c0eeb;
        ((this['_loggedMessages'] = []), this[_0xfb4592(0x1c8)]());
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x127)] = function () {
        const _0xa05a6e = _0x5c0eeb;
        return (
            this['_loggedMessages'] === undefined && this[_0xa05a6e(0x166)](),
            this[_0xa05a6e(0x17e)]
        );
    }),
    (Game_System[_0x5c0eeb(0x1e3)]['createNewLoggedMessageEntry'] = function () {
        const _0x5bf7fb = _0x5c0eeb;
        this[_0x5bf7fb(0x179)] = { speaker: '', faceName: '', faceIndex: 0x0, messageBody: '' };
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x233)] = function () {
        const _0x5a4d4e = _0x5c0eeb,
            _0x84ab71 = this[_0x5a4d4e(0x127)](),
            _0x382024 = this[_0x5a4d4e(0x240)]();
        if (this[_0x5a4d4e(0x1c2)]()) return;
        _0x382024[_0x5a4d4e(0x124)] = _0x382024[_0x5a4d4e(0x124)] || '';
        if (_0x382024[_0x5a4d4e(0x124)][_0x5a4d4e(0x13c)](/<BYPASS MESSAGE LOG>/i)) return;
        if (_0x382024['messageBody']['trim']()[_0x5a4d4e(0x19d)] <= 0x0) return;
        const _0x386b29 = _0x84ab71[_0x84ab71[_0x5a4d4e(0x19d)] - 0x1];
        if (JSON[_0x5a4d4e(0x14a)](_0x382024) === JSON[_0x5a4d4e(0x14a)](_0x386b29)) return;
        _0x84ab71[_0x5a4d4e(0x235)](_0x382024);
        while (_0x84ab71[_0x5a4d4e(0x19d)] > Window_MessageLog[_0x5a4d4e(0x203)]) {
            'hASDx' === _0x5a4d4e(0x1f9)
                ? _0x84ab71[_0x5a4d4e(0x210)]()
                : this['processFastScroll'](![]);
        }
    }),
    (Game_System['prototype']['getLatestMessageLogEntry'] = function () {
        const _0x319ac6 = _0x5c0eeb;
        if (this[_0x319ac6(0x179)] === undefined) {
            if (_0x319ac6(0x16b) !== _0x319ac6(0x16b)) {
                if (this['_MessageLog_MainMenu'] === _0x4f25e7) this['initMessageLogMainMenu']();
                return this[_0x319ac6(0x163)][_0x319ac6(0x1ed)];
            } else this[_0x319ac6(0x1c8)]();
        }
        return this['_newMessageLogEntry'];
    }),
    (Game_System[_0x5c0eeb(0x1e3)]['addTextToMessageLog'] = function (_0x3c5c98) {
        const _0x296df7 = _0x5c0eeb,
            _0x224aba = this[_0x296df7(0x240)]();
        _0x3c5c98 = this['convertMessageLogTextCodes'](_0x3c5c98);
        if (_0x224aba['messageBody']['length'] > 0x0) _0x224aba[_0x296df7(0x124)] += '\x0a';
        _0x224aba[_0x296df7(0x124)] += _0x3c5c98;
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x12f)] = function (_0x6742a7) {
        const _0x4f8218 = _0x5c0eeb;
        return (
            (_0x6742a7 = this[_0x4f8218(0x1cd)](_0x6742a7)),
            (_0x6742a7 = this[_0x4f8218(0x208)](_0x6742a7)),
            (_0x6742a7 = this['convertMessageLogTextRemoval'](_0x6742a7)),
            (_0x6742a7 = this[_0x4f8218(0x1cd)](_0x6742a7)),
            _0x6742a7
        );
    }),
    (Game_System['prototype'][_0x5c0eeb(0x1cd)] = function (_0x30f95f) {
        const _0x2038a5 = _0x5c0eeb;
        while (_0x30f95f['match'](/\\V\[(\d+)\]/gi)) {
            _0x30f95f = _0x30f95f[_0x2038a5(0x16d)](/\\V\[(\d+)\]/gi, (_0x7eaf60, _0x5401df) =>
                $gameVariables['value'](parseInt(_0x5401df))
            );
        }
        return _0x30f95f;
    }),
    (Game_System['prototype'][_0x5c0eeb(0x208)] = function (_0x18d9bd) {
        const _0x5877bd = _0x5c0eeb,
            _0x4da849 = this[_0x5877bd(0x240)]();
        return (
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\ItemQuantity\[(\d+)\]/gi,
                (_0x5814a7, _0x2630d7) =>
                    $gameParty['numItems']($dataItems[Number(_0x2630d7)]) || 0x0
            )),
            (_0x18d9bd = _0x18d9bd['replace'](
                /\\WeaponQuantity\[(\d+)\]/gi,
                (_0x445726, _0x274efc) =>
                    $gameParty['numItems']($dataWeapons[Number(_0x274efc)]) || 0x0
            )),
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\ArmorQuantity\[(\d+)\]/gi,
                (_0x3803f3, _0x46ceeb) =>
                    $gameParty[_0x5877bd(0x17c)]($dataArmors[Number(_0x46ceeb)]) || 0x0
            )),
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\ArmorQuantity\[(\d+)\]/gi,
                (_0x52de8e, _0x13e6c7) =>
                    $gameParty[_0x5877bd(0x17c)]($dataArmors[Number(_0x13e6c7)]) || 0x0
            )),
            (_0x18d9bd = _0x18d9bd['replace'](
                /\\LastGainObjQuantity/gi,
                Window_Base[_0x5877bd(0x1e3)]['lastGainedObjectQuantity']()
            )),
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\LastGainObjName/gi,
                Window_Base[_0x5877bd(0x1e3)]['lastGainedObjectName'](![])
            )),
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\LastGainObj/gi,
                Window_Base[_0x5877bd(0x1e3)][_0x5877bd(0x1ce)](!![])
            )),
            (_0x18d9bd = _0x18d9bd[_0x5877bd(0x16d)](
                /\\ActorFace\[(\d+)\]/gi,
                (_0x58fb2d, _0x3159d2) => {
                    const _0x453201 = _0x5877bd;
                    if (_0x453201(0x214) !== 'erSqk') {
                        const _0x186fc8 = $gameActors['actor'](Number(_0x3159d2));
                        return (
                            _0x186fc8 &&
                                ((_0x4da849['faceName'] = _0x186fc8[_0x453201(0x20c)]()),
                                (_0x4da849[_0x453201(0x213)] = _0x186fc8['faceIndex']())),
                            ''
                        );
                    } else return (this[_0x453201(0x1fa)](), ![]);
                }
            )),
            (_0x18d9bd = _0x18d9bd['replace'](/\\PartyFace\[(\d+)\]/gi, (_0x3621eb, _0x46a4e2) => {
                const _0x4b64b3 = _0x5877bd;
                if (_0x4b64b3(0x1ab) !== _0x4b64b3(0x17a)) {
                    const _0x3a1159 = $gameParty[_0x4b64b3(0x12c)]()[Number(_0x46a4e2) - 0x1];
                    return (
                        _0x3a1159 &&
                            ((_0x4da849[_0x4b64b3(0x20c)] = _0x3a1159['faceName']()),
                            (_0x4da849[_0x4b64b3(0x213)] = _0x3a1159['faceIndex']())),
                        ''
                    );
                } else
                    return this[_0x4b64b3(0x1f3)] && this['_scene'][_0x4b64b3(0x126)] === _0x3f4f38;
            })),
            _0x18d9bd
        );
    }),
    (Game_System['prototype']['convertMessageLogTextRemoval'] = function (_0x1abec3) {
        const _0x3d4cd9 = _0x5c0eeb,
            _0x3c81ba = this[_0x3d4cd9(0x21d)]();
        for (const _0x56ce11 of _0x3c81ba) {
            _0x1abec3 = _0x1abec3[_0x3d4cd9(0x16d)](_0x56ce11, '');
        }
        return _0x1abec3;
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x21d)] = function () {
        const _0x6cff60 = _0x5c0eeb;
        let _0x141a48 = [];
        return (
            _0x141a48['push'](/\\$/gi, /\\\./gi, /\\\|/gi, /\\\!/gi),
            _0x141a48['push'](/\\>/gi, /\\</gi, /\\\^/gi),
            _0x141a48[_0x6cff60(0x235)](/\\(?:Picture|CenterPicture)<(.*?)>/gi),
            _0x141a48['push'](/\\COMMONEVENT\[(\d+)\]>/gi, /\\WAIT\[(\d+)\]/gi),
            _0x141a48['push'](
                /<(?:AUTO|AUTOSIZE|AUTO SIZE)>/gi,
                /<(?:AUTOWIDTH|AUTO WIDTH)>/gi,
                /<(?:AUTOHEIGHT|AUTO HEIGHT)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /<(?:AUTOACTOR|AUTO ACTOR):[ ](.*?)>/gi,
                /<(?:AUTOPARTY|AUTO PARTY):[ ](.*?)>/gi,
                /<(?:AUTOENEMY|AUTO ENEMY):[ ](.*?)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /<(?:AUTOPLAYER|AUTO PLAYER)>/gi,
                /<(?:AUTOEVENT|AUTO EVENT):[ ](.*?)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /<SHOW>/gi,
                /<SHOW[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<SHOW ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<SHOW ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /<HIDE>/gi,
                /<HIDE[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<HIDE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<HIDE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi
            ),
            _0x141a48['push'](
                /<ENABLE>/gi,
                /<ENABLE[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<ENABLE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<ENABLE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi
            ),
            _0x141a48['push'](
                /<DISABLE>/gi,
                /<DISABLE[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<DISABLE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi,
                /<DISABLE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(.*?)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](/\\NormalBG/gi, /\\DimBG/gi, /\\TransparentBG/gi),
            _0x141a48['push'](
                /<POSITION:[ ]*(.*)>/gi,
                /<COORDINATES:[ ]*(.*)>/gi,
                /<DIMENSIONS:[ ]*(.*)>/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /\\(?:WindowMoveTo|WindowMoveBy):[ ]*(.*?)/gi,
                /\\WindowReset/gi
            ),
            _0x141a48[_0x6cff60(0x235)](/\\(?:TroopMember|TroopNameMember)\[(\d+)\]/gi),
            _0x141a48[_0x6cff60(0x235)](/\\ChangeFace<(.*?)>/gi, /\\FaceIndex\[(\d+)\]/gi),
            _0x141a48['push'](/<HIDE (?:BUTTON CONSOLE|CONSOLE|BUTTONS)>/gi),
            _0x141a48[_0x6cff60(0x235)](
                /<HIDE CHOICE WINDOW>/gi,
                /<BIND (?:PICTURE|PICTURES):[ ](\d+)>/gi
            ),
            _0x141a48['push'](/<(?:CHOICE|SELECT) (?:COMMON EVENT|EVENT|COMMONEVENT):[ ](\d+)>/gi),
            _0x141a48[_0x6cff60(0x235)](
                /\\(?:LSON|LSOFF|LETTER SOUND ON|LETTERSOUNDON|LETTER SOUND OFF|LETTERSOUNDOFF)/gi
            ),
            _0x141a48[_0x6cff60(0x235)](/\\(?:LETTERSOUNDNAME|LSN)<(.*?)>/gi),
            _0x141a48['push'](/\\(?:LETTERSOUNDINTERVAL|LSI)\[(\d+)\]/gi),
            _0x141a48[_0x6cff60(0x235)](/\\(?:LETTERSOUNDVOLUME|LSV)\[(\d+)\]/gi),
            _0x141a48[_0x6cff60(0x235)](/\\(?:LETTERSOUNDPITCH|LSPI)\[(\d+)\]/gi),
            _0x141a48[_0x6cff60(0x235)](/\\(?:LETTERSOUNDPAN|LSPA)\[(\d+)\]/gi),
            _0x141a48[_0x6cff60(0x235)](
                /\\(?:LETTERSOUNDVOLUMEVARIANCE|LETTERSOUNDVOLUMEVAR|LSVV)\[(\d+)\]/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /\\(?:LETTERSOUNDPITCHVARIANCE|LETTERSOUNDPITCHVAR|LSPIV)\[(\d+)\]/gi
            ),
            _0x141a48[_0x6cff60(0x235)](
                /\\(?:LETTERSOUNDPANVARIANCE|LETTERSOUNDPANVAR|LSPAV)\[(\d+)\]/gi
            ),
            _0x141a48[_0x6cff60(0x235)](/<CONTINUE>/gi),
            _0x141a48
        );
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1b1)] = function (_0x17aae5) {
        const _0x1f9a4f = _0x5c0eeb,
            _0xfddc8b = this[_0x1f9a4f(0x240)]();
        ((_0x17aae5 = this[_0x1f9a4f(0x15d)](_0x17aae5)),
            (_0x17aae5 = this['convertMessageLogTextCodes'](_0x17aae5)),
            (_0xfddc8b[_0x1f9a4f(0x225)] = _0x17aae5 || ''));
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x15d)] = function (_0x4a5ee2) {
        const _0xf24b41 = _0x5c0eeb;
        if (!_0x4a5ee2) return '';
        const _0x1cde90 = [
            /<LEFT>/gi,
            /<CENTER>/gi,
            /<RIGHT>/gi,
            /<\/LEFT>/gi,
            /<\/CENTER>/gi,
            /<\/RIGHT>/gi,
            /<POSITION:[ ](\d+)>/gi,
        ];
        for (const _0x13f830 of _0x1cde90) {
            if (_0xf24b41(0x199) !== _0xf24b41(0x14c))
                _0x4a5ee2 = _0x4a5ee2[_0xf24b41(0x16d)](_0x13f830, '');
            else {
                if (this[_0xf24b41(0x130)] === _0x5f2d9e) this[_0xf24b41(0x141)]();
                this['_MessageLog_Bypass'] = _0x4d9122;
            }
        }
        return _0x4a5ee2;
    }),
    (Game_System[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1bf)] = function (_0x3f0361, _0x1246fb) {
        const _0x26ac1e = _0x5c0eeb,
            _0x46610e = this['getLatestMessageLogEntry']();
        ((_0x46610e[_0x26ac1e(0x20c)] = _0x3f0361 || ''),
            (_0x46610e[_0x26ac1e(0x213)] = _0x1246fb || 0x0));
    }),
    (VisuMZ['MessageLog'][_0x5c0eeb(0x150)] = Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x12b)]),
    (Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x12b)] = function (_0x7406f4) {
        const _0x51a70d = _0x5c0eeb;
        (VisuMZ[_0x51a70d(0x220)][_0x51a70d(0x150)][_0x51a70d(0x1a0)](this, _0x7406f4),
            $gameSystem[_0x51a70d(0x229)](_0x7406f4));
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x1b2)] = Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1ac)]),
    (Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1ac)] = function (_0x4f2119) {
        const _0x230b32 = _0x5c0eeb;
        (VisuMZ[_0x230b32(0x220)][_0x230b32(0x1b2)][_0x230b32(0x1a0)](this, _0x4f2119),
            $gameSystem[_0x230b32(0x1b1)](_0x4f2119));
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x18b)] = Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x191)]),
    (Game_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x191)] = function (_0x4a0852, _0x2940b2) {
        const _0x47f3ae = _0x5c0eeb;
        (VisuMZ[_0x47f3ae(0x220)]['Game_Message_setFaceImage']['call'](this, _0x4a0852, _0x2940b2),
            $gameSystem[_0x47f3ae(0x1bf)](_0x4a0852, _0x2940b2));
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x200)] = Game_Interpreter['prototype'][_0x5c0eeb(0x16f)]),
    (Game_Interpreter[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x16f)] = function (_0x125805) {
        const _0x22f476 = _0x5c0eeb;
        if (SceneManager[_0x22f476(0x138)]() || !Window_MessageLog[_0x22f476(0x1ee)]) {
            if (_0x22f476(0x245) === 'ZXXdE') $gameSystem[_0x22f476(0x1c8)]();
            else {
                let _0x223e93 = _0x58bd09,
                    _0x3a1bef = this[_0x22f476(0x24a)],
                    _0x463d18 = _0x489e92[_0x22f476(0x234)],
                    _0x33b9c5 = _0x48d14c[_0x22f476(0x1b0)];
                (this[_0x22f476(0x1cc)](
                    _0x43b52d[_0x22f476(0x20c)],
                    _0x834107[_0x22f476(0x213)],
                    _0x223e93,
                    _0x3a1bef,
                    _0x463d18,
                    _0x33b9c5
                ),
                    _0x399172['boxWidth'] <= 0x330 &&
                        (_0x3b3408 = this['innerWidth'] - (_0x405b31 + 0x4)));
            }
        }
        let _0x128485 = VisuMZ['MessageLog'][_0x22f476(0x200)][_0x22f476(0x1a0)](this, _0x125805);
        return (
            (SceneManager[_0x22f476(0x138)]() || !Window_MessageLog['SCENE_MAP_ONLY']) &&
                (_0x22f476(0x1ef) !== _0x22f476(0x1ef)
                    ? (this[_0x22f476(0x1c3)](),
                      this['drawTextEx'](
                          _0x1b1cf1[_0x22f476(0x225)],
                          _0x30a767[_0x22f476(0x205)],
                          this['_lineY'],
                          _0x5f3b14
                      ),
                      (this[_0x22f476(0x24a)] += this[_0x22f476(0x18a)](
                          _0x1f14ba[_0x22f476(0x225)]
                      )[_0x22f476(0x1f8)]),
                      this[_0x22f476(0x181)]())
                    : $gameSystem['addNewLoggedMessageEntry']()),
            _0x128485
        );
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x19a)] = Scene_Menu[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x194)]),
    (Scene_Menu['prototype'][_0x5c0eeb(0x194)] = function () {
        const _0x362240 = _0x5c0eeb;
        VisuMZ[_0x362240(0x220)]['Scene_Menu_createCommandWindow'][_0x362240(0x1a0)](this);
        const _0x1a6f87 = this['_commandWindow'];
        _0x1a6f87[_0x362240(0x198)](
            _0x362240(0x167),
            this[_0x362240(0x1ae)][_0x362240(0x1a3)](this)
        );
    }),
    (Scene_Menu[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1ae)] = function () {
        const _0x2795b3 = _0x5c0eeb;
        SceneManager[_0x2795b3(0x235)](Scene_MessageLog);
    }));
function Scene_MessageLog() {
    this['initialize'](...arguments);
}
((Scene_MessageLog[_0x5c0eeb(0x1e3)] = Object[_0x5c0eeb(0x206)](Scene_MenuBase['prototype'])),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x126)] = Scene_MessageLog),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x204)] = function () {
        const _0x5aa711 = _0x5c0eeb;
        Scene_MenuBase[_0x5aa711(0x1e3)][_0x5aa711(0x204)][_0x5aa711(0x1a0)](this);
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)]['helpAreaHeight'] = function () {
        return 0x0;
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)]['create'] = function () {
        const _0x5efccd = _0x5c0eeb;
        (Scene_MenuBase[_0x5efccd(0x1e3)][_0x5efccd(0x206)][_0x5efccd(0x1a0)](this),
            this['createMessageLogWindow']());
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x137)] = function () {
        const _0x22adff = _0x5c0eeb,
            _0x4d9944 = this[_0x22adff(0x1ec)]();
        ((this[_0x22adff(0x1f6)] = new Window_MessageLog(_0x4d9944)),
            this[_0x22adff(0x16e)](this[_0x22adff(0x1f6)]),
            this[_0x22adff(0x1f6)][_0x22adff(0x198)](
                'cancel',
                this[_0x22adff(0x1f7)][_0x22adff(0x1a3)](this)
            ),
            this[_0x22adff(0x1f6)][_0x22adff(0x246)](
                VisuMZ[_0x22adff(0x220)]['Settings'][_0x22adff(0x165)][_0x22adff(0x21c)]
            ));
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1ec)] = function () {
        const _0x128637 = _0x5c0eeb,
            _0xf19af9 = VisuMZ[_0x128637(0x220)]['Settings'][_0x128637(0x165)][_0x128637(0x1be)];
        if (_0xf19af9) return _0xf19af9[_0x128637(0x1a0)](this);
        const _0x40f6f9 = 0x0,
            _0x36621e = this[_0x128637(0x145)](),
            _0x31c7ee = Graphics[_0x128637(0x13a)],
            _0x1c0423 = this[_0x128637(0x128)]();
        return new Rectangle(_0x40f6f9, _0x36621e, _0x31c7ee, _0x1c0423);
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)]['createBackground'] = function () {
        const _0x47a7da = _0x5c0eeb;
        (Scene_MenuBase['prototype']['createBackground'][_0x47a7da(0x1a0)](this),
            this['setBackgroundOpacity'](this[_0x47a7da(0x1a2)]()),
            this['createCustomBackgroundImages']());
    }),
    (Scene_MessageLog['prototype'][_0x5c0eeb(0x1a2)] = function () {
        const _0x5214b6 = _0x5c0eeb;
        return VisuMZ[_0x5214b6(0x220)][_0x5214b6(0x23d)]['BgSettings'][_0x5214b6(0x171)];
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)]['createCustomBackgroundImages'] = function () {
        const _0x262084 = _0x5c0eeb,
            _0x2e5a4e = VisuMZ[_0x262084(0x220)]['Settings'][_0x262084(0x1c1)];
        _0x2e5a4e &&
            (_0x2e5a4e[_0x262084(0x207)] !== '' || _0x2e5a4e['BgFilename2'] !== '') &&
            ((this[_0x262084(0x1bd)] = new Sprite(
                ImageManager['loadTitle1'](_0x2e5a4e[_0x262084(0x207)])
            )),
            (this['_backSprite2'] = new Sprite(
                ImageManager['loadTitle2'](_0x2e5a4e[_0x262084(0x170)])
            )),
            this[_0x262084(0x1b6)](this[_0x262084(0x1bd)]),
            this[_0x262084(0x1b6)](this[_0x262084(0x176)]),
            this[_0x262084(0x1bd)][_0x262084(0x23c)]['addLoadListener'](
                this[_0x262084(0x14f)][_0x262084(0x1a3)](this, this[_0x262084(0x1bd)])
            ),
            this[_0x262084(0x176)][_0x262084(0x23c)][_0x262084(0x178)](
                this[_0x262084(0x14f)][_0x262084(0x1a3)](this, this[_0x262084(0x176)])
            ));
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x14f)] = function (_0xaedcfa) {
        const _0xf7214d = _0x5c0eeb;
        (this[_0xf7214d(0x1eb)](_0xaedcfa), this[_0xf7214d(0x1a5)](_0xaedcfa));
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x153)] = function () {
        const _0x55dd10 = _0x5c0eeb;
        return TextManager['getInputMultiButtonStrings']('pageup', _0x55dd10(0x1aa));
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x230)] = function () {
        const _0x459b0b = _0x5c0eeb;
        return TextManager['getInputMultiButtonStrings']('up', _0x459b0b(0x1e1));
    }),
    (Scene_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x19f)] = function () {
        return '';
    }),
    (Scene_MessageLog['prototype']['buttonAssistText1'] = function () {
        const _0x2cfeb6 = _0x5c0eeb;
        return TextManager[_0x2cfeb6(0x1c4)];
    }),
    (Scene_MessageLog['prototype'][_0x5c0eeb(0x221)] = function () {
        const _0x44488f = _0x5c0eeb;
        return TextManager[_0x44488f(0x11f)];
    }),
    (VisuMZ[_0x5c0eeb(0x220)]['Window_MenuCommand_addOriginalCommands'] =
        Window_MenuCommand[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x188)]),
    (Window_MenuCommand['prototype'][_0x5c0eeb(0x188)] = function () {
        const _0x1b9af0 = _0x5c0eeb;
        (VisuMZ['MessageLog'][_0x1b9af0(0x242)][_0x1b9af0(0x1a0)](this),
            this['addMessageLogCommand']());
    }),
    (Window_MenuCommand['prototype'][_0x5c0eeb(0x184)] = function () {
        const _0x5d9917 = _0x5c0eeb;
        if (!this[_0x5d9917(0x1ad)]()) return;
        if (!this[_0x5d9917(0x23b)]()) return;
        const _0x478125 = TextManager[_0x5d9917(0x180)],
            _0xe8762c = this[_0x5d9917(0x1dc)]();
        this['addCommand'](_0x478125, _0x5d9917(0x167), _0xe8762c);
    }),
    (Window_MenuCommand[_0x5c0eeb(0x1e3)]['addMessageLogCommandAutomatically'] = function () {
        const _0x180530 = _0x5c0eeb;
        return Imported[_0x180530(0x1da)] ? ![] : !![];
    }),
    (Window_MenuCommand['prototype']['isMessageLogCommandVisible'] = function () {
        return $gameSystem['isMainMenuMessageLogVisible']();
    }),
    (Window_MenuCommand[_0x5c0eeb(0x1e3)]['isMessageLogCommandEnabled'] = function () {
        const _0x5328ed = _0x5c0eeb;
        return $gameSystem[_0x5328ed(0x183)]();
    }),
    (VisuMZ['MessageLog'][_0x5c0eeb(0x1de)] =
        Window_ChoiceList[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1e2)]),
    (Window_ChoiceList[_0x5c0eeb(0x1e3)]['callOkHandler'] = function () {
        const _0x34a6b7 = _0x5c0eeb;
        if (SceneManager[_0x34a6b7(0x138)]() || !Window_MessageLog[_0x34a6b7(0x1ee)]) {
            if (_0x34a6b7(0x23a) === 'XxMwF')
                return _0x10a075[_0x34a6b7(0x216)]('up', _0x34a6b7(0x1e1));
            else this[_0x34a6b7(0x158)](!![]);
        }
        VisuMZ[_0x34a6b7(0x220)][_0x34a6b7(0x1de)]['call'](this);
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x20b)] = Window_ChoiceList['prototype'][_0x5c0eeb(0x13d)]),
    (Window_ChoiceList[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x13d)] = function () {
        const _0x7d3b4f = _0x5c0eeb;
        ((SceneManager[_0x7d3b4f(0x138)]() || !Window_MessageLog['SCENE_MAP_ONLY']) &&
            this[_0x7d3b4f(0x158)](![]),
            VisuMZ[_0x7d3b4f(0x220)][_0x7d3b4f(0x20b)][_0x7d3b4f(0x1a0)](this));
    }),
    (Window_ChoiceList[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x158)] = function (_0x5a96a6) {
        const _0xea5909 = _0x5c0eeb;
        $gameSystem[_0xea5909(0x1c8)]();
        const _0x57c281 = TextManager['MessageLogChoiceListFmt'];
        let _0x30193f = '';
        if (_0x5a96a6) {
            let _0x134d24 = this[_0xea5909(0x20a)](),
                _0x1cb2d8 = $gameMessage[_0xea5909(0x1d5)]()[_0x134d24];
            if (Window_MessageLog[_0xea5909(0x157)]) {
                if (_0xea5909(0x20e) === 'HOzdx')
                    _0x1cb2d8 = '<ColorLock>%1</ColorLock>'[_0xea5909(0x140)](_0x1cb2d8);
                else return '';
            }
            _0x30193f = _0x57c281['format'](_0x1cb2d8);
        } else {
            if (!_0x5a96a6 && !this[_0xea5909(0x1c9)]()) {
                let _0x1fe93b = $gameMessage[_0xea5909(0x129)](),
                    _0x1ccf96 = $gameMessage['choices']()[_0x1fe93b];
                (Window_MessageLog[_0xea5909(0x157)] &&
                    (_0xea5909(0x1cb) === 'HOpxi'
                        ? (_0x1ccf96 = '<ColorLock>%1</ColorLock>'['format'](_0x1ccf96))
                        : (this[_0xea5909(0x1eb)](_0xa9b8f4), this[_0xea5909(0x1a5)](_0x1d8942))),
                    (_0x30193f = _0x57c281[_0xea5909(0x140)](_0x1ccf96)));
            } else _0x30193f = _0x57c281[_0xea5909(0x140)](TextManager[_0xea5909(0x147)]);
        }
        ($gameSystem[_0xea5909(0x229)](_0x30193f), $gameSystem['addNewLoggedMessageEntry']());
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x22a)] =
        Window_NumberInput[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x120)]),
    (Window_NumberInput[_0x5c0eeb(0x1e3)]['processOk'] = function () {
        const _0x4e9c59 = _0x5c0eeb;
        if (SceneManager['isSceneMap']() || !Window_MessageLog['SCENE_MAP_ONLY']) {
            if (_0x4e9c59(0x131) !== 'gUTbn')
                return (
                    (_0x211803 = this['convertMessageLogVariableTextCodes'](_0x1c86bc)),
                    (_0x30a28f = this[_0x4e9c59(0x208)](_0x5874c2)),
                    (_0x4f212d = this[_0x4e9c59(0x1ff)](_0x228498)),
                    (_0x40d2dd = this['convertMessageLogVariableTextCodes'](_0xca8d91)),
                    _0x31b46f
                );
            else this[_0x4e9c59(0x158)]();
        }
        VisuMZ[_0x4e9c59(0x220)][_0x4e9c59(0x22a)][_0x4e9c59(0x1a0)](this);
    }),
    (Window_NumberInput[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x158)] = function () {
        const _0x458e63 = _0x5c0eeb;
        $gameSystem[_0x458e63(0x1c8)]();
        const _0x36b523 = TextManager[_0x458e63(0x151)];
        let _0x27c348 = this['_number'];
        if (Window_MessageLog[_0x458e63(0x20d)]) {
            if (_0x458e63(0x21a) !== _0x458e63(0x154))
                _0x27c348 = _0x458e63(0x237)[_0x458e63(0x140)](_0x27c348);
            else return _0x39f7be;
        }
        let _0x490ce8 = _0x36b523[_0x458e63(0x140)](_0x27c348);
        ($gameSystem[_0x458e63(0x229)](_0x490ce8), $gameSystem[_0x458e63(0x233)]());
    }),
    (VisuMZ['MessageLog']['Window_EventItem_onOk'] = Window_EventItem[_0x5c0eeb(0x1e3)]['onOk']),
    (Window_EventItem[_0x5c0eeb(0x1e3)]['onOk'] = function () {
        const _0x33c7b5 = _0x5c0eeb;
        if (SceneManager['isSceneMap']() || !Window_MessageLog[_0x33c7b5(0x1ee)]) {
            if (_0x33c7b5(0x121) === _0x33c7b5(0x121))
                this[_0x33c7b5(0x158)](this[_0x33c7b5(0x1a8)]());
            else {
                if (!this[_0x33c7b5(0x1ad)]()) return;
                if (!this[_0x33c7b5(0x23b)]()) return;
                const _0x152f65 = _0x3a5e93[_0x33c7b5(0x180)],
                    _0x25b853 = this['isMessageLogCommandEnabled']();
                this[_0x33c7b5(0x164)](_0x152f65, 'messageLog', _0x25b853);
            }
        }
        VisuMZ[_0x33c7b5(0x220)][_0x33c7b5(0x122)][_0x33c7b5(0x1a0)](this);
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x125)] =
        Window_EventItem[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x12a)]),
    (Window_EventItem['prototype'][_0x5c0eeb(0x12a)] = function () {
        const _0x4da46d = _0x5c0eeb;
        if (SceneManager[_0x4da46d(0x138)]() || !Window_MessageLog[_0x4da46d(0x1ee)]) {
            if (_0x4da46d(0x1a7) === _0x4da46d(0x1a7)) this[_0x4da46d(0x158)](![]);
            else {
                if (!_0x9abee6) return '';
                const _0x1cfc39 = [
                    /<LEFT>/gi,
                    /<CENTER>/gi,
                    /<RIGHT>/gi,
                    /<\/LEFT>/gi,
                    /<\/CENTER>/gi,
                    /<\/RIGHT>/gi,
                    /<POSITION:[ ](\d+)>/gi,
                ];
                for (const _0x1133fa of _0x1cfc39) {
                    _0x4fe13b = _0x1d40ad['replace'](_0x1133fa, '');
                }
                return _0x370c25;
            }
        }
        VisuMZ['MessageLog']['Window_EventItem_onCancel'][_0x4da46d(0x1a0)](this);
    }),
    (Window_EventItem['prototype'][_0x5c0eeb(0x158)] = function (_0x43d00b) {
        const _0x47cb5a = _0x5c0eeb;
        $gameSystem[_0x47cb5a(0x1c8)]();
        const _0x322c9c = TextManager[_0x47cb5a(0x169)];
        let _0x5c738f = '';
        if (_0x43d00b) {
            if (_0x47cb5a(0x13f) !== _0x47cb5a(0x13f)) {
                const _0x367bc2 = this[_0x47cb5a(0x127)](),
                    _0x2d8ed6 = this['getLatestMessageLogEntry']();
                if (this[_0x47cb5a(0x1c2)]()) return;
                _0x2d8ed6[_0x47cb5a(0x124)] = _0x2d8ed6[_0x47cb5a(0x124)] || '';
                if (_0x2d8ed6[_0x47cb5a(0x124)]['match'](/<BYPASS MESSAGE LOG>/i)) return;
                if (_0x2d8ed6['messageBody']['trim']()[_0x47cb5a(0x19d)] <= 0x0) return;
                const _0x4a46bc = _0x367bc2[_0x367bc2['length'] - 0x1];
                if (_0x29d380['stringify'](_0x2d8ed6) === _0x58c383[_0x47cb5a(0x14a)](_0x4a46bc))
                    return;
                _0x367bc2[_0x47cb5a(0x235)](_0x2d8ed6);
                while (_0x367bc2[_0x47cb5a(0x19d)] > _0x3ad20a[_0x47cb5a(0x203)]) {
                    _0x367bc2[_0x47cb5a(0x210)]();
                }
            } else {
                let _0x1ff008 = TextManager[_0x47cb5a(0x139)],
                    _0x5a4486 = _0x1ff008['format'](
                        '\x5cI[%1]'[_0x47cb5a(0x140)](_0x43d00b[_0x47cb5a(0x189)]),
                        _0x43d00b['name']
                    );
                (Window_MessageLog[_0x47cb5a(0x14b)] &&
                    (_0x47cb5a(0x135) !== _0x47cb5a(0x215)
                        ? (_0x5a4486 = '<ColorLock>%1</ColorLock>'[_0x47cb5a(0x140)](_0x5a4486))
                        : _0x5565b9['addNewLoggedMessageEntry']()),
                    (_0x5c738f = _0x322c9c[_0x47cb5a(0x140)](_0x5a4486)));
            }
        } else
            'cQrfw' === _0x47cb5a(0x18c)
                ? ((this[_0x47cb5a(0x156)] = _0x586673[_0x47cb5a(0x1fd)](
                      _0x50293d[_0x47cb5a(0x1b8)]
                  )),
                  _0x31e6d8[_0x47cb5a(0x1e3)][_0x47cb5a(0x204)][_0x47cb5a(0x1a0)](this, _0x31d2b3),
                  (this['_allTextHeight'] = 0x0),
                  this[_0x47cb5a(0x1f4)](),
                  this[_0x47cb5a(0x22c)]())
                : (_0x5c738f = _0x322c9c[_0x47cb5a(0x140)](TextManager['MessageLogItemNothing']));
        ($gameSystem['addTextToMessageLog'](_0x5c738f), $gameSystem[_0x47cb5a(0x233)]());
    }),
    (VisuMZ['MessageLog']['Window_Base_preConvertEscapeCharacters'] =
        Window_Base[_0x5c0eeb(0x1e3)]['preConvertEscapeCharacters']),
    (Window_Base[_0x5c0eeb(0x1e3)]['preConvertEscapeCharacters'] = function (_0x722d98) {
        const _0x4f6ed0 = _0x5c0eeb;
        return (
            (_0x722d98 = VisuMZ[_0x4f6ed0(0x220)][_0x4f6ed0(0x228)]['call'](this, _0x722d98)),
            (_0x722d98 = _0x722d98[_0x4f6ed0(0x16d)](/<BYPASS MESSAGE LOG>/i, '')),
            _0x722d98
        );
    }),
    (VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x12e)] =
        Window_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1af)]),
    (Window_Message[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1af)] = function () {
        const _0x4bf51b = _0x5c0eeb;
        let _0x3cd307 = VisuMZ[_0x4bf51b(0x220)][_0x4bf51b(0x12e)]['call'](this);
        if (
            this[_0x4bf51b(0x155)]() &&
            Input[_0x4bf51b(0x1af)](Window_MessageLog[_0x4bf51b(0x1b8)])
        )
            return (this[_0x4bf51b(0x1fa)](), ![]);
        else {
            if (_0x4bf51b(0x211) !== _0x4bf51b(0x211)) {
                if (this[_0x4bf51b(0x163)] === _0xa5b7b4) this[_0x4bf51b(0x141)]();
                this[_0x4bf51b(0x163)][_0x4bf51b(0x1ed)] = _0x590eae;
            } else return _0x3cd307;
        }
    }),
    (Window_Message[_0x5c0eeb(0x1e3)]['canCallMessageLog'] = function () {
        const _0x3f8a1e = _0x5c0eeb;
        return SceneManager[_0x3f8a1e(0x138)]() && $gameSystem[_0x3f8a1e(0x183)]();
    }),
    (Window_Message['prototype']['callMessageLog'] = function () {
        const _0x34ec81 = _0x5c0eeb;
        (this[_0x34ec81(0x16c)](), SceneManager[_0x34ec81(0x235)](Scene_MessageLog));
    }));
function Window_MessageLog() {
    const _0x1b99ab = _0x5c0eeb;
    this[_0x1b99ab(0x204)](...arguments);
}
((Window_MessageLog['prototype'] = Object[_0x5c0eeb(0x206)](Window_Selectable[_0x5c0eeb(0x1e3)])),
    (Window_MessageLog[_0x5c0eeb(0x1e3)]['constructor'] = Window_MessageLog),
    (Window_MessageLog['SCENE_MAP_ONLY'] = !![]),
    (Window_MessageLog[_0x5c0eeb(0x203)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x202)][_0x5c0eeb(0x15a)]),
    (Window_MessageLog[_0x5c0eeb(0x1b8)] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x202)]['ShortcutKey']),
    (Window_MessageLog[_0x5c0eeb(0x223)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)]['General'][_0x5c0eeb(0x192)] ?? !![]),
    (Window_MessageLog[_0x5c0eeb(0x146)] = 0x4),
    (Window_MessageLog[_0x5c0eeb(0x205)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x165)]['SpeakerNameX']),
    (Window_MessageLog['COLOR_LOCK_CHOICE'] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)]['Window']['ColorLockChoice']),
    (Window_MessageLog[_0x5c0eeb(0x20d)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x165)][_0x5c0eeb(0x19e)]),
    (Window_MessageLog[_0x5c0eeb(0x14b)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x165)][_0x5c0eeb(0x142)]),
    (Window_MessageLog[_0x5c0eeb(0x144)] =
        VisuMZ['MessageLog'][_0x5c0eeb(0x23d)][_0x5c0eeb(0x165)]['SlowScrollSpeed']),
    (Window_MessageLog[_0x5c0eeb(0x22b)] =
        VisuMZ['MessageLog']['Settings'][_0x5c0eeb(0x165)]['FastScrollSpeed']),
    (Window_MessageLog[_0x5c0eeb(0x241)] =
        VisuMZ[_0x5c0eeb(0x220)]['Settings'][_0x5c0eeb(0x165)][_0x5c0eeb(0x148)]),
    (Window_MessageLog[_0x5c0eeb(0x136)] =
        VisuMZ[_0x5c0eeb(0x220)][_0x5c0eeb(0x23d)][_0x5c0eeb(0x165)]['FastSoundFreq']),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x204)] = function (_0x2ece73) {
        const _0x1da19a = _0x5c0eeb;
        ((this[_0x1da19a(0x156)] = Input[_0x1da19a(0x1fd)](Window_MessageLog[_0x1da19a(0x1b8)])),
            Window_Selectable[_0x1da19a(0x1e3)][_0x1da19a(0x204)][_0x1da19a(0x1a0)](
                this,
                _0x2ece73
            ),
            (this[_0x1da19a(0x231)] = 0x0),
            this[_0x1da19a(0x1f4)](),
            this[_0x1da19a(0x22c)]());
    }),
    (Window_MessageLog['prototype']['isAutoColorAffected'] = function () {
        return !![];
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1f4)] = function () {
        const _0x174595 = _0x5c0eeb;
        (this[_0x174595(0x1d2)](), this[_0x174595(0x12d)](), this[_0x174595(0x1e8)]());
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1d2)] = function () {
        const _0x13a3e6 = _0x5c0eeb,
            _0x162094 = this[_0x13a3e6(0x244)](),
            _0x2176cf = $gameSystem['getLoggedMessages']();
        this['_allTextHeight'] = _0x162094;
        for (const _0xbba1ee of _0x2176cf) {
            if (_0x13a3e6(0x239) === 'UANYP') {
                if (!_0xbba1ee) continue;
                if (_0xbba1ee[_0x13a3e6(0x225)] !== '')
                    this['_allTextHeight'] += this[_0x13a3e6(0x18a)](_0xbba1ee[_0x13a3e6(0x225)])[
                        _0x13a3e6(0x1f8)
                    ];
                let _0x79f3ca =
                        _0xbba1ee['faceName'] !== '' && Window_MessageLog[_0x13a3e6(0x223)]
                            ? ImageManager[_0x13a3e6(0x1b0)]
                            : 0x0,
                    _0x4c712c = this[_0x13a3e6(0x18a)](_0xbba1ee['messageBody'])[_0x13a3e6(0x1f8)];
                ((this[_0x13a3e6(0x231)] += Math[_0x13a3e6(0x177)](_0x79f3ca, _0x4c712c)),
                    (this['_allTextHeight'] += _0x162094));
            } else _0x3a39f4 = _0x41465d[_0x13a3e6(0x140)](_0x274d39['MessageLogItemNothing']);
        }
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)]['contentsHeight'] = function () {
        const _0xd99272 = _0x5c0eeb;
        return Math[_0xd99272(0x177)](this[_0xd99272(0x231)], 0x1);
    }),
    (Window_MessageLog['prototype'][_0x5c0eeb(0x1e8)] = function () {
        const _0x2025f7 = _0x5c0eeb;
        ((this[_0x2025f7(0x24a)] = 0x0), this[_0x2025f7(0x232)]());
        const _0x1e9a74 = $gameSystem[_0x2025f7(0x127)]();
        for (const _0x51d48a of _0x1e9a74) {
            if (!_0x51d48a) continue;
            (this[_0x2025f7(0x22e)](), this[_0x2025f7(0x1df)](_0x51d48a), this[_0x2025f7(0x16a)]());
        }
        this[_0x2025f7(0x22d)]();
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x232)] = function () {
        const _0x41b9d7 = _0x5c0eeb,
            _0x42232b = new Rectangle(
                0x4,
                this[_0x41b9d7(0x24a)],
                this['innerWidth'] - 0x8,
                this[_0x41b9d7(0x244)]()
            );
        this[_0x41b9d7(0x22e)]();
        const _0x47adaf = Window_MessageLog['HORZ_LINE_THICKNESS'],
            _0x1b8785 = _0x42232b['y'] + (_0x42232b[_0x41b9d7(0x1f8)] - _0x47adaf) / 0x2;
        (this[_0x41b9d7(0x159)](_0x42232b['x'], _0x1b8785, _0x42232b['width'], _0x47adaf),
            (this[_0x41b9d7(0x24a)] += this[_0x41b9d7(0x244)]()));
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x18a)] = function (_0x40d515) {
        const _0x40c4af = _0x5c0eeb;
        let _0x3d23e9 = this[_0x40c4af(0x160)] - (ImageManager[_0x40c4af(0x234)] + 0x18) * 0x2;
        Graphics['boxWidth'] <= 0x330 && (_0x3d23e9 = this['innerWidth'] - 0x8);
        (this['resetFontSettings'](), this[_0x40c4af(0x16a)]());
        const _0x220441 = this[_0x40c4af(0x175)](_0x40d515, 0x0, 0x0, _0x3d23e9);
        return (
            (_0x220441['drawing'] = ![]),
            this[_0x40c4af(0x23f)](_0x220441),
            this[_0x40c4af(0x16a)](),
            { width: _0x220441[_0x40c4af(0x1c0)], height: _0x220441[_0x40c4af(0x19b)] }
        );
    }),
    (Window_MessageLog['prototype'][_0x5c0eeb(0x1df)] = function (_0x379821) {
        const _0x3ab197 = _0x5c0eeb;
        let _0x11d2a2 = 0x4,
            _0x22e01b = ImageManager[_0x3ab197(0x234)] + 0x14,
            _0x51dd46 = this[_0x3ab197(0x160)] - (_0x22e01b + 0x4) * 0x2;
        if (_0x379821[_0x3ab197(0x225)] !== '') {
            if (_0x3ab197(0x123) !== _0x3ab197(0x123))
                return _0x1f441d['getInputMultiButtonStrings']('pageup', _0x3ab197(0x1aa));
            else
                (this[_0x3ab197(0x1c3)](),
                    this['drawTextEx'](
                        _0x379821[_0x3ab197(0x225)],
                        Window_MessageLog[_0x3ab197(0x205)],
                        this['_lineY'],
                        _0x51dd46
                    ),
                    (this[_0x3ab197(0x24a)] += this[_0x3ab197(0x18a)](_0x379821['speaker'])[
                        _0x3ab197(0x1f8)
                    ]),
                    this[_0x3ab197(0x181)]());
        }
        (this['resetFontSettings'](), this['resetWordWrap']());
        if (Window_MessageLog[_0x3ab197(0x223)] && _0x379821[_0x3ab197(0x20c)]) {
            if (_0x3ab197(0x1db) !== 'eMijr') {
                let _0xcef0a6 = _0x11d2a2,
                    _0x473d81 = this['_lineY'],
                    _0x11b7cb = ImageManager[_0x3ab197(0x234)],
                    _0x30c660 = ImageManager[_0x3ab197(0x1b0)];
                (this['drawFace'](
                    _0x379821['faceName'],
                    _0x379821[_0x3ab197(0x213)],
                    _0xcef0a6,
                    _0x473d81,
                    _0x11b7cb,
                    _0x30c660
                ),
                    Graphics[_0x3ab197(0x13a)] <= 0x330 &&
                        (_0x51dd46 = this['innerWidth'] - (_0x22e01b + 0x4)));
            } else
                ((_0x3537ba[_0x3ab197(0x20c)] = _0x10480b[_0x3ab197(0x20c)]()),
                    (_0x20c270[_0x3ab197(0x213)] = _0x29863f[_0x3ab197(0x213)]()));
        } else
            Graphics[_0x3ab197(0x13a)] <= 0x330 &&
                ((_0x22e01b = 0x4), (_0x51dd46 = this['innerWidth'] - 0x8));
        this['drawTextEx'](_0x379821['messageBody'], _0x22e01b, this[_0x3ab197(0x24a)], _0x51dd46);
        let _0x531a3d = this['textSizeEx'](_0x379821['messageBody'])['height'],
            _0x648ab8 =
                _0x379821[_0x3ab197(0x20c)] !== '' && Window_MessageLog[_0x3ab197(0x223)]
                    ? ImageManager['faceHeight']
                    : 0x0;
        ((this[_0x3ab197(0x24a)] += Math[_0x3ab197(0x177)](_0x648ab8, _0x531a3d)),
            this[_0x3ab197(0x22e)](),
            this[_0x3ab197(0x16a)](),
            this[_0x3ab197(0x232)]());
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1c3)] = function () {
        const _0x9da013 = _0x5c0eeb,
            _0x37123d =
                VisuMZ[_0x9da013(0x1b9)][_0x9da013(0x23d)][_0x9da013(0x202)][_0x9da013(0x19c)];
        this[_0x9da013(0x149)] = ColorManager['textColor'](_0x37123d);
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x181)] = function () {
        const _0x66f39a = _0x5c0eeb;
        this[_0x66f39a(0x149)] = undefined;
    }),
    (Window_MessageLog['prototype'][_0x5c0eeb(0x15f)] = function () {
        const _0x3cf192 = _0x5c0eeb;
        (Window_Selectable[_0x3cf192(0x1e3)][_0x3cf192(0x15f)][_0x3cf192(0x1a0)](this),
            this['_forcedNameColor'] &&
                (_0x3cf192(0x1f5) === 'NSPNR'
                    ? (_0x307117 = _0x4ccd1e[_0x3cf192(0x16d)](_0x1466e9, ''))
                    : this[_0x3cf192(0x1d8)](this[_0x3cf192(0x149)])));
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1fb)] = function () {}),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1f1)] = function () {
        const _0x34ea76 = _0x5c0eeb;
        if (Input[_0x34ea76(0x1fd)](Window_MessageLog[_0x34ea76(0x1b8)]) && this[_0x34ea76(0x156)])
            return;
        this['_isHotKeyPressed'] = ![];
        if (Input['isPressed'](_0x34ea76(0x1e1))) this[_0x34ea76(0x243)](!![]);
        else {
            if (Input[_0x34ea76(0x1fd)]('up')) {
                if (_0x34ea76(0x1a4) === _0x34ea76(0x1a1))
                    return (
                        this[_0x34ea76(0x17e)] === _0x22b4c9 && this['initMessageLogSettings'](),
                        this[_0x34ea76(0x17e)]
                    );
                else this[_0x34ea76(0x243)](![]);
            } else {
                if (Input[_0x34ea76(0x1fd)]('pagedown')) this[_0x34ea76(0x1d4)](!![]);
                else {
                    if (Input['isPressed']('pageup'))
                        _0x34ea76(0x209) !== _0x34ea76(0x209)
                            ? (_0x1d7f8f === _0x4a2c16 && this[_0x34ea76(0x196)](),
                              _0x2ff444[_0x34ea76(0x220)]['SceneManager_push'][_0x34ea76(0x1a0)](
                                  this,
                                  _0x2a5e56
                              ))
                            : this[_0x34ea76(0x1d4)](![]);
                    else {
                        if (Input[_0x34ea76(0x1af)](_0x34ea76(0x249))) {
                            if ('Cvnna' !== _0x34ea76(0x1f2)) {
                                const _0x1ecd8d =
                                    _0x2bace8[_0x34ea76(0x220)][_0x34ea76(0x23d)][_0x34ea76(0x165)][
                                        _0x34ea76(0x1be)
                                    ];
                                if (_0x1ecd8d) return _0x1ecd8d[_0x34ea76(0x1a0)](this);
                                const _0x3c61f3 = 0x0,
                                    _0x179a14 = this[_0x34ea76(0x145)](),
                                    _0x288fea = _0x54a0f2[_0x34ea76(0x13a)],
                                    _0x33e76b = this[_0x34ea76(0x128)]();
                                return new _0x5ec5c4(_0x3c61f3, _0x179a14, _0x288fea, _0x33e76b);
                            } else this[_0x34ea76(0x1d6)](!![]);
                        } else {
                            if (Input['isTriggered'](_0x34ea76(0x217))) {
                                if (_0x34ea76(0x226) !== _0x34ea76(0x226))
                                    return (
                                        (_0x46f704 = _0x2dca35[_0x34ea76(0x220)][_0x34ea76(0x228)][
                                            _0x34ea76(0x1a0)
                                        ](this, _0x12a26)),
                                        (_0x2e4499 = _0x361859['replace'](
                                            /<BYPASS MESSAGE LOG>/i,
                                            ''
                                        )),
                                        _0x22cdf8
                                    );
                                else this[_0x34ea76(0x22d)](!![]);
                            }
                        }
                    }
                }
            }
        }
    }),
    (Window_MessageLog['prototype']['processSlowScroll'] = function (_0x277404) {
        const _0x24e9a5 = _0x5c0eeb;
        let _0x86a81b = this[_0x24e9a5(0x1fe)]['y'];
        this[_0x24e9a5(0x1fe)]['y'] +=
            (_0x277404 ? 0x1 : -0x1) * Window_MessageLog['SLOW_SCROLL_SPEED'];
        let _0x62b1a3 = Math[_0x24e9a5(0x177)](0x0, this['_allTextHeight'] - this['innerHeight']);
        this[_0x24e9a5(0x1fe)]['y'] = this['origin']['y'][_0x24e9a5(0x14e)](0x0, _0x62b1a3);
        if (
            _0x86a81b !== this[_0x24e9a5(0x1fe)]['y'] &&
            Graphics['frameCount'] % Window_MessageLog[_0x24e9a5(0x241)] === 0x0
        )
            this[_0x24e9a5(0x187)]();
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x1d4)] = function (_0x2700d1) {
        const _0x2f33de = _0x5c0eeb;
        let _0x34f5f2 = this[_0x2f33de(0x1fe)]['y'];
        this[_0x2f33de(0x1fe)]['y'] +=
            (_0x2700d1 ? 0x1 : -0x1) * Window_MessageLog[_0x2f33de(0x22b)];
        let _0x1ece0d = Math[_0x2f33de(0x177)](
            0x0,
            this[_0x2f33de(0x231)] - this[_0x2f33de(0x1ba)]
        );
        this[_0x2f33de(0x1fe)]['y'] = this[_0x2f33de(0x1fe)]['y'][_0x2f33de(0x14e)](0x0, _0x1ece0d);
        if (
            _0x34f5f2 !== this[_0x2f33de(0x1fe)]['y'] &&
            Graphics[_0x2f33de(0x1b7)] % Window_MessageLog[_0x2f33de(0x136)] === 0x0
        )
            this[_0x2f33de(0x187)]();
    }),
    (Window_MessageLog['prototype'][_0x5c0eeb(0x1d6)] = function (_0x384b84) {
        const _0x19f631 = _0x5c0eeb;
        let _0x42abe7 = this[_0x19f631(0x1fe)]['y'];
        this[_0x19f631(0x1fe)]['y'] = 0x0;
        if (_0x384b84 && _0x42abe7 !== this[_0x19f631(0x1fe)]['y']) this[_0x19f631(0x187)]();
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)][_0x5c0eeb(0x22d)] = function (_0x239d25) {
        const _0x4b29a7 = _0x5c0eeb;
        let _0x4bab67 = this[_0x4b29a7(0x1fe)]['y'],
            _0x535afd = Math[_0x4b29a7(0x177)](
                0x0,
                this[_0x4b29a7(0x231)] - this[_0x4b29a7(0x1ba)]
            );
        this[_0x4b29a7(0x1fe)]['y'] = _0x535afd;
        if (_0x239d25 && _0x4bab67 !== this[_0x4b29a7(0x1fe)]['y']) this[_0x4b29a7(0x187)]();
    }),
    (Window_MessageLog[_0x5c0eeb(0x1e3)]['smoothScrollBy'] = function (_0xdbea67, _0x501d5f) {
        const _0x234170 = _0x5c0eeb;
        this[_0x234170(0x1fe)]['y'] += _0x501d5f;
        let _0x8750f0 = Math[_0x234170(0x177)](0x0, this[_0x234170(0x231)] - this['innerHeight']);
        this['origin']['y'] = this[_0x234170(0x1fe)]['y'][_0x234170(0x14e)](0x0, _0x8750f0);
    }),
    (Window_MessageLog['prototype'][_0x5c0eeb(0x21f)] = function (_0x21a097, _0x57d8ab) {
        const _0x1e00b2 = _0x5c0eeb;
        this['origin']['y'] += _0x57d8ab;
        let _0x2bbfa3 = Math['max'](0x0, this[_0x1e00b2(0x231)] - this[_0x1e00b2(0x1ba)]);
        this['origin']['y'] = this['origin']['y'][_0x1e00b2(0x14e)](0x0, _0x2bbfa3);
    }));
