//=============================================================================
// VisuStella MZ - Tutorial Panel System
// VisuMZ_2_TutorialPanelSys.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_2_TutorialPanelSys = true;

var VisuMZ = VisuMZ || {};
VisuMZ.TutorialPanelSys = VisuMZ.TutorialPanelSys || {};
VisuMZ.TutorialPanelSys.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 2] [Version 1.00] [TutorialPanelSys]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Tutorial_Panel_System_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The plugin adds the ability to display tutorial panels seen in many recent
 * JRPG's. The tutorial panel system allows the player to read forward and
 * backward at their own pace while having visuals displayed on the relevant
 * tutorial pages. The player can later reread the tutorials found in a
 * dedicated Tutorial List scene that is accessible through the main menu.
 *
 * Features include all (but not limited to) the following:
 *
 * * Adds modern JRPG Tutorial Panel system.
 * * Players can read tutorials at their own pace, advancing forward and
 *   backward as needed, all while tutorials provide visuals.
 * * The list of already read tutorials can be accessed from the Main Menu
 *   if enabled and made visible through the Plugin Commands/Parameters.
 * * Tutorials can be viewed from the map scene or battle scene.
 * * Tutorial calls can be bypassed if the player has already viewed them, in
 *   order to prevent tediousness.
 * * Tutorials can be bypassed manually by the player through the Options scene
 *   if they do not wish to read tutorials for whatever reason (such as their
 *   second or third playthrough of the game).
 * * Tutorials can still be forcefully opened ignoring the bypass options of
 *   having already been read or turned off through the Options menu.
 * * Game devs can silently register tutorials to be placed into the Tutorial
 *   List scene for the player to read without having to show the tutorial.
 * * Within the Tutorial List scene, tutorials are separated into categories,
 *   allowing players to sort through them easily.
 * * Players can expand and collapse categories as needed if there are too many
 *   tutorials to navigate through.
 * * Some tutorials can already be made visible and registered by default.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 2 ------
 *
 * This plugin is a Tier 2 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * This plugin provides vocabulary that can be used for the Button Assist
 * Window added through the VisuMZ Core Engine.
 *
 * ---
 *
 * VisuMZ_1_MainMenuCore
 *
 * The latest version of the VisuMZ Main Menu Core already has the settings for
 * the Tutorial List command.
 *
 * ---
 *
 * VisuMZ_1_OptionsCore
 *
 * The latest version of the VisuMZ Options Core should have the settings for
 * showing/hiding tutorials.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Tutorial Plugin Commands ===
 *
 * ---
 *
 * Tutorial: Call
 * - Call forth a tutorial of the designated Tutorial ID Key.
 * - Use on the map or battle scenes.
 *
 *   Tutorial ID Key:
 *   - What is the tutorial identification key?
 *
 *   Force View?:
 *   - Forcefully opens the tutorial regardless of the Options settings or if
 *     "Bypass if Registered?" is enabled.
 *
 *   Bypass if Registered?:
 *   - Ignores opening the tutorial if the tutorial has already been
 *     viewed once.
 *
 *   Register Tutorial?:
 *   - Registers the tutorial to the Tutorial List that the player can revisit.
 *
 * ---
 *
 * Tutorial: Register ID Key(s)
 * - Register specific Tutorial ID Key(s) without opening the tutorial.
 *
 *   Tutorial ID Key(s):
 *   - Add which tutorial identification key(s)?
 *
 * ---
 *
 * === System Plugin Commands ===
 *
 * ---
 *
 * System: Enable Tutorial List in Menu?
 * - Enables/disables "Tutorial List" menu inside the main menu.
 *
 *   Enable/Disable?:
 *   - Enables/disables "Tutorial List" menu inside the main menu.
 *
 * ---
 *
 * System: Show Tutorial List in Menu?
 * - Shows/hides "Tutorial List" inside the main menu.
 *
 *   Show/Hide?:
 *   - Shows/hides "Tutorial List" inside the main menu.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Category List Settings
 * ============================================================================
 *
 * List of categories that are used for this plugin. Categories will be listed
 * in the order they appear with "Unlisted" category displayed first.
 *
 * ---
 *
 * Category
 *
 *   ID Key:
 *   - This category's identification key.
 *   - Categories require unique keys for the plugin to differentiate them.
 *
 *   Title:
 *   - This category's title.
 *   - You may use text codes.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Tutorial List Settings
 * ============================================================================
 *
 * List of tutorials that are used for this plugin. Here is where you add all
 * the tutorials seen in game. How they appear and such is all handled here.
 *
 * ---
 *
 * Tutorial
 *
 *   ID Key:
 *   - This tutorial's identification key.
 *   - Tutorials require unique keys for the plugin to differentiate them.
 *
 *   Title:
 *   - This tutorial's title. Displayed in a separate window.
 *   - You may use text codes.
 *
 *   Category:
 *   - The category this tutorial is listed under.
 *   - If unlisted, the tutorial will be listed under "Unlisted".
 *
 *   Pages:
 *   - List of pages that are shown for this tutorial.
 *   - Pages are displayed in the order listed.
 *
 * ---
 *
 * Page Settings
 *
 *   Filename:
 *   - Displayed image associated with this page.
 *   - Found in the game project's /img/pictures/ folder.
 *
 *   Description:
 *   - The description text displayed for this page.
 *   - Text codes are allowed.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Scene_TutorialData Settings
 * ============================================================================
 *
 * Settings for Scene_TutorialData. This scene is where the contents of the
 * tutorials are displayed.
 *
 * ---
 *
 * Background
 *
 *   Snapshop Opacity:
 *   - Snapshot opacity for the scene.
 *
 *   Background 1:
 *   - Filename used for the bottom background image.
 *   - Leave empty if you don't wish to use one.
 *
 *   Background 2:
 *   - Filename used for the upper background image.
 *   - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * Button Assist Vocab
 *
 *   Change Page:
 *   - Vocabulary used for changing pages.
 *   - You may use text codes.
 *
 *   Next Page:
 *   - Vocabulary used for moving to the next page.
 *   - You may use text codes.
 *
 *   Done Tutorial:
 *   - Vocabulary used for being done with the tutorial.
 *   - You may use text codes.
 *
 * ---
 *
 * Windows > Pages Window
 *
 *   Active Page Text:
 *   - Vocabulary used for active page.
 *   - You may use text codes.
 *
 *   Inactive Page Text:
 *   - Vocabulary used for inactive page.
 *   - You may use text codes.
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Description Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Picture Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Title Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Scene_TutorialList Settings
 * ============================================================================
 *
 * Settings for Scene_TutorialList. This is the scene where the player can go
 * to reread previously viewed tutorials.
 *
 * ---
 *
 * Main Menu Settings
 *
 *   Command Name:
 *   - Name of the 'Tutorials' option in the Main Menu.
 *
 *   Show in Main Menu?:
 *   - Add the 'Tutorials' option to the Main Menu by default?
 *
 *   Enable in Main Menu?:
 *   - Enable the 'Tutorials' option to the Main Menu by default?
 *
 * Background
 *
 *   Snapshop Opacity:
 *   - Snapshot opacity for the scene.
 *
 *   Background 1:
 *   - Filename used for the bottom background image.
 *   - Leave empty if you don't wish to use one.
 *
 *   Background 2:
 *   - Filename used for the upper background image.
 *   - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * Button Assist Vocab
 *
 *   View Tutorial:
 *   - Text for viewing tutorial.
 *
 *   Expand Category:
 *   - Text for expanding categories.
 *
 *   Collapse Category:
 *   - Text for collapsing categories.
 *
 * ---
 *
 * Windows > List Window
 *
 *   Open Categories:
 *   - Text format for an open category.
 *   - %1 - Category Name, %2 - Quest Amount
 *
 *   Closed Categories:
 *   - Text format for an open category.
 *   - %1 - Category Name, %2 - Quest Amount
 *
 *   Unlisted Category:
 *   - Text used for "unlisted" category.
 *   - You may use text codes.
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Scene_Battle Settings
 * ============================================================================
 *
 * Settings for Scene_Battle. This governs how tutorials appear in battle.
 *
 * ---
 *
 * Windows > Battle Status Window
 *
 *   Hide During?:
 *   - Hide the battle status window during tutorials?
 *   - Does NOT affect VisuMZ_3_SideviewBattleUI!
 *
 * ---
 *
 * Windows > Pages Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Description Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Picture Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * Windows > Title Window
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this window.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Options Settings
 * ============================================================================
 *
 * Options settings for this plugin.
 *
 * ---
 *
 * Options
 *
 *   Add Option?:
 *   - Add the 'Tutorials' option to the Options menu?
 *
 *   Adjust Window Height:
 *   - Automatically adjust the options window height?
 *
 *   Option Name:
 *   - Command name of the option.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Sound Settings
 * ============================================================================
 *
 * Sound settings when changing tutorial pages.
 *
 * ---
 *
 * Sound
 *
 *   Filename:
 *   - Filename of the sound effect played.
 *
 *   Volume:
 *   - Volume of the sound effect played.
 *
 *   Pitch:
 *   - Pitch of the sound effect played.
 *
 *   Pan:
 *   - Pan of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Olivia
 * * Irina
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: January 2, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command TutorialCall
 * @text Tutorial: Call
 * @desc Call forth a tutorial of the designated Tutorial ID Key.
 * Use on the map or battle scenes.
 *
 * @arg Key:str
 * @text Tutorial ID Key
 * @desc What is the tutorial identification key?
 * @default Untitled
 *
 * @arg ForceView:eval
 * @text Force View?
 * @type boolean
 * @on Force Open
 * @off Conditional
 * @desc Forcefully opens the tutorial regardless of the Options settings or if "Bypass if Registered?" is enabled.
 * @default false
 *
 * @arg BypassIfRegistered:eval
 * @text Bypass if Registered?
 * @type boolean
 * @on Bypass
 * @off Open Always
 * @desc Ignores opening the tutorial if the tutorial has already been viewed once.
 * @default false
 *
 * @arg RegisterTutorial:eval
 * @text Register Tutorial?
 * @type boolean
 * @on Register
 * @off Bypass
 * @desc Registers the tutorial to the Tutorial List that the player can revisit.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command TutorialRegisterKeys
 * @text Tutorial: Register ID Key(s)
 * @desc Register specific Tutorial ID Key(s) without opening the tutorial.
 *
 * @arg KeyIDs:arraystr
 * @text Tutorial ID Key(s)
 * @type string[]
 * @desc Add which tutorial identification key(s)?
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_System
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemEnableTutorialListMenu
 * @text System: Enable Tutorial List in Menu?
 * @desc Enables/disables "Tutorial List" menu inside the main menu.
 *
 * @arg Enable:eval
 * @text Enable/Disable?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enables/disables "Tutorial List" menu inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemShowTutorialListMenu
 * @text System: Show Tutorial List in Menu?
 * @desc Shows/hides "Tutorial List" inside the main menu.
 *
 * @arg Show:eval
 * @text Show/Hide?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Shows/hides "Tutorial List" inside the main menu.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param TutorialPanelSys
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Data
 *
 * @param Categories:arraystruct
 * @text Category List
 * @parent Data
 * @type struct<Category>[]
 * @desc List of categories that are used for this plugin.
 * @default ["{\"Key:str\":\"General\",\"Title:str\":\"\\\\C[4]General Tutorials\"}","{\"Key:str\":\"Battle\",\"Title:str\":\"\\\\C[6]Battle Tutorials\"}","{\"Key:str\":\"Field\",\"Title:str\":\"\\\\C[24]Field Tutorials\"}","{\"Key:str\":\"Menu\",\"Title:str\":\"\\\\C[5]Menu Tutorials\"}"]
 *
 * @param Tutorials:arraystruct
 * @text Tutorial List
 * @parent Data
 * @type struct<Tutorial>[]
 * @desc List of tutorials that are used for this plugin.
 * @default ["{\"Key:str\":\"Sample\",\"Title:str\":\"\\\\i[7]Sample Tutorial\",\"Category:str\":\"Unlisted\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This is a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]sample tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] description.\\\\\\\\\\\\\\\\nPress \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]left\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and/or \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]right\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to change tutorial pages.\\\\\\\\\\\\\\\\nYou may use \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]text codes\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to highlight words.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This is \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]page 2\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] of the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\\nYou can use different pictures from the project's\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\img\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\pictures\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] folder to display for each page.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Press \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]LEFT\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] or \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]RIGHT\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to change between \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial pages\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\\nYou can also press \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]Z\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to move the page forward.\\\\\\\\\\\\\\\\nPressing \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]X\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] will exit out of the tutorial completely.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"TutorialTips1\",\"Title:str\":\"\\\\I[84]Tip #1 - Usefulness\",\"Category:str\":\"General\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Here are some tips that we have for\\\\\\\\\\\\\\\\nmaking useful \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Provide \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]in-game screenshots\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to accompany the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\ndescriptions\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] for each \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial page\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] since players\\\\\\\\\\\\\\\\ntend to be more \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]visual learners\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"TutorialTips2\",\"Title:str\":\"\\\\I[87]Tip #2 - Legibility\",\"Category:str\":\"General\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Use text codes to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]highlight words\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and/or \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]add icons\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to\\\\\\\\\\\\\\\\nthe \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial pages\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to make them more vivid\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\I[7].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Make sure the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]messages\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] for your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial descriptions\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\nare \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]short\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]concise\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]. \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]Avoid\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] wordiness in order to\\\\\\\\\\\\\\\\nmake sure your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial's message\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] gets across.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"TutorialTips3\",\"Title:str\":\"\\\\I[88]Tip #3 - Concise\",\"Category:str\":\"General\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_5\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Although you can have lots of \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]pages\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] for each\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], keep the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]quantity\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] small. You don't want\\\\\\\\\\\\\\\\nto \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]overload\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] your players with too much information.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_6\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Make sure your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] appear timely. Have them\\\\\\\\\\\\\\\\nappear right before the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]contents\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] of the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\ncan be practiced for \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]maximum impact\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"TutorialTips4\",\"Title:str\":\"\\\\I[89]Tip #4 - Retention\",\"Category:str\":\"General\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_7\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Players can revisit \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] from the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]Tutorial List\\\\\\\\\\\\\\\\nScene\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], but this should \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]never\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] be a requirement!\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_8\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Keep \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] sparse. You don't want one \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\npopping up right after another. Keeping them sparse\\\\\\\\\\\\\\\\nhelps players \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]retain\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] what they've learned.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"BattleTutorialTips1\",\"Title:str\":\"\\\\I[97]Tip #1 - Accessible\",\"Category:str\":\"Battle\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Use the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin Command\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Call\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to open up a\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial panel\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] in \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Unlike opening \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] elsewhere, \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] that\\\\\\\\\\\\\\\\nare displayed in \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] will not send the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to\\\\\\\\\\\\\\\\na \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]different scene\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] in order to preserve the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"While the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] is open in \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] is\\\\\\\\\\\\\\\\nconsidered frozen and will \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]not\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] advance until the\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] finishes reading the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"BattleTutorialTips2\",\"Title:str\":\"\\\\I[98]Tip #2 - One Time\",\"Category:str\":\"Battle\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"As you already know, \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] have to be launched in\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]battle\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] through the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin Command\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Call\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_5\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This can be done through either the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Troop Events\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] or\\\\\\\\\\\\\\\\nskill/item \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Common Events\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_6\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"If they are repeatable \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Troop Events\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] or \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Common Events\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0],\\\\\\\\\\\\\\\\nthen we recommend using the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Bypass if Registered?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\nand \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Register Tutorial?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] options.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_7\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This will prevent the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] from popping up each\\\\\\\\\\\\\\\\ntime the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Troop Event\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] conditions are met or whenever\\\\\\\\\\\\\\\\nthe \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Common Event\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] actions are \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]triggered\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"BattleTutorialTips3\",\"Title:str\":\"\\\\I[99]Tip #3 - Action Sequences\",\"Category:str\":\"Battle\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_8\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"If a skill has a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]unique effect\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] added through an\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Action Sequence\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], throw in a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], too.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Do it either at the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]start\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] of the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Action Sequence\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] so\\\\\\\\\\\\\\\\nthat the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] knows what to look for.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"As the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]unique effect\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] occurs, the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] will have a\\\\\\\\\\\\\\\\nbetter understanding of what happened and ideally\\\\\\\\\\\\\\\\nlearned a whole \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]new\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] mechanic.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Remember to use the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Bypass if Registered?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Register Tutorial?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] options to prevent \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]unnecessary\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\nrepetition for future uses of the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]skill/item\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"FieldTutorialTips1\",\"Title:str\":\"\\\\I[181]Tip #1 - Map Call\",\"Category:str\":\"Field\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Calling a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] from the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]map scene\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can be done\\\\\\\\\\\\\\\\nthrough the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin Command\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Call\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This will send the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]different\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] scene where\\\\\\\\\\\\\\\\nthe \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] is displayed in \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]private\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] so that there is\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]no\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] map \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]event interference\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"FieldTutorialTips2\",\"Title:str\":\"\\\\I[182]Tip #2 - Proximity\",\"Category:str\":\"Field\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"If you are using the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]VisuMZ Events & Movement Core\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0],\\\\\\\\\\\\\\\\nyou can take advantage of \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]proximity\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]-based \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]event\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]activation triggers\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Notetags\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] like \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]<Activation Radius: x>\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] allow certain\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]events\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]trigger\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] upon the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] getting close.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_5\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This allows for \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]tutorial calls\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to pop up at more\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]relevant\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] times and positions so that the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] knows\\\\\\\\\\\\\\\\nwhat they're looking at and learning about.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"FieldTutorialTips3\",\"Title:str\":\"\\\\I[183]Tip #3 - Options\",\"Category:str\":\"Field\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_6\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Be sure to remind your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] that there is an \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]option\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\nfound in the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Options scene\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]turn off\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_7\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Although \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can be very helpful, they \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]aren't\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\nalways \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]relevant\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]returning players\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] who may have\\\\\\\\\\\\\\\\nplayed your game \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]multiple\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] times.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"MenuTutorialTips1\",\"Title:str\":\"\\\\I[234]Tip #1 - Tutorial List\",\"Category:str\":\"Menu\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"When you call \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] using the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Call\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin Command\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and have the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Register Tutorial?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]option\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] set to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]true\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], that \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can be \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]revisited\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]Players\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can revisit \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] in the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]Main Menu\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]'s\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\i[189]Tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] command if it is \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]available\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"If there are \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]no\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] available for reading,\\\\\\\\\\\\\\\\nthen this \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]option\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] will be \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]disabled\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"MenuTutorialTips2\",\"Title:str\":\"\\\\I[233]Tip #2 - Registration\",\"Category:str\":\"Menu\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"You don't always have to throw \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] into your\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]players\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]' faces in order to register them.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_5\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"By using the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Register ID Key(s)\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin\\\\\\\\\\\\\\\\nCommand\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0], you can just \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]quietly\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] add \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to the\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Tutorial List\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] scene.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_6\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This is a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]great way\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to add \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] that may be a\\\\\\\\\\\\\\\\nbit more on the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]obscure\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] side that your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]players\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] may\\\\\\\\\\\\\\\\nor may not need to read.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"MenuTutorialTips3\",\"Title:str\":\"\\\\I[232]Tip #3 - Libraries\",\"Category:str\":\"Menu\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_7\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Does your game feature a \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]library\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] of some sort that\\\\\\\\\\\\\\\\nyour \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can read? \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]Transcribe\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]contents\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] into a\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorial\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] so that the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]read on the go\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]!\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor2_8\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"This is particularly \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]helpful\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] if there are \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]hints\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] and\\\\\\\\\\\\\\\\nsuch found in the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]library\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] books.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_1\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Set the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Force View?\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]option\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]true\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to have the\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] be able to read the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]book\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] regardless of their\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Options settings\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]skip tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0].\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}","{\"Key:str\":\"MenuTutorialTips4\",\"Title:str\":\"\\\\I[231]Tip #4 - Stored Hints\",\"Category:str\":\"Menu\",\"Pages:arraystruct\":\"[\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_2\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Do you have \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]puzzles\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] in your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]game\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]? If so, you can use\\\\\\\\\\\\\\\\nthe \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Tutorial List\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] to store \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]hints\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] that your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] may\\\\\\\\\\\\\\\\nhave found across their playthrough.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_3\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]Silently\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] register hints through the \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]Plugin Command\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[6]\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"Tutorial: Register ID Key(s)\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\"\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] so that your \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0]\\\\\\\\\\\\\\\\ndoesn't need to be bombarded with them.\\\\\\\\\\\\\\\"\\\\\\\"}\\\",\\\"{\\\\\\\"Filename:str\\\\\\\":\\\\\\\"Actor1_4\\\\\\\",\\\\\\\"Description:json\\\\\\\":\\\\\\\"\\\\\\\\\\\\\\\"Adding \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[24]hints\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] in the form of \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[4]tutorials\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] can help the\\\\\\\\\\\\\\\\n\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[5]player\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] save a few \\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[2]backtracking\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\\c[0] trips.\\\\\\\\\\\\\\\"\\\\\\\"}\\\"]\"}"]
 *
 * @param DefaultUnlocked:arraystr
 * @text Default Unlocks
 * @parent Tutorials:arraystruct
 * @type string[]
 * @desc List of ID Keys for tutorials that are already unlocked and viewable.
 * @default ["Sample","TutorialTips1","TutorialTips2","TutorialTips3","TutorialTips4","BattleTutorialTips1","BattleTutorialTips2","BattleTutorialTips3","FieldTutorialTips1","FieldTutorialTips2","FieldTutorialTips3","MenuTutorialTips1","MenuTutorialTips2","MenuTutorialTips3","MenuTutorialTips4"]
 *
 * @param Scenes
 *
 * @param SceneTutorialData:struct
 * @text Scene_TutorialData
 * @parent Scenes
 * @type struct<SceneTutorialData>
 * @desc Settings for Scene_TutorialData.
 * @default {"Background":"","SnapshotOpacity:num":"192","BgFilename1:str":"","BgFilename2:str":"","Vocab":"","VocabChangePage:str":"Change Page","VocabNextPage:str":"Next","VocabDoneTutorial:str":"Done","Windows":"","Window_TutorialPages":"","VocabActivePage:str":"\\I[163]","VocabInactivePage:str":"\\I[161]","PageWindow_BgType:num":"2","PageWindow_RectJS:func":"\"const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\\nconst wh = this.calcWindowHeight(1, true);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = this.mainAreaBottom() - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialDescription":"","DescWindow_BgType:num":"0","DescWindow_RectJS:func":"\"const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\\nconst wh = this.calcWindowHeight(4, false);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = this.mainAreaBottom() - wh - this.calcWindowHeight(1, true);\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialPicture":"","PictureWindow_BgType:num":"2","PictureWindow_RectJS:func":"\"const descWindow = arguments[0];\\n\\nconst ww = descWindow.width;\\nconst wh = this.mainAreaHeight() - descWindow.height - this.calcWindowHeight(1, true);\\nconst wx = descWindow.x;\\nconst wy = this.mainAreaTop();\\n\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialTitle":"","TitleWindow_BgType:num":"0","TitleWindow_RectJS:func":"\"const descWindow = arguments[0];\\n\\nconst ww = Math.max(descWindow.width - 300, 480);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = descWindow.y - Math.floor(wh / 2);\\n\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param SceneTutorialList:struct
 * @text Scene_TutorialList
 * @parent Scenes
 * @type struct<SceneTutorialList>
 * @desc Settings for Scene_TutorialList.
 * @default {"MainMenu":"","MainMenuName:str":"Tutorials","ShowMainMenu:eval":"true","EnableMainMenu:eval":"true","Background":"","SnapshotOpacity:num":"192","BgFilename1:str":"","BgFilename2:str":"","Vocab":"","VocabView:str":"View","VocabExpand:str":"Expand","VocabCollapse:str":"Collapse","Windows":"","Window_TutorialList":"","VocabOpenCategory:str":"- %1(%2)","VocabClosedCategory:str":"+ %1(%2)","VocabUnlisted:str":"\\C[8]Uncategorized","ListWindow_BgType:num":"0","ListWindow_RectJS:func":"\"const fw = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\\nconst ww = Math.max(fw - 300, 480);\\nconst wh = this.calcWindowHeight(10, true);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = Math.floor((Graphics.boxHeight - wh) / 2);\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param SceneBattle:struct
 * @text Scene_Battle
 * @parent Scenes
 * @type struct<SceneBattle>
 * @desc Settings for Scene_Battle.
 * @default {"Windows":"","Window_BattleStatus":"","HideDuring:eval":"true","Window_TutorialPages":"","PageWindow_BgType:num":"2","PageWindow_RectJS:func":"\"const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\\nconst wh = this.calcWindowHeight(1, true);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = Graphics.boxHeight - wh;\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialDescription":"","DescWindow_BgType:num":"0","DescWindow_RectJS:func":"\"const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\\nconst wh = this.calcWindowHeight(4, false);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = Graphics.boxHeight - wh - this.calcWindowHeight(1, true);\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialPicture":"","PictureWindow_BgType:num":"2","PictureWindow_RectJS:func":"\"const descWindow = arguments[0];\\n\\nconst ww = descWindow.width;\\nconst wh = (Graphics.boxHeight - this.buttonAreaHeight()) - descWindow.height - this.calcWindowHeight(1, true);\\nconst wx = descWindow.x;\\nconst wy = this.buttonAreaHeight();\\n\\nreturn new Rectangle(wx, wy, ww, wh);\"","Window_TutorialTitle":"","TitleWindow_BgType:num":"0","TitleWindow_RectJS:func":"\"const descWindow = arguments[0];\\n\\nconst ww = Math.max(descWindow.width - 300, 480);\\nconst wh = this.calcWindowHeight(1, false);\\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\\nconst wy = descWindow.y - Math.floor(wh / 2);\\n\\nreturn new Rectangle(wx, wy, ww, wh);\""}
 *
 * @param Options:struct
 * @text Options Settings
 * @parent Scenes
 * @type struct<Options>
 * @desc Options settings for this plugin.
 * @default {"Options":"","AddTutorialsOption:eval":"true","AdjustRect:eval":"true","Name:str":"Tutorials"}
 *
 * @param Sound:struct
 * @text Sound Settings
 * @parent Scenes
 * @type struct<Sound>
 * @desc Sound settings when changing tutorial pages.
 * @default {"name:str":"Book1","volume:num":"90","pitch:num":"120","pan:num":"0"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Category List Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Category:
 *
 * @param Key:str
 * @text ID Key
 * @desc This category's identification key. Categories require
 * unique keys for the plugin to differentiate them.
 * @default (Needs Key)
 *
 * @param Title:str
 * @text Title
 * @desc This category's title.
 * You may use text codes.
 * @default Untitled
 *
 */
/* ----------------------------------------------------------------------------
 * Tutorial List Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Tutorial:
 *
 * @param Key:str
 * @text ID Key
 * @desc This tutorial's identification key. Tutorials require
 * unique keys for the plugin to differentiate them.
 * @default (Needs Key)
 *
 * @param Title:str
 * @text Title
 * @desc This tutorial's title. Displayed in a separate window.
 * You may use text codes.
 * @default Untitled
 *
 * @param Category:str
 * @text Category
 * @desc The category this tutorial is listed under.
 * If unlisted, the tutorial will be listed under "Unlisted".
 * @default Unlisted
 *
 * @param Pages:arraystruct
 * @text Pages
 * @type struct<Page>[]
 * @desc List of pages that are shown for this tutorial.
 * Pages are displayed in the order listed.
 * @default []
 *
 */
/* ----------------------------------------------------------------------------
 * Page Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Page:
 *
 * @param Filename:str
 * @text Filename
 * @type file
 * @dir img/pictures/
 * @require 1
 * @desc Displayed image associated with this page.
 * Found in the game project's /img/pictures/ folder.
 * @default Untitled
 *
 * @param Description:json
 * @text Description
 * @type note
 * @desc The description text displayed for this page.
 * Text codes are allowed.
 * @default "Line 1\nLine 2\nLine 3"
 *
 */
/* ----------------------------------------------------------------------------
 * Scene_TutorialData Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SceneTutorialData:
 *
 * @param Background
 *
 * @param SnapshotOpacity:num
 * @text Snapshop Opacity
 * @parent Background
 * @type number
 * @min 0
 * @max 255
 * @desc Snapshot opacity for the scene.
 * @default 192
 *
 * @param BgFilename1:str
 * @text Background 1
 * @parent Background
 * @type file
 * @dir img/titles1/
 * @require 1
 * @desc Filename used for the bottom background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param BgFilename2:str
 * @text Background 2
 * @parent Background
 * @type file
 * @dir img/titles2/
 * @require 1
 * @desc Filename used for the upper background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param Vocab
 * @text Button Assist Vocab
 *
 * @param VocabChangePage:str
 * @text Change Page
 * @parent Vocab
 * @desc Vocabulary used for changing pages.
 * You may use text codes.
 * @default Change Page
 *
 * @param VocabNextPage:str
 * @text Next Page
 * @parent Vocab
 * @desc Vocabulary used for moving to the next page.
 * You may use text codes.
 * @default Next
 *
 * @param VocabDoneTutorial:str
 * @text Done Tutorial
 * @parent Vocab
 * @desc Vocabulary used for being done with the tutorial.
 * You may use text codes.
 * @default Done
 *
 * @param Windows
 *
 * @param Window_TutorialPages
 * @text Pages Window
 * @parent Windows
 *
 * @param VocabActivePage:str
 * @text Active Page Text
 * @parent Window_TutorialPages
 * @desc Vocabulary used for active page.
 * You may use text codes.
 * @default \I[163]
 *
 * @param VocabInactivePage:str
 * @text Inactive Page Text
 * @parent Window_TutorialPages
 * @desc Vocabulary used for inactive page.
 * You may use text codes.
 * @default \I[161]
 *
 * @param PageWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialPages
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 2
 *
 * @param PageWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialPages
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\nconst wh = this.calcWindowHeight(1, true);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = this.mainAreaBottom() - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialDescription
 * @text Description Window
 * @parent Windows
 *
 * @param DescWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialDescription
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DescWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialDescription
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\nconst wh = this.calcWindowHeight(4, false);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = this.mainAreaBottom() - wh - this.calcWindowHeight(1, true);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialPicture
 * @text Picture Window
 * @parent Windows
 *
 * @param PictureWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialPicture
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 2
 *
 * @param PictureWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialPicture
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const descWindow = arguments[0];\n\nconst ww = descWindow.width;\nconst wh = this.mainAreaHeight() - descWindow.height - this.calcWindowHeight(1, true);\nconst wx = descWindow.x;\nconst wy = this.mainAreaTop();\n\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialTitle
 * @text Title Window
 * @parent Windows
 *
 * @param TitleWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialTitle
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param TitleWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialTitle
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const descWindow = arguments[0];\n\nconst ww = Math.max(descWindow.width - 300, 480);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = descWindow.y - Math.floor(wh / 2);\n\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Scene_TutorialList Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SceneTutorialList:
 *
 * @param MainMenu
 * @text Main Menu Settings
 *
 * @param MainMenuName:str
 * @text Command Name
 * @parent MainMenu
 * @desc Name of the 'Tutorials' option in the Main Menu.
 * @default Tutorials
 *
 * @param ShowMainMenu:eval
 * @text Show in Main Menu?
 * @parent MainMenu
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Add the 'Tutorials' option to the Main Menu by default?
 * @default true
 *
 * @param EnableMainMenu:eval
 * @text Enable in Main Menu?
 * @parent MainMenu
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable the 'Tutorials' option to the Main Menu by default?
 * @default true
 *
 * @param Background
 *
 * @param SnapshotOpacity:num
 * @text Snapshop Opacity
 * @parent Background
 * @type number
 * @min 0
 * @max 255
 * @desc Snapshot opacity for the scene.
 * @default 192
 *
 * @param BgFilename1:str
 * @text Background 1
 * @parent Background
 * @type file
 * @dir img/titles1/
 * @require 1
 * @desc Filename used for the bottom background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param BgFilename2:str
 * @text Background 2
 * @parent Background
 * @type file
 * @dir img/titles2/
 * @require 1
 * @desc Filename used for the upper background image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @param Vocab
 * @text Button Assist Vocab
 *
 * @param VocabView:str
 * @text View Tutorial
 * @parent Vocab
 * @desc Text for viewing tutorial.
 * @default View
 *
 * @param VocabExpand:str
 * @text Expand Category
 * @parent Vocab
 * @desc Text for expanding categories.
 * @default Expand
 *
 * @param VocabCollapse:str
 * @text Collapse Category
 * @parent Vocab
 * @desc Text for collapsing categories.
 * @default Collapse
 *
 * @param Windows
 *
 * @param Window_TutorialList
 * @text List Window
 * @parent Windows
 *
 * @param VocabOpenCategory:str
 * @text Open Categories
 * @parent Window_TutorialList
 * @desc Text format for an open category.
 * %1 - Category Name, %2 - Quest Amount
 * @default - %1(%2)
 *
 * @param VocabClosedCategory:str
 * @text Closed Categories
 * @parent Window_TutorialList
 * @desc Text format for an open category.
 * %1 - Category Name, %2 - Quest Amount
 * @default + %1(%2)
 *
 * @param VocabUnlisted:str
 * @text Unlisted Category
 * @parent Window_TutorialList
 * @desc Text used for "unlisted" category.
 * You may use text codes.
 * @default \C[8]Uncategorized
 *
 * @param ListWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialList
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param ListWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialList
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const fw = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\nconst ww = Math.max(fw - 300, 480);\nconst wh = this.calcWindowHeight(10, true);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = Math.floor((Graphics.boxHeight - wh) / 2);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Scene_Battle Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~SceneBattle:
 *
 * @param Windows
 *
 * @param Window_BattleStatus
 * @text Battle Status Window
 * @parent Windows
 *
 * @param HideDuring:eval
 * @text Hide During?
 * @parent Window_BattleStatus
 * @type boolean
 * @on Hide
 * @off Normal
 * @desc Hide the battle status window during tutorials?
 * Does NOT affect VisuMZ_3_SideviewBattleUI!
 * @default true
 *
 * @param Window_TutorialPages
 * @text Pages Window
 * @parent Windows
 *
 * @param PageWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialPages
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 2
 *
 * @param PageWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialPages
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\nconst wh = this.calcWindowHeight(1, true);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = Graphics.boxHeight - wh;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialDescription
 * @text Description Window
 * @parent Windows
 *
 * @param DescWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialDescription
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param DescWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialDescription
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const ww = Math.max(720, Math.floor(Graphics.boxWidth * 0.75));\nconst wh = this.calcWindowHeight(4, false);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = Graphics.boxHeight - wh - this.calcWindowHeight(1, true);\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialPicture
 * @text Picture Window
 * @parent Windows
 *
 * @param PictureWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialPicture
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 2
 *
 * @param PictureWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialPicture
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const descWindow = arguments[0];\n\nconst ww = descWindow.width;\nconst wh = (Graphics.boxHeight - this.buttonAreaHeight()) - descWindow.height - this.calcWindowHeight(1, true);\nconst wx = descWindow.x;\nconst wy = this.buttonAreaHeight();\n\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param Window_TutorialTitle
 * @text Title Window
 * @parent Windows
 *
 * @param TitleWindow_BgType:num
 * @text Background Type
 * @parent Window_TutorialTitle
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param TitleWindow_RectJS:func
 * @text JS: X, Y, W, H
 * @parent Window_TutorialTitle
 * @type note
 * @desc Code used to determine the dimensions for this window.
 * @default "const descWindow = arguments[0];\n\nconst ww = Math.max(descWindow.width - 300, 480);\nconst wh = this.calcWindowHeight(1, false);\nconst wx = Math.floor((Graphics.boxWidth - ww) / 2);\nconst wy = descWindow.y - Math.floor(wh / 2);\n\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 */
/* ----------------------------------------------------------------------------
 * Options Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Options:
 *
 * @param Options
 * @text Options
 *
 * @param AddTutorialsOption:eval
 * @text Add Option?
 * @parent Options
 * @type boolean
 * @on Add
 * @off Don't Add
 * @desc Add the 'Tutorials' option to the Options menu?
 * @default true
 *
 * @param AdjustRect:eval
 * @text Adjust Window Height
 * @parent Options
 * @type boolean
 * @on Adjust
 * @off Don't
 * @desc Automatically adjust the options window height?
 * @default true
 *
 * @param Name:str
 * @text Option Name
 * @parent Options
 * @desc Command name of the option.
 * @default Tutorials
 *
 */
/* ----------------------------------------------------------------------------
 * Sound Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Sound:
 *
 * @param name:str
 * @text Filename
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Book1
 *
 * @param volume:num
 * @text Volume
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param pitch:num
 * @text Pitch
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param pan:num
 * @text Pan
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
//=============================================================================

const _0x5bb884 = _0x3e49;
(function (_0x531d3c, _0xa59d72) {
    const _0x4c5970 = _0x3e49,
        _0x3e45eb = _0x531d3c();
    while (!![]) {
        try {
            const _0x358600 =
                (-parseInt(_0x4c5970(0x1b2)) / 0x1) * (parseInt(_0x4c5970(0x19c)) / 0x2) +
                (parseInt(_0x4c5970(0x1bd)) / 0x3) * (parseInt(_0x4c5970(0x133)) / 0x4) +
                parseInt(_0x4c5970(0x1e1)) / 0x5 +
                -parseInt(_0x4c5970(0xc3)) / 0x6 +
                parseInt(_0x4c5970(0x1d2)) / 0x7 +
                -parseInt(_0x4c5970(0x105)) / 0x8 +
                (-parseInt(_0x4c5970(0x127)) / 0x9) * (-parseInt(_0x4c5970(0xd4)) / 0xa);
            if (_0x358600 === _0xa59d72) break;
            else _0x3e45eb['push'](_0x3e45eb['shift']());
        } catch (_0x422021) {
            _0x3e45eb['push'](_0x3e45eb['shift']());
        }
    }
})(_0x1fdd, 0xa68ec);
var label = _0x5bb884(0x1b7),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x5bb884(0x97)](function (_0x32a362) {
        const _0x581fb3 = _0x5bb884;
        return _0x32a362['status'] && _0x32a362[_0x581fb3(0x1c1)]['includes']('[' + label + ']');
    })[0x0];
((VisuMZ[label]['Settings'] = VisuMZ[label][_0x5bb884(0x6d)] || {}),
    (VisuMZ[_0x5bb884(0xa5)] = function (_0x3ef4ba, _0x2e11a9) {
        const _0x82da54 = _0x5bb884;
        for (const _0x5c0da9 in _0x2e11a9) {
            if (_0x5c0da9[_0x82da54(0x18b)](/(.*):(.*)/i)) {
                if (_0x82da54(0x98) !== 'HUNjE') {
                    const _0x4332c0 = String(RegExp['$1']),
                        _0x39d029 = String(RegExp['$2'])[_0x82da54(0x18d)]()['trim']();
                    let _0x2e5e78, _0x274e39, _0x22145a;
                    switch (_0x39d029) {
                        case 'NUM':
                            _0x2e5e78 =
                                _0x2e11a9[_0x5c0da9] !== '' ? Number(_0x2e11a9[_0x5c0da9]) : 0x0;
                            break;
                        case _0x82da54(0x156):
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39['map'](_0x4e4136 => Number(_0x4e4136))));
                            break;
                        case _0x82da54(0xd8):
                            _0x2e5e78 =
                                _0x2e11a9[_0x5c0da9] !== '' ? eval(_0x2e11a9[_0x5c0da9]) : null;
                            break;
                        case _0x82da54(0x122):
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39['map'](_0x21f782 => eval(_0x21f782))));
                            break;
                        case _0x82da54(0x19a):
                            _0x2e5e78 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : '';
                            break;
                        case 'ARRAYJSON':
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39[_0x82da54(0x19d)](_0x2d5f89 =>
                                    JSON['parse'](_0x2d5f89)
                                )));
                            break;
                        case _0x82da54(0x196):
                            _0x2e5e78 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? new Function(JSON['parse'](_0x2e11a9[_0x5c0da9]))
                                    : new Function('return\x200');
                            break;
                        case _0x82da54(0x121):
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39[_0x82da54(0x19d)](
                                    _0x213c1f => new Function(JSON[_0x82da54(0x1b8)](_0x213c1f))
                                )));
                            break;
                        case _0x82da54(0xc1):
                            _0x2e5e78 =
                                _0x2e11a9[_0x5c0da9] !== '' ? String(_0x2e11a9[_0x5c0da9]) : '';
                            break;
                        case _0x82da54(0xec):
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39[_0x82da54(0x19d)](_0x5db2ba =>
                                    String(_0x5db2ba)
                                )));
                            break;
                        case _0x82da54(0xd2):
                            ((_0x22145a =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON['parse'](_0x2e11a9[_0x5c0da9])
                                    : {}),
                                (_0x2e5e78 = VisuMZ[_0x82da54(0xa5)]({}, _0x22145a)));
                            break;
                        case _0x82da54(0x8b):
                            ((_0x274e39 =
                                _0x2e11a9[_0x5c0da9] !== ''
                                    ? JSON[_0x82da54(0x1b8)](_0x2e11a9[_0x5c0da9])
                                    : []),
                                (_0x2e5e78 = _0x274e39['map'](_0xd6730b =>
                                    VisuMZ[_0x82da54(0xa5)]({}, JSON['parse'](_0xd6730b))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x3ef4ba[_0x4332c0] = _0x2e5e78;
                } else {
                    if (
                        !_0x43be4c[_0x82da54(0x1b7)][_0x82da54(0x6d)]['SceneBattle'][
                            _0x82da54(0x170)
                        ]
                    )
                        return ![];
                    return this[_0x82da54(0xde)] && this[_0x82da54(0xde)][_0x82da54(0x1c3)];
                }
            }
        }
        return _0x3ef4ba;
    }),
    (_0x273d32 => {
        const _0x1191b5 = _0x5bb884,
            _0x56b06c = _0x273d32['name'];
        for (const _0x319267 of dependencies) {
            if (_0x1191b5(0xe7) === _0x1191b5(0x18e)) {
                const _0x1eeacb = this[_0x1191b5(0x92)](),
                    _0x1b5959 = new _0x3efce9(_0x1eeacb);
                (this[_0x1191b5(0xed)](_0x1b5959),
                    (this['_pageWindow'] = _0x1b5959),
                    _0x1b5959[_0x1191b5(0xf9)](
                        _0x1191b5(0x1a3),
                        this['popScene'][_0x1191b5(0xe6)](this)
                    ),
                    _0x1b5959[_0x1191b5(0xf9)]('page', this['nextPage'][_0x1191b5(0xe6)](this)),
                    _0x1b5959['setBackgroundType'](
                        _0x4da679[_0x1191b5(0x1b7)][_0x1191b5(0x6d)]['SceneTutorialData'][
                            _0x1191b5(0x1bb)
                        ]
                    ));
            } else {
                if (!Imported[_0x319267]) {
                    if (_0x1191b5(0xd1) !== _0x1191b5(0xdd)) {
                        (alert(_0x1191b5(0x16e)['format'](_0x56b06c, _0x319267)),
                            SceneManager[_0x1191b5(0x12d)]());
                        break;
                    } else return _0x49572c['isMainMenuTutorialListVisible']();
                }
            }
        }
        const _0x400dc5 = _0x273d32[_0x1191b5(0x1c1)];
        if (_0x400dc5[_0x1191b5(0x18b)](/\[Version[ ](.*?)\]/i)) {
            if (_0x1191b5(0x124) !== 'IHaNw') {
                const _0x526f38 = _0x25b173[_0x1191b5(0xf2)][_0x1191b5(0x81)],
                    _0x57db83 = _0x1191b5(0x77);
                this[_0x1191b5(0x146)](_0x526f38, _0x57db83);
            } else {
                const _0x4eb735 = Number(RegExp['$1']);
                _0x4eb735 !== VisuMZ[label][_0x1191b5(0x85)] &&
                    (alert(_0x1191b5(0x11f)['format'](_0x56b06c, _0x4eb735)),
                    SceneManager[_0x1191b5(0x12d)]());
            }
        }
        if (_0x400dc5[_0x1191b5(0x18b)](/\[Tier[ ](\d+)\]/i)) {
            const _0x2c619c = Number(RegExp['$1']);
            _0x2c619c < tier
                ? (alert(_0x1191b5(0x6c)[_0x1191b5(0xe1)](_0x56b06c, _0x2c619c, tier)),
                  SceneManager['exit']())
                : (tier = Math['max'](_0x2c619c, tier));
        }
        VisuMZ[_0x1191b5(0xa5)](VisuMZ[label]['Settings'], _0x273d32[_0x1191b5(0x141)]);
    })(pluginData),
    PluginManager[_0x5bb884(0x125)](pluginData[_0x5bb884(0x15f)], _0x5bb884(0x148), _0x4da8ba => {
        const _0x40081e = _0x5bb884;
        VisuMZ[_0x40081e(0xa5)](_0x4da8ba, _0x4da8ba);
        const _0x45532d = _0x4da8ba['Key'],
            _0x4c8700 = _0x4da8ba[_0x40081e(0x1b1)],
            _0x2042c8 = _0x4da8ba['BypassIfRegistered'],
            _0x260028 = _0x4da8ba['RegisterTutorial'];
        if (VisuMZ[_0x40081e(0x1b7)][_0x40081e(0x101)](_0x45532d)) {
            if (!_0x4c8700 && _0x2042c8 && $gameSystem['isTutorialKeyRegistered'](_0x45532d)) {
                if (_0x40081e(0x12f) === _0x40081e(0x12f)) return;
                else this[_0x40081e(0x77)] = !![];
            }
            if (_0x4c8700 || ConfigManager['showTutorials']) {
                if ('ZVIRq' !== _0x40081e(0xcf)) {
                    if (
                        _0x46eb13[_0x40081e(0x1b7)][_0x40081e(0x6d)][_0x40081e(0x6f)][
                            'PictureWindow_RectJS'
                        ]
                    )
                        return _0x155f23['TutorialPanelSys'][_0x40081e(0x6d)][_0x40081e(0x6f)][
                            _0x40081e(0x72)
                        ]['call'](this, this[_0x40081e(0x10b)]);
                    const _0x75d530 = this[_0x40081e(0x10b)],
                        _0x147c37 = _0x75d530[_0x40081e(0xda)],
                        _0x45de88 =
                            _0x2613bb[_0x40081e(0x118)] -
                            this['buttonAreaHeight']() -
                            _0x75d530[_0x40081e(0x1a5)] -
                            this[_0x40081e(0xef)](0x1, !![]),
                        _0x226306 = _0x75d530['x'],
                        _0x2f3642 = this['buttonAreaHeight']();
                    return new _0x43e2ec(_0x226306, _0x2f3642, _0x147c37, _0x45de88);
                } else $gameTemp[_0x40081e(0x102)](_0x45532d);
            }
            _0x260028 && $gameSystem[_0x40081e(0x1dc)](_0x45532d);
        }
    }),
    PluginManager[_0x5bb884(0x125)](pluginData[_0x5bb884(0x15f)], _0x5bb884(0x1ab), _0x488867 => {
        const _0x3db6df = _0x5bb884;
        VisuMZ['ConvertParams'](_0x488867, _0x488867);
        const _0x241c92 = _0x488867[_0x3db6df(0x74)];
        for (const _0x743149 of _0x241c92) {
            $gameSystem['registerTutorialKey'](_0x743149);
        }
    }),
    PluginManager[_0x5bb884(0x125)](pluginData['name'], _0x5bb884(0x6e), _0x2cd20e => {
        const _0x4e6643 = _0x5bb884;
        (VisuMZ[_0x4e6643(0xa5)](_0x2cd20e, _0x2cd20e),
            $gameSystem[_0x4e6643(0x136)](_0x2cd20e['Enable']));
    }),
    PluginManager[_0x5bb884(0x125)](
        pluginData[_0x5bb884(0x15f)],
        'SystemShowTutorialListMenu',
        _0x3e8263 => {
            const _0x135fa0 = _0x5bb884;
            (VisuMZ[_0x135fa0(0xa5)](_0x3e8263, _0x3e8263),
                $gameSystem[_0x135fa0(0x6a)](_0x3e8263[_0x135fa0(0xd3)]));
        }
    ),
    (VisuMZ[_0x5bb884(0x1b7)]['Scene_Boot_onDatabaseLoaded'] =
        Scene_Boot[_0x5bb884(0xfd)]['onDatabaseLoaded']),
    (Scene_Boot[_0x5bb884(0xfd)][_0x5bb884(0x1af)] = function () {
        const _0x3bc243 = _0x5bb884;
        (VisuMZ[_0x3bc243(0x1b7)][_0x3bc243(0xb3)][_0x3bc243(0x198)](this),
            this[_0x3bc243(0x15d)]());
    }),
    (Scene_Boot[_0x5bb884(0xfd)][_0x5bb884(0x15d)] = function () {
        const _0x2fa304 = _0x5bb884;
        (this['process_VisuMZ_TutorialCategories'](), this[_0x2fa304(0x1db)]());
    }),
    (Scene_Boot[_0x5bb884(0xfd)][_0x5bb884(0x9b)] = function () {
        const _0x13dd22 = _0x5bb884;
        ((VisuMZ[_0x13dd22(0x1b7)][_0x13dd22(0x150)] = ['unlisted']),
            (VisuMZ[_0x13dd22(0x1b7)][_0x13dd22(0x1e0)] = {}),
            (VisuMZ['TutorialPanelSys']['CategoryTutorials'] = {}),
            (VisuMZ[_0x13dd22(0x1b7)][_0x13dd22(0x12a)][_0x13dd22(0xdc)] = []));
        const _0x3e79ad = VisuMZ[_0x13dd22(0x1b7)]['Settings'][_0x13dd22(0x12b)];
        for (const _0x1cc121 of _0x3e79ad) {
            if (_0x13dd22(0xfa) === _0x13dd22(0x1b3))
                (delete this[_0x13dd22(0x149)],
                    delete this[_0x13dd22(0x1c8)],
                    (this['_lastIndex'] = 0x0),
                    (this[_0x13dd22(0x181)] = 0x0));
            else {
                const _0x1088b9 = (_0x1cc121[_0x13dd22(0x87)] || '')[_0x13dd22(0x84)]()['trim']();
                if (_0x1088b9 === '') continue;
                if (_0x1088b9 === '(needs\x20key)') continue;
                (VisuMZ[_0x13dd22(0x1b7)]['CategoryOrder'][_0x13dd22(0x182)](_0x1088b9),
                    (VisuMZ[_0x13dd22(0x1b7)]['CategoryData'][_0x1088b9] = _0x1cc121),
                    (VisuMZ[_0x13dd22(0x1b7)]['CategoryTutorials'][_0x1088b9] = []));
            }
        }
    }),
    (Scene_Boot['prototype']['process_VisuMZ_TutorialData'] = function () {
        const _0x1caaeb = _0x5bb884;
        ((VisuMZ[_0x1caaeb(0x1b7)]['Tutorials'] = {}),
            (VisuMZ[_0x1caaeb(0x1b7)][_0x1caaeb(0x137)] = []));
        const _0x47ebe3 = VisuMZ[_0x1caaeb(0x1b7)]['Settings']['Tutorials'];
        for (const _0x50f76e of _0x47ebe3) {
            if (_0x1caaeb(0x7a) !== 'wqubr') {
                const _0x367299 = (_0x50f76e[_0x1caaeb(0x87)] || '')['toLowerCase']()['trim']();
                if (_0x367299 === '') continue;
                if (_0x367299 === _0x1caaeb(0x169)) continue;
                ((VisuMZ[_0x1caaeb(0x1b7)][_0x1caaeb(0xe0)][_0x367299] = _0x50f76e),
                    VisuMZ[_0x1caaeb(0x1b7)][_0x1caaeb(0x137)][_0x1caaeb(0x182)](_0x367299));
                const _0x4c70d0 = (_0x50f76e['Category'] || '')
                    ['toLowerCase']()
                    [_0x1caaeb(0x12c)]();
                VisuMZ['TutorialPanelSys'][_0x1caaeb(0x12a)][_0x4c70d0]
                    ? VisuMZ[_0x1caaeb(0x1b7)][_0x1caaeb(0x12a)][_0x4c70d0]['push'](_0x367299)
                    : VisuMZ[_0x1caaeb(0x1b7)][_0x1caaeb(0x12a)]['unlisted']['push'](_0x367299);
            } else
                return this[_0x1caaeb(0x154)] === this[_0x1caaeb(0x12e)]()[_0x1caaeb(0x7c)] - 0x1
                    ? _0x424633[_0x1caaeb(0xf2)][_0x1caaeb(0x110)]
                    : _0x26208a['tutorial'][_0x1caaeb(0x17d)];
        }
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x101)] = function (_0xd476f8) {
        const _0x852f15 = _0x5bb884;
        return (
            (_0xd476f8 = _0xd476f8[_0x852f15(0x84)]()['trim']()),
            VisuMZ[_0x852f15(0x1b7)][_0x852f15(0x137)][_0x852f15(0xea)](_0xd476f8)
        );
    }),
    (ConfigManager[_0x5bb884(0x77)] = !![]),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x15e)] = ConfigManager[_0x5bb884(0x112)]),
    (ConfigManager[_0x5bb884(0x112)] = function () {
        const _0x5a682e = _0x5bb884,
            _0xbf30be = VisuMZ[_0x5a682e(0x1b7)]['ConfigManager_makeData']['call'](this);
        return ((_0xbf30be[_0x5a682e(0x77)] = this[_0x5a682e(0x77)]), _0xbf30be);
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x13a)] = ConfigManager[_0x5bb884(0x1ce)]),
    (ConfigManager[_0x5bb884(0x1ce)] = function (_0x5dbf26) {
        const _0x3d67d8 = _0x5bb884;
        (VisuMZ[_0x3d67d8(0x1b7)][_0x3d67d8(0x13a)][_0x3d67d8(0x198)](this, _0x5dbf26),
            this['readFlag'](_0x5dbf26, _0x3d67d8(0x77), !![]));
        if (_0x3d67d8(0x77) in _0x5dbf26) this['showTutorials'] = _0x5dbf26['showTutorials'];
        else {
            if (_0x3d67d8(0x71) === _0x3d67d8(0x71)) this[_0x3d67d8(0x77)] = !![];
            else {
                if (this[_0x3d67d8(0xde)] && this[_0x3d67d8(0xde)][_0x3d67d8(0x1c3)]) return !![];
                return _0x1c45b6['TutorialPanelSys'][_0x3d67d8(0xcd)][_0x3d67d8(0x198)](this);
            }
        }
    }),
    (SoundManager[_0x5bb884(0xb5)] = function () {
        const _0x1367e3 = _0x5bb884,
            _0x11fd64 = VisuMZ['TutorialPanelSys'][_0x1367e3(0x6d)][_0x1367e3(0x1aa)],
            _0x1e629b = {
                name: _0x11fd64[_0x1367e3(0x15f)],
                volume: _0x11fd64[_0x1367e3(0x199)],
                pitch: _0x11fd64['pitch'],
                pan: _0x11fd64[_0x1367e3(0x188)],
            };
        AudioManager[_0x1367e3(0xee)](_0x1e629b);
    }),
    (TextManager['tutorial'] = {
        changePage: VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)][_0x5bb884(0xd5)][_0x5bb884(0x1a7)],
        nextPage: VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)][_0x5bb884(0xd5)]['VocabNextPage'],
        finish: VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)][_0x5bb884(0xd5)][_0x5bb884(0xdf)],
        view: VisuMZ['TutorialPanelSys'][_0x5bb884(0x6d)][_0x5bb884(0x17c)][_0x5bb884(0x95)],
        expand: VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)]['SceneTutorialList'][_0x5bb884(0x1dd)],
        collapse: VisuMZ['TutorialPanelSys']['Settings']['SceneTutorialList']['VocabCollapse'],
        activePage: VisuMZ[_0x5bb884(0x1b7)]['Settings']['SceneTutorialData']['VocabActivePage'],
        inactivePage: VisuMZ[_0x5bb884(0x1b7)]['Settings'][_0x5bb884(0xd5)][_0x5bb884(0x1d8)],
        optionsCmd: VisuMZ['TutorialPanelSys'][_0x5bb884(0x6d)]['Options'][_0x5bb884(0x157)],
        menuCmd: VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)][_0x5bb884(0x17c)]['MainMenuName'],
        openCategoriesFmt:
            VisuMZ['TutorialPanelSys'][_0x5bb884(0x6d)]['SceneTutorialList']['VocabOpenCategory'],
        closedCategoriesFmt:
            VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x6d)][_0x5bb884(0x17c)][_0x5bb884(0x163)],
        unlisted: VisuMZ[_0x5bb884(0x1b7)]['Settings']['SceneTutorialList']['VocabUnlisted'],
    }),
    (SceneManager[_0x5bb884(0x111)] = function () {
        const _0xbdc534 = _0x5bb884;
        return this[_0xbdc534(0xa0)] && this[_0xbdc534(0xa0)][_0xbdc534(0x1a2)] === Scene_Battle;
    }),
    (SceneManager[_0x5bb884(0x8f)] = function () {
        const _0x568cd3 = _0x5bb884;
        return this[_0x568cd3(0xa0)] && this['_scene'][_0x568cd3(0x1a2)] === Scene_Map;
    }),
    (Game_Temp[_0x5bb884(0xfd)][_0x5bb884(0x102)] = function (_0x1a137d) {
        const _0x2c75ac = _0x5bb884;
        if (!VisuMZ[_0x2c75ac(0x1b7)][_0x2c75ac(0x101)](_0x1a137d)) return;
        this[_0x2c75ac(0x138)] = _0x1a137d;
        if (SceneManager[_0x2c75ac(0x111)]()) {
            const _0x57b0fb = SceneManager[_0x2c75ac(0xa0)];
            _0x57b0fb[_0x2c75ac(0x102)](_0x1a137d);
        } else {
            SceneManager['push'](Scene_TutorialData);
            const _0x4c6ffb = this[_0x2c75ac(0x100)]();
            _0x4c6ffb &&
                (_0x2c75ac(0xeb) !== _0x2c75ac(0xeb)
                    ? (_0x416e37[_0x2c75ac(0x10e)](), this['popScene']())
                    : _0x4c6ffb[_0x2c75ac(0xd0)](0xa));
        }
    }),
    (Game_Temp[_0x5bb884(0xfd)][_0x5bb884(0xb2)] = function (_0x428da2) {
        const _0x2f4e1c = _0x5bb884;
        this[_0x2f4e1c(0x89)] = _0x428da2;
    }),
    (Game_Temp[_0x5bb884(0xfd)][_0x5bb884(0x100)] = function () {
        const _0x53024d = _0x5bb884;
        return this[_0x53024d(0x89)];
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x104)] =
        Game_Interpreter[_0x5bb884(0xfd)][_0x5bb884(0xf8)]),
    (Game_Interpreter[_0x5bb884(0xfd)][_0x5bb884(0xf8)] = function (_0x2f6e53) {
        const _0x134a62 = _0x5bb884;
        return (
            $gameTemp[_0x134a62(0xb2)](this),
            VisuMZ[_0x134a62(0x1b7)][_0x134a62(0x104)][_0x134a62(0x198)](this, _0x2f6e53)
        );
    }),
    (VisuMZ[_0x5bb884(0x1b7)]['Game_System_initialize'] =
        Game_System[_0x5bb884(0xfd)][_0x5bb884(0xd9)]),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function () {
        const _0x1a678f = _0x5bb884;
        (VisuMZ[_0x1a678f(0x1b7)][_0x1a678f(0xf1)]['call'](this),
            this[_0x1a678f(0x10c)](),
            this[_0x1a678f(0x164)]());
    }),
    (Game_System['prototype']['initTutorialPanelSysMainMenu'] = function () {
        const _0xbf8576 = _0x5bb884;
        this[_0xbf8576(0x179)] = {
            shown: VisuMZ[_0xbf8576(0x1b7)][_0xbf8576(0x6d)][_0xbf8576(0x17c)][_0xbf8576(0xc6)],
            enabled: VisuMZ[_0xbf8576(0x1b7)][_0xbf8576(0x6d)][_0xbf8576(0x17c)][_0xbf8576(0x1a8)],
        };
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0x129)] = function () {
        const _0x56a2b4 = _0x5bb884;
        if (this[_0x56a2b4(0x179)] === undefined) this[_0x56a2b4(0x10c)]();
        return this[_0x56a2b4(0x179)][_0x56a2b4(0x174)];
    }),
    (Game_System['prototype'][_0x5bb884(0x6a)] = function (_0x4ee202) {
        const _0x412378 = _0x5bb884;
        if (this['_TutorialPanelSys_MainMenu'] === undefined)
            this['initTutorialPanelSysMainMenu']();
        this[_0x412378(0x179)]['shown'] = _0x4ee202;
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0x1c5)] = function () {
        const _0x8ae0a7 = _0x5bb884;
        if (this['_TutorialPanelSys_MainMenu'] === undefined)
            this['initTutorialPanelSysMainMenu']();
        if (!this[_0x8ae0a7(0x83)]) return ![];
        if (this[_0x8ae0a7(0x83)][_0x8ae0a7(0x7c)] <= 0x0) return ![];
        return this[_0x8ae0a7(0x179)][_0x8ae0a7(0x1b5)];
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0x136)] = function (_0x5b20c6) {
        const _0x34d07f = _0x5bb884;
        if (this[_0x34d07f(0x179)] === undefined) this['initTutorialPanelSysMainMenu']();
        this['_TutorialPanelSys_MainMenu'][_0x34d07f(0x1b5)] = _0x5b20c6;
    }),
    (Game_System['prototype'][_0x5bb884(0x164)] = function () {
        const _0x4b2963 = _0x5bb884;
        this[_0x4b2963(0x83)] = this[_0x4b2963(0x83)] || [];
        const _0xc0343c = VisuMZ[_0x4b2963(0x1b7)][_0x4b2963(0x6d)][_0x4b2963(0xc0)];
        for (const _0x197f64 of _0xc0343c) {
            'ZPVSM' !== _0x4b2963(0x151)
                ? this['registerTutorialKey'](_0x197f64)
                : (this['_tutorialDescriptionWindow'][_0x4b2963(0x184)](),
                  this[_0x4b2963(0x10b)]['updatePage'](!![]),
                  this[_0x4b2963(0x10b)][_0x4b2963(0x159)]());
        }
    }),
    (Game_System[_0x5bb884(0xfd)]['registerTutorialKey'] = function (_0x12547e) {
        const _0x738aad = _0x5bb884;
        if (this['_visibleTutorialKeys'] === undefined) this['initTutorialPanelSysSettings']();
        ((_0x12547e = _0x12547e[_0x738aad(0x84)]()[_0x738aad(0x12c)]()),
            VisuMZ[_0x738aad(0x1b7)][_0x738aad(0x101)](_0x12547e) &&
                !this[_0x738aad(0x1cb)](_0x12547e) &&
                this['_visibleTutorialKeys']['push'](_0x12547e));
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0x19e)] = function (_0x39f577) {
        const _0x491c30 = _0x5bb884;
        if (this[_0x491c30(0x83)] === undefined) this[_0x491c30(0x164)]();
        ((_0x39f577 = _0x39f577[_0x491c30(0x84)]()[_0x491c30(0x12c)]()),
            this[_0x491c30(0x83)][_0x491c30(0x11c)](_0x39f577));
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0x1cb)] = function (_0x568795) {
        const _0x90dafd = _0x5bb884;
        if (this['_visibleTutorialKeys'] === undefined) this[_0x90dafd(0x164)]();
        return (
            (_0x568795 = _0x568795[_0x90dafd(0x84)]()[_0x90dafd(0x12c)]()),
            this[_0x90dafd(0x83)][_0x90dafd(0xea)](_0x568795)
        );
    }),
    (Game_System[_0x5bb884(0xfd)]['isTutorialCategoryPopulated'] = function (_0x854607) {
        const _0x53c11f = _0x5bb884;
        _0x854607 = _0x854607[_0x53c11f(0x84)]()[_0x53c11f(0x12c)]();
        if (!VisuMZ[_0x53c11f(0x1b7)][_0x53c11f(0x12a)][_0x854607]) return ![];
        const _0x27a5e4 = VisuMZ[_0x53c11f(0x1b7)][_0x53c11f(0x12a)][_0x854607];
        for (const _0x2503c0 of _0x27a5e4) {
            if (this['isTutorialKeyRegistered'](_0x2503c0)) return !![];
        }
        return ![];
    }),
    (Game_System[_0x5bb884(0xfd)][_0x5bb884(0xb9)] = function (_0x32746f) {
        const _0x1e0a12 = _0x5bb884;
        _0x32746f = _0x32746f[_0x1e0a12(0x84)]()[_0x1e0a12(0x12c)]();
        if (!VisuMZ[_0x1e0a12(0x1b7)][_0x1e0a12(0x12a)][_0x32746f]) return 0x0;
        let _0x497426 = 0x0;
        const _0x3adfdc = VisuMZ['TutorialPanelSys']['CategoryTutorials'][_0x32746f];
        for (const _0x176fae of _0x3adfdc) {
            if (this[_0x1e0a12(0x1cb)](_0x176fae)) _0x497426++;
        }
        return _0x497426;
    }),
    (VisuMZ['TutorialPanelSys'][_0x5bb884(0xa7)] =
        Game_Interpreter[_0x5bb884(0xfd)][_0x5bb884(0x1a1)]),
    (Game_Interpreter[_0x5bb884(0xfd)][_0x5bb884(0x1a1)] = function () {
        const _0x256e54 = _0x5bb884;
        if (this[_0x256e54(0x195)] === _0x256e54(0xf2)) return !![];
        return VisuMZ['TutorialPanelSys'][_0x256e54(0xa7)]['call'](this);
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x186)] =
        Scene_Menu[_0x5bb884(0xfd)]['createCommandWindow']),
    (Scene_Menu[_0x5bb884(0xfd)]['createCommandWindow'] = function () {
        const _0xd0c303 = _0x5bb884;
        VisuMZ[_0xd0c303(0x1b7)][_0xd0c303(0x186)][_0xd0c303(0x198)](this);
        const _0x3535e6 = this[_0xd0c303(0x131)];
        _0x3535e6[_0xd0c303(0xf9)](_0xd0c303(0x16a), this[_0xd0c303(0x1b6)]['bind'](this));
    }),
    (Scene_Menu['prototype'][_0x5bb884(0x1b6)] = function () {
        SceneManager['push'](Scene_TutorialList);
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x75)] = Scene_Options[_0x5bb884(0xfd)][_0x5bb884(0x1d3)]),
    (Scene_Options[_0x5bb884(0xfd)]['maxCommands'] = function () {
        const _0x6d2820 = _0x5bb884;
        let _0x2eef20 = VisuMZ[_0x6d2820(0x1b7)][_0x6d2820(0x75)][_0x6d2820(0x198)](this);
        const _0x5504bd = VisuMZ[_0x6d2820(0x1b7)][_0x6d2820(0x6d)][_0x6d2820(0x16d)];
        if (_0x5504bd['AddTutorialsOption'] && _0x5504bd[_0x6d2820(0x145)]) _0x2eef20++;
        return _0x2eef20;
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0xe3)] = Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xd9)]),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function () {
        const _0x29a07a = _0x5bb884;
        (VisuMZ['TutorialPanelSys'][_0x29a07a(0xe3)][_0x29a07a(0x198)](this),
            this[_0x29a07a(0xb6)]());
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xb6)] = function () {
        const _0x173fdc = _0x5bb884;
        ((this[_0x173fdc(0x138)] = ''),
            (this[_0x173fdc(0x123)] =
                VisuMZ[_0x173fdc(0x1b7)][_0x173fdc(0xe0)][this[_0x173fdc(0x138)]]),
            (this['_tutorialPageIndex'] = 0x0));
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xa1)] = function () {
        return this['_tutorialKey'];
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x178)] = function () {
        const _0x48d97b = _0x5bb884;
        return this[_0x48d97b(0x123)] || {};
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x1ca)] = function () {
        const _0x521d5e = _0x5bb884;
        return this[_0x521d5e(0x178)]()[_0x521d5e(0xad)] || '';
    }),
    (Scene_Battle['prototype'][_0x5bb884(0x12e)] = function () {
        const _0x55c90e = _0x5bb884;
        return this[_0x55c90e(0x178)]()[_0x55c90e(0x15b)] || [];
    }),
    (Scene_Battle[_0x5bb884(0xfd)]['tutorialPageIndex'] = function () {
        const _0x64466e = _0x5bb884;
        return this[_0x64466e(0x154)];
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x1d1)] = function () {
        const _0x547e4f = _0x5bb884;
        return this[_0x547e4f(0x12e)]()[this['tutorialPageIndex']()];
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x168)] = Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xaf)]),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xaf)] = function () {
        const _0x3a9907 = _0x5bb884;
        (VisuMZ[_0x3a9907(0x1b7)][_0x3a9907(0x168)][_0x3a9907(0x198)](this),
            this[_0x3a9907(0xa8)]());
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xa8)] = function () {
        const _0x4d792b = _0x5bb884;
        (this['createTutorialPageWindow'](),
            this[_0x4d792b(0x14d)](),
            this[_0x4d792b(0x155)](),
            this[_0x4d792b(0x134)]());
    }),
    (Scene_Battle[_0x5bb884(0xfd)]['createTutorialPageWindow'] = function () {
        const _0x320984 = _0x5bb884,
            _0x5d8e1f = this[_0x320984(0x16c)](),
            _0x1b926e = new Window_TutorialPages(_0x5d8e1f);
        (this[_0x320984(0xed)](_0x1b926e),
            (this[_0x320984(0xde)] = _0x1b926e),
            _0x1b926e[_0x320984(0xf0)](),
            _0x1b926e[_0x320984(0x14f)](),
            _0x1b926e['setHandler'](_0x320984(0x1a3), this[_0x320984(0x7e)]['bind'](this)),
            _0x1b926e[_0x320984(0xf9)](
                _0x320984(0x18c),
                this[_0x320984(0x18f)][_0x320984(0xe6)](this)
            ),
            _0x1b926e[_0x320984(0x8d)](
                VisuMZ[_0x320984(0x1b7)][_0x320984(0x6d)][_0x320984(0x6f)]['PageWindow_BgType']
            ));
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x16c)] = function () {
        const _0x514f34 = _0x5bb884;
        if (VisuMZ[_0x514f34(0x1b7)][_0x514f34(0x6d)][_0x514f34(0x6f)]['PageWindow_RectJS'])
            return VisuMZ[_0x514f34(0x1b7)][_0x514f34(0x6d)][_0x514f34(0x6f)][_0x514f34(0x128)][
                _0x514f34(0x198)
            ](this);
        const _0x37829c = Math[_0x514f34(0xab)](
                0x2d0,
                Math[_0x514f34(0x1e2)](Graphics[_0x514f34(0x11e)] * 0.75)
            ),
            _0x544091 = this[_0x514f34(0xef)](0x1, !![]),
            _0x2cccb2 = Math[_0x514f34(0x1e2)]((Graphics[_0x514f34(0x11e)] - _0x37829c) / 0x2),
            _0x5b5cf7 = Graphics['boxHeight'] - _0x544091;
        return new Rectangle(_0x2cccb2, _0x5b5cf7, _0x37829c, _0x544091);
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x14d)] = function () {
        const _0x12ea03 = _0x5bb884,
            _0x5425b8 = this[_0x12ea03(0xd7)](),
            _0x369032 = new Window_TutorialDescription(_0x5425b8);
        (this[_0x12ea03(0xed)](_0x369032),
            (this[_0x12ea03(0x10b)] = _0x369032),
            _0x369032[_0x12ea03(0xf0)](),
            _0x369032[_0x12ea03(0x8d)](
                VisuMZ['TutorialPanelSys'][_0x12ea03(0x6d)][_0x12ea03(0x6f)]['DescWindow_BgType']
            ));
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xd7)] = function () {
        const _0x2caacd = _0x5bb884;
        if (VisuMZ[_0x2caacd(0x1b7)][_0x2caacd(0x6d)]['SceneBattle'][_0x2caacd(0xe9)]) {
            if (_0x2caacd(0xc7) === 'AeCtN')
                (this[_0x2caacd(0xde)]['activate'](),
                    this[_0x2caacd(0xde)][_0x2caacd(0x120)](_0x1e60ef));
            else
                return VisuMZ[_0x2caacd(0x1b7)][_0x2caacd(0x6d)][_0x2caacd(0x6f)][_0x2caacd(0xe9)][
                    _0x2caacd(0x198)
                ](this);
        }
        const _0x4d4cd2 = Math['max'](
                0x2d0,
                Math[_0x2caacd(0x1e2)](Graphics[_0x2caacd(0x11e)] * 0.75)
            ),
            _0x1fe680 = this['calcWindowHeight'](0x4, ![]),
            _0xe87bd6 = Math[_0x2caacd(0x1e2)]((Graphics[_0x2caacd(0x11e)] - _0x4d4cd2) / 0x2),
            _0x5b7ec0 = Graphics['boxHeight'] - _0x1fe680 - this[_0x2caacd(0xef)](0x1, !![]);
        return new Rectangle(_0xe87bd6, _0x5b7ec0, _0x4d4cd2, _0x1fe680);
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x155)] = function () {
        const _0x317f01 = _0x5bb884,
            _0x4dff34 = this['tutorialPictureWindowRect'](),
            _0x22fb41 = new Window_TutorialPicture(_0x4dff34);
        (this[_0x317f01(0xed)](_0x22fb41),
            (this[_0x317f01(0x1ae)] = _0x22fb41),
            _0x22fb41['hide'](),
            _0x22fb41[_0x317f01(0x8d)](
                VisuMZ[_0x317f01(0x1b7)][_0x317f01(0x6d)][_0x317f01(0x6f)][_0x317f01(0x14e)]
            ),
            this[_0x317f01(0xed)](this['_tutorialDescriptionWindow']));
    }),
    (Scene_Battle[_0x5bb884(0xfd)]['tutorialPictureWindowRect'] = function () {
        const _0x48ec25 = _0x5bb884;
        if (VisuMZ[_0x48ec25(0x1b7)][_0x48ec25(0x6d)][_0x48ec25(0x6f)][_0x48ec25(0x72)])
            return VisuMZ[_0x48ec25(0x1b7)][_0x48ec25(0x6d)]['SceneBattle'][_0x48ec25(0x72)][
                'call'
            ](this, this[_0x48ec25(0x10b)]);
        const _0x1d70cf = this[_0x48ec25(0x10b)],
            _0x57ba38 = _0x1d70cf['width'],
            _0x50baa7 =
                Graphics['boxHeight'] -
                this[_0x48ec25(0xce)]() -
                _0x1d70cf[_0x48ec25(0x1a5)] -
                this['calcWindowHeight'](0x1, !![]),
            _0x539404 = _0x1d70cf['x'],
            _0x33d783 = this[_0x48ec25(0xce)]();
        return new Rectangle(_0x539404, _0x33d783, _0x57ba38, _0x50baa7);
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x134)] = function () {
        const _0xd9c632 = _0x5bb884,
            _0x2f8410 = this['tutorialTitleWindowRect'](),
            _0x5b9d02 = new Window_TutorialTitle(_0x2f8410);
        (this[_0xd9c632(0xed)](_0x5b9d02),
            (this[_0xd9c632(0x1cd)] = _0x5b9d02),
            _0x5b9d02['hide'](),
            _0x5b9d02[_0xd9c632(0x8d)](
                VisuMZ[_0xd9c632(0x1b7)]['Settings']['SceneBattle'][_0xd9c632(0xff)]
            ));
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0xaa)] = function () {
        const _0x201ecb = _0x5bb884;
        if (VisuMZ[_0x201ecb(0x1b7)][_0x201ecb(0x6d)][_0x201ecb(0x6f)][_0x201ecb(0x14a)])
            return 'IsABK' !== _0x201ecb(0x68)
                ? VisuMZ[_0x201ecb(0x1b7)][_0x201ecb(0x6d)]['SceneBattle'][_0x201ecb(0x14a)][
                      'call'
                  ](this, this[_0x201ecb(0x10b)])
                : 'center';
        const _0x28a145 = this['_tutorialDescriptionWindow'],
            _0x18b275 = Math[_0x201ecb(0xab)](_0x28a145[_0x201ecb(0xda)] - 0x12c, 0x1e0),
            _0xbd71ce = this[_0x201ecb(0xef)](0x1, ![]),
            _0x58f3e9 = Math['floor']((Graphics['boxWidth'] - _0x18b275) / 0x2),
            _0x28370c = _0x28a145['y'] - Math[_0x201ecb(0x1e2)](_0xbd71ce / 0x2);
        return new Rectangle(_0x58f3e9, _0x28370c, _0x18b275, _0xbd71ce);
    }),
    (Scene_Battle[_0x5bb884(0xfd)]['nextTutorialPage'] = function () {
        const _0x5e0d52 = _0x5bb884;
        let _0xf030e7 = this['_tutorialPageWindow'][_0x5e0d52(0x7f)]() + 0x1;
        _0xf030e7 >= this[_0x5e0d52(0x12e)]()['length']
            ? (SoundManager[_0x5e0d52(0x10e)](), this[_0x5e0d52(0x7e)]())
            : (this[_0x5e0d52(0xde)][_0x5e0d52(0x1da)](),
              this[_0x5e0d52(0xde)][_0x5e0d52(0x120)](_0xf030e7));
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x102)] = function (_0x46c3dd) {
        const _0x1b73ae = _0x5bb884;
        if (!VisuMZ[_0x1b73ae(0x1b7)][_0x1b73ae(0x101)](_0x46c3dd)) return;
        ((this[_0x1b73ae(0x138)] = _0x46c3dd[_0x1b73ae(0x84)]()[_0x1b73ae(0x12c)]()),
            (this[_0x1b73ae(0x123)] =
                VisuMZ[_0x1b73ae(0x1b7)][_0x1b73ae(0xe0)][this[_0x1b73ae(0x138)]]),
            (this[_0x1b73ae(0x154)] = 0x0));
        this[_0x1b73ae(0xde)] &&
            (_0x1b73ae(0xbc) !== _0x1b73ae(0xbc)
                ? (_0x51c8c8[_0x1b73ae(0xfd)][_0x1b73ae(0x69)][_0x1b73ae(0x198)](this),
                  this[_0x1b73ae(0x18a)]())
                : (this[_0x1b73ae(0xde)][_0x1b73ae(0x184)](),
                  this['_tutorialPageWindow']['activate'](),
                  this[_0x1b73ae(0xde)][_0x1b73ae(0x91)](),
                  this[_0x1b73ae(0xde)]['refresh']()));
        this[_0x1b73ae(0x10b)] &&
            (this['_tutorialDescriptionWindow']['show'](),
            this[_0x1b73ae(0x10b)][_0x1b73ae(0x18a)](!![]),
            this[_0x1b73ae(0x10b)][_0x1b73ae(0x159)]());
        this[_0x1b73ae(0x1ae)] &&
            (this[_0x1b73ae(0x1ae)][_0x1b73ae(0x184)](),
            this[_0x1b73ae(0x1ae)][_0x1b73ae(0x18a)](!![]),
            this[_0x1b73ae(0x1ae)][_0x1b73ae(0x159)]());
        if (this[_0x1b73ae(0x1cd)]) {
            if (_0x1b73ae(0x93) !== 'ofShl') {
                _0x5652a2['TutorialPanelSys'][_0x1b73ae(0x186)][_0x1b73ae(0x198)](this);
                const _0x1d05c6 = this['_commandWindow'];
                _0x1d05c6[_0x1b73ae(0xf9)](
                    _0x1b73ae(0x16a),
                    this['commandTutorialList']['bind'](this)
                );
            } else
                (this['_tutorialTitleWindow'][_0x1b73ae(0x184)](),
                    this[_0x1b73ae(0x1cd)][_0x1b73ae(0x175)](this[_0x1b73ae(0x1ca)]()));
        }
        $gameTroop[_0x1b73ae(0x16f)][_0x1b73ae(0x11d)]('tutorial');
    }),
    (Scene_Battle[_0x5bb884(0xfd)][_0x5bb884(0x7e)] = function () {
        const _0xb2df47 = _0x5bb884;
        if (this['_tutorialPageWindow']) {
            if (_0xb2df47(0x197) === _0xb2df47(0x197))
                (this[_0xb2df47(0xde)][_0xb2df47(0xf0)](),
                    this['_tutorialPageWindow'][_0xb2df47(0x14f)]());
            else {
                _0x143546 = _0xf25e7c[_0xb2df47(0x84)]()[_0xb2df47(0x12c)]();
                const _0x4b4bde = _0x5f39c1[_0xb2df47(0x1b7)][_0xb2df47(0xe0)][_0x5301fc];
                if (!_0x4b4bde) return;
                const _0x444f1f = _0x4b4bde['Title'];
                this['addCommand'](_0x444f1f, 'tutorial', !![], _0x327709);
            }
        }
        this[_0xb2df47(0x10b)] &&
            ('dOdiV' === _0xb2df47(0x16b)
                ? ((this[_0xb2df47(0x1ad)] = _0x5348ef), this[_0xb2df47(0x159)]())
                : this[_0xb2df47(0x10b)]['hide']());
        this['_tutorialPictureWindow'] &&
            (_0xb2df47(0x15a) === 'WiqPq'
                ? this[_0xb2df47(0x1ae)]['hide']()
                : (_0x2c2972[_0xb2df47(0x1b7)]['Window_MenuCommand_addOriginalCommands'][
                      _0xb2df47(0x198)
                  ](this),
                  this[_0xb2df47(0x152)]()));
        if (this[_0xb2df47(0x1cd)]) {
            if ('XCTUQ' !== _0xb2df47(0x79)) this[_0xb2df47(0x1cd)][_0xb2df47(0xf0)]();
            else {
                _0x3c86b3 = _0x258db7[_0xb2df47(0x84)]()[_0xb2df47(0x12c)]();
                if (!_0x276f02[_0xb2df47(0x1b7)][_0xb2df47(0x12a)][_0x27d1e2]) return 0x0;
                let _0x4bb144 = 0x0;
                const _0x5279fa = _0xa446a9[_0xb2df47(0x1b7)][_0xb2df47(0x12a)][_0x56736b];
                for (const _0x1dd44f of _0x5279fa) {
                    if (this['isTutorialKeyRegistered'](_0x1dd44f)) _0x4bb144++;
                }
                return _0x4bb144;
            }
        }
        $gameTroop[_0xb2df47(0x16f)][_0xb2df47(0x11d)]('');
    }),
    (VisuMZ[_0x5bb884(0x1b7)][_0x5bb884(0x96)] =
        Scene_Battle['prototype']['updateStatusWindowVisibility']),
    (Scene_Battle[_0x5bb884(0xfd)]['updateStatusWindowVisibility'] = function () {
        const _0x49ea2f = _0x5bb884;
        this[_0x49ea2f(0x11b)]()
            ? _0x49ea2f(0x187) === _0x49ea2f(0x187)
                ? this[_0x49ea2f(0x153)][_0x49ea2f(0x1cf)]()
                : this[_0x49ea2f(0x83)]['push'](_0x25a684)
            : VisuMZ[_0x49ea2f(0x1b7)]['Scene_Battle_updateStatusWindowVisibility']['call'](this);
    }),
    (Scene_Battle['prototype'][_0x5bb884(0x11b)] = function () {
        const _0x247f33 = _0x5bb884;
        if (!VisuMZ[_0x247f33(0x1b7)]['Settings'][_0x247f33(0x6f)][_0x247f33(0x170)]) return ![];
        return this[_0x247f33(0xde)] && this['_tutorialPageWindow'][_0x247f33(0x1c3)];
    }),
    (VisuMZ['TutorialPanelSys'][_0x5bb884(0xcd)] =
        Scene_Battle[_0x5bb884(0xfd)]['isAnyInputWindowActive']),
    (Scene_Battle['prototype'][_0x5bb884(0x8c)] = function () {
        const _0x102ea6 = _0x5bb884;
        if (this[_0x102ea6(0xde)] && this[_0x102ea6(0xde)][_0x102ea6(0x1c3)]) return !![];
        return VisuMZ[_0x102ea6(0x1b7)][_0x102ea6(0xcd)][_0x102ea6(0x198)](this);
    }));
function Scene_TutorialData() {
    this['initialize'](...arguments);
}
function _0x3e49(_0x56f73b, _0x554827) {
    const _0x1fddb5 = _0x1fdd();
    return (
        (_0x3e49 = function (_0x3e497b, _0x51db3b) {
            _0x3e497b = _0x3e497b - 0x67;
            let _0x43232b = _0x1fddb5[_0x3e497b];
            return _0x43232b;
        }),
        _0x3e49(_0x56f73b, _0x554827)
    );
}
((Scene_TutorialData[_0x5bb884(0xfd)] = Object['create'](Scene_MenuBase[_0x5bb884(0xfd)])),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x1a2)] = Scene_TutorialData),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function () {
        const _0x598d95 = _0x5bb884;
        (Scene_MenuBase[_0x598d95(0xfd)][_0x598d95(0xd9)][_0x598d95(0x198)](this),
            this[_0x598d95(0xb6)]());
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0xb6)] = function () {
        const _0x3b913b = _0x5bb884;
        ((this[_0x3b913b(0x138)] = $gameTemp[_0x3b913b(0x138)]['toLowerCase']()['trim']()),
            (this[_0x3b913b(0x123)] =
                VisuMZ['TutorialPanelSys']['Tutorials'][this[_0x3b913b(0x138)]]),
            (this[_0x3b913b(0x154)] = 0x0));
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)]['tutorialKey'] = function () {
        const _0x78d921 = _0x5bb884;
        return this[_0x78d921(0x138)];
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x178)] = function () {
        const _0x70f589 = _0x5bb884;
        return this[_0x70f589(0x123)] || {};
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0x1ca)] = function () {
        const _0x15d120 = _0x5bb884;
        return this[_0x15d120(0x178)]()['Title'] || '';
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0x12e)] = function () {
        const _0x5e097a = _0x5bb884;
        return this[_0x5e097a(0x178)]()['Pages'] || [];
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0x19f)] = function () {
        const _0x451445 = _0x5bb884;
        return this[_0x451445(0x154)];
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0x1d1)] = function () {
        const _0x4f93da = _0x5bb884;
        return this[_0x4f93da(0x12e)]()[this['tutorialPageIndex']()];
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)]['helpAreaHeight'] = function () {
        return 0x0;
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0xf5)] = function () {
        const _0x4650d8 = _0x5bb884;
        (Scene_MenuBase[_0x4650d8(0xfd)]['createBackground'][_0x4650d8(0x198)](this),
            this[_0x4650d8(0x180)](this['getBackgroundOpacity']()),
            this[_0x4650d8(0x160)]());
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)]['getBackgroundOpacity'] = function () {
        const _0x3e4145 = _0x5bb884;
        return VisuMZ[_0x3e4145(0x1b7)][_0x3e4145(0x6d)]['SceneTutorialData'][_0x3e4145(0x1de)];
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x160)] = function () {
        const _0x3eb202 = _0x5bb884,
            _0x5f4da4 = VisuMZ[_0x3eb202(0x1b7)][_0x3eb202(0x6d)][_0x3eb202(0xd5)];
        _0x5f4da4 &&
            (_0x5f4da4[_0x3eb202(0x67)] !== '' || _0x5f4da4['BgFilename2'] !== '') &&
            ((this['_backSprite1'] = new Sprite(
                ImageManager[_0x3eb202(0x191)](_0x5f4da4[_0x3eb202(0x67)])
            )),
            (this[_0x3eb202(0xc5)] = new Sprite(
                ImageManager[_0x3eb202(0xe8)](_0x5f4da4['BgFilename2'])
            )),
            this[_0x3eb202(0x9a)](this[_0x3eb202(0x13f)]),
            this[_0x3eb202(0x9a)](this[_0x3eb202(0xc5)]),
            this[_0x3eb202(0x13f)][_0x3eb202(0x113)][_0x3eb202(0x173)](
                this[_0x3eb202(0x167)][_0x3eb202(0xe6)](this, this['_backSprite1'])
            ),
            this['_backSprite2'][_0x3eb202(0x113)][_0x3eb202(0x173)](
                this[_0x3eb202(0x167)][_0x3eb202(0xe6)](this, this[_0x3eb202(0xc5)])
            ));
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x167)] = function (_0x2ea77b) {
        const _0x134e89 = _0x5bb884;
        (this['scaleSprite'](_0x2ea77b), this[_0x134e89(0xbd)](_0x2ea77b));
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0x183)] = function () {
        const _0x5c4374 = _0x5bb884;
        return this[_0x5c4374(0x12e)]()[_0x5c4374(0x7c)] > 0x0
            ? TextManager[_0x5c4374(0xe2)](_0x5c4374(0xa6), _0x5c4374(0x10d))
            : Scene_MenuBase[_0x5c4374(0xfd)]['buttonAssistKey1'][_0x5c4374(0x198)](this);
    }),
    (Scene_TutorialData['prototype']['buttonAssistText1'] = function () {
        return TextManager['tutorial']['changePage'];
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0xca)] = function () {
        const _0x599087 = _0x5bb884;
        if (this['_tutorialPageIndex'] === this[_0x599087(0x12e)]()[_0x599087(0x7c)] - 0x1)
            return TextManager[_0x599087(0xf2)][_0x599087(0x110)];
        else {
            if (_0x599087(0x171) === _0x599087(0x171))
                return TextManager['tutorial'][_0x599087(0x17d)];
            else
                (_0x436a83['ConvertParams'](_0x2f55b3, _0xef1fc5),
                    _0x5c8bd9['setMainMenuTutorialListVisible'](_0x2b6df9[_0x599087(0xd3)]));
        }
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x9f)] = function () {
        const _0x17dbe3 = _0x5bb884;
        return TextManager[_0x17dbe3(0xf2)][_0x17dbe3(0x110)];
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x94)] = function () {
        const _0x5d1770 = _0x5bb884;
        (Scene_MenuBase[_0x5d1770(0xfd)][_0x5d1770(0x94)][_0x5d1770(0x198)](this),
            this[_0x5d1770(0xc9)](),
            this[_0x5d1770(0xe5)](),
            this[_0x5d1770(0x73)](),
            this[_0x5d1770(0x132)]());
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0xc9)] = function () {
        const _0x519573 = _0x5bb884,
            _0x22ce98 = this[_0x519573(0x92)](),
            _0x3d7eb5 = new Window_TutorialPages(_0x22ce98);
        (this[_0x519573(0xed)](_0x3d7eb5),
            (this[_0x519573(0x193)] = _0x3d7eb5),
            _0x3d7eb5[_0x519573(0xf9)]('cancel', this[_0x519573(0xb8)][_0x519573(0xe6)](this)),
            _0x3d7eb5['setHandler']('page', this['nextPage']['bind'](this)),
            _0x3d7eb5[_0x519573(0x8d)](
                VisuMZ[_0x519573(0x1b7)][_0x519573(0x6d)][_0x519573(0xd5)]['PageWindow_BgType']
            ));
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x92)] = function () {
        const _0x5c54a5 = _0x5bb884;
        if (VisuMZ[_0x5c54a5(0x1b7)]['Settings'][_0x5c54a5(0xd5)]['PageWindow_RectJS'])
            return VisuMZ[_0x5c54a5(0x1b7)][_0x5c54a5(0x6d)][_0x5c54a5(0xd5)][_0x5c54a5(0x128)][
                _0x5c54a5(0x198)
            ](this);
        const _0x906192 = Math['max'](
                0x2d0,
                Math[_0x5c54a5(0x1e2)](Graphics[_0x5c54a5(0x11e)] * 0.75)
            ),
            _0x356273 = this[_0x5c54a5(0xef)](0x1, !![]),
            _0x46df53 = Math[_0x5c54a5(0x1e2)]((Graphics[_0x5c54a5(0x11e)] - _0x906192) / 0x2),
            _0xb51045 = this[_0x5c54a5(0x19b)]() - _0x356273;
        return new Rectangle(_0x46df53, _0xb51045, _0x906192, _0x356273);
    }),
    (Scene_TutorialData['prototype']['createDescriptionWindow'] = function () {
        const _0x48a98d = _0x5bb884,
            _0x46fb4f = this[_0x48a98d(0x10a)](),
            _0x22a9a9 = new Window_TutorialDescription(_0x46fb4f);
        (this[_0x48a98d(0xed)](_0x22a9a9),
            (this[_0x48a98d(0x1b9)] = _0x22a9a9),
            _0x22a9a9[_0x48a98d(0x8d)](
                VisuMZ[_0x48a98d(0x1b7)][_0x48a98d(0x6d)][_0x48a98d(0xd5)][_0x48a98d(0x14b)]
            ));
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x10a)] = function () {
        const _0x1afb3f = _0x5bb884;
        if (VisuMZ['TutorialPanelSys'][_0x1afb3f(0x6d)][_0x1afb3f(0xd5)][_0x1afb3f(0xe9)]) {
            if ('PgDcy' !== 'PgDcy') {
                if (this[_0x1afb3f(0x1cb)](_0xf747a2)) _0x304f68++;
            } else
                return VisuMZ['TutorialPanelSys'][_0x1afb3f(0x6d)][_0x1afb3f(0xd5)][
                    'DescWindow_RectJS'
                ]['call'](this);
        }
        const _0x171dd0 = Math[_0x1afb3f(0xab)](
                0x2d0,
                Math[_0x1afb3f(0x1e2)](Graphics[_0x1afb3f(0x11e)] * 0.75)
            ),
            _0x2f6e1c = this[_0x1afb3f(0xef)](0x4, ![]),
            _0x2d087e = Math[_0x1afb3f(0x1e2)]((Graphics['boxWidth'] - _0x171dd0) / 0x2),
            _0x464da6 = this['mainAreaBottom']() - _0x2f6e1c - this[_0x1afb3f(0xef)](0x1, !![]);
        return new Rectangle(_0x2d087e, _0x464da6, _0x171dd0, _0x2f6e1c);
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)]['createPictureWindow'] = function () {
        const _0x16406e = _0x5bb884,
            _0x27093c = this['pictureWindowRect'](),
            _0x34ff58 = new Window_TutorialPicture(_0x27093c);
        (this['addWindow'](_0x34ff58),
            (this['_pictureWindow'] = _0x34ff58),
            _0x34ff58[_0x16406e(0x8d)](
                VisuMZ[_0x16406e(0x1b7)]['Settings'][_0x16406e(0xd5)][_0x16406e(0x14e)]
            ),
            this[_0x16406e(0xed)](this[_0x16406e(0x1b9)]));
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)]['pictureWindowRect'] = function () {
        const _0x154fb3 = _0x5bb884;
        if (VisuMZ['TutorialPanelSys'][_0x154fb3(0x6d)][_0x154fb3(0xd5)][_0x154fb3(0x72)])
            return VisuMZ['TutorialPanelSys'][_0x154fb3(0x6d)]['SceneTutorialData'][
                _0x154fb3(0x72)
            ][_0x154fb3(0x198)](this, this[_0x154fb3(0x1b9)]);
        const _0x4f2e44 = this[_0x154fb3(0x1b9)],
            _0x3f4acd = _0x4f2e44['width'],
            _0xa92a8 =
                this[_0x154fb3(0x144)]() -
                _0x4f2e44[_0x154fb3(0x1a5)] -
                this[_0x154fb3(0xef)](0x1, !![]),
            _0x96a30e = _0x4f2e44['x'],
            _0x1abf20 = this[_0x154fb3(0x1d0)]();
        return new Rectangle(_0x96a30e, _0x1abf20, _0x3f4acd, _0xa92a8);
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x132)] = function () {
        const _0x2580be = _0x5bb884,
            _0x134f67 = this[_0x2580be(0xa9)](),
            _0x10083b = new Window_TutorialTitle(_0x134f67);
        (this['addWindow'](_0x10083b),
            (this['_titleWindow'] = _0x10083b),
            _0x10083b[_0x2580be(0x8d)](
                VisuMZ[_0x2580be(0x1b7)][_0x2580be(0x6d)][_0x2580be(0xd5)][_0x2580be(0xff)]
            ),
            _0x10083b['setText'](this[_0x2580be(0x1ca)]()));
    }),
    (Scene_TutorialData['prototype'][_0x5bb884(0xa9)] = function () {
        const _0x3c9477 = _0x5bb884;
        if (VisuMZ[_0x3c9477(0x1b7)]['Settings'][_0x3c9477(0xd5)][_0x3c9477(0x14a)])
            return VisuMZ[_0x3c9477(0x1b7)][_0x3c9477(0x6d)][_0x3c9477(0xd5)][_0x3c9477(0x14a)][
                _0x3c9477(0x198)
            ](this, this[_0x3c9477(0x1b9)]);
        const _0x10c73d = this[_0x3c9477(0x1b9)],
            _0x58aabc = Math['max'](_0x10c73d[_0x3c9477(0xda)] - 0x12c, 0x1e0),
            _0x2a41d7 = this[_0x3c9477(0xef)](0x1, ![]),
            _0x36febd = Math[_0x3c9477(0x1e2)]((Graphics[_0x3c9477(0x11e)] - _0x58aabc) / 0x2),
            _0x2b62d1 = _0x10c73d['y'] - Math[_0x3c9477(0x1e2)](_0x2a41d7 / 0x2);
        return new Rectangle(_0x36febd, _0x2b62d1, _0x58aabc, _0x2a41d7);
    }),
    (Scene_TutorialData[_0x5bb884(0xfd)][_0x5bb884(0x17d)] = function () {
        const _0x4f93f5 = _0x5bb884;
        let _0x2b714f = this[_0x4f93f5(0x193)]['index']() + 0x1;
        _0x2b714f >= this[_0x4f93f5(0x12e)]()[_0x4f93f5(0x7c)]
            ? (SoundManager[_0x4f93f5(0x10e)](), this[_0x4f93f5(0xb8)]())
            : (this['_pageWindow'][_0x4f93f5(0x1da)](),
              this[_0x4f93f5(0x193)][_0x4f93f5(0x120)](_0x2b714f));
    }));
function Scene_TutorialList() {
    const _0x30980e = _0x5bb884;
    this[_0x30980e(0xd9)](...arguments);
}
((Scene_TutorialList[_0x5bb884(0xfd)] = Object[_0x5bb884(0x94)](Scene_MenuBase[_0x5bb884(0xfd)])),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x1a2)] = Scene_TutorialList),
    (Scene_TutorialList[_0x5bb884(0xfd)]['initialize'] = function () {
        const _0x9ea44a = _0x5bb884;
        Scene_MenuBase[_0x9ea44a(0xfd)][_0x9ea44a(0xd9)]['call'](this);
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0xf5)] = function () {
        const _0x331461 = _0x5bb884;
        (Scene_MenuBase[_0x331461(0xfd)][_0x331461(0xf5)]['call'](this),
            this[_0x331461(0x180)](this['getBackgroundOpacity']()),
            this[_0x331461(0x160)]());
    }),
    (Scene_TutorialList['prototype'][_0x5bb884(0x88)] = function () {
        const _0x26ad7c = _0x5bb884;
        return VisuMZ[_0x26ad7c(0x1b7)][_0x26ad7c(0x6d)]['SceneTutorialList']['SnapshotOpacity'];
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)]['createCustomBackgroundImages'] = function () {
        const _0x37fa17 = _0x5bb884,
            _0x121e71 = VisuMZ[_0x37fa17(0x1b7)][_0x37fa17(0x6d)][_0x37fa17(0x17c)];
        _0x121e71 &&
            (_0x121e71['BgFilename1'] !== '' || _0x121e71[_0x37fa17(0x9c)] !== '') &&
            ((this[_0x37fa17(0x13f)] = new Sprite(
                ImageManager[_0x37fa17(0x191)](_0x121e71['BgFilename1'])
            )),
            (this[_0x37fa17(0xc5)] = new Sprite(
                ImageManager['loadTitle2'](_0x121e71['BgFilename2'])
            )),
            this[_0x37fa17(0x9a)](this[_0x37fa17(0x13f)]),
            this[_0x37fa17(0x9a)](this[_0x37fa17(0xc5)]),
            this['_backSprite1']['bitmap'][_0x37fa17(0x173)](
                this[_0x37fa17(0x167)][_0x37fa17(0xe6)](this, this[_0x37fa17(0x13f)])
            ),
            this[_0x37fa17(0xc5)]['bitmap'][_0x37fa17(0x173)](
                this['adjustSprite']['bind'](this, this[_0x37fa17(0xc5)])
            ));
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x167)] = function (_0x41a8e2) {
        const _0x21564c = _0x5bb884;
        (this[_0x21564c(0xbb)](_0x41a8e2), this[_0x21564c(0xbd)](_0x41a8e2));
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0xca)] = function () {
        const _0x3c509f = _0x5bb884;
        if (this[_0x3c509f(0xc4)] && this[_0x3c509f(0xc4)]['active']) {
            if (_0x3c509f(0x1d6) !== 'pdGRz') {
                if (!_0xb8509d && _0x5baafd && _0x3d7a8a[_0x3c509f(0x1cb)](_0xd7f5aa)) return;
                ((_0x1b82bf || _0x227418[_0x3c509f(0x77)]) && _0x5f04ae['openTutorial'](_0x1d5d14),
                    _0x2bce62 && _0x396503['registerTutorialKey'](_0xd7da08));
            } else {
                const _0x3528d9 = this[_0x3c509f(0xc4)][_0x3c509f(0x9e)]();
                if (_0x3528d9 === _0x3c509f(0x1d9))
                    return this[_0x3c509f(0xc4)][_0x3c509f(0x107)](
                        this[_0x3c509f(0xc4)][_0x3c509f(0x17b)]()
                    )
                        ? TextManager[_0x3c509f(0xf2)][_0x3c509f(0x14c)]
                        : TextManager[_0x3c509f(0xf2)]['expand'];
                else {
                    if (_0x3528d9 === _0x3c509f(0xf2))
                        return TextManager['tutorial'][_0x3c509f(0x76)];
                }
            }
        }
        return Scene_MenuBase[_0x3c509f(0xfd)][_0x3c509f(0xca)]['call'](this);
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x94)] = function () {
        const _0x214f24 = _0x5bb884;
        (Scene_MenuBase[_0x214f24(0xfd)][_0x214f24(0x94)]['call'](this), this[_0x214f24(0x190)]());
        if (this[_0x214f24(0xfb)]) this[_0x214f24(0xfb)][_0x214f24(0x69)]();
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x190)] = function () {
        const _0x16f34c = _0x5bb884,
            _0x2380d5 = this[_0x16f34c(0x1bc)](),
            _0x40b921 = new Window_TutorialList(_0x2380d5);
        (this[_0x16f34c(0xed)](_0x40b921),
            (this[_0x16f34c(0xc4)] = _0x40b921),
            _0x40b921[_0x16f34c(0xf9)](
                _0x16f34c(0x1a3),
                this[_0x16f34c(0xb8)][_0x16f34c(0xe6)](this)
            ),
            _0x40b921['setHandler']('category', this['onListCategory'][_0x16f34c(0xe6)](this)),
            _0x40b921[_0x16f34c(0xf9)](_0x16f34c(0xf2), this['onListTutorial']['bind'](this)),
            _0x40b921['setBackgroundType'](
                VisuMZ[_0x16f34c(0x1b7)][_0x16f34c(0x6d)][_0x16f34c(0x17c)]['ListWindow_BgType']
            ));
    }),
    (Scene_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x1bc)] = function () {
        const _0x41b268 = _0x5bb884;
        if (VisuMZ[_0x41b268(0x1b7)][_0x41b268(0x6d)][_0x41b268(0x17c)][_0x41b268(0x1be)])
            return 'NEAuE' === _0x41b268(0x1c4)
                ? this[_0x41b268(0x123)] || {}
                : VisuMZ['TutorialPanelSys'][_0x41b268(0x6d)][_0x41b268(0x17c)][_0x41b268(0x1be)][
                      'call'
                  ](this);
        const _0x122df2 = Math[_0x41b268(0xab)](
                0x2d0,
                Math[_0x41b268(0x1e2)](Graphics[_0x41b268(0x11e)] * 0.75)
            ),
            _0x2a9786 = Math[_0x41b268(0xab)](_0x122df2 - 0x12c, 0x1e0),
            _0x3230bc = this['calcWindowHeight'](0xa, !![]),
            _0x4fb542 = Math['floor']((Graphics['boxWidth'] - _0x2a9786) / 0x2),
            _0x5742ea = Math['floor']((Graphics['boxHeight'] - _0x3230bc) / 0x2);
        return new Rectangle(_0x4fb542, _0x5742ea, _0x2a9786, _0x3230bc);
    }),
    (Scene_TutorialList['prototype'][_0x5bb884(0x1c2)] = function () {
        const _0x281226 = _0x5bb884;
        (this[_0x281226(0xc4)][_0x281226(0x117)](), this[_0x281226(0xc4)][_0x281226(0x1da)]());
    }),
    (Scene_TutorialList['prototype'][_0x5bb884(0x189)] = function () {
        const _0x5b8ec3 = _0x5bb884,
            _0x13d12d = this[_0x5b8ec3(0xc4)]['currentExt']() || '';
        if (_0x13d12d === '') {
            if (_0x5b8ec3(0x142) !== _0x5b8ec3(0x142))
                return _0x3daad5[_0x5b8ec3(0xf2)][_0x5b8ec3(0x14c)];
            else {
                this[_0x5b8ec3(0xc4)][_0x5b8ec3(0x1da)]();
                return;
            }
        }
        (($gameTemp['_returnTutorialKey'] = _0x13d12d), $gameTemp['openTutorial'](_0x13d12d));
    }),
    (VisuMZ[_0x5bb884(0x1b7)]['Window_MenuCommand_addOriginalCommands'] =
        Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0xb4)]),
    (Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0xb4)] = function () {
        const _0x2ec087 = _0x5bb884;
        (VisuMZ[_0x2ec087(0x1b7)][_0x2ec087(0x114)]['call'](this),
            this['addTutorialListCommand']());
    }),
    (Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0x152)] = function () {
        const _0x4a45af = _0x5bb884;
        if (!this['addTutorialListCommandAutomatically']()) return;
        if (!this[_0x4a45af(0x82)]()) return;
        const _0xa4347 = TextManager[_0x4a45af(0xf2)][_0x4a45af(0x1c6)],
            _0x56498e = this[_0x4a45af(0x1a0)]();
        this[_0x4a45af(0x146)](_0xa4347, _0x4a45af(0x16a), _0x56498e);
    }),
    (Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0x192)] = function () {
        const _0x2e4649 = _0x5bb884;
        return Imported[_0x2e4649(0x165)] ? ![] : !![];
    }),
    (Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0x82)] = function () {
        const _0x49fc20 = _0x5bb884;
        return $gameSystem[_0x49fc20(0x129)]();
    }),
    (Window_MenuCommand[_0x5bb884(0xfd)][_0x5bb884(0x1a0)] = function () {
        const _0x131d2a = _0x5bb884;
        return $gameSystem[_0x131d2a(0x1c5)]();
    }),
    (VisuMZ['TutorialPanelSys']['Window_Options_addGeneralOptions'] =
        Window_Options[_0x5bb884(0xfd)][_0x5bb884(0x1d5)]),
    (Window_Options[_0x5bb884(0xfd)][_0x5bb884(0x1d5)] = function () {
        const _0x5ae02b = _0x5bb884;
        (VisuMZ['TutorialPanelSys']['Window_Options_addGeneralOptions'][_0x5ae02b(0x198)](this),
            this[_0x5ae02b(0x162)]());
    }),
    (Window_Options['prototype'][_0x5bb884(0x162)] = function () {
        const _0x2f22e1 = _0x5bb884;
        VisuMZ['TutorialPanelSys']['Settings'][_0x2f22e1(0x16d)][_0x2f22e1(0x1ba)] &&
            this[_0x2f22e1(0xfe)]();
    }),
    (Window_Options['prototype'][_0x5bb884(0xfe)] = function () {
        const _0x516b20 = _0x5bb884,
            _0xb7b1da = TextManager[_0x516b20(0xf2)][_0x516b20(0x81)],
            _0x36e514 = _0x516b20(0x77);
        this[_0x516b20(0x146)](_0xb7b1da, _0x36e514);
    }));
function Window_TutorialPages() {
    const _0x3aea80 = _0x5bb884;
    this[_0x3aea80(0xd9)](...arguments);
}
((Window_TutorialPages[_0x5bb884(0xfd)] = Object['create'](Window_HorzCommand['prototype'])),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x1a2)] = Window_TutorialPages),
    (Window_TutorialPages['prototype'][_0x5bb884(0xd9)] = function (_0x4aebe0) {
        const _0x56be30 = _0x5bb884;
        ((this[_0x56be30(0x106)] = 0x0),
            Window_HorzCommand[_0x56be30(0xfd)][_0x56be30(0xd9)][_0x56be30(0x198)](this, _0x4aebe0),
            this['select'](0x0),
            this[_0x56be30(0x159)]());
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x91)] = function () {
        const _0x459c30 = _0x5bb884;
        (delete this[_0x459c30(0x149)],
            delete this[_0x459c30(0x1c8)],
            (this[_0x459c30(0x106)] = 0x0),
            (this[_0x459c30(0x181)] = 0x0));
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x1bf)] = function () {
        const _0x2c12ca = _0x5bb884;
        if (this['_maxColCache'] !== undefined) return this['_maxColCache'];
        return (
            (this[_0x2c12ca(0x149)] =
                SceneManager[_0x2c12ca(0xa0)]['tutorialPages']()[_0x2c12ca(0x7c)]),
            this[_0x2c12ca(0x149)]
        );
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)]['colSpacing'] = function () {
        return 0x0;
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)]['itemWidth'] = function () {
        const _0x310e5c = _0x5bb884;
        if (this[_0x310e5c(0x1c8)] !== undefined) return this[_0x310e5c(0x1c8)];
        const _0xa36d16 = TextManager[_0x310e5c(0xf2)][_0x310e5c(0xf6)],
            _0x353f09 = TextManager['tutorial'][_0x310e5c(0x1c9)],
            _0x31b3a2 = Math['max'](
                this['textSizeEx'](_0xa36d16)[_0x310e5c(0xda)],
                this[_0x310e5c(0x140)](_0x353f09)['width']
            );
        return (
            (this[_0x310e5c(0x1c8)] = Math['ceil'](_0x31b3a2 + 0x1 * this[_0x310e5c(0x147)]())),
            this[_0x310e5c(0x1c8)]
        );
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x17e)] = function (_0x5e63cd) {
        const _0x39ce99 = _0x5bb884,
            _0x433c79 = this[_0x39ce99(0x1bf)](),
            _0x2f7c89 = this['itemWidth'](),
            _0x51aea8 = this[_0x39ce99(0x194)](),
            _0x52066c = _0x2f7c89 * _0x433c79,
            _0x2ba6c9 =
                Math['floor']((this[_0x39ce99(0x80)] - _0x52066c) / 0x2) + _0x5e63cd * _0x2f7c89,
            _0x544b56 = 0x0,
            _0x259518 = _0x2f7c89,
            _0xdfb30d = _0x51aea8;
        return new Rectangle(_0x2ba6c9, _0x544b56, _0x259518, _0xdfb30d);
    }),
    (Window_TutorialPages['prototype']['makeCommandList'] = function () {
        const _0xda0b1a = _0x5bb884;
        let _0x22f4e9 = this[_0xda0b1a(0x1bf)]();
        while (_0x22f4e9--) {
            if (_0xda0b1a(0xa4) !== _0xda0b1a(0xa4))
                return (
                    (_0x31daaa = _0x1faae6[_0xda0b1a(0x84)]()['trim']()),
                    this['_categoryStatus'][_0x418fd9]
                );
            else
                this[_0xda0b1a(0x146)](
                    '',
                    _0xda0b1a(0x18c),
                    !![],
                    this[_0xda0b1a(0x1a9)][_0xda0b1a(0x7c)]
                );
        }
    }),
    (Window_TutorialPages['prototype']['drawItemBackground'] = function (_0xbd1f26) {}),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x1ac)] = function (_0x51dbcd) {
        const _0x3a6680 = _0x5bb884,
            _0xa08282 = this[_0x3a6680(0x6b)](_0x51dbcd),
            _0x384c2e = this[_0x3a6680(0x108)](_0x51dbcd),
            _0x4af335 = this[_0x3a6680(0x140)](_0x384c2e)['width'];
        this[_0x3a6680(0x176)](this['isCommandEnabled'](_0x51dbcd));
        const _0x56f9f8 = this[_0x3a6680(0x8e)]();
        if (_0x56f9f8 === _0x3a6680(0x10d))
            this[_0x3a6680(0x13b)](
                _0x384c2e,
                _0xa08282['x'] + _0xa08282[_0x3a6680(0xda)] - _0x4af335,
                _0xa08282['y'],
                _0x4af335
            );
        else {
            if (_0x56f9f8 === _0x3a6680(0x1d4)) {
                if ('YKsNN' === _0x3a6680(0x86)) {
                    if (this[_0x3a6680(0x195)] === _0x3a6680(0xf2)) return !![];
                    return _0x29582a[_0x3a6680(0x1b7)][_0x3a6680(0xa7)][_0x3a6680(0x198)](this);
                } else {
                    const _0x4046ce =
                        _0xa08282['x'] +
                        Math[_0x3a6680(0x1e2)]((_0xa08282['width'] - _0x4af335) / 0x2);
                    this[_0x3a6680(0x13b)](_0x384c2e, _0x4046ce, _0xa08282['y'], _0x4af335);
                }
            } else this[_0x3a6680(0x13b)](_0x384c2e, _0xa08282['x'], _0xa08282['y'], _0x4af335);
        }
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)]['commandName'] = function (_0x2e939d) {
        const _0x14d954 = _0x5bb884;
        if (_0x2e939d === SceneManager[_0x14d954(0xa0)]['tutorialPageIndex']())
            return TextManager[_0x14d954(0xf2)][_0x14d954(0xf6)];
        else {
            if (_0x14d954(0x166) !== _0x14d954(0x166))
                (_0x248a66[_0x14d954(0xfd)][_0x14d954(0x120)][_0x14d954(0x198)](this, _0x22cb57),
                    this['_lastIndex'] !== this['_index'] &&
                        (this[_0x14d954(0x106)] >= 0x0 &&
                            this['_index'] >= 0x0 &&
                            _0x9a47b['playTutorialPageChange'](),
                        (this[_0x14d954(0x106)] = this[_0x14d954(0x181)]),
                        (_0x330d53[_0x14d954(0xa0)]['_tutorialPageIndex'] = this['_index']),
                        this[_0x14d954(0x159)]()));
            else return TextManager[_0x14d954(0xf2)][_0x14d954(0x1c9)];
        }
    }),
    (Window_TutorialPages['prototype'][_0x5bb884(0x120)] = function (_0x54c79f) {
        const _0x16811e = _0x5bb884;
        (Window_HorzCommand[_0x16811e(0xfd)][_0x16811e(0x120)]['call'](this, _0x54c79f),
            this[_0x16811e(0x106)] !== this[_0x16811e(0x181)] &&
                (this[_0x16811e(0x106)] >= 0x0 &&
                    this['_index'] >= 0x0 &&
                    SoundManager['playTutorialPageChange'](),
                (this[_0x16811e(0x106)] = this[_0x16811e(0x181)]),
                (SceneManager[_0x16811e(0xa0)][_0x16811e(0x154)] = this[_0x16811e(0x181)]),
                this['refresh']()));
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0xa3)] = function () {
        const _0x28e0ff = _0x5bb884;
        this[_0x28e0ff(0x1a4)](0x0, 0x0, 0x0, 0x0);
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x8e)] = function () {
        const _0x5068fd = _0x5bb884;
        return _0x5068fd(0x1d4);
    }),
    (Window_TutorialPages['prototype']['isMenuCursorBlacklisted'] = function () {
        return !![];
    }),
    (Window_TutorialPages['prototype'][_0x5bb884(0x17a)] = function () {}),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x7b)] = function () {}),
    (Window_TutorialPages['prototype']['isUseModernControls'] = function () {
        return ![];
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x158)] = function (_0x2584cc) {
        const _0x4ddc0d = _0x5bb884;
        Window_HorzCommand['prototype'][_0x4ddc0d(0x158)][_0x4ddc0d(0x198)](this, ![]);
    }),
    (Window_TutorialPages['prototype']['cursorLeft'] = function (_0x12dd8f) {
        const _0x53f17f = _0x5bb884;
        Window_HorzCommand[_0x53f17f(0xfd)][_0x53f17f(0xac)][_0x53f17f(0x198)](this, ![]);
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)]['cursorPagedown'] = function () {
        const _0x4bca72 = _0x5bb884;
        this[_0x4bca72(0x158)]();
    }),
    (Window_TutorialPages['prototype']['cursorPageup'] = function () {
        const _0x18df81 = _0x5bb884;
        this[_0x18df81(0xac)]();
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)]['onTouchSelect'] = function (_0x521472) {
        const _0x39d64d = _0x5bb884;
        if (_0x521472) return;
        Window_HorzCommand[_0x39d64d(0xfd)][_0x39d64d(0x119)][_0x39d64d(0x198)](this, _0x521472);
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0x1a6)] = function () {
        const _0x28d24c = _0x5bb884,
            _0x5f554b = this['hitIndex']();
        if (_0x5f554b >= 0x0) return;
        const _0x236dbf = 0x3c;
        if (TouchInput['y'] < _0x236dbf || TouchInput['y'] > Graphics['height'] - _0x236dbf) return;
        if (TouchInput['x'] >= Graphics['width'] / 0x2) {
            const _0x4bb93e = Math['min'](this['index']() + 0x1, this['maxCols']() - 0x1);
            this[_0x28d24c(0x120)](_0x4bb93e);
        } else {
            if (TouchInput['x'] < Graphics[_0x28d24c(0xda)] / 0x2) {
                if (_0x28d24c(0xcb) === _0x28d24c(0xcb)) {
                    const _0x1ad907 = Math[_0x28d24c(0xab)](this[_0x28d24c(0x7f)]() - 0x1, 0x0);
                    this[_0x28d24c(0x120)](_0x1ad907);
                } else this['initialize'](...arguments);
            }
        }
    }),
    (Window_TutorialPages[_0x5bb884(0xfd)][_0x5bb884(0xf3)] = function () {
        const _0x5f3860 = _0x5bb884;
        if (this[_0x5f3860(0x10f)]() && !this[_0x5f3860(0x1d7)]()) {
            if (_0x5f3860(0x1c0) === _0x5f3860(0x1cc)) {
                const _0xd44cee = this[_0x5f3860(0x1bc)](),
                    _0x4f3b3a = new _0x251530(_0xd44cee);
                (this[_0x5f3860(0xed)](_0x4f3b3a),
                    (this[_0x5f3860(0xc4)] = _0x4f3b3a),
                    _0x4f3b3a[_0x5f3860(0xf9)](
                        _0x5f3860(0x1a3),
                        this[_0x5f3860(0xb8)][_0x5f3860(0xe6)](this)
                    ),
                    _0x4f3b3a['setHandler'](_0x5f3860(0x1d9), this[_0x5f3860(0x1c2)]['bind'](this)),
                    _0x4f3b3a[_0x5f3860(0xf9)](
                        _0x5f3860(0xf2),
                        this['onListTutorial'][_0x5f3860(0xe6)](this)
                    ),
                    _0x4f3b3a['setBackgroundType'](
                        _0x604895[_0x5f3860(0x1b7)][_0x5f3860(0x6d)][_0x5f3860(0x17c)][
                            _0x5f3860(0xb7)
                        ]
                    ));
            } else {
                const _0x5b2be8 = 0x14;
                if (TouchInput[_0x5f3860(0x90)] >= _0x5b2be8) {
                    if ('iWRCH' !== _0x5f3860(0x139)) return this['_lastPluginCommandInterpreter'];
                    else {
                        const _0xe6c1a1 = Math[_0x5f3860(0x1df)](
                            this[_0x5f3860(0x7f)]() + 0x1,
                            this[_0x5f3860(0x1bf)]() - 0x1
                        );
                        this[_0x5f3860(0x120)](_0xe6c1a1);
                    }
                }
                if (TouchInput[_0x5f3860(0x90)] <= -_0x5b2be8) {
                    if (_0x5f3860(0xdb) !== 'kYuBC') {
                        const _0x23b85f = Math[_0x5f3860(0xab)](this['index']() - 0x1, 0x0);
                        this[_0x5f3860(0x120)](_0x23b85f);
                    } else return _0x5f8d54['tutorial'][_0x5f3860(0x110)];
                }
            }
        }
    }));
function Window_TutorialDescription() {
    this['initialize'](...arguments);
}
((Window_TutorialDescription[_0x5bb884(0xfd)] = Object[_0x5bb884(0x94)](
    Window_Base[_0x5bb884(0xfd)]
)),
    (Window_TutorialDescription['prototype'][_0x5bb884(0x1a2)] = Window_TutorialDescription),
    (Window_TutorialDescription[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function (_0x578662) {
        const _0x23a60e = _0x5bb884;
        (Window_Base[_0x23a60e(0xfd)]['initialize'][_0x23a60e(0x198)](this, _0x578662),
            (this['_text'] = ''),
            (this[_0x23a60e(0xd6)] = -0x1));
    }),
    (Window_TutorialDescription[_0x5bb884(0xfd)][_0x5bb884(0x175)] = function (_0x8d29f8) {
        const _0x1556de = _0x5bb884;
        this[_0x1556de(0x1ad)] !== _0x8d29f8 &&
            ((this[_0x1556de(0x1ad)] = _0x8d29f8), this[_0x1556de(0x159)]());
    }),
    (Window_TutorialDescription['prototype'][_0x5bb884(0x159)] = function () {
        const _0x3cd483 = _0x5bb884;
        this[_0x3cd483(0xba)]['clear']();
        const _0x29680b = this['_text'],
            _0x5a40de = this[_0x3cd483(0x140)](_0x29680b),
            _0x17551d = Math[_0x3cd483(0x1e2)]((this['innerWidth'] - _0x5a40de['width']) / 0x2),
            _0xb78b23 = Math['floor']((this[_0x3cd483(0x1b4)] - _0x5a40de[_0x3cd483(0x1a5)]) / 0x2);
        this[_0x3cd483(0x13b)](_0x29680b, _0x17551d, _0xb78b23);
    }),
    (Window_TutorialDescription[_0x5bb884(0xfd)][_0x5bb884(0x69)] = function () {
        const _0x45c52d = _0x5bb884;
        (Window_Base[_0x45c52d(0xfd)][_0x45c52d(0x69)][_0x45c52d(0x198)](this),
            this[_0x45c52d(0x18a)]());
    }),
    (Window_TutorialDescription[_0x5bb884(0xfd)]['updatePage'] = function (_0x1b885d) {
        const _0x4eaca1 = _0x5bb884;
        if (!_0x1b885d && this['_lastPage'] === SceneManager[_0x4eaca1(0xa0)][_0x4eaca1(0x19f)]())
            return;
        const _0x3ac08f = SceneManager[_0x4eaca1(0xa0)];
        this[_0x4eaca1(0xd6)] = _0x3ac08f[_0x4eaca1(0x19f)]();
        const _0x23648b = _0x3ac08f[_0x4eaca1(0x1d1)]()
            ? _0x3ac08f[_0x4eaca1(0x1d1)]()[_0x4eaca1(0xae)] || ''
            : '';
        this[_0x4eaca1(0x175)](_0x23648b[_0x4eaca1(0x12c)]());
    }));
function Window_TutorialPicture() {
    const _0xd0e3df = _0x5bb884;
    this[_0xd0e3df(0xd9)](...arguments);
}
((Window_TutorialPicture[_0x5bb884(0xfd)] = Object[_0x5bb884(0x94)](Window_Base['prototype'])),
    (Window_TutorialPicture[_0x5bb884(0xfd)]['constructor'] = Window_TutorialPicture),
    (Window_TutorialPicture[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function (_0x1da0e4) {
        const _0xe41b31 = _0x5bb884;
        (Window_Base[_0xe41b31(0xfd)][_0xe41b31(0xd9)][_0xe41b31(0x198)](this, _0x1da0e4),
            this[_0xe41b31(0x13c)](),
            (this[_0xe41b31(0xcc)] = ''),
            (this['_lastPage'] = -0x1));
    }),
    (Window_TutorialPicture['prototype'][_0x5bb884(0x13c)] = function () {
        const _0x1b39b7 = _0x5bb884;
        ((this[_0x1b39b7(0x103)] = new Sprite()),
            this[_0x1b39b7(0x9a)](this['_pictureSprite']),
            (this[_0x1b39b7(0x103)][_0x1b39b7(0x143)]['x'] = 0.5),
            (this[_0x1b39b7(0x103)][_0x1b39b7(0x143)]['y'] = 0.5),
            (this['_pictureSprite']['x'] = Math['floor'](this[_0x1b39b7(0xda)] / 0x2)),
            (this[_0x1b39b7(0x103)]['y'] = Math['floor'](this['height'] / 0x2)));
    }),
    (Window_TutorialPicture['prototype'][_0x5bb884(0xf4)] = function (_0x26b0cc) {
        const _0x295e93 = _0x5bb884;
        if (!this[_0x295e93(0x103)]) return;
        this[_0x295e93(0xcc)] !== _0x26b0cc &&
            (_0x295e93(0x130) === _0x295e93(0x130)
                ? ((this[_0x295e93(0xcc)] = _0x26b0cc), this[_0x295e93(0x159)]())
                : ((this[_0x295e93(0xcc)] = _0xca2624), this['refresh']()));
    }),
    (Window_TutorialPicture[_0x5bb884(0xfd)][_0x5bb884(0x159)] = function () {
        const _0x596e84 = _0x5bb884;
        this[_0x596e84(0xcc)] !== ''
            ? (this[_0x596e84(0x103)][_0x596e84(0x113)] = ImageManager[_0x596e84(0x172)](
                  this[_0x596e84(0xcc)]
              ))
            : (this[_0x596e84(0x103)][_0x596e84(0x113)] = new Bitmap(0x1, 0x1));
    }),
    (Window_TutorialPicture[_0x5bb884(0xfd)][_0x5bb884(0x69)] = function () {
        const _0x25c0d3 = _0x5bb884;
        (Window_Base[_0x25c0d3(0xfd)][_0x25c0d3(0x69)]['call'](this), this[_0x25c0d3(0x18a)]());
    }),
    (Window_TutorialPicture[_0x5bb884(0xfd)][_0x5bb884(0x18a)] = function (_0x10172b) {
        const _0x2bc923 = _0x5bb884;
        if (
            !_0x10172b &&
            this[_0x2bc923(0xd6)] === SceneManager[_0x2bc923(0xa0)][_0x2bc923(0x19f)]()
        )
            return;
        const _0x5c7c5d = SceneManager[_0x2bc923(0xa0)];
        this['_lastPage'] = _0x5c7c5d[_0x2bc923(0x19f)]();
        const _0x167418 = _0x5c7c5d[_0x2bc923(0x1d1)]()
            ? _0x5c7c5d[_0x2bc923(0x1d1)]()[_0x2bc923(0x7d)] || ''
            : '';
        this[_0x2bc923(0xf4)](_0x167418);
    }));
function _0x1fdd() {
    const _0x284e56 = [
        'maxCols',
        'SgZob',
        'description',
        'onListCategory',
        'active',
        'FrSoB',
        'isMainMenuTutorialListEnabled',
        'menuCmd',
        'currentExt',
        '_itemWidthCache',
        'inactivePage',
        'tutorialTitle',
        'isTutorialKeyRegistered',
        'MuLzn',
        '_tutorialTitleWindow',
        'applyData',
        'close',
        'mainAreaTop',
        'currentTutorialPage',
        '742189JRIVfo',
        'maxCommands',
        'center',
        'addGeneralOptions',
        'pdGRz',
        'isTouchedInsideFrame',
        'VocabInactivePage',
        'category',
        'activate',
        'process_VisuMZ_TutorialData',
        'registerTutorialKey',
        'VocabExpand',
        'SnapshotOpacity',
        'min',
        'CategoryData',
        '1167355MQEKVC',
        'floor',
        'BgFilename1',
        'EICFy',
        'update',
        'setMainMenuTutorialListVisible',
        'itemLineRect',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'Settings',
        'SystemEnableTutorialListMenu',
        'SceneBattle',
        'reselectReturnTutorialKey',
        'pNrdr',
        'PictureWindow_RectJS',
        'createPictureWindow',
        'KeyIDs',
        'Scene_Options_maxCommands',
        'view',
        'showTutorials',
        'findIndex',
        'HTfQY',
        'HJuEF',
        'playOkSound',
        'length',
        'Filename',
        'closeTutorial',
        'index',
        'innerWidth',
        'optionsCmd',
        'isTutorialListCommandVisible',
        '_visibleTutorialKeys',
        'toLowerCase',
        'version',
        'wZQDv',
        'Key',
        'getBackgroundOpacity',
        '_lastPluginCommandInterpreter',
        'openCategoriesFmt',
        'ARRAYSTRUCT',
        'isAnyInputWindowActive',
        'setBackgroundType',
        'itemTextAlign',
        'isSceneMap',
        'wheelY',
        'clearCache',
        'pageWindowRect',
        'ofShl',
        'create',
        'VocabView',
        'Scene_Battle_updateStatusWindowVisibility',
        'filter',
        'YdkMf',
        'eKNex',
        'addChild',
        'process_VisuMZ_TutorialCategories',
        'BgFilename2',
        'makeTutorialList',
        'currentSymbol',
        'buttonAssistText5',
        '_scene',
        'tutorialKey',
        'duEry',
        'refreshCursor',
        'RbGTa',
        'ConvertParams',
        'left',
        'Game_Interpreter_updateWaitMode',
        'createTutorialWindows',
        'titleWindowRect',
        'tutorialTitleWindowRect',
        'max',
        'cursorLeft',
        'Title',
        'Description',
        'createAllWindows',
        'LGkHR',
        'maxVisibleItems',
        'setLastPluginCommandInterpreter',
        'Scene_Boot_onDatabaseLoaded',
        'addOriginalCommands',
        'playTutorialPageChange',
        'initTutorialMembers',
        'ListWindow_BgType',
        'popScene',
        'getTutorialCategoryPopulation',
        'contents',
        'scaleSprite',
        'KThEO',
        'centerSprite',
        'addTutorial',
        'eBKIO',
        'DefaultUnlocked',
        'STR',
        'smoothSelect',
        '6035736yfdhLP',
        '_listWindow',
        '_backSprite2',
        'ShowMainMenu',
        'uZNzi',
        'initCategoryStatus',
        'createPageWindow',
        'buttonAssistText4',
        'KTsOj',
        '_filename',
        'Scene_Battle_isAnyInputWindowActive',
        'buttonAreaHeight',
        'ZVIRq',
        'wait',
        'Rbust',
        'STRUCT',
        'Show',
        '16366020xUmXSs',
        'SceneTutorialData',
        '_lastPage',
        'tutorialDescriptionWindowRect',
        'EVAL',
        'initialize',
        'width',
        'aOEtM',
        'unlisted',
        'uMWxC',
        '_tutorialPageWindow',
        'VocabDoneTutorial',
        'Tutorials',
        'format',
        'getInputMultiButtonStrings',
        'Scene_Battle_initialize',
        '_returnTutorialKey',
        'createDescriptionWindow',
        'bind',
        'lUGbg',
        'loadTitle2',
        'DescWindow_RectJS',
        'includes',
        'xkZnD',
        'ARRAYSTR',
        'addWindow',
        'playSe',
        'calcWindowHeight',
        'hide',
        'Game_System_initialize',
        'tutorial',
        'processWheelScroll',
        'setFilename',
        'createBackground',
        'activePage',
        'addCategory',
        'command357',
        'setHandler',
        'DnVLW',
        '_buttonAssistWindow',
        'includeTutorial',
        'prototype',
        'addTutorialPanelSysNewOptionCommand',
        'TitleWindow_BgType',
        'getLastPluginCommandInterpreter',
        'isValidTutorial',
        'openTutorial',
        '_pictureSprite',
        'Game_Interpreter_PluginCommand',
        '604280rhlLVh',
        '_lastIndex',
        'isCategoryOpen',
        'commandName',
        '_scrollDuration',
        'descriptionWindowRect',
        '_tutorialDescriptionWindow',
        'initTutorialPanelSysMainMenu',
        'right',
        'playCancel',
        'isWheelScrollEnabled',
        'finish',
        'isSceneBattle',
        'makeData',
        'bitmap',
        'Window_MenuCommand_addOriginalCommands',
        'findSymbolExt',
        'ext',
        'openCloseCurrentCategory',
        'boxHeight',
        'onTouchSelect',
        '_categoryStatus',
        'isHideStatusWindowTutorial',
        'remove',
        'setWaitMode',
        'boxWidth',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'select',
        'ARRAYFUNC',
        'ARRAYEVAL',
        '_tutorialData',
        'IHaNw',
        'registerCommand',
        'callUpdateHelp',
        '9yYkuDd',
        'PageWindow_RectJS',
        'isMainMenuTutorialListVisible',
        'CategoryTutorials',
        'Categories',
        'trim',
        'exit',
        'tutorialPages',
        'LYnEm',
        'jvBFW',
        '_commandWindow',
        'createTitleWindow',
        '4pvbuzX',
        'createTutorialTitleWindow',
        'updateSmoothScroll',
        'setMainMenuTutorialLabelEnabled',
        'TutorialList',
        '_tutorialKey',
        'iWRCH',
        'ConfigManager_applyData',
        'drawTextEx',
        'createPictureSprite',
        'closedCategoriesFmt',
        'makeCommandList',
        '_backSprite1',
        'textSizeEx',
        'parameters',
        'XVaCi',
        'anchor',
        'mainAreaHeight',
        'AdjustRect',
        'addCommand',
        'itemPadding',
        'TutorialCall',
        '_maxColCache',
        'TitleWindow_RectJS',
        'DescWindow_BgType',
        'collapse',
        'createTutorialDescriptionWindow',
        'PictureWindow_BgType',
        'deactivate',
        'CategoryOrder',
        'VdOzV',
        'addTutorialListCommand',
        '_statusWindow',
        '_tutorialPageIndex',
        'createTutorialPictureWindow',
        'ARRAYNUM',
        'Name',
        'cursorRight',
        'refresh',
        'WiqPq',
        'Pages',
        'includeCategory',
        'process_VisuMZ_TutorialPanelSys',
        'ConfigManager_makeData',
        'name',
        'createCustomBackgroundImages',
        'isTutorialCategoryPopulated',
        'addTutorialListCommands',
        'VocabClosedCategory',
        'initTutorialPanelSysSettings',
        'VisuMZ_1_MainMenuCore',
        'oyiqP',
        'adjustSprite',
        'Scene_Battle_createAllWindows',
        '(needs\x20key)',
        'tutorialList',
        'nxeGU',
        'tutorialPageWindowRect',
        'Options',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        '_interpreter',
        'HideDuring',
        'PJBfd',
        'loadPicture',
        'addLoadListener',
        'shown',
        'setText',
        'changePaintOpacity',
        'isCommandEnabled',
        'tutorialData',
        '_TutorialPanelSys_MainMenu',
        'playCursorSound',
        'currentCategory',
        'SceneTutorialList',
        'nextPage',
        'itemRect',
        'MNfyu',
        'setBackgroundOpacity',
        '_index',
        'push',
        'buttonAssistKey1',
        'show',
        'clear',
        'Scene_Menu_createCommandWindow',
        'kBwan',
        'pan',
        'onListTutorial',
        'updatePage',
        'match',
        'page',
        'toUpperCase',
        'YtjuL',
        'nextTutorialPage',
        'createListWindow',
        'loadTitle1',
        'addTutorialListCommandAutomatically',
        '_pageWindow',
        'itemHeight',
        '_waitMode',
        'FUNC',
        'oDqgq',
        'call',
        'volume',
        'JSON',
        'mainAreaBottom',
        '2zDIKyq',
        'map',
        'removeTutorialKey',
        'tutorialPageIndex',
        'isTutorialListCommandEnabled',
        'updateWaitMode',
        'constructor',
        'cancel',
        'setCursorRect',
        'height',
        'onTouchOk',
        'VocabChangePage',
        'EnableMainMenu',
        '_list',
        'Sound',
        'TutorialRegisterKeys',
        'drawItem',
        '_text',
        '_tutorialPictureWindow',
        'onDatabaseLoaded',
        'setTopRow',
        'ForceView',
        '1343884INJgXD',
        'dhGgT',
        'innerHeight',
        'enabled',
        'commandTutorialList',
        'TutorialPanelSys',
        'parse',
        '_descriptionWindow',
        'AddTutorialsOption',
        'PageWindow_BgType',
        'listWindowRect',
        '3394485NnKXnP',
        'ListWindow_RectJS',
    ];
    _0x1fdd = function () {
        return _0x284e56;
    };
    return _0x1fdd();
}
function Window_TutorialTitle() {
    this['initialize'](...arguments);
}
((Window_TutorialTitle[_0x5bb884(0xfd)] = Object[_0x5bb884(0x94)](Window_Base[_0x5bb884(0xfd)])),
    (Window_TutorialTitle[_0x5bb884(0xfd)][_0x5bb884(0x1a2)] = Window_TutorialTitle),
    (Window_TutorialTitle[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function (_0x15e935) {
        const _0x2c1fa1 = _0x5bb884;
        (Window_Base['prototype'][_0x2c1fa1(0xd9)][_0x2c1fa1(0x198)](this, _0x15e935),
            (this['_text'] = ''));
    }),
    (Window_TutorialTitle[_0x5bb884(0xfd)][_0x5bb884(0x175)] = function (_0x463857) {
        const _0x4cf0aa = _0x5bb884;
        if (this[_0x4cf0aa(0x1ad)] !== _0x463857) {
            if (_0x4cf0aa(0x17f) !== _0x4cf0aa(0xb0))
                ((this[_0x4cf0aa(0x1ad)] = _0x463857), this[_0x4cf0aa(0x159)]());
            else return _0x17cf3b['prototype'][_0x4cf0aa(0x183)][_0x4cf0aa(0x198)](this);
        }
    }),
    (Window_TutorialTitle[_0x5bb884(0xfd)][_0x5bb884(0x185)] = function () {
        const _0x47a41f = _0x5bb884;
        this[_0x47a41f(0x175)]('');
    }),
    (Window_TutorialTitle[_0x5bb884(0xfd)][_0x5bb884(0x159)] = function () {
        const _0x517170 = _0x5bb884;
        this[_0x517170(0xba)][_0x517170(0x185)]();
        const _0x49b42a = this[_0x517170(0x1ad)],
            _0x35057f = this['textSizeEx'](_0x49b42a),
            _0x5066d2 = Math[_0x517170(0x1e2)](
                (this['innerWidth'] - _0x35057f[_0x517170(0xda)]) / 0x2
            ),
            _0x34a149 = Math[_0x517170(0x1e2)](
                (this[_0x517170(0x1b4)] - _0x35057f[_0x517170(0x1a5)]) / 0x2
            );
        this[_0x517170(0x13b)](_0x49b42a, _0x5066d2, _0x34a149);
    }));
function Window_TutorialList() {
    const _0x1bf9ed = _0x5bb884;
    this[_0x1bf9ed(0xd9)](...arguments);
}
((Window_TutorialList[_0x5bb884(0xfd)] = Object[_0x5bb884(0x94)](Window_Command['prototype'])),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x1a2)] = Window_TutorialList),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0xd9)] = function (_0x4a6b42) {
        const _0x411c93 = _0x5bb884;
        (this[_0x411c93(0xc8)](),
            Window_Command[_0x411c93(0xfd)][_0x411c93(0xd9)][_0x411c93(0x198)](this, _0x4a6b42),
            this[_0x411c93(0x70)]());
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0xc8)] = function () {
        const _0x5b6b31 = _0x5bb884;
        this[_0x5b6b31(0x11a)] = {};
        const _0xa0f4d1 = VisuMZ[_0x5b6b31(0x1b7)]['CategoryOrder'];
        for (const _0x2cd05b of _0xa0f4d1) {
            if (_0x5b6b31(0xbf) !== 'eBKIO') {
                let _0x200481 = this[_0x5b6b31(0xde)][_0x5b6b31(0x7f)]() + 0x1;
                _0x200481 >= this[_0x5b6b31(0x12e)]()[_0x5b6b31(0x7c)]
                    ? (_0x6a3291['playCancel'](), this[_0x5b6b31(0x7e)]())
                    : (this[_0x5b6b31(0xde)][_0x5b6b31(0x1da)](),
                      this[_0x5b6b31(0xde)]['select'](_0x200481));
            } else {
                if (!this['includeCategory'](_0x2cd05b)) continue;
                this[_0x5b6b31(0x11a)][_0x2cd05b['toLowerCase']()[_0x5b6b31(0x12c)]()] = !![];
            }
        }
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x70)] = function () {
        const _0x1b00fa = _0x5bb884;
        if ($gameTemp[_0x1b00fa(0xe4)] === undefined) return;
        const _0x13471f = $gameTemp['_returnTutorialKey'];
        $gameTemp[_0x1b00fa(0xe4)] = undefined;
        const _0x297556 = this[_0x1b00fa(0x115)](_0x1b00fa(0xf2), _0x13471f),
            _0x14d032 = Math[_0x1b00fa(0x1e2)](this[_0x1b00fa(0xb1)]() / 0x2) - 0x1;
        (this[_0x1b00fa(0xc2)](_0x297556),
            this[_0x1b00fa(0x109)] > 0x1 &&
                ((this[_0x1b00fa(0x109)] = 0x1), this[_0x1b00fa(0x135)]()),
            this[_0x1b00fa(0x1b0)](_0x297556 - _0x14d032),
            this[_0x1b00fa(0x126)]());
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x115)] = function (_0x2b66f8, _0x2f78af) {
        const _0x3d41f4 = _0x5bb884;
        return this[_0x3d41f4(0x1a9)][_0x3d41f4(0x78)](
            _0x4b681c =>
                _0x4b681c['symbol'] === _0x2b66f8 && _0x4b681c[_0x3d41f4(0x116)] === _0x2f78af
        );
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x13e)] = function () {
        const _0x2b50e4 = _0x5bb884,
            _0x511663 = VisuMZ['TutorialPanelSys']['CategoryOrder'];
        for (const _0x3b24d4 of _0x511663) {
            if (_0x2b50e4(0xa2) === _0x2b50e4(0xa2)) {
                if (!this[_0x2b50e4(0x15c)](_0x3b24d4)) continue;
                this[_0x2b50e4(0xf7)](_0x3b24d4);
            } else this['_tutorialPictureWindow'][_0x2b50e4(0xf0)]();
        }
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x15c)] = function (_0x477383) {
        const _0x418230 = _0x5bb884;
        return $gameSystem[_0x418230(0x161)](_0x477383);
    }),
    (Window_TutorialList[_0x5bb884(0xfd)]['isCategoryOpen'] = function (_0x48e9b0) {
        const _0x57112a = _0x5bb884;
        return (
            (_0x48e9b0 = _0x48e9b0[_0x57112a(0x84)]()['trim']()),
            this[_0x57112a(0x11a)][_0x48e9b0]
        );
    }),
    (Window_TutorialList['prototype'][_0x5bb884(0xf7)] = function (_0x4df83d) {
        const _0x261a51 = _0x5bb884;
        _0x4df83d = _0x4df83d[_0x261a51(0x84)]()[_0x261a51(0x12c)]();
        const _0x103d27 = _0x4df83d === _0x261a51(0xdc),
            _0x43e438 = this['isCategoryOpen'](_0x4df83d)
                ? TextManager[_0x261a51(0xf2)][_0x261a51(0x8a)]
                : TextManager['tutorial'][_0x261a51(0x13d)],
            _0x53d487 = _0x103d27 ? {} : VisuMZ['TutorialPanelSys'][_0x261a51(0x1e0)][_0x4df83d];
        if (!_0x53d487) return;
        const _0x1796d5 = _0x103d27
                ? TextManager[_0x261a51(0xf2)][_0x261a51(0xdc)]
                : _0x53d487['Title'],
            _0x20ae6a = $gameSystem[_0x261a51(0xb9)](_0x4df83d);
        if (_0x20ae6a <= 0x0) return;
        const _0x4c30a8 = _0x43e438[_0x261a51(0xe1)](_0x1796d5, _0x20ae6a);
        (this[_0x261a51(0x146)](_0x4c30a8, _0x261a51(0x1d9), !![], _0x4df83d),
            this[_0x261a51(0x9d)](_0x4df83d));
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x117)] = function () {
        const _0x12def5 = _0x5bb884,
            _0x47fc5d = this[_0x12def5(0x17b)]();
        ((this[_0x12def5(0x11a)][_0x47fc5d] = !this[_0x12def5(0x11a)][_0x47fc5d]),
            this[_0x12def5(0x159)]());
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x17b)] = function () {
        const _0x5e7269 = _0x5bb884;
        return this['currentSymbol']() === _0x5e7269(0x1d9) ? this[_0x5e7269(0x1c7)]() : null;
    }),
    (Window_TutorialList['prototype'][_0x5bb884(0x9d)] = function (_0x40eb41) {
        const _0x3541b5 = _0x5bb884;
        if (!this[_0x3541b5(0x107)](_0x40eb41)) return;
        const _0x1c645a = VisuMZ[_0x3541b5(0x1b7)]['CategoryTutorials'][_0x40eb41];
        for (const _0x1bc734 of _0x1c645a) {
            if (!this[_0x3541b5(0xfc)](_0x1bc734)) continue;
            this[_0x3541b5(0xbe)](_0x1bc734);
        }
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0xfc)] = function (_0x52a8be) {
        const _0x2620e8 = _0x5bb884;
        if (!VisuMZ[_0x2620e8(0x1b7)][_0x2620e8(0x101)](_0x52a8be)) return ![];
        return $gameSystem[_0x2620e8(0x1cb)](_0x52a8be);
    }),
    (Window_TutorialList['prototype']['addTutorial'] = function (_0xce3539) {
        const _0x1c8379 = _0x5bb884;
        _0xce3539 = _0xce3539[_0x1c8379(0x84)]()['trim']();
        const _0x3f9a02 = VisuMZ[_0x1c8379(0x1b7)]['Tutorials'][_0xce3539];
        if (!_0x3f9a02) return;
        const _0x26233b = _0x3f9a02[_0x1c8379(0xad)];
        this[_0x1c8379(0x146)](_0x26233b, 'tutorial', !![], _0xce3539);
    }),
    (Window_TutorialList['prototype']['itemTextAlign'] = function () {
        const _0x161856 = _0x5bb884;
        return _0x161856(0xa6);
    }),
    (Window_TutorialList[_0x5bb884(0xfd)][_0x5bb884(0x1ac)] = function (_0x297f7a) {
        const _0x5ace97 = _0x5bb884,
            _0x40d85a = this[_0x5ace97(0x6b)](_0x297f7a),
            _0x58b9e6 = this['commandName'](_0x297f7a),
            _0x44399d = this[_0x5ace97(0x140)](_0x58b9e6)[_0x5ace97(0xda)];
        this[_0x5ace97(0x176)](this[_0x5ace97(0x177)](_0x297f7a));
        const _0xacd984 = this['itemTextAlign']();
        if (_0xacd984 === 'right')
            this[_0x5ace97(0x13b)](
                _0x58b9e6,
                _0x40d85a['x'] + _0x40d85a[_0x5ace97(0xda)] - _0x44399d,
                _0x40d85a['y'],
                _0x44399d
            );
        else {
            if (_0xacd984 === _0x5ace97(0x1d4)) {
                const _0x812bae =
                    _0x40d85a['x'] + Math['floor']((_0x40d85a['width'] - _0x44399d) / 0x2);
                this[_0x5ace97(0x13b)](_0x58b9e6, _0x812bae, _0x40d85a['y'], _0x44399d);
            } else
                _0x5ace97(0x99) === _0x5ace97(0x99)
                    ? this[_0x5ace97(0x13b)](_0x58b9e6, _0x40d85a['x'], _0x40d85a['y'], _0x44399d)
                    : this[_0x5ace97(0x13b)](_0x504b4f, _0xad8769['x'], _0x5dc28d['y'], _0x5cd7bc);
        }
    }));
