//=============================================================================
// VisuStella MZ - Visual Fogs
// VisuMZ_4_VisualFogs.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_VisualFogs = true;
var VisuMZ = VisuMZ || {};
VisuMZ.VisualFogs = VisuMZ.VisualFogs || {};
VisuMZ.VisualFogs.version = 1.12;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.12] [VisualFogs]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Visual_Fogs_VisuStella_MZ
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Fogs are a handy feature long removed from RPG Maker since RPG Maker XP.
 * This plugin reintroduces them back into RPG Maker MZ. Fogs function similar
 * to parallaxes, except rather than being under the tile map, fogs appear
 * above the tile map and the characters. This plugin gives you an unlimited
 * amount of fogs to apply to each map alongside many controls to make the fogs
 * appear more vivid.
 *
 * A restricted fog area system is also added to this plugin to make fogs
 * appear only within certain regions and/or terrain tags. This way, you can
 * utilize parallaxes as masked layers for obscured sections of the map.
 *
 * Sometimes, fogs may be too intrusive to the player's visibility. A vignette
 * feature has been added to make fogs appear only on the borders or certain
 * sides of the screen. This way, fogs can still add to the atmosphere without
 * obscuring too much of the visible screen.
 *
 * Features include all (but not limited to) the following:
 *
 * * Add, change, and/or remove fogs through map notetags.
 * * Lots of customization options for each of the fogs.
 * * Limit where fogs can be displayed on the map through regions and/or
 *   terrain tags to obscure parts of the map.
 * * Use vignettes to obscure sides of the screen without affecting the center.
 * * Use Plugin Commands midway through the game to add, change, fade, and/or
 *   remove fogs as needed.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Fogs
 *
 * Fogs are not an inherent feature for the map editor. They need to be added
 * through map notetags or Plugin Commands.
 *
 * Each of the fogs added through this plugin's notetags and/or commands are
 * assigned an ID. Referring back to the ID later will allow you to update
 * and/or remove that fog when needed.
 *
 * When fogs are created, they appear above the tile map and characters, but
 * below the weather. This means they are created between the two layers when
 * the map's sprites are generated.
 *
 * Fogs will behave very similar to parallaxes in how they move about the
 * screen. This means that if a fog is set to looping, it will loop in
 * accordance to the screen's display coordinates. This is to maintain
 * consistency with how the RPG Maker MZ engine behaves.
 *
 * ---
 *
 * Regions and Terrain Tags
 *
 * If you don't want a fog to appear for the whole entire foreground and want
 * to confine them to certain areas of the map, you can assign regions or
 * terrain tags for them to appear in.
 *
 * *NOTE*: This effect does not work on looping maps.
 *
 * Only the parts of the map marked by the designated regions and/or terrain
 * tags will reveal the fog. Those parts will be little squares each,
 * equal to the size of a tile. They have soft borders due to blurring options.
 * The foggy tiles will be slightly larger than normal due to spill values.
 *
 * You may notice that some tiles don't blur well when they are towards the
 * right and bottom sides of the screen when the blur values are higher than
 * normal. This is a known issue with Pixi JS's filters and there's not much
 * the VisuStella team can do about it. Instead, what we recommend is that you
 * use a fog vignette on an upper layer to mask the bleeding issue.
 *
 * Each fog layer can have their own custom regions and/or terrain tags to
 * appear in. These can be adjusted through the notetag settings or through the
 * Plugin Commands provided by this plugin. Fog layers can be limited to
 * multiple regions and/or terrain tags at the same time.
 *
 * WARNING: This will cause longer load times on larger maps and affect their
 * performance. We highly recommend that you don't use this feature on maps
 * larger than 120 tiles wide or tall. However, this value can vary from device
 * to device.
 *
 * ---
 *
 * Vignettes
 *
 * If you don't want fogs to obscure the whole screen, use a vignette to make
 * them appear only at the sides of the screen. You can use custom vignette
 * masks or rendered ones provided by this plugin.
 *
 * If you decide to make a custom vignette mask, create them similar to regular
 * image masks. This means that white areas of the masking image will be the
 * parts of the screen where the fog appears while the black areas of the image
 * will hide the fog. You can use gradients to make the vignette mask appear
 * more smooth.
 *
 * Vignettes cannot be used with region and terrain tags. This is because the
 * region and terrain tag tiles move alongside the screen while vignettes are
 * always locked onto the borders of the screen. However, if you wish to use
 * both, just apply two different fog layers instead.
 *
 * ---
 *
 * Not For Battle
 *
 * For clarification, the VisuStella MZ Visual Fogs plugin is NOT made for
 * battle. There's a separate plugin for that called Visual Battle Environment.
 * The reason why fogs aren't made for battle is because the way fogs are
 * handled in map vary from how they would be handled in battle. Using the
 * Visual Fogs Plugin Commands will only alter the fog appearances when the
 * player finishes battle.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Fog-Related Notetags ===
 *
 * ---
 *
 * <Fog id Settings>
 *  Name: filename
 *  optional property
 *  optional property
 *  optional property
 * </Fog id Settings>
 *
 * - Used for: Map Notetags
 * - Creates a regular fog layer for this map by default.
 * - Replace 'id' with a number value to assign to the fog.
 *   - Plugin Commands will refer to this ID for changes and removal.
 * - The 'Name' setting is required. Without it, no fog will be made.
 *   - Replace 'filename' with the filename of the image you want to use as
 *     a fog found in the game project's img/parallaxes/ folder.
 *   - Do not include the file extension.
 * - Insert as many of the optional properties as you want. You can find a list
 *   of them in the section below.
 *
 * ---
 *
 * -=-=- Optional Properties -=-=-
 *
 * Replace the 'optional property' segment of the notetags above with any of
 * the text below to acquire their effects. You can add/remove as many of the
 * optional properties as you need.
 *
 * ---
 *
 * Horz Scroll: x
 * Vert Scroll: y
 *
 * - This enables horizontal or vertical scrolling for the fog.
 * - Replace 'x' or 'y' with a Number value to determine how fast they will
 *   scroll across the screen.
 * - Use a negative value to make them scroll the other way.
 * - These effects are mutually exclusive from the "Map Locked" property.
 *
 * ---
 *
 * Map Locked
 *
 * - This will cause the fog to only scroll when the map scrolls.
 * - This has the same effect as naming a fog with "!" in front of
 *   its filename.
 * - If the filename used for this fog has "!" in front of it, the
 *   Map Locked effect will be automatically turned on.
 * - These effect is mutually exclusive from the "Horz Scroll" and
 *   "Vert Scroll" properties.
 *
 * ---
 *
 * Opacity: x
 * Opacity: x%
 *
 * - Changes the opacity level of the fog.
 * - Replace 'x' with a number from 0 to 255 representing the opacity level.
 * - Replace 'x%' with a percentage from 0% to 100% representing the opacity.
 *
 * ---
 *
 * Blend Mode: Normal
 * Blend Mode: Additive
 * Blend Mode: Multiply
 * Blend Mode: Screen
 *
 * - Sets the blend mode for the icon on the fog.
 * - Use only one of the above.
 *
 * ---
 *
 * Hue: x
 * Hue Shift: x
 *
 * - Changes the hue of the fog to 'x' so that you don't need to create
 *   multiple copies of the files with different colors.
 * - Replace 'x' with a number value between 0 and 360.
 * - If the "Hue Shift" property is also used, then adjust the hue of the
 *   fog each frame by 'x' amount.
 *   - 'x' can be positive or negative.
 *
 * ---
 *
 * Color Tone: red, green, blue, gray
 *
 * - Changes the color tone or tint of the fog.
 * - Replace 'red', 'green', 'blue' with a value between -255 and 255.
 * - Replace 'gray' with a value between 0 and 255.
 *
 * ---
 *
 * Region: id
 * Regions: id, id, id
 *
 * - Forces the fog to only become visible on tiles marked regions with a
 *   matching ID (alongside valid terrain tags).
 * - If this isn't used, then the fog will be as large as the screen.
 * - Replace 'id' with a region ID between 1 and 255.
 *   - Region 0 is ignored and will not work.
 * - Insert multiple ID's to mark more tiles the fog can appear on.
 * - This feature cannot be used with Vignettes.
 * - This feature cannot be used with looping maps.
 * - WARNING: This WILL cause longer load times on larger maps.
 *
 * ---
 *
 * Terrain Tag: id
 * Terrain Tags: id, id, id
 *
 * - Forces the fog to only become visible on tiles marked terrain tags
 *   with a matching ID (alongside valid regions).
 * - If this isn't used, then the fog will be as large as the screen.
 * - Replace 'id' with a terrain tag ID between 1 and 7.
 *   - Terrain tag 0 is ignored and will not work.
 * - Insert multiple ID's to mark more tiles the fog can appear on.
 * - This feature cannot be used with Vignettes.
 * - WARNING: This WILL cause longer load times on larger maps.
 *
 * ---
 *
 * Tile Blur: x
 *
 * - Determines how soft the borders are around the revealed fog tiles.
 * - Use larger numbers to blur them more.
 * - Use a value of zero to remove any blur.
 *
 * ---
 *
 * Tile Spill: x
 *
 * - Determines how much larger to make the revealed fog tiles.
 * - Use larger numbers to spill more and make the tiles larger.
 * - Use a value of zero to not spill at all and use the exact tile sizes.
 *
 * ---
 *
 * Vignette: type
 *
 * - Makes the fog appear along the edge of the screen rather than the entire
 *   visible game screen.
 * - Replace 'type' with any of the following:
 *   - Border
 *   - Horizontal
 *   - Vertical
 *   - Upper
 *   - Lower
 *   - Left
 *   - Right
 *
 * ---
 *
 * Custom Vignette: filename
 *
 * - Allows you to use a custom parallax image as a vignette.
 * - Replace 'filename' with the filename of the image you want to use as
 *   a vignette found in the game project's img/parallaxes/ folder.
 *   - Do not include the file extension.
 * - Custom vignettes are used as masks.
 *   - White areas on the image determine the visible parts of the fog.
 *   - Black areas on the image determine the invisible parts of the fog.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Fog Plugin Commands ===
 *
 * ---
 *
 * Fog: Add/Change Settings
 * - Add/Change settings for target fog.
 * - Does not alter the map editor's fog.
 *
 *   Required:
 *
 *     ID:
 *     - What is the ID of this fog to be added/changed?
 *
 *     Filename:
 *     - What is the filename of the fog?
 *
 *   Optional Settings:
 *
 *     Scrolling:
 *
 *       Map Lock?:
 *       - Lock the fog to the map's scrolling?
 *       - Automatically enable if the filename starts with "!"
 *
 *       Loop Horizontally?:
 *       - Loop the fog horizontally?
 *       - Does not work with Map Lock enabled.
 *
 *         Scroll:
 *         - What is the horizontal scroll speed?
 *         - Use a negative value to invert the direction.
 *
 *       Loop Vertically?:
 *       - Loop the fog vertically?
 *       - Does not work with Map Lock enabled.
 *
 *         Scroll:
 *         - What is the vertical scroll speed?
 *         - Use a negative value to invert the direction.
 *
 *     Appearance:
 *
 *       Opacity:
 *       - What is the opacity level for this fog?
 *       - You may use JavaScript code.
 *
 *       Blend Mode:
 *       - What kind of blend mode do you wish to apply to the fog?
 *       - You may use JavaScript code.
 *         - Normal
 *         - Additive
 *         - Multiply
 *         - Screen
 *
 *       Hue:
 *       - Do you wish to adjust this fog's hue?
 *       - You may use JavaScript code.
 *
 *       Hue Shift:
 *       - How much do you want the hue to shift each frame?
 *       - You may use JavaScript code.
 *
 *       Color Tone:
 *       - What tone do you want for the fog?
 *       - Format: [Red, Green, Blue, Gray]
 *
 *     Location:
 *
 *       Regions:
 *       - Which regions will show this fog?
 *       - Does not work with 0. Leave empty to ignore.
 *
 *       Terrain Tags:
 *       - Which terrain tags will show this fog?
 *       - Does not work with 0. Leave empty to ignore.
 *
 *       Tile Blur:
 *       - What's the blur level you wish to use for tiles?
 *       - You may use JavaScript code.
 *
 *       Tile Spill:
 *       - What's the spill amount you wish to use for tiles?
 *       - You may use JavaScript code.
 *
 *     Vignette:
 *
 *       Type:
 *       - What vignette do you want to use for this fog?
 *       - This will override location settings.
 *
 *       Custom:
 *       - Do you wish to use a custom vignette instead?
 *       - Automatically changes the type to "Custom".
 *
 * ---
 *
 * Fog: Fade Opacity
 * - Fades the target fog(s) opacity to a different value.
 *
 *   ID(s):
 *   - Target which fog(s)?
 *   - Cannot target the map editor's fog.
 *
 *   Target Opacity:
 *   - What opacity level to this value (0-255).
 *   - You may use JavaScript code to determine the value.
 *
 *   Duration:
 *   - How many frames should this change take?
 *   - You may use JavaScript code to determine the value.
 *
 * ---
 *
 * Fog: Remove
 * - Removes target fog(s).
 *
 *   ID(s):
 *   - Remove which fog(s)?
 *   - Cannot remove the map editor's fog.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Default Settings
 * ============================================================================
 *
 * The below are the default settings when it comes to creating fogs through
 * map notetags.
 *
 * ---
 *
 * Defaults
 *
 *   Fog Opacity:
 *   - What is the default fog opacity level for map notetags?
 *
 *   Blend Mode:
 *   - What is the default fog blend mode for map notetags?
 *     - Normal
 *     - Additive
 *     - Multiply
 *     - Screen
 *
 *   Tile Blur:
 *   - What is the default fog tile blur intensity for map notetags?
 *
 *   Tile Spill:
 *   - What is the default fog tile spill amount for map notetags?
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.12: July 18, 2024
 * * Optimization Update!
 * ** Plugin should run more optimized.
 * ** Uses a better algorithm to determine terrain tags.
 *
 * Version 1.11: April 18, 2024
 * * Bug Fixes!
 * ** Fixed a bug where having a '!' at the start of a fog graphic file's name
 *    did not automatically incur map lock when done from plugin commands.
 *    Fix made by Arisu.
 *
 * Version 1.10: November 16, 2023
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.09: August 17, 2023
 * * Bug Fixes!
 * ** Fixed an error that would cause a crash upon using the "Return to Title
 *    Screen" event command with the "Event Title Screen" plugin installed. Fix
 *    made by Irina.
 *
 * Version 1.08: June 15, 2023
 * * Bug Fixes!
 * ** Fixes a visual bug involving the borders with the zoom animation upon
 *    entering a random encounter. Fix made by Arisu.
 *
 * Version 1.07: August 4, 2022
 * * Compatibility Update!
 * ** Vignettes now work better with zoom.
 *
 * Version 1.06: July 7, 2022
 * * Feature Update!
 * ** Blend modes are now revamped for the fogs to behave more like they do for
 *    pictures for better accuracy. Update made by Irina.
 *
 * Version 1.05: December 9, 2021
 * * Documentation Update!
 * ** Added section to "Major Changes" for clarification purposes:
 * *** Not For Battle
 * *** For clarification, the VisuStella MZ Visual Fogs plugin is NOT made for
 *     battle. There's a separate plugin for that called Visual Battle
 *     Environment. The reason why fogs aren't made for battle is because the
 *     way fogs are handled in map vary from how they would be handled in
 *     battle. Using the Visual Fogs Plugin Commands will only alter the fog
 *     appearances when the player finishes battle.
 * * Feature Update!
 * ** Added fail safes to prevent Plugin Command usage during battle to cause
 *    problems while inside battle test. Update made by Irina.
 *
 * Version 1.04: June 25, 2021
 * * Compatibility Update
 * ** Added compatibility functionality for Event Title Scene.
 *
 * Version 1.03: May 28, 2021
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.02: May 21, 2021
 * * Documentation Update!
 * ** Added a clause we forgot to mention that region-locked fog effects only
 *    work on maps with no looping. A note will be added to the "Regions and
 *    Terrain Tags" and notetag sections. We apologize for any inconveniences
 *    this may cause.
 *
 * Version 1.01: May 7, 2021
 * * Bug Fixes!
 * ** Cached vignettes will no longer be cleared from memory. Fix by Irina.
 *
 * Version 1.00 Official Release Date: March 5, 2021
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command FogAddChangeSettings
 * @text Fog: Add/Change Settings
 * @desc Add/Change settings for target fog.
 * Does not alter the map editor's fog.
 *
 * @arg Required
 *
 * @arg id:num
 * @text ID
 * @parent Required
 * @type number
 * @min 1
 * @desc What is the ID of this fog to be added/changed?
 * @default 1
 *
 * @arg filename:str
 * @text Filename
 * @parent Required
 * @type file
 * @dir img/parallaxes/
 * @desc What is the filename of the fog?
 * @default >>>ATTENTION<<<
 *
 * @arg Optional:struct
 * @text Optional Settings
 * @type struct<Optional>
 * @desc Optional settings regarding Visual Fogs.
 * @default {"Scrolling":"","_fogZero:eval":"false","_fogLoopX:eval":"false","_fogSx:eval":"+0","_fogLoopY:eval":"false","_fogSy:eval":"+0","Appearance":"","opacity:eval":"200","blendMode:eval":"1","hue:eval":"0","hueShift:eval":"+0","colorTone:eval":"[0, 0, 0, 0]","Location":"","maskRegions:arraynum":"[]","maskTerrainTags:arraynum":"[]","maskBlur:eval":"10","maskSpill:eval":"10","Vignette":"","vignette:str":"None","vignetteFilename:str":""}
 *
 * @ --------------------------------------------------------------------------
 *
 * @command FogFadeOpacity
 * @text Fog: Fade Opacity
 * @desc Fades the target fog(s) opacity to a different value.
 *
 * @arg list:arraynum
 * @text ID(s)
 * @type number[]
 * @min 1
 * @desc Target which fog(s)?
 * Cannot target the map editor's fog.
 * @default ["1"]
 *
 * @arg targetOpacity:eval
 * @text Target Opacity
 * @desc What opacity level to this value (0-255).
 * You may use JavaScript code to determine the value.
 * @default 255
 *
 * @arg opacityDuration:eval
 * @text Duration
 * @desc How many frames should this change take?
 * You may use JavaScript code to determine the value.
 * @default 60
 *
 * @ --------------------------------------------------------------------------
 *
 * @command FogRemove
 * @text Fog: Remove
 * @desc Removes target fog(s).
 *
 * @arg list:arraynum
 * @text ID(s)
 * @type number[]
 * @min 1
 * @desc Remove which fog(s)?
 * Cannot remove the map editor's fog.
 * @default ["1"]
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param VisualFogs
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Defaults
 *
 * @param FogOpacity:num
 * @text Fog Opacity
 * @parent Defaults
 * @type number
 * @max 255
 * @desc What is the default fog opacity level for map notetags?
 * @default 200
 *
 * @param BlendMode:num
 * @text Blend Mode
 * @parent Defaults
 * @type select
 * @option 0 - Normal
 * @value 0
 * @option 1 - Additive
 * @value 1
 * @option 2 - Multiply
 * @value 2
 * @option 3 - Screen
 * @value 3
 * @desc What is the default fog blend mode for map notetags?
 * @default 1
 *
 * @param MaskBlur:num
 * @text Tile Blur
 * @parent Defaults
 * @type number
 * @desc What is the default fog tile blur intensity for map notetags?
 * @default 10
 *
 * @param MaskSpill:num
 * @text Tile Spill
 * @parent Defaults
 * @type number
 * @desc What is the default fog tile spill amount for map notetags?
 * @default 10
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * Optional Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Optional:
 *
 * @param Scrolling
 *
 * @param _fogZero:eval
 * @text Map Lock?
 * @parent Scrolling
 * @type boolean
 * @on Map Lock
 * @off No Map Lock
 * @desc Lock the fog to the map's scrolling?
 * Automatically enable if the filename starts with "!"
 * @default false
 *
 * @param _fogLoopX:eval
 * @text Loop Horizontally?
 * @parent Scrolling
 * @type boolean
 * @on Loop
 * @off No Loop
 * @desc Loop the fog horizontally?
 * Does not work with Map Lock enabled.
 * @default false
 *
 * @param _fogSx:eval
 * @text Scroll:
 * @parent _fogLoopX:eval
 * @desc What is the horizontal scroll speed?
 * Use a negative value to invert the direction.
 * @default +0
 *
 * @param _fogLoopY:eval
 * @text Loop Vertically?
 * @parent Scrolling
 * @type boolean
 * @on Loop
 * @off No Loop
 * @desc Loop the fog horizontally?
 * Does not work with Map Lock enabled.
 * @default false
 *
 * @param _fogSy:eval
 * @text Scroll:
 * @parent _fogLoopY:eval
 * @desc What is the vertical scroll speed?
 * Use a negative value to invert the direction.
 * @default +0
 *
 * @param Appearance
 *
 * @param opacity:eval
 * @text Opacity
 * @parent Appearance
 * @desc What is the opacity level for this fog?
 * You may use JavaScript code.
 * @default 200
 *
 * @param blendMode:eval
 * @text Blend Mode
 * @parent Appearance
 * @type select
 * @option 0 - Normal
 * @value 0
 * @option 1 - Additive
 * @value 1
 * @option 2 - Multiply
 * @value 2
 * @option 3 - Screen
 * @value 3
 * @desc What kind of blend mode do you wish to apply to the fog?
 * You may use JavaScript code.
 * @default 1
 *
 * @param hue:eval
 * @text Hue
 * @parent Appearance
 * @desc Do you wish to adjust this fog's hue?
 * You may use JavaScript code.
 * @default 0
 *
 * @param hueShift:eval
 * @text Hue Shift
 * @parent hue:eval
 * @desc How much do you want the hue to shift each frame?
 * You may use JavaScript code.
 * @default +0
 *
 * @param colorTone:eval
 * @text Color Tone
 * @parent Appearance
 * @desc What tone do you want for the fog?
 * Format: [Red, Green, Blue, Gray]
 * @default [0, 0, 0, 0]
 *
 * @param Location
 *
 * @param maskRegions:arraynum
 * @text Regions
 * @parent Location
 * @type number[]
 * @min 1
 * @max 255
 * @desc Which regions will show this fog?
 * Does not work with 0. Leave empty to ignore.
 * @default []
 *
 * @param maskTerrainTags:arraynum
 * @text Terrain Tags
 * @parent Location
 * @type number[]
 * @min 1
 * @max 7
 * @desc Which terrain tags will show this fog?
 * Does not work with 0. Leave empty to ignore.
 * @default []
 *
 * @param maskBlur:eval
 * @text Tile Blur
 * @parent Location
 * @desc What's the blur level you wish to use for tiles?
 * You may use JavaScript code.
 * @default 10
 *
 * @param maskSpill:eval
 * @text Tile Spill
 * @parent Location
 * @desc What's the spill amount you wish to use for tiles?
 * You may use JavaScript code.
 * @default 10
 *
 * @param Vignette
 *
 * @param vignette:str
 * @text Type
 * @parent Vignette
 * @type select
 * @option None
 * @option Border
 * @option Horizontal
 * @option Vertical
 * @option Upper
 * @option Lower
 * @option Left
 * @option Right
 * @desc What vignette do you want to use for this fog?
 * This will override location settings.
 * @default None
 *
 * @param vignetteFilename:str
 * @text Custom
 * @parent Vignette
 * @type file
 * @dir img/parallaxes/
 * @desc Do you wish to use a custom vignette instead?
 * Automatically changes the type to "Custom".
 * @default
 *
 */
//=============================================================================
var tier = tier || 0x0;
var dependencies = [];
var pluginData = $plugins.filter(function (_0x9af8fd) {
    return _0x9af8fd.status && _0x9af8fd.description.includes('[VisualFogs]');
})[0x0];
VisuMZ.VisualFogs.Settings = VisuMZ.VisualFogs.Settings || {};
VisuMZ.ConvertParams = function (_0x132f76, _0x5359b3) {
    for (const _0x2cb39d in _0x5359b3) {
        if (_0x2cb39d.match(/(.*):(.*)/i)) {
            const _0x22f77e = String(RegExp.$1);
            const _0x2fe860 = String(RegExp.$2).toUpperCase().trim();
            let _0x5c61a7;
            let _0x40e244;
            let _0x4a30d0;
            switch (_0x2fe860) {
                case 'NUM':
                    _0x5c61a7 = _0x5359b3[_0x2cb39d] !== '' ? Number(_0x5359b3[_0x2cb39d]) : 0x0;
                    break;
                case 'ARRAYNUM':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x52c22a => Number(_0x52c22a));
                    break;
                case 'EVAL':
                    _0x5c61a7 = _0x5359b3[_0x2cb39d] !== '' ? eval(_0x5359b3[_0x2cb39d]) : null;
                    break;
                case 'ARRAYEVAL':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x36884b => eval(_0x36884b));
                    break;
                case 'JSON':
                    _0x5c61a7 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : '';
                    break;
                case 'ARRAYJSON':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x2504e1 => JSON.parse(_0x2504e1));
                    break;
                case 'FUNC':
                    _0x5c61a7 =
                        _0x5359b3[_0x2cb39d] !== ''
                            ? new Function(JSON.parse(_0x5359b3[_0x2cb39d]))
                            : new Function('return 0');
                    break;
                case 'ARRAYFUNC':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x4b0c4c => new Function(JSON.parse(_0x4b0c4c)));
                    break;
                case 'STR':
                    _0x5c61a7 = _0x5359b3[_0x2cb39d] !== '' ? String(_0x5359b3[_0x2cb39d]) : '';
                    break;
                case 'ARRAYSTR':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x4d2a2c => String(_0x4d2a2c));
                    break;
                case 'STRUCT':
                    _0x4a30d0 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : {};
                    _0x5c61a7 = VisuMZ.ConvertParams({}, _0x4a30d0);
                    break;
                case 'ARRAYSTRUCT':
                    _0x40e244 = _0x5359b3[_0x2cb39d] !== '' ? JSON.parse(_0x5359b3[_0x2cb39d]) : [];
                    _0x5c61a7 = _0x40e244.map(_0x4ada55 =>
                        VisuMZ.ConvertParams({}, JSON.parse(_0x4ada55))
                    );
                    break;
                default:
                    continue;
            }
            _0x132f76[_0x22f77e] = _0x5c61a7;
        }
    }
    return _0x132f76;
};
(_0x1823ec => {
    const _0x5bb8d9 = _0x1823ec.name;
    for (const _0x4b1029 of dependencies) {
        if (!Imported[_0x4b1029]) {
            alert(
                '%1 is missing a required plugin.\nPlease install %2 into the Plugin Manager.'.format(
                    _0x5bb8d9,
                    _0x4b1029
                )
            );
            SceneManager.exit();
            break;
        }
    }
    const _0x16add7 = _0x1823ec.description;
    if (_0x16add7.match(/\[Version[ ](.*?)\]/i)) {
        const _0x46c195 = Number(RegExp.$1);
        if (_0x46c195 !== VisuMZ.VisualFogs.version) {
            alert(
                "%1's version does not match plugin's. Please update it in the Plugin Manager.".format(
                    _0x5bb8d9,
                    _0x46c195
                )
            );
            SceneManager.exit();
        }
    }
    if (_0x16add7.match(/\[Tier[ ](\d+)\]/i)) {
        const _0x2aed99 = Number(RegExp.$1);
        if (_0x2aed99 < tier) {
            alert(
                '%1 is incorrectly placed on the plugin list.\nIt is a Tier %2 plugin placed over other Tier %3 plugins.\nPlease reorder the plugin list from smallest to largest tier numbers.'.format(
                    _0x5bb8d9,
                    _0x2aed99,
                    tier
                )
            );
            SceneManager.exit();
        } else {
            tier = Math.max(_0x2aed99, tier);
        }
    }
    VisuMZ.ConvertParams(VisuMZ.VisualFogs.Settings, _0x1823ec.parameters);
})(pluginData);
VisuMZ.VisualFogs.TemplateSettings = function () {
    return {
        id: 0x0,
        filename: '',
        _fogZero: false,
        _fogLoopX: false,
        _fogLoopY: false,
        _fogSx: 0x0,
        _fogSy: 0x0,
        _fogX: 0x0,
        _fogY: 0x0,
        opacity: Game_Map.DEFAULT_FOG_OPACITY,
        targetOpacity: Game_Map.DEFAULT_FOG_OPACITY,
        opacityDuration: 0x0,
        blendMode: Game_Map.DEFAULT_FOG_BLEND_MODE,
        hue: 0x0,
        hueShift: 0x0,
        colorTone: [0x0, 0x0, 0x0, 0x0],
        maskRegions: [],
        maskTerrainTags: [],
        maskBlur: Game_Map.DEFAULT_FOG_TILE_BLUR,
        maskSpill: Game_Map.DEFAULT_FOG_TILE_SPILL,
        vignette: 'none',
        vignetteFilename: '',
    };
};
PluginManager.registerCommand(pluginData.name, 'FogAddChangeSettings', _0x44c328 => {
    VisuMZ.ConvertParams(_0x44c328, _0x44c328);
    if (_0x44c328.id <= 0x0) {
        return;
    }
    if (_0x44c328.filename === '' || _0x44c328.filename === '>>>ATTENTION<<<') {
        return;
    }
    let _0x5c2fc0 = JsonEx.makeDeepCopy(_0x44c328.Optional);
    if (!_0x5c2fc0.hasOwnProperty('maskRegions')) {
        _0x5c2fc0 = VisuMZ.VisualFogs.TemplateSettings();
    }
    _0x5c2fc0.filename = _0x44c328.filename;
    _0x5c2fc0.id = _0x44c328.id;
    while (_0x5c2fc0.colorTone.length < 0x4) {
        _0x5c2fc0.colorTone.push(0x0);
    }
    _0x5c2fc0._fogX = 0x0;
    _0x5c2fc0._fogY = 0x0;
    _0x5c2fc0.targetOpacity = _0x44c328.opacity;
    _0x5c2fc0.opacityDuration = 0x0;
    _0x5c2fc0.vignette = _0x5c2fc0.vignette || 'none';
    _0x5c2fc0.vignette = _0x5c2fc0.vignette.toLowerCase().trim();
    if (_0x5c2fc0.vignetteFilename !== '') {
        _0x5c2fc0.vignette = 'custom';
    }
    $gameMap.addChangeVisualFog(_0x5c2fc0);
});
PluginManager.registerCommand(pluginData.name, 'FogFadeOpacity', _0x4949ab => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x4949ab, _0x4949ab);
    const _0xdc2a52 = _0x4949ab.list;
    for (const _0x2ed37f of _0xdc2a52) {
        const _0x35af9e = $gameMap.getVisualFogSettings(_0x2ed37f);
        if (!_0x35af9e) {
            continue;
        }
        _0x35af9e.targetOpacity = _0x4949ab.targetOpacity || 0x0;
        _0x35af9e.opacityDuration = _0x4949ab.opacityDuration || 0x0;
        if (_0x35af9e.opacityDuration <= 0x0) {
            _0x35af9e.opacity = _0x35af9e.targetOpacity;
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'FogRemove', _0x1e5d66 => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x1e5d66, _0x1e5d66);
    const _0x40767f = _0x1e5d66.list;
    for (const _0x18f4bc of _0x40767f) {
        $gameMap.removeVisualFog(_0x18f4bc);
    }
});
VisuMZ.VisualFogs.RegExp = {
    Start: /<(?:FOG)[ ](\d+)[ ](?:SETTING|SETTINGS)>/i,
    End: /<\/(?:FOG)[ ](\d+)[ ](?:SETTING|SETTINGS)>/i,
    Filename: /(?:FILENAME|NAME):[ ](.*)/i,
    HorzLoop: /(?:HORZ|HORIZONTAL) (?:LOOP|SCROLL):[ ](.*)/i,
    VertLoop: /(?:VERT|VERTICAL) (?:LOOP|SCROLL):[ ](.*)/i,
    ScrollLock: /<(?:MAP|SCROLL)[ ](?:LOCK|LOCKED)>/i,
    OpacityRate: /(?:OPACITY):[ ](\d+)([%％])/i,
    OpacityFlat: /(?:OPACITY):[ ](\d+)/i,
    BlendMode: /BLEND MODE:[ ](.*)/i,
    Hue: /HUE:[ ](\d+)/i,
    HueShift: /HUE (?:SHIFT|SPEED):[ ](.*)/i,
    Tone: /(?:COLOR TONE|TONE|TINT):[ ](.*)/i,
    MaskRegions: /(?:REGION|REGIONS):[ ](.*)/i,
    MaskTerrainTags: /TERRAIN (?:TAG|TAGS):[ ](.*)/i,
    MaskBlur: /(?:TILE BLUR|BLUR):[ ](.*)/i,
    MaskSpill: /(?:TILE SPILL|SPILL):[ ](.*)/i,
    CustomVignette: /CUSTOM (?:VIGNETTE|OVERLAY):[ ](.*)/i,
    PremadeVignette: /(?:VIGNETTE|OVERLAY):[ ](.*)/i,
};
ImageManager.getFogVignette = function (_0x404b3f) {
    if (!_0x404b3f) {
        return this.getFogVignette_empty();
    }
    this._fogVignettes = this._fogVignettes || {};
    _0x404b3f = _0x404b3f.toLowerCase().trim();
    const _0x668e5f = 'getFogVignette_%1'.format(_0x404b3f);
    if (this._fogVignettes[_0x404b3f]) {
        return this._fogVignettes[_0x404b3f];
    } else {
        return this[_0x668e5f] ? this[_0x668e5f]() : this.getFogVignette_empty();
    }
};
ImageManager.getFogVignette_empty = function () {
    if (this._fogVignettes.empty) {
        return this._fogVignettes.empty;
    }
    const _0x69b22e = new Bitmap(Graphics.width, Graphics.height);
    _0x69b22e.fillRect(0x0, 0x0, _0x69b22e.width, _0x69b22e.height, '#ffffff');
    _0x69b22e._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.empty = _0x69b22e;
    return _0x69b22e;
};
ImageManager.getFogVignette_upper = function () {
    const _0x3ccfb7 = new Bitmap(Graphics.width, Graphics.height);
    _0x3ccfb7.gradientFillRect(
        0x0,
        0x0,
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        true
    );
    _0x3ccfb7._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.upper = _0x3ccfb7;
    return _0x3ccfb7;
};
ImageManager.getFogVignette_lower = function () {
    const _0x258dcd = new Bitmap(Graphics.width, Graphics.height);
    _0x258dcd.gradientFillRect(
        0x0,
        Math.ceil((Graphics.height * 0x2) / 0x3),
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        true
    );
    _0x258dcd._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.lower = _0x258dcd;
    return _0x258dcd;
};
ImageManager.getFogVignette_horizontal = function () {
    const _0x52c10e = new Bitmap(Graphics.width, Graphics.height);
    _0x52c10e.gradientFillRect(
        0x0,
        0x0,
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        true
    );
    _0x52c10e.gradientFillRect(
        0x0,
        Math.ceil((Graphics.height * 0x2) / 0x3),
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        true
    );
    _0x52c10e._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.horizontal = _0x52c10e;
    return _0x52c10e;
};
ImageManager.getFogVignette_left = function () {
    const _0x458178 = new Bitmap(Graphics.width, Graphics.height);
    _0x458178.gradientFillRect(
        0x0,
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        false
    );
    _0x458178._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.left = _0x458178;
    return _0x458178;
};
ImageManager.getFogVignette_right = function () {
    const _0x28593e = new Bitmap(Graphics.width, Graphics.height);
    _0x28593e.gradientFillRect(
        Math.ceil((Graphics.width * 0x2) / 0x3),
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        false
    );
    _0x28593e._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.right = _0x28593e;
    return _0x28593e;
};
ImageManager.getFogVignette_vertical = function () {
    const _0x4cdd79 = new Bitmap(Graphics.width, Graphics.height);
    _0x4cdd79.gradientFillRect(
        0x0,
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        false
    );
    _0x4cdd79.gradientFillRect(
        Math.ceil((Graphics.width * 0x2) / 0x3),
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        false
    );
    _0x4cdd79._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.vertical = _0x4cdd79;
    return _0x4cdd79;
};
ImageManager.getFogVignette_border = function () {
    const _0x2bac31 = new Bitmap(Graphics.width, Graphics.height);
    _0x2bac31.gradientFillRect(
        0x0,
        0x0,
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        true
    );
    _0x2bac31.gradientFillRect(
        0x0,
        Math.ceil((Graphics.height * 0x2) / 0x3),
        Graphics.width,
        Math.ceil(Graphics.height / 0x3),
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        true
    );
    _0x2bac31.gradientFillRect(
        0x0,
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        '#ffffff',
        'rgba(0, 0, 0, 0)',
        false
    );
    _0x2bac31.gradientFillRect(
        Math.ceil((Graphics.width * 0x2) / 0x3),
        0x0,
        Math.ceil(Graphics.width / 0x3),
        Graphics.height,
        'rgba(0, 0, 0, 0)',
        '#ffffff',
        false
    );
    _0x2bac31._customModified = false;
    this._fogVignettes = this._fogVignettes || {};
    this._fogVignettes.border = _0x2bac31;
    return _0x2bac31;
};
SceneManager.isSceneMap = function () {
    return this._scene && this._scene.constructor === Scene_Map;
};
SceneManager.isInstanceOfSceneMap = function () {
    return this._scene && this._scene instanceof Scene_Map;
};
VisuMZ.VisualFogs.Game_Map_setup = Game_Map.prototype.setup;
Game_Map.prototype.setup = function (_0x2bf848) {
    VisuMZ.VisualFogs.Game_Map_setup.call(this, _0x2bf848);
    this.setupVisualFogs();
};
Game_Map.DEFAULT_FOG_OPACITY = VisuMZ.VisualFogs.Settings.FogOpacity;
Game_Map.DEFAULT_FOG_BLEND_MODE = VisuMZ.VisualFogs.Settings.BlendMode;
Game_Map.DEFAULT_FOG_TILE_BLUR = VisuMZ.VisualFogs.Settings.MaskBlur;
Game_Map.DEFAULT_FOG_TILE_SPILL = VisuMZ.VisualFogs.Settings.MaskSpill;
Game_Map.prototype.setupVisualFogs = function () {
    this._visualFogSettings = [null];
    if (!$dataMap) {
        return;
    }
    const _0xac7f91 = VisuMZ.VisualFogs.CreateLayerData();
    for (const _0x920de1 of _0xac7f91) {
        if (!_0x920de1) {
            continue;
        }
        this._visualFogSettings[_0x920de1.id] = _0x920de1;
    }
};
VisuMZ.VisualFogs.CreateLayerData = function () {
    if (!$dataMap) {
        return [];
    }
    const _0xde94a5 = [];
    const _0x7ef7ca = VisuMZ.VisualFogs.TemplateSettings();
    if (!$dataMap.note) {
        return [];
    }
    const _0x50ecad = VisuMZ.VisualFogs.RegExp;
    const _0xfb8aac = $dataMap.note.split(/[\r\n]+/);
    let _0x345ecf = JsonEx.makeDeepCopy(_0x7ef7ca);
    for (const _0xc94455 of _0xfb8aac) {
        if (_0xc94455.match(_0x50ecad.Start)) {
            _0x345ecf.id = Number(RegExp.$1);
        } else {
            if (_0xc94455.match(_0x50ecad.End)) {
                const _0x2664e1 = Number(RegExp.$1);
                if (_0x2664e1 > 0x0 && _0x2664e1 === _0x345ecf.id && _0x345ecf.filename !== '') {
                    _0xde94a5.push(_0x345ecf);
                }
                _0x345ecf = JsonEx.makeDeepCopy(_0x7ef7ca);
            } else {
                if (_0x345ecf.id <= 0x0) {
                    continue;
                }
            }
        }
        if (_0xc94455.match(_0x50ecad.Filename)) {
            _0x345ecf.filename = String(RegExp.$1).trim();
            if (_0x345ecf.filename.charAt(0x0) === '!') {
                _0x345ecf._fogZero = true;
            }
        } else {
            if (_0xc94455.match(_0x50ecad.HorzLoop)) {
                _0x345ecf._fogLoopX = true;
                _0x345ecf._fogSx = Number(RegExp.$1) || 0x0;
            } else {
                if (_0xc94455.match(_0x50ecad.VertLoop)) {
                    _0x345ecf._fogLoopY = true;
                    _0x345ecf._fogSy = Number(RegExp.$1) || 0x0;
                } else {
                    if (_0xc94455.match(_0x50ecad.ScrollLock)) {
                        _0x345ecf._fogZero = true;
                    } else {
                        if (_0xc94455.match(_0x50ecad.OpacityRate)) {
                            const _0x353bc2 = Number(RegExp.$1) * 0.01;
                            _0x345ecf.opacity = Math.round(_0x353bc2 * 0xff).clamp(0x0, 0xff);
                        } else {
                            if (_0xc94455.match(_0x50ecad.OpacityFlat)) {
                                _0x345ecf.opacity = Number(RegExp.$1).clamp(0x0, 0xff);
                            } else {
                                if (_0xc94455.match(_0x50ecad.BlendMode)) {
                                    const _0x262be2 = String(RegExp.$1).toUpperCase().trim();
                                    const _0x5831d9 = ['NORMAL', 'ADDITIVE', 'MULTIPLY', 'SCREEN'];
                                    _0x345ecf.blendMode = _0x5831d9
                                        .indexOf(_0x262be2)
                                        .clamp(0x0, 0x3);
                                } else {
                                    if (_0xc94455.match(_0x50ecad.Hue)) {
                                        _0x345ecf.hue = Number(RegExp.$1).clamp(0x0, 0x168);
                                    } else {
                                        if (_0xc94455.match(_0x50ecad.HueShift)) {
                                            _0x345ecf.hueShift = Number(RegExp.$1) || 0x0;
                                        } else {
                                            if (_0xc94455.match(_0x50ecad.Tone)) {
                                                const _0x549ee2 = String(RegExp.$1)
                                                    .split(',')
                                                    .map(_0x5ce07c => Number(_0x5ce07c) || 0x0);
                                                while (_0x549ee2.length < 0x4) {
                                                    _0x549ee2.push(0x0);
                                                }
                                                _0x345ecf.colorTone = _0x549ee2;
                                            } else {
                                                if (_0xc94455.match(_0x50ecad.MaskRegions)) {
                                                    const _0x40169b = String(RegExp.$1)
                                                        .split(',')
                                                        .map(_0x3e43db => Number(_0x3e43db) || 0x1);
                                                    _0x345ecf.maskRegions = _0x40169b;
                                                } else {
                                                    if (
                                                        _0xc94455.match(_0x50ecad.MaskTerrainTags)
                                                    ) {
                                                        const _0x1308d6 = String(RegExp.$1)
                                                            .split(',')
                                                            .map(
                                                                _0x219ec8 =>
                                                                    Number(_0x219ec8) || 0x1
                                                            );
                                                        _0x345ecf.maskTerrainTags = _0x1308d6;
                                                    } else {
                                                        if (_0xc94455.match(_0x50ecad.MaskBlur)) {
                                                            _0x345ecf.maskBlur = Math.max(
                                                                Number(RegExp.$1) || 0x0,
                                                                0x0
                                                            );
                                                        } else {
                                                            if (
                                                                _0xc94455.match(_0x50ecad.MaskSpill)
                                                            ) {
                                                                _0x345ecf.maskSpill = Math.max(
                                                                    Number(RegExp.$1) || 0x0,
                                                                    0x0
                                                                );
                                                            } else {
                                                                if (
                                                                    _0xc94455.match(
                                                                        _0x50ecad.CustomVignette
                                                                    )
                                                                ) {
                                                                    _0x345ecf.vignetteFilename = (
                                                                        String(RegExp.$1) || ''
                                                                    ).trim();
                                                                    _0x345ecf.vignette = 'custom';
                                                                } else if (
                                                                    _0xc94455.match(
                                                                        _0x50ecad.PremadeVignette
                                                                    )
                                                                ) {
                                                                    _0x345ecf.vignette = (
                                                                        String(RegExp.$1) || ''
                                                                    ).toLowerCase();
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    return _0xde94a5;
};
Game_Map.prototype.getVisualFogs = function () {
    if (this._visualFogSettings === undefined) {
        this.setupVisualFogs();
    }
    return this._visualFogSettings.filter(_0x9ec771 => !!_0x9ec771);
};
Game_Map.prototype.getVisualFogSettings = function (_0x2647dc) {
    this._visualFogSettings = this._visualFogSettings || [];
    return this._visualFogSettings[_0x2647dc] || null;
};
Game_Map.prototype.getVisualFogOx = function (_0x3f2a05) {
    const _0xfbac07 = this.getVisualFogSettings(_0x3f2a05);
    if (_0xfbac07._fogZero) {
        return _0xfbac07._fogX * this.tileWidth();
    } else {
        return _0xfbac07._fogLoopX ? (_0xfbac07._fogX * this.tileWidth()) / 0x2 : 0x0;
    }
};
Game_Map.prototype.getVisualFogOy = function (_0x59c30b) {
    const _0xdff266 = this.getVisualFogSettings(_0x59c30b);
    if (_0xdff266._fogZero) {
        return _0xdff266._fogY * this.tileHeight();
    } else {
        return _0xdff266._fogLoopY ? (_0xdff266._fogY * this.tileHeight()) / 0x2 : 0x0;
    }
};
Game_Map.prototype.removeVisualFog = function (_0x56a3a3) {
    this._visualFogSettings = this._visualFogSettings || [];
    if (!this._visualFogSettings[_0x56a3a3]) {
        return;
    }
    this._visualFogSettings[_0x56a3a3] = null;
    const _0x56d567 = SceneManager._scene._spriteset;
    if (_0x56d567) {
        _0x56d567.removeVisualFogLayer(_0x56a3a3);
    }
};
Game_Map.prototype.addChangeVisualFog = function (_0xcab798) {
    const _0x526ffc = _0xcab798.id;
    if (_0xcab798.filename.charAt(0x0) === '!') {
        _0xcab798._fogZero = true;
    }
    let _0x378043 = false;
    this._visualFogSettings = this._visualFogSettings || [];
    if (this._visualFogSettings[_0x526ffc]) {
        const _0x4bc61a = this._visualFogSettings[_0x526ffc];
        if (!_0x4bc61a.maskRegions.equals(_0xcab798.maskRegions)) {
            _0x378043 = true;
        } else {
            if (!_0x4bc61a.maskTerrainTags.equals(_0xcab798.maskTerrainTags)) {
                _0x378043 = true;
            } else if (_0x4bc61a.vignette !== 'none') {
                _0x378043 = true;
            }
        }
    }
    this._visualFogSettings[_0x526ffc] = _0xcab798;
    if (!SceneManager.isSceneMap()) {
        return;
    }
    const _0x598051 = SceneManager._scene._spriteset;
    if (_0x598051) {
        _0x598051.updateVisualFogLayer(_0x526ffc, _0x378043);
    }
};
VisuMZ.VisualFogs.Game_Map_setDisplayPos = Game_Map.prototype.setDisplayPos;
Game_Map.prototype.setDisplayPos = function (_0x3fb3bd, _0x50f0b4) {
    VisuMZ.VisualFogs.Game_Map_setDisplayPos.call(this, _0x3fb3bd, _0x50f0b4);
    for (const _0x1f0b37 of this.getVisualFogs()) {
        if (!_0x1f0b37) {
            continue;
        }
        if (this.isLoopHorizontal()) {
            _0x1f0b37._fogX = _0x3fb3bd;
        } else {
            _0x1f0b37._fogX = this._displayX;
        }
        if (this.isLoopVertical()) {
            _0x1f0b37._fogY = _0x50f0b4;
        } else {
            _0x1f0b37._fogY = this._displayY;
        }
    }
};
VisuMZ.VisualFogs.Game_Map_scrollLeft = Game_Map.prototype.scrollLeft;
Game_Map.prototype.scrollLeft = function (_0x58076a) {
    const _0x2278e8 = this._displayX;
    VisuMZ.VisualFogs.Game_Map_scrollLeft.call(this, _0x58076a);
    for (const _0x219f0f of this.getVisualFogs()) {
        if (!_0x219f0f) {
            continue;
        }
        if (this.isLoopHorizontal()) {
            if (_0x219f0f._fogLoopX) {
                _0x219f0f._fogX -= _0x58076a;
            }
        } else if (this.width() >= this.screenTileX()) {
            _0x219f0f._fogX += this._displayX - _0x2278e8;
        }
    }
};
VisuMZ.VisualFogs.Game_Map_scrollRight = Game_Map.prototype.scrollRight;
Game_Map.prototype.scrollRight = function (_0x56a9fa) {
    const _0xbdd1a3 = this._displayX;
    VisuMZ.VisualFogs.Game_Map_scrollRight.call(this, _0x56a9fa);
    for (const _0x4c6121 of this.getVisualFogs()) {
        if (!_0x4c6121) {
            continue;
        }
        if (this.isLoopHorizontal()) {
            if (_0x4c6121._fogLoopX) {
                _0x4c6121._fogX += _0x56a9fa;
            }
        } else if (this.width() >= this.screenTileX()) {
            _0x4c6121._fogX += this._displayX - _0xbdd1a3;
        }
    }
};
VisuMZ.VisualFogs.Game_Map_scrollDown = Game_Map.prototype.scrollDown;
Game_Map.prototype.scrollDown = function (_0x58142f) {
    const _0xd067e3 = this._displayY;
    VisuMZ.VisualFogs.Game_Map_scrollDown.call(this, _0x58142f);
    for (const _0x5edd60 of this.getVisualFogs()) {
        if (!_0x5edd60) {
            continue;
        }
        if (this.isLoopVertical()) {
            if (_0x5edd60._fogLoopY) {
                _0x5edd60._fogY += _0x58142f;
            }
        } else if (this.height() >= this.screenTileY()) {
            _0x5edd60._fogY += this._displayY - _0xd067e3;
        }
    }
};
VisuMZ.VisualFogs.Game_Map_scrollUp = Game_Map.prototype.scrollUp;
Game_Map.prototype.scrollUp = function (_0x5f0880) {
    const _0x3b42ae = this._displayY;
    VisuMZ.VisualFogs.Game_Map_scrollUp.call(this, _0x5f0880);
    for (const _0x4af221 of this.getVisualFogs()) {
        if (!_0x4af221) {
            continue;
        }
        if (this.isLoopVertical()) {
            if (_0x4af221._fogLoopY) {
                _0x4af221._fogY -= _0x5f0880;
            }
        } else if (this.height() >= this.screenTileY()) {
            _0x4af221._fogY += this._displayY - _0x3b42ae;
        }
    }
};
VisuMZ.VisualFogs.Game_Map_updateParallax = Game_Map.prototype.updateParallax;
Game_Map.prototype.updateParallax = function () {
    VisuMZ.VisualFogs.Game_Map_updateParallax.call(this);
    for (const _0x3fc368 of this.getVisualFogs()) {
        if (!_0x3fc368) {
            continue;
        }
        this.updateVisualFogSettings(_0x3fc368);
    }
};
Game_Map.prototype.updateVisualFogSettings = function (_0x2554a9) {
    if (_0x2554a9._fogLoopX) {
        _0x2554a9._fogX += _0x2554a9._fogSx / this.tileWidth() / 0x2;
    }
    if (_0x2554a9._fogLoopY) {
        _0x2554a9._fogY += _0x2554a9._fogSy / this.tileHeight() / 0x2;
    }
    _0x2554a9.hue += _0x2554a9.hueShift;
    if (_0x2554a9.opacityDuration > 0x0) {
        const _0x505157 = _0x2554a9.opacityDuration;
        _0x2554a9.opacity =
            (_0x2554a9.opacity * (_0x505157 - 0x1) + _0x2554a9.targetOpacity) / _0x505157;
        _0x2554a9.opacityDuration--;
    }
};
function Sprite_VisualFog() {
    this.initialize(...arguments);
}
Sprite_VisualFog.prototype = Object.create(TilingSprite.prototype);
Sprite_VisualFog.prototype.constructor = Sprite_VisualFog;
Sprite_VisualFog.prototype.initialize = function (_0x22749d) {
    this._id = _0x22749d;
    TilingSprite.prototype.initialize.call(this);
    this._createColorFilter();
    this.loadBitmap();
    this.bitmap.addLoadListener(this.createMaskSprite.bind(this));
};
Sprite_VisualFog.prototype.settings = function () {
    return $gameMap.getVisualFogSettings(this._id);
};
Sprite_VisualFog.prototype._createColorFilter = function () {
    this._hue = 0x0;
    this._colorTone = [0x0, 0x0, 0x0, 0x0];
    this._colorFilter = new ColorFilter();
    if (!this.filters) {
        this.filters = [];
    }
    this.filters.push(this._colorFilter);
};
Sprite_VisualFog.prototype._updateColorFilter = function () {
    if (!this._colorFilter) {
        this._createColorFilter();
    }
    this._colorFilter.setHue(this._hue);
    this._colorFilter.setColorTone(this._colorTone);
};
Sprite_VisualFog.prototype.loadBitmap = function () {
    this._fogName = this.settings().filename;
    this.bitmap = ImageManager.loadParallax(this._fogName);
};
Sprite_VisualFog.prototype.createMaskSprite = function () {
    this._maskSprite = new Sprite();
    this.createMaskBitmap();
};
Sprite_VisualFog.prototype.createMaskBitmap = function () {
    if (this._maskSprite.bitmap) {
        this.removeChild(this._maskSprite);
    }
    const _0x326008 = this.settings().maskRegions;
    const _0x4e2f02 = this.settings().maskTerrainTags;
    if (this.settings().vignette === 'custom') {
        this.loadCustomVignette();
    } else {
        if (this.settings().vignette !== 'none') {
            this.loadTemplateVignette();
        } else {
            if (_0x326008.length > 0x0 || _0x4e2f02.length > 0x0) {
                this.createMaskTileBitmap();
            } else if (this.settings().vignette === 'none') {
                this.loadTemplateVignette();
            }
        }
    }
    this.addChild(this._maskSprite);
    this._maskFilter = new PIXI.SpriteMaskFilter(this._maskSprite);
    this.filters.push(this._maskFilter);
    if (this._blurFilter) {
        this.filters.push(this._blurFilter);
    }
};
Sprite_VisualFog.prototype.loadCustomVignette = function () {
    const _0x327dc4 = this.settings().vignetteFilename;
    this._maskSprite.bitmap = ImageManager.loadParallax(_0x327dc4);
    this._maskSprite.bitmap._customModified = false;
};
Sprite_VisualFog.prototype.loadTemplateVignette = function () {
    const _0x1c5427 = this.settings().vignette;
    this._maskSprite.bitmap = ImageManager.getFogVignette(_0x1c5427);
};
Sprite_VisualFog.prototype.createMaskTileBitmap = function () {
    const _0x492488 = this.settings().maskRegions;
    const _0x369b20 = this.settings().maskTerrainTags;
    if (_0x492488.length <= 0x0 && _0x369b20.length <= 0x0) {
        return;
    }
    if ($gameMap.isLoopHorizontal() || $gameMap.isLoopVertical()) {
        return;
    }
    const _0x23b212 = $gameMap.width();
    const _0x4b70ab = $gameMap.height();
    const _0x48fbd4 = $gameMap.tileWidth();
    const _0x319bc3 = $gameMap.tileHeight();
    const _0x5a6a09 = this.settings().maskSpill;
    const _0x492f62 = _0x48fbd4 + _0x5a6a09 * 0x2;
    const _0x49789f = _0x319bc3 + _0x5a6a09 * 0x2;
    this._maskSprite.bitmap = new Bitmap(_0x23b212 * _0x48fbd4, _0x4b70ab * _0x319bc3);
    for (let _0x46495f = 0x0; _0x46495f < _0x23b212; _0x46495f++) {
        for (let _0x411b14 = 0x0; _0x411b14 < _0x4b70ab; _0x411b14++) {
            const _0x28970d = $gameMap.regionId(_0x46495f, _0x411b14);
            if (
                _0x492488.includes(_0x28970d) ||
                _0x369b20.includes($gameMap.terrainTag(_0x46495f, _0x411b14))
            ) {
                this._maskSprite.bitmap.fillRect(
                    _0x46495f * _0x48fbd4 - _0x5a6a09,
                    _0x411b14 * _0x319bc3 - _0x5a6a09,
                    _0x492f62,
                    _0x49789f,
                    '#ffffff'
                );
                if (Imported.VisuMZ_2_TileGrafterSystem && _0x492488.includes(_0x28970d)) {
                    SceneManager._scene._grafterRefreshRegions.push(_0x28970d);
                }
            }
        }
    }
    this.filters = [];
    if (!!PIXI.filters.BlurFilter && !this._blurFilter) {
        this._blurFilter = new PIXI.filters.BlurFilter((clamp = true));
    }
    if (this._blurFilter) {
        const _0x378e7e = this.settings().maskBlur;
        this._blurFilter.blur = _0x378e7e || 0.01;
    }
};
Sprite_VisualFog.prototype.drawMaskTile = function (_0x4c4a0e, _0x1d2a24) {};
Sprite_VisualFog.prototype.update = function () {
    TilingSprite.prototype.update.call(this);
    if (!this.bitmap) {
        return;
    }
    if (!this.settings()) {
        return;
    }
    this.updateOpacity();
    this.updateOrigin();
    this.updateBlendMode();
    this.updateHue();
    this.updateTone();
    this.updateMask();
};
Sprite_VisualFog.prototype.updateOpacity = function () {
    this.opacity = this.settings().opacity;
};
Sprite_VisualFog.prototype.updateOrigin = function () {
    this.origin.x = $gameMap.getVisualFogOx(this._id);
    this.origin.y = $gameMap.getVisualFogOy(this._id);
};
Sprite_VisualFog.prototype.updateBlendMode = function () {
    if (this._maskFilter) {
        this._maskFilter.blendMode = this.settings().blendMode;
    }
};
Sprite_VisualFog.prototype.updateHue = function () {
    this.setHue(this.settings().hue);
};
Sprite_VisualFog.prototype.setHue = function (_0x144829) {
    if (this._hue !== Number(_0x144829)) {
        this._hue = Number(_0x144829);
        this._updateColorFilter();
    }
};
Sprite_VisualFog.prototype.updateTone = function () {
    this.setColorTone(this.settings().colorTone);
};
Sprite_VisualFog.prototype.setColorTone = function (_0x471414) {
    if (!(_0x471414 instanceof Array)) {
        throw new Error('Argument must be an array');
    }
    if (!this._colorTone.equals(_0x471414)) {
        this._colorTone = _0x471414.clone();
        this._updateColorFilter();
    }
};
Sprite_VisualFog.prototype.updateMask = function () {
    if (!this._maskSprite) {
        return;
    }
    const _0x51f8af = this.settings().maskRegions;
    const _0x348ccf = this.settings().maskTerrainTags;
    if (this.settings().vignette !== 'none') {
        this._maskSprite.x = 0x0;
        this._maskSprite.y = 0x0;
        if (!Imported.VisuMZ_4_MapCameraZoom) {
            return;
        }
        this._maskSprite.scale.x = 0x1 / $gameScreen.zoomScale();
        this._maskSprite.scale.y = 0x1 / $gameScreen.zoomScale();
    } else {
        if (_0x51f8af.length > 0x0 || _0x348ccf.length > 0x0) {
            this._maskSprite.x = Math.floor(-$gameMap.displayX() * $gameMap.tileWidth());
            this._maskSprite.y = Math.floor(-$gameMap.displayY() * $gameMap.tileHeight());
            this._maskSprite.scale.x = 0x1;
            this._maskSprite.scale.y = 0x1;
        } else if (this.settings().vignette === 'none') {
            this._maskSprite.x = 0x0;
            this._maskSprite.y = 0x0;
            this._maskSprite.scale.x = 0x1;
            this._maskSprite.scale.y = 0x1;
        }
    }
};
VisuMZ.VisualFogs.Spriteset_Map_createWeather = Spriteset_Map.prototype.createWeather;
Spriteset_Map.prototype.createWeather = function () {
    this.createFogContainer();
    this.createFogLayers();
    this.sortVisualFogs();
    VisuMZ.VisualFogs.Spriteset_Map_createWeather.call(this);
};
Spriteset_Map.prototype.createFogContainer = function () {
    this._fogContainer = new Sprite();
    this._baseSprite.addChild(this._fogContainer);
    this._fogDataRef = [null];
};
Spriteset_Map.prototype.createFogLayers = function () {
    const _0x5bc989 = $gameMap.getVisualFogs();
    for (const _0x5395a5 of _0x5bc989) {
        if (!_0x5395a5) {
            continue;
        }
        this.createNewFogLayer(_0x5395a5);
    }
};
Spriteset_Map.prototype.createNewFogLayer = function (_0x1bdb27) {
    if (!_0x1bdb27) {
        return;
    }
    const _0x485305 = new Sprite_VisualFog(_0x1bdb27.id);
    _0x485305.move(0x0, 0x0, Graphics.width, Graphics.height);
    this._fogContainer.addChild(_0x485305);
};
Spriteset_Map.prototype.sortVisualFogs = function () {
    this._fogContainer.children.sort((_0x207b99, _0x437df1) => _0x207b99._id - _0x437df1._id);
};
Spriteset_Map.prototype.findTargetVisualFog = function (_0x19e079) {
    return this._fogContainer.children.find(_0x6c8cd7 => _0x6c8cd7._id === _0x19e079);
};
Spriteset_Map.prototype.removeVisualFogLayer = function (_0x43f979) {
    const _0x50efbc = this.findTargetVisualFog(_0x43f979);
    if (_0x50efbc) {
        this._fogContainer.removeChild(_0x50efbc);
    }
};
Spriteset_Map.prototype.updateVisualFogLayer = function (_0x2968ed, _0x21bcb2) {
    const _0x5d5c01 = this.findTargetVisualFog(_0x2968ed);
    if (!_0x5d5c01) {
        this.createNewFogLayer($gameMap.getVisualFogSettings(_0x2968ed));
        this.sortVisualFogs();
    } else {
        _0x5d5c01.loadBitmap();
        if (_0x21bcb2) {
            _0x5d5c01.bitmap.addLoadListener(_0x5d5c01.createMaskBitmap.bind(_0x5d5c01));
        }
    }
};
