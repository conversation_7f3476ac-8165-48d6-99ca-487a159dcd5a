/* Stagger System Plugin with <PERSON> Meter for RPG Maker MZ
 *
 * Enhanced with opacity-based hiding when enemies reach 0 HP
 * Configuration: STAGGER_GRADUAL_FADE (line ~240) controls fade behavior
 */
(() => {
    const parameters = PluginManager.parameters('StaggerSystem');

    // Get parameters with defaults
    const staggerText = parameters['StaggerText'] || 'STAGGERED!';
    const staggerSymbolFontSize = parseInt(parameters['StaggerSymbolFontSize']) || 18;

    // Extend Enemy Data to Include Stagger Attributes
    Game_Enemy.prototype.initStaggerGauge = function () {
        this._staggerGauge = 0;
        this._staggerMax = Math.min(Math.max(Math.floor(this.mhp / 100), 1), 50);
        this._isStaggered = false;
        this._staggerDamageMultiplier = 1.0;
        this._lastStaggerUpdate = 0;
        this._staggerUpdateInterval = 16; // ~60 FPS
        this._staggerMultiplierNeedsUpdate = false;
    };

    const _Game_Enemy_initialize = Game_Enemy.prototype.initialize;
    Game_Enemy.prototype.initialize = function (enemyId, x, y) {
        _Game_Enemy_initialize.call(this, enemyId, x, y);
        this.initStaggerGauge();
    };

    // 🚀 OPTIMIZED: Draw the Stagger Gauge Bar with improved performance
    Sprite_Enemy.prototype.drawStaggerGauge = function () {
        // 🐛 FIX: Safety check - don't draw for dead enemies
        if (!this._enemy || this._enemy.isDead() || this._enemy.hp <= 0) {
            if (this._staggerGaugeSprite) {
                this._hideStaggerGauge();
            }
            return;
        }

        if (!this._staggerGaugeSprite) {
            this._initializeStaggerGauge();
        }

        // 🐛 FIX: Safety check - ensure stagger gauge sprites still exist and are properly positioned
        if (this._staggerGaugeSprite && !this._staggerGaugeSprite.parent) {
            // Gauge was removed, recreate it
            this._initializeStaggerGauge();
        }

        // 🐛 FIX: Ensure positioning is correct (in case of sprite animation interference)
        if (this._staggerGaugeSprite) {
            this._staggerGaugeSprite.x = this._staggerGaugeSprite._originalX || 0;
            this._staggerGaugeSprite.y = this._staggerGaugeSprite._originalY || -15;
        }
        if (this._staggerSkullSprite) {
            this._staggerSkullSprite.x = this._staggerSkullSprite._originalX || 0;
            this._staggerSkullSprite.y = this._staggerSkullSprite._originalY || -15;
        }

        // OPTIMIZED: Reduce update frequency to 30 FPS for better performance
        const now = performance.now();
        if (now - this._lastStaggerUpdate < 33) {
            // ~30 FPS instead of 60
            return;
        }
        this._lastStaggerUpdate = now;

        const currentRate = this._staggerGaugeSprite.targetRate;
        const targetRate = this._enemy._staggerGauge / this._enemy._staggerMax;
        this._staggerGaugeSprite.previousRate = currentRate;
        this._staggerGaugeSprite.targetRate += (targetRate - currentRate) * 0.1;

        // OPTIMIZED: Pre-calculate and cache color values
        const progress = this._staggerGaugeSprite.targetRate;
        const colorKey = Math.floor(progress * 100); // Cache key for color

        if (!this._colorCache) this._colorCache = new Map();

        let colorData = this._colorCache.get(colorKey);
        if (!colorData) {
            if (progress < 0.5) {
                colorData = {
                    red: Math.floor(510 * progress),
                    green: 255,
                    rgbString: `rgb(${Math.floor(510 * progress)},255,0)`,
                };
            } else {
                const green = Math.floor(255 - 510 * (progress - 0.5));
                colorData = {
                    red: 255,
                    green: green,
                    rgbString: `rgb(255,${green},0)`,
                };
            }

            // Limit cache size to prevent memory leaks
            if (this._colorCache.size > 100) {
                const firstKey = this._colorCache.keys().next().value;
                this._colorCache.delete(firstKey);
            }
            this._colorCache.set(colorKey, colorData);
        }

        this._staggerGaugeSprite.currentColor = colorData;

        // OPTIMIZED: More efficient change detection
        const rateChanged =
            Math.abs(this._staggerGaugeSprite.lastDrawnRate - this._staggerGaugeSprite.targetRate) >
            0.02; // Increased threshold
        const staggerStateChanged =
            this._staggerGaugeSprite.lastStaggerState !== this._enemy._isStaggered;
        const multiplierNeedsUpdate = this._staggerMultiplierNeedsUpdate;

        if (rateChanged || staggerStateChanged || multiplierNeedsUpdate) {
            this._redrawStaggerGauge();
            this._staggerMultiplierNeedsUpdate = false;
        }

        this._updateStaggerEffects();
    };

    Sprite_Enemy.prototype._initializeStaggerGauge = function () {
        // 🐛 FIX: Don't create stagger gauge for dead enemies
        if (this._enemy && (this._enemy.isDead() || this._enemy.hp <= 0)) {
            return;
        }

        this._staggerGaugeSprite = new Sprite(new Bitmap(64, 8));
        this._staggerGaugeSprite.x = 0;
        this._staggerGaugeSprite.y = -15;
        this.addChild(this._staggerGaugeSprite);
        this._staggerGaugeSprite.anchor.set(0.5);
        this._staggerGaugeSprite.targetRate = 0;
        this._staggerGaugeSprite.previousRate = 0;
        this._staggerGaugeSprite.lastDrawnRate = -1;
        this._staggerGaugeSprite.lastStaggerState = false;
        this._staggerGaugeSprite.currentColor = { red: 0, green: 255 };
        this._lastStaggerUpdate = 0;
        this._staggerUpdateInterval = 16;
        this._staggerMultiplierNeedsUpdate = false;

        // Add stagger symbol sprite
        this._staggerSkullSprite = new Sprite(new Bitmap(64, 64));
        this._staggerSkullSprite.bitmap.fontFace = $gameSystem.mainFontFace();
        this._staggerSkullSprite.bitmap.fontSize = staggerSymbolFontSize;
        this._staggerSkullSprite.bitmap.textColor = '#FFFFFF';
        this._staggerSkullSprite.anchor.set(0.5);
        this._staggerSkullSprite.x = 0;
        this._staggerSkullSprite.y = -15;
        this._staggerSkullSprite.visible = false;
        this.addChild(this._staggerSkullSprite);

        // 🐛 FIX: Store original position for safety checks
        this._staggerGaugeSprite._originalX = 0;
        this._staggerGaugeSprite._originalY = -15;
        this._staggerSkullSprite._originalX = 0;
        this._staggerSkullSprite._originalY = -15;
    };

    // 🚀 OPTIMIZED: Redraw stagger gauge with cached colors and reduced operations
    Sprite_Enemy.prototype._redrawStaggerGauge = function () {
        this._staggerGaugeSprite.bitmap.clear();
        this._staggerGaugeSprite.bitmap.fillRect(0, 0, 64, 8, '#444444');

        // OPTIMIZED: Use pre-calculated color string
        const color = this._staggerGaugeSprite.currentColor.rgbString;
        const borderColor = this._enemy._isStaggered ? '#FF0000' : color;

        this._staggerGaugeSprite.bitmap.strokeRect(0, 0, 64, 8, borderColor);
        this._staggerGaugeSprite.bitmap.fillRect(
            0,
            0,
            64 * this._staggerGaugeSprite.targetRate,
            8,
            this._enemy._isStaggered ? '#FF0000' : color
        );

        // OPTIMIZED: Update stagger symbol with cached font properties
        if (this._enemy._isStaggered) {
            this._staggerSkullSprite.bitmap.clear();

            // Only set font properties if they've changed
            if (!this._fontPropertiesSet) {
                this._staggerSkullSprite.bitmap.fontFace = $gameSystem.mainFontFace();
                this._staggerSkullSprite.bitmap.fontSize = staggerSymbolFontSize;
                this._staggerSkullSprite.bitmap.textColor = '#FFFFFF';
                this._staggerSkullSprite.bitmap.outlineColor = '#000000';
                this._staggerSkullSprite.bitmap.outlineWidth = 2;
                this._fontPropertiesSet = true;
            }

            // Get current multiplier
            const multiplier = this._enemy._staggerDamageMultiplier;
            const multiplierText = `${multiplier}x`;

            this._staggerSkullSprite.bitmap.drawText(multiplierText, 0, 0, 64, 64, 'center');
        }

        this._staggerSkullSprite.visible = this._enemy._isStaggered;

        // Update tracking variables
        this._staggerGaugeSprite.lastDrawnRate = this._staggerGaugeSprite.targetRate;
        this._staggerGaugeSprite.lastStaggerState = this._enemy._isStaggered;
    };

    Sprite_Enemy.prototype._updateStaggerEffects = function () {
        // Handle scaling and effects
        if (this._staggerGaugeSprite.pulseScaling) {
            this._staggerGaugeSprite.scale.x += (1.0 - this._staggerGaugeSprite.scale.x) * 0.1;
            this._staggerGaugeSprite.scale.y += (1.0 - this._staggerGaugeSprite.scale.y) * 0.1;
            if (Math.abs(this._staggerGaugeSprite.scale.x - 1.0) < 0.05) {
                this._staggerGaugeSprite.scale.set(1.0);
                this._staggerGaugeSprite.pulseScaling = false;
            }
        }

        // Handle effects
        if (this._staggerGaugeSprite.hitWhileStaggered) {
            this._staggerGaugeSprite.setBlendColor([255, 0, 0, 192]);
            this._staggerGaugeSprite.hitWhileStaggeredDuration--;
            if (this._staggerGaugeSprite.hitWhileStaggeredDuration <= 0) {
                this._staggerGaugeSprite.setBlendColor([0, 0, 0, 0]);
                this._staggerGaugeSprite.hitWhileStaggered = false;
            }
        } else if (this._staggerGaugeSprite.glowEffect) {
            const fadeAmount = Math.max(
                0,
                192 * (this._staggerGaugeSprite.glowEffectDuration / 30)
            );
            this._staggerGaugeSprite.setBlendColor([
                this._staggerGaugeSprite.currentColor.red,
                this._staggerGaugeSprite.currentColor.green,
                0,
                fadeAmount,
            ]);
            this._staggerGaugeSprite.glowEffectDuration--;
            if (this._staggerGaugeSprite.glowEffectDuration <= 0) {
                this._staggerGaugeSprite.glowEffect = false;
            }
        }
    };

    // Configuration: Set to true for gradual fade-out, false for immediate hiding
    const STAGGER_GRADUAL_FADE = false;

    // 🐛 FIX: Enhanced method to properly clean up stagger gauge when enemy dies
    Sprite_Enemy.prototype._hideStaggerGauge = function () {
        if (this._staggerGaugeSprite) {
            // Immediate opacity hide for instant feedback
            this._staggerGaugeSprite.opacity = 0;
            this._staggerGaugeSprite.visible = false;
            // Remove from parent to prevent positioning issues during death animations
            if (this._staggerGaugeSprite.parent) {
                this._staggerGaugeSprite.parent.removeChild(this._staggerGaugeSprite);
            }
        }
        if (this._staggerSkullSprite) {
            // Immediate opacity hide for instant feedback
            this._staggerSkullSprite.opacity = 0;
            this._staggerSkullSprite.visible = false;
            // Remove from parent to prevent positioning issues during death animations
            if (this._staggerSkullSprite.parent) {
                this._staggerSkullSprite.parent.removeChild(this._staggerSkullSprite);
            }
        }
    };

    // 🐛 FIX: Alternative method for gradual fade-out of stagger gauge when enemy dies
    Sprite_Enemy.prototype._fadeOutStaggerGauge = function () {
        if (this._staggerGaugeSprite && this._staggerGaugeSprite.opacity > 0) {
            this._staggerGaugeSprite.opacity = Math.max(0, this._staggerGaugeSprite.opacity - 15);
            if (this._staggerGaugeSprite.opacity <= 0) {
                this._staggerGaugeSprite.visible = false;
                if (this._staggerGaugeSprite.parent) {
                    this._staggerGaugeSprite.parent.removeChild(this._staggerGaugeSprite);
                }
            }
        }
        if (this._staggerSkullSprite && this._staggerSkullSprite.opacity > 0) {
            this._staggerSkullSprite.opacity = Math.max(0, this._staggerSkullSprite.opacity - 15);
            if (this._staggerSkullSprite.opacity <= 0) {
                this._staggerSkullSprite.visible = false;
                if (this._staggerSkullSprite.parent) {
                    this._staggerSkullSprite.parent.removeChild(this._staggerSkullSprite);
                }
            }
        }
    };

    // 🐛 FIX: Enhanced method to completely destroy stagger gauge sprites
    Sprite_Enemy.prototype._destroyStaggerGauge = function () {
        if (this._staggerGaugeSprite) {
            if (this._staggerGaugeSprite.parent) {
                this._staggerGaugeSprite.parent.removeChild(this._staggerGaugeSprite);
            }
            if (this._staggerGaugeSprite.bitmap) {
                this._staggerGaugeSprite.bitmap.destroy();
            }
            this._staggerGaugeSprite = null;
        }
        if (this._staggerSkullSprite) {
            if (this._staggerSkullSprite.parent) {
                this._staggerSkullSprite.parent.removeChild(this._staggerSkullSprite);
            }
            if (this._staggerSkullSprite.bitmap) {
                this._staggerSkullSprite.bitmap.destroy();
            }
            this._staggerSkullSprite = null;
        }
        // Clear color cache to free memory
        if (this._colorCache) {
            this._colorCache.clear();
            this._colorCache = null;
        }
    };

    const _Sprite_Enemy_update = Sprite_Enemy.prototype.update;
    Sprite_Enemy.prototype.update = function () {
        _Sprite_Enemy_update.call(this);

        // 🐛 FIX: Enhanced death detection and cleanup
        if (this._enemy && this._enemy.isDead()) {
            // Immediate cleanup on first detection of death
            if (!this._staggerDeathCleanupDone) {
                if (this._enemy._isStaggered) {
                    this._enemy.exitStaggeredState();
                }
                if (STAGGER_GRADUAL_FADE) {
                    this._fadeOutStaggerGauge();
                } else {
                    this._hideStaggerGauge();
                }
                this._staggerDeathCleanupDone = true;
            } else if (STAGGER_GRADUAL_FADE) {
                // Continue fading if using gradual fade
                this._fadeOutStaggerGauge();
            }
            // Don't process any stagger gauge updates for dead enemies
            return;
        }

        // 🐛 FIX: Additional safety check - if enemy HP is 0 but not marked as dead yet
        if (this._enemy && this._enemy.hp <= 0) {
            if (!this._staggerDeathCleanupDone) {
                if (this._enemy._isStaggered) {
                    this._enemy.exitStaggeredState();
                }
                if (STAGGER_GRADUAL_FADE) {
                    this._fadeOutStaggerGauge();
                } else {
                    this._hideStaggerGauge();
                }
                this._staggerDeathCleanupDone = true;
            } else if (STAGGER_GRADUAL_FADE) {
                // Continue fading if using gradual fade
                this._fadeOutStaggerGauge();
            }
            return;
        }

        // Normal stagger gauge processing for alive enemies
        if (this._enemy && this._enemy._staggerMax > 0 && this._enemy.isAlive()) {
            // Reset cleanup flag if enemy is alive (for revive scenarios)
            if (this._staggerDeathCleanupDone) {
                this._staggerDeathCleanupDone = false;
            }

            this.drawStaggerGauge();

            // OPTIMIZED: Handle frame-based charge delay
            if (this._enemy._pendingChargeFrames > 0) {
                this._enemy._pendingChargeFrames--;
                if (this._enemy._pendingChargeFrames === 0) {
                    if (SceneManager._scene && SceneManager._scene._cascadeMeter) {
                        SceneManager._scene._cascadeMeter.addCharge();
                    }
                }
            }
        }
    };

    // Fill the Stagger Gauge When Attacked
    const _Game_Action_apply = Game_Action.prototype.apply;
    Game_Action.prototype.apply = function (target) {
        _Game_Action_apply.call(this, target);

        // Only apply stagger if the attack hit and dealt actual damage
        if (target.isEnemy() && this.isDamage()) {
            const result = target.result();
            const actualDamage = result.hpDamage || 0;

            // Check if attack hit and dealt positive damage (not missed, evaded, or blocked)
            if (result.isHit() && !result.missed && !result.evaded && !result.blocked && actualDamage > 0) {
                target.gainStagger(this.calcStaggerValue(target), this.calcElementRate(target) > 1);
                const sprite = BattleManager.getEnemySprite(target);
                if (sprite && sprite._staggerGaugeSprite) {
                    sprite._staggerGaugeSprite.scale.set(2.0);
                    sprite._staggerGaugeSprite.pulseScaling = true;
                    if (target._isStaggered) {
                        sprite._staggerGaugeSprite.hitWhileStaggered = true;
                        sprite._staggerGaugeSprite.hitWhileStaggeredDuration = 20;
                    } else {
                        sprite._staggerGaugeSprite.glowEffect = true;
                        sprite._staggerGaugeSprite.glowEffectDuration = 30;
                    }
                }
            }
        }
    };

    Game_Action.prototype.calcStaggerValue = function (target) {
        // Use actual damage dealt instead of theoretical damage
        const actualDamage = target.result().hpDamage || 0;
        return Math.min(Math.floor(actualDamage * 0.02), 30);
    };

    // Only call this method when actual damage has been dealt (not on misses or negated damage)
    Game_Enemy.prototype.gainStagger = function (value, exploitWeakness = false) {
        if (this._isStaggered || this.isStateAffected(101) || this.isStateAffected(102)) return;

        const bonus = exploitWeakness ? Math.ceil(value * 0.33) : 0;
        this._staggerGauge = Math.min(this._staggerGauge + value + bonus, this._staggerMax);

        if (this._staggerGauge >= this._staggerMax) {
            this.enterStaggeredState();
            this.addState(133);

            // OPTIMIZED: Use frame-based delay instead of setTimeout for better reliability
            if (SceneManager._scene && SceneManager._scene._cascadeMeter) {
                this._pendingChargeFrames = 3; // 3 frames delay instead of 50ms
            }
        }
    };

    // 🚀 OPTIMIZED: Sprite pool for STAGGERED popups
    const StaggerPopupPool = {
        pool: [],
        maxPoolSize: 5,

        getSprite() {
            let sprite = this.pool.pop();
            if (!sprite) {
                sprite = new Sprite(new Bitmap(200, 80));
                // Set font properties once when creating
                sprite.bitmap.fontFace = $gameSystem.mainFontFace();
                sprite.bitmap.fontSize = 24; // Reduced from 36 to 24 for smaller popup
                sprite.bitmap.outlineColor = 'rgba(255, 0, 0, 0.5)';
                sprite.bitmap.outlineWidth = 4; // Also reduced outline width for better proportion
                sprite.bitmap.textColor = '#FFFFFF';
                sprite.anchor.set(0.5);
            }
            return sprite;
        },

        returnSprite(sprite) {
            if (this.pool.length < this.maxPoolSize) {
                sprite.bitmap.clear();
                sprite.opacity = 255;
                sprite.scale.set(1.0);
                sprite.x = 0;
                sprite.y = -80;
                this.pool.push(sprite);
            }
        },
    };

    // Trigger Stagger State - OPTIMIZED
    Game_Enemy.prototype.enterStaggeredState = function () {
        this._isStaggered = true;
        this._staggerGauge = this._staggerMax;
        this.updateStaggerMultiplier(SceneManager._scene && SceneManager._scene._cascadeMeter);

        // Clean up existing popup
        if (this._staggerTextSprite) {
            const oldSprite = BattleManager.getEnemySprite(this);
            if (oldSprite) {
                oldSprite.removeChild(this._staggerTextSprite);
            }
            StaggerPopupPool.returnSprite(this._staggerTextSprite);
        }

        // Get sprite from pool
        this._staggerTextSprite = StaggerPopupPool.getSprite();
        this._staggerTextSprite.bitmap.drawText(staggerText, 0, 0, 200, 80, 'center');

        const sprite = BattleManager.getEnemySprite(this);
        if (sprite) {
            sprite.addChild(this._staggerTextSprite);
            this._staggerTextSprite.x = 0;
            this._staggerTextSprite.y = -80;
            this._staggerTextSprite.opacity = 255;
            this._staggerTextSprite.scale.set(1.5); // Start large

            // OPTIMIZED: Simple frame-based animation
            this._staggerTextSprite._animationFrames = 240;
            this._staggerTextSprite._enemy = this; // Reference for cleanup
        }
    };

    // Handle Stagger State Duration
    const _Game_Battler_onTurnEnd = Game_Battler.prototype.onTurnEnd;
    Game_Battler.prototype.onTurnEnd = function () {
        _Game_Battler_onTurnEnd.call(this);
        if (this.isEnemy() && this._isStaggered) {
            this._staggerGauge -= this._staggerMax / 2;
            if (this._staggerGauge <= 0) {
                this.exitStaggeredState();
            }
        }
    };

    Game_Enemy.prototype.updateStaggerMultiplier = function (cascadeMeter) {
        if (cascadeMeter) {
            this._staggerDamageMultiplier =
                cascadeMeter.charges === 2 ? 3.0 : cascadeMeter.charges === 3 ? 4.0 : 2.0;
        } else {
            this._staggerDamageMultiplier = 2.0;
        }
    };

    // OPTIMIZED: Exit stagger state with sprite pool cleanup
    Game_Enemy.prototype.exitStaggeredState = function () {
        if (this._staggerTextSprite) {
            const sprite = BattleManager.getEnemySprite(this);
            if (sprite) {
                sprite.removeChild(this._staggerTextSprite);
            }
            // Return sprite to pool instead of destroying it
            StaggerPopupPool.returnSprite(this._staggerTextSprite);
            this._staggerTextSprite = null;
        }
        this._isStaggered = false;
        this._staggerGauge = 0;
        this._staggerDamageMultiplier = 1.0;
        this.removeState(133);
    };

    // 🐛 FIX: Clean up stagger gauge when enemy dies (REMOVED - using sprite-based detection instead)

    // Modify Damage Calculation During Stagger
    const _Game_Action_makeDamageValue = Game_Action.prototype.makeDamageValue;
    Game_Action.prototype.makeDamageValue = function (target, critical) {
        let value = _Game_Action_makeDamageValue.call(this, target, critical);
        if (target.isEnemy() && target._isStaggered) {
            target.updateStaggerMultiplier(
                SceneManager._scene && SceneManager._scene._cascadeMeter
            );
            value *= target._staggerDamageMultiplier;
        }
        return value;
    };

    // 🚀 OPTIMIZED: Cached sprite lookup for better performance
    BattleManager.getEnemySprite = function (enemy) {
        // Initialize sprite cache if it doesn't exist
        if (!this._enemySpriteCache) {
            this._enemySpriteCache = new WeakMap();
        }

        // Check cache first
        if (this._enemySpriteCache.has(enemy)) {
            return this._enemySpriteCache.get(enemy);
        }

        // Find sprite and cache it
        const sprite = SceneManager._scene._spriteset._enemySprites.find(
            sprite => sprite._enemy === enemy
        );

        if (sprite) {
            this._enemySpriteCache.set(enemy, sprite);
        }

        return sprite;
    };

    // Clear sprite cache when battle ends
    const _BattleManager_endBattle = BattleManager.endBattle;
    BattleManager.endBattle = function (result) {
        if (this._enemySpriteCache) {
            this._enemySpriteCache = new WeakMap(); // Clear cache
        }
        _BattleManager_endBattle.call(this, result);
    };

    // 🐛 FIX: Override Sprite_Enemy destroy method to clean up stagger gauge
    const _Sprite_Enemy_destroy = Sprite_Enemy.prototype.destroy;
    Sprite_Enemy.prototype.destroy = function (options) {
        // Clean up stagger gauge before destroying the sprite
        this._destroyStaggerGauge();
        _Sprite_Enemy_destroy.call(this, options);
    };

    // 🐛 FIX: Override Sprite_Enemy setBattler to handle enemy changes
    const _Sprite_Enemy_setBattler = Sprite_Enemy.prototype.setBattler;
    Sprite_Enemy.prototype.setBattler = function (battler) {
        // Clean up old stagger gauge if changing battlers
        if (this._enemy && this._enemy !== battler) {
            this._destroyStaggerGauge();
            this._staggerDeathCleanupDone = false;
        }
        _Sprite_Enemy_setBattler.call(this, battler);
    };

    // Modified CascadeMeter class with 25% size increase
    class CascadeMeter {
        constructor() {
            this.charges = 0;
            this.turnsLeft = 0;
            this._lastCharges = -1; // Track last charge state
            this._needsUpdate = true; // Track if meter needs redraw

            // Performance optimization: Cache colors and geometric patterns
            this._colorCache = new Map();
            this._lastDrawnState = null;
            this._preCalculatedPatterns = new Map();
            this._lastGlowEffectDuration = 0;

            this.createMeter();
        }

        createMeter() {
            this.meterContainer = new Sprite();
            this.meterContainer.x = 10;
            this.meterContainer.y = 809;

            // Create base bitmap
            this.meterBitmap = new Bitmap(250, 125);
            this.meterContainer.bitmap = this.meterBitmap;

            // Cache common colors and values
            this.colors = {
                empty: '#000000',
                green: '#00FF00',
                yellow: '#FFD700',
                red: '#FF0000',
                white: '#FFFFFF',
            };

            this.particleColors = [0x00ff00, 0xffd700, 0xff0000];
            this.positions = Array(3)
                .fill(0)
                .map((_, i) => 25 + i * 75);

            this.updateMeter();
            SceneManager._scene.addChild(this.meterContainer);
        }

        // 🚀 OPTIMIZED: Update meter with frame rate limiting
        updateMeter() {
            // OPTIMIZED: Frame rate limiting for cascade meter updates
            const now = performance.now();
            if (this._lastMeterUpdate && now - this._lastMeterUpdate < 33) {
                // ~30 FPS
                return;
            }
            this._lastMeterUpdate = now;

            const needsRedraw =
                this.charges !== this._lastCharges ||
                this._needsUpdate ||
                this.glowEffectDuration !== this._lastGlowEffectDuration;

            if (!needsRedraw) return;

            // Update tracking variables
            this._lastCharges = this.charges;
            this._lastGlowEffectDuration = this.glowEffectDuration || 0;
            this._needsUpdate = false;

            this.meterBitmap.clear();
            const currentColor =
                this.colors[
                    this.charges === 0
                        ? 'empty'
                        : this.charges === 1
                          ? 'green'
                          : this.charges === 2
                            ? 'yellow'
                            : 'red'
                ];
            // Draw black background for connecting lines first
            for (let i = 0; i < 2; i++) {
                const x = this.positions[i];
                this.meterBitmap.fillRect(x + 17, 21, 42, 9, '#000000');
            }
            // Draw black backgrounds for circles
            for (let i = 0; i < 3; i++) {
                const x = this.positions[i];
                this.meterBitmap.drawCircle(x, 25, 23, '#000000', '#000000');
            }
            // OPTIMIZED: Simplified connecting lines with reduced canvas operations
            for (let i = 0; i < 2; i++) {
                const x = this.positions[i];
                const isActive = i < this.charges - 1;
                const barColor = isActive ? currentColor : this.colors.empty;

                // OPTIMIZED: Use simple gradient instead of complex effects
                const ctx = this.meterBitmap._context;
                ctx.save();

                // Simple gradient fill (reduced from 5 operations to 1)
                const grad = ctx.createLinearGradient(x + 19, 23, x + 19, 28);
                grad.addColorStop(0, this._lightenColor(barColor, 0.18));
                grad.addColorStop(0.5, barColor);
                grad.addColorStop(1, this._darkenColor(barColor, 0.18));
                ctx.fillStyle = grad;
                ctx.fillRect(x + 19, 23, 38, 5);

                // OPTIMIZED: Removed expensive inner glow, shine, and facet patterns
                // These were causing 20+ canvas operations per bar

                ctx.restore();
            }
            // OPTIMIZED: Simplified circles with reduced canvas operations
            for (let i = 0; i < 3; i++) {
                const x = this.positions[i];
                const isActive = i < this.charges;
                const circleColor = isActive ? currentColor : this.colors.empty;
                const ctx = this.meterBitmap._context;
                ctx.save();

                // OPTIMIZED: Simple gradient fill (reduced from 4 operations to 1)
                ctx.beginPath();
                ctx.arc(x, 25, 21, 0, Math.PI * 2);
                ctx.clip();

                const grad = ctx.createLinearGradient(x, 4, x, 46);
                grad.addColorStop(0, this._lightenColor(circleColor, 0.45));
                grad.addColorStop(0.5, circleColor);
                grad.addColorStop(1, this._darkenColor(circleColor, 0.38));
                ctx.fillStyle = grad;
                ctx.fillRect(x - 21, 4, 42, 42);

                // OPTIMIZED: Removed expensive inner glow and glossy shine
                // These were causing 10+ canvas operations per circle
                // OPTIMIZED: Clean octagon pattern for gem-like appearance
                // Professional looking and performance-friendly
                if (isActive) {
                    ctx.save();

                    // Draw octagon using the existing helper function
                    const octPoints = this._getOctagonPattern(x, 25, 12);

                    // Fill octagon with gradient
                    ctx.fillStyle = this._lightenColor(circleColor, 0.4);
                    ctx.beginPath();
                    ctx.moveTo(octPoints[0][0], octPoints[0][1]);
                    for (let j = 1; j < 8; j++) {
                        ctx.lineTo(octPoints[j][0], octPoints[j][1]);
                    }
                    ctx.closePath();
                    ctx.fill();

                    // Stroke octagon outline for definition
                    ctx.strokeStyle = this._lightenColor(circleColor, 0.8);
                    ctx.lineWidth = 1.5;
                    ctx.stroke();

                    ctx.restore();
                }

                // Add glossy shine effect for polished gem appearance
                ctx.save();
                ctx.globalAlpha = 0.18;
                ctx.beginPath();
                ctx.ellipse(x, 15, 12, 6, 0, Math.PI * 0.15, Math.PI * 0.85);
                ctx.lineWidth = 3;
                ctx.strokeStyle = 'white';
                ctx.stroke();
                ctx.restore();
                ctx.restore();
                // Draw white border
                this.meterBitmap.drawCircle(x, 25, 21, 'rgba(0,0,0,0)', this.colors.white);
                // Draw multiplier text
                if (isActive) {
                    this.meterBitmap.fontSize = 20;
                    this.meterBitmap.textColor = this.colors.white;
                    this.meterBitmap.outlineColor = '#000000';
                    this.meterBitmap.outlineWidth = 4;
                    this.meterBitmap.drawText(`${i + 2}x`, x - 13, 0, 50, 25, 'center');
                }
            }
            // Draw title text
            this.meterBitmap.fontFace = $gameSystem.mainFontFace();
            this.meterBitmap.fontSize = 20;
            this.meterBitmap.outlineWidth = 4;
            this.meterBitmap.outlineColor = 'rgba(0, 0, 0, 1)';
            this.meterBitmap.textColor = this.colors.white;
            this.meterBitmap.drawText('STAGGER MULTIPLIER', 38, 16, 125, 38, 'left');
        }

        // Utility: lighten color
        _lightenColor(color, amt) {
            // Check cache first
            const cacheKey = `lighten_${color}_${amt}`;
            if (this._colorCache.has(cacheKey)) {
                return this._colorCache.get(cacheKey);
            }

            let result;
            if (color.startsWith('#')) {
                let num = parseInt(color.slice(1), 16);
                let r = Math.min(255, ((num >> 16) & 0xff) + 255 * amt);
                let g = Math.min(255, ((num >> 8) & 0xff) + 255 * amt);
                let b = Math.min(255, (num & 0xff) + 255 * amt);
                result = `rgb(${r},${g},${b})`;
            } else if (color.startsWith('rgba')) {
                let [r, g, b, a] = color.match(/\d+\.?\d*/g).map(Number);
                r = Math.min(255, r + 255 * amt);
                g = Math.min(255, g + 255 * amt);
                b = Math.min(255, b + 255 * amt);
                result = `rgba(${r},${g},${b},${a})`;
            } else {
                result = color;
            }

            // Cache the result
            this._colorCache.set(cacheKey, result);
            return result;
        }

        // Utility: darken color
        _darkenColor(color, amt) {
            // Check cache first
            const cacheKey = `darken_${color}_${amt}`;
            if (this._colorCache.has(cacheKey)) {
                return this._colorCache.get(cacheKey);
            }

            let result;
            if (color.startsWith('#')) {
                let num = parseInt(color.slice(1), 16);
                let r = Math.max(0, ((num >> 16) & 0xff) - 255 * amt);
                let g = Math.max(0, ((num >> 8) & 0xff) - 255 * amt);
                let b = Math.max(0, (num & 0xff) - 255 * amt);
                result = `rgb(${r},${g},${b})`;
            } else if (color.startsWith('rgba')) {
                let [r, g, b, a] = color.match(/\d+\.?\d*/g).map(Number);
                r = Math.max(0, r - 255 * amt);
                g = Math.max(0, g - 255 * amt);
                b = Math.max(0, b - 255 * amt);
                result = `rgba(${r},${g},${b},${a})`;
            } else {
                result = color;
            }

            // Cache the result
            this._colorCache.set(cacheKey, result);
            return result;
        }

        addCharge() {
            if (this.charges < 3) {
                this.charges++;
                this._needsUpdate = true;

                this.notifyStaggeredEnemiesOfUpdate();
                this.triggerGlowEffect();
                this.updateMeter();

                if (this.charges === 3) {
                    this.turnsLeft = 2;
                }
            }
        }

        triggerGlowEffect() {
            // Get the current bar color based on charges
            let glowColor;
            if (this.charges === 1) {
                glowColor = [0, 255, 0, 192]; // Green
            } else if (this.charges === 2) {
                glowColor = [255, 215, 0, 192]; // Yellow
            } else if (this.charges === 3) {
                glowColor = [255, 0, 0, 192]; // Red
            } else {
                glowColor = [255, 215, 0, 192]; // Default to yellow if somehow at 0
            }

            this.meterContainer.setBlendColor(glowColor);
            this.glowEffectDuration = 30;
            this._currentGlowColor = glowColor; // Store for fade effect
            this.updateMeter();
            this.notifyStaggeredEnemiesOfUpdate();
        }

        updateGlowEffect() {
            // Performance optimization: Only update if glow effect is active and has changed
            if (this.glowEffectDuration > 0) {
                this.glowEffectDuration--;
                const fadeAmount = Math.max(0, 192 * (this.glowEffectDuration / 30));

                // Only update blend color if it has changed significantly
                const currentBlend = [
                    this._currentGlowColor[0],
                    this._currentGlowColor[1],
                    this._currentGlowColor[2],
                    fadeAmount,
                ];

                // Check if blend color has changed significantly
                const lastBlend = this._lastBlendColor || [0, 0, 0, 0];
                const hasChanged =
                    Math.abs(currentBlend[0] - lastBlend[0]) > 5 ||
                    Math.abs(currentBlend[1] - lastBlend[1]) > 5 ||
                    Math.abs(currentBlend[2] - lastBlend[2]) > 5 ||
                    Math.abs(currentBlend[3] - lastBlend[3]) > 5;

                if (hasChanged) {
                    this.meterContainer.setBlendColor(currentBlend);
                    this._lastBlendColor = currentBlend;
                }
            } else if (this._lastBlendColor && this._lastBlendColor[3] > 0) {
                // Clear blend color when effect ends
                this.meterContainer.setBlendColor([0, 0, 0, 0]);
                this._lastBlendColor = [0, 0, 0, 0];
            }
        }

        decrementTurns() {
            // If we have 3 or more charges and turns left, decrement
            if (this.charges >= 3 && this.turnsLeft > 0) {
                this.turnsLeft--;
                if (this.turnsLeft <= 0) {
                    this.resetMeter();
                }
            }
        }

        resetMeter() {
            this.charges = 0;
            this.turnsLeft = 0;
            this.glowEffectDuration = 0;
            this._needsUpdate = true;

            // Clear any visual effects
            if (this.meterContainer) {
                this.meterContainer.setBlendColor([0, 0, 0, 0]);
            }

            this.updateMeter();
            this.notifyStaggeredEnemiesOfUpdate();
        }

        notifyStaggeredEnemiesOfUpdate() {
            if (!$gameParty.inBattle()) return;
            $gameTroop.members().forEach(enemy => {
                if (enemy.isAlive() && enemy._isStaggered) {
                    enemy.updateStaggerMultiplier(this);
                    const sprite = BattleManager.getEnemySprite(enemy);
                    if (sprite) {
                        sprite._staggerMultiplierNeedsUpdate = true;
                    }
                }
            });
        }

        // Performance optimization: Pre-calculate octagon patterns
        _getOctagonPattern(centerX, centerY, radius) {
            const cacheKey = `${centerX}_${centerY}_${radius}`;
            if (this._preCalculatedPatterns.has(cacheKey)) {
                return this._preCalculatedPatterns.get(cacheKey);
            }

            const points = [];
            for (let j = 0; j < 8; j++) {
                const angle = (Math.PI / 4) * j - Math.PI / 8; // Rotate for style
                points.push([
                    centerX + radius * Math.cos(angle),
                    centerY + radius * Math.sin(angle),
                ]);
            }

            this._preCalculatedPatterns.set(cacheKey, points);
            return points;
        }
    }

    const _Scene_Battle_createDisplayObjects = Scene_Battle.prototype.createDisplayObjects;
    Scene_Battle.prototype.createDisplayObjects = function () {
        _Scene_Battle_createDisplayObjects.call(this);
        this._cascadeMeter = new CascadeMeter();
    };

    const _Scene_Battle_update = Scene_Battle.prototype.update;
    Scene_Battle.prototype.update = function () {
        _Scene_Battle_update.call(this);
        if (this._cascadeMeter) {
            // Performance optimization: Only update glow effect if it's active
            if (this._cascadeMeter.glowEffectDuration > 0) {
                this._cascadeMeter.updateGlowEffect();
            }
        }

        // OPTIMIZED: Update all stagger popups efficiently
        this.updateStaggerPopups();
    };

    // OPTIMIZED: Centralized popup animation system
    Scene_Battle.prototype.updateStaggerPopups = function () {
        if (!this._spriteset || !this._spriteset._enemySprites) return;

        // Update all active stagger popups
        for (const enemySprite of this._spriteset._enemySprites) {
            if (enemySprite._enemy && enemySprite._enemy._staggerTextSprite) {
                const popup = enemySprite._enemy._staggerTextSprite;

                if (popup._animationFrames > 0) {
                    popup._animationFrames--;

                    // OPTIMIZED: Simple fade and scale animation
                    const progress = popup._animationFrames / 240;
                    popup.opacity = Math.floor(255 * progress);
                    popup.scale.set(1.5 - 0.5 * (1 - progress)); // Scale from 1.5 to 1.0

                    // Remove when animation complete
                    if (popup._animationFrames <= 0) {
                        enemySprite.removeChild(popup);
                        StaggerPopupPool.returnSprite(popup);
                        popup._enemy._staggerTextSprite = null;
                    }
                }
            }
        }
    };

    const _BattleManager_endTurn = BattleManager.endTurn;
    BattleManager.endTurn = function () {
        _BattleManager_endTurn.call(this);
        if (SceneManager._scene && SceneManager._scene._cascadeMeter) {
            SceneManager._scene._cascadeMeter.decrementTurns();
        }
    };

    const _Game_Battler_revive = Game_Battler.prototype.revive;
    Game_Battler.prototype.revive = function () {
        _Game_Battler_revive.call(this);
        if (this.isEnemy()) {
            this.initStaggerGauge();
        }
    };

    // Add battle end cleanup to ensure cascade meter resets
    const _Scene_Battle_terminate = Scene_Battle.prototype.terminate;
    Scene_Battle.prototype.terminate = function () {
        // Reset cascade meter when battle ends
        if (this._cascadeMeter) {
            this._cascadeMeter.resetMeter();
        }

        _Scene_Battle_terminate.call(this);
    };
})();
