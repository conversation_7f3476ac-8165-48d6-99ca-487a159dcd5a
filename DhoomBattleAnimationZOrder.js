//=============================================================================
// DhoomBattleAnimationZOrder.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_BattleAnimationZOrder = '1.1';

var Dhoom = Dhoom || {};
Dhoom.BattleAnimationZOrder = Dhoom.BattleAnimationZOrder || {};
/*:
 * @target MZ
 * @plugindesc Dhoom BattleAnimationZOrder v1.1 - 17/05/2024
 * <AUTHOR> | DrDhoom#8315
 * @url drd-workshop.blogspot.com
 *
 * @param Default Nametag
 * @desc Animation nametag to set the render order to MZ default. Case sensitive.
 * @type text
 * @default [Z DEFAULT]
 *
 * @param Same as Battler Nametag
 * @desc Animation nametag to set the render order to the same as the battler. Case sensitive.
 * @type text
 * @default [SAME AS BATTLER]
 *
 * @param Behind Battler Nametag
 * @desc Animation nametag to set the render order to behind the battler. Case sensitive.
 * @type text
 * @default [BEHIND BATTLER]
 *
 * @param Default Animation Z Order
 * @desc Define the default behaviour when an animation z order is not specified with a nametag.
 * @type select
 * @option MZ default
 * @option Same as battler
 * @option Behind battler
 * @default Same as battler
 *
 * @param Default Play At Coord Animation Target
 * @desc When the plugin command target is not defined, use this instead.
 * @type combo
 * @option current target
 * @option prev target
 * @option next target
 * @option all targets
 * @option focus
 * @option not focus
 * @option
 * @option special
 * @option special x
 * @option
 * @option alive friends
 * @option alive friends not user
 * @option alive friends not target
 * @option dead friends
 * @option friend index x
 * @option
 * @option alive opponents
 * @option alive opponents not target
 * @option dead opponents
 * @option opponent index x
 * @option
 * @option alive actors
 * @option alive actors not user
 * @option alive actors not target
 * @option dead actors
 * @option actor index x
 * @option actor ID x
 * @option
 * @option alive enemies
 * @option alive enemies not user
 * @option alive enemies not target
 * @option dead enemies
 * @option enemy index x
 * @option enemy ID x
 * @option
 * @option alive battlers
 * @option alive battlers not user
 * @option alive battlers not target
 * @option dead battlers
 * @option
 * @default user
 *
 * @help This plugin changes battle animation render order.
 */

var _0x19a55a = _0x4688;
((function (_0xbedd2c, _0x216a3a) {
    var _0x40bfa8 = _0x4688,
        _0x57424d = _0xbedd2c();
    while (!![]) {
        try {
            var _0x35e244 =
                -parseInt(_0x40bfa8(0x12f)) / 0x1 +
                parseInt(_0x40bfa8(0x165)) / 0x2 +
                -parseInt(_0x40bfa8(0x170)) / 0x3 +
                (parseInt(_0x40bfa8(0x174)) / 0x4) * (parseInt(_0x40bfa8(0x16e)) / 0x5) +
                -parseInt(_0x40bfa8(0x144)) / 0x6 +
                parseInt(_0x40bfa8(0x159)) / 0x7 +
                (-parseInt(_0x40bfa8(0x142)) / 0x8) * (parseInt(_0x40bfa8(0x133)) / 0x9);
            if (_0x35e244 === _0x216a3a) break;
            else _0x57424d['push'](_0x57424d['shift']());
        } catch (_0x47c111) {
            _0x57424d['push'](_0x57424d['shift']());
        }
    }
})(_0x3c35, 0x7a469),
    (Dhoom[_0x19a55a(0x14c)] = $plugins['filter'](function (_0x437a5f) {
        var _0x37d15e = _0x19a55a;
        return _0x437a5f[_0x37d15e(0x171)][_0x37d15e(0x151)](/Dhoom BattleAnimationZOrder/);
    })[0x0]['parameters']));
function _0x4688(_0x44e162, _0x1bba9e) {
    var _0x3c35fe = _0x3c35();
    return (
        (_0x4688 = function (_0x4688c3, _0x2ca725) {
            _0x4688c3 = _0x4688c3 - 0x127;
            var _0x38bbc6 = _0x3c35fe[_0x4688c3];
            return _0x38bbc6;
        }),
        _0x4688(_0x44e162, _0x1bba9e)
    );
}
!Dhoom[_0x19a55a(0x166)] &&
    (Dhoom['jsonParse'] = function (_0x497cd6) {
        var _0x9fbf54 = _0x19a55a;
        if (typeof _0x497cd6 === _0x9fbf54(0x12e))
            try {
                return JSON['parse'](
                    _0x497cd6,
                    function (_0x4117b5, _0xf64882) {
                        var _0x35b7e9 = _0x9fbf54;
                        if (typeof _0xf64882 === _0x35b7e9(0x12e))
                            try {
                                return this[_0x35b7e9(0x166)](_0xf64882);
                            } catch (_0x108db0) {
                                return _0xf64882;
                            }
                        else return _0xf64882;
                    }[_0x9fbf54(0x15e)](this)
                );
            } catch (_0x2c141b) {
                return _0x497cd6;
            }
        else return _0x497cd6;
    });
function _0x3c35() {
    var _0x1c24df = [
        'BattleAnimationZOrder',
        'value',
        'battler',
        'isAnimationShownOnBattlePortrait',
        'battleAnimation',
        'pointX',
        '3728795WYEhkW',
        'Spriteset_Battle_createPointAnimation',
        '2320503wtHVzW',
        'description',
        'indexOf',
        '_battlerContainer',
        '4Ovmvxr',
        'pointY',
        'contains',
        '_scene',
        'addAnimationSpriteToContainer',
        'VisuMZ_1_BattleCore',
        'behindBattlerNametag',
        'slice',
        'Behind\x20battler',
        'Sprite_Animation_update',
        'string',
        '229956BGuqRG',
        '_pointAnimationQueue',
        'Spriteset_Battle_addAnimationSpriteToContainer',
        'Spriteset_Battle_removeAnimation',
        '9KUDDfJ',
        '_targets',
        'round',
        'DataManager_isDatabaseLoaded',
        'getLastPluginCommandInterpreter',
        'removeAnimation',
        'sameasBattlerNametag',
        'DhoomInitBattleAnimationZOrder',
        'requestPointAnimation',
        'name',
        'renderType',
        'defaultAnimationZOrder',
        'enableSwitch',
        'isDatabaseLoaded',
        'loadParameters',
        '4137016rLdxUw',
        'updatePointAnimations',
        '937368xVbykW',
        'MZ\x20default',
        'Game_Temp_requestPointAnimation',
        '_baseY',
        'toUpperCase',
        '_pointAnimationTarget',
        'loadParam',
        'prototype',
        'Parameters',
        'Mirror',
        '_animation',
        'isBattleAnimationZOrderInitialized',
        'Target',
        'match',
        'ActSeq_Animation_PlayAtCoordinate',
        'Sprite_AnimationMV_update',
        'Mute',
        'toLowerCase',
        '_spriteset',
        'length',
        '_lastPointAnimationTarget',
        '3127852BVBUPJ',
        'updateZOrderBaseY',
        'ConvertActionSequenceTarget',
        'WaitComplete',
        'replace',
        'bind',
        'update',
        'isEnabled',
        'ConvertParams',
        'defaultNametag',
        'defaultPlayAtCoordAnimationTarget',
        'call',
        '1970116VThepK',
        'jsonParse',
        'addChild',
    ];
    _0x3c35 = function () {
        return _0x1c24df;
    };
    return _0x3c35();
}
!Dhoom[_0x19a55a(0x14a)] &&
    (Dhoom[_0x19a55a(0x14a)] = function (_0x698c1f) {
        var _0x4ebdfa = _0x19a55a;
        return Dhoom[_0x4ebdfa(0x166)](Dhoom[_0x4ebdfa(0x14c)][_0x698c1f]);
    });
((Dhoom[_0x19a55a(0x168)][_0x19a55a(0x141)] = function () {
    var _0x52822f = _0x19a55a;
    for (let _0x191137 in Dhoom[_0x52822f(0x14c)]) {
        let _0x3c5047 = _0x191137[_0x52822f(0x15d)](/\s+/g, '');
        ((_0x3c5047 =
            (_0x3c5047[0x1] && _0x3c5047[0x1][_0x52822f(0x148)]() === _0x3c5047[0x1]
                ? _0x3c5047[0x0]
                : _0x3c5047[0x0][_0x52822f(0x155)]()) + _0x3c5047[_0x52822f(0x12b)](0x1)),
            (Dhoom[_0x52822f(0x168)][_0x3c5047] = Dhoom[_0x52822f(0x14a)](_0x191137)));
    }
}),
    Dhoom[_0x19a55a(0x168)][_0x19a55a(0x141)](),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x160)] = function () {
        var _0x273b70 = _0x19a55a;
        return (
            !this[_0x273b70(0x13f)] ||
            ($gameSwitches && $gameSwitches[_0x273b70(0x169)](this[_0x273b70(0x13f)]))
        );
    }),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x136)] = DataManager[_0x19a55a(0x140)]),
    (DataManager['isDatabaseLoaded'] = function () {
        var _0x534f00 = _0x19a55a;
        if (!Dhoom[_0x534f00(0x168)][_0x534f00(0x136)]['call'](this)) return ![];
        return (
            !Dhoom[_0x534f00(0x168)][_0x534f00(0x14f)] &&
                (this[_0x534f00(0x13a)](), (Dhoom[_0x534f00(0x168)][_0x534f00(0x14f)] = !![])),
            !![]
        );
    }),
    (DataManager[_0x19a55a(0x13a)] = function () {
        var _0xd08640 = _0x19a55a,
            _0x12da71 = $dataAnimations;
        for (var _0x116b04 = 0x1; _0x116b04 < _0x12da71[_0xd08640(0x157)]; _0x116b04++) {
            var _0x10846e = _0x12da71[_0x116b04];
            if (_0x10846e) {
                var _0x250a65 = _0x10846e[_0xd08640(0x13c)];
                ((_0x10846e[_0xd08640(0x13d)] = [
                    _0xd08640(0x145),
                    'Same\x20as\x20battler',
                    _0xd08640(0x12c),
                ][_0xd08640(0x172)](Dhoom['BattleAnimationZOrder'][_0xd08640(0x13e)])),
                    _0x250a65[_0xd08640(0x176)](Dhoom[_0xd08640(0x168)][_0xd08640(0x162)]) &&
                        (_0x10846e[_0xd08640(0x13d)] = 0x0),
                    _0x250a65[_0xd08640(0x176)](Dhoom['BattleAnimationZOrder'][_0xd08640(0x139)]) &&
                        (_0x10846e[_0xd08640(0x13d)] = 0x1),
                    _0x250a65[_0xd08640(0x176)](Dhoom['BattleAnimationZOrder'][_0xd08640(0x12a)]) &&
                        (_0x10846e[_0xd08640(0x13d)] = 0x2));
            }
        }
    }),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x146)] = Game_Temp['prototype']['requestPointAnimation']),
    (Game_Temp[_0x19a55a(0x14b)]['requestPointAnimation'] = function (
        _0x355732,
        _0x17953a,
        _0x2c6dbd,
        _0x460b2f,
        _0x39bf4f,
        _0x2f8877
    ) {
        var _0x5a69bc = _0x19a55a,
            _0x340143 = this[_0x5a69bc(0x130)]['length'];
        (Dhoom[_0x5a69bc(0x168)][_0x5a69bc(0x146)][_0x5a69bc(0x164)](
            this,
            _0x355732,
            _0x17953a,
            _0x2c6dbd,
            _0x460b2f,
            _0x39bf4f
        ),
            _0x340143 < this[_0x5a69bc(0x130)][_0x5a69bc(0x157)] &&
                (this[_0x5a69bc(0x130)][_0x340143]['target'] = _0x2f8877));
    }),
    (Dhoom['BattleAnimationZOrder'][_0x19a55a(0x12d)] =
        Sprite_Animation[_0x19a55a(0x14b)][_0x19a55a(0x15f)]),
    (Sprite_Animation[_0x19a55a(0x14b)]['update'] = function () {
        var _0x50a226 = _0x19a55a;
        (Dhoom['BattleAnimationZOrder'][_0x50a226(0x12d)]['call'](this), this[_0x50a226(0x15a)]());
    }),
    (Sprite_Animation[_0x19a55a(0x14b)][_0x19a55a(0x15a)] = function () {
        var _0x1a4c12 = _0x19a55a;
        if (this[_0x1a4c12(0x14e)] && this[_0x1a4c12(0x14e)][_0x1a4c12(0x13d)]) {
            var _0x1c4d36 = this[_0x1a4c12(0x134)][0x0];
            if (this[_0x1a4c12(0x149)]) _0x1c4d36 = this[_0x1a4c12(0x149)][_0x1a4c12(0x16a)]();
            _0x1c4d36 &&
                (this[_0x1a4c12(0x14e)]['renderType'] === 0x1
                    ? (this[_0x1a4c12(0x147)] = _0x1c4d36[_0x1a4c12(0x147)] + 0.000001)
                    : (this[_0x1a4c12(0x147)] = _0x1c4d36[_0x1a4c12(0x147)] - 0.000001));
        }
    }),
    (Dhoom[_0x19a55a(0x168)]['Sprite_AnimationMV_update'] =
        Sprite_AnimationMV[_0x19a55a(0x14b)][_0x19a55a(0x15f)]),
    (Sprite_AnimationMV[_0x19a55a(0x14b)][_0x19a55a(0x15f)] = function () {
        var _0x46a41c = _0x19a55a;
        (Dhoom['BattleAnimationZOrder'][_0x46a41c(0x153)]['call'](this),
            this['updateZOrderBaseY']());
    }),
    (Sprite_AnimationMV['prototype']['updateZOrderBaseY'] = function () {
        var _0x3d90de = _0x19a55a;
        if (this[_0x3d90de(0x14e)] && this[_0x3d90de(0x14e)][_0x3d90de(0x13d)]) {
            var _0x1e27b1 = this[_0x3d90de(0x134)][0x0];
            if (this[_0x3d90de(0x149)])
                _0x1e27b1 = this['_pointAnimationTarget'][_0x3d90de(0x16a)]();
            _0x1e27b1 &&
                (this[_0x3d90de(0x14e)]['renderType'] === 0x1
                    ? (this[_0x3d90de(0x147)] = _0x1e27b1[_0x3d90de(0x147)] + 0.000001)
                    : (this[_0x3d90de(0x147)] = _0x1e27b1[_0x3d90de(0x147)] - 0.000001));
        }
    }),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x16f)] =
        Spriteset_Battle['prototype']['createPointAnimation']),
    (Spriteset_Battle[_0x19a55a(0x14b)]['createPointAnimation'] = function (_0x45b62c) {
        var _0x198a62 = _0x19a55a;
        (($gameTemp[_0x198a62(0x158)] = _0x45b62c['target']),
            Dhoom['BattleAnimationZOrder']['Spriteset_Battle_createPointAnimation'][
                _0x198a62(0x164)
            ](this, _0x45b62c),
            ($gameTemp[_0x198a62(0x158)] = undefined));
    }),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x131)] =
        Spriteset_Battle[_0x19a55a(0x14b)][_0x19a55a(0x128)]),
    (Spriteset_Battle[_0x19a55a(0x14b)][_0x19a55a(0x128)] = function (_0x4314c2) {
        var _0x43c2ac = _0x19a55a;
        $gameTemp['_lastPointAnimationTarget'] &&
            (_0x4314c2[_0x43c2ac(0x149)] = VisuMZ[_0x43c2ac(0x15b)](
                $gameTemp['_lastPointAnimationTarget']
            )[0x0]);
        if (
            !this[_0x43c2ac(0x16b)](_0x4314c2) &&
            _0x4314c2 &&
            _0x4314c2[_0x43c2ac(0x14e)] &&
            _0x4314c2[_0x43c2ac(0x14e)][_0x43c2ac(0x13d)] !== 0x0
        ) {
            (this[_0x43c2ac(0x173)][_0x43c2ac(0x167)](_0x4314c2),
                this['_animationSprites']['push'](_0x4314c2));
            return;
        }
        Dhoom[_0x43c2ac(0x168)][_0x43c2ac(0x131)]['call'](this, _0x4314c2);
    }),
    (Dhoom[_0x19a55a(0x168)][_0x19a55a(0x132)] =
        Spriteset_Battle[_0x19a55a(0x14b)]['removeAnimation']),
    (Spriteset_Battle['prototype'][_0x19a55a(0x138)] = function (_0xfc6f55) {
        var _0x5218c9 = _0x19a55a;
        (_0xfc6f55[_0x5218c9(0x147)] !== undefined &&
            this[_0x5218c9(0x173)]['removeChild'](_0xfc6f55),
            Dhoom[_0x5218c9(0x168)][_0x5218c9(0x132)]['call'](this, _0xfc6f55));
    }),
    PluginManager['registerCommand'](_0x19a55a(0x129), _0x19a55a(0x152), _0x5e39e5 => {
        var _0x3d9a17 = _0x19a55a;
        if (!SceneManager[_0x3d9a17(0x127)]) return;
        if (!SceneManager[_0x3d9a17(0x127)][_0x3d9a17(0x156)]) return;
        if (!Imported['VisuMZ_0_CoreEngine']) return;
        VisuMZ[_0x3d9a17(0x161)](_0x5e39e5, _0x5e39e5);
        const _0x1757ee = $gameTemp[_0x3d9a17(0x137)](),
            _0x626fe9 = Math[_0x3d9a17(0x135)](_0x5e39e5[_0x3d9a17(0x16d)]),
            _0xa7dec0 = Math[_0x3d9a17(0x135)](_0x5e39e5[_0x3d9a17(0x175)]);
        ($gameTemp[_0x3d9a17(0x13b)](
            _0x626fe9,
            _0xa7dec0,
            _0x5e39e5['AnimationID'],
            _0x5e39e5[_0x3d9a17(0x14d)],
            _0x5e39e5[_0x3d9a17(0x154)],
            _0x5e39e5[_0x3d9a17(0x150)] !== undefined
                ? _0x5e39e5[_0x3d9a17(0x150)]
                : Dhoom[_0x3d9a17(0x168)][_0x3d9a17(0x163)]
        ),
            _0x5e39e5[_0x3d9a17(0x15c)] &&
                _0x1757ee &&
                (SceneManager[_0x3d9a17(0x127)]['_spriteset'][_0x3d9a17(0x143)](),
                _0x1757ee['setWaitMode'](_0x3d9a17(0x16c))));
    }));
