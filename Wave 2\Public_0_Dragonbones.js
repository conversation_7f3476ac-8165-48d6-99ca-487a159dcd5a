//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 0] [Version 5.7.002b] [Dragonbones Library]
 * <AUTHOR>
 * @url http://dragonbones.com/
 *
 * Dragonbones is licensed under the MIT License.
 * http://www.opensource.org/licenses/mit-license
 *
 */

var Imported = Imported || {};
Imported.Dragonbones = true;

var __extends =
    (this && this.__extends) ||
    (function () {
        var r = function (t, e) {
            r =
                Object.setPrototypeOf ||
                ({ __proto__: [] } instanceof Array &&
                    function (t, e) {
                        t.__proto__ = e;
                    }) ||
                function (t, e) {
                    for (var a in e) if (e.hasOwnProperty(a)) t[a] = e[a];
                };
            return r(t, e);
        };
        return function (t, e) {
            r(t, e);
            function a() {
                this.constructor = t;
            }
            t.prototype = e === null ? Object.create(e) : ((a.prototype = e.prototype), new a());
        };
    })();
var dragonBones;
(function (o) {
    var t = (function () {
        function e(t) {
            this._clock = new o.WorldClock();
            this._events = [];
            this._objects = [];
            this._eventManager = null;
            this._eventManager = t;
        }
        e.prototype.advanceTime = function (t) {
            if (this._objects.length > 0) {
                for (var e = 0, a = this._objects; e < a.length; e++) {
                    var r = a[e];
                    r.returnToPool();
                }
                this._objects.length = 0;
            }
            this._clock.advanceTime(t);
            if (this._events.length > 0) {
                for (var i = 0; i < this._events.length; ++i) {
                    var n = this._events[i];
                    var s = n.armature;
                    if (s._armatureData !== null) {
                        s.eventDispatcher.dispatchDBEvent(n.type, n);
                        if (n.type === o.EventObject.SOUND_EVENT) {
                            this._eventManager.dispatchDBEvent(n.type, n);
                        }
                    }
                    this.bufferObject(n);
                }
                this._events.length = 0;
            }
        };
        e.prototype.bufferEvent = function (t) {
            if (this._events.indexOf(t) < 0) {
                this._events.push(t);
            }
        };
        e.prototype.bufferObject = function (t) {
            if (this._objects.indexOf(t) < 0) {
                this._objects.push(t);
            }
        };
        Object.defineProperty(e.prototype, 'clock', {
            get: function () {
                return this._clock;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(e.prototype, 'eventManager', {
            get: function () {
                return this._eventManager;
            },
            enumerable: true,
            configurable: true,
        });
        e.VERSION = '5.7.000';
        e.yDown = true;
        e.debug = false;
        e.debugDraw = false;
        return e;
    })();
    o.DragonBones = t;
})(dragonBones || (dragonBones = {}));
if (!console.warn) {
    console.warn = function () {};
}
if (!console.assert) {
    console.assert = function () {};
}
if (!Date.now) {
    Date.now = function t() {
        return new Date().getTime();
    };
}
var __extends = function (t, e) {
    function a() {
        this.constructor = t;
    }
    for (var r in e) {
        if (e.hasOwnProperty(r)) {
            t[r] = e[r];
        }
    }
    ((a.prototype = e.prototype), (t.prototype = new a()));
};
if (typeof global === 'undefined' && typeof window !== 'undefined') {
    var global = window;
}
if (typeof exports === 'object' && typeof module === 'object') {
    module.exports = dragonBones;
} else if (typeof define === 'function' && define['amd']) {
    define(['dragonBones'], function () {
        return dragonBones;
    });
} else if (typeof exports === 'object') {
    exports = dragonBones;
} else if (typeof global !== 'undefined') {
    global.dragonBones = dragonBones;
}
var dragonBones;
(function (t) {
    var e = (function () {
        function n() {
            this.hashCode = n._hashCode++;
            this._isInPool = false;
        }
        n._returnObject = function (t) {
            var e = String(t.constructor);
            var a = e in n._maxCountMap ? n._maxCountMap[e] : n._defaultMaxCount;
            var r = (n._poolsMap[e] = n._poolsMap[e] || []);
            if (r.length < a) {
                if (!t._isInPool) {
                    t._isInPool = true;
                    r.push(t);
                } else {
                    console.warn('The object is already in the pool.');
                }
            } else {
            }
        };
        n.toString = function () {
            throw new Error();
        };
        n.setMaxCount = function (t, e) {
            if (e < 0 || e !== e) {
                e = 0;
            }
            if (t !== null) {
                var a = String(t);
                var r = a in n._poolsMap ? n._poolsMap[a] : null;
                if (r !== null && r.length > e) {
                    r.length = e;
                }
                n._maxCountMap[a] = e;
            } else {
                n._defaultMaxCount = e;
                for (var a in n._poolsMap) {
                    var r = n._poolsMap[a];
                    if (r.length > e) {
                        r.length = e;
                    }
                    if (a in n._maxCountMap) {
                        n._maxCountMap[a] = e;
                    }
                }
            }
        };
        n.clearPool = function (t) {
            if (t === void 0) {
                t = null;
            }
            if (t !== null) {
                var e = String(t);
                var a = e in n._poolsMap ? n._poolsMap[e] : null;
                if (a !== null && a.length > 0) {
                    a.length = 0;
                }
            } else {
                for (var r in n._poolsMap) {
                    var a = n._poolsMap[r];
                    a.length = 0;
                }
            }
        };
        n.borrowObject = function (t) {
            var e = String(t);
            var a = e in n._poolsMap ? n._poolsMap[e] : null;
            if (a !== null && a.length > 0) {
                var r = a.pop();
                r._isInPool = false;
                return r;
            }
            var i = new t();
            i._onClear();
            return i;
        };
        n.prototype.returnToPool = function () {
            this._onClear();
            n._returnObject(this);
        };
        n._hashCode = 0;
        n._defaultMaxCount = 3e3;
        n._maxCountMap = {};
        n._poolsMap = {};
        return n;
    })();
    t.BaseObject = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t(t, e, a, r, i, n) {
            if (t === void 0) {
                t = 1;
            }
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = 0;
            }
            if (r === void 0) {
                r = 1;
            }
            if (i === void 0) {
                i = 0;
            }
            if (n === void 0) {
                n = 0;
            }
            this.a = t;
            this.b = e;
            this.c = a;
            this.d = r;
            this.tx = i;
            this.ty = n;
        }
        t.prototype.toString = function () {
            return (
                '[object dragonBones.Matrix] a:' +
                this.a +
                ' b:' +
                this.b +
                ' c:' +
                this.c +
                ' d:' +
                this.d +
                ' tx:' +
                this.tx +
                ' ty:' +
                this.ty
            );
        };
        t.prototype.copyFrom = function (t) {
            this.a = t.a;
            this.b = t.b;
            this.c = t.c;
            this.d = t.d;
            this.tx = t.tx;
            this.ty = t.ty;
            return this;
        };
        t.prototype.copyFromArray = function (t, e) {
            if (e === void 0) {
                e = 0;
            }
            this.a = t[e];
            this.b = t[e + 1];
            this.c = t[e + 2];
            this.d = t[e + 3];
            this.tx = t[e + 4];
            this.ty = t[e + 5];
            return this;
        };
        t.prototype.identity = function () {
            this.a = this.d = 1;
            this.b = this.c = 0;
            this.tx = this.ty = 0;
            return this;
        };
        t.prototype.concat = function (t) {
            var e = this.a * t.a;
            var a = 0;
            var r = 0;
            var i = this.d * t.d;
            var n = this.tx * t.a + t.tx;
            var s = this.ty * t.d + t.ty;
            if (this.b !== 0 || this.c !== 0) {
                e += this.b * t.c;
                a += this.b * t.d;
                r += this.c * t.a;
                i += this.c * t.b;
            }
            if (t.b !== 0 || t.c !== 0) {
                a += this.a * t.b;
                r += this.d * t.c;
                n += this.ty * t.c;
                s += this.tx * t.b;
            }
            this.a = e;
            this.b = a;
            this.c = r;
            this.d = i;
            this.tx = n;
            this.ty = s;
            return this;
        };
        t.prototype.invert = function () {
            var t = this.a;
            var e = this.b;
            var a = this.c;
            var r = this.d;
            var i = this.tx;
            var n = this.ty;
            if (e === 0 && a === 0) {
                this.b = this.c = 0;
                if (t === 0 || r === 0) {
                    this.a = this.b = this.tx = this.ty = 0;
                } else {
                    t = this.a = 1 / t;
                    r = this.d = 1 / r;
                    this.tx = -t * i;
                    this.ty = -r * n;
                }
                return this;
            }
            var s = t * r - e * a;
            if (s === 0) {
                this.a = this.d = 1;
                this.b = this.c = 0;
                this.tx = this.ty = 0;
                return this;
            }
            s = 1 / s;
            var o = (this.a = r * s);
            e = this.b = -e * s;
            a = this.c = -a * s;
            r = this.d = t * s;
            this.tx = -(o * i + a * n);
            this.ty = -(e * i + r * n);
            return this;
        };
        t.prototype.transformPoint = function (t, e, a, r) {
            if (r === void 0) {
                r = false;
            }
            a.x = this.a * t + this.c * e;
            a.y = this.b * t + this.d * e;
            if (!r) {
                a.x += this.tx;
                a.y += this.ty;
            }
        };
        t.prototype.transformRectangle = function (t, e) {
            if (e === void 0) {
                e = false;
            }
            var a = this.a;
            var r = this.b;
            var i = this.c;
            var n = this.d;
            var s = e ? 0 : this.tx;
            var o = e ? 0 : this.ty;
            var l = t.x;
            var h = t.y;
            var u = l + t.width;
            var f = h + t.height;
            var _ = a * l + i * h + s;
            var m = r * l + n * h + o;
            var p = a * u + i * h + s;
            var c = r * u + n * h + o;
            var d = a * u + i * f + s;
            var y = r * u + n * f + o;
            var v = a * l + i * f + s;
            var g = r * l + n * f + o;
            var D = 0;
            if (_ > p) {
                D = _;
                _ = p;
                p = D;
            }
            if (d > v) {
                D = d;
                d = v;
                v = D;
            }
            t.x = Math.floor(_ < d ? _ : d);
            t.width = Math.ceil((p > v ? p : v) - t.x);
            if (m > c) {
                D = m;
                m = c;
                c = D;
            }
            if (y > g) {
                D = y;
                y = g;
                g = D;
            }
            t.y = Math.floor(m < y ? m : y);
            t.height = Math.ceil((c > g ? c : g) - t.y);
        };
        return t;
    })();
    t.Matrix = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function n(t, e, a, r, i, n) {
            if (t === void 0) {
                t = 0;
            }
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = 0;
            }
            if (r === void 0) {
                r = 0;
            }
            if (i === void 0) {
                i = 1;
            }
            if (n === void 0) {
                n = 1;
            }
            this.x = t;
            this.y = e;
            this.skew = a;
            this.rotation = r;
            this.scaleX = i;
            this.scaleY = n;
        }
        n.normalizeRadian = function (t) {
            t = (t + Math.PI) % (Math.PI * 2);
            t += t > 0 ? -Math.PI : Math.PI;
            return t;
        };
        n.prototype.toString = function () {
            return (
                '[object dragonBones.Transform] x:' +
                this.x +
                ' y:' +
                this.y +
                ' skewX:' +
                (this.skew * 180) / Math.PI +
                ' skewY:' +
                (this.rotation * 180) / Math.PI +
                ' scaleX:' +
                this.scaleX +
                ' scaleY:' +
                this.scaleY
            );
        };
        n.prototype.copyFrom = function (t) {
            this.x = t.x;
            this.y = t.y;
            this.skew = t.skew;
            this.rotation = t.rotation;
            this.scaleX = t.scaleX;
            this.scaleY = t.scaleY;
            return this;
        };
        n.prototype.identity = function () {
            this.x = this.y = 0;
            this.skew = this.rotation = 0;
            this.scaleX = this.scaleY = 1;
            return this;
        };
        n.prototype.add = function (t) {
            this.x += t.x;
            this.y += t.y;
            this.skew += t.skew;
            this.rotation += t.rotation;
            this.scaleX *= t.scaleX;
            this.scaleY *= t.scaleY;
            return this;
        };
        n.prototype.minus = function (t) {
            this.x -= t.x;
            this.y -= t.y;
            this.skew -= t.skew;
            this.rotation -= t.rotation;
            this.scaleX /= t.scaleX;
            this.scaleY /= t.scaleY;
            return this;
        };
        n.prototype.fromMatrix = function (t) {
            var e = this.scaleX,
                a = this.scaleY;
            var r = n.PI_Q;
            this.x = t.tx;
            this.y = t.ty;
            this.rotation = Math.atan(t.b / t.a);
            var i = Math.atan(-t.c / t.d);
            this.scaleX =
                this.rotation > -r && this.rotation < r
                    ? t.a / Math.cos(this.rotation)
                    : t.b / Math.sin(this.rotation);
            this.scaleY = i > -r && i < r ? t.d / Math.cos(i) : -t.c / Math.sin(i);
            if (e >= 0 && this.scaleX < 0) {
                this.scaleX = -this.scaleX;
                this.rotation = this.rotation - Math.PI;
            }
            if (a >= 0 && this.scaleY < 0) {
                this.scaleY = -this.scaleY;
                i = i - Math.PI;
            }
            this.skew = i - this.rotation;
            return this;
        };
        n.prototype.toMatrix = function (t) {
            if (this.rotation === 0) {
                t.a = 1;
                t.b = 0;
            } else {
                t.a = Math.cos(this.rotation);
                t.b = Math.sin(this.rotation);
            }
            if (this.skew === 0) {
                t.c = -t.b;
                t.d = t.a;
            } else {
                t.c = -Math.sin(this.skew + this.rotation);
                t.d = Math.cos(this.skew + this.rotation);
            }
            if (this.scaleX !== 1) {
                t.a *= this.scaleX;
                t.b *= this.scaleX;
            }
            if (this.scaleY !== 1) {
                t.c *= this.scaleY;
                t.d *= this.scaleY;
            }
            t.tx = this.x;
            t.ty = this.y;
            return this;
        };
        n.PI = Math.PI;
        n.PI_D = Math.PI * 2;
        n.PI_H = Math.PI / 2;
        n.PI_Q = Math.PI / 4;
        n.RAD_DEG = 180 / Math.PI;
        n.DEG_RAD = Math.PI / 180;
        return n;
    })();
    t.Transform = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t(t, e, a, r, i, n, s, o) {
            if (t === void 0) {
                t = 1;
            }
            if (e === void 0) {
                e = 1;
            }
            if (a === void 0) {
                a = 1;
            }
            if (r === void 0) {
                r = 1;
            }
            if (i === void 0) {
                i = 0;
            }
            if (n === void 0) {
                n = 0;
            }
            if (s === void 0) {
                s = 0;
            }
            if (o === void 0) {
                o = 0;
            }
            this.alphaMultiplier = t;
            this.redMultiplier = e;
            this.greenMultiplier = a;
            this.blueMultiplier = r;
            this.alphaOffset = i;
            this.redOffset = n;
            this.greenOffset = s;
            this.blueOffset = o;
        }
        t.prototype.copyFrom = function (t) {
            this.alphaMultiplier = t.alphaMultiplier;
            this.redMultiplier = t.redMultiplier;
            this.greenMultiplier = t.greenMultiplier;
            this.blueMultiplier = t.blueMultiplier;
            this.alphaOffset = t.alphaOffset;
            this.redOffset = t.redOffset;
            this.greenOffset = t.greenOffset;
            this.blueOffset = t.blueOffset;
        };
        t.prototype.identity = function () {
            this.alphaMultiplier =
                this.redMultiplier =
                this.greenMultiplier =
                this.blueMultiplier =
                    1;
            this.alphaOffset = this.redOffset = this.greenOffset = this.blueOffset = 0;
        };
        return t;
    })();
    t.ColorTransform = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t(t, e) {
            if (t === void 0) {
                t = 0;
            }
            if (e === void 0) {
                e = 0;
            }
            this.x = t;
            this.y = e;
        }
        t.prototype.copyFrom = function (t) {
            this.x = t.x;
            this.y = t.y;
        };
        t.prototype.clear = function () {
            this.x = this.y = 0;
        };
        return t;
    })();
    t.Point = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t(t, e, a, r) {
            if (t === void 0) {
                t = 0;
            }
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = 0;
            }
            if (r === void 0) {
                r = 0;
            }
            this.x = t;
            this.y = e;
            this.width = a;
            this.height = r;
        }
        t.prototype.copyFrom = function (t) {
            this.x = t.x;
            this.y = t.y;
            this.width = t.width;
            this.height = t.height;
        };
        t.prototype.clear = function () {
            this.x = this.y = 0;
            this.width = this.height = 0;
        };
        return t;
    })();
    t.Rectangle = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.ints = [];
            t.floats = [];
            t.strings = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.UserData]';
        };
        t.prototype._onClear = function () {
            this.ints.length = 0;
            this.floats.length = 0;
            this.strings.length = 0;
        };
        t.prototype.addInt = function (t) {
            this.ints.push(t);
        };
        t.prototype.addFloat = function (t) {
            this.floats.push(t);
        };
        t.prototype.addString = function (t) {
            this.strings.push(t);
        };
        t.prototype.getInt = function (t) {
            if (t === void 0) {
                t = 0;
            }
            return t >= 0 && t < this.ints.length ? this.ints[t] : 0;
        };
        t.prototype.getFloat = function (t) {
            if (t === void 0) {
                t = 0;
            }
            return t >= 0 && t < this.floats.length ? this.floats[t] : 0;
        };
        t.prototype.getString = function (t) {
            if (t === void 0) {
                t = 0;
            }
            return t >= 0 && t < this.strings.length ? this.strings[t] : '';
        };
        return t;
    })(t.BaseObject);
    t.UserData = e;
    var a = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.data = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.ActionData]';
        };
        t.prototype._onClear = function () {
            if (this.data !== null) {
                this.data.returnToPool();
            }
            this.type = 0;
            this.name = '';
            this.bone = null;
            this.slot = null;
            this.data = null;
        };
        return t;
    })(t.BaseObject);
    t.ActionData = a;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.frameIndices = [];
            t.cachedFrames = [];
            t.armatureNames = [];
            t.armatures = {};
            t.userData = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.DragonBonesData]';
        };
        t.prototype._onClear = function () {
            for (var t in this.armatures) {
                this.armatures[t].returnToPool();
                delete this.armatures[t];
            }
            if (this.userData !== null) {
                this.userData.returnToPool();
            }
            this.autoSearch = false;
            this.frameRate = 0;
            this.version = '';
            this.name = '';
            this.stage = null;
            this.frameIndices.length = 0;
            this.cachedFrames.length = 0;
            this.armatureNames.length = 0;
            this.binary = null;
            this.intArray = null;
            this.floatArray = null;
            this.frameIntArray = null;
            this.frameFloatArray = null;
            this.frameArray = null;
            this.timelineArray = null;
            this.colorArray = null;
            this.userData = null;
        };
        t.prototype.addArmature = function (t) {
            if (t.name in this.armatures) {
                console.warn('Same armature: ' + t.name);
                return;
            }
            t.parent = this;
            this.armatures[t.name] = t;
            this.armatureNames.push(t.name);
        };
        t.prototype.getArmature = function (t) {
            return t in this.armatures ? this.armatures[t] : null;
        };
        return t;
    })(t.BaseObject);
    t.DragonBonesData = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (a) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.aabb = new a.Rectangle();
            t.animationNames = [];
            t.sortedBones = [];
            t.sortedSlots = [];
            t.defaultActions = [];
            t.actions = [];
            t.bones = {};
            t.slots = {};
            t.constraints = {};
            t.skins = {};
            t.animations = {};
            t.canvas = null;
            t.userData = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.ArmatureData]';
        };
        t.prototype._onClear = function () {
            for (var t = 0, e = this.defaultActions; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            for (var r = 0, i = this.actions; r < i.length; r++) {
                var a = i[r];
                a.returnToPool();
            }
            for (var n in this.bones) {
                this.bones[n].returnToPool();
                delete this.bones[n];
            }
            for (var n in this.slots) {
                this.slots[n].returnToPool();
                delete this.slots[n];
            }
            for (var n in this.constraints) {
                this.constraints[n].returnToPool();
                delete this.constraints[n];
            }
            for (var n in this.skins) {
                this.skins[n].returnToPool();
                delete this.skins[n];
            }
            for (var n in this.animations) {
                this.animations[n].returnToPool();
                delete this.animations[n];
            }
            if (this.canvas !== null) {
                this.canvas.returnToPool();
            }
            if (this.userData !== null) {
                this.userData.returnToPool();
            }
            this.type = 0;
            this.frameRate = 0;
            this.cacheFrameRate = 0;
            this.scale = 1;
            this.name = '';
            this.aabb.clear();
            this.animationNames.length = 0;
            this.sortedBones.length = 0;
            this.sortedSlots.length = 0;
            this.defaultActions.length = 0;
            this.actions.length = 0;
            this.defaultSkin = null;
            this.defaultAnimation = null;
            this.canvas = null;
            this.userData = null;
            this.parent = null;
        };
        t.prototype.sortBones = function () {
            var t = this.sortedBones.length;
            if (t <= 0) {
                return;
            }
            var e = this.sortedBones.concat();
            var a = 0;
            var r = 0;
            this.sortedBones.length = 0;
            while (r < t) {
                var i = e[a++];
                if (a >= t) {
                    a = 0;
                }
                if (this.sortedBones.indexOf(i) >= 0) {
                    continue;
                }
                var n = false;
                for (var s in this.constraints) {
                    var o = this.constraints[s];
                    if (o.root === i && this.sortedBones.indexOf(o.target) < 0) {
                        n = true;
                        break;
                    }
                }
                if (n) {
                    continue;
                }
                if (i.parent !== null && this.sortedBones.indexOf(i.parent) < 0) {
                    continue;
                }
                this.sortedBones.push(i);
                r++;
            }
        };
        t.prototype.cacheFrames = function (t) {
            if (this.cacheFrameRate > 0) {
                return;
            }
            this.cacheFrameRate = t;
            for (var e in this.animations) {
                this.animations[e].cacheFrames(this.cacheFrameRate);
            }
        };
        t.prototype.setCacheFrame = function (t, e) {
            var a = this.parent.cachedFrames;
            var r = a.length;
            a.length += 10;
            a[r] = t.a;
            a[r + 1] = t.b;
            a[r + 2] = t.c;
            a[r + 3] = t.d;
            a[r + 4] = t.tx;
            a[r + 5] = t.ty;
            a[r + 6] = e.rotation;
            a[r + 7] = e.skew;
            a[r + 8] = e.scaleX;
            a[r + 9] = e.scaleY;
            return r;
        };
        t.prototype.getCacheFrame = function (t, e, a) {
            var r = this.parent.cachedFrames;
            t.a = r[a];
            t.b = r[a + 1];
            t.c = r[a + 2];
            t.d = r[a + 3];
            t.tx = r[a + 4];
            t.ty = r[a + 5];
            e.rotation = r[a + 6];
            e.skew = r[a + 7];
            e.scaleX = r[a + 8];
            e.scaleY = r[a + 9];
            e.x = t.tx;
            e.y = t.ty;
        };
        t.prototype.addBone = function (t) {
            if (t.name in this.bones) {
                console.warn('Same bone: ' + t.name);
                return;
            }
            this.bones[t.name] = t;
            this.sortedBones.push(t);
        };
        t.prototype.addSlot = function (t) {
            if (t.name in this.slots) {
                console.warn('Same slot: ' + t.name);
                return;
            }
            this.slots[t.name] = t;
            this.sortedSlots.push(t);
        };
        t.prototype.addConstraint = function (t) {
            if (t.name in this.constraints) {
                console.warn('Same constraint: ' + t.name);
                return;
            }
            this.constraints[t.name] = t;
        };
        t.prototype.addSkin = function (t) {
            if (t.name in this.skins) {
                console.warn('Same skin: ' + t.name);
                return;
            }
            t.parent = this;
            this.skins[t.name] = t;
            if (this.defaultSkin === null) {
                this.defaultSkin = t;
            }
            if (t.name === 'default') {
                this.defaultSkin = t;
            }
        };
        t.prototype.addAnimation = function (t) {
            if (t.name in this.animations) {
                console.warn('Same animation: ' + t.name);
                return;
            }
            t.parent = this;
            this.animations[t.name] = t;
            this.animationNames.push(t.name);
            if (this.defaultAnimation === null) {
                this.defaultAnimation = t;
            }
        };
        t.prototype.addAction = function (t, e) {
            if (e) {
                this.defaultActions.push(t);
            } else {
                this.actions.push(t);
            }
        };
        t.prototype.getBone = function (t) {
            return t in this.bones ? this.bones[t] : null;
        };
        t.prototype.getSlot = function (t) {
            return t in this.slots ? this.slots[t] : null;
        };
        t.prototype.getConstraint = function (t) {
            return t in this.constraints ? this.constraints[t] : null;
        };
        t.prototype.getSkin = function (t) {
            return t in this.skins ? this.skins[t] : null;
        };
        t.prototype.getMesh = function (t, e, a) {
            var r = this.getSkin(t);
            if (r === null) {
                return null;
            }
            return r.getDisplay(e, a);
        };
        t.prototype.getAnimation = function (t) {
            return t in this.animations ? this.animations[t] : null;
        };
        return t;
    })(a.BaseObject);
    a.ArmatureData = t;
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.transform = new a.Transform();
            t.userData = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.BoneData]';
        };
        t.prototype._onClear = function () {
            if (this.userData !== null) {
                this.userData.returnToPool();
            }
            this.inheritTranslation = false;
            this.inheritRotation = false;
            this.inheritScale = false;
            this.inheritReflection = false;
            this.type = 0;
            this.length = 0;
            this.alpha = 1;
            this.name = '';
            this.transform.identity();
            this.userData = null;
            this.parent = null;
        };
        return t;
    })(a.BaseObject);
    a.BoneData = e;
    var r = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.geometry = new a.GeometryData();
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.SurfaceData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.type = 1;
            this.segmentX = 0;
            this.segmentY = 0;
            this.geometry.clear();
        };
        return t;
    })(e);
    a.SurfaceData = r;
    var i = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.color = null;
            t.userData = null;
            return t;
        }
        t.createColor = function () {
            return new a.ColorTransform();
        };
        t.toString = function () {
            return '[class dragonBones.SlotData]';
        };
        t.prototype._onClear = function () {
            if (this.userData !== null) {
                this.userData.returnToPool();
            }
            this.blendMode = 0;
            this.displayIndex = 0;
            this.zOrder = 0;
            this.zIndex = 0;
            this.alpha = 1;
            this.name = '';
            this.color = null;
            this.userData = null;
            this.parent = null;
        };
        t.DEFAULT_COLOR = new a.ColorTransform();
        return t;
    })(a.BaseObject);
    a.SlotData = i;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.prototype._onClear = function () {
            this.order = 0;
            this.name = '';
            this.type = 0;
            this.target = null;
            this.root = null;
            this.bone = null;
        };
        return e;
    })(t.BaseObject);
    t.ConstraintData = e;
    var a = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.IKConstraintData]';
        };
        e.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this.scaleEnabled = false;
            this.bendPositive = false;
            this.weight = 1;
        };
        return e;
    })(e);
    t.IKConstraintData = a;
    var r = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.bones = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.PathConstraintData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.pathSlot = null;
            this.pathDisplayData = null;
            this.bones.length = 0;
            this.positionMode = 0;
            this.spacingMode = 1;
            this.rotateMode = 1;
            this.position = 0;
            this.spacing = 0;
            this.rotateOffset = 0;
            this.rotateMix = 0;
            this.translateMix = 0;
        };
        t.prototype.AddBone = function (t) {
            this.bones.push(t);
        };
        return t;
    })(e);
    t.PathConstraintData = r;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.CanvasData]';
        };
        e.prototype._onClear = function () {
            this.hasBackground = false;
            this.color = 0;
            this.x = 0;
            this.y = 0;
            this.width = 0;
            this.height = 0;
        };
        return e;
    })(t.BaseObject);
    t.CanvasData = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.displays = {};
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.SkinData]';
        };
        t.prototype._onClear = function () {
            for (var t in this.displays) {
                var e = this.displays[t];
                for (var a = 0, r = e; a < r.length; a++) {
                    var i = r[a];
                    if (i !== null) {
                        i.returnToPool();
                    }
                }
                delete this.displays[t];
            }
            this.name = '';
            this.parent = null;
        };
        t.prototype.addDisplay = function (t, e) {
            if (!(t in this.displays)) {
                this.displays[t] = [];
            }
            if (e !== null) {
                e.parent = this;
            }
            var a = this.displays[t];
            a.push(e);
        };
        t.prototype.getDisplay = function (t, e) {
            var a = this.getDisplays(t);
            if (a !== null) {
                for (var r = 0, i = a; r < i.length; r++) {
                    var n = i[r];
                    if (n !== null && n.name === e) {
                        return n;
                    }
                }
            }
            return null;
        };
        t.prototype.getDisplays = function (t) {
            if (!(t in this.displays)) {
                return null;
            }
            return this.displays[t];
        };
        return t;
    })(t.BaseObject);
    t.SkinData = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (a) {
    var r = (function () {
        function t() {
            this.weight = null;
        }
        t.prototype.clear = function () {
            if (!this.isShared && this.weight !== null) {
                this.weight.returnToPool();
            }
            this.isShared = false;
            this.inheritDeform = false;
            this.offset = 0;
            this.data = null;
            this.weight = null;
        };
        t.prototype.shareFrom = function (t) {
            this.isShared = true;
            this.offset = t.offset;
            this.weight = t.weight;
        };
        Object.defineProperty(t.prototype, 'vertexCount', {
            get: function () {
                var t = this.data.intArray;
                return t[this.offset + 0];
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'triangleCount', {
            get: function () {
                var t = this.data.intArray;
                return t[this.offset + 1];
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })();
    a.GeometryData = r;
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.transform = new a.Transform();
            return t;
        }
        t.prototype._onClear = function () {
            this.name = '';
            this.path = '';
            this.transform.identity();
            this.parent = null;
        };
        return t;
    })(a.BaseObject);
    a.DisplayData = t;
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.pivot = new a.Point();
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.ImageDisplayData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.type = 0;
            this.pivot.clear();
            this.texture = null;
        };
        return t;
    })(t);
    a.ImageDisplayData = e;
    var i = (function (r) {
        __extends(t, r);
        function t() {
            var t = (r !== null && r.apply(this, arguments)) || this;
            t.actions = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.ArmatureDisplayData]';
        };
        t.prototype._onClear = function () {
            r.prototype._onClear.call(this);
            for (var t = 0, e = this.actions; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            this.type = 1;
            this.inheritAnimation = false;
            this.actions.length = 0;
            this.armature = null;
        };
        t.prototype.addAction = function (t) {
            this.actions.push(t);
        };
        return t;
    })(t);
    a.ArmatureDisplayData = i;
    var n = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.geometry = new r();
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.MeshDisplayData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.type = 2;
            this.geometry.clear();
            this.texture = null;
        };
        return t;
    })(t);
    a.MeshDisplayData = n;
    var s = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.boundingBox = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.BoundingBoxDisplayData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            if (this.boundingBox !== null) {
                this.boundingBox.returnToPool();
            }
            this.type = 3;
            this.boundingBox = null;
        };
        return t;
    })(t);
    a.BoundingBoxDisplayData = s;
    var o = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.geometry = new r();
            t.curveLengths = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.PathDisplayData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.type = 4;
            this.closed = false;
            this.constantSpeed = false;
            this.geometry.clear();
            this.curveLengths.length = 0;
        };
        return t;
    })(t);
    a.PathDisplayData = o;
    var l = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.bones = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.WeightData]';
        };
        t.prototype._onClear = function () {
            this.count = 0;
            this.offset = 0;
            this.bones.length = 0;
        };
        t.prototype.addBone = function (t) {
            this.bones.push(t);
        };
        return t;
    })(a.BaseObject);
    a.WeightData = l;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.prototype._onClear = function () {
            this.color = 0;
            this.width = 0;
            this.height = 0;
        };
        return e;
    })(t.BaseObject);
    t.BoundingBoxData = e;
    var h = (function (t) {
        __extends(D, t);
        function D() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        D.toString = function () {
            return '[class dragonBones.RectangleBoundingBoxData]';
        };
        D._computeOutCode = function (t, e, a, r, i, n) {
            var s = 0;
            if (t < a) {
                s |= 1;
            } else if (t > i) {
                s |= 2;
            }
            if (e < r) {
                s |= 4;
            } else if (e > n) {
                s |= 8;
            }
            return s;
        };
        D.rectangleIntersectsSegment = function (t, e, a, r, i, n, s, o, l, h, u) {
            if (l === void 0) {
                l = null;
            }
            if (h === void 0) {
                h = null;
            }
            if (u === void 0) {
                u = null;
            }
            var f = t > i && t < s && e > n && e < o;
            var _ = a > i && a < s && r > n && r < o;
            if (f && _) {
                return -1;
            }
            var m = 0;
            var p = D._computeOutCode(t, e, i, n, s, o);
            var c = D._computeOutCode(a, r, i, n, s, o);
            while (true) {
                if ((p | c) === 0) {
                    m = 2;
                    break;
                } else if ((p & c) !== 0) {
                    break;
                }
                var d = 0;
                var y = 0;
                var v = 0;
                var g = p !== 0 ? p : c;
                if ((g & 4) !== 0) {
                    d = t + ((a - t) * (n - e)) / (r - e);
                    y = n;
                    if (u !== null) {
                        v = -Math.PI * 0.5;
                    }
                } else if ((g & 8) !== 0) {
                    d = t + ((a - t) * (o - e)) / (r - e);
                    y = o;
                    if (u !== null) {
                        v = Math.PI * 0.5;
                    }
                } else if ((g & 2) !== 0) {
                    y = e + ((r - e) * (s - t)) / (a - t);
                    d = s;
                    if (u !== null) {
                        v = 0;
                    }
                } else if ((g & 1) !== 0) {
                    y = e + ((r - e) * (i - t)) / (a - t);
                    d = i;
                    if (u !== null) {
                        v = Math.PI;
                    }
                }
                if (g === p) {
                    t = d;
                    e = y;
                    p = D._computeOutCode(t, e, i, n, s, o);
                    if (u !== null) {
                        u.x = v;
                    }
                } else {
                    a = d;
                    r = y;
                    c = D._computeOutCode(a, r, i, n, s, o);
                    if (u !== null) {
                        u.y = v;
                    }
                }
            }
            if (m) {
                if (f) {
                    m = 2;
                    if (l !== null) {
                        l.x = a;
                        l.y = r;
                    }
                    if (h !== null) {
                        h.x = a;
                        h.y = a;
                    }
                    if (u !== null) {
                        u.x = u.y + Math.PI;
                    }
                } else if (_) {
                    m = 1;
                    if (l !== null) {
                        l.x = t;
                        l.y = e;
                    }
                    if (h !== null) {
                        h.x = t;
                        h.y = e;
                    }
                    if (u !== null) {
                        u.y = u.x + Math.PI;
                    }
                } else {
                    m = 3;
                    if (l !== null) {
                        l.x = t;
                        l.y = e;
                    }
                    if (h !== null) {
                        h.x = a;
                        h.y = r;
                    }
                }
            }
            return m;
        };
        D.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this.type = 0;
        };
        D.prototype.containsPoint = function (t, e) {
            var a = this.width * 0.5;
            if (t >= -a && t <= a) {
                var r = this.height * 0.5;
                if (e >= -r && e <= r) {
                    return true;
                }
            }
            return false;
        };
        D.prototype.intersectsSegment = function (t, e, a, r, i, n, s) {
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            var o = this.width * 0.5;
            var l = this.height * 0.5;
            var h = D.rectangleIntersectsSegment(t, e, a, r, -o, -l, o, l, i, n, s);
            return h;
        };
        return D;
    })(e);
    t.RectangleBoundingBoxData = h;
    var a = (function (t) {
        __extends(l, t);
        function l() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        l.toString = function () {
            return '[class dragonBones.EllipseData]';
        };
        l.ellipseIntersectsSegment = function (t, e, a, r, i, n, s, o, l, h, u) {
            if (l === void 0) {
                l = null;
            }
            if (h === void 0) {
                h = null;
            }
            if (u === void 0) {
                u = null;
            }
            var f = s / o;
            var _ = f * f;
            e *= f;
            r *= f;
            var m = a - t;
            var p = r - e;
            var c = Math.sqrt(m * m + p * p);
            var d = m / c;
            var y = p / c;
            var v = (i - t) * d + (n - e) * y;
            var g = v * v;
            var D = t * t + e * e;
            var T = s * s;
            var b = T - D + g;
            var A = 0;
            if (b >= 0) {
                var P = Math.sqrt(b);
                var S = v - P;
                var O = v + P;
                var x = S < 0 ? -1 : S <= c ? 0 : 1;
                var B = O < 0 ? -1 : O <= c ? 0 : 1;
                var E = x * B;
                if (E < 0) {
                    return -1;
                } else if (E === 0) {
                    if (x === -1) {
                        A = 2;
                        a = t + O * d;
                        r = (e + O * y) / f;
                        if (l !== null) {
                            l.x = a;
                            l.y = r;
                        }
                        if (h !== null) {
                            h.x = a;
                            h.y = r;
                        }
                        if (u !== null) {
                            u.x = Math.atan2((r / T) * _, a / T);
                            u.y = u.x + Math.PI;
                        }
                    } else if (B === 1) {
                        A = 1;
                        t = t + S * d;
                        e = (e + S * y) / f;
                        if (l !== null) {
                            l.x = t;
                            l.y = e;
                        }
                        if (h !== null) {
                            h.x = t;
                            h.y = e;
                        }
                        if (u !== null) {
                            u.x = Math.atan2((e / T) * _, t / T);
                            u.y = u.x + Math.PI;
                        }
                    } else {
                        A = 3;
                        if (l !== null) {
                            l.x = t + S * d;
                            l.y = (e + S * y) / f;
                            if (u !== null) {
                                u.x = Math.atan2((l.y / T) * _, l.x / T);
                            }
                        }
                        if (h !== null) {
                            h.x = t + O * d;
                            h.y = (e + O * y) / f;
                            if (u !== null) {
                                u.y = Math.atan2((h.y / T) * _, h.x / T);
                            }
                        }
                    }
                }
            }
            return A;
        };
        l.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this.type = 1;
        };
        l.prototype.containsPoint = function (t, e) {
            var a = this.width * 0.5;
            if (t >= -a && t <= a) {
                var r = this.height * 0.5;
                if (e >= -r && e <= r) {
                    e *= a / r;
                    return Math.sqrt(t * t + e * e) <= a;
                }
            }
            return false;
        };
        l.prototype.intersectsSegment = function (t, e, a, r, i, n, s) {
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            var o = l.ellipseIntersectsSegment(
                t,
                e,
                a,
                r,
                0,
                0,
                this.width * 0.5,
                this.height * 0.5,
                i,
                n,
                s
            );
            return o;
        };
        return l;
    })(e);
    t.EllipseBoundingBoxData = a;
    var r = (function (e) {
        __extends(l, e);
        function l() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.vertices = [];
            return t;
        }
        l.toString = function () {
            return '[class dragonBones.PolygonBoundingBoxData]';
        };
        l.polygonIntersectsSegment = function (t, e, a, r, i, n, s, o) {
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            if (o === void 0) {
                o = null;
            }
            if (t === a) {
                t = a + 1e-6;
            }
            if (e === r) {
                e = r + 1e-6;
            }
            var l = i.length;
            var h = t - a;
            var u = e - r;
            var f = t * r - e * a;
            var _ = 0;
            var m = i[l - 2];
            var p = i[l - 1];
            var c = 0;
            var d = 0;
            var y = 0;
            var v = 0;
            var g = 0;
            var D = 0;
            for (var T = 0; T < l; T += 2) {
                var b = i[T];
                var A = i[T + 1];
                if (m === b) {
                    m = b + 1e-4;
                }
                if (p === A) {
                    p = A + 1e-4;
                }
                var P = m - b;
                var S = p - A;
                var O = m * A - p * b;
                var x = h * S - u * P;
                var B = (f * P - h * O) / x;
                if (
                    ((B >= m && B <= b) || (B >= b && B <= m)) &&
                    (h === 0 || (B >= t && B <= a) || (B >= a && B <= t))
                ) {
                    var E = (f * S - u * O) / x;
                    if (
                        ((E >= p && E <= A) || (E >= A && E <= p)) &&
                        (u === 0 || (E >= e && E <= r) || (E >= r && E <= e))
                    ) {
                        if (s !== null) {
                            var I = B - t;
                            if (I < 0) {
                                I = -I;
                            }
                            if (_ === 0) {
                                c = I;
                                d = I;
                                y = B;
                                v = E;
                                g = B;
                                D = E;
                                if (o !== null) {
                                    o.x = Math.atan2(A - p, b - m) - Math.PI * 0.5;
                                    o.y = o.x;
                                }
                            } else {
                                if (I < c) {
                                    c = I;
                                    y = B;
                                    v = E;
                                    if (o !== null) {
                                        o.x = Math.atan2(A - p, b - m) - Math.PI * 0.5;
                                    }
                                }
                                if (I > d) {
                                    d = I;
                                    g = B;
                                    D = E;
                                    if (o !== null) {
                                        o.y = Math.atan2(A - p, b - m) - Math.PI * 0.5;
                                    }
                                }
                            }
                            _++;
                        } else {
                            y = B;
                            v = E;
                            g = B;
                            D = E;
                            _++;
                            if (o !== null) {
                                o.x = Math.atan2(A - p, b - m) - Math.PI * 0.5;
                                o.y = o.x;
                            }
                            break;
                        }
                    }
                }
                m = b;
                p = A;
            }
            if (_ === 1) {
                if (n !== null) {
                    n.x = y;
                    n.y = v;
                }
                if (s !== null) {
                    s.x = y;
                    s.y = v;
                }
                if (o !== null) {
                    o.y = o.x + Math.PI;
                }
            } else if (_ > 1) {
                _++;
                if (n !== null) {
                    n.x = y;
                    n.y = v;
                }
                if (s !== null) {
                    s.x = g;
                    s.y = D;
                }
            }
            return _;
        };
        l.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.type = 2;
            this.x = 0;
            this.y = 0;
            this.vertices.length = 0;
        };
        l.prototype.containsPoint = function (t, e) {
            var a = false;
            if (t >= this.x && t <= this.width && e >= this.y && e <= this.height) {
                for (var r = 0, i = this.vertices.length, n = i - 2; r < i; r += 2) {
                    var s = this.vertices[n + 1];
                    var o = this.vertices[r + 1];
                    if ((o < e && s >= e) || (s < e && o >= e)) {
                        var l = this.vertices[n];
                        var h = this.vertices[r];
                        if (((e - o) * (l - h)) / (s - o) + h < t) {
                            a = !a;
                        }
                    }
                    n = r;
                }
            }
            return a;
        };
        l.prototype.intersectsSegment = function (t, e, a, r, i, n, s) {
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            var o = 0;
            if (
                h.rectangleIntersectsSegment(
                    t,
                    e,
                    a,
                    r,
                    this.x,
                    this.y,
                    this.x + this.width,
                    this.y + this.height,
                    null,
                    null,
                    null
                ) !== 0
            ) {
                o = l.polygonIntersectsSegment(t, e, a, r, this.vertices, i, n, s);
            }
            return o;
        };
        return l;
    })(e);
    t.PolygonBoundingBoxData = r;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.cachedFrames = [];
            t.boneTimelines = {};
            t.slotTimelines = {};
            t.constraintTimelines = {};
            t.animationTimelines = {};
            t.boneCachedFrameIndices = {};
            t.slotCachedFrameIndices = {};
            t.actionTimeline = null;
            t.zOrderTimeline = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationData]';
        };
        t.prototype._onClear = function () {
            for (var t in this.boneTimelines) {
                for (var e = 0, a = this.boneTimelines[t]; e < a.length; e++) {
                    var r = a[e];
                    r.returnToPool();
                }
                delete this.boneTimelines[t];
            }
            for (var t in this.slotTimelines) {
                for (var i = 0, n = this.slotTimelines[t]; i < n.length; i++) {
                    var r = n[i];
                    r.returnToPool();
                }
                delete this.slotTimelines[t];
            }
            for (var t in this.constraintTimelines) {
                for (var s = 0, o = this.constraintTimelines[t]; s < o.length; s++) {
                    var r = o[s];
                    r.returnToPool();
                }
                delete this.constraintTimelines[t];
            }
            for (var t in this.animationTimelines) {
                for (var l = 0, h = this.animationTimelines[t]; l < h.length; l++) {
                    var r = h[l];
                    r.returnToPool();
                }
                delete this.animationTimelines[t];
            }
            for (var t in this.boneCachedFrameIndices) {
                delete this.boneCachedFrameIndices[t];
            }
            for (var t in this.slotCachedFrameIndices) {
                delete this.slotCachedFrameIndices[t];
            }
            if (this.actionTimeline !== null) {
                this.actionTimeline.returnToPool();
            }
            if (this.zOrderTimeline !== null) {
                this.zOrderTimeline.returnToPool();
            }
            this.frameIntOffset = 0;
            this.frameFloatOffset = 0;
            this.frameOffset = 0;
            this.blendType = 0;
            this.frameCount = 0;
            this.playTimes = 0;
            this.duration = 0;
            this.scale = 1;
            this.fadeInTime = 0;
            this.cacheFrameRate = 0;
            this.name = '';
            this.cachedFrames.length = 0;
            this.actionTimeline = null;
            this.zOrderTimeline = null;
            this.parent = null;
        };
        t.prototype.cacheFrames = function (t) {
            if (this.cacheFrameRate > 0) {
                return;
            }
            this.cacheFrameRate = Math.max(Math.ceil(t * this.scale), 1);
            var e = Math.ceil(this.cacheFrameRate * this.duration) + 1;
            this.cachedFrames.length = e;
            for (var a = 0, r = this.cacheFrames.length; a < r; ++a) {
                this.cachedFrames[a] = false;
            }
            for (var i = 0, n = this.parent.sortedBones; i < n.length; i++) {
                var s = n[i];
                var o = new Array(e);
                for (var a = 0, r = o.length; a < r; ++a) {
                    o[a] = -1;
                }
                this.boneCachedFrameIndices[s.name] = o;
            }
            for (var l = 0, h = this.parent.sortedSlots; l < h.length; l++) {
                var u = h[l];
                var o = new Array(e);
                for (var a = 0, r = o.length; a < r; ++a) {
                    o[a] = -1;
                }
                this.slotCachedFrameIndices[u.name] = o;
            }
        };
        t.prototype.addBoneTimeline = function (t, e) {
            var a = t in this.boneTimelines ? this.boneTimelines[t] : (this.boneTimelines[t] = []);
            if (a.indexOf(e) < 0) {
                a.push(e);
            }
        };
        t.prototype.addSlotTimeline = function (t, e) {
            var a = t in this.slotTimelines ? this.slotTimelines[t] : (this.slotTimelines[t] = []);
            if (a.indexOf(e) < 0) {
                a.push(e);
            }
        };
        t.prototype.addConstraintTimeline = function (t, e) {
            var a =
                t in this.constraintTimelines
                    ? this.constraintTimelines[t]
                    : (this.constraintTimelines[t] = []);
            if (a.indexOf(e) < 0) {
                a.push(e);
            }
        };
        t.prototype.addAnimationTimeline = function (t, e) {
            var a =
                t in this.animationTimelines
                    ? this.animationTimelines[t]
                    : (this.animationTimelines[t] = []);
            if (a.indexOf(e) < 0) {
                a.push(e);
            }
        };
        t.prototype.getBoneTimelines = function (t) {
            return t in this.boneTimelines ? this.boneTimelines[t] : null;
        };
        t.prototype.getSlotTimelines = function (t) {
            return t in this.slotTimelines ? this.slotTimelines[t] : null;
        };
        t.prototype.getConstraintTimelines = function (t) {
            return t in this.constraintTimelines ? this.constraintTimelines[t] : null;
        };
        t.prototype.getAnimationTimelines = function (t) {
            return t in this.animationTimelines ? this.animationTimelines[t] : null;
        };
        t.prototype.getBoneCachedFrameIndices = function (t) {
            return t in this.boneCachedFrameIndices ? this.boneCachedFrameIndices[t] : null;
        };
        t.prototype.getSlotCachedFrameIndices = function (t) {
            return t in this.slotCachedFrameIndices ? this.slotCachedFrameIndices[t] : null;
        };
        return t;
    })(t.BaseObject);
    t.AnimationData = e;
    var a = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.TimelineData]';
        };
        e.prototype._onClear = function () {
            this.type = 10;
            this.offset = 0;
            this.frameIndicesOffset = -1;
        };
        return e;
    })(t.BaseObject);
    t.TimelineData = a;
    var r = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.AnimationTimelineData]';
        };
        e.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this.x = 0;
            this.y = 0;
        };
        return e;
    })(a);
    t.AnimationTimelineData = r;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.boneMask = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationConfig]';
        };
        t.prototype._onClear = function () {
            this.pauseFadeOut = true;
            this.fadeOutMode = 4;
            this.fadeOutTweenType = 1;
            this.fadeOutTime = -1;
            this.actionEnabled = true;
            this.additive = false;
            this.displayControl = true;
            this.pauseFadeIn = true;
            this.resetToPose = true;
            this.fadeInTweenType = 1;
            this.playTimes = -1;
            this.layer = 0;
            this.position = 0;
            this.duration = -1;
            this.timeScale = -100;
            this.weight = 1;
            this.fadeInTime = -1;
            this.autoFadeOutTime = -1;
            this.name = '';
            this.animation = '';
            this.group = '';
            this.boneMask.length = 0;
        };
        t.prototype.clear = function () {
            this._onClear();
        };
        t.prototype.copyFrom = function (t) {
            this.pauseFadeOut = t.pauseFadeOut;
            this.fadeOutMode = t.fadeOutMode;
            this.autoFadeOutTime = t.autoFadeOutTime;
            this.fadeOutTweenType = t.fadeOutTweenType;
            this.actionEnabled = t.actionEnabled;
            this.additive = t.additive;
            this.displayControl = t.displayControl;
            this.pauseFadeIn = t.pauseFadeIn;
            this.resetToPose = t.resetToPose;
            this.playTimes = t.playTimes;
            this.layer = t.layer;
            this.position = t.position;
            this.duration = t.duration;
            this.timeScale = t.timeScale;
            this.fadeInTime = t.fadeInTime;
            this.fadeOutTime = t.fadeOutTime;
            this.fadeInTweenType = t.fadeInTweenType;
            this.weight = t.weight;
            this.name = t.name;
            this.animation = t.animation;
            this.group = t.group;
            this.boneMask.length = t.boneMask.length;
            for (var e = 0, a = this.boneMask.length; e < a; ++e) {
                this.boneMask[e] = t.boneMask[e];
            }
        };
        return t;
    })(t.BaseObject);
    t.AnimationConfig = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (r) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.textures = {};
            return t;
        }
        t.prototype._onClear = function () {
            for (var t in this.textures) {
                this.textures[t].returnToPool();
                delete this.textures[t];
            }
            this.autoSearch = false;
            this.width = 0;
            this.height = 0;
            this.scale = 1;
            this.name = '';
            this.imagePath = '';
        };
        t.prototype.copyFrom = function (t) {
            this.autoSearch = t.autoSearch;
            this.scale = t.scale;
            this.width = t.width;
            this.height = t.height;
            this.name = t.name;
            this.imagePath = t.imagePath;
            for (var e in this.textures) {
                this.textures[e].returnToPool();
                delete this.textures[e];
            }
            for (var e in t.textures) {
                var a = this.createTexture();
                a.copyFrom(t.textures[e]);
                this.textures[e] = a;
            }
        };
        t.prototype.addTexture = function (t) {
            if (t.name in this.textures) {
                console.warn('Same texture: ' + t.name);
                return;
            }
            t.parent = this;
            this.textures[t.name] = t;
        };
        t.prototype.getTexture = function (t) {
            return t in this.textures ? this.textures[t] : null;
        };
        return t;
    })(r.BaseObject);
    r.TextureAtlasData = t;
    var e = (function (e) {
        __extends(a, e);
        function a() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.region = new r.Rectangle();
            t.frame = null;
            return t;
        }
        a.createRectangle = function () {
            return new r.Rectangle();
        };
        a.prototype._onClear = function () {
            this.rotated = false;
            this.name = '';
            this.region.clear();
            this.parent = null;
            this.frame = null;
        };
        a.prototype.copyFrom = function (t) {
            this.rotated = t.rotated;
            this.name = t.name;
            this.region.copyFrom(t.region);
            this.parent = t.parent;
            if (this.frame === null && t.frame !== null) {
                this.frame = a.createRectangle();
            } else if (this.frame !== null && t.frame === null) {
                this.frame = null;
            }
            if (this.frame !== null && t.frame !== null) {
                this.frame.copyFrom(t.frame);
            }
        };
        return a;
    })(r.BaseObject);
    r.TextureData = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (i) {
    var t = (function (e) {
        __extends(y, e);
        function y() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._bones = [];
            t._slots = [];
            t._constraints = [];
            t._actions = [];
            t._animation = null;
            t._proxy = null;
            t._replaceTextureAtlasData = null;
            t._clock = null;
            return t;
        }
        y.toString = function () {
            return '[class dragonBones.Armature]';
        };
        y._onSortSlots = function (t, e) {
            return t._zIndex * 1e3 + t._zOrder > e._zIndex * 1e3 + e._zOrder ? 1 : -1;
        };
        y.prototype._onClear = function () {
            if (this._clock !== null) {
                this._clock.remove(this);
            }
            for (var t = 0, e = this._bones; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            for (var r = 0, i = this._slots; r < i.length; r++) {
                var n = i[r];
                n.returnToPool();
            }
            for (var s = 0, o = this._constraints; s < o.length; s++) {
                var l = o[s];
                l.returnToPool();
            }
            for (var h = 0, u = this._actions; h < u.length; h++) {
                var f = u[h];
                f.returnToPool();
            }
            if (this._animation !== null) {
                this._animation.returnToPool();
            }
            if (this._proxy !== null) {
                this._proxy.dbClear();
            }
            if (this._replaceTextureAtlasData !== null) {
                this._replaceTextureAtlasData.returnToPool();
            }
            this.inheritAnimation = true;
            this.userData = null;
            this._lockUpdate = false;
            this._slotsDirty = true;
            this._zOrderDirty = false;
            this._zIndexDirty = false;
            this._alphaDirty = true;
            this._flipX = false;
            this._flipY = false;
            this._cacheFrameIndex = -1;
            this._alpha = 1;
            this._globalAlpha = 1;
            this._bones.length = 0;
            this._slots.length = 0;
            this._constraints.length = 0;
            this._actions.length = 0;
            this._armatureData = null;
            this._animation = null;
            this._proxy = null;
            this._display = null;
            this._replaceTextureAtlasData = null;
            this._replacedTexture = null;
            this._dragonBones = null;
            this._clock = null;
            this._parent = null;
        };
        y.prototype._sortZOrder = function (t, e) {
            var a = this._armatureData.sortedSlots;
            var r = t === null;
            if (this._zOrderDirty || !r) {
                for (var i = 0, n = a.length; i < n; ++i) {
                    var s = r ? i : t[e + i];
                    if (s < 0 || s >= n) {
                        continue;
                    }
                    var o = a[s];
                    var l = this.getSlot(o.name);
                    if (l !== null) {
                        l._setZOrder(i);
                    }
                }
                this._slotsDirty = true;
                this._zOrderDirty = !r;
            }
        };
        y.prototype._addBone = function (t) {
            if (this._bones.indexOf(t) < 0) {
                this._bones.push(t);
            }
        };
        y.prototype._addSlot = function (t) {
            if (this._slots.indexOf(t) < 0) {
                this._slots.push(t);
            }
        };
        y.prototype._addConstraint = function (t) {
            if (this._constraints.indexOf(t) < 0) {
                this._constraints.push(t);
            }
        };
        y.prototype._bufferAction = function (t, e) {
            if (this._actions.indexOf(t) < 0) {
                if (e) {
                    this._actions.push(t);
                } else {
                    this._actions.unshift(t);
                }
            }
        };
        y.prototype.dispose = function () {
            if (this._armatureData !== null) {
                this._lockUpdate = true;
                this._dragonBones.bufferObject(this);
            }
        };
        y.prototype.init = function (t, e, a, r) {
            if (this._armatureData !== null) {
                return;
            }
            this._armatureData = t;
            this._animation = i.BaseObject.borrowObject(i.Animation);
            this._proxy = e;
            this._display = a;
            this._dragonBones = r;
            this._proxy.dbInit(this);
            this._animation.init(this);
            this._animation.animations = this._armatureData.animations;
        };
        y.prototype.advanceTime = function (t) {
            if (this._lockUpdate) {
                return;
            }
            this._lockUpdate = true;
            if (this._armatureData === null) {
                console.warn('The armature has been disposed.');
                return;
            } else if (this._armatureData.parent === null) {
                console.warn(
                    'The armature data has been disposed.\nPlease make sure dispose armature before call factory.clear().'
                );
                return;
            }
            var e = this._cacheFrameIndex;
            this._animation.advanceTime(t);
            if (this._slotsDirty || this._zIndexDirty) {
                this._slots.sort(y._onSortSlots);
                if (this._zIndexDirty) {
                    for (var a = 0, r = this._slots.length; a < r; ++a) {
                        this._slots[a]._setZOrder(a);
                    }
                }
                this._slotsDirty = false;
                this._zIndexDirty = false;
            }
            if (this._alphaDirty) {
                this._alphaDirty = false;
                this._globalAlpha =
                    this._alpha * (this._parent !== null ? this._parent._globalAlpha : 1);
                for (var i = 0, n = this._bones; i < n.length; i++) {
                    var s = n[i];
                    s._updateAlpha();
                }
                for (var o = 0, l = this._slots; o < l.length; o++) {
                    var h = l[o];
                    h._updateAlpha();
                }
            }
            if (this._cacheFrameIndex < 0 || this._cacheFrameIndex !== e) {
                var a = 0,
                    r = 0;
                for (a = 0, r = this._bones.length; a < r; ++a) {
                    this._bones[a].update(this._cacheFrameIndex);
                }
                for (a = 0, r = this._slots.length; a < r; ++a) {
                    this._slots[a].update(this._cacheFrameIndex);
                }
            }
            if (this._actions.length > 0) {
                for (var u = 0, f = this._actions; u < f.length; u++) {
                    var _ = f[u];
                    var m = _.actionData;
                    if (m !== null) {
                        if (m.type === 0) {
                            if (_.slot !== null) {
                                var p = _.slot.childArmature;
                                if (p !== null) {
                                    p.animation.fadeIn(m.name);
                                }
                            } else if (_.bone !== null) {
                                for (var c = 0, d = this.getSlots(); c < d.length; c++) {
                                    var h = d[c];
                                    if (h.parent === _.bone) {
                                        var p = h.childArmature;
                                        if (p !== null) {
                                            p.animation.fadeIn(m.name);
                                        }
                                    }
                                }
                            } else {
                                this._animation.fadeIn(m.name);
                            }
                        }
                    }
                    _.returnToPool();
                }
                this._actions.length = 0;
            }
            this._lockUpdate = false;
            this._proxy.dbUpdate();
        };
        y.prototype.invalidUpdate = function (t, e) {
            if (t === void 0) {
                t = null;
            }
            if (e === void 0) {
                e = false;
            }
            if (t !== null && t.length > 0) {
                var a = this.getBone(t);
                if (a !== null) {
                    a.invalidUpdate();
                    if (e) {
                        for (var r = 0, i = this._slots; r < i.length; r++) {
                            var n = i[r];
                            if (n.parent === a) {
                                n.invalidUpdate();
                            }
                        }
                    }
                }
            } else {
                for (var s = 0, o = this._bones; s < o.length; s++) {
                    var a = o[s];
                    a.invalidUpdate();
                }
                if (e) {
                    for (var l = 0, h = this._slots; l < h.length; l++) {
                        var n = h[l];
                        n.invalidUpdate();
                    }
                }
            }
        };
        y.prototype.containsPoint = function (t, e) {
            for (var a = 0, r = this._slots; a < r.length; a++) {
                var i = r[a];
                if (i.containsPoint(t, e)) {
                    return i;
                }
            }
            return null;
        };
        y.prototype.intersectsSegment = function (t, e, a, r, i, n, s) {
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            var o = t === a;
            var l = 0;
            var h = 0;
            var u = 0;
            var f = 0;
            var _ = 0;
            var m = 0;
            var p = 0;
            var c = 0;
            var d = null;
            var y = null;
            for (var v = 0, g = this._slots; v < g.length; v++) {
                var D = g[v];
                var T = D.intersectsSegment(t, e, a, r, i, n, s);
                if (T > 0) {
                    if (i !== null || n !== null) {
                        if (i !== null) {
                            var b = o ? i.y - e : i.x - t;
                            if (b < 0) {
                                b = -b;
                            }
                            if (d === null || b < l) {
                                l = b;
                                u = i.x;
                                f = i.y;
                                d = D;
                                if (s) {
                                    p = s.x;
                                }
                            }
                        }
                        if (n !== null) {
                            var b = n.x - t;
                            if (b < 0) {
                                b = -b;
                            }
                            if (y === null || b > h) {
                                h = b;
                                _ = n.x;
                                m = n.y;
                                y = D;
                                if (s !== null) {
                                    c = s.y;
                                }
                            }
                        }
                    } else {
                        d = D;
                        break;
                    }
                }
            }
            if (d !== null && i !== null) {
                i.x = u;
                i.y = f;
                if (s !== null) {
                    s.x = p;
                }
            }
            if (y !== null && n !== null) {
                n.x = _;
                n.y = m;
                if (s !== null) {
                    s.y = c;
                }
            }
            return d;
        };
        y.prototype.getBone = function (t) {
            for (var e = 0, a = this._bones; e < a.length; e++) {
                var r = a[e];
                if (r.name === t) {
                    return r;
                }
            }
            return null;
        };
        y.prototype.getBoneByDisplay = function (t) {
            var e = this.getSlotByDisplay(t);
            return e !== null ? e.parent : null;
        };
        y.prototype.getSlot = function (t) {
            for (var e = 0, a = this._slots; e < a.length; e++) {
                var r = a[e];
                if (r.name === t) {
                    return r;
                }
            }
            return null;
        };
        y.prototype.getSlotByDisplay = function (t) {
            if (t !== null) {
                for (var e = 0, a = this._slots; e < a.length; e++) {
                    var r = a[e];
                    if (r.display === t) {
                        return r;
                    }
                }
            }
            return null;
        };
        y.prototype.getBones = function () {
            return this._bones;
        };
        y.prototype.getSlots = function () {
            return this._slots;
        };
        Object.defineProperty(y.prototype, 'flipX', {
            get: function () {
                return this._flipX;
            },
            set: function (t) {
                if (this._flipX === t) {
                    return;
                }
                this._flipX = t;
                this.invalidUpdate();
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'flipY', {
            get: function () {
                return this._flipY;
            },
            set: function (t) {
                if (this._flipY === t) {
                    return;
                }
                this._flipY = t;
                this.invalidUpdate();
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'cacheFrameRate', {
            get: function () {
                return this._armatureData.cacheFrameRate;
            },
            set: function (t) {
                if (this._armatureData.cacheFrameRate !== t) {
                    this._armatureData.cacheFrames(t);
                    for (var e = 0, a = this._slots; e < a.length; e++) {
                        var r = a[e];
                        var i = r.childArmature;
                        if (i !== null) {
                            i.cacheFrameRate = t;
                        }
                    }
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'name', {
            get: function () {
                return this._armatureData.name;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'armatureData', {
            get: function () {
                return this._armatureData;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'animation', {
            get: function () {
                return this._animation;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'proxy', {
            get: function () {
                return this._proxy;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'eventDispatcher', {
            get: function () {
                return this._proxy;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'display', {
            get: function () {
                return this._display;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'replacedTexture', {
            get: function () {
                return this._replacedTexture;
            },
            set: function (t) {
                if (this._replacedTexture === t) {
                    return;
                }
                if (this._replaceTextureAtlasData !== null) {
                    this._replaceTextureAtlasData.returnToPool();
                    this._replaceTextureAtlasData = null;
                }
                this._replacedTexture = t;
                for (var e = 0, a = this._slots; e < a.length; e++) {
                    var r = a[e];
                    r.invalidUpdate();
                    r.update(-1);
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'clock', {
            get: function () {
                return this._clock;
            },
            set: function (t) {
                if (this._clock === t) {
                    return;
                }
                if (this._clock !== null) {
                    this._clock.remove(this);
                }
                this._clock = t;
                if (this._clock) {
                    this._clock.add(this);
                }
                for (var e = 0, a = this._slots; e < a.length; e++) {
                    var r = a[e];
                    var i = r.childArmature;
                    if (i !== null) {
                        i.clock = this._clock;
                    }
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(y.prototype, 'parent', {
            get: function () {
                return this._parent;
            },
            enumerable: true,
            configurable: true,
        });
        y.prototype.getDisplay = function () {
            return this._display;
        };
        return y;
    })(i.BaseObject);
    i.Armature = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (a) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.globalTransformMatrix = new a.Matrix();
            t.global = new a.Transform();
            t.offset = new a.Transform();
            return t;
        }
        t.prototype._onClear = function () {
            this.globalTransformMatrix.identity();
            this.global.identity();
            this.offset.identity();
            this.origin = null;
            this.userData = null;
            this._globalDirty = false;
            this._alpha = 1;
            this._globalAlpha = 1;
            this._armature = null;
        };
        t.prototype.updateGlobalTransform = function () {
            if (this._globalDirty) {
                this._globalDirty = false;
                this.global.fromMatrix(this.globalTransformMatrix);
            }
        };
        Object.defineProperty(t.prototype, 'armature', {
            get: function () {
                return this._armature;
            },
            enumerable: true,
            configurable: true,
        });
        t._helpMatrix = new a.Matrix();
        t._helpTransform = new a.Transform();
        t._helpPoint = new a.Point();
        return t;
    })(a.BaseObject);
    a.TransformObject = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (y) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.animationPose = new y.Transform();
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.Bone]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.offsetMode = 1;
            this.animationPose.identity();
            this._transformDirty = false;
            this._childrenTransformDirty = false;
            this._localDirty = true;
            this._hasConstraint = false;
            this._visible = true;
            this._cachedFrameIndex = -1;
            this._boneData = null;
            this._parent = null;
            this._cachedFrameIndices = null;
        };
        t.prototype._updateGlobalTransformMatrix = function (t) {
            var e = this._boneData;
            var a = this.global;
            var r = this.globalTransformMatrix;
            var i = this.origin;
            var n = this.offset;
            var s = this.animationPose;
            var o = this._parent;
            var l = this._armature.flipX;
            var h = this._armature.flipY === y.DragonBones.yDown;
            var u = o !== null;
            var f = 0;
            if (this.offsetMode === 1) {
                if (i !== null) {
                    a.x = i.x + n.x + s.x;
                    a.scaleX = i.scaleX * n.scaleX * s.scaleX;
                    a.scaleY = i.scaleY * n.scaleY * s.scaleY;
                    if (y.DragonBones.yDown) {
                        a.y = i.y + n.y + s.y;
                        a.skew = i.skew + n.skew + s.skew;
                        a.rotation = i.rotation + n.rotation + s.rotation;
                    } else {
                        a.y = i.y - n.y + s.y;
                        a.skew = i.skew - n.skew + s.skew;
                        a.rotation = i.rotation - n.rotation + s.rotation;
                    }
                } else {
                    a.copyFrom(n);
                    if (!y.DragonBones.yDown) {
                        a.y = -a.y;
                        a.skew = -a.skew;
                        a.rotation = -a.rotation;
                    }
                    a.add(s);
                }
            } else if (this.offsetMode === 0) {
                if (i !== null) {
                    a.copyFrom(i).add(s);
                } else {
                    a.copyFrom(s);
                }
            } else {
                u = false;
                a.copyFrom(n);
                if (!y.DragonBones.yDown) {
                    a.y = -a.y;
                    a.skew = -a.skew;
                    a.rotation = -a.rotation;
                }
            }
            if (u) {
                var _ = o._boneData.type === 1;
                var m = _ ? o._bone : null;
                var p = _ ? o._getGlobalTransformMatrix(a.x, a.y) : o.globalTransformMatrix;
                if (e.inheritScale && (!_ || m !== null)) {
                    if (_) {
                        if (e.inheritRotation) {
                            a.rotation += o.global.rotation;
                        }
                        m.updateGlobalTransform();
                        a.scaleX *= m.global.scaleX;
                        a.scaleY *= m.global.scaleY;
                        p.transformPoint(a.x, a.y, a);
                        a.toMatrix(r);
                        if (e.inheritTranslation) {
                            a.x = r.tx;
                            a.y = r.ty;
                        } else {
                            r.tx = a.x;
                            r.ty = a.y;
                        }
                    } else {
                        if (!e.inheritRotation) {
                            o.updateGlobalTransform();
                            if (l && h) {
                                f = a.rotation - (o.global.rotation + Math.PI);
                            } else if (l) {
                                f = a.rotation + o.global.rotation + Math.PI;
                            } else if (h) {
                                f = a.rotation + o.global.rotation;
                            } else {
                                f = a.rotation - o.global.rotation;
                            }
                            a.rotation = f;
                        }
                        a.toMatrix(r);
                        r.concat(p);
                        if (e.inheritTranslation) {
                            a.x = r.tx;
                            a.y = r.ty;
                        } else {
                            r.tx = a.x;
                            r.ty = a.y;
                        }
                        if (t) {
                            a.fromMatrix(r);
                        } else {
                            this._globalDirty = true;
                        }
                    }
                } else {
                    if (e.inheritTranslation) {
                        var c = a.x;
                        var d = a.y;
                        a.x = p.a * c + p.c * d + p.tx;
                        a.y = p.b * c + p.d * d + p.ty;
                    } else {
                        if (l) {
                            a.x = -a.x;
                        }
                        if (h) {
                            a.y = -a.y;
                        }
                    }
                    if (e.inheritRotation) {
                        o.updateGlobalTransform();
                        if (o.global.scaleX < 0) {
                            f = a.rotation + o.global.rotation + Math.PI;
                        } else {
                            f = a.rotation + o.global.rotation;
                        }
                        if (p.a * p.d - p.b * p.c < 0) {
                            f -= a.rotation * 2;
                            if (l !== h || e.inheritReflection) {
                                a.skew += Math.PI;
                            }
                            if (!y.DragonBones.yDown) {
                                a.skew = -a.skew;
                            }
                        }
                        a.rotation = f;
                    } else if (l || h) {
                        if (l && h) {
                            f = a.rotation + Math.PI;
                        } else {
                            if (l) {
                                f = Math.PI - a.rotation;
                            } else {
                                f = -a.rotation;
                            }
                            a.skew += Math.PI;
                        }
                        a.rotation = f;
                    }
                    a.toMatrix(r);
                }
            } else {
                if (l || h) {
                    if (l) {
                        a.x = -a.x;
                    }
                    if (h) {
                        a.y = -a.y;
                    }
                    if (l && h) {
                        f = a.rotation + Math.PI;
                    } else {
                        if (l) {
                            f = Math.PI - a.rotation;
                        } else {
                            f = -a.rotation;
                        }
                        a.skew += Math.PI;
                    }
                    a.rotation = f;
                }
                a.toMatrix(r);
            }
        };
        t.prototype._updateAlpha = function () {
            if (this._parent !== null) {
                this._globalAlpha = this._alpha * this._parent._globalAlpha;
            } else {
                this._globalAlpha = this._alpha * this._armature._globalAlpha;
            }
        };
        t.prototype.init = function (t, e) {
            if (this._boneData !== null) {
                return;
            }
            this._boneData = t;
            this._armature = e;
            this._alpha = this._boneData.alpha;
            if (this._boneData.parent !== null) {
                this._parent = this._armature.getBone(this._boneData.parent.name);
            }
            this._armature._addBone(this);
            this.origin = this._boneData.transform;
        };
        t.prototype.update = function (t) {
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var e = this._cachedFrameIndices[t];
                if (e >= 0 && this._cachedFrameIndex === e) {
                    this._transformDirty = false;
                } else if (e >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = e;
                } else {
                    if (this._hasConstraint) {
                        for (var a = 0, r = this._armature._constraints; a < r.length; a++) {
                            var i = r[a];
                            if (i._root === this) {
                                i.update();
                            }
                        }
                    }
                    if (
                        this._transformDirty ||
                        (this._parent !== null && this._parent._childrenTransformDirty)
                    ) {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1;
                    } else if (this._cachedFrameIndex >= 0) {
                        this._transformDirty = false;
                        this._cachedFrameIndices[t] = this._cachedFrameIndex;
                    } else {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1;
                    }
                }
            } else {
                if (this._hasConstraint) {
                    for (var n = 0, s = this._armature._constraints; n < s.length; n++) {
                        var i = s[n];
                        if (i._root === this) {
                            i.update();
                        }
                    }
                }
                if (
                    this._transformDirty ||
                    (this._parent !== null && this._parent._childrenTransformDirty)
                ) {
                    t = -1;
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1;
                }
            }
            if (this._transformDirty) {
                this._transformDirty = false;
                this._childrenTransformDirty = true;
                if (this._cachedFrameIndex < 0) {
                    var o = t >= 0;
                    if (this._localDirty) {
                        this._updateGlobalTransformMatrix(o);
                    }
                    if (o && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] =
                            this._armature._armatureData.setCacheFrame(
                                this.globalTransformMatrix,
                                this.global
                            );
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(
                        this.globalTransformMatrix,
                        this.global,
                        this._cachedFrameIndex
                    );
                }
            } else if (this._childrenTransformDirty) {
                this._childrenTransformDirty = false;
            }
            this._localDirty = true;
        };
        t.prototype.updateByConstraint = function () {
            if (this._localDirty) {
                this._localDirty = false;
                if (
                    this._transformDirty ||
                    (this._parent !== null && this._parent._childrenTransformDirty)
                ) {
                    this._updateGlobalTransformMatrix(true);
                }
                this._transformDirty = true;
            }
        };
        t.prototype.invalidUpdate = function () {
            this._transformDirty = true;
        };
        t.prototype.contains = function (t) {
            if (t === this) {
                return false;
            }
            var e = t;
            while (e !== this && e !== null) {
                e = e.parent;
            }
            return e === this;
        };
        Object.defineProperty(t.prototype, 'boneData', {
            get: function () {
                return this._boneData;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'visible', {
            get: function () {
                return this._visible;
            },
            set: function (t) {
                if (this._visible === t) {
                    return;
                }
                this._visible = t;
                for (var e = 0, a = this._armature.getSlots(); e < a.length; e++) {
                    var r = a[e];
                    if (r.parent === this) {
                        r._updateVisible();
                    }
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'name', {
            get: function () {
                return this._boneData.name;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'parent', {
            get: function () {
                return this._parent;
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })(y.TransformObject);
    y.Bone = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (l) {
        __extends(P, l);
        function P() {
            var t = (l !== null && l.apply(this, arguments)) || this;
            t._vertices = [];
            t._deformVertices = [];
            t._hullCache = [];
            t._matrixCahce = [];
            return t;
        }
        P.toString = function () {
            return '[class dragonBones.Surface]';
        };
        P.prototype._onClear = function () {
            l.prototype._onClear.call(this);
            this._dX = 0;
            this._dY = 0;
            this._k = 0;
            this._kX = 0;
            this._kY = 0;
            this._vertices.length = 0;
            this._deformVertices.length = 0;
            this._matrixCahce.length = 0;
            this._hullCache.length = 0;
            this._bone = null;
        };
        P.prototype._getAffineTransform = function (t, e, a, r, i, n, s, o, l, h, u, f, _) {
            var m = s - i;
            var p = o - n;
            var c = l - i;
            var d = h - n;
            u.rotation = Math.atan2(p, m);
            u.skew = Math.atan2(d, c) - Math.PI * 0.5 - u.rotation;
            if (_) {
                u.rotation += Math.PI;
            }
            u.scaleX = Math.sqrt(m * m + p * p) / a;
            u.scaleY = Math.sqrt(c * c + d * d) / r;
            u.toMatrix(f);
            u.x = f.tx = i - (f.a * t + f.c * e);
            u.y = f.ty = n - (f.b * t + f.d * e);
        };
        P.prototype._updateVertices = function () {
            var t = this._armature.armatureData.parent;
            var e = this._boneData.geometry;
            var a = t.intArray;
            var r = t.floatArray;
            var i = a[e.offset + 0];
            var n = a[e.offset + 2];
            var s = this._vertices;
            var o = this._deformVertices;
            if (this._parent !== null) {
                if (this._parent._boneData.type === 1) {
                    for (var l = 0, h = i; l < h; ++l) {
                        var u = l * 2;
                        var f = r[n + u] + o[u];
                        var _ = r[n + u + 1] + o[u + 1];
                        var m = this._parent._getGlobalTransformMatrix(f, _);
                        s[u] = m.a * f + m.c * _ + m.tx;
                        s[u + 1] = m.b * f + m.d * _ + m.ty;
                    }
                } else {
                    var p = this._parent.globalTransformMatrix;
                    for (var l = 0, h = i; l < h; ++l) {
                        var u = l * 2;
                        var f = r[n + u] + o[u];
                        var _ = r[n + u + 1] + o[u + 1];
                        s[u] = p.a * f + p.c * _ + p.tx;
                        s[u + 1] = p.b * f + p.d * _ + p.ty;
                    }
                }
            } else {
                for (var l = 0, h = i; l < h; ++l) {
                    var u = l * 2;
                    s[u] = r[n + u] + o[u];
                    s[u + 1] = r[n + u + 1] + o[u + 1];
                }
            }
        };
        P.prototype._updateGlobalTransformMatrix = function (t) {
            t;
            var e = this._boneData.segmentX * 2;
            var a = this._vertices.length - 2;
            var r = 200;
            var i = this._vertices[0];
            var n = this._vertices[1];
            var s = this._vertices[e];
            var o = this._vertices[e + 1];
            var l = this._vertices[a];
            var h = this._vertices[a + 1];
            var u = this._vertices[a - e];
            var f = this._vertices[a - e + 1];
            var _ = i + (l - i) * 0.5;
            var m = n + (h - n) * 0.5;
            var p = s + (u - s) * 0.5;
            var c = o + (f - o) * 0.5;
            var d = _ + (p - _) * 0.5;
            var y = m + (c - m) * 0.5;
            var v = s + (l - s) * 0.5;
            var g = o + (h - o) * 0.5;
            var D = u + (l - u) * 0.5;
            var T = f + (h - f) * 0.5;
            this._getAffineTransform(
                0,
                0,
                r,
                r,
                d,
                y,
                v,
                g,
                D,
                T,
                this.global,
                this.globalTransformMatrix,
                false
            );
            this._globalDirty = false;
        };
        P.prototype._getGlobalTransformMatrix = function (t, e) {
            var a = 200;
            var r = 1e3;
            if (t < -r || r < t || e < -r || r < e) {
                return this.globalTransformMatrix;
            }
            var i = false;
            var n = this._boneData;
            var s = n.segmentX;
            var o = n.segmentY;
            var l = n.segmentX * 2;
            var h = this._dX;
            var u = this._dY;
            var f = Math.floor((t + a) / h);
            var _ = Math.floor((e + a) / u);
            var m = 0;
            var p = f * h - a;
            var c = _ * u - a;
            var d = this._matrixCahce;
            var y = P._helpMatrix;
            if (t < -a) {
                if (e < -a || e >= a) {
                    return this.globalTransformMatrix;
                }
                i = e > this._kX * (t + a) + c;
                m = ((s * o + s + o + o + _) * 2 + (i ? 1 : 0)) * 7;
                if (d[m] > 0) {
                    y.copyFromArray(d, m + 1);
                } else {
                    var v = _ * (l + 2);
                    var g = this._hullCache[4];
                    var D = this._hullCache[5];
                    var T = this._hullCache[2] - (o - _) * g;
                    var b = this._hullCache[3] - (o - _) * D;
                    var A = this._vertices;
                    if (i) {
                        this._getAffineTransform(
                            -a,
                            c + u,
                            r - a,
                            u,
                            A[v + l + 2],
                            A[v + l + 3],
                            T + g,
                            b + D,
                            A[v],
                            A[v + 1],
                            P._helpTransform,
                            y,
                            true
                        );
                    } else {
                        this._getAffineTransform(
                            -r,
                            c,
                            r - a,
                            u,
                            T,
                            b,
                            A[v],
                            A[v + 1],
                            T + g,
                            b + D,
                            P._helpTransform,
                            y,
                            false
                        );
                    }
                    d[m] = 1;
                    d[m + 1] = y.a;
                    d[m + 2] = y.b;
                    d[m + 3] = y.c;
                    d[m + 4] = y.d;
                    d[m + 5] = y.tx;
                    d[m + 6] = y.ty;
                }
            } else if (t >= a) {
                if (e < -a || e >= a) {
                    return this.globalTransformMatrix;
                }
                i = e > this._kX * (t - r) + c;
                m = ((s * o + s + _) * 2 + (i ? 1 : 0)) * 7;
                if (d[m] > 0) {
                    y.copyFromArray(d, m + 1);
                } else {
                    var v = (_ + 1) * (l + 2) - 2;
                    var g = this._hullCache[4];
                    var D = this._hullCache[5];
                    var T = this._hullCache[0] + _ * g;
                    var b = this._hullCache[1] + _ * D;
                    var A = this._vertices;
                    if (i) {
                        this._getAffineTransform(
                            r,
                            c + u,
                            r - a,
                            u,
                            T + g,
                            b + D,
                            A[v + l + 2],
                            A[v + l + 3],
                            T,
                            b,
                            P._helpTransform,
                            y,
                            true
                        );
                    } else {
                        this._getAffineTransform(
                            a,
                            c,
                            r - a,
                            u,
                            A[v],
                            A[v + 1],
                            T,
                            b,
                            A[v + l + 2],
                            A[v + l + 3],
                            P._helpTransform,
                            y,
                            false
                        );
                    }
                    d[m] = 1;
                    d[m + 1] = y.a;
                    d[m + 2] = y.b;
                    d[m + 3] = y.c;
                    d[m + 4] = y.d;
                    d[m + 5] = y.tx;
                    d[m + 6] = y.ty;
                }
            } else if (e < -a) {
                if (t < -a || t >= a) {
                    return this.globalTransformMatrix;
                }
                i = e > this._kY * (t - p - h) - r;
                m = ((s * o + f) * 2 + (i ? 1 : 0)) * 7;
                if (d[m] > 0) {
                    y.copyFromArray(d, m + 1);
                } else {
                    var v = f * 2;
                    var g = this._hullCache[10];
                    var D = this._hullCache[11];
                    var T = this._hullCache[8] + f * g;
                    var b = this._hullCache[9] + f * D;
                    var A = this._vertices;
                    if (i) {
                        this._getAffineTransform(
                            p + h,
                            -a,
                            h,
                            r - a,
                            A[v + 2],
                            A[v + 3],
                            A[v],
                            A[v + 1],
                            T + g,
                            b + D,
                            P._helpTransform,
                            y,
                            true
                        );
                    } else {
                        this._getAffineTransform(
                            p,
                            -r,
                            h,
                            r - a,
                            T,
                            b,
                            T + g,
                            b + D,
                            A[v],
                            A[v + 1],
                            P._helpTransform,
                            y,
                            false
                        );
                    }
                    d[m] = 1;
                    d[m + 1] = y.a;
                    d[m + 2] = y.b;
                    d[m + 3] = y.c;
                    d[m + 4] = y.d;
                    d[m + 5] = y.tx;
                    d[m + 6] = y.ty;
                }
            } else if (e >= a) {
                if (t < -a || t >= a) {
                    return this.globalTransformMatrix;
                }
                i = e > this._kY * (t - p - h) + a;
                m = ((s * o + s + o + f) * 2 + (i ? 1 : 0)) * 7;
                if (d[m] > 0) {
                    y.copyFromArray(d, m + 1);
                } else {
                    var v = o * (l + 2) + f * 2;
                    var g = this._hullCache[10];
                    var D = this._hullCache[11];
                    var T = this._hullCache[6] - (s - f) * g;
                    var b = this._hullCache[7] - (s - f) * D;
                    var A = this._vertices;
                    if (i) {
                        this._getAffineTransform(
                            p + h,
                            r,
                            h,
                            r - a,
                            T + g,
                            b + D,
                            T,
                            b,
                            A[v + 2],
                            A[v + 3],
                            P._helpTransform,
                            y,
                            true
                        );
                    } else {
                        this._getAffineTransform(
                            p,
                            a,
                            h,
                            r - a,
                            A[v],
                            A[v + 1],
                            A[v + 2],
                            A[v + 3],
                            T,
                            b,
                            P._helpTransform,
                            y,
                            false
                        );
                    }
                    d[m] = 1;
                    d[m + 1] = y.a;
                    d[m + 2] = y.b;
                    d[m + 3] = y.c;
                    d[m + 4] = y.d;
                    d[m + 5] = y.tx;
                    d[m + 6] = y.ty;
                }
            } else {
                i = e > this._k * (t - p - h) + c;
                m = ((s * _ + f) * 2 + (i ? 1 : 0)) * 7;
                if (d[m] > 0) {
                    y.copyFromArray(d, m + 1);
                } else {
                    var v = f * 2 + _ * (l + 2);
                    var A = this._vertices;
                    if (i) {
                        this._getAffineTransform(
                            p + h,
                            c + u,
                            h,
                            u,
                            A[v + l + 4],
                            A[v + l + 5],
                            A[v + l + 2],
                            A[v + l + 3],
                            A[v + 2],
                            A[v + 3],
                            P._helpTransform,
                            y,
                            true
                        );
                    } else {
                        this._getAffineTransform(
                            p,
                            c,
                            h,
                            u,
                            A[v],
                            A[v + 1],
                            A[v + 2],
                            A[v + 3],
                            A[v + l + 2],
                            A[v + l + 3],
                            P._helpTransform,
                            y,
                            false
                        );
                    }
                    d[m] = 1;
                    d[m + 1] = y.a;
                    d[m + 2] = y.b;
                    d[m + 3] = y.c;
                    d[m + 4] = y.d;
                    d[m + 5] = y.tx;
                    d[m + 6] = y.ty;
                }
            }
            return y;
        };
        P.prototype.init = function (t, e) {
            if (this._boneData !== null) {
                return;
            }
            l.prototype.init.call(this, t, e);
            var a = t.segmentX;
            var r = t.segmentY;
            var i = this._armature.armatureData.parent.intArray[t.geometry.offset + 0];
            var n = 1e3;
            var s = 200;
            this._dX = (s * 2) / a;
            this._dY = (s * 2) / r;
            this._k = -this._dY / this._dX;
            this._kX = -this._dY / (n - s);
            this._kY = -(n - s) / this._dX;
            this._vertices.length = i * 2;
            this._deformVertices.length = i * 2;
            this._matrixCahce.length = (a * r + a * 2 + r * 2) * 2 * 7;
            this._hullCache.length = 10;
            for (var o = 0; o < i * 2; ++o) {
                this._deformVertices[o] = 0;
            }
            if (this._parent !== null) {
                if (this._parent.boneData.type === 0) {
                    this._bone = this._parent;
                } else {
                    this._bone = this._parent._bone;
                }
            }
        };
        P.prototype.update = function (t) {
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var e = this._cachedFrameIndices[t];
                if (e >= 0 && this._cachedFrameIndex === e) {
                    this._transformDirty = false;
                } else if (e >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = e;
                } else {
                    if (this._hasConstraint) {
                        for (var a = 0, r = this._armature._constraints; a < r.length; a++) {
                            var i = r[a];
                            if (i._root === this) {
                                i.update();
                            }
                        }
                    }
                    if (
                        this._transformDirty ||
                        (this._parent !== null && this._parent._childrenTransformDirty)
                    ) {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1;
                    } else if (this._cachedFrameIndex >= 0) {
                        this._transformDirty = false;
                        this._cachedFrameIndices[t] = this._cachedFrameIndex;
                    } else {
                        this._transformDirty = true;
                        this._cachedFrameIndex = -1;
                    }
                }
            } else {
                if (this._hasConstraint) {
                    for (var n = 0, s = this._armature._constraints; n < s.length; n++) {
                        var i = s[n];
                        if (i._root === this) {
                            i.update();
                        }
                    }
                }
                if (
                    this._transformDirty ||
                    (this._parent !== null && this._parent._childrenTransformDirty)
                ) {
                    t = -1;
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1;
                }
            }
            if (this._transformDirty) {
                this._transformDirty = false;
                this._childrenTransformDirty = true;
                for (var o = 0, l = this._matrixCahce.length; o < l; o += 7) {
                    this._matrixCahce[o] = -1;
                }
                this._updateVertices();
                if (this._cachedFrameIndex < 0) {
                    var h = t >= 0;
                    if (this._localDirty) {
                        this._updateGlobalTransformMatrix(h);
                    }
                    if (h && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] =
                            this._armature._armatureData.setCacheFrame(
                                this.globalTransformMatrix,
                                this.global
                            );
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(
                        this.globalTransformMatrix,
                        this.global,
                        this._cachedFrameIndex
                    );
                }
                var u = 1e3;
                var f = 200;
                var _ = 2 * this.global.x;
                var m = 2 * this.global.y;
                var p = P._helpPoint;
                this.globalTransformMatrix.transformPoint(u, -f, p);
                this._hullCache[0] = p.x;
                this._hullCache[1] = p.y;
                this._hullCache[2] = _ - p.x;
                this._hullCache[3] = m - p.y;
                this.globalTransformMatrix.transformPoint(0, this._dY, p, true);
                this._hullCache[4] = p.x;
                this._hullCache[5] = p.y;
                this.globalTransformMatrix.transformPoint(f, u, p);
                this._hullCache[6] = p.x;
                this._hullCache[7] = p.y;
                this._hullCache[8] = _ - p.x;
                this._hullCache[9] = m - p.y;
                this.globalTransformMatrix.transformPoint(this._dX, 0, p, true);
                this._hullCache[10] = p.x;
                this._hullCache[11] = p.y;
            } else if (this._childrenTransformDirty) {
                this._childrenTransformDirty = false;
            }
            this._localDirty = true;
        };
        return P;
    })(t.Bone);
    t.Surface = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (c) {
    var r = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.deformVertices = [];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.DisplayFrame]';
        };
        t.prototype._onClear = function () {
            this.rawDisplayData = null;
            this.displayData = null;
            this.textureData = null;
            this.display = null;
            this.deformVertices.length = 0;
        };
        t.prototype.updateDeformVertices = function () {
            if (this.rawDisplayData === null || this.deformVertices.length !== 0) {
                return;
            }
            var t;
            if (this.rawDisplayData.type === 2) {
                t = this.rawDisplayData.geometry;
            } else if (this.rawDisplayData.type === 4) {
                t = this.rawDisplayData.geometry;
            } else {
                return;
            }
            var e = 0;
            if (t.weight !== null) {
                e = t.weight.count * 2;
            } else {
                e = t.data.intArray[t.offset + 0] * 2;
            }
            this.deformVertices.length = e;
            for (var a = 0, r = this.deformVertices.length; a < r; ++a) {
                this.deformVertices[a] = 0;
            }
        };
        t.prototype.getGeometryData = function () {
            if (this.displayData !== null) {
                if (this.displayData.type === 2) {
                    return this.displayData.geometry;
                }
                if (this.displayData.type === 4) {
                    return this.displayData.geometry;
                }
            }
            if (this.rawDisplayData !== null) {
                if (this.rawDisplayData.type === 2) {
                    return this.rawDisplayData.geometry;
                }
                if (this.rawDisplayData.type === 4) {
                    return this.rawDisplayData.geometry;
                }
            }
            return null;
        };
        t.prototype.getBoundingBox = function () {
            if (this.displayData !== null && this.displayData.type === 3) {
                return this.displayData.boundingBox;
            }
            if (this.rawDisplayData !== null && this.rawDisplayData.type === 3) {
                return this.rawDisplayData.boundingBox;
            }
            return null;
        };
        t.prototype.getTextureData = function () {
            if (this.displayData !== null) {
                if (this.displayData.type === 0) {
                    return this.displayData.texture;
                }
                if (this.displayData.type === 2) {
                    return this.displayData.texture;
                }
            }
            if (this.textureData !== null) {
                return this.textureData;
            }
            if (this.rawDisplayData !== null) {
                if (this.rawDisplayData.type === 0) {
                    return this.rawDisplayData.texture;
                }
                if (this.rawDisplayData.type === 2) {
                    return this.rawDisplayData.texture;
                }
            }
            return null;
        };
        return t;
    })(c.BaseObject);
    c.DisplayFrame = r;
    var t = (function (l) {
        __extends(p, l);
        function p() {
            var t = (l !== null && l.apply(this, arguments)) || this;
            t._localMatrix = new c.Matrix();
            t._colorTransform = new c.ColorTransform();
            t._displayFrames = [];
            t._geometryBones = [];
            t._rawDisplay = null;
            t._meshDisplay = null;
            t._display = null;
            return t;
        }
        p.prototype._onClear = function () {
            l.prototype._onClear.call(this);
            var t = [];
            for (var e = 0, a = this._displayFrames; e < a.length; e++) {
                var r = a[e];
                var i = r.display;
                if (i !== this._rawDisplay && i !== this._meshDisplay && t.indexOf(i) < 0) {
                    t.push(i);
                }
                r.returnToPool();
            }
            for (var n = 0, s = t; n < s.length; n++) {
                var o = s[n];
                if (o instanceof c.Armature) {
                    o.dispose();
                } else {
                    this._disposeDisplay(o, true);
                }
            }
            if (this._meshDisplay !== null && this._meshDisplay !== this._rawDisplay) {
                this._disposeDisplay(this._meshDisplay, false);
            }
            if (this._rawDisplay !== null) {
                this._disposeDisplay(this._rawDisplay, false);
            }
            this.displayController = null;
            this._displayDataDirty = false;
            this._displayDirty = false;
            this._geometryDirty = false;
            this._textureDirty = false;
            this._visibleDirty = false;
            this._blendModeDirty = false;
            this._zOrderDirty = false;
            this._colorDirty = false;
            this._verticesDirty = false;
            this._transformDirty = false;
            this._visible = true;
            this._blendMode = 0;
            this._displayIndex = -1;
            this._animationDisplayIndex = -1;
            this._zOrder = 0;
            this._zIndex = 0;
            this._cachedFrameIndex = -1;
            this._pivotX = 0;
            this._pivotY = 0;
            this._localMatrix.identity();
            this._colorTransform.identity();
            this._displayFrames.length = 0;
            this._geometryBones.length = 0;
            this._slotData = null;
            this._displayFrame = null;
            this._geometryData = null;
            this._boundingBoxData = null;
            this._textureData = null;
            this._rawDisplay = null;
            this._meshDisplay = null;
            this._display = null;
            this._childArmature = null;
            this._parent = null;
            this._cachedFrameIndices = null;
        };
        p.prototype._hasDisplay = function (t) {
            for (var e = 0, a = this._displayFrames; e < a.length; e++) {
                var r = a[e];
                if (r.display === t) {
                    return true;
                }
            }
            return false;
        };
        p.prototype._isBonesUpdate = function () {
            for (var t = 0, e = this._geometryBones; t < e.length; t++) {
                var a = e[t];
                if (a !== null && a._childrenTransformDirty) {
                    return true;
                }
            }
            return false;
        };
        p.prototype._updateAlpha = function () {
            var t = this._alpha * this._parent._globalAlpha;
            if (this._globalAlpha !== t) {
                this._globalAlpha = t;
                this._colorDirty = true;
            }
        };
        p.prototype._updateDisplayData = function () {
            var t = this._displayFrame;
            var e = this._geometryData;
            var a = this._textureData;
            var r = null;
            var i = null;
            this._displayFrame = null;
            this._geometryData = null;
            this._boundingBoxData = null;
            this._textureData = null;
            if (this._displayIndex >= 0 && this._displayIndex < this._displayFrames.length) {
                this._displayFrame = this._displayFrames[this._displayIndex];
                r = this._displayFrame.rawDisplayData;
                i = this._displayFrame.displayData;
                this._geometryData = this._displayFrame.getGeometryData();
                this._boundingBoxData = this._displayFrame.getBoundingBox();
                this._textureData = this._displayFrame.getTextureData();
            }
            if (this._displayFrame !== t || this._geometryData !== e || this._textureData !== a) {
                if (this._geometryData === null && this._textureData !== null) {
                    var n = i !== null && i.type === 0 ? i : r;
                    var s = this._textureData.parent.scale * this._armature._armatureData.scale;
                    var o = this._textureData.frame;
                    this._pivotX = n.pivot.x;
                    this._pivotY = n.pivot.y;
                    var l = o !== null ? o : this._textureData.region;
                    var h = l.width;
                    var u = l.height;
                    if (this._textureData.rotated && o === null) {
                        h = l.height;
                        u = l.width;
                    }
                    this._pivotX *= h * s;
                    this._pivotY *= u * s;
                    if (o !== null) {
                        this._pivotX += o.x * s;
                        this._pivotY += o.y * s;
                    }
                    if (r !== null && n !== r) {
                        r.transform.toMatrix(p._helpMatrix);
                        p._helpMatrix.invert();
                        p._helpMatrix.transformPoint(0, 0, p._helpPoint);
                        this._pivotX -= p._helpPoint.x;
                        this._pivotY -= p._helpPoint.y;
                        n.transform.toMatrix(p._helpMatrix);
                        p._helpMatrix.invert();
                        p._helpMatrix.transformPoint(0, 0, p._helpPoint);
                        this._pivotX += p._helpPoint.x;
                        this._pivotY += p._helpPoint.y;
                    }
                    if (!c.DragonBones.yDown) {
                        this._pivotY =
                            (this._textureData.rotated
                                ? this._textureData.region.width
                                : this._textureData.region.height) *
                                s -
                            this._pivotY;
                    }
                } else {
                    this._pivotX = 0;
                    this._pivotY = 0;
                }
                if (r !== null) {
                    this.origin = r.transform;
                } else if (i !== null) {
                    this.origin = i.transform;
                } else {
                    this.origin = null;
                }
                if (this.origin !== null) {
                    this.global.copyFrom(this.origin).add(this.offset).toMatrix(this._localMatrix);
                } else {
                    this.global.copyFrom(this.offset).toMatrix(this._localMatrix);
                }
                if (this._geometryData !== e) {
                    this._geometryDirty = true;
                    this._verticesDirty = true;
                    if (this._geometryData !== null) {
                        this._geometryBones.length = 0;
                        if (this._geometryData.weight !== null) {
                            for (
                                var f = 0, _ = this._geometryData.weight.bones.length;
                                f < _;
                                ++f
                            ) {
                                var m = this._armature.getBone(
                                    this._geometryData.weight.bones[f].name
                                );
                                this._geometryBones.push(m);
                            }
                        }
                    } else {
                        this._geometryBones.length = 0;
                        this._geometryData = null;
                    }
                }
                this._textureDirty = this._textureData !== a;
                this._transformDirty = true;
            }
        };
        p.prototype._updateDisplay = function () {
            var t = this._display !== null ? this._display : this._rawDisplay;
            var e = this._childArmature;
            if (this._displayFrame !== null) {
                this._display = this._displayFrame.display;
                if (this._display !== null && this._display instanceof c.Armature) {
                    this._childArmature = this._display;
                    this._display = this._childArmature.display;
                } else {
                    this._childArmature = null;
                }
            } else {
                this._display = null;
                this._childArmature = null;
            }
            var a = this._display !== null ? this._display : this._rawDisplay;
            if (a !== t) {
                this._textureDirty = true;
                this._visibleDirty = true;
                this._blendModeDirty = true;
                this._colorDirty = true;
                this._transformDirty = true;
                this._onUpdateDisplay();
                this._replaceDisplay(t);
            }
            if (this._childArmature !== e) {
                if (e !== null) {
                    e._parent = null;
                    e.clock = null;
                    if (e.inheritAnimation) {
                        e.animation.reset();
                    }
                }
                if (this._childArmature !== null) {
                    this._childArmature._parent = this;
                    this._childArmature.clock = this._armature.clock;
                    if (this._childArmature.inheritAnimation) {
                        if (this._childArmature.cacheFrameRate === 0) {
                            var r = this._armature.cacheFrameRate;
                            if (r !== 0) {
                                this._childArmature.cacheFrameRate = r;
                            }
                        }
                        if (this._displayFrame !== null) {
                            var i = null;
                            var n =
                                this._displayFrame.displayData !== null
                                    ? this._displayFrame.displayData
                                    : this._displayFrame.rawDisplayData;
                            if (n !== null && n.type === 1) {
                                i = n.actions;
                            }
                            if (i !== null && i.length > 0) {
                                for (var s = 0, o = i; s < o.length; s++) {
                                    var l = o[s];
                                    var h = c.BaseObject.borrowObject(c.EventObject);
                                    c.EventObject.actionDataToInstance(l, h, this._armature);
                                    h.slot = this;
                                    this._armature._bufferAction(h, false);
                                }
                            } else {
                                this._childArmature.animation.play();
                            }
                        }
                    }
                }
            }
        };
        p.prototype._updateGlobalTransformMatrix = function (t) {
            var e =
                this._parent._boneData.type === 0
                    ? this._parent.globalTransformMatrix
                    : this._parent._getGlobalTransformMatrix(this.global.x, this.global.y);
            this.globalTransformMatrix.copyFrom(this._localMatrix);
            this.globalTransformMatrix.concat(e);
            if (t) {
                this.global.fromMatrix(this.globalTransformMatrix);
            } else {
                this._globalDirty = true;
            }
        };
        p.prototype._setDisplayIndex = function (t, e) {
            if (e === void 0) {
                e = false;
            }
            if (e) {
                if (this._animationDisplayIndex === t) {
                    return;
                }
                this._animationDisplayIndex = t;
            }
            if (this._displayIndex === t) {
                return;
            }
            this._displayIndex =
                t < this._displayFrames.length ? t : this._displayFrames.length - 1;
            this._displayDataDirty = true;
            this._displayDirty =
                this._displayIndex < 0 ||
                this._display !== this._displayFrames[this._displayIndex].display;
        };
        p.prototype._setZOrder = function (t) {
            if (this._zOrder === t) {
            }
            this._zOrder = t;
            this._zOrderDirty = true;
            return this._zOrderDirty;
        };
        p.prototype._setColor = function (t) {
            this._colorTransform.copyFrom(t);
            return (this._colorDirty = true);
        };
        p.prototype.init = function (t, e, a, r) {
            if (this._slotData !== null) {
                return;
            }
            this._slotData = t;
            this._colorDirty = true;
            this._blendModeDirty = true;
            this._blendMode = this._slotData.blendMode;
            this._zOrder = this._slotData.zOrder;
            this._zIndex = this._slotData.zIndex;
            this._alpha = this._slotData.alpha;
            this._colorTransform.copyFrom(this._slotData.color);
            this._rawDisplay = a;
            this._meshDisplay = r;
            this._armature = e;
            var i = this._armature.getBone(this._slotData.parent.name);
            if (i !== null) {
                this._parent = i;
            } else {
            }
            this._armature._addSlot(this);
            this._initDisplay(this._rawDisplay, false);
            if (this._rawDisplay !== this._meshDisplay) {
                this._initDisplay(this._meshDisplay, false);
            }
            this._onUpdateDisplay();
            this._addDisplay();
        };
        p.prototype.update = function (t) {
            if (this._displayDataDirty) {
                this._updateDisplayData();
                this._displayDataDirty = false;
            }
            if (this._displayDirty) {
                this._updateDisplay();
                this._displayDirty = false;
            }
            if (this._geometryDirty || this._textureDirty) {
                if (
                    this._display === null ||
                    this._display === this._rawDisplay ||
                    this._display === this._meshDisplay
                ) {
                    this._updateFrame();
                }
                this._geometryDirty = false;
                this._textureDirty = false;
            }
            if (this._display === null) {
                return;
            }
            if (this._visibleDirty) {
                this._updateVisible();
                this._visibleDirty = false;
            }
            if (this._blendModeDirty) {
                this._updateBlendMode();
                this._blendModeDirty = false;
            }
            if (this._colorDirty) {
                this._updateColor();
                this._colorDirty = false;
            }
            if (this._zOrderDirty) {
                this._updateZOrder();
                this._zOrderDirty = false;
            }
            if (this._geometryData !== null && this._display === this._meshDisplay) {
                var e = this._geometryData.weight !== null;
                var a = this._parent._boneData.type !== 0;
                if (
                    this._verticesDirty ||
                    (e && this._isBonesUpdate()) ||
                    (a && this._parent._childrenTransformDirty)
                ) {
                    this._verticesDirty = false;
                    this._updateMesh();
                }
                if (e || a) {
                    return;
                }
            }
            if (t >= 0 && this._cachedFrameIndices !== null) {
                var r = this._cachedFrameIndices[t];
                if (r >= 0 && this._cachedFrameIndex === r) {
                    this._transformDirty = false;
                } else if (r >= 0) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = r;
                } else if (this._transformDirty || this._parent._childrenTransformDirty) {
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1;
                } else if (this._cachedFrameIndex >= 0) {
                    this._transformDirty = false;
                    this._cachedFrameIndices[t] = this._cachedFrameIndex;
                } else {
                    this._transformDirty = true;
                    this._cachedFrameIndex = -1;
                }
            } else if (this._transformDirty || this._parent._childrenTransformDirty) {
                t = -1;
                this._transformDirty = true;
                this._cachedFrameIndex = -1;
            }
            if (this._transformDirty) {
                if (this._cachedFrameIndex < 0) {
                    var i = t >= 0;
                    this._updateGlobalTransformMatrix(i);
                    if (i && this._cachedFrameIndices !== null) {
                        this._cachedFrameIndex = this._cachedFrameIndices[t] =
                            this._armature._armatureData.setCacheFrame(
                                this.globalTransformMatrix,
                                this.global
                            );
                    }
                } else {
                    this._armature._armatureData.getCacheFrame(
                        this.globalTransformMatrix,
                        this.global,
                        this._cachedFrameIndex
                    );
                }
                this._updateTransform();
                this._transformDirty = false;
            }
        };
        p.prototype.invalidUpdate = function () {
            this._displayDataDirty = true;
            this._displayDirty = true;
            this._transformDirty = true;
        };
        p.prototype.updateTransformAndMatrix = function () {
            if (this._transformDirty) {
                this._updateGlobalTransformMatrix(false);
                this._transformDirty = false;
            }
        };
        p.prototype.replaceRawDisplayData = function (t, e) {
            if (e === void 0) {
                e = -1;
            }
            if (e < 0) {
                e = this._displayIndex < 0 ? 0 : this._displayIndex;
            } else if (e >= this._displayFrames.length) {
                return;
            }
            var a = this._displayFrames[e];
            if (a.rawDisplayData !== t) {
                a.deformVertices.length = 0;
                a.rawDisplayData = t;
                if (a.rawDisplayData === null) {
                    var r = this._armature._armatureData.defaultSkin;
                    if (r !== null) {
                        var i = r.getDisplays(this._slotData.name);
                        if (i !== null && e < i.length) {
                            a.rawDisplayData = i[e];
                        }
                    }
                }
                if (e === this._displayIndex) {
                    this._displayDataDirty = true;
                }
            }
        };
        p.prototype.replaceDisplayData = function (t, e) {
            if (e === void 0) {
                e = -1;
            }
            if (e < 0) {
                e = this._displayIndex < 0 ? 0 : this._displayIndex;
            } else if (e >= this._displayFrames.length) {
                return;
            }
            var a = this._displayFrames[e];
            if (a.displayData !== t && a.rawDisplayData !== t) {
                a.displayData = t;
                if (e === this._displayIndex) {
                    this._displayDataDirty = true;
                }
            }
        };
        p.prototype.replaceTextureData = function (t, e) {
            if (e === void 0) {
                e = -1;
            }
            if (e < 0) {
                e = this._displayIndex < 0 ? 0 : this._displayIndex;
            } else if (e >= this._displayFrames.length) {
                return;
            }
            var a = this._displayFrames[e];
            if (a.textureData !== t) {
                a.textureData = t;
                if (e === this._displayIndex) {
                    this._displayDataDirty = true;
                }
            }
        };
        p.prototype.replaceDisplay = function (t, e) {
            if (e === void 0) {
                e = -1;
            }
            if (e < 0) {
                e = this._displayIndex < 0 ? 0 : this._displayIndex;
            } else if (e >= this._displayFrames.length) {
                return;
            }
            var a = this._displayFrames[e];
            if (a.display !== t) {
                var r = a.display;
                a.display = t;
                if (
                    r !== null &&
                    r !== this._rawDisplay &&
                    r !== this._meshDisplay &&
                    !this._hasDisplay(r)
                ) {
                    if (r instanceof c.Armature) {
                    } else {
                        this._disposeDisplay(r, true);
                    }
                }
                if (
                    t !== null &&
                    t !== this._rawDisplay &&
                    t !== this._meshDisplay &&
                    !this._hasDisplay(r) &&
                    !(t instanceof c.Armature)
                ) {
                    this._initDisplay(t, true);
                }
                if (e === this._displayIndex) {
                    this._displayDirty = true;
                }
            }
        };
        p.prototype.containsPoint = function (t, e) {
            if (this._boundingBoxData === null) {
                return false;
            }
            this.updateTransformAndMatrix();
            p._helpMatrix.copyFrom(this.globalTransformMatrix);
            p._helpMatrix.invert();
            p._helpMatrix.transformPoint(t, e, p._helpPoint);
            return this._boundingBoxData.containsPoint(p._helpPoint.x, p._helpPoint.y);
        };
        p.prototype.intersectsSegment = function (t, e, a, r, i, n, s) {
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = null;
            }
            if (s === void 0) {
                s = null;
            }
            if (this._boundingBoxData === null) {
                return 0;
            }
            this.updateTransformAndMatrix();
            p._helpMatrix.copyFrom(this.globalTransformMatrix);
            p._helpMatrix.invert();
            p._helpMatrix.transformPoint(t, e, p._helpPoint);
            t = p._helpPoint.x;
            e = p._helpPoint.y;
            p._helpMatrix.transformPoint(a, r, p._helpPoint);
            a = p._helpPoint.x;
            r = p._helpPoint.y;
            var o = this._boundingBoxData.intersectsSegment(t, e, a, r, i, n, s);
            if (o > 0) {
                if (o === 1 || o === 2) {
                    if (i !== null) {
                        this.globalTransformMatrix.transformPoint(i.x, i.y, i);
                        if (n !== null) {
                            n.x = i.x;
                            n.y = i.y;
                        }
                    } else if (n !== null) {
                        this.globalTransformMatrix.transformPoint(n.x, n.y, n);
                    }
                } else {
                    if (i !== null) {
                        this.globalTransformMatrix.transformPoint(i.x, i.y, i);
                    }
                    if (n !== null) {
                        this.globalTransformMatrix.transformPoint(n.x, n.y, n);
                    }
                }
                if (s !== null) {
                    this.globalTransformMatrix.transformPoint(
                        Math.cos(s.x),
                        Math.sin(s.x),
                        p._helpPoint,
                        true
                    );
                    s.x = Math.atan2(p._helpPoint.y, p._helpPoint.x);
                    this.globalTransformMatrix.transformPoint(
                        Math.cos(s.y),
                        Math.sin(s.y),
                        p._helpPoint,
                        true
                    );
                    s.y = Math.atan2(p._helpPoint.y, p._helpPoint.x);
                }
            }
            return o;
        };
        p.prototype.getDisplayFrameAt = function (t) {
            return this._displayFrames[t];
        };
        Object.defineProperty(p.prototype, 'visible', {
            get: function () {
                return this._visible;
            },
            set: function (t) {
                if (this._visible === t) {
                    return;
                }
                this._visible = t;
                this._updateVisible();
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'displayFrameCount', {
            get: function () {
                return this._displayFrames.length;
            },
            set: function (t) {
                var e = this._displayFrames.length;
                if (e < t) {
                    this._displayFrames.length = t;
                    for (var a = e; a < t; ++a) {
                        this._displayFrames[a] = c.BaseObject.borrowObject(r);
                    }
                } else if (e > t) {
                    for (var a = e - 1; a < t; --a) {
                        this.replaceDisplay(null, a);
                        this._displayFrames[a].returnToPool();
                    }
                    this._displayFrames.length = t;
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'displayIndex', {
            get: function () {
                return this._displayIndex;
            },
            set: function (t) {
                this._setDisplayIndex(t);
                this.update(-1);
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'name', {
            get: function () {
                return this._slotData.name;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'displayList', {
            get: function () {
                var t = new Array();
                for (var e = 0, a = this._displayFrames; e < a.length; e++) {
                    var r = a[e];
                    t.push(r.display);
                }
                return t;
            },
            set: function (t) {
                this.displayFrameCount = t.length;
                var e = 0;
                for (var a = 0, r = t; a < r.length; a++) {
                    var i = r[a];
                    this.replaceDisplay(i, e++);
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'slotData', {
            get: function () {
                return this._slotData;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'boundingBoxData', {
            get: function () {
                return this._boundingBoxData;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'rawDisplay', {
            get: function () {
                return this._rawDisplay;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'meshDisplay', {
            get: function () {
                return this._meshDisplay;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'display', {
            get: function () {
                return this._display;
            },
            set: function (t) {
                if (this._display === t) {
                    return;
                }
                if (this._displayFrames.length === 0) {
                    this.displayFrameCount = 1;
                    this._displayIndex = 0;
                }
                this.replaceDisplay(t, this._displayIndex);
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'childArmature', {
            get: function () {
                return this._childArmature;
            },
            set: function (t) {
                if (this._childArmature === t) {
                    return;
                }
                this.display = t;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(p.prototype, 'parent', {
            get: function () {
                return this._parent;
            },
            enumerable: true,
            configurable: true,
        });
        p.prototype.getDisplay = function () {
            return this._display;
        };
        p.prototype.setDisplay = function (t) {
            this.display = t;
        };
        return p;
    })(c.TransformObject);
    c.Slot = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (X) {
    var t = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.prototype._onClear = function () {
            this._armature = null;
            this._target = null;
            this._root = null;
            this._bone = null;
        };
        Object.defineProperty(e.prototype, 'name', {
            get: function () {
                return this._constraintData.name;
            },
            enumerable: true,
            configurable: true,
        });
        e._helpMatrix = new X.Matrix();
        e._helpTransform = new X.Transform();
        e._helpPoint = new X.Point();
        return e;
    })(X.BaseObject);
    X.Constraint = t;
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.IKConstraint]';
        };
        e.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this._scaleEnabled = false;
            this._bendPositive = false;
            this._weight = 1;
            this._constraintData = null;
        };
        e.prototype._computeA = function () {
            var t = this._target.global;
            var e = this._root.global;
            var a = this._root.globalTransformMatrix;
            var r = Math.atan2(t.y - e.y, t.x - e.x);
            if (e.scaleX < 0) {
                r += Math.PI;
            }
            e.rotation += X.Transform.normalizeRadian(r - e.rotation) * this._weight;
            e.toMatrix(a);
        };
        e.prototype._computeB = function () {
            var t = this._bone._boneData.length;
            var e = this._root;
            var a = this._target.global;
            var r = e.global;
            var i = this._bone.global;
            var n = this._bone.globalTransformMatrix;
            var s = n.a * t;
            var o = n.b * t;
            var l = s * s + o * o;
            var h = Math.sqrt(l);
            var u = i.x - r.x;
            var f = i.y - r.y;
            var _ = u * u + f * f;
            var m = Math.sqrt(_);
            var p = i.rotation;
            var c = r.rotation;
            var d = Math.atan2(f, u);
            u = a.x - r.x;
            f = a.y - r.y;
            var y = u * u + f * f;
            var v = Math.sqrt(y);
            var g = 0;
            if (h + m <= v || v + h <= m || v + m <= h) {
                g = Math.atan2(a.y - r.y, a.x - r.x);
                if (h + m <= v) {
                } else if (m < h) {
                    g += Math.PI;
                }
            } else {
                var D = (_ - l + y) / (2 * y);
                var T = Math.sqrt(_ - D * D * y) / v;
                var b = r.x + u * D;
                var A = r.y + f * D;
                var P = -f * T;
                var S = u * T;
                var O = false;
                var x = e.parent;
                if (x !== null) {
                    var B = x.globalTransformMatrix;
                    O = B.a * B.d - B.b * B.c < 0;
                }
                if (O !== this._bendPositive) {
                    i.x = b - P;
                    i.y = A - S;
                } else {
                    i.x = b + P;
                    i.y = A + S;
                }
                g = Math.atan2(i.y - r.y, i.x - r.x);
            }
            var E = X.Transform.normalizeRadian(g - d);
            r.rotation = c + E * this._weight;
            r.toMatrix(e.globalTransformMatrix);
            var I = d + E * this._weight;
            i.x = r.x + Math.cos(I) * m;
            i.y = r.y + Math.sin(I) * m;
            var M = Math.atan2(a.y - i.y, a.x - i.x);
            if (i.scaleX < 0) {
                M += Math.PI;
            }
            i.rotation = r.rotation + p - c + X.Transform.normalizeRadian(M - E - p) * this._weight;
            i.toMatrix(n);
        };
        e.prototype.init = function (t, e) {
            if (this._constraintData !== null) {
                return;
            }
            this._constraintData = t;
            this._armature = e;
            this._target = this._armature.getBone(this._constraintData.target.name);
            this._root = this._armature.getBone(this._constraintData.root.name);
            this._bone =
                this._constraintData.bone !== null
                    ? this._armature.getBone(this._constraintData.bone.name)
                    : null;
            {
                var a = this._constraintData;
                this._scaleEnabled = a.scaleEnabled;
                this._bendPositive = a.bendPositive;
                this._weight = a.weight;
            }
            this._root._hasConstraint = true;
        };
        e.prototype.update = function () {
            this._root.updateByConstraint();
            if (this._bone !== null) {
                this._bone.updateByConstraint();
                this._computeB();
            } else {
                this._computeA();
            }
        };
        e.prototype.invalidUpdate = function () {
            this._root.invalidUpdate();
            if (this._bone !== null) {
                this._bone.invalidUpdate();
            }
        };
        return e;
    })(t);
    X.IKConstraint = e;
    var a = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._bones = [];
            t._spaces = [];
            t._positions = [];
            t._curves = [];
            t._boneLengths = [];
            t._pathGlobalVertices = [];
            t._segments = [10];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.PathConstraint]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            this.dirty = false;
            this.pathOffset = 0;
            this.position = 0;
            this.spacing = 0;
            this.rotateOffset = 0;
            this.rotateMix = 1;
            this.translateMix = 1;
            this._pathSlot = null;
            this._bones.length = 0;
            this._spaces.length = 0;
            this._positions.length = 0;
            this._curves.length = 0;
            this._boneLengths.length = 0;
            this._pathGlobalVertices.length = 0;
        };
        t.prototype._updatePathVertices = function (t) {
            var e = this._armature;
            var a = e.armatureData.parent;
            var r = e.armatureData.scale;
            var i = a.intArray;
            var n = a.floatArray;
            var s = t.offset;
            var o = i[s + 0];
            var l = i[s + 2];
            this._pathGlobalVertices.length = o * 2;
            var h = t.weight;
            if (h === null) {
                var u = this._pathSlot.parent;
                u.updateByConstraint();
                var f = u.globalTransformMatrix;
                for (var _ = 0, m = l; _ < o; _ += 2) {
                    var p = n[m++] * r;
                    var c = n[m++] * r;
                    var d = f.a * p + f.c * c + f.tx;
                    var y = f.b * p + f.d * c + f.ty;
                    this._pathGlobalVertices[_] = d;
                    this._pathGlobalVertices[_ + 1] = y;
                }
                return;
            }
            var v = this._pathSlot._geometryBones;
            var g = h.bones.length;
            var D = h.offset;
            var T = i[D + 1];
            var b = T;
            var A = D + 2 + g;
            for (var _ = 0, P = 0; _ < o; _++) {
                var S = i[A++];
                var O = 0,
                    x = 0;
                for (var B = 0, E = S; B < E; B++) {
                    var I = i[A++];
                    var M = v[I];
                    if (M === null) {
                        continue;
                    }
                    M.updateByConstraint();
                    var f = M.globalTransformMatrix;
                    var F = n[b++];
                    var p = n[b++] * r;
                    var c = n[b++] * r;
                    O += (f.a * p + f.c * c + f.tx) * F;
                    x += (f.b * p + f.d * c + f.ty) * F;
                }
                this._pathGlobalVertices[P++] = O;
                this._pathGlobalVertices[P++] = x;
            }
        };
        t.prototype._computeVertices = function (t, e, a, r) {
            for (var i = a, n = t; i < e; i += 2) {
                r[i] = this._pathGlobalVertices[n++];
                r[i + 1] = this._pathGlobalVertices[n++];
            }
        };
        t.prototype._computeBezierCurve = function (t, e, a, r, i) {
            var n = this._armature;
            var s = n.armatureData.parent.intArray;
            var o = s[t.geometry.offset + 0];
            var l = this._positions;
            var h = this._spaces;
            var u = t.closed;
            var f = Array();
            var _ = o * 2;
            var m = _ / 6;
            var p = -1;
            var c = this.position;
            l.length = e * 3 + 2;
            var d = 0;
            if (!t.constantSpeed) {
                var y = t.curveLengths;
                m -= u ? 1 : 2;
                d = y[m];
                if (r) {
                    c *= d;
                }
                if (i) {
                    for (var v = 0; v < e; v++) {
                        h[v] *= d;
                    }
                }
                f.length = 8;
                for (var v = 0, g = 0, D = 0; v < e; v++, g += 3) {
                    var T = h[v];
                    c += T;
                    if (u) {
                        c %= d;
                        if (c < 0) {
                            c += d;
                        }
                        D = 0;
                    } else if (c < 0) {
                        continue;
                    } else if (c > d) {
                        continue;
                    }
                    var b = 0;
                    for (; ; D++) {
                        var A = y[D];
                        if (c > A) {
                            continue;
                        }
                        if (D === 0) {
                            b = c / A;
                        } else {
                            var P = y[D - 1];
                            b = (c - P) / (A - P);
                        }
                        break;
                    }
                    if (D !== p) {
                        p = D;
                        if (u && D === m) {
                            this._computeVertices(_ - 4, 4, 0, f);
                            this._computeVertices(0, 4, 4, f);
                        } else {
                            this._computeVertices(D * 6 + 2, 8, 0, f);
                        }
                    }
                    this.addCurvePosition(
                        b,
                        f[0],
                        f[1],
                        f[2],
                        f[3],
                        f[4],
                        f[5],
                        f[6],
                        f[7],
                        l,
                        g,
                        a
                    );
                }
                return;
            }
            if (u) {
                _ += 2;
                f.length = o;
                this._computeVertices(2, _ - 4, 0, f);
                this._computeVertices(0, 2, _ - 4, f);
                f[_ - 2] = f[0];
                f[_ - 1] = f[1];
            } else {
                m--;
                _ -= 4;
                f.length = _;
                this._computeVertices(2, _, 0, f);
            }
            var S = new Array(m);
            d = 0;
            var O = f[0],
                x = f[1],
                B = 0,
                E = 0,
                I = 0,
                M = 0,
                F = 0,
                w = 0;
            var C, N, R, k, j, L, V, Y;
            for (var v = 0, X = 2; v < m; v++, X += 6) {
                B = f[X];
                E = f[X + 1];
                I = f[X + 2];
                M = f[X + 3];
                F = f[X + 4];
                w = f[X + 5];
                C = (O - B * 2 + I) * 0.1875;
                N = (x - E * 2 + M) * 0.1875;
                R = ((B - I) * 3 - O + F) * 0.09375;
                k = ((E - M) * 3 - x + w) * 0.09375;
                j = C * 2 + R;
                L = N * 2 + k;
                V = (B - O) * 0.75 + C + R * 0.16666667;
                Y = (E - x) * 0.75 + N + k * 0.16666667;
                d += Math.sqrt(V * V + Y * Y);
                V += j;
                Y += L;
                j += R;
                L += k;
                d += Math.sqrt(V * V + Y * Y);
                V += j;
                Y += L;
                d += Math.sqrt(V * V + Y * Y);
                V += j + R;
                Y += L + k;
                d += Math.sqrt(V * V + Y * Y);
                S[v] = d;
                O = F;
                x = w;
            }
            if (r) {
                c *= d;
            }
            if (i) {
                for (var v = 0; v < e; v++) {
                    h[v] *= d;
                }
            }
            var U = this._segments;
            var G = 0;
            for (var v = 0, g = 0, D = 0, H = 0; v < e; v++, g += 3) {
                var T = h[v];
                c += T;
                var z = c;
                if (u) {
                    z %= d;
                    if (z < 0) z += d;
                    D = 0;
                } else if (z < 0) {
                    continue;
                } else if (z > d) {
                    continue;
                }
                for (; ; D++) {
                    var W = S[D];
                    if (z > W) continue;
                    if (D === 0) z /= W;
                    else {
                        var K = S[D - 1];
                        z = (z - K) / (W - K);
                    }
                    break;
                }
                if (D !== p) {
                    p = D;
                    var Z = D * 6;
                    O = f[Z];
                    x = f[Z + 1];
                    B = f[Z + 2];
                    E = f[Z + 3];
                    I = f[Z + 4];
                    M = f[Z + 5];
                    F = f[Z + 6];
                    w = f[Z + 7];
                    C = (O - B * 2 + I) * 0.03;
                    N = (x - E * 2 + M) * 0.03;
                    R = ((B - I) * 3 - O + F) * 0.006;
                    k = ((E - M) * 3 - x + w) * 0.006;
                    j = C * 2 + R;
                    L = N * 2 + k;
                    V = (B - O) * 0.3 + C + R * 0.16666667;
                    Y = (E - x) * 0.3 + N + k * 0.16666667;
                    G = Math.sqrt(V * V + Y * Y);
                    U[0] = G;
                    for (Z = 1; Z < 8; Z++) {
                        V += j;
                        Y += L;
                        j += R;
                        L += k;
                        G += Math.sqrt(V * V + Y * Y);
                        U[Z] = G;
                    }
                    V += j;
                    Y += L;
                    G += Math.sqrt(V * V + Y * Y);
                    U[8] = G;
                    V += j + R;
                    Y += L + k;
                    G += Math.sqrt(V * V + Y * Y);
                    U[9] = G;
                    H = 0;
                }
                z *= G;
                for (; ; H++) {
                    var q = U[H];
                    if (z > q) continue;
                    if (H === 0) z /= q;
                    else {
                        var K = U[H - 1];
                        z = H + (z - K) / (q - K);
                    }
                    break;
                }
                this.addCurvePosition(z * 0.1, O, x, B, E, I, M, F, w, l, g, a);
            }
        };
        t.prototype.addCurvePosition = function (t, e, a, r, i, n, s, o, l, h, u, f) {
            if (t === 0) {
                h[u] = e;
                h[u + 1] = a;
                h[u + 2] = 0;
                return;
            }
            if (t === 1) {
                h[u] = o;
                h[u + 1] = l;
                h[u + 2] = 0;
                return;
            }
            var _ = 1 - t;
            var m = _ * _;
            var p = t * t;
            var c = m * _;
            var d = m * t * 3;
            var y = _ * p * 3;
            var v = t * p;
            var g = c * e + d * r + y * n + v * o;
            var D = c * a + d * i + y * s + v * l;
            h[u] = g;
            h[u + 1] = D;
            if (f) {
                h[u + 2] = Math.atan2(D - (c * a + d * i + y * s), g - (c * e + d * r + y * n));
            } else {
                h[u + 2] = 0;
            }
        };
        t.prototype.init = function (t, e) {
            this._constraintData = t;
            this._armature = e;
            var a = t;
            this.pathOffset = a.pathDisplayData.geometry.offset;
            this.position = a.position;
            this.spacing = a.spacing;
            this.rotateOffset = a.rotateOffset;
            this.rotateMix = a.rotateMix;
            this.translateMix = a.translateMix;
            this._root = this._armature.getBone(a.root.name);
            this._target = this._armature.getBone(a.target.name);
            this._pathSlot = this._armature.getSlot(a.pathSlot.name);
            for (var r = 0, i = a.bones.length; r < i; r++) {
                var n = this._armature.getBone(a.bones[r].name);
                if (n !== null) {
                    this._bones.push(n);
                }
            }
            if (a.rotateMode === 2) {
                this._boneLengths.length = this._bones.length;
            }
            this._root._hasConstraint = true;
        };
        t.prototype.update = function () {
            var t = this._pathSlot;
            if (t._geometryData === null || t._geometryData.offset !== this.pathOffset) {
                return;
            }
            var e = this._constraintData;
            var a = false;
            if (this._root._childrenTransformDirty) {
                this._updatePathVertices(t._geometryData);
                a = true;
            } else if (t._verticesDirty || t._isBonesUpdate()) {
                this._updatePathVertices(t._geometryData);
                t._verticesDirty = false;
                a = true;
            }
            if (!a && !this.dirty) {
                return;
            }
            var r = e.positionMode;
            var i = e.spacingMode;
            var n = e.rotateMode;
            var s = this._bones;
            var o = i === 0;
            var l = n === 2;
            var h = n === 0;
            var u = s.length;
            var f = h ? u : u + 1;
            var _ = this.spacing;
            var m = this._spaces;
            m.length = f;
            if (l || o) {
                m[0] = 0;
                for (var p = 0, c = f - 1; p < c; p++) {
                    var d = s[p];
                    d.updateByConstraint();
                    var y = d._boneData.length;
                    var v = d.globalTransformMatrix;
                    var g = y * v.a;
                    var D = y * v.b;
                    var T = Math.sqrt(g * g + D * D);
                    if (l) {
                        this._boneLengths[p] = T;
                    }
                    m[p + 1] = ((y + _) * T) / y;
                }
            } else {
                for (var p = 0; p < f; p++) {
                    m[p] = _;
                }
            }
            this._computeBezierCurve(t._displayFrame.rawDisplayData, f, h, r === 1, i === 2);
            var b = this._positions;
            var A = this.rotateOffset;
            var P = b[0],
                S = b[1];
            var O;
            if (A === 0) {
                O = n === 1;
            } else {
                O = false;
                var d = t.parent;
                if (d !== null) {
                    var v = d.globalTransformMatrix;
                    A *= v.a * v.d - v.b * v.c > 0 ? X.Transform.DEG_RAD : -X.Transform.DEG_RAD;
                }
            }
            var x = this.rotateMix;
            var B = this.translateMix;
            for (var p = 0, E = 3; p < u; p++, E += 3) {
                var d = s[p];
                d.updateByConstraint();
                var v = d.globalTransformMatrix;
                v.tx += (P - v.tx) * B;
                v.ty += (S - v.ty) * B;
                var g = b[E],
                    D = b[E + 1];
                var I = g - P,
                    M = D - S;
                if (l) {
                    var F = this._boneLengths[p];
                    var w = (Math.sqrt(I * I + M * M) / F - 1) * x + 1;
                    v.a *= w;
                    v.b *= w;
                }
                P = g;
                S = D;
                if (x > 0) {
                    var C = v.a,
                        N = v.b,
                        R = v.c,
                        k = v.d,
                        j = void 0,
                        L = void 0,
                        V = void 0;
                    if (h) {
                        j = b[E - 1];
                    } else {
                        j = Math.atan2(M, I);
                    }
                    j -= Math.atan2(N, C);
                    if (O) {
                        L = Math.cos(j);
                        V = Math.sin(j);
                        var Y = d._boneData.length;
                        P += (Y * (L * C - V * N) - I) * x;
                        S += (Y * (V * C + L * N) - M) * x;
                    } else {
                        j += A;
                    }
                    if (j > X.Transform.PI) {
                        j -= X.Transform.PI_D;
                    } else if (j < -X.Transform.PI) {
                        j += X.Transform.PI_D;
                    }
                    j *= x;
                    L = Math.cos(j);
                    V = Math.sin(j);
                    v.a = L * C - V * N;
                    v.b = V * C + L * N;
                    v.c = L * R - V * k;
                    v.d = V * R + L * k;
                }
                d.global.fromMatrix(v);
            }
            this.dirty = false;
        };
        t.prototype.invalidUpdate = function () {};
        return t;
    })(t);
    X.PathConstraint = a;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t(t) {
            if (t === void 0) {
                t = 0;
            }
            this.time = 0;
            this.timeScale = 1;
            this._systemTime = 0;
            this._animatebles = [];
            this._clock = null;
            this.time = t;
            this._systemTime = new Date().getTime() * 0.001;
        }
        t.prototype.advanceTime = function (t) {
            if (t !== t) {
                t = 0;
            }
            var e = Date.now() * 0.001;
            if (t < 0) {
                t = e - this._systemTime;
            }
            this._systemTime = e;
            if (this.timeScale !== 1) {
                t *= this.timeScale;
            }
            if (t === 0) {
                return;
            }
            if (t < 0) {
                this.time -= t;
            } else {
                this.time += t;
            }
            var a = 0,
                r = 0,
                i = this._animatebles.length;
            for (; a < i; ++a) {
                var n = this._animatebles[a];
                if (n !== null) {
                    if (r > 0) {
                        this._animatebles[a - r] = n;
                        this._animatebles[a] = null;
                    }
                    n.advanceTime(t);
                } else {
                    r++;
                }
            }
            if (r > 0) {
                i = this._animatebles.length;
                for (; a < i; ++a) {
                    var s = this._animatebles[a];
                    if (s !== null) {
                        this._animatebles[a - r] = s;
                    } else {
                        r++;
                    }
                }
                this._animatebles.length -= r;
            }
        };
        t.prototype.contains = function (t) {
            if (t === this) {
                return false;
            }
            var e = t;
            while (e !== this && e !== null) {
                e = e.clock;
            }
            return e === this;
        };
        t.prototype.add = function (t) {
            if (this._animatebles.indexOf(t) < 0) {
                this._animatebles.push(t);
                t.clock = this;
            }
        };
        t.prototype.remove = function (t) {
            var e = this._animatebles.indexOf(t);
            if (e >= 0) {
                this._animatebles[e] = null;
                t.clock = null;
            }
        };
        t.prototype.clear = function () {
            for (var t = 0, e = this._animatebles; t < e.length; t++) {
                var a = e[t];
                if (a !== null) {
                    a.clock = null;
                }
            }
        };
        Object.defineProperty(t.prototype, 'clock', {
            get: function () {
                return this._clock;
            },
            set: function (t) {
                if (this._clock === t) {
                    return;
                }
                if (this._clock !== null) {
                    this._clock.remove(this);
                }
                this._clock = t;
                if (this._clock !== null) {
                    this._clock.add(this);
                }
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })();
    t.WorldClock = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (g) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._animationNames = [];
            t._animationStates = [];
            t._animations = {};
            t._blendStates = {};
            t._animationConfig = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.Animation]';
        };
        t.prototype._onClear = function () {
            for (var t = 0, e = this._animationStates; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            for (var r in this._animations) {
                delete this._animations[r];
            }
            for (var r in this._blendStates) {
                var i = this._blendStates[r];
                for (var n in i) {
                    i[n].returnToPool();
                }
                delete this._blendStates[r];
            }
            if (this._animationConfig !== null) {
                this._animationConfig.returnToPool();
            }
            this.timeScale = 1;
            this._animationDirty = false;
            this._inheritTimeScale = 1;
            this._animationNames.length = 0;
            this._animationStates.length = 0;
            this._armature = null;
            this._animationConfig = null;
            this._lastAnimationState = null;
        };
        t.prototype._fadeOut = function (t) {
            switch (t.fadeOutMode) {
                case 1:
                    for (var e = 0, a = this._animationStates; e < a.length; e++) {
                        var r = a[e];
                        if (r._parent !== null) {
                            continue;
                        }
                        if (r.layer === t.layer) {
                            r.fadeOut(t.fadeOutTime, t.pauseFadeOut);
                        }
                    }
                    break;
                case 2:
                    for (var i = 0, n = this._animationStates; i < n.length; i++) {
                        var r = n[i];
                        if (r._parent !== null) {
                            continue;
                        }
                        if (r.group === t.group) {
                            r.fadeOut(t.fadeOutTime, t.pauseFadeOut);
                        }
                    }
                    break;
                case 3:
                    for (var s = 0, o = this._animationStates; s < o.length; s++) {
                        var r = o[s];
                        if (r._parent !== null) {
                            continue;
                        }
                        if (r.layer === t.layer && r.group === t.group) {
                            r.fadeOut(t.fadeOutTime, t.pauseFadeOut);
                        }
                    }
                    break;
                case 4:
                    for (var l = 0, h = this._animationStates; l < h.length; l++) {
                        var r = h[l];
                        if (r._parent !== null) {
                            continue;
                        }
                        r.fadeOut(t.fadeOutTime, t.pauseFadeOut);
                    }
                    break;
                case 5:
                default:
                    break;
            }
        };
        t.prototype.init = function (t) {
            if (this._armature !== null) {
                return;
            }
            this._armature = t;
            this._animationConfig = g.BaseObject.borrowObject(g.AnimationConfig);
        };
        t.prototype.advanceTime = function (t) {
            if (t < 0) {
                t = -t;
            }
            if (this._armature.inheritAnimation && this._armature._parent !== null) {
                this._inheritTimeScale =
                    this._armature._parent._armature.animation._inheritTimeScale * this.timeScale;
            } else {
                this._inheritTimeScale = this.timeScale;
            }
            if (this._inheritTimeScale !== 1) {
                t *= this._inheritTimeScale;
            }
            for (var e in this._blendStates) {
                var a = this._blendStates[e];
                for (var r in a) {
                    a[r].reset();
                }
            }
            var i = this._animationStates.length;
            if (i === 1) {
                var n = this._animationStates[0];
                if (n._fadeState > 0 && n._subFadeState > 0) {
                    this._armature._dragonBones.bufferObject(n);
                    this._animationStates.length = 0;
                    this._lastAnimationState = null;
                } else {
                    var s = n.animationData;
                    var o = s.cacheFrameRate;
                    if (this._animationDirty && o > 0) {
                        this._animationDirty = false;
                        for (var l = 0, h = this._armature.getBones(); l < h.length; l++) {
                            var u = h[l];
                            u._cachedFrameIndices = s.getBoneCachedFrameIndices(u.name);
                        }
                        for (var f = 0, _ = this._armature.getSlots(); f < _.length; f++) {
                            var m = _[f];
                            if (m.displayFrameCount > 0) {
                                var p = m.getDisplayFrameAt(0).rawDisplayData;
                                if (
                                    p !== null &&
                                    p.parent === this._armature.armatureData.defaultSkin
                                ) {
                                    m._cachedFrameIndices = s.getSlotCachedFrameIndices(m.name);
                                    continue;
                                }
                            }
                            m._cachedFrameIndices = null;
                        }
                    }
                    n.advanceTime(t, o);
                }
            } else if (i > 1) {
                for (var c = 0, d = 0; c < i; ++c) {
                    var n = this._animationStates[c];
                    if (n._fadeState > 0 && n._subFadeState > 0) {
                        d++;
                        this._armature._dragonBones.bufferObject(n);
                        this._animationDirty = true;
                        if (this._lastAnimationState === n) {
                            this._lastAnimationState = null;
                        }
                    } else {
                        if (d > 0) {
                            this._animationStates[c - d] = n;
                        }
                        n.advanceTime(t, 0);
                    }
                    if (c === i - 1 && d > 0) {
                        this._animationStates.length -= d;
                        if (this._lastAnimationState === null && this._animationStates.length > 0) {
                            this._lastAnimationState =
                                this._animationStates[this._animationStates.length - 1];
                        }
                    }
                }
                this._armature._cacheFrameIndex = -1;
            } else {
                this._armature._cacheFrameIndex = -1;
            }
        };
        t.prototype.reset = function () {
            for (var t = 0, e = this._animationStates; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            this._animationDirty = false;
            this._animationConfig.clear();
            this._animationStates.length = 0;
            this._lastAnimationState = null;
        };
        t.prototype.stop = function (t) {
            if (t === void 0) {
                t = null;
            }
            if (t !== null) {
                var e = this.getState(t);
                if (e !== null) {
                    e.stop();
                }
            } else {
                for (var a = 0, r = this._animationStates; a < r.length; a++) {
                    var e = r[a];
                    e.stop();
                }
            }
        };
        t.prototype.playConfig = function (t) {
            var e = t.animation;
            if (!(e in this._animations)) {
                console.warn(
                    'Non-existent animation.\n',
                    'DragonBones name: ' + this._armature.armatureData.parent.name,
                    'Armature name: ' + this._armature.name,
                    'Animation name: ' + e
                );
                return null;
            }
            var a = this._animations[e];
            if (t.fadeOutMode === 5) {
                for (var r = 0, i = this._animationStates; r < i.length; r++) {
                    var n = i[r];
                    if (n._fadeState < 1 && n.layer === t.layer && n.animationData === a) {
                        return n;
                    }
                }
            }
            if (this._animationStates.length === 0) {
                t.fadeInTime = 0;
            } else if (t.fadeInTime < 0) {
                t.fadeInTime = a.fadeInTime;
            }
            if (t.fadeOutTime < 0) {
                t.fadeOutTime = t.fadeInTime;
            }
            if (t.timeScale <= -100) {
                t.timeScale = 1 / a.scale;
            }
            if (a.frameCount > 0) {
                if (t.position < 0) {
                    t.position %= a.duration;
                    t.position = a.duration - t.position;
                } else if (t.position === a.duration) {
                    t.position -= 1e-6;
                } else if (t.position > a.duration) {
                    t.position %= a.duration;
                }
                if (t.duration > 0 && t.position + t.duration > a.duration) {
                    t.duration = a.duration - t.position;
                }
                if (t.playTimes < 0) {
                    t.playTimes = a.playTimes;
                }
            } else {
                t.playTimes = 1;
                t.position = 0;
                if (t.duration > 0) {
                    t.duration = 0;
                }
            }
            if (t.duration === 0) {
                t.duration = -1;
            }
            this._fadeOut(t);
            var s = g.BaseObject.borrowObject(g.AnimationState);
            s.init(this._armature, a, t);
            this._animationDirty = true;
            this._armature._cacheFrameIndex = -1;
            if (this._animationStates.length > 0) {
                var o = false;
                for (var l = 0, h = this._animationStates.length; l < h; ++l) {
                    if (s.layer > this._animationStates[l].layer) {
                        o = true;
                        this._animationStates.splice(l, 0, s);
                        break;
                    } else if (l !== h - 1 && s.layer > this._animationStates[l + 1].layer) {
                        o = true;
                        this._animationStates.splice(l + 1, 0, s);
                        break;
                    }
                }
                if (!o) {
                    this._animationStates.push(s);
                }
            } else {
                this._animationStates.push(s);
            }
            for (var u = 0, f = this._armature.getSlots(); u < f.length; u++) {
                var _ = f[u];
                var m = _.childArmature;
                if (
                    m !== null &&
                    m.inheritAnimation &&
                    m.animation.hasAnimation(e) &&
                    m.animation.getState(e) === null
                ) {
                    m.animation.fadeIn(e);
                }
            }
            for (var p in a.animationTimelines) {
                var c = this.fadeIn(p, 0, 1, s.layer, '', 5);
                if (c === null) {
                    continue;
                }
                var d = a.animationTimelines[p];
                c.actionEnabled = false;
                c.resetToPose = false;
                c.stop();
                s.addState(c, d);
                var y = this._animationStates.indexOf(s);
                var v = this._animationStates.indexOf(c);
                if (v < y) {
                    this._animationStates.splice(y, 1);
                    this._animationStates.splice(v, 0, s);
                }
            }
            this._lastAnimationState = s;
            return s;
        };
        t.prototype.play = function (t, e) {
            if (t === void 0) {
                t = null;
            }
            if (e === void 0) {
                e = -1;
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = e;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t !== null ? t : '';
            if (t !== null && t.length > 0) {
                this.playConfig(this._animationConfig);
            } else if (this._lastAnimationState === null) {
                var a = this._armature.armatureData.defaultAnimation;
                if (a !== null) {
                    this._animationConfig.animation = a.name;
                    this.playConfig(this._animationConfig);
                }
            } else if (
                !this._lastAnimationState.isPlaying &&
                !this._lastAnimationState.isCompleted
            ) {
                this._lastAnimationState.play();
            } else {
                this._animationConfig.animation = this._lastAnimationState.name;
                this.playConfig(this._animationConfig);
            }
            return this._lastAnimationState;
        };
        t.prototype.fadeIn = function (t, e, a, r, i, n) {
            if (e === void 0) {
                e = -1;
            }
            if (a === void 0) {
                a = -1;
            }
            if (r === void 0) {
                r = 0;
            }
            if (i === void 0) {
                i = null;
            }
            if (n === void 0) {
                n = 3;
            }
            this._animationConfig.clear();
            this._animationConfig.fadeOutMode = n;
            this._animationConfig.playTimes = a;
            this._animationConfig.layer = r;
            this._animationConfig.fadeInTime = e;
            this._animationConfig.animation = t;
            this._animationConfig.group = i !== null ? i : '';
            return this.playConfig(this._animationConfig);
        };
        t.prototype.gotoAndPlayByTime = function (t, e, a) {
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = -1;
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.position = e;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            return this.playConfig(this._animationConfig);
        };
        t.prototype.gotoAndPlayByFrame = function (t, e, a) {
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = -1;
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            var r = t in this._animations ? this._animations[t] : null;
            if (r !== null) {
                this._animationConfig.position =
                    r.frameCount > 0 ? (r.duration * e) / r.frameCount : 0;
            }
            return this.playConfig(this._animationConfig);
        };
        t.prototype.gotoAndPlayByProgress = function (t, e, a) {
            if (e === void 0) {
                e = 0;
            }
            if (a === void 0) {
                a = -1;
            }
            this._animationConfig.clear();
            this._animationConfig.resetToPose = true;
            this._animationConfig.playTimes = a;
            this._animationConfig.fadeInTime = 0;
            this._animationConfig.animation = t;
            var r = t in this._animations ? this._animations[t] : null;
            if (r !== null) {
                this._animationConfig.position = r.duration * (e > 0 ? e : 0);
            }
            return this.playConfig(this._animationConfig);
        };
        t.prototype.gotoAndStopByTime = function (t, e) {
            if (e === void 0) {
                e = 0;
            }
            var a = this.gotoAndPlayByTime(t, e, 1);
            if (a !== null) {
                a.stop();
            }
            return a;
        };
        t.prototype.gotoAndStopByFrame = function (t, e) {
            if (e === void 0) {
                e = 0;
            }
            var a = this.gotoAndPlayByFrame(t, e, 1);
            if (a !== null) {
                a.stop();
            }
            return a;
        };
        t.prototype.gotoAndStopByProgress = function (t, e) {
            if (e === void 0) {
                e = 0;
            }
            var a = this.gotoAndPlayByProgress(t, e, 1);
            if (a !== null) {
                a.stop();
            }
            return a;
        };
        t.prototype.getBlendState = function (t, e, a) {
            if (!(t in this._blendStates)) {
                this._blendStates[t] = {};
            }
            var r = this._blendStates[t];
            if (!(e in r)) {
                var i = (r[e] = g.BaseObject.borrowObject(g.BlendState));
                i.target = a;
            }
            return r[e];
        };
        t.prototype.getState = function (t, e) {
            if (e === void 0) {
                e = -1;
            }
            var a = this._animationStates.length;
            while (a--) {
                var r = this._animationStates[a];
                if (r.name === t && (e < 0 || r.layer === e)) {
                    return r;
                }
            }
            return null;
        };
        t.prototype.hasAnimation = function (t) {
            return t in this._animations;
        };
        t.prototype.getStates = function () {
            return this._animationStates;
        };
        Object.defineProperty(t.prototype, 'isPlaying', {
            get: function () {
                for (var t = 0, e = this._animationStates; t < e.length; t++) {
                    var a = e[t];
                    if (a.isPlaying) {
                        return true;
                    }
                }
                return false;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'isCompleted', {
            get: function () {
                for (var t = 0, e = this._animationStates; t < e.length; t++) {
                    var a = e[t];
                    if (!a.isCompleted) {
                        return false;
                    }
                }
                return this._animationStates.length > 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'lastAnimationName', {
            get: function () {
                return this._lastAnimationState !== null ? this._lastAnimationState.name : '';
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'animationNames', {
            get: function () {
                return this._animationNames;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'animations', {
            get: function () {
                return this._animations;
            },
            set: function (t) {
                if (this._animations === t) {
                    return;
                }
                this._animationNames.length = 0;
                for (var e in this._animations) {
                    delete this._animations[e];
                }
                for (var e in t) {
                    this._animationNames.push(e);
                    this._animations[e] = t[e];
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'animationConfig', {
            get: function () {
                this._animationConfig.clear();
                return this._animationConfig;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'lastAnimationState', {
            get: function () {
                return this._lastAnimationState;
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })(g.BaseObject);
    g.Animation = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (L) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._boneMask = [];
            t._boneTimelines = [];
            t._boneBlendTimelines = [];
            t._slotTimelines = [];
            t._slotBlendTimelines = [];
            t._constraintTimelines = [];
            t._animationTimelines = [];
            t._poseTimelines = [];
            t._actionTimeline = null;
            t._zOrderTimeline = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationState]';
        };
        t.prototype._onClear = function () {
            for (var t = 0, e = this._boneTimelines; t < e.length; t++) {
                var a = e[t];
                a.returnToPool();
            }
            for (var r = 0, i = this._boneBlendTimelines; r < i.length; r++) {
                var a = i[r];
                a.returnToPool();
            }
            for (var n = 0, s = this._slotTimelines; n < s.length; n++) {
                var a = s[n];
                a.returnToPool();
            }
            for (var o = 0, l = this._slotBlendTimelines; o < l.length; o++) {
                var a = l[o];
                a.returnToPool();
            }
            for (var h = 0, u = this._constraintTimelines; h < u.length; h++) {
                var a = u[h];
                a.returnToPool();
            }
            for (var f = 0, _ = this._animationTimelines; f < _.length; f++) {
                var a = _[f];
                var m = a.target;
                if (m._parent === this) {
                    m._fadeState = 1;
                    m._subFadeState = 1;
                    m._parent = null;
                }
                a.returnToPool();
            }
            if (this._actionTimeline !== null) {
                this._actionTimeline.returnToPool();
            }
            if (this._zOrderTimeline !== null) {
                this._zOrderTimeline.returnToPool();
            }
            this.actionEnabled = false;
            this.additive = false;
            this.displayControl = false;
            this.resetToPose = false;
            this.blendType = 0;
            this.playTimes = 1;
            this.layer = 0;
            this.timeScale = 1;
            this._weight = 1;
            this.parameterX = 0;
            this.parameterY = 0;
            this.positionX = 0;
            this.positionY = 0;
            this.autoFadeOutTime = 0;
            this.fadeTotalTime = 0;
            this.name = '';
            this.group = '';
            this._timelineDirty = 2;
            this._playheadState = 0;
            this._fadeState = -1;
            this._subFadeState = -1;
            this._position = 0;
            this._duration = 0;
            this._fadeTime = 0;
            this._time = 0;
            this._fadeProgress = 0;
            this._weightResult = 0;
            this._boneMask.length = 0;
            this._boneTimelines.length = 0;
            this._boneBlendTimelines.length = 0;
            this._slotTimelines.length = 0;
            this._slotBlendTimelines.length = 0;
            this._constraintTimelines.length = 0;
            this._animationTimelines.length = 0;
            this._poseTimelines.length = 0;
            this._animationData = null;
            this._armature = null;
            this._actionTimeline = null;
            this._zOrderTimeline = null;
            this._activeChildA = null;
            this._activeChildB = null;
            this._parent = null;
        };
        t.prototype._updateTimelines = function () {
            {
                for (var t = 0, e = this._armature._constraints; t < e.length; t++) {
                    var a = e[t];
                    var r = this._animationData.getConstraintTimelines(a.name);
                    if (r !== null) {
                        for (var i = 0, n = r; i < n.length; i++) {
                            var s = n[i];
                            switch (s.type) {
                                case 30: {
                                    var o = L.BaseObject.borrowObject(L.IKConstraintTimelineState);
                                    o.target = a;
                                    o.init(this._armature, this, s);
                                    this._constraintTimelines.push(o);
                                    break;
                                }
                                default:
                                    break;
                            }
                        }
                    } else if (this.resetToPose) {
                        var o = L.BaseObject.borrowObject(L.IKConstraintTimelineState);
                        o.target = a;
                        o.init(this._armature, this, null);
                        this._constraintTimelines.push(o);
                        this._poseTimelines.push(o);
                    }
                }
            }
        };
        t.prototype._updateBoneAndSlotTimelines = function () {
            {
                var t = {};
                for (var e = 0, a = this._boneTimelines; e < a.length; e++) {
                    var r = a[e];
                    var i = r.target.target.name;
                    if (!(i in t)) {
                        t[i] = [];
                    }
                    t[i].push(r);
                }
                for (var n = 0, s = this._boneBlendTimelines; n < s.length; n++) {
                    var r = s[n];
                    var i = r.target.target.name;
                    if (!(i in t)) {
                        t[i] = [];
                    }
                    t[i].push(r);
                }
                for (var o = 0, l = this._armature.getBones(); o < l.length; o++) {
                    var h = l[o];
                    var i = h.name;
                    if (!this.containsBoneMask(i)) {
                        continue;
                    }
                    if (i in t) {
                        delete t[i];
                    } else {
                        var u = this._animationData.getBoneTimelines(i);
                        var f = this._armature.animation.getBlendState(V.BONE_TRANSFORM, h.name, h);
                        if (u !== null) {
                            for (var _ = 0, m = u; _ < m.length; _++) {
                                var p = m[_];
                                switch (p.type) {
                                    case 10: {
                                        var r = L.BaseObject.borrowObject(L.BoneAllTimelineState);
                                        r.target = f;
                                        r.init(this._armature, this, p);
                                        this._boneTimelines.push(r);
                                        break;
                                    }
                                    case 11: {
                                        var r = L.BaseObject.borrowObject(
                                            L.BoneTranslateTimelineState
                                        );
                                        r.target = f;
                                        r.init(this._armature, this, p);
                                        this._boneTimelines.push(r);
                                        break;
                                    }
                                    case 12: {
                                        var r = L.BaseObject.borrowObject(
                                            L.BoneRotateTimelineState
                                        );
                                        r.target = f;
                                        r.init(this._armature, this, p);
                                        this._boneTimelines.push(r);
                                        break;
                                    }
                                    case 13: {
                                        var r = L.BaseObject.borrowObject(L.BoneScaleTimelineState);
                                        r.target = f;
                                        r.init(this._armature, this, p);
                                        this._boneTimelines.push(r);
                                        break;
                                    }
                                    case 60: {
                                        var r = L.BaseObject.borrowObject(L.AlphaTimelineState);
                                        r.target = this._armature.animation.getBlendState(
                                            V.BONE_ALPHA,
                                            h.name,
                                            h
                                        );
                                        r.init(this._armature, this, p);
                                        this._boneBlendTimelines.push(r);
                                        break;
                                    }
                                    case 50: {
                                        var r = L.BaseObject.borrowObject(L.SurfaceTimelineState);
                                        r.target = this._armature.animation.getBlendState(
                                            V.SURFACE,
                                            h.name,
                                            h
                                        );
                                        r.init(this._armature, this, p);
                                        this._boneBlendTimelines.push(r);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                        } else if (this.resetToPose) {
                            if (h._boneData.type === 0) {
                                var r = L.BaseObject.borrowObject(L.BoneAllTimelineState);
                                r.target = f;
                                r.init(this._armature, this, null);
                                this._boneTimelines.push(r);
                                this._poseTimelines.push(r);
                            } else {
                                var r = L.BaseObject.borrowObject(L.SurfaceTimelineState);
                                r.target = this._armature.animation.getBlendState(
                                    V.SURFACE,
                                    h.name,
                                    h
                                );
                                r.init(this._armature, this, null);
                                this._boneBlendTimelines.push(r);
                                this._poseTimelines.push(r);
                            }
                        }
                    }
                }
                for (var c in t) {
                    for (var d = 0, y = t[c]; d < y.length; d++) {
                        var r = y[d];
                        var v = this._boneTimelines.indexOf(r);
                        if (v >= 0) {
                            this._boneTimelines.splice(v, 1);
                            r.returnToPool();
                        }
                        v = this._boneBlendTimelines.indexOf(r);
                        if (v >= 0) {
                            this._boneBlendTimelines.splice(v, 1);
                            r.returnToPool();
                        }
                    }
                }
            }
            {
                var g = {};
                var D = [];
                for (var T = 0, b = this._slotTimelines; T < b.length; T++) {
                    var r = b[T];
                    var i = r.target.name;
                    if (!(i in g)) {
                        g[i] = [];
                    }
                    g[i].push(r);
                }
                for (var A = 0, P = this._slotBlendTimelines; A < P.length; A++) {
                    var r = P[A];
                    var i = r.target.target.name;
                    if (!(i in g)) {
                        g[i] = [];
                    }
                    g[i].push(r);
                }
                for (var S = 0, O = this._armature.getSlots(); S < O.length; S++) {
                    var x = O[S];
                    var B = x.parent.name;
                    if (!this.containsBoneMask(B)) {
                        continue;
                    }
                    var i = x.name;
                    if (i in g) {
                        delete g[i];
                    } else {
                        var E = false;
                        var I = false;
                        D.length = 0;
                        var u = this._animationData.getSlotTimelines(i);
                        if (u !== null) {
                            for (var M = 0, F = u; M < F.length; M++) {
                                var p = F[M];
                                switch (p.type) {
                                    case 20: {
                                        var r = L.BaseObject.borrowObject(
                                            L.SlotDisplayTimelineState
                                        );
                                        r.target = x;
                                        r.init(this._armature, this, p);
                                        this._slotTimelines.push(r);
                                        E = true;
                                        break;
                                    }
                                    case 23: {
                                        var r = L.BaseObject.borrowObject(
                                            L.SlotZIndexTimelineState
                                        );
                                        r.target = this._armature.animation.getBlendState(
                                            V.SLOT_Z_INDEX,
                                            x.name,
                                            x
                                        );
                                        r.init(this._armature, this, p);
                                        this._slotBlendTimelines.push(r);
                                        break;
                                    }
                                    case 21: {
                                        var r = L.BaseObject.borrowObject(L.SlotColorTimelineState);
                                        r.target = x;
                                        r.init(this._armature, this, p);
                                        this._slotTimelines.push(r);
                                        I = true;
                                        break;
                                    }
                                    case 22: {
                                        var r = L.BaseObject.borrowObject(L.DeformTimelineState);
                                        r.target = this._armature.animation.getBlendState(
                                            V.SLOT_DEFORM,
                                            x.name,
                                            x
                                        );
                                        r.init(this._armature, this, p);
                                        if (r.target !== null) {
                                            this._slotBlendTimelines.push(r);
                                            D.push(r.geometryOffset);
                                        } else {
                                            r.returnToPool();
                                        }
                                        break;
                                    }
                                    case 24: {
                                        var r = L.BaseObject.borrowObject(L.AlphaTimelineState);
                                        r.target = this._armature.animation.getBlendState(
                                            V.SLOT_ALPHA,
                                            x.name,
                                            x
                                        );
                                        r.init(this._armature, this, p);
                                        this._slotBlendTimelines.push(r);
                                        break;
                                    }
                                    default:
                                        break;
                                }
                            }
                        }
                        if (this.resetToPose) {
                            if (!E) {
                                var r = L.BaseObject.borrowObject(L.SlotDisplayTimelineState);
                                r.target = x;
                                r.init(this._armature, this, null);
                                this._slotTimelines.push(r);
                                this._poseTimelines.push(r);
                            }
                            if (!I) {
                                var r = L.BaseObject.borrowObject(L.SlotColorTimelineState);
                                r.target = x;
                                r.init(this._armature, this, null);
                                this._slotTimelines.push(r);
                                this._poseTimelines.push(r);
                            }
                            for (var w = 0, C = x.displayFrameCount; w < C; ++w) {
                                var N = x.getDisplayFrameAt(w);
                                if (N.deformVertices.length === 0) {
                                    continue;
                                }
                                var R = N.getGeometryData();
                                if (R !== null && D.indexOf(R.offset) < 0) {
                                    var r = L.BaseObject.borrowObject(L.DeformTimelineState);
                                    r.geometryOffset = R.offset;
                                    r.displayFrame = N;
                                    r.target = this._armature.animation.getBlendState(
                                        V.SLOT_DEFORM,
                                        x.name,
                                        x
                                    );
                                    r.init(this._armature, this, null);
                                    this._slotBlendTimelines.push(r);
                                    this._poseTimelines.push(r);
                                }
                            }
                        }
                    }
                }
                for (var c in g) {
                    for (var k = 0, j = g[c]; k < j.length; k++) {
                        var r = j[k];
                        var v = this._slotTimelines.indexOf(r);
                        if (v >= 0) {
                            this._slotTimelines.splice(v, 1);
                            r.returnToPool();
                        }
                        v = this._slotBlendTimelines.indexOf(r);
                        if (v >= 0) {
                            this._slotBlendTimelines.splice(v, 1);
                            r.returnToPool();
                        }
                    }
                }
            }
        };
        t.prototype._advanceFadeTime = function (t) {
            var e = this._fadeState > 0;
            if (this._subFadeState < 0) {
                this._subFadeState = 0;
                var a = this._parent === null && this.actionEnabled;
                if (a) {
                    var r = e ? L.EventObject.FADE_OUT : L.EventObject.FADE_IN;
                    if (this._armature.eventDispatcher.hasDBEventListener(r)) {
                        var i = L.BaseObject.borrowObject(L.EventObject);
                        i.type = r;
                        i.armature = this._armature;
                        i.animationState = this;
                        this._armature._dragonBones.bufferEvent(i);
                    }
                }
            }
            if (t < 0) {
                t = -t;
            }
            this._fadeTime += t;
            if (this._fadeTime >= this.fadeTotalTime) {
                this._subFadeState = 1;
                this._fadeProgress = e ? 0 : 1;
            } else if (this._fadeTime > 0) {
                this._fadeProgress = e
                    ? 1 - this._fadeTime / this.fadeTotalTime
                    : this._fadeTime / this.fadeTotalTime;
            } else {
                this._fadeProgress = e ? 1 : 0;
            }
            if (this._subFadeState > 0) {
                if (!e) {
                    this._playheadState |= 1;
                    this._fadeState = 0;
                }
                var a = this._parent === null && this.actionEnabled;
                if (a) {
                    var r = e ? L.EventObject.FADE_OUT_COMPLETE : L.EventObject.FADE_IN_COMPLETE;
                    if (this._armature.eventDispatcher.hasDBEventListener(r)) {
                        var i = L.BaseObject.borrowObject(L.EventObject);
                        i.type = r;
                        i.armature = this._armature;
                        i.animationState = this;
                        this._armature._dragonBones.bufferEvent(i);
                    }
                }
            }
        };
        t.prototype.init = function (t, e, a) {
            if (this._armature !== null) {
                return;
            }
            this._armature = t;
            this._animationData = e;
            this.resetToPose = a.resetToPose;
            this.additive = a.additive;
            this.displayControl = a.displayControl;
            this.actionEnabled = a.actionEnabled;
            this.blendType = e.blendType;
            this.layer = a.layer;
            this.playTimes = a.playTimes;
            this.timeScale = a.timeScale;
            this.fadeTotalTime = a.fadeInTime;
            this.autoFadeOutTime = a.autoFadeOutTime;
            this.name = a.name.length > 0 ? a.name : a.animation;
            this.group = a.group;
            this._weight = a.weight;
            if (a.pauseFadeIn) {
                this._playheadState = 2;
            } else {
                this._playheadState = 3;
            }
            if (a.duration < 0) {
                this._position = 0;
                this._duration = this._animationData.duration;
                if (a.position !== 0) {
                    if (this.timeScale >= 0) {
                        this._time = a.position;
                    } else {
                        this._time = a.position - this._duration;
                    }
                } else {
                    this._time = 0;
                }
            } else {
                this._position = a.position;
                this._duration = a.duration;
                this._time = 0;
            }
            if (this.timeScale < 0 && this._time === 0) {
                this._time = -1e-6;
            }
            if (this.fadeTotalTime <= 0) {
                this._fadeProgress = 0.999999;
            }
            if (a.boneMask.length > 0) {
                this._boneMask.length = a.boneMask.length;
                for (var r = 0, i = this._boneMask.length; r < i; ++r) {
                    this._boneMask[r] = a.boneMask[r];
                }
            }
            this._actionTimeline = L.BaseObject.borrowObject(L.ActionTimelineState);
            this._actionTimeline.init(this._armature, this, this._animationData.actionTimeline);
            this._actionTimeline.currentTime = this._time;
            if (this._actionTimeline.currentTime < 0) {
                this._actionTimeline.currentTime =
                    this._duration - this._actionTimeline.currentTime;
            }
            if (this._animationData.zOrderTimeline !== null) {
                this._zOrderTimeline = L.BaseObject.borrowObject(L.ZOrderTimelineState);
                this._zOrderTimeline.init(this._armature, this, this._animationData.zOrderTimeline);
            }
        };
        t.prototype.advanceTime = function (t, e) {
            if (this._fadeState !== 0 || this._subFadeState !== 0) {
                this._advanceFadeTime(t);
            }
            if (this._playheadState === 3) {
                if (this.timeScale !== 1) {
                    t *= this.timeScale;
                }
                this._time += t;
            }
            if (this._timelineDirty !== 0) {
                if (this._timelineDirty === 2) {
                    this._updateTimelines();
                }
                this._timelineDirty = 0;
                this._updateBoneAndSlotTimelines();
            }
            var a = this._fadeState !== 0 || this._subFadeState === 0;
            var r = this._fadeState === 0 && e > 0;
            var i = true;
            var n = true;
            var s = this._time;
            this._weightResult = this._weight * this._fadeProgress;
            if (this._parent !== null) {
                this._weightResult *= this._parent._weightResult;
            }
            if (this._actionTimeline.playState <= 0) {
                this._actionTimeline.update(s);
            }
            if (this._weight === 0) {
                return;
            }
            if (r) {
                var o = e * 2;
                this._actionTimeline.currentTime =
                    Math.floor(this._actionTimeline.currentTime * o) / o;
            }
            if (this._zOrderTimeline !== null && this._zOrderTimeline.playState <= 0) {
                this._zOrderTimeline.update(s);
            }
            if (r) {
                var l = Math.floor(this._actionTimeline.currentTime * e);
                if (this._armature._cacheFrameIndex === l) {
                    i = false;
                    n = false;
                } else {
                    this._armature._cacheFrameIndex = l;
                    if (this._animationData.cachedFrames[l]) {
                        n = false;
                    } else {
                        this._animationData.cachedFrames[l] = true;
                    }
                }
            }
            if (i) {
                var h = false;
                var u = null;
                if (n) {
                    for (var f = 0, _ = this._boneTimelines.length; f < _; ++f) {
                        var m = this._boneTimelines[f];
                        if (m.playState <= 0) {
                            m.update(s);
                        }
                        if (m.target !== u) {
                            var p = m.target;
                            h = p.update(this);
                            u = p;
                            if (p.dirty === 1) {
                                var c = p.target.animationPose;
                                c.x = 0;
                                c.y = 0;
                                c.rotation = 0;
                                c.skew = 0;
                                c.scaleX = 1;
                                c.scaleY = 1;
                            }
                        }
                        if (h) {
                            m.blend(a);
                        }
                    }
                }
                for (var f = 0, _ = this._boneBlendTimelines.length; f < _; ++f) {
                    var m = this._boneBlendTimelines[f];
                    if (m.playState <= 0) {
                        m.update(s);
                    }
                    if (m.target.update(this)) {
                        m.blend(a);
                    }
                }
                if (this.displayControl) {
                    for (var f = 0, _ = this._slotTimelines.length; f < _; ++f) {
                        var m = this._slotTimelines[f];
                        if (m.playState <= 0) {
                            var d = m.target;
                            var y = d.displayController;
                            if (y === null || y === this.name || y === this.group) {
                                m.update(s);
                            }
                        }
                    }
                }
                for (var f = 0, _ = this._slotBlendTimelines.length; f < _; ++f) {
                    var m = this._slotBlendTimelines[f];
                    if (m.playState <= 0) {
                        var p = m.target;
                        m.update(s);
                        if (p.update(this)) {
                            m.blend(a);
                        }
                    }
                }
                for (var f = 0, _ = this._constraintTimelines.length; f < _; ++f) {
                    var m = this._constraintTimelines[f];
                    if (m.playState <= 0) {
                        m.update(s);
                    }
                }
                if (this._animationTimelines.length > 0) {
                    var v = 100;
                    var g = 100;
                    var D = null;
                    var T = null;
                    for (var f = 0, _ = this._animationTimelines.length; f < _; ++f) {
                        var m = this._animationTimelines[f];
                        if (m.playState <= 0) {
                            m.update(s);
                        }
                        if (this.blendType === 1) {
                            var b = m.target;
                            var A = this.parameterX - b.positionX;
                            if (A >= 0) {
                                if (A < v) {
                                    v = A;
                                    D = b;
                                }
                            } else {
                                if (-A < g) {
                                    g = -A;
                                    T = b;
                                }
                            }
                        }
                    }
                    if (D !== null) {
                        if (this._activeChildA !== D) {
                            if (this._activeChildA !== null) {
                                this._activeChildA.weight = 0;
                            }
                            this._activeChildA = D;
                            this._activeChildA.activeTimeline();
                        }
                        if (this._activeChildB !== T) {
                            if (this._activeChildB !== null) {
                                this._activeChildB.weight = 0;
                            }
                            this._activeChildB = T;
                        }
                        D.weight = g / (v + g);
                        if (T) {
                            T.weight = 1 - D.weight;
                        }
                    }
                }
            }
            if (this._fadeState === 0) {
                if (this._subFadeState > 0) {
                    this._subFadeState = 0;
                    if (this._poseTimelines.length > 0) {
                        for (var P = 0, S = this._poseTimelines; P < S.length; P++) {
                            var m = S[P];
                            var O = this._boneTimelines.indexOf(m);
                            if (O >= 0) {
                                this._boneTimelines.splice(O, 1);
                                m.returnToPool();
                                continue;
                            }
                            O = this._boneBlendTimelines.indexOf(m);
                            if (O >= 0) {
                                this._boneBlendTimelines.splice(O, 1);
                                m.returnToPool();
                                continue;
                            }
                            O = this._slotTimelines.indexOf(m);
                            if (O >= 0) {
                                this._slotTimelines.splice(O, 1);
                                m.returnToPool();
                                continue;
                            }
                            O = this._slotBlendTimelines.indexOf(m);
                            if (O >= 0) {
                                this._slotBlendTimelines.splice(O, 1);
                                m.returnToPool();
                                continue;
                            }
                            O = this._constraintTimelines.indexOf(m);
                            if (O >= 0) {
                                this._constraintTimelines.splice(O, 1);
                                m.returnToPool();
                                continue;
                            }
                        }
                        this._poseTimelines.length = 0;
                    }
                }
                if (this._actionTimeline.playState > 0) {
                    if (this.autoFadeOutTime >= 0) {
                        this.fadeOut(this.autoFadeOutTime);
                    }
                }
            }
        };
        t.prototype.play = function () {
            this._playheadState = 3;
        };
        t.prototype.stop = function () {
            this._playheadState &= 1;
        };
        t.prototype.fadeOut = function (t, e) {
            if (e === void 0) {
                e = true;
            }
            if (t < 0) {
                t = 0;
            }
            if (e) {
                this._playheadState &= 2;
            }
            if (this._fadeState > 0) {
                if (t > this.fadeTotalTime - this._fadeTime) {
                    return;
                }
            } else {
                this._fadeState = 1;
                this._subFadeState = -1;
                if (t <= 0 || this._fadeProgress <= 0) {
                    this._fadeProgress = 1e-6;
                }
                for (var a = 0, r = this._boneTimelines; a < r.length; a++) {
                    var i = r[a];
                    i.fadeOut();
                }
                for (var n = 0, s = this._boneBlendTimelines; n < s.length; n++) {
                    var i = s[n];
                    i.fadeOut();
                }
                for (var o = 0, l = this._slotTimelines; o < l.length; o++) {
                    var i = l[o];
                    i.fadeOut();
                }
                for (var h = 0, u = this._slotBlendTimelines; h < u.length; h++) {
                    var i = u[h];
                    i.fadeOut();
                }
                for (var f = 0, _ = this._constraintTimelines; f < _.length; f++) {
                    var i = _[f];
                    i.fadeOut();
                }
                for (var m = 0, p = this._animationTimelines; m < p.length; m++) {
                    var i = p[m];
                    i.fadeOut();
                    var c = i.target;
                    c.fadeOut(999999, true);
                }
            }
            this.displayControl = false;
            this.fadeTotalTime = this._fadeProgress > 1e-6 ? t / this._fadeProgress : 0;
            this._fadeTime = this.fadeTotalTime * (1 - this._fadeProgress);
        };
        t.prototype.containsBoneMask = function (t) {
            return this._boneMask.length === 0 || this._boneMask.indexOf(t) >= 0;
        };
        t.prototype.addBoneMask = function (t, e) {
            if (e === void 0) {
                e = true;
            }
            var a = this._armature.getBone(t);
            if (a === null) {
                return;
            }
            if (this._boneMask.indexOf(t) < 0) {
                this._boneMask.push(t);
            }
            if (e) {
                for (var r = 0, i = this._armature.getBones(); r < i.length; r++) {
                    var n = i[r];
                    if (this._boneMask.indexOf(n.name) < 0 && a.contains(n)) {
                        this._boneMask.push(n.name);
                    }
                }
            }
            this._timelineDirty = 1;
        };
        t.prototype.removeBoneMask = function (t, e) {
            if (e === void 0) {
                e = true;
            }
            var a = this._boneMask.indexOf(t);
            if (a >= 0) {
                this._boneMask.splice(a, 1);
            }
            if (e) {
                var r = this._armature.getBone(t);
                if (r !== null) {
                    var i = this._armature.getBones();
                    if (this._boneMask.length > 0) {
                        for (var n = 0, s = i; n < s.length; n++) {
                            var o = s[n];
                            var l = this._boneMask.indexOf(o.name);
                            if (l >= 0 && r.contains(o)) {
                                this._boneMask.splice(l, 1);
                            }
                        }
                    } else {
                        for (var h = 0, u = i; h < u.length; h++) {
                            var o = u[h];
                            if (o === r) {
                                continue;
                            }
                            if (!r.contains(o)) {
                                this._boneMask.push(o.name);
                            }
                        }
                    }
                }
            }
            this._timelineDirty = 1;
        };
        t.prototype.removeAllBoneMask = function () {
            this._boneMask.length = 0;
            this._timelineDirty = 1;
        };
        t.prototype.addState = function (t, e) {
            if (e === void 0) {
                e = null;
            }
            if (e !== null) {
                for (var a = 0, r = e; a < r.length; a++) {
                    var i = r[a];
                    switch (i.type) {
                        case 40: {
                            var n = L.BaseObject.borrowObject(L.AnimationProgressTimelineState);
                            n.target = t;
                            n.init(this._armature, this, i);
                            this._animationTimelines.push(n);
                            if (this.blendType !== 0) {
                                var s = i;
                                t.positionX = s.x;
                                t.positionY = s.y;
                                t.weight = 0;
                            }
                            t._parent = this;
                            this.resetToPose = false;
                            break;
                        }
                        case 41: {
                            var n = L.BaseObject.borrowObject(L.AnimationWeightTimelineState);
                            n.target = t;
                            n.init(this._armature, this, i);
                            this._animationTimelines.push(n);
                            break;
                        }
                        case 42: {
                            var n = L.BaseObject.borrowObject(L.AnimationParametersTimelineState);
                            n.target = t;
                            n.init(this._armature, this, i);
                            this._animationTimelines.push(n);
                            break;
                        }
                        default:
                            break;
                    }
                }
            }
            if (t._parent === null) {
                t._parent = this;
            }
        };
        t.prototype.activeTimeline = function () {
            for (var t = 0, e = this._slotTimelines; t < e.length; t++) {
                var a = e[t];
                a.dirty = true;
                a.currentTime = -1;
            }
        };
        Object.defineProperty(t.prototype, 'isFadeIn', {
            get: function () {
                return this._fadeState < 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'isFadeOut', {
            get: function () {
                return this._fadeState > 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'isFadeComplete', {
            get: function () {
                return this._fadeState === 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'isPlaying', {
            get: function () {
                return (this._playheadState & 2) !== 0 && this._actionTimeline.playState <= 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'isCompleted', {
            get: function () {
                return this._actionTimeline.playState > 0;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'currentPlayTimes', {
            get: function () {
                return this._actionTimeline.currentPlayTimes;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'totalTime', {
            get: function () {
                return this._duration;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'currentTime', {
            get: function () {
                return this._actionTimeline.currentTime;
            },
            set: function (t) {
                var e =
                    this._actionTimeline.currentPlayTimes -
                    (this._actionTimeline.playState > 0 ? 1 : 0);
                if (t < 0 || this._duration < t) {
                    t = (t % this._duration) + e * this._duration;
                    if (t < 0) {
                        t += this._duration;
                    }
                }
                if (
                    this.playTimes > 0 &&
                    e === this.playTimes - 1 &&
                    t === this._duration &&
                    this._parent === null
                ) {
                    t = this._duration - 1e-6;
                }
                if (this._time === t) {
                    return;
                }
                this._time = t;
                this._actionTimeline.setCurrentTime(this._time);
                if (this._zOrderTimeline !== null) {
                    this._zOrderTimeline.playState = -1;
                }
                for (var a = 0, r = this._boneTimelines; a < r.length; a++) {
                    var i = r[a];
                    i.playState = -1;
                }
                for (var n = 0, s = this._slotTimelines; n < s.length; n++) {
                    var i = s[n];
                    i.playState = -1;
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'weight', {
            get: function () {
                return this._weight;
            },
            set: function (t) {
                if (this._weight === t) {
                    return;
                }
                this._weight = t;
                for (var e = 0, a = this._boneTimelines; e < a.length; e++) {
                    var r = a[e];
                    r.dirty = true;
                }
                for (var i = 0, n = this._boneBlendTimelines; i < n.length; i++) {
                    var r = n[i];
                    r.dirty = true;
                }
                for (var s = 0, o = this._slotBlendTimelines; s < o.length; s++) {
                    var r = o[s];
                    r.dirty = true;
                }
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'animationData', {
            get: function () {
                return this._animationData;
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })(L.BaseObject);
    L.AnimationState = t;
    var V = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.BlendState]';
        };
        e.prototype._onClear = function () {
            this.reset();
            this.target = null;
        };
        e.prototype.update = function (t) {
            var e = t.layer;
            var a = t._weightResult;
            if (this.dirty > 0) {
                if (this.leftWeight > 0) {
                    if (this.layer !== e) {
                        if (this.layerWeight >= this.leftWeight) {
                            this.dirty++;
                            this.layer = e;
                            this.leftWeight = 0;
                            this.blendWeight = 0;
                            return false;
                        }
                        this.layer = e;
                        this.leftWeight -= this.layerWeight;
                        this.layerWeight = 0;
                    }
                    a *= this.leftWeight;
                    this.dirty++;
                    this.blendWeight = a;
                    this.layerWeight += this.blendWeight;
                    return true;
                }
                return false;
            }
            this.dirty++;
            this.layer = e;
            this.leftWeight = 1;
            this.blendWeight = a;
            this.layerWeight = a;
            return true;
        };
        e.prototype.reset = function () {
            this.dirty = 0;
            this.layer = 0;
            this.leftWeight = 0;
            this.layerWeight = 0;
            this.blendWeight = 0;
        };
        e.BONE_TRANSFORM = 'boneTransform';
        e.BONE_ALPHA = 'boneAlpha';
        e.SURFACE = 'surface';
        e.SLOT_DEFORM = 'slotDeform';
        e.SLOT_ALPHA = 'slotAlpha';
        e.SLOT_Z_INDEX = 'slotZIndex';
        return e;
    })(L.BaseObject);
    L.BlendState = V;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.prototype._onClear = function () {
            this.dirty = false;
            this.playState = -1;
            this.currentPlayTimes = 0;
            this.currentTime = -1;
            this.target = null;
            this._isTween = false;
            this._valueOffset = 0;
            this._frameValueOffset = 0;
            this._frameOffset = 0;
            this._frameRate = 0;
            this._frameCount = 0;
            this._frameIndex = -1;
            this._frameRateR = 0;
            this._position = 0;
            this._duration = 0;
            this._timeScale = 1;
            this._timeOffset = 0;
            this._animationData = null;
            this._timelineData = null;
            this._armature = null;
            this._animationState = null;
            this._actionTimeline = null;
            this._frameArray = null;
            this._valueArray = null;
            this._timelineArray = null;
            this._frameIndices = null;
        };
        e.prototype._setCurrentTime = function (t) {
            var e = this.playState;
            var a = this.currentPlayTimes;
            var r = this.currentTime;
            if (this._actionTimeline !== null && this._frameCount <= 1) {
                this.playState = this._actionTimeline.playState >= 0 ? 1 : -1;
                this.currentPlayTimes = 1;
                this.currentTime = this._actionTimeline.currentTime;
            } else if (
                this._actionTimeline === null ||
                this._timeScale !== 1 ||
                this._timeOffset !== 0
            ) {
                var i = this._animationState.playTimes;
                var n = i * this._duration;
                t *= this._timeScale;
                if (this._timeOffset !== 0) {
                    t += this._timeOffset * this._animationData.duration;
                }
                if (i > 0 && (t >= n || t <= -n)) {
                    if (this.playState <= 0 && this._animationState._playheadState === 3) {
                        this.playState = 1;
                    }
                    this.currentPlayTimes = i;
                    if (t < 0) {
                        this.currentTime = 0;
                    } else {
                        this.currentTime =
                            this.playState === 1 ? this._duration + 1e-6 : this._duration;
                    }
                } else {
                    if (this.playState !== 0 && this._animationState._playheadState === 3) {
                        this.playState = 0;
                    }
                    if (t < 0) {
                        t = -t;
                        this.currentPlayTimes = Math.floor(t / this._duration);
                        this.currentTime = this._duration - (t % this._duration);
                    } else {
                        this.currentPlayTimes = Math.floor(t / this._duration);
                        this.currentTime = t % this._duration;
                    }
                }
                this.currentTime += this._position;
            } else {
                this.playState = this._actionTimeline.playState;
                this.currentPlayTimes = this._actionTimeline.currentPlayTimes;
                this.currentTime = this._actionTimeline.currentTime;
            }
            if (this.currentPlayTimes === a && this.currentTime === r) {
                return false;
            }
            if (
                (e < 0 && this.playState !== e) ||
                (this.playState <= 0 && this.currentPlayTimes !== a)
            ) {
                this._frameIndex = -1;
            }
            return true;
        };
        e.prototype.init = function (t, e, a) {
            this._armature = t;
            this._animationState = e;
            this._timelineData = a;
            this._actionTimeline = this._animationState._actionTimeline;
            if (this === this._actionTimeline) {
                this._actionTimeline = null;
            }
            this._animationData = this._animationState.animationData;
            this._frameRate = this._animationData.parent.frameRate;
            this._frameRateR = 1 / this._frameRate;
            this._position = this._animationState._position;
            this._duration = this._animationState._duration;
            if (this._timelineData !== null) {
                var r = this._animationData.parent.parent;
                this._frameArray = r.frameArray;
                this._timelineArray = r.timelineArray;
                this._frameIndices = r.frameIndices;
                this._frameCount = this._timelineArray[this._timelineData.offset + 2];
                this._frameValueOffset = this._timelineArray[this._timelineData.offset + 4];
                this._timeScale = 100 / this._timelineArray[this._timelineData.offset + 0];
                this._timeOffset = this._timelineArray[this._timelineData.offset + 1] * 0.01;
            }
        };
        e.prototype.fadeOut = function () {
            this.dirty = false;
        };
        e.prototype.update = function (t) {
            if (this._setCurrentTime(t)) {
                if (this._frameCount > 1) {
                    var e = Math.floor(this.currentTime * this._frameRate);
                    var a = this._frameIndices[this._timelineData.frameIndicesOffset + e];
                    if (this._frameIndex !== a) {
                        this._frameIndex = a;
                        this._frameOffset =
                            this._animationData.frameOffset +
                            this._timelineArray[this._timelineData.offset + 5 + this._frameIndex];
                        this._onArriveAtFrame();
                    }
                } else if (this._frameIndex < 0) {
                    this._frameIndex = 0;
                    if (this._timelineData !== null) {
                        this._frameOffset =
                            this._animationData.frameOffset +
                            this._timelineArray[this._timelineData.offset + 5];
                    }
                    this._onArriveAtFrame();
                }
                if (this._isTween || this.dirty) {
                    this._onUpdateFrame();
                }
            }
        };
        e.prototype.blend = function (t) {};
        return e;
    })(t.BaseObject);
    t.TimelineState = e;
    var a = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e._getEasingValue = function (t, e, a) {
            var r = e;
            switch (t) {
                case 3:
                    r = Math.pow(e, 2);
                    break;
                case 4:
                    r = 1 - Math.pow(1 - e, 2);
                    break;
                case 5:
                    r = 0.5 * (1 - Math.cos(e * Math.PI));
                    break;
            }
            return (r - e) * a + e;
        };
        e._getEasingCurveValue = function (t, e, a, r) {
            if (t <= 0) {
                return 0;
            } else if (t >= 1) {
                return 1;
            }
            var i = a > 0;
            var n = a + 1;
            var s = Math.floor(t * n);
            var o = 0;
            var l = 0;
            if (i) {
                o = s === 0 ? 0 : e[r + s - 1];
                l = s === n - 1 ? 1e4 : e[r + s];
            } else {
                o = e[r + s - 1];
                l = e[r + s];
            }
            return (o + (l - o) * (t * n - s)) * 1e-4;
        };
        e.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this._tweenType = 0;
            this._curveCount = 0;
            this._framePosition = 0;
            this._frameDurationR = 0;
            this._tweenEasing = 0;
            this._tweenProgress = 0;
            this._valueScale = 1;
        };
        e.prototype._onArriveAtFrame = function () {
            if (
                this._frameCount > 1 &&
                (this._frameIndex !== this._frameCount - 1 ||
                    this._animationState.playTimes === 0 ||
                    this._animationState.currentPlayTimes < this._animationState.playTimes - 1)
            ) {
                this._tweenType = this._frameArray[this._frameOffset + 1];
                this._isTween = this._tweenType !== 0;
                if (this._isTween) {
                    if (this._tweenType === 2) {
                        this._curveCount = this._frameArray[this._frameOffset + 2];
                    } else if (this._tweenType !== 0 && this._tweenType !== 1) {
                        this._tweenEasing = this._frameArray[this._frameOffset + 2] * 0.01;
                    }
                } else {
                    this.dirty = true;
                }
                this._framePosition = this._frameArray[this._frameOffset] * this._frameRateR;
                if (this._frameIndex === this._frameCount - 1) {
                    this._frameDurationR = 1 / (this._animationData.duration - this._framePosition);
                } else {
                    var t =
                        this._animationData.frameOffset +
                        this._timelineArray[this._timelineData.offset + 5 + this._frameIndex + 1];
                    var e = this._frameArray[t] * this._frameRateR - this._framePosition;
                    if (e > 0) {
                        this._frameDurationR = 1 / e;
                    } else {
                        this._frameDurationR = 0;
                    }
                }
            } else {
                this.dirty = true;
                this._isTween = false;
            }
        };
        e.prototype._onUpdateFrame = function () {
            if (this._isTween) {
                this.dirty = true;
                this._tweenProgress =
                    (this.currentTime - this._framePosition) * this._frameDurationR;
                if (this._tweenType === 2) {
                    this._tweenProgress = e._getEasingCurveValue(
                        this._tweenProgress,
                        this._frameArray,
                        this._curveCount,
                        this._frameOffset + 3
                    );
                } else if (this._tweenType !== 1) {
                    this._tweenProgress = e._getEasingValue(
                        this._tweenType,
                        this._tweenProgress,
                        this._tweenEasing
                    );
                }
            }
        };
        return e;
    })(e);
    t.TweenTimelineState = a;
    var r = (function (i) {
        __extends(t, i);
        function t() {
            return (i !== null && i.apply(this, arguments)) || this;
        }
        t.prototype._onClear = function () {
            i.prototype._onClear.call(this);
            this._current = 0;
            this._difference = 0;
            this._result = 0;
        };
        t.prototype._onArriveAtFrame = function () {
            i.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var t = this._valueScale;
                var e = this._valueArray;
                var a = this._valueOffset + this._frameValueOffset + this._frameIndex;
                if (this._isTween) {
                    var r =
                        this._frameIndex === this._frameCount - 1
                            ? this._valueOffset + this._frameValueOffset
                            : a + 1;
                    if (t === 1) {
                        this._current = e[a];
                        this._difference = e[r] - this._current;
                    } else {
                        this._current = e[a] * t;
                        this._difference = e[r] * t - this._current;
                    }
                } else {
                    this._result = e[a] * t;
                }
            } else {
                this._result = 0;
            }
        };
        t.prototype._onUpdateFrame = function () {
            i.prototype._onUpdateFrame.call(this);
            if (this._isTween) {
                this._result = this._current + this._difference * this._tweenProgress;
            }
        };
        return t;
    })(a);
    t.SingleValueTimelineState = r;
    var i = (function (i) {
        __extends(t, i);
        function t() {
            return (i !== null && i.apply(this, arguments)) || this;
        }
        t.prototype._onClear = function () {
            i.prototype._onClear.call(this);
            this._currentA = 0;
            this._currentB = 0;
            this._differenceA = 0;
            this._differenceB = 0;
            this._resultA = 0;
            this._resultB = 0;
        };
        t.prototype._onArriveAtFrame = function () {
            i.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var t = this._valueScale;
                var e = this._valueArray;
                var a = this._valueOffset + this._frameValueOffset + this._frameIndex * 2;
                if (this._isTween) {
                    var r =
                        this._frameIndex === this._frameCount - 1
                            ? this._valueOffset + this._frameValueOffset
                            : a + 2;
                    if (t === 1) {
                        this._currentA = e[a];
                        this._currentB = e[a + 1];
                        this._differenceA = e[r] - this._currentA;
                        this._differenceB = e[r + 1] - this._currentB;
                    } else {
                        this._currentA = e[a] * t;
                        this._currentB = e[a + 1] * t;
                        this._differenceA = e[r] * t - this._currentA;
                        this._differenceB = e[r + 1] * t - this._currentB;
                    }
                } else {
                    this._resultA = e[a] * t;
                    this._resultB = e[a + 1] * t;
                }
            } else {
                this._resultA = 0;
                this._resultB = 0;
            }
        };
        t.prototype._onUpdateFrame = function () {
            i.prototype._onUpdateFrame.call(this);
            if (this._isTween) {
                this._resultA = this._currentA + this._differenceA * this._tweenProgress;
                this._resultB = this._currentB + this._differenceB * this._tweenProgress;
            }
        };
        return t;
    })(a);
    t.DoubleValueTimelineState = i;
    var n = (function (o) {
        __extends(t, o);
        function t() {
            var t = (o !== null && o.apply(this, arguments)) || this;
            t._rd = [];
            return t;
        }
        t.prototype._onClear = function () {
            o.prototype._onClear.call(this);
            this._valueCount = 0;
            this._rd.length = 0;
        };
        t.prototype._onArriveAtFrame = function () {
            o.prototype._onArriveAtFrame.call(this);
            var t = this._valueCount;
            var e = this._rd;
            if (this._timelineData !== null) {
                var a = this._valueScale;
                var r = this._valueArray;
                var i = this._valueOffset + this._frameValueOffset + this._frameIndex * t;
                if (this._isTween) {
                    var n =
                        this._frameIndex === this._frameCount - 1
                            ? this._valueOffset + this._frameValueOffset
                            : i + t;
                    if (a === 1) {
                        for (var s = 0; s < t; ++s) {
                            e[t + s] = r[n + s] - r[i + s];
                        }
                    } else {
                        for (var s = 0; s < t; ++s) {
                            e[t + s] = (r[n + s] - r[i + s]) * a;
                        }
                    }
                } else if (a === 1) {
                    for (var s = 0; s < t; ++s) {
                        e[s] = r[i + s];
                    }
                } else {
                    for (var s = 0; s < t; ++s) {
                        e[s] = r[i + s] * a;
                    }
                }
            } else {
                for (var s = 0; s < t; ++s) {
                    e[s] = 0;
                }
            }
        };
        t.prototype._onUpdateFrame = function () {
            o.prototype._onUpdateFrame.call(this);
            if (this._isTween) {
                var t = this._valueCount;
                var e = this._valueScale;
                var a = this._tweenProgress;
                var r = this._valueArray;
                var i = this._rd;
                var n = this._valueOffset + this._frameValueOffset + this._frameIndex * t;
                if (e === 1) {
                    for (var s = 0; s < t; ++s) {
                        i[s] = r[n + s] + i[t + s] * a;
                    }
                } else {
                    for (var s = 0; s < t; ++s) {
                        i[s] = r[n + s] * e + i[t + s] * a;
                    }
                }
            }
        };
        return t;
    })(a);
    t.MutilpleValueTimelineState = n;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (y) {
    var t = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.ActionTimelineState]';
        };
        e.prototype._onCrossFrame = function (t) {
            var e = this._armature.eventDispatcher;
            if (this._animationState.actionEnabled) {
                var a =
                    this._animationData.frameOffset +
                    this._timelineArray[this._timelineData.offset + 5 + t];
                var r = this._frameArray[a + 1];
                var i = this._animationData.parent.actions;
                for (var n = 0; n < r; ++n) {
                    var s = this._frameArray[a + 2 + n];
                    var o = i[s];
                    if (o.type === 0) {
                        var l = y.BaseObject.borrowObject(y.EventObject);
                        l.time = this._frameArray[a] / this._frameRate;
                        l.animationState = this._animationState;
                        y.EventObject.actionDataToInstance(o, l, this._armature);
                        this._armature._bufferAction(l, true);
                    } else {
                        var h =
                            o.type === 10 ? y.EventObject.FRAME_EVENT : y.EventObject.SOUND_EVENT;
                        if (o.type === 11 || e.hasDBEventListener(h)) {
                            var l = y.BaseObject.borrowObject(y.EventObject);
                            l.time = this._frameArray[a] / this._frameRate;
                            l.animationState = this._animationState;
                            y.EventObject.actionDataToInstance(o, l, this._armature);
                            this._armature._dragonBones.bufferEvent(l);
                        }
                    }
                }
            }
        };
        e.prototype._onArriveAtFrame = function () {};
        e.prototype._onUpdateFrame = function () {};
        e.prototype.update = function (t) {
            var e = this.playState;
            var a = this.currentPlayTimes;
            var r = this.currentTime;
            if (this._setCurrentTime(t)) {
                var i = this._animationState._parent === null && this._animationState.actionEnabled;
                var n = this._armature.eventDispatcher;
                if (e < 0) {
                    if (this.playState !== e) {
                        if (
                            this._animationState.displayControl &&
                            this._animationState.resetToPose
                        ) {
                            this._armature._sortZOrder(null, 0);
                        }
                        if (i && n.hasDBEventListener(y.EventObject.START)) {
                            var s = y.BaseObject.borrowObject(y.EventObject);
                            s.type = y.EventObject.START;
                            s.armature = this._armature;
                            s.animationState = this._animationState;
                            this._armature._dragonBones.bufferEvent(s);
                        }
                    } else {
                        return;
                    }
                }
                var o = this._animationState.timeScale < 0;
                var l = null;
                var h = null;
                if (i && this.currentPlayTimes !== a) {
                    if (n.hasDBEventListener(y.EventObject.LOOP_COMPLETE)) {
                        l = y.BaseObject.borrowObject(y.EventObject);
                        l.type = y.EventObject.LOOP_COMPLETE;
                        l.armature = this._armature;
                        l.animationState = this._animationState;
                    }
                    if (this.playState > 0) {
                        if (n.hasDBEventListener(y.EventObject.COMPLETE)) {
                            h = y.BaseObject.borrowObject(y.EventObject);
                            h.type = y.EventObject.COMPLETE;
                            h.armature = this._armature;
                            h.animationState = this._animationState;
                        }
                    }
                }
                if (this._frameCount > 1) {
                    var u = this._timelineData;
                    var f = Math.floor(this.currentTime * this._frameRate);
                    var _ = this._frameIndices[u.frameIndicesOffset + f];
                    if (this._frameIndex !== _) {
                        var m = this._frameIndex;
                        this._frameIndex = _;
                        if (this._timelineArray !== null) {
                            this._frameOffset =
                                this._animationData.frameOffset +
                                this._timelineArray[u.offset + 5 + this._frameIndex];
                            if (o) {
                                if (m < 0) {
                                    var p = Math.floor(r * this._frameRate);
                                    m = this._frameIndices[u.frameIndicesOffset + p];
                                    if (this.currentPlayTimes === a) {
                                        if (m === _) {
                                            m = -1;
                                        }
                                    }
                                }
                                while (m >= 0) {
                                    var c =
                                        this._animationData.frameOffset +
                                        this._timelineArray[u.offset + 5 + m];
                                    var d = this._frameArray[c] / this._frameRate;
                                    if (
                                        this._position <= d &&
                                        d <= this._position + this._duration
                                    ) {
                                        this._onCrossFrame(m);
                                    }
                                    if (l !== null && m === 0) {
                                        this._armature._dragonBones.bufferEvent(l);
                                        l = null;
                                    }
                                    if (m > 0) {
                                        m--;
                                    } else {
                                        m = this._frameCount - 1;
                                    }
                                    if (m === _) {
                                        break;
                                    }
                                }
                            } else {
                                if (m < 0) {
                                    var p = Math.floor(r * this._frameRate);
                                    m = this._frameIndices[u.frameIndicesOffset + p];
                                    var c =
                                        this._animationData.frameOffset +
                                        this._timelineArray[u.offset + 5 + m];
                                    var d = this._frameArray[c] / this._frameRate;
                                    if (this.currentPlayTimes === a) {
                                        if (r <= d) {
                                            if (m > 0) {
                                                m--;
                                            } else {
                                                m = this._frameCount - 1;
                                            }
                                        } else if (m === _) {
                                            m = -1;
                                        }
                                    }
                                }
                                while (m >= 0) {
                                    if (m < this._frameCount - 1) {
                                        m++;
                                    } else {
                                        m = 0;
                                    }
                                    var c =
                                        this._animationData.frameOffset +
                                        this._timelineArray[u.offset + 5 + m];
                                    var d = this._frameArray[c] / this._frameRate;
                                    if (
                                        this._position <= d &&
                                        d <= this._position + this._duration
                                    ) {
                                        this._onCrossFrame(m);
                                    }
                                    if (l !== null && m === 0) {
                                        this._armature._dragonBones.bufferEvent(l);
                                        l = null;
                                    }
                                    if (m === _) {
                                        break;
                                    }
                                }
                            }
                        }
                    }
                } else if (this._frameIndex < 0) {
                    this._frameIndex = 0;
                    if (this._timelineData !== null) {
                        this._frameOffset =
                            this._animationData.frameOffset +
                            this._timelineArray[this._timelineData.offset + 5];
                        var d = this._frameArray[this._frameOffset] / this._frameRate;
                        if (this.currentPlayTimes === a) {
                            if (r <= d) {
                                this._onCrossFrame(this._frameIndex);
                            }
                        } else if (this._position <= d) {
                            if (!o && l !== null) {
                                this._armature._dragonBones.bufferEvent(l);
                                l = null;
                            }
                            this._onCrossFrame(this._frameIndex);
                        }
                    }
                }
                if (l !== null) {
                    this._armature._dragonBones.bufferEvent(l);
                }
                if (h !== null) {
                    this._armature._dragonBones.bufferEvent(h);
                }
            }
        };
        e.prototype.setCurrentTime = function (t) {
            this._setCurrentTime(t);
            this._frameIndex = -1;
        };
        return e;
    })(y.TimelineState);
    y.ActionTimelineState = t;
    var e = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.ZOrderTimelineState]';
        };
        e.prototype._onArriveAtFrame = function () {
            if (this.playState >= 0) {
                var t = this._frameArray[this._frameOffset + 1];
                if (t > 0) {
                    this._armature._sortZOrder(this._frameArray, this._frameOffset + 2);
                } else {
                    this._armature._sortZOrder(null, 0);
                }
            }
        };
        e.prototype._onUpdateFrame = function () {};
        return e;
    })(y.TimelineState);
    y.ZOrderTimelineState = e;
    var a = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.BoneAllTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            r.prototype._onArriveAtFrame.call(this);
            if (this._isTween && this._frameIndex === this._frameCount - 1) {
                this._rd[2] = y.Transform.normalizeRadian(this._rd[2]);
                this._rd[3] = y.Transform.normalizeRadian(this._rd[3]);
            }
            if (this._timelineData === null) {
                this._rd[4] = 1;
                this._rd[5] = 1;
            }
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameFloatOffset;
            this._valueCount = 6;
            this._valueArray = this._animationData.parent.parent.frameFloatArray;
        };
        t.prototype.fadeOut = function () {
            this.dirty = false;
            this._rd[2] = y.Transform.normalizeRadian(this._rd[2]);
            this._rd[3] = y.Transform.normalizeRadian(this._rd[3]);
        };
        t.prototype.blend = function (t) {
            var e = this._armature.armatureData.scale;
            var a = this._rd;
            var r = this.target;
            var i = r.target;
            var n = r.blendWeight;
            var s = i.animationPose;
            if (r.dirty > 1) {
                s.x += a[0] * n * e;
                s.y += a[1] * n * e;
                s.rotation += a[2] * n;
                s.skew += a[3] * n;
                s.scaleX += (a[4] - 1) * n;
                s.scaleY += (a[5] - 1) * n;
            } else {
                s.x = a[0] * n * e;
                s.y = a[1] * n * e;
                s.rotation = a[2] * n;
                s.skew = a[3] * n;
                s.scaleX = (a[4] - 1) * n + 1;
                s.scaleY = (a[5] - 1) * n + 1;
            }
            if (t || this.dirty) {
                this.dirty = false;
                i._transformDirty = true;
            }
        };
        return t;
    })(y.MutilpleValueTimelineState);
    y.BoneAllTimelineState = a;
    var r = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.BoneTranslateTimelineState]';
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameFloatOffset;
            this._valueScale = this._armature.armatureData.scale;
            this._valueArray = this._animationData.parent.parent.frameFloatArray;
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            var i = a.animationPose;
            if (e.dirty > 1) {
                i.x += this._resultA * r;
                i.y += this._resultB * r;
            } else if (r !== 1) {
                i.x = this._resultA * r;
                i.y = this._resultB * r;
            } else {
                i.x = this._resultA;
                i.y = this._resultB;
            }
            if (t || this.dirty) {
                this.dirty = false;
                a._transformDirty = true;
            }
        };
        return t;
    })(y.DoubleValueTimelineState);
    y.BoneTranslateTimelineState = r;
    var i = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.BoneRotateTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            r.prototype._onArriveAtFrame.call(this);
            if (this._isTween && this._frameIndex === this._frameCount - 1) {
                this._differenceA = y.Transform.normalizeRadian(this._differenceA);
                this._differenceB = y.Transform.normalizeRadian(this._differenceB);
            }
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameFloatOffset;
            this._valueArray = this._animationData.parent.parent.frameFloatArray;
        };
        t.prototype.fadeOut = function () {
            this.dirty = false;
            this._resultA = y.Transform.normalizeRadian(this._resultA);
            this._resultB = y.Transform.normalizeRadian(this._resultB);
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            var i = a.animationPose;
            if (e.dirty > 1) {
                i.rotation += this._resultA * r;
                i.skew += this._resultB * r;
            } else if (r !== 1) {
                i.rotation = this._resultA * r;
                i.skew = this._resultB * r;
            } else {
                i.rotation = this._resultA;
                i.skew = this._resultB;
            }
            if (t || this.dirty) {
                this.dirty = false;
                a._transformDirty = true;
            }
        };
        return t;
    })(y.DoubleValueTimelineState);
    y.BoneRotateTimelineState = i;
    var n = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.BoneScaleTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            r.prototype._onArriveAtFrame.call(this);
            if (this._timelineData === null) {
                this._resultA = 1;
                this._resultB = 1;
            }
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameFloatOffset;
            this._valueArray = this._animationData.parent.parent.frameFloatArray;
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            var i = a.animationPose;
            if (e.dirty > 1) {
                i.scaleX += (this._resultA - 1) * r;
                i.scaleY += (this._resultB - 1) * r;
            } else if (r !== 1) {
                i.scaleX = (this._resultA - 1) * r + 1;
                i.scaleY = (this._resultB - 1) * r + 1;
            } else {
                i.scaleX = this._resultA;
                i.scaleY = this._resultB;
            }
            if (t || this.dirty) {
                this.dirty = false;
                a._transformDirty = true;
            }
        };
        return t;
    })(y.DoubleValueTimelineState);
    y.BoneScaleTimelineState = n;
    var s = (function (s) {
        __extends(t, s);
        function t() {
            return (s !== null && s.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.SurfaceTimelineState]';
        };
        t.prototype._onClear = function () {
            s.prototype._onClear.call(this);
            this._deformCount = 0;
            this._deformOffset = 0;
            this._sameValueOffset = 0;
        };
        t.prototype.init = function (t, e, a) {
            s.prototype.init.call(this, t, e, a);
            if (this._timelineData !== null) {
                var r = this._animationData.parent.parent;
                var i = r.frameIntArray;
                var n =
                    this._animationData.frameIntOffset +
                    this._timelineArray[this._timelineData.offset + 3];
                this._valueOffset = this._animationData.frameFloatOffset;
                this._valueCount = i[n + 2];
                this._deformCount = i[n + 1];
                this._deformOffset = i[n + 3];
                this._sameValueOffset = i[n + 4] + this._animationData.frameFloatOffset;
                this._valueScale = this._armature.armatureData.scale;
                this._valueArray = r.frameFloatArray;
                this._rd.length = this._valueCount * 2;
            } else {
                this._deformCount = this.target.target._deformVertices.length;
            }
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            var i = a._deformVertices;
            var n = this._valueArray;
            if (n !== null) {
                var s = this._valueCount;
                var o = this._deformOffset;
                var l = this._sameValueOffset;
                var h = this._rd;
                for (var u = 0; u < this._deformCount; ++u) {
                    var f = 0;
                    if (u < o) {
                        f = n[l + u];
                    } else if (u < o + s) {
                        f = h[u - o];
                    } else {
                        f = n[l + u - s];
                    }
                    if (e.dirty > 1) {
                        i[u] += f * r;
                    } else {
                        i[u] = f * r;
                    }
                }
            } else if (e.dirty === 1) {
                for (var u = 0; u < this._deformCount; ++u) {
                    i[u] = 0;
                }
            }
            if (t || this.dirty) {
                this.dirty = false;
                a._transformDirty = true;
            }
        };
        return t;
    })(y.MutilpleValueTimelineState);
    y.SurfaceTimelineState = s;
    var o = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.AlphaTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            r.prototype._onArriveAtFrame.call(this);
            if (this._timelineData === null) {
                this._result = 1;
            }
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueScale = 0.01;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            if (e.dirty > 1) {
                a._alpha += this._result * r;
                if (a._alpha > 1) {
                    a._alpha = 1;
                }
            } else {
                a._alpha = this._result * r;
            }
            if (t || this.dirty) {
                this.dirty = false;
                this._armature._alphaDirty = true;
            }
        };
        return t;
    })(y.SingleValueTimelineState);
    y.AlphaTimelineState = o;
    var l = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.SlotDisplayTimelineState]';
        };
        e.prototype._onArriveAtFrame = function () {
            if (this.playState >= 0) {
                var t = this.target;
                var e =
                    this._timelineData !== null
                        ? this._frameArray[this._frameOffset + 1]
                        : t._slotData.displayIndex;
                if (t.displayIndex !== e) {
                    t._setDisplayIndex(e, true);
                }
            }
        };
        e.prototype._onUpdateFrame = function () {};
        return e;
    })(y.TimelineState);
    y.SlotDisplayTimelineState = l;
    var h = (function (o) {
        __extends(t, o);
        function t() {
            var t = (o !== null && o.apply(this, arguments)) || this;
            t._current = [0, 0, 0, 0, 0, 0, 0, 0];
            t._difference = [0, 0, 0, 0, 0, 0, 0, 0];
            t._result = [0, 0, 0, 0, 0, 0, 0, 0];
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.SlotColorTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            o.prototype._onArriveAtFrame.call(this);
            if (this._timelineData !== null) {
                var t = this._animationData.parent.parent;
                var e = t.colorArray;
                var a = t.frameIntArray;
                var r =
                    this._animationData.frameIntOffset + this._frameValueOffset + this._frameIndex;
                var i = a[r];
                if (i < 0) {
                    i += 65536;
                }
                if (this._isTween) {
                    this._current[0] = e[i++];
                    this._current[1] = e[i++];
                    this._current[2] = e[i++];
                    this._current[3] = e[i++];
                    this._current[4] = e[i++];
                    this._current[5] = e[i++];
                    this._current[6] = e[i++];
                    this._current[7] = e[i++];
                    if (this._frameIndex === this._frameCount - 1) {
                        i = a[this._animationData.frameIntOffset + this._frameValueOffset];
                    } else {
                        i = a[r + 1];
                    }
                    if (i < 0) {
                        i += 65536;
                    }
                    this._difference[0] = e[i++] - this._current[0];
                    this._difference[1] = e[i++] - this._current[1];
                    this._difference[2] = e[i++] - this._current[2];
                    this._difference[3] = e[i++] - this._current[3];
                    this._difference[4] = e[i++] - this._current[4];
                    this._difference[5] = e[i++] - this._current[5];
                    this._difference[6] = e[i++] - this._current[6];
                    this._difference[7] = e[i++] - this._current[7];
                } else {
                    this._result[0] = e[i++] * 0.01;
                    this._result[1] = e[i++] * 0.01;
                    this._result[2] = e[i++] * 0.01;
                    this._result[3] = e[i++] * 0.01;
                    this._result[4] = e[i++];
                    this._result[5] = e[i++];
                    this._result[6] = e[i++];
                    this._result[7] = e[i++];
                }
            } else {
                var n = this.target;
                var s = n.slotData.color;
                this._result[0] = s.alphaMultiplier;
                this._result[1] = s.redMultiplier;
                this._result[2] = s.greenMultiplier;
                this._result[3] = s.blueMultiplier;
                this._result[4] = s.alphaOffset;
                this._result[5] = s.redOffset;
                this._result[6] = s.greenOffset;
                this._result[7] = s.blueOffset;
            }
        };
        t.prototype._onUpdateFrame = function () {
            o.prototype._onUpdateFrame.call(this);
            if (this._isTween) {
                this._result[0] =
                    (this._current[0] + this._difference[0] * this._tweenProgress) * 0.01;
                this._result[1] =
                    (this._current[1] + this._difference[1] * this._tweenProgress) * 0.01;
                this._result[2] =
                    (this._current[2] + this._difference[2] * this._tweenProgress) * 0.01;
                this._result[3] =
                    (this._current[3] + this._difference[3] * this._tweenProgress) * 0.01;
                this._result[4] = this._current[4] + this._difference[4] * this._tweenProgress;
                this._result[5] = this._current[5] + this._difference[5] * this._tweenProgress;
                this._result[6] = this._current[6] + this._difference[6] * this._tweenProgress;
                this._result[7] = this._current[7] + this._difference[7] * this._tweenProgress;
            }
        };
        t.prototype.fadeOut = function () {
            this._isTween = false;
        };
        t.prototype.update = function (t) {
            o.prototype.update.call(this, t);
            if (this._isTween || this.dirty) {
                var e = this.target;
                var a = e._colorTransform;
                if (
                    this._animationState._fadeState !== 0 ||
                    this._animationState._subFadeState !== 0
                ) {
                    if (
                        a.alphaMultiplier !== this._result[0] ||
                        a.redMultiplier !== this._result[1] ||
                        a.greenMultiplier !== this._result[2] ||
                        a.blueMultiplier !== this._result[3] ||
                        a.alphaOffset !== this._result[4] ||
                        a.redOffset !== this._result[5] ||
                        a.greenOffset !== this._result[6] ||
                        a.blueOffset !== this._result[7]
                    ) {
                        var r = Math.pow(this._animationState._fadeProgress, 4);
                        a.alphaMultiplier += (this._result[0] - a.alphaMultiplier) * r;
                        a.redMultiplier += (this._result[1] - a.redMultiplier) * r;
                        a.greenMultiplier += (this._result[2] - a.greenMultiplier) * r;
                        a.blueMultiplier += (this._result[3] - a.blueMultiplier) * r;
                        a.alphaOffset += (this._result[4] - a.alphaOffset) * r;
                        a.redOffset += (this._result[5] - a.redOffset) * r;
                        a.greenOffset += (this._result[6] - a.greenOffset) * r;
                        a.blueOffset += (this._result[7] - a.blueOffset) * r;
                        e._colorDirty = true;
                    }
                } else if (this.dirty) {
                    this.dirty = false;
                    if (
                        a.alphaMultiplier !== this._result[0] ||
                        a.redMultiplier !== this._result[1] ||
                        a.greenMultiplier !== this._result[2] ||
                        a.blueMultiplier !== this._result[3] ||
                        a.alphaOffset !== this._result[4] ||
                        a.redOffset !== this._result[5] ||
                        a.greenOffset !== this._result[6] ||
                        a.blueOffset !== this._result[7]
                    ) {
                        a.alphaMultiplier = this._result[0];
                        a.redMultiplier = this._result[1];
                        a.greenMultiplier = this._result[2];
                        a.blueMultiplier = this._result[3];
                        a.alphaOffset = this._result[4];
                        a.redOffset = this._result[5];
                        a.greenOffset = this._result[6];
                        a.blueOffset = this._result[7];
                        e._colorDirty = true;
                    }
                }
            }
        };
        return t;
    })(y.TweenTimelineState);
    y.SlotColorTimelineState = h;
    var u = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.SlotZIndexTimelineState]';
        };
        t.prototype._onArriveAtFrame = function () {
            r.prototype._onArriveAtFrame.call(this);
            if (this._timelineData === null) {
                var t = this.target;
                var e = t.target;
                this._result = e.slotData.zIndex;
            }
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            if (e.dirty > 1) {
                a._zIndex += this._result * r;
            } else {
                a._zIndex = this._result * r;
            }
            if (t || this.dirty) {
                this.dirty = false;
                this._armature._zIndexDirty = true;
            }
        };
        return t;
    })(y.SingleValueTimelineState);
    y.SlotZIndexTimelineState = u;
    var f = (function (f) {
        __extends(t, f);
        function t() {
            return (f !== null && f.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.DeformTimelineState]';
        };
        t.prototype._onClear = function () {
            f.prototype._onClear.call(this);
            this.geometryOffset = 0;
            this.displayFrame = null;
            this._deformCount = 0;
            this._deformOffset = 0;
            this._sameValueOffset = 0;
        };
        t.prototype.init = function (t, e, a) {
            f.prototype.init.call(this, t, e, a);
            if (this._timelineData !== null) {
                var r =
                    this._animationData.frameIntOffset +
                    this._timelineArray[this._timelineData.offset + 3];
                var i = this._animationData.parent.parent;
                var n = i.frameIntArray;
                var s = this.target.target;
                this.geometryOffset = n[r + 0];
                if (this.geometryOffset < 0) {
                    this.geometryOffset += 65536;
                }
                for (var o = 0, l = s.displayFrameCount; o < l; ++o) {
                    var h = s.getDisplayFrameAt(o);
                    var u = h.getGeometryData();
                    if (u === null) {
                        continue;
                    }
                    if (u.offset === this.geometryOffset) {
                        this.displayFrame = h;
                        this.displayFrame.updateDeformVertices();
                        break;
                    }
                }
                if (this.displayFrame === null) {
                    this.returnToPool();
                    return;
                }
                this._valueOffset = this._animationData.frameFloatOffset;
                this._valueCount = n[r + 2];
                this._deformCount = n[r + 1];
                this._deformOffset = n[r + 3];
                this._sameValueOffset = n[r + 4] + this._animationData.frameFloatOffset;
                this._valueScale = this._armature.armatureData.scale;
                this._valueArray = i.frameFloatArray;
                this._rd.length = this._valueCount * 2;
            } else {
                this._deformCount = this.displayFrame.deformVertices.length;
            }
        };
        t.prototype.blend = function (t) {
            var e = this.target;
            var a = e.target;
            var r = e.blendWeight;
            var i = this.displayFrame.deformVertices;
            var n = this._valueArray;
            if (n !== null) {
                var s = this._valueCount;
                var o = this._deformOffset;
                var l = this._sameValueOffset;
                var h = this._rd;
                for (var u = 0; u < this._deformCount; ++u) {
                    var f = 0;
                    if (u < o) {
                        f = n[l + u];
                    } else if (u < o + s) {
                        f = h[u - o];
                    } else {
                        f = n[l + u - s];
                    }
                    if (e.dirty > 1) {
                        i[u] += f * r;
                    } else {
                        i[u] = f * r;
                    }
                }
            } else if (e.dirty === 1) {
                for (var u = 0; u < this._deformCount; ++u) {
                    i[u] = 0;
                }
            }
            if (t || this.dirty) {
                this.dirty = false;
                if (a._geometryData === this.displayFrame.getGeometryData()) {
                    a._verticesDirty = true;
                }
            }
        };
        return t;
    })(y.MutilpleValueTimelineState);
    y.DeformTimelineState = f;
    var _ = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.IKConstraintTimelineState]';
        };
        t.prototype._onUpdateFrame = function () {
            r.prototype._onUpdateFrame.call(this);
            var t = this.target;
            if (this._timelineData !== null) {
                t._bendPositive = this._currentA > 0;
                t._weight = this._currentB;
            } else {
                var e = t._constraintData;
                t._bendPositive = e.bendPositive;
                t._weight = e.weight;
            }
            t.invalidUpdate();
            this.dirty = false;
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueScale = 0.01;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        return t;
    })(y.DoubleValueTimelineState);
    y.IKConstraintTimelineState = _;
    var m = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationProgressTimelineState]';
        };
        t.prototype._onUpdateFrame = function () {
            r.prototype._onUpdateFrame.call(this);
            var t = this.target;
            if (t._parent !== null) {
                t.currentTime = this._result * t.totalTime;
            }
            this.dirty = false;
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueScale = 1e-4;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        return t;
    })(y.SingleValueTimelineState);
    y.AnimationProgressTimelineState = m;
    var p = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationWeightTimelineState]';
        };
        t.prototype._onUpdateFrame = function () {
            r.prototype._onUpdateFrame.call(this);
            var t = this.target;
            if (t._parent !== null) {
                t.weight = this._result;
            }
            this.dirty = false;
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueScale = 1e-4;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        return t;
    })(y.SingleValueTimelineState);
    y.AnimationWeightTimelineState = p;
    var c = (function (r) {
        __extends(t, r);
        function t() {
            return (r !== null && r.apply(this, arguments)) || this;
        }
        t.toString = function () {
            return '[class dragonBones.AnimationParametersTimelineState]';
        };
        t.prototype._onUpdateFrame = function () {
            r.prototype._onUpdateFrame.call(this);
            var t = this.target;
            if (t._parent !== null) {
                t.parameterX = this._resultA;
                t.parameterY = this._resultB;
            }
            this.dirty = false;
        };
        t.prototype.init = function (t, e, a) {
            r.prototype.init.call(this, t, e, a);
            this._valueOffset = this._animationData.frameIntOffset;
            this._valueScale = 1e-4;
            this._valueArray = this._animationData.parent.parent.frameIntArray;
        };
        return t;
    })(y.DoubleValueTimelineState);
    y.AnimationParametersTimelineState = c;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function (t) {
        __extends(r, t);
        function r() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        r.actionDataToInstance = function (t, e, a) {
            if (t.type === 0) {
                e.type = r.FRAME_EVENT;
            } else {
                e.type = t.type === 10 ? r.FRAME_EVENT : r.SOUND_EVENT;
            }
            e.name = t.name;
            e.armature = a;
            e.actionData = t;
            e.data = t.data;
            if (t.bone !== null) {
                e.bone = a.getBone(t.bone.name);
            }
            if (t.slot !== null) {
                e.slot = a.getSlot(t.slot.name);
            }
        };
        r.toString = function () {
            return '[class dragonBones.EventObject]';
        };
        r.prototype._onClear = function () {
            this.time = 0;
            this.type = '';
            this.name = '';
            this.armature = null;
            this.bone = null;
            this.slot = null;
            this.animationState = null;
            this.actionData = null;
            this.data = null;
        };
        r.START = 'start';
        r.LOOP_COMPLETE = 'loopComplete';
        r.COMPLETE = 'complete';
        r.FADE_IN = 'fadeIn';
        r.FADE_IN_COMPLETE = 'fadeInComplete';
        r.FADE_OUT = 'fadeOut';
        r.FADE_OUT_COMPLETE = 'fadeOutComplete';
        r.FRAME_EVENT = 'frameEvent';
        r.SOUND_EVENT = 'soundEvent';
        return r;
    })(t.BaseObject);
    t.EventObject = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (t) {
    var e = (function () {
        function t() {}
        t._getArmatureType = function (t) {
            switch (t.toLowerCase()) {
                case 'stage':
                    return 2;
                case 'armature':
                    return 0;
                case 'movieclip':
                    return 1;
                default:
                    return 0;
            }
        };
        t._getBoneType = function (t) {
            switch (t.toLowerCase()) {
                case 'bone':
                    return 0;
                case 'surface':
                    return 1;
                default:
                    return 0;
            }
        };
        t._getPositionMode = function (t) {
            switch (t.toLocaleLowerCase()) {
                case 'percent':
                    return 1;
                case 'fixed':
                    return 0;
                default:
                    return 1;
            }
        };
        t._getSpacingMode = function (t) {
            switch (t.toLocaleLowerCase()) {
                case 'length':
                    return 0;
                case 'percent':
                    return 2;
                case 'fixed':
                    return 1;
                default:
                    return 0;
            }
        };
        t._getRotateMode = function (t) {
            switch (t.toLocaleLowerCase()) {
                case 'tangent':
                    return 0;
                case 'chain':
                    return 1;
                case 'chainscale':
                    return 2;
                default:
                    return 0;
            }
        };
        t._getDisplayType = function (t) {
            switch (t.toLowerCase()) {
                case 'image':
                    return 0;
                case 'mesh':
                    return 2;
                case 'armature':
                    return 1;
                case 'boundingbox':
                    return 3;
                case 'path':
                    return 4;
                default:
                    return 0;
            }
        };
        t._getBoundingBoxType = function (t) {
            switch (t.toLowerCase()) {
                case 'rectangle':
                    return 0;
                case 'ellipse':
                    return 1;
                case 'polygon':
                    return 2;
                default:
                    return 0;
            }
        };
        t._getBlendMode = function (t) {
            switch (t.toLowerCase()) {
                case 'normal':
                    return 0;
                case 'add':
                    return 1;
                case 'alpha':
                    return 2;
                case 'darken':
                    return 3;
                case 'difference':
                    return 4;
                case 'erase':
                    return 5;
                case 'hardlight':
                    return 6;
                case 'invert':
                    return 7;
                case 'layer':
                    return 8;
                case 'lighten':
                    return 9;
                case 'multiply':
                    return 10;
                case 'overlay':
                    return 11;
                case 'screen':
                    return 12;
                case 'subtract':
                    return 13;
                default:
                    return 0;
            }
        };
        t._getAnimationBlendType = function (t) {
            switch (t.toLowerCase()) {
                case 'none':
                    return 0;
                case '1d':
                    return 1;
                default:
                    return 0;
            }
        };
        t._getActionType = function (t) {
            switch (t.toLowerCase()) {
                case 'play':
                    return 0;
                case 'frame':
                    return 10;
                case 'sound':
                    return 11;
                default:
                    return 0;
            }
        };
        t.DATA_VERSION_2_3 = '2.3';
        t.DATA_VERSION_3_0 = '3.0';
        t.DATA_VERSION_4_0 = '4.0';
        t.DATA_VERSION_4_5 = '4.5';
        t.DATA_VERSION_5_0 = '5.0';
        t.DATA_VERSION_5_5 = '5.5';
        t.DATA_VERSION_5_6 = '5.6';
        t.DATA_VERSION = t.DATA_VERSION_5_6;
        t.DATA_VERSIONS = [
            t.DATA_VERSION_4_0,
            t.DATA_VERSION_4_5,
            t.DATA_VERSION_5_0,
            t.DATA_VERSION_5_5,
            t.DATA_VERSION_5_6,
        ];
        t.TEXTURE_ATLAS = 'textureAtlas';
        t.SUB_TEXTURE = 'SubTexture';
        t.FORMAT = 'format';
        t.IMAGE_PATH = 'imagePath';
        t.WIDTH = 'width';
        t.HEIGHT = 'height';
        t.ROTATED = 'rotated';
        t.FRAME_X = 'frameX';
        t.FRAME_Y = 'frameY';
        t.FRAME_WIDTH = 'frameWidth';
        t.FRAME_HEIGHT = 'frameHeight';
        t.DRADON_BONES = 'dragonBones';
        t.USER_DATA = 'userData';
        t.ARMATURE = 'armature';
        t.CANVAS = 'canvas';
        t.BONE = 'bone';
        t.SURFACE = 'surface';
        t.SLOT = 'slot';
        t.CONSTRAINT = 'constraint';
        t.SKIN = 'skin';
        t.DISPLAY = 'display';
        t.FRAME = 'frame';
        t.IK = 'ik';
        t.PATH_CONSTRAINT = 'path';
        t.ANIMATION = 'animation';
        t.TIMELINE = 'timeline';
        t.FFD = 'ffd';
        t.TRANSLATE_FRAME = 'translateFrame';
        t.ROTATE_FRAME = 'rotateFrame';
        t.SCALE_FRAME = 'scaleFrame';
        t.DISPLAY_FRAME = 'displayFrame';
        t.COLOR_FRAME = 'colorFrame';
        t.DEFAULT_ACTIONS = 'defaultActions';
        t.ACTIONS = 'actions';
        t.EVENTS = 'events';
        t.INTS = 'ints';
        t.FLOATS = 'floats';
        t.STRINGS = 'strings';
        t.TRANSFORM = 'transform';
        t.PIVOT = 'pivot';
        t.AABB = 'aabb';
        t.COLOR = 'color';
        t.VERSION = 'version';
        t.COMPATIBLE_VERSION = 'compatibleVersion';
        t.FRAME_RATE = 'frameRate';
        t.TYPE = 'type';
        t.SUB_TYPE = 'subType';
        t.NAME = 'name';
        t.PARENT = 'parent';
        t.TARGET = 'target';
        t.STAGE = 'stage';
        t.SHARE = 'share';
        t.PATH = 'path';
        t.LENGTH = 'length';
        t.DISPLAY_INDEX = 'displayIndex';
        t.Z_ORDER = 'zOrder';
        t.Z_INDEX = 'zIndex';
        t.BLEND_MODE = 'blendMode';
        t.INHERIT_TRANSLATION = 'inheritTranslation';
        t.INHERIT_ROTATION = 'inheritRotation';
        t.INHERIT_SCALE = 'inheritScale';
        t.INHERIT_REFLECTION = 'inheritReflection';
        t.INHERIT_ANIMATION = 'inheritAnimation';
        t.INHERIT_DEFORM = 'inheritDeform';
        t.SEGMENT_X = 'segmentX';
        t.SEGMENT_Y = 'segmentY';
        t.BEND_POSITIVE = 'bendPositive';
        t.CHAIN = 'chain';
        t.WEIGHT = 'weight';
        t.BLEND_TYPE = 'blendType';
        t.FADE_IN_TIME = 'fadeInTime';
        t.PLAY_TIMES = 'playTimes';
        t.SCALE = 'scale';
        t.OFFSET = 'offset';
        t.POSITION = 'position';
        t.DURATION = 'duration';
        t.TWEEN_EASING = 'tweenEasing';
        t.TWEEN_ROTATE = 'tweenRotate';
        t.TWEEN_SCALE = 'tweenScale';
        t.CLOCK_WISE = 'clockwise';
        t.CURVE = 'curve';
        t.SOUND = 'sound';
        t.EVENT = 'event';
        t.ACTION = 'action';
        t.X = 'x';
        t.Y = 'y';
        t.SKEW_X = 'skX';
        t.SKEW_Y = 'skY';
        t.SCALE_X = 'scX';
        t.SCALE_Y = 'scY';
        t.VALUE = 'value';
        t.ROTATE = 'rotate';
        t.SKEW = 'skew';
        t.ALPHA = 'alpha';
        t.ALPHA_OFFSET = 'aO';
        t.RED_OFFSET = 'rO';
        t.GREEN_OFFSET = 'gO';
        t.BLUE_OFFSET = 'bO';
        t.ALPHA_MULTIPLIER = 'aM';
        t.RED_MULTIPLIER = 'rM';
        t.GREEN_MULTIPLIER = 'gM';
        t.BLUE_MULTIPLIER = 'bM';
        t.UVS = 'uvs';
        t.VERTICES = 'vertices';
        t.TRIANGLES = 'triangles';
        t.WEIGHTS = 'weights';
        t.SLOT_POSE = 'slotPose';
        t.BONE_POSE = 'bonePose';
        t.BONES = 'bones';
        t.POSITION_MODE = 'positionMode';
        t.SPACING_MODE = 'spacingMode';
        t.ROTATE_MODE = 'rotateMode';
        t.SPACING = 'spacing';
        t.ROTATE_OFFSET = 'rotateOffset';
        t.ROTATE_MIX = 'rotateMix';
        t.TRANSLATE_MIX = 'translateMix';
        t.TARGET_DISPLAY = 'targetDisplay';
        t.CLOSED = 'closed';
        t.CONSTANT_SPEED = 'constantSpeed';
        t.VERTEX_COUNT = 'vertexCount';
        t.LENGTHS = 'lengths';
        t.GOTO_AND_PLAY = 'gotoAndPlay';
        t.DEFAULT_NAME = 'default';
        return t;
    })();
    t.DataParser = e;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (tt) {
    var t = (function (e) {
        __extends($, e);
        function $() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._rawTextureAtlasIndex = 0;
            t._rawBones = [];
            t._data = null;
            t._armature = null;
            t._bone = null;
            t._geometry = null;
            t._slot = null;
            t._skin = null;
            t._mesh = null;
            t._animation = null;
            t._timeline = null;
            t._rawTextureAtlases = null;
            t._frameValueType = 0;
            t._defaultColorOffset = -1;
            t._prevClockwise = 0;
            t._prevRotation = 0;
            t._frameDefaultValue = 0;
            t._frameValueScale = 1;
            t._helpMatrixA = new tt.Matrix();
            t._helpMatrixB = new tt.Matrix();
            t._helpTransform = new tt.Transform();
            t._helpColorTransform = new tt.ColorTransform();
            t._helpPoint = new tt.Point();
            t._helpArray = [];
            t._intArray = [];
            t._floatArray = [];
            t._frameIntArray = [];
            t._frameFloatArray = [];
            t._frameArray = [];
            t._timelineArray = [];
            t._colorArray = [];
            t._cacheRawMeshes = [];
            t._cacheMeshes = [];
            t._actionFrames = [];
            t._weightSlotPose = {};
            t._weightBonePoses = {};
            t._cacheBones = {};
            t._slotChildActions = {};
            return t;
        }
        $._getBoolean = function (t, e, a) {
            if (e in t) {
                var r = t[e];
                var i = typeof r;
                if (i === 'boolean') {
                    return r;
                } else if (i === 'string') {
                    switch (r) {
                        case '0':
                        case 'NaN':
                        case '':
                        case 'false':
                        case 'null':
                        case 'undefined':
                            return false;
                        default:
                            return true;
                    }
                } else {
                    return !!r;
                }
            }
            return a;
        };
        $._getNumber = function (t, e, a) {
            if (e in t) {
                var r = t[e];
                if (r === null || r === 'NaN') {
                    return a;
                }
                return +r || 0;
            }
            return a;
        };
        $._getString = function (t, e, a) {
            if (e in t) {
                var r = t[e];
                var i = typeof r;
                if (i === 'string') {
                    return r;
                }
                return String(r);
            }
            return a;
        };
        $.prototype._getCurvePoint = function (t, e, a, r, i, n, s, o, l, h) {
            var u = 1 - l;
            var f = u * u;
            var _ = l * l;
            var m = u * f;
            var p = 3 * l * f;
            var c = 3 * u * _;
            var d = l * _;
            h.x = m * t + p * a + c * i + d * s;
            h.y = m * e + p * r + c * n + d * o;
        };
        $.prototype._samplingEasingCurve = function (t, e) {
            var a = t.length;
            if (a % 3 === 1) {
                var r = -2;
                for (var i = 0, n = e.length; i < n; ++i) {
                    var s = (i + 1) / (n + 1);
                    while ((r + 6 < a ? t[r + 6] : 1) < s) {
                        r += 6;
                    }
                    var o = r >= 0 && r + 6 < a;
                    var l = o ? t[r] : 0;
                    var h = o ? t[r + 1] : 0;
                    var u = t[r + 2];
                    var f = t[r + 3];
                    var _ = t[r + 4];
                    var m = t[r + 5];
                    var p = o ? t[r + 6] : 1;
                    var c = o ? t[r + 7] : 1;
                    var d = 0;
                    var y = 1;
                    while (y - d > 1e-4) {
                        var v = (y + d) * 0.5;
                        this._getCurvePoint(l, h, u, f, _, m, p, c, v, this._helpPoint);
                        if (s - this._helpPoint.x > 0) {
                            d = v;
                        } else {
                            y = v;
                        }
                    }
                    e[i] = this._helpPoint.y;
                }
                return true;
            } else {
                var r = 0;
                for (var i = 0, n = e.length; i < n; ++i) {
                    var s = (i + 1) / (n + 1);
                    while (t[r + 6] < s) {
                        r += 6;
                    }
                    var l = t[r];
                    var h = t[r + 1];
                    var u = t[r + 2];
                    var f = t[r + 3];
                    var _ = t[r + 4];
                    var m = t[r + 5];
                    var p = t[r + 6];
                    var c = t[r + 7];
                    var d = 0;
                    var y = 1;
                    while (y - d > 1e-4) {
                        var v = (y + d) * 0.5;
                        this._getCurvePoint(l, h, u, f, _, m, p, c, v, this._helpPoint);
                        if (s - this._helpPoint.x > 0) {
                            d = v;
                        } else {
                            y = v;
                        }
                    }
                    e[i] = this._helpPoint.y;
                }
                return false;
            }
        };
        $.prototype._parseActionDataInFrame = function (t, e, a, r) {
            if (tt.DataParser.EVENT in t) {
                this._mergeActionFrame(t[tt.DataParser.EVENT], e, 10, a, r);
            }
            if (tt.DataParser.SOUND in t) {
                this._mergeActionFrame(t[tt.DataParser.SOUND], e, 11, a, r);
            }
            if (tt.DataParser.ACTION in t) {
                this._mergeActionFrame(t[tt.DataParser.ACTION], e, 0, a, r);
            }
            if (tt.DataParser.EVENTS in t) {
                this._mergeActionFrame(t[tt.DataParser.EVENTS], e, 10, a, r);
            }
            if (tt.DataParser.ACTIONS in t) {
                this._mergeActionFrame(t[tt.DataParser.ACTIONS], e, 0, a, r);
            }
        };
        $.prototype._mergeActionFrame = function (t, e, a, r, i) {
            var n = this._armature.actions.length;
            var s = this._parseActionData(t, a, r, i);
            var o = 0;
            var l = null;
            for (var h = 0, u = s; h < u.length; h++) {
                var f = u[h];
                this._armature.addAction(f, false);
            }
            if (this._actionFrames.length === 0) {
                l = new D();
                l.frameStart = 0;
                this._actionFrames.push(l);
                l = null;
            }
            for (var _ = 0, m = this._actionFrames; _ < m.length; _++) {
                var p = m[_];
                if (p.frameStart === e) {
                    l = p;
                    break;
                } else if (p.frameStart > e) {
                    break;
                }
                o++;
            }
            if (l === null) {
                l = new D();
                l.frameStart = e;
                this._actionFrames.splice(o, 0, l);
            }
            for (var c = 0; c < s.length; ++c) {
                l.actions.push(n + c);
            }
        };
        $.prototype._parseArmature = function (t, e) {
            var a = tt.BaseObject.borrowObject(tt.ArmatureData);
            a.name = $._getString(t, tt.DataParser.NAME, '');
            a.frameRate = $._getNumber(t, tt.DataParser.FRAME_RATE, this._data.frameRate);
            a.scale = e;
            if (tt.DataParser.TYPE in t && typeof t[tt.DataParser.TYPE] === 'string') {
                a.type = tt.DataParser._getArmatureType(t[tt.DataParser.TYPE]);
            } else {
                a.type = $._getNumber(t, tt.DataParser.TYPE, 0);
            }
            if (a.frameRate === 0) {
                a.frameRate = 24;
            }
            this._armature = a;
            if (tt.DataParser.CANVAS in t) {
                var r = t[tt.DataParser.CANVAS];
                var i = tt.BaseObject.borrowObject(tt.CanvasData);
                if (tt.DataParser.COLOR in r) {
                    i.hasBackground = true;
                } else {
                    i.hasBackground = false;
                }
                i.color = $._getNumber(r, tt.DataParser.COLOR, 0);
                i.x = $._getNumber(r, tt.DataParser.X, 0) * a.scale;
                i.y = $._getNumber(r, tt.DataParser.Y, 0) * a.scale;
                i.width = $._getNumber(r, tt.DataParser.WIDTH, 0) * a.scale;
                i.height = $._getNumber(r, tt.DataParser.HEIGHT, 0) * a.scale;
                a.canvas = i;
            }
            if (tt.DataParser.AABB in t) {
                var n = t[tt.DataParser.AABB];
                a.aabb.x = $._getNumber(n, tt.DataParser.X, 0) * a.scale;
                a.aabb.y = $._getNumber(n, tt.DataParser.Y, 0) * a.scale;
                a.aabb.width = $._getNumber(n, tt.DataParser.WIDTH, 0) * a.scale;
                a.aabb.height = $._getNumber(n, tt.DataParser.HEIGHT, 0) * a.scale;
            }
            if (tt.DataParser.BONE in t) {
                var s = t[tt.DataParser.BONE];
                for (var o = 0, l = s; o < l.length; o++) {
                    var h = l[o];
                    var u = $._getString(h, tt.DataParser.PARENT, '');
                    var f = this._parseBone(h);
                    if (u.length > 0) {
                        var _ = a.getBone(u);
                        if (_ !== null) {
                            f.parent = _;
                        } else {
                            if (!(u in this._cacheBones)) {
                                this._cacheBones[u] = [];
                            }
                            this._cacheBones[u].push(f);
                        }
                    }
                    if (f.name in this._cacheBones) {
                        for (var m = 0, p = this._cacheBones[f.name]; m < p.length; m++) {
                            var c = p[m];
                            c.parent = f;
                        }
                        delete this._cacheBones[f.name];
                    }
                    a.addBone(f);
                    this._rawBones.push(f);
                }
            }
            if (tt.DataParser.IK in t) {
                var d = t[tt.DataParser.IK];
                for (var y = 0, v = d; y < v.length; y++) {
                    var g = v[y];
                    var D = this._parseIKConstraint(g);
                    if (D) {
                        a.addConstraint(D);
                    }
                }
            }
            a.sortBones();
            if (tt.DataParser.SLOT in t) {
                var T = 0;
                var b = t[tt.DataParser.SLOT];
                for (var A = 0, P = b; A < P.length; A++) {
                    var S = P[A];
                    a.addSlot(this._parseSlot(S, T++));
                }
            }
            if (tt.DataParser.SKIN in t) {
                var O = t[tt.DataParser.SKIN];
                for (var x = 0, B = O; x < B.length; x++) {
                    var E = B[x];
                    a.addSkin(this._parseSkin(E));
                }
            }
            if (tt.DataParser.PATH_CONSTRAINT in t) {
                var I = t[tt.DataParser.PATH_CONSTRAINT];
                for (var M = 0, F = I; M < F.length; M++) {
                    var w = F[M];
                    var D = this._parsePathConstraint(w);
                    if (D) {
                        a.addConstraint(D);
                    }
                }
            }
            for (var C = 0, N = this._cacheRawMeshes.length; C < N; ++C) {
                var R = this._cacheRawMeshes[C];
                var k = $._getString(R, tt.DataParser.SHARE, '');
                if (k.length === 0) {
                    continue;
                }
                var j = $._getString(R, tt.DataParser.SKIN, tt.DataParser.DEFAULT_NAME);
                if (j.length === 0) {
                    j = tt.DataParser.DEFAULT_NAME;
                }
                var L = a.getMesh(j, '', k);
                if (L === null) {
                    continue;
                }
                var V = this._cacheMeshes[C];
                V.geometry.shareFrom(L.geometry);
            }
            if (tt.DataParser.ANIMATION in t) {
                var Y = t[tt.DataParser.ANIMATION];
                for (var X = 0, U = Y; X < U.length; X++) {
                    var G = U[X];
                    var H = this._parseAnimation(G);
                    a.addAnimation(H);
                }
            }
            if (tt.DataParser.DEFAULT_ACTIONS in t) {
                var z = this._parseActionData(t[tt.DataParser.DEFAULT_ACTIONS], 0, null, null);
                for (var W = 0, K = z; W < K.length; W++) {
                    var Z = K[W];
                    a.addAction(Z, true);
                    if (Z.type === 0) {
                        var H = a.getAnimation(Z.name);
                        if (H !== null) {
                            a.defaultAnimation = H;
                        }
                    }
                }
            }
            if (tt.DataParser.ACTIONS in t) {
                var z = this._parseActionData(t[tt.DataParser.ACTIONS], 0, null, null);
                for (var q = 0, Q = z; q < Q.length; q++) {
                    var Z = Q[q];
                    a.addAction(Z, false);
                }
            }
            this._rawBones.length = 0;
            this._cacheRawMeshes.length = 0;
            this._cacheMeshes.length = 0;
            this._armature = null;
            for (var J in this._weightSlotPose) {
                delete this._weightSlotPose[J];
            }
            for (var J in this._weightBonePoses) {
                delete this._weightBonePoses[J];
            }
            for (var J in this._cacheBones) {
                delete this._cacheBones[J];
            }
            for (var J in this._slotChildActions) {
                delete this._slotChildActions[J];
            }
            return a;
        };
        $.prototype._parseBone = function (t) {
            var e = 0;
            if (tt.DataParser.TYPE in t && typeof t[tt.DataParser.TYPE] === 'string') {
                e = tt.DataParser._getBoneType(t[tt.DataParser.TYPE]);
            } else {
                e = $._getNumber(t, tt.DataParser.TYPE, 0);
            }
            if (e === 0) {
                var a = this._armature.scale;
                var r = tt.BaseObject.borrowObject(tt.BoneData);
                r.inheritTranslation = $._getBoolean(t, tt.DataParser.INHERIT_TRANSLATION, true);
                r.inheritRotation = $._getBoolean(t, tt.DataParser.INHERIT_ROTATION, true);
                r.inheritScale = $._getBoolean(t, tt.DataParser.INHERIT_SCALE, true);
                r.inheritReflection = $._getBoolean(t, tt.DataParser.INHERIT_REFLECTION, true);
                r.length = $._getNumber(t, tt.DataParser.LENGTH, 0) * a;
                r.alpha = $._getNumber(t, tt.DataParser.ALPHA, 1);
                r.name = $._getString(t, tt.DataParser.NAME, '');
                if (tt.DataParser.TRANSFORM in t) {
                    this._parseTransform(t[tt.DataParser.TRANSFORM], r.transform, a);
                }
                return r;
            }
            var i = tt.BaseObject.borrowObject(tt.SurfaceData);
            i.alpha = $._getNumber(t, tt.DataParser.ALPHA, 1);
            i.name = $._getString(t, tt.DataParser.NAME, '');
            i.segmentX = $._getNumber(t, tt.DataParser.SEGMENT_X, 0);
            i.segmentY = $._getNumber(t, tt.DataParser.SEGMENT_Y, 0);
            this._parseGeometry(t, i.geometry);
            return i;
        };
        $.prototype._parseIKConstraint = function (t) {
            var e = this._armature.getBone($._getString(t, tt.DataParser.BONE, ''));
            if (e === null) {
                return null;
            }
            var a = this._armature.getBone($._getString(t, tt.DataParser.TARGET, ''));
            if (a === null) {
                return null;
            }
            var r = $._getNumber(t, tt.DataParser.CHAIN, 0);
            var i = tt.BaseObject.borrowObject(tt.IKConstraintData);
            i.scaleEnabled = $._getBoolean(t, tt.DataParser.SCALE, false);
            i.bendPositive = $._getBoolean(t, tt.DataParser.BEND_POSITIVE, true);
            i.weight = $._getNumber(t, tt.DataParser.WEIGHT, 1);
            i.name = $._getString(t, tt.DataParser.NAME, '');
            i.type = 0;
            i.target = a;
            if (r > 0 && e.parent !== null) {
                i.root = e.parent;
                i.bone = e;
            } else {
                i.root = e;
                i.bone = null;
            }
            return i;
        };
        $.prototype._parsePathConstraint = function (t) {
            var e = this._armature.getSlot($._getString(t, tt.DataParser.TARGET, ''));
            if (e === null) {
                return null;
            }
            var a = this._armature.defaultSkin;
            if (a === null) {
                return null;
            }
            var r = a.getDisplay(e.name, $._getString(t, tt.DataParser.TARGET_DISPLAY, e.name));
            if (r === null || !(r instanceof tt.PathDisplayData)) {
                return null;
            }
            var i = t[tt.DataParser.BONES];
            if (i === null || i.length === 0) {
                return null;
            }
            var n = tt.BaseObject.borrowObject(tt.PathConstraintData);
            n.name = $._getString(t, tt.DataParser.NAME, '');
            n.type = 1;
            n.pathSlot = e;
            n.pathDisplayData = r;
            n.target = e.parent;
            n.positionMode = tt.DataParser._getPositionMode(
                $._getString(t, tt.DataParser.POSITION_MODE, '')
            );
            n.spacingMode = tt.DataParser._getSpacingMode(
                $._getString(t, tt.DataParser.SPACING_MODE, '')
            );
            n.rotateMode = tt.DataParser._getRotateMode(
                $._getString(t, tt.DataParser.ROTATE_MODE, '')
            );
            n.position = $._getNumber(t, tt.DataParser.POSITION, 0);
            n.spacing = $._getNumber(t, tt.DataParser.SPACING, 0);
            n.rotateOffset = $._getNumber(t, tt.DataParser.ROTATE_OFFSET, 0);
            n.rotateMix = $._getNumber(t, tt.DataParser.ROTATE_MIX, 1);
            n.translateMix = $._getNumber(t, tt.DataParser.TRANSLATE_MIX, 1);
            for (var s = 0, o = i; s < o.length; s++) {
                var l = o[s];
                var h = this._armature.getBone(l);
                if (h !== null) {
                    n.AddBone(h);
                    if (n.root === null) {
                        n.root = h;
                    }
                }
            }
            return n;
        };
        $.prototype._parseSlot = function (t, e) {
            var a = tt.BaseObject.borrowObject(tt.SlotData);
            a.displayIndex = $._getNumber(t, tt.DataParser.DISPLAY_INDEX, 0);
            a.zOrder = e;
            a.zIndex = $._getNumber(t, tt.DataParser.Z_INDEX, 0);
            a.alpha = $._getNumber(t, tt.DataParser.ALPHA, 1);
            a.name = $._getString(t, tt.DataParser.NAME, '');
            a.parent = this._armature.getBone($._getString(t, tt.DataParser.PARENT, ''));
            if (tt.DataParser.BLEND_MODE in t && typeof t[tt.DataParser.BLEND_MODE] === 'string') {
                a.blendMode = tt.DataParser._getBlendMode(t[tt.DataParser.BLEND_MODE]);
            } else {
                a.blendMode = $._getNumber(t, tt.DataParser.BLEND_MODE, 0);
            }
            if (tt.DataParser.COLOR in t) {
                a.color = tt.SlotData.createColor();
                this._parseColorTransform(t[tt.DataParser.COLOR], a.color);
            } else {
                a.color = tt.SlotData.DEFAULT_COLOR;
            }
            if (tt.DataParser.ACTIONS in t) {
                this._slotChildActions[a.name] = this._parseActionData(
                    t[tt.DataParser.ACTIONS],
                    0,
                    null,
                    null
                );
            }
            return a;
        };
        $.prototype._parseSkin = function (t) {
            var e = tt.BaseObject.borrowObject(tt.SkinData);
            e.name = $._getString(t, tt.DataParser.NAME, tt.DataParser.DEFAULT_NAME);
            if (e.name.length === 0) {
                e.name = tt.DataParser.DEFAULT_NAME;
            }
            if (tt.DataParser.SLOT in t) {
                var a = t[tt.DataParser.SLOT];
                this._skin = e;
                for (var r = 0, i = a; r < i.length; r++) {
                    var n = i[r];
                    var s = $._getString(n, tt.DataParser.NAME, '');
                    var o = this._armature.getSlot(s);
                    if (o !== null) {
                        this._slot = o;
                        if (tt.DataParser.DISPLAY in n) {
                            var l = n[tt.DataParser.DISPLAY];
                            for (var h = 0, u = l; h < u.length; h++) {
                                var f = u[h];
                                if (f) {
                                    e.addDisplay(s, this._parseDisplay(f));
                                } else {
                                    e.addDisplay(s, null);
                                }
                            }
                        }
                        this._slot = null;
                    }
                }
                this._skin = null;
            }
            return e;
        };
        $.prototype._parseDisplay = function (t) {
            var e = $._getString(t, tt.DataParser.NAME, '');
            var a = $._getString(t, tt.DataParser.PATH, '');
            var r = 0;
            var i = null;
            if (tt.DataParser.TYPE in t && typeof t[tt.DataParser.TYPE] === 'string') {
                r = tt.DataParser._getDisplayType(t[tt.DataParser.TYPE]);
            } else {
                r = $._getNumber(t, tt.DataParser.TYPE, r);
            }
            switch (r) {
                case 0: {
                    var n = (i = tt.BaseObject.borrowObject(tt.ImageDisplayData));
                    n.name = e;
                    n.path = a.length > 0 ? a : e;
                    this._parsePivot(t, n);
                    break;
                }
                case 1: {
                    var s = (i = tt.BaseObject.borrowObject(tt.ArmatureDisplayData));
                    s.name = e;
                    s.path = a.length > 0 ? a : e;
                    s.inheritAnimation = true;
                    if (tt.DataParser.ACTIONS in t) {
                        var o = this._parseActionData(t[tt.DataParser.ACTIONS], 0, null, null);
                        for (var l = 0, h = o; l < h.length; l++) {
                            var u = h[l];
                            s.addAction(u);
                        }
                    } else if (this._slot.name in this._slotChildActions) {
                        var f = this._skin.getDisplays(this._slot.name);
                        if (
                            f === null
                                ? this._slot.displayIndex === 0
                                : this._slot.displayIndex === f.length
                        ) {
                            for (
                                var _ = 0, m = this._slotChildActions[this._slot.name];
                                _ < m.length;
                                _++
                            ) {
                                var u = m[_];
                                s.addAction(u);
                            }
                            delete this._slotChildActions[this._slot.name];
                        }
                    }
                    break;
                }
                case 2: {
                    var p = (i = tt.BaseObject.borrowObject(tt.MeshDisplayData));
                    p.geometry.inheritDeform = $._getBoolean(t, tt.DataParser.INHERIT_DEFORM, true);
                    p.name = e;
                    p.path = a.length > 0 ? a : e;
                    if (tt.DataParser.SHARE in t) {
                        p.geometry.data = this._data;
                        this._cacheRawMeshes.push(t);
                        this._cacheMeshes.push(p);
                    } else {
                        this._parseMesh(t, p);
                    }
                    break;
                }
                case 3: {
                    var c = this._parseBoundingBox(t);
                    if (c !== null) {
                        var d = (i = tt.BaseObject.borrowObject(tt.BoundingBoxDisplayData));
                        d.name = e;
                        d.path = a.length > 0 ? a : e;
                        d.boundingBox = c;
                    }
                    break;
                }
                case 4: {
                    var y = t[tt.DataParser.LENGTHS];
                    var v = (i = tt.BaseObject.borrowObject(tt.PathDisplayData));
                    v.closed = $._getBoolean(t, tt.DataParser.CLOSED, false);
                    v.constantSpeed = $._getBoolean(t, tt.DataParser.CONSTANT_SPEED, false);
                    v.name = e;
                    v.path = a.length > 0 ? a : e;
                    v.curveLengths.length = y.length;
                    for (var g = 0, D = y.length; g < D; ++g) {
                        v.curveLengths[g] = y[g];
                    }
                    this._parsePath(t, v);
                    break;
                }
            }
            if (i !== null && tt.DataParser.TRANSFORM in t) {
                this._parseTransform(t[tt.DataParser.TRANSFORM], i.transform, this._armature.scale);
            }
            return i;
        };
        $.prototype._parsePath = function (t, e) {
            this._parseGeometry(t, e.geometry);
        };
        $.prototype._parsePivot = function (t, e) {
            if (tt.DataParser.PIVOT in t) {
                var a = t[tt.DataParser.PIVOT];
                e.pivot.x = $._getNumber(a, tt.DataParser.X, 0);
                e.pivot.y = $._getNumber(a, tt.DataParser.Y, 0);
            } else {
                e.pivot.x = 0.5;
                e.pivot.y = 0.5;
            }
        };
        $.prototype._parseMesh = function (t, e) {
            this._parseGeometry(t, e.geometry);
            if (tt.DataParser.WEIGHTS in t) {
                var a = t[tt.DataParser.SLOT_POSE];
                var r = t[tt.DataParser.BONE_POSE];
                var i = this._skin.name + '_' + this._slot.name + '_' + e.name;
                this._weightSlotPose[i] = a;
                this._weightBonePoses[i] = r;
            }
        };
        $.prototype._parseBoundingBox = function (t) {
            var e = null;
            var a = 0;
            if (tt.DataParser.SUB_TYPE in t && typeof t[tt.DataParser.SUB_TYPE] === 'string') {
                a = tt.DataParser._getBoundingBoxType(t[tt.DataParser.SUB_TYPE]);
            } else {
                a = $._getNumber(t, tt.DataParser.SUB_TYPE, a);
            }
            switch (a) {
                case 0:
                    e = tt.BaseObject.borrowObject(tt.RectangleBoundingBoxData);
                    break;
                case 1:
                    e = tt.BaseObject.borrowObject(tt.EllipseBoundingBoxData);
                    break;
                case 2:
                    e = this._parsePolygonBoundingBox(t);
                    break;
            }
            if (e !== null) {
                e.color = $._getNumber(t, tt.DataParser.COLOR, 0);
                if (e.type === 0 || e.type === 1) {
                    e.width = $._getNumber(t, tt.DataParser.WIDTH, 0);
                    e.height = $._getNumber(t, tt.DataParser.HEIGHT, 0);
                }
            }
            return e;
        };
        $.prototype._parsePolygonBoundingBox = function (t) {
            var e = tt.BaseObject.borrowObject(tt.PolygonBoundingBoxData);
            if (tt.DataParser.VERTICES in t) {
                var a = this._armature.scale;
                var r = t[tt.DataParser.VERTICES];
                var i = e.vertices;
                i.length = r.length;
                for (var n = 0, s = r.length; n < s; n += 2) {
                    var o = r[n] * a;
                    var l = r[n + 1] * a;
                    i[n] = o;
                    i[n + 1] = l;
                    if (n === 0) {
                        e.x = o;
                        e.y = l;
                        e.width = o;
                        e.height = l;
                    } else {
                        if (o < e.x) {
                            e.x = o;
                        } else if (o > e.width) {
                            e.width = o;
                        }
                        if (l < e.y) {
                            e.y = l;
                        } else if (l > e.height) {
                            e.height = l;
                        }
                    }
                }
                e.width -= e.x;
                e.height -= e.y;
            } else {
                console.warn('Data error.\n Please reexport DragonBones Data to fixed the bug.');
            }
            return e;
        };
        $.prototype._parseAnimation = function (t) {
            var e = tt.BaseObject.borrowObject(tt.AnimationData);
            e.blendType = tt.DataParser._getAnimationBlendType(
                $._getString(t, tt.DataParser.BLEND_TYPE, '')
            );
            e.frameCount = $._getNumber(t, tt.DataParser.DURATION, 0);
            e.playTimes = $._getNumber(t, tt.DataParser.PLAY_TIMES, 1);
            e.duration = e.frameCount / this._armature.frameRate;
            e.fadeInTime = $._getNumber(t, tt.DataParser.FADE_IN_TIME, 0);
            e.scale = $._getNumber(t, tt.DataParser.SCALE, 1);
            e.name = $._getString(t, tt.DataParser.NAME, tt.DataParser.DEFAULT_NAME);
            if (e.name.length === 0) {
                e.name = tt.DataParser.DEFAULT_NAME;
            }
            e.frameIntOffset = this._frameIntArray.length;
            e.frameFloatOffset = this._frameFloatArray.length;
            e.frameOffset = this._frameArray.length;
            this._animation = e;
            if (tt.DataParser.FRAME in t) {
                var a = t[tt.DataParser.FRAME];
                var r = a.length;
                if (r > 0) {
                    for (var i = 0, n = 0; i < r; ++i) {
                        var s = a[i];
                        this._parseActionDataInFrame(s, n, null, null);
                        n += $._getNumber(s, tt.DataParser.DURATION, 1);
                    }
                }
            }
            if (tt.DataParser.Z_ORDER in t) {
                this._animation.zOrderTimeline = this._parseTimeline(
                    t[tt.DataParser.Z_ORDER],
                    null,
                    tt.DataParser.FRAME,
                    1,
                    0,
                    0,
                    this._parseZOrderFrame
                );
            }
            if (tt.DataParser.BONE in t) {
                var o = t[tt.DataParser.BONE];
                for (var l = 0, h = o; l < h.length; l++) {
                    var u = h[l];
                    this._parseBoneTimeline(u);
                }
            }
            if (tt.DataParser.SLOT in t) {
                var o = t[tt.DataParser.SLOT];
                for (var f = 0, _ = o; f < _.length; f++) {
                    var u = _[f];
                    this._parseSlotTimeline(u);
                }
            }
            if (tt.DataParser.FFD in t) {
                var o = t[tt.DataParser.FFD];
                for (var m = 0, p = o; m < p.length; m++) {
                    var u = p[m];
                    var c = $._getString(u, tt.DataParser.SKIN, tt.DataParser.DEFAULT_NAME);
                    var d = $._getString(u, tt.DataParser.SLOT, '');
                    var y = $._getString(u, tt.DataParser.NAME, '');
                    if (c.length === 0) {
                        c = tt.DataParser.DEFAULT_NAME;
                    }
                    this._slot = this._armature.getSlot(d);
                    this._mesh = this._armature.getMesh(c, d, y);
                    if (this._slot === null || this._mesh === null) {
                        continue;
                    }
                    var v = this._parseTimeline(
                        u,
                        null,
                        tt.DataParser.FRAME,
                        22,
                        2,
                        0,
                        this._parseSlotDeformFrame
                    );
                    if (v !== null) {
                        this._animation.addSlotTimeline(d, v);
                    }
                    this._slot = null;
                    this._mesh = null;
                }
            }
            if (tt.DataParser.IK in t) {
                var o = t[tt.DataParser.IK];
                for (var g = 0, D = o; g < D.length; g++) {
                    var u = D[g];
                    var T = $._getString(u, tt.DataParser.NAME, '');
                    var b = this._armature.getConstraint(T);
                    if (b === null) {
                        continue;
                    }
                    var v = this._parseTimeline(
                        u,
                        null,
                        tt.DataParser.FRAME,
                        30,
                        1,
                        2,
                        this._parseIKConstraintFrame
                    );
                    if (v !== null) {
                        this._animation.addConstraintTimeline(T, v);
                    }
                }
            }
            if (this._actionFrames.length > 0) {
                this._animation.actionTimeline = this._parseTimeline(
                    null,
                    this._actionFrames,
                    '',
                    0,
                    0,
                    0,
                    this._parseActionFrame
                );
                this._actionFrames.length = 0;
            }
            if (tt.DataParser.TIMELINE in t) {
                var o = t[tt.DataParser.TIMELINE];
                for (var A = 0, P = o; A < P.length; A++) {
                    var u = P[A];
                    var S = $._getNumber(u, tt.DataParser.TYPE, 0);
                    var O = $._getString(u, tt.DataParser.NAME, '');
                    var v = null;
                    switch (S) {
                        case 0:
                            break;
                        case 20:
                        case 23:
                        case 60:
                        case 24:
                        case 40:
                        case 41:
                            if (S === 20) {
                                this._frameValueType = 0;
                                this._frameValueScale = 1;
                            } else {
                                this._frameValueType = 1;
                                if (S === 23) {
                                    this._frameValueScale = 1;
                                } else if (S === 40 || S === 41) {
                                    this._frameValueScale = 1e4;
                                } else {
                                    this._frameValueScale = 100;
                                }
                            }
                            if (S === 60 || S === 24 || S === 41) {
                                this._frameDefaultValue = 1;
                            } else {
                                this._frameDefaultValue = 0;
                            }
                            if (S === 40 && e.blendType !== 0) {
                                v = tt.BaseObject.borrowObject(tt.AnimationTimelineData);
                                var x = v;
                                x.x = $._getNumber(u, tt.DataParser.X, 0);
                                x.y = $._getNumber(u, tt.DataParser.Y, 0);
                            }
                            v = this._parseTimeline(
                                u,
                                null,
                                tt.DataParser.FRAME,
                                S,
                                this._frameValueType,
                                1,
                                this._parseSingleValueFrame,
                                v
                            );
                            break;
                        case 11:
                        case 12:
                        case 13:
                        case 30:
                        case 42:
                            if (S === 30 || S === 42) {
                                this._frameValueType = 1;
                                if (S === 42) {
                                    this._frameValueScale = 1e4;
                                } else {
                                    this._frameValueScale = 100;
                                }
                            } else {
                                if (S === 12) {
                                    this._frameValueScale = tt.Transform.DEG_RAD;
                                } else {
                                    this._frameValueScale = 1;
                                }
                                this._frameValueType = 2;
                            }
                            if (S === 13 || S === 30) {
                                this._frameDefaultValue = 1;
                            } else {
                                this._frameDefaultValue = 0;
                            }
                            v = this._parseTimeline(
                                u,
                                null,
                                tt.DataParser.FRAME,
                                S,
                                this._frameValueType,
                                2,
                                this._parseDoubleValueFrame
                            );
                            break;
                        case 1:
                            break;
                        case 50: {
                            var B = this._armature.getBone(O);
                            if (B === null) {
                                continue;
                            }
                            this._geometry = B.geometry;
                            v = this._parseTimeline(
                                u,
                                null,
                                tt.DataParser.FRAME,
                                S,
                                2,
                                0,
                                this._parseDeformFrame
                            );
                            this._geometry = null;
                            break;
                        }
                        case 22: {
                            this._geometry = null;
                            for (var c in this._armature.skins) {
                                var E = this._armature.skins[c];
                                for (var I in E.displays) {
                                    var M = E.displays[I];
                                    for (var F = 0, w = M; F < w.length; F++) {
                                        var C = w[F];
                                        if (C !== null && C.name === O) {
                                            this._geometry = C.geometry;
                                            break;
                                        }
                                    }
                                }
                            }
                            if (this._geometry === null) {
                                continue;
                            }
                            v = this._parseTimeline(
                                u,
                                null,
                                tt.DataParser.FRAME,
                                S,
                                2,
                                0,
                                this._parseDeformFrame
                            );
                            this._geometry = null;
                            break;
                        }
                        case 21:
                            v = this._parseTimeline(
                                u,
                                null,
                                tt.DataParser.FRAME,
                                S,
                                1,
                                1,
                                this._parseSlotColorFrame
                            );
                            break;
                    }
                    if (v !== null) {
                        switch (S) {
                            case 0:
                                break;
                            case 1:
                                break;
                            case 11:
                            case 12:
                            case 13:
                            case 50:
                            case 60:
                                this._animation.addBoneTimeline(O, v);
                                break;
                            case 20:
                            case 21:
                            case 22:
                            case 23:
                            case 24:
                                this._animation.addSlotTimeline(O, v);
                                break;
                            case 30:
                                this._animation.addConstraintTimeline(O, v);
                                break;
                            case 40:
                            case 41:
                            case 42:
                                this._animation.addAnimationTimeline(O, v);
                                break;
                        }
                    }
                }
            }
            this._animation = null;
            return e;
        };
        $.prototype._parseTimeline = function (t, e, a, r, i, n, s, o) {
            if (o === void 0) {
                o = null;
            }
            if (t !== null && a.length > 0 && a in t) {
                e = t[a];
            }
            if (e === null) {
                return null;
            }
            var l = e.length;
            if (l === 0) {
                return null;
            }
            var h = this._frameIntArray.length;
            var u = this._frameFloatArray.length;
            var f = this._timelineArray.length;
            if (o === null) {
                o = tt.BaseObject.borrowObject(tt.TimelineData);
            }
            o.type = r;
            o.offset = f;
            this._frameValueType = i;
            this._timeline = o;
            this._timelineArray.length += 1 + 1 + 1 + 1 + 1 + l;
            if (t !== null) {
                this._timelineArray[f + 0] = Math.round(
                    $._getNumber(t, tt.DataParser.SCALE, 1) * 100
                );
                this._timelineArray[f + 1] = Math.round(
                    $._getNumber(t, tt.DataParser.OFFSET, 0) * 100
                );
            } else {
                this._timelineArray[f + 0] = 100;
                this._timelineArray[f + 1] = 0;
            }
            this._timelineArray[f + 2] = l;
            this._timelineArray[f + 3] = n;
            switch (this._frameValueType) {
                case 0:
                    this._timelineArray[f + 4] = 0;
                    break;
                case 1:
                    this._timelineArray[f + 4] = h - this._animation.frameIntOffset;
                    break;
                case 2:
                    this._timelineArray[f + 4] = u - this._animation.frameFloatOffset;
                    break;
            }
            if (l === 1) {
                o.frameIndicesOffset = -1;
                this._timelineArray[f + 5 + 0] =
                    s.call(this, e[0], 0, 0) - this._animation.frameOffset;
            } else {
                var _ = this._animation.frameCount + 1;
                var m = this._data.frameIndices;
                var p = m.length;
                m.length += _;
                o.frameIndicesOffset = p;
                for (var c = 0, d = 0, y = 0, v = 0; c < _; ++c) {
                    if (y + v <= c && d < l) {
                        var g = e[d];
                        y = c;
                        if (d === l - 1) {
                            v = this._animation.frameCount - y;
                        } else {
                            if (g instanceof D) {
                                v = this._actionFrames[d + 1].frameStart - y;
                            } else {
                                v = $._getNumber(g, tt.DataParser.DURATION, 1);
                            }
                        }
                        this._timelineArray[f + 5 + d] =
                            s.call(this, g, y, v) - this._animation.frameOffset;
                        d++;
                    }
                    m[p + c] = d - 1;
                }
            }
            this._timeline = null;
            return o;
        };
        $.prototype._parseBoneTimeline = function (t) {
            var e = this._armature.getBone($._getString(t, tt.DataParser.NAME, ''));
            if (e === null) {
                return;
            }
            this._bone = e;
            this._slot = this._armature.getSlot(this._bone.name);
            if (tt.DataParser.TRANSLATE_FRAME in t) {
                this._frameDefaultValue = 0;
                this._frameValueScale = 1;
                var a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.TRANSLATE_FRAME,
                    11,
                    2,
                    2,
                    this._parseDoubleValueFrame
                );
                if (a !== null) {
                    this._animation.addBoneTimeline(e.name, a);
                }
            }
            if (tt.DataParser.ROTATE_FRAME in t) {
                this._frameDefaultValue = 0;
                this._frameValueScale = 1;
                var a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.ROTATE_FRAME,
                    12,
                    2,
                    2,
                    this._parseBoneRotateFrame
                );
                if (a !== null) {
                    this._animation.addBoneTimeline(e.name, a);
                }
            }
            if (tt.DataParser.SCALE_FRAME in t) {
                this._frameDefaultValue = 1;
                this._frameValueScale = 1;
                var a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.SCALE_FRAME,
                    13,
                    2,
                    2,
                    this._parseBoneScaleFrame
                );
                if (a !== null) {
                    this._animation.addBoneTimeline(e.name, a);
                }
            }
            if (tt.DataParser.FRAME in t) {
                var a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.FRAME,
                    10,
                    2,
                    6,
                    this._parseBoneAllFrame
                );
                if (a !== null) {
                    this._animation.addBoneTimeline(e.name, a);
                }
            }
            this._bone = null;
            this._slot = null;
        };
        $.prototype._parseSlotTimeline = function (t) {
            var e = this._armature.getSlot($._getString(t, tt.DataParser.NAME, ''));
            if (e === null) {
                return;
            }
            var a = null;
            var r = null;
            this._slot = e;
            if (tt.DataParser.DISPLAY_FRAME in t) {
                a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.DISPLAY_FRAME,
                    20,
                    0,
                    0,
                    this._parseSlotDisplayFrame
                );
            } else {
                a = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.FRAME,
                    20,
                    0,
                    0,
                    this._parseSlotDisplayFrame
                );
            }
            if (tt.DataParser.COLOR_FRAME in t) {
                r = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.COLOR_FRAME,
                    21,
                    1,
                    1,
                    this._parseSlotColorFrame
                );
            } else {
                r = this._parseTimeline(
                    t,
                    null,
                    tt.DataParser.FRAME,
                    21,
                    1,
                    1,
                    this._parseSlotColorFrame
                );
            }
            if (a !== null) {
                this._animation.addSlotTimeline(e.name, a);
            }
            if (r !== null) {
                this._animation.addSlotTimeline(e.name, r);
            }
            this._slot = null;
        };
        $.prototype._parseFrame = function (t, e, a) {
            t;
            a;
            var r = this._frameArray.length;
            this._frameArray.length += 1;
            this._frameArray[r + 0] = e;
            return r;
        };
        $.prototype._parseTweenFrame = function (t, e, a) {
            var r = this._parseFrame(t, e, a);
            if (a > 0) {
                if (tt.DataParser.CURVE in t) {
                    var i = a + 1;
                    this._helpArray.length = i;
                    var n = this._samplingEasingCurve(t[tt.DataParser.CURVE], this._helpArray);
                    this._frameArray.length += 1 + 1 + this._helpArray.length;
                    this._frameArray[r + 1] = 2;
                    this._frameArray[r + 2] = n ? i : -i;
                    for (var s = 0; s < i; ++s) {
                        this._frameArray[r + 3 + s] = Math.round(this._helpArray[s] * 1e4);
                    }
                } else {
                    var o = -2;
                    var l = o;
                    if (tt.DataParser.TWEEN_EASING in t) {
                        l = $._getNumber(t, tt.DataParser.TWEEN_EASING, o);
                    }
                    if (l === o) {
                        this._frameArray.length += 1;
                        this._frameArray[r + 1] = 0;
                    } else if (l === 0) {
                        this._frameArray.length += 1;
                        this._frameArray[r + 1] = 1;
                    } else if (l < 0) {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[r + 1] = 3;
                        this._frameArray[r + 2] = Math.round(-l * 100);
                    } else if (l <= 1) {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[r + 1] = 4;
                        this._frameArray[r + 2] = Math.round(l * 100);
                    } else {
                        this._frameArray.length += 1 + 1;
                        this._frameArray[r + 1] = 5;
                        this._frameArray[r + 2] = Math.round(l * 100 - 100);
                    }
                }
            } else {
                this._frameArray.length += 1;
                this._frameArray[r + 1] = 0;
            }
            return r;
        };
        $.prototype._parseSingleValueFrame = function (t, e, a) {
            var r = 0;
            switch (this._frameValueType) {
                case 0: {
                    r = this._parseFrame(t, e, a);
                    this._frameArray.length += 1;
                    this._frameArray[r + 1] = $._getNumber(
                        t,
                        tt.DataParser.VALUE,
                        this._frameDefaultValue
                    );
                    break;
                }
                case 1: {
                    r = this._parseTweenFrame(t, e, a);
                    var i = this._frameIntArray.length;
                    this._frameIntArray.length += 1;
                    this._frameIntArray[i] = Math.round(
                        $._getNumber(t, tt.DataParser.VALUE, this._frameDefaultValue) *
                            this._frameValueScale
                    );
                    break;
                }
                case 2: {
                    r = this._parseTweenFrame(t, e, a);
                    var i = this._frameFloatArray.length;
                    this._frameFloatArray.length += 1;
                    this._frameFloatArray[i] =
                        $._getNumber(t, tt.DataParser.VALUE, this._frameDefaultValue) *
                        this._frameValueScale;
                    break;
                }
            }
            return r;
        };
        $.prototype._parseDoubleValueFrame = function (t, e, a) {
            var r = 0;
            switch (this._frameValueType) {
                case 0: {
                    r = this._parseFrame(t, e, a);
                    this._frameArray.length += 2;
                    this._frameArray[r + 1] = $._getNumber(
                        t,
                        tt.DataParser.X,
                        this._frameDefaultValue
                    );
                    this._frameArray[r + 2] = $._getNumber(
                        t,
                        tt.DataParser.Y,
                        this._frameDefaultValue
                    );
                    break;
                }
                case 1: {
                    r = this._parseTweenFrame(t, e, a);
                    var i = this._frameIntArray.length;
                    this._frameIntArray.length += 2;
                    this._frameIntArray[i] = Math.round(
                        $._getNumber(t, tt.DataParser.X, this._frameDefaultValue) *
                            this._frameValueScale
                    );
                    this._frameIntArray[i + 1] = Math.round(
                        $._getNumber(t, tt.DataParser.Y, this._frameDefaultValue) *
                            this._frameValueScale
                    );
                    break;
                }
                case 2: {
                    r = this._parseTweenFrame(t, e, a);
                    var i = this._frameFloatArray.length;
                    this._frameFloatArray.length += 2;
                    this._frameFloatArray[i] =
                        $._getNumber(t, tt.DataParser.X, this._frameDefaultValue) *
                        this._frameValueScale;
                    this._frameFloatArray[i + 1] =
                        $._getNumber(t, tt.DataParser.Y, this._frameDefaultValue) *
                        this._frameValueScale;
                    break;
                }
            }
            return r;
        };
        $.prototype._parseActionFrame = function (t, e, a) {
            a;
            var r = this._frameArray.length;
            var i = t.actions.length;
            this._frameArray.length += 1 + 1 + i;
            this._frameArray[r + 0] = e;
            this._frameArray[r + 0 + 1] = i;
            for (var n = 0; n < i; ++n) {
                this._frameArray[r + 0 + 2 + n] = t.actions[n];
            }
            return r;
        };
        $.prototype._parseZOrderFrame = function (t, e, a) {
            var r = this._parseFrame(t, e, a);
            if (tt.DataParser.Z_ORDER in t) {
                var i = t[tt.DataParser.Z_ORDER];
                if (i.length > 0) {
                    var n = this._armature.sortedSlots.length;
                    var s = new Array(n - i.length / 2);
                    var o = new Array(n);
                    for (var l = 0; l < s.length; ++l) {
                        s[l] = 0;
                    }
                    for (var h = 0; h < n; ++h) {
                        o[h] = -1;
                    }
                    var u = 0;
                    var f = 0;
                    for (var _ = 0, m = i.length; _ < m; _ += 2) {
                        var p = i[_];
                        var c = i[_ + 1];
                        while (u !== p) {
                            s[f++] = u++;
                        }
                        var d = u + c;
                        o[d] = u++;
                    }
                    while (u < n) {
                        s[f++] = u++;
                    }
                    this._frameArray.length += 1 + n;
                    this._frameArray[r + 1] = n;
                    var y = n;
                    while (y--) {
                        if (o[y] === -1) {
                            this._frameArray[r + 2 + y] = s[--f] || 0;
                        } else {
                            this._frameArray[r + 2 + y] = o[y] || 0;
                        }
                    }
                    return r;
                }
            }
            this._frameArray.length += 1;
            this._frameArray[r + 1] = 0;
            return r;
        };
        $.prototype._parseBoneAllFrame = function (t, e, a) {
            this._helpTransform.identity();
            if (tt.DataParser.TRANSFORM in t) {
                this._parseTransform(t[tt.DataParser.TRANSFORM], this._helpTransform, 1);
            }
            var r = this._helpTransform.rotation;
            if (e !== 0) {
                if (this._prevClockwise === 0) {
                    r = this._prevRotation + tt.Transform.normalizeRadian(r - this._prevRotation);
                } else {
                    if (
                        this._prevClockwise > 0 ? r >= this._prevRotation : r <= this._prevRotation
                    ) {
                        this._prevClockwise =
                            this._prevClockwise > 0
                                ? this._prevClockwise - 1
                                : this._prevClockwise + 1;
                    }
                    r =
                        this._prevRotation +
                        r -
                        this._prevRotation +
                        tt.Transform.PI_D * this._prevClockwise;
                }
            }
            this._prevClockwise = $._getNumber(t, tt.DataParser.TWEEN_ROTATE, 0);
            this._prevRotation = r;
            var i = this._parseTweenFrame(t, e, a);
            var n = this._frameFloatArray.length;
            this._frameFloatArray.length += 6;
            this._frameFloatArray[n++] = this._helpTransform.x;
            this._frameFloatArray[n++] = this._helpTransform.y;
            this._frameFloatArray[n++] = r;
            this._frameFloatArray[n++] = this._helpTransform.skew;
            this._frameFloatArray[n++] = this._helpTransform.scaleX;
            this._frameFloatArray[n++] = this._helpTransform.scaleY;
            this._parseActionDataInFrame(t, e, this._bone, this._slot);
            return i;
        };
        $.prototype._parseBoneTranslateFrame = function (t, e, a) {
            var r = this._parseTweenFrame(t, e, a);
            var i = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[i++] = $._getNumber(t, tt.DataParser.X, 0);
            this._frameFloatArray[i++] = $._getNumber(t, tt.DataParser.Y, 0);
            return r;
        };
        $.prototype._parseBoneRotateFrame = function (t, e, a) {
            var r = $._getNumber(t, tt.DataParser.ROTATE, 0) * tt.Transform.DEG_RAD;
            if (e !== 0) {
                if (this._prevClockwise === 0) {
                    r = this._prevRotation + tt.Transform.normalizeRadian(r - this._prevRotation);
                } else {
                    if (
                        this._prevClockwise > 0 ? r >= this._prevRotation : r <= this._prevRotation
                    ) {
                        this._prevClockwise =
                            this._prevClockwise > 0
                                ? this._prevClockwise - 1
                                : this._prevClockwise + 1;
                    }
                    r =
                        this._prevRotation +
                        r -
                        this._prevRotation +
                        tt.Transform.PI_D * this._prevClockwise;
                }
            }
            this._prevClockwise = $._getNumber(t, tt.DataParser.CLOCK_WISE, 0);
            this._prevRotation = r;
            var i = this._parseTweenFrame(t, e, a);
            var n = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[n++] = r;
            this._frameFloatArray[n++] =
                $._getNumber(t, tt.DataParser.SKEW, 0) * tt.Transform.DEG_RAD;
            return i;
        };
        $.prototype._parseBoneScaleFrame = function (t, e, a) {
            var r = this._parseTweenFrame(t, e, a);
            var i = this._frameFloatArray.length;
            this._frameFloatArray.length += 2;
            this._frameFloatArray[i++] = $._getNumber(t, tt.DataParser.X, 1);
            this._frameFloatArray[i++] = $._getNumber(t, tt.DataParser.Y, 1);
            return r;
        };
        $.prototype._parseSlotDisplayFrame = function (t, e, a) {
            var r = this._parseFrame(t, e, a);
            this._frameArray.length += 1;
            if (tt.DataParser.VALUE in t) {
                this._frameArray[r + 1] = $._getNumber(t, tt.DataParser.VALUE, 0);
            } else {
                this._frameArray[r + 1] = $._getNumber(t, tt.DataParser.DISPLAY_INDEX, 0);
            }
            this._parseActionDataInFrame(t, e, this._slot.parent, this._slot);
            return r;
        };
        $.prototype._parseSlotColorFrame = function (t, e, a) {
            var r = this._parseTweenFrame(t, e, a);
            var i = -1;
            if (tt.DataParser.VALUE in t || tt.DataParser.COLOR in t) {
                var n = tt.DataParser.VALUE in t ? t[tt.DataParser.VALUE] : t[tt.DataParser.COLOR];
                for (var s in n) {
                    s;
                    this._parseColorTransform(n, this._helpColorTransform);
                    i = this._colorArray.length;
                    this._colorArray.length += 8;
                    this._colorArray[i++] = Math.round(
                        this._helpColorTransform.alphaMultiplier * 100
                    );
                    this._colorArray[i++] = Math.round(
                        this._helpColorTransform.redMultiplier * 100
                    );
                    this._colorArray[i++] = Math.round(
                        this._helpColorTransform.greenMultiplier * 100
                    );
                    this._colorArray[i++] = Math.round(
                        this._helpColorTransform.blueMultiplier * 100
                    );
                    this._colorArray[i++] = Math.round(this._helpColorTransform.alphaOffset);
                    this._colorArray[i++] = Math.round(this._helpColorTransform.redOffset);
                    this._colorArray[i++] = Math.round(this._helpColorTransform.greenOffset);
                    this._colorArray[i++] = Math.round(this._helpColorTransform.blueOffset);
                    i -= 8;
                    break;
                }
            }
            if (i < 0) {
                if (this._defaultColorOffset < 0) {
                    this._defaultColorOffset = i = this._colorArray.length;
                    this._colorArray.length += 8;
                    this._colorArray[i++] = 100;
                    this._colorArray[i++] = 100;
                    this._colorArray[i++] = 100;
                    this._colorArray[i++] = 100;
                    this._colorArray[i++] = 0;
                    this._colorArray[i++] = 0;
                    this._colorArray[i++] = 0;
                    this._colorArray[i++] = 0;
                }
                i = this._defaultColorOffset;
            }
            var o = this._frameIntArray.length;
            this._frameIntArray.length += 1;
            this._frameIntArray[o] = i;
            return r;
        };
        $.prototype._parseSlotDeformFrame = function (t, e, a) {
            var r = this._frameFloatArray.length;
            var i = this._parseTweenFrame(t, e, a);
            var n = tt.DataParser.VERTICES in t ? t[tt.DataParser.VERTICES] : null;
            var s = $._getNumber(t, tt.DataParser.OFFSET, 0);
            var o = this._intArray[this._mesh.geometry.offset + 0];
            var l = this._mesh.parent.name + '_' + this._slot.name + '_' + this._mesh.name;
            var h = this._mesh.geometry.weight;
            var u = 0;
            var f = 0;
            var _ = 0;
            var m = 0;
            if (h !== null) {
                var p = this._weightSlotPose[l];
                this._helpMatrixA.copyFromArray(p, 0);
                this._frameFloatArray.length += h.count * 2;
                _ = h.offset + 2 + h.bones.length;
            } else {
                this._frameFloatArray.length += o * 2;
            }
            for (var c = 0; c < o * 2; c += 2) {
                if (n === null) {
                    u = 0;
                    f = 0;
                } else {
                    if (c < s || c - s >= n.length) {
                        u = 0;
                    } else {
                        u = n[c - s];
                    }
                    if (c + 1 < s || c + 1 - s >= n.length) {
                        f = 0;
                    } else {
                        f = n[c + 1 - s];
                    }
                }
                if (h !== null) {
                    var d = this._weightBonePoses[l];
                    var y = this._intArray[_++];
                    this._helpMatrixA.transformPoint(u, f, this._helpPoint, true);
                    u = this._helpPoint.x;
                    f = this._helpPoint.y;
                    for (var v = 0; v < y; ++v) {
                        var g = this._intArray[_++];
                        this._helpMatrixB.copyFromArray(d, g * 7 + 1);
                        this._helpMatrixB.invert();
                        this._helpMatrixB.transformPoint(u, f, this._helpPoint, true);
                        this._frameFloatArray[r + m++] = this._helpPoint.x;
                        this._frameFloatArray[r + m++] = this._helpPoint.y;
                    }
                } else {
                    this._frameFloatArray[r + c] = u;
                    this._frameFloatArray[r + c + 1] = f;
                }
            }
            if (e === 0) {
                var D = this._frameIntArray.length;
                this._frameIntArray.length += 1 + 1 + 1 + 1 + 1;
                this._frameIntArray[D + 0] = this._mesh.geometry.offset;
                this._frameIntArray[D + 1] = this._frameFloatArray.length - r;
                this._frameIntArray[D + 2] = this._frameFloatArray.length - r;
                this._frameIntArray[D + 3] = 0;
                this._frameIntArray[D + 4] = r - this._animation.frameFloatOffset;
                this._timelineArray[this._timeline.offset + 3] = D - this._animation.frameIntOffset;
            }
            return i;
        };
        $.prototype._parseIKConstraintFrame = function (t, e, a) {
            var r = this._parseTweenFrame(t, e, a);
            var i = this._frameIntArray.length;
            this._frameIntArray.length += 2;
            this._frameIntArray[i++] = $._getBoolean(t, tt.DataParser.BEND_POSITIVE, true) ? 1 : 0;
            this._frameIntArray[i++] = Math.round($._getNumber(t, tt.DataParser.WEIGHT, 1) * 100);
            return r;
        };
        $.prototype._parseActionData = function (t, e, a, r) {
            var i = new Array();
            if (typeof t === 'string') {
                var n = tt.BaseObject.borrowObject(tt.ActionData);
                n.type = e;
                n.name = t;
                n.bone = a;
                n.slot = r;
                i.push(n);
            } else if (t instanceof Array) {
                for (var s = 0, o = t; s < o.length; s++) {
                    var l = o[s];
                    var n = tt.BaseObject.borrowObject(tt.ActionData);
                    if (tt.DataParser.GOTO_AND_PLAY in l) {
                        n.type = 0;
                        n.name = $._getString(l, tt.DataParser.GOTO_AND_PLAY, '');
                    } else {
                        if (tt.DataParser.TYPE in l && typeof l[tt.DataParser.TYPE] === 'string') {
                            n.type = tt.DataParser._getActionType(l[tt.DataParser.TYPE]);
                        } else {
                            n.type = $._getNumber(l, tt.DataParser.TYPE, e);
                        }
                        n.name = $._getString(l, tt.DataParser.NAME, '');
                    }
                    if (tt.DataParser.BONE in l) {
                        var h = $._getString(l, tt.DataParser.BONE, '');
                        n.bone = this._armature.getBone(h);
                    } else {
                        n.bone = a;
                    }
                    if (tt.DataParser.SLOT in l) {
                        var u = $._getString(l, tt.DataParser.SLOT, '');
                        n.slot = this._armature.getSlot(u);
                    } else {
                        n.slot = r;
                    }
                    var f = null;
                    if (tt.DataParser.INTS in l) {
                        if (f === null) {
                            f = tt.BaseObject.borrowObject(tt.UserData);
                        }
                        var _ = l[tt.DataParser.INTS];
                        for (var m = 0, p = _; m < p.length; m++) {
                            var c = p[m];
                            f.addInt(c);
                        }
                    }
                    if (tt.DataParser.FLOATS in l) {
                        if (f === null) {
                            f = tt.BaseObject.borrowObject(tt.UserData);
                        }
                        var d = l[tt.DataParser.FLOATS];
                        for (var y = 0, v = d; y < v.length; y++) {
                            var c = v[y];
                            f.addFloat(c);
                        }
                    }
                    if (tt.DataParser.STRINGS in l) {
                        if (f === null) {
                            f = tt.BaseObject.borrowObject(tt.UserData);
                        }
                        var g = l[tt.DataParser.STRINGS];
                        for (var D = 0, T = g; D < T.length; D++) {
                            var c = T[D];
                            f.addString(c);
                        }
                    }
                    n.data = f;
                    i.push(n);
                }
            }
            return i;
        };
        $.prototype._parseDeformFrame = function (t, e, a) {
            var r = this._frameFloatArray.length;
            var i = this._parseTweenFrame(t, e, a);
            var n =
                tt.DataParser.VERTICES in t
                    ? t[tt.DataParser.VERTICES]
                    : tt.DataParser.VALUE in t
                      ? t[tt.DataParser.VALUE]
                      : null;
            var s = $._getNumber(t, tt.DataParser.OFFSET, 0);
            var o = this._intArray[this._geometry.offset + 0];
            var l = this._geometry.weight;
            var h = 0;
            var u = 0;
            if (l !== null) {
            } else {
                this._frameFloatArray.length += o * 2;
                for (var f = 0; f < o * 2; f += 2) {
                    if (n !== null) {
                        if (f < s || f - s >= n.length) {
                            h = 0;
                        } else {
                            h = n[f - s];
                        }
                        if (f + 1 < s || f + 1 - s >= n.length) {
                            u = 0;
                        } else {
                            u = n[f + 1 - s];
                        }
                    } else {
                        h = 0;
                        u = 0;
                    }
                    this._frameFloatArray[r + f] = h;
                    this._frameFloatArray[r + f + 1] = u;
                }
            }
            if (e === 0) {
                var _ = this._frameIntArray.length;
                this._frameIntArray.length += 1 + 1 + 1 + 1 + 1;
                this._frameIntArray[_ + 0] = this._geometry.offset;
                this._frameIntArray[_ + 1] = this._frameFloatArray.length - r;
                this._frameIntArray[_ + 2] = this._frameFloatArray.length - r;
                this._frameIntArray[_ + 3] = 0;
                this._frameIntArray[_ + 4] = r - this._animation.frameFloatOffset;
                this._timelineArray[this._timeline.offset + 3] = _ - this._animation.frameIntOffset;
            }
            return i;
        };
        $.prototype._parseTransform = function (t, e, a) {
            e.x = $._getNumber(t, tt.DataParser.X, 0) * a;
            e.y = $._getNumber(t, tt.DataParser.Y, 0) * a;
            if (tt.DataParser.ROTATE in t || tt.DataParser.SKEW in t) {
                e.rotation = tt.Transform.normalizeRadian(
                    $._getNumber(t, tt.DataParser.ROTATE, 0) * tt.Transform.DEG_RAD
                );
                e.skew = tt.Transform.normalizeRadian(
                    $._getNumber(t, tt.DataParser.SKEW, 0) * tt.Transform.DEG_RAD
                );
            } else if (tt.DataParser.SKEW_X in t || tt.DataParser.SKEW_Y in t) {
                e.rotation = tt.Transform.normalizeRadian(
                    $._getNumber(t, tt.DataParser.SKEW_Y, 0) * tt.Transform.DEG_RAD
                );
                e.skew =
                    tt.Transform.normalizeRadian(
                        $._getNumber(t, tt.DataParser.SKEW_X, 0) * tt.Transform.DEG_RAD
                    ) - e.rotation;
            }
            e.scaleX = $._getNumber(t, tt.DataParser.SCALE_X, 1);
            e.scaleY = $._getNumber(t, tt.DataParser.SCALE_Y, 1);
        };
        $.prototype._parseColorTransform = function (t, e) {
            e.alphaMultiplier = $._getNumber(t, tt.DataParser.ALPHA_MULTIPLIER, 100) * 0.01;
            e.redMultiplier = $._getNumber(t, tt.DataParser.RED_MULTIPLIER, 100) * 0.01;
            e.greenMultiplier = $._getNumber(t, tt.DataParser.GREEN_MULTIPLIER, 100) * 0.01;
            e.blueMultiplier = $._getNumber(t, tt.DataParser.BLUE_MULTIPLIER, 100) * 0.01;
            e.alphaOffset = $._getNumber(t, tt.DataParser.ALPHA_OFFSET, 0);
            e.redOffset = $._getNumber(t, tt.DataParser.RED_OFFSET, 0);
            e.greenOffset = $._getNumber(t, tt.DataParser.GREEN_OFFSET, 0);
            e.blueOffset = $._getNumber(t, tt.DataParser.BLUE_OFFSET, 0);
        };
        $.prototype._parseGeometry = function (t, e) {
            var a = t[tt.DataParser.VERTICES];
            var r = Math.floor(a.length / 2);
            var i = 0;
            var n = this._intArray.length;
            var s = this._floatArray.length;
            e.offset = n;
            e.data = this._data;
            this._intArray.length += 1 + 1 + 1 + 1;
            this._intArray[n + 0] = r;
            this._intArray[n + 2] = s;
            this._intArray[n + 3] = -1;
            this._floatArray.length += r * 2;
            for (var o = 0, l = r * 2; o < l; ++o) {
                this._floatArray[s + o] = a[o];
            }
            if (tt.DataParser.TRIANGLES in t) {
                var h = t[tt.DataParser.TRIANGLES];
                i = Math.floor(h.length / 3);
                this._intArray.length += i * 3;
                for (var o = 0, l = i * 3; o < l; ++o) {
                    this._intArray[n + 4 + o] = h[o];
                }
            }
            this._intArray[n + 1] = i;
            if (tt.DataParser.UVS in t) {
                var u = t[tt.DataParser.UVS];
                var f = s + r * 2;
                this._floatArray.length += r * 2;
                for (var o = 0, l = r * 2; o < l; ++o) {
                    this._floatArray[f + o] = u[o];
                }
            }
            if (tt.DataParser.WEIGHTS in t) {
                var _ = t[tt.DataParser.WEIGHTS];
                var m = Math.floor(_.length - r) / 2;
                var p = this._intArray.length;
                var c = this._floatArray.length;
                var d = 0;
                var y = this._armature.sortedBones;
                var v = tt.BaseObject.borrowObject(tt.WeightData);
                v.count = m;
                v.offset = p;
                this._intArray.length += 1 + 1 + d + r + m;
                this._intArray[p + 1] = c;
                if (tt.DataParser.BONE_POSE in t) {
                    var g = t[tt.DataParser.SLOT_POSE];
                    var D = t[tt.DataParser.BONE_POSE];
                    var T = new Array();
                    d = Math.floor(D.length / 7);
                    T.length = d;
                    for (var o = 0; o < d; ++o) {
                        var b = D[o * 7];
                        var A = this._rawBones[b];
                        v.addBone(A);
                        T[o] = b;
                        this._intArray[p + 2 + o] = y.indexOf(A);
                    }
                    this._floatArray.length += m * 3;
                    this._helpMatrixA.copyFromArray(g, 0);
                    for (var o = 0, P = 0, S = p + 2 + d, O = c; o < r; ++o) {
                        var x = o * 2;
                        var B = (this._intArray[S++] = _[P++]);
                        var E = this._floatArray[s + x];
                        var I = this._floatArray[s + x + 1];
                        this._helpMatrixA.transformPoint(E, I, this._helpPoint);
                        E = this._helpPoint.x;
                        I = this._helpPoint.y;
                        for (var M = 0; M < B; ++M) {
                            var b = _[P++];
                            var F = T.indexOf(b);
                            this._helpMatrixB.copyFromArray(D, F * 7 + 1);
                            this._helpMatrixB.invert();
                            this._helpMatrixB.transformPoint(E, I, this._helpPoint);
                            this._intArray[S++] = F;
                            this._floatArray[O++] = _[P++];
                            this._floatArray[O++] = this._helpPoint.x;
                            this._floatArray[O++] = this._helpPoint.y;
                        }
                    }
                } else {
                    var w = t[tt.DataParser.BONES];
                    d = w.length;
                    for (var o = 0; o < d; o++) {
                        var b = w[o];
                        var A = this._rawBones[b];
                        v.addBone(A);
                        this._intArray[p + 2 + o] = y.indexOf(A);
                    }
                    this._floatArray.length += m * 3;
                    for (var o = 0, P = 0, O = 0, S = p + 2 + d, C = c; o < m; o++) {
                        var B = _[P++];
                        this._intArray[S++] = B;
                        for (var M = 0; M < B; M++) {
                            var F = _[P++];
                            var N = _[P++];
                            var E = a[O++];
                            var I = a[O++];
                            this._intArray[S++] = w.indexOf(F);
                            this._floatArray[C++] = N;
                            this._floatArray[C++] = E;
                            this._floatArray[C++] = I;
                        }
                    }
                }
                e.weight = v;
            }
        };
        $.prototype._parseArray = function (t) {
            t;
            this._intArray.length = 0;
            this._floatArray.length = 0;
            this._frameIntArray.length = 0;
            this._frameFloatArray.length = 0;
            this._frameArray.length = 0;
            this._timelineArray.length = 0;
            this._colorArray.length = 0;
        };
        $.prototype._modifyArray = function () {
            if (this._intArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._intArray.push(0);
            }
            if (this._frameIntArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._frameIntArray.push(0);
            }
            if (this._frameArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._frameArray.push(0);
            }
            if (this._timelineArray.length % Uint16Array.BYTES_PER_ELEMENT !== 0) {
                this._timelineArray.push(0);
            }
            if (this._timelineArray.length % Int16Array.BYTES_PER_ELEMENT !== 0) {
                this._colorArray.push(0);
            }
            var t = this._intArray.length * Int16Array.BYTES_PER_ELEMENT;
            var e = this._floatArray.length * Float32Array.BYTES_PER_ELEMENT;
            var a = this._frameIntArray.length * Int16Array.BYTES_PER_ELEMENT;
            var r = this._frameFloatArray.length * Float32Array.BYTES_PER_ELEMENT;
            var i = this._frameArray.length * Int16Array.BYTES_PER_ELEMENT;
            var n = this._timelineArray.length * Uint16Array.BYTES_PER_ELEMENT;
            var s = this._colorArray.length * Int16Array.BYTES_PER_ELEMENT;
            var o = t + e + a + r + i + n + s;
            var l = new ArrayBuffer(o);
            var h = new Int16Array(l, 0, this._intArray.length);
            var u = new Float32Array(l, t, this._floatArray.length);
            var f = new Int16Array(l, t + e, this._frameIntArray.length);
            var _ = new Float32Array(l, t + e + a, this._frameFloatArray.length);
            var m = new Int16Array(l, t + e + a + r, this._frameArray.length);
            var p = new Uint16Array(l, t + e + a + r + i, this._timelineArray.length);
            var c = new Int16Array(l, t + e + a + r + i + n, this._colorArray.length);
            for (var d = 0, y = this._intArray.length; d < y; ++d) {
                h[d] = this._intArray[d];
            }
            for (var d = 0, y = this._floatArray.length; d < y; ++d) {
                u[d] = this._floatArray[d];
            }
            for (var d = 0, y = this._frameIntArray.length; d < y; ++d) {
                f[d] = this._frameIntArray[d];
            }
            for (var d = 0, y = this._frameFloatArray.length; d < y; ++d) {
                _[d] = this._frameFloatArray[d];
            }
            for (var d = 0, y = this._frameArray.length; d < y; ++d) {
                m[d] = this._frameArray[d];
            }
            for (var d = 0, y = this._timelineArray.length; d < y; ++d) {
                p[d] = this._timelineArray[d];
            }
            for (var d = 0, y = this._colorArray.length; d < y; ++d) {
                c[d] = this._colorArray[d];
            }
            this._data.binary = l;
            this._data.intArray = h;
            this._data.floatArray = u;
            this._data.frameIntArray = f;
            this._data.frameFloatArray = _;
            this._data.frameArray = m;
            this._data.timelineArray = p;
            this._data.colorArray = c;
            this._defaultColorOffset = -1;
        };
        $.prototype.parseDragonBonesData = function (t, e) {
            if (e === void 0) {
                e = 1;
            }
            console.assert(t !== null && t !== undefined, 'Data error.');
            var a = $._getString(t, tt.DataParser.VERSION, '');
            var r = $._getString(t, tt.DataParser.COMPATIBLE_VERSION, '');
            if (
                tt.DataParser.DATA_VERSIONS.indexOf(a) >= 0 ||
                tt.DataParser.DATA_VERSIONS.indexOf(r) >= 0
            ) {
                var i = tt.BaseObject.borrowObject(tt.DragonBonesData);
                i.version = a;
                i.name = $._getString(t, tt.DataParser.NAME, '');
                i.frameRate = $._getNumber(t, tt.DataParser.FRAME_RATE, 24);
                if (i.frameRate === 0) {
                    i.frameRate = 24;
                }
                if (tt.DataParser.ARMATURE in t) {
                    this._data = i;
                    this._parseArray(t);
                    var n = t[tt.DataParser.ARMATURE];
                    for (var s = 0, o = n; s < o.length; s++) {
                        var l = o[s];
                        i.addArmature(this._parseArmature(l, e));
                    }
                    if (!this._data.binary) {
                        this._modifyArray();
                    }
                    if (tt.DataParser.STAGE in t) {
                        i.stage = i.getArmature($._getString(t, tt.DataParser.STAGE, ''));
                    } else if (i.armatureNames.length > 0) {
                        i.stage = i.getArmature(i.armatureNames[0]);
                    }
                    this._data = null;
                }
                if (tt.DataParser.TEXTURE_ATLAS in t) {
                    this._rawTextureAtlases = t[tt.DataParser.TEXTURE_ATLAS];
                }
                return i;
            } else {
                console.assert(
                    false,
                    'Nonsupport data version: ' +
                        a +
                        '\n' +
                        'Please convert DragonBones data to support version.\n' +
                        'Read more: https://github.com/DragonBones/Tools/'
                );
            }
            return null;
        };
        $.prototype.parseTextureAtlasData = function (t, e, a) {
            if (a === void 0) {
                a = 1;
            }
            console.assert(t !== undefined);
            if (t === null) {
                if (this._rawTextureAtlases === null || this._rawTextureAtlases.length === 0) {
                    return false;
                }
                var r = this._rawTextureAtlases[this._rawTextureAtlasIndex++];
                this.parseTextureAtlasData(r, e, a);
                if (this._rawTextureAtlasIndex >= this._rawTextureAtlases.length) {
                    this._rawTextureAtlasIndex = 0;
                    this._rawTextureAtlases = null;
                }
                return true;
            }
            e.width = $._getNumber(t, tt.DataParser.WIDTH, 0);
            e.height = $._getNumber(t, tt.DataParser.HEIGHT, 0);
            e.scale = a === 1 ? 1 / $._getNumber(t, tt.DataParser.SCALE, 1) : a;
            e.name = $._getString(t, tt.DataParser.NAME, '');
            e.imagePath = $._getString(t, tt.DataParser.IMAGE_PATH, '');
            if (tt.DataParser.SUB_TEXTURE in t) {
                var i = t[tt.DataParser.SUB_TEXTURE];
                for (var n = 0, s = i.length; n < s; ++n) {
                    var o = i[n];
                    var l = $._getNumber(o, tt.DataParser.FRAME_WIDTH, -1);
                    var h = $._getNumber(o, tt.DataParser.FRAME_HEIGHT, -1);
                    var u = e.createTexture();
                    u.rotated = $._getBoolean(o, tt.DataParser.ROTATED, false);
                    u.name = $._getString(o, tt.DataParser.NAME, '');
                    u.region.x = $._getNumber(o, tt.DataParser.X, 0);
                    u.region.y = $._getNumber(o, tt.DataParser.Y, 0);
                    u.region.width = $._getNumber(o, tt.DataParser.WIDTH, 0);
                    u.region.height = $._getNumber(o, tt.DataParser.HEIGHT, 0);
                    if (l > 0 && h > 0) {
                        u.frame = tt.TextureData.createRectangle();
                        u.frame.x = $._getNumber(o, tt.DataParser.FRAME_X, 0);
                        u.frame.y = $._getNumber(o, tt.DataParser.FRAME_Y, 0);
                        u.frame.width = l;
                        u.frame.height = h;
                    }
                    e.addTexture(u);
                }
            }
            return true;
        };
        $.getInstance = function () {
            if ($._objectDataParserInstance === null) {
                $._objectDataParserInstance = new $();
            }
            return $._objectDataParserInstance;
        };
        $._objectDataParserInstance = null;
        return $;
    })(tt.DataParser);
    tt.ObjectDataParser = t;
    var D = (function () {
        function t() {
            this.frameStart = 0;
            this.actions = [];
        }
        return t;
    })();
    tt.ActionFrame = D;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (g) {
    var t = (function (o) {
        __extends(t, o);
        function t() {
            return (o !== null && o.apply(this, arguments)) || this;
        }
        t.prototype._inRange = function (t, e, a) {
            return e <= t && t <= a;
        };
        t.prototype._decodeUTF8 = function (t) {
            var e = -1;
            var a = -1;
            var r = 65533;
            var i = 0;
            var n = '';
            var s;
            var o = 0;
            var l = 0;
            var h = 0;
            var u = 0;
            while (t.length > i) {
                var f = t[i++];
                if (f === e) {
                    if (l !== 0) {
                        s = r;
                    } else {
                        s = a;
                    }
                } else {
                    if (l === 0) {
                        if (this._inRange(f, 0, 127)) {
                            s = f;
                        } else {
                            if (this._inRange(f, 194, 223)) {
                                l = 1;
                                u = 128;
                                o = f - 192;
                            } else if (this._inRange(f, 224, 239)) {
                                l = 2;
                                u = 2048;
                                o = f - 224;
                            } else if (this._inRange(f, 240, 244)) {
                                l = 3;
                                u = 65536;
                                o = f - 240;
                            } else {
                            }
                            o = o * Math.pow(64, l);
                            s = null;
                        }
                    } else if (!this._inRange(f, 128, 191)) {
                        o = 0;
                        l = 0;
                        h = 0;
                        u = 0;
                        i--;
                        s = f;
                    } else {
                        h += 1;
                        o = o + (f - 128) * Math.pow(64, l - h);
                        if (h !== l) {
                            s = null;
                        } else {
                            var _ = o;
                            var m = u;
                            o = 0;
                            l = 0;
                            h = 0;
                            u = 0;
                            if (this._inRange(_, m, 1114111) && !this._inRange(_, 55296, 57343)) {
                                s = _;
                            } else {
                                s = f;
                            }
                        }
                    }
                }
                if (s !== null && s !== a) {
                    if (s <= 65535) {
                        if (s > 0) n += String.fromCharCode(s);
                    } else {
                        s -= 65536;
                        n += String.fromCharCode(55296 + ((s >> 10) & 1023));
                        n += String.fromCharCode(56320 + (s & 1023));
                    }
                }
            }
            return n;
        };
        t.prototype._parseBinaryTimeline = function (t, e, a) {
            if (a === void 0) {
                a = null;
            }
            var r = a !== null ? a : g.BaseObject.borrowObject(g.TimelineData);
            r.type = t;
            r.offset = e;
            this._timeline = r;
            var i = this._timelineArrayBuffer[r.offset + 2];
            if (i === 1) {
                r.frameIndicesOffset = -1;
            } else {
                var n = 0;
                var s = this._animation.frameCount + 1;
                var o = this._data.frameIndices;
                n = o.length;
                o.length += s;
                r.frameIndicesOffset = n;
                for (var l = 0, h = 0, u = 0, f = 0; l < s; ++l) {
                    if (u + f <= l && h < i) {
                        u =
                            this._frameArrayBuffer[
                                this._animation.frameOffset +
                                    this._timelineArrayBuffer[r.offset + 5 + h]
                            ];
                        if (h === i - 1) {
                            f = this._animation.frameCount - u;
                        } else {
                            f =
                                this._frameArrayBuffer[
                                    this._animation.frameOffset +
                                        this._timelineArrayBuffer[r.offset + 5 + h + 1]
                                ] - u;
                        }
                        h++;
                    }
                    o[n + l] = h - 1;
                }
            }
            this._timeline = null;
            return r;
        };
        t.prototype._parseAnimation = function (t) {
            var e = g.BaseObject.borrowObject(g.AnimationData);
            e.blendType = g.DataParser._getAnimationBlendType(
                g.ObjectDataParser._getString(t, g.DataParser.BLEND_TYPE, '')
            );
            e.frameCount = g.ObjectDataParser._getNumber(t, g.DataParser.DURATION, 0);
            e.playTimes = g.ObjectDataParser._getNumber(t, g.DataParser.PLAY_TIMES, 1);
            e.duration = e.frameCount / this._armature.frameRate;
            e.fadeInTime = g.ObjectDataParser._getNumber(t, g.DataParser.FADE_IN_TIME, 0);
            e.scale = g.ObjectDataParser._getNumber(t, g.DataParser.SCALE, 1);
            e.name = g.ObjectDataParser._getString(t, g.DataParser.NAME, g.DataParser.DEFAULT_NAME);
            if (e.name.length === 0) {
                e.name = g.DataParser.DEFAULT_NAME;
            }
            var a = t[g.DataParser.OFFSET];
            e.frameIntOffset = a[0];
            e.frameFloatOffset = a[1];
            e.frameOffset = a[2];
            this._animation = e;
            if (g.DataParser.ACTION in t) {
                e.actionTimeline = this._parseBinaryTimeline(0, t[g.DataParser.ACTION]);
            }
            if (g.DataParser.Z_ORDER in t) {
                e.zOrderTimeline = this._parseBinaryTimeline(1, t[g.DataParser.Z_ORDER]);
            }
            if (g.DataParser.BONE in t) {
                var r = t[g.DataParser.BONE];
                for (var i in r) {
                    var n = r[i];
                    var s = this._armature.getBone(i);
                    if (s === null) {
                        continue;
                    }
                    for (var o = 0, l = n.length; o < l; o += 2) {
                        var h = n[o];
                        var u = n[o + 1];
                        var f = this._parseBinaryTimeline(h, u);
                        this._animation.addBoneTimeline(s.name, f);
                    }
                }
            }
            if (g.DataParser.SLOT in t) {
                var r = t[g.DataParser.SLOT];
                for (var i in r) {
                    var n = r[i];
                    var _ = this._armature.getSlot(i);
                    if (_ === null) {
                        continue;
                    }
                    for (var o = 0, l = n.length; o < l; o += 2) {
                        var h = n[o];
                        var u = n[o + 1];
                        var f = this._parseBinaryTimeline(h, u);
                        this._animation.addSlotTimeline(_.name, f);
                    }
                }
            }
            if (g.DataParser.CONSTRAINT in t) {
                var r = t[g.DataParser.CONSTRAINT];
                for (var i in r) {
                    var n = r[i];
                    var m = this._armature.getConstraint(i);
                    if (m === null) {
                        continue;
                    }
                    for (var o = 0, l = n.length; o < l; o += 2) {
                        var h = n[o];
                        var u = n[o + 1];
                        var f = this._parseBinaryTimeline(h, u);
                        this._animation.addConstraintTimeline(m.name, f);
                    }
                }
            }
            if (g.DataParser.TIMELINE in t) {
                var n = t[g.DataParser.TIMELINE];
                for (var p = 0, c = n; p < c.length; p++) {
                    var d = c[p];
                    var u = g.ObjectDataParser._getNumber(d, g.DataParser.OFFSET, 0);
                    if (u >= 0) {
                        var h = g.ObjectDataParser._getNumber(d, g.DataParser.TYPE, 0);
                        var y = g.ObjectDataParser._getString(d, g.DataParser.NAME, '');
                        var f = null;
                        if (h === 40 && e.blendType !== 0) {
                            f = g.BaseObject.borrowObject(g.AnimationTimelineData);
                            var v = f;
                            v.x = g.ObjectDataParser._getNumber(d, g.DataParser.X, 0);
                            v.y = g.ObjectDataParser._getNumber(d, g.DataParser.Y, 0);
                        }
                        f = this._parseBinaryTimeline(h, u, f);
                        switch (h) {
                            case 0:
                                break;
                            case 1:
                                break;
                            case 11:
                            case 12:
                            case 13:
                            case 50:
                            case 60:
                                this._animation.addBoneTimeline(y, f);
                                break;
                            case 20:
                            case 21:
                            case 22:
                            case 23:
                            case 24:
                                this._animation.addSlotTimeline(y, f);
                                break;
                            case 30:
                                this._animation.addConstraintTimeline(y, f);
                                break;
                            case 40:
                            case 41:
                            case 42:
                                this._animation.addAnimationTimeline(y, f);
                                break;
                        }
                    }
                }
            }
            this._animation = null;
            return e;
        };
        t.prototype._parseGeometry = function (t, e) {
            e.offset = t[g.DataParser.OFFSET];
            e.data = this._data;
            var a = this._intArrayBuffer[e.offset + 3];
            if (a >= 0) {
                var r = g.BaseObject.borrowObject(g.WeightData);
                var i = this._intArrayBuffer[e.offset + 0];
                var n = this._intArrayBuffer[a + 0];
                r.offset = a;
                for (var s = 0; s < n; ++s) {
                    var o = this._intArrayBuffer[a + 2 + s];
                    r.addBone(this._rawBones[o]);
                }
                var l = a + 2 + n;
                var h = 0;
                for (var s = 0, u = i; s < u; ++s) {
                    var f = this._intArrayBuffer[l++];
                    h += f;
                    l += f;
                }
                r.count = h;
                e.weight = r;
            }
        };
        t.prototype._parseArray = function (t) {
            var e = t[g.DataParser.OFFSET];
            var a = e[1];
            var r = e[3];
            var i = e[5];
            var n = e[7];
            var s = e[9];
            var o = e[11];
            var l = e.length > 12 ? e[13] : 0;
            var h = new Int16Array(
                this._binary,
                this._binaryOffset + e[0],
                a / Int16Array.BYTES_PER_ELEMENT
            );
            var u = new Float32Array(
                this._binary,
                this._binaryOffset + e[2],
                r / Float32Array.BYTES_PER_ELEMENT
            );
            var f = new Int16Array(
                this._binary,
                this._binaryOffset + e[4],
                i / Int16Array.BYTES_PER_ELEMENT
            );
            var _ = new Float32Array(
                this._binary,
                this._binaryOffset + e[6],
                n / Float32Array.BYTES_PER_ELEMENT
            );
            var m = new Int16Array(
                this._binary,
                this._binaryOffset + e[8],
                s / Int16Array.BYTES_PER_ELEMENT
            );
            var p = new Uint16Array(
                this._binary,
                this._binaryOffset + e[10],
                o / Uint16Array.BYTES_PER_ELEMENT
            );
            var c =
                l > 0
                    ? new Int16Array(
                          this._binary,
                          this._binaryOffset + e[12],
                          l / Int16Array.BYTES_PER_ELEMENT
                      )
                    : h;
            this._data.binary = this._binary;
            this._data.intArray = this._intArrayBuffer = h;
            this._data.floatArray = u;
            this._data.frameIntArray = f;
            this._data.frameFloatArray = _;
            this._data.frameArray = this._frameArrayBuffer = m;
            this._data.timelineArray = this._timelineArrayBuffer = p;
            this._data.colorArray = c;
        };
        t.prototype.parseDragonBonesData = function (t, e) {
            if (e === void 0) {
                e = 1;
            }
            console.assert(
                t !== null && t !== undefined && t instanceof ArrayBuffer,
                'Data error.'
            );
            var a = new Uint8Array(t, 0, 8);
            if (
                a[0] !== 'D'.charCodeAt(0) ||
                a[1] !== 'B'.charCodeAt(0) ||
                a[2] !== 'D'.charCodeAt(0) ||
                a[3] !== 'T'.charCodeAt(0)
            ) {
                console.assert(false, 'Nonsupport data.');
                return null;
            }
            var r = new Uint32Array(t, 8, 1)[0];
            var i = new Uint8Array(t, 8 + 4, r);
            var n = this._decodeUTF8(i);
            var s = JSON.parse(n);
            this._binaryOffset = 8 + 4 + r;
            this._binary = t;
            return o.prototype.parseDragonBonesData.call(this, s, e);
        };
        t.getInstance = function () {
            if (t._binaryDataParserInstance === null) {
                t._binaryDataParserInstance = new t();
            }
            return t._binaryDataParserInstance;
        };
        t._binaryDataParserInstance = null;
        return t;
    })(g.ObjectDataParser);
    g.BinaryDataParser = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (y) {
    var t = (function () {
        function s(t) {
            if (t === void 0) {
                t = null;
            }
            this.autoSearch = false;
            this._dragonBonesDataMap = {};
            this._textureAtlasDataMap = {};
            this._dragonBones = null;
            this._dataParser = null;
            if (s._objectParser === null) {
                s._objectParser = new y.ObjectDataParser();
            }
            if (s._binaryParser === null) {
                s._binaryParser = new y.BinaryDataParser();
            }
            this._dataParser = t !== null ? t : s._objectParser;
        }
        s.prototype._isSupportMesh = function () {
            return true;
        };
        s.prototype._getTextureData = function (t, e) {
            if (t in this._textureAtlasDataMap) {
                for (var a = 0, r = this._textureAtlasDataMap[t]; a < r.length; a++) {
                    var i = r[a];
                    var n = i.getTexture(e);
                    if (n !== null) {
                        return n;
                    }
                }
            }
            if (this.autoSearch) {
                for (var s in this._textureAtlasDataMap) {
                    for (var o = 0, l = this._textureAtlasDataMap[s]; o < l.length; o++) {
                        var i = l[o];
                        if (i.autoSearch) {
                            var n = i.getTexture(e);
                            if (n !== null) {
                                return n;
                            }
                        }
                    }
                }
            }
            return null;
        };
        s.prototype._fillBuildArmaturePackage = function (t, e, a, r, i) {
            var n = null;
            var s = null;
            if (e.length > 0) {
                if (e in this._dragonBonesDataMap) {
                    n = this._dragonBonesDataMap[e];
                    s = n.getArmature(a);
                }
            }
            if (s === null && (e.length === 0 || this.autoSearch)) {
                for (var o in this._dragonBonesDataMap) {
                    n = this._dragonBonesDataMap[o];
                    if (e.length === 0 || n.autoSearch) {
                        s = n.getArmature(a);
                        if (s !== null) {
                            e = o;
                            break;
                        }
                    }
                }
            }
            if (s !== null) {
                t.dataName = e;
                t.textureAtlasName = i;
                t.data = n;
                t.armature = s;
                t.skin = null;
                if (r.length > 0) {
                    t.skin = s.getSkin(r);
                    if (t.skin === null && this.autoSearch) {
                        for (var o in this._dragonBonesDataMap) {
                            var l = this._dragonBonesDataMap[o];
                            var h = l.getArmature(r);
                            if (h !== null) {
                                t.skin = h.defaultSkin;
                                break;
                            }
                        }
                    }
                }
                if (t.skin === null) {
                    t.skin = s.defaultSkin;
                }
                return true;
            }
            return false;
        };
        s.prototype._buildBones = function (t, e) {
            for (var a = 0, r = t.armature.sortedBones; a < r.length; a++) {
                var i = r[a];
                var n = y.BaseObject.borrowObject(i.type === 0 ? y.Bone : y.Surface);
                n.init(i, e);
            }
        };
        s.prototype._buildSlots = function (t, e) {
            var a = t.skin;
            var r = t.armature.defaultSkin;
            if (a === null || r === null) {
                return;
            }
            var i = {};
            for (var n in r.displays) {
                var s = r.getDisplays(n);
                i[n] = s;
            }
            if (a !== r) {
                for (var n in a.displays) {
                    var s = a.getDisplays(n);
                    i[n] = s;
                }
            }
            for (var o = 0, l = t.armature.sortedSlots; o < l.length; o++) {
                var h = l[o];
                var u = h.name in i ? i[h.name] : null;
                var f = this._buildSlot(t, h, e);
                if (u !== null) {
                    f.displayFrameCount = u.length;
                    for (var _ = 0, m = f.displayFrameCount; _ < m; ++_) {
                        var p = u[_];
                        f.replaceRawDisplayData(p, _);
                        if (p !== null) {
                            if (t.textureAtlasName.length > 0) {
                                var c = this._getTextureData(t.textureAtlasName, p.path);
                                f.replaceTextureData(c, _);
                            }
                            var d = this._getSlotDisplay(t, p, f);
                            f.replaceDisplay(d, _);
                        } else {
                            f.replaceDisplay(null);
                        }
                    }
                }
                f._setDisplayIndex(h.displayIndex, true);
            }
        };
        s.prototype._buildConstraints = function (t, e) {
            var a = t.armature.constraints;
            for (var r in a) {
                var i = a[r];
                switch (i.type) {
                    case 0:
                        var n = y.BaseObject.borrowObject(y.IKConstraint);
                        n.init(i, e);
                        e._addConstraint(n);
                        break;
                    case 1:
                        var s = y.BaseObject.borrowObject(y.PathConstraint);
                        s.init(i, e);
                        e._addConstraint(s);
                        break;
                    default:
                        var o = y.BaseObject.borrowObject(y.IKConstraint);
                        o.init(i, e);
                        e._addConstraint(o);
                        break;
                }
            }
        };
        s.prototype._buildChildArmature = function (t, e, a) {
            return this.buildArmature(
                a.path,
                t !== null ? t.dataName : '',
                '',
                t !== null ? t.textureAtlasName : ''
            );
        };
        s.prototype._getSlotDisplay = function (t, e, a) {
            var r = t !== null ? t.dataName : e.parent.parent.parent.name;
            var i = null;
            switch (e.type) {
                case 0: {
                    var n = e;
                    if (n.texture === null) {
                        n.texture = this._getTextureData(r, e.path);
                    }
                    i = a.rawDisplay;
                    break;
                }
                case 2: {
                    var s = e;
                    if (s.texture === null) {
                        s.texture = this._getTextureData(r, s.path);
                    }
                    if (this._isSupportMesh()) {
                        i = a.meshDisplay;
                    } else {
                        i = a.rawDisplay;
                    }
                    break;
                }
                case 1: {
                    var o = e;
                    var l = this._buildChildArmature(t, a, o);
                    if (l !== null) {
                        l.inheritAnimation = o.inheritAnimation;
                        if (!l.inheritAnimation) {
                            var h =
                                o.actions.length > 0 ? o.actions : l.armatureData.defaultActions;
                            if (h.length > 0) {
                                for (var u = 0, f = h; u < f.length; u++) {
                                    var _ = f[u];
                                    var m = y.BaseObject.borrowObject(y.EventObject);
                                    y.EventObject.actionDataToInstance(_, m, a.armature);
                                    m.slot = a;
                                    a.armature._bufferAction(m, false);
                                }
                            } else {
                                l.animation.play();
                            }
                        }
                        o.armature = l.armatureData;
                    }
                    i = l;
                    break;
                }
                case 3:
                    break;
                default:
                    break;
            }
            return i;
        };
        s.prototype.parseDragonBonesData = function (t, e, a) {
            if (e === void 0) {
                e = null;
            }
            if (a === void 0) {
                a = 1;
            }
            var r = t instanceof ArrayBuffer ? s._binaryParser : this._dataParser;
            var i = r.parseDragonBonesData(t, a);
            while (true) {
                var n = this._buildTextureAtlasData(null, null);
                if (r.parseTextureAtlasData(null, n, a)) {
                    this.addTextureAtlasData(n, e);
                } else {
                    n.returnToPool();
                    break;
                }
            }
            if (i !== null) {
                this.addDragonBonesData(i, e);
            }
            return i;
        };
        s.prototype.parseTextureAtlasData = function (t, e, a, r) {
            if (a === void 0) {
                a = null;
            }
            if (r === void 0) {
                r = 1;
            }
            var i = this._buildTextureAtlasData(null, null);
            this._dataParser.parseTextureAtlasData(t, i, r);
            this._buildTextureAtlasData(i, e || null);
            this.addTextureAtlasData(i, a);
            return i;
        };
        s.prototype.updateTextureAtlases = function (t, e) {
            var a = this.getTextureAtlasData(e);
            if (a !== null) {
                for (var r = 0, i = a.length; r < i; ++r) {
                    if (r < t.length) {
                        this._buildTextureAtlasData(a[r], t[r]);
                    }
                }
            }
        };
        s.prototype.getDragonBonesData = function (t) {
            return t in this._dragonBonesDataMap ? this._dragonBonesDataMap[t] : null;
        };
        s.prototype.addDragonBonesData = function (t, e) {
            if (e === void 0) {
                e = null;
            }
            e = e !== null ? e : t.name;
            if (e in this._dragonBonesDataMap) {
                if (this._dragonBonesDataMap[e] === t) {
                    return;
                }
                console.warn('Can not add same name data: ' + e);
                return;
            }
            this._dragonBonesDataMap[e] = t;
        };
        s.prototype.removeDragonBonesData = function (t, e) {
            if (e === void 0) {
                e = true;
            }
            if (t in this._dragonBonesDataMap) {
                if (e) {
                    this._dragonBones.bufferObject(this._dragonBonesDataMap[t]);
                }
                delete this._dragonBonesDataMap[t];
            }
        };
        s.prototype.getTextureAtlasData = function (t) {
            return t in this._textureAtlasDataMap ? this._textureAtlasDataMap[t] : null;
        };
        s.prototype.addTextureAtlasData = function (t, e) {
            if (e === void 0) {
                e = null;
            }
            e = e !== null ? e : t.name;
            var a =
                e in this._textureAtlasDataMap
                    ? this._textureAtlasDataMap[e]
                    : (this._textureAtlasDataMap[e] = []);
            if (a.indexOf(t) < 0) {
                a.push(t);
            }
        };
        s.prototype.removeTextureAtlasData = function (t, e) {
            if (e === void 0) {
                e = true;
            }
            if (t in this._textureAtlasDataMap) {
                var a = this._textureAtlasDataMap[t];
                if (e) {
                    for (var r = 0, i = a; r < i.length; r++) {
                        var n = i[r];
                        this._dragonBones.bufferObject(n);
                    }
                }
                delete this._textureAtlasDataMap[t];
            }
        };
        s.prototype.getArmatureData = function (t, e) {
            if (e === void 0) {
                e = '';
            }
            var a = new o();
            if (!this._fillBuildArmaturePackage(a, e, t, '', '')) {
                return null;
            }
            return a.armature;
        };
        s.prototype.clear = function (t) {
            if (t === void 0) {
                t = true;
            }
            for (var e in this._dragonBonesDataMap) {
                if (t) {
                    this._dragonBones.bufferObject(this._dragonBonesDataMap[e]);
                }
                delete this._dragonBonesDataMap[e];
            }
            for (var e in this._textureAtlasDataMap) {
                if (t) {
                    var a = this._textureAtlasDataMap[e];
                    for (var r = 0, i = a; r < i.length; r++) {
                        var n = i[r];
                        this._dragonBones.bufferObject(n);
                    }
                }
                delete this._textureAtlasDataMap[e];
            }
        };
        s.prototype.buildArmature = function (t, e, a, r) {
            if (e === void 0) {
                e = '';
            }
            if (a === void 0) {
                a = '';
            }
            if (r === void 0) {
                r = '';
            }
            var i = new o();
            if (!this._fillBuildArmaturePackage(i, e || '', t, a || '', r || '')) {
                console.warn('No armature data: ' + t + ', ' + (e !== null ? e : ''));
                return null;
            }
            var n = this._buildArmature(i);
            this._buildBones(i, n);
            this._buildSlots(i, n);
            this._buildConstraints(i, n);
            n.invalidUpdate(null, true);
            n.advanceTime(0);
            return n;
        };
        s.prototype.replaceDisplay = function (t, e, a) {
            if (a === void 0) {
                a = -1;
            }
            if (a < 0) {
                a = t.displayIndex;
            }
            if (a < 0) {
                a = 0;
            }
            t.replaceDisplayData(e, a);
            if (e !== null) {
                var r = this._getSlotDisplay(null, e, t);
                if (e.type === 0) {
                    var i = t.getDisplayFrameAt(a).rawDisplayData;
                    if (i !== null && i.type === 2) {
                        r = t.meshDisplay;
                    }
                }
                t.replaceDisplay(r, a);
            } else {
                t.replaceDisplay(null, a);
            }
        };
        s.prototype.replaceSlotDisplay = function (t, e, a, r, i, n) {
            if (n === void 0) {
                n = -1;
            }
            var s = this.getArmatureData(e, t || '');
            if (s === null || s.defaultSkin === null) {
                return false;
            }
            var o = s.defaultSkin.getDisplay(a, r);
            this.replaceDisplay(i, o, n);
            return true;
        };
        s.prototype.replaceSlotDisplayList = function (t, e, a, r) {
            var i = this.getArmatureData(e, t || '');
            if (!i || !i.defaultSkin) {
                return false;
            }
            var n = i.defaultSkin.getDisplays(a);
            if (!n) {
                return false;
            }
            r.displayFrameCount = n.length;
            for (var s = 0, o = r.displayFrameCount; s < o; ++s) {
                var l = n[s];
                this.replaceDisplay(r, l, s);
            }
            return true;
        };
        s.prototype.replaceSkin = function (t, e, a, r) {
            if (a === void 0) {
                a = false;
            }
            if (r === void 0) {
                r = null;
            }
            var i = false;
            var n = e.parent.defaultSkin;
            for (var s = 0, o = t.getSlots(); s < o.length; s++) {
                var l = o[s];
                if (r !== null && r.indexOf(l.name) >= 0) {
                    continue;
                }
                var h = e.getDisplays(l.name);
                if (h === null) {
                    if (n !== null && e !== n) {
                        h = n.getDisplays(l.name);
                    }
                    if (h === null) {
                        if (a) {
                            l.displayFrameCount = 0;
                        }
                        continue;
                    }
                }
                l.displayFrameCount = h.length;
                for (var u = 0, f = l.displayFrameCount; u < f; ++u) {
                    var _ = h[u];
                    l.replaceRawDisplayData(_, u);
                    if (_ !== null) {
                        l.replaceDisplay(this._getSlotDisplay(null, _, l), u);
                    } else {
                        l.replaceDisplay(null, u);
                    }
                }
                i = true;
            }
            return i;
        };
        s.prototype.replaceAnimation = function (t, e, a) {
            if (a === void 0) {
                a = true;
            }
            var r = e.defaultSkin;
            if (r === null) {
                return false;
            }
            if (a) {
                t.animation.animations = e.animations;
            } else {
                var i = t.animation.animations;
                var n = {};
                for (var s in i) {
                    n[s] = i[s];
                }
                for (var s in e.animations) {
                    n[s] = e.animations[s];
                }
                t.animation.animations = n;
            }
            for (var o = 0, l = t.getSlots(); o < l.length; o++) {
                var h = l[o];
                var u = 0;
                for (var f = 0, _ = h.displayList; f < _.length; f++) {
                    var m = _[f];
                    if (m instanceof y.Armature) {
                        var p = r.getDisplays(h.name);
                        if (p !== null && u < p.length) {
                            var c = p[u];
                            if (c !== null && c.type === 1) {
                                var d = this.getArmatureData(c.path, c.parent.parent.parent.name);
                                if (d) {
                                    this.replaceAnimation(m, d, a);
                                }
                            }
                        }
                    }
                    u++;
                }
            }
            return true;
        };
        s.prototype.getAllDragonBonesData = function () {
            return this._dragonBonesDataMap;
        };
        s.prototype.getAllTextureAtlasData = function () {
            return this._textureAtlasDataMap;
        };
        Object.defineProperty(s.prototype, 'clock', {
            get: function () {
                return this._dragonBones.clock;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(s.prototype, 'dragonBones', {
            get: function () {
                return this._dragonBones;
            },
            enumerable: true,
            configurable: true,
        });
        s._objectParser = null;
        s._binaryParser = null;
        return s;
    })();
    y.BaseFactory = t;
    var o = (function () {
        function t() {
            this.dataName = '';
            this.textureAtlasName = '';
            this.skin = null;
        }
        return t;
    })();
    y.BuildArmaturePackage = o;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (a) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t._renderTexture = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.PixiTextureAtlasData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            if (this._renderTexture !== null) {
            }
            this._renderTexture = null;
        };
        t.prototype.createTexture = function () {
            return a.BaseObject.borrowObject(r);
        };
        Object.defineProperty(t.prototype, 'renderTexture', {
            get: function () {
                return this._renderTexture;
            },
            set: function (t) {
                if (this._renderTexture === t) {
                    return;
                }
                this._renderTexture = t;
                if (this._renderTexture !== null) {
                    for (var e in this.textures) {
                        var a = this.textures[e];
                        a.renderTexture = new PIXI.Texture(
                            this._renderTexture,
                            new PIXI.Rectangle(
                                a.region.x,
                                a.region.y,
                                a.region.width,
                                a.region.height
                            ),
                            new PIXI.Rectangle(
                                a.region.x,
                                a.region.y,
                                a.region.width,
                                a.region.height
                            ),
                            new PIXI.Rectangle(0, 0, a.region.width, a.region.height),
                            a.rotated
                        );
                    }
                } else {
                    for (var e in this.textures) {
                        var a = this.textures[e];
                        a.renderTexture = null;
                    }
                }
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })(a.TextureAtlasData);
    a.PixiTextureAtlasData = t;
    var r = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.renderTexture = null;
            return t;
        }
        t.toString = function () {
            return '[class dragonBones.PixiTextureData]';
        };
        t.prototype._onClear = function () {
            e.prototype._onClear.call(this);
            if (this.renderTexture !== null) {
                this.renderTexture.destroy(false);
            }
            this.renderTexture = null;
        };
        return t;
    })(a.TextureData);
    a.PixiTextureData = r;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (b) {
    var t = (function (e) {
        __extends(t, e);
        function t() {
            var t = (e !== null && e.apply(this, arguments)) || this;
            t.debugDraw = false;
            t._debugDraw = false;
            t._armature = null;
            t._debugDrawer = null;
            return t;
        }
        t.prototype.dbInit = function (t) {
            this._armature = t;
        };
        t.prototype.dbClear = function () {
            if (this._debugDrawer !== null) {
                this._debugDrawer.destroy({ children: true, texture: true, baseTexture: true });
            }
            this._armature = null;
            this._debugDrawer = null;
            e.prototype.destroy.call(this);
        };
        t.prototype.dbUpdate = function () {
            var t = b.DragonBones.debugDraw || this.debugDraw;
            if (t || this._debugDraw) {
                this._debugDraw = t;
                if (this._debugDraw) {
                    if (this._debugDrawer === null) {
                        this._debugDrawer = new PIXI.Sprite(PIXI.Texture.EMPTY);
                        var e = new PIXI.Graphics();
                        this._debugDrawer.addChild(e);
                    }
                    this.addChild(this._debugDrawer);
                    var a = this._debugDrawer.getChildAt(0);
                    a.clear();
                    var r = this._armature.getBones();
                    for (var i = 0, n = r.length; i < n; ++i) {
                        var s = r[i];
                        var o = s.boneData.length;
                        var l = s.globalTransformMatrix.tx;
                        var h = s.globalTransformMatrix.ty;
                        var u = l + s.globalTransformMatrix.a * o;
                        var f = h + s.globalTransformMatrix.b * o;
                        a.lineStyle(2, 65535, 0.7);
                        a.moveTo(l, h);
                        a.lineTo(u, f);
                        a.lineStyle(0, 0, 0);
                        a.beginFill(65535, 0.7);
                        a.drawCircle(l, h, 3);
                        a.endFill();
                    }
                    var _ = this._armature.getSlots();
                    for (var i = 0, n = _.length; i < n; ++i) {
                        var m = _[i];
                        var p = m.boundingBoxData;
                        if (p) {
                            var c = this._debugDrawer.getChildByName(m.name);
                            if (!c) {
                                c = new PIXI.Graphics();
                                c.name = m.name;
                                this._debugDrawer.addChild(c);
                            }
                            c.clear();
                            c.lineStyle(2, 16711935, 0.7);
                            switch (p.type) {
                                case 0:
                                    c.drawRect(-p.width * 0.5, -p.height * 0.5, p.width, p.height);
                                    break;
                                case 1:
                                    c.drawEllipse(
                                        -p.width * 0.5,
                                        -p.height * 0.5,
                                        p.width,
                                        p.height
                                    );
                                    break;
                                case 2:
                                    var d = p.vertices;
                                    for (var y = 0, v = d.length; y < v; y += 2) {
                                        var g = d[y];
                                        var D = d[y + 1];
                                        if (y === 0) {
                                            c.moveTo(g, D);
                                        } else {
                                            c.lineTo(g, D);
                                        }
                                    }
                                    c.lineTo(d[0], d[1]);
                                    break;
                                default:
                                    break;
                            }
                            c.endFill();
                            m.updateTransformAndMatrix();
                            m.updateGlobalTransform();
                            var T = m.global;
                            c.setTransform(
                                T.x,
                                T.y,
                                T.scaleX,
                                T.scaleY,
                                T.rotation,
                                T.skew,
                                0,
                                m._pivotX,
                                m._pivotY
                            );
                        } else {
                            var c = this._debugDrawer.getChildByName(m.name);
                            if (c) {
                                this._debugDrawer.removeChild(c);
                            }
                        }
                    }
                } else if (this._debugDrawer !== null && this._debugDrawer.parent === this) {
                    this.removeChild(this._debugDrawer);
                }
            }
        };
        t.prototype.dispose = function (t) {
            if (t === void 0) {
                t = true;
            }
            t;
            if (this._armature !== null) {
                this._armature.dispose();
                this._armature = null;
            }
        };
        t.prototype.destroy = function () {
            this.dispose();
        };
        t.prototype.dispatchDBEvent = function (t, e) {
            this.emit(t, e);
        };
        t.prototype.hasDBEventListener = function (t) {
            return this.listenerCount(t) > 0;
        };
        t.prototype.addDBEventListener = function (t, e, a) {
            this.addListener(t, e, a);
        };
        t.prototype.removeDBEventListener = function (t, e, a) {
            this.removeListener(t, e, a);
        };
        Object.defineProperty(t.prototype, 'armature', {
            get: function () {
                return this._armature;
            },
            enumerable: true,
            configurable: true,
        });
        Object.defineProperty(t.prototype, 'animation', {
            get: function () {
                return this._armature.animation;
            },
            enumerable: true,
            configurable: true,
        });
        return t;
    })(PIXI.Sprite);
    b.PixiArmatureDisplay = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (b) {
    var t = (function (t) {
        __extends(e, t);
        function e() {
            return (t !== null && t.apply(this, arguments)) || this;
        }
        e.toString = function () {
            return '[class dragonBones.PixiSlot]';
        };
        e.prototype._onClear = function () {
            t.prototype._onClear.call(this);
            this._textureScale = 1;
            this._renderDisplay = null;
            this._updateTransform =
                PIXI.VERSION[0] === '3' ? this._updateTransformV3 : this._updateTransformV4;
        };
        e.prototype._initDisplay = function (t, e) {
            t;
            e;
        };
        e.prototype._disposeDisplay = function (t, e) {
            t;
            if (!e) {
                t.destroy();
            }
        };
        e.prototype._onUpdateDisplay = function () {
            this._renderDisplay = this._display ? this._display : this._rawDisplay;
        };
        e.prototype._addDisplay = function () {
            var t = this._armature.display;
            t.addChild(this._renderDisplay);
        };
        e.prototype._replaceDisplay = function (t) {
            var e = this._armature.display;
            var a = t;
            e.addChild(this._renderDisplay);
            e.swapChildren(this._renderDisplay, a);
            e.removeChild(a);
            this._textureScale = 1;
        };
        e.prototype._removeDisplay = function () {
            this._renderDisplay.parent.removeChild(this._renderDisplay);
        };
        e.prototype._updateZOrder = function () {
            var t = this._armature.display;
            var e = t.getChildIndex(this._renderDisplay);
            if (e === this._zOrder) {
                return;
            }
            t.addChildAt(this._renderDisplay, this._zOrder);
        };
        e.prototype._updateVisible = function () {
            this._renderDisplay.visible = this._parent.visible && this._visible;
        };
        e.prototype._updateBlendMode = function () {
            if (this._renderDisplay instanceof PIXI.Sprite) {
                switch (this._blendMode) {
                    case 0:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.NORMAL;
                        break;
                    case 1:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.ADD;
                        break;
                    case 3:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.DARKEN;
                        break;
                    case 4:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.DIFFERENCE;
                        break;
                    case 6:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.HARD_LIGHT;
                        break;
                    case 9:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.LIGHTEN;
                        break;
                    case 10:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.MULTIPLY;
                        break;
                    case 11:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.OVERLAY;
                        break;
                    case 12:
                        this._renderDisplay.blendMode = PIXI.BLEND_MODES.SCREEN;
                        break;
                    default:
                        break;
                }
            }
        };
        e.prototype._updateColor = function () {
            var t = this._colorTransform.alphaMultiplier * this._globalAlpha;
            this._renderDisplay.alpha = t;
            if (
                this._renderDisplay instanceof PIXI.Sprite ||
                this._renderDisplay instanceof PIXI.SimpleMesh
            ) {
                var e =
                    (Math.round(this._colorTransform.redMultiplier * 255) << 16) +
                    (Math.round(this._colorTransform.greenMultiplier * 255) << 8) +
                    Math.round(this._colorTransform.blueMultiplier * 255);
                this._renderDisplay.tint = e;
            }
        };
        e.prototype._updateFrame = function () {
            var t = this._textureData;
            if (this._displayIndex >= 0 && this._display !== null && t !== null) {
                var e = t.parent;
                if (this._armature.replacedTexture !== null) {
                    if (this._armature._replaceTextureAtlasData === null) {
                        e = b.BaseObject.borrowObject(b.PixiTextureAtlasData);
                        e.copyFrom(t.parent);
                        e.renderTexture = this._armature.replacedTexture;
                        this._armature._replaceTextureAtlasData = e;
                    } else {
                        e = this._armature._replaceTextureAtlasData;
                    }
                    t = e.getTexture(t.name);
                }
                var a = t.renderTexture;
                if (a !== null) {
                    if (this._geometryData !== null) {
                        var r = this._geometryData.data;
                        var i = r.intArray;
                        var n = r.floatArray;
                        var s = i[this._geometryData.offset + 0];
                        var o = i[this._geometryData.offset + 1];
                        var l = i[this._geometryData.offset + 2];
                        if (l < 0) {
                            l += 65536;
                        }
                        var h = l + s * 2;
                        var u = this._armature._armatureData.scale;
                        var f = this._renderDisplay;
                        var _ = new Float32Array(s * 2);
                        var m = new Float32Array(s * 2);
                        var p = new Uint16Array(o * 3);
                        for (var c = 0, d = s * 2; c < d; ++c) {
                            _[c] = n[l + c] * u;
                        }
                        for (var c = 0; c < o * 3; ++c) {
                            p[c] = i[this._geometryData.offset + 4 + c];
                        }
                        for (var c = 0, d = s * 2; c < d; c += 2) {
                            var y = n[h + c];
                            var v = n[h + c + 1];
                            if (t.rotated) {
                                m[c] = 1 - v;
                                m[c + 1] = y;
                            } else {
                                m[c] = y;
                                m[c + 1] = v;
                            }
                        }
                        this._textureScale = 1;
                        f.texture = a;
                        f.vertices = _;
                        f.uvBuffer.update(m);
                        f.geometry.addIndex(p);
                        var g = this._geometryData.weight !== null;
                        var D = this._parent._boneData.type !== 0;
                        if (g || D) {
                            this._identityTransform();
                        }
                    } else {
                        this._textureScale = t.parent.scale * this._armature._armatureData.scale;
                        var T = this._renderDisplay;
                        T.texture = a;
                    }
                    this._visibleDirty = true;
                    return;
                }
            }
            if (this._geometryData !== null) {
                var f = this._renderDisplay;
                f.texture = null;
                f.x = 0;
                f.y = 0;
                f.visible = false;
            } else {
                var T = this._renderDisplay;
                T.texture = null;
                T.x = 0;
                T.y = 0;
                T.visible = false;
            }
        };
        e.prototype._updateMesh = function () {
            var t = this._armature._armatureData.scale;
            var e = this._displayFrame.deformVertices;
            var a = this._geometryBones;
            var r = this._geometryData;
            var i = r.weight;
            var n = e.length > 0 && r.inheritDeform;
            var s = this._renderDisplay;
            if (i !== null) {
                var o = r.data;
                var l = o.intArray;
                var h = o.floatArray;
                var u = l[r.offset + 0];
                var f = l[i.offset + 1];
                if (f < 0) {
                    f += 65536;
                }
                for (var _ = 0, m = 0, p = i.offset + 2 + a.length, c = f, d = 0; _ < u; ++_) {
                    var y = l[p++];
                    var v = 0,
                        g = 0;
                    for (var D = 0; D < y; ++D) {
                        var T = l[p++];
                        var b = a[T];
                        if (b !== null) {
                            var A = b.globalTransformMatrix;
                            var P = h[c++];
                            var S = h[c++] * t;
                            var O = h[c++] * t;
                            if (n) {
                                S += e[d++];
                                O += e[d++];
                            }
                            v += (A.a * S + A.c * O + A.tx) * P;
                            g += (A.b * S + A.d * O + A.ty) * P;
                        }
                    }
                    s.vertices[m++] = v;
                    s.vertices[m++] = g;
                }
            } else {
                var x = this._parent._boneData.type !== 0;
                var o = r.data;
                var l = o.intArray;
                var h = o.floatArray;
                var u = l[r.offset + 0];
                var B = l[r.offset + 2];
                if (B < 0) {
                    B += 65536;
                }
                for (var _ = 0, E = u * 2; _ < E; _ += 2) {
                    var I = h[B + _] * t;
                    var M = h[B + _ + 1] * t;
                    if (n) {
                        I += e[_];
                        M += e[_ + 1];
                    }
                    if (x) {
                        var A = this._parent._getGlobalTransformMatrix(I, M);
                        s.vertices[_] = A.a * I + A.c * M + A.tx;
                        s.vertices[_ + 1] = A.b * I + A.d * M + A.ty;
                    } else {
                        s.vertices[_] = I;
                        s.vertices[_ + 1] = M;
                    }
                }
            }
        };
        e.prototype._updateTransform = function () {
            throw new Error();
        };
        e.prototype._updateTransformV3 = function () {
            this.updateGlobalTransform();
            var t = this.global;
            if (
                this._renderDisplay === this._rawDisplay ||
                this._renderDisplay === this._meshDisplay
            ) {
                var e =
                    t.x -
                    (this.globalTransformMatrix.a * this._pivotX +
                        this.globalTransformMatrix.c * this._pivotY);
                var a =
                    t.y -
                    (this.globalTransformMatrix.b * this._pivotX +
                        this.globalTransformMatrix.d * this._pivotY);
                this._renderDisplay.setTransform(
                    e,
                    a,
                    t.scaleX * this._textureScale,
                    t.scaleY * this._textureScale,
                    t.rotation,
                    t.skew,
                    0
                );
            } else {
                this._renderDisplay.position.set(t.x, t.y);
                this._renderDisplay.rotation = t.rotation;
                this._renderDisplay.skew.set(t.skew, 0);
                this._renderDisplay.scale.set(t.scaleX, t.scaleY);
            }
        };
        e.prototype._updateTransformV4 = function () {
            this.updateGlobalTransform();
            var t = this.global;
            if (
                this._renderDisplay === this._rawDisplay ||
                this._renderDisplay === this._meshDisplay
            ) {
                var e =
                    t.x -
                    (this.globalTransformMatrix.a * this._pivotX +
                        this.globalTransformMatrix.c * this._pivotY);
                var a =
                    t.y -
                    (this.globalTransformMatrix.b * this._pivotX +
                        this.globalTransformMatrix.d * this._pivotY);
                this._renderDisplay.setTransform(
                    e,
                    a,
                    t.scaleX * this._textureScale,
                    t.scaleY * this._textureScale,
                    t.rotation,
                    -t.skew,
                    0
                );
            } else {
                this._renderDisplay.position.set(t.x, t.y);
                this._renderDisplay.rotation = t.rotation;
                this._renderDisplay.skew.set(-t.skew, 0);
                this._renderDisplay.scale.set(t.scaleX, t.scaleY);
            }
        };
        e.prototype._identityTransform = function () {
            this._renderDisplay.setTransform(0, 0, 1, 1, 0, 0, 0);
        };
        return e;
    })(b.Slot);
    b.PixiSlot = t;
})(dragonBones || (dragonBones = {}));
var dragonBones;
(function (s) {
    var t = (function (i) {
        __extends(n, i);
        function n(t, e) {
            if (t === void 0) {
                t = null;
            }
            if (e === void 0) {
                e = true;
            }
            var a = i.call(this, t) || this;
            if (n._dragonBonesInstance === null) {
                var r = new s.PixiArmatureDisplay(PIXI.Texture.EMPTY);
                n._dragonBonesInstance = new s.DragonBones(r);
                if (e) {
                    PIXI.Ticker.shared.add(n._clockHandler, n);
                }
            }
            a._dragonBones = n._dragonBonesInstance;
            return a;
        }
        n._clockHandler = function (t) {
            this._dragonBonesInstance.advanceTime((t / PIXI.settings.TARGET_FPMS) * 0.001);
        };
        n.advanceTime = function (t) {
            this._dragonBonesInstance.advanceTime(t);
        };
        Object.defineProperty(n, 'factory', {
            get: function () {
                if (n._factory === null) {
                    n._factory = new n(null, n.useSharedTicker);
                }
                return n._factory;
            },
            enumerable: true,
            configurable: true,
        });
        n.newInstance = function (t) {
            if (t === void 0) {
                t = true;
            }
            if (n._factory === null) {
                n._factory = new n(null, t);
            }
            return n._factory;
        };
        n.prototype._buildTextureAtlasData = function (t, e) {
            if (t) {
                t.renderTexture = e;
            } else {
                t = s.BaseObject.borrowObject(s.PixiTextureAtlasData);
            }
            return t;
        };
        n.prototype._buildArmature = function (t) {
            var e = s.BaseObject.borrowObject(s.Armature);
            var a = new s.PixiArmatureDisplay(PIXI.Texture.EMPTY);
            e.init(t.armature, a, a, this._dragonBones);
            return e;
        };
        n.prototype._buildSlot = function (t, e, a) {
            var r = s.BaseObject.borrowObject(s.PixiSlot);
            r.init(e, a, new PIXI.Sprite(PIXI.Texture.EMPTY), new PIXI.SimpleMesh());
            return r;
        };
        n.prototype.buildArmatureDisplay = function (t, e, a, r) {
            if (e === void 0) {
                e = '';
            }
            if (a === void 0) {
                a = '';
            }
            if (r === void 0) {
                r = '';
            }
            var i = this.buildArmature(t, e || '', a || '', r || '');
            if (i !== null) {
                this._dragonBones.clock.add(i);
                return i.display;
            }
            return null;
        };
        n.prototype.getTextureDisplay = function (t, e) {
            if (e === void 0) {
                e = null;
            }
            var a = this._getTextureData(e !== null ? e : '', t);
            if (a !== null && a.renderTexture !== null) {
                return new PIXI.Sprite(a.renderTexture);
            }
            return null;
        };
        Object.defineProperty(n.prototype, 'soundEventManager', {
            get: function () {
                return this._dragonBones.eventManager;
            },
            enumerable: true,
            configurable: true,
        });
        n._dragonBonesInstance = null;
        n._factory = null;
        n.useSharedTicker = true;
        return n;
    })(s.BaseFactory);
    s.PixiFactory = t;
})(dragonBones || (dragonBones = {}));
