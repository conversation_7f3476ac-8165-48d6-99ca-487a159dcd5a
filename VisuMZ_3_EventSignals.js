//=============================================================================
// VisuStella MZ - Event Signals
// VisuMZ_3_EventSignals.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_EventSignals = true;

var VisuMZ = VisuMZ || {};
VisuMZ.EventSignals = VisuMZ.EventSignals || {};
VisuMZ.EventSignals.version = 1.01;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.01] [EventSignals]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Event_Signals_VisuStella_MZ
 * @base VisuMZ_1_EventsMoveCore
 * @orderAfter VisuMZ_1_EventsMoveCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Event Signals is a new way for events to trigger actions, either to or from
 * one another. This will cause events to respond in certain ways based on the
 * type of signals they receive provided that they have a response set up for
 * those specific signals.
 *
 * Features include all (but not limited to) the following:
 *
 * * Emit signals from specific locations (ie the player's location) using a
 *   Plugin Command.
 * * Any nearby events or events within the designated range can respond to
 *   them if they have the associated notetags.
 * * Responding events will stop whatever it is that they're doing and perform
 *   the signal page's event commands as a parallel.
 * * Optionally play an animation at the emitting tile location.
 * * Once the signal page's event commands are finished, events will resume
 *   regular behavior.
 * * This signal response setup allows for a more organic way for events to
 *   respond to various scenarios in-game.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_1_EventsMoveCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * How Do Event Signals Work?
 * ============================================================================
 *
 * This section explains how Event Signals Work.
 *
 * ---
 *
 * === Signal Emitters ===
 *
 * Signal emitters are where it all begins. A signal is nothing more than a
 * piece of "text" that is sent to nearby events or events within the
 * designated range.
 *
 * To emit a signal, use either a Plugin Command or a Script Call. The Plugin
 * Commands and Script Calls will determine the range and center of where the
 * signal will be emitted from.
 *
 * ---
 *
 * === Signal Responses ===
 *
 * Only events can respond to signals. In order to respond to a signal, they
 * need to have a signal response comment tag applied to one of their pages.
 *
 *   <Responds to Signal: text>
 *
 * If this comment tag exists on a page, this page can NEVER be the active page
 * on its own unless an emitted signal causes it to trigger. When it does
 * trigger, the signal response page will take over as the active page, perform
 * its event command actions as a parallel from start to finish, and then the
 * event will resume its normal event behavior.
 *
 * This means that the event will need a valid event page OTHER than the signal
 * response page. Otherwise, the event will be invisible until the signal
 * response conditions are met, appear temporarily, and the return back to
 * being invisible.
 *
 * Keep in mind that even if an event is the one launching the Plugin Command
 * to emit a signal, it can respond to its own signal, too. However, you can
 * bypass this by utilizing the Event variant of the plugin command and setting
 * the designated event as its own exception.
 *
 * ---
 *
 * === Different Signal Responses ===
 *
 * If you want an event to be able to respond to different signals, make them
 * separate pages for the event. For example:
 *
 *   <Responds to Signal: Bomb>
 *
 *   <Responds to Signal: Fire>
 *
 * These should be on different pages. Therefore, if the event receives a
 * nearby "Bomb" signal, then the "Bomb" page will become the active event page
 * until it is done. If a "Fire" signal is sent, then the "Fire" page will be
 * active and performing and not the "Bomb" page.
 *
 * ---
 *
 * === Signal Immunity ===
 *
 * Sometimes, you don't want an event to respond to specific signals all the
 * time. This can be achieved through this comment tag:
 *
 *   <Immune to Signal: text>
 *
 * This immunity will only last while the current event page is the active page
 * and not a random page.
 *
 * Also, if an event is already responding to a signal, then it cannot respond
 * to any other signals until the event signal response page is finished. From
 * there, it will only respond to signals it receives after finishing.
 *
 * ---
 *
 * === Response Switch ===
 *
 * If you are emitting a signal through a Plugin Command, you can set up a
 * "Response Switch ID" to turn ON/OFF a Switch if there are any events that
 * have responded to the signals.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Signal-Related Notetags ===
 *
 * ---
 *
 * <Responds to Signal: text>
 * <Responds to Signals: text, text, text>
 *
 * - Used for: Event Comment Tags
 * - Must be used in an event's page comments and NOT its note box.
 * - Causes this specific event page to be a response to receiving 'text'
 *   signal(s) as long as its other page conditions are met.
 * - This page becomes the active page and the event commands will be ran as
 *   a parallel from start to finish.
 * - Once the events finished, the event returns to its normal behavior.
 * - Replace 'text' with the signal being emitted.
 *   - Insert multiple 'text' entries for this page to respond to multiple
 *     signals being emitted.
 *
 * ---
 *
 * <Immune to Signal: text>
 * <Immune to Signals: text, text, text>
 *
 * - Used for: Event Comment Tags
 * - Must be used in an event's page comments and NOT its note box.
 * - While this page is the active event page, it will not respond to 'text'
 *   signal even if the other signal respond conditions are met.
 * - Replace 'text' with the signal that this active event page will ignore.
 *   - Insert multiple 'text' entires for this active event page to ignore as
 *     signal emissions.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Emit Signal Plugin Commands ===
 *
 * ---
 *
 * Emit Signal: From Coordinate
 * - Emit Signal(s) from target coordinate as the central location.
 * - Must be used on the map scene!
 *
 *   Coordinate X:
 *   - The x coordinate for the signal emmission center.
 *   - You may use JavaScript code.
 *
 *   Coordinate Y:
 *   - The y coordinate for the signal emmission center.
 *   - You may use JavaScript code.
 *
 *   Signal(s):
 *   - What signal do you wish to emit to the surroundings?
 *
 *   Range Type:
 *   - What is the range type to emit the signal to?
 *     - Square: A square-shaped range with the event at the center.
 *     - Circle: A circle-shaped range with the event at the center.
 *     - Delta: A diamond-shaped range with the event at the center.
 *     - Row: Spans horizontally across the map. 'x' expands up and down.
 *     - Column: Spans vertically across the map. 'x' expands left and right.
 *     - Exact: Ignores range distance. Picks only coordinates.
 *
 *     Range Distance:
 *     - What is the range distance to emit the signal to?
 *
 *   Response Switch ID:
 *   - Turns ON this Switch if an event responds to the signal.
 *   - Use 0 to ignore.
 *
 *   Animation ID:
 *   - Play this animation at target tile location.
 *   - Leave at 0 to not play an animation.
 *
 *     Mirror Animation?:
 *     - Mirror the animation?
 *
 *     Mute Animation?:
 *     - Mute the animation?
 *
 * ---
 *
 * Emit Signal: From Event
 * - Emit Signal(s) from target event as the central location.
 * - Must be used on the map scene!
 *
 *   Event ID:
 *   - The ID of the target event.  Use 0 for current event.
 *   - You may use JavaScript code.
 *
 *   Signal(s):
 *   - What signal do you wish to emit to the surroundings?
 *
 *   Range Type:
 *   - What is the range type to emit the signal to?
 *     - Square: A square-shaped range with the event at the center.
 *     - Circle: A circle-shaped range with the event at the center.
 *     - Delta: A diamond-shaped range with the event at the center.
 *     - Row: Spans horizontally across the map. 'x' expands up and down.
 *     - Column: Spans vertically across the map. 'x' expands left and right.
 *     - Exact: Ignores range distance. Picks only coordinates.
 *
 *     Range Distance:
 *     - What is the range distance to emit the signal to?
 *
 *   Response Switch ID:
 *   - Turns ON this Switch if an event responds to the signal.
 *   - Use 0 to ignore.
 *
 *   Animation ID:
 *   - Play this animation at target tile location.
 *   - Leave at 0 to not play an animation.
 *
 *     Mirror Animation?:
 *     - Mirror the animation?
 *
 *     Mute Animation?:
 *     - Mute the animation?
 *
 * ---
 *
 * Emit Signal: From Player
 * - Emit Signal(s) from player as the central location.
 * - Must be used on the map scene!
 *
 *   Signal(s):
 *   - What signal do you wish to emit to the surroundings?
 *
 *   Range Type:
 *   - What is the range type to emit the signal to?
 *     - Square: A square-shaped range with the event at the center.
 *     - Circle: A circle-shaped range with the event at the center.
 *     - Delta: A diamond-shaped range with the event at the center.
 *     - Row: Spans horizontally across the map. 'x' expands up and down.
 *     - Column: Spans vertically across the map. 'x' expands left and right.
 *     - Exact: Ignores range distance. Picks only coordinates.
 *
 *     Range Distance:
 *     - What is the range distance to emit the signal to?
 *
 *   Response Switch ID:
 *   - Turns ON this Switch if an event responds to the signal.
 *   - Use 0 to ignore.
 *
 *   Animation ID:
 *   - Play this animation at target tile location.
 *   - Leave at 0 to not play an animation.
 *
 *     Mirror Animation?:
 *     - Mirror the animation?
 *
 *     Mute Animation?:
 *     - Mute the animation?
 *
 * ---
 *
 * ============================================================================
 * Script Calls
 * ============================================================================
 *
 * The following are Script Calls that can be used with this plugin. These are
 * made for JavaScript proficient users. We are not responsible if you use them
 * incorrectly or for unintended usage.
 *
 * ---
 *
 * === Type-Related Script Calls ===
 *
 * ---
 *
 * $emitSignalAtSquare(signal, centerX, centerY, range, exceptions)
 * $emitSignalAtCircle(signal, centerX, centerY, range, exceptions)
 * $emitSignalAtDelta(signal, centerX, centerY, range, exceptions)
 * $emitSignalAtRow(signal, centerX, centerY, range, exceptions)
 * $emitSignalAtColumn(signal, centerX, centerY, range, exceptions)
 *
 * - Emits a 'signal' in a certain shape of a square with the center coordinate
 *   'centerX' and 'centerY' with a 'range' distance.
 * - Replace 'signal' with a string indicating the signal.
 * - Replace 'centerX' with a number representing the X map coordinate.
 * - Replace 'centerY' with a number representing the Y map coordinate.
 * - Replace 'range' to determine the range of the area to emit the signal.
 * - OPTIONAL ARG. Replace 'exceptions' with an array including the ID's of the
 *   events to not send signals to. 0 does NOT work here for "this event"
 *   unlike the Plugin Command. Be sure to include the target ID manually.
 *
 *   Example:
 *
 *   $emitSignalAtSquare('panic', 5, 8, 3)
 *   $emitSignalAtCircle('aggro', $gamePlayer.x, $gamePlayer.y, 5, [2])
 *   $emitSignalAtDelta('toggle', $gameMap.event(1).x, $gameMap.event(1).y, 1)
 *   $emitSignalAtRow('beam', 5, 4, 2, [$gameVariables.value(8)])
 *   $emitSignalAtColumn('charm', 8, 6, 4, [1, 2, 3])
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Irina
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.01: February 15, 2024
 * * Bug Fixes!
 * ** Fixed a bug with clashing common events that would cause a crash after
 *    leaving the main menu. Fix made by Arisu.
 *
 * Version 1.00 Official Release Date: January 24, 2024
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command EmitSignalFromCoordiniate
 * @text Emit Signal: From Coordinate
 * @desc Emit Signal(s) from target coordinate as the central location.
 * Must be used on the map scene!
 *
 * @arg CoordinateX:eval
 * @text Coordinate X
 * @desc The x coordinate for the signal emmission center.
 * You may use JavaScript code.
 * @default $gamePlayer.x
 *
 * @arg CoordinateY:eval
 * @text Coordinate Y
 * @desc The y coordinate for the signal emmission center.
 * You may use JavaScript code.
 * @default $gamePlayer.y
 *
 * @arg Signals:arraystr
 * @text Signal(s)
 * @type string[]
 * @desc What signal do you wish to emit to the surroundings?
 * @default ["signal"]
 *
 * @arg Exceptions:arraynum
 * @text Event Exceptions
 * @parent Signals:arraystr
 * @type number[]
 * @desc Insert the ID's of the events you want to NOT be able
 * to receive this signal. Use 0 for this event.
 * @default []
 *
 * @arg Type:str
 * @text Range Type
 * @type select
 * @option square
 * @option circle
 * @option delta
 * @option row
 * @option column
 * @option exact
 * @desc What is the range type to emit the signal to?
 * @default circle
 *
 * @arg RangeDist:eval
 * @text Range Distance
 * @parent Type:str
 * @desc What is the range distance to emit the signal to?
 * @default 5
 *
 * @arg ResponseSwitch:num
 * @text Response Switch ID
 * @type switch
 * @desc Turns ON this Switch if an event responds to the signal.
 * Use 0 to ignore.
 * @default 0
 *
 * @arg AnimationID:num
 * @text Animation ID
 * @type animation
 * @desc Play this animation at target tile location.
 * Leave at 0 to not play an animation.
 * @default 2
 *
 * @arg Mirror:eval
 * @text Mirror Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the animation?
 * @default false
 *
 * @arg Mute:eval
 * @text Mute Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the animation?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command EmitSignalFromEvent
 * @text Emit Signal: From Event
 * @desc Emit Signal(s) from target event as the central location.
 * Must be used on the map scene!
 *
 * @arg EventId:eval
 * @text Event ID
 * @parent MapId:eval
 * @desc The ID of the target event.  Use 0 for current event.
 * You may use JavaScript code.
 * @default 0
 *
 * @arg Signals:arraystr
 * @text Signal(s)
 * @type string[]
 * @desc What signal do you wish to emit to the surroundings?
 * @default ["signal"]
 *
 * @arg Exceptions:arraynum
 * @text Event Exceptions
 * @parent Signals:arraystr
 * @type number[]
 * @desc Insert the ID's of the events you want to NOT be able
 * to receive this signal. Use 0 for this event.
 * @default []
 *
 * @arg Type:str
 * @text Range Type
 * @type select
 * @option square
 * @option circle
 * @option delta
 * @option row
 * @option column
 * @option exact
 * @desc What is the range type to emit the signal to?
 * @default circle
 *
 * @arg RangeDist:eval
 * @text Range Distance
 * @parent Type:str
 * @desc What is the range distance to emit the signal to?
 * @default 5
 *
 * @arg ResponseSwitch:num
 * @text Response Switch ID
 * @type switch
 * @desc Turns ON this Switch if an event responds to the signal.
 * Use 0 to ignore.
 * @default 0
 *
 * @arg AnimationID:num
 * @text Animation ID
 * @type animation
 * @desc Play this animation at target tile location.
 * Leave at 0 to not play an animation.
 * @default 2
 *
 * @arg Mirror:eval
 * @text Mirror Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the animation?
 * @default false
 *
 * @arg Mute:eval
 * @text Mute Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the animation?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command EmitSignalFromPlayer
 * @text Emit Signal: From Player
 * @desc Emit Signal(s) from player as the central location.
 * Must be used on the map scene!
 *
 * @arg Signals:arraystr
 * @text Signal(s)
 * @type string[]
 * @desc What signal do you wish to emit to the surroundings?
 * @default ["signal"]
 *
 * @arg Exceptions:arraynum
 * @text Event Exceptions
 * @parent Signals:arraystr
 * @type number[]
 * @desc Insert the ID's of the events you want to NOT be able
 * to receive this signal. Use 0 for this event.
 * @default []
 *
 * @arg Type:str
 * @text Range Type
 * @type select
 * @option square
 * @option circle
 * @option delta
 * @option row
 * @option column
 * @option exact
 * @desc What is the range type to emit the signal to?
 * @default circle
 *
 * @arg RangeDist:eval
 * @text Range Distance
 * @parent Type:str
 * @desc What is the range distance to emit the signal to?
 * @default 5
 *
 * @arg ResponseSwitch:num
 * @text Response Switch ID
 * @type switch
 * @desc Turns ON this Switch if an event responds to the signal.
 * Use 0 to ignore.
 * @default 0
 *
 * @arg AnimationID:num
 * @text Animation ID
 * @type animation
 * @desc Play this animation at target tile location.
 * Leave at 0 to not play an animation.
 * @default 2
 *
 * @arg Mirror:eval
 * @text Mirror Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the animation?
 * @default false
 *
 * @arg Mute:eval
 * @text Mute Animation?
 * @parent AnimationID:num
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the animation?
 * @default false
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================

function _0x4fd1(_0x570f77, _0x3a7c60) {
    const _0xf65e70 = _0xf65e();
    return (
        (_0x4fd1 = function (_0x4fd1e2, _0x26e673) {
            _0x4fd1e2 = _0x4fd1e2 - 0x144;
            let _0x5600ad = _0xf65e70[_0x4fd1e2];
            return _0x5600ad;
        }),
        _0x4fd1(_0x570f77, _0x3a7c60)
    );
}
const _0x536cf1 = _0x4fd1;
(function (_0x556b4a, _0x236951) {
    const _0x4e8930 = _0x4fd1,
        _0x41b1c5 = _0x556b4a();
    while (!![]) {
        try {
            const _0x21fd6b =
                (parseInt(_0x4e8930(0x154)) / 0x1) * (parseInt(_0x4e8930(0x1cc)) / 0x2) +
                (-parseInt(_0x4e8930(0x1a6)) / 0x3) * (-parseInt(_0x4e8930(0x1f0)) / 0x4) +
                (-parseInt(_0x4e8930(0x1e2)) / 0x5) * (parseInt(_0x4e8930(0x192)) / 0x6) +
                (-parseInt(_0x4e8930(0x166)) / 0x7) * (parseInt(_0x4e8930(0x183)) / 0x8) +
                (parseInt(_0x4e8930(0x15e)) / 0x9) * (parseInt(_0x4e8930(0x1a9)) / 0xa) +
                -parseInt(_0x4e8930(0x1ce)) / 0xb +
                parseInt(_0x4e8930(0x1de)) / 0xc;
            if (_0x21fd6b === _0x236951) break;
            else _0x41b1c5['push'](_0x41b1c5['shift']());
        } catch (_0x2426d1) {
            _0x41b1c5['push'](_0x41b1c5['shift']());
        }
    }
})(_0xf65e, 0xe222b);
var label = _0x536cf1(0x1bf),
    tier = tier || 0x0,
    dependencies = [_0x536cf1(0x1ec)],
    pluginData = $plugins[_0x536cf1(0x156)](function (_0x4bce4b) {
        const _0x11732d = _0x536cf1;
        return (
            _0x4bce4b[_0x11732d(0x16e)] &&
            _0x4bce4b['description'][_0x11732d(0x1a3)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label][_0x536cf1(0x1d0)] = VisuMZ[label][_0x536cf1(0x1d0)] || {}),
    (VisuMZ[_0x536cf1(0x1a4)] = function (_0x75f7c, _0x3ab8b5) {
        const _0x14965f = _0x536cf1;
        for (const _0x2737a2 in _0x3ab8b5) {
            if (_0x14965f(0x19a) === 'ONuSY')
                ((this[_0x14965f(0x185)] = _0x195184),
                    (this['_signalPages'] = _0xf1b154),
                    (this[_0x14965f(0x16f)] = _0x171cd4),
                    (this[_0x14965f(0x15f)] = _0x3d9cfd));
            else {
                if (_0x2737a2[_0x14965f(0x189)](/(.*):(.*)/i)) {
                    const _0x17a88d = String(RegExp['$1']),
                        _0x1ee9fd = String(RegExp['$2'])[_0x14965f(0x1da)]()[_0x14965f(0x198)]();
                    let _0x455da9, _0x1a430a, _0x3d3d0a;
                    switch (_0x1ee9fd) {
                        case _0x14965f(0x181):
                            _0x455da9 =
                                _0x3ab8b5[_0x2737a2] !== '' ? Number(_0x3ab8b5[_0x2737a2]) : 0x0;
                            break;
                        case _0x14965f(0x1f7):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a['map'](_0x1dbcc9 => Number(_0x1dbcc9))));
                            break;
                        case _0x14965f(0x176):
                            _0x455da9 =
                                _0x3ab8b5[_0x2737a2] !== '' ? eval(_0x3ab8b5[_0x2737a2]) : null;
                            break;
                        case _0x14965f(0x18b):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a[_0x14965f(0x14e)](_0x598a88 =>
                                    eval(_0x598a88)
                                )));
                            break;
                        case _0x14965f(0x1c6):
                            _0x455da9 =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : '';
                            break;
                        case _0x14965f(0x1e9):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a[_0x14965f(0x14e)](_0x2c382f =>
                                    JSON[_0x14965f(0x175)](_0x2c382f)
                                )));
                            break;
                        case 'FUNC':
                            _0x455da9 =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? new Function(JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2]))
                                    : new Function(_0x14965f(0x147));
                            break;
                        case _0x14965f(0x19f):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON['parse'](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a[_0x14965f(0x14e)](
                                    _0x2675b0 => new Function(JSON[_0x14965f(0x175)](_0x2675b0))
                                )));
                            break;
                        case _0x14965f(0x1e5):
                            _0x455da9 =
                                _0x3ab8b5[_0x2737a2] !== '' ? String(_0x3ab8b5[_0x2737a2]) : '';
                            break;
                        case _0x14965f(0x1e8):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a[_0x14965f(0x14e)](_0x1b1211 =>
                                    String(_0x1b1211)
                                )));
                            break;
                        case 'STRUCT':
                            ((_0x3d3d0a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON['parse'](_0x3ab8b5[_0x2737a2])
                                    : {}),
                                (_0x455da9 = VisuMZ['ConvertParams']({}, _0x3d3d0a)));
                            break;
                        case _0x14965f(0x1d2):
                            ((_0x1a430a =
                                _0x3ab8b5[_0x2737a2] !== ''
                                    ? JSON[_0x14965f(0x175)](_0x3ab8b5[_0x2737a2])
                                    : []),
                                (_0x455da9 = _0x1a430a[_0x14965f(0x14e)](_0xc76348 =>
                                    VisuMZ['ConvertParams']({}, JSON['parse'](_0xc76348))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x75f7c[_0x17a88d] = _0x455da9;
                }
            }
        }
        return _0x75f7c;
    }),
    (_0x3578d7 => {
        const _0x1cbb11 = _0x536cf1,
            _0x176ecb = _0x3578d7[_0x1cbb11(0x196)];
        for (const _0x5be3ae of dependencies) {
            if (_0x1cbb11(0x1b4) === _0x1cbb11(0x165)) {
                const _0x33fbc7 = _0x5be088(_0x4d77cb['$1']);
                _0x33fbc7 !== _0x256051[_0x2d161a][_0x1cbb11(0x193)] &&
                    (_0xb99fa(
                        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                            _0x1cbb11(0x1ac)
                        ](_0x279c34, _0x33fbc7)
                    ),
                    _0x4f6822[_0x1cbb11(0x160)]());
            } else {
                if (!Imported[_0x5be3ae]) {
                    (alert(_0x1cbb11(0x159)[_0x1cbb11(0x1ac)](_0x176ecb, _0x5be3ae)),
                        SceneManager['exit']());
                    break;
                }
            }
        }
        const _0xf3fda0 = _0x3578d7[_0x1cbb11(0x1d9)];
        if (_0xf3fda0['match'](/\[Version[ ](.*?)\]/i)) {
            const _0x5c3114 = Number(RegExp['$1']);
            _0x5c3114 !== VisuMZ[label][_0x1cbb11(0x193)] &&
                (alert(
                    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                        _0x1cbb11(0x1ac)
                    ](_0x176ecb, _0x5c3114)
                ),
                SceneManager[_0x1cbb11(0x160)]());
        }
        if (_0xf3fda0['match'](/\[Tier[ ](\d+)\]/i)) {
            const _0x60222f = Number(RegExp['$1']);
            _0x60222f < tier
                ? (alert(_0x1cbb11(0x1a8)[_0x1cbb11(0x1ac)](_0x176ecb, _0x60222f, tier)),
                  SceneManager[_0x1cbb11(0x160)]())
                : _0x1cbb11(0x14c) !== _0x1cbb11(0x14c)
                  ? (_0x3f460c = !![])
                  : (tier = Math[_0x1cbb11(0x1c7)](_0x60222f, tier));
        }
        VisuMZ[_0x1cbb11(0x1a4)](VisuMZ[label][_0x1cbb11(0x1d0)], _0x3578d7[_0x1cbb11(0x149)]);
    })(pluginData));
if (VisuMZ[_0x536cf1(0x162)][_0x536cf1(0x193)] < 1.5) {
    let text = '';
    ((text += _0x536cf1(0x1f1)),
        (text += _0x536cf1(0x16c)),
        alert(text),
        SceneManager[_0x536cf1(0x160)]());
}
(PluginManager[_0x536cf1(0x1f3)](
    pluginData[_0x536cf1(0x196)],
    'EmitSignalFromCoordiniate',
    _0x5cfa0b => {
        const _0x448178 = _0x536cf1;
        if (!SceneManager['isInstanceOfSceneMap']()) return;
        VisuMZ[_0x448178(0x1a4)](_0x5cfa0b, _0x5cfa0b);
        const _0x1ecbd3 = _0x5cfa0b[_0x448178(0x182)] || 0x0,
            _0x513de0 = _0x5cfa0b[_0x448178(0x168)] || 0x0,
            _0x1f4d63 = _0x5cfa0b['Signals'] || [],
            _0x39233c = _0x5cfa0b[_0x448178(0x1db)] || '',
            _0x523953 = _0x5cfa0b[_0x448178(0x16b)] || 0x0,
            _0x427501 = _0x5cfa0b['ResponseSwitch'] || 0x0,
            _0x3201d5 = _0x5cfa0b[_0x448178(0x16d)] || 0x0,
            _0x506a9e = $gameTemp['getLastPluginCommandInterpreter']();
        let _0x1645b5 = (_0x5cfa0b[_0x448178(0x1e3)] || [])['map'](_0x309fa => Number(_0x309fa));
        if (_0x506a9e) {
            if (_0x448178(0x1cb) === _0x448178(0x19b)) {
                if ([0x6c, 0x198][_0x448178(0x1a3)](_0x18a4d9['code'])) {
                    if (_0x34bbb1 !== '') _0x34822d += '\x0a';
                    _0x5c7adf += _0x25f7f5['parameters'][0x0];
                }
            } else {
                const _0x25f3e9 = _0x506a9e[_0x448178(0x1bb)]() || 0x0;
                _0x1645b5 = _0x1645b5['map'](_0xc203cc =>
                    _0xc203cc === 0x0 ? _0x25f3e9 : _0xc203cc
                );
            }
        }
        const _0x540eac = $gameMap[_0x448178(0x152)](
            _0x1f4d63,
            _0x39233c,
            _0x1ecbd3,
            _0x513de0,
            _0x523953,
            _0x1645b5
        );
        (_0x427501 > 0x0 && $gameSwitches['setValue'](_0x427501, _0x540eac),
            _0x3201d5 > 0x0 &&
                $gameTemp['requestEventSignalAnimation'](
                    null,
                    _0x1ecbd3,
                    _0x513de0,
                    _0x3201d5,
                    _0x5cfa0b[_0x448178(0x1ca)],
                    _0x5cfa0b[_0x448178(0x14a)]
                ));
    }
),
    PluginManager[_0x536cf1(0x1f3)](pluginData[_0x536cf1(0x196)], _0x536cf1(0x1e4), _0x2f21d1 => {
        const _0x2e01d7 = _0x536cf1;
        if (!SceneManager['isInstanceOfSceneMap']()) return;
        VisuMZ[_0x2e01d7(0x1a4)](_0x2f21d1, _0x2f21d1);
        const _0x12f067 = $gameTemp['getLastPluginCommandInterpreter'](),
            _0x119568 = _0x2f21d1[_0x2e01d7(0x1c0)] || _0x12f067[_0x2e01d7(0x1bb)](),
            _0x25b73a = _0x2f21d1['Signals'] || [],
            _0xca3a09 = _0x2f21d1[_0x2e01d7(0x1db)] || '',
            _0x1e6f37 = _0x2f21d1[_0x2e01d7(0x16b)] || 0x0,
            _0x3da477 = _0x2f21d1['ResponseSwitch'] || 0x0,
            _0x1240be = _0x2f21d1['AnimationID'] || 0x0;
        let _0x4b262d = (_0x2f21d1[_0x2e01d7(0x1e3)] || [])[_0x2e01d7(0x14e)](_0xc54681 =>
            Number(_0xc54681)
        );
        if (_0x12f067) {
            const _0x3f6cea = _0x12f067[_0x2e01d7(0x1bb)]() || 0x0;
            _0x4b262d = _0x4b262d[_0x2e01d7(0x14e)](_0x4ddd8c =>
                _0x4ddd8c === 0x0 ? _0x3f6cea : _0x4ddd8c
            );
        }
        const _0x37710f = $gameMap['event'](_0x119568),
            _0xf838c3 = _0x37710f['x'],
            _0x25a0ad = _0x37710f['y'],
            _0x46de86 = $gameMap[_0x2e01d7(0x152)](
                _0x25b73a,
                _0xca3a09,
                _0xf838c3,
                _0x25a0ad,
                _0x1e6f37,
                _0x4b262d
            );
        (_0x3da477 > 0x0 && $gameSwitches['setValue'](_0x3da477, _0x46de86),
            _0x1240be > 0x0 &&
                $gameTemp['requestEventSignalAnimation'](
                    _0x37710f,
                    _0xf838c3,
                    _0x25a0ad,
                    _0x1240be,
                    _0x2f21d1[_0x2e01d7(0x1ca)],
                    _0x2f21d1[_0x2e01d7(0x14a)]
                ));
    }),
    PluginManager['registerCommand'](pluginData[_0x536cf1(0x196)], _0x536cf1(0x17e), _0x592b4a => {
        const _0x1dab24 = _0x536cf1;
        if (!SceneManager[_0x1dab24(0x1c5)]()) return;
        VisuMZ[_0x1dab24(0x1a4)](_0x592b4a, _0x592b4a);
        const _0xf69ec7 = _0x592b4a[_0x1dab24(0x1cf)] || [],
            _0x3aa8df = _0x592b4a['Type'] || '',
            _0x523b11 = _0x592b4a['RangeDist'] || 0x0,
            _0xe339e0 = _0x592b4a[_0x1dab24(0x1a0)] || 0x0,
            _0xb075af = _0x592b4a[_0x1dab24(0x16d)] || 0x0,
            _0x2317ec = $gameTemp['getLastPluginCommandInterpreter']();
        let _0xa7cee = (_0x592b4a[_0x1dab24(0x1e3)] || [])[_0x1dab24(0x14e)](_0xcaff24 =>
            Number(_0xcaff24)
        );
        if (_0x2317ec) {
            const _0x483649 = _0x2317ec['eventId']() || 0x0;
            _0xa7cee = _0xa7cee[_0x1dab24(0x14e)](_0x2e7260 =>
                _0x2e7260 === 0x0 ? _0x483649 : _0x2e7260
            );
        }
        const _0x31033b = $gamePlayer['x'],
            _0x239721 = $gamePlayer['y'],
            _0x584b1d = $gameMap[_0x1dab24(0x152)](
                _0xf69ec7,
                _0x3aa8df,
                _0x31033b,
                _0x239721,
                _0x523b11,
                _0xa7cee
            );
        (_0xe339e0 > 0x0 && $gameSwitches['setValue'](_0xe339e0, _0x584b1d),
            _0xb075af > 0x0 &&
                $gameTemp['requestEventSignalAnimation'](
                    $gamePlayer,
                    _0x31033b,
                    _0x239721,
                    _0xb075af,
                    _0x592b4a['Mirror'],
                    _0x592b4a['Mute']
                ));
    }),
    (VisuMZ['EventSignals'][_0x536cf1(0x1f2)] = {
        ImmuneToSignal: /<IMMUNE TO SIGNAL(?:|S):[ ](.*?)>/i,
        RespondsToSignal: /<RESPONDS TO SIGNAL(?:|S):[ ](.*?)>/i,
    }),
    (SceneManager[_0x536cf1(0x19c)] = function () {
        const _0x336e71 = _0x536cf1;
        return this[_0x336e71(0x144)] && this[_0x336e71(0x144)]['constructor'] === Scene_Map;
    }),
    (SceneManager[_0x536cf1(0x1c5)] = function () {
        const _0x1c7046 = _0x536cf1;
        return this['_scene'] && this[_0x1c7046(0x144)] instanceof Scene_Map;
    }),
    (Game_Temp[_0x536cf1(0x161)][_0x536cf1(0x18c)] = function (
        _0x1a369d,
        _0x10d0a6,
        _0x86255e,
        _0x5ec9db,
        _0x59be5,
        _0x3ba5eb
    ) {
        const _0x35d961 = _0x536cf1;
        let _0x432ed6 = 0x0;
        if (_0x1a369d) _0x432ed6 = _0x1a369d[_0x35d961(0x1a2)]();
        else {
            const _0x15ee65 = $gameMap['tileWidth']();
            _0x432ed6 = Math[_0x35d961(0x1ad)](
                $gameMap[_0x35d961(0x1b6)](_0x10d0a6) * _0x15ee65 + _0x15ee65 / 0x2
            );
        }
        let _0x52e13b = 0x0;
        if (_0x1a369d) {
            if (_0x35d961(0x151) === 'OfGNB') {
                if (_0x3649c3 !== '') _0x7591f7 += '\x0a';
                _0x13c119 += _0x391c09['parameters'][0x0];
            } else _0x52e13b = _0x1a369d[_0x35d961(0x1ab)]();
        } else {
            if (_0x35d961(0x19d) === 'AXWDj') _0x1d3105 = _0x28062e['max'](_0x2bfa8b, _0x174206);
            else {
                const _0xc072e7 = $gameMap['tileHeight']();
                _0x52e13b = Math['floor']($gameMap['adjustY'](_0x86255e) * _0xc072e7 + _0xc072e7);
            }
        }
        ((_0x52e13b -= Math[_0x35d961(0x1ef)]($gameMap[_0x35d961(0x1a5)]() / 0x2)),
            this[_0x35d961(0x1be)](_0x432ed6, _0x52e13b, _0x5ec9db, _0x59be5, _0x3ba5eb));
    }),
    (Game_Map[_0x536cf1(0x161)][_0x536cf1(0x152)] = function (
        _0x56ba22,
        _0x420989,
        _0x316ceb,
        _0x22abbe,
        _0x1c26da,
        _0x332151
    ) {
        const _0x43d53c = _0x536cf1;
        _0x332151 = _0x332151 || [];
        const _0x1dbe58 = this[_0x43d53c(0x18a)](_0x420989, _0x316ceb, _0x22abbe, _0x1c26da),
            _0x7b2470 = this[_0x43d53c(0x15d)](_0x1dbe58);
        let _0x14777a = ![];
        for (const _0x34d65d of _0x7b2470) {
            if ('zqKHq' !== _0x43d53c(0x14d)) {
                let _0x5de12b = '';
                ((_0x5de12b += 'VisuMZ_1_EventsMoveCore\x20needs\x20to\x20be\x20updated\x20'),
                    (_0x5de12b += _0x43d53c(0x16c)),
                    _0xe09c11(_0x5de12b),
                    _0x3d1386[_0x43d53c(0x160)]());
            } else {
                if (_0x332151['includes'](_0x34d65d[_0x43d53c(0x1bb)]())) {
                    if (_0x43d53c(0x1d5) === 'vkFwd') _0x38b139 = _0x50acf0[_0x43d53c(0x1ab)]();
                    else continue;
                }
                if (_0x34d65d[_0x43d53c(0x180)](_0x56ba22)) {
                    if (_0x43d53c(0x17c) === _0x43d53c(0x1c3)) {
                        const _0x405def = this['_signalResponses'][_0x2fc777],
                            _0x10b7cb = this['event']()[_0x43d53c(0x1b1)][_0x405def];
                        if (
                            _0x10b7cb &&
                            _0x2e7b3b['EventSignals'][_0x43d53c(0x1df)][_0x43d53c(0x1c4)](
                                this,
                                _0x10b7cb
                            )
                        )
                            return _0x405def;
                    } else _0x14777a = !![];
                }
            }
        }
        return _0x14777a;
    }),
    (Game_Map['prototype'][_0x536cf1(0x18a)] = function (
        _0x1d1806,
        _0x101810,
        _0x2452c2,
        _0x550edc
    ) {
        const _0x406153 = _0x536cf1;
        let _0x32854b = [];
        switch (_0x1d1806) {
            case 'square':
                _0x32854b = _0x32854b['concat'](
                    this[_0x406153(0x178)](_0x101810, _0x2452c2, _0x550edc)
                );
                break;
            case 'circle':
                _0x32854b = _0x32854b[_0x406153(0x167)](
                    this['getCircleCoordinatesFromXy'](_0x101810, _0x2452c2, _0x550edc)
                );
                break;
            case 'radius':
            case _0x406153(0x157):
                _0x32854b = _0x32854b[_0x406153(0x167)](
                    this['getDeltaCoordinatesFromXy'](_0x101810, _0x2452c2, _0x550edc)
                );
                break;
            case _0x406153(0x172):
                _0x32854b = _0x32854b[_0x406153(0x167)](
                    this[_0x406153(0x190)](_0x101810, _0x2452c2, _0x550edc)
                );
                break;
            case 'column':
                _0x32854b = _0x32854b[_0x406153(0x167)](
                    this[_0x406153(0x186)](_0x101810, _0x2452c2, _0x550edc)
                );
                break;
            default:
                _0x32854b[_0x406153(0x17f)]({ x: _0x101810, y: _0x2452c2 });
                break;
        }
        return ((_0x32854b = VisuMZ[_0x406153(0x1bf)][_0x406153(0x1a1)](_0x32854b)), _0x32854b);
    }),
    (Game_Map[_0x536cf1(0x161)][_0x536cf1(0x178)] = function (_0xecaf4a, _0x2ad929, _0x5bc46a) {
        const _0x6acedc = _0x536cf1;
        let _0x106ba5 = [];
        for (let _0x316014 = -_0x5bc46a; _0x316014 <= _0x5bc46a; _0x316014++) {
            if (_0x6acedc(0x1b3) === _0x6acedc(0x1b0)) {
                if (!this['event']()) return;
                (this[_0x6acedc(0x1c1)](), this[_0x6acedc(0x1bc)]());
            } else
                for (let _0x29f602 = -_0x5bc46a; _0x29f602 <= _0x5bc46a; _0x29f602++) {
                    if (_0x6acedc(0x150) === _0x6acedc(0x150))
                        _0x106ba5[_0x6acedc(0x17f)]({
                            x: _0xecaf4a + _0x316014,
                            y: _0x2ad929 + _0x29f602,
                        });
                    else {
                        const _0x41cc81 = [_0xda6926 || ''],
                            _0x5a0a05 = _0x6acedc(0x157);
                        return (
                            (_0x4d8937 = _0x5816a7 || 0x0),
                            (_0x4e34e8 = _0x5eb59e || 0x0),
                            (_0x401389 = _0x3e904b || 0x0),
                            (_0x36e826 = _0x4673b5 || []),
                            _0x589e84[_0x6acedc(0x152)](
                                _0x41cc81,
                                _0x5a0a05,
                                _0x5d212c,
                                _0xc46540,
                                _0x393162,
                                _0x34f0af
                            )
                        );
                    }
                }
        }
        return _0x106ba5;
    }),
    (Game_Map['prototype'][_0x536cf1(0x155)] = function (_0x1b7d3e, _0x84ecb5, _0x422fb7) {
        const _0x128c4 = _0x536cf1;
        let _0x4dcb19 = [];
        for (let _0x3461c3 = -_0x422fb7; _0x3461c3 <= _0x422fb7; _0x3461c3++) {
            for (let _0x132d6d = -_0x422fb7; _0x132d6d <= _0x422fb7; _0x132d6d++) {
                const _0xe4bc92 = Math[_0x128c4(0x1e6)](_0x1b7d3e - (_0x1b7d3e + _0x3461c3), 0x2),
                    _0x4ab0bc = Math[_0x128c4(0x1e6)](_0x84ecb5 - (_0x84ecb5 + _0x132d6d), 0x2),
                    _0x508a75 = Math[_0x128c4(0x1ef)](Math['sqrt'](_0xe4bc92 + _0x4ab0bc));
                if (_0x508a75 > _0x422fb7) continue;
                _0x4dcb19['push']({ x: _0x1b7d3e + _0x3461c3, y: _0x84ecb5 + _0x132d6d });
            }
        }
        return _0x4dcb19;
    }),
    (Game_Map[_0x536cf1(0x161)]['getDeltaCoordinatesFromXy'] = function (
        _0xde81c3,
        _0x494416,
        _0x296852
    ) {
        const _0x58047f = _0x536cf1;
        let _0x3ac87e = [];
        for (let _0x4f5f70 = -_0x296852; _0x4f5f70 <= _0x296852; _0x4f5f70++) {
            if (_0x58047f(0x1ee) !== _0x58047f(0x1ee))
                (this['clearSignalResponses'](),
                    _0x1170c1[_0x58047f(0x1bf)][_0x58047f(0x1d7)][_0x58047f(0x1c4)](
                        this,
                        _0x423c8b
                    ));
            else
                for (let _0xb2ca55 = -_0x296852; _0xb2ca55 <= _0x296852; _0xb2ca55++) {
                    if (Math['abs'](_0x4f5f70) + Math[_0x58047f(0x1a7)](_0xb2ca55) > _0x296852)
                        continue;
                    _0x3ac87e[_0x58047f(0x17f)]({
                        x: _0xde81c3 + _0x4f5f70,
                        y: _0x494416 + _0xb2ca55,
                    });
                }
        }
        return _0x3ac87e;
    }),
    (Game_Map[_0x536cf1(0x161)][_0x536cf1(0x190)] = function (_0x35087a, _0x3a9a50, _0x1c4a0f) {
        const _0x18b370 = _0x536cf1;
        let _0x1cdc5f = [];
        const _0x38b020 = $gameMap[_0x18b370(0x153)]();
        for (let _0xaa687 = 0x0; _0xaa687 < _0x38b020; _0xaa687++) {
            if ('bswYA' === _0x18b370(0x1b8))
                for (let _0x5f0853 = -_0x4666fe; _0x5f0853 <= _0x4d01c; _0x5f0853++) {
                    _0x52176e[_0x18b370(0x17f)]({
                        x: _0x2a5c0e + _0xec858d,
                        y: _0x3916a7 + _0x5f0853,
                    });
                }
            else
                for (let _0x252a24 = -_0x1c4a0f; _0x252a24 <= _0x1c4a0f; _0x252a24++) {
                    _0x1cdc5f[_0x18b370(0x17f)]({ x: _0xaa687, y: _0x3a9a50 + _0x252a24 });
                }
        }
        return _0x1cdc5f;
    }),
    (Game_Map['prototype'][_0x536cf1(0x186)] = function (_0x31fd58, _0x3073fb, _0x333cc2) {
        const _0x4e52b6 = _0x536cf1;
        let _0x3487b4 = [];
        const _0x2c60cf = $gameMap[_0x4e52b6(0x15a)]();
        for (let _0x4f48b7 = -_0x333cc2; _0x4f48b7 <= _0x333cc2; _0x4f48b7++) {
            if ('kOLMI' !== _0x4e52b6(0x1bd))
                for (let _0x5119ac = 0x0; _0x5119ac < _0x2c60cf; _0x5119ac++) {
                    _0x3487b4[_0x4e52b6(0x17f)]({ x: _0x31fd58 + _0x4f48b7, y: _0x5119ac });
                }
            else {
                if (!_0x1e3aec) return ![];
                if (this[_0x4e52b6(0x164)](_0x2cb413)) return ![];
                return _0x29b793[_0x4e52b6(0x1bf)][_0x4e52b6(0x1df)][_0x4e52b6(0x1c4)](
                    this,
                    _0x167182
                );
            }
        }
        return _0x3487b4;
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x1a1)] = function (_0x515f62) {
        const _0x223c94 = _0x536cf1,
            _0xc2eb94 = $gameMap[_0x223c94(0x1d6)](),
            _0x391408 = $gameMap['isLoopVertical'](),
            _0x2414b5 = $gameMap[_0x223c94(0x153)](),
            _0x5e5cbb = $gameMap[_0x223c94(0x15a)]();
        for (const _0x2f480f of _0x515f62) {
            if (_0x223c94(0x15b) === 'CLNKQ') {
                const _0x4416a8 = [_0x50df81 || ''];
                ((_0x504738 = _0x1f4698 || 0x0), (_0x1317e5 = _0x53aa82 || 0x0));
                const _0x4aeadf = 'square';
                return (
                    (_0x4af898 = _0x28123a || 0x0),
                    (_0x55009e = _0x234229 || []),
                    _0x52091d[_0x223c94(0x152)](
                        _0x4416a8,
                        _0x4aeadf,
                        _0xeaa559,
                        _0x4e3e48,
                        _0x1b63f9,
                        _0x5173a9
                    )
                );
            } else {
                if (_0xc2eb94) _0x2f480f['x'] = _0x2f480f['x'] % _0x2414b5;
                if (_0x391408) _0x2f480f['y'] = _0x2f480f['y'] % _0x5e5cbb;
            }
        }
        return _0x515f62;
    }),
    (Game_Map['prototype'][_0x536cf1(0x15d)] = function (_0x2150bc) {
        const _0x491757 = _0x536cf1;
        let _0x1ce638 = [];
        for (const _0x5a16db of _0x2150bc) {
            if (_0x491757(0x191) === 'sZBvC') {
                if (this[_0x491757(0x15f)] !== _0x3df15c) return this[_0x491757(0x15f)];
                return _0x212276[_0x491757(0x1bf)]['Game_Event_findProperPageIndex'][
                    _0x491757(0x1c4)
                ](this);
            } else
                _0x1ce638 = _0x1ce638['concat'](
                    this[_0x491757(0x18e)](_0x5a16db['x'], _0x5a16db['y'])
                );
        }
        return (
            (_0x1ce638 = _0x1ce638[_0x491757(0x156)](
                (_0x34cb43, _0x4c6af9, _0x1faab4) => _0x1faab4['indexOf'](_0x34cb43) === _0x4c6af9
            )),
            (_0x1ce638 = _0x1ce638['filter'](_0x8fb41e => !_0x8fb41e[_0x491757(0x1e7)])),
            _0x1ce638
        );
    }),
    (VisuMZ[_0x536cf1(0x1bf)]['Game_Event_clearPageSettings'] =
        Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1b9)]),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1b9)] = function () {
        const _0x29bf0f = _0x536cf1;
        (VisuMZ[_0x29bf0f(0x1bf)][_0x29bf0f(0x1d8)]['call'](this),
            this['initEventSignalsEffects']());
    }),
    (VisuMZ['EventSignals'][_0x536cf1(0x1d4)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x146)]),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x146)] = function () {
        const _0x43145b = _0x536cf1;
        (VisuMZ[_0x43145b(0x1bf)]['Game_Event_setupPageSettings'][_0x43145b(0x1c4)](this),
            this['setupEventSignalsEffects']());
    }),
    (Game_Event['prototype']['setupEventSignalsEffects'] = function () {
        const _0x467d6c = _0x536cf1;
        if (!this[_0x467d6c(0x15c)]()) return;
        (this['initEventSignalsEffects'](), this[_0x467d6c(0x1bc)]());
    }),
    (Game_Event['prototype'][_0x536cf1(0x1bc)] = function () {
        const _0x2b938e = _0x536cf1;
        if (!this[_0x2b938e(0x1d1)]()) return;
        const _0x13543a = this['list']();
        let _0x51d8f6 = '';
        for (const _0x6f5dc of _0x13543a) {
            if (_0x2b938e(0x1ba) === _0x2b938e(0x1dd)) {
                const _0x5e81fc = _0x1d0d95[_0x2b938e(0x1a5)]();
                _0x29720f = _0x7d32e9[_0x2b938e(0x1ad)](
                    _0x42a0fe[_0x2b938e(0x174)](_0x135e2f) * _0x5e81fc + _0x5e81fc
                );
            } else {
                if ([0x6c, 0x198]['includes'](_0x6f5dc[_0x2b938e(0x18d)])) {
                    if (_0x51d8f6 !== '') _0x51d8f6 += '\x0a';
                    _0x51d8f6 += _0x6f5dc[_0x2b938e(0x149)][0x0];
                }
            }
        }
        this[_0x2b938e(0x195)](_0x51d8f6);
    }),
    (Game_Event['prototype'][_0x536cf1(0x1c1)] = function () {
        const _0x1efed8 = _0x536cf1;
        this[_0x1efed8(0x17a)] = [];
    }),
    (Game_Event[_0x536cf1(0x161)]['checkEventSignalsStringTags'] = function (_0x4d398d) {
        const _0x2b1ae1 = _0x536cf1,
            _0xb50f3e = VisuMZ[_0x2b1ae1(0x1bf)][_0x2b1ae1(0x1f2)];
        if (_0x4d398d[_0x2b1ae1(0x189)](_0xb50f3e['ImmuneToSignal'])) {
            if (_0x2b1ae1(0x170) === _0x2b1ae1(0x1ea)) {
                const _0x5a52fe = _0xbb7a0d[_0x2b1ae1(0x1bb)]() || 0x0;
                _0x51a96d = _0x348a36[_0x2b1ae1(0x14e)](_0x20bd98 =>
                    _0x20bd98 === 0x0 ? _0x5a52fe : _0x20bd98
                );
            } else {
                const _0x4b5ffb = String(RegExp['$1'])
                    [_0x2b1ae1(0x1e1)](',')
                    [_0x2b1ae1(0x14e)](_0x361633 => _0x361633[_0x2b1ae1(0x1eb)]()['trim']());
                for (const _0xe87da8 of _0x4b5ffb) {
                    this[_0x2b1ae1(0x17a)][_0x2b1ae1(0x17f)](
                        _0xe87da8[_0x2b1ae1(0x1eb)]()[_0x2b1ae1(0x198)]()
                    );
                }
            }
        }
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x163)] = function () {
        const _0x5b3e69 = _0x536cf1;
        ((this[_0x5b3e69(0x185)] = undefined),
            (this[_0x5b3e69(0x145)] = undefined),
            (this['_signalParallelOnce'] = undefined),
            (this[_0x5b3e69(0x15f)] = undefined));
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1c8)] = function () {
        const _0x1314ab = _0x536cf1;
        ((this[_0x1314ab(0x185)] = {}), (this[_0x1314ab(0x145)] = []));
        const _0x407d65 = this[_0x1314ab(0x15c)]()[_0x1314ab(0x1b1)],
            _0x1142d7 = _0x407d65[_0x1314ab(0x1aa)],
            _0x4d93f1 = VisuMZ['EventSignals'][_0x1314ab(0x1f2)];
        for (let _0x21a4b4 = 0x0; _0x21a4b4 < _0x1142d7; _0x21a4b4++) {
            const _0x2990ef = _0x407d65[_0x21a4b4];
            if (!_0x2990ef) continue;
            const _0x20d4f1 = _0x2990ef[_0x1314ab(0x1b2)];
            for (const _0x1d0bae of _0x20d4f1) {
                if (_0x1314ab(0x188) === _0x1314ab(0x148))
                    (this[_0x1314ab(0x163)](),
                        _0x1b8dfb[_0x1314ab(0x1bf)][_0x1314ab(0x17d)]['call'](
                            this,
                            _0x590f13,
                            _0x210166
                        ));
                else {
                    if (![0x6c, 0x198][_0x1314ab(0x1a3)](_0x1d0bae[_0x1314ab(0x18d)])) continue;
                    const _0x2ad961 = _0x1d0bae[_0x1314ab(0x149)][0x0] || '';
                    if (!_0x2ad961[_0x1314ab(0x189)](_0x4d93f1['RespondsToSignal'])) continue;
                    if (!this[_0x1314ab(0x145)][_0x1314ab(0x1a3)](_0x21a4b4))
                        this[_0x1314ab(0x145)][_0x1314ab(0x17f)](_0x21a4b4);
                    _0x2990ef['trigger'] = 0x4;
                    const _0x5ba680 = String(RegExp['$1'])
                        [_0x1314ab(0x1e1)](',')
                        [_0x1314ab(0x14e)](_0x1c7b43 =>
                            _0x1c7b43[_0x1314ab(0x1eb)]()[_0x1314ab(0x198)]()
                        );
                    for (const _0x4e1710 of _0x5ba680) {
                        if (this[_0x1314ab(0x185)][_0x4e1710] !== undefined) continue;
                        this[_0x1314ab(0x185)][_0x4e1710] = _0x21a4b4;
                    }
                }
            }
        }
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x18f)] = function (_0x8df394) {
        const _0x1b165b = _0x536cf1;
        if (this[_0x1b165b(0x17a)] === undefined) this[_0x1b165b(0x1ae)]();
        return this[_0x1b165b(0x17a)]['includes'](_0x8df394);
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x1df)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1e0)]),
    (Game_Event['prototype'][_0x536cf1(0x1e0)] = function (_0x2edf3b) {
        const _0x2b5173 = _0x536cf1;
        if (!_0x2edf3b) return ![];
        if (this['hasSignalResponseCommentTag'](_0x2edf3b)) return ![];
        return VisuMZ[_0x2b5173(0x1bf)]['Game_Event_meetsConditions'][_0x2b5173(0x1c4)](
            this,
            _0x2edf3b
        );
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x164)] = function (_0x39b3bf) {
        const _0x3ed365 = _0x536cf1;
        if (this[_0x3ed365(0x185)] === undefined) this['checkSignalResponses']();
        const _0x34df4d = this[_0x3ed365(0x15c)]()['pages'][_0x3ed365(0x1dc)](_0x39b3bf);
        return this['_signalPages'][_0x3ed365(0x1a3)](_0x34df4d);
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x180)] = function (_0x52f2b7) {
        const _0x3a2399 = _0x536cf1;
        if (this[_0x3a2399(0x15f)] !== undefined) return ![];
        if (this[_0x3a2399(0x185)] === undefined) this[_0x3a2399(0x1c8)]();
        const _0x21948c = this[_0x3a2399(0x14f)](_0x52f2b7);
        if (_0x21948c < 0x0) return ![];
        return (this['processSignalResponse'](_0x21948c), !![]);
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x14f)] = function (_0x36fccf) {
        const _0x1c7e1e = _0x536cf1;
        for (const _0x5cd48f of _0x36fccf) {
            if (_0x1c7e1e(0x1f5) !== _0x1c7e1e(0x1f5)) {
                let _0x408673 = [];
                for (const _0x547372 of _0x3ca88a) {
                    _0x408673 = _0x408673[_0x1c7e1e(0x167)](
                        this[_0x1c7e1e(0x18e)](_0x547372['x'], _0x547372['y'])
                    );
                }
                return (
                    (_0x408673 = _0x408673[_0x1c7e1e(0x156)](
                        (_0x5003f5, _0x5d8deb, _0x27d3fc) =>
                            _0x27d3fc[_0x1c7e1e(0x1dc)](_0x5003f5) === _0x5d8deb
                    )),
                    (_0x408673 = _0x408673[_0x1c7e1e(0x156)](
                        _0xc0ea9c => !_0xc0ea9c[_0x1c7e1e(0x1e7)]
                    )),
                    _0x408673
                );
            } else {
                const _0x4b5011 = _0x5cd48f[_0x1c7e1e(0x1eb)]()[_0x1c7e1e(0x198)]();
                if (this[_0x1c7e1e(0x18f)](_0x4b5011)) continue;
                if (this[_0x1c7e1e(0x185)][_0x4b5011] !== undefined) {
                    const _0x3bd019 = this[_0x1c7e1e(0x185)][_0x4b5011],
                        _0x2b810a = this[_0x1c7e1e(0x15c)]()[_0x1c7e1e(0x1b1)][_0x3bd019];
                    if (
                        _0x2b810a &&
                        VisuMZ['EventSignals'][_0x1c7e1e(0x1df)][_0x1c7e1e(0x1c4)](this, _0x2b810a)
                    )
                        return _0x3bd019;
                }
            }
        }
        return -0x1;
    }),
    (Game_Event[_0x536cf1(0x161)]['processSignalResponse'] = function (_0x39d5be) {
        const _0x1f891f = _0x536cf1;
        ((this[_0x1f891f(0x15f)] = _0x39d5be), this[_0x1f891f(0x177)]());
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x199)] = Game_Event['prototype']['findProperPageIndex']),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1af)] = function () {
        const _0x42b0d2 = _0x536cf1;
        if (this[_0x42b0d2(0x15f)] !== undefined) return this[_0x42b0d2(0x15f)];
        return VisuMZ[_0x42b0d2(0x1bf)][_0x42b0d2(0x199)][_0x42b0d2(0x1c4)](this);
    }),
    (VisuMZ[_0x536cf1(0x1bf)]['Game_Event_updateParallel'] =
        Game_Event[_0x536cf1(0x161)][_0x536cf1(0x19e)]),
    (Game_Event[_0x536cf1(0x161)]['updateParallel'] = function () {
        const _0x517686 = _0x536cf1;
        if (this['_signalPageIndex'] !== undefined)
            _0x517686(0x14b) === _0x517686(0x14b)
                ? this[_0x517686(0x1b7)]()
                : ((this['_signalPageIndex'] = _0x1479a9), this[_0x517686(0x177)]());
        else {
            if (_0x517686(0x1ed) === _0x517686(0x1c9))
                return this['_scene'] && this[_0x517686(0x144)][_0x517686(0x1f6)] === _0x31fac0;
            else VisuMZ[_0x517686(0x1bf)]['Game_Event_updateParallel'][_0x517686(0x1c4)](this);
        }
    }),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1b7)] = function () {
        const _0x2fcfc6 = _0x536cf1;
        if (!this[_0x2fcfc6(0x169)]) {
            this[_0x2fcfc6(0x169)] = new Game_Interpreter();
            return;
        }
        if (!this[_0x2fcfc6(0x169)]['isRunning']()) {
            if (_0x2fcfc6(0x16a) === _0x2fcfc6(0x197))
                (_0x4e168c(_0x2fcfc6(0x1a8)[_0x2fcfc6(0x1ac)](_0x363abe, _0x1b5ddf, _0xc0963)),
                    _0x5110ce[_0x2fcfc6(0x160)]());
            else {
                if (this[_0x2fcfc6(0x16f)]) {
                    if (_0x2fcfc6(0x1c2) === 'yGPKc') {
                        ((this[_0x2fcfc6(0x16f)] = undefined),
                            (this['_signalPageIndex'] = undefined),
                            this[_0x2fcfc6(0x177)]());
                        return;
                    } else {
                        if (!this['page']()) return [];
                        return _0x50bd61[_0x2fcfc6(0x1bf)][_0x2fcfc6(0x171)][_0x2fcfc6(0x1c4)](
                            this
                        );
                    }
                } else
                    ((this[_0x2fcfc6(0x16f)] = !![]),
                        this['_interpreter'][_0x2fcfc6(0x179)](
                            this[_0x2fcfc6(0x1b2)](),
                            this[_0x2fcfc6(0x17b)]
                        ));
            }
        }
        this[_0x2fcfc6(0x169)][_0x2fcfc6(0x1cd)]();
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x171)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1b2)]),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x1b2)] = function () {
        const _0x55de04 = _0x536cf1;
        if (!this[_0x55de04(0x1d1)]()) return [];
        return VisuMZ['EventSignals']['Game_Event_list'][_0x55de04(0x1c4)](this);
    }),
    (VisuMZ['EventSignals'][_0x536cf1(0x187)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x173)]),
    (Game_Event[_0x536cf1(0x161)][_0x536cf1(0x173)] = function () {
        const _0x2e5fa3 = _0x536cf1;
        (this[_0x2e5fa3(0x163)](),
            VisuMZ[_0x2e5fa3(0x1bf)][_0x2e5fa3(0x187)][_0x2e5fa3(0x1c4)](this));
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x1b5)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x158)]),
    (Game_Event[_0x536cf1(0x161)]['morphInto'] = function (_0x11ffff, _0x10209e, _0x29a507) {
        const _0x45ebb0 = _0x536cf1;
        (this[_0x45ebb0(0x163)](),
            VisuMZ[_0x45ebb0(0x1bf)][_0x45ebb0(0x1b5)]['call'](
                this,
                _0x11ffff,
                _0x10209e,
                _0x29a507
            ));
    }),
    (VisuMZ[_0x536cf1(0x1bf)][_0x536cf1(0x17d)] = Game_Event['prototype'][_0x536cf1(0x1f4)]),
    (Game_Event[_0x536cf1(0x161)]['morphIntoEventSignals'] = function (_0x30fb31, _0x4855c3) {
        const _0x426268 = _0x536cf1;
        (this[_0x426268(0x163)](),
            VisuMZ[_0x426268(0x1bf)]['Game_Event_morphIntoEventSignals'][_0x426268(0x1c4)](
                this,
                _0x30fb31,
                _0x4855c3
            ));
    }),
    (VisuMZ['EventSignals'][_0x536cf1(0x1d7)] = Game_Event[_0x536cf1(0x161)][_0x536cf1(0x184)]),
    (Game_Event[_0x536cf1(0x161)]['setupSpawn'] = function (_0x166493) {
        const _0x216a90 = _0x536cf1;
        (this[_0x216a90(0x163)](),
            VisuMZ['EventSignals'][_0x216a90(0x1d7)][_0x216a90(0x1c4)](this, _0x166493));
    }));
var $emitSignalAtSquare = function (_0x3f8a26, _0x5a213e, _0x2a8c84, _0x7a8b86, _0x45496e) {
        const _0x5b1e93 = _0x536cf1,
            _0xc551b7 = [_0x3f8a26 || ''];
        ((_0x5a213e = _0x5a213e || 0x0), (_0x2a8c84 = _0x2a8c84 || 0x0));
        const _0x368424 = _0x5b1e93(0x1d3);
        return (
            (_0x7a8b86 = _0x7a8b86 || 0x0),
            (_0x45496e = _0x45496e || []),
            $gameMap[_0x5b1e93(0x152)](
                _0xc551b7,
                _0x368424,
                _0x5a213e,
                _0x2a8c84,
                _0x7a8b86,
                _0x45496e
            )
        );
    },
    $emitSignalAtCircle = function (_0x18db3f, _0x2975ce, _0x11890a, _0x135e30, _0x41f332) {
        const _0x4cc8c8 = _0x536cf1,
            _0xcb191b = [_0x18db3f || ''],
            _0x196c24 = 'circle';
        return (
            (_0x2975ce = _0x2975ce || 0x0),
            (_0x11890a = _0x11890a || 0x0),
            (_0x135e30 = _0x135e30 || 0x0),
            (_0x41f332 = _0x41f332 || []),
            $gameMap[_0x4cc8c8(0x152)](
                _0xcb191b,
                _0x196c24,
                _0x2975ce,
                _0x11890a,
                _0x135e30,
                _0x41f332
            )
        );
    },
    $emitSignalAtDelta = function (_0x22eb96, _0x1667c0, _0x471669, _0x276c13, _0x31d99f) {
        const _0xac5a51 = _0x536cf1,
            _0x18b755 = [_0x22eb96 || ''],
            _0x56829f = 'delta';
        return (
            (_0x1667c0 = _0x1667c0 || 0x0),
            (_0x471669 = _0x471669 || 0x0),
            (_0x276c13 = _0x276c13 || 0x0),
            (_0x31d99f = _0x31d99f || []),
            $gameMap[_0xac5a51(0x152)](
                _0x18b755,
                _0x56829f,
                _0x1667c0,
                _0x471669,
                _0x276c13,
                _0x31d99f
            )
        );
    },
    $emitSignalAtRow = function (_0x46603b, _0x1098d3, _0x52e927, _0x5e25b6, _0x2a9b4f) {
        const _0x52d8a = _0x536cf1,
            _0x4d97a2 = [_0x46603b || ''],
            _0x1ce7a4 = _0x52d8a(0x172);
        return (
            (_0x1098d3 = _0x1098d3 || 0x0),
            (_0x52e927 = _0x52e927 || 0x0),
            (_0x5e25b6 = _0x5e25b6 || 0x0),
            (_0x2a9b4f = _0x2a9b4f || []),
            $gameMap[_0x52d8a(0x152)](
                _0x4d97a2,
                _0x1ce7a4,
                _0x1098d3,
                _0x52e927,
                _0x5e25b6,
                _0x2a9b4f
            )
        );
    },
    $emitSignalAtColumn = function (_0x53bad5, _0x31aff1, _0x52d8ca, _0x4fcbec, _0x221250) {
        const _0x204197 = _0x536cf1,
            _0x3e5020 = [_0x53bad5 || ''],
            _0xe78896 = _0x204197(0x194);
        return (
            (_0x31aff1 = _0x31aff1 || 0x0),
            (_0x52d8ca = _0x52d8ca || 0x0),
            (_0x4fcbec = _0x4fcbec || 0x0),
            (_0x221250 = _0x221250 || []),
            $gameMap['emitSignalTypeAtXy'](
                _0x3e5020,
                _0xe78896,
                _0x31aff1,
                _0x52d8ca,
                _0x4fcbec,
                _0x221250
            )
        );
    };
function _0xf65e() {
    const _0x5355f3 = [
        'code',
        'eventsXy',
        'isSignalImmune',
        'getRowCoordinatesFromXy',
        'AUvmX',
        '138mAtRMH',
        'version',
        'column',
        'checkEventSignalsStringTags',
        'name',
        'LvJLH',
        'trim',
        'Game_Event_findProperPageIndex',
        'Btcvy',
        'NHkum',
        'isSceneMap',
        'JIZoF',
        'updateParallel',
        'ARRAYFUNC',
        'ResponseSwitch',
        'ConvertLoopMapCoordinates',
        'screenX',
        'includes',
        'ConvertParams',
        'tileHeight',
        '171tHWEYp',
        'abs',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        '10EXOGRM',
        'length',
        'screenY',
        'format',
        'floor',
        'setupEventSignalsEffects',
        'findProperPageIndex',
        'VhSgY',
        'pages',
        'list',
        'TymPb',
        'DTYhC',
        'Game_Event_morphInto',
        'adjustX',
        'updateSignalParallel',
        'HzRHI',
        'clearPageSettings',
        'arDot',
        'eventId',
        'setupEventSignalsCommentTags',
        'LlhLZ',
        'requestPointAnimation',
        'EventSignals',
        'EventId',
        'initEventSignalsEffects',
        'yGPKc',
        'VrAFU',
        'call',
        'isInstanceOfSceneMap',
        'JSON',
        'max',
        'checkSignalResponses',
        'wMLbu',
        'Mirror',
        'rKbKY',
        '18ZucVca',
        'update',
        '13536347lJwlkz',
        'Signals',
        'Settings',
        'page',
        'ARRAYSTRUCT',
        'square',
        'Game_Event_setupPageSettings',
        'DzdYS',
        'isLoopHorizontal',
        'Game_Event_setupSpawn',
        'Game_Event_clearPageSettings',
        'description',
        'toUpperCase',
        'Type',
        'indexOf',
        'VejHZ',
        '21603768IkHoJA',
        'Game_Event_meetsConditions',
        'meetsConditions',
        'split',
        '286915EOWTiZ',
        'Exceptions',
        'EmitSignalFromEvent',
        'STR',
        'pow',
        '_erased',
        'ARRAYSTR',
        'ARRAYJSON',
        'ZGrqd',
        'toLowerCase',
        'VisuMZ_1_EventsMoveCore',
        'pZePT',
        'pTAnu',
        'round',
        '25924mlidBi',
        'VisuMZ_1_EventsMoveCore\x20needs\x20to\x20be\x20updated\x20',
        'RegExp',
        'registerCommand',
        'morphIntoEventSignals',
        'eUgby',
        'constructor',
        'ARRAYNUM',
        '_scene',
        '_signalPages',
        'setupPageSettings',
        'return\x200',
        'AcmYj',
        'parameters',
        'Mute',
        'Ydxfy',
        'puBIG',
        'zqKHq',
        'map',
        'findSignalResponsePageIndex',
        'bEvgN',
        'KPpuH',
        'emitSignalTypeAtXy',
        'width',
        '137076Uapuyg',
        'getCircleCoordinatesFromXy',
        'filter',
        'delta',
        'morphInto',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'height',
        'bkZsE',
        'event',
        'getEventsAtSignalCoordinates',
        '13440393UhLmmh',
        '_signalPageIndex',
        'exit',
        'prototype',
        'EventsMoveCore',
        'clearSignalResponses',
        'hasSignalResponseCommentTag',
        'TmPBm',
        '105qJgwqK',
        'concat',
        'CoordinateY',
        '_interpreter',
        'KvuZz',
        'RangeDist',
        'in\x20order\x20for\x20VisuMZ_3_EventSignals\x20to\x20work.',
        'AnimationID',
        'status',
        '_signalParallelOnce',
        'xsSpX',
        'Game_Event_list',
        'row',
        'setupCopyEvent',
        'adjustY',
        'parse',
        'EVAL',
        'refresh',
        'getSquareCoordinatesFromXy',
        'setup',
        '_immuneToSignals',
        '_eventId',
        'rSZia',
        'Game_Event_morphIntoEventSignals',
        'EmitSignalFromPlayer',
        'push',
        'performSignalResponse',
        'NUM',
        'CoordinateX',
        '757416xjDrAP',
        'setupSpawn',
        '_signalResponses',
        'getColumnCoordinatesFromXy',
        'Game_Event_setupCopyEvent',
        'NChlQ',
        'match',
        'getSignalTypeCoordinatesFromXy',
        'ARRAYEVAL',
        'requestEventSignalAnimation',
    ];
    _0xf65e = function () {
        return _0x5355f3;
    };
    return _0xf65e();
}
