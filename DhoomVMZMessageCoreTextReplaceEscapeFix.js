//=============================================================================
// DhoomVMZMessageCoreTextReplaceEscapeFix.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_VMZMessageCoreReplaceFix = '1.0';

var Dhoom = Dhoom || {};
Dhoom.VMZMessageCoreReplaceFix = Dhoom.VMZMessageCoreReplaceFix || {};
/*:
 * @plugindesc Dhoom VMZMessageCoreReplaceFix v1.0 - 12/08/2024
 * <AUTHOR>
 * @url drd-workshop.blogspot.com
 * @target MZ
 *
 * @help This plugin fixes VisuMZ_MessageCore Text Code Replacement, where
 * when getting an item name that contains escape characters, the characters
 * is not processed correctly.
 */

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom VMZMessageCoreReplaceFix/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.VMZMessageCoreReplaceFix.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.VMZMessageCoreReplaceFix[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.VMZMessageCoreReplaceFix.loadParameters();

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_Base
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
if (Imported.VisuMZ_1_MessageCore) {
    Dhoom.VMZMessageCoreReplaceFix.Window_Base_postConvertEscapeCharacters =
        Window_Base.prototype.postConvertEscapeCharacters;
    Window_Base.prototype.postConvertEscapeCharacters = function (text) {
        text = Dhoom.VMZMessageCoreReplaceFix.Window_Base_postConvertEscapeCharacters.call(
            this,
            text
        );
        return this.convertBackslashCharacters(text);
    };
}
