//=============================================================================
// VisuStella MZ - Skill Mastery
// VisuMZ_3_SkillMastery.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_SkillMastery = true;

var VisuMZ = VisuMZ || {};
VisuMZ.SkillMastery = VisuMZ.SkillMastery || {};
VisuMZ.SkillMastery.version = 1.02;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.02] [SkillMastery]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Skill_Mastery_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_SkillsStatesCore
 * @orderAfter VisuMZ_1_SkillsStatesCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * With this plugin, a Skill Mastery mechanic is put into play where when an
 * actor or enemy uses a skill enough times, it can gain mastery levels. With
 * increased mastery levels, damage/healing can increase, costs can be changed,
 * cooldown turns, state turns, buff turns, and debuff turns can also be
 * adjusted based on mastery levels.
 *
 * Features include all (but not limited to) the following:
 *
 * * Actors and enemies that use skills enough times can raise their mastery
 *   levels to become more powerful.
 * * Mastery effects include damage/healing amplification, cost reduction or
 *   increases, changes to cooldown turns, changes to buff/debuff turns, and/or
 *   changes to state turns.
 * * Customize these changes individually per skill through notetags.
 * * Adjust the amount of Skill Mastery EXP needed through a formula in the
 *   Plugin Parameters.
 * * Setup a variable to automatically record the skill mastery level of the
 *   last used skill in battle or in the skill menu.
 * * Plugin Commands allow you to manually adjust the gain of levels and skill
 *   mastery EXP.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_0_CoreEngine
 * * VisuMZ_1_SkillsStatesCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_3_VisualGaugeStyles
 *
 * If VisuStella MZ's Visual Gauge Styles is also installed, you can change the
 * way the Skill Mastery EXP gauge appears.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Mastery-Related Notetags ===
 *
 * ---
 *
 * <Max Skill Mastery Level: x>
 *
 * - Used for: Skill Notetags
 * - Sets the maximum skill mastery level for this skill to 'x'.
 * - Replace 'x' with a number representing the max skill mastery level.
 * - If this notetag is not used, refer to the default max level found in the
 *   Plugin Parameters.
 *
 * ---
 *
 * <JS Skill Mastery EXP>
 *  code
 *  code
 *  exp = code;
 * </JS Skill Mastery EXP>
 *
 * - Used for: Skill Notetags
 * - Create a custom skill mastery EXP formula for this skill.
 * - The variable 'user' refers to user of the skill.
 * - The variable 'targetLevel' refers to the target level whose EXP is being
 *   calculated for.
 * - The variable 'exp' is returned and determines how much EXP is needed to
 *   achieve the target level.
 *
 * ---
 *
 * <Starting Skill Masteries>
 *  Skill id: level
 *  Skill id: level, exp
 *  name: level
 *  name: level, exp
 * </Starting Skill Masteries>
 *
 * - Used for: Actor, Enemy Notetags
 * - Allows you to adjust the starting skill mastery levels for actors and
 *   enemies. Initialized actors will also reset their mastery levels to these
 *   values.
 * - Replace 'id' with a number presenting the ID of the skill to set the
 *   mastery level for.
 * - Replace 'name' with the name of the skill to set the mastery level for.
 * - Replace 'level' with a number representing the starting mastery level.
 * - Replace 'exp' with a number representing the current mastery level EXP.
 *
 * ---
 *
 * === Mastery Effect-Related Notetags ===
 *
 * ---
 *
 * <Mastery Effect: +x HP Cost Per Level>
 * <Mastery Effect: -x HP Cost Per Level>
 *
 * <Mastery Effect: +x% HP Cost Per Level>
 * <Mastery Effect: -x% HP Cost Per Level>
 *
 * <Mastery Effect: +x MP Cost Per Level>
 * <Mastery Effect: -x MP Cost Per Level>
 *
 * <Mastery Effect: +x% MP Cost Per Level>
 * <Mastery Effect: -x% MP Cost Per Level>
 *
 * <Mastery Effect: +x TP Cost Per Level>
 * <Mastery Effect: -x TP Cost Per Level>
 *
 * <Mastery Effect: +x% TP Cost Per Level>
 * <Mastery Effect: -x% TP Cost Per Level>
 *
 * - Used for: Skill Notetags
 * - Alters the HP, MP, and/or TP costs of the skill per mastery level.
 * - Replace 'x' with a number representing either a flat change or percentile
 *   change in skill cost.
 * - Skill costs cannot be altered unless the base cost is at least above 0.
 * - Depending on the Plugin Parameter settings, skill costs cannot reach 0.
 * - If these notetags are not used, refer to the default settings found in
 *   the Plugin Parameters.
 *
 * ---
 *
 * <Mastery Effect: +x Damage Per Level>
 * <Mastery Effect: -x Damage Per Level>
 *
 * <Mastery Effect: +x% Damage Per Level>
 * <Mastery Effect: -x% Damage Per Level>
 *
 * <Mastery Effect: +x Healing Per Level>
 * <Mastery Effect: -x Healing Per Level>
 *
 * <Mastery Effect: +x% Healing Per Level>
 * <Mastery Effect: -x% Healing Per Level>
 *
 * - Used for: Skill Notetags
 * - Alters the damage/healing of the skill per mastery level.
 * - Replace 'x' with a number representing either a flat change or percentile
 *   change in damage/healing.
 * - If these notetags are not used, refer to the default settings found in
 *   the Plugin Parameters.
 *
 * ---
 *
 * <Mastery Effect: +x Cooldown Turns Per Level>
 * <Mastery Effect: -x Cooldown Turns Per Level>
 *
 * <Mastery Effect: +x Buff Turns Per Level>
 * <Mastery Effect: -x Buff Turns Per Level>
 *
 * <Mastery Effect: +x Debuff Turns Per Level>
 * <Mastery Effect: -x Debuff Turns Per Level>
 *
 * <Mastery Effect: +x State Turns Per Level>
 * <Mastery Effect: -x State Turns Per Level>
 *
 * - Used for: Skill Notetags
 * - Alters the cooldown, buff, debuff, or state turns of the skill per
 *   mastery level.
 * - Replace 'x' with a number representing either the turn change.
 * - Depending on the Plugin Parameters, cooldowns may or may not be able
 *   to reach zero due to this effect.
 * - Buffs, debuffs, and state turns are able to reach zero values because they
 *   can still hold an effect at zero turns.
 * - If these notetags are not used, refer to the default settings found in
 *   the Plugin Parameters.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Actor Plugin Commands ===
 *
 * ---
 *
 * Actor: Gain Skill Mastery / EXP
 * - Target actor(s) gain Skill Mastery and/or EXP.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Skill ID:
 *   - What is the ID of the skill to adjust?
 *
 *     Level:
 *     - Gains this many mastery levels for the skill.
 *
 *     EXP:
 *     - Gains this much exp of the mastery level.
 *
 * ---
 *
 * Actor: Set Skill Mastery / EXP
 * - Sets the Skill Mastery level and EXP for target actor(s).
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Skill ID:
 *   - What is the ID of the skill to adjust?
 *
 *     Level:
 *     - Sets the mastery level of the skill.
 *
 *     EXP:
 *     - Sets the exp of the current mastery level.
 *
 * ---
 *
 * === Enemy Plugin Commands ===
 *
 * ---
 *
 * Enemy: Gain Skill Mastery / EXP
 * - Target enemy(ies) gain Skill Mastery and/or EXP.
 *
 *   Enemy Index(es):
 *   - Select which Enemy Index(es) to affect.
 *   - Index values start at 0.
 *
 *   Skill ID:
 *   - What is the ID of the skill to adjust?
 *
 *     Level:
 *     - Gains this many mastery levels for the skill.
 *
 *     EXP:
 *     - Gains this much exp of the mastery level.
 *
 * ---
 *
 * Enemy: Set Skill Mastery / EXP
 * - Sets the Skill Mastery level and EXP for target enemy(ies).
 *
 *   Enemy Index(es):
 *   - Select which Enemy Index(es) to affect.
 *   - Index values start at 0.
 *
 *   Skill ID:
 *   - What is the ID of the skill to adjust?
 *
 *     Level:
 *     - Sets the mastery level of the skill.
 *
 *     EXP:
 *     - Sets the exp of the current mastery level.
 *
 * ---
 *
 * ============================================================================
 * Script Calls
 * ============================================================================
 *
 * The following are Script Calls that can be used with this plugin. These are
 * made for JavaScript proficient users. We are not responsible if you use them
 * incorrectly or for unintended usage.
 *
 * ---
 *
 * === Data Retrieval-Related Script Calls ===
 *
 * ---
 *
 * $actorSkillMasteryLevel(actorID, skillID)
 * $actorSkillMasteryExp(actorID, skillID)
 *
 * - These will return a numeric value detailing the level/exp of the actor
 *   being specified.
 * - Replace 'actorID' with a number representing the ID of the actor to look
 *   up the skill mastery level or exp of.
 * - Replace 'skillID' with a number representing the ID of the skill to look
 *   up the skill mastery level or exp of.
 * - This will return a number value.
 *
 *   Examples:
 *
 *   $actorSkillMasteryLevel(6, 99)
 *   $actorSkillMasteryExp(7, 52)
 *
 * ---
 *
 * $enemySkillMasteryLevel(enemyIndex, skillID)
 * $enemySkillMasteryExp(enemyIndex, skillID)
 *
 * - These will return a numeric value detailing the level/exp of the enemy
 *   being specified.
 * - Replace 'enemyIndex' with a number representing the index position of the
 *   enemy to look up the skill mastery level or exp of.
 *   - Index values for enemy troops typically range from 0 to 7, with 0 being
 *     the first enemy inserted into a troop.
 * - Replace 'skillID' with a number representing the ID of the skill to look
 *   up the skill mastery level or exp of.
 * - This will return a number value.
 *
 *   Examples:
 *
 *   $enemySkillMasteryLevel(0, 99)
 *   $enemySkillMasteryLevel(2, 52)
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * Adjust the general settings involving this plugin including mechanics and
 * default mastery effect values.
 *
 * ---
 *
 * Auto
 *
 *   Variable: Skill Level:
 *   - Select a variable ID to automatically record the last used skill's
 *     mastery level.
 *   - 0 to not use.
 *
 * ---
 *
 * Defaults
 *
 *   EXP Formula:
 *   - Default formula used to calculate needed EXP.
 *   - Return: exp.
 *   - Variables: user, skill, targetLevel.
 *
 *   Max Level:
 *   - Default max level for skill masteries.
 *
 * ---
 *
 * Defaults > Effects Per Level
 *
 *   HP Cost:
 *   HP% Cost:
 *   MP Cost:
 *   MP% Cost:
 *   TP Cost:
 *   TP% Cost:
 *   Damage/Heal:
 *   Damage/Heal%:
 *   Cooldown Turns:
 *   Buff Turns:
 *   Debuff Turns:
 *   State Turns:
 *   - Default mastery effect bonus per level.
 *
 * ---
 *
 * Prevent EXP Gain
 *
 *   Basic Attack?:
 *   - Prevent EXP gain for basic attacks?
 *
 *   Basic Guard?:
 *   - Prevent EXP gain for basic guarding?
 *
 * ---
 *
 * Prevent Effects
 *
 *   Costs to Zero?:
 *   - Prevent costs from reaching zero?
 *
 *   Cooldowns to Zero?:
 *   - Prevent cooldowns from reaching zero?
 *
 * ---
 *
 * Display
 *
 *   Name Format:
 *   - Change how skill names appear with mastery levels.
 *   - %1 - Skill Name, %2 - Mastery Level.
 *
 *   Max Gauge Width:
 *   - Gauge widths adjust to the window size but there is a maximum gauge
 *     width amount.
 *
 *   Gauge Color 1:
 *   Gauge Color 2:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Gauge Style:
 *   - Select the gauge style to use for skill mastery EXP.
 *   - Requires VisuMZ_3_VisualGaugeStyles!
 *
 * ---
 *
 * Window_ActorCommand
 *
 *   Show Level in Name?:
 *   - Show the skill level in name for single skills?
 *   - Only applies to Window_ActorCommand!
 *   - Not all skill name displays will show skill levels.
 *
 *   Show Mastery Gauge?:
 *   - Show the mastery gauge for single skills?
 *   - Only applies to Window_ActorCommand!
 *   - Not all skill name displays will show skill masteries.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Level Up Effect Settings
 * ============================================================================
 *
 * These settings let you adjust the in-battle mastery level up effects used
 * for this plugin.
 *
 * ---
 *
 * Animation
 *
 *   Animation ID:
 *   - Play this animation when the effect activates.
 *
 *   Mirror Animation:
 *   - Mirror the effect animation?
 *
 *   Mute Animation:
 *   - Mute the effect animation?
 *
 * ---
 *
 * Popups
 *
 *   Text:
 *   - Text displayed upon the effect activating.
 *
 *   Text Color:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Flash Color:
 *   - Adjust the popup's flash color.
 *   - Format: [red, green, blue, alpha]
 *
 *   Flash Duration:
 *   - What is the frame duration of the flash effect?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Level Up Sound Settings
 * ============================================================================
 *
 * These settings let you adjust the sound effects used for this plugin.
 *
 * ---
 *
 * Settings
 *
 *   Filename:
 *   - Filename of the sound effect played.
 *
 *   Volume:
 *   - Volume of the sound effect played.
 *
 *   Pitch:
 *   - Pitch of the sound effect played.
 *
 *   Pan:
 *   - Pan of the sound effect played.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.02: March 20, 2025
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameters added by Irina:
 * *** Parameters > General > Window_ActorCommand > Show Level in Name?
 * **** Show the skill level in name for single skills?
 * **** Only applies to Window_ActorCommand!
 * **** Not all skill name displays will show skill levels.
 * *** Parameters > General > Window_ActorCommand > Show Mastery Gauge?
 * **** Show the mastery gauge for single skills?
 * **** Only applies to Window_ActorCommand!
 * **** Not all skill name displays will show skill levels.
 *
 * Version 1.01: July 13, 2023
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New script calls added by Arisu:
 * *** $actorSkillMasteryLevel(actorID, skillID)
 * *** $actorSkillMasteryExp(actorID, skillID)
 * *** $enemySkillMasteryLevel(enemyIndex, skillID)
 * *** $enemySkillMasteryExp(enemyIndex, skillID)
 * **** Please refer to the help file on how to use these script calls.
 *
 * Version 1.00 Official Release Date: June 28, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorGainSkillMasteryExp
 * @text Actor: Gain Skill Mastery / EXP
 * @desc Target actor(s) gain Skill Mastery and/or EXP.
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to adjust?
 * @default 0
 *
 * @arg Level:eval
 * @text Level
 * @parent SkillID:num
 * @type number
 * @desc Gains this many mastery levels for the skill.
 * @default 0
 *
 * @arg Exp:eval
 * @text EXP
 * @parent SkillID:num
 * @type number
 * @desc Gains this much exp of the mastery level.
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorSetSkillMasteryExp
 * @text Actor: Set Skill Mastery / EXP
 * @desc Sets the Skill Mastery level and EXP for target actor(s).
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to adjust?
 * @default 0
 *
 * @arg Level:eval
 * @text Level
 * @parent SkillID:num
 * @type number
 * @desc Sets the mastery level of the skill.
 * @default 0
 *
 * @arg Exp:eval
 * @text EXP
 * @parent SkillID:num
 * @type number
 * @desc Sets the exp of the current mastery level.
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Enemy
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command EnemyGainSkillMasteryExp
 * @text Enemy: Gain Skill Mastery / EXP
 * @desc Target enemy(ies) gain Skill Mastery and/or EXP.
 *
 * @arg EnemyIndex:arraynum
 * @text Enemy Index(es)
 * @type actor[]
 * @desc Select which Enemy Index(es) to affect.
 * Index values start at 0.
 * @default ["0"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to adjust?
 * @default 0
 *
 * @arg Level:eval
 * @text Level
 * @parent SkillID:num
 * @type number
 * @desc Gains this many mastery levels for the skill.
 * @default 0
 *
 * @arg Exp:eval
 * @text EXP
 * @parent SkillID:num
 * @type number
 * @desc Gains this much exp of the mastery level.
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command EnemySetSkillMasteryExp
 * @text Enemy: Set Skill Mastery / EXP
 * @desc Sets the Skill Mastery level and EXP for target enemy(ies).
 *
 * @arg EnemyIndex:arraynum
 * @text Enemy Index(es)
 * @type actor[]
 * @desc Select which Enemy Index(es) to affect.
 * Index values start at 0.
 * @default ["0"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to adjust?
 * @default 0
 *
 * @arg Level:eval
 * @text Level
 * @parent SkillID:num
 * @type number
 * @desc Sets the mastery level of the skill.
 * @default 0
 *
 * @arg Exp:eval
 * @text EXP
 * @parent SkillID:num
 * @type number
 * @desc Sets the exp of the current mastery level.
 * @default 0
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param SkillMastery
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc Adjust the general settings involving this plugin including mechanics and default values.
 * @default {"Auto":"","AutoVariableID:num":"0","Defaults":"","DefaultExpFormula:json":"\"exp = targetLevel * 3\"","DefaultMaxLevel:num":"99","DefaultEffects":"","hpCostFlat:num":"-0","hpCostRate:num":"-0.10","mpCostFlat:num":"-0","mpCostRate:num":"-0.10","tpCostFlat:num":"-0","tpCostRate:num":"-0.10","dmgFlat:num":"+0","dmgRate:num":"+0.20","cooldown:num":"-1","buffTurn:num":"+1","debuffTurn:num":"+1","stateTurn:num":"+1","PreventExp":"","preventExpForAttack:eval":"true","preventExpForGuard:eval":"true","PreventEffect":"","preventCostModToZero:eval":"true","preventCooldownModToZero:eval":"true","Display":"","masteryFmt:str":"Lv%2 %1","maxGaugeWidth:num":"384","gaugeColor1:str":"12","gaugeColor2:str":"4","gaugeStyle:str":"Growth"}
 *
 * @param Effect:struct
 * @text Level Up Effect Settings
 * @type struct<Effect>
 * @desc These settings let you adjust the in-battle mastery level up effects used for this plugin.
 * @default {"Animation":"","AnimationID:num":"45","Mirror:eval":"false","Mute:eval":"false","Popups":"","PopupText:str":"SKILL MASTERY UP!","TextColor:str":"6","FlashColor:eval":"[255, 255, 0, 160]","FlashDuration:num":"600"}
 *
 * @param Sound:struct
 * @text Level Up Sound Settings
 * @type struct<Sound>
 * @desc These settings let you adjust the sound effects used for this plugin.
 * @default {"name:str":"Barrier","volume:num":"90","pitch:num":"120","pan:num":"0"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param Auto
 *
 * @param AutoVariableID:num
 * @text Variable: Skill Level
 * @parent Auto
 * @type variable
 * @desc Select a variable ID to automatically record the last
 * used skill's mastery level. 0 to not use.
 * @default 0
 *
 * @param Defaults
 *
 * @param DefaultExpFormula:json
 * @text EXP Formula
 * @type note
 * @parent Defaults
 * @desc Default formula used to calculate needed EXP.
 * Return: exp. Variables: user, skill, targetLevel.
 * @default "exp = targetLevel * 3"
 *
 * @param DefaultMaxLevel:num
 * @text Max Level
 * @parent Defaults
 * @desc Default max level for skill masteries.
 * @default 99
 *
 * @param DefaultEffects
 * @text Effects Per Level
 * @parent Defaults
 *
 * @param hpCostFlat:num
 * @text HP Cost
 * @parent DefaultEffects
 * @desc Default flat HP Cost bonus per level.
 * @default -0
 *
 * @param hpCostRate:num
 * @text HP% Cost
 * @parent DefaultEffects
 * @desc Default HP Cost rate bonus per level.
 * @default -0.10
 *
 * @param mpCostFlat:num
 * @text MP Cost
 * @parent DefaultEffects
 * @desc Default flat MP Cost bonus per level.
 * @default -0
 *
 * @param mpCostRate:num
 * @text MP% Cost
 * @parent DefaultEffects
 * @desc Default MP Cost rate bonus per level.
 * @default -0.10
 *
 * @param tpCostFlat:num
 * @text TP Cost
 * @parent DefaultEffects
 * @desc Default flat TP Cost bonus per level.
 * @default -0
 *
 * @param tpCostRate:num
 * @text TP% Cost
 * @parent DefaultEffects
 * @desc Default TP Cost rate bonus per level.
 * @default -0.10
 *
 * @param dmgFlat:num
 * @text Damage/Heal
 * @parent DefaultEffects
 * @desc Default flat damage/heal bonus per level.
 * @default +0
 *
 * @param dmgRate:num
 * @text Damage/Heal%
 * @parent DefaultEffects
 * @desc Default damage/heal rate bonus per level.
 * @default +0.20
 *
 * @param cooldown:num
 * @text Cooldown Turns
 * @parent DefaultEffects
 * @desc Default cooldown turn bonus per level.
 * @default -1
 *
 * @param buffTurn:num
 * @text Buff Turns
 * @parent DefaultEffects
 * @desc Default buff turn bonus per level.
 * @default +1
 *
 * @param debuffTurn:num
 * @text Debuff Turns
 * @parent DefaultEffects
 * @desc Default debuff turn bonus per level.
 * @default +1
 *
 * @param stateTurn:num
 * @text State Turns
 * @parent DefaultEffects
 * @desc Default state turn bonus per level.
 * @default +1
 *
 * @param PreventExp
 * @text Prevent EXP Gain
 *
 * @param preventExpForAttack:eval
 * @text Basic Attack?
 * @parent PreventExp
 * @type boolean
 * @on Prevent
 * @off Allow
 * @desc Prevent EXP gain for basic attacks?
 * @default true
 *
 * @param preventExpForGuard:eval
 * @text Basic Guard?
 * @parent PreventExp
 * @type boolean
 * @on Prevent
 * @off Allow
 * @desc Prevent EXP gain for basic guarding?
 * @default true
 *
 * @param PreventEffect
 * @text Prevent Effects
 *
 * @param preventCostModToZero:eval
 * @text Costs to Zero?
 * @parent PreventEffect
 * @type boolean
 * @on Prevent
 * @off Allow
 * @desc Prevent costs from reaching zero?
 * @default true
 *
 * @param preventCooldownModToZero:eval
 * @text Cooldowns to Zero?
 * @parent PreventEffect
 * @type boolean
 * @on Prevent
 * @off Allow
 * @desc Prevent cooldowns from reaching zero?
 * @default true
 *
 * @param Display
 * @text Visual Display
 *
 * @param masteryFmt:str
 * @text Name Format
 * @parent Display
 * @desc Change how skill names appear with mastery levels.
 * %1 - Skill Name, %2 - Mastery Level.
 * @default Lv%2 %1
 *
 * @param maxGaugeWidth:num
 * @text Max Gauge Width
 * @parent Display
 * @desc Gauge widths adjust to the window size but there is
 * a maximum gauge width amount.
 * @default 384
 *
 * @param gaugeColor1:str
 * @text Gauge Color 1
 * @parent Display
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 12
 *
 * @param gaugeColor2:str
 * @text Gauge Color 2
 * @parent Display
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 4
 *
 * @param gaugeStyle:str
 * @text Gauge Style
 * @parent Display
 * @type select
 * @option -
 * @option Normal
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for skill mastery EXP.
 * Requires VisuMZ_3_VisualGaugeStyles!
 * @default Growth
 *
 * @param ActorCommand
 * @text Window_ActorCommand
 *
 * @param showBattleCommandLvName:eval
 * @text Show Level in Name?
 * @parent ActorCommand
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the skill level in name for single skills?
 * Only applies to Window_ActorCommand!
 * @default false
 *
 * @param showBattleCommandExpGauge:eval
 * @text Show Mastery Gauge?
 * @parent ActorCommand
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the mastery gauge for single skills?
 * Only applies to Window_ActorCommand!
 * @default false
 *
 */
/* ----------------------------------------------------------------------------
 * Effect Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Effect:
 *
 * @param Animation
 *
 * @param AnimationID:num
 * @text Animation ID
 * @parent Animation
 * @type animation
 * @desc Play this animation when the effect activates.
 * @default 45
 *
 * @param Mirror:eval
 * @text Mirror Animation
 * @parent Animation
 * @type boolean
 * @on Mirror
 * @off Normal
 * @desc Mirror the effect animation?
 * @default false
 *
 * @param Mute:eval
 * @text Mute Animation
 * @parent Animation
 * @type boolean
 * @on Mute
 * @off Normal
 * @desc Mute the effect animation?
 * @default false
 *
 * @param Popups
 *
 * @param PopupText:str
 * @text Text
 * @parent Popups
 * @desc Text displayed upon the effect activating.
 * @default SKILL MASTERY UP!
 *
 * @param TextColor:str
 * @text Text Color
 * @parent Popups
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 6
 *
 * @param FlashColor:eval
 * @text Flash Color
 * @parent Popups
 * @desc Adjust the popup's flash color.
 * Format: [red, green, blue, alpha]
 * @default [255, 255, 0, 160]
 *
 * @param FlashDuration:num
 * @text Flash Duration
 * @parent Popups
 * @type number
 * @desc What is the frame duration of the flash effect?
 * @default 600
 *
 */
/* ----------------------------------------------------------------------------
 * Sound Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Sound:
 *
 * @param name:str
 * @text Filename
 * @type file
 * @dir audio/se/
 * @require 1
 * @desc Filename of the sound effect played.
 * @default Barrier
 *
 * @param volume:num
 * @text Volume
 * @type number
 * @max 100
 * @desc Volume of the sound effect played.
 * @default 90
 *
 * @param pitch:num
 * @text Pitch
 * @type number
 * @desc Pitch of the sound effect played.
 * @default 120
 *
 * @param pan:num
 * @text Pan
 * @desc Pan of the sound effect played.
 * @default 0
 *
 */
//=============================================================================

const _0x4b1e51 = _0x3b54;
(function (_0x2431ee, _0xc445dd) {
    const _0x4fe72b = _0x3b54,
        _0x5c0009 = _0x2431ee();
    while (!![]) {
        try {
            const _0x55617d =
                parseInt(_0x4fe72b(0x239)) / 0x1 +
                (parseInt(_0x4fe72b(0x267)) / 0x2) * (-parseInt(_0x4fe72b(0x291)) / 0x3) +
                -parseInt(_0x4fe72b(0x258)) / 0x4 +
                parseInt(_0x4fe72b(0x1f6)) / 0x5 +
                parseInt(_0x4fe72b(0x214)) / 0x6 +
                -parseInt(_0x4fe72b(0x295)) / 0x7 +
                (-parseInt(_0x4fe72b(0x2cc)) / 0x8) * (-parseInt(_0x4fe72b(0x28e)) / 0x9);
            if (_0x55617d === _0xc445dd) break;
            else _0x5c0009['push'](_0x5c0009['shift']());
        } catch (_0x11873d) {
            _0x5c0009['push'](_0x5c0009['shift']());
        }
    }
})(_0x4e03, 0x26d5a);
var label = _0x4b1e51(0x224),
    tier = tier || 0x0,
    dependencies = ['VisuMZ_0_CoreEngine', _0x4b1e51(0x277)],
    pluginData = $plugins[_0x4b1e51(0x20a)](function (_0x80ddde) {
        const _0x5d1f20 = _0x4b1e51;
        return (
            _0x80ddde[_0x5d1f20(0x21e)] &&
            _0x80ddde[_0x5d1f20(0x28f)][_0x5d1f20(0x20f)]('[' + label + ']')
        );
    })[0x0];
function _0x3b54(_0x38c502, _0x22bbbc) {
    const _0x4e03b3 = _0x4e03();
    return (
        (_0x3b54 = function (_0x3b5451, _0x1ff10f) {
            _0x3b5451 = _0x3b5451 - 0x1f1;
            let _0x25bc2b = _0x4e03b3[_0x3b5451];
            return _0x25bc2b;
        }),
        _0x3b54(_0x38c502, _0x22bbbc)
    );
}
((VisuMZ[label][_0x4b1e51(0x281)] = VisuMZ[label][_0x4b1e51(0x281)] || {}),
    (VisuMZ[_0x4b1e51(0x221)] = function (_0x333c9a, _0x26a316) {
        const _0x9c3d68 = _0x4b1e51;
        for (const _0x268266 in _0x26a316) {
            if (_0x268266[_0x9c3d68(0x2cb)](/(.*):(.*)/i)) {
                const _0x38a367 = String(RegExp['$1']),
                    _0x4d84b6 = String(RegExp['$2'])[_0x9c3d68(0x236)]()['trim']();
                let _0x572ea4, _0xcba8ec, _0x8e5c6c;
                switch (_0x4d84b6) {
                    case _0x9c3d68(0x23e):
                        _0x572ea4 =
                            _0x26a316[_0x268266] !== '' ? Number(_0x26a316[_0x268266]) : 0x0;
                        break;
                    case _0x9c3d68(0x26e):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== '' ? JSON['parse'](_0x26a316[_0x268266]) : []),
                            (_0x572ea4 = _0xcba8ec['map'](_0x174eb9 => Number(_0x174eb9))));
                        break;
                    case _0x9c3d68(0x2c6):
                        _0x572ea4 = _0x26a316[_0x268266] !== '' ? eval(_0x26a316[_0x268266]) : null;
                        break;
                    case _0x9c3d68(0x2b2):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : []),
                            (_0x572ea4 = _0xcba8ec[_0x9c3d68(0x202)](_0x5257eb =>
                                eval(_0x5257eb)
                            )));
                        break;
                    case _0x9c3d68(0x2ab):
                        _0x572ea4 =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : '';
                        break;
                    case _0x9c3d68(0x22f):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : []),
                            (_0x572ea4 = _0xcba8ec[_0x9c3d68(0x202)](_0x1abf01 =>
                                JSON[_0x9c3d68(0x2c0)](_0x1abf01)
                            )));
                        break;
                    case _0x9c3d68(0x261):
                        _0x572ea4 =
                            _0x26a316[_0x268266] !== ''
                                ? new Function(JSON['parse'](_0x26a316[_0x268266]))
                                : new Function(_0x9c3d68(0x296));
                        break;
                    case _0x9c3d68(0x230):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== '' ? JSON['parse'](_0x26a316[_0x268266]) : []),
                            (_0x572ea4 = _0xcba8ec[_0x9c3d68(0x202)](
                                _0x273f7f => new Function(JSON[_0x9c3d68(0x2c0)](_0x273f7f))
                            )));
                        break;
                    case _0x9c3d68(0x210):
                        _0x572ea4 = _0x26a316[_0x268266] !== '' ? String(_0x26a316[_0x268266]) : '';
                        break;
                    case _0x9c3d68(0x201):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : []),
                            (_0x572ea4 = _0xcba8ec[_0x9c3d68(0x202)](_0xfc546f =>
                                String(_0xfc546f)
                            )));
                        break;
                    case _0x9c3d68(0x22b):
                        ((_0x8e5c6c =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : {}),
                            (_0x572ea4 = VisuMZ[_0x9c3d68(0x221)]({}, _0x8e5c6c)));
                        break;
                    case _0x9c3d68(0x1f8):
                        ((_0xcba8ec =
                            _0x26a316[_0x268266] !== ''
                                ? JSON[_0x9c3d68(0x2c0)](_0x26a316[_0x268266])
                                : []),
                            (_0x572ea4 = _0xcba8ec['map'](_0x22278c =>
                                VisuMZ[_0x9c3d68(0x221)]({}, JSON[_0x9c3d68(0x2c0)](_0x22278c))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x333c9a[_0x38a367] = _0x572ea4;
            }
        }
        return _0x333c9a;
    }),
    (_0x3f6e86 => {
        const _0x555781 = _0x4b1e51,
            _0x55499e = _0x3f6e86[_0x555781(0x2c7)];
        for (const _0x28617b of dependencies) {
            if (!Imported[_0x28617b]) {
                (alert(_0x555781(0x28b)[_0x555781(0x26b)](_0x55499e, _0x28617b)),
                    SceneManager[_0x555781(0x2c3)]());
                break;
            }
        }
        const _0x1faafd = _0x3f6e86['description'];
        if (_0x1faafd[_0x555781(0x2cb)](/\[Version[ ](.*?)\]/i)) {
            const _0x310bbf = Number(RegExp['$1']);
            _0x310bbf !== VisuMZ[label][_0x555781(0x2c5)] &&
                (alert(
                    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                        _0x555781(0x26b)
                    ](_0x55499e, _0x310bbf)
                ),
                SceneManager[_0x555781(0x2c3)]());
        }
        if (_0x1faafd[_0x555781(0x2cb)](/\[Tier[ ](\d+)\]/i)) {
            const _0x3a39e8 = Number(RegExp['$1']);
            _0x3a39e8 < tier
                ? (alert(_0x555781(0x29f)[_0x555781(0x26b)](_0x55499e, _0x3a39e8, tier)),
                  SceneManager[_0x555781(0x2c3)]())
                : (tier = Math['max'](_0x3a39e8, tier));
        }
        VisuMZ[_0x555781(0x221)](VisuMZ[label][_0x555781(0x281)], _0x3f6e86[_0x555781(0x2b1)]);
    })(pluginData));
if (VisuMZ[_0x4b1e51(0x222)]['version'] < 1.38) {
    let text = '';
    ((text += _0x4b1e51(0x1fc)),
        (text += _0x4b1e51(0x275)),
        alert(text),
        SceneManager[_0x4b1e51(0x2c3)]());
}
(PluginManager[_0x4b1e51(0x2ba)](
    pluginData[_0x4b1e51(0x2c7)],
    'ActorGainSkillMasteryExp',
    _0x5ba5cb => {
        const _0x5a833e = _0x4b1e51;
        VisuMZ[_0x5a833e(0x221)](_0x5ba5cb, _0x5ba5cb);
        const _0xe54ad = _0x5ba5cb[_0x5a833e(0x20e)]
                [_0x5a833e(0x202)](_0x18ac22 => $gameActors[_0x5a833e(0x1fb)](_0x18ac22))
                ['remove'](null)
                [_0x5a833e(0x21c)](undefined),
            _0x24a8ca = _0x5ba5cb['SkillID'] || 0x0,
            _0x294035 = _0x5ba5cb['Level'] || 0x0,
            _0x3f3937 = _0x5ba5cb[_0x5a833e(0x242)] || 0x0;
        for (const _0xaa18c4 of _0xe54ad) {
            const _0x8255 = _0xaa18c4[_0x5a833e(0x262)](_0x24a8ca);
            _0xaa18c4[_0x5a833e(0x1f9)](_0x24a8ca, _0x8255 + _0x294035);
            const _0x39d5c6 = _0xaa18c4['skillMasteryExp'](_0x24a8ca);
            _0xaa18c4[_0x5a833e(0x292)](_0x24a8ca, _0x39d5c6 + _0x3f3937, !![]);
        }
    }
),
    PluginManager[_0x4b1e51(0x2ba)](pluginData['name'], _0x4b1e51(0x2b5), _0x44a75d => {
        const _0x5788a8 = _0x4b1e51;
        VisuMZ[_0x5788a8(0x221)](_0x44a75d, _0x44a75d);
        const _0x2e15f6 = _0x44a75d[_0x5788a8(0x20e)]
                [_0x5788a8(0x202)](_0x4d325d => $gameActors[_0x5788a8(0x1fb)](_0x4d325d))
                ['remove'](null)
                [_0x5788a8(0x21c)](undefined),
            _0x39b4a9 = _0x44a75d[_0x5788a8(0x216)] || 0x0,
            _0x3968ee = _0x44a75d[_0x5788a8(0x1fe)] || 0x0,
            _0x5bea2d = _0x44a75d[_0x5788a8(0x242)] || 0x0;
        for (const _0x59900b of _0x2e15f6) {
            (_0x59900b[_0x5788a8(0x1f9)](_0x39b4a9, _0x3968ee),
                _0x59900b[_0x5788a8(0x292)](_0x39b4a9, _0x5bea2d, !![]));
        }
    }),
    PluginManager[_0x4b1e51(0x2ba)](
        pluginData[_0x4b1e51(0x2c7)],
        'EnemyGainSkillMasteryExp',
        _0x3a71de => {
            const _0x116657 = _0x4b1e51;
            VisuMZ['ConvertParams'](_0x3a71de, _0x3a71de);
            const _0x5d212f = _0x3a71de['EnemyIndex']
                    [_0x116657(0x202)](_0x55c5fb => $gameTroop[_0x116657(0x211)]()[_0x55c5fb])
                    ['remove'](null)
                    ['remove'](undefined),
                _0x2e186c = _0x3a71de['SkillID'] || 0x0,
                _0x2b70eb = _0x3a71de[_0x116657(0x1fe)] || 0x0,
                _0x58809d = _0x3a71de[_0x116657(0x242)] || 0x0;
            for (const _0x5eea59 of _0x5d212f) {
                const _0x2545f0 = _0x5eea59[_0x116657(0x262)](_0x2e186c);
                _0x5eea59[_0x116657(0x1f9)](_0x2e186c, _0x2545f0 + _0x2b70eb);
                const _0x2881df = _0x5eea59[_0x116657(0x237)](_0x2e186c);
                _0x5eea59[_0x116657(0x292)](_0x2e186c, _0x2881df + _0x58809d, !![]);
            }
        }
    ),
    PluginManager['registerCommand'](
        pluginData[_0x4b1e51(0x2c7)],
        'EnemySetSkillMasteryExp',
        _0x138bb3 => {
            const _0x112de4 = _0x4b1e51;
            VisuMZ[_0x112de4(0x221)](_0x138bb3, _0x138bb3);
            const _0x369554 = _0x138bb3['EnemyIndex']
                    ['map'](_0x5d06c7 => $gameTroop['members']()[_0x5d06c7])
                    [_0x112de4(0x21c)](null)
                    ['remove'](undefined),
                _0x320d8f = _0x138bb3[_0x112de4(0x216)] || 0x0,
                _0x1e1915 = _0x138bb3[_0x112de4(0x1fe)] || 0x0,
                _0x1691b8 = _0x138bb3[_0x112de4(0x242)] || 0x0;
            for (const _0x3c4c59 of _0x369554) {
                (_0x3c4c59[_0x112de4(0x1f9)](_0x320d8f, _0x1e1915),
                    _0x3c4c59[_0x112de4(0x292)](_0x320d8f, _0x1691b8, !![]));
            }
        }
    ),
    (VisuMZ['SkillMastery'][_0x4b1e51(0x276)] = {
        expFormula: /<JS SKILL MASTERY EXP>\s*([\s\S]*?)\s*<\/JS SKILL MASTERY EXP>/i,
        maxLevel: /<MAX SKILL MASTERY LEVEL:[ ](\d+)>/i,
        costChangePerLevelFlat:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) (.*) COST(?:| PER LEVEL)>/gi,
        costChangePerLevelRate:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+)([%％]) (.*) COST(?:| PER LEVEL)>/gi,
        dmgChangePerLevelFlat:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) (?:DMG|DAMAGE|HEAL|HEALING)(?:| PER LEVEL)>/gi,
        dmgChangePerLevelRate:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+)([%％]) (?:DMG|DAMAGE|HEAL|HEALING)(?:| PER LEVEL)>/gi,
        cooldownChangePerLevel:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) COOLDOWN(?:| TURN| TURNS)(?:| PER LEVEL)>/i,
        buffTurnChangePerLevel:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) BUFF(?:| TURN| TURNS)(?:| PER LEVEL)>/i,
        debuffTurnChangePerLevel:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) DEBUFF(?:| TURN| TURNS)(?:| PER LEVEL)>/i,
        stateTurnChangePerLevel:
            /<MASTERY EFFECT(?:| PER LEVEL): ([\+\-]\d+) STATE(?:| TURN| TURNS)(?:| PER LEVEL)>/i,
        startMasteries: /<STARTING SKILL MASTERIES>\s*([\s\S]*?)\s*<\/STARTING SKILL MASTERIES>/i,
    }),
    (DataManager[_0x4b1e51(0x200)] = function (_0x5b8cb2) {
        const _0x4f0898 = _0x4b1e51,
            _0x371cf2 = $dataSkills[_0x5b8cb2];
        if (!_0x371cf2) return VisuMZ[_0x4f0898(0x224)]['DefaultFormula'];
        this[_0x4f0898(0x278)] = this[_0x4f0898(0x278)] || {};
        if (this[_0x4f0898(0x278)][_0x5b8cb2] !== undefined)
            return this[_0x4f0898(0x278)][_0x5b8cb2];
        let _0x123156 = Game_BattlerBase[_0x4f0898(0x2cd)][_0x4f0898(0x2a3)];
        const _0x4758d8 = VisuMZ['SkillMastery']['RegExp'],
            _0x3e8c0a = _0x371cf2[_0x4f0898(0x21b)] || '';
        _0x3e8c0a['match'](_0x4758d8[_0x4f0898(0x2a3)]) && (_0x123156 = String(RegExp['$1']));
        const _0x1167e6 = _0x4f0898(0x23b)['format'](_0x123156);
        return (
            (this[_0x4f0898(0x278)][_0x5b8cb2] = new Function(_0x1167e6)),
            this[_0x4f0898(0x278)][_0x5b8cb2]
        );
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x293)] = function () {
        const _0x4b30a5 = _0x4b1e51,
            _0x251cea = arguments[0x0],
            _0x536d16 = arguments[0x1],
            _0x35074b = arguments[0x2],
            _0x22cc04 = _0x251cea,
            _0x14ec3a = _0x251cea;
        let _0x2c7327 = 0xf4240;
        try {
            _0x2c7327 = _0x35074b * 0x3;
        } catch (_0x4fbfcc) {
            if ($gameTemp[_0x4b30a5(0x20c)]()) console['log'](_0x4fbfcc);
        }
        return _0x2c7327;
    }),
    (DataManager[_0x4b1e51(0x27f)] = function (_0x350472) {
        const _0x55c319 = _0x4b1e51,
            _0x424fe3 = $dataSkills[_0x350472];
        if (!_0x424fe3) return VisuMZ[_0x55c319(0x224)]['DefaultFormula'];
        this[_0x55c319(0x2b6)] = this[_0x55c319(0x2b6)] || {};
        if (this['_skillMasteryMaxLevel'][_0x350472] !== undefined)
            return this[_0x55c319(0x2b6)][_0x350472];
        this['_skillMasteryMaxLevel'][_0x350472] =
            Game_BattlerBase[_0x55c319(0x2cd)][_0x55c319(0x2a7)];
        const _0x53599e = VisuMZ[_0x55c319(0x224)][_0x55c319(0x276)],
            _0x22838e = _0x424fe3[_0x55c319(0x21b)] || '';
        return (
            _0x22838e['match'](_0x53599e['maxLevel']) &&
                (this[_0x55c319(0x2b6)][_0x350472] = Math[_0x55c319(0x2b4)](
                    Number(RegExp['$1']),
                    0x0
                )),
            this['_skillMasteryMaxLevel'][_0x350472]
        );
    }),
    (DataManager[_0x4b1e51(0x1fa)] = function (_0x5638f9) {
        const _0x21f178 = _0x4b1e51,
            _0x144a77 = $dataSkills[_0x5638f9];
        if (!_0x144a77) return VisuMZ[_0x21f178(0x224)][_0x21f178(0x293)];
        this[_0x21f178(0x1f7)] = this[_0x21f178(0x1f7)] || {};
        if (this[_0x21f178(0x1f7)][_0x5638f9] !== undefined)
            return this[_0x21f178(0x1f7)][_0x5638f9];
        const _0x3e89ed = VisuMZ['SkillMastery'][_0x21f178(0x281)][_0x21f178(0x25b)];
        this[_0x21f178(0x1f7)][_0x5638f9] = {
            costFlat: {},
            costRate: {},
            dmgFlat: _0x3e89ed['dmgFlat'] ?? 0x0,
            dmgRate: _0x3e89ed[_0x21f178(0x289)] ?? 0.2,
            cooldown: _0x3e89ed[_0x21f178(0x287)] ?? -0x1,
            buffTurn: _0x3e89ed[_0x21f178(0x2ce)] ?? 0x1,
            debuffTurn: _0x3e89ed[_0x21f178(0x223)] ?? 0x1,
            stateTurn: _0x3e89ed[_0x21f178(0x25c)] ?? 0x1,
        };
        const _0x5bb2be = [],
            _0x1b4e60 = VisuMZ[_0x21f178(0x224)][_0x21f178(0x276)],
            _0x46cc58 = _0x144a77['note'] || '',
            _0x773115 = _0x46cc58['match'](_0x1b4e60[_0x21f178(0x27a)]);
        if (_0x773115)
            for (const _0xebe94a of _0x773115) {
                _0xebe94a[_0x21f178(0x2cb)](_0x1b4e60['costChangePerLevelFlat']);
                const _0x29980f = Number(RegExp['$1']),
                    _0x51cf6f = String(RegExp['$2'])[_0x21f178(0x236)]()['trim']();
                ((this['_skillMasteryLevelEffectData'][_0x5638f9][_0x21f178(0x247)][_0x51cf6f] =
                    _0x29980f),
                    _0x5bb2be['push'](_0x51cf6f));
            }
        const _0xc08819 = _0x46cc58[_0x21f178(0x2cb)](_0x1b4e60[_0x21f178(0x286)]);
        if (_0xc08819)
            for (const _0x4f59f4 of _0xc08819) {
                _0x4f59f4[_0x21f178(0x2cb)](_0x1b4e60['costChangePerLevelRate']);
                const _0x270b6b = Number(RegExp['$1']) * 0.01,
                    _0x105ea3 = String(RegExp['$3'])['toUpperCase']()['trim']();
                ((this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x1f2)][_0x105ea3] = _0x270b6b),
                    _0x5bb2be[_0x21f178(0x260)](_0x105ea3));
            }
        !_0x5bb2be['includes']('HP') &&
            ((this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x247)]['HP'] =
                _0x3e89ed[_0x21f178(0x259)] ?? 0x0),
            (this[_0x21f178(0x1f7)][_0x5638f9]['costRate']['HP'] =
                _0x3e89ed[_0x21f178(0x203)] ?? -0.1));
        !_0x5bb2be[_0x21f178(0x20f)]('MP') &&
            ((this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x247)]['MP'] =
                _0x3e89ed[_0x21f178(0x2c2)] ?? 0x0),
            (this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x1f2)]['MP'] =
                _0x3e89ed[_0x21f178(0x232)] ?? -0.1));
        !_0x5bb2be[_0x21f178(0x20f)]('TP') &&
            ((this[_0x21f178(0x1f7)][_0x5638f9]['costFlat']['TP'] =
                _0x3e89ed[_0x21f178(0x220)] ?? 0x0),
            (this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x1f2)]['TP'] =
                _0x3e89ed[_0x21f178(0x233)] ?? -0.1));
        const _0x2cf7f0 = _0x46cc58[_0x21f178(0x2cb)](_0x1b4e60['dmgChangePerLevelFlat']);
        if (_0x2cf7f0)
            for (const _0x380f12 of _0x2cf7f0) {
                _0x380f12[_0x21f178(0x2cb)](_0x1b4e60['dmgChangePerLevelFlat']);
                const _0x2d6616 = Number(RegExp['$1']);
                this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x2a0)] = _0x2d6616;
            }
        const _0x4144b7 = _0x46cc58[_0x21f178(0x2cb)](_0x1b4e60[_0x21f178(0x21d)]);
        if (_0x4144b7)
            for (const _0x348ff7 of _0x4144b7) {
                _0x348ff7[_0x21f178(0x2cb)](_0x1b4e60[_0x21f178(0x21d)]);
                const _0x19c8b0 = Number(RegExp['$1']) * 0.01;
                this[_0x21f178(0x1f7)][_0x5638f9][_0x21f178(0x289)] = _0x19c8b0;
            }
        const _0x549ca4 = [
            ['cooldownChangePerLevel', _0x21f178(0x287)],
            ['buffTurnChangePerLevel', _0x21f178(0x2ce)],
            [_0x21f178(0x298), _0x21f178(0x223)],
            ['stateTurnChangePerLevel', _0x21f178(0x25c)],
        ];
        for (const _0x1bcc20 of _0x549ca4) {
            const _0x1c012b = _0x1bcc20[0x0],
                _0x7b31cf = _0x1bcc20[0x1];
            if (_0x46cc58['match'](_0x1b4e60[_0x1c012b])) {
                const _0x217c94 = Number(RegExp['$1']);
                this[_0x21f178(0x1f7)][_0x5638f9][_0x7b31cf] = _0x217c94;
            }
        }
        return this['_skillMasteryLevelEffectData'][_0x5638f9];
    }),
    (SoundManager[_0x4b1e51(0x21f)] = function () {
        const _0x12e241 = _0x4b1e51,
            _0x97b80e = VisuMZ[_0x12e241(0x224)][_0x12e241(0x281)]['Sound'],
            _0x5ec664 = {
                name: _0x97b80e[_0x12e241(0x2c7)],
                volume: _0x97b80e[_0x12e241(0x238)],
                pitch: _0x97b80e[_0x12e241(0x27b)],
                pan: _0x97b80e[_0x12e241(0x23f)],
            };
        AudioManager['playSe'](_0x5ec664);
    }),
    (SceneManager['isSceneBattle'] = function () {
        const _0x420eeb = _0x4b1e51;
        return this['_scene'] && this[_0x420eeb(0x2af)]['constructor'] === Scene_Battle;
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x21a)] = BattleManager[_0x4b1e51(0x27d)]),
    (BattleManager[_0x4b1e51(0x27d)] = function (_0x306fb1, _0x32f84a, _0x9f6776) {
        const _0x255e27 = _0x4b1e51;
        (VisuMZ[_0x255e27(0x224)]['BattleManager_setup']['call'](
            this,
            _0x306fb1,
            _0x32f84a,
            _0x9f6776
        ),
            (this[_0x255e27(0x2b0)] = undefined),
            (this[_0x255e27(0x1f4)] = undefined));
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x269)] = BattleManager[_0x4b1e51(0x24b)]),
    (BattleManager[_0x4b1e51(0x24b)] = function () {
        const _0x2689e1 = _0x4b1e51;
        (this[_0x2689e1(0x228)](),
            VisuMZ[_0x2689e1(0x224)][_0x2689e1(0x269)][_0x2689e1(0x2b8)](this));
    }),
    (BattleManager[_0x4b1e51(0x228)] = function () {
        const _0x56721f = _0x4b1e51,
            _0x395608 = this[_0x56721f(0x2a8)],
            _0x445043 = _0x395608[_0x56721f(0x24a)]();
        _0x395608 &&
            _0x445043 &&
            _0x445043[_0x56721f(0x266)]() &&
            _0x445043['isSkill']() &&
            ((this[_0x56721f(0x2b0)] = this[_0x56721f(0x2a8)]),
            (this['_masterySkill'] = _0x445043[_0x56721f(0x266)]()),
            $gameVariables[_0x56721f(0x26a)](_0x395608, _0x445043[_0x56721f(0x266)]()));
    }),
    (VisuMZ[_0x4b1e51(0x224)]['BattleManager_endAction'] = BattleManager[_0x4b1e51(0x249)]),
    (BattleManager[_0x4b1e51(0x249)] = function () {
        const _0x5efa83 = _0x4b1e51;
        (this[_0x5efa83(0x25a)](),
            VisuMZ[_0x5efa83(0x224)]['BattleManager_endAction'][_0x5efa83(0x2b8)](this));
    }),
    (BattleManager[_0x4b1e51(0x25a)] = function () {
        const _0x509b43 = _0x4b1e51;
        if (this[_0x509b43(0x2b0)] && this[_0x509b43(0x1f4)]) {
            const _0x706f97 = this[_0x509b43(0x2b0)],
                _0x12636e = this['_masterySkill'];
            _0x706f97[_0x509b43(0x217)](_0x12636e['id']);
        }
        ((this['_masterySubject'] = undefined), (this[_0x509b43(0x1f4)] = undefined));
    }),
    (Game_Variables[_0x4b1e51(0x2cd)] = {
        targetVariableID: VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x279)] ?? 0x0,
    }),
    (Game_Variables['prototype']['updateSkillMasteryVariable'] = function (_0x3074c5, _0x42a7c6) {
        const _0x2eba49 = _0x4b1e51,
            _0x2f04d2 = Game_Variables[_0x2eba49(0x2cd)][_0x2eba49(0x29a)];
        if (_0x2f04d2 <= 0x0) return;
        if (!_0x3074c5) return;
        if (!_0x42a7c6) return;
        const _0x8436f4 = _0x3074c5[_0x2eba49(0x262)](_0x42a7c6['id']);
        this['setValue'](_0x2f04d2, _0x8436f4);
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x256)] = Game_Action[_0x4b1e51(0x2c1)][_0x4b1e51(0x264)]),
    (Game_Action[_0x4b1e51(0x2c1)][_0x4b1e51(0x264)] = function (_0x307c5f, _0x113bfd) {
        const _0xe7919a = _0x4b1e51;
        _0x307c5f = VisuMZ[_0xe7919a(0x224)][_0xe7919a(0x256)][_0xe7919a(0x2b8)](
            this,
            _0x307c5f,
            _0x113bfd
        );
        if (!this[_0xe7919a(0x265)]()) return _0x307c5f;
        const _0x371fd8 = this[_0xe7919a(0x229)]()[_0xe7919a(0x262)](
            this[_0xe7919a(0x266)]()['id']
        );
        if (_0x371fd8 <= 0x0) return _0x307c5f;
        const _0x492f86 = DataManager['skillMasteryLevelEffectData'](this['item']()['id']);
        if (_0x492f86[_0xe7919a(0x289)] !== 0x0) {
            const _0x27c89f = 0x1 + _0x371fd8 * _0x492f86[_0xe7919a(0x289)];
            _0x307c5f *= _0x27c89f;
        }
        if (_0x492f86[_0xe7919a(0x2a0)] !== 0x0) {
            if (_0x307c5f > 0x0) _0x307c5f += _0x371fd8 * _0x492f86[_0xe7919a(0x2a0)];
            else _0x307c5f < 0x0 && (_0x307c5f -= _0x371fd8 * _0x492f86['dmgFlat']);
        }
        return _0x307c5f;
    }),
    (Game_BattlerBase[_0x4b1e51(0x2cd)] = {
        expFormula:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x218)] ??
            _0x4b1e51(0x268),
        maxLevel:
            VisuMZ['SkillMastery'][_0x4b1e51(0x281)][_0x4b1e51(0x25b)]['DefaultMaxLevel'] ?? 0x63,
        preventExpForAttack:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x205)] ?? !![],
        preventExpForGuard:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x248)] ?? !![],
        allowCostModToZero:
            !VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)]['preventCostModToZero'] ??
            ![],
        allowCooldownModToZero:
            !VisuMZ['SkillMastery'][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x1f3)] ?? ![],
    }),
    (VisuMZ['SkillMastery'][_0x4b1e51(0x250)] =
        Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x288)]),
    (Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x288)] = function () {
        const _0x2174d0 = _0x4b1e51;
        (VisuMZ[_0x2174d0(0x224)]['Game_BattlerBase_initMembers']['call'](this),
            this[_0x2174d0(0x244)]());
    }),
    (Game_BattlerBase['prototype'][_0x4b1e51(0x244)] = function () {
        const _0xa497a9 = _0x4b1e51;
        ((this[_0xa497a9(0x285)] = {}), (this[_0xa497a9(0x2a4)] = {}));
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x262)] = function (_0x3aefa9) {
        const _0x1fd410 = _0x4b1e51;
        if (
            Game_BattlerBase[_0x1fd410(0x2cd)][_0x1fd410(0x205)] &&
            _0x3aefa9 === this[_0x1fd410(0x2a1)]()
        )
            return 0x0;
        if (
            Game_BattlerBase[_0x1fd410(0x2cd)][_0x1fd410(0x248)] &&
            _0x3aefa9 === this[_0x1fd410(0x2aa)]()
        )
            return 0x0;
        if (this[_0x1fd410(0x285)] === undefined) this[_0x1fd410(0x244)]();
        return this[_0x1fd410(0x285)][_0x3aefa9] || 0x0;
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)]['setSkillMasteryLevel'] = function (_0x4efad1, _0x3b1a7e) {
        const _0x134209 = _0x4b1e51;
        if (
            Game_BattlerBase[_0x134209(0x2cd)][_0x134209(0x205)] &&
            _0x4efad1 === this[_0x134209(0x2a1)]()
        )
            return;
        if (
            Game_BattlerBase[_0x134209(0x2cd)][_0x134209(0x248)] &&
            _0x4efad1 === this[_0x134209(0x2aa)]()
        )
            return;
        if (this[_0x134209(0x285)] === undefined) this[_0x134209(0x244)]();
        if (this[_0x134209(0x2a4)] === undefined) this[_0x134209(0x244)]();
        this[_0x134209(0x285)][_0x4efad1] !== _0x3b1a7e &&
            ((this[_0x134209(0x285)][_0x4efad1] = _0x3b1a7e[_0x134209(0x2b3)](
                0x0,
                DataManager[_0x134209(0x27f)](_0x4efad1)
            )),
            (this[_0x134209(0x2a4)][_0x4efad1] = 0x0));
    }),
    (Game_BattlerBase['prototype'][_0x4b1e51(0x237)] = function (_0x25e3f5) {
        const _0x2515bd = _0x4b1e51;
        if (
            Game_BattlerBase[_0x2515bd(0x2cd)]['preventExpForAttack'] &&
            _0x25e3f5 === this[_0x2515bd(0x2a1)]()
        )
            return 0x0;
        if (
            Game_BattlerBase[_0x2515bd(0x2cd)][_0x2515bd(0x248)] &&
            _0x25e3f5 === this[_0x2515bd(0x2aa)]()
        )
            return 0x0;
        if (this[_0x2515bd(0x2a4)] === undefined) this[_0x2515bd(0x244)]();
        return this[_0x2515bd(0x2a4)][_0x25e3f5] || 0x0;
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x292)] = function (
        _0x19419c,
        _0x5879a3,
        _0x23a361,
        _0x3733a0
    ) {
        const _0x15c5ce = _0x4b1e51;
        if (
            Game_BattlerBase['SKILL_MASTERY'][_0x15c5ce(0x205)] &&
            _0x19419c === this['attackSkillId']()
        )
            return;
        if (
            Game_BattlerBase[_0x15c5ce(0x2cd)]['preventExpForGuard'] &&
            _0x19419c === this[_0x15c5ce(0x2aa)]()
        )
            return;
        if (this[_0x15c5ce(0x2a4)] === undefined) this[_0x15c5ce(0x244)]();
        this[_0x15c5ce(0x2a4)][_0x19419c] = Math[_0x15c5ce(0x2b4)](_0x5879a3, 0x0);
        if (_0x3733a0) return;
        this['checkSkillMasteryLevelUp'](_0x19419c, _0x23a361);
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)]['gainSkillMasteryExp'] = function (
        _0x14c89a,
        _0x78257d,
        _0xe2b613
    ) {
        const _0x1cb960 = _0x4b1e51;
        if (
            Game_BattlerBase[_0x1cb960(0x2cd)]['preventExpForAttack'] &&
            _0x14c89a === this[_0x1cb960(0x2a1)]()
        )
            return;
        if (
            Game_BattlerBase[_0x1cb960(0x2cd)]['preventExpForGuard'] &&
            _0x14c89a === this[_0x1cb960(0x2aa)]()
        )
            return;
        if (this[_0x1cb960(0x2a4)] === undefined) this[_0x1cb960(0x244)]();
        ((_0x78257d = _0x78257d || 0x1),
            (this[_0x1cb960(0x2a4)][_0x14c89a] = this[_0x1cb960(0x2a4)][_0x14c89a] || 0x0),
            (this[_0x1cb960(0x2a4)][_0x14c89a] += _0x78257d),
            (this[_0x1cb960(0x2a4)][_0x14c89a] = Math[_0x1cb960(0x2b4)](
                this[_0x1cb960(0x2a4)][_0x14c89a],
                0x0
            )));
        if (_0xe2b613) return;
        this[_0x1cb960(0x25f)](_0x14c89a);
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x243)] = Game_Battler[_0x4b1e51(0x2c1)][_0x4b1e51(0x29c)]),
    (Game_Battler[_0x4b1e51(0x2c1)][_0x4b1e51(0x29c)] = function (_0x13366b) {
        const _0x11d7c0 = _0x4b1e51;
        (VisuMZ['SkillMastery'][_0x11d7c0(0x243)][_0x11d7c0(0x2b8)](this, _0x13366b),
            DataManager[_0x11d7c0(0x265)](_0x13366b) &&
                !SceneManager['isSceneBattle']() &&
                (this[_0x11d7c0(0x217)](_0x13366b['id']),
                $gameVariables[_0x11d7c0(0x26a)](this, _0x13366b)));
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)]['setupStartingSkillMasteries'] = function () {
        const _0x23c81b = _0x4b1e51;
        this['initSkillMasteries']();
        const _0x460e34 = VisuMZ[_0x23c81b(0x224)]['RegExp'],
            _0x4c5ef0 =
                (this[_0x23c81b(0x297)]()
                    ? this['actor']()[_0x23c81b(0x21b)]
                    : this['enemy']()[_0x23c81b(0x21b)]) || '';
        if (_0x4c5ef0[_0x23c81b(0x2cb)](_0x460e34[_0x23c81b(0x27c)])) {
            const _0xcf5484 = String(RegExp['$1'])['split'](/[\r\n]+/);
            for (const _0x21e845 of _0xcf5484) {
                if (_0x21e845[_0x23c81b(0x2cb)](/(.*):[ ](.*)/i)) {
                    const _0x25a17e = String(RegExp['$1']),
                        _0x18d8fb = RegExp['$2']
                            ['split'](',')
                            [_0x23c81b(0x202)](_0x5039f1 => Number(_0x5039f1));
                    let _0x122dca = 0x0;
                    _0x25a17e[_0x23c81b(0x2cb)](/SKILL[ ](\d+)/i)
                        ? (_0x122dca = Number(RegExp['$1']))
                        : (_0x122dca = DataManager['getSkillIdWithName'](_0x25a17e));
                    if (_0x122dca > 0x0) {
                        const _0x58f4d5 = _0x18d8fb[0x0] || 0x0,
                            _0x5f455a = _0x18d8fb[0x1] || 0x0;
                        (this[_0x23c81b(0x1f9)](_0x122dca, _0x58f4d5),
                            this['setSkillMasteryExp'](_0x122dca, _0x5f455a, !![]));
                    }
                }
            }
        }
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x25f)] = function (_0x5e4eeb, _0x3de16f) {
        const _0x2be4e3 = _0x4b1e51;
        let _0x1441ee = ![];
        for (;;) {
            const _0x162140 = this[_0x2be4e3(0x262)](_0x5e4eeb);
            if (_0x162140 >= DataManager[_0x2be4e3(0x27f)](_0x5e4eeb)) break;
            const _0x24406a = this['skillMasteryExp'](_0x5e4eeb),
                _0x568edd = this[_0x2be4e3(0x240)](_0x5e4eeb, _0x162140 + 0x1);
            if (_0x24406a >= _0x568edd)
                ((_0x1441ee = !![]),
                    this['setSkillMasteryLevel'](_0x5e4eeb, _0x162140 + 0x1),
                    this['setSkillMasteryExp'](_0x5e4eeb, _0x24406a - _0x568edd, _0x3de16f, !![]));
            else break;
        }
        _0x1441ee &&
            (!_0x3de16f && SoundManager['playSkillMasteryLevelUp'](),
            SceneManager[_0x2be4e3(0x272)]() &&
                Imported[_0x2be4e3(0x241)] &&
                this[_0x2be4e3(0x24c)](_0x5e4eeb));
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)]['skillMasteryExpNeeded'] = function (_0x727d2b, _0x43802c) {
        const _0x46e25f = _0x4b1e51,
            _0x499264 = DataManager[_0x46e25f(0x200)](_0x727d2b);
        return _0x499264[_0x46e25f(0x2b8)](this, this, $dataSkills[_0x727d2b], _0x43802c);
    }),
    (Game_Battler[_0x4b1e51(0x2c1)]['displaySkillMasteryLevelUpEffect'] = function (_0x54da22) {
        const _0x3ff7a7 = _0x4b1e51;
        if (!SceneManager[_0x3ff7a7(0x272)]()) return ![];
        const _0x52b401 = VisuMZ[_0x3ff7a7(0x224)][_0x3ff7a7(0x281)][_0x3ff7a7(0x2ad)];
        if (!_0x52b401) return;
        if (_0x52b401[_0x3ff7a7(0x23a)] > 0x0) {
            const _0x52d9c6 = [this],
                _0x38b1aa = _0x52b401[_0x3ff7a7(0x23a)],
                _0x3f11ce = _0x52b401[_0x3ff7a7(0x24e)],
                _0x3ac933 = _0x52b401['Mute'];
            $gameTemp[_0x3ff7a7(0x280)](_0x52d9c6, _0x38b1aa, _0x3f11ce, _0x3ac933);
        }
        if (_0x52b401[_0x3ff7a7(0x219)] !== '') {
            const _0x36b338 = _0x52b401[_0x3ff7a7(0x219)],
                _0x435cb2 = {
                    textColor: _0x52b401[_0x3ff7a7(0x284)],
                    flashColor: _0x52b401[_0x3ff7a7(0x274)],
                    flashDuration: _0x52b401['FlashDuration'],
                };
            this['setupTextPopup'](_0x36b338, _0x435cb2);
        }
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x2a9)] =
        Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x206)]),
    (Game_BattlerBase['prototype']['adjustSkillCost'] = function (_0x2a0b36, _0x281aac, _0x29a667) {
        const _0x422e8 = _0x4b1e51;
        _0x281aac = VisuMZ['SkillMastery'][_0x422e8(0x2a9)]['call'](
            this,
            _0x2a0b36,
            _0x281aac,
            _0x29a667
        );
        if (_0x281aac <= 0x0) return _0x281aac;
        const _0xe2bd14 = this[_0x422e8(0x262)](_0x2a0b36['id']);
        if (_0xe2bd14 <= 0x0) return _0x281aac;
        let _0x1b1260 = ![];
        const _0x1341aa = DataManager['skillMasteryLevelEffectData'](_0x2a0b36['id']),
            _0x3f8e43 = _0x29a667['Name'][_0x422e8(0x236)]();
        if (_0x1341aa['costRate'][_0x3f8e43] !== undefined) {
            const _0x476687 = 0x1 + _0xe2bd14 * _0x1341aa['costRate'][_0x3f8e43];
            ((_0x281aac *= _0x476687), (_0x1b1260 = !![]));
        }
        _0x1341aa[_0x422e8(0x247)][_0x3f8e43] !== undefined &&
            ((_0x281aac += _0xe2bd14 * _0x1341aa[_0x422e8(0x247)][_0x3f8e43]), (_0x1b1260 = !![]));
        if (_0x1b1260) {
            const _0xee075d = Game_BattlerBase[_0x422e8(0x2cd)][_0x422e8(0x2bb)] ? 0x0 : 0x1;
            _0x281aac = Math[_0x422e8(0x2b4)](Math[_0x422e8(0x271)](_0x281aac), _0xee075d);
        }
        return _0x281aac;
    }),
    (Game_BattlerBase['prototype']['canApplySkillMasteryEffect'] = function () {
        const _0x3e209b = _0x4b1e51;
        if (SceneManager['isSceneBattle']())
            return (
                BattleManager['_subject'] &&
                BattleManager['_action'] &&
                BattleManager[_0x3e209b(0x22d)][_0x3e209b(0x265)]() &&
                [_0x3e209b(0x22a), _0x3e209b(0x22c)]['includes'](BattleManager['_phase'])
            );
        else {
            const _0x43991c = SceneManager[_0x3e209b(0x2af)];
            return (
                _0x43991c &&
                _0x43991c['_actor'] &&
                _0x43991c[_0x3e209b(0x266)] &&
                _0x43991c[_0x3e209b(0x266)]() &&
                DataManager['isSkill'](_0x43991c[_0x3e209b(0x266)]())
            );
        }
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x2a2)] =
        Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x204)]),
    (Game_BattlerBase['prototype'][_0x4b1e51(0x204)] = function (_0x263a62) {
        const _0x3c3618 = _0x4b1e51;
        VisuMZ[_0x3c3618(0x224)][_0x3c3618(0x2a2)][_0x3c3618(0x2b8)](this, _0x263a62);
        if (!this[_0x3c3618(0x2ae)]()) return;
        const _0x3c6b10 = $dataStates[_0x263a62],
            _0x4a3fb5 = this[_0x3c3618(0x263)](_0x3c6b10)[_0x3c3618(0x1ff)]()[_0x3c3618(0x2be)]();
        if (![_0x3c3618(0x213), 'add', _0x3c3618(0x299)][_0x3c3618(0x20f)](_0x4a3fb5)) return;
        const _0x3d4c62 = SceneManager[_0x3c3618(0x272)]()
                ? BattleManager[_0x3c3618(0x2a8)]
                : SceneManager[_0x3c3618(0x2af)]['actor'](),
            _0x1fae61 = SceneManager[_0x3c3618(0x272)]()
                ? BattleManager['_action']['item']()
                : SceneManager[_0x3c3618(0x2af)][_0x3c3618(0x266)](),
            _0x18f51f = _0x3d4c62[_0x3c3618(0x262)](_0x1fae61['id']);
        if (_0x18f51f <= 0x0) return;
        const _0x340cff = DataManager[_0x3c3618(0x1fa)](_0x1fae61['id']);
        if (_0x340cff[_0x3c3618(0x25c)] !== 0x0) {
            if (_0x4a3fb5 === _0x3c3618(0x299)) {
                const _0x1f3773 = $dataStates[_0x263a62],
                    _0x5f5bec =
                        0x1 +
                        Math[_0x3c3618(0x2b4)](
                            _0x1f3773[_0x3c3618(0x28a)] - _0x1f3773['minTurns'],
                            0x0
                        ),
                    _0x551b21 =
                        _0x1f3773[_0x3c3618(0x23c)] +
                        Math['randomInt'](_0x5f5bec) +
                        _0x340cff[_0x3c3618(0x25c)] * _0x18f51f;
                this['_stateTurns'][_0x263a62] = Math[_0x3c3618(0x2b4)](
                    this['_stateTurns'][_0x263a62],
                    _0x551b21
                );
            } else this[_0x3c3618(0x254)][_0x263a62] += _0x340cff['stateTurn'] * _0x18f51f;
            this['_stateTurns'][_0x263a62] = Math[_0x3c3618(0x2b4)](
                this[_0x3c3618(0x254)][_0x263a62],
                0x0
            );
        }
    }),
    (VisuMZ[_0x4b1e51(0x224)]['Game_BattlerBase_overwriteBuffTurns'] =
        Game_BattlerBase['prototype'][_0x4b1e51(0x227)]),
    (Game_BattlerBase[_0x4b1e51(0x2c1)]['overwriteBuffTurns'] = function (_0xbfce45, _0x31b914) {
        const _0x3bafea = _0x4b1e51;
        VisuMZ[_0x3bafea(0x224)][_0x3bafea(0x246)]['call'](this, _0xbfce45, _0x31b914);
        if (!this[_0x3bafea(0x2ae)]()) return;
        const _0x5518ac =
            VisuMZ[_0x3bafea(0x222)][_0x3bafea(0x281)][_0x3bafea(0x26d)][_0x3bafea(0x22e)];
        if (![_0x3bafea(0x213), 'add', _0x3bafea(0x299)][_0x3bafea(0x20f)](_0x5518ac)) return;
        const _0x8a0c9d = SceneManager[_0x3bafea(0x272)]()
                ? BattleManager['_subject']
                : SceneManager['_scene'][_0x3bafea(0x1fb)](),
            _0x3038d3 = SceneManager[_0x3bafea(0x272)]()
                ? BattleManager[_0x3bafea(0x22d)]['item']()
                : SceneManager[_0x3bafea(0x2af)][_0x3bafea(0x266)](),
            _0x3c63fd = _0x8a0c9d[_0x3bafea(0x262)](_0x3038d3['id']);
        if (_0x3c63fd <= 0x0) return;
        const _0x7488ac = DataManager[_0x3bafea(0x1fa)](_0x3038d3['id']);
        if (this[_0x3bafea(0x2b7)](_0xbfce45) && _0x7488ac[_0x3bafea(0x2ce)] !== 0x0) {
            if (_0x5518ac === _0x3bafea(0x299)) {
                const _0x4f8608 = _0x31b914 + _0x7488ac['buffTurn'] * _0x3c63fd;
                this[_0x3bafea(0x225)][_0xbfce45] = Math[_0x3bafea(0x2b4)](
                    this[_0x3bafea(0x225)][_0xbfce45],
                    _0x4f8608
                );
            } else this['_buffTurns'][_0xbfce45] += _0x7488ac['buffTurn'] * _0x3c63fd;
        }
        if (this[_0x3bafea(0x2bf)](_0xbfce45) && _0x7488ac[_0x3bafea(0x223)] !== 0x0) {
            if (_0x5518ac === _0x3bafea(0x299)) {
                const _0x3a0913 = _0x31b914 + _0x7488ac[_0x3bafea(0x223)] * _0x3c63fd;
                this[_0x3bafea(0x225)][_0xbfce45] = Math[_0x3bafea(0x2b4)](
                    this['_buffTurns'][_0xbfce45],
                    _0x3a0913
                );
            } else this[_0x3bafea(0x225)][_0xbfce45] += _0x7488ac[_0x3bafea(0x223)] * _0x3c63fd;
        }
        const _0x400364 =
            VisuMZ['SkillsStatesCore'][_0x3bafea(0x281)][_0x3bafea(0x26d)][_0x3bafea(0x29d)];
        this[_0x3bafea(0x225)][_0xbfce45] = this[_0x3bafea(0x225)][_0xbfce45][_0x3bafea(0x2b3)](
            0x0,
            _0x400364
        );
    }),
    (Game_BattlerBase[_0x4b1e51(0x2c1)][_0x4b1e51(0x26c)] = function (_0x3efbe3, _0x3b0566) {
        const _0x57f07d = _0x4b1e51;
        if (_0x3b0566 <= 0x0) return _0x3b0566;
        const _0x4a796b = this['skillMasteryLevel'](_0x3efbe3['id']);
        if (_0x4a796b <= 0x0) return _0x3b0566;
        const _0x419c30 = DataManager[_0x57f07d(0x1fa)](_0x3efbe3['id']);
        _0x419c30['cooldown'] !== 0x0 && (_0x3b0566 += _0x4a796b * _0x419c30['cooldown']);
        const _0x4f3078 = Game_BattlerBase[_0x57f07d(0x2cd)]['allowCooldownModToZero'] ? 0x0 : 0x1;
        return Math['max'](_0x3b0566, _0x4f3078);
    }));
var $actorSkillMasteryLevel = function (_0x28108b, _0x79edf) {
        const _0x549b11 = $gameActors['actor'](_0x28108b);
        if (!_0x549b11) return 0x0;
        return _0x549b11['skillMasteryLevel'](_0x79edf);
    },
    $actorSkillMasteryExp = function (_0x1c667a, _0x1571c9) {
        const _0x548fba = _0x4b1e51,
            _0x59b5c7 = $gameActors[_0x548fba(0x1fb)](_0x1c667a);
        if (!_0x59b5c7) return 0x0;
        return _0x59b5c7['skillMasteryExp'](_0x1571c9);
    },
    $enemySkillMasteryLevel = function (_0x6b05db, _0x2e975a) {
        const _0x4ff880 = _0x4b1e51;
        if (!$gameParty['inBattle']()) return 0x0;
        const _0x8958b1 = $gameTroop[_0x4ff880(0x211)]()[_0x6b05db];
        if (!_0x8958b1) return 0x0;
        return _0x8958b1[_0x4ff880(0x262)](_0x2e975a);
    },
    $enemySkillMasteryExp = function (_0x143da3, _0x5cc58a) {
        const _0x33d739 = _0x4b1e51;
        if (!$gameParty[_0x33d739(0x282)]()) return 0x0;
        const _0x184ad9 = $gameTroop[_0x33d739(0x211)]()[enemyIndex];
        if (!_0x184ad9) return 0x0;
        return _0x184ad9['skillMasteryExp'](_0x5cc58a);
    };
function _0x4e03() {
    const _0x57f858 = [
        'growth',
        'reset',
        '945738kavCLl',
        'min',
        'SkillID',
        'gainSkillMasteryExp',
        'DefaultExpFormula',
        'PopupText',
        'BattleManager_setup',
        'note',
        'remove',
        'dmgChangePerLevelRate',
        'status',
        'playSkillMasteryLevelUp',
        'tpCostFlat',
        'ConvertParams',
        'SkillsStatesCore',
        'debuffTurn',
        'SkillMastery',
        '_buffTurns',
        'Lv%2\x20%1',
        'overwriteBuffTurns',
        'startActionSkillMastery',
        'subject',
        'action',
        'STRUCT',
        'custom',
        '_action',
        'ReapplyRules',
        'ARRAYJSON',
        'ARRAYFUNC',
        'gaugeStyle',
        'mpCostRate',
        'tpCostRate',
        'Window_Base_drawItemName',
        '_list',
        'toUpperCase',
        'skillMasteryExp',
        'volume',
        '125389KfXnQw',
        'AnimationID',
        '\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Arguments\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20user\x20=\x20arguments[0];\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20skill\x20=\x20arguments[1];\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20targetLevel\x20=\x20arguments[2];\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Declare\x20Variables\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20a\x20=\x20user;\x0a\x20\x20\x20\x20\x20\x20\x20\x20const\x20b\x20=\x20user;\x0a\x20\x20\x20\x20\x20\x20\x20\x20let\x20exp\x20=\x201000000;\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Process\x20Code\x0a\x20\x20\x20\x20\x20\x20\x20\x20try\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20%1\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x20catch\x20(e)\x20{\x0a\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20\x20if\x20($gameTemp.isPlaytest())\x20console.log(e);\x0a\x20\x20\x20\x20\x20\x20\x20\x20}\x0a\x0a\x20\x20\x20\x20\x20\x20\x20\x20//\x20Return\x20Exp\x0a\x20\x20\x20\x20\x20\x20\x20\x20return\x20exp;\x0a\x20\x20\x20\x20',
        'minTurns',
        'gaugeColor2',
        'NUM',
        'pan',
        'skillMasteryExpNeeded',
        'VisuMZ_1_BattleCore',
        'Exp',
        'Game_Battler_useItem',
        'initSkillMasteries',
        'setupStartingSkillMasteries',
        'Game_BattlerBase_overwriteBuffTurns',
        'costFlat',
        'preventExpForGuard',
        'endAction',
        'currentAction',
        'startAction',
        'displaySkillMasteryLevelUpEffect',
        'drawGauge',
        'Mirror',
        'Window_ActorCommand_commandStyleCheck',
        'Game_BattlerBase_initMembers',
        'GetGaugeHeight',
        'addEquipBattleSkillsMarkers',
        'Window_ActorCommand_addSingleSkillCommand',
        '_stateTurns',
        'showBattleCommandLvName',
        'Game_Action_applyVariance',
        'constructor',
        '909580uQAFMN',
        'hpCostFlat',
        'endActionSkillMastery',
        'General',
        'stateTurn',
        'VisualGaugeStyles',
        'itemLineRect',
        'checkSkillMasteryLevelUp',
        'push',
        'FUNC',
        'skillMasteryLevel',
        'getStateReapplyRulings',
        'applyVariance',
        'isSkill',
        'item',
        '92IsZXLK',
        'exp\x20=\x20targetLevel\x20*\x203',
        'BattleManager_startAction',
        'updateSkillMasteryVariable',
        'format',
        'applyMasteryEffectCooldownTurns',
        'Buffs',
        'ARRAYNUM',
        'ChangeSkillName',
        '_maxValueSegment',
        'floor',
        'isSceneBattle',
        'getColor',
        'FlashColor',
        'in\x20order\x20for\x20VisuMZ_3_SkillMastery\x20to\x20work.',
        'RegExp',
        'VisuMZ_1_SkillsStatesCore',
        '_skillMasteryLevelFormula',
        'AutoVariableID',
        'costChangePerLevelFlat',
        'pitch',
        'startMasteries',
        'setup',
        'commandSymbol',
        'skillMasteryMaxLevel',
        'requestFauxAnimation',
        'Settings',
        'inBattle',
        '_actor',
        'TextColor',
        '_skillMasteryLevels',
        'costChangePerLevelRate',
        'cooldown',
        'initMembers',
        'dmgRate',
        'maxTurns',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'width',
        'drawSkillMastery',
        '63dyjqzN',
        'description',
        'commandStyleCheck',
        '9507xbJoxq',
        'setSkillMasteryExp',
        'DefaultFormula',
        'Window_EquipBattleSkillList_addEquipBattleSkillsMarkers',
        '2077509CylXZb',
        'return\x200',
        'isActor',
        'debuffTurnChangePerLevel',
        'greater',
        'targetVariableID',
        'maxGaugeWidth',
        'useItem',
        'MaxTurns',
        'drawItem',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'dmgFlat',
        'attackSkillId',
        'Game_BattlerBase_resetStateCounts',
        'expFormula',
        '_skillMasteryExp',
        'drawEquipBattleSkillName',
        'Game_Actor_setup',
        'maxLevel',
        '_subject',
        'Game_BattlerBase_adjustSkillCost',
        'guardSkillId',
        'JSON',
        'ext',
        'Effect',
        'canApplySkillMasteryEffect',
        '_scene',
        '_masterySubject',
        'parameters',
        'ARRAYEVAL',
        'clamp',
        'max',
        'ActorSetSkillMasteryExp',
        '_skillMasteryMaxLevel',
        'isBuffAffected',
        'call',
        'gaugeBackColor',
        'registerCommand',
        'allowCostModToZero',
        'gaugeColor1',
        '_originalNamePreMastery',
        'trim',
        'isDebuffAffected',
        'parse',
        'prototype',
        'mpCostFlat',
        'exit',
        'masteryFmt',
        'version',
        'EVAL',
        'name',
        'Window_Base_drawEquipBattleSkillName',
        'addSingleSkillCommand',
        'Window_ActorCommand_drawItem',
        'match',
        '335288qaEoMj',
        'SKILL_MASTERY',
        'buffTurn',
        'singleSkill',
        'costRate',
        'preventCooldownModToZero',
        '_masterySkill',
        'lineHeight',
        '1263165biheyp',
        '_skillMasteryLevelEffectData',
        'ARRAYSTRUCT',
        'setSkillMasteryLevel',
        'skillMasteryLevelEffectData',
        'actor',
        'VisuMZ_1_SkillsStatesCore\x20needs\x20to\x20be\x20updated\x20',
        'Game_Enemy_setup',
        'Level',
        'toLowerCase',
        'skillMasteryLevelFormula',
        'ARRAYSTR',
        'map',
        'hpCostRate',
        'resetStateCounts',
        'preventExpForAttack',
        'adjustSkillCost',
        'showBattleCommandExpGauge',
        'drawVisualStyleGauge',
        'iconText',
        'filter',
        'iconWidth',
        'isPlaytest',
        'VisuMZ_2_EquipBattleSkills',
        'ActorIDs',
        'includes',
        'STR',
        'members',
    ];
    _0x4e03 = function () {
        return _0x57f858;
    };
    return _0x4e03();
}
((VisuMZ[_0x4b1e51(0x224)]['Game_Actor_setup'] = Game_Actor[_0x4b1e51(0x2c1)]['setup']),
    (Game_Actor[_0x4b1e51(0x2c1)][_0x4b1e51(0x27d)] = function (_0x3e09cd) {
        const _0x5ef5c8 = _0x4b1e51;
        (VisuMZ[_0x5ef5c8(0x224)][_0x5ef5c8(0x2a6)][_0x5ef5c8(0x2b8)](this, _0x3e09cd),
            this[_0x5ef5c8(0x245)]());
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x1fd)] = Game_Enemy['prototype']['setup']),
    (Game_Enemy[_0x4b1e51(0x2c1)][_0x4b1e51(0x27d)] = function (_0x33de12, _0x453a41, _0x3530f5) {
        const _0x4c1599 = _0x4b1e51;
        (VisuMZ[_0x4c1599(0x224)][_0x4c1599(0x1fd)][_0x4c1599(0x2b8)](
            this,
            _0x33de12,
            _0x453a41,
            _0x3530f5
        ),
            this['setupStartingSkillMasteries']());
    }),
    (Window_Base['SKILL_MASTERY'] = {
        masteryFmt:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)]['General'][_0x4b1e51(0x2c4)] ??
            _0x4b1e51(0x226),
        maxGaugeWidth:
            VisuMZ[_0x4b1e51(0x224)]['Settings'][_0x4b1e51(0x25b)][_0x4b1e51(0x29b)] ?? 0x180,
        gaugeColor1:
            VisuMZ[_0x4b1e51(0x224)]['Settings'][_0x4b1e51(0x25b)][_0x4b1e51(0x2bc)] ?? 0xc,
        gaugeColor2:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x23d)] ?? 0x4,
        gaugeStyle:
            VisuMZ[_0x4b1e51(0x224)]['Settings'][_0x4b1e51(0x25b)]['gaugeStyle'] ??
            _0x4b1e51(0x212),
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x234)] = Window_Base[_0x4b1e51(0x2c1)]['drawItemName']),
    (Window_Base['prototype']['drawItemName'] = function (
        _0x2dbecb,
        _0x551ca4,
        _0x541fd2,
        _0x162e0d
    ) {
        const _0x59f2b5 = _0x4b1e51;
        (this[_0x59f2b5(0x283)] &&
            DataManager['isSkill'](_0x2dbecb) &&
            ((this[_0x59f2b5(0x2bd)] = VisuMZ[_0x59f2b5(0x224)]['ChangeSkillName'](
                this[_0x59f2b5(0x283)],
                _0x2dbecb
            )),
            this[_0x59f2b5(0x28d)](_0x2dbecb, _0x551ca4, _0x541fd2, _0x162e0d)),
            VisuMZ[_0x59f2b5(0x224)][_0x59f2b5(0x234)]['call'](
                this,
                _0x2dbecb,
                _0x551ca4,
                _0x541fd2,
                _0x162e0d
            ),
            this['_originalNamePreMastery'] !== undefined &&
                ((_0x2dbecb[_0x59f2b5(0x2c7)] = this[_0x59f2b5(0x2bd)]),
                (this[_0x59f2b5(0x2bd)] = undefined)));
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x26f)] = function (_0x4c9b52, _0x9a0ac0) {
        const _0x81ea43 = _0x4b1e51,
            _0x2c9377 = _0x9a0ac0[_0x81ea43(0x2c7)],
            _0x5e648e = _0x4c9b52[_0x81ea43(0x262)](_0x9a0ac0['id']);
        if (_0x5e648e > 0x0) {
            const _0x8b44b = Window_Base['SKILL_MASTERY'][_0x81ea43(0x2c4)];
            _0x9a0ac0[_0x81ea43(0x2c7)] = _0x8b44b[_0x81ea43(0x26b)](_0x2c9377, _0x5e648e);
        }
        return _0x2c9377;
    }),
    (Window_Base[_0x4b1e51(0x2c1)][_0x4b1e51(0x28d)] = function (
        _0x37ef69,
        _0x1ff112,
        _0x1a09ed,
        _0x5e5784
    ) {
        const _0x4bae25 = _0x4b1e51;
        if (!this[_0x4bae25(0x283)]) return;
        if (!_0x37ef69) return;
        const _0x19d6b8 = Window_Base['SKILL_MASTERY'];
        ((_0x5e5784 = _0x5e5784 || 0xa8),
            (_0x1ff112 += ImageManager[_0x4bae25(0x20b)] + 0x4),
            (_0x5e5784 -= ImageManager['iconWidth'] + 0x4),
            (_0x5e5784 = Math[_0x4bae25(0x215)](_0x5e5784, _0x19d6b8[_0x4bae25(0x29b)])));
        const _0x330de5 = ColorManager['getColor'](_0x19d6b8[_0x4bae25(0x2bc)]),
            _0x5eef7e = ColorManager[_0x4bae25(0x273)](_0x19d6b8[_0x4bae25(0x23d)]),
            _0x5e25e3 = _0x19d6b8[_0x4bae25(0x231)],
            _0x4a595b = this[_0x4bae25(0x283)][_0x4bae25(0x262)](_0x37ef69['id']);
        let _0x3f8814 = 0x0;
        if (_0x4a595b >= DataManager['skillMasteryMaxLevel'](_0x37ef69['id'])) _0x3f8814 = 0x1;
        else {
            const _0x25b62e = this[_0x4bae25(0x283)]['skillMasteryExp'](_0x37ef69['id']),
                _0xeee47e = this[_0x4bae25(0x283)][_0x4bae25(0x240)](
                    _0x37ef69['id'],
                    _0x4a595b + 0x1
                );
            _0x3f8814 = _0x25b62e / _0xeee47e;
        }
        if (Imported['VisuMZ_3_VisualGaugeStyles']) {
            const _0x2b7f51 = (VisuMZ[_0x4bae25(0x25d)][_0x4bae25(0x251)](_0x5e25e3) ?? 0xc)[
                    _0x4bae25(0x2b3)
                ](0x1, 0x20),
                _0xfb5c5f = _0x1a09ed + this[_0x4bae25(0x1f5)]() - _0x2b7f51 - 0x2,
                _0x223cba = ColorManager[_0x4bae25(0x2b9)]();
            ((VisuMZ[_0x4bae25(0x25d)][_0x4bae25(0x270)] = this[_0x4bae25(0x283)][_0x4bae25(0x240)](
                _0x37ef69['id'],
                _0x4a595b + 0x1
            )),
                this['contents'][_0x4bae25(0x208)](
                    _0x5e25e3,
                    _0x1ff112,
                    _0xfb5c5f,
                    _0x5e5784,
                    _0x2b7f51,
                    _0x3f8814,
                    _0x223cba,
                    _0x330de5,
                    _0x5eef7e
                ));
        } else
            this[_0x4bae25(0x24d)](
                _0x1ff112,
                _0x1a09ed,
                _0x5e5784,
                _0x3f8814,
                _0x330de5,
                _0x5eef7e
            );
    }),
    (VisuMZ['SkillMastery']['Window_Base_drawEquipBattleSkillName'] =
        Window_Base[_0x4b1e51(0x2c1)][_0x4b1e51(0x2a5)]),
    (Window_Base[_0x4b1e51(0x2c1)][_0x4b1e51(0x2a5)] = function (
        _0x4b2bd8,
        _0x30b7b4,
        _0x960f59,
        _0xd56488
    ) {
        const _0x3591e9 = _0x4b1e51;
        (this[_0x3591e9(0x283)] &&
            DataManager['isSkill'](_0x4b2bd8) &&
            (this[_0x3591e9(0x257)] !== Window_EquipBattleSkillList
                ? (this[_0x3591e9(0x2bd)] = VisuMZ[_0x3591e9(0x224)]['ChangeSkillName'](
                      this[_0x3591e9(0x283)],
                      _0x4b2bd8
                  ))
                : (this[_0x3591e9(0x2bd)] = _0x4b2bd8[_0x3591e9(0x2c7)]),
            this[_0x3591e9(0x28d)](_0x4b2bd8, _0x30b7b4, _0x960f59, _0xd56488)),
            VisuMZ[_0x3591e9(0x224)][_0x3591e9(0x2c8)][_0x3591e9(0x2b8)](
                this,
                _0x4b2bd8,
                _0x30b7b4,
                _0x960f59,
                _0xd56488
            ),
            this[_0x3591e9(0x2bd)] !== undefined &&
                ((_0x4b2bd8[_0x3591e9(0x2c7)] = this['_originalNamePreMastery']),
                (this[_0x3591e9(0x2bd)] = undefined)));
    }),
    (Window_ActorCommand['SKILL_MASTERY'] = {
        showBattleCommandLvName:
            VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x255)] ?? ![],
        showBattleCommandExpGauge:
            VisuMZ['SkillMastery'][_0x4b1e51(0x281)][_0x4b1e51(0x25b)][_0x4b1e51(0x207)] ?? ![],
    }),
    (VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x253)] =
        Window_ActorCommand[_0x4b1e51(0x2c1)][_0x4b1e51(0x2c9)]),
    (Window_ActorCommand['prototype'][_0x4b1e51(0x2c9)] = function (_0x177015) {
        const _0x524ba0 = _0x4b1e51;
        (Window_ActorCommand[_0x524ba0(0x2cd)][_0x524ba0(0x255)] &&
            (this['_originalNamePreMastery'] = VisuMZ[_0x524ba0(0x224)][_0x524ba0(0x26f)](
                this[_0x524ba0(0x283)],
                _0x177015
            )),
            VisuMZ[_0x524ba0(0x224)]['Window_ActorCommand_addSingleSkillCommand'][_0x524ba0(0x2b8)](
                this,
                _0x177015
            ),
            this['_originalNamePreMastery'] !== undefined &&
                ((_0x177015[_0x524ba0(0x2c7)] = this[_0x524ba0(0x2bd)]),
                (this[_0x524ba0(0x2bd)] = undefined)));
    }),
    (VisuMZ[_0x4b1e51(0x224)]['Window_ActorCommand_drawItem'] =
        Window_ActorCommand[_0x4b1e51(0x2c1)][_0x4b1e51(0x29e)]),
    (Window_ActorCommand[_0x4b1e51(0x2c1)]['drawItem'] = function (_0x20d13f) {
        const _0x56582f = _0x4b1e51,
            _0x526fa2 = this['commandSymbol'](_0x20d13f);
        if (
            _0x526fa2 === _0x56582f(0x1f1) &&
            Window_ActorCommand[_0x56582f(0x2cd)][_0x56582f(0x207)]
        ) {
            const _0x2718da = this[_0x56582f(0x25e)](_0x20d13f),
                _0x429698 = this[_0x56582f(0x235)][_0x20d13f][_0x56582f(0x2ac)] || 0x0,
                _0x594f95 = $dataSkills[_0x429698];
            if (_0x594f95)
                this['drawSkillMastery'](
                    _0x594f95,
                    _0x2718da['x'],
                    _0x2718da['y'],
                    _0x2718da[_0x56582f(0x28c)]
                );
        }
        VisuMZ[_0x56582f(0x224)][_0x56582f(0x2ca)][_0x56582f(0x2b8)](this, _0x20d13f);
    }),
    (VisuMZ['SkillMastery'][_0x4b1e51(0x24f)] =
        Window_ActorCommand[_0x4b1e51(0x2c1)][_0x4b1e51(0x290)]),
    (Window_ActorCommand[_0x4b1e51(0x2c1)][_0x4b1e51(0x290)] = function (_0x3ce530) {
        const _0x2a7858 = _0x4b1e51;
        if (!this[_0x2a7858(0x235)][_0x3ce530]) return _0x2a7858(0x209);
        const _0x154b3b = this[_0x2a7858(0x27e)](_0x3ce530);
        if (_0x154b3b === 'singleSkill' && Window_ActorCommand[_0x2a7858(0x2cd)][_0x2a7858(0x207)])
            return 'iconText';
        return VisuMZ[_0x2a7858(0x224)][_0x2a7858(0x24f)][_0x2a7858(0x2b8)](this, _0x3ce530);
    }));
Imported[_0x4b1e51(0x20d)] &&
    ((VisuMZ[_0x4b1e51(0x224)][_0x4b1e51(0x294)] =
        Window_EquipBattleSkillList[_0x4b1e51(0x2c1)][_0x4b1e51(0x252)]),
    (Window_EquipBattleSkillList['prototype'][_0x4b1e51(0x252)] = function (_0xed42ba) {
        const _0x5f11cd = _0x4b1e51;
        (VisuMZ[_0x5f11cd(0x224)]['ChangeSkillName'](this[_0x5f11cd(0x283)], _0xed42ba),
            VisuMZ[_0x5f11cd(0x224)][_0x5f11cd(0x294)][_0x5f11cd(0x2b8)](this, _0xed42ba));
    }));
