//=============================================================================
// VisuStella MZ - Skills & States Core
// VisuMZ_1_SkillsStatesCore.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_1_SkillsStatesCore = true;
var VisuMZ = VisuMZ || {};
VisuMZ.SkillsStatesCore = VisuMZ.SkillsStatesCore || {};
VisuMZ.SkillsStatesCore.version = 1.5;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 1] [Version 1.50] [SkillsStatesCore]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Skills_and_States_Core_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * The Skills & States Core plugin extends and builds upon the functionality of
 * RPG Maker MZ's inherent skill, state, and buff functionalities and allows
 * game devs to customize its various aspects.
 *
 * Features include all (but not limited to) the following:
 *
 * * Assigning multiple Skill Types to Skills.
 * * Making custom Skill Cost Types (such as HP, Gold, and Items).
 * * Allowing Skill Costs to become percentile-based or dynamic either directly
 *   through the Skills themselves or through trait-like notetags.
 * * Replacing gauges for different classes to display different types of
 *   Skill Cost Type resources.
 * * Hiding/Showing and enabling/disabling skills based on switches, learned
 *   skills, and code.
 * * Setting rulings for states, including if they're cleared upon death, how
 *   reapplying the state affects their turn count, and more.
 * * Allowing states to be categorized and affected by categories, too.
 * * Displaying turn counts on states drawn in the window or on sprites.
 * * Manipulation of state, buff, and debuff turns through skill and item
 *   effect notetags.
 * * Create custom damage over time state calculations through notetags.
 * * Allow database objects to apply passive states to its user.
 * * Passive states can have conditions before they become active as well.
 * * Updated Skill Menu Scene layout to fit more modern appearances.
 * * Added bonus if Items & Equips Core is installed to utilize the Shop Status
 *   Window to display skill data inside the Skill Menu.
 * * Control over various aspects of the Skill Menu Scene.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 1 ------
 *
 * This plugin is a Tier 1 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Action End Removal for States
 *
 * - If your Plugin Parameter settings for "Action End Update" are enabled,
 * then "Action End" has been updated so that it actually applies per action
 * used instead of just being at the start of a battler's action set.
 *
 * - However, there are side effects to this: if a state has the "Cannot Move"
 * restriction along with the "Action End" removal timing, then unsurprisingly,
 * the state will never wear off because it's now based on actual actions
 * ending. To offset this and remove confusion, "Action End" auto-removal
 * timings for states with "Cannot Move" restrictions will be turned into
 * "Turn End" auto-removal timings while the "Action End Update" is enabled.
 *
 * - This automatic change won't make it behave like an "Action End" removal
 * timing would, but it's better than completely softlocking a battler.
 *
 * EXAMPLE:
 *
 * - The new state: "Fiery Blade" will allow the affected battler to deal fire
 * elemental damage. With Action End, this means for 5 actions, those attacks
 * will deal fire damage.
 *
 * - This means that if no action is taken, due to a status effect like "Sleep"
 * or "Stun", then the duration count will not decrease.
 *
 * - On the flip side, if the battler performs multiple actions a turn, then
 * the duration count drops faster because more actions have been spent.
 *
 * - However, if this "Fiery Blade" state was using Turn End instead, it will
 * have its duration reduced by 1 each turn, regardless of "Sleep" or "Stun"
 * states, and regardless of how many actions are performed each turn.
 *
 * ---
 *
 * Buff & Debuff Level Management
 *
 * - In RPG Maker MZ, buffs and debuffs when applied to one another will shift
 * the buff modifier level up or down. This plugin will add an extra change to
 * the mechanic by making it so that once the buff modifier level reaches a
 * neutral point, the buff or debuff is removed altogether and resets the buff
 * and debuff turn counter for better accuracy.
 *
 * ---
 *
 * Skill Costs
 *
 * - In RPG Maker MZ, skill costs used to be hard-coded. Now, all Skill Cost
 * Types are now moved to the Plugin Parameters, including MP and TP. This
 * means that from payment to checking for them, it's all done through the
 * options available.
 *
 * - By default in RPG Maker MZ, displayed skill costs would only display only
 * one type: TP if available, then MP. If a skill costs both TP and MP, then
 * only TP was displayed. This plugin changes that aspect by displaying all the
 * cost types available in order of the Plugin Parameter Skill Cost Types.
 *
 * - By default in RPG Maker MZ, displayed skill costs were only color-coded.
 * This plugin changes that aspect by displaying the Skill Cost Type's name
 * alongside the cost. This is to help color-blind players distinguish what
 * costs a skill has.
 *
 * ---
 *
 * Sprite Gauges
 *
 * - Sprite Gauges in RPG Maker MZ by default are hard-coded and only work for
 * HP, MP, TP, and Time (used for ATB). This plugin makes it possible for them
 * to be customized through the use of Plugin Parameters under the Skill Cost
 * Types and their related-JavaScript entries.
 *
 * ---
 *
 * State Displays
 *
 * - To put values onto states and display them separately from the state turns
 * you can use the following script calls.
 *
 *   battler.getStateDisplay(stateId)
 *   - This returns whatever value is stored for the specified battler under
 *     that specific state value.
 *   - If there is no value to be returned it will return an empty string.
 *
 *   battler.setStateDisplay(stateId, value)
 *   - This sets the display for the battler's specific state to whatever you
 *     declared as the value.
 *   - The value is best used as a number or a string.
 *
 *   battler.clearStateDisplay(stateId)
 *   - This clears the display for the battler's specific state.
 *   - In short, this sets the stored display value to an empty string.
 *
 * ---
 *
 * Window Functions Moved
 *
 * - Some functions found in RPG Maker MZ's default code for Window_StatusBase
 * and Window_SkillList are now moved to Window_Base to make the functions
 * available throughout all windows for usage.
 *
 * ---
 *
 * ============================================================================
 * Slip Damage Popup Clarification
 * ============================================================================
 *
 * Slip Damage popups only show one popup for HP, MP, and TP each and it is the
 * grand total of all the states and effects combined regardless of the number
 * of states and effects on a battler. This is how it is in vanilla RPG Maker
 * MZ and this is how we intend for it to be with the VisuStella MZ library.
 *
 * This is NOT a bug!
 *
 * The reason we are not changing this is because it does not properly relay
 * information to the player accurately. When multiple popups appear, players
 * only have roughly a second and a half to calculate it all for any form of
 * information takeaway. We feel it is better suited for the player's overall
 * convenience to show a cummulative change and steer the experience towards a
 * more positive one.
 *
 * ============================================================================
 * Passive State Clarification
 * ============================================================================
 *
 * This section will explain various misconceptions regarding passive states.
 * No, passive states do not work the same way as states code-wise. Yes, they
 * use the same effects as states mechanically, but there are differences.
 *
 * ---
 *
 * For those using the code "a.isStateAffected(10)" to check if a target is
 * affected by a state or not, this does NOT check passive states. This only
 * checks for states that were directly applied to the target.
 *
 * This is NOT a bug.
 *
 * Instead, use "a.states().includes($dataStates[10])" to check for them. This
 * code will search for both directly applied states and passive states alike.
 *
 * ---
 *
 * As passive states are NOT considered directly applied to, they do NOT match
 * a Conditional Branch's state check as well. The Conditional Branch effect
 * checks for an affected state.
 *
 * ---
 *
 * Because passive states are NOT directly applied to a battler, the functions
 * of "addNewState", "addState", "eraseState", "removeState" do NOT apply to
 * passive states either. This means that any of the related JS notetags tied
 * to those functions will not occur either.
 *
 * ---
 *
 * Why are passive states not considered affected by? Let's look at it
 * differently. There are two ways to grant skills to actors. They can acquire
 * skills by levels/items/events or they can equip gear that temporarily grants
 * the skill in question.
 *
 * Learning the skill is direct. Temporarily granting the skill is indirect.
 * These two factors have mechanical importance and require differentiation.
 *
 * Regular states and passive states are the same way. Regular states are
 * directly applied, therefore, need to be distinguished in order for things
 * like state turns and steps, removal conditionals, and similar to matter at
 * all. Passive states are indirect and are therefore, unaffected by state
 * turns, steps, and removal conditions. These mechanical differences are
 * important for how RPG Maker works.
 *
 * ---
 *
 * Once again, it is NOT a bug that when using "a.isStateAffected(10)" to
 * check if a target has a passive state will return false.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * === General Skill Notetags ===
 *
 * The following are general notetags that are skill-related.
 *
 * ---
 *
 * <Skill Type: x>
 * <Skill Types: x,x,x>
 *
 * <Skill Type: name>
 * <Skill Types: name, name, name>
 *
 * - Used for: Skill Notetags
 * - Marks the skill to have multiple Skill Types, meaning they would appear
 *   under different skill types without needing to create duplicate skills.
 * - Replace 'x' with a number value representing the Skill Type's ID.
 * - If using 'name' notetag variant, replace 'name' with the Skill Type(s)
 *   name desired to be added.
 *
 * ---
 *
 * <List Name: name>
 *
 * - Used for: Skill Notetags
 * - Makes the name of the skill appear different when show in the skill list.
 * - Using \V[x] as a part of the name will display that variable.
 *
 * ---
 *
 * <ID Sort Priority: x>
 *
 * - Used for: Skill Notetags
 * - Used for Scene_Skill.
 * - Changes sorting priority by ID for skills to 'x'.
 *   - Default priority level is '50'.
 * - Skills with higher priority values will be sorted higher up on the list
 *   while lower values will be lower on the list.
 *
 * ---
 *
 * === Skill Cost Notetags ===
 *
 * The following are notetags that can be used to adjust skill costs. Some of
 * these notetags are added through the Plugin Parameter: Skill Cost Types and
 * can be altered there. This also means that some of these notetags can have
 * their functionality altered and/or removed.
 *
 * ---
 *
 * <type Cost: x>
 * <type Cost: x%>
 *
 * - Used for: Skill Notetags
 * - These notetags are used to designate costs of custom or already existing
 *   types that cannot be made by the Database Editor.
 * - Replace 'type' with a resource type. Existing ones found in the Plugin
 *   Parameters are 'HP', 'MP', 'TP', 'Gold', and 'Potion'. More can be added.
 * - Replace 'x' with a number value to determine the exact type cost value.
 *   This lets you bypass the Database Editor's limit of 9,999 MP and 100 TP.
 * - The 'x%' version is replaced with a percentile value to determine a cost
 *   equal to a % of the type's maximum quantity limit.
 * - Functionality for these notetags can be altered in the Plugin Parameters.
 *
 * Examples:
 *   <HP Cost: 500>
 *   <MP Cost: 25%>
 *   <Gold Cost: 3000>
 *   <Potion Cost: 5>
 *
 * ---
 *
 * <type Cost Max: x>
 * <type Cost Min: x>
 *
 * - Used for: Skill Notetags
 * - These notetags are used to ensure conditional and % costs don't become too
 *   large or too small.
 * - Replace 'type' with a resource type. Existing ones found in the Plugin
 *   Parameters are 'HP', 'MP', 'TP', 'Gold', and 'Potion'. More can be added.
 * - Replace 'x' with a number value to determine the maximum or minimum values
 *   that the cost can be.
 * - Functionality for these notetags can be altered in the Plugin Parameters.
 *
 * Examples:
 *   <HP Cost Max: 1500>
 *   <MP Cost Min: 5>
 *   <Gold Cost Max: 10000>
 *   <Potion Cost Min: 3>
 *
 * ---
 *
 * <type Cost: +x>
 * <type Cost: -x>
 *
 * <type Cost: x%>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - The related actor will raise/lower the cost of any skill that uses the
 *   'type' cost by a specified amount.
 * - Replace 'type' with a resource type. Existing ones found in the Plugin
 *   Parameters are 'HP', 'MP', 'TP', 'Gold', and 'Potion'. More can be added.
 * - For % notetag variant: Replace 'x' with a number value to determine the
 *   rate to adjust the Skill Cost Type by as a rate value. This is applied
 *   before <type Cost: +x> and <type Cost: -x> notetags.
 * - For + and - notetag variants: Replace 'x' with a number value to determine
 *   how much to adjust the Skill Cost Type by as a flat value. This is applied
 *   after <type Cost: x%> notetags.
 * - Functionality for these notetags can be altered in the Plugin Parameters.
 *
 * Examples:
 *   <HP Cost: +20>
 *   <MP Cost: -10>
 *   <Gold Cost: 50%>
 *   <Potion Cost: 200%>
 *
 * ---
 *
 * <Custom Cost Text>
 *  text
 * </Custom Cost Text>
 *
 * - Used for: Skill Notetags
 * - Allows you to insert custom text into the skill's cost area towards the
 *   end of the costs.
 * - Replace 'text' with the text you wish to display.
 * - Text codes may be used.
 *
 * ---
 *
 * === JavaScript Notetags: Skill Costs ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * determine any dynamic Skill Cost Types used for particular skills.
 *
 * ---
 *
 * <JS type Cost>
 *  code
 *  code
 *  cost = code;
 * </JS type Cost>
 *
 * - Used for: Skill Notetags
 * - Replace 'type' with a resource type. Existing ones found in the Plugin
 *   Parameters are 'HP', 'MP', 'TP', 'Gold', and 'Potion'. More can be added.
 * - Replace 'code' to determine the type 'cost' of the skill.
 * - Insert the final type cost into the 'cost' variable.
 * - The 'user' variable refers to the user about to perform the skill.
 * - The 'skill' variable refers to the skill being used.
 * - Functionality for the notetag can be altered in the Plugin Parameters.
 *
 * ---
 *
 * === Gauge Replacement Notetags ===
 *
 * Certain classes can have their gauges swapped out for other Skill Cost
 * Types. This is especially helpful for the classes that don't utilize those
 * Skill Cost Types. You can mix and match them however you want.
 *
 * ---
 *
 * <Replace HP Gauge: type>
 * <Replace MP Gauge: type>
 * <Replace TP Gauge: type>
 *
 * - Used for: Class Notetags
 * - Replaces the HP (1st), MP (2nd), or TP (3rd) gauge with a different Skill
 *   Cost Type.
 * - Replace 'type' with a resource type. Existing ones found in the Plugin
 *   Parameters are 'HP', 'MP', 'TP', 'Gold', and 'Potion'. More can be added.
 *   - Does not work with 'Item Cost', 'Weapon Cost', or 'Armor Cost'.
 * - Replace 'type' with 'none' to not display any gauges there.
 * - The <Replace TP Gauge: type> will require 'Display TP in Window' setting
 *   to be on in the Database > System 1 tab.
 * - Functionality for the notetags can be altered by changes made to the
 *   Skill & States Core Plugin Parameters.
 *
 * ---
 *
 * === Item Cost-Related Notetags ===
 *
 * ---
 *
 * <Item Cost: x name>
 * <Weapon Cost: x name>
 * <Armor Cost: x name>
 *
 * - Used for: Skill Notetags
 * - The skill will consume items, weapons, and/or armors in order to be used.
 *   - Even non-consumable items will be consumed.
 * - Replace 'x' with a number representing the respective item cost.
 * - Replace 'name' with text representing the respective item, weapon, or
 *   armor to be consumed.
 * - Insert multiples of this notetag to consume multiple items, weapons,
 *   and/or armors.
 * - Functionality for these notetags can be altered in the Plugin Parameters.
 *
 * Examples:
 *
 *   <Item Cost: 5 Magic Water>
 *   <Item Cost: 2 Antidote>
 *   <Weapon Cost: 1 Short Sword>
 *   <Armor Cost: 3 Cloth Armor>
 *
 * ---
 *
 * <Item Cost Max: x name>
 * <Item Cost Min: x name>
 *
 * <Weapon Cost Max: x name>
 * <Weapon Cost Min: x name>
 *
 * <Armor Cost Max: x name>
 * <Armor Cost Min: x name>
 *
 * - Used for: Skill Notetags
 * - Sets up a maximum/minimum cost for the item, weapon, armor type costs.
 * - Replace 'x' with a number representing the maximum or minimum cost.
 * - Replace 'name' with text representing the respective item, weapon, or
 *   armor to be consumed.
 *
 * Examples:
 *
 *   <Item Cost Max: 10 Magic Water>
 *   <Item Cost Min: 2 Antidote>
 *   <Weapon Cost Max: 3 Short Sword>
 *   <Armor Cost Min: 1 Cloth Armor>
 *
 * ---
 *
 * <Item Cost: +x name>
 * <Item Cost: -x name>
 *
 * <Weapon Cost: +x name>
 * <Weapon Cost: -x name>
 *
 * <Armor Cost: +x name>
 * <Armor Cost: -x name>
 *
 * <Item Cost: x% name>
 * <Weapon Cost: x% name>
 * <Armor Cost: x% name>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - The related actor will raise/lower the item, weapon, and/or armor costs of
 *   any skill that costs those items, weapons, and/or armors by x%.
 * - For % notetag variant: Replace 'x' with a number value to determine the
 *   rate to adjust the Skill Cost Type by as a rate value. This is applied
 *   before <type Cost: +x> and <type Cost: -x> notetags.
 * - For + and - notetag variants: Replace 'x' with a number value to determine
 *   how much to adjust the Skill Cost Type by as a flat value. This is applied
 *   after <type Cost: x%> notetags.
 * - Replace 'name' with text representing the respective item, weapon, or
 *   armor to be consumed.
 * - Insert multiples of this notetag to consume multiple items, weapons,
 *   and/or armors.
 * - Functionality for these notetags can be altered in the Plugin Parameters.
 *
 * Examples:
 *
 *   <Item Cost: +1 Magic Water>
 *   <Item Cost: -2 Antidote>
 *   <Weapon Cost: 50% Short Sword>
 *   <Armor Cost: 200% Cloth Armor>
 *
 * ---
 *
 * <Replace Item name1 Cost: name2>
 * <Replace Weapon name1 Cost: name2>
 * <Replace Armor name1 Cost: name2>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - The related actor will not consume 'name1' items, weapons, or armors.
 *   Instead, the cost will be redirected to 'name2' items, weapons, or armors.
 *   - Even non-consumable items will be consumed.
 * - Replace 'name1' with text representing the respective item, weapon, or
 *   armor that is the original cost type.
 * - Replace 'name2' with text representing the respective item, weapon, or
 *   armor that will be consumed instead.
 *
 * Examples:
 *
 *   <Replace Item Magic Water Cost: Potion>
 *   <Replace Item Antidote Cost: Dispel Herb>
 *   <Replace Weapon Short Sword Cost: Falchion>
 *   <Replace Armor Cloth Armor Cost: Leather Armor>
 *
 * ---
 *
 * === Skill Accessibility Notetags ===
 *
 * Sometimes, you don't want all skills to be visible whether it be to hide
 * menu-only skills during battle, until certain switches are turned ON/OFF, or
 * until certain skills have been learned.
 *
 * ---
 *
 * <Hide in Battle>
 * <Hide outside Battle>
 *
 * - Used for: Skill Notetags
 * - Makes the specific skill visible or hidden depending on whether or not the
 *   player is currently in battle.
 *
 * ---
 *
 * <Show Switch: x>
 *
 * <Show All Switches: x,x,x>
 * <Show Any Switches: x,x,x>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on switches.
 * - Replace 'x' with the switch ID to determine the skill's visibility.
 * - If 'All' notetag variant is used, skill will be hidden until all switches
 *   are ON. Then, it would be shown.
 * - If 'Any' notetag variant is used, skill will be shown if any of the
 *   switches are ON. Otherwise, it would be hidden.
 *
 * ---
 *
 * <Hide Switch: x>
 *
 * <Hide All Switches: x,x,x>
 * <Hide Any Switches: x,x,x>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on switches.
 * - Replace 'x' with the switch ID to determine the skill's visibility.
 * - If 'All' notetag variant is used, skill will be shown until all switches
 *   are ON. Then, it would be hidden.
 * - If 'Any' notetag variant is used, skill will be hidden if any of the
 *   switches are ON. Otherwise, it would be shown.
 *
 * ---
 *
 * <Show if learned Skill: x>
 *
 * <Show if learned All Skills: x,x,x>
 * <Show if learned Any Skills: x,x,x>
 *
 * <Show if learned Skill: name>
 *
 * <Show if learned All Skills: name, name, name>
 * <Show if learned Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on skills learned.
 * - This does not apply to skills added by traits on actors, classes, any
 *   equipment, or states. These are not considered learned skills. They are
 *   considered temporary skills.
 * - Replace 'x' with the skill ID to determine the skill's visibility.
 * - If 'name' notetag viarant is used, replace 'name' with the skill's name to
 *   be checked for the notetag.
 * - If 'All' notetag variant is used, skill will be hidden until all skills
 *   are learned. Then, it would be shown.
 * - If 'Any' notetag variant is used, skill will be shown if any of the skills
 *   are learned. Otherwise, it would be hidden.
 *
 * ---
 *
 * <Hide if learned Skill: x>
 *
 * <Hide if learned All Skills: x,x,x>
 * <Hide if learned Any Skills: x,x,x>
 *
 * <Hide if learned Skill: name>
 *
 * <Hide if learned All Skills: name, name, name>
 * <Hide if learned Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on skills learned.
 * - This does not apply to skills added by traits on actors, classes, any
 *   equipment, or states. These are not considered learned skills. They are
 *   considered temporary skills.
 * - Replace 'x' with the skill ID to determine the skill's visibility.
 * - If 'name' notetag viarant is used, replace 'name' with the skill's name to
 *   be checked for the notetag.
 * - If 'All' notetag variant is used, skill will be shown until all skills
 *   are learned. Then, it would be hidden.
 * - If 'Any' notetag variant is used, skill will be hidden if any of the
 *   skills are learned. Otherwise, it would be shown.
 *
 * ---
 *
 * <Show if has Skill: x>
 *
 * <Show if have All Skills: x,x,x>
 * <Show if have Any Skills: x,x,x>
 *
 * <Show if has Skill: name>
 *
 * <Show if have All Skills: name, name, name>
 * <Show if have Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on skills available.
 * - This applies to both skills that have been learned and/or temporarily
 *   added through traits on actors, classes, equipment, or states.
 * - Replace 'x' with the skill ID to determine the skill's visibility.
 * - If 'name' notetag viarant is used, replace 'name' with the skill's name to
 *   be checked for the notetag.
 * - If 'All' notetag variant is used, skill will be hidden until all skills
 *   are learned. Then, it would be shown.
 * - If 'Any' notetag variant is used, skill will be shown if any of the skills
 *   are learned. Otherwise, it would be hidden.
 *
 * ---
 *
 * <Hide if has Skill: x>
 *
 * <Hide if have All Skills: x,x,x>
 * <Hide if have Any Skills: x,x,x>
 *
 * <Hide if has Skill: name>
 *
 * <Hide if have All Skills: name, name, name>
 * <Hide if have Any Skills: name, name, name>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on skills available.
 * - This applies to both skills that have been learned and/or temporarily
 *   added through traits on actors, classes, equipment, or states.
 * - Replace 'x' with the skill ID to determine the skill's visibility.
 * - If 'name' notetag viarant is used, replace 'name' with the skill's name to
 *   be checked for the notetag.
 * - If 'All' notetag variant is used, skill will be shown until all skills
 *   are learned. Then, it would be hidden.
 * - If 'Any' notetag variant is used, skill will be hidden if any of the
 *   skills are learned. Otherwise, it would be shown.
 *
 * ---
 *
 * <Enable Switch: x>
 *
 * <Enable All Switches: x,x,x>
 * <Enable Any Switches: x,x,x>
 *
 * - Used for: Skill Notetags
 * - Determines the enabled status of the skill based on switches.
 * - Replace 'x' with the switch ID to determine the skill's enabled status.
 * - If 'All' notetag variant is used, skill will be disabled until all
 *   switches are ON. Then, it would be enabled.
 * - If 'Any' notetag variant is used, skill will be enabled if any of the
 *   switches are ON. Otherwise, it would be disabled.
 *
 * ---
 *
 * <Disable Switch: x>
 *
 * <Disable All Switches: x,x,x>
 * <Disable Any Switches: x,x,x>
 *
 * - Used for: Skill Notetags
 * - Determines the enabled status of the skill based on switches.
 * - Replace 'x' with the switch ID to determine the skill's enabled status.
 * - If 'All' notetag variant is used, skill will be enabled until all switches
 *   are ON. Then, it would be disabled.
 * - If 'Any' notetag variant is used, skill will be disabled if any of the
 *   switches are ON. Otherwise, it would be enabled.
 *
 * ---
 *
 * === JavaScript Notetags: Skill Accessibility ===
 *
 * The following are notetags made for users with JavaScript knowledge to
 * determine if a skill can be accessible visibly or through usage.
 *
 * ---
 *
 * <JS Skill Visible>
 *  code
 *  code
 *  visible = code;
 * </JS Skill Visible>
 *
 * - Used for: Skill Notetags
 * - Determines the visibility of the skill based on JavaScript code.
 * - Replace 'code' to determine the type visibility of the skill.
 * - The 'visible' variable returns a boolean (true/false) to determine if the
 *   skill will be visible or not.
 * - The 'user' variable refers to the user with the skill.
 * - The 'skill' variable refers to the skill being checked.
 * - All other visibility conditions must be met for this code to count.
 *
 * ---
 *
 * <JS Skill Enable>
 *  code
 *  code
 *  enabled = code;
 * </JS Skill Enable>
 *
 * - Used for: Skill Notetags
 * - Determines the enabled status of the skill based on JavaScript code.
 * - Replace 'code' to determine the type enabled status of the skill.
 * - The 'enabled' variable returns a boolean (true/false) to determine if the
 *   skill will be enabled or not.
 * - The 'user' variable refers to the user with the skill.
 * - The 'skill' variable refers to the skill being checked.
 * - All other skill conditions must be met in order for this to code to count.
 *
 * ---
 *
 * === General State-Related Notetags ===
 *
 * The following notetags are centered around states, such as how their turn
 * counts are displayed, items and skills that affect state turns, if the state
 * can avoid removal by death state, etc.
 *
 * ---
 *
 * <No Death Clear>
 *
 * - Used for: State Notetags
 * - Prevents this state from being cleared upon death.
 * - This allows this state to be added to an already dead battler, too.
 *
 * ---
 *
 * <No Recover All Clear>
 *
 * - Used for: State Notetags
 * - Prevents this state from being cleared upon using the Recover All command.
 *
 * ---
 *
 * <Group Defeat>
 *
 * - Used for: State Notetags
 * - If an entire party is affected by states with the <Group Defeat> notetag,
 *   they are considered defeated.
 * - Usage for this includes party-wide petrification, frozen, etc.
 *
 * ---
 *
 * <Reapply Rules: Ignore>
 * <Reapply Rules: Reset>
 * <Reapply Rules: Greater>
 * <Reapply Rules: Add>
 *
 * - Used for: State Notetags
 * - Choose what kind of rules this state follows if the state is being applied
 *   to a target that already has the state. This affects turns specifically.
 * - 'Ignore' will bypass any turn changes.
 * - 'Reset' will recalculate the state's turns.
 * - 'Greater' will choose to either keep the current turn count if it's higher
 *   than the reset amount or reset it if the current turn count is lower.
 * - 'Add' will add the state's turn count to the applied amount.
 * - If this notetag isn't used, it will use the rules set in the States >
 *   Plugin Parameters.
 *
 * ---
 *
 * <Positive State>
 * <Negative State>
 *
 * - Used for: State Notetags
 * - Marks the state as a positive state or negative state, also altering the
 *   state's turn count color to match the Plugin Parameter settings.
 * - This also puts the state into either the 'Positive' category or
 *   'Negative' category.
 *
 * ---
 *
 * <Category: name>
 * <Category: name, name, name>
 *
 * - Used for: State Notetags
 * - Arranges states into certain/multiple categories.
 * - Replace 'name' with a category name to mark this state as.
 * - Insert multiples of this to mark the state with  multiple categories.
 *
 * ---
 *
 * <Categories>
 *  name
 *  name
 * </Categories>
 *
 * - Used for: State Notetags
 * - Arranges states into certain/multiple categories.
 * - Replace each 'name' with a category name to mark this state as.
 *
 * ---
 *
 * <Bypass State Damage Removal: id>
 * <Bypass State Damage Removal: id, id, id>
 *
 * <Bypass State Damage Removal: name>
 * <Bypass State Damage Removal: name, name, name>
 *
 * - Used for: Skill, Item Notetags
 * - When this skill/item is used to attack an enemy with the listed state that
 *   would normally have on damage removal (ie Sleep).
 * - For 'id' variant, replace each 'id' with a number representing the state's
 *   ID to bypass the damage removal for.
 * - For 'name' variant, replace each 'name' with the state's name to bypass
 *   the damage removal for.
 * - This can be used for attacks like "Dream Eater" that would prevent waking
 *   up a sleeping opponent.
 *
 * ---
 *
 * <Bypass State Damage Removal as Attacker: id>
 * <Bypass State Damage Removal as Attacker: id, id, id>
 *
 * <Bypass State Damage Removal as Attacker: name>
 * <Bypass State Damage Removal as Attacker: name, name, name>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - When an attacker with an associated trait object that has this notetag
 *   would attack an enemy with the listed state, bypass on damage removal.
 * - For 'id' variant, replace each 'id' with a number representing the state's
 *   ID to bypass the damage removal for.
 * - For 'name' variant, replace each 'name' with the state's name to bypass
 *   the damage removal for.
 * - This can be used for effects like "Sleep Striker" that would prevent the
 *   attacker from waking up a sleeping opponent.
 *
 * ---
 *
 * <Bypass State Damage Removal as Target: id>
 * <Bypass State Damage Removal as Target: id, id, id>
 *
 * <Bypass State Damage Removal as Target: name>
 * <Bypass State Damage Removal as Target: name, name, name>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - When a target with an associated trait object that has this notetag is
 *   attacked as the target with the listed state, bypass on damage removal.
 * - For 'id' variant, replace each 'id' with a number representing the state's
 *   ID to bypass the damage removal for.
 * - For 'name' variant, replace each 'name' with the state's name to bypass
 *   the damage removal for.
 * - This can be used for effects like "Deep Sleep" that would prevent the
 *   attacked target from waking up.
 *
 * ---
 *
 * <Resist State Category: name>
 * <Resist State Categories: name, name, name>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - Causes the affected battler resist the listed categories.
 * - Replace each 'name' with a category name to resist.
 *   - Insert multiple 'name' entries to add more categories.
 * - This works exactly like how state resistances work in-game. If a battler
 *   who was originally NOT resistant to "Poison" before gaining a
 *   poison-resistant trait, the "Poison" state will remain because it was
 *   applied before poison-resistance as enabled.
 *
 * ---
 *
 * <Resist State Categories>
 *  name
 *  name
 *  name
 * </Resist State Categories>
 *
 * - Used for: Actor, Class, Weapon, Armor, Enemy, State Notetags
 * - Causes the affected battler resist the listed categories.
 * - Replace each 'name' with a category name to resist.
 *   - Insert multiple 'name' entries to add more categories.
 * - This works exactly like how state resistances work in-game. If a battler
 *   who was originally NOT resistant to "Poison" before gaining a
 *   poison-resistant trait, the "Poison" state will remain because it was
 *   applied before poison-resistance as enabled.
 *
 * ---
 *
 * <State x Category Remove: y>
 *
 * <State x Category Remove: All>
 *
 * - Used for: Skill, Item Notetags
 * - Allows the skill/item to remove 'y' states from specific category 'x'.
 * - Replace 'x' with a category name to remove from.
 * - Replace 'y' with the number of times to remove from that category.
 * - Use the 'All' variant to remove all of the states of that category.
 * - Insert multiples of this to remove different types of categories.
 *
 * ---
 *
 * <Remove Other x States>
 *
 * - Used for: State Notetags
 * - When the state with this notetag is added, remove other 'x' category
 *   states from the battler (except for the state being added).
 * - Replace 'x' with a category name to remove from.
 * - Insert multiples of this to remove different types of categories.
 * - Useful for thing state types like stances and forms that there is usually
 *   only one active at a time.
 *
 * ---
 *
 * <Hide State Turns>
 *
 * - Used for: State Notetags
 * - Hides the state turns from being shown at all.
 * - This will by pass any Plugin Parameter settings.
 *
 * ---
 *
 * <Turn Color: x>
 * <Turn Color: #rrggbb>
 *
 * - Used for: State Notetags
 * - Hides the state turns from being shown at all.
 * - Determines the color of the state's turn count.
 * - Replace 'x' with a number value depicting a window text color.
 * - Replace 'rrggbb' with a hex color code for a more custom color.
 *
 * ---
 *
 * <Max Turns: x>
 *
 * - Used for: State Notetags
 * - Determines the upper limit on the maximum number of turns for this state.
 * - Replace 'x' with a number representing the maximum number of turns used
 *   for this state.
 * - If no notetag is used, refer to the default setting found in the Plugin
 *   Parameters under "State Settings".
 *
 * ---
 *
 * <State id Turns: +x>
 * <State id Turns: -x>
 *
 * <Set State id Turns: x>
 *
 * <State name Turns: +x>
 * <State name Turns: -x>
 *
 * <Set State name Turns: x>
 *
 * - Used for: Skill, Item Notetags
 * - If the target is affected by state 'id' or state 'name', change the state
 *   turn duration for target.
 * - For 'id' variant, replace 'id' with the ID of the state to modify.
 * - For 'name' variant, replace 'name' with the name of the state to modify.
 * - Replace 'x' with the value you wish to increase, decrease, or set to.
 * - Insert multiples of this notetag to affect multiple states at once.
 *
 * ---
 *
 * <param Buff Turns: +x>
 * <param Buff Turns: -x>
 *
 * <Set param Buff Turns: x>
 *
 * - Used for: Skill, Item Notetags
 * - If the target is affected by a 'param' buff, change that buff's turn
 *   duration for target.
 * - Replace 'param' with 'MaxHP', 'MaxMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI',
 *   or 'LUK' to determine which parameter buff to modify.
 * - Replace 'x' with the value you wish to increase, decrease, or set to.
 * - Insert multiples of this notetag to affect multiple parameters at once.
 *
 * ---
 *
 * <param Debuff Turns: +x>
 * <param Debuff Turns: -x>
 *
 * <Set param Debuff Turns: x>
 *
 * - Used for: Skill, Item Notetags
 * - If the target is affected by a 'param' debuff, change that debuff's turn
 *   duration for target.
 * - Replace 'param' with 'MaxHP', 'MaxMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI',
 *   or 'LUK' to determine which parameter debuff to modify.
 * - Replace 'x' with the value you wish to increase, decrease, or set to.
 * - Insert multiples of this notetag to affect multiple parameters at once.
 *
 * ---
 *
 * === JavaScript Notetags: On Add/Erase/Expire ===
 *
 * Using JavaScript code, you can use create custom effects that occur when a
 * state has bee added, erased, or expired.
 *
 * ---
 *
 * <JS On Add State>
 *  code
 *  code
 * </JS On Add State>
 *
 * - Used for: State Notetags
 * - When a state is added, run the code added by this notetag.
 * - The 'user' variable refers to the current active battler.
 * - The 'target' variable refers to the battler affected by this state.
 * - The 'origin' variable refers to the one who applied this state.
 * - The 'state' variable refers to the current state being affected.
 *
 * ---
 *
 * <JS On Erase State>
 *  code
 *  code
 * </JS On Erase State>
 *
 * - Used for: State Notetags
 * - When a state is erased, run the code added by this notetag.
 * - The 'user' variable refers to the current active battler.
 * - The 'target' variable refers to the battler affected by this state.
 * - The 'origin' variable refers to the one who applied this state.
 * - The 'state' variable refers to the current state being affected.
 *
 * ---
 *
 * <JS On Expire State>
 *  code
 *  code
 * </JS On Expire State>
 *
 * - Used for: State Notetags
 * - When a state has expired, run the code added by this notetag.
 * - The 'user' variable refers to the current active battler.
 * - The 'target' variable refers to the battler affected by this state.
 * - The 'origin' variable refers to the one who applied this state.
 * - The 'state' variable refers to the current state being affected.
 *
 * ---
 *
 * === JavaScript Notetags: Slip Damage/Healing ===
 *
 * Slip Damage, in RPG Maker vocabulary, refers to damage over time. The
 * following notetags allow you to perform custom slip damage/healing.
 *
 * ---
 *
 * <JS type Slip Damage>
 *  code
 *  code
 *  damage = code;
 * </JS type Slip Damage>
 *
 * - Used for: State Notetags
 * - Code used to determine how much slip damage is dealt to the affected unit
 *   during each regeneration phase.
 * - Replace 'type' with 'HP', 'MP', or 'TP'.
 * - Replace 'code' with the calculations on what to determine slip damage.
 * - The 'user' variable refers to the origin of the state.
 * - The 'target' variable refers to the affected unit receiving the damage.
 * - The 'state' variable refers to the current state being affected.
 * - The 'damage' variable is the finalized slip damage to be dealt.
 * - When these states are applied via action effects, the slip calculations
 *   are one time calculations made upon applying and the damage is cached to
 *   be used for future on regeneration calculations.
 * - For that reason, do not include game mechanics here such as adding states,
 *   buffs, debuffs, etc. as this notetag is meant for calculations only. Use
 *   the VisuStella Battle Core's <JS Pre-Regenerate> and <JS Post-Regenerate>
 *   notetags for game mechanics instead.
 * - Passive states and states with the <JS Slip Refresh> notetag are exempt
 *   from the one time calculation and recalculated each regeneration phase.
 *
 * ---
 *
 * <JS type Slip Heal>
 *  code
 *  code
 *  heal = code;
 * </JS type Slip Heal>
 *
 * - Used for: State Notetags
 * - Code used to determine how much slip healing is dealt to the affected unit
 *   during each regeneration phase.
 * - Replace 'type' with 'HP', 'MP', or 'TP'.
 * - Replace 'code' with the calculations on what to determine slip healing.
 * - The 'user' variable refers to the origin of the state.
 * - The 'target' variable refers to the affected unit receiving the healing.
 * - The 'state' variable refers to the current state being affected.
 * - The 'heal' variable is the finalized slip healing to be recovered.
 * - When these states are applied via action effects, the slip calculations
 *   are one time calculations made upon applying and the damage is cached to
 *   be used for future on regeneration calculations.
 * - For that reason, do not include game mechanics here such as adding states,
 *   buffs, debuffs, etc. as this notetag is meant for calculations only. Use
 *   the VisuStella Battle Core's <JS Pre-Regenerate> and <JS Post-Regenerate>
 *   notetags for game mechanics instead.
 * - Passive states and states with the <JS Slip Refresh> notetag are exempt
 *   from the one time calculation and recalculated each regeneration phase.
 *
 * ---
 *
 * <JS Slip Refresh>
 *
 * - Used for: State Notetags
 * - Refreshes the calculations made for the JS Slip Damage/Heal amounts at the
 *   start of each regeneration phase to allow for dynamic damage ranges.
 *
 * ---
 *
 * === Passive State Notetags ===
 *
 * Passive States are states that are always applied to actors and enemies
 * provided that their conditions have been met. These can be granted through
 * database objects or through the Passive States Plugin Parameters.
 *
 * ---
 *
 * For those using the code "a.isStateAffected(10)" to check if a target is
 * affected by a state or not, this does NOT check passive states. This only
 * checks for states that were directly applied to the target.
 *
 * This is NOT a bug.
 *
 * Instead, use "a.states().includes($dataStates[10])" to check for them. This
 * code will search for both directly applied states and passive states alike.
 *
 * ---
 *
 * As passive states are NOT considered directly applied to, they do NOT match
 * a Conditional Branch's state check as well. The Conditional Branch effect
 * checks for an affected state.
 *
 * ---
 *
 * Because passive states are NOT directly applied to a battler, the functions
 * of "addNewState", "addState", "eraseState", "removeState" do NOT apply to
 * passive states either. This means that any of the related JS notetags tied
 * to those functions will not occur either.
 *
 * ---
 *
 * Why are passive states not considered affected by? Let's look at it
 * differently. There are two ways to grant skills to actors. They can acquire
 * skills by levels/items/events or they can equip gear that temporarily grants
 * the skill in question.
 *
 * Learning the skill is direct. Temporarily granting the skill is indirect.
 * These two factors have mechanical importance and require differentiation.
 *
 * Regular states and passive states are the same way. Regular states are
 * directly applied, therefore, need to be distinguished in order for things
 * like state turns and steps, removal conditionals, and similar to matter at
 * all. Passive states are indirect and are therefore, unaffected by state
 * turns, steps, and removal conditions. These mechanical differences are
 * important for how RPG Maker works.
 *
 * ---
 *
 * Once again, it is NOT a bug that when using "a.isStateAffected(10)" to
 * check if a target has a passive state will return false.
 *
 * ---
 *
 * <Passive State: x>
 * <Passive States: x,x,x>
 *
 * <Passive State: name>
 * <Passive States: name, name, name>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy Notetags
 * - Adds passive state(s) x to trait object, applying it to related actor or
 *   enemy unit(s).
 * - Replace 'x' with a number to determine which state to add as a passive.
 * - If using 'name' notetag variant, replace 'name' with the name of the
 *   state(s) to add as a passive.
 * - Note: If you plan on applying a passive state through a skill, it must be
 *   through a skill that has been learned by the target and not a skill that
 *   is given through a trait.
 *
 * ---
 *
 * <Passive Stackable>
 *
 * - Used for: State Notetags
 * - Makes it possible for this passive state to be added multiple times.
 * - Otherwise, only one instance of the passive state can be available.
 *
 * ---
 *
 * <Passive Condition Class: id>
 * <Passive Condition Classes: id, id, id>
 *
 * <Passive Condition Class: name>
 * <Passive Condition Classes: name, name, name>
 *
 * - Used for: State Notetags
 * - Determines the passive condition of the passive state based on the actor's
 *   current class. As long as the actor's current class matches one of the
 *   data entries, the passive condition is considered passed.
 * - For 'id' variant, replace 'id' with a number representing class's ID.
 * - For 'name' variant, replace 'name' with the class's name.
 *
 * ---
 *
 * <Passive Condition Multiclass: id>
 * <Passive Condition Multiclass: id, id, id>
 *
 * <Passive Condition Multiclass: name>
 * <Passive Condition Multiclass: name, name, name>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_ClassChangeSystem!
 * - Determines the passive condition of the passive state based on the actor's
 *   multiclasses. As long as the actor has any of the matching classes
 *   assigned as a multiclass, the passive condition is considered passed.
 * - For 'id' variant, replace 'id' with a number representing class's ID.
 * - For 'name' variant, replace 'name' with the class's name.
 *
 * ---
 *
 * <Passive Condition Switch ON: x>
 *
 * <Passive Condition All Switches ON: x,x,x>
 * <Passive Condition Any Switch ON: x,x,x>
 *
 * - Used for: State Notetags
 * - Determines the passive condition of the passive state based on switches.
 * - Replace 'x' with the switch ID to determine the state's passive condition.
 * - If 'All' notetag variant is used, conditions will not be met until all
 *   switches are ON. Then, it would be met.
 * - If 'Any' notetag variant is used, conditions will be met if any of the
 *   switches are ON. Otherwise, it would not be met.
 *
 * ---
 *
 * <Passive Condition Switch OFF: x>
 *
 * <Passive Condition All Switches OFF: x,x,x>
 * <Passive Condition Any Switch OFF: x,x,x>
 *
 * - Used for: State Notetags
 * - Determines the passive condition of the passive state based on switches.
 * - Replace 'x' with the switch ID to determine the state's passive condition.
 * - If 'All' notetag variant is used, conditions will not be met until all
 *   switches are OFF. Then, it would be met.
 * - If 'Any' notetag variant is used, conditions will be met if any of the
 *   switches are OFF. Otherwise, it would not be met.
 *
 * ---
 *
 * === Aura & Miasma Notetags ===
 *
 * Auras are a type passive that affects an allied party. Miasmas are a type of
 * passive that affects an opposing party. Auras and Miasmas only need to come
 * from a single source to give an entire party or troop a passive provided
 * that the battler emitting the aura/miasma is alive and in battle.
 *
 * ---
 *
 * <Aura State: x>
 * <Aura States: x, x, x>
 *
 * <Aura State: name>
 * <Aura States: name, name, name>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy Notetags
 * - Emits an aura that affects the battler's allies and gives each affected
 *   member passive state(s) 'x'.
 * - Replace 'x' with a number to determine which state to add as a passive
 *   generated by this aura.
 * - If using 'name' notetag variant, replace 'name' with the name of the
 *   state(s) to add as a passive generated by this aura.
 * - Note: If you plan on applying an aura effect through a skill, it must be
 *   through a skill that has been learned by the target and not a skill that
 *   is given through a trait.
 *
 * ---
 *
 * <Miasma State: x>
 * <Miasma States: x, x, x>
 *
 * <Miasma State: name>
 * <Miasma States: name, name, name>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy Notetags
 * - Emits an miasma that affects the battler's opponents and gives each
 *   affected member passive state(s) 'x'.
 * - Miasmas do NOT apply outside of battle.
 * - Replace 'x' with a number to determine which state to add as a passive
 *   generated by this miasma.
 * - If using 'name' notetag variant, replace 'name' with the name of the
 *   state(s) to add as a passive generated by this miasma.
 * - Note: If you plan on applying a miasma effect through a skill, it must be
 *   through a skill that has been learned by the target and not a skill that
 *   is given through a trait.
 *
 * ---
 *
 * <Not User Aura>
 * <Aura Not For User>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy, State Notetags
 * - Prevents the emitting user from being affected by the related aura.
 *
 * ---
 *
 * <Allow Dead Aura>
 * <Allow Dead Miasma>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy, State Notetags
 * - Allows aura/miasma to continue emitting even after the emitting user is
 *   in a dead state.
 * - When used with Actor, Class, Skill, Weapon, Armor, Enemy objects, it will
 *   only affect the auras/miasmas emitted from that object.
 * - When used with States, the effect will take place as long as it is used
 *   as an aura or miasma regardless of where it is emitting from.
 * - Takes priority over <Dead Aura Only> and <Dead Miasma Only> notetags.
 *
 * ---
 *
 * <Dead Aura Only>
 * <Dead Miasma Only>
 *
 * - Used for: Actor, Class, Skill, Weapon, Armor, Enemy, State Notetags
 * - Allows aura/miasma to only emit if the emitting user is in a dead state.
 * - When used with Actor, Class, Skill, Weapon, Armor, Enemy objects, it will
 *   only affect the auras/miasmas emitted from that object.
 * - When used with States, the effect will take place as long as it is used
 *   as an aura or miasma regardless of where it is emitting from.
 *
 * ---
 *
 * === JavaScript Notetags: Passive State ===
 *
 * The following is a notetag made for users with JavaScript knowledge to
 * determine if a passive state's condition can be met.
 *
 * ---
 *
 * <JS Passive Condition>
 *  code
 *  code
 *  condition = code;
 * </JS Passive Condition>
 *
 * - Used for: State Notetags
 * - Determines the passive condition of the state based on JavaScript code.
 * - Replace 'code' to determine if a passive state's condition has been met.
 * - The 'condition' variable returns a boolean (true/false) to determine if
 *   the passive state's condition is met or not.
 * - The 'user' variable refers to the user affected by the passive state.
 * - The 'state' variable refers to the passive state being checked.
 * - All other passive conditions must be met for this code to count.
 *
 * **NOTE** Not everything can be used as a custom JS Passive Condition due to
 * limitations of the code. There are failsafe checks to prevent infinite loops
 * and some passive conditions will not register for this reason and the
 * conditional checks will behave as if the passive states have NOT been
 * applied for this reason. Such examples include the following:
 *
 * - A passive state that requires another passive state
 * - A passive state that requires a trait effect from another state
 * - A passive state that requires a parameter value altered by another state
 * - A passive state that requires equipment to be worn but its equipment type
 *   access is provided by another state.
 * - Anything else that is similar in style.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Skill Cost Plugin Commands ===
 *
 * ---
 *
 * Skill Cost: Emulate Actor Pay
 * - Target actor(s) emulates paying for skill cost.
 * -
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) will pay skill cost.
 *
 *   Skill ID:
 *   - What is the ID of the skill to emulate paying the skill cost for?
 *
 * ---
 *
 * Skill Cost: Emulate Enemy Pay
 * - Target enemy(s) emulates paying for skill cost.
 * -
 *
 *   Enemy Index(es):
 *   - Select which enemy index(es) will pay skill cost.
 *
 *   Skill ID:
 *   - What is the ID of the skill to emulate paying the skill cost for?
 *
 * ---
 *
 * === State Turns Plugin Commands ===
 *
 * ---
 *
 * State Turns: Actor State Turns Change By
 * - Changes actor(s) state turns by an amount.
 * - Only works on states that can have turns.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   State ID:
 *   - What is the ID of the state you wish to change turns for?
 *   - Only works on states that can have turns.
 *
 *   Change Turns By:
 *   - How many turns should the state be changed to?
 *   - You may use JavaScript code.
 *
 *   Auto-Add State?:
 *   - Automatically adds state if actor(s) does not have it applied?
 *
 * ---
 *
 * State Turns: Actor State Turns Change To
 * - Changes actor(s) state turns to a specific value.
 * - Only works on states that can have turns.
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   State ID:
 *   - What is the ID of the state you wish to change turns for?
 *   - Only works on states that can have turns.
 *
 *   Change Turns To:
 *   - How many turns should the state be changed to?
 *   - You may use JavaScript code.
 *
 *   Auto-Add State?:
 *   - Automatically adds state if actor(s) does not have it applied?
 *
 * ---
 *
 * State Turns: Enemy State Turns Change By
 * - Changes enemy(s) state turns by an amount.
 * - Only works on states that can have turns.
 *
 *   Enemy Index(es):
 *   - Select which enemy index(es) to affect.
 *
 *   State ID:
 *   - What is the ID of the state you wish to change turns for?
 *   - Only works on states that can have turns.
 *
 *   Change Turns By:
 *   - How many turns should the state be changed to?
 *   - You may use JavaScript code.
 *
 *   Auto-Add State?:
 *   - Automatically adds state if actor(s) does not have it applied?
 *
 * ---
 *
 * State Turns: Enemy State Turns Change To
 * - Changes enemy(s) state turns to a specific value.
 * - Only works on states that can have turns.
 *
 *   Enemy Index(es):
 *   - Select which enemy index(es) to affect.
 *
 *   State ID:
 *   - What is the ID of the state you wish to change turns for?
 *   - Only works on states that can have turns.
 *
 *   Change Turns To:
 *   - How many turns should the state be changed to?
 *   - You may use JavaScript code.
 *
 *   Auto-Add State?:
 *   - Automatically adds state if actor(s) does not have it applied?
 *
 * ---
 *
 *
 * ============================================================================
 * Plugin Parameters: General Skill Settings
 * ============================================================================
 *
 * These Plugin Parameters adjust various aspects of the game regarding skills
 * from the custom Skill Menu Layout to global custom effects made in code.
 *
 * ---
 *
 * General
 *
 *   Use Updated Layout:
 *   - Use the Updated Skill Menu Layout provided by this plugin?
 *   - This will automatically enable the Status Window.
 *   - This will override the Core Engine windows settings.
 *
 *   Layout Style:
 *   - If using an updated layout, how do you want to style the menu scene?
 *     - Upper Help, Left Input
 *     - Upper Help, Right Input
 *     - Lower Help, Left Input
 *     - Lower Help, Right Input
 *
 * ---
 *
 * Skill Type Window
 *
 *   Style:
 *   - How do you wish to draw commands in the Skill Type Window?
 *   - Text Only: Display only the text.
 *   - Icon Only: Display only the icon.
 *   - Icon + Text: Display the icon first, then the text.
 *   - Auto: Determine which is better to use based on the size of the cell.
 *
 *   Text Align:
 *   - Text alignment for the Skill Type Window.
 *
 *   Window Width:
 *   - What is the desired pixel width of this window?
 *   - Default: 240
 *
 * ---
 *
 * List Window
 *
 *   Columns:
 *   - Number of maximum columns.
 *
 * ---
 *
 * Shop Status Window
 *
 *   Show in Skill Menu?:
 *   - Show the Shop Status Window in the Skill Menu?
 *   - This is enabled if the Updated Layout is on.
 *
 *   Adjust List Window?:
 *   - Automatically adjust the Skill List Window in the Skill Menu if using
 *     the Shop Status Window?
 *
 *   Background Type:
 *   - Select background type for this window.
 *     - 0 - Window
 *     - 1 - Dim
 *     - 2 - Transparent
 *
 *   JS: X, Y, W, H:
 *   - Code used to determine the dimensions for this Shop Status Window in the
 *     Skill Menu.
 *
 * ---
 *
 * Skill Types
 *
 *   Hidden Skill Types:
 *   - Insert the ID's of the Skill Types you want hidden from view ingame.
 *
 *   Hidden During Battle:
 *   - Insert the ID's of the Skill Types you want hidden during battle only.
 *
 *   Icon: Normal Type:
 *   - Icon used for normal skill types that aren't assigned any icons.
 *   - To assign icons to skill types, simply insert \I[x] into the
 *     skill type's name in the Database > Types tab.
 *
 *   Icon: Magic Type:
 *   - Icon used for magic skill types that aren't assigned any icons.
 *   - To assign icons to skill types, simply insert \I[x] into the
 *     skill type's name in the Database > Types tab.
 *
 *   Sort: Alphabetical:
 *   - Insert the ID's of Skill Types you want sorted alphabetically.
 *
 * ---
 *
 * Global JS Effects
 *
 *   JS: Skill Conditions:
 *   - JavaScript code for a global-wide skill condition check.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Skill Cost Types
 * ============================================================================
 *
 * Skill Cost Types are the resources that are used for your skills. These can
 * range from the default MP and TP resources to the newly added HP, Gold, and
 * Potion resources.
 *
 * ---
 *
 * Settings
 *
 *   Name:
 *   - A name for this Skill Cost Type.
 *
 *   Icon:
 *   - Icon used for this Skill Cost Type.
 *   - Use 0 for no icon.
 *
 *   Font Color:
 *   - Text Color used to display this cost.
 *   - For a hex color, use #rrggbb with VisuMZ_1_MessageCore
 *
 *   Font Size:
 *   - Font size used to display this cost.
 *
 * ---
 *
 * Cost Processing
 *
 *   JS: Cost Calculation:
 *   - Code on how to calculate this resource cost for the skill.
 *
 *   JS: Can Pay Cost?:
 *   - Code on calculating whether or not the user is able to pay the cost.
 *
 *   JS: Paying Cost:
 *   - Code for if met, this is the actual process of paying of the cost.
 *
 * ---
 *
 * Window Display
 *
 *   JS: Show Cost?:
 *   - Code for determining if the cost is shown or not.
 *
 *   JS: Cost Text:
 *   - Code to determine the text (with Text Code support) used for the
 *     displayed cost.
 *
 * ---
 *
 * Gauge Display
 *
 *   JS: Maximum Value:
 *   - Code to determine the maximum value used for this Skill Cost resource
 *     for gauges.
 *
 *   JS: Current Value:
 *   - Code to determine the current value used for this Skill Cost resource
 *     for gauges.
 *
 *   JS: Draw Gauge:
 *   - Code to determine how to draw the Skill Cost resource for this
 *     gauge type.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Gauge Settings
 * ============================================================================
 *
 * Settings in regards to how skill cost gauges function and appear.
 *
 * ---
 *
 * Labels
 *
 *   Font Type:
 *   - Which font type should be used for labels?
 *
 *   Match Label Color:
 *   - Match the label color to the Gauge Color being used?
 *
 *     Match: Gauge # ?:
 *     - Which Gauge Color should be matched?
 *
 *     Preset: Gauge Color:
 *     - Use #rrggbb for custom colors or regular numbers for text colors from
 *       the Window Skin.
 *
 *   Solid Outline:
 *   - Make the label outline a solid black color?
 *
 *   Outline Width:
 *   - What width do you wish to use for your outline?
 *   - Use 0 to not use an outline.
 *
 * ---
 *
 * Values
 *
 *   Font Type:
 *   - Which font type should be used for values?
 *
 *   Solid Outline:
 *   - Make the value outline a solid black color?
 *
 *   Outline Width:
 *   - What width do you wish to use for your outline?
 *   - Use 0 to not use an outline.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General State Settings
 * ============================================================================
 *
 * These are general settings regarding RPG Maker MZ's state-related aspects
 * from how turns are reapplied to custom code that's ran whenever states are
 * added, erased, or expired.
 *
 * ---
 *
 * General
 *
 *   Reapply Rules:
 *   - These are the rules when reapplying states.
 *   - Ignore: State doesn't get added.
 *   - Reset: Turns get reset.
 *   - Greater: Turns take greater value (current vs reset).
 *   - Add: Turns add upon existing turns.
 *
 *   Maximum Turns:
 *   - Maximum number of turns to let states go up to.
 *   - This can be changed with the <Max Turns: x> notetag.
 *
 *   Action End Update:
 *   - Refer to "Major Changes" in Help File for explanation.
 *
 *   Turn End on Map:
 *   - Update any state and buff turns on the map after this many steps.
 *   - Use 0 to disable.
 *
 * ---
 *
 * Turn Display
 *
 *   Show Turns?:
 *   - Display state turns on top of window icons and sprites?
 *
 *   Turn Font Size:
 *   - Font size used for displaying turns.
 *
 *   Offset X:
 *   - Offset the X position of the turn display.
 *
 *   Offset Y:
 *   - Offset the Y position of the turn display.
 *
 *   Turn Font Size:
 *   - Font size used for displaying turns.
 *
 *   Turn Color: Neutral:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Turn Color: Positive:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Turn Color: Negative:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 * ---
 *
 * Data Display
 *
 *   Show Data?:
 *   - Display state data on top of window icons and sprites?
 *
 *   Data Font Size:
 *   - Font size used for displaying state data.
 *
 *   Offset X:
 *   - Offset the X position of the state data display.
 *
 *   Offset Y:
 *   - Offset the Y position of the state data display.
 *
 * ---
 *
 * Global JS Effects
 *
 *   JS: On Add State:
 *   - JavaScript code for a global-wide custom effect whenever a state
 *     is added.
 *
 *   JS: On Erase State:
 *   - JavaScript code for a global-wide custom effect whenever a state
 *     is erased.
 *
 *   JS: On Expire State:
 *   - JavaScript code for a global-wide custom effect whenever a state
 *     has expired.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Buff/Debuff Settings
 * ============================================================================
 *
 * Buffs and debuffs don't count as states by RPG Maker MZ's mechanics, but
 * they do function close enough for them to be added to this plugin for
 * adjusting. Change these settings to make buffs and debuffs work to your
 * game's needs.
 *
 * ---
 *
 * General
 *
 *   Reapply Rules:
 *   - These are the rules when reapplying buffs/debuffs.
 *   - Ignore: Buff/Debuff doesn't get added.
 *   - Reset: Turns get reset.
 *   - Greater: Turns take greater value (current vs reset).
 *   - Add: Turns add upon existing turns.
 *
 *   Maximum Turns:
 *   - Maximum number of turns to let buffs and debuffs go up to.
 *
 * ---
 *
 * Stacking
 *
 *   Max Stacks: Buff:
 *   - Maximum number of stacks for buffs.
 *
 *   Max Stacks: Debuff:
 *   - Maximum number of stacks for debuffs.
 *
 *   JS: Buff/Debuff Rate:
 *   - Code to determine how much buffs and debuffs affect parameters.
 *
 * ---
 *
 * Turn Display
 *
 *   Show Turns?:
 *   - Display buff and debuff turns on top of window icons and sprites?
 *
 *   Turn Font Size:
 *   - Font size used for displaying turns.
 *
 *   Offset X:
 *   - Offset the X position of the turn display.
 *
 *   Offset Y:
 *   - Offset the Y position of the turn display.
 *
 *   Turn Color: Buffs:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Turn Color: Debuffs:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 * ---
 *
 * Rate Display
 *
 *   Show Rate?:
 *   - Display buff and debuff rate on top of window icons and sprites?
 *
 *   Rate Font Size:
 *   - Font size used for displaying rate.
 *
 *   Offset X:
 *   - Offset the X position of the rate display.
 *
 *   Offset Y:
 *   - Offset the Y position of the rate display.
 *
 * ---
 *
 * Global JS Effects
 *
 *   JS: On Add Buff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     buff is added.
 *
 *   JS: On Add Debuff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     debuff is added.
 *
 *   JS: On Erase Buff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     buff is added.
 *
 *   JS: On Erase Debuff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     debuff is added.
 *
 *   JS: On Expire Buff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     buff is added.
 *
 *   JS: On Expire Debuff:
 *   - JavaScript code for a global-wide custom effect whenever a
 *     debuff is added.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Passive State Settings
 * ============================================================================
 *
 * These Plugin Parameters adjust passive states that can affect all actors and
 * enemies as well as have global conditions.
 *
 * ---
 *
 * For those using the code "a.isStateAffected(10)" to check if a target is
 * affected by a state or not, this does NOT check passive states. This only
 * checks for states that were directly applied to the target.
 *
 * This is NOT a bug.
 *
 * Instead, use "a.states().includes($dataStates[10])" to check for them. This
 * code will search for both directly applied states and passive states alike.
 *
 * ---
 *
 * As passive states are NOT considered directly applied to, they do NOT match
 * a Conditional Branch's state check as well. The Conditional Branch effect
 * checks for an affected state.
 *
 * ---
 *
 * Because passive states are NOT directly applied to a battler, the functions
 * of "addNewState", "addState", "eraseState", "removeState" do NOT apply to
 * passive states either. This means that any of the related JS notetags tied
 * to those functions will not occur either.
 *
 * ---
 *
 * Why are passive states not considered affected by? Let's look at it
 * differently. There are two ways to grant skills to actors. They can acquire
 * skills by levels/items/events or they can equip gear that temporarily grants
 * the skill in question.
 *
 * Learning the skill is direct. Temporarily granting the skill is indirect.
 * These two factors have mechanical importance and require differentiation.
 *
 * Regular states and passive states are the same way. Regular states are
 * directly applied, therefore, need to be distinguished in order for things
 * like state turns and steps, removal conditionals, and similar to matter at
 * all. Passive states are indirect and are therefore, unaffected by state
 * turns, steps, and removal conditions. These mechanical differences are
 * important for how RPG Maker works.
 *
 * ---
 *
 * Once again, it is NOT a bug that when using "a.isStateAffected(10)" to
 * check if a target has a passive state will return false.
 *
 * ---
 *
 * List
 *
 *   Global Passives:
 *   - A list of passive states to affect actors and enemies.
 *
 *   Actor-Only Passives:
 *   - A list of passive states to affect actors only.
 *
 *   Enemy Passives:
 *   - A list of passive states to affect enemies only.
 *
 * ---
 *
 * Cache
 *
 *   Switch Refresh?:
 *   - Refresh all battle members when switches are changed in battle?
 *   - This is primarily used for passive state conditions involve parameters
 *     that do not update due to cached data until a refresh occurs.
 *   - If this is on, do not spam Switch changes during battle in order to
 *     prevent lag spikes.
 *
 *   Variable Refresh?:
 *   - Refresh all battle members when variables are changed in battle?
 *   - This is primarily used for passive state conditions involve parameters
 *     that do not update due to cached data until a refresh occurs.
 *   - If this is on, do not spam Variable changes during battle in order to
 *     prevent lag spikes.
 *
 * ---
 *
 * Global JS Effects
 *
 *   JS: Condition Check:
 *   - JavaScript code for a global-wide passive condition check.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * - Yanfly
 * - Arisu
 * - Olivia
 * - Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.50: March 20, 2025
 * * Documentation Update!
 * ** Changed the description of Plugin Parameter 'Action End Update' to
 *    'Refer to "Major Changes" in Help File for explanation.'
 * ** Added examples of "Action End Update" under "Major Changes"
 * *** The new state: "Fiery Blade" will allow the affected battler to deal
 *     fire elemental damage. With Action End, this means for 5 actions, those
 *     attacks will deal fire damage.
 * *** This means that if no action is taken, due to a status effect like
 *     "Sleep" or "Stun", then the duration count will not decrease.
 * *** On the flip side, if the battler performs multiple actions a turn, then
 *     the duration count drops faster because more actions have been spent.
 * *** However, if this "Fiery Blade" state was using Turn End instead, it will
 *     have its duration reduced by 1 each turn, regardless of "Sleep" or
 *     "Stun" states, and regardless of how many actions are performed each
 *     turn.
 *
 * Version 1.49: February 20, 2025
 * * Bug Fixes!
 * ** Fixed a bug where causing a dead battler to refresh afterwards would
 *    yield multiple death states on that battler. Fix made by Arisu.
 * * Compatibility Update!
 * ** Updated for RPG Maker MZ Core Scripts 1.9.0!
 * *** Better compatibility with different icon sizes.
 *
 * Version 1.48: December 19, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** Auras & Miasmas added by Olivia:
 * *** Auras are a type passive that affects an allied party. Miasmas are a
 *     type of passive that affects an opposing party. Auras and Miasmas only
 *     need to come from a single source to give an entire party or troop a
 *     passive provided that the battler emitting the aura/miasma is alive and
 *     in battle.
 * ** New Notetags added by Olivia:
 * *** <Aura State: x>
 * **** Emits an aura that affects the battler's allies and gives each affected
 *      member passive state(s) 'x'.
 * *** <Miasma State: x>
 * **** Emits an aura that affects the battler's opponents and gives each
 *      affected member passive state(s) 'x'.
 * *** <Not User Aura>
 * **** Prevents the emitting user from being affected by the related aura.
 * *** <Allow Dead Aura>
 * *** <Allow Dead Miasma>
 * **** Allows aura/miasma to continue emitting even after the emitting user is
 *      in a dead state.
 * *** <Dead Aura Only>
 * *** <Dead Miasma Only>
 * **** Allows aura/miasma to only emit if the emitting user is in a dead state
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.47: August 29, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New notetags added by Arisu:
 * *** <Bypass State Damage Removal: id/name>
 * **** When this skill/item is used to attack an enemy with the listed state
 *      that would normally have on damage removal (ie Sleep).
 * **** This can be used for attacks like "Dream Eater" that would prevent
 *      waking up a sleeping opponent.
 * *** <Bypass State Damage Removal as Attacker: id/name>
 * **** When an attacker with an associated trait object that has this notetag
 *      would attack an enemy with the listed state, bypass on damage removal.
 * **** This can be used for effects like "Sleep Striker" that would prevent
 *      the attacker from waking up a sleeping opponent.
 * *** <Bypass State Damage Removal as Target: id/name>
 * **** When a target with an associated trait object that has this notetag is
 *      attacked as the target with the listed state, bypass on damage removal.
 * **** This can be used for effects like "Deep Sleep" that would prevent the
 *      attacked target from waking up.
 *
 * Version 1.46: July 18, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameter added by Irina:
 * *** Parameters > Skill Settings > Skill Types > Sort: Alphabetical
 * **** Insert the ID's of Skill Types you want sorted alphabetically.
 * ** New notetags added by Irina:
 * *** <ID Sort Priority: x>
 * **** Used for Scene_Skill.
 * **** Changes sorting priority by ID for skill to 'x'.
 * **** Default priority level is '50'.
 * **** Skills with higher priority values will be sorted higher up on the list
 *      while lower values will be lower on the list.
 *
 * Version 1.45: May 16, 2024
 * * Bug Fixes!
 * ** Fixed a problem with passive state conditional notetags not working
 *    properly. Fix made by Irina.
 *
 * Version 1.44: April 18, 2024
 * * Bug Fixes!
 * ** Fixed a bug where passive states would not appear. Fix made by Olivia.
 * ** Fixed a bug where a crash would occur if certain plugins cleared the
 *    passive state cache midway through trying to register it. Fix by Olivia.
 * * Optimization Update!
 * ** Plugin should run more optimized.
 * ** States with lots and lots of text data within their notes will no longer
 *    cause FPS drops.
 *
 * Version 1.43: January 18, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Commands added by Arisu!
 * *** Skill Cost: Emulate Actor Pay
 * *** Skill Cost: Emulate Enemy Pay
 * **** Target actor(s)/enemy(s) emulates paying for skill cost.
 * *** State Turns: Actor State Turns Change By
 * *** State Turns: Actor State Turns Change To
 * *** State Turns: Enemy State Turns Change By
 * *** State Turns: Enemy State Turns Change To
 * **** Changes actor(s)/enemy(s) state turns to a specific value/by an amount.
 * **** Only works on states that can have turns.
 *
 * Version 1.42: November 16, 2023
 * * Bug Fixes!
 * ** 'origin' variable was not working properly for <JS On Expire State>
 *    JavaScript notetag. Should now be working properly. Fix made by Irina.
 *
 * Version 1.41: September 14, 2023
 * * Bug Fixes!
 * ** Fixed a bug that prevented <Max Turns: x> for states from working due to
 *    one of the recent updates. Fix made by Arisu.
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 * * Documentation Update!
 * ** Apparently, we never put <Max Turns: x> in the help notetag section.
 *    Woops... It's there now.
 *
 * Version 1.40: August 17, 2023
 * * Bug Fixes!
 * ** Fixed a bug involving the "Item Cost" skill cost type found in the Plugin
 *    Parameters when involving consumable items.
 * *** If you want to acquire these settings for an already-existing project,
 *     do either of the following:
 * **** Delete the existing VisuMZ_1_SkillsStatesCore.js in the Plugin Manager
 *      list and install the newest version.
 * **** Or create a new project, install VisuMZ_1_SkillsStatesCore.js there,
 *      then copy over the "Item Cost" plugin parameters found in the "Skill
 *      Cost Types" plugin parameter settings to your current project.
 *
 * Version 1.39: July 13, 2023
 * * Feature Update!
 * ** Updated the "Item Cost" skill cost type found in the Plugin Parameters to
 *    no longer consume items that are key items or nonconsumable.
 * *** If you want to acquire these settings for an already-existing project,
 *     do either of the following:
 * **** Delete the existing VisuMZ_1_SkillsStatesCore.js in the Plugin Manager
 *      list and install the newest version.
 * **** Or create a new project, install VisuMZ_1_SkillsStatesCore.js there,
 *      then copy over the "Item Cost" plugin parameters found in the "Skill
 *      Cost Types" plugin parameter settings to your current project.
 *
 * Version 1.38: March 16, 2023
 * * Documentation Update!
 * ** Help file updated for new features.
 * ** Added segment to <Replace x Gauge: type> in documentation:
 * *** Does not work with 'Item Cost', 'Weapon Cost', or 'Armor Cost'.
 * * New Features!
 * ** New "Skill Cost Type" and notetags added by Arisu and sponsored by FAQ.
 * *** <Item Cost: x name>
 * *** <Weapon Cost: x name>
 * *** <Armor Cost: x name>
 * **** The skill will consume items, weapons, and/or armors in order to be
 *      used. Even non-consumable items will be consumed.
 * *** <Item Cost Max/Min: x name>
 * *** <Weapon Cost Max/Min: x name>
 * *** <Armor Cost Max/Min: x name>
 * **** Sets up a maximum/minimum cost for the item, weapon, armor type costs.
 * *** <Item Cost: x% name>
 * *** <Weapon Cost: x% name>
 * *** <Armor Cost: x% name>
 * **** Alters cost rate of skills that would consume item, weapon, or armor.
 * *** <Item Cost: +/-x name>
 * *** <Weapon Cost: +/-x name>
 * *** <Armor Cost: +/-x name>
 * **** Alters flat costs of skills that would consume item, weapon, or armor.
 * *** <Replace Item name1 Cost: name2>
 * *** <Replace Weapon name1 Cost: name2>
 * *** <Replace Armor name1 Cost: name2>
 * **** Replaces item, weapon, or armor to be consumed for another type.
 * *** Projects with the Skills and States Core already installed will not have
 *     this update, but you can copy over the settings from a new project with
 *     the following steps:
 * **** Create a new project. Install Skills and States Core. Open up the new
 *      project's 'Skill Cost Types'.
 * **** Right click the 'Item Cost' option(s) and click copy.
 * **** Go to the target project's Skills and States Core's 'Skill Cost Types'
 *      plugin parameter. Paste the command where you want it to go.
 * **** Only 'Item Cost' is needed as it encompasses all three types for item,
 *      weapon, and armor costs.
 *
 * Version 1.38: February 16, 2023
 * * Compatibility Update!
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.37: January 20, 2023
 * * Bug Fixes!
 * ** Fixed a bug that caused equipment to unequip if the needed equipment
 *    traits came from passive states upon learning new skills. Fix by Irina.
 *
 * Version 1.36: December 15, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * Feature Update!
 * ** When enemies are defeated with their entire party having a state with the
 *    <Group Defeat> notetag, then the party will gain EXP, Gold, and Drops
 *    before when they wouldn't. Update made by Irina.
 * * New Features!
 * ** New Plugin Parameter added by Irina!
 * *** Plugin Parameters > Skill Settings > Skill Type Window > Window Width
 * **** What is the desired pixel width of this window? Default: 240
 *
 * Verison 1.35: October 13, 2022
 * * Feature Update!
 * ** Default values for Passive States > Cache > Switch Refresh? and Variable
 *    Refresh? are now set to "false" in order to prevent sudden lag spikes for
 *    those who are unfamiliar with how this setting works.
 * ** Update made by Irina.
 *
 * Version 1.34: September 29, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameters added by Irina and sponsored by AndyL:
 * *** Plugin Parameters > Gauge Settings
 * **** These settings allow you to make minor tweaks to how the gauges look
 *      ranging from the color used for the labels to the outline types used
 *      for the values.
 *
 * Version 1.33: August 11, 2022
 * * Bug Fixes!
 * ** Fixed a crash that occurs when performing a custom action sequence
 *    without a skill attached to it. Fix made by Olivia.
 *
 * Version 1.32: June 16, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameters added by Arisu:
 * *** Plugin Parameters > Passive State Settings > Cache > Switch Refresh?
 * *** Plugin Parameters > Passive State Settings > Cache > Variable Refresh?
 * **** Refresh all battle members when switches/variables are changed in
 *      battle?
 * **** This is primarily used for passive state conditions involve parameters
 *      that do not update due to cached data until a refresh occurs.
 * **** If this is on, do not spam Switch/Variable changes during battle in
 *      order to prevent lag spikes.
 *
 * Version 1.31: April 28, 2022
 * * Bug Fixes!
 * ** Custom Slip Damage JS is now totalled correctly into regular slip damage
 *    totals for damage popups. Fix made by Olivia.
 *
 * Version 1.30: April 14, 2022
 * * Feature Update!
 * ** Changed the state data removal timing to be after JS notetag effects
 *    take place in order for data such as origin data to remain intact. Update
 *    made by Irina.
 *
 * Version 1.29: March 31, 2022
 * * Bug Fixes!
 * ** Fixed an error with <State x Category Remove: y> not countaing correctly
 *    unless the state count matched the exact amount. The notetag effect
 *    should work properly now. Fix made by Olivia.
 *
 * Version 1.28: March 10, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * Feature Update!
 * ** <State x Category Remove: All> updated to allow multiple cases in a
 *    single notebox. Updated by Arisu.
 * * New Features!
 * ** New Notetag added by Arisu and sponsored by Archeia!
 * *** <Remove Other x States>
 * **** When the state with this notetag is added, remove other 'x' category
 *      states from the battler (except for the state being added).
 * **** Useful for thing state types like stances and forms that there is
 *      usually only one active at a time.
 *
 * Version 1.27: January 27, 2022
 * * Bug Fixes!
 * ** Custom JS Slip Damage/Healing values should now be recalculated on
 *    demand. Fix made by Olivia.
 *
 * Version 1.26: January 20, 2022
 * * Documentation Update!
 * ** Help file updated for new features.
 * * Feature Update!
 * ** Conditional Passive Bypass check is now stronger to prevent even more
 *    infinite loops from happening. Update made by Olivia.
 * * New Features!
 * ** New Plugin Parameter added by Olivia:
 * *** Plugin Parameters > State Settings > General > Turn End on Map
 * **** Update any state and buff turns on the map after this many steps.
 * **** Use 0 to disable.
 *
 * Version 1.25: November 11, 2021
 * * Bug Fixes!
 * ** Hidden skill notetags should no longer crash upon not detecting actors
 *    for learned skills. Fix made by Olivia.
 *
 * Version 1.24: November 4, 2021
 * * Documentation Update!
 * ** Added section: "Slip Damage Popup Clarification"
 * *** Slip Damage popups only show one popup for HP, MP, and TP each and it is
 *     the grand total of all the states and effects combined regardless of the
 *     number of states and effects on a battler. This is how it is in vanilla
 *     RPG Maker MZ and this is how we intend for it to be with the VisuStella
 *     MZ library.
 * *** This is NOT a bug!
 * *** The reason we are not changing this is because it does not properly
 *     relay information to the player accurately. When multiple popups appear,
 *     players only have roughly a second and a half to calculate it all for
 *     any form of information takeaway. We feel it is better suited for the
 *     player's overall convenience to show a cummulative change and steer the
 *     experience towards a more positive one.
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.23: September 17, 2021
 * * Compatibility Update!
 * ** RPG Maker MZ 1.3.3 compatibility.
 * *** Updated how gauges are drawn.
 * *** Skill Cost Types Plugin Parameters need to be updated for those who want
 *     the updated gauges. This can be done easily with the following steps:
 * **** Step 1: Create a new project.
 * **** Step 2: Install Skills and States Core version 1.23 into it.
 * **** Step 3: Copy the Plugin Parameter Settings for "Skill Cost Types".
 * **** Step 4: Return back to your original project.
 * **** Step 5: Paste Plugin Parameter Settings on top of "Skill Cost Types".
 *
 * Version 1.22: August 6, 2021
 * * Documentation Update!
 * ** "Action End Removal for States" under Major Updates is changed to:
 * *** If your Plugin Parameter settings for "Action End Update" are enabled,
 *     then "Action End" has been updated so that it actually applies per
 *     action used instead of just being at the start of a battler's action
 *     set.
 * *** However, there are side effects to this: if a state has the "Cannot
 *     Move" restriction along with the "Action End" removal timing, then
 *     unsurprisingly, the state will never wear off because it's now based on
 *     actual actions ending. To offset this and remove confusion, "Action End"
 *     auto-removal timings for states with "Cannot Move" restrictions will be
 *     turned into "Turn End" auto-removal timings while the "Action End
 *     Update" is enabled.
 * *** This automatic change won't make it behave like an "Action End" removal
 *     timing would, but it's better than completely softlocking a battler.
 * * Feature Update!
 * ** Those using "Cannot Move" states with "Action End" auto-removal will now
 *    have be automatically converted into "Turn End" auto-removal if the
 *    plugin parameter "Action End Update" is set to true. Update by Irina.
 *
 * Version 1.21: July 30, 2021
 * * Documentation Update!
 * ** Expanded "Action End Removal for States" section in Major Changes.
 * *** These changes have been in effect since Version 1.07 but have not been
 *     explained in excess detail in the documentation since.
 * **** Action End has been updated so that it actually applies per action used
 *      instead of just being at the start of a battler's action set. However,
 *      there are side effects to this: if a state has the "Cannot Move"
 *      restriction along with the "Action End" removal timing, then
 *      unsurprisingly, the state will never wear off because it's now based on
 *      actual actions ending. There are two solutions to this:
 * **** Don't make "Cannot Move" restriction states with "Action End". This is
 *      not a workaround. This is how the state removal is intended to work
 *      under the new change.
 * **** Go to the Skills & States Core Plugin Parameters, go to State
 *      Setttings, look for "Action End Update", and set it to false. You now
 *      reverted the removal timing system back to how it originally was in RPG
 *      Maker MZ's default battle system where it only updates based on an
 *      action set rather than per actual action ending.
 *
 * Version 1.20: June 18, 2021
 * * Feature Update!
 * ** Updated automatic caching for conditional passive states to update more
 *    efficiently. Update made by Arisu.
 *
 * Version 1.19: June 4, 2021
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.18: May 21, 2021
 * * Documentation Update
 * ** Added "Passive State Clarification" section.
 * *** As there is a lot of confusion regarding how passive states work and how
 *     people still miss the explanations found in the "Passive State Notetags"
 *     section AND the "Plugin Parameters: Passive State Settings", we are
 *     adding a third section to explain how they work.
 * *** All three sections will contain the full detailed explanation of how
 *     passive states work to clear common misconceptions about them.
 *
 * Version 1.17: May 7, 2021
 * * Bug Fixes
 * ** State category removal is now usable outside of battle. Fix by Irina.
 *
 * Version 1.16: April 30, 2021
 * * Bug Fixes!
 * ** When states with step removal have the <No Recover All Clear> or
 *    <No Death Clear> notetags, their step counter is no longer reset either.
 *    Fix made by Irina.
 * * New Features!
 * ** New notetag added by Arisu!
 * *** <List Name: name>
 * **** Makes the name of the skill appear different when show in the skill
 *      list. Using \V[x] as a part of the name will display that variable.
 *
 * Version 1.15: March 19, 2021
 * * Compatibility Update
 * ** Added compatibility functionality for future plugins.
 *
 * Version 1.14: March 12, 2021
 * * Bug Fixes!
 * ** Max HP Buff/Debuff should now display its turn counter. Fix by Yanfly.
 * * Documentation Update!
 * ** For the <JS Passive Condition>, we've added documentation on the
 *    limitations of passive conditions since they have been reported as bug
 *    reports, when in reality, they are failsafes to prevent infinite loops.
 *    Such limitations include the following:
 * *** A passive state that requires another passive state
 * *** A passive state that requires a trait effect from another state
 * *** A passive state that requires a parameter value altered by another state
 * *** A passive state that requires equipment to be worn but its equipment
 *     type access is provided by another state.
 * *** Anything else that is similar in style.
 *
 * Version 1.13: February 26, 2021
 * * Documentation Update!
 * ** For <JS type Slip Damage> and <JS type Slip Heal> notetags, added the
 *    following notes:
 * *** When these states are applied via action effects, the slip calculations
 *     are one time calculations made upon applying and the damage is cached to
 *     be used for future on regeneration calculations.
 * *** For that reason, do not include game mechanics here such as adding
 *     states, buffs, debuffs, etc. as this notetag is meant for calculations
 *     only. Use the VisuStella Battle Core's <JS Pre-Regenerate> and
 *     <JS Post-Regenerate> notetags for game mechanics instead.
 * *** Passive states and states with the <JS Slip Refresh> notetag are exempt
 *     from the one time calculation and recalculated each regeneration phase.
 * * Feature Update!
 * ** Changed slip refresh requirements to entail <JS Slip Refresh> notetag for
 *    extra clarity. Update made by Olivia.
 *
 * Version 1.12: February 19, 2021
 * * Feature Update
 * ** Changed the way passive state infinite stacking as a blanket coverage.
 *    Update made by Olivia.
 *
 * Version 1.11: February 12, 2021
 * * Bug Fixes!
 * ** Added a check to prevent passive states from infinitely stacking. Fix
 *    made by Olivia.
 *
 * Version 1.10: January 15, 2021
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Feature!
 * ** New Plugin Parameters added
 * *** Plugin Parameters > Skill Settings > Background Type
 *
 * Version 1.09: January 1, 2021
 * * Bug Fixes!
 * ** Custom JS TP slip damage and healing should now work properly.
 *    Fix made by Yanfly.
 *
 * Version 1.08: December 25, 2020
 * * Bug Fixes!
 * ** <JS On Add State> should no longer trigger multiple times for the death
 *    state. Fix made by Yanfly.
 * * Documentation Update!
 * ** Added documentation for updated feature(s)!
 * * Feature Update!
 * ** <No Death Clear> can now allow the affected state to be added to an
 *    already dead battler. Update made by Yanfly.
 *
 * Version 1.07: December 18, 2020
 * * Documentation Update!
 * ** Added documentation for new feature(s)!
 * * New Features!
 * ** New notetags added by Yanfly:
 * *** <Passive Condition Multiclass: id>
 * *** <Passive Condition Multiclass: id, id, id>
 * *** <Passive Condition Multiclass: name>
 * *** <Passive Condition Multiclass: name, name, name>
 * ** New Plugin Parameter added by Yanfly.
 * *** Plugin Parameters > States > General > Action End Update
 * **** States with "Action End" auto-removal will also update turns at the end
 *      of each action instead of all actions.
 * ***** Turn this off if you wish for state turn updates to function like they
 *       do by default for "Action End".
 *
 * Version 1.06: December 4, 2020
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.05: November 15, 2020
 * * Bug Fixes!
 * ** The alignment of the Skill Type Window is now fixed and will reflect upon
 *    the default settings. Fix made by Yanfly.
 * * Documentation Update!
 * ** Added documentation for new feature(s)!
 * * New Features!
 * ** <State x Category Remove: All> notetag added by Yanfly.
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.04: September 27, 2020
 * * Documentation Update
 * ** "Use Updated Layout" plugin parameters now have the added clause:
 *    "This will override the Core Engine windows settings." to reduce
 *    confusion. Added by Irina.
 *
 * Version 1.03: September 13, 2020
 * * Bug Fixes!
 * ** <JS type Slip Damage> custom notetags now work for passive states. Fix
 *    made by Olivia.
 * ** Setting the Command Window style to "Text Only" will no longer add in
 *    the icon text codes. Bug fixed by Yanfly.
 *
 * Version 1.02: August 30, 2020
 * * Bug Fixes!
 * ** The JS Notetags for Add, Erase, and Expire states are now fixed. Fix made
 *    by Yanfly.
 * * Documentation Update!
 * ** <Show if learned Skill: x> and <Hide if learned Skill: x> notetags have
 *    the following added to their descriptions:
 * *** This does not apply to skills added by traits on actors, classes, any
 *     equipment, or states. These are not considered learned skills. They are
 *     considered temporary skills.
 * * New Features!
 * ** Notetags added by Yanfly:
 * *** <Show if has Skill: x>
 * *** <Show if have All Skills: x,x,x>
 * *** <Show if have Any Skills: x,x,x>
 * *** <Show if has Skill: name>
 * *** <Show if have All Skills: name, name, name>
 * *** <Show if have Any Skills: name, name, name>
 * *** <Hide if has Skill: x>
 * *** <Hide if have All Skills: x,x,x>
 * *** <Hide if have Any Skills: x,x,x>
 * *** <Hide if has Skill: name>
 * *** <Hide if have All Skills: name, name, name>
 * *** <Hide if have Any Skills: name, name, name>
 * *** These have been added to remove the confusion regarding learned skills
 *     as skills added through trait effects are not considered learned skills
 *     by RPG Maker MZ.
 *
 * Version 1.01: August 23, 2020
 * * Bug Fixes!
 * ** Passive states from Elements & Status Menu Core are now functional.
 *    Fix made by Olivia.
 * * Compatibility Update
 * ** Extended functions to allow for better compatibility.
 * * Updated documentation
 * ** Explains that passive states are not directly applied and are therefore
 *    not affected by code such as "a.isStateAffected(10)".
 * ** Instead, use "a.states().includes($dataStates[10])"
 * ** "Use #rrggbb for a hex color." lines now replaced with
 *    "For a hex color, use #rrggbb with VisuMZ_1_MessageCore"
 *
 * Version 1.00: August 20, 2020
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillActorPaySkillCost
 * @text Skill Cost: Emulate Actor Pay
 * @desc Target actor(s) emulates paying for skill cost.
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) will pay skill cost.
 * @default ["1"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to emulate paying the skill cost for?
 * @default 99
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SkillEnemyPaySkillCost
 * @text Skill Cost: Emulate Enemy Pay
 * @desc Target enemy(s) emulates paying for skill cost.
 *
 * @arg EnemyIndex:arraynum
 * @text Enemy Index(es)
 * @type actr[]
 * @desc Select which enemy index(es) will pay skill cost.
 * @default ["1"]
 *
 * @arg SkillID:num
 * @text Skill ID
 * @type skill
 * @desc What is the ID of the skill to emulate paying the skill cost for?
 * @default 99
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_StateTurns
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command StateTurnsActorChangeBy
 * @text State Turns: Actor State Turns Change By
 * @desc Changes actor(s) state turns by an amount.
 * Only works on states that can have turns.
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateID:num
 * @text State ID
 * @type state
 * @desc What is the ID of the state you wish to change turns for?
 * Only works on states that can have turns.
 * @default 5
 *
 * @arg Turns:eval
 * @text Change Turns By
 * @desc How many turns should the state be changed to?
 * You may use JavaScript code.
 * @default +1
 *
 * @arg AutoAddState:eval
 * @text Auto-Add State?
 * @type boolean
 * @on Auto-Add
 * @off Don't Add
 * @desc Automatically adds state if actor(s) does not have it applied?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command StateTurnsActorChangeTo
 * @text State Turns: Actor State Turns Change To
 * @desc Changes actor(s) state turns to a specific value.
 * Only works on states that can have turns.
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateID:num
 * @text State ID
 * @type state
 * @desc What is the ID of the state you wish to change turns for?
 * Only works on states that can have turns.
 * @default 5
 *
 * @arg Turns:eval
 * @text Change Turns To
 * @desc How many turns should the state be changed to?
 * You may use JavaScript code.
 * @default 10
 *
 * @arg AutoAddState:eval
 * @text Auto-Add State?
 * @type boolean
 * @on Auto-Add
 * @off Don't Add
 * @desc Automatically adds state if actor(s) does not have it applied?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command StateTurnsEnemyChangeBy
 * @text State Turns: Enemy State Turns Change By
 * @desc Changes enemy(s) state turns by an amount.
 * Only works on states that can have turns.
 *
 * @arg EnemyIndex:arraynum
 * @text Enemy Index(es)
 * @type actr[]
 * @desc Select which enemy index(es) to affect.
 * @default ["1"]
 *
 * @arg StateID:num
 * @text State ID
 * @type state
 * @desc What is the ID of the state you wish to change turns for?
 * Only works on states that can have turns.
 * @default 5
 *
 * @arg Turns:eval
 * @text Change Turns By
 * @desc How many turns should the state be changed to?
 * You may use JavaScript code.
 * @default +1
 *
 * @arg AutoAddState:eval
 * @text Auto-Add State?
 * @type boolean
 * @on Auto-Add
 * @off Don't Add
 * @desc Automatically adds state if enemy(s) does not have it applied?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command StateTurnsEnemyChangeTo
 * @text State Turns: Enemy State Turns Change To
 * @desc Changes enemy(s) state turns to a specific value.
 * Only works on states that can have turns.
 *
 * @arg EnemyIndex:arraynum
 * @text Enemy Index(es)
 * @type actr[]
 * @desc Select which enemy index(es) to affect.
 * @default ["1"]
 *
 * @arg StateID:num
 * @text State ID
 * @type state
 * @desc What is the ID of the state you wish to change turns for?
 * Only works on states that can have turns.
 * @default 5
 *
 * @arg Turns:eval
 * @text Change Turns To
 * @desc How many turns should the state be changed to?
 * You may use JavaScript code.
 * @default 10
 *
 * @arg AutoAddState:eval
 * @text Auto-Add State?
 * @type boolean
 * @on Auto-Add
 * @off Don't Add
 * @desc Automatically adds state if enemy(s) does not have it applied?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param SkillsStatesCore
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param Skills:struct
 * @text Skill Settings
 * @type struct<Skills>
 * @desc Adjust general skill settings here.
 * @default {"General":"","EnableLayout:eval":"true","LayoutStyle:str":"upper/left","SkillTypeWindow":"","CmdStyle:str":"auto","CmdTextAlign:str":"left","ListWindow":"","ListWindowCols:num":"1","ShopStatusWindow":"","ShowShopStatus:eval":"true","SkillSceneAdjustSkillList:eval":"true","SkillMenuStatusRect:func":"\"const ww = this.shopStatusWidth();\\nconst wh = this._itemWindow.height;\\nconst wx = Graphics.boxWidth - this.shopStatusWidth();\\nconst wy = this._itemWindow.y;\\nreturn new Rectangle(wx, wy, ww, wh);\"","SkillTypes":"","HiddenSkillTypes:arraynum":"[]","BattleHiddenSkillTypes:arraynum":"[]","IconStypeNorm:num":"78","IconStypeMagic:num":"79","CustomJS":"","SkillConditionJS:func":"\"// Declare Variables\\nconst skill = arguments[0];\\nconst user = this;\\nconst target = this;\\nconst a = this;\\nconst b = this;\\nlet enabled = true;\\n\\n// Perform Checks\\n\\n\\n// Return boolean\\nreturn enabled;\""}
 *
 * @param Costs:arraystruct
 * @text Skill Cost Types
 * @parent Skills:struct
 * @type struct<Cost>[]
 * @desc A list of all the skill cost types added by this plugin
 * and the code that controls them in-game.
 * @default ["{\"Name:str\":\"HP\",\"Settings\":\"\",\"Icon:num\":\"0\",\"FontColor:str\":\"20\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\nif (note.match(/<HP COST:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost += Number(RegExp.$1);\\\\n}\\\\nif (note.match(/<HP COST:[ ](\\\\\\\\d+)([%％])>/i)) {\\\\n    cost += Math.ceil(Number(RegExp.$1) * user.mhp / 100);\\\\n}\\\\nif (note.match(/<JS HP COST>\\\\\\\\s*([\\\\\\\\s\\\\\\\\S]*)\\\\\\\\s*<\\\\\\\\/JS HP COST>/i)) {\\\\n    const code = String(RegExp.$1);\\\\n    eval(code);\\\\n}\\\\n\\\\n// Apply Trait Cost Alterations\\\\nif (cost > 0) {\\\\n    const rateNote = /<HP COST:[ ](\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)([%％])>/i;\\\\n    const rates = user.traitObjects().map((obj) => (obj && obj.note.match(rateNote) ? Number(RegExp.$1) / 100 : 1));\\\\n    const flatNote = /<HP COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)>/i;\\\\n    const flats = user.traitObjects().map((obj) => (obj && obj.note.match(flatNote) ? Number(RegExp.$1) : 0));\\\\n    cost = rates.reduce((r, rate) => r * rate, cost);\\\\n    cost = flats.reduce((r, flat) => r + flat, cost);\\\\n    cost = Math.max(1, cost);\\\\n}\\\\n\\\\n// Set Cost Limits\\\\nif (note.match(/<HP COST MAX:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.min(cost, Number(RegExp.$1));\\\\n}\\\\nif (note.match(/<HP COST MIN:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.max(cost, Number(RegExp.$1));\\\\n}\\\\n\\\\n// Return cost value\\\\nreturn Math.round(Math.max(0, cost));\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nif (cost <= 0) {\\\\n    return true;\\\\n} else {\\\\n    return user._hp > cost;\\\\n}\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Process Payment\\\\nuser._hp -= cost;\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn cost > 0;\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = TextManager.hp;\\\\nconst icon = settings.Icon;\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\ntext += '%1 %2'.format(cost, name);\\\\n\\\\n// Text: Add Icon\\\\nif (icon  > 0) {\\\\n    text += '\\\\\\\\\\\\\\\\I[%1]'.format(icon);\\\\n}\\\\n\\\\n// Return text\\\\nreturn text;\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.mhp;\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.hp;\\\"\",\"GaugeDrawJS:func\":\"\\\"// Declare Settings\\\\nconst color1 = ColorManager.hpGaugeColor1();\\\\nconst color2 = ColorManager.hpGaugeColor2();\\\\nconst label = TextManager.hpA;\\\\n\\\\n// Declare Variables\\\\nconst sprite = this;\\\\nconst settings = sprite._costSettings;\\\\nconst bitmap = sprite.bitmap;\\\\nconst user = sprite._battler;\\\\nconst currentValue = sprite.currentDisplayedValue();\\\\nconst bitmapWidth = sprite.bitmapWidth();\\\\nconst bitmapHeight = sprite.textHeight ? sprite.textHeight() : sprite.bitmapHeight();\\\\nconst gaugeHeight = sprite.gaugeHeight();\\\\n\\\\n// Draw Gauge\\\\nconst gx = 0;\\\\nconst gy = bitmapHeight - gaugeHeight;\\\\nconst gw = bitmapWidth - gx;\\\\nconst gh = gaugeHeight;\\\\nthis.drawFullGauge(color1, color2, gx, gy, gw, gh);\\\\n\\\\n// Draw Label\\\\nconst lx = 4;\\\\nconst ly = 0;\\\\nconst lw = bitmapWidth;\\\\nconst lh = bitmapHeight;\\\\nsprite.setupLabelFont();\\\\nbitmap.paintOpacity = 255;\\\\nbitmap.drawText(label, lx, ly, lw, lh, \\\\\\\"left\\\\\\\");\\\\n\\\\n// Draw Value\\\\nconst vw = bitmapWidth - 2;\\\\nconst vh = bitmapHeight;\\\\nsprite.setupValueFont();\\\\nbitmap.textColor = ColorManager.hpColor(user);\\\\nbitmap.drawText(currentValue, 0, 0, vw, vh, \\\\\\\"right\\\\\\\");\\\"\"}","{\"Name:str\":\"MP\",\"Settings\":\"\",\"Icon:num\":\"0\",\"FontColor:str\":\"23\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\ncost = Math.floor(skill.mpCost * user.mcr);\\\\nif (note.match(/<MP COST:[ ](\\\\\\\\d+)([%％])>/i)) {\\\\n    cost += Math.ceil(Number(RegExp.$1) * user.mmp / 100);\\\\n}\\\\nif (note.match(/<JS MP COST>\\\\\\\\s*([\\\\\\\\s\\\\\\\\S]*)\\\\\\\\s*<\\\\\\\\/JS MP COST>/i)) {\\\\n    const code = String(RegExp.$1);\\\\n    eval(code);\\\\n}\\\\n\\\\n// Apply Trait Cost Alterations\\\\nif (cost > 0) {\\\\n    const rateNote = /<MP COST:[ ](\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)([%％])>/i;\\\\n    const rates = user.traitObjects().map((obj) => (obj && obj.note.match(rateNote) ? Number(RegExp.$1) / 100 : 1));\\\\n    const flatNote = /<MP COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)>/i;\\\\n    const flats = user.traitObjects().map((obj) => (obj && obj.note.match(flatNote) ? Number(RegExp.$1) : 0));\\\\n    cost = rates.reduce((r, rate) => r * rate, cost);\\\\n    cost = flats.reduce((r, flat) => r + flat, cost);\\\\n    cost = Math.max(1, cost);\\\\n}\\\\n\\\\n// Set Cost Limits\\\\nif (note.match(/<MP COST MAX:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.min(cost, Number(RegExp.$1));\\\\n}\\\\nif (note.match(/<MP COST MIN:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.max(cost, Number(RegExp.$1));\\\\n}\\\\n\\\\n// Return cost value\\\\nreturn Math.round(Math.max(0, cost));\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn user._mp >= cost;\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Process Payment\\\\nuser._mp -= cost;\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn cost > 0;\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = TextManager.mp;\\\\nconst icon = settings.Icon;\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<#%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\ntext += '%1 %2'.format(cost, name);\\\\n\\\\n// Text: Add Icon\\\\nif (icon  > 0) {\\\\n    text += '\\\\\\\\\\\\\\\\I[%1]'.format(icon);\\\\n}\\\\n\\\\n// Return text\\\\nreturn text;\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.mmp;\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.mp;\\\"\",\"GaugeDrawJS:func\":\"\\\"// Declare Settings\\\\nconst color1 = ColorManager.mpGaugeColor1();\\\\nconst color2 = ColorManager.mpGaugeColor2();\\\\nconst label = TextManager.mpA;\\\\n\\\\n// Declare Variables\\\\nconst sprite = this;\\\\nconst settings = sprite._costSettings;\\\\nconst bitmap = sprite.bitmap;\\\\nconst user = sprite._battler;\\\\nconst currentValue = sprite.currentDisplayedValue();\\\\nconst bitmapWidth = sprite.bitmapWidth();\\\\nconst bitmapHeight = sprite.textHeight ? sprite.textHeight() : sprite.bitmapHeight();\\\\nconst gaugeHeight = sprite.gaugeHeight();\\\\n\\\\n// Draw Gauge\\\\nconst gx = 0;\\\\nconst gy = bitmapHeight - gaugeHeight;\\\\nconst gw = bitmapWidth - gx;\\\\nconst gh = gaugeHeight;\\\\nthis.drawFullGauge(color1, color2, gx, gy, gw, gh);\\\\n\\\\n// Draw Label\\\\nconst lx = 4;\\\\nconst ly = 0;\\\\nconst lw = bitmapWidth;\\\\nconst lh = bitmapHeight;\\\\nsprite.setupLabelFont();\\\\nbitmap.paintOpacity = 255;\\\\nbitmap.drawText(label, lx, ly, lw, lh, \\\\\\\"left\\\\\\\");\\\\n\\\\n// Draw Value\\\\nconst vw = bitmapWidth - 2;\\\\nconst vh = bitmapHeight;\\\\nsprite.setupValueFont();\\\\nbitmap.textColor = ColorManager.mpColor(user);\\\\nbitmap.drawText(currentValue, 0, 0, vw, vh, \\\\\\\"right\\\\\\\");\\\"\"}","{\"Name:str\":\"TP\",\"Settings\":\"\",\"Icon:num\":\"0\",\"FontColor:str\":\"29\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\ncost = skill.tpCost;\\\\nif (note.match(/<TP COST:[ ](\\\\\\\\d+)([%％])>/i)) {\\\\n    cost += Math.ceil(Number(RegExp.$1) * user.maxTp() / 100);\\\\n}\\\\nif (note.match(/<JS TP COST>\\\\\\\\s*([\\\\\\\\s\\\\\\\\S]*)\\\\\\\\s*<\\\\\\\\/JS TP COST>/i)) {\\\\n    const code = String(RegExp.$1);\\\\n    eval(code);\\\\n}\\\\n\\\\n// Apply Trait Cost Alterations\\\\nif (cost > 0) {\\\\n    const rateNote = /<TP COST:[ ](\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)([%％])>/i;\\\\n    const rates = user.traitObjects().map((obj) => (obj && obj.note.match(rateNote) ? Number(RegExp.$1) / 100 : 1));\\\\n    const flatNote = /<TP COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)>/i;\\\\n    const flats = user.traitObjects().map((obj) => (obj && obj.note.match(flatNote) ? Number(RegExp.$1) : 0));\\\\n    cost = rates.reduce((r, rate) => r * rate, cost);\\\\n    cost = flats.reduce((r, flat) => r + flat, cost);\\\\n    cost = Math.max(1, cost);\\\\n}\\\\n\\\\n// Set Cost Limits\\\\nif (note.match(/<TP COST MAX:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.min(cost, Number(RegExp.$1));\\\\n}\\\\nif (note.match(/<TP COST MIN:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.max(cost, Number(RegExp.$1));\\\\n}\\\\n\\\\n// Return cost value\\\\nreturn Math.round(Math.max(0, cost));\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn user._tp >= cost;\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Process Payment\\\\nuser._tp -= cost;\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn cost > 0;\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = TextManager.tp;\\\\nconst icon = settings.Icon;\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<#%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\ntext += '%1 %2'.format(cost, name);\\\\n\\\\n// Text: Add Icon\\\\nif (icon  > 0) {\\\\n    text += '\\\\\\\\\\\\\\\\I[%1]'.format(icon);\\\\n}\\\\n\\\\n// Return text\\\\nreturn text;\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.maxTp();\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn user.tp;\\\"\",\"GaugeDrawJS:func\":\"\\\"// Declare Settings\\\\nconst color1 = ColorManager.tpGaugeColor1();\\\\nconst color2 = ColorManager.tpGaugeColor2();\\\\nconst label = TextManager.tpA;\\\\n\\\\n// Declare Variables\\\\nconst sprite = this;\\\\nconst settings = sprite._costSettings;\\\\nconst bitmap = sprite.bitmap;\\\\nconst user = sprite._battler;\\\\nconst currentValue = sprite.currentDisplayedValue();\\\\nconst bitmapWidth = sprite.bitmapWidth();\\\\nconst bitmapHeight = sprite.textHeight ? sprite.textHeight() : sprite.bitmapHeight();\\\\nconst gaugeHeight = sprite.gaugeHeight();\\\\n\\\\n// Draw Gauge\\\\nconst gx = 0;\\\\nconst gy = bitmapHeight - gaugeHeight;\\\\nconst gw = bitmapWidth - gx;\\\\nconst gh = gaugeHeight;\\\\nthis.drawFullGauge(color1, color2, gx, gy, gw, gh);\\\\n\\\\n// Draw Label\\\\nconst lx = 4;\\\\nconst ly = 0;\\\\nconst lw = bitmapWidth;\\\\nconst lh = bitmapHeight;\\\\nsprite.setupLabelFont();\\\\nbitmap.paintOpacity = 255;\\\\nbitmap.drawText(label, lx, ly, lw, lh, \\\\\\\"left\\\\\\\");\\\\n\\\\n// Draw Value\\\\nconst vw = bitmapWidth - 2;\\\\nconst vh = bitmapHeight;\\\\nsprite.setupValueFont();\\\\nbitmap.textColor = ColorManager.tpColor(user);\\\\nbitmap.drawText(currentValue, 0, 0, vw, vh, \\\\\\\"right\\\\\\\");\\\"\"}","{\"Name:str\":\"Gold\",\"Settings\":\"\",\"Icon:num\":\"0\",\"FontColor:str\":\"17\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\nif (note.match(/<GOLD COST:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost += Number(RegExp.$1);\\\\n}\\\\nif (note.match(/<GOLD COST:[ ](\\\\\\\\d+)([%％])>/i)) {\\\\n    cost += Math.ceil(Number(RegExp.$1) * $gameParty.gold() / 100);\\\\n}\\\\nif (note.match(/<JS GOLD COST>\\\\\\\\s*([\\\\\\\\s\\\\\\\\S]*)\\\\\\\\s*<\\\\\\\\/JS GOLD COST>/i)) {\\\\n    const code = String(RegExp.$1);\\\\n    eval(code);\\\\n}\\\\n\\\\n// Apply Trait Cost Alterations\\\\nif (cost > 0) {\\\\n    const rateNote = /<GOLD COST:[ ](\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)([%％])>/i;\\\\n    const rates = user.traitObjects().map((obj) => (obj && obj.note.match(rateNote) ? Number(RegExp.$1) / 100 : 1));\\\\n    const flatNote = /<GOLD COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)>/i;\\\\n    const flats = user.traitObjects().map((obj) => (obj && obj.note.match(flatNote) ? Number(RegExp.$1) : 0));\\\\n    cost = rates.reduce((r, rate) => r * rate, cost);\\\\n    cost = flats.reduce((r, flat) => r + flat, cost);\\\\n    cost = Math.max(1, cost);\\\\n}\\\\n\\\\n// Set Cost Limits\\\\nif (note.match(/<GOLD COST MAX:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.min(cost, Number(RegExp.$1));\\\\n}\\\\nif (note.match(/<GOLD COST MIN:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.max(cost, Number(RegExp.$1));\\\\n}\\\\n\\\\n// Return cost value\\\\nreturn Math.round(Math.max(0, cost));\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn $gameParty.gold() >= cost;\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Process Payment\\\\n$gameParty.loseGold(cost);\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn cost > 0;\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = TextManager.currencyUnit;\\\\nconst icon = settings.Icon;\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<#%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\ntext += '%1 %2'.format(cost, name);\\\\n\\\\n// Text: Add Icon\\\\nif (icon  > 0) {\\\\n    text += '\\\\\\\\\\\\\\\\I[%1]'.format(icon);\\\\n}\\\\n\\\\n// Return text\\\\nreturn text;\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn $gameParty.maxGold();\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn $gameParty.gold();\\\"\",\"GaugeDrawJS:func\":\"\\\"// Declare Variables\\\\nconst sprite = this;\\\\nconst settings = sprite._costSettings;\\\\nconst bitmap = sprite.bitmap;\\\\nconst user = sprite._battler;\\\\nconst currentValue = sprite.currentDisplayedValue();\\\\n\\\\n// Draw Label\\\\nconst label = TextManager.currencyUnit;\\\\nconst lx = 4;\\\\nconst ly = 0;\\\\nconst lw = sprite.bitmapWidth();\\\\nconst lh = sprite.bitmapHeight();\\\\nsprite.setupLabelFont();\\\\nbitmap.paintOpacity = 255;\\\\nbitmap.drawText(label, lx, ly, lw, lh, \\\\\\\"left\\\\\\\");\\\\n\\\\n// Draw Value\\\\nconst vw = sprite.bitmapWidth() - 2;\\\\nconst vh = sprite.bitmapHeight();\\\\nsprite.setupValueFont();\\\\nbitmap.textColor = ColorManager.normalColor();\\\\nbitmap.drawText(currentValue, 0, 0, vw, vh, \\\\\\\"right\\\\\\\");\\\"\"}","{\"Name:str\":\"Potion\",\"Settings\":\"\",\"Icon:num\":\"176\",\"FontColor:str\":\"0\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\nif (note.match(/<POTION COST:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost += Number(RegExp.$1);\\\\n}\\\\nif (note.match(/<JS POTION COST>\\\\\\\\s*([\\\\\\\\s\\\\\\\\S]*)\\\\\\\\s*<\\\\\\\\/JS POTION COST>/i)) {\\\\n    const code = String(RegExp.$1);\\\\n    eval(code);\\\\n}\\\\n\\\\n// Apply Trait Cost Alterations\\\\nif (cost > 0) {\\\\n    const rateNote = /<POTION COST:[ ](\\\\\\\\d+\\\\\\\\.?\\\\\\\\d*)([%％])>/i;\\\\n    const rates = user.traitObjects().map((obj) => (obj && obj.note.match(rateNote) ? Number(RegExp.$1) / 100 : 1));\\\\n    const flatNote = /<POTION COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)>/i;\\\\n    const flats = user.traitObjects().map((obj) => (obj && obj.note.match(flatNote) ? Number(RegExp.$1) : 0));\\\\n    cost = rates.reduce((r, rate) => r * rate, cost);\\\\n    cost = flats.reduce((r, flat) => r + flat, cost);\\\\n    cost = Math.max(1, cost);\\\\n}\\\\n\\\\n// Set Cost Limits\\\\nif (note.match(/<POTION COST MAX:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.min(cost, Number(RegExp.$1));\\\\n}\\\\nif (note.match(/<POTION COST MIN:[ ](\\\\\\\\d+)>/i)) {\\\\n    cost = Math.max(cost, Number(RegExp.$1));\\\\n}\\\\n\\\\n// Return cost value\\\\nreturn Math.round(Math.max(0, cost));\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst item = $dataItems[7];\\\\n\\\\n// Return Boolean\\\\nif (user.isActor() && cost > 0) {\\\\n    return $gameParty.numItems(item) >= cost;\\\\n} else {\\\\n    return true;\\\\n}\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst item = $dataItems[7];\\\\n\\\\n// Process Payment\\\\nif (user.isActor()) {\\\\n    $gameParty.loseItem(item, cost);\\\\n}\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Return Boolean\\\\nreturn cost > 0;\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst item = $dataItems[7];\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = settings.Name;\\\\nconst icon = settings.Icon;\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<#%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\ntext += '×%1'.format(cost);\\\\n\\\\n// Text: Add Icon\\\\ntext += '\\\\\\\\\\\\\\\\I[%1]'.format(item.iconIndex);\\\\n\\\\n// Return text\\\\nreturn text;\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst item = $dataItems[7];\\\\n\\\\n// Return value\\\\nreturn $gameParty.maxItems(item);\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst item = $dataItems[7];\\\\n\\\\n// Return value\\\\nreturn $gameParty.numItems(item);\\\"\",\"GaugeDrawJS:func\":\"\\\"// Declare Settings\\\\nconst color1 = ColorManager.textColor(30);\\\\nconst color2 = ColorManager.textColor(31);\\\\n\\\\n// Declare Variables\\\\nconst sprite = this;\\\\nconst settings = sprite._costSettings;\\\\nconst bitmap = sprite.bitmap;\\\\nconst user = sprite._battler;\\\\nconst item = $dataItems[7];\\\\nconst currentValue = sprite.currentDisplayedValue();\\\\nconst bitmapWidth = sprite.bitmapWidth();\\\\nconst bitmapHeight = sprite.textHeight ? sprite.textHeight() : sprite.bitmapHeight();\\\\nconst gaugeHeight = sprite.gaugeHeight();\\\\n\\\\n// Draw Gauge\\\\nconst gx = 0;\\\\nconst gy = bitmapHeight - gaugeHeight;\\\\nconst gw = bitmapWidth - gx;\\\\nconst gh = gaugeHeight;\\\\nthis.drawFullGauge(color1, color2, gx, gy, gw, gh);\\\\n\\\\n// Draw Icon\\\\nconst iconIndex = item.iconIndex;\\\\nconst iconBitmap = ImageManager.loadSystem(\\\\\\\"IconSet\\\\\\\");\\\\nconst pw = ImageManager.iconWidth;\\\\nconst ph = ImageManager.iconHeight;\\\\nconst sx = (iconIndex % 16) * pw;\\\\nconst sy = Math.floor(iconIndex / 16) * ph;\\\\nbitmap.blt(iconBitmap, sx, sy, pw, ph, 0, 0, 24, 24);\\\\n\\\\n// Draw Value\\\\nconst vw = bitmapWidth - 2;\\\\nconst vh = bitmapHeight;\\\\nsprite.setupValueFont();\\\\nbitmap.textColor = ColorManager.normalColor();\\\\nbitmap.drawText(currentValue, 0, 0, vw, vh, \\\\\\\"right\\\\\\\");\\\"\"}","{\"Name:str\":\"Item Cost\",\"Settings\":\"\",\"Icon:num\":\"0\",\"FontColor:str\":\"0\",\"FontSize:num\":\"22\",\"Cost\":\"\",\"CalcJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nlet cost = 0;\\\\n\\\\n// Calculations\\\\nconst note = skill.note;\\\\ncost = {\\\\n    items: {},\\\\n    weapons: {},\\\\n    armors: {},\\\\n};\\\\n\\\\n// Gather Cost Notetags\\\\n{ // Item Costs\\\\n    const notetag = /<ITEM COST:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n    const matches = note.match(notetag);\\\\n    if (matches) {\\\\n        for (const currentMatch of matches) {\\\\n            currentMatch.match(notetag);\\\\n            const amount = Number(RegExp.$1);\\\\n            const name = String(RegExp.$2).toUpperCase().trim();\\\\n            const entry = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n            if (entry) {\\\\n                cost.items[entry.id] = amount;\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n{ // Weapon Costs\\\\n    const notetag = /<WEAPON COST:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n    const matches = note.match(notetag);\\\\n    if (matches) {\\\\n        for (const currentMatch of matches) {\\\\n            currentMatch.match(notetag);\\\\n            const amount = Number(RegExp.$1);\\\\n            const name = String(RegExp.$2).toUpperCase().trim();\\\\n            const entry = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n            if (entry) {\\\\n                cost.weapons[entry.id] = amount;\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n{ // Armor Costs\\\\n    const notetag = /<ARMOR COST:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n    const matches = note.match(notetag);\\\\n    if (matches) {\\\\n        for (const currentMatch of matches) {\\\\n            currentMatch.match(notetag);\\\\n            const amount = Number(RegExp.$1);\\\\n            const name = String(RegExp.$2).toUpperCase().trim();\\\\n            const entry = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n            if (entry) {\\\\n                cost.armors[entry.id] = amount;\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Declare Trait Objects\\\\nconst traitObjects = user.traitObjects();\\\\n\\\\n// Apply Cost Rate Modifiers\\\\nfor (const traitObject of traitObjects) {\\\\n    if (!traitObject) continue;\\\\n    const objNote = traitObject.note || '';\\\\n    { // Item Cost Rate Modifiers\\\\n        const notetag = /<ITEM COST:[ ](\\\\\\\\d+)([%％])[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const rate = Number(RegExp.$1) * 0.01;\\\\n                const name = String(RegExp.$3).toUpperCase().trim();\\\\n                const entry = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.items[entry.id]) {\\\\n                    cost.items[entry.id] = Math.ceil(cost.items[entry.id] * rate);\\\\n                    if (cost.items[entry.id] <= 0) cost.items[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Weapon Cost Rate Modifiers\\\\n        const notetag = /<WEAPON COST:[ ](\\\\\\\\d+)([%％])[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const rate = Number(RegExp.$1) * 0.01;\\\\n                const name = String(RegExp.$3).toUpperCase().trim();\\\\n                const entry = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.weapons[entry.id]) {\\\\n                    cost.weapons[entry.id] = Math.ceil(cost.weapons[entry.id] * rate);\\\\n                    if (cost.weapons[entry.id] <= 0) cost.weapons[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Armor Cost Rate Modifiers\\\\n        const notetag = /<ARMOR COST:[ ](\\\\\\\\d+)([%％])[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const rate = Number(RegExp.$1) * 0.01;\\\\n                const name = String(RegExp.$3).toUpperCase().trim();\\\\n                const entry = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.armors[entry.id]) {\\\\n                    cost.armors[entry.id] = Math.ceil(cost.armors[entry.id] * rate);\\\\n                    if (cost.armors[entry.id] <= 0) cost.armors[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Apply Flat Cost Modifiers\\\\nfor (const traitObject of traitObjects) {\\\\n    if (!traitObject) continue;\\\\n    const objNote = traitObject.note || '';\\\\n    { // Item Flat Cost Modifiers\\\\n        const notetag = /<ITEM COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const flat = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.items[entry.id]) {\\\\n                    cost.items[entry.id] += flat;\\\\n                    if (cost.items[entry.id] <= 0) cost.items[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Weapon Flat Cost Modifiers\\\\n        const notetag = /<WEAPON COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const flat = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.weapons[entry.id]) {\\\\n                    cost.weapons[entry.id] += flat;\\\\n                    if (cost.weapons[entry.id] <= 0) cost.weapons[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Armor Flat Cost Modifiers\\\\n        const notetag = /<ARMOR COST:[ ]([\\\\\\\\+\\\\\\\\-]\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const flat = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.armors[entry.id]) {\\\\n                    cost.armors[entry.id] += flat;\\\\n                    if (cost.armors[entry.id] <= 0) cost.armors[entry.id] = 0;\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Set Cost Limits\\\\n{ // Item Cost Limits\\\\n    { // Maximum Cost\\\\n        const notetag = /<ITEM COST MAX:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const max = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.items[entry.id] !== undefined) {\\\\n                    cost.items[entry.id] = Math.min(max, cost.items[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Minimum Cost\\\\n        const notetag = /<ITEM COST MIN:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const min = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.items[entry.id] !== undefined) {\\\\n                    cost.items[entry.id] = Math.max(min, cost.items[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n{ // Weapon Cost Limits\\\\n    { // Maximum Cost\\\\n        const notetag = /<WEAPON COST MAX:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const max = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.weapons[entry.id] !== undefined) {\\\\n                    cost.weapons[entry.id] = Math.min(max, cost.weapons[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Minimum Cost\\\\n        const notetag = /<WEAPON COST MIN:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const min = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.weapons[entry.id] !== undefined) {\\\\n                    cost.weapons[entry.id] = Math.max(min, cost.weapons[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n{ // Armor Cost Limits\\\\n    { // Maximum Cost\\\\n        const notetag = /<ARMOR COST MAX:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const max = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.armors[entry.id] !== undefined) {\\\\n                    cost.armors[entry.id] = Math.min(max, cost.armors[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Minimum Cost\\\\n        const notetag = /<ARMOR COST MIN:[ ](\\\\\\\\d+)[ ](.*)>/gi;\\\\n        const matches = note.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const min = Number(RegExp.$1);\\\\n                const name = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name);\\\\n                if (entry && cost.armors[entry.id] !== undefined) {\\\\n                    cost.armors[entry.id] = Math.max(min, cost.armors[entry.id]);\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Apply Replacement Costs\\\\nfor (const traitObject of traitObjects) {\\\\n    if (!traitObject) continue;\\\\n    const objNote = traitObject.note || '';\\\\n    { // Item Replacement Costs\\\\n        const notetag = /<REPLACE ITEM (.*) COST:[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const name1 = String(RegExp.$1).toUpperCase().trim();\\\\n                const name2 = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry1 = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name1);\\\\n                const entry2 = $dataItems.find(obj => obj && obj.name.toUpperCase().trim() === name2);\\\\n                if (entry1 && entry2 && cost.items[entry1.id]) {\\\\n                    cost.items[entry2.id] = cost.items[entry1.id];\\\\n                    delete cost.items[entry1.id];\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Weapon Replacement Costs\\\\n        const notetag = /<REPLACE WEAPON (.*) COST:[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const name1 = String(RegExp.$1).toUpperCase().trim();\\\\n                const name2 = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry1 = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name1);\\\\n                const entry2 = $dataWeapons.find(obj => obj && obj.name.toUpperCase().trim() === name2);\\\\n                if (entry1 && entry2 && cost.weapons[entry1.id]) {\\\\n                    cost.weapons[entry2.id] = cost.weapons[entry1.id];\\\\n                    delete cost.items[entry1.id];\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n    { // Armor Replacement Costs\\\\n        const notetag = /<REPLACE ARMOR (.*) COST:[ ](.*)>/gi;\\\\n        const matches = objNote.match(notetag);\\\\n        if (matches) {\\\\n            for (const currentMatch of matches) {\\\\n                currentMatch.match(notetag);\\\\n                const name1 = String(RegExp.$1).toUpperCase().trim();\\\\n                const name2 = String(RegExp.$2).toUpperCase().trim();\\\\n                const entry1 = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name1);\\\\n                const entry2 = $dataArmors.find(obj => obj && obj.name.toUpperCase().trim() === name2);\\\\n                if (entry1 && entry2 && cost.armors[entry1.id]) {\\\\n                    cost.armors[entry2.id] = cost.armors[entry1.id];\\\\n                    delete cost.items[entry1.id];\\\\n                }\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Return cost data\\\\nreturn cost;\\\"\",\"CanPayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Check Individual Costs\\\\n{ // Check Item Costs\\\\n    for (let id in cost.items) {\\\\n        const obj = $dataItems[id];\\\\n        if (obj) {\\\\n            const costAmount = cost.items[id];\\\\n            const ownedAmount = $gameParty.numItems(obj);\\\\n            if (costAmount > ownedAmount) return false;\\\\n        }\\\\n    }\\\\n}\\\\n{ // Check Weapon Costs\\\\n    for (let id in cost.weapons) {\\\\n        const obj = $dataWeapons[id];\\\\n        if (obj) {\\\\n            const costAmount = cost.weapons[id];\\\\n            const ownedAmount = $gameParty.numItems(obj);\\\\n            if (costAmount > ownedAmount) return false;\\\\n        }\\\\n    }\\\\n}\\\\n{ // Check Armor Costs\\\\n    for (let id in cost.armors) {\\\\n        const obj = $dataArmors[id];\\\\n        if (obj) {\\\\n            const costAmount = cost.armors[id];\\\\n            const ownedAmount = $gameParty.numItems(obj);\\\\n            if (costAmount > ownedAmount) return false;\\\\n        }\\\\n    }\\\\n}\\\\n\\\\n// Return True\\\\nreturn true;\\\"\",\"PayJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Process Payment\\\\n{ // Check Item Costs\\\\n    for (let id in cost.items) {\\\\n        const obj = $dataItems[id];\\\\n        if (obj && obj.consumable) {\\\\n            if (obj.itypeId !== 2) {\\\\n                const costAmount = cost.items[id];\\\\n                $gameParty.loseItem(obj, costAmount);\\\\n            }\\\\n        }\\\\n    }\\\\n}\\\\n{ // Check Weapon Costs\\\\n    for (let id in cost.weapons) {\\\\n        const obj = $dataWeapons[id];\\\\n        if (obj) {\\\\n            const costAmount = cost.weapons[id];\\\\n            $gameParty.loseItem(obj, costAmount);\\\\n        }\\\\n    }\\\\n}\\\\n{ // Check Armor Costs\\\\n    for (let id in cost.armors) {\\\\n        const obj = $dataArmors[id];\\\\n        if (obj) {\\\\n            const costAmount = cost.armors[id];\\\\n            $gameParty.loseItem(obj, costAmount);\\\\n        }\\\\n    }\\\\n}\\\"\",\"Windows\":\"\",\"ShowJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\n\\\\n// Check Keys\\\\nconst keys = ['items', 'weapons', 'armors'];\\\\n\\\\n// Return False\\\\nreturn keys.some(key => Object.keys(cost[key]).length > 0);\\\"\",\"TextJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\nconst skill = arguments[0];\\\\nconst cost = arguments[1];\\\\nconst settings = arguments[2];\\\\nconst fontSize = settings.FontSize;\\\\nconst color = settings.FontColor;\\\\nconst name = settings.Name;\\\\nconst icon = settings.Icon;\\\\nconst keys = ['items', 'weapons', 'armors'];\\\\nlet text = '';\\\\n\\\\n// Text: Change Font Size\\\\ntext += '\\\\\\\\\\\\\\\\FS[%1]'.format(fontSize);\\\\n\\\\n// Text: Add Color\\\\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\\\\n    text += '\\\\\\\\\\\\\\\\HexColor<#%1>'.format(String(RegExp.$1));\\\\n} else {\\\\n    text += '\\\\\\\\\\\\\\\\C[%1]'.format(color);\\\\n}\\\\n\\\\n// Text: Add Cost\\\\nfor (const key of keys) {\\\\n    const database = [$dataItems, $dataWeapons, $dataArmors][keys.indexOf(key)];\\\\n    const costData = cost[key];\\\\n    const idList = Object.keys(costData).sort((a, b) => a - b);\\\\n    for (const id of idList) {\\\\n        const obj = database[id];\\\\n        const iconIndex = obj.iconIndex;\\\\n        const costAmount = costData[id];\\\\n        text += '\\\\\\\\\\\\\\\\I[%1]×%2 '.format(iconIndex, costAmount);\\\\n    }\\\\n}\\\\n\\\\n// Return text\\\\nreturn text.trim();\\\"\",\"Gauges\":\"\",\"GaugeMaxJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn 0;\\\"\",\"GaugeCurrentJS:func\":\"\\\"// Declare Variables\\\\nconst user = this;\\\\n\\\\n// Return value\\\\nreturn 0;\\\"\",\"GaugeDrawJS:func\":\"\\\"// Don't Draw Anything\\\\n// This does not work as a gauge.\\\"\"}"]
 *
 * @param Gauge:struct
 * @text Gauge Settings
 * @parent Skills:struct
 * @type struct<Gauge>
 * @desc Settings in regards to how skill cost gauges function and appear.
 * @default {"Labels":"","LabelFontMainType:str":"main","MatchLabelColor:eval":"true","MatchLabelGaugeColor:num":"2","PresetLabelGaugeColor:num":"16","LabelOutlineSolid:eval":"true","LabelOutlineWidth:num":"3","Values":"","ValueFontMainType:str":"number","ValueOutlineSolid:eval":"true","ValueOutlineWidth:num":"3"}
 *
 * @param BreakSkills
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param States:struct
 * @text State Settings
 * @type struct<States>
 * @desc Adjust general state settings here.
 * @default {"General":"","ReapplyRules:str":"greater","MaxTurns:num":"99","ActionEndUpdate:eval":"true","Turns":"","ShowTurns:eval":"true","TurnFontSize:num":"16","TurnOffsetX:num":"-4","TurnOffsetY:num":"-6","ColorNeutral:str":"0","ColorPositive:str":"24","ColorNegative:str":"27","Data":"","ShowData:eval":"true","DataFontSize:num":"12","DataOffsetX:num":"0","DataOffsetY:num":"8","CustomJS":"","onAddStateJS:func":"\"// Declare Variables\\nconst stateId = arguments[0];\\nconst origin = this.getStateOrigin(stateId);\\nconst state = $dataStates[stateId];\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\"","onEraseStateJS:func":"\"// Declare Variables\\nconst stateId = arguments[0];\\nconst origin = this.getStateOrigin(stateId);\\nconst state = $dataStates[stateId];\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onExpireStateJS:func":"\"// Declare Variables\\nconst stateId = arguments[0];\\nconst origin = this.getStateOrigin(stateId);\\nconst state = $dataStates[stateId];\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\""}
 *
 * @param Buffs:struct
 * @text Buff/Debuff Settings
 * @parent States:struct
 * @type struct<Buffs>
 * @desc Adjust general buff/debuff settings here.
 * @default {"General":"","ReapplyRules:str":"greater","MaxTurns:num":"99","Stacking":"","StackBuffMax:num":"2","StackDebuffMax:num":"2","MultiplierJS:func":"\"// Declare Variables\\nconst user = this;\\nconst paramId = arguments[0];\\nconst buffLevel = arguments[1];\\nlet rate = 1;\\n\\n// Perform Calculations\\nrate += buffLevel * 0.25;\\n\\n// Return Rate\\nreturn Math.max(0, rate);\"","Turns":"","ShowTurns:eval":"true","TurnFontSize:num":"16","TurnOffsetX:num":"-4","TurnOffsetY:num":"-6","ColorBuff:str":"24","ColorDebuff:str":"27","Data":"","ShowData:eval":"false","DataFontSize:num":"12","DataOffsetX:num":"0","DataOffsetY:num":"8","CustomJS":"","onAddBuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onAddDebuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onEraseBuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onEraseDebuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onExpireBuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\"","onExpireDebuffJS:func":"\"// Declare Variables\\nconst paramId = arguments[0];\\nconst modifier = this._buffs[paramId];\\nconst origin = this.getCurrentStateActiveUser();\\nconst user = this.getCurrentStateActiveUser();\\nconst target = this;\\nconst a = origin;\\nconst b = this;\\n\\n// Perform Actions\\n\""}
 *
 * @param PassiveStates:struct
 * @text Passive States
 * @parent States:struct
 * @type struct<PassiveStates>
 * @desc Adjust passive state settings here.
 * @default {"List":"","Global:arraynum":"[]","Actor:arraynum":"[]","Enemy:arraynum":"[]","CustomJS":"","PassiveConditionJS:func":"\"// Declare Variables\\nconst state = arguments[0];\\nconst stateId = state.id;\\nconst user = this;\\nconst target = this;\\nconst a = this;\\nconst b = this;\\nlet condition = true;\\n\\n// Perform Checks\\n\\n\\n// Return boolean\\nreturn condition;\""}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Skill Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Skills:
 *
 * @param General
 *
 * @param EnableLayout:eval
 * @text Use Updated Layout
 * @parent General
 * @type boolean
 * @on Use
 * @off Don't Use
 * @desc Use the Updated Skill Menu Layout provided by this plugin?
 * This will override the Core Engine windows settings.
 * @default true
 *
 * @param LayoutStyle:str
 * @text Layout Style
 * @parent General
 * @type select
 * @option Upper Help, Left Input
 * @value upper/left
 * @option Upper Help, Right Input
 * @value upper/right
 * @option Lower Help, Left Input
 * @value lower/left
 * @option Lower Help, Right Input
 * @value lower/right
 * @desc If using an updated layout, how do you want to style
 * the menu scene layout?
 * @default upper/left
 *
 * @param SkillTypeWindow
 * @text Skill Type Window
 *
 * @param CmdStyle:str
 * @text Style
 * @parent SkillTypeWindow
 * @type select
 * @option Text Only
 * @value text
 * @option Icon Only
 * @value icon
 * @option Icon + Text
 * @value iconText
 * @option Automatic
 * @value auto
 * @desc How do you wish to draw commands in the Skill Type Window?
 * @default auto
 *
 * @param CmdTextAlign:str
 * @text Text Align
 * @parent SkillTypeWindow
 * @type combo
 * @option left
 * @option center
 * @option right
 * @desc Text alignment for the Skill Type Window.
 * @default left
 *
 * @param CmdWidth:num
 * @text Window Width
 * @parent SkillTypeWindow
 * @type number
 * @min 1
 * @desc What is the desired pixel width of this window?
 * Default: 240
 * @default 240
 *
 * @param ListWindow
 * @text List Window
 *
 * @param ListWindowCols:num
 * @text Columns
 * @parent ListWindow
 * @type number
 * @min 1
 * @desc Number of maximum columns.
 * @default 1
 *
 * @param ShopStatusWindow
 * @text Shop Status Window
 *
 * @param ShowShopStatus:eval
 * @text Show in Skill Menu?
 * @parent ShopStatusWindow
 * @type boolean
 * @on Show
 * @off Don't Show
 * @desc Show the Shop Status Window in the Skill Menu?
 * This is enabled if the Updated Layout is on.
 * @default true
 *
 * @param SkillSceneAdjustSkillList:eval
 * @text Adjust List Window?
 * @parent ShopStatusWindow
 * @type boolean
 * @on Adjust
 * @off Don't
 * @desc Automatically adjust the Skill List Window in the Skill Menu if using the Shop Status Window?
 * @default true
 *
 * @param SkillSceneStatusBgType:num
 * @text Background Type
 * @parent ShopStatusWindow
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param SkillMenuStatusRect:func
 * @text JS: X, Y, W, H
 * @parent ShopStatusWindow
 * @type note
 * @desc Code used to determine the dimensions for this Shop Status Window in the Skill Menu.
 * @default "const ww = this.shopStatusWidth();\nconst wh = this._itemWindow.height;\nconst wx = Graphics.boxWidth - this.shopStatusWidth();\nconst wy = this._itemWindow.y;\nreturn new Rectangle(wx, wy, ww, wh);"
 *
 * @param SkillTypes
 * @text Skill Types
 *
 * @param HiddenSkillTypes:arraynum
 * @text Hidden Skill Types
 * @parent SkillTypes
 * @type number[]
 * @min 1
 * @max 99
 * @desc Insert the ID's of the Skill Types you want hidden from view ingame.
 * @default []
 *
 * @param BattleHiddenSkillTypes:arraynum
 * @text Hidden During Battle
 * @parent SkillTypes
 * @type number[]
 * @min 1
 * @max 99
 * @desc Insert the ID's of the Skill Types you want hidden during battle only.
 * @default []
 *
 * @param IconStypeNorm:num
 * @text Icon: Normal Type
 * @parent SkillTypes
 * @desc Icon used for normal skill types that aren't assigned any icons.
 * @default 78
 *
 * @param IconStypeMagic:num
 * @text Icon: Magic Type
 * @parent SkillTypes
 * @desc Icon used for magic skill types that aren't assigned any icons.
 * @default 79
 *
 * @param SortSkillTypesAbc:arraynum
 * @text Sort: Alphabetical
 * @parent SkillTypes
 * @type number[]
 * @min 1
 * @max 99
 * @desc Insert the ID's of Skill Types you want sorted alphabetically.
 * @default []
 *
 * @param CustomJS
 * @text Global JS Effects
 *
 * @param SkillConditionJS:func
 * @text JS: Skill Conditions
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide skill condition check.
 * @default "// Declare Variables\nconst skill = arguments[0];\nconst user = this;\nconst target = this;\nconst a = this;\nconst b = this;\nlet enabled = true;\n\n// Perform Checks\n\n\n// Return boolean\nreturn enabled;"
 *
 */
/* ----------------------------------------------------------------------------
 * Skill Cost Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Cost:
 *
 * @param Name:str
 * @text Name
 * @desc A name for this Skill Cost Type.
 * @default Untitled
 *
 * @param Settings
 *
 * @param Icon:num
 * @text Icon
 * @parent Settings
 * @desc Icon used for this Skill Cost Type.
 * Use 0 for no icon.
 * @default 0
 *
 * @param FontColor:str
 * @text Font Color
 * @parent Settings
 * @desc Text Color used to display this cost.
 * For a hex color, use #rrggbb with VisuMZ_1_MessageCore
 * @default 0
 *
 * @param FontSize:num
 * @text Font Size
 * @parent Settings
 * @type number
 * @min 1
 * @desc Font size used to display this cost.
 * @default 22
 *
 * @param Cost
 * @text Cost Processing
 *
 * @param CalcJS:func
 * @text JS: Cost Calculation
 * @parent Cost
 * @type note
 * @desc Code on how to calculate this resource cost for the skill.
 * @default "// Declare Variables\nconst user = this;\nconst skill = arguments[0];\nlet cost = 0;\n\n// Return cost value\nreturn Math.round(Math.max(0, cost));"
 *
 * @param CanPayJS:func
 * @text JS: Can Pay Cost?
 * @parent Cost
 * @type note
 * @desc Code on calculating whether or not the user is able to pay the cost.
 * @default "// Declare Variables\nconst user = this;\nconst skill = arguments[0];\nconst cost = arguments[1];\n\n// Return Boolean\nreturn true;"
 *
 * @param PayJS:func
 * @text JS: Paying Cost
 * @parent Cost
 * @type note
 * @desc Code for if met, this is the actual process of paying of the cost.
 * @default "// Declare Variables\nconst user = this;\nconst skill = arguments[0];\nconst cost = arguments[1];\n\n// Process Payment\n"
 *
 * @param Windows
 * @text Window Display
 *
 * @param ShowJS:func
 * @text JS: Show Cost?
 * @parent  Windows
 * @type note
 * @desc Code for determining if the cost is shown or not.
 * @default "// Declare Variables\nconst user = this;\nconst skill = arguments[0];\nconst cost = arguments[1];\n\n// Return Boolean\nreturn cost > 0;"
 *
 * @param TextJS:func
 * @text JS: Cost Text
 * @parent  Windows
 * @type note
 * @desc Code to determine the text (with Text Code support) used for the displayed cost.
 * @default "// Declare Variables\nconst user = this;\nconst skill = arguments[0];\nconst cost = arguments[1];\nconst settings = arguments[2];\nconst fontSize = settings.FontSize;\nconst color = settings.FontColor;\nconst name = settings.Name;\nconst icon = settings.Icon;\nlet text = '';\n\n// Text: Change Font Size\ntext += '\\\\FS[%1]'.format(fontSize);\n\n// Text: Add Color\nif (color.match(/#(.*)/i) && Imported.VisuMZ_1_MessageCore) {\n    text += '\\\\HexColor<#%1>'.format(String(RegExp.$1));\n} else {\n    text += '\\\\C[%1]'.format(color);\n}\n\n// Text: Add Cost\ntext += '%1 %2'.format(cost, name);\n\n// Text: Add Icon\nif (icon  > 0) {\n    text += '\\\\I[%1]'.format(icon);\n}\n\n// Return text\nreturn text;"
 *
 * @param Gauges
 * @text Gauge Display
 *
 * @param GaugeMaxJS:func
 * @text JS: Maximum Value
 * @parent  Gauges
 * @type note
 * @desc Code to determine the maximum value used for this Skill Cost resource for gauges.
 * @default "// Declare Variables\nconst user = this;\n\n// Return value\nreturn 0;"
 *
 * @param GaugeCurrentJS:func
 * @text JS: Current Value
 * @parent  Gauges
 * @type note
 * @desc Code to determine the current value used for this Skill Cost resource for gauges.
 * @default "// Declare Variables\nconst user = this;\n\n// Return value\nreturn 0;"
 *
 * @param GaugeDrawJS:func
 * @text JS: Draw Gauge
 * @parent  Gauges
 * @type note
 * @desc Code to determine how to draw the Skill Cost resource for this gauge type.
 * @default "// Declare Variables\nconst sprite = this;\nconst settings = sprite._costSettings;\nconst bitmap = sprite.bitmap;\nconst user = sprite._battler;\nconst currentValue = sprite.currentDisplayedValue();\n\n// Draw Gauge\nconst color1 = ColorManager.textColor(30);\nconst color2 = ColorManager.textColor(31);\nconst gx = 0;\nconst gy = sprite.bitmapHeight() - sprite.gaugeHeight();\nconst gw = sprite.bitmapWidth() - gx;\nconst gh = sprite.gaugeHeight();\nthis.drawFullGauge(color1, color2, gx, gy, gw, gh);\n\n// Draw Label\nconst label = settings.Name;\nconst lx = 4;\nconst ly = 0;\nconst lw = sprite.bitmapWidth();\nconst lh = sprite.bitmapHeight();\nsprite.setupLabelFont();\nbitmap.paintOpacity = 255;\nbitmap.drawText(label, lx, ly, lw, lh, \"left\");\n\n// Draw Value\nconst vw = sprite.bitmapWidth() - 2;\nconst vh = sprite.bitmapHeight();\nsprite.setupValueFont();\nbitmap.textColor = ColorManager.normalColor();\nbitmap.drawText(currentValue, 0, 0, vw, vh, \"right\");"
 *
 */
/* ----------------------------------------------------------------------------
 * Gauge Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Gauge:
 *
 * @param Labels
 *
 * @param LabelFontMainType:str
 * @text Font Type
 * @parent Labels
 * @type select
 * @option main
 * @option number
 * @desc Which font type should be used for labels?
 * @default main
 *
 * @param MatchLabelColor:eval
 * @text Match Label Color
 * @parent Labels
 * @type boolean
 * @on Match
 * @off Preset
 * @desc Match the label color to the Gauge Color being used?
 * @default true
 *
 * @param MatchLabelGaugeColor:num
 * @text Match: Gauge # ?
 * @parent MatchLabelColor:eval
 * @type number
 * @min 1
 * @max 2
 * @desc Which Gauge Color should be matched?
 * @default 2
 *
 * @param PresetLabelGaugeColor:num
 * @text Preset: Gauge Color
 * @parent MatchLabelColor:eval
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 16
 *
 * @param LabelOutlineSolid:eval
 * @text Solid Outline
 * @parent Labels
 * @type boolean
 * @on Solid
 * @off Semi-Transparent
 * @desc Make the label outline a solid black color?
 * @default true
 *
 * @param LabelOutlineWidth:num
 * @text Outline Width
 * @parent Labels
 * @type number
 * @min 0
 * @desc What width do you wish to use for your outline?
 * Use 0 to not use an outline.
 * @default 3
 *
 * @param Values
 *
 * @param ValueFontMainType:str
 * @text Font Type
 * @parent Values
 * @type select
 * @option main
 * @option number
 * @desc Which font type should be used for values?
 * @default number
 *
 * @param ValueOutlineSolid:eval
 * @text Solid Outline
 * @parent Values
 * @type boolean
 * @on Solid
 * @off Semi-Transparent
 * @desc Make the value outline a solid black color?
 * @default true
 *
 * @param ValueOutlineWidth:num
 * @text Outline Width
 * @parent Values
 * @type number
 * @min 0
 * @desc What width do you wish to use for your outline?
 * Use 0 to not use an outline.
 * @default 3
 *
 */
/* ----------------------------------------------------------------------------
 * General State Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~States:
 *
 * @param General
 *
 * @param ReapplyRules:str
 * @text Reapply Rules
 * @parent General
 * @type select
 * @option Ignore: State doesn't get added.
 * @value ignore
 * @option Reset: Turns get reset.
 * @value reset
 * @option Greater: Turns take greater value (current vs reset).
 * @value greater
 * @option Add: Turns add upon existing turns.
 * @value add
 * @desc These are the rules when reapplying states.
 * @default greater
 *
 * @param MaxTurns:num
 * @text Maximum Turns
 * @parent General
 * @type number
 * @min 1
 * @desc Maximum number of turns to let states go up to.
 * This can be changed with the <Max Turns: x> notetag.
 * @default 9999
 *
 * @param ActionEndUpdate:eval
 * @text Action End Update
 * @parent General
 * @type boolean
 * @on Update Each Action
 * @off Don't Change
 * @desc Refer to "Major Changes" in Help File for explanation.
 * @default true
 *
 * @param TurnEndOnMap:num
 * @text Turn End on Map
 * @parent General
 * @type number
 * @desc Update any state and buff turns on the map after
 * this many steps. Use 0 to disable.
 * @default 20
 *
 * @param Turns
 * @text Turn Display
 *
 * @param ShowTurns:eval
 * @text Show Turns?
 * @parent Turns
 * @type boolean
 * @on Display
 * @off Hide
 * @desc Display state turns on top of window icons and sprites?
 * @default true
 *
 * @param TurnFontSize:num
 * @text Turn Font Size
 * @parent Turns
 * @type number
 * @min 1
 * @desc Font size used for displaying turns.
 * @default 16
 *
 * @param TurnOffsetX:num
 * @text Offset X
 * @parent Turns
 * @desc Offset the X position of the turn display.
 * @default -4
 *
 * @param TurnOffsetY:num
 * @text Offset Y
 * @parent Turns
 * @desc Offset the Y position of the turn display.
 * @default -6
 *
 * @param TurnFontSize:num
 * @text Turn Font Size
 * @parent Turns
 * @desc Font size used for displaying turns.
 * @default 16
 *
 * @param ColorNeutral:str
 * @text Turn Color: Neutral
 * @parent Turns
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 0
 *
 * @param ColorPositive:str
 * @text Turn Color: Positive
 * @parent Turns
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 24
 *
 * @param ColorNegative:str
 * @text Turn Color: Negative
 * @parent Turns
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 27
 *
 * @param Data
 * @text Data Display
 *
 * @param ShowData:eval
 * @text Show Data?
 * @parent Data
 * @type boolean
 * @on Display
 * @off Hide
 * @desc Display state data on top of window icons and sprites?
 * @default true
 *
 * @param DataFontSize:num
 * @text Data Font Size
 * @parent Data
 * @type number
 * @min 1
 * @desc Font size used for displaying state data.
 * @default 12
 *
 * @param DataOffsetX:num
 * @text Offset X
 * @parent Data
 * @desc Offset the X position of the state data display.
 * @default 0
 *
 * @param DataOffsetY:num
 * @text Offset Y
 * @parent Data
 * @desc Offset the Y position of the state data display.
 * @default 8
 *
 * @param CustomJS
 * @text Global JS Effects
 *
 * @param onAddStateJS:func
 * @text JS: On Add State
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * state is added.
 * @default "// Declare Variables\nconst stateId = arguments[0];\nconst origin = this.getStateOrigin(stateId);\nconst state = $dataStates[stateId];\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onEraseStateJS:func
 * @text JS: On Erase State
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * state is erased.
 * @default "// Declare Variables\nconst stateId = arguments[0];\nconst origin = this.getStateOrigin(stateId);\nconst state = $dataStates[stateId];\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onExpireStateJS:func
 * @text JS: On Expire State
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * state has expired.
 * @default "// Declare Variables\nconst stateId = arguments[0];\nconst origin = this.getStateOrigin(stateId);\nconst state = $dataStates[stateId];\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 */
/* ----------------------------------------------------------------------------
 * General Buff/Debuff Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Buffs:
 *
 * @param General
 *
 * @param ReapplyRules:str
 * @text Reapply Rules
 * @parent General
 * @type select
 * @option Ignore: Buff/Debuff doesn't get added.
 * @value ignore
 * @option Reset: Turns get reset.
 * @value reset
 * @option Greater: Turns take greater value (current vs reset).
 * @value greater
 * @option Add: Turns add upon existing turns.
 * @value add
 * @desc These are the rules when reapplying buffs/debuffs.
 * @default greater
 *
 * @param MaxTurns:num
 * @text Maximum Turns
 * @parent General
 * @type number
 * @min 1
 * @desc Maximum number of turns to let buffs and debuffs go up to.
 * @default 9999
 *
 * @param Stacking
 *
 * @param StackBuffMax:num
 * @text Max Stacks: Buff
 * @parent Stacking
 * @type number
 * @min 1
 * @desc Maximum number of stacks for buffs.
 * @default 2
 *
 * @param StackDebuffMax:num
 * @text Max Stacks: Debuff
 * @parent Stacking
 * @type number
 * @min 1
 * @desc Maximum number of stacks for debuffs.
 * @default 2
 *
 * @param MultiplierJS:func
 * @text JS: Buff/Debuff Rate
 * @parent Stacking
 * @type note
 * @desc Code to determine how much buffs and debuffs affect parameters.
 * @default "// Declare Variables\nconst user = this;\nconst paramId = arguments[0];\nconst buffLevel = arguments[1];\nlet rate = 1;\n\n// Perform Calculations\nrate += buffLevel * 0.25;\n\n// Return Rate\nreturn Math.max(0, rate);"
 *
 * @param Turns
 * @text Turns Display
 *
 * @param ShowTurns:eval
 * @text Show Turns?
 * @parent Turns
 * @type boolean
 * @on Display
 * @off Hide
 * @desc Display buff and debuff turns on top of window icons and sprites?
 * @default true
 *
 * @param TurnFontSize:num
 * @text Turn Font Size
 * @parent Turns
 * @type number
 * @min 1
 * @desc Font size used for displaying turns.
 * @default 16
 *
 * @param TurnOffsetX:num
 * @text Offset X
 * @parent Turns
 * @desc Offset the X position of the turn display.
 * @default -4
 *
 * @param TurnOffsetY:num
 * @text Offset Y
 * @parent Turns
 * @desc Offset the Y position of the turn display.
 * @default -6
 *
 * @param ColorBuff:str
 * @text Turn Color: Buffs
 * @parent Turns
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 24
 *
 * @param ColorDebuff:str
 * @text Turn Color: Debuffs
 * @parent Turns
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 27
 *
 * @param Data
 * @text Rate Display
 *
 * @param ShowData:eval
 * @text Show Rate?
 * @parent Data
 * @type boolean
 * @on Display
 * @off Hide
 * @desc Display buff and debuff rate on top of window icons and sprites?
 * @default false
 *
 * @param DataFontSize:num
 * @text Rate Font Size
 * @parent Data
 * @type number
 * @min 1
 * @desc Font size used for displaying rate.
 * @default 12
 *
 * @param DataOffsetX:num
 * @text Offset X
 * @parent Data
 * @desc Offset the X position of the rate display.
 * @default 0
 *
 * @param DataOffsetY:num
 * @text Offset Y
 * @parent Data
 * @desc Offset the Y position of the rate display.
 * @default 8
 *
 * @param CustomJS
 * @text Global JS Effects
 *
 * @param onAddBuffJS:func
 * @text JS: On Add Buff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * buff is added.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onAddDebuffJS:func
 * @text JS: On Add Debuff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * debuff is added.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onEraseBuffJS:func
 * @text JS: On Erase Buff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * buff is erased.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onEraseDebuffJS:func
 * @text JS: On Erase Debuff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * debuff is erased.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onExpireBuffJS:func
 * @text JS: On Expire Buff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * buff has expired.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 * @param onExpireDebuffJS:func
 * @text JS: On Expire Debuff
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide custom effect whenever a
 * debuff has expired.
 * @default "// Declare Variables\nconst paramId = arguments[0];\nconst modifier = this._buffs[paramId];\nconst origin = this.getCurrentStateActiveUser();\nconst user = this.getCurrentStateActiveUser();\nconst target = this;\nconst a = origin;\nconst b = this;\n\n// Perform Actions\n"
 *
 */
/* ----------------------------------------------------------------------------
 * Passive State Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~PassiveStates:
 *
 * @param List
 *
 * @param Global:arraynum
 * @text Global Passives
 * @parent List
 * @type state[]
 * @desc A list of passive states to affect actors and enemies.
 * @default []
 *
 * @param Actor:arraynum
 * @text Actor-Only Passives
 * @parent List
 * @type state[]
 * @desc A list of passive states to affect actors only.
 * @default []
 *
 * @param Enemy:arraynum
 * @text Enemy Passives
 * @parent List
 * @type state[]
 * @desc A list of passive states to affect enemies only.
 * @default []
 *
 * @param Cache
 *
 * @param RefreshCacheSwitch:eval
 * @text Switch Refresh?
 * @parent Cache
 * @type boolean
 * @on Refresh
 * @off No Changes
 * @desc Refresh all battle members when switches are changed in battle?
 * @default false
 *
 * @param RefreshCacheVar:eval
 * @text Variable Refresh?
 * @parent Cache
 * @type boolean
 * @on Refresh
 * @off No Changes
 * @desc Refresh all battle members when variables are changed in battle?
 * @default false
 *
 * @param CustomJS
 * @text Global JS Effects
 *
 * @param PassiveConditionJS:func
 * @text JS: Condition Check
 * @parent CustomJS
 * @type note
 * @desc JavaScript code for a global-wide passive condition check.
 * @default "// Declare Variables\nconst state = arguments[0];\nconst stateId = state.id;\nconst user = this;\nconst target = this;\nconst a = this;\nconst b = this;\nlet condition = true;\n\n// Perform Checks\n\n\n// Return boolean\nreturn condition;"
 *
 */
//=============================================================================
var tier = tier || 0x0;
var dependencies = [];
var pluginData = $plugins.filter(function (_0x28b71e) {
    return _0x28b71e.status && _0x28b71e.description.includes('[SkillsStatesCore]');
})[0x0];
VisuMZ.SkillsStatesCore.Settings = VisuMZ.SkillsStatesCore.Settings || {};
VisuMZ.ConvertParams = function (_0x27f553, _0x359644) {
    for (const _0x5613fb in _0x359644) {
        if (_0x5613fb.match(/(.*):(.*)/i)) {
            const _0x21e30b = String(RegExp.$1);
            const _0x2a5d87 = String(RegExp.$2).toUpperCase().trim();
            let _0x4043cb;
            let _0x23c28a;
            let _0xebe754;
            switch (_0x2a5d87) {
                case 'NUM':
                    _0x4043cb = _0x359644[_0x5613fb] !== '' ? Number(_0x359644[_0x5613fb]) : 0x0;
                    break;
                case 'ARRAYNUM':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0x220f9d => Number(_0x220f9d));
                    break;
                case 'EVAL':
                    _0x4043cb = _0x359644[_0x5613fb] !== '' ? eval(_0x359644[_0x5613fb]) : null;
                    break;
                case 'ARRAYEVAL':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0x58a5df => eval(_0x58a5df));
                    break;
                case 'JSON':
                    _0x4043cb = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : '';
                    break;
                case 'ARRAYJSON':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0x417cfb => JSON.parse(_0x417cfb));
                    break;
                case 'FUNC':
                    _0x4043cb =
                        _0x359644[_0x5613fb] !== ''
                            ? new Function(JSON.parse(_0x359644[_0x5613fb]))
                            : new Function('return 0');
                    break;
                case 'ARRAYFUNC':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0x539b71 => new Function(JSON.parse(_0x539b71)));
                    break;
                case 'STR':
                    _0x4043cb = _0x359644[_0x5613fb] !== '' ? String(_0x359644[_0x5613fb]) : '';
                    break;
                case 'ARRAYSTR':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0xd48f71 => String(_0xd48f71));
                    break;
                case 'STRUCT':
                    _0xebe754 = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : {};
                    _0x27f553[_0x21e30b] = {};
                    VisuMZ.ConvertParams(_0x27f553[_0x21e30b], _0xebe754);
                    continue;
                case 'ARRAYSTRUCT':
                    _0x23c28a = _0x359644[_0x5613fb] !== '' ? JSON.parse(_0x359644[_0x5613fb]) : [];
                    _0x4043cb = _0x23c28a.map(_0x3e3575 =>
                        VisuMZ.ConvertParams({}, JSON.parse(_0x3e3575))
                    );
                    break;
                default:
                    continue;
            }
            _0x27f553[_0x21e30b] = _0x4043cb;
        }
    }
    return _0x27f553;
};
(_0x3d324a => {
    const _0x33e915 = _0x3d324a.name;
    for (const _0x269fcf of dependencies) {
        if (!Imported[_0x269fcf]) {
            alert(
                '%1 is missing a required plugin.\nPlease install %2 into the Plugin Manager.'.format(
                    _0x33e915,
                    _0x269fcf
                )
            );
            SceneManager.exit();
            break;
        }
    }
    const _0x569fff = _0x3d324a.description;
    if (_0x569fff.match(/\[Version[ ](.*?)\]/i)) {
        const _0x26d574 = Number(RegExp.$1);
        if (_0x26d574 !== VisuMZ.SkillsStatesCore.version) {
            alert(
                "%1's version does not match plugin's. Please update it in the Plugin Manager.".format(
                    _0x33e915,
                    _0x26d574
                )
            );
            SceneManager.exit();
        }
    }
    if (_0x569fff.match(/\[Tier[ ](\d+)\]/i)) {
        const _0x247c99 = Number(RegExp.$1);
        if (_0x247c99 < tier) {
            alert(
                '%1 is incorrectly placed on the plugin list.\nIt is a Tier %2 plugin placed over other Tier %3 plugins.\nPlease reorder the plugin list from smallest to largest tier numbers.'.format(
                    _0x33e915,
                    _0x247c99,
                    tier
                )
            );
            SceneManager.exit();
        } else {
            tier = Math.max(_0x247c99, tier);
        }
    }
    VisuMZ.ConvertParams(VisuMZ.SkillsStatesCore.Settings, _0x3d324a.parameters);
})(pluginData);
PluginManager.registerCommand(pluginData.name, 'SkillActorPaySkillCost', _0x576acf => {
    VisuMZ.ConvertParams(_0x576acf, _0x576acf);
    const _0x3907b2 = _0x576acf.ActorIDs || [];
    const _0x196b12 = Number(_0x576acf.SkillID);
    const _0x11061d = $dataSkills[_0x196b12];
    if (!_0x11061d) {
        return;
    }
    for (const _0x4613d2 of _0x3907b2) {
        const _0x5e1b9c = $gameActors.actor(_0x4613d2);
        if (!_0x5e1b9c) {
            continue;
        }
        _0x5e1b9c.paySkillCost(_0x11061d);
    }
});
PluginManager.registerCommand(pluginData.name, 'SkillEnemyPaySkillCost', _0x362d67 => {
    VisuMZ.ConvertParams(_0x362d67, _0x362d67);
    const _0xa323e3 = _0x362d67.EnemyIndex || [];
    const _0x398fa4 = Number(_0x362d67.SkillID);
    const _0x3e95b2 = $dataSkills[_0x398fa4];
    if (!_0x3e95b2) {
        return;
    }
    for (const _0x33ec0d of _0xa323e3) {
        const _0x21a4b4 = $gameTroop.members()[_0x33ec0d];
        if (!_0x21a4b4) {
            continue;
        }
        _0x21a4b4.paySkillCost(_0x3e95b2);
    }
});
PluginManager.registerCommand(pluginData.name, 'StateTurnsActorChangeBy', _0x20b123 => {
    VisuMZ.ConvertParams(_0x20b123, _0x20b123);
    const _0x3ba803 = _0x20b123.ActorIDs || [];
    const _0x956389 = Number(_0x20b123.StateID);
    const _0xfc6fa5 = Number(_0x20b123.Turns);
    const _0x5029c1 = _0x20b123.AutoAddState;
    for (const _0x14a7d5 of _0x3ba803) {
        const _0x52c062 = $gameActors.actor(_0x14a7d5);
        if (!_0x52c062) {
            continue;
        }
        if (_0x5029c1 && !_0x52c062.isStateAffected(_0x956389)) {
            _0x52c062.addState(_0x956389);
            _0x52c062.setStateTurns(_0x956389, _0xfc6fa5);
        } else {
            _0x52c062.addStateTurns(_0x956389, _0xfc6fa5);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'StateTurnsActorChangeTo', _0x2b4e45 => {
    VisuMZ.ConvertParams(_0x2b4e45, _0x2b4e45);
    const _0x51bf22 = _0x2b4e45.ActorIDs || [];
    const _0x4bb471 = Number(_0x2b4e45.StateID);
    const _0x4984e8 = Math.max(Number(_0x2b4e45.Turns), 0x0);
    const _0x2af103 = _0x2b4e45.AutoAddState;
    for (const _0x21a07f of _0x51bf22) {
        const _0x7a5801 = $gameActors.actor(_0x21a07f);
        if (!_0x7a5801) {
            continue;
        }
        if (_0x2af103 && !_0x7a5801.isStateAffected(_0x4bb471)) {
            _0x7a5801.addState(_0x4bb471);
        }
        _0x7a5801.setStateTurns(_0x4bb471, _0x4984e8);
    }
});
PluginManager.registerCommand(pluginData.name, 'StateTurnsEnemyChangeBy', _0x2a655f => {
    if (!$gameParty.inBattle()) {
        return;
    }
    VisuMZ.ConvertParams(_0x2a655f, _0x2a655f);
    const _0x32e96b = _0x2a655f.EnemyIndex || [];
    const _0x57c8fa = Number(_0x2a655f.StateID);
    const _0x59f000 = Number(_0x2a655f.Turns);
    const _0x31d1e9 = _0x2a655f.AutoAddState;
    for (const _0x5cfa23 of _0x32e96b) {
        const _0x2941e5 = $gameTroop.members()[_0x5cfa23];
        if (!_0x2941e5) {
            continue;
        }
        if (_0x31d1e9 && !_0x2941e5.isStateAffected(_0x57c8fa)) {
            _0x2941e5.addState(_0x57c8fa);
            _0x2941e5.setStateTurns(_0x57c8fa, _0x59f000);
        } else {
            _0x2941e5.addStateTurns(_0x57c8fa, _0x59f000);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'StateTurnsEnemyChangeTo', _0x96cd5f => {
    if (!$gameParty.inBattle()) {
        return;
    }
    VisuMZ.ConvertParams(_0x96cd5f, _0x96cd5f);
    const _0x483419 = _0x96cd5f.EnemyIndex || [];
    const _0x533d3e = Number(_0x96cd5f.StateID);
    const _0x37dd5c = Math.max(Number(_0x96cd5f.Turns), 0x0);
    const _0x2bfdf8 = _0x96cd5f.AutoAddState;
    for (const _0x1dcfa2 of _0x483419) {
        const _0x1a5281 = $gameTroop.members()[_0x1dcfa2];
        if (!_0x1a5281) {
            continue;
        }
        if (_0x2bfdf8 && !_0x1a5281.isStateAffected(_0x533d3e)) {
            _0x1a5281.addState(_0x533d3e);
        }
        _0x1a5281.setStateTurns(_0x533d3e, _0x37dd5c);
    }
});
VisuMZ.SkillsStatesCore.Scene_Boot_onDatabaseLoaded = Scene_Boot.prototype.onDatabaseLoaded;
Scene_Boot.prototype.onDatabaseLoaded = function () {
    VisuMZ.SkillsStatesCore.Scene_Boot_onDatabaseLoaded.call(this);
    this.process_VisuMZ_SkillsStatesCore_Notetags();
    VisuMZ.SkillsStatesCore.CheckIncompatibleStates();
};
Scene_Boot.prototype.process_VisuMZ_SkillsStatesCore_Notetags = function () {
    this.process_VisuMZ_SkillsStatesCore_CheckForAuras();
    if (VisuMZ.ParseAllNotetags) {
        return;
    }
    this.process_VisuMZ_SkillsStatesCore_Skill_Notetags();
    this.process_VisuMZ_SkillsStatesCore_State_Notetags();
};
Scene_Boot.prototype.process_VisuMZ_SkillsStatesCore_Skill_Notetags = function () {
    for (const _0x1dfaeb of $dataSkills) {
        if (!_0x1dfaeb) {
            continue;
        }
        VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Cost(_0x1dfaeb);
        VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x1dfaeb);
        VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_JS(_0x1dfaeb);
    }
};
Scene_Boot.prototype.process_VisuMZ_SkillsStatesCore_State_Notetags = function () {
    for (const _0x2b2de3 of $dataStates) {
        if (!_0x2b2de3) {
            continue;
        }
        VisuMZ.SkillsStatesCore.Parse_Notetags_State_Category(_0x2b2de3);
        VisuMZ.SkillsStatesCore.Parse_Notetags_State_PassiveJS(_0x2b2de3);
        VisuMZ.SkillsStatesCore.Parse_Notetags_State_SlipEffectJS(_0x2b2de3);
        VisuMZ.SkillsStatesCore.Parse_Notetags_State_ApplyRemoveLeaveJS(_0x2b2de3);
    }
};
VisuMZ.SkillsStatesCore.ParseSkillNotetags = VisuMZ.ParseSkillNotetags;
VisuMZ.ParseSkillNotetags = function (_0x4bba8a) {
    VisuMZ.SkillsStatesCore.ParseSkillNotetags.call(this, _0x4bba8a);
    VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Cost(_0x4bba8a);
    VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x4bba8a);
    VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_JS(_0x4bba8a);
};
VisuMZ.SkillsStatesCore.ParseStateNotetags = VisuMZ.ParseStateNotetags;
VisuMZ.ParseStateNotetags = function (_0x198367) {
    VisuMZ.SkillsStatesCore.ParseStateNotetags.call(this, _0x198367);
    VisuMZ.SkillsStatesCore.Parse_Notetags_State_Category(_0x198367);
    VisuMZ.SkillsStatesCore.Parse_Notetags_State_PassiveJS(_0x198367);
    VisuMZ.SkillsStatesCore.Parse_Notetags_State_SlipEffectJS(_0x198367);
    VisuMZ.SkillsStatesCore.Parse_Notetags_State_ApplyRemoveLeaveJS(_0x198367);
};
VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Cost = function (_0x12b0b9) {
    const _0x39f799 = _0x12b0b9.note;
    if (_0x39f799.match(/<MP COST:[ ](\d+)>/i)) {
        _0x12b0b9.mpCost = Number(RegExp.$1);
    }
    if (_0x39f799.match(/<TP COST:[ ](\d+)>/i)) {
        _0x12b0b9.tpCost = Number(RegExp.$1);
    }
};
VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting = function (_0x5e1811) {
    if (!_0x5e1811) {
        return;
    }
    _0x5e1811.sortPriority = 0x32;
    const _0x402e00 = _0x5e1811.note || '';
    if (_0x402e00.match(/<(?:|ID )SORT(?:|ING)[ ]PRIORITY:[ ](\d+)>/i)) {
        _0x5e1811.sortPriority = Number(RegExp.$1);
    }
};
VisuMZ.SkillsStatesCore.skillEnableJS = {};
VisuMZ.SkillsStatesCore.skillVisibleJS = {};
VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_JS = function (_0x48fe91) {
    const _0x11b318 = _0x48fe91.note;
    if (_0x11b318.match(/<JS SKILL ENABLE>\s*([\s\S]*)\s*<\/JS SKILL ENABLE>/i)) {
        const _0x22401e = String(RegExp.$1);
        const _0x1d7b20 =
            '\n            let enabled = true;\n            const user = this;\n            const target = this;\n            const a = this;\n            const b = this;\n            try {\n                %1\n            } catch (e) {\n                if ($gameTemp.isPlaytest()) console.log(e);\n            }\n            return enabled;\n        '.format(
                _0x22401e
            );
        VisuMZ.SkillsStatesCore.skillEnableJS[_0x48fe91.id] = new Function('skill', _0x1d7b20);
    }
    if (_0x11b318.match(/<JS SKILL VISIBLE>\s*([\s\S]*)\s*<\/JS SKILL VISIBLE>/i)) {
        const _0x51eb14 = String(RegExp.$1);
        const _0x52ab07 =
            '\n            let visible = true;\n            const user = this._actor;\n            const target = this._actor;\n            const a = this._actor;\n            const b = this._actor;\n            try {\n                %1\n            } catch (e) {\n                if ($gameTemp.isPlaytest()) console.log(e);\n            }\n            return visible;\n        '.format(
                _0x51eb14
            );
        VisuMZ.SkillsStatesCore.skillVisibleJS[_0x48fe91.id] = new Function('skill', _0x52ab07);
    }
};
VisuMZ.SkillsStatesCore.Parse_Notetags_State_Category = function (_0x46e729) {
    _0x46e729.categories = ['ALL', 'ANY'];
    const _0x55b7ca = _0x46e729.note;
    const _0x1ce3ac = _0x55b7ca.match(/<(?:CATEGORY|CATEGORIES):[ ](.*)>/gi);
    if (_0x1ce3ac) {
        for (const _0x5106ee of _0x1ce3ac) {
            _0x5106ee.match(/<(?:CATEGORY|CATEGORIES):[ ](.*)>/gi);
            const _0x1d37cf = String(RegExp.$1).toUpperCase().trim().split(',');
            for (const _0x479e4a of _0x1d37cf) {
                _0x46e729.categories.push(_0x479e4a.trim());
            }
        }
    }
    if (_0x55b7ca.match(/<(?:CATEGORY|CATEGORIES)>\s*([\s\S]*)\s*<\/(?:CATEGORY|CATEGORIES)>/i)) {
        const _0xae5eb9 = RegExp.$1.split(/[\r\n]+/);
        for (const _0x337bc7 of _0xae5eb9) {
            _0x46e729.categories.push(_0x337bc7.toUpperCase().trim());
        }
    }
    if (_0x55b7ca.match(/<POSITIVE STATE>/i)) {
        _0x46e729.categories.push('POSITIVE');
    }
    if (_0x55b7ca.match(/<NEGATIVE STATE>/i)) {
        _0x46e729.categories.push('NEGATIVE');
    }
};
VisuMZ.SkillsStatesCore.statePassiveConditionJS = {};
VisuMZ.SkillsStatesCore.Parse_Notetags_State_PassiveJS = function (_0x1b8ea3) {
    const _0x4b3769 = _0x1b8ea3.note;
    if (_0x4b3769.match(/<JS PASSIVE CONDITION>\s*([\s\S]*)\s*<\/JS PASSIVE CONDITION>/i)) {
        const _0x35c7a8 = String(RegExp.$1);
        const _0x3170b7 =
            '\n            let condition = true;\n            const user = this;\n            const target = this;\n            const a = this;\n            const b = this;\n            try {\n                %1\n            } catch (e) {\n                if ($gameTemp.isPlaytest()) console.log(e);\n            }\n            return condition;\n        '.format(
                _0x35c7a8
            );
        VisuMZ.SkillsStatesCore.statePassiveConditionJS[_0x1b8ea3.id] = new Function(
            'state',
            _0x3170b7
        );
    }
};
VisuMZ.SkillsStatesCore.stateHpSlipDamageJS = {};
VisuMZ.SkillsStatesCore.stateHpSlipHealJS = {};
VisuMZ.SkillsStatesCore.stateMpSlipDamageJS = {};
VisuMZ.SkillsStatesCore.stateMpSlipHealJS = {};
VisuMZ.SkillsStatesCore.stateTpSlipDamageJS = {};
VisuMZ.SkillsStatesCore.stateTpSlipHealJS = {};
VisuMZ.SkillsStatesCore.Parse_Notetags_State_SlipEffectJS = function (_0x1d2a4b) {
    const _0x262bb7 = _0x1d2a4b.note;
    if (_0x262bb7.match(/<JS HP SLIP DAMAGE>\s*([\s\S]*)\s*<\/JS HP SLIP DAMAGE>/i)) {
        const _0x156a5f = String(RegExp.$1);
        const _0x5df75b =
            "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                _0x156a5f,
                'damage',
                -0x1,
                'slipHp'
            );
        VisuMZ.SkillsStatesCore.stateHpSlipDamageJS[_0x1d2a4b.id] = new Function(
            'stateId',
            _0x5df75b
        );
    } else {
        if (_0x262bb7.match(/<JS HP SLIP HEAL>\s*([\s\S]*)\s*<\/JS HP SLIP HEAL>/i)) {
            const _0x45accf = String(RegExp.$1);
            const _0x23dfaf =
                "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                    _0x45accf,
                    'heal',
                    0x1,
                    'slipHp'
                );
            VisuMZ.SkillsStatesCore.stateHpSlipHealJS[_0x1d2a4b.id] = new Function(
                'stateId',
                _0x23dfaf
            );
        }
    }
    if (_0x262bb7.match(/<JS MP SLIP DAMAGE>\s*([\s\S]*)\s*<\/JS MP SLIP DAMAGE>/i)) {
        const _0x398124 = String(RegExp.$1);
        const _0x49b013 =
            "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                _0x398124,
                'damage',
                -0x1,
                'slipMp'
            );
        VisuMZ.SkillsStatesCore.stateMpSlipDamageJS[_0x1d2a4b.id] = new Function(
            'stateId',
            _0x49b013
        );
    } else {
        if (_0x262bb7.match(/<JS MP SLIP HEAL>\s*([\s\S]*)\s*<\/JS MP SLIP HEAL>/i)) {
            const _0x450a0 = String(RegExp.$1);
            const _0x403234 =
                "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                    _0x450a0,
                    'heal',
                    0x1,
                    'slipMp'
                );
            VisuMZ.SkillsStatesCore.stateMpSlipHealJS[_0x1d2a4b.id] = new Function(
                'stateId',
                _0x403234
            );
        }
    }
    if (_0x262bb7.match(/<JS TP SLIP DAMAGE>\s*([\s\S]*)\s*<\/JS TP SLIP DAMAGE>/i)) {
        const _0x1d3658 = String(RegExp.$1);
        const _0x280a38 =
            "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                _0x1d3658,
                'damage',
                -0x1,
                'slipTp'
            );
        VisuMZ.SkillsStatesCore.stateTpSlipDamageJS[_0x1d2a4b.id] = new Function(
            'stateId',
            _0x280a38
        );
    } else {
        if (_0x262bb7.match(/<JS TP SLIP HEAL>\s*([\s\S]*)\s*<\/JS TP SLIP HEAL>/i)) {
            const _0x57961b = String(RegExp.$1);
            const _0x257c58 =
                "\n        let %2 = 0;\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = origin;\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n        %2 = Math.round(Math.max(0, %2) * %3);\n        this.setStateData(stateId, '%4', %2);\n    ".format(
                    _0x57961b,
                    'heal',
                    0x1,
                    'slipTp'
                );
            VisuMZ.SkillsStatesCore.stateTpSlipHealJS[_0x1d2a4b.id] = new Function(
                'stateId',
                _0x257c58
            );
        }
    }
};
VisuMZ.SkillsStatesCore.stateAddJS = {};
VisuMZ.SkillsStatesCore.stateEraseJS = {};
VisuMZ.SkillsStatesCore.stateExpireJS = {};
VisuMZ.SkillsStatesCore.Parse_Notetags_State_ApplyRemoveLeaveJS = function (_0x2ae8f1) {
    const _0x57df95 = _0x2ae8f1.note;
    if (_0x57df95.match(/<JS ON ADD STATE>\s*([\s\S]*)\s*<\/JS ON ADD STATE>/i)) {
        const _0x58c05e = String(RegExp.$1);
        const _0x54d9c6 =
            '\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = this.getCurrentStateActiveUser();\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n    '.format(
                _0x58c05e
            );
        VisuMZ.SkillsStatesCore.stateAddJS[_0x2ae8f1.id] = new Function('stateId', _0x54d9c6);
    }
    if (_0x57df95.match(/<JS ON ERASE STATE>\s*([\s\S]*)\s*<\/JS ON ERASE STATE>/i)) {
        const _0x4ac754 = String(RegExp.$1);
        const _0x3eeab9 =
            '\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = this.getCurrentStateActiveUser();\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n    '.format(
                _0x4ac754
            );
        VisuMZ.SkillsStatesCore.stateEraseJS[_0x2ae8f1.id] = new Function('stateId', _0x3eeab9);
    }
    if (_0x57df95.match(/<JS ON EXPIRE STATE>\s*([\s\S]*)\s*<\/JS ON EXPIRE STATE>/i)) {
        const _0x3ccb90 = String(RegExp.$1);
        const _0x39f5de =
            '\n        const origin = this.getStateOrigin(stateId);\n        const state = $dataStates[stateId];\n        const user = this.getCurrentStateActiveUser();\n        const target = this;\n        const a = origin;\n        const b = this;\n        try {\n            %1\n        } catch (e) {\n            if ($gameTemp.isPlaytest()) console.log(e);\n        }\n    '.format(
                _0x3ccb90
            );
        VisuMZ.SkillsStatesCore.stateExpireJS[_0x2ae8f1.id] = new Function('stateId', _0x39f5de);
    }
};
VisuMZ.SkillsStatesCore.CheckIncompatibleStates = function () {
    if (!VisuMZ.SkillsStatesCore.Settings.States.ActionEndUpdate) {
        return;
    }
    for (const _0x14b314 of $dataStates) {
        if (!_0x14b314) {
            continue;
        }
        if (_0x14b314.restriction === 0x4 && _0x14b314.autoRemovalTiming === 0x1) {
            _0x14b314.autoRemovalTiming = 0x2;
        }
    }
};
VisuMZ.SkillsStatesCore.createKeyJS = function (_0x5c1d33, _0x515ee0) {
    if (VisuMZ.createKeyJS) {
        return VisuMZ.createKeyJS(_0x5c1d33, _0x515ee0);
    }
    let _0x248aa8 = '';
    if ($dataActors.includes(_0x5c1d33)) {
        _0x248aa8 = 'Actor-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataClasses.includes(_0x5c1d33)) {
        _0x248aa8 = 'Class-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataSkills.includes(_0x5c1d33)) {
        _0x248aa8 = 'Skill-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataItems.includes(_0x5c1d33)) {
        _0x248aa8 = 'Item-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataWeapons.includes(_0x5c1d33)) {
        _0x248aa8 = 'Weapon-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataArmors.includes(_0x5c1d33)) {
        _0x248aa8 = 'Armor-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataEnemies.includes(_0x5c1d33)) {
        _0x248aa8 = 'Enemy-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    if ($dataStates.includes(_0x5c1d33)) {
        _0x248aa8 = 'State-%1-%2'.format(_0x5c1d33.id, _0x515ee0);
    }
    return _0x248aa8;
};
DataManager.getClassIdWithName = function (_0x391223) {
    _0x391223 = _0x391223.toUpperCase().trim();
    this._classIDs = this._classIDs || {};
    if (this._classIDs[_0x391223]) {
        return this._classIDs[_0x391223];
    }
    for (const _0x1733c5 of $dataClasses) {
        if (!_0x1733c5) {
            continue;
        }
        let _0x5cbd39 = _0x1733c5.name;
        _0x5cbd39 = _0x5cbd39.replace(/\x1I\[(\d+)\]/gi, '');
        _0x5cbd39 = _0x5cbd39.replace(/\\I\[(\d+)\]/gi, '');
        this._classIDs[_0x5cbd39.toUpperCase().trim()] = _0x1733c5.id;
    }
    return this._classIDs[_0x391223] || 0x0;
};
DataManager.getSkillTypes = function (_0x4cb7fd) {
    this._stypeIDs = this._stypeIDs || {};
    if (this._stypeIDs[_0x4cb7fd.id]) {
        return this._stypeIDs[_0x4cb7fd.id];
    }
    this._stypeIDs[_0x4cb7fd.id] = [_0x4cb7fd.stypeId];
    if (_0x4cb7fd.note.match(/<SKILL[ ](?:TYPE|TYPES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x51855c = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        this._stypeIDs[_0x4cb7fd.id] = this._stypeIDs[_0x4cb7fd.id].concat(_0x51855c);
    } else {
        if (_0x4cb7fd.note.match(/<SKILL[ ](?:TYPE|TYPES):[ ](.*)>/i)) {
            const _0x41ca24 = RegExp.$1.split(',');
            for (const _0x28e795 of _0x41ca24) {
                const _0x138d0f = DataManager.getStypeIdWithName(_0x28e795);
                if (_0x138d0f) {
                    this._stypeIDs[_0x4cb7fd.id].push(_0x138d0f);
                }
            }
        }
    }
    return this._stypeIDs[_0x4cb7fd.id];
};
DataManager.getStypeIdWithName = function (_0x5b69a7) {
    _0x5b69a7 = _0x5b69a7.toUpperCase().trim();
    this._stypeIDs = this._stypeIDs || {};
    if (this._stypeIDs[_0x5b69a7]) {
        return this._stypeIDs[_0x5b69a7];
    }
    for (let _0x38778a = 0x1; _0x38778a < 0x64; _0x38778a++) {
        if (!$dataSystem.skillTypes[_0x38778a]) {
            continue;
        }
        let _0x255ba1 = $dataSystem.skillTypes[_0x38778a].toUpperCase().trim();
        _0x255ba1 = _0x255ba1.replace(/\x1I\[(\d+)\]/gi, '');
        _0x255ba1 = _0x255ba1.replace(/\\I\[(\d+)\]/gi, '');
        this._stypeIDs[_0x255ba1] = _0x38778a;
    }
    return this._stypeIDs[_0x5b69a7] || 0x0;
};
DataManager.getSkillIdWithName = function (_0x40b22d) {
    _0x40b22d = _0x40b22d.toUpperCase().trim();
    this._skillIDs = this._skillIDs || {};
    if (this._skillIDs[_0x40b22d]) {
        return this._skillIDs[_0x40b22d];
    }
    for (const _0x2a83a7 of $dataSkills) {
        if (!_0x2a83a7) {
            continue;
        }
        this._skillIDs[_0x2a83a7.name.toUpperCase().trim()] = _0x2a83a7.id;
    }
    return this._skillIDs[_0x40b22d] || 0x0;
};
DataManager.getStateIdWithName = function (_0x2bbe42) {
    _0x2bbe42 = _0x2bbe42.toUpperCase().trim();
    this._stateIDs = this._stateIDs || {};
    if (this._stateIDs[_0x2bbe42]) {
        return this._stateIDs[_0x2bbe42];
    }
    for (const _0x373754 of $dataStates) {
        if (!_0x373754) {
            continue;
        }
        this._stateIDs[_0x373754.name.toUpperCase().trim()] = _0x373754.id;
    }
    return this._stateIDs[_0x2bbe42] || 0x0;
};
DataManager.stateMaximumTurns = function (_0x5a330d) {
    this._stateMaxTurns = this._stateMaxTurns || {};
    if (this._stateMaxTurns[_0x5a330d]) {
        return this._stateMaxTurns[_0x5a330d];
    }
    if ($dataStates[_0x5a330d].note.match(/<MAX TURNS:[ ](\d+)>/i)) {
        this._stateMaxTurns[_0x5a330d] = Number(RegExp.$1);
    } else {
        this._stateMaxTurns[_0x5a330d] = VisuMZ.SkillsStatesCore.Settings.States.MaxTurns;
    }
    return this._stateMaxTurns[_0x5a330d];
};
DataManager.getSkillChangesFromState = function (_0x13a097) {
    if (!_0x13a097) {
        return {};
    }
    this._skillChangesFromState = this._skillChangesFromState || {};
    if (this._skillChangesFromState[_0x13a097.id] !== undefined) {
        return this._skillChangesFromState[_0x13a097.id];
    }
    const _0x3bd7c5 = _0x13a097.note || '';
    const _0x4bd5e9 = {};
    {
        const _0x4f94d5 = _0x3bd7c5.match(/<SKILL CHANGE(?:|S):[ ](.*)[ ]>>>[ ](.*)>/gi);
        if (_0x4f94d5) {
            for (const _0x12ff94 of _0x4f94d5) {
                _0x12ff94.match(/<SKILL CHANGE(?:|S):[ ](.*)[ ]>>>[ ](.*)>/gi);
                let _0x2d67ac = String(RegExp.$1);
                let _0x3c707f = String(RegExp.$2);
                VisuMZ.SkillsStatesCore.ParseSkillChangessIntoData(_0x4bd5e9, _0x2d67ac, _0x3c707f);
            }
        }
    }
    if (_0x3bd7c5.match(/<SKILL CHANGE(?:|S)>\s*([\s\S]*)\s*<\/SKILL CHANGE(?:|S)>/i)) {
        const _0x5f7710 = String(RegExp.$1)
            .split(/[\r\n]+/)
            .remove('');
        for (const _0x5a3e19 of _0x5f7710) {
            if (_0x5a3e19.match(/(.*)[ ]>>>[ ](.*)/i)) {
                let _0x266e41 = String(RegExp.$1);
                let _0x1e79a2 = String(RegExp.$2);
                VisuMZ.SkillsStatesCore.ParseSkillChangessIntoData(_0x4bd5e9, _0x266e41, _0x1e79a2);
            }
        }
    }
    this._skillChangesFromState[_0x13a097.id] = _0x4bd5e9;
    return this._skillChangesFromState[_0x13a097.id];
};
VisuMZ.SkillsStatesCore.ParseSkillChangessIntoData = function (_0x2db75f, _0x562195, _0x163e34) {
    if (/^\d+$/.test(_0x562195)) {
        _0x562195 = Number(_0x562195);
    } else {
        _0x562195 = DataManager.getSkillIdWithName(_0x562195);
    }
    if (/^\d+$/.test(_0x163e34)) {
        _0x163e34 = Number(_0x163e34);
    } else {
        _0x163e34 = DataManager.getSkillIdWithName(_0x163e34);
    }
    _0x2db75f[_0x562195] = _0x163e34;
};
ColorManager.getColorDataFromPluginParameters = function (_0xd11cff, _0x3ff2d6) {
    _0x3ff2d6 = String(_0x3ff2d6);
    this._colorCache = this._colorCache || {};
    if (_0x3ff2d6.match(/#(.*)/i)) {
        this._colorCache[_0xd11cff] = '#%1'.format(String(RegExp.$1));
    } else {
        this._colorCache[_0xd11cff] = this.textColor(Number(_0x3ff2d6));
    }
    return this._colorCache[_0xd11cff];
};
ColorManager.getColor = function (_0x4b1383) {
    _0x4b1383 = String(_0x4b1383);
    return _0x4b1383.match(/#(.*)/i)
        ? '#%1'.format(String(RegExp.$1))
        : this.textColor(Number(_0x4b1383));
};
ColorManager.stateColor = function (_0x244756) {
    if (typeof _0x244756 === 'number') {
        _0x244756 = $dataStates[_0x244756];
    }
    const _0x9e9fbd = '_stored_state-%1-color'.format(_0x244756.id);
    this._colorCache = this._colorCache || {};
    if (this._colorCache[_0x9e9fbd]) {
        return this._colorCache[_0x9e9fbd];
    }
    const _0x1fc606 = this.retrieveStateColor(_0x244756);
    return this.getColorDataFromPluginParameters(_0x9e9fbd, _0x1fc606);
};
ColorManager.retrieveStateColor = function (_0x2ae060) {
    const _0x26b21c = _0x2ae060.note;
    if (_0x26b21c.match(/<TURN COLOR:[ ](.*)>/i)) {
        return String(RegExp.$1);
    } else {
        if (_0x26b21c.match(/<POSITIVE STATE>/i)) {
            return VisuMZ.SkillsStatesCore.Settings.States.ColorPositive;
        } else {
            return _0x26b21c.match(/<NEGATIVE STATE>/i)
                ? VisuMZ.SkillsStatesCore.Settings.States.ColorNegative
                : VisuMZ.SkillsStatesCore.Settings.States.ColorNeutral;
        }
    }
};
ColorManager.buffColor = function () {
    this._colorCache = this._colorCache || {};
    if (this._colorCache._stored_buffColor) {
        return this._colorCache._stored_buffColor;
    }
    const _0xa99f81 = VisuMZ.SkillsStatesCore.Settings.Buffs.ColorBuff;
    return this.getColorDataFromPluginParameters('_stored_buffColor', _0xa99f81);
};
ColorManager.debuffColor = function () {
    this._colorCache = this._colorCache || {};
    if (this._colorCache._stored_debuffColor) {
        return this._colorCache._stored_debuffColor;
    }
    const _0x51b726 = VisuMZ.SkillsStatesCore.Settings.Buffs.ColorDebuff;
    return this.getColorDataFromPluginParameters('_stored_debuffColor', _0x51b726);
};
SceneManager.isSceneBattle = function () {
    return this._scene && this._scene.constructor === Scene_Battle;
};
VisuMZ.SkillsStatesCore.BattleManager_endAction = BattleManager.endAction;
BattleManager.endAction = function () {
    this.updateStatesActionEnd();
    VisuMZ.SkillsStatesCore.BattleManager_endAction.call(this);
};
BattleManager.updateStatesActionEnd = function () {
    const _0x4c8237 = VisuMZ.SkillsStatesCore.Settings.States;
    if (!_0x4c8237) {
        return;
    }
    if (_0x4c8237.ActionEndUpdate === false) {
        return;
    }
    if (!this._subject) {
        return;
    }
    this._subject.updateStatesActionEnd();
};
Game_Battler.prototype.updateStatesActionEnd = function () {
    if (BattleManager._phase !== 'action') {
        return;
    }
    if (this._lastStatesActionEndFrameCount === Graphics.frameCount) {
        return;
    }
    this._lastStatesActionEndFrameCount = Graphics.frameCount;
    for (const _0xe05231 of this._states) {
        const _0x1f9591 = $dataStates[_0xe05231];
        if (!_0x1f9591) {
            continue;
        }
        if (_0x1f9591.autoRemovalTiming !== 0x1) {
            continue;
        }
        if (this._stateTurns[_0xe05231] > 0x0) {
            this._stateTurns[_0xe05231]--;
        }
    }
    this.removeStatesAuto(0x1);
};
Game_BattlerBase.prototype.updateStateTurns = function () {
    const _0x29ec68 = VisuMZ.SkillsStatesCore.Settings.States;
    for (const _0x12c1b4 of this._states) {
        const _0x2fe18a = $dataStates[_0x12c1b4];
        if (_0x29ec68 && _0x29ec68.ActionEndUpdate !== false) {
            if (_0x2fe18a && _0x2fe18a.autoRemovalTiming === 0x1) {
                continue;
            }
        }
        if (this._stateTurns[_0x12c1b4] > 0x0) {
            this._stateTurns[_0x12c1b4]--;
        }
    }
};
VisuMZ.SkillsStatesCore.Game_Switches_onChange = Game_Switches.prototype.onChange;
Game_Switches.prototype.onChange = function () {
    VisuMZ.SkillsStatesCore.Game_Switches_onChange.call(this);
    const _0x3d3a8d = VisuMZ.SkillsStatesCore.Settings.PassiveStates.RefreshCacheSwitch ?? true;
    if (!_0x3d3a8d) {
        return;
    }
    if (SceneManager.isSceneBattle()) {
        for (const _0x3a7f58 of BattleManager.allBattleMembers()) {
            if (_0x3a7f58) {
                _0x3a7f58.refresh();
            }
        }
    }
};
VisuMZ.SkillsStatesCore.Game_Variables_onChange = Game_Variables.prototype.onChange;
Game_Variables.prototype.onChange = function () {
    VisuMZ.SkillsStatesCore.Game_Variables_onChange.call(this);
    const _0x5f4297 = VisuMZ.SkillsStatesCore.Settings.PassiveStates.RefreshCacheVar ?? true;
    if (!_0x5f4297) {
        return;
    }
    if (SceneManager.isSceneBattle()) {
        for (const _0x40403e of BattleManager.allBattleMembers()) {
            if (_0x40403e) {
                _0x40403e.refresh();
            }
        }
    }
};
VisuMZ.SkillsStatesCore.Game_Action_applyItemUserEffect = Game_Action.prototype.applyItemUserEffect;
Game_Action.prototype.applyItemUserEffect = function (_0xe882c) {
    VisuMZ.SkillsStatesCore.Game_Action_applyItemUserEffect.call(this, _0xe882c);
    this.applySkillsStatesCoreEffects(_0xe882c);
};
Game_Action.prototype.applySkillsStatesCoreEffects = function (_0x4efae8) {
    this.applyStateCategoryRemovalEffects(_0x4efae8);
    this.applyStateTurnManipulationEffects(_0x4efae8);
    this.applyBuffTurnManipulationEffects(_0x4efae8);
    this.applyDebuffTurnManipulationEffects(_0x4efae8);
};
VisuMZ.SkillsStatesCore.Game_Action_testApply = Game_Action.prototype.testApply;
Game_Action.prototype.testApply = function (_0x5b1c8e) {
    if (this.testSkillStatesCoreNotetags(_0x5b1c8e)) {
        return true;
    }
    return VisuMZ.SkillsStatesCore.Game_Action_testApply.call(this, _0x5b1c8e);
};
Game_Action.prototype.testSkillStatesCoreNotetags = function (_0x547956) {
    if (!this.item()) {
        return;
    }
    const _0x8fc452 = this.item().note;
    if (_0x8fc452.match(/<STATE[ ](.*)[ ]CATEGORY REMOVE:[ ](.*)>/i)) {
        const _0x130aa9 = String(RegExp.$1);
        if (_0x547956.isStateCategoryAffected(_0x130aa9)) {
            return true;
        }
    }
    if (_0x8fc452.match(/<SET STATE[ ](\d+)[ ]TURNS:[ ](.*)>/i)) {
        const _0x48fb85 = Number(RegExp.$1);
        if (_0x547956.isStateAffected(_0x48fb85)) {
            return true;
        }
    } else {
        if (_0x8fc452.match(/<SET STATE[ ](.*)[ ]TURNS:[ ](.*)>/i)) {
            const _0x212e0f = DataManager.getStateIdWithName(RegExp.$1);
            if (_0x547956.isStateAffected(_0x212e0f)) {
                return true;
            }
        }
    }
    return false;
};
Game_Action.prototype.applyStateCategoryRemovalEffects = function (_0x266e35) {
    if (_0x266e35.states().length <= 0x0) {
        return;
    }
    const _0x215c70 = this.item().note;
    {
        const _0x4ca5c6 = _0x215c70.match(/<STATE[ ](.*)[ ]CATEGORY REMOVE:[ ]ALL>/gi);
        if (_0x4ca5c6) {
            for (const _0x419c16 of _0x4ca5c6) {
                _0x419c16.match(/<STATE[ ](.*)[ ]CATEGORY REMOVE:[ ]ALL>/i);
                const _0xed3c07 = String(RegExp.$1);
                _0x266e35.removeStatesByCategoryAll(_0xed3c07);
            }
        }
    }
    {
        const _0x9c785b = _0x215c70.match(/<STATE[ ](.*)[ ]CATEGORY REMOVE:[ ](\d+)>/gi);
        if (_0x9c785b) {
            for (const _0x37ffb4 of _0x9c785b) {
                _0x37ffb4.match(/<STATE[ ](.*)[ ]CATEGORY REMOVE:[ ](\d+)>/i);
                const _0xd81ec4 = String(RegExp.$1);
                const _0x4a04d7 = Number(RegExp.$2);
                _0x266e35.removeStatesByCategory(_0xd81ec4, _0x4a04d7);
            }
        }
    }
};
Game_Action.prototype.applyStateTurnManipulationEffects = function (_0x5baf32) {
    const _0x28540b = this.item().note;
    const _0x3aeda1 = _0x28540b.match(/<SET STATE[ ](.*)[ ]TURNS:[ ](\d+)>/gi);
    if (_0x3aeda1) {
        for (const _0x2c9e3f of _0x3aeda1) {
            let _0x103bf8 = 0x0;
            let _0x55408b = 0x0;
            if (_0x2c9e3f.match(/<SET STATE[ ](\d+)[ ]TURNS:[ ](\d+)>/i)) {
                _0x103bf8 = Number(RegExp.$1);
                _0x55408b = Number(RegExp.$2);
            } else if (_0x2c9e3f.match(/<SET STATE[ ](.*)[ ]TURNS:[ ](\d+)>/i)) {
                _0x103bf8 = DataManager.getStateIdWithName(RegExp.$1);
                _0x55408b = Number(RegExp.$2);
            }
            _0x5baf32.setStateTurns(_0x103bf8, _0x55408b);
            this.makeSuccess(_0x5baf32);
        }
    }
    const _0xd84bf8 = _0x28540b.match(/<STATE[ ](.*)[ ]TURNS:[ ]([\+\-]\d+)>/gi);
    if (_0xd84bf8) {
        for (const _0x51e6a1 of _0xd84bf8) {
            let _0x283a47 = 0x0;
            let _0x29839e = 0x0;
            if (_0x51e6a1.match(/<STATE[ ](\d+)[ ]TURNS:[ ]([\+\-]\d+)>/i)) {
                _0x283a47 = Number(RegExp.$1);
                _0x29839e = Number(RegExp.$2);
            } else if (_0x51e6a1.match(/<STATE[ ](.*)[ ]TURNS:[ ]([\+\-]\d+)>/i)) {
                _0x283a47 = DataManager.getStateIdWithName(RegExp.$1);
                _0x29839e = Number(RegExp.$2);
            }
            _0x5baf32.addStateTurns(_0x283a47, _0x29839e);
            this.makeSuccess(_0x5baf32);
        }
    }
};
Game_Action.prototype.applyBuffTurnManipulationEffects = function (_0x4cd486) {
    const _0x286e1e = ['MAXHP', 'MAXMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
    const _0x54c1c6 = this.item().note;
    const _0x2ab341 = _0x54c1c6.match(/<SET[ ](.*)[ ]BUFF TURNS:[ ](\d+)>/gi);
    if (_0x2ab341) {
        for (const _0x2939ee of _0x2ab341) {
            _0x2939ee.match(/<SET[ ](.*)[ ]BUFF TURNS:[ ](\d+)>/i);
            const _0x4ca0fa = _0x286e1e.indexOf(String(RegExp.$1).toUpperCase());
            const _0x1b0d16 = Number(RegExp.$2);
            if (_0x4ca0fa >= 0x0) {
                _0x4cd486.setBuffTurns(_0x4ca0fa, _0x1b0d16);
                this.makeSuccess(_0x4cd486);
            }
        }
    }
    const _0x104e5b = _0x54c1c6.match(/<(.*)[ ]BUFF TURNS:[ ]([\+\-]\d+)>/gi);
    if (_0x104e5b) {
        for (const _0x4e1d5f of _0x2ab341) {
            _0x4e1d5f.match(/<(.*)[ ]BUFF TURNS:[ ]([\+\-]\d+)>/i);
            const _0x234738 = _0x286e1e.indexOf(String(RegExp.$1).toUpperCase());
            const _0x31fbcb = Number(RegExp.$2);
            if (_0x234738 >= 0x0) {
                _0x4cd486.addBuffTurns(_0x234738, _0x31fbcb);
                this.makeSuccess(_0x4cd486);
            }
        }
    }
};
Game_Action.prototype.applyDebuffTurnManipulationEffects = function (_0x6271e1) {
    const _0x562a33 = ['MAXHP', 'MAXMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
    const _0x5dee34 = this.item().note;
    const _0x2dd2c8 = _0x5dee34.match(/<SET[ ](.*)[ ]DEBUFF TURNS:[ ](\d+)>/gi);
    if (_0x2dd2c8) {
        for (const _0x5e2fec of _0x2dd2c8) {
            _0x5e2fec.match(/<SET[ ](.*)[ ]DEBUFF TURNS:[ ](\d+)>/i);
            const _0x4fb834 = _0x562a33.indexOf(String(RegExp.$1).toUpperCase());
            const _0x16ca92 = Number(RegExp.$2);
            if (_0x4fb834 >= 0x0) {
                _0x6271e1.setDebuffTurns(_0x4fb834, _0x16ca92);
                this.makeSuccess(_0x6271e1);
            }
        }
    }
    const _0xba4bdc = _0x5dee34.match(/<(.*)[ ]DEBUFF TURNS:[ ]([\+\-]\d+)>/gi);
    if (_0xba4bdc) {
        for (const _0x41110f of _0x2dd2c8) {
            _0x41110f.match(/<(.*)[ ]DEBUFF TURNS:[ ]([\+\-]\d+)>/i);
            const _0x39a492 = _0x562a33.indexOf(String(RegExp.$1).toUpperCase());
            const _0x4ff87e = Number(RegExp.$2);
            if (_0x39a492 >= 0x0) {
                _0x6271e1.addDebuffTurns(_0x39a492, _0x4ff87e);
                this.makeSuccess(_0x6271e1);
            }
        }
    }
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_initMembers = Game_BattlerBase.prototype.initMembers;
Game_BattlerBase.prototype.initMembers = function () {
    this._cache = {};
    this.initMembersSkillsStatesCore();
    VisuMZ.SkillsStatesCore.Game_BattlerBase_initMembers.call(this);
};
Game_BattlerBase.prototype.initMembersSkillsStatesCore = function () {
    this._stateRetainType = '';
    this._stateData = {};
    this._stateDisplay = {};
    this._stateOrigin = {};
};
Game_BattlerBase.prototype.checkCacheKey = function (_0x3ef109) {
    this._cache = this._cache || {};
    return this._cache[_0x3ef109] !== undefined;
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_refresh = Game_BattlerBase.prototype.refresh;
Game_BattlerBase.prototype.refresh = function () {
    this._cache = {};
    VisuMZ.SkillsStatesCore.Game_BattlerBase_refresh.call(this);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_eraseState = Game_BattlerBase.prototype.eraseState;
Game_BattlerBase.prototype.eraseState = function (_0x38e3b1) {
    let _0x89023e = this.isStateAffected(_0x38e3b1);
    VisuMZ.SkillsStatesCore.Game_BattlerBase_eraseState.call(this, _0x38e3b1);
    if (_0x89023e && !this.isStateAffected(_0x38e3b1)) {
        this.onRemoveState(_0x38e3b1);
    }
};
Game_BattlerBase.prototype.onRemoveState = function (_0x4301b7) {
    this.clearStateData(_0x4301b7);
    this.clearStateDisplay(_0x4301b7);
};
VisuMZ.SkillsStatesCore.Game_Battler_onBattleEnd = Game_Battler.prototype.onBattleEnd;
Game_Battler.prototype.onBattleEnd = function () {
    VisuMZ.SkillsStatesCore.Game_Battler_onBattleEnd.call(this);
    this.clearAllStateOrigins();
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_resetStateCounts =
    Game_BattlerBase.prototype.resetStateCounts;
Game_BattlerBase.prototype.resetStateCounts = function (_0x32bb17) {
    const _0x129bcb = $dataStates[_0x32bb17];
    const _0x18156f = this.stateTurns(_0x32bb17);
    const _0x3416c9 = this.getStateReapplyRulings(_0x129bcb).toLowerCase().trim();
    switch (_0x3416c9) {
        case 'ignore':
            if (_0x18156f <= 0x0) {
                this.prepareResetStateCounts(_0x32bb17);
            }
            break;
        case 'reset':
            this.prepareResetStateCounts(_0x32bb17);
            break;
        case 'greater':
            this.prepareResetStateCounts(_0x32bb17);
            this._stateTurns[_0x32bb17] = Math.max(this._stateTurns[_0x32bb17], _0x18156f);
            break;
        case 'add':
            this.prepareResetStateCounts(_0x32bb17);
            this._stateTurns[_0x32bb17] += _0x18156f;
            break;
        default:
            this.prepareResetStateCounts(_0x32bb17);
            break;
    }
    if (this.isStateAffected(_0x32bb17)) {
        const _0x672bb9 = DataManager.stateMaximumTurns(_0x32bb17);
        this._stateTurns[_0x32bb17] = this._stateTurns[_0x32bb17].clamp(0x0, _0x672bb9);
    }
};
Game_BattlerBase.prototype.prepareResetStateCounts = function (_0x42e674) {
    VisuMZ.SkillsStatesCore.Game_BattlerBase_resetStateCounts.call(this, _0x42e674);
};
Game_BattlerBase.prototype.getStateReapplyRulings = function (_0x403617) {
    const _0x1a6966 = _0x403617.note;
    return _0x1a6966.match(/<REAPPLY RULES:[ ](.*)>/i)
        ? String(RegExp.$1)
        : VisuMZ.SkillsStatesCore.Settings.States.ReapplyRules;
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_overwriteBuffTurns =
    Game_BattlerBase.prototype.overwriteBuffTurns;
Game_BattlerBase.prototype.overwriteBuffTurns = function (_0x43204b, _0x4ea11f) {
    const _0x4208fa = VisuMZ.SkillsStatesCore.Settings.Buffs.ReapplyRules;
    const _0x5cd43e = this.buffTurns(_0x43204b);
    switch (_0x4208fa) {
        case 'ignore':
            if (_0x5cd43e <= 0x0) {
                this._buffTurns[_0x43204b] = _0x4ea11f;
            }
            break;
        case 'reset':
            this._buffTurns[_0x43204b] = _0x4ea11f;
            break;
        case 'greater':
            this._buffTurns[_0x43204b] = Math.max(_0x5cd43e, _0x4ea11f);
            break;
        case 'add':
            this._buffTurns[_0x43204b] += _0x4ea11f;
            break;
        default:
            VisuMZ.SkillsStatesCore.Game_BattlerBase_overwriteBuffTurns.call(
                this,
                _0x43204b,
                _0x4ea11f
            );
            break;
    }
    const _0x46de9f = VisuMZ.SkillsStatesCore.Settings.Buffs.MaxTurns;
    this._buffTurns[_0x43204b] = this._buffTurns[_0x43204b].clamp(0x0, _0x46de9f);
};
Game_BattlerBase.prototype.isGroupDefeatStateAffected = function () {
    if (this._cache.groupDefeat !== undefined) {
        return this._cache.groupDefeat;
    }
    this._cache.groupDefeat = false;
    const _0x1eb13b = this.states();
    for (const _0x555345 of _0x1eb13b) {
        if (!_0x555345) {
            continue;
        }
        if (_0x555345.note.match(/<GROUP DEFEAT>/i)) {
            this._cache.groupDefeat = true;
            break;
        }
    }
    return this._cache.groupDefeat;
};
VisuMZ.SkillsStatesCore.Game_Unit_deadMembers = Game_Unit.prototype.deadMembers;
Game_Unit.prototype.deadMembers = function () {
    let _0x2af3d0 = VisuMZ.SkillsStatesCore.Game_Unit_deadMembers.call(this);
    if (BattleManager._endingBattle) {
        _0x2af3d0 = _0x2af3d0.concat(
            this.members().filter(_0x581660 => _0x581660.isGroupDefeatStateAffected())
        );
    }
    return _0x2af3d0;
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_clearStates = Game_BattlerBase.prototype.clearStates;
Game_BattlerBase.prototype.clearStates = function () {
    if (this.getStateRetainType() !== '') {
        this.clearStatesWithStateRetain();
    } else {
        VisuMZ.SkillsStatesCore.Game_BattlerBase_clearStates.call(this);
        this.initMembersSkillsStatesCore();
    }
};
Game_Actor.prototype.clearStates = function () {
    this._stateSteps = this._stateSteps || {};
    Game_Battler.prototype.clearStates.call(this);
};
Game_BattlerBase.prototype.clearStatesWithStateRetain = function () {
    const _0x53568e = this.states();
    for (const _0x595620 of _0x53568e) {
        if (_0x595620 && this.canClearState(_0x595620)) {
            this.eraseState(_0x595620.id);
        }
    }
    this._cache = {};
};
Game_BattlerBase.prototype.canClearState = function (_0x5ed795) {
    const _0x2ecd04 = this.getStateRetainType();
    if (_0x2ecd04 !== '') {
        const _0x46b7d8 = _0x5ed795.note;
        if (_0x2ecd04 === 'death' && _0x46b7d8.match(/<NO DEATH CLEAR>/i)) {
            return false;
        }
        if (_0x2ecd04 === 'recover all' && _0x46b7d8.match(/<NO RECOVER ALL CLEAR>/i)) {
            return false;
        }
    }
    return this.isStateAffected(_0x5ed795.id);
};
Game_BattlerBase.prototype.getStateRetainType = function () {
    return this._stateRetainType;
};
Game_BattlerBase.prototype.setStateRetainType = function (_0x6c6642) {
    this._stateRetainType = _0x6c6642;
};
Game_BattlerBase.prototype.clearStateRetainType = function () {
    this._stateRetainType = '';
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_die = Game_BattlerBase.prototype.die;
Game_BattlerBase.prototype.die = function () {
    this.setStateRetainType('death');
    VisuMZ.SkillsStatesCore.Game_BattlerBase_die.call(this);
    this.clearStateRetainType();
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_recoverAll = Game_BattlerBase.prototype.recoverAll;
Game_BattlerBase.prototype.recoverAll = function () {
    this.setStateRetainType('recover all');
    VisuMZ.SkillsStatesCore.Game_BattlerBase_recoverAll.call(this);
    this.clearStateRetainType();
};
Game_BattlerBase.prototype.adjustSkillCost = function (_0xb82fde, _0x341341, _0x56dda1) {
    return _0x341341;
};
Game_BattlerBase.prototype.canPaySkillCost = function (_0x460779) {
    for (settings of VisuMZ.SkillsStatesCore.Settings.Costs) {
        let _0x129018 = settings.CalcJS.call(this, _0x460779);
        _0x129018 = this.adjustSkillCost(_0x460779, _0x129018, settings);
        if (!settings.CanPayJS.call(this, _0x460779, _0x129018)) {
            return false;
        }
    }
    return true;
};
Game_BattlerBase.prototype.paySkillCost = function (_0x438a48) {
    for (settings of VisuMZ.SkillsStatesCore.Settings.Costs) {
        let _0x442850 = settings.CalcJS.call(this, _0x438a48);
        _0x442850 = this.adjustSkillCost(_0x438a48, _0x442850, settings);
        settings.PayJS.call(this, _0x438a48, _0x442850);
    }
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_meetsSkillConditions =
    Game_BattlerBase.prototype.meetsSkillConditions;
Game_BattlerBase.prototype.meetsSkillConditions = function (_0x101dcb) {
    if (!_0x101dcb) {
        return false;
    }
    if (!VisuMZ.SkillsStatesCore.Game_BattlerBase_meetsSkillConditions.call(this, _0x101dcb)) {
        return false;
    }
    if (!this.checkSkillConditionsNotetags(_0x101dcb)) {
        return false;
    }
    if (!this.meetsSkillConditionsEnableJS(_0x101dcb)) {
        return false;
    }
    if (!this.meetsSkillConditionsGlobalJS(_0x101dcb)) {
        return false;
    }
    return true;
};
Game_BattlerBase.prototype.checkSkillConditionsNotetags = function (_0x2ba42c) {
    if (!this.checkSkillConditionsSwitchNotetags(_0x2ba42c)) {
        return false;
    }
    return true;
};
Game_BattlerBase.prototype.checkSkillConditionsSwitchNotetags = function (_0x509aef) {
    const _0x4ed79e = _0x509aef.note;
    if (_0x4ed79e.match(/<ENABLE[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x220eda = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x3a8e42 of _0x220eda) {
            if (!$gameSwitches.value(_0x3a8e42)) {
                return false;
            }
        }
        return true;
    }
    if (_0x4ed79e.match(/<ENABLE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x2363f5 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x50a0ac of _0x2363f5) {
            if (!$gameSwitches.value(_0x50a0ac)) {
                return false;
            }
        }
        return true;
    }
    if (_0x4ed79e.match(/<ENABLE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x5ef4ae = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x30c607 of _0x5ef4ae) {
            if ($gameSwitches.value(_0x30c607)) {
                return true;
            }
        }
        return false;
    }
    if (_0x4ed79e.match(/<DISABLE[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x17363b = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x8936a of _0x17363b) {
            if (!$gameSwitches.value(_0x8936a)) {
                return true;
            }
        }
        return false;
    }
    if (_0x4ed79e.match(/<DISABLE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x2bed67 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x38a9e0 of _0x2bed67) {
            if (!$gameSwitches.value(_0x38a9e0)) {
                return true;
            }
        }
        return false;
    }
    if (_0x4ed79e.match(/<DISABLE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x2e1ed3 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x2a54ac of _0x2e1ed3) {
            if ($gameSwitches.value(_0x2a54ac)) {
                return false;
            }
        }
        return true;
    }
    return true;
};
Game_BattlerBase.prototype.meetsSkillConditionsEnableJS = function (_0x1d95f3) {
    const _0x291b56 = VisuMZ.SkillsStatesCore.skillEnableJS;
    return _0x291b56[_0x1d95f3.id] ? _0x291b56[_0x1d95f3.id].call(this, _0x1d95f3) : true;
};
Game_BattlerBase.prototype.meetsSkillConditionsGlobalJS = function (_0x4a8d72) {
    return VisuMZ.SkillsStatesCore.Settings.Skills.SkillConditionJS.call(this, _0x4a8d72);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_skillMpCost = Game_BattlerBase.prototype.skillMpCost;
Game_BattlerBase.prototype.skillMpCost = function (_0x4f7f40) {
    for (settings of VisuMZ.SkillsStatesCore.Settings.Costs) {
        if (settings.Name.toUpperCase() === 'MP') {
            let _0x313463 = settings.CalcJS.call(this, _0x4f7f40);
            _0x313463 = this.adjustSkillCost(_0x4f7f40, _0x313463, settings);
            return _0x313463;
        }
    }
    return VisuMZ.SkillsStatesCore.Game_BattlerBase_skillMpCost.call(this, _0x4f7f40);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_skillTpCost = Game_BattlerBase.prototype.skillTpCost;
Game_BattlerBase.prototype.skillTpCost = function (_0x4806d3) {
    for (settings of VisuMZ.SkillsStatesCore.Settings.Costs) {
        if (settings.Name.toUpperCase() === 'TP') {
            let _0x456e3c = settings.CalcJS.call(this, _0x4806d3);
            _0x456e3c = this.adjustSkillCost(_0x4806d3, _0x456e3c, settings);
            return _0x456e3c;
        }
    }
    return VisuMZ.SkillsStatesCore.Game_BattlerBase_skillTpCost.call(this, _0x4806d3);
};
Game_BattlerBase.prototype.hasState = function (_0x585111) {
    if (typeof _0x585111 === 'number') {
        _0x585111 = $dataStates[_0x585111];
    }
    return this.states().includes(_0x585111);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_states = Game_BattlerBase.prototype.states;
Game_BattlerBase.prototype.states = function () {
    let _0x2074b9 = VisuMZ.SkillsStatesCore.Game_BattlerBase_states.call(this);
    if ($gameTemp._checkingPassiveStates) {
        return _0x2074b9;
    }
    $gameTemp._checkingPassiveStates = true;
    this.addPassiveStates(_0x2074b9);
    $gameTemp._checkingPassiveStates = undefined;
    return _0x2074b9;
};
Game_BattlerBase.prototype.addPassiveStates = function (_0x4381ee) {
    const _0x2e95e2 = this.passiveStates();
    for (state of _0x2e95e2) {
        if (!state) {
            continue;
        }
        if (!this.isPassiveStateStackable(state) && _0x4381ee.includes(state)) {
            continue;
        }
        _0x4381ee.push(state);
    }
    if (_0x2e95e2.length > 0x0) {
        _0x4381ee.sort((_0x27fdba, _0x567324) => {
            const _0x279734 = _0x27fdba.priority;
            const _0x38c82e = _0x567324.priority;
            if (_0x279734 !== _0x38c82e) {
                return _0x38c82e - _0x279734;
            }
            return _0x27fdba - _0x567324;
        });
    }
};
Game_BattlerBase.prototype.isPassiveStateStackable = function (_0x10aeb2) {
    return _0x10aeb2.note.match(/<PASSIVE STACKABLE>/i);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_traitsSet = Game_BattlerBase.prototype.traitsSet;
Game_BattlerBase.prototype.traitsSet = function (_0x5570ed) {
    this._checkingTraitsSetSkillsStatesCore = true;
    let _0x4970ec = VisuMZ.SkillsStatesCore.Game_BattlerBase_traitsSet.call(this, _0x5570ed);
    this._checkingTraitsSetSkillsStatesCore = undefined;
    return _0x4970ec;
};
Game_BattlerBase.prototype.convertPassiveStates = function () {
    let _0xd72a89 = [];
    this._passiveStateResults = this._passiveStateResults || {};
    for (;;) {
        _0xd72a89 = [];
        let _0x3ad900 = true;
        for (const _0xca64d9 of this._cache.passiveStates) {
            const _0x5b4a37 = $dataStates[_0xca64d9];
            if (!_0x5b4a37) {
                continue;
            }
            let _0x4f84f9 = this.meetsPassiveStateConditions(_0x5b4a37);
            if (this._passiveStateResults[_0xca64d9] !== _0x4f84f9) {
                _0x3ad900 = false;
                this._passiveStateResults[_0xca64d9] = _0x4f84f9;
            }
            if (!_0x4f84f9) {
                continue;
            }
            _0xd72a89.push(_0x5b4a37);
        }
        if (_0x3ad900) {
            break;
        } else {
            if (!this._checkingTraitsSetSkillsStatesCore) {
                this.refresh();
            }
            this.createPassiveStatesCache();
        }
    }
    return _0xd72a89;
};
Game_BattlerBase.prototype.meetsPassiveStateConditions = function (_0x3d04e1) {
    if (!this.meetsPassiveStateConditionClasses(_0x3d04e1)) {
        return false;
    }
    if (!this.meetsPassiveStateConditionSwitches(_0x3d04e1)) {
        return false;
    }
    if (!this.meetsPassiveStateConditionJS(_0x3d04e1)) {
        return false;
    }
    if (!this.meetsPassiveStateGlobalConditionJS(_0x3d04e1)) {
        return false;
    }
    return true;
};
Game_BattlerBase.prototype.meetsPassiveStateConditionClasses = function (_0x1b54fb) {
    return true;
};
Game_Actor.prototype.meetsPassiveStateConditionClasses = function (_0x2ae761) {
    const _0x285dbe = DataManager.getPassiveStateConditionClassesData(_0x2ae761);
    if (_0x285dbe.currentClass.length > 0x0) {
        const _0x4982c2 = _0x285dbe.currentClass;
        if (!_0x4982c2.includes(this.currentClass())) {
            return false;
        }
    }
    if (_0x285dbe.multiClass.length > 0x0) {
        const _0x2d2b2c = _0x285dbe.multiClass;
        let _0x2334ad = [this.currentClass()];
        if (Imported.VisuMZ_2_ClassChangeSystem && this.multiclasses) {
            _0x2334ad = this.multiclasses();
        }
        if (_0x2d2b2c.filter(_0x4c8581 => _0x2334ad.includes(_0x4c8581)).length <= 0x0) {
            return false;
        }
    }
    return Game_BattlerBase.prototype.meetsPassiveStateConditionClasses.call(this, _0x2ae761);
};
DataManager.getPassiveStateConditionClassesData = function (_0x7dc155) {
    const _0x3e3ab5 = {
        currentClass: [],
        multiClass: [],
    };
    if (!_0x7dc155) {
        return _0x3e3ab5;
    }
    this._cache_getPassiveStateConditionClassesData =
        this._cache_getPassiveStateConditionClassesData || {};
    if (this._cache_getPassiveStateConditionClassesData[_0x7dc155.id] !== undefined) {
        return this._cache_getPassiveStateConditionClassesData[_0x7dc155.id];
    }
    const _0x1c1346 = _0x7dc155.note || '';
    if (_0x1c1346.match(/<PASSIVE CONDITION[ ](?:CLASS|CLASSES):[ ](.*)>/i)) {
        const _0x2e696b = String(RegExp.$1)
            .split(',')
            .map(_0x536def => _0x536def.trim());
        _0x3e3ab5.currentClass = VisuMZ.SkillsStatesCore.ParseClassIDs(_0x2e696b);
    }
    if (_0x1c1346.match(/<PASSIVE CONDITION[ ](?:MULTICLASS|MULTICLASSES):[ ](.*)>/i)) {
        const _0x51854d = String(RegExp.$1)
            .split(',')
            .map(_0x4aca10 => _0x4aca10.trim());
        _0x3e3ab5.multiClass = VisuMZ.SkillsStatesCore.ParseClassIDs(_0x51854d);
    }
    this._cache_getPassiveStateConditionClassesData[_0x7dc155.id] = _0x3e3ab5;
    return this._cache_getPassiveStateConditionClassesData[_0x7dc155.id];
};
VisuMZ.SkillsStatesCore.ParseClassIDs = function (_0x2ab112) {
    const _0x314000 = [];
    for (let _0x1bb34f of _0x2ab112) {
        _0x1bb34f = (String(_0x1bb34f) || '').trim();
        const _0x3230da = /^\d+$/.test(_0x1bb34f);
        if (_0x3230da) {
            _0x314000.push(Number(_0x1bb34f));
        } else {
            _0x314000.push(DataManager.getClassIdWithName(_0x1bb34f));
        }
    }
    return _0x314000.map(_0x4f0adc => $dataClasses[Number(_0x4f0adc)]).remove(null);
};
Game_BattlerBase.prototype.meetsPassiveStateConditionSwitches = function (_0x536436) {
    const _0xf09fce = DataManager.getPassiveStateConditionSwitchData(_0x536436);
    if (_0xf09fce.allSwitchOn && _0xf09fce.allSwitchOn.length > 0x0) {
        const _0xe28792 = _0xf09fce.allSwitchOn;
        for (const _0x46bc5b of _0xe28792) {
            if (!$gameSwitches.value(_0x46bc5b)) {
                return false;
            }
        }
    }
    if (_0xf09fce.anySwitchOn && _0xf09fce.anySwitchOn.length > 0x0) {
        const _0x31fba8 = _0xf09fce.anySwitchOn;
        let _0x227b89 = true;
        for (const _0x75df47 of _0x31fba8) {
            if ($gameSwitches.value(_0x75df47)) {
                _0x227b89 = false;
                break;
            }
        }
        if (_0x227b89) {
            return false;
        }
    }
    if (_0xf09fce.allSwitchOff && _0xf09fce.allSwitchOff.length > 0x0) {
        const _0x595d79 = _0xf09fce.allSwitchOff;
        for (const _0x5cf2ba of _0x595d79) {
            if ($gameSwitches.value(_0x5cf2ba)) {
                return false;
            }
        }
    }
    if (_0xf09fce.anySwitchOff && _0xf09fce.anySwitchOff.length > 0x0) {
        const _0x39fbc6 = _0xf09fce.anySwitchOff;
        let _0x1316aa = true;
        for (const _0x5d78dd of _0x39fbc6) {
            if (!$gameSwitches.value(_0x5d78dd)) {
                _0x1316aa = false;
                break;
            }
        }
        if (_0x1316aa) {
            return false;
        }
    }
    return true;
};
DataManager.getPassiveStateConditionSwitchData = function (_0x23059e) {
    let _0x771d5 = {
        allSwitchOn: [],
        anySwitchOn: [],
        allSwitchOff: [],
        anySwitchOff: [],
    };
    if (!_0x23059e) {
        return _0x771d5;
    }
    const _0x4a34e7 = _0x23059e.id;
    this._cache_getPassiveStateConditionSwitchData =
        this._cache_getPassiveStateConditionSwitchData || {};
    if (this._cache_getPassiveStateConditionSwitchData[_0x4a34e7] !== undefined) {
        return this._cache_getPassiveStateConditionSwitchData[_0x4a34e7];
    }
    const _0x17e529 = _0x23059e.note || '';
    if (_0x17e529.match(/PASSIVE CONDITION(?:| ALL)[ ](?:SWITCH|SWITCHES)[ ]ON:[ ](.*)>/i)) {
        _0x771d5.allSwitchOn = String(RegExp.$1)
            .split(',')
            .map(_0x24af5d => Number(_0x24af5d));
    }
    if (_0x17e529.match(/PASSIVE CONDITION ANY[ ](?:SWITCH|SWITCHES)[ ]ON:[ ](.*)>/i)) {
        _0x771d5.anySwitchOn = String(RegExp.$1)
            .split(',')
            .map(_0x182d53 => Number(_0x182d53));
    }
    if (_0x17e529.match(/PASSIVE CONDITION(?:| ALL)[ ](?:SWITCH|SWITCHES)[ ]OFF:[ ](.*)>/i)) {
        _0x771d5.allSwitchOff = String(RegExp.$1)
            .split(',')
            .map(_0x1f032c => Number(_0x1f032c));
    }
    if (_0x17e529.match(/PASSIVE CONDITION ANY[ ](?:SWITCH|SWITCHES)[ ]OFF:[ ](.*)>/i)) {
        _0x771d5.anySwitchOff = String(RegExp.$1)
            .split(',')
            .map(_0x30c2c7 => Number(_0x30c2c7));
    }
    this._cache_getPassiveStateConditionSwitchData[_0x4a34e7] = _0x771d5;
    return this._cache_getPassiveStateConditionSwitchData[_0x4a34e7];
};
Game_BattlerBase.prototype.meetsPassiveStateConditionJS = function (_0x1a8a12) {
    const _0x5c6c77 = VisuMZ.SkillsStatesCore.statePassiveConditionJS;
    if (_0x5c6c77[_0x1a8a12.id] && !_0x5c6c77[_0x1a8a12.id].call(this, _0x1a8a12)) {
        return false;
    }
    return true;
};
Game_BattlerBase.prototype.meetsPassiveStateGlobalConditionJS = function (_0x210cb1) {
    return VisuMZ.SkillsStatesCore.Settings.PassiveStates.PassiveConditionJS.call(this, _0x210cb1);
};
Game_BattlerBase.prototype.passiveStates = function () {
    if (this.checkCacheKey('passiveStates')) {
        return this.convertPassiveStates();
    }
    if (this._checkingVisuMzPassiveStateObjects) {
        return [];
    }
    this._checkingVisuMzPassiveStateObjects = true;
    this.createPassiveStatesCache();
    this._checkingVisuMzPassiveStateObjects = undefined;
    return this.convertPassiveStates();
};
Game_BattlerBase.prototype.createPassiveStatesCache = function () {
    this._checkingVisuMzPassiveStateObjects = true;
    this._cache.passiveStates = [];
    this.addPassiveStatesFromOtherPlugins();
    this.addPassiveStatesByNotetag();
    this.addPassiveStatesByPluginParameters();
    if (Game_BattlerBase.AURA_SYSTEM_ENABLED) {
        this.addAuraPassiveStateIDs();
    }
    this._cache.passiveStates = this._cache.passiveStates.sort(
        (_0x3b5d65, _0x5cb363) => _0x3b5d65 - _0x5cb363
    );
    this._checkingVisuMzPassiveStateObjects = undefined;
};
Game_BattlerBase.prototype.addPassiveStatesFromOtherPlugins = function () {
    if (Imported.VisuMZ_1_ElementStatusCore) {
        this.addPassiveStatesTraitSets();
    }
};
Game_BattlerBase.prototype.passiveStateObjects = function () {
    return [];
};
Game_BattlerBase.prototype.addPassiveStatesByNotetag = function () {
    const _0x5ea042 = this._cache.passiveStates || [];
    const _0x3c6898 = this.passiveStateObjects();
    this._cache.passiveStates = _0x5ea042 || [];
    for (const _0x389afd of _0x3c6898) {
        if (!_0x389afd) {
            continue;
        }
        const _0x367952 = DataManager.getPassiveStatesFromObj(_0x389afd);
        for (const _0x17b013 of _0x367952) {
            this._cache.passiveStates.push(_0x17b013);
        }
    }
};
DataManager.getPassiveStatesFromObj = function (_0x4faff4) {
    if (!_0x4faff4) {
        return [];
    }
    const _0x63b37a = VisuMZ.SkillsStatesCore.createKeyJS(_0x4faff4, 'passiveStateIDs');
    this._cache_getPassiveStatesFromObj = this._cache_getPassiveStatesFromObj || {};
    if (this._cache_getPassiveStatesFromObj[_0x63b37a] !== undefined) {
        return this._cache_getPassiveStatesFromObj[_0x63b37a];
    }
    const _0x3b947f = [];
    const _0x4ad5d3 = _0x4faff4.note || '';
    const _0x27fb78 = /<PASSIVE (?:STATE|STATES):[ ](.*)>/gi;
    const _0x31d77f = _0x4ad5d3.match(_0x27fb78);
    if (_0x31d77f) {
        for (const _0x10300b of _0x31d77f) {
            _0x10300b.match(_0x27fb78);
            const _0xd8b1e3 = String(RegExp.$1)
                .split(',')
                .map(_0x3d2aa7 => _0x3d2aa7.trim());
            for (const _0x377856 of _0xd8b1e3) {
                const _0x1a9047 = /^\d+$/.test(_0x377856);
                let _0x13b8a9 = 0x0;
                if (_0x1a9047) {
                    _0x13b8a9 = Number(_0x377856);
                } else {
                    _0x13b8a9 = DataManager.getStateIdWithName(_0x377856);
                }
                if (_0x13b8a9) {
                    _0x3b947f.push(_0x13b8a9);
                }
            }
        }
    }
    this._cache_getPassiveStatesFromObj[_0x63b37a] = _0x3b947f;
    return this._cache_getPassiveStatesFromObj[_0x63b37a];
};
Game_BattlerBase.prototype.addPassiveStatesByPluginParameters = function () {
    const _0x53f9b9 = VisuMZ.SkillsStatesCore.Settings.PassiveStates.Global;
    this._cache.passiveStates = this._cache.passiveStates.concat(_0x53f9b9);
};
Game_BattlerBase.AURA_SYSTEM_ENABLED = false;
Scene_Boot.prototype.process_VisuMZ_SkillsStatesCore_CheckForAuras = function () {
    const _0x4e2e3a = [
        $dataActors,
        $dataClasses,
        $dataSkills,
        $dataWeapons,
        $dataArmors,
        $dataEnemies,
    ];
    for (const _0x53abd0 of _0x4e2e3a) {
        for (const _0xe9c378 of _0x53abd0) {
            if (!_0xe9c378) {
                continue;
            }
            const _0x2a760d = _0xe9c378.note || '';
            if (_0x2a760d.match(/<(?:AURA|MIASMA) (?:STATE|STATES):[ ](.*)>/gi)) {
                Game_BattlerBase.AURA_SYSTEM_ENABLED = true;
                break;
            }
        }
    }
};
Game_BattlerBase.prototype.addAuraPassiveStateIDs = function () {
    if (this.isDead()) {
        return;
    }
    if (!this.isAppeared()) {
        return;
    }
    const _0x647d20 = this._cache.passiveStates || [];
    const _0x240949 = this;
    const _0x5c1a72 = this.friendsUnit().getAuraPassiveStateIDs(true, _0x240949);
    const _0x59bdde = $gameParty.inBattle()
        ? this.opponentsUnit().getAuraPassiveStateIDs(false, _0x240949)
        : [];
    this._cache.passiveStates = _0x647d20 || [];
    this._cache.passiveStates = this._cache.passiveStates.concat(_0x5c1a72).concat(_0x59bdde);
};
Game_Unit.prototype.getAuraPassiveStateIDs = function (_0x14f03c, _0x2dbab5) {
    let _0x1bc752 = [];
    const _0x2472ac = this === $gameParty ? this.battleMembers() : this.members();
    for (const _0x4e54e8 of _0x2472ac) {
        if (!_0x4e54e8) {
            continue;
        }
        if (!_0x4e54e8.isAppeared()) {
            continue;
        }
        const _0x44e904 = _0x4e54e8.passiveStateObjects();
        for (const _0x58d364 of _0x44e904) {
            if (!_0x58d364) {
                continue;
            }
            if (
                !VisuMZ.SkillsStatesCore.MeetsAuraObjConditions(
                    _0x58d364,
                    _0x14f03c,
                    _0x4e54e8,
                    _0x2dbab5
                )
            ) {
                continue;
            }
            let _0x169860 = DataManager.getAuraPassiveStatesFromObj(_0x58d364, _0x14f03c);
            for (const _0x3c553b of _0x169860) {
                if (
                    !VisuMZ.SkillsStatesCore.MeetsAuraStateConditions(
                        _0x3c553b,
                        _0x14f03c,
                        _0x4e54e8,
                        _0x2dbab5
                    )
                ) {
                    continue;
                }
                _0x1bc752.push(_0x3c553b);
                if (!_0x2dbab5.isStateAffected(_0x3c553b)) {
                    _0x2dbab5.setStateOrigin(_0x3c553b, _0x4e54e8);
                }
            }
        }
    }
    return _0x1bc752;
};
DataManager.getAuraPassiveStatesFromObj = function (_0x28dd51, _0x324de0) {
    if (!_0x28dd51) {
        return [];
    }
    const _0x471968 = _0x324de0 ? 'auraStateIDs' : 'miasmaStateIDs';
    const _0x2bf348 = VisuMZ.SkillsStatesCore.createKeyJS(_0x28dd51, _0x471968);
    this._cache_getAuraPassiveStatesFromObj = this._cache_getAuraPassiveStatesFromObj || {};
    if (this._cache_getAuraPassiveStatesFromObj[_0x2bf348] !== undefined) {
        return this._cache_getAuraPassiveStatesFromObj[_0x2bf348];
    }
    const _0x3146da = [];
    const _0x271eae = _0x28dd51.note || '';
    const _0x3567cb = _0x324de0
        ? /<AURA (?:STATE|STATES):[ ](.*)>/gi
        : /<MIASMA (?:STATE|STATES):[ ](.*)>/gi;
    const _0x52fabb = _0x271eae.match(_0x3567cb);
    if (_0x52fabb) {
        for (const _0x160b5f of _0x52fabb) {
            _0x160b5f.match(_0x3567cb);
            const _0x582e3d = String(RegExp.$1)
                .split(',')
                .map(_0xebcc40 => _0xebcc40.trim());
            for (const _0x404cf0 of _0x582e3d) {
                const _0x3b2770 = /^\d+$/.test(_0x404cf0);
                let _0x565ba6 = 0x0;
                if (_0x3b2770) {
                    _0x565ba6 = Number(_0x404cf0);
                } else {
                    _0x565ba6 = DataManager.getStateIdWithName(_0x404cf0);
                }
                if (_0x565ba6) {
                    _0x3146da.push(_0x565ba6);
                }
            }
        }
    }
    this._cache_getAuraPassiveStatesFromObj[_0x2bf348] = _0x3146da;
    return this._cache_getAuraPassiveStatesFromObj[_0x2bf348];
};
VisuMZ.SkillsStatesCore.MeetsAuraObjConditions = function (
    _0x99ebb3,
    _0x5e9448,
    _0x45afbd,
    _0x11e08c
) {
    if (!_0x99ebb3) {
        return false;
    }
    if (_0x99ebb3.autoRemovalTiming !== undefined && _0x99ebb3.maxTurns !== undefined) {
        return false;
    }
    const _0x1aa65a = _0x99ebb3.note || '';
    if (
        !VisuMZ.SkillsStatesCore.MeetsAuraNoteConditions(_0x1aa65a, _0x5e9448, _0x45afbd, _0x11e08c)
    ) {
        return false;
    }
    return true;
};
VisuMZ.SkillsStatesCore.MeetsAuraStateConditions = function (
    _0x4f91d2,
    _0x3482dc,
    _0x49788b,
    _0x5eb832
) {
    const _0x2ea264 = $dataStates[_0x4f91d2];
    if (!_0x2ea264) {
        return false;
    }
    const _0x553fa9 = _0x2ea264.note || '';
    if (
        !VisuMZ.SkillsStatesCore.MeetsAuraNoteConditions(_0x553fa9, _0x3482dc, _0x49788b, _0x5eb832)
    ) {
        return false;
    }
    return true;
};
VisuMZ.SkillsStatesCore.MeetsAuraNoteConditions = function (
    _0xdac307,
    _0x21adca,
    _0x3becfb,
    _0x3c8127
) {
    _0xdac307 = _0xdac307 || '';
    if (_0x3becfb.isDead()) {
        if (_0x21adca && _0xdac307.match(/<ALLOW DEAD AURA>/i)) {
        } else {
            if (!_0x21adca && _0xdac307.match(/<ALLOW DEAD MIASMA>/i)) {
            } else {
                if (_0x21adca && _0xdac307.match(/<DEAD AURA ONLY>/i)) {
                } else {
                    if (!_0x21adca && _0xdac307.match(/<DEAD MIASMA ONLY>/i)) {
                    } else {
                        return false;
                    }
                }
            }
        }
    } else {
        if (_0x21adca && _0xdac307.match(/<DEAD AURA ONLY>/i)) {
            return false;
        } else {
            if (!_0x21adca && _0xdac307.match(/<DEAD MIASMA ONLY>/i)) {
                return false;
            }
        }
    }
    if (_0x21adca) {
        if (_0xdac307.match(/<AURA NOT FOR USER>/i)) {
            if (_0x3becfb === _0x3c8127) {
                return false;
            }
        } else {
            if (_0xdac307.match(/<NOT USER AURA>/i)) {
                if (_0x3becfb === _0x3c8127) {
                    return false;
                }
            }
        }
    }
    return true;
};
Game_BattlerBase.prototype.stateTurns = function (_0x42cf58) {
    if (typeof _0x42cf58 !== 'number') {
        _0x42cf58 = _0x42cf58.id;
    }
    return this._stateTurns[_0x42cf58] || 0x0;
};
Game_BattlerBase.prototype.setStateTurns = function (_0xbf82f5, _0x4ffa97) {
    if (typeof _0xbf82f5 !== 'number') {
        _0xbf82f5 = _0xbf82f5.id;
    }
    if (this.isStateAffected(_0xbf82f5)) {
        const _0x4d4635 = DataManager.stateMaximumTurns(_0xbf82f5);
        this._stateTurns[_0xbf82f5] = _0x4ffa97.clamp(0x0, _0x4d4635);
        if (this._stateTurns[_0xbf82f5] <= 0x0) {
            this.removeState(_0xbf82f5);
        }
    }
};
Game_BattlerBase.prototype.addStateTurns = function (_0x36e8e3, _0x48545c) {
    if (typeof _0x36e8e3 !== 'number') {
        _0x36e8e3 = _0x36e8e3.id;
    }
    if (this.isStateAffected(_0x36e8e3)) {
        _0x48545c += this.stateTurns(_0x36e8e3);
        this.setStateTurns(_0x36e8e3, _0x48545c);
    }
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_eraseBuff = Game_BattlerBase.prototype.eraseBuff;
Game_BattlerBase.prototype.eraseBuff = function (_0x48c22d) {
    const _0x4765a4 = this._buffs[_0x48c22d];
    VisuMZ.SkillsStatesCore.Game_BattlerBase_eraseBuff.call(this, _0x48c22d);
    if (_0x4765a4 > 0x0) {
        this.onEraseBuff(_0x48c22d);
    }
    if (_0x4765a4 < 0x0) {
        this.onEraseDebuff(_0x48c22d);
    }
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_increaseBuff = Game_BattlerBase.prototype.increaseBuff;
Game_BattlerBase.prototype.increaseBuff = function (_0x3ec17f) {
    VisuMZ.SkillsStatesCore.Game_BattlerBase_increaseBuff.call(this, _0x3ec17f);
    if (!this.isBuffOrDebuffAffected(_0x3ec17f)) {
        this.eraseBuff(_0x3ec17f);
    }
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_decreaseBuff = Game_BattlerBase.prototype.decreaseBuff;
Game_BattlerBase.prototype.decreaseBuff = function (_0x4d13e0) {
    VisuMZ.SkillsStatesCore.Game_BattlerBase_decreaseBuff.call(this, _0x4d13e0);
    if (!this.isBuffOrDebuffAffected(_0x4d13e0)) {
        this.eraseBuff(_0x4d13e0);
    }
};
Game_BattlerBase.prototype.onEraseBuff = function (_0x210535) {};
Game_BattlerBase.prototype.onEraseDebuff = function (_0x58df16) {};
Game_BattlerBase.prototype.isMaxBuffAffected = function (_0x442569) {
    return this._buffs[_0x442569] === VisuMZ.SkillsStatesCore.Settings.Buffs.StackBuffMax;
};
Game_BattlerBase.prototype.isMaxDebuffAffected = function (_0x3eaf15) {
    return this._buffs[_0x3eaf15] === -VisuMZ.SkillsStatesCore.Settings.Buffs.StackDebuffMax;
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_buffIconIndex = Game_BattlerBase.prototype.buffIconIndex;
Game_BattlerBase.prototype.buffIconIndex = function (_0x3e349c, _0x3365fd) {
    _0x3e349c = _0x3e349c.clamp(-0x2, 0x2);
    return VisuMZ.SkillsStatesCore.Game_BattlerBase_buffIconIndex.call(this, _0x3e349c, _0x3365fd);
};
Game_BattlerBase.prototype.paramBuffRate = function (_0x8f9448) {
    const _0x3254b7 = this._buffs[_0x8f9448];
    return VisuMZ.SkillsStatesCore.Settings.Buffs.MultiplierJS.call(this, _0x8f9448, _0x3254b7);
};
Game_BattlerBase.prototype.buffTurns = function (_0x11f4c7) {
    return this._buffTurns[_0x11f4c7] || 0x0;
};
Game_BattlerBase.prototype.debuffTurns = function (_0x4a4fe9) {
    return this.buffTurns(_0x4a4fe9);
};
Game_BattlerBase.prototype.setBuffTurns = function (_0x38a5b6, _0x21fcc4) {
    if (this.isBuffAffected(_0x38a5b6)) {
        const _0x22372a = VisuMZ.SkillsStatesCore.Settings.Buffs.MaxTurns;
        this._buffTurns[_0x38a5b6] = _0x21fcc4.clamp(0x0, _0x22372a);
    }
};
Game_BattlerBase.prototype.addBuffTurns = function (_0x1bd137, _0x5c99ed) {
    if (this.isBuffAffected(_0x1bd137)) {
        _0x5c99ed += this.buffTurns(stateId);
        this.setBuffTurns(_0x1bd137, _0x5c99ed);
    }
};
Game_BattlerBase.prototype.setDebuffTurns = function (_0x4fd88b, _0x450543) {
    if (this.isDebuffAffected(_0x4fd88b)) {
        const _0x326d1f = VisuMZ.SkillsStatesCore.Settings.Buffs.MaxTurns;
        this._buffTurns[_0x4fd88b] = _0x450543.clamp(0x0, _0x326d1f);
    }
};
Game_BattlerBase.prototype.addDebuffTurns = function (_0x356248, _0x541655) {
    if (this.isDebuffAffected(_0x356248)) {
        _0x541655 += this.buffTurns(stateId);
        this.setDebuffTurns(_0x356248, _0x541655);
    }
};
Game_BattlerBase.prototype.stateData = function (_0x25f1dd) {
    if (typeof _0x25f1dd !== 'number') {
        _0x25f1dd = _0x25f1dd.id;
    }
    this._stateData = this._stateData || {};
    this._stateData[_0x25f1dd] = this._stateData[_0x25f1dd] || {};
    return this._stateData[_0x25f1dd];
};
Game_BattlerBase.prototype.getStateData = function (_0x1dd24e, _0x3f8630) {
    if (typeof _0x1dd24e !== 'number') {
        _0x1dd24e = _0x1dd24e.id;
    }
    const _0x264cb2 = this.stateData(_0x1dd24e);
    return _0x264cb2[_0x3f8630];
};
Game_BattlerBase.prototype.setStateData = function (_0x3c2f8d, _0x5f0ee8, _0x595973) {
    if (typeof _0x3c2f8d !== 'number') {
        _0x3c2f8d = _0x3c2f8d.id;
    }
    const _0x3ceca6 = this.stateData(_0x3c2f8d);
    _0x3ceca6[_0x5f0ee8] = _0x595973;
};
Game_BattlerBase.prototype.clearStateData = function (_0x790e4f) {
    if (typeof _0x790e4f !== 'number') {
        _0x790e4f = _0x790e4f.id;
    }
    this._stateData = this._stateData || {};
    this._stateData[_0x790e4f] = {};
};
Game_BattlerBase.prototype.getStateDisplay = function (_0x331f3a) {
    if (typeof _0x331f3a !== 'number') {
        _0x331f3a = _0x331f3a.id;
    }
    this._stateDisplay = this._stateDisplay || {};
    if (this._stateDisplay[_0x331f3a] === undefined) {
        this._stateDisplay[_0x331f3a] = '';
    }
    return this._stateDisplay[_0x331f3a];
};
Game_BattlerBase.prototype.setStateDisplay = function (_0x5e7b99, _0x1f5730) {
    if (typeof _0x5e7b99 !== 'number') {
        _0x5e7b99 = _0x5e7b99.id;
    }
    this._stateDisplay = this._stateDisplay || {};
    this._stateDisplay[_0x5e7b99] = _0x1f5730;
};
Game_BattlerBase.prototype.clearStateDisplay = function (_0x48bc9a) {
    if (typeof _0x48bc9a !== 'number') {
        _0x48bc9a = _0x48bc9a.id;
    }
    this._stateDisplay = this._stateDisplay || {};
    this._stateDisplay[_0x48bc9a] = '';
};
Game_BattlerBase.prototype.getStateOrigin = function (_0x3a26e0) {
    if (typeof _0x3a26e0 !== 'number') {
        _0x3a26e0 = _0x3a26e0.id;
    }
    this._stateOrigin = this._stateOrigin || {};
    this._stateOrigin[_0x3a26e0] = this._stateOrigin[_0x3a26e0] || 'user';
    const _0x4a24ea = this._stateOrigin[_0x3a26e0];
    return this.getStateOriginByKey(_0x4a24ea);
};
Game_BattlerBase.prototype.setStateOrigin = function (_0x10f071, _0x3a3f14) {
    this._stateOrigin = this._stateOrigin || {};
    const _0x5d4d02 = _0x3a3f14
        ? this.convertTargetToStateOriginKey(_0x3a3f14)
        : this.getCurrentStateOriginKey();
    this._stateOrigin[_0x10f071] = _0x5d4d02;
};
Game_BattlerBase.prototype.clearStateOrigin = function (_0x239250) {
    this._stateOrigin = this._stateOrigin || {};
    delete this._stateOrigin[_0x239250];
};
Game_BattlerBase.prototype.clearAllStateOrigins = function () {
    this._stateOrigin = {};
};
Game_BattlerBase.prototype.getCurrentStateOriginKey = function () {
    const _0x55f632 = this.getCurrentStateActiveUser();
    return this.convertTargetToStateOriginKey(_0x55f632);
};
Game_BattlerBase.prototype.getCurrentStateActiveUser = function () {
    if ($gameParty.inBattle()) {
        if (BattleManager._subject) {
            return BattleManager._subject;
        } else {
            if (BattleManager._currentActor) {
                return BattleManager._currentActor;
            }
        }
    } else {
        const _0xb69731 = SceneManager._scene;
        if (![Scene_Map, Scene_Item].includes(_0xb69731.constructor)) {
            return $gameParty.menuActor();
        }
    }
    return this;
};
Game_BattlerBase.prototype.convertTargetToStateOriginKey = function (_0x338b48) {
    if (!_0x338b48) {
        return 'user';
    }
    if (_0x338b48.isActor()) {
        return '<actor-%1>'.format(_0x338b48.actorId());
    } else {
        const _0x4f165f = '<enemy-%1>'.format(_0x338b48.enemyId());
        const _0x5c2f66 = '<member-%1>'.format(_0x338b48.index());
        const _0x159a62 = '<troop-%1>'.format($gameTroop.getCurrentTroopUniqueID());
        return '%1 %2 %3'.format(_0x4f165f, _0x5c2f66, _0x159a62);
    }
    return 'user';
};
Game_BattlerBase.prototype.getStateOriginByKey = function (_0x2912b0) {
    if (_0x2912b0 === 'user') {
        return this;
    } else {
        if (_0x2912b0.match(/<actor-(\d+)>/i)) {
            return $gameActors.actor(Number(RegExp.$1));
        } else {
            if ($gameParty.inBattle() && _0x2912b0.match(/<troop-(\d+)>/i)) {
                const _0x34b366 = Number(RegExp.$1);
                if (_0x34b366 === $gameTroop.getCurrentTroopUniqueID()) {
                    if (_0x2912b0.match(/<member-(\d+)>/i)) {
                        return $gameTroop.members()[Number(RegExp.$1)];
                    }
                }
            }
            if (_0x2912b0.match(/<enemy-(\d+)>/i)) {
                return new Game_Enemy(Number(RegExp.$1), -0x1f4, -0x1f4);
            }
        }
    }
    return this;
};
VisuMZ.SkillsStatesCore.Game_Battler_addState = Game_Battler.prototype.addState;
Game_Battler.prototype.addState = function (_0x2717b7) {
    const _0x203e58 = this.isStateAddable(_0x2717b7);
    VisuMZ.SkillsStatesCore.Game_Battler_addState.call(this, _0x2717b7);
    if (_0x203e58 && this.hasState($dataStates[_0x2717b7])) {
        this.onAddState(_0x2717b7);
    }
};
VisuMZ.SkillsStatesCore.Game_Battler_isStateAddable = Game_Battler.prototype.isStateAddable;
Game_Battler.prototype.isStateAddable = function (_0x2709f7) {
    const _0x387b76 = $dataStates[_0x2709f7];
    if (_0x387b76 && _0x387b76.note.match(/<NO DEATH CLEAR>/i)) {
        return (
            !this.isStateResist(_0x2709f7) &&
            !this.isStateRestrict(_0x2709f7) &&
            !this._result.isStateRemoved(_0x2709f7)
        );
    }
    return VisuMZ.SkillsStatesCore.Game_Battler_isStateAddable.call(this, _0x2709f7);
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_addNewState = Game_BattlerBase.prototype.addNewState;
Game_BattlerBase.prototype.addNewState = function (_0x66d60e) {
    VisuMZ.SkillsStatesCore.Game_BattlerBase_addNewState.call(this, _0x66d60e);
    if (_0x66d60e === this.deathStateId()) {
        while (this._states.filter(_0x4134fd => _0x4134fd === this.deathStateId()).length > 0x1) {
            const _0x30d3fa = this._states.indexOf(this.deathStateId());
            this._states.splice(_0x30d3fa, 0x1);
        }
    }
};
Game_Battler.prototype.onAddState = function (_0x505d9a) {
    this.setStateOrigin(_0x505d9a);
    this.removeOtherStatesOfSameCategory(_0x505d9a);
    this.onAddStateMakeCustomSlipValues(_0x505d9a);
    this.onAddStateCustomJS(_0x505d9a);
    this.onAddStateGlobalJS(_0x505d9a);
};
Game_Battler.prototype.onRemoveState = function (_0x5e68ff) {
    this.onEraseStateCustomJS(_0x5e68ff);
    this.onEraseStateGlobalJS(_0x5e68ff);
    Game_BattlerBase.prototype.onRemoveState.call(this, _0x5e68ff);
};
Game_Battler.prototype.removeStatesAuto = function (_0x39caa3) {
    for (const _0x17c0e0 of this.states()) {
        if (this.isStateExpired(_0x17c0e0.id) && _0x17c0e0.autoRemovalTiming === _0x39caa3) {
            this.removeState(_0x17c0e0.id);
            this.onExpireState(_0x17c0e0.id);
            this.onExpireStateGlobalJS(_0x17c0e0.id);
        }
    }
};
Game_Battler.prototype.onExpireState = function (_0x2e67b3) {
    this.onExpireStateCustomJS(_0x2e67b3);
};
Game_Battler.prototype.onAddStateCustomJS = function (_0x2f500a) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    const _0x34a116 = VisuMZ.SkillsStatesCore.stateAddJS;
    if (_0x34a116[_0x2f500a]) {
        _0x34a116[_0x2f500a].call(this, _0x2f500a);
    }
};
Game_Battler.prototype.onEraseStateCustomJS = function (_0xdadc4d) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    const _0x508790 = VisuMZ.SkillsStatesCore.stateEraseJS;
    if (_0x508790[_0xdadc4d]) {
        _0x508790[_0xdadc4d].call(this, _0xdadc4d);
    }
};
Game_Battler.prototype.onExpireStateCustomJS = function (_0x270e2a) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    const _0x1940df = VisuMZ.SkillsStatesCore.stateExpireJS;
    if (_0x1940df[_0x270e2a]) {
        _0x1940df[_0x270e2a].call(this, _0x270e2a);
    }
};
Game_Battler.prototype.onAddStateGlobalJS = function (_0x307a99) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    try {
        VisuMZ.SkillsStatesCore.Settings.States.onAddStateJS.call(this, _0x307a99);
    } catch (_0x2d5b70) {
        if ($gameTemp.isPlaytest()) {
            console.log(_0x2d5b70);
        }
    }
};
Game_Battler.prototype.onEraseStateGlobalJS = function (_0x3ac9b4) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    try {
        VisuMZ.SkillsStatesCore.Settings.States.onEraseStateJS.call(this, _0x3ac9b4);
    } catch (_0x593e89) {
        if ($gameTemp.isPlaytest()) {
            console.log(_0x593e89);
        }
    }
};
Game_Battler.prototype.onExpireStateGlobalJS = function (_0xd5a677) {
    if (this._tempActor || this._tempBattler) {
        return;
    }
    try {
        VisuMZ.SkillsStatesCore.Settings.States.onExpireStateJS.call(this, _0xd5a677);
    } catch (_0x2269f2) {
        if ($gameTemp.isPlaytest()) {
            console.log(_0x2269f2);
        }
    }
};
Game_Battler.prototype.statesByCategory = function (_0x326519) {
    _0x326519 = _0x326519.toUpperCase().trim();
    return this.states().filter(_0x1175bf => _0x1175bf.categories.includes(_0x326519));
};
Game_Battler.prototype.removeStatesByCategory = function (_0x2f153d, _0xd65cc0) {
    _0x2f153d = _0x2f153d.toUpperCase().trim();
    _0xd65cc0 = _0xd65cc0 || 0x0;
    const _0x285b01 = this.statesByCategory(_0x2f153d);
    const _0x2d0b70 = [];
    for (const _0x33c3b0 of _0x285b01) {
        if (!_0x33c3b0) {
            continue;
        }
        if (_0xd65cc0 <= 0x0) {
            break;
        }
        _0x2d0b70.push(_0x33c3b0.id);
        this._result.success = true;
        _0xd65cc0--;
    }
    while (_0x2d0b70.length > 0x0) {
        this.removeState(_0x2d0b70.shift());
    }
};
Game_Battler.prototype.removeStatesByCategoryAll = function (_0x258953, _0x59037c) {
    _0x258953 = _0x258953.toUpperCase().trim();
    _0x59037c = _0x59037c || [];
    const _0x2c5e30 = this.statesByCategory(_0x258953);
    const _0xb0db01 = [];
    for (const _0x3852b5 of _0x2c5e30) {
        if (!_0x3852b5) {
            continue;
        }
        if (_0x59037c.includes(_0x3852b5)) {
            continue;
        }
        _0xb0db01.push(_0x3852b5.id);
        this._result.success = true;
    }
    while (_0xb0db01.length > 0x0) {
        this.removeState(_0xb0db01.shift());
    }
};
Game_Battler.prototype.isStateCategoryAffected = function (_0x125edf) {
    return this.totalStateCategoryAffected(_0x125edf) > 0x0;
};
Game_Battler.prototype.hasStateCategory = function (_0x3b02bf) {
    return this.totalStateCategory(_0x3b02bf) > 0x0;
};
Game_Battler.prototype.totalStateCategoryAffected = function (_0x43b65b) {
    const _0x1bd5a1 = this.statesByCategory(_0x43b65b).filter(_0x2a7c9f =>
        this.isStateAffected(_0x2a7c9f.id)
    );
    return _0x1bd5a1.length;
};
Game_Battler.prototype.totalStateCategory = function (_0x33c8fb) {
    const _0xe87b31 = this.statesByCategory(_0x33c8fb);
    return _0xe87b31.length;
};
VisuMZ.SkillsStatesCore.Game_BattlerBase_isStateResist = Game_BattlerBase.prototype.isStateResist;
Game_BattlerBase.prototype.isStateResist = function (_0x53e22e) {
    const _0x2e6301 = $dataStates[_0x53e22e];
    if (_0x2e6301 && _0x2e6301.categories.length > 0x0) {
        for (const _0x571473 of _0x2e6301.categories) {
            if (this.isStateCategoryResisted(_0x571473)) {
                return true;
            }
        }
    }
    return VisuMZ.SkillsStatesCore.Game_BattlerBase_isStateResist.call(this, _0x53e22e);
};
Game_BattlerBase.prototype.isStateCategoryResisted = function (_0x53d491) {
    if (this.checkCacheKey('stateCategoriesResisted')) {
        return this._cache.stateCategoriesResisted.includes(_0x53d491);
    }
    this._cache.stateCategoriesResisted = this.makeResistedStateCategories();
    return this._cache.stateCategoriesResisted.includes(_0x53d491);
};
Game_BattlerBase.prototype.makeResistedStateCategories = function () {
    const _0x561425 = /<RESIST STATE (?:CATEGORY|CATEGORIES):[ ](.*)>/gi;
    const _0x1ee51c =
        /<RESIST STATE (?:CATEGORY|CATEGORIES)>\s*([\s\S]*)\s*<\/RESIST STATE (?:CATEGORY|CATEGORIES)>/i;
    let _0x430405 = [];
    for (const _0x51a6bd of this.traitObjects()) {
        if (!_0x51a6bd) {
            continue;
        }
        const _0x123259 = _0x51a6bd.note;
        const _0x5f93a = _0x123259.match(_0x561425);
        if (_0x5f93a) {
            for (const _0x3f8a75 of _0x5f93a) {
                _0x3f8a75.match(_0x561425);
                const _0x218120 = String(RegExp.$1)
                    .split(',')
                    .map(_0x1e5c5a => String(_0x1e5c5a).toUpperCase().trim());
                _0x430405 = _0x430405.concat(_0x218120);
            }
        }
        if (_0x123259.match(_0x1ee51c)) {
            const _0x444791 = String(RegExp.$1)
                .split(/[\r\n]+/)
                .map(_0x775ff6 => String(_0x775ff6).toUpperCase().trim());
            _0x430405 = _0x430405.concat(_0x444791);
        }
    }
    return _0x430405;
};
Game_BattlerBase.prototype.removeOtherStatesOfSameCategory = function (_0x564454) {
    const _0x59c83e = $dataStates[_0x564454];
    if (!_0x59c83e) {
        return;
    }
    const _0x9ad394 = _0x59c83e.note || '';
    const _0x585d0a = _0x9ad394.match(/<REMOVE OTHER (.*) STATES>/gi);
    if (_0x585d0a) {
        const _0x59f187 = [_0x59c83e];
        for (const _0x5dc367 of _0x585d0a) {
            _0x5dc367.match(/<REMOVE OTHER (.*) STATES>/i);
            const _0x53f91d = String(RegExp.$1);
            this.removeStatesByCategoryAll(_0x53f91d, _0x59f187);
        }
    }
};
Game_Battler.prototype.removeStatesByDamage = function () {
    for (const _0x32fdbc of this.states()) {
        if (!_0x32fdbc) {
            continue;
        }
        if (!this.isStateAffected(_0x32fdbc.id)) {
            continue;
        }
        if (!_0x32fdbc.removeByDamage) {
            continue;
        }
        if (this.bypassRemoveStatesByDamage(_0x32fdbc)) {
            continue;
        }
        if (Math.randomInt(0x64) < _0x32fdbc.chanceByDamage) {
            this.removeState(_0x32fdbc.id);
        }
    }
};
VisuMZ.SkillsStatesCore.Game_Action_executeHpDamage_bypassStateDmgRemoval =
    Game_Action.prototype.executeHpDamage;
Game_Action.prototype.executeHpDamage = function (_0x2aec25, _0x41a42e) {
    $gameTemp._bypassRemoveStateDamage_action = this.item();
    $gameTemp._bypassRemoveStateDamage_user = this.subject();
    $gameTemp._bypassRemoveStateDamage_value = _0x41a42e;
    VisuMZ.SkillsStatesCore.Game_Action_executeHpDamage_bypassStateDmgRemoval.call(
        this,
        _0x2aec25,
        _0x41a42e
    );
    $gameTemp._bypassRemoveStateDamage_action = undefined;
    $gameTemp._bypassRemoveStateDamage_user = undefined;
    $gameTemp._bypassRemoveStateDamage_value = undefined;
};
Game_Battler.prototype.bypassRemoveStatesByDamage = function (_0x57e67c) {
    if ($gameTemp._bypassRemoveStateDamage_action) {
        const _0x10a33c = $gameTemp._bypassRemoveStateDamage_action;
        const _0x3cb7d5 = /<BYPASS STATE DAMAGE REMOVAL:[ ](.*)>/gi;
        if (
            DataManager.CheckBypassRemoveStatesByDamage(_0x57e67c, _0x10a33c, _0x3cb7d5, 'action')
        ) {
            return true;
        }
    }
    if ($gameTemp._bypassRemoveStateDamage_user) {
        const _0x4b0fd1 = $gameTemp._bypassRemoveStateDamage_user;
        if (_0x4b0fd1.isUserBypassRemoveStatesByDamage(_0x57e67c)) {
            return true;
        }
    }
    if (this.isTargetBypassRemoveStatesByDamage(_0x57e67c)) {
        return true;
    }
    return false;
};
Game_Battler.prototype.isUserBypassRemoveStatesByDamage = function (_0x3a223b) {
    const _0x1098dd = /<BYPASS STATE DAMAGE REMOVAL AS (?:ATTACKER|USER):[ ](.*)>/gi;
    for (const _0x680bf4 of this.traitObjects()) {
        if (!_0x680bf4) {
            continue;
        }
        if (
            DataManager.CheckBypassRemoveStatesByDamage(_0x3a223b, _0x680bf4, _0x1098dd, 'attacker')
        ) {
            return true;
        }
    }
    return false;
};
Game_Battler.prototype.isTargetBypassRemoveStatesByDamage = function (_0x42bf3e) {
    const _0x158dc4 = /<BYPASS STATE DAMAGE REMOVAL AS (?:TARGET|VICTIM):[ ](.*)>/gi;
    for (const _0x4a6434 of this.traitObjects()) {
        if (!_0x4a6434) {
            continue;
        }
        if (
            DataManager.CheckBypassRemoveStatesByDamage(_0x42bf3e, _0x4a6434, _0x158dc4, 'target')
        ) {
            return true;
        }
    }
    return false;
};
DataManager.CheckBypassRemoveStatesByDamage = function (_0xcad38, _0x40fe3b, _0x21e029, _0x543134) {
    const _0xde6f71 = '%1-%2-%3'.format(_0x40fe3b.name, _0x40fe3b.id, _0x543134);
    this._cache_CheckBypassRemoveStatesByDamage = this._cache_CheckBypassRemoveStatesByDamage || {};
    if (this._cache_CheckBypassRemoveStatesByDamage[_0xde6f71] !== undefined) {
        return this._cache_CheckBypassRemoveStatesByDamage[_0xde6f71].includes(_0xcad38.id);
    }
    const _0x172be0 = [];
    const _0x3c72eb = _0x40fe3b.note.match(_0x21e029);
    if (_0x3c72eb) {
        for (const _0x373a12 of _0x3c72eb) {
            _0x373a12.match(_0x21e029);
            const _0x21870a = String(RegExp.$1)
                .split(',')
                .map(_0x1256e6 => _0x1256e6.trim());
            for (let _0x161afc of _0x21870a) {
                _0x161afc = (String(_0x161afc) || '').trim();
                if (_0x161afc.match(/(\d+)[ ](?:THROUGH|to)[ ](\d+)/i)) {
                    const _0x3c0700 = Math.min(Number(RegExp.$1), Number(RegExp.$2));
                    const _0x38fa7f = Math.max(Number(RegExp.$1), Number(RegExp.$2));
                    for (let _0x557b81 = _0x3c0700; _0x557b81 <= _0x38fa7f; _0x557b81++) {
                        elements.push(_0x557b81);
                    }
                    continue;
                }
                const _0x127269 = /^\d+$/.test(_0x161afc);
                if (_0x127269) {
                    entryID = Number(_0x161afc);
                } else {
                    entryID = DataManager.getStateIdWithName(_0x161afc);
                }
                if (entryID) {
                    _0x172be0.push(entryID);
                }
            }
        }
    }
    this._cache_CheckBypassRemoveStatesByDamage[_0xde6f71] = _0x172be0;
    return this._cache_CheckBypassRemoveStatesByDamage[_0xde6f71].includes(_0xcad38.id);
};
VisuMZ.SkillsStatesCore.Game_Battler_addBuff = Game_Battler.prototype.addBuff;
Game_Battler.prototype.addBuff = function (_0x258171, _0x3132d6) {
    VisuMZ.SkillsStatesCore.Game_Battler_addBuff.call(this, _0x258171, _0x3132d6);
    if (this.isBuffAffected(_0x258171)) {
        this.onAddBuff(_0x258171, _0x3132d6);
    }
};
Game_Battler.prototype.isBuffPrevented = function (_0x305668) {};
VisuMZ.SkillsStatesCore.Game_Battler_addDebuff = Game_Battler.prototype.addDebuff;
Game_Battler.prototype.addDebuff = function (_0x2bd6b1, _0x273f14) {
    VisuMZ.SkillsStatesCore.Game_Battler_addDebuff.call(this, _0x2bd6b1, _0x273f14);
    if (this.isDebuffAffected(_0x2bd6b1)) {
        this.onAddDebuff(_0x2bd6b1, _0x273f14);
    }
};
Game_Battler.prototype.removeBuffsAuto = function () {
    for (let _0x3ccd78 = 0x0; _0x3ccd78 < this.buffLength(); _0x3ccd78++) {
        if (this.isBuffExpired(_0x3ccd78)) {
            const _0x34dd6c = this._buffs[_0x3ccd78];
            this.removeBuff(_0x3ccd78);
            if (_0x34dd6c > 0x0) {
                this.onExpireBuff(_0x3ccd78);
            }
            if (_0x34dd6c < 0x0) {
                this.onExpireDebuff(_0x3ccd78);
            }
        }
    }
};
Game_Battler.prototype.onAddBuff = function (_0x561af1, _0x27dcc4) {
    this.onAddBuffGlobalJS(_0x561af1, _0x27dcc4);
};
Game_Battler.prototype.onAddDebuff = function (_0x29fb40, _0x228f9d) {
    this.onAddDebuffGlobalJS(_0x29fb40, _0x228f9d);
};
Game_Battler.prototype.onEraseBuff = function (_0x5654a0) {
    Game_BattlerBase.prototype.onEraseBuff.call(this, _0x5654a0);
    this.onEraseBuffGlobalJS(_0x5654a0);
};
Game_Battler.prototype.onEraseDebuff = function (_0x2e589a) {
    Game_BattlerBase.prototype.onEraseDebuff.call(this, _0x2e589a);
    this.onEraseDebuffGlobalJS(_0x2e589a);
};
Game_Battler.prototype.onExpireBuff = function (_0x4cb3ca) {
    this.onExpireBuffGlobalJS(_0x4cb3ca);
};
Game_Battler.prototype.onExpireDebuff = function (_0x5f1c53) {
    this.onExpireDebuffGlobalJS(_0x5f1c53);
};
Game_Battler.prototype.onAddBuffGlobalJS = function (_0x3c812a, _0x9e06aa) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onAddBuffJS.call(this, _0x3c812a, _0x9e06aa);
};
Game_Battler.prototype.onAddDebuffGlobalJS = function (_0x3b2b2f, _0x2243c7) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onAddDebuffJS.call(this, _0x3b2b2f, _0x2243c7);
};
Game_BattlerBase.prototype.onEraseBuffGlobalJS = function (_0x200339) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onEraseBuffJS.call(this, _0x200339);
};
Game_BattlerBase.prototype.onEraseDebuffGlobalJS = function (_0x4375e8) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onEraseDebuffJS.call(this, _0x4375e8);
};
Game_Battler.prototype.onExpireBuffGlobalJS = function (_0x1083e8) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onExpireBuffJS.call(this, _0x1083e8);
};
Game_Battler.prototype.onExpireDebuffGlobalJS = function (_0x332ce3) {
    VisuMZ.SkillsStatesCore.Settings.Buffs.onExpireDebuffJS.call(this, _0x332ce3);
};
Game_Battler.prototype.onAddStateMakeCustomSlipValues = function (_0xa4fdaf) {
    const _0x38bd9e = VisuMZ.SkillsStatesCore;
    const _0x2bdb36 = [
        'stateHpSlipDamageJS',
        'stateHpSlipHealJS',
        'stateMpSlipDamageJS',
        'stateMpSlipHealJS',
        'stateTpSlipDamageJS',
        'stateTpSlipHealJS',
    ];
    for (const _0x2966e8 of _0x2bdb36) {
        if (_0x38bd9e[_0x2966e8][_0xa4fdaf]) {
            _0x38bd9e[_0x2966e8][_0xa4fdaf].call(this, _0xa4fdaf);
        }
    }
};
VisuMZ.SkillsStatesCore.Game_Battler_regenerateAll = Game_Battler.prototype.regenerateAll;
Game_Battler.prototype.regenerateAll = function () {
    this.recalculateSlipDamageJS();
    VisuMZ.SkillsStatesCore.Game_Battler_regenerateAll.call(this);
    this.setPassiveStateSlipDamageJS();
    this.regenerateAllSkillsStatesCore();
};
Game_Battler.prototype.setPassiveStateSlipDamageJS = function () {
    for (const _0x3b0660 of this.passiveStates()) {
        if (!_0x3b0660) {
            continue;
        }
        this.onAddStateMakeCustomSlipValues(_0x3b0660.id);
    }
};
Game_Battler.prototype.recalculateSlipDamageJS = function () {
    for (const _0x4786ca of this.states()) {
        if (!_0x4786ca) {
            continue;
        }
        if (_0x4786ca.note.match(/<JS SLIP REFRESH>/i)) {
            this.onAddStateMakeCustomSlipValues(_0x4786ca.id);
        }
    }
};
Game_Battler.prototype.regenerateAllSkillsStatesCore = function () {
    if (!this.isAlive()) {
        return;
    }
    const _0x25c227 = this.states();
    for (const _0x2eafce of _0x25c227) {
        if (!_0x2eafce) {
            continue;
        }
        this.onRegenerateCustomStateDamageOverTime(_0x2eafce);
    }
};
Game_Battler.prototype.onRegenerateCustomStateDamageOverTime = function (_0x3087c9) {
    const _0x3cd280 = this.getStateData(_0x3087c9.id, 'slipHp') || 0x0;
    const _0x49e479 = -this.maxSlipDamage();
    const _0x10e7ee = Math.max(_0x3cd280, _0x49e479);
    if (_0x10e7ee !== 0x0) {
        const _0x26c404 = this._result.hpDamage || 0x0;
        this.gainHp(_0x10e7ee);
        this._result.hpDamage += _0x26c404;
    }
    const _0xa35d6b = this.getStateData(_0x3087c9.id, 'slipMp') || 0x0;
    if (_0xa35d6b !== 0x0) {
        const _0xcd1c40 = this._result.mpDamage || 0x0;
        this.gainMp(_0xa35d6b);
        this._result.mpDamage += _0xcd1c40;
    }
    const _0x5b9bda = this.getStateData(_0x3087c9.id, 'slipTp') || 0x0;
    if (_0x5b9bda !== 0x0) {
        this.gainSilentTp(_0x5b9bda);
    }
};
VisuMZ.SkillsStatesCore.Game_Actor_skillTypes = Game_Actor.prototype.skillTypes;
Game_Actor.prototype.skillTypes = function () {
    const _0x4e6490 = VisuMZ.SkillsStatesCore.Game_Actor_skillTypes.call(this);
    const _0x19b9e3 = VisuMZ.SkillsStatesCore.Settings.Skills;
    let _0x4c8e4a = _0x19b9e3.HiddenSkillTypes;
    if ($gameParty.inBattle()) {
        _0x4c8e4a = _0x4c8e4a.concat(_0x19b9e3.BattleHiddenSkillTypes);
    }
    return _0x4e6490.filter(_0x45f648 => !_0x4c8e4a.includes(_0x45f648));
};
Game_Actor.prototype.usableSkills = function () {
    return this.skills().filter(_0x5170c5 => this.isSkillUsableForAutoBattle(_0x5170c5));
};
Game_Actor.prototype.isSkillUsableForAutoBattle = function (_0x2bf433) {
    if (!this.canUse(_0x2bf433)) {
        return false;
    }
    if (!_0x2bf433) {
        return false;
    }
    if (!this.isSkillTypeMatchForUse(_0x2bf433)) {
        return false;
    }
    if (this.isSkillHidden(_0x2bf433)) {
        return false;
    }
    return true;
};
Game_Actor.prototype.isSkillTypeMatchForUse = function (_0x25ed5b) {
    const _0x4be0b7 = this.skillTypes();
    const _0x52e340 = DataManager.getSkillTypes(_0x25ed5b);
    const _0x3f91ae = _0x4be0b7.filter(_0x540413 => _0x52e340.includes(_0x540413));
    return _0x3f91ae.length > 0x0;
};
Game_Actor.prototype.isSkillHidden = function (_0x50bc73) {
    if (!VisuMZ.SkillsStatesCore.CheckVisibleBattleNotetags(this, _0x50bc73)) {
        return true;
    }
    if (!VisuMZ.SkillsStatesCore.CheckVisibleSwitchNotetags(this, _0x50bc73)) {
        return true;
    }
    if (!VisuMZ.SkillsStatesCore.CheckVisibleSkillNotetags(this, _0x50bc73)) {
        return true;
    }
    return false;
};
Game_Actor.prototype.passiveStateObjects = function () {
    let _0x308f8c = [this.actor(), this.currentClass()];
    _0x308f8c = _0x308f8c.concat(this.equips().filter(_0x430d4d => _0x430d4d));
    for (const _0x44c15a of this._skills) {
        const _0x4c2770 = $dataSkills[_0x44c15a];
        if (!_0x4c2770) {
            continue;
        }
        _0x308f8c.push(_0x4c2770);
    }
    return _0x308f8c;
};
Game_Actor.prototype.addPassiveStatesByPluginParameters = function () {
    Game_Battler.prototype.addPassiveStatesByPluginParameters.call(this);
    const _0x5f348b = VisuMZ.SkillsStatesCore.Settings.PassiveStates.Actor;
    this._cache.passiveStates = this._cache.passiveStates.concat(_0x5f348b);
};
VisuMZ.SkillsStatesCore.Game_Actor_learnSkill = Game_Actor.prototype.learnSkill;
Game_Actor.prototype.learnSkill = function (_0x6c6603) {
    VisuMZ.SkillsStatesCore.Game_Actor_learnSkill.call(this, _0x6c6603);
    this._cache = {};
    this.passiveStates();
};
VisuMZ.SkillsStatesCore.Game_Actor_forgetSkill = Game_Actor.prototype.forgetSkill;
Game_Actor.prototype.forgetSkill = function (_0x5ca49f) {
    VisuMZ.SkillsStatesCore.Game_Actor_forgetSkill.call(this, _0x5ca49f);
    this._cache = {};
    this.passiveStates();
};
Game_Actor.prototype.stepsForTurn = function () {
    return VisuMZ.SkillsStatesCore.Settings.States.TurnEndOnMap ?? 0x14;
};
Game_Enemy.prototype.passiveStateObjects = function () {
    let _0x330f8c = [this.enemy()];
    return _0x330f8c.concat(this.skills());
};
Game_Enemy.prototype.addPassiveStatesByPluginParameters = function () {
    Game_Battler.prototype.addPassiveStatesByPluginParameters.call(this);
    const _0x95b823 = VisuMZ.SkillsStatesCore.Settings.PassiveStates.Enemy;
    this._cache.passiveStates = this._cache.passiveStates.concat(_0x95b823);
};
Game_Enemy.prototype.skills = function () {
    const _0x5c6e86 = [];
    for (const _0x7f9780 of this.enemy().actions) {
        const _0x4756d1 = $dataSkills[_0x7f9780.skillId];
        if (_0x4756d1 && !_0x5c6e86.includes(_0x4756d1)) {
            _0x5c6e86.push(_0x4756d1);
        }
    }
    return _0x5c6e86;
};
Game_Enemy.prototype.meetsStateCondition = function (_0x128059) {
    return this.hasState($dataStates[_0x128059]);
};
VisuMZ.SkillsStatesCore.Game_Unit_isAllDead = Game_Unit.prototype.isAllDead;
Game_Unit.prototype.isAllDead = function () {
    if (this.isPartyAllAffectedByGroupDefeatStates()) {
        return true;
    }
    return VisuMZ.SkillsStatesCore.Game_Unit_isAllDead.call(this);
};
Game_Unit.prototype.isPartyAllAffectedByGroupDefeatStates = function () {
    const _0x49b46f = this.aliveMembers();
    for (const _0x48519b of _0x49b46f) {
        if (!_0x48519b.isGroupDefeatStateAffected()) {
            return false;
        }
    }
    return true;
};
Game_Unit.prototype.refreshAllMembers = function () {
    for (const _0x199ee8 of this.members()) {
        if (!_0x199ee8) {
            continue;
        }
        _0x199ee8.refresh();
    }
};
VisuMZ.SkillsStatesCore.Game_Player_refresh = Game_Player.prototype.refresh;
Game_Player.prototype.refresh = function () {
    VisuMZ.SkillsStatesCore.Game_Player_refresh.call(this);
    $gameParty.refreshAllMembers();
    if ($gameParty.inBattle()) {
        $gameTroop.refreshAllMembers();
    }
};
VisuMZ.SkillsStatesCore.Game_Troop_setup = Game_Troop.prototype.setup;
Game_Troop.prototype.setup = function (_0x4a1202) {
    VisuMZ.SkillsStatesCore.Game_Troop_setup.call(this, _0x4a1202);
    this.makeCurrentTroopUniqueID();
};
Game_Troop.prototype.makeCurrentTroopUniqueID = function () {
    this._currentTroopUniqueID = Graphics.frameCount;
};
Game_Troop.prototype.getCurrentTroopUniqueID = function () {
    this._currentTroopUniqueID = this._currentTroopUniqueID || Graphics.frameCount;
    return this._currentTroopUniqueID;
};
Scene_Skill.prototype.isBottomHelpMode = function () {
    if (ConfigManager.uiMenuStyle && ConfigManager.uiHelpPosition !== undefined) {
        return ConfigManager.uiHelpPosition;
    } else {
        if (this.isUseSkillsStatesCoreUpdatedLayout()) {
            return this.updatedLayoutStyle().match(/LOWER/i);
        } else {
            Scene_ItemBase.prototype.isRightInputMode.call(this);
        }
    }
};
Scene_Skill.prototype.isRightInputMode = function () {
    if (ConfigManager.uiMenuStyle && ConfigManager.uiInputPosition !== undefined) {
        return ConfigManager.uiInputPosition;
    } else {
        return this.isUseSkillsStatesCoreUpdatedLayout()
            ? this.updatedLayoutStyle().match(/RIGHT/i)
            : Scene_ItemBase.prototype.isRightInputMode.call(this);
    }
};
Scene_Skill.prototype.updatedLayoutStyle = function () {
    return VisuMZ.SkillsStatesCore.Settings.Skills.LayoutStyle;
};
Scene_Skill.prototype.isUseModernControls = function () {
    return this._categoryWindow && this._categoryWindow.isUseModernControls();
};
Scene_Skill.prototype.isUseSkillsStatesCoreUpdatedLayout = function () {
    return VisuMZ.SkillsStatesCore.Settings.Skills.EnableLayout;
};
VisuMZ.SkillsStatesCore.Scene_Skill_helpWindowRect = Scene_Skill.prototype.helpWindowRect;
Scene_Skill.prototype.helpWindowRect = function () {
    return this.isUseSkillsStatesCoreUpdatedLayout()
        ? this.helpWindowRectSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Scene_Skill_helpWindowRect.call(this);
};
Scene_Skill.prototype.helpWindowRectSkillsStatesCore = function () {
    const _0x3fe9a3 = this.helpAreaTop();
    const _0x1a03c0 = Graphics.boxWidth;
    const _0x5b2586 = this.helpAreaHeight();
    return new Rectangle(0x0, _0x3fe9a3, _0x1a03c0, _0x5b2586);
};
VisuMZ.SkillsStatesCore.Scene_Skill_skillTypeWindowRect = Scene_Skill.prototype.skillTypeWindowRect;
Scene_Skill.prototype.skillTypeWindowRect = function () {
    return this.isUseSkillsStatesCoreUpdatedLayout()
        ? this.skillTypeWindowRectSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Scene_Skill_skillTypeWindowRect.call(this);
};
Scene_Skill.prototype.mainCommandWidth = function () {
    return (
        VisuMZ.SkillsStatesCore.Settings.Skills.CmdWidth ??
        Scene_MenuBase.prototype.mainCommandWidth.call(this)
    );
};
Scene_Skill.prototype.skillTypeWindowRectSkillsStatesCore = function () {
    const _0x398750 = this.mainCommandWidth();
    const _0x1ea00f = this.calcWindowHeight(0x3, true);
    const _0xf1cd20 = this.isRightInputMode() ? Graphics.boxWidth - _0x398750 : 0x0;
    const _0x2b8454 = this.mainAreaTop();
    return new Rectangle(_0xf1cd20, _0x2b8454, _0x398750, _0x1ea00f);
};
VisuMZ.SkillsStatesCore.Scene_Skill_statusWindowRect = Scene_Skill.prototype.statusWindowRect;
Scene_Skill.prototype.statusWindowRect = function () {
    return this.isUseSkillsStatesCoreUpdatedLayout()
        ? this.statusWindowRectSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Scene_Skill_statusWindowRect.call(this);
};
Scene_Skill.prototype.statusWindowRectSkillsStatesCore = function () {
    const _0x380448 = Graphics.boxWidth - this.mainCommandWidth();
    const _0x463997 = this._skillTypeWindow.height;
    const _0x406af5 = this.isRightInputMode() ? 0x0 : Graphics.boxWidth - _0x380448;
    const _0x559f70 = this.mainAreaTop();
    return new Rectangle(_0x406af5, _0x559f70, _0x380448, _0x463997);
};
VisuMZ.SkillsStatesCore.Scene_Skill_createItemWindow = Scene_Skill.prototype.createItemWindow;
Scene_Skill.prototype.createItemWindow = function () {
    VisuMZ.SkillsStatesCore.Scene_Skill_createItemWindow.call(this);
    if (this.allowCreateShopStatusWindow()) {
        this.createShopStatusWindow();
    }
};
VisuMZ.SkillsStatesCore.Scene_Skill_itemWindowRect = Scene_Skill.prototype.itemWindowRect;
Scene_Skill.prototype.itemWindowRect = function () {
    if (this.isUseSkillsStatesCoreUpdatedLayout()) {
        return this.itemWindowRectSkillsStatesCore();
    } else {
        const _0x29cc3e = VisuMZ.SkillsStatesCore.Scene_Skill_itemWindowRect.call(this);
        if (this.allowCreateShopStatusWindow() && this.adjustItemWidthByShopStatus()) {
            _0x29cc3e.width -= this.shopStatusWidth();
        }
        return _0x29cc3e;
    }
};
Scene_Skill.prototype.itemWindowRectSkillsStatesCore = function () {
    const _0x3c2b23 = Graphics.boxWidth - this.shopStatusWidth();
    const _0x22edae = this.mainAreaHeight() - this._statusWindow.height;
    const _0x101708 = this.isRightInputMode() ? Graphics.boxWidth - _0x3c2b23 : 0x0;
    const _0x55185c = this._statusWindow.y + this._statusWindow.height;
    return new Rectangle(_0x101708, _0x55185c, _0x3c2b23, _0x22edae);
};
Scene_Skill.prototype.allowCreateShopStatusWindow = function () {
    if (!Imported.VisuMZ_1_ItemsEquipsCore) {
        return false;
    } else {
        return this.isUseSkillsStatesCoreUpdatedLayout()
            ? true
            : VisuMZ.SkillsStatesCore.Settings.Skills.ShowShopStatus;
    }
};
Scene_Skill.prototype.adjustItemWidthByShopStatus = function () {
    return VisuMZ.SkillsStatesCore.Settings.Skills.SkillSceneAdjustSkillList;
};
Scene_Skill.prototype.createShopStatusWindow = function () {
    const _0x44b441 = this.shopStatusWindowRect();
    this._shopStatusWindow = new Window_ShopStatus(_0x44b441);
    this.addWindow(this._shopStatusWindow);
    this._itemWindow.setStatusWindow(this._shopStatusWindow);
    const _0x293030 = VisuMZ.SkillsStatesCore.Settings.Skills.SkillSceneStatusBgType;
    this._shopStatusWindow.setBackgroundType(_0x293030 || 0x0);
};
Scene_Skill.prototype.shopStatusWindowRect = function () {
    return this.isUseSkillsStatesCoreUpdatedLayout()
        ? this.shopStatusWindowRectSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Settings.Skills.SkillMenuStatusRect.call(this);
};
Scene_Skill.prototype.shopStatusWindowRectSkillsStatesCore = function () {
    const _0x516166 = this.shopStatusWidth();
    const _0x5ceeee = this._itemWindow.height;
    const _0x3eb78e = this.isRightInputMode() ? 0x0 : Graphics.boxWidth - this.shopStatusWidth();
    const _0x377ed = this._itemWindow.y;
    return new Rectangle(_0x3eb78e, _0x377ed, _0x516166, _0x5ceeee);
};
Scene_Skill.prototype.shopStatusWidth = function () {
    return Imported.VisuMZ_1_ItemsEquipsCore ? Scene_Shop.prototype.statusWidth() : 0x0;
};
Scene_Skill.prototype.buttonAssistText1 = function () {
    return this._skillTypeWindow && this._skillTypeWindow.active
        ? TextManager.buttonAssistSwitch
        : '';
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_initMembers = Sprite_Gauge.prototype.initMembers;
Sprite_Gauge.prototype.initMembers = function () {
    VisuMZ.SkillsStatesCore.Sprite_Gauge_initMembers.call(this);
    this._costSettings = null;
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_setup = Sprite_Gauge.prototype.setup;
Sprite_Gauge.prototype.setup = function (_0x36fedb, _0x2682be) {
    this.setupSkillsStatesCore(_0x36fedb, _0x2682be);
    _0x2682be = _0x2682be.toLowerCase();
    VisuMZ.SkillsStatesCore.Sprite_Gauge_setup.call(this, _0x36fedb, _0x2682be);
};
Sprite_Gauge.prototype.setupSkillsStatesCore = function (_0x11e3a4, _0xcc0d2e) {
    const _0x582d4a = VisuMZ.SkillsStatesCore.Settings.Costs.filter(
        _0x33676b => _0x33676b.Name.toUpperCase() === _0xcc0d2e.toUpperCase()
    );
    if (_0x582d4a.length >= 0x1) {
        this._costSettings = _0x582d4a[0x0];
    } else {
        this._costSettings = null;
    }
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_currentValue = Sprite_Gauge.prototype.currentValue;
Sprite_Gauge.prototype.currentValue = function () {
    return this._battler && this._costSettings
        ? this.currentValueSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Sprite_Gauge_currentValue.call(this);
};
Sprite_Gauge.prototype.currentValueSkillsStatesCore = function () {
    return this._costSettings.GaugeCurrentJS.call(this._battler);
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_currentMaxValue = Sprite_Gauge.prototype.currentMaxValue;
Sprite_Gauge.prototype.currentMaxValue = function () {
    return this._battler && this._costSettings
        ? this.currentMaxValueSkillsStatesCore()
        : VisuMZ.SkillsStatesCore.Sprite_Gauge_currentMaxValue.call(this);
};
Sprite_Gauge.prototype.currentMaxValueSkillsStatesCore = function () {
    return this._costSettings.GaugeMaxJS.call(this._battler);
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_gaugeRate = Sprite_Gauge.prototype.gaugeRate;
Sprite_Gauge.prototype.gaugeRate = function () {
    const _0x2b9916 = VisuMZ.SkillsStatesCore.Sprite_Gauge_gaugeRate.call(this);
    return _0x2b9916.clamp(0x0, 0x1);
};
VisuMZ.SkillsStatesCore.Sprite_Gauge_redraw = Sprite_Gauge.prototype.redraw;
Sprite_Gauge.prototype.redraw = function () {
    if (this._battler && this._costSettings) {
        this.bitmap.clear();
        this.redrawSkillsStatesCore();
    } else {
        VisuMZ.SkillsStatesCore.Sprite_Gauge_redraw.call(this);
    }
};
Sprite_Gauge.prototype.currentDisplayedValue = function () {
    let _0x386cc8 = this.currentValue();
    if (Imported.VisuMZ_0_CoreEngine && this.useDigitGrouping()) {
        _0x386cc8 = VisuMZ.GroupDigits(_0x386cc8);
    }
    return _0x386cc8;
};
Sprite_Gauge.prototype.redrawSkillsStatesCore = function () {
    this.bitmap.clear();
    this._costSettings.GaugeDrawJS.call(this);
};
Sprite_Gauge.prototype.drawFullGauge = function (
    _0x436e0a,
    _0x1d5688,
    _0x5674a1,
    _0x28d1a2,
    _0x1d9ea1,
    _0xbb24e2
) {
    const _0x7c1761 = this.gaugeRate();
    const _0x3f2438 = Math.floor((_0x1d9ea1 - 0x2) * _0x7c1761);
    const _0x334ee0 = _0xbb24e2 - 0x2;
    const _0x14ed55 = this.gaugeBackColor();
    this.bitmap.fillRect(_0x5674a1, _0x28d1a2, _0x1d9ea1, _0xbb24e2, _0x14ed55);
    this.bitmap.gradientFillRect(
        _0x5674a1 + 0x1,
        _0x28d1a2 + 0x1,
        _0x3f2438,
        _0x334ee0,
        _0x436e0a,
        _0x1d5688
    );
};
Sprite_Gauge.prototype.labelFontFace = function () {
    const _0x4ccbe0 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    return _0x4ccbe0.LabelFontMainType === 'number'
        ? $gameSystem.numberFontFace()
        : $gameSystem.mainFontFace();
};
Sprite_Gauge.prototype.labelFontSize = function () {
    const _0x50c456 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    return _0x50c456.LabelFontMainType === 'number'
        ? $gameSystem.mainFontSize() - 0x6
        : $gameSystem.mainFontSize() - 0x2;
};
Sprite_Gauge.prototype.valueFontFace = function () {
    const _0x53a090 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    return _0x53a090.ValueFontMainType === 'number'
        ? $gameSystem.numberFontFace()
        : $gameSystem.mainFontFace();
};
Sprite_Gauge.prototype.valueFontSize = function () {
    const _0x174514 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    return _0x174514.ValueFontMainType === 'number'
        ? $gameSystem.mainFontSize() - 0x6
        : $gameSystem.mainFontSize() - 0x2;
};
Sprite_Gauge.prototype.labelColor = function () {
    const _0x53faa6 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    if (_0x53faa6.MatchLabelColor) {
        if (_0x53faa6.MatchLabelGaugeColor === 0x1) {
            return this.gaugeColor1();
        } else {
            if (_0x53faa6.MatchLabelGaugeColor === 0x2) {
                return this.gaugeColor2();
            }
        }
    }
    const _0x1a038a = _0x53faa6.PresetLabelGaugeColor;
    return ColorManager.getColor(_0x1a038a);
};
Sprite_Gauge.prototype.labelOutlineColor = function () {
    const _0x2b0e08 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    if (this.labelOutlineWidth() <= 0x0) {
        return 'rgba(0, 0, 0, 0)';
    } else {
        return _0x2b0e08.LabelOutlineSolid ? 'rgba(0, 0, 0, 1)' : ColorManager.outlineColor();
    }
};
Sprite_Gauge.prototype.labelOutlineWidth = function () {
    return VisuMZ.SkillsStatesCore.Settings.Gauge.LabelOutlineWidth || 0x0;
};
Sprite_Gauge.prototype.valueOutlineColor = function () {
    const _0x4b6ba1 = VisuMZ.SkillsStatesCore.Settings.Gauge;
    if (this.valueOutlineWidth() <= 0x0) {
        return 'rgba(0, 0, 0, 0)';
    } else {
        return _0x4b6ba1.ValueOutlineSolid ? 'rgba(0, 0, 0, 1)' : ColorManager.outlineColor();
    }
};
Sprite_Gauge.prototype.valueOutlineWidth = function () {
    return VisuMZ.SkillsStatesCore.Settings.Gauge.ValueOutlineWidth || 0x0;
};
VisuMZ.SkillsStatesCore.Sprite_StateIcon_loadBitmap = Sprite_StateIcon.prototype.loadBitmap;
Sprite_StateIcon.prototype.loadBitmap = function () {
    VisuMZ.SkillsStatesCore.Sprite_StateIcon_loadBitmap.call(this);
    this.createTurnDisplaySprite();
};
Sprite_StateIcon.prototype.createTurnDisplaySprite = function () {
    const _0x50605c = Window_Base.prototype.lineHeight();
    this._turnDisplaySprite = new Sprite();
    this._turnDisplaySprite.bitmap = new Bitmap(ImageManager.iconWidth, _0x50605c);
    this._turnDisplaySprite.anchor.x = this.anchor.x;
    this._turnDisplaySprite.anchor.y = this.anchor.y;
    this.addChild(this._turnDisplaySprite);
    this.contents = this._turnDisplaySprite.bitmap;
};
VisuMZ.SkillsStatesCore.Sprite_StateIcon_updateFrame = Sprite_StateIcon.prototype.updateFrame;
Sprite_StateIcon.prototype.updateFrame = function () {
    VisuMZ.SkillsStatesCore.Sprite_StateIcon_updateFrame.call(this);
    this.updateTurnDisplaySprite();
};
Sprite_StateIcon.prototype.drawText = function (
    _0x491661,
    _0x22f430,
    _0xf51ebe,
    _0x27ac44,
    _0x315797
) {
    this.contents.drawText(
        _0x491661,
        _0x22f430,
        _0xf51ebe,
        _0x27ac44,
        this.contents.height,
        _0x315797
    );
};
Sprite_StateIcon.prototype.updateTurnDisplaySprite = function () {
    this.resetFontSettings();
    this.contents.clear();
    const _0x1b2a15 = this._battler;
    if (!_0x1b2a15) {
        return;
    }
    const _0x3f6b8d = _0x1b2a15.states().filter(_0xbb1ceb => _0xbb1ceb.iconIndex > 0x0);
    const _0xdae356 = [...Array(0x8).keys()].filter(_0x16bd94 => _0x1b2a15.buff(_0x16bd94) !== 0x0);
    const _0x13e24c = this._animationIndex;
    const _0x24783e = _0x3f6b8d[_0x13e24c];
    if (_0x24783e) {
        Window_Base.prototype.drawActorStateTurns.call(this, _0x1b2a15, _0x24783e, 0x0, 0x0);
        Window_Base.prototype.drawActorStateData.call(this, _0x1b2a15, _0x24783e, 0x0, 0x0);
    } else {
        const _0x388ad1 = _0xdae356[_0x13e24c - _0x3f6b8d.length];
        if (_0x388ad1 === undefined) {
            return;
        }
        Window_Base.prototype.drawActorBuffTurns.call(this, _0x1b2a15, _0x388ad1, 0x0, 0x0);
        Window_Base.prototype.drawActorBuffRates.call(this, _0x1b2a15, _0x388ad1, 0x0, 0x0);
    }
};
Sprite_StateIcon.prototype.resetFontSettings = function () {
    this.contents.fontFace = $gameSystem.mainFontFace();
    this.contents.fontSize = $gameSystem.mainFontSize();
    this.resetTextColor();
};
Sprite_StateIcon.prototype.resetTextColor = function () {
    this.changeTextColor(ColorManager.normalColor());
    this.changeOutlineColor(ColorManager.outlineColor());
};
Sprite_StateIcon.prototype.changeTextColor = function (_0x4a2c64) {
    this.contents.textColor = _0x4a2c64;
};
Sprite_StateIcon.prototype.changeOutlineColor = function (_0x4ff49f) {
    this.contents.outlineColor = _0x4ff49f;
};
Sprite_StateIcon.prototype.hide = function () {
    this._hidden = true;
    this.updateVisibility();
};
Window_Base.prototype.drawSkillCost = function (
    _0x549bfc,
    _0x3c2d2d,
    _0x7f62bb,
    _0x5180cb,
    _0x1d4b97
) {
    const _0x1ce4b8 = this.createAllSkillCostText(_0x549bfc, _0x3c2d2d);
    const _0x4318b2 = this.textSizeEx(_0x1ce4b8, _0x7f62bb, _0x5180cb, _0x1d4b97);
    const _0x216359 = _0x7f62bb + _0x1d4b97 - _0x4318b2.width;
    this.drawTextEx(_0x1ce4b8, _0x216359, _0x5180cb, _0x1d4b97);
    this.resetFontSettings();
};
Window_Base.prototype.createAllSkillCostText = function (_0x42f0dd, _0x12fb0b) {
    let _0x21c107 = '';
    for (settings of VisuMZ.SkillsStatesCore.Settings.Costs) {
        if (!this.isSkillCostShown(_0x42f0dd, _0x12fb0b, settings)) {
            continue;
        }
        if (_0x21c107.length > 0x0) {
            _0x21c107 += this.skillCostSeparator();
        }
        _0x21c107 += this.createSkillCostText(_0x42f0dd, _0x12fb0b, settings);
    }
    _0x21c107 = this.makeAdditionalSkillCostText(_0x42f0dd, _0x12fb0b, _0x21c107);
    if (_0x12fb0b.note.match(/<CUSTOM COST TEXT>\s*([\s\S]*)\s*<\/CUSTOM COST TEXT>/i)) {
        if (_0x21c107.length > 0x0) {
            _0x21c107 += this.skillCostSeparator();
        }
        _0x21c107 += String(RegExp.$1);
    }
    return _0x21c107;
};
Window_Base.prototype.makeAdditionalSkillCostText = function (_0x51fc77, _0x7e8d4b, _0x83f876) {
    return _0x83f876;
};
Window_Base.prototype.isSkillCostShown = function (_0x318854, _0x403af2, _0x2d8a85) {
    let _0x4fa8f3 = _0x2d8a85.CalcJS.call(_0x318854, _0x403af2);
    _0x4fa8f3 = _0x318854.adjustSkillCost(_0x403af2, _0x4fa8f3, _0x2d8a85);
    return _0x2d8a85.ShowJS.call(_0x318854, _0x403af2, _0x4fa8f3, _0x2d8a85);
};
Window_Base.prototype.createSkillCostText = function (_0xfaaae1, _0x14e641, _0x464953) {
    let _0xfbfa6a = _0x464953.CalcJS.call(_0xfaaae1, _0x14e641);
    _0xfbfa6a = _0xfaaae1.adjustSkillCost(_0x14e641, _0xfbfa6a, _0x464953);
    return _0x464953.TextJS.call(_0xfaaae1, _0x14e641, _0xfbfa6a, _0x464953);
};
Window_Base.prototype.skillCostSeparator = function () {
    return ' ';
};
Window_Base.prototype.drawActorIcons = function (_0x26f88e, _0x2e471f, _0x5c9b0b, _0x3d870a) {
    if (!_0x26f88e) {
        return;
    }
    VisuMZ.SkillsStatesCore.Window_StatusBase_drawActorIcons.call(
        this,
        _0x26f88e,
        _0x2e471f,
        _0x5c9b0b,
        _0x3d870a
    );
    this.drawActorIconsAllTurnCounters(_0x26f88e, _0x2e471f, _0x5c9b0b, _0x3d870a);
};
Window_Base.prototype.drawActorIconsAllTurnCounters = function (
    _0x5c1acc,
    _0x4aa63f,
    _0x1ec49f,
    _0x4e3b1a
) {
    _0x4e3b1a = _0x4e3b1a || 0x90;
    const _0x23b701 = ImageManager.standardIconWidth || 0x20;
    const _0x5ead63 = _0x5c1acc.allIcons().slice(0x0, Math.floor(_0x4e3b1a / _0x23b701));
    const _0x54ac09 = _0x5c1acc.states().filter(_0x50abfe => _0x50abfe.iconIndex > 0x0);
    const _0x57205a = [...Array(0x8).keys()].filter(_0x951e43 => _0x5c1acc.buff(_0x951e43) !== 0x0);
    const _0x233227 = [];
    let _0x31dbbc = _0x4aa63f;
    for (let _0x115513 = 0x0; _0x115513 < _0x5ead63.length; _0x115513++) {
        this.resetFontSettings();
        const _0x1069a = _0x54ac09[_0x115513];
        if (_0x1069a) {
            if (!_0x233227.includes(_0x1069a)) {
                this.drawActorStateTurns(_0x5c1acc, _0x1069a, _0x31dbbc, _0x1ec49f);
            }
            this.drawActorStateData(_0x5c1acc, _0x1069a, _0x31dbbc, _0x1ec49f);
            _0x233227.push(_0x1069a);
        } else {
            const _0x30b6b3 = _0x57205a[_0x115513 - _0x54ac09.length];
            this.drawActorBuffTurns(_0x5c1acc, _0x30b6b3, _0x31dbbc, _0x1ec49f);
            this.drawActorBuffRates(_0x5c1acc, _0x30b6b3, _0x31dbbc, _0x1ec49f);
        }
        _0x31dbbc += _0x23b701;
    }
};
Window_Base.prototype.drawActorStateTurns = function (_0x1a0a6e, _0x32fe5b, _0x4a6309, _0x473fcf) {
    if (!VisuMZ.SkillsStatesCore.Settings.States.ShowTurns) {
        return;
    }
    if (!_0x1a0a6e.isStateAffected(_0x32fe5b.id)) {
        return;
    }
    if (_0x32fe5b.autoRemovalTiming === 0x0) {
        return;
    }
    if (_0x32fe5b.note.match(/<HIDE STATE TURNS>/i)) {
        return;
    }
    const _0xa6fb62 = ImageManager.standardIconWidth || 0x20;
    const _0x4e5d8e = _0x1a0a6e.stateTurns(_0x32fe5b.id);
    const _0xa2464b = ColorManager.stateColor(_0x32fe5b);
    this.changeTextColor(_0xa2464b);
    this.changeOutlineColor('rgba(0, 0, 0, 1)');
    this.contents.fontBold = true;
    this.contents.fontSize = VisuMZ.SkillsStatesCore.Settings.States.TurnFontSize;
    _0x4a6309 += VisuMZ.SkillsStatesCore.Settings.States.TurnOffsetX;
    _0x473fcf += VisuMZ.SkillsStatesCore.Settings.States.TurnOffsetY;
    this.drawText(_0x4e5d8e, _0x4a6309, _0x473fcf, _0xa6fb62, 'right');
    this.contents.fontBold = false;
    this.resetFontSettings();
};
Window_Base.prototype.drawActorStateData = function (_0x5a75b3, _0x43fcef, _0x49f054, _0x80ef38) {
    if (!VisuMZ.SkillsStatesCore.Settings.States.ShowData) {
        return;
    }
    const _0x373006 = ImageManager.standardIconWidth || 0x20;
    const _0x427eb5 = ColorManager.normalColor();
    this.changeTextColor(_0x427eb5);
    this.changeOutlineColor('rgba(0, 0, 0, 1)');
    this.contents.fontBold = true;
    this.contents.fontSize = VisuMZ.SkillsStatesCore.Settings.States.DataFontSize;
    _0x49f054 += VisuMZ.SkillsStatesCore.Settings.States.DataOffsetX;
    _0x80ef38 += VisuMZ.SkillsStatesCore.Settings.States.DataOffsetY;
    const _0x2ca020 = String(_0x5a75b3.getStateDisplay(_0x43fcef.id));
    this.drawText(_0x2ca020, _0x49f054, _0x80ef38, _0x373006, 'center');
    this.contents.fontBold = false;
    this.resetFontSettings();
};
Window_Base.prototype.drawActorBuffTurns = function (_0x1b5472, _0x487487, _0x18ec63, _0x2378fb) {
    if (!VisuMZ.SkillsStatesCore.Settings.Buffs.ShowTurns) {
        return;
    }
    const _0x32ec8f = _0x1b5472.buff(_0x487487);
    if (_0x32ec8f === 0x0) {
        return;
    }
    const _0x393754 = _0x1b5472.buffTurns(_0x487487);
    const _0x1b3ce7 = ImageManager.iconWidth;
    const _0x3a8849 = _0x32ec8f > 0x0 ? ColorManager.buffColor() : ColorManager.debuffColor();
    this.changeTextColor(_0x3a8849);
    this.changeOutlineColor('rgba(0, 0, 0, 1)');
    this.contents.fontBold = true;
    this.contents.fontSize = VisuMZ.SkillsStatesCore.Settings.Buffs.TurnFontSize;
    _0x18ec63 += VisuMZ.SkillsStatesCore.Settings.Buffs.TurnOffsetX;
    _0x2378fb += VisuMZ.SkillsStatesCore.Settings.Buffs.TurnOffsetY;
    this.drawText(_0x393754, _0x18ec63, _0x2378fb, _0x1b3ce7, 'right');
    this.contents.fontBold = false;
    this.resetFontSettings();
};
Window_Base.prototype.drawActorBuffRates = function (_0x3c4635, _0x40f73b, _0x190cf1, _0x254065) {
    if (!VisuMZ.SkillsStatesCore.Settings.Buffs.ShowData) {
        return;
    }
    const _0xdd554c = _0x3c4635.paramBuffRate(_0x40f73b);
    const _0x2a169f = _0x3c4635.buff(_0x40f73b);
    const _0x4244f7 = ImageManager.standardIconWidth || 0x20;
    const _0x327b38 = _0x2a169f > 0x0 ? ColorManager.buffColor() : ColorManager.debuffColor();
    this.changeTextColor(_0x327b38);
    this.changeOutlineColor('rgba(0, 0, 0, 1)');
    this.contents.fontBold = true;
    this.contents.fontSize = VisuMZ.SkillsStatesCore.Settings.Buffs.DataFontSize;
    _0x190cf1 += VisuMZ.SkillsStatesCore.Settings.Buffs.DataOffsetX;
    _0x254065 += VisuMZ.SkillsStatesCore.Settings.Buffs.DataOffsetY;
    const _0x21363f = '%1%'.format(Math.round(_0xdd554c * 0x64));
    this.drawText(_0x21363f, _0x190cf1, _0x254065, _0x4244f7, 'center');
    this.contents.fontBold = false;
    this.resetFontSettings();
};
VisuMZ.SkillsStatesCore.Window_StatusBase_placeGauge = Window_StatusBase.prototype.placeGauge;
Window_StatusBase.prototype.placeGauge = function (_0x221c78, _0x2e4514, _0x19a6d9, _0x3edd81) {
    if (_0x221c78.isActor()) {
        _0x2e4514 = this.convertGaugeTypeSkillsStatesCore(_0x221c78, _0x2e4514);
    }
    this.placeExactGauge(_0x221c78, _0x2e4514, _0x19a6d9, _0x3edd81);
};
Window_StatusBase.prototype.placeExactGauge = function (
    _0x32f235,
    _0x5f589b,
    _0x56155e,
    _0x284d8d
) {
    if (['none', 'untitled'].includes(_0x5f589b.toLowerCase())) {
        return;
    }
    VisuMZ.SkillsStatesCore.Window_StatusBase_placeGauge.call(
        this,
        _0x32f235,
        _0x5f589b,
        _0x56155e,
        _0x284d8d
    );
};
Window_StatusBase.prototype.convertGaugeTypeSkillsStatesCore = function (_0x5bbbba, _0x2966b9) {
    const _0x1a274a = _0x5bbbba.currentClass().note;
    if (_0x2966b9 === 'hp' && _0x1a274a.match(/<REPLACE HP GAUGE:[ ](.*)>/i)) {
        return String(RegExp.$1);
    } else {
        if (_0x2966b9 === 'mp' && _0x1a274a.match(/<REPLACE MP GAUGE:[ ](.*)>/i)) {
            return String(RegExp.$1);
        } else {
            return _0x2966b9 === 'tp' && _0x1a274a.match(/<REPLACE TP GAUGE:[ ](.*)>/i)
                ? String(RegExp.$1)
                : _0x2966b9;
        }
    }
};
VisuMZ.SkillsStatesCore.Window_StatusBase_drawActorIcons =
    Window_StatusBase.prototype.drawActorIcons;
Window_StatusBase.prototype.drawActorIcons = function (_0x476388, _0x221c8e, _0x2de445, _0x238a0a) {
    if (!_0x476388) {
        return;
    }
    Window_Base.prototype.drawActorIcons.call(this, _0x476388, _0x221c8e, _0x2de445, _0x238a0a);
};
VisuMZ.SkillsStatesCore.Window_SkillType_initialize = Window_SkillType.prototype.initialize;
Window_SkillType.prototype.initialize = function (_0x2fb099) {
    VisuMZ.SkillsStatesCore.Window_SkillType_initialize.call(this, _0x2fb099);
    this.createCommandNameWindow(_0x2fb099);
};
Window_SkillType.prototype.createCommandNameWindow = function (_0x1da3c2) {
    const _0x4fe0c1 = new Rectangle(0x0, 0x0, _0x1da3c2.width, _0x1da3c2.height);
    this._commandNameWindow = new Window_Base(_0x4fe0c1);
    this._commandNameWindow.opacity = 0x0;
    this.addChild(this._commandNameWindow);
    this.updateCommandNameWindow();
};
Window_SkillType.prototype.callUpdateHelp = function () {
    Window_Command.prototype.callUpdateHelp.call(this);
    if (this._commandNameWindow) {
        this.updateCommandNameWindow();
    }
};
Window_SkillType.prototype.updateCommandNameWindow = function () {
    const _0x41407d = this._commandNameWindow;
    _0x41407d.contents.clear();
    const _0x4bfa7c = this.commandStyleCheck(this.index());
    if (_0x4bfa7c === 'icon' && this.maxItems() > 0x0) {
        const _0x47c833 = this.itemLineRect(this.index());
        let _0x51ee89 = this.commandName(this.index());
        _0x51ee89 = _0x51ee89.replace(/\\I\[(\d+)\]/gi, '');
        _0x41407d.resetFontSettings();
        this.commandNameWindowDrawBackground(_0x51ee89, _0x47c833);
        this.commandNameWindowDrawText(_0x51ee89, _0x47c833);
        this.commandNameWindowCenter(_0x51ee89, _0x47c833);
    }
};
Window_SkillType.prototype.commandNameWindowDrawBackground = function (_0x198bc5, _0x520c23) {};
Window_SkillType.prototype.commandNameWindowDrawText = function (_0x35cfa9, _0x2f8094) {
    const _0x357995 = this._commandNameWindow;
    _0x357995.drawText(_0x35cfa9, 0x0, _0x2f8094.y, _0x357995.innerWidth, 'center');
};
Window_SkillType.prototype.commandNameWindowCenter = function (_0x4034d6, _0x5e0187) {
    const _0x1bef57 = this._commandNameWindow;
    const _0x1efcce = $gameSystem.windowPadding();
    const _0x543f22 = _0x5e0187.x + Math.floor(_0x5e0187.width / 0x2) + _0x1efcce;
    _0x1bef57.x = _0x1bef57.width / -0x2 + _0x543f22;
    _0x1bef57.y = Math.floor(_0x5e0187.height / 0x2);
};
Window_SkillType.prototype.isUseModernControls = function () {
    return Imported.VisuMZ_0_CoreEngine && Window_Command.prototype.isUseModernControls.call(this);
};
Window_SkillType.prototype.makeCommandList = function () {
    if (!this._actor) {
        return;
    }
    const _0x3c622d = this._actor.skillTypes();
    for (const _0x52e1d9 of _0x3c622d) {
        const _0xa0dd98 = this.makeCommandName(_0x52e1d9);
        this.addCommand(_0xa0dd98, 'skill', true, _0x52e1d9);
    }
};
Window_SkillType.prototype.makeCommandName = function (_0x50c637) {
    let _0x1dcbdc = $dataSystem.skillTypes[_0x50c637];
    if (_0x1dcbdc.match(/\\I\[(\d+)\]/i)) {
        return _0x1dcbdc;
    }
    if (this.commandStyle() === 'text') {
        return _0x1dcbdc;
    }
    const _0x2ca9c8 = VisuMZ.SkillsStatesCore.Settings.Skills;
    const _0x5a7717 = $dataSystem.magicSkills.includes(_0x50c637);
    const _0xb7dbca = _0x5a7717 ? _0x2ca9c8.IconStypeMagic : _0x2ca9c8.IconStypeNorm;
    return '\\I[%1]%2'.format(_0xb7dbca, _0x1dcbdc);
};
Window_SkillType.prototype.itemTextAlign = function () {
    return VisuMZ.SkillsStatesCore.Settings.Skills.CmdTextAlign;
};
Window_SkillType.prototype.drawItem = function (_0x221855) {
    const _0x1c220b = this.commandStyleCheck(_0x221855);
    if (_0x1c220b === 'iconText') {
        this.drawItemStyleIconText(_0x221855);
    } else if (_0x1c220b === 'icon') {
        this.drawItemStyleIcon(_0x221855);
    } else {
        Window_Command.prototype.drawItem.call(this, _0x221855);
    }
};
Window_SkillType.prototype.commandStyle = function () {
    return VisuMZ.SkillsStatesCore.Settings.Skills.CmdStyle;
};
Window_SkillType.prototype.commandStyleCheck = function (_0x290e9a) {
    if (_0x290e9a < 0x0) {
        return 'text';
    }
    const _0xffcd6f = this.commandStyle();
    if (_0xffcd6f !== 'auto') {
        return _0xffcd6f;
    } else {
        if (this.maxItems() > 0x0) {
            const _0x4842be = this.commandName(_0x290e9a);
            if (_0x4842be.match(/\\I\[(\d+)\]/i)) {
                const _0x54c3ef = this.itemLineRect(_0x290e9a);
                const _0x1d4e75 = this.textSizeEx(_0x4842be).width;
                return _0x1d4e75 <= _0x54c3ef.width ? 'iconText' : 'icon';
            }
        }
    }
    return 'text';
};
Window_SkillType.prototype.drawItemStyleIconText = function (_0x49bf1c) {
    const _0x3dcfad = this.itemLineRect(_0x49bf1c);
    const _0x35881c = this.commandName(_0x49bf1c);
    const _0x2ad287 = this.textSizeEx(_0x35881c).width;
    this.changePaintOpacity(this.isCommandEnabled(_0x49bf1c));
    const _0x13e515 = this.itemTextAlign();
    if (_0x13e515 === 'right') {
        this.drawTextEx(
            _0x35881c,
            _0x3dcfad.x + _0x3dcfad.width - _0x2ad287,
            _0x3dcfad.y,
            _0x2ad287
        );
    } else {
        if (_0x13e515 === 'center') {
            const _0x29bf66 = _0x3dcfad.x + Math.floor((_0x3dcfad.width - _0x2ad287) / 0x2);
            this.drawTextEx(_0x35881c, _0x29bf66, _0x3dcfad.y, _0x2ad287);
        } else {
            this.drawTextEx(_0x35881c, _0x3dcfad.x, _0x3dcfad.y, _0x2ad287);
        }
    }
};
Window_SkillType.prototype.drawItemStyleIcon = function (_0x15c6f4) {
    this.commandName(_0x15c6f4).match(/\\I\[(\d+)\]/i);
    const _0x254a1c = Number(RegExp.$1) || 0x0;
    const _0x4cd1c4 = this.itemLineRect(_0x15c6f4);
    const _0x30dcd0 = _0x4cd1c4.x + Math.floor((_0x4cd1c4.width - ImageManager.iconWidth) / 0x2);
    const _0x2d435e = _0x4cd1c4.y + (_0x4cd1c4.height - ImageManager.iconHeight) / 0x2;
    this.drawIcon(_0x254a1c, _0x30dcd0, _0x2d435e);
};
VisuMZ.SkillsStatesCore.Window_SkillStatus_refresh = Window_SkillStatus.prototype.refresh;
Window_SkillStatus.prototype.refresh = function () {
    VisuMZ.SkillsStatesCore.Window_SkillStatus_refresh.call(this);
    if (this._actor) {
        this.drawExtendedSkillsStatesCoreStatus();
    }
};
Window_SkillStatus.prototype.drawExtendedSkillsStatesCoreStatus = function () {
    if (!Imported.VisuMZ_0_CoreEngine) {
        return;
    }
    if (!Imported.VisuMZ_1_MainMenuCore) {
        return;
    }
    const _0x4c4ead = this.gaugeLineHeight();
    let _0x375256 = this.colSpacing() / 0x2 + 0xb4 + 0xb4 + 0xb4;
    let _0xd3b041 = this.innerWidth - _0x375256 - 0x2;
    if (_0xd3b041 >= 0x12c) {
        const _0x283962 = VisuMZ.CoreEngine.Settings.Param.DisplayedParams;
        const _0xf422ea = Math.floor(_0xd3b041 / 0x2) - 0x18;
        let _0x3dbbc5 = _0x375256;
        let _0x2deee6 = Math.floor(
            (this.innerHeight - Math.ceil(_0x283962.length / 0x2) * _0x4c4ead) / 0x2
        );
        let _0x391af7 = 0x0;
        for (const _0x6cd9ae of _0x283962) {
            this.drawExtendedParameter(_0x3dbbc5, _0x2deee6, _0xf422ea, _0x6cd9ae);
            _0x391af7++;
            if (_0x391af7 % 0x2 === 0x0) {
                _0x3dbbc5 = _0x375256;
                _0x2deee6 += _0x4c4ead;
            } else {
                _0x3dbbc5 += _0xf422ea + 0x18;
            }
        }
    }
    this.resetFontSettings();
};
Window_SkillStatus.prototype.drawExtendedParameter = function (
    _0x5cde67,
    _0x4fb5be,
    _0x20a99e,
    _0x1e61c3
) {
    const _0x1c5f73 = this.gaugeLineHeight();
    this.resetFontSettings();
    this.drawParamText(_0x5cde67, _0x4fb5be, _0x20a99e, _0x1e61c3, true);
    this.resetTextColor();
    this.contents.fontSize -= 0x8;
    const _0x59d472 = this._actor.paramValueByName(_0x1e61c3, true);
    this.contents.drawText(_0x59d472, _0x5cde67, _0x4fb5be, _0x20a99e, _0x1c5f73, 'right');
};
VisuMZ.SkillsStatesCore.Window_SkillList_includes = Window_SkillList.prototype.includes;
Window_SkillList.prototype.includes = function (_0x6e6137) {
    if (this._stypeId <= 0x0) {
        return false;
    }
    return this.includesSkillsStatesCore(_0x6e6137);
};
VisuMZ.SkillsStatesCore.Window_SkillList_maxCols = Window_SkillList.prototype.maxCols;
Window_SkillList.prototype.maxCols = function () {
    return SceneManager._scene.constructor === Scene_Battle
        ? VisuMZ.SkillsStatesCore.Window_SkillList_maxCols.call(this)
        : VisuMZ.SkillsStatesCore.Settings.Skills.ListWindowCols;
};
VisuMZ.SkillsStatesCore.Window_SkillList_setActor = Window_SkillList.prototype.setActor;
Window_SkillList.prototype.setActor = function (_0x32e96a) {
    const _0x482cae = this._actor !== _0x32e96a;
    VisuMZ.SkillsStatesCore.Window_SkillList_setActor.call(this, _0x32e96a);
    if (_0x482cae) {
        if (this._statusWindow && this._statusWindow.constructor === Window_ShopStatus) {
            this._statusWindow.setItem(this.itemAt(0x0));
        }
    }
};
Window_SkillList.prototype.setStypeId = function (_0x485a08) {
    if (this._stypeId === _0x485a08) {
        return;
    }
    if (!_0x485a08) {
        return;
    }
    this._stypeId = _0x485a08;
    this.refresh();
    this.scrollTo(0x0, 0x0);
    if (this._statusWindow && this._statusWindow.constructor === Window_ShopStatus) {
        this._statusWindow.setItem(this.itemAt(0x0));
    }
};
Window_SkillList.prototype.includesSkillsStatesCore = function (_0x119b5c) {
    if (!_0x119b5c) {
        return VisuMZ.SkillsStatesCore.Window_SkillList_includes.call(this, _0x119b5c);
    }
    if (!this.checkSkillTypeMatch(_0x119b5c)) {
        return false;
    }
    if (!this.checkShowHideNotetags(_0x119b5c)) {
        return false;
    }
    if (!this.checkShowHideJS(_0x119b5c)) {
        return false;
    }
    return true;
};
Window_SkillList.prototype.checkSkillTypeMatch = function (_0x8f32c5) {
    return DataManager.getSkillTypes(_0x8f32c5).includes(this._stypeId);
};
Window_SkillList.prototype.checkShowHideNotetags = function (_0x113d4f) {
    if (!VisuMZ.SkillsStatesCore.CheckVisibleBattleNotetags(this._actor, _0x113d4f)) {
        return false;
    }
    if (!VisuMZ.SkillsStatesCore.CheckVisibleSwitchNotetags(this._actor, _0x113d4f)) {
        return false;
    }
    if (!VisuMZ.SkillsStatesCore.CheckVisibleSkillNotetags(this._actor, _0x113d4f)) {
        return false;
    }
    return true;
};
VisuMZ.SkillsStatesCore.CheckVisibleBattleNotetags = function (_0x999ab7, _0x4817cc) {
    const _0x5175ad = _0x4817cc.note;
    if (_0x5175ad.match(/<HIDE IN BATTLE>/i) && $gameParty.inBattle()) {
        return false;
    } else {
        return !(_0x5175ad.match(/<HIDE OUTSIDE BATTLE>/i) && !$gameParty.inBattle());
    }
};
VisuMZ.SkillsStatesCore.CheckVisibleSwitchNotetags = function (_0x47c15c, _0x211095) {
    const _0x370325 = _0x211095.note;
    if (_0x370325.match(/<SHOW[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x175b7e = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x217d94 of _0x175b7e) {
            if (!$gameSwitches.value(_0x217d94)) {
                return false;
            }
        }
        return true;
    }
    if (_0x370325.match(/<SHOW ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x459921 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x2b1b20 of _0x459921) {
            if (!$gameSwitches.value(_0x2b1b20)) {
                return false;
            }
        }
        return true;
    }
    if (_0x370325.match(/<SHOW ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x558dc9 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x261303 of _0x558dc9) {
            if ($gameSwitches.value(_0x261303)) {
                return true;
            }
        }
        return false;
    }
    if (_0x370325.match(/<HIDE[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x521969 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x48e676 of _0x521969) {
            if (!$gameSwitches.value(_0x48e676)) {
                return true;
            }
        }
        return false;
    }
    if (_0x370325.match(/<HIDE ALL[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x575c64 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x5a274d of _0x575c64) {
            if (!$gameSwitches.value(_0x5a274d)) {
                return true;
            }
        }
        return false;
    }
    if (_0x370325.match(/<HIDE ANY[ ](?:SW|SWITCH|SWITCHES):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x34b3fc = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x5a388e of _0x34b3fc) {
            if ($gameSwitches.value(_0x5a388e)) {
                return false;
            }
        }
        return true;
    }
    return true;
};
VisuMZ.SkillsStatesCore.CheckVisibleSkillNotetags = function (_0x4df0f4, _0x4edc75) {
    const _0x367272 = _0x4edc75.note;
    if (_0x367272.match(/<SHOW IF LEARNED[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x3f9d47 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x490ab0 of _0x3f9d47) {
            if (!_0x4df0f4.isLearnedSkill(_0x490ab0)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<SHOW IF LEARNED[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x450e3a = RegExp.$1.split(',');
            for (const _0x14679c of _0x450e3a) {
                const _0x2f03e1 = DataManager.getSkillIdWithName(_0x14679c);
                if (!_0x2f03e1) {
                    continue;
                }
                if (!_0x4df0f4.isLearnedSkill(_0x2f03e1)) {
                    return false;
                }
            }
            return true;
        }
    }
    if (_0x367272.match(/<SHOW IF LEARNED ALL[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x32efcd = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1d4548 of _0x32efcd) {
            if (!_0x4df0f4.isLearnedSkill(_0x1d4548)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<SHOW IF LEARNED ALL[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x5cc63d = RegExp.$1.split(',');
            for (const _0x1af70e of _0x5cc63d) {
                const _0x5ca044 = DataManager.getSkillIdWithName(_0x1af70e);
                if (!_0x5ca044) {
                    continue;
                }
                if (!_0x4df0f4.isLearnedSkill(_0x5ca044)) {
                    return false;
                }
            }
            return true;
        }
    }
    if (_0x367272.match(/<SHOW IF LEARNED ANY[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x13f49f = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1e4a17 of _0x13f49f) {
            if (_0x4df0f4.isLearnedSkill(_0x1e4a17)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<SHOW IF LEARNED ANY[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x215d48 = RegExp.$1.split(',');
            for (const _0x6a3871 of _0x215d48) {
                const _0x31f93b = DataManager.getSkillIdWithName(_0x6a3871);
                if (!_0x31f93b) {
                    continue;
                }
                if (_0x4df0f4.isLearnedSkill(_0x31f93b)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (_0x367272.match(/<HIDE IF LEARNED[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x1d6248 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x3e559c of _0x1d6248) {
            if (!_0x4df0f4.isLearnedSkill(_0x3e559c)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<HIDE IF LEARNED[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x4c8cf9 = RegExp.$1.split(',');
            for (const _0xfce35f of _0x4c8cf9) {
                const _0x32279f = DataManager.getSkillIdWithName(_0xfce35f);
                if (!_0x32279f) {
                    continue;
                }
                if (!_0x4df0f4.isLearnedSkill(_0x32279f)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (_0x367272.match(/<HIDE IF LEARNED ALL[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x3b58c2 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0xe0d10a of _0x3b58c2) {
            if (!_0x4df0f4.isLearnedSkill(_0xe0d10a)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<HIDE IF LEARNED ALL[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x45d777 = RegExp.$1.split(',');
            for (const _0x1d9b53 of _0x45d777) {
                const _0xed35e4 = DataManager.getSkillIdWithName(_0x1d9b53);
                if (!_0xed35e4) {
                    continue;
                }
                if (!_0x4df0f4.isLearnedSkill(_0xed35e4)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (_0x367272.match(/<HIDE IF LEARNED ANY[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x273e42 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0xd9ef26 of _0x273e42) {
            if (_0x4df0f4.isLearnedSkill(_0xd9ef26)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<HIDE IF LEARNED ANY[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x27aa93 = RegExp.$1.split(',');
            for (const _0x50e877 of _0x27aa93) {
                const _0x33275c = DataManager.getSkillIdWithName(_0x50e877);
                if (!_0x33275c) {
                    continue;
                }
                if (_0x4df0f4.isLearnedSkill(_0x33275c)) {
                    return false;
                }
            }
            return true;
        }
    }
    if (_0x367272.match(/<SHOW IF (?:HAS|HAVE)[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x16ca0d = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x343571 of _0x16ca0d) {
            if (!_0x4df0f4.hasSkill(_0x343571)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<SHOW IF (?:HAS|HAVE)[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x4a6845 = RegExp.$1.split(',');
            for (const _0x13c0b2 of _0x4a6845) {
                const _0x53c292 = DataManager.getSkillIdWithName(_0x13c0b2);
                if (!_0x53c292) {
                    continue;
                }
                if (!_0x4df0f4.hasSkill(_0x53c292)) {
                    return false;
                }
            }
            return true;
        }
    }
    if (
        _0x367272.match(/<SHOW IF (?:HAS|HAVE) ALL[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)
    ) {
        const _0x287e45 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1361ca of _0x287e45) {
            if (!_0x4df0f4.hasSkill(_0x1361ca)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<SHOW IF (?:HAS|HAVE) ALL[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x40f52a = RegExp.$1.split(',');
            for (const _0x52826e of _0x40f52a) {
                const _0x1368cf = DataManager.getSkillIdWithName(_0x52826e);
                if (!_0x1368cf) {
                    continue;
                }
                if (!_0x4df0f4.hasSkill(_0x1368cf)) {
                    return false;
                }
            }
            return true;
        }
    }
    if (
        _0x367272.match(/<SHOW IF (?:HAS|HAVE) ANY[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)
    ) {
        const _0x4e4aff = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x46b213 of _0x4e4aff) {
            if (_0x4df0f4.hasSkill(_0x46b213)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<SHOW IF (?:HAS|HAVE) ANY[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x36bfcf = RegExp.$1.split(',');
            for (const _0x40d845 of _0x36bfcf) {
                const _0x26460d = DataManager.getSkillIdWithName(_0x40d845);
                if (!_0x26460d) {
                    continue;
                }
                if (_0x4df0f4.hasSkill(_0x26460d)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (_0x367272.match(/<HIDE IF (?:HAS|HAVE)[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)) {
        const _0x557f42 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1d4d64 of _0x557f42) {
            if (!_0x4df0f4.hasSkill(_0x1d4d64)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<HIDE IF (?:HAS|HAVE)[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x4b2a24 = RegExp.$1.split(',');
            for (const _0x113791 of _0x4b2a24) {
                const _0x24908a = DataManager.getSkillIdWithName(_0x113791);
                if (!_0x24908a) {
                    continue;
                }
                if (!_0x4df0f4.hasSkill(_0x24908a)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (
        _0x367272.match(/<HIDE IF (?:HAS|HAVE) ALL[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)
    ) {
        const _0xe5d79d = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1208d2 of _0xe5d79d) {
            if (!_0x4df0f4.hasSkill(_0x1208d2)) {
                return true;
            }
        }
        return false;
    } else {
        if (_0x367272.match(/<HIDE IF (?:HAS|HAVE) ALL[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x1053d1 = RegExp.$1.split(',');
            for (const _0x90bc9c of _0x1053d1) {
                const _0x5f1a64 = DataManager.getSkillIdWithName(_0x90bc9c);
                if (!_0x5f1a64) {
                    continue;
                }
                if (!_0x4df0f4.hasSkill(_0x5f1a64)) {
                    return true;
                }
            }
            return false;
        }
    }
    if (
        _0x367272.match(/<HIDE IF (?:HAS|HAVE) ANY[ ](?:SKILL|SKILLS):[ ]*(\d+(?:\s*,\s*\d+)*)>/i)
    ) {
        const _0xf9d9c3 = JSON.parse('[' + RegExp.$1.match(/\d+/g) + ']');
        for (const _0x1e7e56 of _0xf9d9c3) {
            if (_0x4df0f4.hasSkill(_0x1e7e56)) {
                return false;
            }
        }
        return true;
    } else {
        if (_0x367272.match(/<HIDE IF (?:HAS|HAVE) ANY[ ](?:SKILL|SKILLS):[ ](.*)>/i)) {
            const _0x470dfa = RegExp.$1.split(',');
            for (const _0xdfd264 of _0x470dfa) {
                const _0x4992bd = DataManager.getSkillIdWithName(_0xdfd264);
                if (!_0x4992bd) {
                    continue;
                }
                if (_0x4df0f4.hasSkill(_0x4992bd)) {
                    return false;
                }
            }
            return true;
        }
    }
    return true;
};
Window_SkillList.prototype.checkShowHideJS = function (_0x3e9e1e) {
    const _0x157aaf = VisuMZ.SkillsStatesCore.skillVisibleJS;
    return _0x157aaf[_0x3e9e1e.id] ? _0x157aaf[_0x3e9e1e.id].call(this, _0x3e9e1e) : true;
};
VisuMZ.SkillsStatesCore.Window_SkillList_makeItemList = Window_SkillList.prototype.makeItemList;
Window_SkillList.prototype.makeItemList = function () {
    VisuMZ.SkillsStatesCore.Window_SkillList_makeItemList.call(this);
    if (this.canSortSkillTypeList()) {
        this.sortSkillList();
    }
    if (this.canChangeSkillsThroughStateEffects()) {
        this.changeSkillsThroughStateEffects();
    }
};
Window_SkillList.prototype.canSortSkillTypeList = function () {
    return true;
};
Window_SkillList.prototype.sortSkillList = function () {
    const _0x133fc9 = VisuMZ.SkillsStatesCore.Settings.Skills.SortSkillTypesAbc || [];
    if (_0x133fc9 && _0x133fc9.includes(this._stypeId)) {
        this._data.sort((_0x4b1bbd, _0x2a1194) => {
            if (!!_0x4b1bbd && !!_0x2a1194) {
                return _0x4b1bbd.name.localeCompare(_0x2a1194.name);
            }
            return 0x0;
        });
    } else {
        VisuMZ.SkillsStatesCore.SortByIDandPriority(this._data);
    }
    return this._data;
};
VisuMZ.SkillsStatesCore.SortByIDandPriority = function (_0x2aceed) {
    _0x2aceed.sort((_0x10e19b, _0x3ab0af) => {
        if (!!_0x10e19b && !!_0x3ab0af) {
            if (_0x10e19b.sortPriority === undefined) {
                VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x10e19b);
            }
            if (_0x3ab0af.sortPriority === undefined) {
                VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x3ab0af);
            }
            const _0x34b353 = _0x10e19b.sortPriority;
            const _0x57cda0 = _0x3ab0af.sortPriority;
            if (_0x34b353 !== _0x57cda0) {
                return _0x57cda0 - _0x34b353;
            }
            return _0x10e19b.id - _0x3ab0af.id;
        }
        return 0x0;
    });
    return _0x2aceed;
};
VisuMZ.SkillsStatesCore.SortByIDandPriorityUsingIDs = function (_0x860adb) {
    _0x860adb.sort((_0x59156d, _0x318d58) => {
        const _0x3ee185 = $dataSkills[_0x59156d];
        const _0x3bb03c = $dataSkills[_0x318d58];
        if (!!_0x3ee185 && !!_0x3bb03c) {
            if (_0x3ee185.sortPriority === undefined) {
                VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x3ee185);
            }
            if (_0x3bb03c.sortPriority === undefined) {
                VisuMZ.SkillsStatesCore.Parse_Notetags_Skill_Sorting(_0x3bb03c);
            }
            const _0xb7eecb = _0x3ee185.sortPriority;
            const _0x2f5748 = _0x3bb03c.sortPriority;
            if (_0xb7eecb !== _0x2f5748) {
                return _0x2f5748 - _0xb7eecb;
            }
            return _0x59156d - _0x318d58;
        }
        return 0x0;
    });
    return _0x860adb;
};
Window_SkillList.prototype.canChangeSkillsThroughStateEffects = function () {
    if (!this._actor) {
        return false;
    }
    if (['skillLearn', 'equipBattleSkills', 'equipPassives'].includes(this._stypeId)) {
        return false;
    }
    return true;
};
Window_SkillList.prototype.changeSkillsThroughStateEffects = function () {
    const _0x4073ff = this._actor.states();
    for (const _0x100211 of _0x4073ff) {
        const _0x34f0e2 = DataManager.getSkillChangesFromState(_0x100211);
        for (const _0x4a3753 in _0x34f0e2) {
            const _0x336e70 = $dataSkills[Number(_0x4a3753)] || null;
            const _0xa21199 = $dataSkills[Number(_0x34f0e2[_0x4a3753])] || null;
            while (this._data.includes(_0x336e70)) {
                const _0x1bcbf5 = this._data.indexOf(_0x336e70);
                this._data[_0x1bcbf5] = _0xa21199;
            }
        }
    }
};
VisuMZ.SkillsStatesCore.Window_SkillList_drawItem = Window_SkillList.prototype.drawItem;
Window_SkillList.prototype.drawItem = function (_0x534b15) {
    const _0xc775e3 = this.itemAt(_0x534b15);
    const _0x5df9e5 = _0xc775e3 ? _0xc775e3.name : '';
    if (_0xc775e3) {
        this.alterSkillName(_0xc775e3);
    }
    VisuMZ.SkillsStatesCore.Window_SkillList_drawItem.call(this, _0x534b15);
    if (_0xc775e3) {
        _0xc775e3.name = _0x5df9e5;
    }
};
Window_SkillList.prototype.alterSkillName = function (_0x4f75b6) {
    if (_0x4f75b6 && _0x4f75b6.note.match(/<LIST NAME:[ ](.*)>/i)) {
        _0x4f75b6.name = String(RegExp.$1).trim();
        for (;;) {
            if (_0x4f75b6.name.match(/\\V\[(\d+)\]/gi)) {
                _0x4f75b6.name = _0x4f75b6.name.replace(/\\V\[(\d+)\]/gi, (_0x3b4f44, _0x274524) =>
                    $gameVariables.value(parseInt(_0x274524))
                );
            } else {
                break;
            }
        }
    }
};
Window_SkillList.prototype.drawSkillCost = function (_0x5513fd, _0x4a5f33, _0x1ca626, _0x327ee1) {
    Window_Base.prototype.drawSkillCost.call(
        this,
        this._actor,
        _0x5513fd,
        _0x4a5f33,
        _0x1ca626,
        _0x327ee1
    );
};
Window_SkillList.prototype.setStatusWindow = function (_0x3cb580) {
    this._statusWindow = _0x3cb580;
    this.callUpdateHelp();
};
VisuMZ.SkillsStatesCore.Window_SkillList_updateHelp = Window_SkillList.prototype.updateHelp;
Window_SkillList.prototype.updateHelp = function () {
    VisuMZ.SkillsStatesCore.Window_SkillList_updateHelp.call(this);
    if (this._statusWindow && this._statusWindow.constructor === Window_ShopStatus) {
        this._statusWindow.setItem(this.item());
    }
};
