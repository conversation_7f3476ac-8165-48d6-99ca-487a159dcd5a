/*:
@target MZ
<AUTHOR>
@plugindesc Splash Screen

@param fadeInFrames
@type number
@default 0

@param displayFrames
@type number
@default 60

@param fadeOutFrames
@type number
@default 0

@param image
@type file
@dir img/titles1
@default Splash
@require 1

@param sound
@desc sound effect to play
@type struct<se>
@default {"name":"","pan":"0","pitch":"100","volume":"90"}

@param soundTiming
@desc frames to wait before playing sound
@type number
@default 0

@param skipKeys
@type select[]
@default ["ok", "cancel", "menu"]
@option OK
@value ok
@option Cancel
@value cancel
@option Menu
@value menu
@option Shift
@value shift
*/

/*~struct~se:
@param name
@type file
@dir audio/se
@require 1

@param pan
@type number
@min -100
@max 100
@default 0

@param pitch
@type number
@min 50
@max 200
@default 100

@param volume
@type number
@min 0
@max 100
@default 90
*/
(() => {
    'use strict';
    const _plugin = 'CC_Splash';
    const _params = PluginManager.parameters(_plugin);
    const _splash_filename = _params.image;
    const _sound_effect = JSON.parse(_params.sound || '{}');
    const _splashWaitFrames = parseInt(_params.displayFrames) || 60;
    const _splashFadeInFrames = parseInt(_params.fadeInFrames) || 0;
    const _splashFadeOutFrames = parseInt(_params.fadeOutFrames) || 0;
    const _soundTiming = parseInt(_params._soundTiming) || 0;
    const _skipKeys = JSON.parse(_params.skipKeys || '[]');
    let _Scene_Boot_loadSystemImages = Scene_Boot.prototype.loadSystemImages;
    Scene_Boot.prototype.loadSystemImages = function () {
        this._splashSprite = new Sprite(ImageManager.loadTitle1(_splash_filename));
        this._splashCount = 0;
        if (_splashFadeInFrames > 0) {
            this._splashSprite.opacity = 0;
        }
        this.addChild(this._splashSprite);
        _Scene_Boot_loadSystemImages.call(this);
    };

    let _Scene_Boot_startNormalGame = Scene_Boot.prototype.startNormalGame;
    Scene_Boot.prototype.startNormalGame = function () {};

    let _Scene_Boot_update = Scene_Boot.prototype.update || Scene_Base.prototype.update;
    Scene_Boot.prototype.update = function () {
        if (this._splashCount || (this._splashSprite && this._splashSprite.bitmap.isReady())) {
            if (_soundTiming == this._splashCount && _sound_effect && _sound_effect.name) {
                AudioManager.playSe(_sound_effect);
            }
            this._splashCount++;
            for (const k of _skipKeys) {
                if (Input.isPressed(k)) {
                    _Scene_Boot_startNormalGame.call(this);
                    break;
                }
            }
            if (this._splashCount < _splashFadeInFrames) {
                this._splashSprite.opacity = (this._splashCount * 255) / _splashFadeInFrames;
            } else if (this._splashCount < _splashFadeInFrames + _splashWaitFrames) {
                this._splashSprite.opacity = 255;
            } else if (
                this._splashCount <
                _splashFadeInFrames + _splashWaitFrames + _splashFadeOutFrames
            ) {
                this._splashSprite.opacity =
                    255 -
                    ((this._splashCount - _splashFadeInFrames - _splashWaitFrames) * 255) /
                        _splashFadeOutFrames;
            } else {
                _Scene_Boot_startNormalGame.call(this);
            }
        }
        _Scene_Boot_update.call(this);
    };
})();
