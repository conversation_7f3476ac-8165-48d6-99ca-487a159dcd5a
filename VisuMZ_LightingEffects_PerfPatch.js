//=============================================================================
// VisuMZ_LightingEffects_PerfPatch.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc [Patch] Performance optimizations for VisuMZ_2_LightingEffects.js (light culling, update throttling, max lights)
 * <AUTHOR> Deobfuscation Patch
 * @orderAfter VisuMZ_2_LightingEffects
 * @help
 * This patch optimizes VisuMZ_2_LightingEffects.js by:
 * 1. Culling lights outside the visible screen area.
 * 2. Throttling light updates (configurable frequency).
 * 3. Limiting the maximum number of active lights per map (configurable).
 * 4. Caching expensive calculations and bitmap operations.
 * 5. Optimizing Graphics.frameCount usage.
 *
 * @param maxLights
 * @text Max Active Lights
 * @type number
 * @min 1
 * @desc Maximum number of active lights per map. Extra lights will be ignored. Default: 32
 * @default 32
 *
 * @param lightUpdateThrottle
 * @text Light Update Throttle (frames)
 * @type number
 * @min 1
 * @desc Update lights every N frames. Higher = better performance. Default: 2
 * @default 2
 *
 * @param enableVisibilityCulling
 * @text Enable Visibility Culling
 * @type boolean
 * @desc Skip updates for lights outside the visible screen area. Default: true
 * @default true
 *
 * @param enableBehaviorCache
 * @text Enable Behavior Cache
 * @type boolean
 * @desc Cache expensive light behavior calculations. Default: true
 * @default true
 *
 * @param debugMode
 * @text Debug Mode
 * @type boolean
 * @desc Show performance information in console. Default: false
 * @default false
 */

(() => {
    'use strict';

    // Check if VisuMZ_2_LightingEffects is loaded
    if (!Imported.VisuMZ_2_LightingEffects) {
        console.warn(
            '[VisuMZ_LightingEffects_PerfPatch] VisuMZ_2_LightingEffects not found. Patch disabled.'
        );
        return;
    }

    const parameters = PluginManager.parameters('VisuMZ_LightingEffects_PerfPatch');
    const settings = {
        maxLights: Number(parameters.maxLights || 32),
        lightUpdateThrottle: Number(parameters.lightUpdateThrottle || 2),
        enableVisibilityCulling: parameters.enableVisibilityCulling !== 'false',
        enableBehaviorCache: parameters.enableBehaviorCache !== 'false',
        debugMode: parameters.debugMode === 'true',
    };

    // Debug logging
    function debugLog(message) {
        if (settings.debugMode) {
            console.log(`[VisuMZ_LightingEffects_PerfPatch] ${message}`);
        }
    }

    // Performance tracking
    const perfStats = {
        lightsSkipped: 0,
        lightsUpdated: 0,
        behaviorCacheHits: 0,
        behaviorCacheMisses: 0,
    };

    // Caches
    const behaviorCache = new Map();
    const visibilityCache = new Map();

    // Frame counter replacement to reduce Graphics.frameCount calls
    let internalFrameCounter = 0;
    const _Graphics_render = Graphics.render;
    Graphics.render = function () {
        internalFrameCounter++;
        _Graphics_render.call(this);
    };

    // 🎯 OPTIMIZATION 1: Throttle light updates (SAFE VERSION)
    const _Sprite_LightBase_update = Sprite_LightBase.prototype.update;
    Sprite_LightBase.prototype.update = function () {
        // Safety check - only apply optimizations if everything is properly initialized
        if (!this._source || !this.lightData || typeof this.isEnabled !== 'function') {
            // Fall back to original update if not properly initialized
            return _Sprite_LightBase_update.call(this);
        }

        // Initialize update counter
        if (!this._updateCounter) this._updateCounter = 0;
        this._updateCounter++;

        // Always call parent update for basic sprite functionality
        Sprite.prototype.update.call(this);

        // Check properties every frame (lightweight) - with safety check
        if (typeof this.checkProperties === 'function') {
            this.checkProperties();
        }

        if (!this.isEnabled() || !this._source) {
            if (typeof this.updateVisibility === 'function') {
                this.updateVisibility();
            }
            return;
        }

        // Throttle expensive updates
        if (this._updateCounter % settings.lightUpdateThrottle !== 0) {
            perfStats.lightsSkipped++;
            return;
        }

        // 🎯 OPTIMIZATION 2: Visibility culling (with safety check)
        if (
            settings.enableVisibilityCulling &&
            typeof this.isLightVisible === 'function' &&
            !this.isLightVisible()
        ) {
            perfStats.lightsSkipped++;
            this.visible = false;
            return;
        }

        // Perform throttled updates (with safety checks)
        if (typeof this.updateMain === 'function') {
            this.updateMain();
        }
        if (typeof this.updateBehavior === 'function') {
            this.updateBehavior();
        }
        if (typeof this.updateVisibility === 'function') {
            this.updateVisibility();
        }

        perfStats.lightsUpdated++;
    };

    // 🎯 OPTIMIZATION 3: Cache expensive behavior calculations (SAFE VERSION)
    if (settings.enableBehaviorCache) {
        const _Sprite_LightBase_updateBehavior = Sprite_LightBase.prototype.updateBehavior;
        Sprite_LightBase.prototype.updateBehavior = function () {
            // Safety check - only apply caching if everything is properly initialized
            if (!this.lightData || typeof this.lightData !== 'function') {
                return _Sprite_LightBase_updateBehavior.call(this);
            }

            const lightData = this.lightData();
            if (!lightData) {
                return _Sprite_LightBase_updateBehavior.call(this);
            }

            // Create cache key from light properties (with safety checks)
            const cacheKey = `${lightData.blink || 0}_${lightData.flicker || 0}_${lightData.flash || 0}_${lightData.flare || 0}_${lightData.glow || 0}_${lightData.pulse || 0}_${internalFrameCounter}`;

            // Check cache first
            if (behaviorCache.has(cacheKey)) {
                const cached = behaviorCache.get(cacheKey);
                this.opacity = cached.opacity;
                this.scale.x = cached.scaleX;
                this.scale.y = cached.scaleY;
                perfStats.behaviorCacheHits++;
                return;
            }

            // Store original values
            const originalOpacity = this.opacity;
            const originalScaleX = this.scale.x;
            const originalScaleY = this.scale.y;

            // Call original method
            _Sprite_LightBase_updateBehavior.call(this);

            // Cache the result
            behaviorCache.set(cacheKey, {
                opacity: this.opacity,
                scaleX: this.scale.x,
                scaleY: this.scale.y,
            });

            perfStats.behaviorCacheMisses++;

            // Limit cache size to prevent memory leaks
            if (behaviorCache.size > 200) {
                const firstKey = behaviorCache.keys().next().value;
                behaviorCache.delete(firstKey);
            }
        };
    }

    // 🎯 OPTIMIZATION 4: Optimize Graphics.frameCount usage in behaviors
    const _Sprite_LightBase_updateGlow = Sprite_LightBase.prototype.updateGlow;
    Sprite_LightBase.prototype.updateGlow = function () {
        if (!ConfigManager.pulsingLights) return;

        const lightData = this.lightData();
        if (lightData.glow) {
            const glowRate = lightData.glowRate;
            const glowSpeed = lightData.glowSpeed;
            const glowRng = lightData.glowRng ? this._glowRng : 0;

            // Use internal counter instead of Graphics.frameCount
            const frameCount = internalFrameCounter + glowRng;
            this.opacity *= 1 - glowRate + Math.cos(frameCount * glowSpeed) * glowRate;
        }
    };

    const _Sprite_LightBase_updatePulse = Sprite_LightBase.prototype.updatePulse;
    Sprite_LightBase.prototype.updatePulse = function () {
        if (!ConfigManager.pulsingLights) return;

        const lightData = this.lightData();
        if (lightData.pulse) {
            const pulseRate = lightData.pulseRate;
            const pulseSpeed = lightData.pulseSpeed;
            const pulseRng = lightData.pulseRng ? this._pulseRng : 0;

            // Use internal counter instead of Graphics.frameCount
            const frameCount = internalFrameCounter + pulseRng;
            const scale = 1 - pulseRate + Math.cos(frameCount * pulseSpeed) * pulseRate;
            this.scale.x = this.scale.y = scale;
        } else {
            this.scale.x = this.scale.y = 1;
        }
    };

    // 🎯 OPTIMIZATION 5: Limit maximum active lights (SAFE VERSION)
    const _Sprite_LightingEffects_createLightSprites =
        Sprite_LightingEffects.prototype.createLightSprites;
    Sprite_LightingEffects.prototype.createLightSprites = function () {
        // Safety check - only apply optimization if everything is properly initialized
        if (!this.getLightSources || typeof this.getLightSources !== 'function') {
            return _Sprite_LightingEffects_createLightSprites.call(this);
        }

        // Get all light sources
        const allSources = this.getLightSources();

        // Safety check for allSources
        if (!Array.isArray(allSources)) {
            return _Sprite_LightingEffects_createLightSprites.call(this);
        }

        // Limit to maximum lights for performance
        if (allSources.length > settings.maxLights) {
            debugLog(
                `Limiting lights from ${allSources.length} to ${settings.maxLights} for performance`
            );

            // Prioritize player and important events
            const prioritized = allSources.sort((a, b) => {
                if (a === $gamePlayer) return -1;
                if (b === $gamePlayer) return 1;
                if (a._eventId && b._eventId) return a._eventId - b._eventId;
                return 0;
            });

            // Use only the first maxLights sources
            this._lightSources = prioritized.slice(0, settings.maxLights);
        } else {
            this._lightSources = allSources;
        }

        // Call original method with limited sources
        _Sprite_LightingEffects_createLightSprites.call(this);
    };

    // 🎯 OPTIMIZATION 6: Performance monitoring
    if (settings.debugMode) {
        setInterval(() => {
            debugLog(
                `Performance Stats: Lights Updated=${perfStats.lightsUpdated}, Skipped=${perfStats.lightsSkipped}, Behavior Cache Hits=${perfStats.behaviorCacheHits}, Misses=${perfStats.behaviorCacheMisses}`
            );

            // Reset counters
            perfStats.lightsSkipped = 0;
            perfStats.lightsUpdated = 0;
            perfStats.behaviorCacheHits = 0;
            perfStats.behaviorCacheMisses = 0;
        }, 5000); // Every 5 seconds
    }

    // 🎯 OPTIMIZATION 7: Clean up caches on scene change
    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function () {
        _Scene_Map_terminate.call(this);

        // Clear caches to prevent memory leaks
        behaviorCache.clear();
        visibilityCache.clear();
        debugLog('Cleared all lighting caches on scene change');
    };

    debugLog(
        `Lighting Effects Performance Patch loaded! Settings: MaxLights=${settings.maxLights}, Throttle=${settings.lightUpdateThrottle}, Culling=${settings.enableVisibilityCulling}, BehaviorCache=${settings.enableBehaviorCache}`
    );
})();
