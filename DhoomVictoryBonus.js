//=============================================================================
// DhoomVictoryBonus.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_VictoryBonus = '1.0a';

var Dhoom = Dhoom || {};
Dhoom.VictoryBonus = Dhoom.VictoryBonus || {};
/*:
 * @plugindesc Dhoom VictoryBonus v1.0a - 01/07/2024
 * <AUTHOR>
 * @url drd-workshop.blogspot.com
 *
 * @param Bonus Settings
 * @desc Bonus settings.
 * @type struct<bonusSetting>[]
 * @default ["{\"switch\":\"0\",\"name\":\"Damage Dealt \\\\C[24]%a%\",\"condition\":\"a >= 1000\",\"exp\":\"Math.floor(a / 1000) * 5\",\"gold\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"Damage Taken \\\\C[25]%b%\",\"condition\":\"b >= 2000\",\"exp\":\"Math.floor(b / 2000) * -5\",\"gold\":\"\",\"success\":\"\",\"failed\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"No Damage Taken\",\"condition\":\"b === 0\",\"exp\":\"20\",\"gold\":\"\",\"success\":\"\",\"failed\":\"\",\"stack\":\"false\"}","{\"switch\":\"0\",\"name\":\"Zayne in Action\",\"condition\":\"$gameParty.battleMembers().contains($gameActors.actor(4))\",\"exp\":\"5\",\"gold\":\"5\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"Healed \\\\C[24]%c%\",\"condition\":\"c >= 500\",\"exp\":\"\",\"gold\":\"5\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"Turns Taken \\\\C[24]%n%\",\"condition\":\"n > 1 && n < 10\",\"exp\":\"10\",\"gold\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"Turn Penalty \\\\C[25]%n%\",\"condition\":\"n >= 10\",\"exp\":\"Math.floor((n - 10) / 5) * 10\",\"gold\":\"\",\"success\":\"\",\"failed\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"No Heroes Down\",\"condition\":\"e === 0\",\"exp\":\"20\",\"gold\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"Death Penalty \\\\C[25]%e%\",\"condition\":\"e > 0\",\"exp\":\"e * -10\",\"gold\":\"\",\"success\":\"\",\"failed\":\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"1-turn Victor\",\"condition\":\"n === 1 && $gameVariables.value(250) < 5\",\"exp\":\"10\",\"gold\":\"\",\"success\":\"\\\"let id = 250;\\\\n$gameVariables.setValue(id, $gameVariables.value(id) + 1);\\\"\",\"failed\":\"\\\"let id = 250;\\\\nif ($gameVariables.value(id) < 5) {\\\\n  $gameVariables.setValue(id, 0);\\\\n}\\\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"1-turn Ace\",\"condition\":\"n === 1 && $gameVariables.value(250) >= 5 && $gameVariables.value(250) < 10\",\"exp\":\"20\",\"gold\":\"\",\"success\":\"\\\"let id = 250;\\\\n$gameVariables.setValue(id, $gameVariables.value(id) + 1);\\\"\",\"failed\":\"\\\"let id = 250;\\\\nif ($gameVariables.value(id) >= 5 && $gameVariables.value(id) < 10) {\\\\n  $gameVariables.setValue(id, 0);\\\\n}\\\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"1-turn Hero\",\"condition\":\"n === 1 && $gameVariables.value(250) >= 10\",\"exp\":\"40\",\"gold\":\"\",\"success\":\"\\\"let id = 250;\\\\n$gameVariables.setValue(id, $gameVariables.value(id) + 1);\\\"\",\"failed\":\"\\\"let id = 250;\\\\nif ($gameVariables.value(id) >= 10) {\\\\n  $gameVariables.setValue(id, 0);\\\\n}\\\"\",\"stack\":\"true\"}","{\"switch\":\"0\",\"name\":\"\\\\{\\\\C[1]S\\\\C[2]L\\\\C[3]A\\\\C[4]U\\\\C[5]G\\\\C[6]H\\\\C[7]T\\\\C[8]E\\\\C[9]R\",\"condition\":\"a >= 10000\",\"exp\":\"100\",\"gold\":\"15\",\"success\":\"\",\"failed\":\"\",\"stack\":\"true\",\"customSe\":\"true\",\"se\":\"{\\\"name\\\":\\\"Skill3\\\",\\\"volume\\\":\\\"60\\\",\\\"pitch\\\":\\\"100\\\",\\\"pan\\\":\\\"0\\\"}\"}"]
 *
 * @param Rewards Window Setting
 * @desc Rewards window setting.
 * @type struct<rewardWindowSetting>
 * @default {"x":"0","y":"0","width":"1280","height":"720","opacity":"0","background":"true","images":"[]","texts":"[]","experience":"{\"x\":\"640\",\"y\":\"104\",\"spacingX\":\"0\",\"spacingY\":\"84\",\"face\":\"{\\\"x\\\":\\\"0\\\",\\\"y\\\":\\\"0\\\",\\\"width\\\":\\\"80\\\",\\\"height\\\":\\\"80\\\"}\",\"name\":\"{\\\"x\\\":\\\"84\\\",\\\"y\\\":\\\"0\\\"}\",\"gauge\":\"{\\\"x\\\":\\\"84\\\",\\\"y\\\":\\\"36\\\"}\"}","items":"{\"x\":\"640\",\"y\":\"290\",\"width\":\"580\",\"height\":\"288\",\"title\":\"false\"}","bonus":"{\"x\":\"100\",\"y\":\"252\",\"spacingX\":\"0\",\"spacingY\":\"36\",\"title\":\"{\\\"text\\\":\\\"%1\\\",\\\"x\\\":\\\"0\\\",\\\"y\\\":\\\"0\\\",\\\"width\\\":\\\"200\\\",\\\"height\\\":\\\"36\\\",\\\"style\\\":\\\"{\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"size\\\\\\\":\\\\\\\"27\\\\\\\",\\\\\\\"color\\\\\\\":\\\\\\\"#fff\\\\\\\",\\\\\\\"outlineWidth\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"outlineColor\\\\\\\":\\\\\\\"rgba(0, 0, 0, 1)\\\\\\\",\\\\\\\"shadowBlur\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"shadowColor\\\\\\\":\\\\\\\"#000000\\\\\\\",\\\\\\\"bold\\\\\\\":\\\\\\\"false\\\\\\\",\\\\\\\"italic\\\\\\\":\\\\\\\"false\\\\\\\",\\\\\\\"spacing\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"align\\\\\\\":\\\\\\\"left\\\\\\\",\\\\\\\"case\\\\\\\":\\\\\\\"unchanged\\\\\\\"}\\\",\\\"wordwrap\\\":\\\"false\\\",\\\"spacing\\\":\\\"3\\\",\\\"centerVertical\\\":\\\"true\\\"}\",\"value\":\"{\\\"text\\\":\\\"%1 %2\\\",\\\"x\\\":\\\"200\\\",\\\"y\\\":\\\"0\\\",\\\"width\\\":\\\"300\\\",\\\"height\\\":\\\"36\\\",\\\"style\\\":\\\"{\\\\\\\"name\\\\\\\":\\\\\\\"\\\\\\\",\\\\\\\"size\\\\\\\":\\\\\\\"27\\\\\\\",\\\\\\\"color\\\\\\\":\\\\\\\"#fff\\\\\\\",\\\\\\\"outlineWidth\\\\\\\":\\\\\\\"4\\\\\\\",\\\\\\\"outlineColor\\\\\\\":\\\\\\\"rgba(0, 0, 0, 1)\\\\\\\",\\\\\\\"shadowBlur\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"shadowColor\\\\\\\":\\\\\\\"#000000\\\\\\\",\\\\\\\"bold\\\\\\\":\\\\\\\"false\\\\\\\",\\\\\\\"italic\\\\\\\":\\\\\\\"false\\\\\\\",\\\\\\\"spacing\\\\\\\":\\\\\\\"0\\\\\\\",\\\\\\\"align\\\\\\\":\\\\\\\"right\\\\\\\",\\\\\\\"case\\\\\\\":\\\\\\\"unchanged\\\\\\\"}\\\",\\\"wordwrap\\\":\\\"false\\\",\\\"spacing\\\":\\\"3\\\",\\\"centerVertical\\\":\\\"true\\\"}\",\"exp\":\"\\\\C[16]EXP\\\\C[0] \\\\CH%1%\",\"gold\":\"\\\\C[16]G\\\\C[0] \\\\CH%1%\",\"delay\":\"30\",\"se\":\"{\\\"name\\\":\\\"Skill3\\\",\\\"volume\\\":\\\"40\\\",\\\"pitch\\\":\\\"120\\\",\\\"pan\\\":\\\"0\\\"}\"}","bonusExp":"{\"text\":\"%1\",\"x\":\"526\",\"y\":\"144\",\"width\":\"120\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#81ff81\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"left\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"anchor\":\"4\",\"scaleDuration\":\"15\",\"easing\":\"EaseOutBounce\",\"scaleXStart\":\"1.00\",\"scaleYStart\":\"0.00\",\"scaleXEnd\":\"1\",\"scaleYEnd\":\"1\"}","bonusGold":"{\"text\":\"%1\",\"x\":\"526\",\"y\":\"180\",\"width\":\"120\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#81ff81\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"left\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"anchor\":\"4\",\"scaleDuration\":\"15\",\"easing\":\"EaseOutBounce\",\"scaleXStart\":\"1.00\",\"scaleYStart\":\"0.00\",\"scaleXEnd\":\"1\",\"scaleYEnd\":\"1\"}"}
 *
 * @help =============================================================================
 * • Usable Data Variables
 * =============================================================================
 * a = Damage dealt
 * b = Damage taken
 * c = Heal done
 * d = Total turn
 * e = Total death
 * f = Total kill
 * g = Player miss total
 * h = Player evade total
 * i = Player miss + evade total
 * j = Enemy miss total
 * k = Enemy evade total
 * l = Enemy miss + evade total
 * m = Number of items used.
 * n = Player total turn.
 * o = Enemy total turn.
 */

/*~struct~bonusSetting:
@param switch
@text Enable Switch
@desc Switch to enable this bonus. 0/None = Always enabled.
@type switch
@default 0

@param name
@text Name
@desc If there are more than one setting with the same name, it can be stacked. To display value variable, uses %CHAR%.
@default 

@param condition
@text Condition
@desc Refer to plugin help for usable data variables.
@default 

@param exp
@text EXP Bonus
@desc Refer to plugin help for usable data variables.
@default 

@param gold
@text Gold Bonus
@desc Refer to plugin help for usable data variables.
@default 

@param success
@text Success Script
@desc Script that will run when this bonus condition is succeeded.
@type note
@default 

@param failed
@text Failed Script
@desc Script that will run when this bonus condition is failed.
@type note
@default 

@param stack
@text Stackable?
@desc true = the value will stack with others that has the same name. false = the previous value will be ignored.
@type boolean
@default true

@param customSe
@text Use Custom SE?
@desc true = Draw SE on this parameter will be used, false = Default Draw SE will be used.
@type boolean
@default false

@param se
@text Draw SE
@desc Only if Use Custom SE is set to true.
@type struct<seSetting>
@default {"name":"","volume":"80","pitch":"100","pan":"0"}
*/

/*~struct~rewardWindowSetting:
@param x
@text X Position
@desc Window X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Window Y position.
@type number
@min -999999
@default 0

@param width
@text Window Width
@desc Window width.
@type number
@min 1
@default 1280

@param height
@text Window Height
@desc Window height.
@type number
@min 1
@default 760

@param opacity
@text Window Opacity
@desc Window opacity.
@type number
@min 0
@max 255
@default 0

@param background
@text Draw Background?
@desc Draw black background with the white outline?
@type boolean
@default true

@param images
@text Drawn Image Settings
@desc Custom image that will be drawn.
@type struct<imageSetting>[]
@default []

@param texts
@text Drawn Text Settings
@desc Custom text that will be drawn.
@type struct<textSettingCondition>[]
@default []

@param experience
@text Actor Experience Setting
@type struct<actorExpSetting>
@default 

@param items
@text Items Setting
@type struct<itemSetting>
@default

@param bonus
@text Bonus Text Setting
@type struct<bonusTextSetting>
@default 

@param bonusExp
@text Bonus EXP Text Setting
@type struct<bonusExpSetting>
@default 

@param bonusGold
@text Bonus Gold Text Setting
@type struct<bonusExpSetting>
@default 
*/

/*~struct~bonusExpSetting:
@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area.
@type number
@min 4
@default 1

@param height
@text Text Area Height
@desc Height of text area.
@type number
@min 4
@default 1

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param anchor
@text Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 5

@param scaleDuration
@text Scaling Duration
@desc Scaling duration, in frame.
@type number
@min 0
@default 15

@param easing
@text Easing Type
@desc Scaling easing type.
@type select
@option Linear
@option EaseInQuad
@option EaseOutQuad
@option EaseInOutQuad
@option EaseInCubic
@option EaseOutCubic
@option EaseInOutCubic
@option EaseInQuart
@option EaseOutQuart
@option EaseInOutQuart
@option EaseInQuint
@option EaseOutQuint
@option EaseInOutQuint
@option EaseInSine
@option EaseOutSine
@option EaseInOutSine
@option EaseInExpo
@option EaseOutExpo
@option EaseInOutExpo
@option EaseInCirc
@option EaseOutCirc
@option EaseInOutCirc
@option EaseInElastic
@option EaseOutElastic
@option EaseInOutElastic
@option EaseInBack
@option EaseOutBack
@option EaseInOutBack
@option EaseInBounce
@option EaseOutBounce
@option EaseInOutBounce
@default Linear

@param scaleXStart
@text Start Scale X
@desc Starting x scale.
@type number
@min -99999
@decimals 2
@default 0

@param scaleYStart
@text Start Scale Y
@desc Starting y scale.
@type number
@min -99999
@decimals 2
@default 0.5

@param scaleXEnd
@text End Scale X
@desc Ending x scale.
@type number
@min -99999
@decimals 2
@default 1

@param scaleYEnd
@text End Scale Y
@desc Ending y scale.
@type number
@min -99999
@decimals 2
@default 1
*/

/*~struct~bonusTextSetting:
@param x
@text Start X
@desc Starting X position.
@type number
@min -99999
@default 0

@param y
@text Start Y
@desc Starting Y position.
@type number
@min -99999
@default 0

@param spacingX
@text Spacing X
@desc X spacing value.
@type number
@min -99999
@default 0

@param spacingY
@text Spacing Y
@desc Y spacing value.
@type number
@min -99999
@default 0

@param title
@text Title Text
@desc %1 = Bonus Title.
@type struct<textSetting>
@default 

@param value
@text Value Text
@desc %1 = EXP bonus, %2 = Gold bonus.
@type struct<textSetting>
@default 

@param exp
@text EXP Bonus Text Format
@desc %1 = EXP Bonus Percentage, %2 = EXP Bonus Value. Use \CH to change text color according to bonus value.
@default EXP: %1%

@param gold
@text Gold Bonus Text Format
@desc %1 = Gold Bonus Percentage, %2 = Gold Bonus Value. Use \CH to change text color according to bonus value.
@default G: %1%

@param delay
@text Delay Duration
@desc Delay duration for each bonus text draw, in frame.
@type number
@min 0
@default 30

@param se
@text Draw SE
@desc SE that will be played when drawing a bonus text.
@type struct<seSetting>
@default 
*/

/*~struct~seSetting:
@param name
@text Name
@desc SE name.
@type file
@dir audio/se
@default 

@param volume
@text Volume
@desc SE volume.
@type number
@min 0
@default 80

@param pitch
@text Pitch
@desc SE pitch.
@type number
@min 0
@max 200
@default 100

@param pan
@text Pan
@desc SE pan.
@type number
@min -100
@max 100
@default 0
*/

/*~struct~itemSetting:
@param x
@text Start X
@desc Starting X position.
@type number
@min -99999
@default 0

@param y
@text Start Y
@desc Starting Y position.
@type number
@min -99999
@default 0

@param width
@text Item List Window Width
@type number
@min 1
@default 500

@param height
@text Item List Window Height
@type number
@min 1
@default 252

@param title
@text Draw Title?
@desc Draw item title and the line?
@type boolean
@default true
*/

/*~struct~actorExpSetting:
@param x
@text Start X
@desc Starting X position.
@type number
@min -99999
@default 0

@param y
@text Start Y
@desc Starting Y position.
@type number
@min -99999
@default 0

@param spacingX
@text Spacing X
@desc X spacing value.
@type number
@min -99999
@default 0

@param spacingY
@text Spacing Y
@desc Y spacing value.
@type number
@min -99999
@default 0

@param face
@text Face Setting
@desc Actor face graphic setting.
@type struct<faceSetting>
@default 

@param name
@text Name Setting
@desc Actor name setting.
@type struct<nameSetting>
@default 

@param gauge
@text EXP Gauge Setting
@desc Actor EXP gauge setting.
@type struct<gaugeSetting>
@default 
*/

/*~struct~faceSetting:
@param x
@text X Position
@desc X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Y position.
@type number
@min -999999
@default 0

@param width
@text Width
@desc Face width.
@type number
@min 1
@default 144

@param height
@text Height
@desc Face height.
@type number
@min 1
@default 144
*/

/*~struct~nameSetting:
@param x
@text X Position
@desc X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Y position.
@type number
@min -999999
@default 0
*/

/*~struct~gaugeSetting:
@param x
@text X Position
@desc X position.
@type number
@min -999999
@default 0

@param y
@text Y Position
@desc Y position.
@type number
@min -999999
@default 0
*/

/*~struct~imageSetting:
@param condition
@text Script Condition
@desc Empty = always enabled.

@param anchor
@text Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 5

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param filename
@text Filename
@desc Image filename.
@type file
@dir img/system/

@param opacity
@text Opacity
@desc Image opacity.
@type number
@min 0
@max 255
@default 255

@param scaleX
@text Scale X
@desc Horizontal scale, in percentage.
@type number
@min -999999
@decimals 1
@default 100.0

@param scaleY
@text Scale Y
@desc Vertical scale, in percentage.
@type number
@min -999999
@decimals 1
@default 100.0

@param layer
@text Drawn Layer
@desc Choose which layer the image will be drawn.
@type select
@option Above windows contents
@value 0
@option Above windows background
@value 1
@option Underneath windows background
@value 2
@default 2
*/

/*~struct~textSettingCondition:
@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area.
@type number
@min 4
@default 1

@param height
@text Text Area Height
@desc Height of text area.
@type number
@min 4
@default 1

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param wordwrap
@text Word Wrap
@type boolean
@default true

@param spacing
@text Vertical Spacing
@type number
@min -999999
@default 3

@param centerVertical
@text Center Vertically?
@type boolean
@default true
*/

/*~struct~textSetting:
@param condition
@text Script Condition
@desc Empty = always enabled.

@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area.
@type number
@min 4
@default 1

@param height
@text Text Area Height
@desc Height of text area.
@type number
@min 4
@default 1

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param wordwrap
@text Word Wrap
@type boolean
@default true

@param spacing
@text Vertical Spacing
@type number
@min -999999
@default 3

@param centerVertical
@text Center Vertically?
@type boolean
@default true
*/

/*~struct~FontStyle:
@param name
@text Font Name
@desc Font name, leave empty if you want to use the default font.
@default 

@param size
@text Font Size
@desc Font size
@default 32
@type number
@min 1

@param color
@text Font Color
@desc Font color
@default #FFFFFF

@param outlineWidth
@text Font Outline Width
@desc Font outline width
@default 1
@type number

@param outlineColor
@text Font Outline Color
@desc Font outline color
@default #000000

@param shadowBlur
@text Shadow Blur
@desc Shadow blur strength.
@type number
@min 0
@default 0

@param shadowColor
@text Shadow color
@default #000000

@param bold
@text Font Bold
@desc Font bold
@default false
@type boolean

@param italic
@text Font Italic
@desc Font italic
@default false
@type boolean

@param spacing
@text Font Spacing
@type number
@min 0
@default 0

@param align
@text Text Alignment
@desc Text alignment
@default center
@type select
@option left
@option center
@option right

@param case
@text Text Case
@type select
@option unchanged
@option lowercase
@option UPPERCASE
@option Titlecase
@default unchanged
*/

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom VictoryBonus/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.VictoryBonus.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.VictoryBonus[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.VictoryBonus.loadParameters();

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// String
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
String.prototype.toTitleCase = function () {
    return this.replace(/([^\W_]+[^\s-]*) */g, function (txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Bitmap
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Bitmap.prototype.changeTextStyle = function (style) {
    this.fontFace = style.name.length > 0 ? style.name : $gameSystem.mainFontFace();
    this.fontSize = style.size;
    this.textColor = style.color;
    this.outlineWidth = style.outlineWidth;
    this.outlineColor = style.outlineColor;
    this.fontBold = style.bold;
    this.fontItalic = style.italic;
    this.fontShadowBlur = style.shadowBlur;
    this.fontShadowColor = style.shadowColor;
    this.spacing = (style.spacing || 0) + 'px';
    this.fontCase = style.case ? style.case.toLowerCase() : '';
};

Dhoom.VictoryBonus.Bitmap_drawText = Bitmap.prototype.drawText;
Bitmap.prototype.drawText = function (text, x, y, maxWidth, lineHeight, align) {
    if (this.fontCase === 'lowercase') text = text.toLowerCase();
    if (this.fontCase === 'uppercase') text = text.toUpperCase();
    if (this.fontCase === 'titlecase') text = text.toTitleCase();
    if (this.fontItalic) maxWidth -= 4;
    if (this._context) this._context.letterSpacing = this.spacing;
    Dhoom.VictoryBonus.Bitmap_drawText.call(this, text, x, y, maxWidth, lineHeight, align);
};

Dhoom.VictoryBonus.Bitmap_drawTextOutline = Bitmap.prototype._drawTextOutline;
Bitmap.prototype._drawTextOutline = function (text, tx, ty, maxWidth) {
    if (this.outlineWidth === 0) return;
    if (this.fontShadowBlur) {
        var context = this._context;
        context.shadowBlur = this.fontShadowBlur;
        context.shadowColor = this.fontShadowColor;
    }
    Dhoom.VictoryBonus.Bitmap_drawTextOutline.call(this, text, tx, ty, maxWidth);
};

Dhoom.VictoryBonus.Bitmap_drawTextBody = Bitmap.prototype._drawTextBody;
Bitmap.prototype._drawTextBody = function (text, tx, ty, maxWidth) {
    if (this.outlineWidth === 0 && this.fontShadowBlur) {
        var context = this._context;
        context.shadowBlur = this.fontShadowBlur;
        context.shadowColor = this.fontShadowColor;
    }
    Dhoom.VictoryBonus.Bitmap_drawTextBody.call(this, text, tx, ty, maxWidth);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Sprite
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Sprite.prototype.setAnchor = function (anchor) {
    this.anchor.x = 0;
    this.anchor.y = 0;
    if ([2, 5, 8].contains(anchor)) this.anchor.x = 0.5;
    if ([3, 6, 9].contains(anchor)) this.anchor.x = 1;
    if ([4, 5, 6].contains(anchor)) this.anchor.y = 0.5;
    if ([1, 2, 3].contains(anchor)) this.anchor.y = 1;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_VictoryBonusCounter
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
function Game_VictoryBonusCounter() {
    this.initialize.apply(this, arguments);
}

Game_VictoryBonusCounter.prototype.initialize = function () {
    this.damageDealt = 0;
    this.damageTaken = 0;
    this.healDone = 0;
    this.turnTotal = 0;
    this.playerMissTotal = 0;
    this.criticalTotal = 0;
    this.itemUsed = 0;
    this.deathTotal = 0;
    this.playerEvadeTotal = 0;
    this.playerMissEvadeTotal = 0;
    this.killTotal = 0;
    this.enemyMissTotal = 0;
    this.enemyEvadeTotal = 0;
    this.enemyMissEvadeTotal = 0;
    this.playerTurnTotal = 0;
    this.enemyTurnTotal = 0;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Action
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Game_Action_apply = Game_Action.prototype.apply;
Game_Action.prototype.apply = function (target) {
    Dhoom.VictoryBonus.Game_Action_apply.call(this, target);
    const result = target.result();
    if (target.isActor()) {
        if (result.missed) BattleManager._victoryData.missTotal++;
        if (result.evaded) BattleManager._victoryData.evadeTotal++;
    }
};

Dhoom.VictoryBonus.Game_Action_executeHpDamage = Game_Action.prototype.executeHpDamage;
Game_Action.prototype.executeHpDamage = function (target, value) {
    Dhoom.VictoryBonus.Game_Action_executeHpDamage.call(this, target, value);
    if ($gameParty.inBattle()) {
        if (this.subject().isActor()) {
            if (target.isActor() && value < 0)
                BattleManager._victoryData.healDone += Math.abs(value);
            if (value > 0) BattleManager._victoryData.damageDealt += value;
        }
        if (target.isActor() && value > 0) BattleManager._victoryData.damageTaken += value;
        if (target.result().critical && this.subject().isActor())
            BattleManager._victoryData.criticalTotal++;
        if (target.isActor() && target.isDead()) BattleManager._victoryData.deathTotal++;
        if (this.subject().isActor() && target.isEnemy() && target.isDead())
            BattleManager._victoryData.killTotal++;
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_BattlerBase
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Game_BattlerBase_removeImmortal = Game_BattlerBase.prototype.removeImmortal;
Game_BattlerBase.prototype.removeImmortal = function () {
    const alive = this.isAlive();
    Dhoom.VictoryBonus.Game_BattlerBase_removeImmortal.call(this);
    if ($gameParty.inBattle()) {
        if (alive && this.isDead()) {
            if (this.isActor()) BattleManager._victoryData.deathTotal++;
            if (BattleManager._action.subject().isActor() && this.isEnemy())
                BattleManager._victoryData.killTotal++;
        }
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Battler
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Game_Battler_consumeItem = Game_Battler.prototype.consumeItem;
Game_Battler.prototype.consumeItem = function (item) {
    Dhoom.VictoryBonus.Game_Battler_consumeItem.call(this, item);
    if ($gameParty.inBattle()) BattleManager._victoryData.itemUsed++;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// BattleManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.BattleManager_initMembers = BattleManager.initMembers;
BattleManager.initMembers = function () {
    Dhoom.VictoryBonus.BattleManager_initMembers.call(this);
    BattleManager._victoryData = new Game_VictoryBonusCounter();
};

Dhoom.VictoryBonus.BattleManager_makeRewards = BattleManager.makeRewards;
BattleManager.makeRewards = function () {
    Dhoom.VictoryBonus.BattleManager_makeRewards.call(this);
    this._rewards.originalExp = this._rewards.exp;
    this._rewards.originalGold = this._rewards.gold;
    this._rewards.bonusExp = 0;
    this._rewards.bonusGold = 0;
    this.makeVictoryBonusRewards();
};

BattleManager.makeVictoryBonusRewards = function () {
    let settings = Dhoom.VictoryBonus.bonusSettings;
    this._victoryBonuses = {};
    for (let i = 0; i < settings.length; i++) {
        if (this.isVictoryBonusEnabled(settings[i].switch)) {
            let valid = this.isVictoryBonusValid(settings[i].condition);
            if (valid) {
                let name = settings[i].name;
                if (!this._victoryBonuses[name]) {
                    this._victoryBonuses[name] = {
                        name: name,
                        id: i,
                        exp: 0,
                        gold: 0,
                        expValue: 0,
                        goldValue: 0,
                    };
                }
                let exp = this.getVictoryBonusValue(settings[i].exp) || 0;
                let gold = this.getVictoryBonusValue(settings[i].gold) || 0;
                if (!settings[i].stack) {
                    this._victoryBonuses[name].exp = 0;
                    this._victoryBonuses[name].gold = 0;
                }
                this._victoryBonuses[name].exp += exp;
                this._victoryBonuses[name].gold += gold;
                this.runVictoryBonusEval(settings[i].success);
            } else {
                this.runVictoryBonusEval(settings[i].failed);
            }
        }
    }
    const names = Object.keys(this._victoryBonuses);
    for (let i = 0; i < names.length; i++) {
        let d = this._victoryBonuses[names[i]];
        d.expValue = d.exp ? Math.floor(d.exp * this._rewards.originalExp) : 0;
        d.goldValue = d.gold ? Math.floor(d.gold * this._rewards.originalGold) : 0;
        this._rewards.bonusExp += d.expValue ?? 0;
        this._rewards.bonusGold += d.goldValue ?? 0;
    }
};

BattleManager.isVictoryBonusEnabled = function (switchId) {
    return !switchId || $gameSwitches.value(switchId);
};

BattleManager.isVictoryBonusValid = function (condition) {
    let data = this._victoryData;
    let a = data.damageDealt;
    let b = data.damageTaken;
    let c = data.healDone;
    let d = data.turnTotal;
    let e = data.deathTotal;
    let f = data.killTotal;
    let g = data.playerMissTotal;
    let h = data.playerEvadeTotal;
    let i = data.playerMissEvadeTotal;
    let j = data.enemyMissTotal;
    let k = data.enemyEvadeTotal;
    let l = data.enemyMissEvadeTotal;
    let m = data.itemUsed;
    let n = data.playerTurnTotal;
    let o = data.enemyTurnTotal;
    try {
        return eval(condition);
    } catch (e) {
        console.warn('Bonus Victory Condition Error!');
        console.log(e);
        console.log(condition);
    }
    return false;
};

BattleManager.getVictoryBonusValue = function (formula) {
    let data = this._victoryData;
    let a = data.damageDealt;
    let b = data.damageTaken;
    let c = data.healDone;
    let d = data.turnTotal;
    let e = data.deathTotal;
    let f = data.killTotal;
    let g = data.playerMissTotal;
    let h = data.playerEvadeTotal;
    let i = data.playerMissEvadeTotal;
    let j = data.enemyMissTotal;
    let k = data.enemyEvadeTotal;
    let l = data.enemyMissEvadeTotal;
    let m = data.itemUsed;
    let n = data.playerTurnTotal;
    let o = data.enemyTurnTotal;
    if (formula) {
        try {
            return eval(formula) / 100;
        } catch (e) {
            console.warn('Bonus Victory Formula Error!');
            console.log(e);
            console.log(formula);
        }
    }
    return 0;
};

BattleManager.runVictoryBonusEval = function (script) {
    let data = this._victoryData;
    let a = data.damageDealt;
    let b = data.damageTaken;
    let c = data.healDone;
    let d = data.turnTotal;
    let e = data.deathTotal;
    let f = data.killTotal;
    let g = data.playerMissTotal;
    let h = data.playerEvadeTotal;
    let i = data.playerMissEvadeTotal;
    let j = data.enemyMissTotal;
    let k = data.enemyEvadeTotal;
    let l = data.enemyMissEvadeTotal;
    let m = data.itemUsed;
    let n = data.playerTurnTotal;
    let o = data.enemyTurnTotal;
    try {
        eval(script);
    } catch (e) {
        console.warn('Bonus Victory Script Error!');
        console.log(e);
        console.log(script);
    }
};

Dhoom.VictoryBonus.BattleManager_startAction = BattleManager.startAction;
BattleManager.startAction = function () {
    Dhoom.VictoryBonus.BattleManager_startAction.call(this);
    this._victoryData.turnTotal++;
    if (this._subject.isActor()) {
        this._victoryData.playerTurnTotal++;
    } else {
        this._victoryData.enemyTurnTotal++;
    }
};

Dhoom.VictoryBonus.BattleManager_processVictoryAftermathTransition =
    BattleManager.processVictoryAftermathTransition;
BattleManager.processVictoryAftermathTransition = function () {
    this._rewards.exp = this._rewards.originalExp + this._rewards.bonusExp;
    Dhoom.VictoryBonus.BattleManager_processVictoryAftermathTransition.call(this);
    this._rewards.exp = this._rewards.originalExp;
};

Dhoom.VictoryBonus.BattleManager_gainExp = BattleManager.gainExp;
BattleManager.gainExp = function () {
    this._rewards.exp = this._rewards.originalExp + this._rewards.bonusExp;
    Dhoom.VictoryBonus.BattleManager_gainExp.call(this);
    this._rewards.exp = this._rewards.originalExp;
};

Dhoom.VictoryBonus.BattleManager_gainGold = BattleManager.gainGold;
BattleManager.gainGold = function () {
    Dhoom.VictoryBonus.BattleManager_gainGold.call(this);
    $gameParty.gainGold(this._rewards.bonusGold);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Sprite_VictoryGauge
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Sprite_VictoryGauge.prototype.refresh = function () {
    this.bitmap.clear();
    this.resetFontSettings();
    this.drawExpGauge();
    this.drawExpValues();
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_VictoryBonusText
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_VictoryBonusText extends Window_Base {
    constructor() {
        super(...arguments);
    }

    initialize(setting) {
        this._setting = setting;
        this._value = 0;
        super.initialize(this.windowRect());
        this.opacity = 0;
    }

    setting() {
        return this._setting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y + this.lineHeight() * 2,
            this.setting().width,
            this.setting().height
        );
    }

    updatePadding() {
        this.padding = 0;
    }

    setValue(value) {
        if (this._value !== value) {
            this._value = value;
            this.refresh();
            this.startScaling();
        }
    }

    resetFontSettings() {
        if (this._isDrawingCustomText) return;
        super.resetFontSettings();
    }

    sliceText(text, width) {
        width = width || this.contents.width;
        var result = [];
        var texts = text.split(' ');
        var t = '';
        var s = null;
        while (texts.length) {
            var c = texts.shift();
            if (c.contains('\n')) {
                s = c.split('\n');
                c = s[0];
            }
            if (this.textSizeEx((t + ' ' + c).trim()).width <= width) {
                t = (t + ' ' + c).trim();
            } else if (t) {
                result.push(t);
                t = c;
            }
            if (s) {
                result.push(t);
                t = s[1];
                s = null;
            }
        }
        if (t) result.push(t);
        if (result.length === 0) result.push(text);
        return result;
    }

    refresh() {
        this.contents.clear();
        this.contents.changeTextStyle(this.setting().style);
        this._isDrawingCustomText = true;
        var text = this.setting().text;
        text = text.format(this._value > 0 ? '+' + this._value : this._value);
        text = this.convertEscapeCharacters(text);
        this.contents.changeTextStyle(this.setting().style);
        let width = this.width;
        var cw = this.textSizeEx(text).width + this.contents.outlineWidth * 2;
        var sx = this.contents.outlineWidth;
        if (this.setting().style.align === 'center') {
            sx = (width - cw) / 2;
        }
        if (this.setting().style.align === 'right') {
            sx = width - cw;
        }
        this.contents.changeTextStyle(this.setting().style);
        this.drawTextEx(text, sx, 0);
        this._isDrawingCustomText = false;
    }

    startScaling() {
        this.scale.x = this.setting().scaleXStart;
        this.scale.y = this.setting().scaleYStart;
        this._scaleDuration = this.setting().scaleDuration;
    }

    update() {
        super.update();
        this.updateScaling();
        this.updatePosition();
    }

    updateScaling() {
        if (this._scaleDuration) {
            this._scaleDuration--;
            var e = this.setting().scaleDuration - this._scaleDuration;
            var p =
                (this.setting().scaleDuration - this._scaleDuration) / this.setting().scaleDuration;
            const easing = this.setting().easing.toLowerCase();
            this.scale.x = this.processEasing(
                easing,
                this.setting().scaleXStart,
                this.setting().scaleXEnd,
                p,
                e,
                this.setting().scaleDuration
            );
            this.scale.y = this.processEasing(
                easing,
                this.setting().scaleYStart,
                this.setting().scaleYEnd,
                p,
                e,
                this.setting().scaleDuration
            );
            if (this._scaleDuration <= 0) this._scaleDuration = 0;
        }
    }

    processEasing(type, start, end, percent, elapsed, totalDuration) {
        switch (type) {
            case 'linear':
                return start + (end - start) * percent;
            case 'easeinquad':
                return (end - start) * (elapsed /= totalDuration) * elapsed + start;
            case 'easeoutquad':
                return -(end - start) * (elapsed /= totalDuration) * (elapsed - 2) + start;
            case 'easeinoutquad':
                if ((elapsed /= totalDuration / 2) < 1)
                    return ((end - start) / 2) * elapsed * elapsed + start;
                return (-(end - start) / 2) * (--elapsed * (elapsed - 2) - 1) + start;
            case 'easeincubic':
                return (end - start) * (elapsed /= totalDuration) * elapsed * elapsed + start;
            case 'easeoutcubic':
                return (
                    (end - start) *
                        ((elapsed = elapsed / totalDuration - 1) * elapsed * elapsed + 1) +
                    start
                );
            case 'easeinoutcubic':
                if ((elapsed /= totalDuration / 2) < 1)
                    return ((end - start) / 2) * elapsed * elapsed * elapsed + start;
                return ((end - start) / 2) * ((elapsed -= 2) * elapsed * elapsed + 2) + start;
            case 'easeinquart':
                return (
                    (end - start) * (elapsed /= totalDuration) * elapsed * elapsed * elapsed + start
                );
            case 'easeoutquart':
                return (
                    -(end - start) *
                        ((elapsed = elapsed / totalDuration - 1) * elapsed * elapsed * elapsed -
                            1) +
                    start
                );
            case 'easeinoutquart':
                if ((elapsed /= totalDuration / 2) < 1)
                    return ((end - start) / 2) * elapsed * elapsed * elapsed * elapsed + start;
                return (
                    (-(end - start) / 2) * ((elapsed -= 2) * elapsed * elapsed * elapsed - 2) +
                    start
                );
            case 'easeinquint':
                return (
                    (end - start) *
                        (elapsed /= totalDuration) *
                        elapsed *
                        elapsed *
                        elapsed *
                        elapsed +
                    start
                );
            case 'easeoutquint':
                return (
                    (end - start) *
                        ((elapsed = elapsed / totalDuration - 1) *
                            elapsed *
                            elapsed *
                            elapsed *
                            elapsed +
                            1) +
                    start
                );
            case 'easeinoutquint':
                if ((elapsed /= totalDuration / 2) < 1)
                    return (
                        ((end - start) / 2) * elapsed * elapsed * elapsed * elapsed * elapsed +
                        start
                    );
                return (
                    ((end - start) / 2) *
                        ((elapsed -= 2) * elapsed * elapsed * elapsed * elapsed + 2) +
                    start
                );
            case 'easeinsine':
                return (
                    -(end - start) * Math.cos((elapsed / totalDuration) * (Math.PI / 2)) +
                    (end - start) +
                    start
                );
            case 'easeoutsine':
                return (end - start) * Math.sin((elapsed / totalDuration) * (Math.PI / 2)) + start;
            case 'easeinoutsine':
                return (
                    (-(end - start) / 2) * (Math.cos((Math.PI * elapsed) / totalDuration) - 1) +
                    start
                );
            case 'easeinexpo':
                return elapsed == 0
                    ? start
                    : (end - start) * Math.pow(2, 10 * (elapsed / totalDuration - 1)) + start;
            case 'easeoutexpo':
                return elapsed == totalDuration
                    ? start + (end - start)
                    : (end - start) * (-Math.pow(2, (-10 * elapsed) / totalDuration) + 1) + start;
            case 'easeinoutexpo':
                if (elapsed == 0) return start;
                if (elapsed == totalDuration) return start + (end - start);
                if ((elapsed /= totalDuration / 2) < 1)
                    return ((end - start) / 2) * Math.pow(2, 10 * (elapsed - 1)) + start;
                return ((end - start) / 2) * (-Math.pow(2, -10 * --elapsed) + 2) + start;
            case 'easeincirc':
                return (
                    -(end - start) * (Math.sqrt(1 - (elapsed /= totalDuration) * elapsed) - 1) +
                    start
                );
            case 'easeoutcirc':
                return (
                    (end - start) *
                        Math.sqrt(1 - (elapsed = elapsed / totalDuration - 1) * elapsed) +
                    start
                );
            case 'easeinoutcirc':
                if ((elapsed /= totalDuration / 2) < 1)
                    return (-(end - start) / 2) * (Math.sqrt(1 - elapsed * elapsed) - 1) + start;
                return ((end - start) / 2) * (Math.sqrt(1 - (elapsed -= 2) * elapsed) + 1) + start;
            case 'easeinelastic':
                var s = 1.70158;
                var p = 0;
                var a = end - start;
                if (elapsed === 0) return start;
                if ((elapsed /= totalDuration) === 1) return end;
                if (!p) p = totalDuration * 0.3;
                if (a < Math.abs(end - start)) {
                    a = end - start;
                    var s = p / 4;
                } else {
                    var s = (p / (2 * Math.PI)) * Math.asin((end - start) / a);
                }
                return (
                    -(
                        a *
                        Math.pow(2, 10 * (elapsed -= 1)) *
                        Math.sin(((elapsed * totalDuration - s) * (2 * Math.PI)) / p)
                    ) + start
                );
            case 'easeoutelastic':
                var s = 1.70158;
                var p = 0;
                var a = end - start;
                if (elapsed == 0) return start;
                if ((elapsed /= totalDuration) == 1) return start + (end - start);
                if (!p) p = totalDuration * 0.3;
                if (a < Math.abs(end - start)) {
                    a = end - start;
                    var s = p / 4;
                } else var s = (p / (2 * Math.PI)) * Math.asin((end - start) / a);
                return (
                    a *
                        Math.pow(2, -10 * elapsed) *
                        Math.sin(((elapsed * totalDuration - s) * (2 * Math.PI)) / p) +
                    (end - start) +
                    start
                );
            case 'easeinoutelastic':
                var s = 1.70158;
                var p = 0;
                var a = end - start;
                if (elapsed == 0) return start;
                if ((elapsed /= totalDuration / 2) == 2) return start + (end - start);
                if (!p) p = totalDuration * (0.3 * 1.5);
                if (a < Math.abs(end - start)) {
                    a = end - start;
                    var s = p / 4;
                } else var s = (p / (2 * Math.PI)) * Math.asin((end - start) / a);
                if (elapsed < 1)
                    return (
                        -0.5 *
                            (a *
                                Math.pow(2, 10 * (elapsed -= 1)) *
                                Math.sin(((elapsed * totalDuration - s) * (2 * Math.PI)) / p)) +
                        start
                    );
                return (
                    a *
                        Math.pow(2, -10 * (elapsed -= 1)) *
                        Math.sin(((elapsed * totalDuration - s) * (2 * Math.PI)) / p) *
                        0.5 +
                    (end - start) +
                    start
                );
            case 'easeinback':
                var s = 1.70158;
                return (
                    (end - start) * (elapsed /= totalDuration) * elapsed * ((s + 1) * elapsed - s) +
                    start
                );
            case 'easeoutback':
                var s = 1.70158;
                return (
                    (end - start) *
                        ((elapsed = elapsed / totalDuration - 1) *
                            elapsed *
                            ((s + 1) * elapsed + s) +
                            1) +
                    start
                );
            case 'easeinoutback':
                var s = 1.70158;
                if ((elapsed /= totalDuration / 2) < 1)
                    return (
                        ((end - start) / 2) *
                            (elapsed * elapsed * (((s *= 1.525) + 1) * elapsed - s)) +
                        start
                    );
                return (
                    ((end - start) / 2) *
                        ((elapsed -= 2) * elapsed * (((s *= 1.525) + 1) * elapsed + s) + 2) +
                    start
                );
            case 'easeinbounce':
                return (
                    end -
                    start -
                    this.processEasing(
                        'easeoutbounce',
                        0,
                        end - start,
                        percent,
                        totalDuration - elapsed,
                        totalDuration
                    ) +
                    start
                );
            case 'easeoutbounce':
                if ((elapsed /= totalDuration) < 1 / 2.75) {
                    return (end - start) * (7.5625 * elapsed * elapsed) + start;
                } else if (elapsed < 2 / 2.75) {
                    return (
                        (end - start) * (7.5625 * (elapsed -= 1.5 / 2.75) * elapsed + 0.75) + start
                    );
                } else if (elapsed < 2.5 / 2.75) {
                    return (
                        (end - start) * (7.5625 * (elapsed -= 2.25 / 2.75) * elapsed + 0.9375) +
                        start
                    );
                } else {
                    return (
                        (end - start) * (7.5625 * (elapsed -= 2.625 / 2.75) * elapsed + 0.984375) +
                        start
                    );
                }
            case 'easeinoutbounce':
                if (elapsed < totalDuration / 2)
                    return (
                        this.processEasing(
                            'easeinbounce',
                            0,
                            end - start,
                            percent,
                            elapsed * 2,
                            totalDuration
                        ) *
                            0.5 +
                        start
                    );
                return (
                    this.processEasing(
                        'easeoutbounce',
                        0,
                        end - start,
                        percent,
                        elapsed * 2 - totalDuration,
                        totalDuration
                    ) *
                        0.5 +
                    (end - start) * 0.5 +
                    start
                );
            default:
                return end;
        }
    }

    updatePosition() {
        this.x = this.setting().x;
        this.y = this.setting().y;
        const anchor = this.setting().anchor;
        if ([2, 5, 8].contains(anchor)) this.x -= Math.round((this.width * this.scale.x) / 2);
        if ([3, 6, 9].contains(anchor)) this.x -= Math.round(this.width * this.scale.x);
        if ([4, 5, 6].contains(anchor)) this.y -= Math.round((this.height * this.scale.y) / 2);
        if ([1, 2, 3].contains(anchor)) this.y -= Math.round(this.height * this.scale.y);
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_VictoryItem
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Window_VictoryItem_initialize = Window_VictoryItem.prototype.initialize;
Window_VictoryItem.prototype.initialize = function (rect, mainWindow) {
    Dhoom.VictoryBonus.Window_VictoryItem_initialize.call(this, this.windowRect(), mainWindow);
};

Window_VictoryItem.prototype.windowRect = function () {
    return new Rectangle(
        this.setting().x,
        this.setting().y + this.lineHeight() * 2,
        this.setting().width,
        this.setting().height
    );
};

Window_VictoryItem.prototype.setting = function () {
    return Dhoom.VictoryBonus.rewardsWindowSetting.items;
};

Window_VictoryItem.prototype.contentsHeight = function () {
    return this.itemHeight() * this.maxItems();
};

Dhoom.VictoryBonus.Window_VictoryItem_makeItemList = Window_VictoryItem.prototype.makeItemList;
Window_VictoryItem.prototype.makeItemList = function () {
    Dhoom.VictoryBonus.Window_VictoryItem_makeItemList.call(this);
    this.createContents();
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_VictoryRewards
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Window_VictoryRewards_initialize = Window_VictoryRewards.prototype.initialize;
Window_VictoryRewards.prototype.initialize = function (rect) {
    Dhoom.VictoryBonus.Window_VictoryRewards_initialize.call(this, this.windowRect());
    this.opacity = this.setting().opacity;
    this.createImageSprites();
};

Window_VictoryRewards.prototype.setting = function () {
    return Dhoom.VictoryBonus.rewardsWindowSetting;
};

Window_VictoryRewards.prototype.windowRect = function () {
    return new Rectangle(
        this.setting().x,
        this.setting().y,
        this.setting().width,
        this.setting().height
    );
};

Window_VictoryRewards.prototype.createImageSprites = function () {
    this._imageSprites = [];
    let thisRef = this;
    this.setting().images.forEach(setting => {
        if (this.isCustomConditionValid(setting.condition)) {
            let sprite = new Sprite();
            sprite.setting = setting;
            sprite.bitmap = ImageManager.loadSystem(setting.filename);
            sprite.x = setting.x;
            sprite.y = setting.y;
            sprite.scale.x = setting.scaleX / 100;
            sprite.scale.y = setting.scaleY / 100;
            sprite.opacity = setting.opacity;
            sprite.setAnchor(setting.anchor);
            this._imageSprites.push(sprite);
            if (setting.layer === 0) {
                this.addChild(sprite);
            } else if (setting.layer === 2) {
                this.addChildToBack(sprite);
            }
            sprite.update = function () {
                Sprite.prototype.update.call(this);
                this.opacity = (this.setting.opacity * thisRef.contentsOpacity) / 255;
            };
        }
    }, this);
};

Dhoom.VictoryBonus.Window_VictoryRewards_refresh = Window_VictoryRewards.prototype.refresh;
Window_VictoryRewards.prototype.refresh = function () {
    if (this._bonusExpTextWindow) {
        this.removeChild(this._bonusExpTextWindow);
        this._bonusExpTextWindow = undefined;
    }
    if (this._bonusGoldTextWindow) {
        this.removeChild(this._bonusGoldTextWindow);
        this._bonusGoldTextWindow = undefined;
    }
    Dhoom.VictoryBonus.Window_VictoryRewards_refresh.call(this);
    this.createBonusExpText();
    this.createBonusGoldText();
    this.startVictoryBonuses();
};

Window_VictoryRewards.prototype.createBonusExpText = function () {
    if (!this._bonusExpTextWindow) {
        this._bonusExpTextWindow = new Window_VictoryBonusText(this.setting().bonusExp);
        this.addChild(this._bonusExpTextWindow);
    }
};

Window_VictoryRewards.prototype.createBonusGoldText = function () {
    if (!this._bonusGoldTextWindow) {
        this._bonusGoldTextWindow = new Window_VictoryBonusText(this.setting().bonusGold);
        this.addChild(this._bonusGoldTextWindow);
    }
};

Dhoom.VictoryBonus.Window_VictoryRewards_drawBackgroundElements =
    Window_VictoryRewards.prototype.drawBackgroundElements;
Window_VictoryRewards.prototype.drawBackgroundElements = function () {
    if (this.setting().background)
        Dhoom.VictoryBonus.Window_VictoryRewards_drawBackgroundElements.call(this);
    this.drawCustomImages();
    this.drawCustomTexts();
};

Window_VictoryRewards.prototype.drawCustomImages = function () {
    this.setting().images.forEach(setting => {
        if (setting.layer === 1) {
            if (this.isCustomConditionValid(setting.condition)) {
                let bitmap = ImageManager.loadSystem(setting.filename);
                if (bitmap.isReady()) {
                    let width = (bitmap.width * setting.scaleX) / 100;
                    let height = (bitmap.height * setting.scaleY) / 100;
                    let x = setting.x;
                    let y = setting.y;
                    if ([1, 2, 3].contains(setting.anchor)) y -= height;
                    if ([4, 5, 6].contains(setting.anchor)) y -= Math.round(height / 2);
                    if ([3, 6, 9].contains(setting.anchor)) x -= width;
                    if ([2, 5, 8].contains(setting.anchor)) x -= Math.round(width / 2);
                    this.contents.paintOpacity = setting.opacity;
                    this.contents.blt(
                        bitmap,
                        0,
                        0,
                        bitmap.width,
                        bitmap.height,
                        x,
                        y,
                        width,
                        height
                    );
                } else {
                    this._needRefresh = true;
                }
            }
        }
    }, this);
    this.resetFontSettings();
    this.changePaintOpacity(true);
};

Window_VictoryRewards.prototype.isCustomConditionValid = function (condition) {
    if (!condition) return true;
    let data = BattleManager._victoryData;
    let a = data.damageDealt;
    let b = data.damageTaken;
    let c = data.healDone;
    let d = data.turnTotal;
    let e = data.deathTotal;
    let f = data.killTotal;
    let g = data.playerMissTotal;
    let h = data.playerEvadeTotal;
    let i = data.playerMissEvadeTotal;
    let j = data.enemyMissTotal;
    let k = data.enemyEvadeTotal;
    let l = data.enemyMissEvadeTotal;
    let m = data.itemUsed;
    let n = data.playerTurnTotal;
    let o = data.enemyTurnTotal;
    try {
        return eval(condition);
    } catch (e) {
        console.warn('Custom Condition Error!');
        console.log(e);
        console.log(condition);
    }
    return false;
};

Window_VictoryRewards.prototype.drawCustomTexts = function () {
    this.setting().texts.forEach(setting => {
        this.drawCustomText(0, 0, setting, []);
    }, this);
};

Dhoom.VictoryBonus.Window_Base_resetFontSettings = Window_Base.prototype.resetFontSettings;
Window_VictoryRewards.prototype.resetFontSettings = function () {
    if (this._isDrawingCustomText) return;
    Dhoom.VictoryBonus.Window_Base_resetFontSettings.call(this);
};

Window_VictoryRewards.prototype.sliceText = function (text, width) {
    width = width || this.contents.width;
    var result = [];
    var texts = text.split(' ');
    var t = '';
    var s = null;
    while (texts.length) {
        var c = texts.shift();
        if (c.contains('\n')) {
            s = c.split('\n');
            c = s[0];
        }
        if (this.textSizeEx((t + ' ' + c).trim()).width <= width) {
            t = (t + ' ' + c).trim();
        } else if (t) {
            result.push(t);
            t = c;
        }
        if (s) {
            result.push(t);
            t = s[1];
            s = null;
        }
    }
    if (t) result.push(t);
    if (result.length === 0) result.push(text);
    return result;
};

Window_VictoryRewards.prototype.drawCustomText = function (x, y, setting, replaces) {
    this._isDrawingCustomText = true;
    this._customTextSetting = setting;
    this.contents.changeTextStyle(setting.style);
    var text = setting.text;
    text = text.format.apply(text, replaces);
    text = this.formatBonus(text);
    text = this.convertEscapeCharacters(text);
    if (setting.wordwrap) {
        var texts = this.sliceText(text, setting.width);
    } else {
        var texts = [text];
    }
    this.contents.changeTextStyle(setting.style);
    var lh = this.contents.fontSize + 2;
    var spacing = setting.spacing;
    var width = setting.width;
    var sy = setting.centerVertical
        ? (setting.height - (texts.length * (lh + spacing) - spacing)) / 2
        : 0;
    for (var i = 0; i < texts.length; i++) {
        this.contents.changeTextStyle(setting.style);
        var cw = this.textSizeEx(texts[i]).width + this.contents.outlineWidth * 2;
        var sx = this.contents.outlineWidth;
        if (setting.style.align === 'center') {
            sx = (width - cw) / 2;
        }
        if (setting.style.align === 'right') {
            sx = width - cw;
        }
        this.contents.changeTextStyle(setting.style);
        this.drawTextEx(texts[i], x + sx + setting.x, y + sy + setting.y);
        sy += lh + spacing;
    }
    this._isDrawingCustomText = false;
};

Window_VictoryRewards.prototype.drawPartyExpGauges = function () {
    this.resetFontSettings();
    if (VisuMZ.VictoryAftermath.Settings.General.ShowExpGauges ?? true) {
        for (let i = 0; i < $gameParty.maxBattleMembers(); i++) {
            this.drawRewardActorExpGauge(i);
        }
    }
};

Window_VictoryRewards.prototype.drawRewardActorExpGauge = function (index) {
    let actor = $gameParty.members()[index];
    if (!actor) return;
    this.drawRewardActorFace(index);
    this.drawRewardActorName(index);
    this.drawRewardActorGauge(index);
};

Window_VictoryRewards.prototype.drawRewardActorFace = function (index) {
    let actor = $gameParty.members()[index];
    if (!actor) return;
    let mainSetting = this.setting().experience;
    let setting = mainSetting.face;
    let x = mainSetting.x + mainSetting.spacingX * index + setting.x;
    let y = mainSetting.y + mainSetting.spacingY * index + setting.y;
    let width = setting.width;
    let height = setting.height;
    let faceIndex = actor.faceIndex();
    const bitmap = ImageManager.loadFace(actor.faceName());
    const pw = ImageManager.faceWidth;
    const ph = ImageManager.faceHeight;
    const dx = Math.floor(x + Math.max(width - pw, 0) / 2);
    const dy = Math.floor(y + Math.max(height - ph, 0) / 2);
    const sx = Math.floor((faceIndex % 4) * pw);
    const sy = Math.floor(Math.floor(faceIndex / 4) * ph);
    this.contents.blt(bitmap, sx, sy, pw, ph, dx, dy, width, height);
};

Window_VictoryRewards.prototype.drawRewardActorName = function (index) {
    let actor = $gameParty.members()[index];
    if (!actor) return;
    let mainSetting = this.setting().experience;
    let setting = mainSetting.name;
    let x = mainSetting.x + mainSetting.spacingX * index + setting.x;
    let y = mainSetting.y + mainSetting.spacingY * index + setting.y;
    let width = 500;
    this.drawActorNameStrip(x, y, width);
    this.resetFontSettings();
    this.drawText(
        actor.name(),
        x + Math.round(this.lineHeight() / 2),
        y,
        width - this.lineHeight(),
        'left'
    );
    this.resetFontSettings();
    const text = TextManager.victoryDisplayLvFmt.format(actor.level);
    if (this._showLevelUp) this.changeTextColor(ColorManager.powerUpColor());
    this.drawText(
        text,
        x + Math.round(this.lineHeight() / 2),
        y,
        width - this.lineHeight(),
        'right'
    );
};

Window_VictoryRewards.prototype.drawRewardActorGauge = function (index) {
    let actor = $gameParty.members()[index];
    if (!actor) return;
    let mainSetting = this.setting().experience;
    let setting = mainSetting.gauge;
    let x = mainSetting.x + mainSetting.spacingX * index + setting.x;
    let y = mainSetting.y + mainSetting.spacingY * index + setting.y;
    y -= this.lineHeight();
    let width = 500;
    this.placeActorGauges(index, x, y, width);
};

Dhoom.VictoryBonus.Window_VictoryRewards_update = Window_VictoryRewards.prototype.update;
Window_VictoryRewards.prototype.update = function () {
    Dhoom.VictoryBonus.Window_VictoryRewards_update.call(this);
    if (this._needRefresh && ImageManager.isReady()) {
        this._needRefresh = false;
        this.refresh();
    }
    this.updateDrawVictoryBonuses();
    if (this._bonusExpTextWindow) {
        this._bonusExpTextWindow.contentsOpacity = this.contentsOpacity;
        this._bonusGoldTextWindow.contentsOpacity = this.contentsOpacity;
    }
};

Window_VictoryRewards.prototype.drawItemGainTitle = function () {
    this.resetFontSettings();
    if (BattleManager._rewards.items.length <= 0) return;
    if (!this.setting().items.title) return;
    const lh = this.lineHeight();
    const x = this.setting().items.x + 40;
    const y = this.setting().items.y;
    const width = Math.round(this.width / 2 - 140);
    const text = TextManager.victoryDisplayItem;
    const color = ColorManager.normalColor();
    this.makeFontBigger();
    this.drawText(text, x, y, width, 'left');
    this.contents.fillRect(
        this.setting().items.x,
        y + lh * 1.5,
        Math.round(this.width / 2) - 100,
        2,
        color
    );
};

Window_VictoryRewards.prototype.startVictoryBonuses = function () {
    this._victoryBonusNames = Object.keys(BattleManager._victoryBonuses);
    this._victoryBonusIndex = -1;
    this._victoryBonusDuration = this.setting().bonus.delay;
};

Window_VictoryRewards.prototype.formatBonus = function (text) {
    text = text.replace(/%a%/gi, (s, n) => BattleManager._victoryData.damageDealt);
    text = text.replace(/%b%/gi, (s, n) => BattleManager._victoryData.damageTaken);
    text = text.replace(/%c%/gi, (s, n) => BattleManager._victoryData.healDone);
    text = text.replace(/%d%/gi, (s, n) => BattleManager._victoryData.turnTotal);
    text = text.replace(/%e%/gi, (s, n) => BattleManager._victoryData.deathTotal);
    text = text.replace(/%f%/gi, (s, n) => BattleManager._victoryData.killTotal);
    text = text.replace(/%g%/gi, (s, n) => BattleManager._victoryData.playerMissTotal);
    text = text.replace(/%h%/gi, (s, n) => BattleManager._victoryData.playerEvadeTotal);
    text = text.replace(/%i%/gi, (s, n) => BattleManager._victoryData.playerMissEvadeTotal);
    text = text.replace(/%j%/gi, (s, n) => BattleManager._victoryData.enemyMissTotal);
    text = text.replace(/%k%/gi, (s, n) => BattleManager._victoryData.enemyEvadeTotal);
    text = text.replace(/%l%/gi, (s, n) => BattleManager._victoryData.enemyMissEvadeTotal);
    text = text.replace(/%m%/gi, (s, n) => BattleManager._victoryData.itemUsed);
    text = text.replace(/%n%/gi, (s, n) => BattleManager._victoryData.playerTurnTotal);
    text = text.replace(/%o%/gi, (s, n) => BattleManager._victoryData.enemyTurnTotal);
    return text;
};

Window_VictoryRewards.prototype.isDrawingVictoryBonuses = function () {
    return this._victoryBonusNames && this._victoryBonusIndex < this._victoryBonusNames.length - 1;
};

Window_VictoryRewards.prototype.updateDrawVictoryBonuses = function () {
    if (this.isDrawingVictoryBonuses()) {
        if (--this._victoryBonusDuration <= 0) {
            this._victoryBonusDuration = this.setting().bonus.delay;
            this._victoryBonusIndex++;
            this.drawVictoryBonuses();
        }
    }
};

Window_VictoryRewards.prototype.drawVictoryBonuses = function (dontPlaySe) {
    let data = BattleManager._victoryBonuses;
    let setting = this.setting().bonus;
    let x = setting.x + setting.spacingX * this._victoryBonusIndex;
    let y = setting.y + setting.spacingY * this._victoryBonusIndex;
    let d = data[this._victoryBonusNames[this._victoryBonusIndex]];
    let name = this._victoryBonusNames[this._victoryBonusIndex];
    name = this.formatBonus(name);
    this.drawCustomText(x, y, setting.title, [name]);
    let exp = '';
    let e = Math.floor(d.exp * 100);
    let ev = d.expValue;
    if (d.exp) {
        exp = setting.exp.format(e > 0 ? '+' + e : e, ev);
        this._convertEscapeCHValue = e;
        exp = this.convertEscapeCharacters(exp);
        this._bonusExpTextWindow.setValue(this._bonusExpTextWindow._value + ev);
    }
    let gold = '';
    let g = Math.floor(d.gold * 100);
    let gv = d.goldValue;
    if (d.gold) {
        gold = setting.gold.format(g > 0 ? '+' + g : g, gv);
        this._convertEscapeCHValue = g;
        gold = this.convertEscapeCharacters(gold);
        this._bonusGoldTextWindow.setValue(this._bonusGoldTextWindow._value + gv);
    }
    this.drawCustomText(x, y, setting.value, [exp, gold]);
    if (!dontPlaySe) {
        let se = this.setting().bonus.se;
        if (Dhoom.VictoryBonus.bonusSettings[d.id].customSe)
            se = Dhoom.VictoryBonus.bonusSettings[d.id].se;
        AudioManager.playSe(se);
    }
};

Window_VictoryRewards.prototype.convertEscapeCharacters = function (text) {
    text = Window_Base.prototype.convertEscapeCharacters.call(this, text);
    text = text.replace(
        /\x1bCH/gi,
        '\x1bC[' +
            (this._convertEscapeCHValue > 0 ? 24 : this._convertEscapeCHValue < 0 ? 25 : 0) +
            ']'
    );
    return text;
};

Window_VictoryRewards.prototype.skipDrawingVictoryBonuses = function () {
    while (this.isDrawingVictoryBonuses()) {
        this._victoryBonusDuration = 0;
        this._victoryBonusIndex++;
        this.drawVictoryBonuses(this._victoryBonusIndex < this._victoryBonusNames.length - 1);
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Scene_Battle
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.VictoryBonus.Scene_Battle_updateVictorySteps = Scene_Battle.prototype.updateVictorySteps;
Scene_Battle.prototype.updateVictorySteps = function () {
    if (this._victoryStep === 'rewards' && this._victoryRewardsWindow.isDrawingVictoryBonuses()) {
        this._victoryRewardsWindow.skipDrawingVictoryBonuses();
        return;
    }
    Dhoom.VictoryBonus.Scene_Battle_updateVictorySteps.call(this);
};
