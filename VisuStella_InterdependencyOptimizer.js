/*:
 * @target MZ
 * @plugindesc v2.0.0 VisuStella Plugin Interdependency Optimizer
 * <AUTHOR> Performance Optimizer
 * @orderAfter VisuStella_AdaptiveThrottling
 * @help VisuStella_InterdependencyOptimizer.js
 *
 * ============================================================================
 * VisuStella Plugin Interdependency Optimizer
 * ============================================================================
 *
 * This plugin identifies and optimizes redundant operations between VisuStella
 * plugins that work together, eliminating duplicate calculations and
 * coordinating shared resources.
 *
 * Optimizations:
 * - Shared sprite management between visual plugins
 * - Coordinated update cycles to prevent conflicts
 * - Unified event handling for related plugins
 * - Shared calculation results between similar operations
 * - Coordinated memory management and cleanup
 *
 * @param enableSharedSpriteManagement
 * @text Enable Shared Sprite Management
 * @desc Coordinate sprite creation/destruction between plugins
 * @type boolean
 * @default true
 *
 * @param enableUnifiedEventHandling
 * @text Enable Unified Event Handling
 * @desc Coordinate event processing between plugins
 * @type boolean
 * @default true
 *
 * @param enableCalculationSharing
 * @text Enable Calculation Sharing
 * @desc Share expensive calculation results between plugins
 * @type boolean
 * @default true
 */

(() => {
    'use strict';

    const parameters = PluginManager.parameters('VisuStella_InterdependencyOptimizer');
    const enableSharedSpriteManagement = parameters['enableSharedSpriteManagement'] !== 'false';
    const enableUnifiedEventHandling = parameters['enableUnifiedEventHandling'] !== 'false';
    const enableCalculationSharing = parameters['enableCalculationSharing'] !== 'false';

    // Check for required systems
    if (!window.VisuStellaPerformanceManager) {
        console.error('[VisuStella_InterdependencyOptimizer] VisuStella_UnifiedPerformanceSystem not found!');
        return;
    }

    //=============================================================================
    // Interdependency Optimizer
    //=============================================================================

    class InterdependencyOptimizer {
        constructor() {
            this.initialize();
        }

        initialize() {
            // Shared resource pools
            this.sharedSprites = new Map();
            this.sharedCalculations = new Map();
            this.eventCoordinator = new Map();

            // Plugin detection
            this.detectedPlugins = this.detectVisuStellaPlugins();
            
            // Setup optimizations
            this.setupSharedSpriteManagement();
            this.setupUnifiedEventHandling();
            this.setupCalculationSharing();

            console.log(`[InterdependencyOptimizer] Initialized - Detected plugins: ${Object.keys(this.detectedPlugins).join(', ')}`);
        }

        // Detect which VisuStella plugins are active
        detectVisuStellaPlugins() {
            const plugins = {};

            // Core plugins
            if (typeof VisuMZ !== 'undefined') {
                if (VisuMZ.CoreEngine) plugins.coreEngine = true;
                if (VisuMZ.BattleCore) plugins.battleCore = true;
                if (VisuMZ.EventsMoveCore) plugins.eventsMoveCore = true;
                if (VisuMZ.MessageCore) plugins.messageCore = true;
            }

            // Visual effect plugins
            if (typeof Imported !== 'undefined') {
                if (Imported.VisuMZ_2_LightingEffects) plugins.lightingEffects = true;
                if (Imported.VisuMZ_2_WeatherEffects) plugins.weatherEffects = true;
                if (Imported.VisuMZ_2_PictureEffects) plugins.pictureEffects = true;
                if (Imported.VisuMZ_2_MovementEffects) plugins.movementEffects = true;
            }

            return plugins;
        }

        // Setup shared sprite management
        setupSharedSpriteManagement() {
            if (!enableSharedSpriteManagement) return;

            // Coordinate sprite creation between lighting and weather effects
            if (this.detectedPlugins.lightingEffects && this.detectedPlugins.weatherEffects) {
                this.coordinateLightingWeatherSprites();
            }

            // Coordinate movement and picture effects
            if (this.detectedPlugins.movementEffects && this.detectedPlugins.pictureEffects) {
                this.coordinateMovementPictureSprites();
            }
        }

        // Coordinate lighting and weather sprite management
        coordinateLightingWeatherSprites() {
            // Shared sprite pool for similar visual effects
            const sharedEffectPool = [];

            // Override sprite creation to use shared pool
            if (typeof Sprite_LightBase !== 'undefined') {
                const _Sprite_LightBase_initialize = Sprite_LightBase.prototype.initialize;
                Sprite_LightBase.prototype.initialize = function() {
                    // Try to reuse sprite from shared pool
                    if (sharedEffectPool.length > 0) {
                        const reusedSprite = sharedEffectPool.pop();
                        Object.setPrototypeOf(this, Sprite_LightBase.prototype);
                        this._reused = true;
                    }
                    
                    _Sprite_LightBase_initialize.call(this);
                };
            }

            // Return sprites to shared pool when destroyed
            const _Sprite_destroy = Sprite.prototype.destroy;
            Sprite.prototype.destroy = function() {
                if (this instanceof Sprite_LightBase || 
                    (typeof Sprite_WeatherParticle !== 'undefined' && this instanceof Sprite_WeatherParticle)) {
                    
                    // Reset sprite for reuse
                    this.visible = false;
                    this.opacity = 255;
                    this.scale.set(1, 1);
                    this.rotation = 0;
                    
                    // Return to shared pool (limit pool size)
                    if (sharedEffectPool.length < 50) {
                        sharedEffectPool.push(this);
                        return; // Don't actually destroy
                    }
                }
                
                _Sprite_destroy.call(this);
            };
        }

        // Coordinate movement and picture effect sprites
        coordinateMovementPictureSprites() {
            // Shared update scheduling for visual effects
            const visualEffectQueue = [];
            let visualEffectCounter = 0;

            // Batch visual effect updates
            const processVisualEffects = () => {
                visualEffectCounter++;
                
                // Process effects in batches to prevent frame drops
                const batchSize = window.AdaptiveThrottlingManager ? 
                    (window.AdaptiveThrottlingManager.currentProfile === 'aggressive' ? 3 : 5) : 5;
                
                for (let i = 0; i < Math.min(batchSize, visualEffectQueue.length); i++) {
                    const effect = visualEffectQueue.shift();
                    if (effect && typeof effect === 'function') {
                        try {
                            effect();
                        } catch (error) {
                            console.warn('[InterdependencyOptimizer] Visual effect error:', error);
                        }
                    }
                }
            };

            // Hook into scene update to process visual effects
            const _Scene_Map_update = Scene_Map.prototype.update;
            Scene_Map.prototype.update = function() {
                _Scene_Map_update.call(this);
                processVisualEffects();
            };
        }

        // Setup unified event handling
        setupUnifiedEventHandling() {
            if (!enableUnifiedEventHandling) return;

            // Coordinate event processing between EventsMoveCore and other plugins
            if (this.detectedPlugins.eventsMoveCore) {
                this.coordinateEventProcessing();
            }
        }

        // Coordinate event processing
        coordinateEventProcessing() {
            const eventProcessingQueue = new Map();
            
            // Batch event updates by type
            const _Game_Event_update = Game_Event.prototype.update;
            Game_Event.prototype.update = function() {
                const eventId = this._eventId;
                const mapId = this._mapId;
                const key = `${mapId}_${eventId}`;
                
                // Queue event for batch processing
                if (!eventProcessingQueue.has(key)) {
                    eventProcessingQueue.set(key, {
                        event: this,
                        lastUpdate: 0,
                        updateCount: 0
                    });
                }
                
                const eventData = eventProcessingQueue.get(key);
                eventData.updateCount++;
                
                // Batch similar events together
                const now = Date.now();
                if (now - eventData.lastUpdate > 16) { // ~60fps
                    _Game_Event_update.call(this);
                    eventData.lastUpdate = now;
                } else {
                    // Skip this update, will be processed in next batch
                    eventData.updateCount--;
                }
            };
        }

        // Setup calculation sharing
        setupCalculationSharing() {
            if (!enableCalculationSharing) return;

            // Share expensive calculations between plugins
            this.setupSharedDistanceCalculations();
            this.setupSharedVisibilityCalculations();
            this.setupSharedColorCalculations();
        }

        // Share distance calculations between plugins
        setupSharedDistanceCalculations() {
            const distanceCache = new Map();
            
            // Create shared distance calculation function
            window.SharedCalculations = window.SharedCalculations || {};
            window.SharedCalculations.getDistance = (x1, y1, x2, y2) => {
                const key = `${Math.floor(x1)}_${Math.floor(y1)}_${Math.floor(x2)}_${Math.floor(y2)}`;
                
                let cached = distanceCache.get(key);
                if (cached && Date.now() - cached.timestamp < 1000) {
                    return cached.distance;
                }
                
                const distance = Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2));
                distanceCache.set(key, { distance, timestamp: Date.now() });
                
                // Limit cache size
                if (distanceCache.size > 200) {
                    const oldestKey = distanceCache.keys().next().value;
                    distanceCache.delete(oldestKey);
                }
                
                return distance;
            };
        }

        // Share visibility calculations
        setupSharedVisibilityCalculations() {
            const visibilityCache = new Map();
            
            window.SharedCalculations = window.SharedCalculations || {};
            window.SharedCalculations.isInViewport = (x, y, margin = 100) => {
                const key = `${Math.floor(x / 50)}_${Math.floor(y / 50)}_${margin}`;
                
                let cached = visibilityCache.get(key);
                if (cached && Date.now() - cached.timestamp < 2000) {
                    return cached.visible;
                }
                
                const visible = x > -margin && x < Graphics.width + margin &&
                               y > -margin && y < Graphics.height + margin;
                
                visibilityCache.set(key, { visible, timestamp: Date.now() });
                
                // Limit cache size
                if (visibilityCache.size > 100) {
                    const oldestKey = visibilityCache.keys().next().value;
                    visibilityCache.delete(oldestKey);
                }
                
                return visible;
            };
        }

        // Share color calculations
        setupSharedColorCalculations() {
            const colorCache = new Map();
            
            window.SharedCalculations = window.SharedCalculations || {};
            window.SharedCalculations.blendColors = (color1, color2, ratio) => {
                const key = `${color1}_${color2}_${Math.floor(ratio * 100)}`;
                
                let cached = colorCache.get(key);
                if (cached) {
                    return cached;
                }
                
                // Simple color blending
                const r1 = parseInt(color1.substr(1, 2), 16);
                const g1 = parseInt(color1.substr(3, 2), 16);
                const b1 = parseInt(color1.substr(5, 2), 16);
                
                const r2 = parseInt(color2.substr(1, 2), 16);
                const g2 = parseInt(color2.substr(3, 2), 16);
                const b2 = parseInt(color2.substr(5, 2), 16);
                
                const r = Math.floor(r1 * (1 - ratio) + r2 * ratio);
                const g = Math.floor(g1 * (1 - ratio) + g2 * ratio);
                const b = Math.floor(b1 * (1 - ratio) + b2 * ratio);
                
                const result = `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
                
                colorCache.set(key, result);
                
                // Limit cache size
                if (colorCache.size > 150) {
                    const oldestKey = colorCache.keys().next().value;
                    colorCache.delete(oldestKey);
                }
                
                return result;
            };
        }

        // Get optimization statistics
        getOptimizationStats() {
            return {
                detectedPlugins: Object.keys(this.detectedPlugins).length,
                sharedSprites: this.sharedSprites.size,
                sharedCalculations: this.sharedCalculations.size,
                eventCoordination: this.eventCoordinator.size
            };
        }

        // Cleanup on scene change
        cleanup() {
            // Clear shared resources
            this.sharedSprites.clear();
            this.sharedCalculations.clear();
            this.eventCoordinator.clear();
            
            // Clear shared calculation caches
            if (window.SharedCalculations) {
                // Caches are handled internally by each function
            }
        }
    }

    //=============================================================================
    // Global Interdependency Optimizer
    //=============================================================================

    window.InterdependencyOptimizer = new InterdependencyOptimizer();

    // Hook into scene termination for cleanup
    const _Scene_Base_terminate = Scene_Base.prototype.terminate;
    Scene_Base.prototype.terminate = function() {
        _Scene_Base_terminate.call(this);
        
        if (window.InterdependencyOptimizer) {
            window.InterdependencyOptimizer.cleanup();
        }
    };

    console.log('[VisuStella_InterdependencyOptimizer] Plugin interdependency optimization loaded');
})();
