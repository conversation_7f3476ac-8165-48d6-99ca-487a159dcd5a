/*:
 * @target MZ
 * @plugindesc Allows toggling choice selection between vertical (up/down) and horizontal (left/right) with plugin commands.
 * @command ToggleChoiceHorizontal
 * @text Toggle Choice Horizontal
 * @desc Toggles choice selection to left/right.
 *
 * @command ToggleChoiceVertical
 * @text Toggle Choice Vertical
 * @desc Toggles choice selection to up/down (default).
 *
 * @help
 * Plugin Commands:
 *   Toggle Choice Horizontal - Toggles choice selection to left/right.
 *   Toggle Choice Vertical - Toggles choice selection to up/down (default).
 */

(() => {
    const pluginName = 'ToggleChoiceSelection';
    let isHorizontal = false;

    PluginManager.registerCommand(pluginName, 'ToggleChoiceHorizontal', () => {
        isHorizontal = true;
        console.log('Choice selection toggled to horizontal (left/right).');
    });

    PluginManager.registerCommand(pluginName, 'ToggleChoiceVertical', () => {
        isHorizontal = false;
        console.log('Choice selection toggled to vertical (up/down).');
    });

    const _Window_ChoiceList_processCursorMove = Window_ChoiceList.prototype.processCursorMove;
    Window_ChoiceList.prototype.processCursorMove = function () {
        if (this.isOpenAndActive()) {
            if (isHorizontal) {
                if (Input.isRepeated('right')) {
                    this.cursorRight(Input.isTriggered('right'));
                } else if (Input.isRepeated('left')) {
                    this.cursorLeft(Input.isTriggered('left'));
                }
            } else {
                if (Input.isRepeated('down')) {
                    this.cursorDown(Input.isTriggered('down'));
                } else if (Input.isRepeated('up')) {
                    this.cursorUp(Input.isTriggered('up'));
                }
            }
        }
    };

    const _Window_ChoiceList_cursorDown = Window_ChoiceList.prototype.cursorDown;
    Window_ChoiceList.prototype.cursorDown = function (wrap) {
        if (!isHorizontal) {
            _Window_ChoiceList_cursorDown.call(this, wrap);
        }
    };

    const _Window_ChoiceList_cursorUp = Window_ChoiceList.prototype.cursorUp;
    Window_ChoiceList.prototype.cursorUp = function (wrap) {
        if (!isHorizontal) {
            _Window_ChoiceList_cursorUp.call(this, wrap);
        }
    };

    const _Window_ChoiceList_cursorRight = Window_ChoiceList.prototype.cursorRight;
    Window_ChoiceList.prototype.cursorRight = function (wrap) {
        if (isHorizontal) {
            const index = this.index();
            const maxItems = this.maxItems();
            if (index < maxItems - 1) {
                this.select((index + 1) % maxItems);
            } else if (wrap) {
                this.select(0);
            }
        }
    };

    const _Window_ChoiceList_cursorLeft = Window_ChoiceList.prototype.cursorLeft;
    Window_ChoiceList.prototype.cursorLeft = function (wrap) {
        if (isHorizontal) {
            const index = this.index();
            const maxItems = this.maxItems();
            if (index > 0) {
                this.select((index - 1 + maxItems) % maxItems);
            } else if (wrap) {
                this.select(maxItems - 1);
            }
        }
    };
})();
