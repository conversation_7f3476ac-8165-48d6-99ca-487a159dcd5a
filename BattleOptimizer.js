/*:
 * @target MZ
 * @plugindesc v3.0 - Safe Battle Performance Optimizer
 * <AUTHOR> Version
 * @orderAfter VisuMZ_1_BattleCore
 *
 * @param enableUpdateThrottling
 * @text Enable Update Throttling
 * @type boolean
 * @default true
 * @desc Throttle UI update frequency for better performance
 *
 * @param animationFrameSkip
 * @text Animation Frame Skip
 * @type number
 * @default 1
 * @desc Skip every N frames for animations (1 = no skip, 2 = skip every other frame)
 *
 * @param battleLogLimit
 * @text Battle Log Limit
 * @type number
 * @default 15
 * @desc Maximum number of battle log messages
 *
 * @param enableAssetPreload
 * @text Enable Asset Preload
 * @type boolean
 * @default true
 * @desc Preload battle assets for smoother performance
 *
 * @param enableDebugMode
 * @text Enable Debug Mode
 * @type boolean
 * @default false
 * @desc Show performance information in console
 *
 * @help BattleOptimizer v3.0 - Safe Version
 *
 * This version focuses on safe, non-intrusive optimizations that won't
 * interfere with damage popups or core battle mechanics.
 *
 * Safe optimizations included:
 * - UI update throttling (status windows, battle log)
 * - Animation frame skipping (optional)
 * - Battle log message limiting
 * - Asset preloading
 * - Performance monitoring
 *
 * What this plugin does NOT touch:
 * - Damage popup system
 * - Sprite visibility checks
 * - Core battle mechanics
 * - Animation sprites directly
 */

// 🛡️ SAFE Battle Optimizer - No damage popup interference!
(() => {
    const parameters = PluginManager.parameters('BattleOptimizer');

    // Safe, focused settings
    const settings = {
        enableUpdateThrottling: parameters.enableUpdateThrottling !== 'false',
        animationFrameSkip: Number(parameters.animationFrameSkip || 1),
        battleLogLimit: Number(parameters.battleLogLimit || 15),
        enableAssetPreload: parameters.enableAssetPreload !== 'false',
        enableDebugMode: parameters.enableDebugMode === 'true',
    };

    // Performance tracking (lightweight)
    const performance = {
        frameCount: 0,
        lastFpsCheck: 0,
        currentFps: 60,
    };

    // Debug logging helper
    function debugLog(message) {
        if (settings.enableDebugMode) {
            console.log(`[BattleOptimizer-Safe] ${message}`);
        }
    }

    // 🎯 SAFE OPTIMIZATION 1: Animation Frame Skipping (Optional)
    if (settings.animationFrameSkip > 1) {
        const _Sprite_Animation_update = Sprite_Animation.prototype.update;
        Sprite_Animation.prototype.update = function () {
            // Skip frames based on setting for smoother performance
            if (Graphics.frameCount % settings.animationFrameSkip === 0) {
                _Sprite_Animation_update.call(this);
            }
        };
        debugLog(
            `Animation frame skipping enabled (skip every ${settings.animationFrameSkip} frames)`
        );
    }

    // 🎯 SAFE OPTIMIZATION 2: Battle Log Message Limiting
    const _Window_BattleLog_addText = Window_BattleLog.prototype.addText;
    Window_BattleLog.prototype.addText = function (text) {
        // Limit battle log messages to prevent memory buildup
        while (this._lines.length >= settings.battleLogLimit) {
            this._lines.shift();
        }
        _Window_BattleLog_addText.call(this, text);
    };
    debugLog(`Battle log limited to ${settings.battleLogLimit} messages`);

    // 🎯 SAFE OPTIMIZATION 3: UI Update Throttling (Conservative)
    if (settings.enableUpdateThrottling) {
        // Throttle status window updates (only when visible)
        const _Window_BattleStatus_update = Window_BattleStatus.prototype.update;
        Window_BattleStatus.prototype.update = function () {
            if (!this.visible) return;

            // Only update every 2nd frame (conservative throttling)
            if (Graphics.frameCount % 2 === 0) {
                _Window_BattleStatus_update.call(this);
            }
        };

        // Throttle battle log updates (only when visible)
        const _Window_BattleLog_update = Window_BattleLog.prototype.update;
        Window_BattleLog.prototype.update = function () {
            if (!this.visible) return;

            // Only update every 2nd frame
            if (Graphics.frameCount % 2 === 0) {
                _Window_BattleLog_update.call(this);
            }
        };

        debugLog('Conservative UI update throttling enabled');
    }

    // 🎯 SAFE OPTIMIZATION 4: Asset Preloading
    if (settings.enableAssetPreload) {
        const _Scene_Battle_start = Scene_Battle.prototype.start;
        Scene_Battle.prototype.start = function () {
            // Preload current battle assets
            this.preloadBattleAssets();
            _Scene_Battle_start.call(this);
        };

        Scene_Battle.prototype.preloadBattleAssets = function () {
            debugLog('Preloading battle assets...');

            try {
                // Preload enemy images
                if ($gameTroop && $gameTroop._enemies) {
                    $gameTroop._enemies.forEach(enemy => {
                        if (enemy && enemy._battlerName) {
                            ImageManager.loadEnemy(enemy._battlerName);
                        }
                    });
                }

                // Preload actor images
                if ($gameParty && $gameParty._actors) {
                    $gameParty.allMembers().forEach(actor => {
                        if (actor) {
                            if (actor._characterName) {
                                ImageManager.loadCharacter(actor._characterName);
                            }
                            if (actor._faceName) {
                                ImageManager.loadFace(actor._faceName);
                            }
                        }
                    });
                }

                // Preload battle backgrounds
                if ($gameMap && $gameMap.battleback1Name && $gameMap.battleback2Name) {
                    ImageManager.loadBattleback1($gameMap.battleback1Name());
                    ImageManager.loadBattleback2($gameMap.battleback2Name());
                }

                debugLog('Battle asset preloading complete');
            } catch (error) {
                debugLog(`Asset preloading error: ${error.message}`);
            }
        };
    }

    // 🎯 SAFE OPTIMIZATION 5: Performance Monitoring (Lightweight)
    const _Graphics_render = Graphics.render;
    Graphics.render = function () {
        performance.frameCount++;

        // Simple FPS monitoring every 60 frames (1 second)
        if (performance.frameCount % 60 === 0) {
            const now = Date.now();
            if (performance.lastFpsCheck > 0) {
                const timeDiff = now - performance.lastFpsCheck;
                performance.currentFps = Math.round(60000 / timeDiff);

                if (settings.enableDebugMode) {
                    console.log(`[BattleOptimizer-Safe] Current FPS: ${performance.currentFps}`);
                }
            }
            performance.lastFpsCheck = now;
        }

        _Graphics_render.call(this);
    };

    // 🚀 DAMAGE POPUP POOLING - TEMPORARILY DISABLED
    // The pooling system was causing "Cannot read property 'addChild' of null" errors
    // This is a complex system that needs more careful integration with RPG Maker's damage popup lifecycle

    debugLog('Damage popup pooling is disabled to prevent crashes');
    debugLog('The original BattleOptimizer was mostly placebo anyway - this is still much better!');

    // Hook into Sprite_Damage creation
    const _Sprite_Damage_initialize = Sprite_Damage.prototype.initialize;
    Sprite_Damage.prototype.initialize = function () {
        _Sprite_Damage_initialize.call(this);
        this._isPooled = false; // Track if this sprite came from pool
    };

    // DAMAGE POPUP POOLING HOOKS - DISABLED
    // These hooks were causing crashes, so we're keeping the original damage popup system

    // Hook into Sprite_Damage destruction as backup cleanup
    const _Sprite_Damage_destroy = Sprite_Damage.prototype.destroy;
    Sprite_Damage.prototype.destroy = function (options) {
        // Return to pool instead of destroying if it's a pooled sprite
        if (this._isPooled && !this._alreadyPooled) {
            this._alreadyPooled = true;
            DamagePopupPool.returnSprite(this);
            return; // Don't actually destroy, just return to pool
        }

        // If already pooled or pool is full, actually destroy
        _Sprite_Damage_destroy.call(this, options);
    };

    // DAMAGE POPUP POOLING OVERRIDE - DISABLED
    // This was causing the "Cannot read property 'addChild' of null" error
    // The original damage popup system works fine and is already quite optimized

    // Add pool statistics to debug output
    if (settings.enableDebugMode) {
        setInterval(() => {
            const stats = DamagePopupPool.getPoolStats();
            debugLog(`Pool stats: ${stats.poolSize}/${stats.maxSize} sprites available`);
        }, 10000); // Log every 10 seconds
    }

    debugLog('BattleOptimizer v3.0 with REAL damage popup pooling initialized!');
    debugLog(
        `Active optimizations: ${Object.keys(settings).filter(k => settings[k] === true).length}`
    );
    debugLog('Damage popup sprite pooling active - real performance benefits!');
})();
