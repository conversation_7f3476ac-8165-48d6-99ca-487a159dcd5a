//=============================================================================
// DhoomEquipmentRarity.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_EquipmentRarity = '1.0c';

var Dhoom = Dhoom || {};
Dhoom.EquipmentRarity = Dhoom.EquipmentRarity || {};
/*:
 * @plugindesc Dhoom EquipmentRarity v1.0c - 26/06/2025
 * <AUTHOR>
 * @url drd-workshop.blogspot.com
 * @target MZ
 *
 * @param No Weapon Rarity Chance
 * @desc The chance that the weapon will have no rarity.
 * @type number
 * @min 0
 * @default 100
 *
 * @param Weapon Rarity Settings
 * @type struct<raritySetting>[]
 * @default ["{\"name\":\"\\\\c[3]Uncommon %1\\\\c[0]\",\"chance\":\"100\",\"description\":\"%1 \\\\c[3]Uncommon\\\\c[0] ATK +%4\",\"belowIcon\":\"2721\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"5\\\",\\\"minimum\\\":\\\"1\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}","{\"name\":\"\\\\c[12]Rare %1\\\\c[0]\",\"chance\":\"60\",\"description\":\"%1 \\\\c[12]Rare\\\\c[0] MAT +%4 LUK +%9\",\"belowIcon\":\"2690\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"10\\\",\\\"minimum\\\":\\\"2\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"5\\\",\\\"minimum\\\":\\\"1\\\"}\"}","{\"name\":\"\\\\c[13]Epic %1\\\\c[0]\",\"chance\":\"40\",\"description\":\"%1 \\\\c[13]Epic\\\\c[0] MHP +%2 ATK +%4\",\"belowIcon\":\"2315\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"15\\\",\\\"minimum\\\":\\\"50\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"15\\\",\\\"minimum\\\":\\\"3\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}","{\"name\":\"\\\\c[14]Legendary %1\\\\c[0]\",\"chance\":\"20\",\"description\":\"%1 \\\\c[14]Legendary\\\\c[0] ATK +%4 MAT +%6\",\"belowIcon\":\"2753\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"25\\\",\\\"minimum\\\":\\\"5\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"25\\\",\\\"minimum\\\":\\\"5\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}"]
 *
 * @param No Armor Rarity Chance
 * @desc The chance that the armor will have no rarity.
 * @type number
 * @min 0
 * @default 100
 *
 * @param Armor Rarity Settings
 * @type struct<raritySetting>[]
 * @default ["{\"name\":\"\\\\c[3]Uncommon %1\\\\c[0]\",\"chance\":\"100\",\"description\":\"%1 \\\\c[3]Uncommon\\\\c[0] DEF +%5\",\"belowIcon\":\"2721\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"35\\\",\\\"minimum\\\":\\\"1\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}","{\"name\":\"\\\\c[12]Rare %1\\\\c[0]\",\"chance\":\"60\",\"description\":\"%1 \\\\c[12]Rare\\\\c[0] DEF +%5 MDF +%7\",\"belowIcon\":\"2690\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"60\\\",\\\"minimum\\\":\\\"2\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"40\\\",\\\"minimum\\\":\\\"2\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}","{\"name\":\"\\\\c[13]Epic %1\\\\c[0]\",\"chance\":\"40\",\"description\":\"%1 \\\\c[13]Epic\\\\c[0] DEF +%5 MDF +%7 AGI +%8\",\"belowIcon\":\"2315\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"100\\\",\\\"minimum\\\":\\\"4\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"75\\\",\\\"minimum\\\":\\\"4\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"75\\\",\\\"minimum\\\":\\\"4\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\"}","{\"name\":\"\\\\c[14]Legendary %1\\\\c[0]\",\"chance\":\"20\",\"description\":\"%1 \\\\c[14]Legendary\\\\c[0] DEF +%5 MDF +%7 LUK +%9\",\"belowIcon\":\"2753\",\"aboveIcon\":\"0\",\"mhp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mmp\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"atk\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"def\":\"{\\\"percentage\\\":\\\"150\\\",\\\"minimum\\\":\\\"5\\\"}\",\"mat\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"mdf\":\"{\\\"percentage\\\":\\\"150\\\",\\\"minimum\\\":\\\"5\\\"}\",\"agi\":\"{\\\"percentage\\\":\\\"0\\\",\\\"minimum\\\":\\\"0\\\"}\",\"luk\":\"{\\\"percentage\\\":\\\"200\\\",\\\"minimum\\\":\\\"5\\\"}\"}"]
 *
 * @help Install below DhoomEquipmentSockets if installed.
 *
 * ============================================================================
 *  Weapon & Armor Notetags
 * ============================================================================
 *   <rarityEligible>
 *   - Flag this equipment for rarity randomization.
 */

/*~struct~raritySetting:
@param name
@text Name Format
@desc The new equipment name format. %1 = Original equipment name.
@default %1

@param chance
@text Chance
@desc The higher the number the higher the chance to get this rarity.
@type number
@min 0
@default 1

@param description
@text Description Format
@desc The new equipment description format. %1 = Original equipment description. %2 ~ %9 = Bonus stats.
@default %1

@param belowIcon
@text Below Icon
@desc Icon that will be drawn below the equipment icon.
@type icon
@default 0

@param aboveIcon
@text Above Icon
@desc Icon that will be drawn above the equipment icon.
@type icon
@default 0

@param mhp
@text Max HP Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param mmp
@text Max MP Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param atk
@text Attack Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param def
@text Defense Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param mat
@text Magic Attack Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param mdf
@text Magic Defense Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param agi
@text Agility Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}

@param luk
@text Luck Bonus Setting
@type struct<bonusStat>
@default {"percentage":"0","minimum":"0"}
*/

/*~struct~bonusStat:
@param percentage
@text Percentage
@desc Percentage bonus.
@type number
@min 0
@default 10

@param minimum
@text Minimum Bonus Value
@desc Minimum bonus value when the percentage value is lower than this.
@type number
@min 0
@default 0
*/

Dhoom.data = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom EquipmentRarity/);
})[0];
Dhoom.Parameters = Dhoom.data.parameters;
Dhoom.EquipmentRarity.pluginName = Dhoom.data.name;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.EquipmentRarity.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.EquipmentRarity[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.EquipmentRarity.loadParameters();

Dhoom.EquipmentRarity.startId = 2000;
Dhoom.EquipmentRarity.weaponRarityList = {};
Dhoom.EquipmentRarity.armorRarityList = {};

Dhoom.EquipmentRarity.weaponSeeds = { 0: Date.now() };
Dhoom.EquipmentRarity.armorSeeds = { 0: Date.now() };

Dhoom.EquipmentRarity.createRarityEquipments = function () {
    let weaponId = this.startId;
    $dataWeapons.forEach((weapon, index) => {
        if (weapon && weapon.rarityEligible) {
            this.weaponRarityList[index] = [];
            weapon.baseId = weapon.id;
            $dataWeapons[weaponId] = JsonEx.makeDeepCopy(weapon);
            $dataWeapons[weaponId].id = weaponId;
            $dataWeapons[weaponId].isRarityEquipment = true;
            $dataWeapons[weaponId].socketBaseItem = weaponId;
            this.weaponRarityList[index].push(weaponId);
            weaponId++;
            this.weaponRaritySettings.forEach((setting, i) => {
                const newWeapon = this.createRarityEquipment(weapon, setting, i);
                newWeapon.rarityIndex = i;
                newWeapon.id = weaponId;
                $dataWeapons[weaponId] = newWeapon;
                $dataWeapons[weaponId].socketBaseItem = weaponId;
                this.weaponRarityList[index].push(weaponId);
                weaponId++;
            }, this);
        }
    }, this);
    let armorId = this.startId;
    $dataArmors.forEach((armor, index) => {
        if (armor && armor.rarityEligible) {
            this.armorRarityList[index] = [];
            armor.baseId = armor.id;
            $dataArmors[armorId] = JsonEx.makeDeepCopy(armor);
            $dataArmors[armorId].id = armorId;
            $dataArmors[armorId].isRarityEquipment = true;
            $dataArmors[armorId].socketBaseItem = armorId;
            this.armorRarityList[index].push(armorId);
            armorId++;
            this.armorRaritySettings.forEach((setting, i) => {
                const newArmor = this.createRarityEquipment(armor, setting, i);
                newArmor.rarityIndex = i;
                newArmor.id = armorId;
                $dataArmors[armorId] = newArmor;
                $dataArmors[armorId].socketBaseItem = armorId;
                this.armorRarityList[index].push(armorId);
                armorId++;
            }, this);
        }
    }, this);
};

Dhoom.EquipmentRarity.createRarityEquipment = function (equip, setting, rarityIndex) {
    const newEquip = JsonEx.makeDeepCopy(equip);
    newEquip.name = setting.name.format(equip.name);
    newEquip.socketRealName = setting.name.format(equip.socketRealName);
    const bonus = ['mhp', 'mmp', 'atk', 'def', 'mat', 'mdf', 'agi', 'luk'];
    const bonusValue = [];
    bonus.forEach((p, i) => {
        bonusValue[i] = this.getBonusValue(equip.params[i], setting[p]);
        newEquip.params[i] += bonusValue[i];
    }, this);
    // Add stars for colorblind accessibility
    const rarityStars = ['★', '★★', '★★★', '★★★★']; // 1-4 stars for Mystic, Runic, Astral, Paragon
    let stars = rarityStars[rarityIndex] || '';
    // Extract color code from rarity name
    let starColor = 4; // default green
    if (setting.name) {
        const match = setting.name.match(/\\c\[(\d+)\]/);
        if (match) starColor = match[1];
    }
    // Color each star
    if (stars) {
        stars = stars
            .split('')
            .map(s => (s === '★' ? `\\c[${starColor}]★\\c[0]` : s))
            .join('');
    }
    newEquip.description =
        setting.description.format(
            equip.description,
            bonusValue[0],
            bonusValue[1],
            bonusValue[2],
            bonusValue[3],
            bonusValue[4],
            bonusValue[5],
            bonusValue[6],
            bonusValue[7]
        ) + (stars ? ' ' + stars : '');
    newEquip.baseId = equip.id;
    newEquip.isRarityEquipment = true;
    newEquip.iconIndexes = [setting.belowIcon, newEquip.iconIndex, setting.aboveIcon];
    return newEquip;
};

Dhoom.EquipmentRarity.getBonusValue = function (value, setting) {
    const percentageBonus = Math.floor((value * setting.percentage) / 100);
    // Only apply minimum bonus if the base stat is not 0
    const minimumBonus = value > 0 ? setting.minimum : 0;
    return Math.max(minimumBonus, percentageBonus);
};

Dhoom.EquipmentRarity.getRarityEquipmentId = function (equip, rarityIndex) {
    const list = DataManager.isWeapon(equip) ? this.weaponRarityList : this.armorRarityList;
    return list[equip.id][rarityIndex];
};

Dhoom.EquipmentRarity.getRarity = function (equip) {
    const isWeapon = DataManager.isWeapon(equip);
    const settings = isWeapon ? this.weaponRaritySettings : this.armorRaritySettings;
    const noRarity = isWeapon ? this.noWeaponRarityChance : this.noArmorRarityChance;
    const total = settings.reduce((p, c) => p + c.chance, noRarity);
    let r = Math.floor(
        total *
            (isWeapon ? this.nextWeaponRandom(equip.baseId) : this.nextArmorRandom(equip.baseId))
    );
    for (let i = 0; i < settings.length; i++) {
        r -= settings[i].chance;
        if (r <= 0) return i + 1;
    }
    return 0;
};

Dhoom.EquipmentRarity.nextWeaponRandom = function (id) {
    if (isNaN(this.weaponSeeds[id])) this.weaponSeeds[id] = this.weaponSeeds[0] * id;
    this.weaponSeeds[id] = this.weaponSeeds[id] || this.weaponSeeds[0] * id;
    this.weaponSeeds[id] = (this.weaponSeeds[id] * 16807) % 2147483647;
    return (this.weaponSeeds[id] - 1) / 2147483646;
};

Dhoom.EquipmentRarity.nextArmorRandom = function (id) {
    if (isNaN(this.armorSeeds[id])) this.armorSeeds[id] = this.armorSeeds[0] * id;
    this.armorSeeds[id] = this.armorSeeds[id] || this.armorSeeds[0] * id;
    this.armorSeeds[id] = (this.armorSeeds[id] * 16807) % 2147483647;
    return (this.armorSeeds[id] - 1) / 2147483646;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// DataManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentRarity.DataManager_isDatabaseLoaded = DataManager.isDatabaseLoaded;
DataManager.isDatabaseLoaded = function () {
    if (!Dhoom.EquipmentRarity.DataManager_isDatabaseLoaded.call(this)) return false;
    if (!Dhoom.EquipmentRarity.isEquipmentRarityInitialized) {
        this.DhoomInitEquipmentRarity();
        Dhoom.EquipmentRarity.isEquipmentRarityInitialized = true;
    }
    return true;
};

DataManager.DhoomInitEquipmentRarity = function () {
    var group = [].concat($dataWeapons).concat($dataArmors);
    for (var i = 1; i < group.length; i++) {
        var object = group[i];
        if (object) {
            var notedata = object.note.split(/[\r\n]+/);
            for (var n = 0; n < notedata.length; n++) {
                var match = /<rarityeligible>/i.exec(notedata[n]);
                if (match) {
                    object.rarityEligible = true;
                }
            }
        }
    }
};

Dhoom.EquipmentRarity.DataManager_createGameObjects = DataManager.createGameObjects;
DataManager.createGameObjects = function () {
    Dhoom.EquipmentRarity.DataManager_createGameObjects.call(this);
    Dhoom.EquipmentRarity.createRarityEquipments();
};

Dhoom.EquipmentRarity.DataManager_makeSaveContents = DataManager.makeSaveContents;
DataManager.makeSaveContents = function () {
    const contents = Dhoom.EquipmentRarity.DataManager_makeSaveContents.call(this);
    contents.weaponRaritySeeds = Dhoom.EquipmentRarity.weaponSeeds;
    contents.armorRaritySeeds = Dhoom.EquipmentRarity.armorSeeds;
    return contents;
};

Dhoom.EquipmentRarity.DataManager_extractSaveContents = DataManager.extractSaveContents;
DataManager.extractSaveContents = function (contents) {
    Dhoom.EquipmentRarity.DataManager_extractSaveContents.call(this, contents);
    Dhoom.EquipmentRarity.weaponSeeds = contents.weaponRaritySeeds || { 0: Date.now() };
    Dhoom.EquipmentRarity.armorSeeds = contents.armorRaritySeeds || { 0: Date.now() };
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Actor
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentRarity.Game_Actor_refresh = Game_Actor.prototype.refresh;
Game_Actor.prototype.refresh = function () {
    Dhoom.EquipmentRarity.Game_Actor_refresh.call(this);
    this.refreshRarityEquipments();
};

Game_Actor.prototype.refreshRarityEquipments = function () {
    this._equips.forEach(equip => {
        if (equip.isEquipItem()) {
            const obj = equip.object();
            if (obj.rarityEligible && !obj.isRarityEquipment) {
                const list = equip.isWeapon()
                    ? Dhoom.EquipmentRarity.weaponRarityList
                    : Dhoom.EquipmentRarity.armorRarityList;
                equip.setEquip(equip.isWeapon(), list[equip._itemId][0]);
            }
        }
    }, this);
};

Dhoom.EquipmentRarity.Game_Actor_tradeItemWithParty = Game_Actor.prototype.tradeItemWithParty;
Game_Actor.prototype.tradeItemWithParty = function (newItem, oldItem) {
    $gameParty._disableGainItemRarity = true;
    const result = Dhoom.EquipmentRarity.Game_Actor_tradeItemWithParty.call(this, newItem, oldItem);
    $gameParty._disableGainItemRarity = false;
    return result;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Party
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentRarity.Game_Party_gainItem = Game_Party.prototype.gainItem;
Game_Party.prototype.gainItem = function (item, amount, includeEquip) {
    if (
        !this._disableGainItemRarity &&
        amount > 0 &&
        item &&
        !item.isRarityEquipment &&
        item.rarityEligible
    ) {
        for (let i = 0; i < amount; i++) {
            const rarity = Dhoom.EquipmentRarity.getRarity(item);
            if (rarity >= 0) {
                const newId = Dhoom.EquipmentRarity.getRarityEquipmentId(item, rarity);
                const newItem = DataManager.isWeapon(item)
                    ? $dataWeapons[newId]
                    : $dataArmors[newId];
                Dhoom.EquipmentRarity.Game_Party_gainItem.call(this, newItem, 1, includeEquip);
            } else {
                Dhoom.EquipmentRarity.Game_Party_gainItem.call(this, item, 1, includeEquip);
            }
        }
        return;
    }
    Dhoom.EquipmentRarity.Game_Party_gainItem.call(this, item, amount, includeEquip);
};

Dhoom.EquipmentRarity.Game_Party_setupBattleTestItems = Game_Party.prototype.setupBattleTestItems;
Game_Party.prototype.setupBattleTestItems = function () {
    this._disableGainItemRarity = true;
    Dhoom.EquipmentRarity.Game_Party_setupBattleTestItems.call(this);
    this._disableGainItemRarity = false;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_Base
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentRarity.Window_Base_drawItemName = Window_Base.prototype.drawItemName;
Window_Base.prototype.drawItemName = function (item, x, y, width) {
    Dhoom.EquipmentRarity.Window_Base_drawItemName.call(this, item, x, y, width);
    if (item && item.iconIndexes) {
        const iconY = y + (this.lineHeight() - ImageManager.iconHeight) / 2;
        const delta = ImageManager.standardIconWidth - ImageManager.iconWidth;
        this.contents.clearRect(
            x + delta / 2,
            iconY,
            ImageManager.iconWidth,
            ImageManager.iconHeight
        );
        item.iconIndexes.forEach(icon => {
            this.drawIcon(icon, x + delta / 2, iconY);
        }, this);
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ItemList
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
if (Imported.Dhoom_EquipmentSockets) {
    Window_ItemList.prototype.drawItemNumber = function (item, x, y, width) {
        Dhoom.EquipmentSockets.Window_ItemList_drawItemNumber.call(this, item, x, y, width);
    };
}

Dhoom.EquipmentRarity.Window_ItemList_drawBigItemIcon = Window_ItemList.prototype.drawBigItemIcon;
Window_ItemList.prototype.drawBigItemIcon = function (item, rect) {
    if (item && item.iconIndexes) {
        item.iconIndexes.forEach(icon => this.drawBigIcon(icon, rect));
    } else {
        Dhoom.EquipmentRarity.Window_ItemList_drawBigItemIcon.call(this, item, rect);
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_Help - Show proper stat values with bonuses
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentRarity.Window_Help_setItem = Window_Help.prototype.setItem;
Window_Help.prototype.setItem = function (item) {
    Dhoom.EquipmentRarity.Window_Help_setItem.call(this, item);
    if (item && (DataManager.isWeapon(item) || DataManager.isArmor(item))) {
        let text = item.description || '';

        // Show total stats (base + bonus) for rarity equipment
        if (item && item.params && item.params.length) {
            const paramNames = ['MaxHP', 'MaxMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
            const paramIcons = [
                32, // MaxHP
                33, // MaxMP
                34, // ATK
                35, // DEF
                36, // MAT
                37, // MDF
                38, // AGI
                39, // LUK
            ];
            let statText = [];
            // Calculate gem bonuses for each param
            let gemBonuses = Array(item.params.length).fill(0);
            if (item.socketItems && item.socketItems.length > 0) {
                item.socketItems.forEach(itemId => {
                    if (itemId) {
                        const gem = $dataItems[itemId];
                        if (gem && gem.socketParams) {
                            Object.keys(gem.socketParams).forEach(param => {
                                const paramId = [
                                    'mhp',
                                    'mmp',
                                    'atk',
                                    'def',
                                    'mat',
                                    'mdf',
                                    'agi',
                                    'luk',
                                ].indexOf(param);
                                if (paramId >= 0) {
                                    gemBonuses[paramId] += gem.socketParams[param];
                                }
                            });
                        }
                    }
                });
            }
            for (let i = 0; i < item.params.length; i++) {
                // Get base param from original equipment
                let base = 0;
                if (item.baseId && item.baseId !== item.id) {
                    const baseItem = DataManager.isWeapon(item)
                        ? $dataWeapons[item.baseId]
                        : $dataArmors[item.baseId];
                    if (baseItem) {
                        base = baseItem.params[i];
                    }
                }
                // Calculate rarity bonus (total - base - gemBonus)
                const total = item.params[i];
                const rarityBonus = total - base - gemBonuses[i];
                const displayValue = base + rarityBonus;
                if (displayValue > 0) {
                    const icon = `\\i[${paramIcons[i]}]`;
                    // Extract rarity color
                    let bonusColor = 4; // default green
                    if (item.isRarityEquipment && typeof item.rarityIndex !== 'undefined') {
                        const settings = DataManager.isWeapon(item)
                            ? Dhoom.EquipmentRarity.weaponRaritySettings
                            : Dhoom.EquipmentRarity.armorRaritySettings;
                        const raritySetting = settings[item.rarityIndex];
                        if (raritySetting && raritySetting.name) {
                            const match = raritySetting.name.match(/\\c\[(\d+)\]/);
                            if (match) bonusColor = match[1];
                        }
                    }
                    if (base > 0 && rarityBonus > 0) {
                        statText.push(
                            `${icon}${paramNames[i]}: ${displayValue} (\\c[${bonusColor}]+${rarityBonus}\\c[0])`
                        );
                    } else {
                        statText.push(`${icon}${paramNames[i]}: ${displayValue}`);
                    }
                }
            }
            if (statText.length) {
                text += ` ${statText.join('  ')}`;
            }
        }

        // Add gem stat boosts on the second line if present
        if (item && item.socketItems && item.socketItems.length > 0) {
            const paramNames = ['MaxHP', 'MaxMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
            const bparamIds = ['mhp', 'mmp', 'atk', 'def', 'mat', 'mdf', 'agi', 'luk'];
            const xparamIds = [
                'hit',
                'eva',
                'cri',
                'cev',
                'mev',
                'mrf',
                'cnt',
                'hrg',
                'mrg',
                'trg',
            ];
            const sparamIds = [
                'tgr',
                'grd',
                'rec',
                'pha',
                'mcr',
                'tcr',
                'pdr',
                'mdr',
                'fdr',
                'exr',
            ];

            const emptyIcon =
                typeof Dhoom !== 'undefined' &&
                Dhoom.EquipmentSockets &&
                Dhoom.EquipmentSockets.emptySlotIcon
                    ? Dhoom.EquipmentSockets.emptySlotIcon
                    : 16;
            let gemBoosts = [];

            const getBoosts = gem => {
                let boosts = [];
                const processParams = (params, isRate) => {
                    if (!params) return;
                    Object.keys(params).forEach(param => {
                        const value = params[param];
                        if (value === 0) return;

                        const bParamId = bparamIds.indexOf(param);
                        if (bParamId >= 0) {
                            boosts.push(`${paramNames[bParamId]} (+${value}${isRate ? '%' : ''})`);
                            return;
                        }

                        const paramName = param.toUpperCase();
                        if (xparamIds.includes(param) || sparamIds.includes(param)) {
                            boosts.push(`${paramName} (+${value}%)`);
                        }
                    });
                };

                processParams(gem.socketParams, false);
                processParams(gem.socketParamRates, true);
                return boosts;
            };

            item.socketItems.forEach(itemId => {
                if (itemId) {
                    const gem = $dataItems[itemId];
                    if (gem) {
                        const boosts = getBoosts(gem);
                        if (boosts.length > 0) {
                            gemBoosts.push(`\\i[${gem.iconIndex}]${gem.name}: ${boosts.join(' ')}`);
                        } else {
                            gemBoosts.push(`\\i[${gem.iconIndex}]${gem.name}`);
                        }
                    }
                } else {
                    gemBoosts.push(`\\i[${emptyIcon}]Empty`);
                }
            });

            if (gemBoosts.length > 0) {
                text += `<br>${gemBoosts.join('  ')}`;
            }
        }

        this.setText(text);
    }
};
