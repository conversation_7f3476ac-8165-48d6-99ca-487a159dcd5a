/*:
 * @target MZ
 * @plugindesc Sorts skills based on their position in Learnable Skills.
 * @help This plugin sorts skills based on their position in the Learnable Skills list instead of the database.
 */

(() => {
    const _Game_Actor_skills = Game_Actor.prototype.skills;
    Game_Actor.prototype.skills = function () {
        const skills = [];
        const addedSkillIds = new Set();

        // Add skills from the learnable skills list
        for (const learning of this.currentClass().learnings) {
            const skill = $dataSkills[learning.skillId];
            if (this.isLearnedSkill(skill.id) && !addedSkillIds.has(skill.id)) {
                skills.push(skill);
                addedSkillIds.add(skill.id);
            }
        }

        // Add manually added skills
        for (const skillId of this._skills) {
            if (!addedSkillIds.has(skillId)) {
                const skill = $dataSkills[skillId];
                skills.push(skill);
                addedSkillIds.add(skillId);
            }
        }

        // Sort skills based on their position in the learnable skills list
        return skills.sort((a, b) => {
            const classLearnings = this.currentClass().learnings;
            const indexA = classLearnings.findIndex(learning => learning.skillId === a.id);
            const indexB = classLearnings.findIndex(learning => learning.skillId === b.id);
            return indexA - indexB;
        });
    };
})();
