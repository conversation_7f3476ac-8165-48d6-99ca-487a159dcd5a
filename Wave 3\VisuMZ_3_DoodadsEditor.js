//=============================================================================
// VisuStella MZ - Grid-Free Doodads - In-Game Editor
// VisuMZ_3_DoodadsEditor.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_DoodadsEditor = true;

var VisuMZ = VisuMZ || {};
VisuMZ.DoodadsEditor = VisuMZ.DoodadsEditor || {};
VisuMZ.DoodadsEditor.version = 1.02;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.02] [DoodadsEditor]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Grid-Free_Doodads_VisuStella_MZ
 * @base VisuMZ_2_DoodadsSystem
 * @orderAfter VisuMZ_2_DoodadsSystem
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * In RPG Maker MZ, tilesets are used for mapping purposes. Tileset A is used
 * for drawing land while Tilesets B through E are used to add doodads. But in
 * RPG Maker MZ, doodads added by Tilesets B through E are locked to the grid
 * and add a rather unnatural feel to it. This plugin will allow you to break
 * free of the grid and add doodads unbound by the grid. Doodads can come in
 * all forms, from large to small, static and animated, you name it!
 *
 * There are two plugins for Grid-Free Doodads. One is the system, which has
 * all of the data contained for how doodads are handled in-game. The other is
 * the in-game editor, which allows you to add, remove, and edit doodads during
 * Playtest mode. These are separate so that when you deploy the game and want
 * just the doodad data to remain without the in-game editor, you can leave the
 * editor out. Or in the event there's ever an external editor, you can use
 * that instead.
 *
 * This plugin is only the in-game editor plugin and does not contain the
 * system. The system can be found separately as a Tier 2 plugin from the
 * VisuStella MZ library.
 *
 * Features include all (but not limited to) the following:
 *
 * * Gain the ability to add doodads onto the map through outside of the grid.
 * * Or lock them to the grid if that's what you want.
 * * Add doodads from specified image sources, from the icon sheet, or from the
 *   map's current tileset, too.
 * * Doodads can be animated, too.
 * * Apply a variety of settings to your doodads, ranging from blend modes, to
 *   scaling, to hue changes, tone shifts, blur effects, and more!
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_2_DoodadsSystem
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Instructions - Installing the Grid-Free Doodads System
 * ============================================================================
 *
 * Please follow these instructions in order to start working on putting
 * doodads into your game!
 *
 * ---
 *
 * * Download VisuMZ_2_DoodadsSystem
 * * This is a Tier 2 plugin.
 * * Place it into the usual js/plugins/ folder.
 * * Add it to your Plugin Manager list with the other Tier 2 plugins.
 * * This is just the system plugin.
 * * All this does is make doodads work in your game without the
 *   in-game editor.
 *
 * ---
 *
 * ============================================================================
 * Instructions - Installing the Grid-Free Doodads In-Game Editor
 * ============================================================================
 *
 * Please follow these instructions in order to gain access to the Grid-Free
 * Doodads In-Game Editor!
 *
 * ---
 *
 * * Download VisuMZ_3_DoodadsEditor
 * * This is a Tier 3 plugin. Not Tier 2. Please don't mix them up.
 * * Place it into the usual js/plugins/ folder.
 * * Add it to your Plugin Manager list with the other Tier 3 plugins.
 * * Save your game.
 * * Start your game in "Playtest" mode from the editor.
 * * This will create the Doodads.json file in the project's /data/ folder.
 * * This will also create the /img/doodads/ folder (or whichever folder you
 *   set it to) inside your project's /img/ folder.
 *
 * ---
 *
 * ============================================================================
 * Instructions - Adding New Doodads
 * ============================================================================
 *
 * Please follow these instructions to add new doodads to your game.
 *
 * ---
 *
 * * Open up your game project's folder.
 * * Go to /img/doodads/ or whichever folder you have it set to.
 * * Add the images inside there.
 * * Only PNG images can be used by RPG Maker MZ and this is no exception.
 * * If a playtest is currently running and you want to load the new doodads,
 *   close the playtest session and reopen it to access the new doodads.
 *
 * ---
 *
 * ============================================================================
 * Instructions - Opening the In-Game Doodads Editor
 * ============================================================================
 *
 * Please follow these instructions on how to open up the in-game editor.
 *
 * ---
 *
 * * Start a Playtest session from the RPG Maker MZ editor (Ctrl + R).
 * * Go to the map you want to apply doodads to.
 * * Press F10.
 * * Add all the doodads you want!
 * * Remember to save!
 * * If you do save, you'll receive a message from RPG Maker MZ saying:
 *   "Project data has been modified externally. Do you want to reload?"
 * * If you click yes, the Playtest session will refresh.
 * * If you click no, the Playtest session will continue.
 * * There are no penalties for clicking no.
 *
 * ---
 *
 * WARNING: Doodads do not have collision detection! Your player and events
 * will walk through them! If you do not want the player and events to walk
 * through them, use the VisuMZ_1_EventsMoveCore region restriction features
 * to make custom collision maps!
 *
 * ============================================================================
 * Instructions - Making Your Own Doodads
 * ============================================================================
 *
 * Doodads have only two requirements.
 *
 * ---
 *
 * * They must be PNG's.
 * * They must exist within the 'doodads' folder (or specified folder from
 *   the plugin parameters) or within a folder inside the 'doodads' folder.
 *
 * ---
 *
 * If a folder is placed inside of the 'doodads' folder, it will be listed as
 * on the doodads list as a directory tree to navigate through.
 *
 * ---
 *
 * ============================================================================
 * Instructions - Making Animated Doodads
 * ============================================================================
 *
 * Doodads can be made into animated doodads. An animated doodad is one that
 * will animate whenever the game's graphics update. Follow these steps to make
 * an animated doodad:
 *
 * ---
 *
 * * Create a doodad with a cell layout similar to a sprite.
 * * Each cell must be the same width and height as the other.
 * * When naming the doodad, add [AxB] in its name. Replace A and B with
 *   numbers representing the number of cells horizontally (A) and the
 *   number of cells vertically (B). A doodad with 3 horizontal cells and
 *   2 vertical cells would be named something like 'Torch [3x2].png'.
 * * The doodads cells will animate left to right. Once they reach all the
 *   way right, they will move down a row and update left to right again.
 *   The doodad named 'Torch [3x2].png' will update like such:
 *
 *      0   1   2
 *      3   4   5
 *
 * * You can adjust the frame speed with the doodad's settings in-game.
 *
 * ---
 *
 * ============================================================================
 * In-Game Editor Menus - Main Menu
 * ============================================================================
 *
 * This section will explain what each part of the in-game edtiro's main menu
 * items do.
 *
 * ---
 *
 * Place Doodads
 *
 * - This will take you do your doodads folder, where you can select a doodad
 * to place on the map.
 *
 * ---
 *
 * Edit Doodads
 *
 * - This will allow you to edit the doodads that you have already placed on
 * the map. Here, you can select which doodads based on the layer they're on
 * or from all doodads at once. Doodads are ordered based on their position
 * from top to bottom, left to right.
 *
 * ---
 *
 * Clear Doodads
 *
 * - This will clear all doodads on the map.
 *
 * ---
 *
 * Import from Another Map
 *
 * - This allows you to import doodad settings from another map.
 *
 * ---
 *
 * Toggle Region Overlay
 *
 * - This will cause an overlay of the regions to appear on your screen to
 * show you what tiles are affected by which regions. Use it again to hide
 * the regions.
 *
 * WARNING: Using this on large maps for the first time will cause a bit of
 * lag as the regions have to load. The larger the map, the longer the amount
 * of time is required for it to load.
 *
 * ---
 *
 * Cancel and Close
 *
 * - This will remove any changes made to the doodad settings on the map and
 * close out of the Doodad Menu.
 *
 * ---
 *
 * Save and Close
 *
 * - This will save any changes made to the doodad settings on the map and
 * close out of the Doodad Menu.
 *
 * ---
 *
 * ============================================================================
 * In-Game Editor Menus - Place Doodads
 * ============================================================================
 *
 * You will enter this menu from selecting "Place Doodads" in the in-game
 * editor's main menu.
 *
 * The doodad list will show a list of all the doodads you can use for your map
 * based on the current folder it's in. There are three types of options you
 * can select from here:
 *
 * ---
 *
 * IconSet
 *
 * - This will let you make a doodad out of an icon from the iconset.
 *
 * ---
 *
 * TileSet
 *
 * - This will let you make a doodad out of an icon from the current map's
 * tileset. These are all of the B through E tilesets.
 *
 * ---
 *
 * Folders
 *
 * - Folders will be marked with a / at the end of the name and will have an
 * icon shared by all other folders. Selecting a folder will go into that
 * folder's contents (and further).
 *
 * ---
 *
 * Images
 *
 * - Images will show a small preview of themselves to the left of the name.
 * These images can be used as doodads without any restrictions. Selecting an
 * image will take you to the Doodad Placing Mode.
 *
 * ---
 *
 * ============================================================================
 * In-Game Editor Menus - Edit Doodads
 * ============================================================================
 *
 * You will enter this menu from selecting "Edit Doodads" in the in-game
 * editor's main menu.
 *
 * This menu allows you to select specific doodads from the current map and
 * alter their settings or even delete them.
 *
 * ---
 *
 * Finish Edit
 *
 * - Closes out of the "Edit Doodads" menu and returns to the main menu.
 *
 * ---
 *
 * All Doodads
 *
 * - Loads an entire list of doodads for you to select to work with.
 *
 * ---
 *
 * Layers 0 through 10
 *
 * - Loads a specific layer of doodads for you to select to work with.
 *
 * ---
 *
 * ============================================================================
 * In-Game Editor Menus - Placement Mode
 * ============================================================================
 *
 * Once you've selected a doodad to place, you'll be in "placement mode" where
 * you can use your mouse or the arrow keys (for pixel precision) to place
 * doodads where you see fit.
 *
 * At the bottom of the screen, there will be a small section with some
 * instructional text as to the various keyboard shortcuts on controls.
 *
 * ---
 *
 * Q and E: Layer -/+
 *
 * - This allows you to decrease or increase the doodad's current layer.
 *
 * ---
 *
 * T: Tweak Settings
 *
 * - Pressing this will open up the settings menu for the current doodad.
 *
 * ---
 *
 * W A S D: Move Screen
 *
 * - This will move the screen around so you can have a clear view of the map
 * without needing to reposition the player character.
 *
 * ---
 *
 * ↑←↓→: Precision Move
 *
 * - Pressing the directional keys will allow you to move the doodad using
 * the keyboard instead of the mouse. If you wish to move using the mouse,
 * just click on the map somewhere to return control back to the mouse.
 *
 * ---
 *
 * Z and X: Place or Cancel
 *
 * - Pressing Z will place the doodad in its current state on the map.
 * - Pressing X will return you back to the Doodad List, Icon Picker, or
 * Tileset Picker depending on which screen you came from.
 *
 * ---
 *
 * These are keys that are only usable if a TileSet doodad is to be placed.
 * These will not work with other settings.
 *
 * ---
 *
 * U and O: Change Icon -/+
 *
 * - Switches between the icon to be placed.
 * - Usable only with Icons.
 *
 * ---
 *
 * U and O: Change Tile -/+
 *
 * - Switches between the tile to be placed.
 * - Usable only with Tiles.
 *
 * ---
 *
 * I J K L: Shrink/Grow Tile Group
 *
 * - Allows you to shrink/grow the tile. This will grow from the upper left
 * corner of the tile selected.
 * - Usable only with Tiles.
 *
 * ---
 *
 * There are some hidden keyboard commands that you can use. These are rewarded
 * to the users who read these instructions carefully. Hooray for you!
 *
 * ---
 *
 * H: Hide/Show the Instruction Window
 *
 * - Pressing H will hide the instructional window so you can get a clear
 * view of where you're placing the doodad. Pressing it again will make it
 * show back up.
 *
 * ---
 *
 * 1 2 3 4 5 6 7 8 9 0: Quick Opacity Change
 *
 * - The 1 through 0 keys (not NumPad) will allow you to quickly adjust the
 * opacity level of the doodad. 1 will set 10%, 2 sets 20%, 3 sets 30%, etc.
 * However, 0 will set 100%.
 *
 * ---
 *
 * G: Grid Snap Menu
 *
 * - This opens up the Grid Snap Menu where you can activate or deactivate
 * Grid Snapping and the grid snapping parameters.
 *
 * ---
 *
 * R: Region Overlay
 *
 * - This will cause an overlay of the regions to appear on your screen to
 * show you what tiles are affected by which regions. Press R again to hide
 * the regions.
 *
 * WARNING: Using this on large maps for the first time will cause a bit of
 * lag as the regions have to load. The larger the map, the longer the amount
 * of time is required for it to load.
 *
 * ---
 *
 * ============================================================================
 * In-Game Editor Menus - Doodad Settings
 * ============================================================================
 *
 * You can tweak the settings of individual doodads you want to place or have
 * placed on the map. The Doodad Settings menu can be accessed a number of
 * ways, from pressing the "T" button during Placement Mode or going to the
 * "Edit Doodads" option through the in-game editor's main menu.
 *
 * ---
 *
 * Accept Settings
 *
 * - Accepts all of the settings made and returns back to your previous mode.
 *
 * ---
 *
 * Revert Settings
 *
 * - Cancels all of the settings made and returns back to your previous mode.
 *
 * ---
 *
 * Delete Doodad
 *
 * - Only selectable if accessed from individual doodad management.
 *
 * - This will let you delete the doodad and then return to the doodad
 *   management list.
 *
 * ---
 *
 * Change Position
 *
 * - Only selectable if accessed from individual doodad management.
 *
 * - This will allow you to reposition the doodad.
 *
 * ---
 *
 * Layer
 *
 * - This allows you to change the layer of the doodad. Higher layers will
 * make the doodad appear above others (and characters) and lower layers will
 * cause doodads to appear below.
 *
 * ---
 *
 * Hue
 *
 * - Changing to hue will change the doodad's current color shift. Be warned
 * as this process takes up a lot of processing power, and I highly advise
 * against using doodads of different hues if you plan to export to mobile.
 *
 * ---
 *
 * Opacity
 *
 * - Changes the opacity of the doodad. When the opacity value is higher, the
 * doodad will be less transparent. When the opacity value is lower, it will
 * be more transparent.
 *
 * ---
 *
 * Scale X
 *
 * Scale Y
 *
 * - This changes the amount of stretch on a doodad. X will cause a doodad to
 * stretch horizontally while Y will cause the doodad to stretch vertically.
 * If you decide to use a negative value, it will cause the doodad to mirror.
 *
 * ---
 *
 * Anchor X
 *
 * Anchor Y
 *
 * - This sets the base coordinates of the doodad to be located. How other
 * doodads/objects of the same layer interact with this doodad will be based
 * on its coordinates.
 *
 * ---
 *
 * Frame Speed
 *
 * - If the doodad is animated, you can adjust the frame speed of the doodad
 * here. The number represents the number of frames that must pass before the
 * doodad updates to the next animation cell. This means lower numbers have
 * faster animations while higher numbers have slower animations.
 *
 * ---
 *
 * Blend
 *
 * - Allows you to change the blend modes of the doodads. Blend modes will
 * cause color differences based on the blend mode type to fit in with the
 * visual effects behind it.
 *
 * ---
 *
 * Smooth
 *
 * - Let's you choose whether or not you want to load the doodad with either
 * smooth or hard edges.
 *
 * ---
 *
 * Position Type
 *
 * - Lets you decide how the doodad is positioned. A map-locked position means
 * it will scroll with the map. A screen-locked position means it will stick to
 * its exact position on the screen (like a HUD).
 *
 * ---
 *
 * Angle
 *
 * - Allows you to adjust the angle of the doodad.
 *
 * ---
 *
 * Icon Index
 *
 * - If the doodad is an icon, you can change the icon index here.
 *
 * ---
 *
 * Tile ID
 *
 * Tile Columns
 *
 * Tile Rows
 *
 * - If the doodad is a tile, you can change the tile ID and the number of
 * columns and/or rows it expands with.
 *
 * ---
 *
 * Blur
 *
 * - Lets you decide if the doodad has the blur filter or not.
 *
 * ---
 *
 * Contrast
 *
 * - Changes the contrast of the doodad. High contrast makes colors sharper and
 * brighter while normal will have the colors displayed as they're intended.
 *
 * ---
 *
 * Sepia
 *
 * - This gives you the option to appliy a sepia color schema to the doodad.
 *
 * ---
 *
 * Party
 *
 * - If you would like for certain doodads to appear or disappear if a certain
 * actor has joined the party or is missing from the party, you can use this
 * setting to do so. This way, you can add doodads to a vacant room (if a party
 * member has joined) and remove doodads from their original room. This will
 * count for if the party member is in the active party or the reserve party.
 * There is no differentiation for it.
 *
 * - These doodads will be visible from the in-game editor mode, but will
 * vanish once out of it unless its conditions are met.
 *
 * ---
 *
 * Switch
 *
 * - For those who would like for some doodads to appear while certain switches
 * are on/off, you can make use of this option. Here, you can set conditions
 * for multiple switches per doodad. All the conditions must be met for the
 * doodad to appear visibly. If a doodad requires a switch to be ON and that
 * switch is OFF, the doodad will be invisible until it is on. The same will
 * apply if reversed. If a doodad requires a switch to be OFF, it will remain
 * visible until the switch turns on, which will cause the doodad to disappear.
 *
 * - These doodads will be visible from the in-game editor mode, but will
 * vanish once out of it unless its conditions are met.
 *
 * ---
 *
 * Tone Preset
 *
 * - Select from a list of tones to set the doodad to.
 *
 * ---
 *
 * Tone: Red
 *
 * Tone: Green
 *
 * Tone: Blue
 *
 * Tone: Grey
 *
 * - The tone is the doodad sprite's tone after all hues are applied. This can
 * be used to provide a different shade of colors that hues won't be able to
 * support, thus giving you more options on how to make your doodads appear in
 * the game without the need to create a bunch of resources. This is also very
 * light on memory usage compared to hues, too, which may make it potentially
 * more favorable to use.
 *
 * - Allows you to adjust the individual values of the each of the four tone
 * properties. Use the left/right keys to adjust them manually or press OK on
 * them to quickly select from common number ranges.
 *
 * - Left and Right will adjust them by values of 5. Holding down shift will
 * allow you to adjust them by values of 1 instead.
 *
 * ---
 *
 * Tone: Randomize
 *
 * - Allows you to randomize a specific tone value or all of them.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: In-Game Editor General Settings
 * ============================================================================
 *
 * There aren't too many settings for the in-game editor, but here they are!
 *
 * ---
 *
 * General
 *
 *   Doodads Smoothing:
 *   - Default smooth out doodad edges or give them hard edges?
 *
 *   Alphabetical Settings:
 *   - List doodad settings in alphabetical order?
 *
 * ---
 *
 * Grid Snap
 *
 *   Default Grid Snap:
 *   - Do you want Grid Snap enabled by default?
 *
 *   Grid Snap Width:
 *   - The default grid snap width.
 *
 *   Grid Snap Height:
 *   - The default grid snap height.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 *
 * * Yanfly
 * * Hudell
 * * Liquidize
 * * Arisu
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.02: January 8, 2021
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.01: November 1, 2020
 * * Feature Update!
 * ** Disabled window jumping with the touch input on selecting an existing
 *    Doodads if the mouse is hovered over the window. Update by Yanfly.
 *
 * Version 1.00: October 5, 2020
 * * Finished Plugin!
 * ** Version 1.00a
 * *** Region Overlay is now aligned
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param DoodadsEditor
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General
 *
 * @param GFDSmooth:eval
 * @text Doodads Smoothing
 * @parent General
 * @type boolean
 * @on Smooth
 * @off Pixelated
 * @desc Default smooth out doodad edges or give them hard edges?
 * @default false
 *
 * @param GFDAlphabet:eval
 * @text Alphabetical Settings
 * @parent General
 * @type boolean
 * @on Alphabetical
 * @off By ID
 * @desc List doodad settings in alphabetical order?
 * @default false
 *
 * @param GridSnap
 * @text Grid Snap
 *
 * @param GFDGridSnap:eval
 * @text Default Grid Snap
 * @parent GridSnap
 * @type boolean
 * @on Enabled
 * @off Disabled
 * @desc Do you want Grid Snap enabled by default?
 * @default false
 *
 * @param GFDGridWidth:num
 * @text Grid Snap Width
 * @parent GridSnap
 * @type number
 * @min 1
 * @desc The default grid snap width.
 * @default 48
 *
 * @param GFDGridHeight:num
 * @text Grid Snap Height
 * @parent GridSnap
 * @type number
 * @min 1
 * @desc The default grid snap height.
 * @default 48
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================

const _0x83ea = [
    '_gfdSettingsShadowWindow',
    '_spriteset',
    'itemWidth',
    'cmdGFDEditDoodads',
    'keys',
    'left',
    'doodadFolder',
    'Q\x20E\x20-\x20Layer\x20-/+',
    'path',
    'Place',
    'OFF',
    'createGFDGridMenuWindow',
    'createDoodads',
    'Scale\x20Y',
    'enterDoodadPlacingMode',
    'cmdGFDListIcons',
    'ARRAYNUM',
    'changeSetting_blend',
    'max',
    'drawText',
    'changeSetting_anchorX',
    '_manualY',
    'eTiles',
    '128',
    'cmdGFDTileId',
    'exitDoodadPlacingMode',
    'revertSettings',
    'setLayer',
    'cmdGFDSettPositionTypeOk',
    '30:Slow',
    'cmdGFDSettingsLayer',
    'isReady',
    '23669ZJPUUL',
    '800%',
    '_prevDoodad',
    '\x205.\x20Same\x20as\x20Char',
    '_needsRefresh',
    'cmdGFDSettingsToneBlue',
    'cmdGFDSettingsIconIndex',
    'changeSetting_hue',
    'tileset',
    'blendModes',
    'Sepia',
    'Additive',
    'padZero',
    'Hard\x20Light',
    'isPressed',
    'playCursor',
    'currentDoodadY',
    'cmdGFDSettSwitchNone',
    'yFrames',
    '_padding',
    'cmdGFDGridMenuToggleGrid',
    'anchorY',
    'cancelGFDSettBlur',
    'Tone:\x20Red',
    'GFDGridSnap',
    '200%',
    'Window_Base_updateTone',
    'cmdGFDSettingsAngle',
    'Lower(1)',
    'layerList',
    'createGFDSettingsSmoothWindow',
    'performCanvasAction_ScreenUp',
    'revert',
    'startScroll',
    'createGFDSettingsOutlineWindow',
    'Tone:\x20Randomize\x20All',
    'join',
    'updateCanvasMode',
    'getTemplate',
    'screenX',
    'maxCols',
    'cancelGFDSettTileRows',
    'Tone:\x20Grey',
    'getItemText',
    'performCanvasAction_',
    'layer',
    'delete',
    '#f06eaa',
    'getItemText_switch',
    '\x204.\x20Under\x20Layer',
    '\x202.\x20Low\x20Layer',
    '_regionOverlayWindowV',
    'isHovered',
    '_gfdGridMenuWindow',
    '_regionOverlayWindowOn',
    'updateClick',
    '\x20Off',
    'rotateBitmapHue',
    'update',
    'red',
    'updateRefresh',
    '_gfdSettingsToneRGBWindow',
    '\x203.\x20Below\x20Characters',
    'rawDoodadY',
    'cmdGFDSettingsPositionType',
    'edit',
    '_gfdSettingsLayersWindow',
    'Center',
    'drawIcon',
    'makeTileList',
    'switchName',
    'getLocalPath',
    '\x20Joined',
    'toggle',
    'itemRectWithPadding',
    'Multiply',
    'Tile\x20Rows',
    'length',
    'findIndex',
    'addNew',
    'call',
    'Game_Player_canMove',
    'index',
    '_currentDoodadY',
    'makeTotalNumbers',
    'Normal',
    'initialize',
    'changeSetting_outline',
    '_gfdSettingsToneGreyWindow',
    'refreshSettings',
    '_context',
    'green',
    'openGFDGridMenuWindow',
    'windowX',
    'cmdGFDSettingsBlur',
    'createGFDTileWindow',
    'filters',
    'cmdGFDSettAnchorYOk',
    'ctrlKey',
    'Additive\x20NPM',
    'performCanvasAction_ScreenRight',
    '\x206.\x20Upper\x20Layer',
    'exit',
    'Return\x20to\x20Layer\x20List',
    'updateOpacity',
    'cmdGFDSettingsShadow',
    '_gfdSettingsWindow',
    '_regionOverlayWindowHV',
    'addFileList',
    'Color',
    'getXFrames',
    'updateTripleMode',
    'pageY',
    'defineProperty',
    'getItemText_',
    'scaleX',
    'switchOn',
    '_pX',
    'Middle',
    'FUNC',
    '_isGFDWindow',
    'centerScreenPlayer',
    'createGFDListWindow',
    'Delete\x20Doodad',
    'playSave',
    '_gfdSettingsFrameSpeedWindow',
    'toneRandomBlue',
    'isLoopHorizontal',
    'cancelGFDSettParty',
    'Window_initialize',
    '#fff200',
    'settings',
    'openGFDWindows',
    'Settings',
    'iconHeight',
    'performCanvasAction_TweakSettings',
    'lineHeight',
    'playEquip',
    'addCustomCommandsLast',
    '_gfdSettingsSepiaWindow',
    'Burn',
    '40%',
    'actor',
    '_onMouseMove',
    'map',
    'tileCols',
    'iconSet',
    'version',
    'party',
    'performCanvasAction_DescendLayer',
    'GFDSmooth',
    'includes',
    'partyNone',
    'cancelGFDTile',
    '#00FF00',
    'dTiles',
    'moveTo',
    'cancelGFDGridMenu',
    'cancelGFDSettIconIndex',
    'updateScene',
    'getItemText_blur',
    'grey',
    'createGFDPickDoodadListWindow',
    'changeSetting_anchorY',
    'width',
    'normalColor',
    'switch',
    'performCanvasAction_5',
    'toneRed',
    'currentSymbol',
    '\x201:Very\x20Fast',
    'Tone:\x20Randomize\x20Grey',
    'splice',
    '_gfdSettingsGlowWindow',
    'cTiles',
    'Destination\x20Out',
    'changeSetting_frameSpeed',
    'tileWidth',
    'Opacity',
    'updatePosition',
    'Scene_Map_isMenuEnabled',
    'cancelGFDSettScale',
    '#ed145b',
    'Source\x20Atop',
    '_gfdSettingsSwitchWindow',
    'statSync',
    'cmdGFDSettHueOk',
    'scaleY',
    'drawTile',
    'rawDoodadX',
    'currentExt',
    'cancelGFDSettFrameSpeed',
    '_gridLockY',
    'performCanvasAction_Place',
    '_layer',
    '#f7941d',
    'isTilePresent',
    'changeSetting_scaleX',
    'addCustomCommands',
    '_displayY',
    '_scene',
    'IconSet',
    'activate',
    'Exclusion',
    'indexOf',
    'performCanvasAction_ScreenLeft',
    'contentsBack',
    'performCanvasAction_4',
    'createGFDCanvasWindow',
    '10%',
    'toggleGFDWindows',
    'tilesetNames',
    'skippedSettingSymbols',
    'cmdGFDSettingsRevert',
    '30%',
    'getYFrames',
    '\x205.\x20Same\x20as\x20Characters',
    'gaugeBackColor',
    'getSettingsList',
    'createGFDSettingsSepiaWindow',
    'setHandler',
    '_gfdSettingsHueWindow',
    'cancelGFDSettToneGrey',
    'GridSettings',
    'cancelGFDSettBlend',
    'createGFDSettingsAnchorYWindow',
    'changeSetting_layer',
    'TweakSettings',
    'changeTextColor',
    'toneColors',
    '255',
    'createGFDSettingsContrastWindow',
    'cancelGFDSettTileId',
    'createGFDSettingsSwitchWindow',
    'isReduceOpacity',
    'adjustManualMove',
    'cmdGFDSettingsPosition',
    'Above\x20Char(7)',
    'Not\x20Animated',
    '-75%',
    '250',
    'switches',
    'sort',
    'readdirSync',
    '_gfdSettingsTileIdWindow',
    'cmdGFDSettGlowOk',
    'performCanvasAction_1',
    'performCanvasAction_8',
    'endLoading',
    '_gfdSettingsTileRowsWindow',
    'reduce',
    'performCanvasAction_GrowRows',
    'save',
    'addCustomCommandsFirst',
    'Highest(10)',
    'Place\x20Doodads',
    'createGFDSettingsBlendWindow',
    'cmdGFDSettingsAccept',
    'cmdGFDSettingsToneGrey',
    'outline',
    '-100%',
    'createGFDSettingsToneRGBWindow',
    'contents',
    'iconWidth',
    'compareColors',
    'match',
    '200',
    '#00a99d',
    'rowSpacing',
    'opacity',
    'ShrinkRows',
    'filter',
    'toneRandomRed',
    'performCanvasAction_ClimbLayer',
    'Same\x20as\x20Char(5)',
    'createGFDSettingsTileRowsWindow',
    '100%',
    'cmdGFDSettLayerOk',
    'cancelGFDSettAnchorX',
    'cancelGFDSettOutline',
    'actorName',
    'Sepia\x20Mode',
    '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
    'toneRandomAll',
    'dirname',
    'lineTo',
    '_currentDoodad',
    'current',
    'create',
    'cancelGFDPickDoodadLayer',
    'cmdGFDSettingsOpacity',
    'symbol',
    '-25%',
    'performCanvasAction_NextTile',
    'windowY',
    '-255',
    'tonePreset',
    'performCanvasAction_Hide',
    'Null',
    'tileId',
    '_gfdSettingsAnchorYWindow',
    'shift',
    'ScreenLeft',
    'rgb(',
    'createGFDSettingsPositionTypeWindow',
    'cmdGFDSettSwitchOff',
    '_canvasMode',
    'cmdGFDMenuRegionOverlay',
    'Game_Event_updateSelfMovement',
    '_gfdDrawGridWindow',
    'Glow',
    'beginPath',
    'refresh',
    'pop',
    'mkdirSync',
    'getItemText_glow',
    '_gfdTileWindow',
    'cmdGFDSettToneRandomAll',
    '45:Mega\x20Slow',
    'setGridLockMode',
    'cmdGFDSettSepiaOk',
    'createGFDSettingsTileColsWindow',
    'getItemText_hue',
    'none',
    'existsSync',
    'performCanvasAction_Up',
    '_mouseOverX',
    'Window_isOpen',
    'folder',
    '_loadedData',
    'createContents',
    'cmdGFDSettingsScale',
    'screenY',
    'cmdGFDListTiles',
    'TileSet',
    'replace',
    'cancelGFDSettOpacity',
    '_mapHeight',
    'cmdGFDSettPartyNone',
    'putImageData',
    'initData',
    'Scene_Map_isDebugCalled',
    'cancelGFDSettToneRGB',
    '-150',
    '55:Ultra\x20Slow',
    '---line---',
    '25:Average',
    'Switch\x20',
    'active',
    'cmdGFDSettTileIdOk',
    '_prevDoodadSettings',
    'closeGFDMode',
    '\x201.\x20Lower\x20Layer',
    'scale',
    '35:Slower',
    'setManualMove',
    '_lastDisplayX',
    'toneGreen',
    'High(8)',
    '_doodadCursor',
    'partyHave',
    'bTiles',
    '_mouseOverY',
    'cmdGFDSettOutlineOk',
    'Below\x20Char(3)',
    '25%',
    'changeSetting_glow',
    'close',
    'setCanvasMode',
    'getItemText_angle',
    'paintOpacity',
    'performCanvasAction_0',
    '_mapWidth',
    'isAnimated',
    'canMove',
    '50%',
    'performCanvasAction_PrevTile',
    'smooth',
    'changeSetting_tileId',
    'cancelGFDSett',
    'Destination\x20Atop',
    'cancelGFDSettHue',
    'bind',
    '_gfdSettingsAnchorXWindow',
    'getItemText_anchorX',
    'cmdGFDSettingsHue',
    'addFolderList',
    '#000000',
    '75%',
    'itemPadding',
    'createGFDSettingsHueWindow',
    'cmdGFDSettToneGreyOk',
    'data',
    'createGFDSettingsLayersWindow',
    'cmdGFDSettingsParty',
    'cancelGFDPickDoodadList',
    '_gfdWindows',
    'setDoodadCursor',
    'createGFDSettingsShadowWindow',
    'ScreenUp',
    'keyDown',
    '↑←↓→\x20-\x20Precision\x20Move',
    'round',
    '80%',
    'ShrinkCols',
    'padding',
    'isDebugCalled',
    'Dodge',
    'addRegionOverlayWindow',
    'getItemText_tileRows',
    '_tileWidth',
    'createGFDSettingsToneGreyWindow',
    '_closing',
    '-75',
    '_data',
    'assign',
    'xFrames',
    'positionType',
    'select',
    '-50%',
    'switchNone',
    'performCanvasAction_2',
    'performCanvasAction_3',
    'hasOwnProperty',
    'ARRAYEVAL',
    'freeze',
    'prototype',
    'Tone:\x20Randomize\x20Green',
    'gridLockX',
    'smoothSelect',
    'Icon\x20Index',
    'format',
    'Low(2)',
    'createGFDSettingsGlowWindow',
    '_gfdIconWindow',
    'changeSetting_tileRows',
    'cmdGFDSettBlendOk',
    'ARRAYSTRUCT',
    'contentsWidth',
    'performCanvasAction_ChangeOpacity',
    'data/',
    '_gfdSettingsTonePresetWindow',
    'performCanvasAction_Return',
    'Clear',
    'makeDeepCopy',
    'Tone:\x20Blue',
    'hide',
    'regionColor',
    'getSortedSettings',
    'mapId',
    '60%',
    'addCommand',
    'cmdGFDSettingsTonePreset',
    'cancelGFDSettGlow',
    '_doodadEditorMode',
    'cancelGFDSettLayer',
    '_gfdSettingsBlurWindow',
    'performCanvasAction_7',
    '_manualX',
    'adjustY',
    'Graphics_onKeyDown',
    '_displayX',
    'gridX',
    'contentsOpacity',
    'TILE_ID_B',
    'regionOverlay',
    'Change\x20Position',
    'deactivate',
    'Blur',
    'Tone:\x20Randomize\x20Red',
    'GFDAlphabet',
    'contrast',
    '_gfdListWindow',
    '\x209.\x20Higher\x20Layer',
    '_regionOverlayWindowH',
    'performCanvasAction_Right',
    'cmdGFDMenuPlace',
    'Screen',
    'enabled',
    'cancelGFDSettAnchorY',
    'visible',
    'Map',
    'Return',
    'playCancel',
    '\x20Missing',
    'getItemText_scaleY',
    'itemHeight',
    '_gfdSettingsPositionTypeWindow',
    'height',
    'regionRect',
    'file',
    '_gfdToneColor',
    'cancelGFDImport',
    'T\x20-\x20Tweak\x20Settings',
    'windowWidth',
    'createGFDMenuWindow',
    'description',
    '50:Super\x20Slow',
    'Tile\x20ID',
    'cmdGFDIconIndex',
    'ext',
    'cmdGFDMenuSave',
    'ScreenDown',
    '_doodadsTotal',
    'cmdGFDSettingsOutline',
    'drawRegion',
    'G\x20-\x20Grid\x20Settings',
    'loadDoodad',
    'updateCurrentDoodad',
    '_gfdPickDoodadListWindow',
    'return\x200',
    '_folder',
    'setGFD',
    'getItemText_contrast',
    'Toggle\x20Region\x20Overlay',
    'Smooth',
    'Lighten',
    'clamp',
    '_gfdSettingsOpacityWindow',
    '_gfdSettingsTileColsWindow',
    'cancelGFDSettContrast',
    'Scene_Map_updateScene',
    'cmdGFDSettingsToneGreen',
    'cmdGFDMenuClear',
    '#00a651',
    'Right',
    'cmdGFDSettingsFrameSpeed',
    'changeSetting_positionType',
    'pageToCanvasX',
    'createGFDSettingsTileIdWindow',
    '_debugActive',
    'writeFileSync',
    'Destination\x20Over',
    'glow',
    'constructor',
    'addAcceptCommands',
    'NextTile',
    'frameSpeed',
    'performCanvasAction_Down',
    'stringify',
    'sortDoodads',
    'Frame\x20Speed',
    'STR',
    'selectExt',
    'inputLeft',
    'rgbToStr',
    'changeSetting_smooth',
    'tileSet',
    'cmdGFDSettTileColsOk',
    'tileRows',
    'gridY',
    'Blend',
    'cmdGFDSettAnchorXOk',
    'colSpacing',
    '_opening',
    'Normal\x20Mode',
    'abs',
    'TILE_ID_A5',
    'cmdGFDSettAngleOk',
    'getItemText_layer',
    'strokeStyle',
    'currentDoodadX',
    'None',
    'itemRect',
    'frameUpdate',
    '\x205:Faster',
    'changePaintOpacity',
    'show',
    'cmdGFDSettingsTileRows',
    'numVisibleRows',
    '1572165jeOzsR',
    'cmdGFDSettFrameSpeedOk',
    'cmdGFDSettingsAnchorX',
    'cmdGFDSettToneRandomGreen',
    'getItemText_scaleX',
    'cmdGFDSettingsTileId',
    'isScrolling',
    'inputRight',
    'Tile\x20Columns',
    '_regionOverlayWindow',
    'TILE_ID_D',
    'removeChild',
    'createGFDSettingsOpacityWindow',
    '-200',
    'stroke',
    'drawItem',
    'Layer',
    'getLayerList',
    '-250',
    'createGFDSettingsIconIndexWindow',
    'isDirectory',
    '_settingsMode',
    'cmdGFDMenuImport',
    '12345',
    'cmdGFDSettingsAnchorY',
    '_gfdCanvasWindow',
    'pageX',
    'cancelGFDSettTonePreset',
    'Higher(9)',
    'loadSystem',
    'toneBlue',
    'angle',
    'createGFDSettingsBlurWindow',
    'ClimbLayer',
    'push',
    'cmdGFDSettBlurOk',
    'centerScreenDoodad',
    'cmdGFDSettingsToneRed',
    'createGFDPickDoodadLayerWindow',
    'min',
    'cancelGFDSettShadow',
    'layerTitles',
    'blend',
    'regionId',
    'open',
    'cmdGFDSettToneRandomRed',
    'cmdGFDSettSwitchOn',
    'getItemText_anchorY',
    '_editMode',
    'updateCurrentMode',
    'updateSettingsMode',
    'STRUCT',
    'doodad',
    'position',
    'resetTextColor',
    'setDoodad',
    '_gfdSettingsPartyWindow',
    'blur',
    'Width',
    '_doodads',
    'changeSetting_opacity',
    'Set\x20Grid\x20Free',
    '90%',
    'drawDoodadImage',
    'isLoopVertical',
    'X:\x20',
    'cancelGFDIcon',
    '_gfdSettingsBlendWindow',
    'pageToCanvasY',
    'toneRandomGrey',
    'updatePadding',
    'isMap',
    'cmdGFDSettToneRandomGrey',
    'Many',
    '10.\x20Highest\x20Layer',
    'updateTone',
    'Luminosity',
    'changeColorForSymbol',
    'cancelGFDWindows',
    '-1000%',
    'layerNames',
    'isMenuEnabled',
    '_textWidth',
    'isRepeated',
    'cmdGFDSettTonePresetOk',
    'cmdGFDSettShadowOk',
    'High\x20Contrast',
    'addPositionCommands',
    'cancelGFDSettAngle',
    'closeGFDWindows',
    '#ed1c24',
    'itemTextAlign',
    'getOpacity',
    'fittingHeight',
    '76hhHfLR',
    'find',
    '#0054a6',
    'GlowFilter',
    'switchOff',
    'hue',
    'accept',
    'TILE_ID_E',
    'drawHeight',
    'updateClose',
    'Z\x20X\x20-\x20Place\x20/\x20Return',
    '-400%',
    'filename',
    'tileLists',
    'ARRAYJSON',
    'cmdGFDSettingsDelete',
    'cancelGFDSettSmooth',
    'getItemText_smooth',
    'cmdGFDSettIconIndexOk',
    'layerText',
    'backOpacity',
    'createGFDWindows',
    'isHoverEnabled',
    'I\x20J\x20K\x20L\x20-\x20Shrink/Grow\x20Tile\x20Group',
    'test',
    '_lastDisplayY',
    'addTileRange',
    'floor',
    'Darken',
    '_tilemap',
    'random',
    'cmdGFDSettingsContrast',
    '#a3d39c',
    'randomIntBetween',
    '_gfdMenuWindow',
    'cmdGFDSettToneRGBOk',
    'doodads',
    'cmdGFDSettingsSwitch',
    'TILE_ID_C',
    'parse',
    'commandName',
    'windowHeight',
    'Saturation',
    'toneRandomGreen',
    'import',
    'iconIndex',
    'toneGrey',
    '_gridLockMode',
    'GFDGridWidth',
    'High',
    'createGFDSettingsTonePresetWindow',
    'drawHorzLine',
    'ScreenRight',
    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
    'cancelGFDSettPositionType',
    'changeSetting_contrast',
    '_gfdSettingsSmoothWindow',
    'Height',
    'screen',
    '_doodad',
    'keyCode',
    'Difference',
    'W\x20A\x20S\x20D\x20-\x20Move\x20Screen',
    'altKey',
    'createGFDDrawGridWindow',
    'lineColor',
    'openGFDDoodadSettings',
    '_pY',
    'folderPath',
    'cancelGFDSettTileCols',
    'isGFD',
    '_gfdPickDoodadLayerWindow',
    'Clear\x20Doodads',
    'DescendLayer',
    'shadow',
    'ARRAYSTR',
    '226713NdsndI',
    'setSettingsMode',
    'This\x20is\x20a\x20static\x20class',
    'Sprite_Doodad_update',
    'cancelGFDSettSwitch',
    'Base',
    'regionColors',
    'partyMiss',
    'currentCopy',
    'updateManualMove',
    '_gfdSettingsAngleWindow',
    'Source\x20Out',
    'playUseItem',
    'setEditMode',
    'Finish\x20Edit',
    'addLineCommand',
    '\x208.\x20High\x20Layer',
    'getItemText_opacity',
    'Window_Base_updateOpen',
    'updateOpen',
    'DoodadsEditor',
    '20:Normal',
    '_gfdSettingsOutlineWindow',
    'createGFDSettingsWindow',
    'Destination\x20In',
    '150',
    'Switch',
    '_gfdSettingsScaleWindow',
    '_list',
    'Left',
    'toneSet',
    'updateSelfMovement',
    '\x20On',
    'Window_isClosed',
    '10:Fast',
    'updateNewSettings',
    'changeSetting_shadow',
    '-100',
    'isOpen',
    'playUseSkill',
    'Source\x20In',
    'changeSetting_sepia',
    'selectSymbol',
    'name',
    'fillRect',
    'cancelGFDSettSepia',
    'createGFDSettingsAnchorXWindow',
    'getImageData',
    'createGFDSettingsPartyWindow',
    'commandSymbol',
    'cmdGFDSettPartyMiss',
    'tileHeight',
    'blue',
    'blt',
    'right',
    'currentLayer',
    'showRegionOverlayWindow',
    'cmdGFDSettingsBlend',
    'createGFDSettingsFrameSpeedWindow',
    '_onKeyDown',
    'JSON',
    'parameters',
    'center',
    'EVAL',
    'makeCommandList',
    '_manualMove',
    'mainModule',
    'cmdGFDListFolder',
    'isTriggered',
    'cmdGFDMenuRevert',
    'cancelGFDList',
    'changeSetting_tileCols',
    'createGFDSettingsAngleWindow',
    '\x200.\x20Lowest\x20Layer',
    'Hard',
    'Tone:\x20Green',
    'Soft\x20Light',
    'adjustX',
    'toUpperCase',
    '134397qfIERR',
    '_gridLockX',
    'sepia',
    'getItemText_frameSpeed',
    'apply',
    'closeRegionOverlayWindow',
    '_gfdSettingsIconIndexWindow',
    'createGFDImportWindow',
    'cmdGFDSettingsTileCols',
    'cmdGFDSettToneRandomBlue',
    'addFolder',
    '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
    'updateEditMode',
    'cmdGFDSettingsSepia',
    'deleteFolder',
    'Upper(6)',
    'Hue',
    'PrevTile',
    'n/a',
    'getItemText_sepia',
    'cmdGFDDoodadListSelect',
    'toggleRegionOverlayWindow',
    'isCommandEnabled',
    'performCanvasAction_6',
    '_gfdSettingsContrastWindow',
    'cancel',
    'setDisplayPos',
    'place',
    'Custom',
    'layers',
    'hslToRgb',
    'cmdGFDSettTileRowsOk',
    'Accept\x20Settings',
    '70%',
    'addChild',
    'changeSetting_toneBlue',
    'anchorX',
    '1256762QWSbfH',
    'createGFDSettingsScaleWindow',
    '_tileHeight',
    'changeSetting_',
    'getItemText_tileId',
    'Window_Base_updateClose',
    'Head',
    '_currentDoodadX',
    'setTone',
    'drawDarkRect',
    'clearMap',
    'isOptionValid',
    'cmdGFDSettContrastOk',
    'updateSettingsOpacity',
    '40:Very\x20Slow',
    'isClosed',
    'ConvertParams',
    'cmdGFDSettingsSmooth',
    'addCommandList',
    'maxItems',
    '1050883pGEEes',
    'clear',
    '817564UOySUT',
    'toggleGrid',
    'getItemText_blend',
    '_gfdImportWindow',
    'createGFDSettingsSubwindows',
    'addMainCommands',
    'Set\x20Grid\x20Snap',
];
const _0x22f3 = function (_0x2d3c22, _0x259c81) {
    _0x2d3c22 = _0x2d3c22 - 0x78;
    let _0x83ea22 = _0x83ea[_0x2d3c22];
    return _0x83ea22;
};
const _0x9a8c74 = _0x22f3;
(function (_0x162b39, _0x27d8e4) {
    const _0x3d66ef = _0x22f3;
    while (!![]) {
        try {
            const _0x11f026 =
                -parseInt(_0x3d66ef(0x2a3)) +
                -parseInt(_0x3d66ef(0x254)) +
                -parseInt(_0x3d66ef(0x2de)) +
                parseInt(_0x3d66ef(0x2dc)) +
                parseInt(_0x3d66ef(0x208)) * -parseInt(_0x3d66ef(0x305)) +
                parseInt(_0x3d66ef(0x2c8)) +
                parseInt(_0x3d66ef(0x1aa));
            if (_0x11f026 === _0x27d8e4) break;
            else _0x162b39['push'](_0x162b39['shift']());
        } catch (_0x2135c8) {
            _0x162b39['push'](_0x162b39['shift']());
        }
    }
})(_0x83ea, 0xdc494);
if (Utils['isNwjs']() && Utils[_0x9a8c74(0x2d3)](_0x9a8c74(0x220))) {
    var label = _0x9a8c74(0x268),
        tier = tier || 0x0,
        dependencies = ['VisuMZ_2_DoodadsSystem'],
        pluginData = $plugins['filter'](function (_0x35c61a) {
            return _0x35c61a['status'] && _0x35c61a['description']['includes']('[' + label + ']');
        })[0x0];
    ((VisuMZ[label][_0x9a8c74(0x38a)] = VisuMZ[label]['Settings'] || {}),
        (VisuMZ[_0x9a8c74(0x2d8)] = function (_0x59f984, _0x4848e0) {
            const _0x2a07ee = _0x9a8c74;
            for (const _0x47d48e in _0x4848e0) {
                if (_0x47d48e[_0x2a07ee(0x79)](/(.*):(.*)/i)) {
                    const _0x25e20b = String(RegExp['$1']),
                        _0x2cce5d = String(RegExp['$2'])[_0x2a07ee(0x2a2)]()['trim']();
                    let _0x422df4, _0x29733e, _0x465cb8;
                    switch (_0x2cce5d) {
                        case 'NUM':
                            _0x422df4 =
                                _0x4848e0[_0x47d48e] !== '' ? Number(_0x4848e0[_0x47d48e]) : 0x0;
                            break;
                        case _0x2a07ee(0x2f5):
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](_0x5a7097 =>
                                    Number(_0x5a7097)
                                )));
                            break;
                        case _0x2a07ee(0x293):
                            _0x422df4 =
                                _0x4848e0[_0x47d48e] !== '' ? eval(_0x4848e0[_0x47d48e]) : null;
                            break;
                        case _0x2a07ee(0x118):
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](_0x3443ef =>
                                    eval(_0x3443ef)
                                )));
                            break;
                        case _0x2a07ee(0x290):
                            _0x422df4 =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : '';
                            break;
                        case _0x2a07ee(0x216):
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](_0x5f334a =>
                                    JSON[_0x2a07ee(0x22f)](_0x5f334a)
                                )));
                            break;
                        case _0x2a07ee(0x37c):
                            _0x422df4 =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? new Function(JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e]))
                                    : new Function(_0x2a07ee(0x16e));
                            break;
                        case 'ARRAYFUNC':
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON['parse'](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](
                                    _0x399a06 => new Function(JSON['parse'](_0x399a06))
                                )));
                            break;
                        case _0x2a07ee(0x18e):
                            _0x422df4 =
                                _0x4848e0[_0x47d48e] !== '' ? String(_0x4848e0[_0x47d48e]) : '';
                            break;
                        case _0x2a07ee(0x253):
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](_0x24d82b =>
                                    String(_0x24d82b)
                                )));
                            break;
                        case _0x2a07ee(0x1dd):
                            ((_0x465cb8 =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON['parse'](_0x4848e0[_0x47d48e])
                                    : {}),
                                (_0x422df4 = VisuMZ['ConvertParams']({}, _0x465cb8)));
                            break;
                        case _0x2a07ee(0x125):
                            ((_0x29733e =
                                _0x4848e0[_0x47d48e] !== ''
                                    ? JSON[_0x2a07ee(0x22f)](_0x4848e0[_0x47d48e])
                                    : []),
                                (_0x422df4 = _0x29733e[_0x2a07ee(0x395)](_0x3ffe8f =>
                                    VisuMZ[_0x2a07ee(0x2d8)]({}, JSON[_0x2a07ee(0x22f)](_0x3ffe8f))
                                )));
                            break;
                        default:
                            continue;
                    }
                    _0x59f984[_0x25e20b] = _0x422df4;
                }
            }
            return _0x59f984;
        }),
        (_0x20bc18 => {
            const _0x23d16f = _0x9a8c74,
                _0xaffdfb = _0x20bc18[_0x23d16f(0x27f)];
            for (const _0x3bc663 of dependencies) {
                if (!Imported[_0x3bc663]) {
                    (alert(_0x23d16f(0x2ae)['format'](_0xaffdfb, _0x3bc663)),
                        SceneManager[_0x23d16f(0x36b)]());
                    break;
                }
            }
            const _0x52f133 = _0x20bc18[_0x23d16f(0x160)];
            if (_0x52f133[_0x23d16f(0x79)](/\[Version[ ](.*?)\]/i)) {
                const _0x4f7fbd = Number(RegExp['$1']);
                _0x4f7fbd !== VisuMZ[label][_0x23d16f(0x398)] &&
                    (alert(_0x23d16f(0x23d)[_0x23d16f(0x11f)](_0xaffdfb, _0x4f7fbd)),
                    SceneManager[_0x23d16f(0x36b)]());
            }
            if (_0x52f133[_0x23d16f(0x79)](/\[Tier[ ](\d+)\]/i)) {
                const _0x542d19 = Number(RegExp['$1']);
                _0x542d19 < tier
                    ? (alert(_0x23d16f(0x8a)['format'](_0xaffdfb, _0x542d19, tier)),
                      SceneManager[_0x23d16f(0x36b)]())
                    : (tier = Math[_0x23d16f(0x2f7)](_0x542d19, tier));
            }
            VisuMZ[_0x23d16f(0x2d8)](VisuMZ[label]['Settings'], _0x20bc18[_0x23d16f(0x291)]);
        })(pluginData),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x13c)] = Graphics[_0x9a8c74(0x28f)]),
        (Graphics['_onKeyDown'] = function (_0x1a83d1) {
            const _0x3fe75d = _0x9a8c74;
            (VisuMZ[_0x3fe75d(0x268)]['Graphics_onKeyDown'][_0x3fe75d(0x355)](this, _0x1a83d1),
                !_0x1a83d1[_0x3fe75d(0x367)] &&
                    !_0x1a83d1[_0x3fe75d(0x247)] &&
                    DoodadManager[_0x3fe75d(0x100)](_0x1a83d1[_0x3fe75d(0x244)]));
        }),
        (VisuMZ[_0x9a8c74(0x268)]['TouchInput_onMouseMove'] = TouchInput[_0x9a8c74(0x394)]),
        (TouchInput[_0x9a8c74(0x394)] = function (_0x2ca8db) {
            const _0x46d00c = _0x9a8c74;
            (VisuMZ['DoodadsEditor']['TouchInput_onMouseMove'][_0x46d00c(0x355)](this, _0x2ca8db),
                (this[_0x46d00c(0xb6)] = Graphics[_0x46d00c(0x180)](_0x2ca8db[_0x46d00c(0x1c4)])),
                (this[_0x46d00c(0xda)] = Graphics[_0x46d00c(0x1ee)](_0x2ca8db[_0x46d00c(0x375)])));
        }),
        (StorageManager['saveDoodadSettings'] = function () {
            const _0xbdce5c = _0x9a8c74,
                _0x3f1c35 = JSON[_0xbdce5c(0x18b)]($dataDoodads, null, 0x2),
                _0x3f91ff = require('fs'),
                _0x421ed4 = require(_0xbdce5c(0x2ed)),
                _0x17f4d6 = _0x421ed4[_0xbdce5c(0x8c)](process['mainModule'][_0xbdce5c(0x214)]),
                _0x23aed2 = _0x421ed4[_0xbdce5c(0x329)](_0x17f4d6, _0xbdce5c(0x128));
            !_0x3f91ff[_0xbdce5c(0xb4)](_0x23aed2) && _0x3f91ff[_0xbdce5c(0xaa)](_0x23aed2);
            const _0x9785de = _0x421ed4[_0xbdce5c(0x329)](_0x23aed2, 'Doodads.json');
            _0x3f91ff[_0xbdce5c(0x183)](_0x9785de, _0x3f1c35);
        }),
        (ColorManager[_0x9a8c74(0x2c1)] = function (_0x491286, _0x470a33, _0x45dbcc) {
            const _0x48d0f0 = _0x9a8c74;
            ((_0x470a33 /= 0x64), (_0x45dbcc /= 0x64));
            let _0x13f167 = (0x1 - Math[_0x48d0f0(0x19c)](0x2 * _0x45dbcc - 0x1)) * _0x470a33,
                _0x1d3778 = _0x13f167 * (0x1 - Math['abs'](((_0x491286 / 0x3c) % 0x2) - 0x1)),
                _0x2bc69e = _0x45dbcc - _0x13f167 / 0x2,
                _0x39ed76 = 0x0,
                _0x43e4df = 0x0,
                _0x4658ed = 0x0;
            if (0x0 <= _0x491286 && _0x491286 < 0x3c)
                ((_0x39ed76 = _0x13f167), (_0x43e4df = _0x1d3778), (_0x4658ed = 0x0));
            else {
                if (0x3c <= _0x491286 && _0x491286 < 0x78)
                    ((_0x39ed76 = _0x1d3778), (_0x43e4df = _0x13f167), (_0x4658ed = 0x0));
                else {
                    if (0x78 <= _0x491286 && _0x491286 < 0xb4)
                        ((_0x39ed76 = 0x0), (_0x43e4df = _0x13f167), (_0x4658ed = _0x1d3778));
                    else {
                        if (0xb4 <= _0x491286 && _0x491286 < 0xf0)
                            ((_0x39ed76 = 0x0), (_0x43e4df = _0x1d3778), (_0x4658ed = _0x13f167));
                        else {
                            if (0xf0 <= _0x491286 && _0x491286 < 0x12c)
                                ((_0x39ed76 = _0x1d3778),
                                    (_0x43e4df = 0x0),
                                    (_0x4658ed = _0x13f167));
                            else
                                0x12c <= _0x491286 &&
                                    _0x491286 < 0x168 &&
                                    ((_0x39ed76 = _0x13f167),
                                    (_0x43e4df = 0x0),
                                    (_0x4658ed = _0x1d3778));
                        }
                    }
                }
            }
            return (
                (_0x39ed76 = Math[_0x48d0f0(0x102)]((_0x39ed76 + _0x2bc69e) * 0xff)),
                (_0x43e4df = Math['round']((_0x43e4df + _0x2bc69e) * 0xff)),
                (_0x4658ed = Math[_0x48d0f0(0x102)]((_0x4658ed + _0x2bc69e) * 0xff)),
                [_0x39ed76, _0x43e4df, _0x4658ed]
            );
        }),
        (ColorManager[_0x9a8c74(0x191)] = function (_0x5ab84c) {
            const _0x1a3eb8 = _0x9a8c74,
                _0x21c319 = _0x5ab84c[0x0],
                _0x5db404 = _0x5ab84c[0x1],
                _0x35d6a4 = _0x5ab84c[0x2];
            return _0x1a3eb8(0x9f) + _0x21c319 + ',' + _0x5db404 + ',' + _0x35d6a4 + ')';
        }),
        (Doodads[_0x9a8c74(0x322)] = [0x0, 0.1, 0.2, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8]),
        (Doodads['blendModes'] = [
            { name: 'Normal', enabled: !![] },
            { name: _0x9a8c74(0x310), enabled: !![] },
            { name: _0x9a8c74(0x350), enabled: !![] },
            { name: _0x9a8c74(0x14d), enabled: !![] },
            { name: 'Overlay', enabled: ![] },
            { name: _0x9a8c74(0x224), enabled: ![] },
            { name: _0x9a8c74(0x174), enabled: ![] },
            { name: _0x9a8c74(0x107), enabled: ![] },
            { name: _0x9a8c74(0x391), enabled: ![] },
            { name: _0x9a8c74(0x312), enabled: ![] },
            { name: _0x9a8c74(0x2a0), enabled: ![] },
            { name: _0x9a8c74(0x245), enabled: ![] },
            { name: _0x9a8c74(0x3d0), enabled: ![] },
            { name: _0x9a8c74(0x2b3), enabled: ![] },
            { name: _0x9a8c74(0x232), enabled: ![] },
            { name: _0x9a8c74(0x372), enabled: ![] },
            { name: _0x9a8c74(0x1f6), enabled: ![] },
            { name: 'Normal\x20NPM', enabled: ![] },
            { name: _0x9a8c74(0x368), enabled: ![] },
            { name: 'Screen\x20NPM', enabled: ![] },
            { name: _0x9a8c74(0x1a2), enabled: ![] },
            { name: _0x9a8c74(0x27c), enabled: ![] },
            { name: _0x9a8c74(0x25f), enabled: ![] },
            { name: _0x9a8c74(0x3bc), enabled: ![] },
            { name: _0x9a8c74(0x184), enabled: ![] },
            { name: _0x9a8c74(0x26c), enabled: ![] },
            { name: _0x9a8c74(0x3b4), enabled: ![] },
            { name: _0x9a8c74(0xec), enabled: ![] },
            { name: 'Subtract', enabled: ![] },
            { name: 'Xor', enabled: ![] },
        ]),
        (Doodads[_0x9a8c74(0x25a)] = [
            _0x9a8c74(0x3bb),
            _0x9a8c74(0x204),
            _0x9a8c74(0x3c8),
            _0x9a8c74(0x387),
            _0x9a8c74(0x228),
            _0x9a8c74(0x17c),
            _0x9a8c74(0x7b),
            '#00bff3',
            '#0072bc',
            _0x9a8c74(0x20a),
            '#a864a8',
            _0x9a8c74(0x334),
        ]),
        (Doodads[_0x9a8c74(0x2c0)] = [
            'Lowest(0)',
            _0x9a8c74(0x321),
            _0x9a8c74(0x120),
            _0x9a8c74(0xdc),
            'Under(4)',
            _0x9a8c74(0x82),
            _0x9a8c74(0x2b2),
            _0x9a8c74(0x3f2),
            _0x9a8c74(0xd6),
            _0x9a8c74(0x1c6),
            _0x9a8c74(0x403),
        ]),
        (Doodads['layerTitles'] = [
            _0x9a8c74(0x29d),
            _0x9a8c74(0xd0),
            '\x202.\x20Low\x20Layer',
            _0x9a8c74(0x343),
            '\x204.\x20Under\x20Layer',
            _0x9a8c74(0x3dd),
            _0x9a8c74(0x36a),
            '\x207.\x20Above\x20Characters',
            _0x9a8c74(0x264),
            _0x9a8c74(0x149),
            _0x9a8c74(0x1f4),
        ]),
        (Doodads['skippedSettingSymbols'] = [
            _0x9a8c74(0x333),
            _0x9a8c74(0x325),
            'accept',
            _0x9a8c74(0xb3),
        ]),
        (Doodads['makeTileList'] = (_0x5585e9, _0xb08eb9) => {
            const _0x46a9cf = _0x9a8c74,
                _0x4ecc94 = [];
            for (
                let _0x54ba8b = 0x0;
                _0x54ba8b < (_0xb08eb9 - _0x5585e9) / 0x10;
                _0x54ba8b += 0x1
            ) {
                for (let _0x45ddc0 = 0x0; _0x45ddc0 < 0x8; _0x45ddc0++) {
                    _0x4ecc94[_0x46a9cf(0x1cc)](_0x54ba8b * 0x8 + _0x45ddc0 + _0x5585e9);
                }
                for (let _0x12baaa = 0x0; _0x12baaa < 0x8; _0x12baaa++) {
                    _0x4ecc94[_0x46a9cf(0x1cc)](
                        _0x54ba8b * 0x8 + _0x12baaa + (_0xb08eb9 - _0x5585e9) / 0x2 + _0x5585e9
                    );
                }
            }
            return _0x4ecc94;
        }),
        (Doodads[_0x9a8c74(0xd9)] = Object[_0x9a8c74(0x119)](
            Doodads[_0x9a8c74(0x34a)](Tilemap[_0x9a8c74(0x140)], Tilemap[_0x9a8c74(0x22e)])
        )),
        (Doodads[_0x9a8c74(0x3b3)] = Object[_0x9a8c74(0x119)](
            Doodads[_0x9a8c74(0x34a)](Tilemap[_0x9a8c74(0x22e)], Tilemap[_0x9a8c74(0x1b4)])
        )),
        (Doodads[_0x9a8c74(0x3a0)] = Object[_0x9a8c74(0x119)](
            Doodads[_0x9a8c74(0x34a)](Tilemap[_0x9a8c74(0x1b4)], Tilemap[_0x9a8c74(0x20f)])
        )),
        (Doodads[_0x9a8c74(0x2fb)] = Object[_0x9a8c74(0x119)](
            Doodads[_0x9a8c74(0x34a)](Tilemap[_0x9a8c74(0x20f)], Tilemap[_0x9a8c74(0x19d)])
        )),
        (Doodads[_0x9a8c74(0x215)] = [
            Doodads['bTiles'],
            Doodads[_0x9a8c74(0x3b3)],
            Doodads[_0x9a8c74(0x3a0)],
            Doodads[_0x9a8c74(0x2fb)],
        ]),
        (Doodads['toneColors'] = {
            Normal: { red: 0x0, green: 0x0, blue: 0x0, grey: 0x0 },
            Grey: { red: 0x0, green: 0x0, blue: 0x0, grey: 0xff },
            Red: { red: 0xff, green: 0x0, blue: 0x0, grey: 0xff },
            Orange: { red: 0xff, green: 0x40, blue: 0x0, grey: 0xff },
            Yellow: { red: 0xff, green: 0xff, blue: 0x0, grey: 0xff },
            Lime: { red: 0x44, green: 0xff, blue: 0x0, grey: 0xff },
            Green: { red: 0x0, green: 0xff, blue: 0x0, grey: 0xff },
            Turquoise: { red: 0x0, green: 0xff, blue: 0x44, grey: 0xff },
            Cyan: { red: 0x0, green: 0xff, blue: 0xff, grey: 0xff },
            Sky: { red: 0x0, green: 0x44, blue: 0xff, grey: 0xff },
            Blue: { red: 0x0, green: 0x0, blue: 0xff, grey: 0xff },
            Purple: { red: 0x44, green: 0x0, blue: 0xff, grey: 0xff },
            Magenta: { red: 0xff, green: 0x0, blue: 0xff, grey: 0xff },
            Pink: { red: 0xff, green: 0x0, blue: 0x44, grey: 0xff },
            Dark: { red: -0x44, green: -0x44, blue: -0x44, grey: 0x0 },
            Sepia: { red: 0x22, green: -0x22, blue: -0x44, grey: 0xaa },
            Sunset: { red: 0x44, green: -0x22, blue: -0x22, grey: 0x0 },
            Night: { red: -0x44, green: -0x44, blue: 0x0, grey: 0x44 },
        }),
        (VisuMZ[_0x9a8c74(0x229)] = function (_0x209d6b, _0x48d217) {
            const _0x53a864 = _0x9a8c74;
            return Math['floor'](
                Math[_0x53a864(0x226)]() * (_0x48d217 - _0x209d6b + 0x1) + _0x209d6b
            );
        }),
        (Doodads['canvasModeKeys'] = {
            0x51: _0x9a8c74(0x251),
            0x45: _0x9a8c74(0x1cb),
            0x47: _0x9a8c74(0x3e4),
            0x54: _0x9a8c74(0x3e8),
            0x57: _0x9a8c74(0xff),
            0x41: _0x9a8c74(0x9e),
            0x53: _0x9a8c74(0x166),
            0x44: _0x9a8c74(0x23c),
            0x26: 'Up',
            0x25: _0x9a8c74(0x271),
            0x28: 'Down',
            0x27: _0x9a8c74(0x17d),
            0x48: 'Hide',
            0x5a: _0x9a8c74(0x2ee),
            0x58: _0x9a8c74(0x152),
            0x1b: _0x9a8c74(0x152),
            0x55: _0x9a8c74(0x2b4),
            0x4f: _0x9a8c74(0x188),
            0x49: _0x9a8c74(0x7e),
            0x4a: _0x9a8c74(0x104),
            0x4b: 'GrowRows',
            0x4c: 'GrowCols',
            0x31: '1',
            0x32: '2',
            0x33: '3',
            0x34: '4',
            0x35: '5',
            0x36: '6',
            0x37: '7',
            0x38: '8',
            0x39: '9',
            0x30: '0',
        }));
    function DoodadManager() {
        const _0x4adf0f = _0x9a8c74;
        throw new Error(_0x4adf0f(0x256));
    }
    (Object[_0x9a8c74(0x376)](DoodadManager, _0x9a8c74(0x388), {
        value: VisuMZ['DoodadsEditor'][_0x9a8c74(0x38a)],
        configurable: ![],
    }),
        (DoodadManager[_0x9a8c74(0x131)] = function () {
            return $gameMap['mapId']();
        }),
        (DoodadManager[_0x9a8c74(0x354)] = function (_0x55f908) {
            const _0x1efe3a = _0x9a8c74;
            (($dataDoodads[this['mapId']()] = $dataDoodads[this['mapId']()] || []),
                $dataDoodads[this[_0x1efe3a(0x131)]()][_0x1efe3a(0x1cc)](_0x55f908),
                this[_0x1efe3a(0x18c)]($dataDoodads[this['mapId']()]),
                this[_0x1efe3a(0xa8)]());
        }),
        (DoodadManager[_0x9a8c74(0x333)] = function (_0x328ced) {
            const _0x1c3c52 = _0x9a8c74;
            $dataDoodads[this['mapId']()] = $dataDoodads[this['mapId']()] || [];
            const _0x2b5b4e = $dataDoodads[this[_0x1c3c52(0x131)]()][_0x1c3c52(0x3d1)](_0x328ced);
            (_0x2b5b4e >= 0x0 && $dataDoodads[this['mapId']()][_0x1c3c52(0x3b1)](_0x2b5b4e, 0x1),
                this[_0x1c3c52(0x18c)]($dataDoodads[this[_0x1c3c52(0x131)]()]),
                this[_0x1c3c52(0xa8)]());
        }),
        (DoodadManager['sortDoodads'] = function (_0x255c19) {
            const _0x290b63 = _0x9a8c74;
            _0x255c19[_0x290b63(0x3f7)]((_0x20639d, _0x58f835) => {
                if (_0x20639d['z'] !== _0x58f835['z']) return _0x58f835['z'] - _0x20639d['z'];
                else
                    return _0x20639d['y'] !== _0x58f835['y']
                        ? _0x20639d['y'] - _0x58f835['y']
                        : _0x20639d['x'] - _0x58f835['x'];
            });
        }),
        (DoodadManager[_0x9a8c74(0x32b)] = function (_0x42b73d, _0x2c95b6) {
            const _0x15fca6 = _0x9a8c74;
            return {
                folder: _0x42b73d || '',
                bitmap: _0x2c95b6 || '',
                hue: 0x0,
                x: 0x0,
                y: 0x0,
                z: 0x3,
                iconIndex: 0x0,
                tileId: 0x0,
                tileCols: 0x1,
                tileRows: 0x1,
                anchorX: 0.5,
                anchorY: 0x1,
                scaleX: 0x64,
                scaleY: 0x64,
                blend: 0x0,
                opacity: 0xff,
                xFrames: 0x1,
                yFrames: 0x1,
                frameUpdate: 0x14,
                smooth: this['settings'][_0x15fca6(0x39b)],
                positionType: 'map',
                angle: 0x0,
                blur: ![],
                contrast: ![],
                sepia: ![],
                outline: ![],
                shadow: ![],
                glow: ![],
            };
        }),
        (DoodadManager['compareColors'] = function (
            _0x15084c,
            _0x9d7e70,
            _0x14b24e,
            _0x2f0b15,
            _0x11636a
        ) {
            const _0x5143d6 = _0x9a8c74;
            return (
                _0x15084c[_0x5143d6(0x340)] === _0x9d7e70 &&
                _0x15084c['green'] === _0x14b24e &&
                _0x15084c[_0x5143d6(0x288)] === _0x2f0b15 &&
                _0x15084c[_0x5143d6(0x3a6)] === _0x11636a
            );
        }),
        (DoodadManager['refresh'] = function () {
            const _0x27ebed = _0x9a8c74,
                _0x34329d = SceneManager[_0x27ebed(0x3cd)];
            _0x34329d &&
                (_0x34329d[_0x27ebed(0x2e6)][_0x27ebed(0x2f1)](),
                _0x34329d[_0x27ebed(0x2e6)][_0x27ebed(0x33f)]());
        }),
        (DoodadManager[_0x9a8c74(0x2d2)] = function () {
            const _0x2e5adb = _0x9a8c74;
            (($dataDoodads[this[_0x2e5adb(0x131)]()] = []), this[_0x2e5adb(0xa8)]());
        }),
        (DoodadManager[_0x9a8c74(0x34e)] = function () {
            const _0xcdb109 = _0x9a8c74;
            if (!SceneManager[_0xcdb109(0x3cd)]['isMap']()) return;
            if ($gameTemp[_0xcdb109(0x136)]) return;
            if (SceneManager[_0xcdb109(0x3cd)][_0xcdb109(0x182)]) return;
            (($gameTemp[_0xcdb109(0x136)] = !$gameTemp[_0xcdb109(0x136)]),
                SceneManager[_0xcdb109(0x3cd)][_0xcdb109(0x3d7)]());
        }),
        (DoodadManager[_0x9a8c74(0xe0)] = function (_0x2a4769) {
            const _0x2be275 = _0x9a8c74;
            (_0x2a4769 &&
                ((this['_manualMove'] = ![]),
                this['setGridLockMode'](this[_0x2be275(0x388)][_0x2be275(0x31d)])),
                (this['_canvasMode'] = _0x2a4769));
        }),
        (DoodadManager[_0x9a8c74(0xd3)] = function (_0x3f9e9b) {
            const _0x4a2a94 = _0x9a8c74;
            (_0x3f9e9b && this[_0x4a2a94(0x25d)](), (this['_manualMove'] = _0x3f9e9b));
        }),
        (DoodadManager[_0x9a8c74(0x255)] = function (_0x2507db) {
            const _0x4df55a = _0x9a8c74;
            (_0x2507db && this['setManualMove'](![]), (this[_0x4df55a(0x1bf)] = _0x2507db));
        }),
        (DoodadManager[_0x9a8c74(0x322)] = function () {
            const _0x54e695 = _0x9a8c74;
            return Doodads[_0x54e695(0x322)];
        }),
        (DoodadManager[_0x9a8c74(0x1fa)] = function () {
            const _0x30a595 = _0x9a8c74;
            return Doodads[_0x30a595(0x2c0)];
        }),
        (DoodadManager[_0x9a8c74(0x1d3)] = function () {
            const _0x411ad3 = _0x9a8c74;
            return Doodads[_0x411ad3(0x1d3)];
        }),
        (DoodadManager[_0x9a8c74(0x30e)] = function () {
            const _0x306908 = _0x9a8c74;
            return Doodads[_0x306908(0x30e)];
        }),
        (DoodadManager[_0x9a8c74(0x8f)] = function () {
            const _0x395b2b = _0x9a8c74;
            return SceneManager[_0x395b2b(0x3cd)][_0x395b2b(0x8e)];
        }),
        (DoodadManager[_0x9a8c74(0x25c)] = function () {
            const _0x395edd = _0x9a8c74;
            return { ...SceneManager['_scene'][_0x395edd(0x8e)] };
        }),
        (DoodadManager[_0x9a8c74(0x25d)] = function () {
            const _0x463118 = _0x9a8c74;
            if (this[_0x463118(0x295)] !== ![]) return;
            ((this[_0x463118(0x13a)] =
                TouchInput[_0x463118(0xb6)] || Math[_0x463118(0x223)](Graphics['width'] / 0x2)),
                (this[_0x463118(0x2fa)] =
                    TouchInput[_0x463118(0xda)] ||
                    Math[_0x463118(0x223)](Graphics[_0x463118(0x158)] / 0x2)));
        }),
        (DoodadManager[_0x9a8c74(0x16c)] = function () {
            const _0x2ccdf6 = _0x9a8c74;
            (SceneManager[_0x2ccdf6(0x3cd)]['_spriteset'][_0x2ccdf6(0xd7)] &&
                SceneManager[_0x2ccdf6(0x3cd)][_0x2ccdf6(0x2e6)][_0x2ccdf6(0xd7)][
                    _0x2ccdf6(0x35e)
                ](),
                SceneManager[_0x2ccdf6(0x3cd)][_0x2ccdf6(0x2e6)][_0x2ccdf6(0x33f)]());
        }),
        (DoodadManager[_0x9a8c74(0x373)] = function (_0x758c5f) {
            const _0xd0377e = _0x9a8c74;
            if (_0x758c5f[_0xd0377e(0x79)](/\[(\d+)x(\d+)\]/i)) return parseInt(RegExp['$1']);
            return 0x1;
        }),
        (DoodadManager['getYFrames'] = function (_0x26006d) {
            const _0x239d50 = _0x9a8c74;
            if (_0x26006d[_0x239d50(0x79)](/\[(\d+)x(\d+)\]/i)) return parseInt(RegExp['$2']);
            return 0x1;
        }),
        (DoodadManager[_0x9a8c74(0x277)] = function () {
            const _0x5b125a = _0x9a8c74;
            this[_0x5b125a(0xa2)] ? this[_0x5b125a(0x16c)]() : this['refresh']();
        }),
        (DoodadManager[_0x9a8c74(0x2ff)] = function (_0x1e2228, _0x2f6633) {
            const _0xdcb44c = _0x9a8c74;
            Object[_0xdcb44c(0x10f)](_0x1e2228, _0x2f6633);
        }),
        (DoodadManager[_0x9a8c74(0xaf)] = function (_0x485d37) {
            this['_gridLockMode'] = _0x485d37;
        }),
        (DoodadManager[_0x9a8c74(0x11c)] = function () {
            const _0x1b55b0 = _0x9a8c74;
            return (
                (this['_gridLockX'] =
                    this[_0x1b55b0(0x2a4)] || this[_0x1b55b0(0x388)][_0x1b55b0(0x238)]),
                this[_0x1b55b0(0x2a4)]
            );
        }),
        (DoodadManager['gridLockY'] = function () {
            const _0x130da7 = _0x9a8c74;
            return (
                (this['_gridLockY'] =
                    this[_0x130da7(0x3c5)] || this[_0x130da7(0x388)]['GFDGridHeight']),
                this['_gridLockY']
            );
        }),
        (DoodadManager[_0x9a8c74(0x261)] = function (_0x4418f8) {
            const _0x54283 = _0x9a8c74;
            this[_0x54283(0x1da)] = _0x4418f8;
        }),
        (DoodadManager[_0x9a8c74(0x100)] = function (_0x434e75) {
            const _0x36dcc2 = _0x9a8c74;
            if (!$gameTemp) return;
            if (_0x434e75 === 0x79) {
                this[_0x36dcc2(0x34e)]();
                return;
            }
            if ($gameTemp[_0x36dcc2(0x136)] && _0x434e75 === 0x52) {
                SceneManager[_0x36dcc2(0x3cd)]['_spriteset'][_0x36dcc2(0x2b8)]();
                return;
            }
            this[_0x36dcc2(0x1db)](_0x434e75);
        }),
        (DoodadManager['updateCurrentMode'] = function (_0x379799) {
            const _0x2fb239 = _0x9a8c74;
            if (this['_canvasMode'] && this[_0x2fb239(0x1bf)] && this[_0x2fb239(0x1da)]) {
                if (SceneManager['_scene'][_0x2fb239(0x33a)]['active']) return;
                (this[_0x2fb239(0x374)](_0x379799), this['updateCurrentDoodad']());
                return;
            }
            if (this[_0x2fb239(0x1bf)]) {
                this[_0x2fb239(0x1dc)](_0x379799);
                return;
            }
            if (this[_0x2fb239(0xa2)]) {
                if (SceneManager[_0x2fb239(0x3cd)]['_gfdGridMenuWindow'][_0x2fb239(0xcc)]) return;
                (this['updateCanvasMode'](_0x379799), this['updateCurrentDoodad']());
                return;
            }
            this[_0x2fb239(0x1da)] && this['updateEditMode'](_0x379799);
        }),
        (DoodadManager[_0x9a8c74(0x39a)] = function () {
            const _0x174c10 = _0x9a8c74,
                _0x4f0a39 = this[_0x174c10(0x322)]()[_0x174c10(0x3d1)](this['current']()['z']),
                _0x2da7ae = (this[_0x174c10(0x322)]()[_0x174c10(0x3d1)](this['current']()['z']) -
                    0x1)[_0x174c10(0x175)](0x0, 0xa);
            if (_0x4f0a39 !== _0x2da7ae) SoundManager[_0x174c10(0x314)]();
            ((this[_0x174c10(0x8f)]()['z'] = this[_0x174c10(0x322)]()[_0x2da7ae]),
                SceneManager[_0x174c10(0x3cd)][_0x174c10(0x1c3)][_0x174c10(0xa8)]());
        }),
        (DoodadManager[_0x9a8c74(0x81)] = function () {
            const _0x4f14b9 = _0x9a8c74,
                _0x10112c = this[_0x4f14b9(0x322)]()['indexOf'](this[_0x4f14b9(0x8f)]()['z']),
                _0x37a6cc = (this[_0x4f14b9(0x322)]()[_0x4f14b9(0x3d1)](this['current']()['z']) +
                    0x1)['clamp'](0x0, 0xa);
            (_0x10112c !== _0x37a6cc && SoundManager[_0x4f14b9(0x314)](),
                (this[_0x4f14b9(0x8f)]()['z'] = this[_0x4f14b9(0x322)]()[_0x37a6cc]),
                SceneManager[_0x4f14b9(0x3cd)][_0x4f14b9(0x1c3)][_0x4f14b9(0xa8)]());
        }),
        (DoodadManager[_0x9a8c74(0x38c)] = function () {
            const _0x37faea = _0x9a8c74;
            (SoundManager[_0x37faea(0x38e)](),
                SceneManager[_0x37faea(0x3cd)][_0x37faea(0x24a)](DoodadManager[_0x37faea(0x8f)]()));
        }),
        (DoodadManager['performCanvasAction_GridSettings'] = function () {
            const _0x21cd85 = _0x9a8c74;
            (SoundManager[_0x21cd85(0x38e)](), SceneManager['_scene'][_0x21cd85(0x361)]());
        }),
        (DoodadManager[_0x9a8c74(0x324)] = function () {
            const _0x271e3e = _0x9a8c74;
            !$gameMap[_0x271e3e(0x1b0)]() && $gameMap[_0x271e3e(0x326)](0x8, 0x1, 0x6);
        }),
        (DoodadManager['performCanvasAction_ScreenDown'] = function () {
            const _0x13c6bb = _0x9a8c74;
            !$gameMap[_0x13c6bb(0x1b0)]() && $gameMap[_0x13c6bb(0x326)](0x2, 0x1, 0x6);
        }),
        (DoodadManager[_0x9a8c74(0x3d2)] = function () {
            const _0x3571b1 = _0x9a8c74;
            !$gameMap[_0x3571b1(0x1b0)]() && $gameMap['startScroll'](0x4, 0x1, 0x6);
        }),
        (DoodadManager[_0x9a8c74(0x369)] = function () {
            const _0x567336 = _0x9a8c74;
            !$gameMap[_0x567336(0x1b0)]() && $gameMap[_0x567336(0x326)](0x6, 0x1, 0x6);
        }),
        (DoodadManager[_0x9a8c74(0xe8)] = function () {
            const _0x3b4080 = _0x9a8c74,
                _0x10de20 = this[_0x3b4080(0x8f)]();
            if (!_0x10de20) return;
            DoodadManager[_0x3b4080(0xea)](-0x1, _0x10de20);
        }),
        (DoodadManager[_0x9a8c74(0x95)] = function () {
            const _0x14ab25 = _0x9a8c74,
                _0x248989 = this[_0x14ab25(0x8f)]();
            if (!_0x248989) return;
            DoodadManager['changeSetting_tileId'](0x1, _0x248989);
        }),
        (DoodadManager['performCanvasAction_ShrinkRows'] = function () {
            const _0x1077df = _0x9a8c74,
                _0x210291 = this[_0x1077df(0x8f)]();
            if (!_0x210291) return;
            DoodadManager['changeSetting_tileRows'](-0x1, _0x210291);
        }),
        (DoodadManager['performCanvasAction_ShrinkCols'] = function () {
            const _0x20c5c9 = _0x9a8c74,
                _0x2f2638 = this[_0x20c5c9(0x8f)]();
            if (!_0x2f2638) return;
            DoodadManager['changeSetting_tileCols'](-0x1, _0x2f2638);
        }),
        (DoodadManager[_0x9a8c74(0x400)] = function () {
            const _0x5b8f3e = _0x9a8c74,
                _0x425d16 = this[_0x5b8f3e(0x8f)]();
            if (!_0x425d16) return;
            DoodadManager[_0x5b8f3e(0x123)](0x1, _0x425d16);
        }),
        (DoodadManager['performCanvasAction_GrowCols'] = function () {
            const _0xb88fa5 = _0x9a8c74,
                _0xffd80d = this['current']();
            if (!_0xffd80d) return;
            DoodadManager[_0xb88fa5(0x29b)](0x1, _0xffd80d);
        }),
        (DoodadManager[_0x9a8c74(0xb5)] = function () {
            const _0x5145f9 = _0x9a8c74;
            this[_0x5145f9(0xd3)](!![]);
            const _0x3bb271 = Input['isPressed'](_0x5145f9(0x9d)) ? 0xa : 0x1;
            ((this[_0x5145f9(0x2fa)] -= DoodadManager[_0x5145f9(0x237)]
                ? DoodadManager[_0x5145f9(0x3c5)]
                : _0x3bb271),
                this['adjustManualMove']());
        }),
        (DoodadManager[_0x9a8c74(0x18a)] = function () {
            const _0x400499 = _0x9a8c74;
            this[_0x400499(0xd3)](!![]);
            const _0x256a9c = Input[_0x400499(0x313)](_0x400499(0x9d)) ? 0xa : 0x1;
            ((this[_0x400499(0x2fa)] += DoodadManager[_0x400499(0x237)]
                ? DoodadManager[_0x400499(0x3c5)]
                : _0x256a9c),
                this[_0x400499(0x3f0)]());
        }),
        (DoodadManager['performCanvasAction_Left'] = function () {
            const _0x25e739 = _0x9a8c74;
            this[_0x25e739(0xd3)](!![]);
            const _0x46aa5e = Input[_0x25e739(0x313)](_0x25e739(0x9d)) ? 0xa : 0x1;
            ((this['_manualX'] -= DoodadManager['_gridLockMode']
                ? DoodadManager[_0x25e739(0x2a4)]
                : _0x46aa5e),
                this[_0x25e739(0x3f0)]());
        }),
        (DoodadManager[_0x9a8c74(0x14b)] = function () {
            const _0x99f04d = _0x9a8c74;
            this[_0x99f04d(0xd3)](!![]);
            const _0x2563da = Input[_0x99f04d(0x313)](_0x99f04d(0x9d)) ? 0xa : 0x1;
            ((this[_0x99f04d(0x13a)] += DoodadManager['_gridLockMode']
                ? DoodadManager[_0x99f04d(0x2a4)]
                : _0x2563da),
                this[_0x99f04d(0x3f0)]());
        }),
        (DoodadManager[_0x9a8c74(0x3c6)] = function () {
            const _0x5ae0f9 = _0x9a8c74;
            SoundManager[_0x5ae0f9(0x260)]();
            const _0xf7b285 = DoodadManager[_0x5ae0f9(0x25c)]();
            ((_0xf7b285['x'] =
                SceneManager[_0x5ae0f9(0x3cd)][_0x5ae0f9(0x1c3)][_0x5ae0f9(0x1a1)]()),
                (_0xf7b285['y'] =
                    SceneManager[_0x5ae0f9(0x3cd)][_0x5ae0f9(0x1c3)][_0x5ae0f9(0x315)]()),
                DoodadManager[_0x5ae0f9(0x354)](_0xf7b285));
        }),
        (DoodadManager[_0x9a8c74(0x12a)] = function () {
            const _0x5b473e = _0x9a8c74;
            (SoundManager[_0x5b473e(0x153)](), SceneManager[_0x5b473e(0x3cd)][_0x5b473e(0x2fe)]());
        }),
        (DoodadManager[_0x9a8c74(0x99)] = function () {
            const _0x5ab224 = _0x9a8c74;
            (SoundManager[_0x5ab224(0x38e)](),
                (SceneManager[_0x5ab224(0x3cd)][_0x5ab224(0x1c3)][_0x5ab224(0x150)] =
                    !SceneManager[_0x5ab224(0x3cd)]['_gfdCanvasWindow']['visible']));
        }),
        (DoodadManager[_0x9a8c74(0x127)] = function (_0x2d0537) {
            const _0x23adbd = _0x9a8c74;
            (SoundManager[_0x23adbd(0x153)](),
                _0x2d0537 === 0x0
                    ? (this[_0x23adbd(0x8f)]()[_0x23adbd(0x7d)] = 0xff)
                    : (this['current']()['opacity'] = Math[_0x23adbd(0x223)](
                          0xff * 0.1 * _0x2d0537
                      )));
        }),
        (DoodadManager[_0x9a8c74(0x3fb)] = function () {
            this['performCanvasAction_ChangeOpacity'](0x1);
        }),
        (DoodadManager[_0x9a8c74(0x115)] = function () {
            const _0x34bbb7 = _0x9a8c74;
            this[_0x34bbb7(0x127)](0x2);
        }),
        (DoodadManager[_0x9a8c74(0x116)] = function () {
            const _0x44e736 = _0x9a8c74;
            this[_0x44e736(0x127)](0x3);
        }),
        (DoodadManager[_0x9a8c74(0x3d4)] = function () {
            this['performCanvasAction_ChangeOpacity'](0x4);
        }),
        (DoodadManager[_0x9a8c74(0x3ac)] = function () {
            const _0x5359d6 = _0x9a8c74;
            this[_0x5359d6(0x127)](0x5);
        }),
        (DoodadManager[_0x9a8c74(0x2ba)] = function () {
            const _0x4ee7a7 = _0x9a8c74;
            this[_0x4ee7a7(0x127)](0x6);
        }),
        (DoodadManager[_0x9a8c74(0x139)] = function () {
            const _0x3adf8b = _0x9a8c74;
            this[_0x3adf8b(0x127)](0x7);
        }),
        (DoodadManager[_0x9a8c74(0x3fc)] = function () {
            const _0x12b587 = _0x9a8c74;
            this[_0x12b587(0x127)](0x8);
        }),
        (DoodadManager['performCanvasAction_9'] = function () {
            this['performCanvasAction_ChangeOpacity'](0x9);
        }),
        (DoodadManager[_0x9a8c74(0xe3)] = function () {
            this['performCanvasAction_ChangeOpacity'](0x0);
        }),
        (DoodadManager[_0x9a8c74(0x32a)] = function (_0x1ee029) {
            const _0x3f924a = _0x9a8c74,
                _0x4c494d = Doodads['canvasModeKeys'][_0x1ee029];
            if (!_0x4c494d) return;
            const _0x2dee73 = _0x3f924a(0x331) + _0x4c494d;
            this[_0x2dee73] && this[_0x2dee73]();
        }),
        (DoodadManager[_0x9a8c74(0x3e7)] = function (_0x16227d, _0x265595) {
            const _0x4841a8 = _0x9a8c74,
                _0x8d0871 = this['layerList']();
            _0x16227d /= Math[_0x4841a8(0x19c)](_0x16227d);
            const _0x28eb53 = (_0x8d0871['indexOf'](_0x265595['z']) + _0x16227d)[_0x4841a8(0x175)](
                    0x0,
                    0xa
                ),
                _0x2dedfd = _0x8d0871[_0x28eb53];
            if (_0x265595['z'] === _0x2dedfd) return ![];
            _0x265595['z'] = _0x2dedfd;
        }),
        (DoodadManager[_0x9a8c74(0x30c)] = function (_0x1f514d, _0x50a9ba) {
            const _0x2b375e = _0x9a8c74,
                _0x33506d = (_0x50a9ba[_0x2b375e(0x20d)] + _0x1f514d)['clamp'](0x0, 0x168);
            if (_0x50a9ba['hue'] === _0x33506d) return ![];
            _0x50a9ba['hue'] = _0x33506d;
        }),
        (DoodadManager[_0x9a8c74(0x1e6)] = function (_0xc77449, _0x4790f7) {
            const _0x575c9c = _0x9a8c74,
                _0x172039 = (_0x4790f7['opacity'] + _0xc77449)[_0x575c9c(0x175)](0x0, 0xff);
            if (_0x4790f7['opacity'] === _0x172039) return ![];
            _0x4790f7[_0x575c9c(0x7d)] = _0x172039;
        }),
        (DoodadManager[_0x9a8c74(0x3ca)] = function (_0xe3c42e, _0x55e89a) {
            const _0x401aab = _0x9a8c74;
            Input[_0x401aab(0x313)]('shift')
                ? (_0xe3c42e /= Math['abs'](_0xe3c42e))
                : (_0xe3c42e *= 0x5);
            const _0x58bc94 = (_0x55e89a[_0x401aab(0x378)] + _0xe3c42e)[_0x401aab(0x175)](
                -0x3e8,
                0x3e8
            );
            if (_0x55e89a[_0x401aab(0x378)] === _0x58bc94) return ![];
            _0x55e89a[_0x401aab(0x378)] = _0x58bc94;
        }),
        (DoodadManager['changeSetting_scaleY'] = function (_0x3c52a3, _0x13d5f1) {
            const _0x3c363d = _0x9a8c74;
            Input['isPressed'](_0x3c363d(0x9d))
                ? (_0x3c52a3 /= Math[_0x3c363d(0x19c)](_0x3c52a3))
                : (_0x3c52a3 *= 0x5);
            const _0x8e4efb = (_0x13d5f1['scaleY'] + _0x3c52a3)[_0x3c363d(0x175)](-0x3e8, 0x3e8);
            if (_0x13d5f1['scaleY'] === _0x8e4efb) return ![];
            _0x13d5f1[_0x3c363d(0x3c0)] = _0x8e4efb;
        }),
        (DoodadManager[_0x9a8c74(0x17f)] = function (_0x5b0fcd, _0x6b7fdf) {
            const _0x571900 = _0x9a8c74;
            _0x6b7fdf[_0x571900(0x111)] =
                _0x6b7fdf[_0x571900(0x111)] === _0x571900(0x242)
                    ? _0x571900(0x395)
                    : _0x571900(0x242);
        }),
        (DoodadManager['changeSetting_angle'] = function (_0x18f39c, _0xaea88d) {
            const _0x2a27c2 = _0x9a8c74;
            let _0x5efd36 = (_0xaea88d[_0x2a27c2(0x1c9)] + _0x18f39c)['clamp'](0x0, 0x168);
            if (_0x5efd36 === 0x168) _0x5efd36 = 0x0;
            else
                _0xaea88d[_0x2a27c2(0x1c9)] === 0x0 &&
                    _0x18f39c < 0x0 &&
                    (_0x5efd36 = (0x168 + _0x18f39c)['clamp'](0x0, 0x168));
            if (_0xaea88d[_0x2a27c2(0x1c9)] === _0x5efd36) return ![];
            _0xaea88d[_0x2a27c2(0x1c9)] = _0x5efd36;
        }),
        (DoodadManager['isTilePresent'] = function (_0x45d315) {
            const _0x342284 = _0x9a8c74,
                _0x5cc0a8 =
                    0x5 +
                    Doodads[_0x342284(0x215)][_0x342284(0x353)](_0x2da970 =>
                        _0x2da970['includes'](_0x45d315)
                    );
            if (_0x5cc0a8 < 0x5) return ![];
            return Boolean($gameMap[_0x342284(0x30d)]()[_0x342284(0x3d8)][_0x5cc0a8]);
        }),
        (DoodadManager[_0x9a8c74(0xea)] = function (_0x5866fb, _0x4898cc) {
            const _0x3e63b1 = _0x9a8c74;
            if (_0x4898cc[_0x3e63b1(0x235)]) {
                ((_0x4898cc[_0x3e63b1(0x235)] += _0x5866fb),
                    (_0x4898cc[_0x3e63b1(0x235)] = Math[_0x3e63b1(0x2f7)](
                        _0x4898cc[_0x3e63b1(0x235)],
                        0x1
                    )));
                return;
            }
            if (!_0x4898cc[_0x3e63b1(0x9b)]) return ![];
            _0x5866fb /= Math['abs'](_0x5866fb);
            const _0xb6f117 = Doodads[_0x3e63b1(0x215)][_0x3e63b1(0x209)](_0x4b84de =>
                _0x4b84de[_0x3e63b1(0x39c)](_0x4898cc[_0x3e63b1(0x9b)])
            );
            if (!_0xb6f117) return ![];
            let _0xbbe2b7 = (_0x4898cc[_0x3e63b1(0x9b)] + _0x5866fb)['clamp'](
                0x1,
                Tilemap[_0x3e63b1(0x19d)]
            );
            if (_0x4898cc['tileId'] === _0xbbe2b7) return ![];
            const _0x46ea00 = Doodads[_0x3e63b1(0x215)][_0x3e63b1(0x209)](_0x884955 =>
                _0x884955[_0x3e63b1(0x39c)](_0xbbe2b7)
            );
            if (_0x46ea00 !== _0xb6f117)
                while (!this[_0x3e63b1(0x3c9)](_0xbbe2b7)) {
                    _0xbbe2b7 += _0x5866fb * 0x80;
                    if (_0xbbe2b7 <= 0x0 || _0xbbe2b7 >= Tilemap[_0x3e63b1(0x19d)]) return ![];
                }
            _0x4898cc[_0x3e63b1(0x9b)] = _0xbbe2b7;
        }),
        (DoodadManager[_0x9a8c74(0x29b)] = function (_0x21b421, _0x5b2631) {
            const _0x249828 = _0x9a8c74;
            _0x21b421 /= Math[_0x249828(0x19c)](_0x21b421);
            const _0x7280c5 = (_0x5b2631[_0x249828(0x396)] + _0x21b421)[_0x249828(0x175)](
                0x1,
                0x10
            );
            if (_0x5b2631[_0x249828(0x396)] === _0x7280c5) return ![];
            ((_0x5b2631[_0x249828(0x396)] = _0x7280c5), SoundManager['playCursor']());
        }),
        (DoodadManager[_0x9a8c74(0x123)] = function (_0x439d2e, _0x28bf0d) {
            const _0x241698 = _0x9a8c74;
            _0x439d2e /= Math[_0x241698(0x19c)](_0x439d2e);
            const _0x167bf4 = (_0x28bf0d[_0x241698(0x195)] + _0x439d2e)[_0x241698(0x175)](
                0x1,
                0x10
            );
            if (_0x28bf0d[_0x241698(0x195)] === _0x167bf4) return ![];
            ((_0x28bf0d[_0x241698(0x195)] = _0x167bf4), SoundManager[_0x241698(0x314)]());
        }),
        (DoodadManager[_0x9a8c74(0x2f9)] = function (_0x2a9966, _0xe7da6b) {
            const _0x347e10 = _0x9a8c74;
            _0x2a9966 /= Math[_0x347e10(0x19c)](_0x2a9966 * 0x2);
            const _0x1a9a8b = (_0xe7da6b[_0x347e10(0x2c7)] + _0x2a9966)[_0x347e10(0x175)](0x0, 0x1);
            if (_0xe7da6b[_0x347e10(0x2c7)] === _0x1a9a8b) return ![];
            _0xe7da6b['anchorX'] = _0x1a9a8b;
        }),
        (DoodadManager[_0x9a8c74(0x3a8)] = function (_0xe9bcf8, _0x10dbfd) {
            const _0x432a9a = _0x9a8c74;
            _0xe9bcf8 /= Math[_0x432a9a(0x19c)](_0xe9bcf8 * 0x2);
            const _0x3f9d7d = (_0x10dbfd['anchorY'] + _0xe9bcf8)['clamp'](0x0, 0x1);
            if (_0x10dbfd[_0x432a9a(0x31a)] === _0x3f9d7d) return ![];
            _0x10dbfd[_0x432a9a(0x31a)] = _0x3f9d7d;
        }),
        (DoodadManager[_0x9a8c74(0x3b5)] = function (_0x1b2e43, _0x4e5723) {
            const _0x449580 = _0x9a8c74;
            if (!SceneManager[_0x449580(0x3cd)][_0x449580(0x36f)][_0x449580(0xe5)]()) return ![];
            const _0x33b776 = Math[_0x449580(0x2f7)](0x1, _0x4e5723['frameUpdate'] + _0x1b2e43);
            if (_0x4e5723['frameUpdate'] === _0x33b776) return ![];
            _0x4e5723[_0x449580(0x1a4)] = _0x33b776;
        }),
        (DoodadManager[_0x9a8c74(0x2f6)] = function (_0x2e3951, _0x45d689) {
            const _0x28ff74 = _0x9a8c74;
            _0x2e3951 /= Math['abs'](_0x2e3951);
            const _0x36c328 = (_0x45d689[_0x28ff74(0x1d4)] + _0x2e3951)['clamp'](0x0, 0x3);
            if (_0x45d689['blend'] === _0x36c328) return ![];
            _0x45d689['blend'] = _0x36c328;
        }),
        (DoodadManager[_0x9a8c74(0x192)] = function (_0x3cac0a, _0x1692dc) {
            const _0x4db18a = _0x9a8c74;
            _0x1692dc[_0x4db18a(0xe9)] = !_0x1692dc[_0x4db18a(0xe9)];
        }),
        (DoodadManager[_0x9a8c74(0x23f)] = function (_0x3a56c3, _0x2964ed) {
            const _0x5b2e01 = _0x9a8c74;
            _0x2964ed[_0x5b2e01(0x147)] = !_0x2964ed['contrast'];
        }),
        (DoodadManager[_0x9a8c74(0x27d)] = function (_0x22d3c0, _0x50c8fa) {
            const _0x2cf0f2 = _0x9a8c74;
            _0x50c8fa[_0x2cf0f2(0x2a5)] = !_0x50c8fa[_0x2cf0f2(0x2a5)];
        }),
        (DoodadManager['changeSetting_blur'] = function (_0x428e41, _0x30a294) {
            const _0x19d509 = _0x9a8c74;
            _0x30a294[_0x19d509(0x1e3)] = !_0x30a294[_0x19d509(0x1e3)];
        }),
        (DoodadManager[_0x9a8c74(0x35c)] = function (_0x3385bd, _0x88b40) {
            const _0x321d0f = _0x9a8c74;
            _0x88b40[_0x321d0f(0x408)] = !_0x88b40[_0x321d0f(0x408)];
        }),
        (DoodadManager[_0x9a8c74(0x278)] = function (_0x37378b, _0x2da8f5) {
            const _0x7b97a7 = _0x9a8c74;
            _0x2da8f5[_0x7b97a7(0x252)] = !_0x2da8f5['shadow'];
        }),
        (DoodadManager[_0x9a8c74(0xde)] = function (_0x265e88, _0x46b0b7) {
            const _0x26654d = _0x9a8c74;
            _0x46b0b7[_0x26654d(0x185)] = !_0x46b0b7[_0x26654d(0x185)];
        }),
        (DoodadManager['changeSetting_toneSet'] = function (_0x3ff62f, _0x4ad095) {
            const _0x868717 = _0x9a8c74;
            _0x3ff62f /= Math[_0x868717(0x19c)](_0x3ff62f);
            const _0x2fed4b = Object['keys'](Doodads['toneColors']);
            let _0x3024da;
            for (const _0x53eb4b in Doodads[_0x868717(0x3ea)]) {
                if (!Doodads[_0x868717(0x3ea)][_0x868717(0x117)](_0x53eb4b)) continue;
                if (
                    DoodadManager[_0x868717(0x78)](
                        Doodads[_0x868717(0x3ea)][_0x53eb4b],
                        _0x4ad095[_0x868717(0x3ad)],
                        _0x4ad095[_0x868717(0xd5)],
                        _0x4ad095[_0x868717(0x1c8)],
                        _0x4ad095[_0x868717(0x236)]
                    )
                ) {
                    _0x3024da = _0x2fed4b[_0x868717(0x3d1)](_0x53eb4b);
                    break;
                }
            }
            if (_0x3024da === undefined) return ![];
            const _0x1c1fc6 = _0x3024da + _0x3ff62f,
                _0x582c01 = _0x2fed4b[_0x1c1fc6 % _0x2fed4b['length']],
                _0x5e3fe7 = Doodads[_0x868717(0x3ea)][_0x582c01];
            ((_0x4ad095['toneRed'] = _0x5e3fe7['red']),
                (_0x4ad095[_0x868717(0xd5)] = _0x5e3fe7[_0x868717(0x360)]),
                (_0x4ad095[_0x868717(0x1c8)] = _0x5e3fe7['blue']),
                (_0x4ad095[_0x868717(0x236)] = _0x5e3fe7[_0x868717(0x3a6)]));
        }),
        (DoodadManager['changeSetting_toneRed'] = function (_0x11791d, _0x812e4f) {
            const _0xc0a240 = _0x9a8c74;
            Input[_0xc0a240(0x313)](_0xc0a240(0x9d))
                ? (_0x11791d /= Math[_0xc0a240(0x19c)](_0x11791d))
                : (_0x11791d *= 0x5);
            const _0x24b8d7 = ((_0x812e4f['toneRed'] ?? 0x0) + _0x11791d)[_0xc0a240(0x175)](
                -0xff,
                0xff
            );
            if (_0x812e4f[_0xc0a240(0x3ad)] === _0x24b8d7) return ![];
            _0x812e4f['toneRed'] = _0x24b8d7;
        }),
        (DoodadManager['changeSetting_toneGreen'] = function (_0x1cb446, _0x240b7a) {
            const _0xc035d6 = _0x9a8c74;
            Input[_0xc035d6(0x313)](_0xc035d6(0x9d))
                ? (_0x1cb446 /= Math['abs'](_0x1cb446))
                : (_0x1cb446 *= 0x5);
            const _0x3edfe9 = ((_0x240b7a[_0xc035d6(0xd5)] ?? 0x0) + _0x1cb446)[_0xc035d6(0x175)](
                -0xff,
                0xff
            );
            if (_0x240b7a[_0xc035d6(0xd5)] === _0x3edfe9) return ![];
            _0x240b7a[_0xc035d6(0xd5)] = _0x3edfe9;
        }),
        (DoodadManager[_0x9a8c74(0x2c6)] = function (_0x531449, _0x57acb0) {
            const _0x4c26d3 = _0x9a8c74;
            Input[_0x4c26d3(0x313)](_0x4c26d3(0x9d))
                ? (_0x531449 /= Math[_0x4c26d3(0x19c)](_0x531449))
                : (_0x531449 *= 0x5);
            const _0x5aca82 = ((_0x57acb0['toneBlue'] ?? 0x0) + _0x531449)[_0x4c26d3(0x175)](
                -0xff,
                0xff
            );
            if (_0x57acb0['toneBlue'] === _0x5aca82) return ![];
            _0x57acb0['toneBlue'] = _0x5aca82;
        }),
        (DoodadManager['changeSetting_toneGrey'] = function (_0x45cc49, _0x276510) {
            const _0x502f63 = _0x9a8c74;
            Input['isPressed'](_0x502f63(0x9d))
                ? (_0x45cc49 /= Math['abs'](_0x45cc49))
                : (_0x45cc49 *= 0x5);
            const _0x309298 = ((_0x276510[_0x502f63(0x236)] ?? 0x0) + _0x45cc49)[_0x502f63(0x175)](
                0x0,
                0xff
            );
            if (_0x276510[_0x502f63(0x236)] === _0x309298) return ![];
            _0x276510[_0x502f63(0x236)] = _0x309298;
        }),
        (DoodadManager[_0x9a8c74(0x1dc)] = function (_0x24346e) {
            const _0x5619d5 = _0x9a8c74,
                _0x5e047e = SceneManager['_scene']['_gfdSettingsWindow'];
            if (!_0x5e047e['active']) return;
            if (_0x24346e !== 0x25 && _0x24346e !== 0x27) return;
            const _0x266cdb = _0x5e047e['_doodad'];
            let _0x49b6bf = 0x1;
            const _0x365622 = _0x5e047e[_0x5619d5(0x3ae)]();
            if (Doodads[_0x5619d5(0x3d9)][_0x5619d5(0x39c)](_0x365622)) return;
            _0x24346e === 0x25 && (_0x49b6bf *= -0x1);
            Input[_0x5619d5(0x313)](_0x5619d5(0x9d)) && (_0x49b6bf *= 0xa);
            const _0x3bf56c = _0x5619d5(0x2cb) + _0x365622;
            if (this[_0x3bf56c]) {
                this[_0x3bf56c](_0x49b6bf, _0x266cdb) !== ![] &&
                    (SoundManager[_0x5619d5(0x314)](),
                    _0x5e047e['refresh'](),
                    this[_0x5619d5(0x277)]());
                return;
            }
            if (_0x49b6bf < 0x0)
                return this[_0x5619d5(0x190)](_0x266cdb, _0x365622, _0x49b6bf * -0x1);
            return this[_0x5619d5(0x1b1)](_0x266cdb, _0x365622, _0x49b6bf);
        }),
        (DoodadManager['adjustManualMove'] = function () {
            const _0x5e4c01 = _0x9a8c74;
            ((this[_0x5e4c01(0x13a)] = this[_0x5e4c01(0x13a)][_0x5e4c01(0x175)](
                0x0,
                Graphics['width'] - 0x1
            )),
                (this[_0x5e4c01(0x2fa)] = this[_0x5e4c01(0x2fa)][_0x5e4c01(0x175)](
                    0x0,
                    Graphics['height'] - 0x1
                )));
        }),
        (DoodadManager[_0x9a8c74(0x374)] = function (_0x3bb32b) {
            const _0x3e1559 = _0x9a8c74;
            if (_0x3bb32b === 0x5a) {
                SoundManager[_0x3e1559(0x260)]();
                const _0x199145 = this[_0x3e1559(0x8f)]();
                ((_0x199145['x'] =
                    SceneManager[_0x3e1559(0x3cd)][_0x3e1559(0x1c3)][_0x3e1559(0x1a1)]()),
                    (_0x199145['y'] =
                        SceneManager[_0x3e1559(0x3cd)][_0x3e1559(0x1c3)][_0x3e1559(0x315)]()),
                    this[_0x3e1559(0xa8)]());
                return;
            }
            if (_0x3bb32b === 0x54) return;
            this[_0x3e1559(0x32a)](_0x3bb32b);
        }),
        (DoodadManager[_0x9a8c74(0x190)] = function (_0x5b8fc2, _0x5ad179, _0x48d3da) {}),
        (DoodadManager[_0x9a8c74(0x1b1)] = function (_0x49692f, _0x33c04c, _0x445c61) {}),
        (DoodadManager[_0x9a8c74(0x2af)] = function (_0x2bf2e6) {
            const _0x47ff4f = _0x9a8c74,
                _0xfdc4a4 = SceneManager[_0x47ff4f(0x3cd)]['_gfdPickDoodadListWindow'];
            if (!_0xfdc4a4[_0x47ff4f(0xcc)]) return;
            if (_0x2bf2e6 === 0x8 || _0x2bf2e6 === 0x2e) {
                const _0x249487 = _0xfdc4a4[_0x47ff4f(0x3c3)]();
                if (!_0x249487) return;
                (this[_0x47ff4f(0x333)](_0x249487),
                    SoundManager[_0x47ff4f(0x27b)](),
                    _0xfdc4a4[_0x47ff4f(0xa8)]());
                const _0x3b3238 = Math[_0x47ff4f(0x1d1)](
                    _0xfdc4a4[_0x47ff4f(0x357)](),
                    _0xfdc4a4[_0x47ff4f(0x2db)]() - 0x1
                );
                _0xfdc4a4[_0x47ff4f(0x112)](_0x3b3238);
            }
        }),
        (DoodadManager[_0x9a8c74(0x33e)] = function (_0x2b0a56, _0x2e2ab9) {
            const _0x2b4a78 = _0x9a8c74;
            function _0x21d8a1(_0x59b17f, _0x341ab5, _0x2ac781) {
                const _0x3325af = _0x22f3,
                    _0x102cd1 = Math[_0x3325af(0x1d1)](_0x59b17f, _0x341ab5, _0x2ac781),
                    _0x5cc40a = Math[_0x3325af(0x2f7)](_0x59b17f, _0x341ab5, _0x2ac781);
                let _0x49d79f = 0x0,
                    _0x378334 = 0x0;
                const _0x5d47bf = (_0x102cd1 + _0x5cc40a) / 0x2,
                    _0x2e11c2 = _0x5cc40a - _0x102cd1;
                if (_0x2e11c2 > 0x0) {
                    if (_0x59b17f === _0x5cc40a)
                        _0x49d79f = 0x3c * (((_0x341ab5 - _0x2ac781) / _0x2e11c2 + 0x6) % 0x6);
                    else
                        _0x341ab5 === _0x5cc40a
                            ? (_0x49d79f = 0x3c * ((_0x2ac781 - _0x59b17f) / _0x2e11c2 + 0x2))
                            : (_0x49d79f = 0x3c * ((_0x59b17f - _0x341ab5) / _0x2e11c2 + 0x4));
                    _0x378334 = _0x2e11c2 / (0xff - Math[_0x3325af(0x19c)](0x2 * _0x5d47bf - 0xff));
                }
                return [_0x49d79f, _0x378334, _0x5d47bf];
            }
            function _0x3c9e69(_0x324d72, _0x1ca65d, _0xd775e7) {
                const _0x223226 = _0x22f3,
                    _0x47243a = (0xff - Math[_0x223226(0x19c)](0x2 * _0xd775e7 - 0xff)) * _0x1ca65d,
                    _0x2bcd35 =
                        _0x47243a *
                        (0x1 - Math[_0x223226(0x19c)](((_0x324d72 / 0x3c) % 0x2) - 0x1)),
                    _0x21451f = _0xd775e7 - _0x47243a / 0x2,
                    _0x24fece = _0x47243a + _0x21451f,
                    _0x116ea5 = _0x2bcd35 + _0x21451f;
                if (_0x324d72 < 0x3c) return [_0x24fece, _0x116ea5, _0x21451f];
                else {
                    if (_0x324d72 < 0x78) return [_0x116ea5, _0x24fece, _0x21451f];
                    else {
                        if (_0x324d72 < 0xb4) return [_0x21451f, _0x24fece, _0x116ea5];
                        else {
                            if (_0x324d72 < 0xf0) return [_0x21451f, _0x116ea5, _0x24fece];
                            else
                                return _0x324d72 < 0x12c
                                    ? [_0x116ea5, _0x21451f, _0x24fece]
                                    : [_0x24fece, _0x21451f, _0x116ea5];
                        }
                    }
                }
            }
            if (_0x2e2ab9 && _0x2b0a56['width'] > 0x0 && _0x2b0a56['height'] > 0x0) {
                _0x2e2ab9 = ((_0x2e2ab9 % 0x168) + 0x168) % 0x168;
                const _0x3f7435 = _0x2b0a56[_0x2b4a78(0x35f)],
                    _0x5e0c53 = _0x3f7435[_0x2b4a78(0x283)](
                        0x0,
                        0x0,
                        _0x2b0a56[_0x2b4a78(0x3a9)],
                        _0x2b0a56[_0x2b4a78(0x158)]
                    ),
                    _0x86b82 = _0x5e0c53[_0x2b4a78(0xf8)];
                for (
                    let _0x1d392a = 0x0;
                    _0x1d392a < _0x86b82[_0x2b4a78(0x352)];
                    _0x1d392a += 0x4
                ) {
                    const _0x434997 = _0x21d8a1(
                            _0x86b82[_0x1d392a + 0x0],
                            _0x86b82[_0x1d392a + 0x1],
                            _0x86b82[_0x1d392a + 0x2]
                        ),
                        _0x9054c3 = (_0x434997[0x0] + _0x2e2ab9) % 0x168,
                        _0x301573 = _0x434997[0x1],
                        _0x4d2af8 = _0x434997[0x2],
                        _0x39be77 = _0x3c9e69(_0x9054c3, _0x301573, _0x4d2af8);
                    ((_0x86b82[_0x1d392a + 0x0] = _0x39be77[0x0]),
                        (_0x86b82[_0x1d392a + 0x1] = _0x39be77[0x1]),
                        (_0x86b82[_0x1d392a + 0x2] = _0x39be77[0x2]));
                }
                _0x3f7435[_0x2b4a78(0xc3)](_0x5e0c53, 0x0, 0x0);
            }
        }),
        (Game_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x37e)] = function () {
            $gamePlayer['center']($gamePlayer['x'], $gamePlayer['y']);
        }),
        (Game_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1ce)] = function (_0x4bb5dc) {
            const _0x1e0a28 = _0x9a8c74,
                _0x3abef1 = _0x4bb5dc['x'] / this[_0x1e0a28(0x3b6)](),
                _0x5f294f = _0x4bb5dc['y'] / this[_0x1e0a28(0x287)](),
                _0x1482d6 = (Graphics[_0x1e0a28(0x3a9)] / this[_0x1e0a28(0x3b6)]() - 0x1) / 0x2,
                _0x32929a = (Graphics[_0x1e0a28(0x158)] / this[_0x1e0a28(0x287)]() - 0x1) / 0x2;
            this[_0x1e0a28(0x2bd)](_0x3abef1 - _0x1482d6, _0x5f294f - _0x32929a);
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x356)] =
            Game_Player[_0x9a8c74(0x11a)][_0x9a8c74(0xe6)]),
        (Game_Player[_0x9a8c74(0x11a)][_0x9a8c74(0xe6)] = function () {
            const _0x1db5bc = _0x9a8c74;
            if ($gameTemp[_0x1db5bc(0x136)]) return ![];
            return VisuMZ[_0x1db5bc(0x268)][_0x1db5bc(0x356)][_0x1db5bc(0x355)](this);
        }),
        (VisuMZ['DoodadsEditor'][_0x9a8c74(0xa4)] = Game_Event[_0x9a8c74(0x11a)][_0x9a8c74(0x273)]),
        (Game_Event['prototype'][_0x9a8c74(0x273)] = function () {
            const _0xfa4e54 = _0x9a8c74;
            if ($gameTemp[_0xfa4e54(0x136)]) return;
            VisuMZ[_0xfa4e54(0x268)][_0xfa4e54(0xa4)]['call'](this);
        }),
        (Scene_Base[_0x9a8c74(0x11a)][_0x9a8c74(0x1f1)] = function () {
            return ![];
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['isMap'] = function () {
            return !![];
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x179)] =
            Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3a4)]),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3a4)] = function () {
            const _0x584e35 = _0x9a8c74;
            if ($gameTemp[_0x584e35(0x136)]) return;
            VisuMZ[_0x584e35(0x268)][_0x584e35(0x179)][_0x584e35(0x355)](this);
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x3d7)] = function () {
            const _0x228944 = _0x9a8c74;
            $gameTemp[_0x228944(0x136)] ? this[_0x228944(0x389)]() : this[_0x228944(0x203)]();
        }),
        (VisuMZ['DoodadsEditor'][_0x9a8c74(0x3b9)] = Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1fb)]),
        (Scene_Map['prototype'][_0x9a8c74(0x1fb)] = function () {
            const _0x105de5 = _0x9a8c74;
            if ($gameTemp[_0x105de5(0x136)]) return ![];
            return VisuMZ[_0x105de5(0x268)][_0x105de5(0x3b9)][_0x105de5(0x355)](this);
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0xc5)] = Scene_Map['prototype'][_0x9a8c74(0x106)]),
        (Scene_Map[_0x9a8c74(0x11a)]['isDebugCalled'] = function () {
            const _0x374d5e = _0x9a8c74;
            if ($gameTemp[_0x374d5e(0x136)]) return ![];
            return VisuMZ[_0x374d5e(0x268)][_0x374d5e(0xc5)][_0x374d5e(0x355)](this);
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x21d)] = function () {
            const _0xa44cd0 = _0x9a8c74;
            ((this['_gfdWindows'] = []),
                this['createGFDMenuWindow'](),
                this[_0xa44cd0(0x37f)](),
                this['createGFDIconWindow'](),
                this[_0xa44cd0(0x364)](),
                this[_0xa44cd0(0x248)](),
                this[_0xa44cd0(0x3d5)](),
                this[_0xa44cd0(0x26b)](),
                this['createGFDSettingsSubwindows'](),
                this[_0xa44cd0(0x2f0)](),
                this[_0xa44cd0(0x1d0)](),
                this[_0xa44cd0(0x3a7)](),
                this[_0xa44cd0(0x2aa)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x15f)] = function () {
            const _0x523dc9 = _0x9a8c74;
            ((this[_0x523dc9(0x22a)] = new Window_Doodads_Menu()),
                this[_0x523dc9(0x2c5)](this[_0x523dc9(0x22a)]),
                this[_0x523dc9(0xfc)][_0x523dc9(0x1cc)](this['_gfdMenuWindow']));
            const _0x337ec9 = this[_0x523dc9(0x22a)];
            (_0x337ec9[_0x523dc9(0x3e1)](
                _0x523dc9(0x2bc),
                this[_0x523dc9(0x1f8)][_0x523dc9(0xee)](this)
            ),
                _0x337ec9['setHandler'](
                    _0x523dc9(0x346),
                    this[_0x523dc9(0x2e8)][_0x523dc9(0xee)](this)
                ),
                _0x337ec9['setHandler']('place', this[_0x523dc9(0x14c)]['bind'](this)),
                _0x337ec9[_0x523dc9(0x3e1)]('save', this[_0x523dc9(0x165)][_0x523dc9(0xee)](this)),
                _0x337ec9['setHandler'](
                    _0x523dc9(0x325),
                    this[_0x523dc9(0x299)][_0x523dc9(0xee)](this)
                ),
                _0x337ec9[_0x523dc9(0x3e1)](
                    _0x523dc9(0x2dd),
                    this['cmdGFDMenuClear'][_0x523dc9(0xee)](this)
                ),
                _0x337ec9[_0x523dc9(0x3e1)](
                    _0x523dc9(0x141),
                    this['cmdGFDMenuRegionOverlay']['bind'](this)
                ),
                _0x337ec9[_0x523dc9(0x3e1)](
                    _0x523dc9(0x234),
                    this[_0x523dc9(0x1c0)][_0x523dc9(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDListWindow'] = function () {
            const _0x3b68e9 = _0x9a8c74;
            ((this['_gfdListWindow'] = new Window_Doodads_List()),
                this[_0x3b68e9(0x2c5)](this[_0x3b68e9(0x148)]),
                this[_0x3b68e9(0xfc)][_0x3b68e9(0x1cc)](this[_0x3b68e9(0x148)]));
            const _0x13db9e = this[_0x3b68e9(0x148)];
            (_0x13db9e[_0x3b68e9(0x3e1)]('cancel', this[_0x3b68e9(0x29a)][_0x3b68e9(0xee)](this)),
                _0x13db9e[_0x3b68e9(0x3e1)](
                    _0x3b68e9(0xb8),
                    this[_0x3b68e9(0x297)][_0x3b68e9(0xee)](this)
                ),
                _0x13db9e['setHandler'](
                    _0x3b68e9(0x397),
                    this[_0x3b68e9(0x2f4)][_0x3b68e9(0xee)](this)
                ),
                _0x13db9e[_0x3b68e9(0x3e1)](
                    'tileSet',
                    this[_0x3b68e9(0xbd)][_0x3b68e9(0xee)](this)
                ),
                _0x13db9e[_0x3b68e9(0x3e1)](
                    _0x3b68e9(0x15a),
                    this['cmdGFDListFile'][_0x3b68e9(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDIconWindow'] = function () {
            const _0x1d2236 = _0x9a8c74;
            ((this['_gfdIconWindow'] = new Window_Doodads_Icons()),
                this[_0x1d2236(0x2c5)](this[_0x1d2236(0x122)]),
                this[_0x1d2236(0xfc)][_0x1d2236(0x1cc)](this[_0x1d2236(0x122)]));
            const _0x46fada = this[_0x1d2236(0x122)];
            (_0x46fada[_0x1d2236(0x3e1)](
                _0x1d2236(0x2bc),
                this['cancelGFDIcon'][_0x1d2236(0xee)](this)
            ),
                _0x46fada[_0x1d2236(0x3e1)](
                    _0x1d2236(0x235),
                    this[_0x1d2236(0x163)]['bind'](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x364)] = function () {
            const _0x466dee = _0x9a8c74;
            ((this['_gfdTileWindow'] = new Window_Doodads_Tiles()),
                this[_0x466dee(0x2c5)](this[_0x466dee(0xac)]),
                this[_0x466dee(0xfc)][_0x466dee(0x1cc)](this[_0x466dee(0xac)]));
            const _0x7d8de9 = this[_0x466dee(0xac)];
            (_0x7d8de9[_0x466dee(0x3e1)](_0x466dee(0x2bc), this[_0x466dee(0x39e)]['bind'](this)),
                _0x7d8de9[_0x466dee(0x3e1)](
                    _0x466dee(0x9b),
                    this[_0x466dee(0x2fd)][_0x466dee(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3d5)] = function () {
            const _0x5ab2ee = _0x9a8c74;
            ((this[_0x5ab2ee(0x1c3)] = new Window_Doodads_Canvas()),
                this[_0x5ab2ee(0x2c5)](this['_gfdCanvasWindow']),
                this[_0x5ab2ee(0xfc)][_0x5ab2ee(0x1cc)](this['_gfdCanvasWindow']));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x248)] = function () {
            const _0x13ad89 = _0x9a8c74;
            ((this[_0x13ad89(0xa5)] = new Window_Doodads_DrawGrid()),
                this[_0x13ad89(0x2c5)](this['_gfdDrawGridWindow']),
                this['_gfdWindows'][_0x13ad89(0x1cc)](this[_0x13ad89(0xa5)]));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x26b)] = function () {
            const _0x273ad7 = _0x9a8c74;
            ((this[_0x273ad7(0x36f)] = new Window_Doodads_Settings()),
                this['addChild'](this[_0x273ad7(0x36f)]),
                this[_0x273ad7(0xfc)]['push'](this[_0x273ad7(0x36f)]));
            const _0x140882 = this[_0x273ad7(0x36f)];
            (_0x140882['setHandler']('cancel', this[_0x273ad7(0xeb)][_0x273ad7(0xee)](this)),
                _0x140882['setHandler'](
                    _0x273ad7(0x325),
                    this[_0x273ad7(0x3da)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    'accept',
                    this[_0x273ad7(0x406)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x332),
                    this['cmdGFDSettingsLayer'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)]('hue', this[_0x273ad7(0xf1)][_0x273ad7(0xee)](this)),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x7d),
                    this[_0x273ad7(0x92)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](
                    _0x273ad7(0x378),
                    this[_0x273ad7(0xbb)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x3c0),
                    this['cmdGFDSettingsScale'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x2c7),
                    this[_0x273ad7(0x1ac)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x31a),
                    this['cmdGFDSettingsAnchorY'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x189),
                    this[_0x273ad7(0x17e)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](
                    _0x273ad7(0x1d4),
                    this[_0x273ad7(0x28d)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    'smooth',
                    this['cmdGFDSettingsSmooth'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x333),
                    this[_0x273ad7(0x217)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x1df),
                    this[_0x273ad7(0x3f1)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](
                    _0x273ad7(0x111),
                    this[_0x273ad7(0x345)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](
                    _0x273ad7(0x1c9),
                    this[_0x273ad7(0x320)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler']('blur', this[_0x273ad7(0x363)]['bind'](this)),
                _0x140882[_0x273ad7(0x3e1)](_0x273ad7(0x408), this[_0x273ad7(0x168)]['bind'](this)),
                _0x140882['setHandler']('shadow', this[_0x273ad7(0x36e)][_0x273ad7(0xee)](this)),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x185),
                    this['cmdGFDSettingsGlow'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x147),
                    this[_0x273ad7(0x227)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](_0x273ad7(0x2a5), this[_0x273ad7(0x2b0)]['bind'](this)),
                _0x140882['setHandler'](
                    _0x273ad7(0x235),
                    this[_0x273ad7(0x30b)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](_0x273ad7(0x9b), this[_0x273ad7(0x1af)]['bind'](this)),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x396),
                    this[_0x273ad7(0x2ab)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x195),
                    this[_0x273ad7(0x1a8)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x399),
                    this[_0x273ad7(0xfa)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    'switch',
                    this[_0x273ad7(0x22d)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](_0x273ad7(0x272), this[_0x273ad7(0x134)]['bind'](this)),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x3ad),
                    this[_0x273ad7(0x1cf)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0xd5),
                    this[_0x273ad7(0x17a)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](
                    _0x273ad7(0x1c8),
                    this[_0x273ad7(0x30a)][_0x273ad7(0xee)](this)
                ),
                _0x140882['setHandler'](_0x273ad7(0x236), this[_0x273ad7(0x407)]['bind'](this)),
                _0x140882[_0x273ad7(0x3e1)](
                    'toneRandomRed',
                    this['cmdGFDSettToneRandomRed'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x233),
                    this[_0x273ad7(0x1ad)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    'toneRandomBlue',
                    this[_0x273ad7(0x2ac)][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x1ef),
                    this['cmdGFDSettToneRandomGrey'][_0x273ad7(0xee)](this)
                ),
                _0x140882[_0x273ad7(0x3e1)](
                    _0x273ad7(0x8b),
                    this[_0x273ad7(0xad)][_0x273ad7(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x2e2)] = function () {
            const _0x13df35 = _0x9a8c74;
            (this[_0x13df35(0xf9)](),
                this[_0x13df35(0xf6)](),
                this[_0x13df35(0x1b6)](),
                this[_0x13df35(0x2c9)](),
                this[_0x13df35(0x282)](),
                this[_0x13df35(0x3e6)](),
                this[_0x13df35(0x28e)](),
                this[_0x13df35(0x405)](),
                this[_0x13df35(0x323)](),
                this['createGFDSettingsContrastWindow'](),
                this['createGFDSettingsSepiaWindow'](),
                this[_0x13df35(0x1ca)](),
                this[_0x13df35(0x327)](),
                this[_0x13df35(0xfe)](),
                this[_0x13df35(0x121)](),
                this[_0x13df35(0xa0)](),
                this[_0x13df35(0x29c)](),
                this[_0x13df35(0x1bd)](),
                this[_0x13df35(0x181)](),
                this['createGFDSettingsTileColsWindow'](),
                this[_0x13df35(0x83)](),
                this['createGFDSettingsTonePresetWindow'](),
                this[_0x13df35(0x40a)](),
                this[_0x13df35(0x10b)](),
                this['createGFDSettingsPartyWindow'](),
                this[_0x13df35(0x3ee)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xf9)] = function () {
            const _0x1d5529 = _0x9a8c74;
            ((this[_0x1d5529(0x347)] = new Window_Doodads_Settings_Layers()),
                this['addChild'](this['_gfdSettingsLayersWindow']),
                this[_0x1d5529(0xfc)][_0x1d5529(0x1cc)](this[_0x1d5529(0x347)]));
            const _0xe7c95e = this['_gfdSettingsLayersWindow'];
            (_0xe7c95e[_0x1d5529(0x3e1)](
                _0x1d5529(0x2bc),
                this[_0x1d5529(0x137)][_0x1d5529(0xee)](this)
            ),
                _0xe7c95e[_0x1d5529(0x3e1)](
                    'layer',
                    this['cmdGFDSettLayerOk'][_0x1d5529(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xf6)] = function () {
            const _0x42e2b5 = _0x9a8c74;
            ((this[_0x42e2b5(0x3e2)] = new Window_Doodads_Settings_Hue()),
                this[_0x42e2b5(0x2c5)](this[_0x42e2b5(0x3e2)]),
                this[_0x42e2b5(0xfc)]['push'](this[_0x42e2b5(0x3e2)]));
            const _0x47d067 = this['_gfdSettingsHueWindow'];
            (_0x47d067[_0x42e2b5(0x3e1)]('cancel', this[_0x42e2b5(0xed)][_0x42e2b5(0xee)](this)),
                _0x47d067[_0x42e2b5(0x3e1)](
                    _0x42e2b5(0x20d),
                    this[_0x42e2b5(0x3bf)][_0x42e2b5(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1b6)] = function () {
            const _0x1190ca = _0x9a8c74;
            ((this['_gfdSettingsOpacityWindow'] = new Window_Doodads_Settings_Opacity()),
                this[_0x1190ca(0x2c5)](this[_0x1190ca(0x176)]),
                this['_gfdWindows'][_0x1190ca(0x1cc)](this[_0x1190ca(0x176)]));
            const _0x22a13a = this[_0x1190ca(0x176)];
            (_0x22a13a[_0x1190ca(0x3e1)]('cancel', this[_0x1190ca(0xc0)][_0x1190ca(0xee)](this)),
                _0x22a13a[_0x1190ca(0x3e1)](
                    'opacity',
                    this['cmdGFDSettOpacityOk'][_0x1190ca(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDSettingsScaleWindow'] = function () {
            const _0x39f62e = _0x9a8c74;
            ((this[_0x39f62e(0x26f)] = new Window_Doodads_Settings_Scale()),
                this[_0x39f62e(0x2c5)](this[_0x39f62e(0x26f)]),
                this[_0x39f62e(0xfc)][_0x39f62e(0x1cc)](this[_0x39f62e(0x26f)]));
            const _0x2b856c = this[_0x39f62e(0x26f)];
            (_0x2b856c[_0x39f62e(0x3e1)]('cancel', this[_0x39f62e(0x3ba)][_0x39f62e(0xee)](this)),
                _0x2b856c[_0x39f62e(0x3e1)](
                    _0x39f62e(0xd1),
                    this['cmdGFDSettScaleOk'][_0x39f62e(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0xa0)] = function () {
            const _0x18c103 = _0x9a8c74;
            ((this['_gfdSettingsPositionTypeWindow'] = new Window_Doodads_Settings_Position_Type()),
                this[_0x18c103(0x2c5)](this[_0x18c103(0x157)]),
                this[_0x18c103(0xfc)]['push'](this['_gfdSettingsPositionTypeWindow']));
            const _0x3623ff = this[_0x18c103(0x157)];
            (_0x3623ff[_0x18c103(0x3e1)]('cancel', this['cancelGFDSettPositionType']['bind'](this)),
                _0x3623ff[_0x18c103(0x3e1)](
                    _0x18c103(0x111),
                    this[_0x18c103(0x301)]['bind'](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x29c)] = function () {
            const _0x20f123 = _0x9a8c74;
            ((this[_0x20f123(0x25e)] = new Window_Doodads_Settings_Angle()),
                this['addChild'](this[_0x20f123(0x25e)]),
                this['_gfdWindows'][_0x20f123(0x1cc)](this['_gfdSettingsAngleWindow']));
            const _0x330655 = this['_gfdSettingsAngleWindow'];
            (_0x330655[_0x20f123(0x3e1)](
                _0x20f123(0x2bc),
                this['cancelGFDSettAngle'][_0x20f123(0xee)](this)
            ),
                _0x330655[_0x20f123(0x3e1)](
                    _0x20f123(0x1c9),
                    this[_0x20f123(0x19e)]['bind'](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1bd)] = function () {
            const _0x34152e = _0x9a8c74;
            ((this[_0x34152e(0x2a9)] = new Window_Doodads_Settings_Icon_Index()),
                this[_0x34152e(0x2c5)](this[_0x34152e(0x2a9)]),
                this[_0x34152e(0xfc)][_0x34152e(0x1cc)](this['_gfdSettingsIconIndexWindow']));
            const _0x4bde73 = this[_0x34152e(0x2a9)];
            (_0x4bde73[_0x34152e(0x3e1)](
                _0x34152e(0x2bc),
                this[_0x34152e(0x3a3)][_0x34152e(0xee)](this)
            ),
                _0x4bde73['setHandler'](
                    _0x34152e(0x235),
                    this[_0x34152e(0x21a)][_0x34152e(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x181)] = function () {
            const _0x2c7d0a = _0x9a8c74;
            ((this[_0x2c7d0a(0x3f9)] = new Window_Doodads_Settings_Tile_ID()),
                this['addChild'](this['_gfdSettingsTileIdWindow']),
                this[_0x2c7d0a(0xfc)][_0x2c7d0a(0x1cc)](this[_0x2c7d0a(0x3f9)]));
            const _0x4410ff = this[_0x2c7d0a(0x3f9)];
            (_0x4410ff[_0x2c7d0a(0x3e1)](
                _0x2c7d0a(0x2bc),
                this[_0x2c7d0a(0x3ed)][_0x2c7d0a(0xee)](this)
            ),
                _0x4410ff[_0x2c7d0a(0x3e1)](
                    _0x2c7d0a(0x9b),
                    this['cmdGFDSettTileIdOk'][_0x2c7d0a(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xb1)] = function () {
            const _0x18327f = _0x9a8c74;
            ((this[_0x18327f(0x177)] = new Window_Doodads_Settings_Tile_Cols()),
                this[_0x18327f(0x2c5)](this[_0x18327f(0x177)]),
                this[_0x18327f(0xfc)][_0x18327f(0x1cc)](this[_0x18327f(0x177)]));
            const _0x4ed15d = this[_0x18327f(0x177)];
            (_0x4ed15d[_0x18327f(0x3e1)](
                _0x18327f(0x2bc),
                this['cancelGFDSettTileCols'][_0x18327f(0xee)](this)
            ),
                _0x4ed15d[_0x18327f(0x3e1)](
                    _0x18327f(0x396),
                    this['cmdGFDSettTileColsOk']['bind'](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x83)] = function () {
            const _0x2fa124 = _0x9a8c74;
            ((this['_gfdSettingsTileRowsWindow'] = new Window_Doodads_Settings_Tile_Rows()),
                this[_0x2fa124(0x2c5)](this['_gfdSettingsTileRowsWindow']),
                this['_gfdWindows'][_0x2fa124(0x1cc)](this[_0x2fa124(0x3fe)]));
            const _0x592cc0 = this['_gfdSettingsTileRowsWindow'];
            (_0x592cc0[_0x2fa124(0x3e1)]('cancel', this[_0x2fa124(0x32e)][_0x2fa124(0xee)](this)),
                _0x592cc0[_0x2fa124(0x3e1)](
                    _0x2fa124(0x195),
                    this['cmdGFDSettTileRowsOk'][_0x2fa124(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDSettingsAnchorXWindow'] = function () {
            const _0x3e0123 = _0x9a8c74;
            ((this[_0x3e0123(0xef)] = new Window_Doodads_Settings_Anchor_X()),
                this[_0x3e0123(0x2c5)](this[_0x3e0123(0xef)]),
                this[_0x3e0123(0xfc)]['push'](this[_0x3e0123(0xef)]));
            const _0x55c4c9 = this[_0x3e0123(0xef)];
            (_0x55c4c9[_0x3e0123(0x3e1)](_0x3e0123(0x2bc), this[_0x3e0123(0x86)]['bind'](this)),
                _0x55c4c9[_0x3e0123(0x3e1)](
                    _0x3e0123(0x2c7),
                    this[_0x3e0123(0x198)][_0x3e0123(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDSettingsAnchorYWindow'] = function () {
            const _0xf017b2 = _0x9a8c74;
            ((this[_0xf017b2(0x9c)] = new Window_Doodads_Settings_Anchor_Y()),
                this[_0xf017b2(0x2c5)](this[_0xf017b2(0x9c)]),
                this[_0xf017b2(0xfc)][_0xf017b2(0x1cc)](this[_0xf017b2(0x9c)]));
            const _0x9ecbc7 = this[_0xf017b2(0x9c)];
            (_0x9ecbc7['setHandler'](
                _0xf017b2(0x2bc),
                this[_0xf017b2(0x14f)][_0xf017b2(0xee)](this)
            ),
                _0x9ecbc7['setHandler'](
                    _0xf017b2(0x31a),
                    this[_0xf017b2(0x366)][_0xf017b2(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x28e)] = function () {
            const _0x5cb531 = _0x9a8c74;
            ((this[_0x5cb531(0x382)] = new Window_Doodads_Settings_Frame_Speed()),
                this[_0x5cb531(0x2c5)](this[_0x5cb531(0x382)]),
                this[_0x5cb531(0xfc)][_0x5cb531(0x1cc)](this[_0x5cb531(0x382)]));
            const _0x207e4c = this[_0x5cb531(0x382)];
            (_0x207e4c[_0x5cb531(0x3e1)](
                _0x5cb531(0x2bc),
                this[_0x5cb531(0x3c4)][_0x5cb531(0xee)](this)
            ),
                _0x207e4c[_0x5cb531(0x3e1)]('frameUpdate', this[_0x5cb531(0x1ab)]['bind'](this)));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDSettingsBlendWindow'] = function () {
            const _0x57e4c0 = _0x9a8c74;
            ((this[_0x57e4c0(0x1ed)] = new Window_Doodads_Settings_Blend()),
                this[_0x57e4c0(0x2c5)](this['_gfdSettingsBlendWindow']),
                this[_0x57e4c0(0xfc)][_0x57e4c0(0x1cc)](this[_0x57e4c0(0x1ed)]));
            const _0x3f6a00 = this[_0x57e4c0(0x1ed)];
            (_0x3f6a00['setHandler']('cancel', this[_0x57e4c0(0x3e5)][_0x57e4c0(0xee)](this)),
                _0x3f6a00[_0x57e4c0(0x3e1)](
                    _0x57e4c0(0x1d4),
                    this[_0x57e4c0(0x124)][_0x57e4c0(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['createGFDSettingsSmoothWindow'] = function () {
            const _0x3c1946 = _0x9a8c74;
            ((this[_0x3c1946(0x240)] = new Window_Doodads_Settings_Smooth()),
                this[_0x3c1946(0x2c5)](this['_gfdSettingsSmoothWindow']),
                this[_0x3c1946(0xfc)]['push'](this[_0x3c1946(0x240)]));
            const _0x1786ec = this[_0x3c1946(0x240)];
            (_0x1786ec['setHandler']('cancel', this['cancelGFDSettSmooth'][_0x3c1946(0xee)](this)),
                _0x1786ec[_0x3c1946(0x3e1)](
                    'smooth',
                    this['cmdGFDSettSmoothOk'][_0x3c1946(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3ec)] = function () {
            const _0x4a7dc7 = _0x9a8c74;
            ((this[_0x4a7dc7(0x2bb)] = new Window_Doodads_Settings_Contrast()),
                this[_0x4a7dc7(0x2c5)](this[_0x4a7dc7(0x2bb)]),
                this[_0x4a7dc7(0xfc)]['push'](this['_gfdSettingsContrastWindow']));
            const _0x524bc4 = this['_gfdSettingsContrastWindow'];
            (_0x524bc4[_0x4a7dc7(0x3e1)](
                _0x4a7dc7(0x2bc),
                this[_0x4a7dc7(0x178)][_0x4a7dc7(0xee)](this)
            ),
                _0x524bc4[_0x4a7dc7(0x3e1)](
                    'contrast',
                    this[_0x4a7dc7(0x2d4)][_0x4a7dc7(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x3e0)] = function () {
            const _0x32b1f0 = _0x9a8c74;
            ((this[_0x32b1f0(0x390)] = new Window_Doodads_Settings_Sepia()),
                this[_0x32b1f0(0x2c5)](this[_0x32b1f0(0x390)]),
                this[_0x32b1f0(0xfc)][_0x32b1f0(0x1cc)](this['_gfdSettingsSepiaWindow']));
            const _0x1c81ea = this[_0x32b1f0(0x390)];
            (_0x1c81ea[_0x32b1f0(0x3e1)](
                _0x32b1f0(0x2bc),
                this[_0x32b1f0(0x281)][_0x32b1f0(0xee)](this)
            ),
                _0x1c81ea[_0x32b1f0(0x3e1)]('sepia', this[_0x32b1f0(0xb0)][_0x32b1f0(0xee)](this)));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1ca)] = function () {
            const _0x5df96c = _0x9a8c74;
            ((this['_gfdSettingsBlurWindow'] = new Window_Doodads_Settings_Blur()),
                this[_0x5df96c(0x2c5)](this[_0x5df96c(0x138)]),
                this['_gfdWindows'][_0x5df96c(0x1cc)](this[_0x5df96c(0x138)]));
            const _0x1952e1 = this[_0x5df96c(0x138)];
            (_0x1952e1[_0x5df96c(0x3e1)](_0x5df96c(0x2bc), this[_0x5df96c(0x31b)]['bind'](this)),
                _0x1952e1[_0x5df96c(0x3e1)](
                    'blur',
                    this['cmdGFDSettBlurOk'][_0x5df96c(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x327)] = function () {
            const _0x5f0dae = _0x9a8c74;
            ((this['_gfdSettingsOutlineWindow'] = new Window_Doodads_Settings_Outline()),
                this[_0x5f0dae(0x2c5)](this['_gfdSettingsOutlineWindow']),
                this['_gfdWindows'][_0x5f0dae(0x1cc)](this[_0x5f0dae(0x26a)]));
            const _0x57b106 = this[_0x5f0dae(0x26a)];
            (_0x57b106['setHandler'](
                _0x5f0dae(0x2bc),
                this[_0x5f0dae(0x87)][_0x5f0dae(0xee)](this)
            ),
                _0x57b106['setHandler']('outline', this[_0x5f0dae(0xdb)]['bind'](this)));
        }),
        (Scene_Map['prototype']['createGFDSettingsShadowWindow'] = function () {
            const _0x1016b0 = _0x9a8c74;
            ((this['_gfdSettingsShadowWindow'] = new Window_Doodads_Settings_Shadow()),
                this[_0x1016b0(0x2c5)](this[_0x1016b0(0x2e5)]),
                this[_0x1016b0(0xfc)]['push'](this[_0x1016b0(0x2e5)]));
            const _0x47d86f = this[_0x1016b0(0x2e5)];
            (_0x47d86f[_0x1016b0(0x3e1)](
                _0x1016b0(0x2bc),
                this[_0x1016b0(0x1d2)][_0x1016b0(0xee)](this)
            ),
                _0x47d86f[_0x1016b0(0x3e1)](
                    _0x1016b0(0x252),
                    this['cmdGFDSettShadowOk'][_0x1016b0(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x121)] = function () {
            const _0x34da73 = _0x9a8c74;
            ((this[_0x34da73(0x3b2)] = new Window_Doodads_Settings_Glow()),
                this[_0x34da73(0x2c5)](this[_0x34da73(0x3b2)]),
                this['_gfdWindows'][_0x34da73(0x1cc)](this[_0x34da73(0x3b2)]));
            const _0x247128 = this[_0x34da73(0x3b2)];
            (_0x247128[_0x34da73(0x3e1)](
                _0x34da73(0x2bc),
                this[_0x34da73(0x135)][_0x34da73(0xee)](this)
            ),
                _0x247128['setHandler'](_0x34da73(0x185), this['cmdGFDSettGlowOk']['bind'](this)));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2f0)] = function () {
            const _0xd2149 = _0x9a8c74;
            ((this[_0xd2149(0x33a)] = new Window_Doodads_Grid_Menu()),
                this[_0xd2149(0x2c5)](this[_0xd2149(0x33a)]),
                this[_0xd2149(0xfc)]['push'](this['_gfdGridMenuWindow']));
            const _0x549f33 = this[_0xd2149(0x33a)];
            (_0x549f33['setHandler'](_0xd2149(0x2bc), this[_0xd2149(0x3a2)][_0xd2149(0xee)](this)),
                _0x549f33[_0xd2149(0x3e1)](
                    _0xd2149(0x2df),
                    this[_0xd2149(0x319)][_0xd2149(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1d0)] = function () {
            const _0x27bbdd = _0x9a8c74;
            ((this['_gfdPickDoodadLayerWindow'] = new Window_Doodads_PickDoodadLayer()),
                this['addChild'](this[_0x27bbdd(0x24f)]),
                this['_gfdWindows'][_0x27bbdd(0x1cc)](this[_0x27bbdd(0x24f)]));
            const _0x5baef3 = this[_0x27bbdd(0x24f)];
            (_0x5baef3[_0x27bbdd(0x3e1)](
                _0x27bbdd(0x2bc),
                this[_0x27bbdd(0x91)][_0x27bbdd(0xee)](this)
            ),
                _0x5baef3[_0x27bbdd(0x3e1)](
                    _0x27bbdd(0x346),
                    this['openGFDPickDoodadList']['bind'](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3a7)] = function () {
            const _0x117eda = _0x9a8c74;
            ((this[_0x117eda(0x16d)] = new Window_Doodads_PickDoodadList()),
                this[_0x117eda(0x2c5)](this[_0x117eda(0x16d)]),
                this[_0x117eda(0xfc)]['push'](this['_gfdPickDoodadListWindow']));
            const _0x440edf = this[_0x117eda(0x16d)];
            (_0x440edf['setHandler'](_0x117eda(0x2bc), this[_0x117eda(0xfb)]['bind'](this)),
                _0x440edf['setHandler'](
                    _0x117eda(0x1de),
                    this[_0x117eda(0x2b7)][_0x117eda(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2aa)] = function () {
            const _0x3c357c = _0x9a8c74;
            ((this[_0x3c357c(0x2e1)] = new Window_Doodads_Import()),
                this['addChild'](this[_0x3c357c(0x2e1)]),
                this[_0x3c357c(0xfc)]['push'](this[_0x3c357c(0x2e1)]));
            const _0x94d127 = this[_0x3c357c(0x2e1)];
            (_0x94d127[_0x3c357c(0x3e1)](_0x3c357c(0x2bc), this['cancelGFDImport']['bind'](this)),
                _0x94d127[_0x3c357c(0x3e1)](
                    _0x3c357c(0x395),
                    this['cmdCFDImportMap'][_0x3c357c(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x389)] = function () {
            const _0x5297a4 = _0x9a8c74;
            (!this[_0x5297a4(0xfc)] && this['createGFDWindows'](),
                ($gameTemp[_0x5297a4(0xce)] = JsonEx['makeDeepCopy']($dataDoodads)),
                this[_0x5297a4(0x22a)][_0x5297a4(0x3cf)](),
                this[_0x5297a4(0x22a)][_0x5297a4(0x1d6)](),
                this[_0x5297a4(0x22a)][_0x5297a4(0x11d)](0x0),
                this[_0x5297a4(0x22a)][_0x5297a4(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1f8)] = function () {
            const _0x5b8ce0 = _0x9a8c74;
            (this[_0x5b8ce0(0x22a)][_0x5b8ce0(0x3cf)](),
                this[_0x5b8ce0(0x22a)][_0x5b8ce0(0x3ae)]() === _0x5b8ce0(0x325)
                    ? this['cmdGFDMenuRevert']()
                    : this['_gfdMenuWindow']['selectSymbol']('revert'));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xcf)] = function () {
            const _0x4c7dd6 = _0x9a8c74;
            (($gameTemp['_doodadEditorMode'] = ![]),
                this[_0x4c7dd6(0x203)](),
                $gamePlayer[_0x4c7dd6(0x292)]($gamePlayer['x'], $gamePlayer['y']),
                DoodadManager[_0x4c7dd6(0xa8)](),
                this['_spriteset'][_0x4c7dd6(0x2a8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x203)] = function () {
            const _0x1c2ed7 = _0x9a8c74;
            for (const _0x3aef81 of this[_0x1c2ed7(0xfc)]) {
                if (!_0x3aef81) continue;
                (_0x3aef81[_0x1c2ed7(0x143)](),
                    _0x3aef81['close'](),
                    this[_0x1c2ed7(0x1b5)](_0x3aef81));
            }
            delete this[_0x1c2ed7(0xfc)];
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x14c)] = function () {
            const _0x54c304 = _0x9a8c74;
            (this[_0x54c304(0x22a)]['close'](),
                this['_gfdListWindow']['open'](),
                this[_0x54c304(0x148)][_0x54c304(0x3cf)](),
                this[_0x54c304(0x148)][_0x54c304(0xa8)](),
                this[_0x54c304(0x148)]['smoothSelect'](0x0));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x165)] = function () {
            const _0x337343 = _0x9a8c74;
            (StorageManager['saveDoodadSettings'](),
                ($gameTemp[_0x337343(0xce)] = JsonEx['makeDeepCopy']($dataDoodads)),
                SoundManager[_0x337343(0x381)](),
                this['closeGFDMode']());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x299)] = function () {
            const _0x384b9f = _0x9a8c74;
            (($dataDoodads = JsonEx[_0x384b9f(0x12c)]($gameTemp['_prevDoodadSettings'])),
                this[_0x384b9f(0xcf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x17b)] = function () {
            const _0x502944 = _0x9a8c74;
            (DoodadManager['clearMap'](),
                this['_gfdMenuWindow'][_0x502944(0xa8)](),
                this[_0x502944(0x22a)]['activate']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xa3)] = function () {
            const _0x5e0c30 = _0x9a8c74;
            (this[_0x5e0c30(0x22a)][_0x5e0c30(0x3cf)](),
                this[_0x5e0c30(0x2e6)][_0x5e0c30(0x2b8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDMenuImport'] = function () {
            const _0x50cc33 = _0x9a8c74;
            (this['_gfdImportWindow'][_0x50cc33(0x3cf)](),
                this[_0x50cc33(0x2e1)][_0x50cc33(0x1d6)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x29a)] = function () {
            const _0x4039a3 = _0x9a8c74;
            if (this['_gfdListWindow'][_0x4039a3(0xb8)]()['length'] <= 0x0)
                (this[_0x4039a3(0x148)][_0x4039a3(0xdf)](),
                    this[_0x4039a3(0x22a)]['open'](),
                    this[_0x4039a3(0x22a)][_0x4039a3(0x3cf)](),
                    this[_0x4039a3(0x22a)][_0x4039a3(0xa8)](),
                    this['_gfdMenuWindow'][_0x4039a3(0x11d)](0x0));
            else {
                this[_0x4039a3(0x148)][_0x4039a3(0x3cf)]();
                const _0xa21261 = this[_0x4039a3(0x148)]['deleteFolder']();
                (this['_gfdListWindow'][_0x4039a3(0xa8)](),
                    this[_0x4039a3(0x148)][_0x4039a3(0x18f)](_0xa21261));
            }
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x297)] = function () {
            const _0x12afb5 = _0x9a8c74,
                _0x9fb61a = this[_0x12afb5(0x148)][_0x12afb5(0x3c3)]();
            (this['_gfdListWindow'][_0x12afb5(0x2ad)](_0x9fb61a),
                this['_gfdListWindow'][_0x12afb5(0x3cf)](),
                this['_gfdListWindow']['refresh'](),
                this[_0x12afb5(0x148)][_0x12afb5(0x11d)](0x0));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2f4)] = function () {
            const _0x455227 = _0x9a8c74;
            (this[_0x455227(0x148)][_0x455227(0xdf)](),
                this[_0x455227(0x122)][_0x455227(0x1d6)](),
                this[_0x455227(0x122)][_0x455227(0x3cf)](),
                this[_0x455227(0x122)][_0x455227(0x11d)](0x0));
        }),
        (Scene_Map['prototype']['cmdGFDListTiles'] = function () {
            const _0x294e05 = _0x9a8c74;
            (this[_0x294e05(0x148)]['close'](),
                this[_0x294e05(0xac)]['open'](),
                this['_gfdTileWindow'][_0x294e05(0x3cf)](),
                this[_0x294e05(0xac)][_0x294e05(0x11d)](0x0));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1ec)] = function () {
            const _0x49f0a7 = _0x9a8c74;
            (this[_0x49f0a7(0x122)][_0x49f0a7(0xdf)](),
                this[_0x49f0a7(0x148)][_0x49f0a7(0x1d6)](),
                this[_0x49f0a7(0x148)][_0x49f0a7(0x3cf)](),
                this['_gfdListWindow'][_0x49f0a7(0xa8)](),
                this['_gfdListWindow'][_0x49f0a7(0x11d)](0x0));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x39e)] = function () {
            const _0x154256 = _0x9a8c74;
            (this[_0x154256(0xac)]['close'](),
                this['_gfdListWindow'][_0x154256(0x1d6)](),
                this['_gfdListWindow']['activate'](),
                this[_0x154256(0x148)][_0x154256(0xa8)](),
                this[_0x154256(0x148)][_0x154256(0x11d)](0x0));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x163)] = function () {
            const _0x2681b5 = _0x9a8c74;
            (this[_0x2681b5(0x122)][_0x2681b5(0xdf)](),
                (this[_0x2681b5(0x8e)] = DoodadManager[_0x2681b5(0x32b)]('', _0x2681b5(0x3ce))),
                (this[_0x2681b5(0x8e)][_0x2681b5(0x235)] =
                    this[_0x2681b5(0x122)][_0x2681b5(0x357)]()),
                this[_0x2681b5(0x2f3)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDTileId'] = function () {
            const _0x2c89e8 = _0x9a8c74;
            (this[_0x2c89e8(0xac)][_0x2c89e8(0xdf)](),
                (this[_0x2c89e8(0x8e)] = DoodadManager[_0x2c89e8(0x32b)]('', _0x2c89e8(0xbe))),
                (this[_0x2c89e8(0x8e)][_0x2c89e8(0x9b)] = this[_0x2c89e8(0xac)][_0x2c89e8(0x9b)]()),
                this['enterDoodadPlacingMode']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDListFile'] = function () {
            const _0x2fdbb7 = _0x9a8c74;
            this['_gfdListWindow'][_0x2fdbb7(0xdf)]();
            const _0x59866a = this[_0x2fdbb7(0x148)][_0x2fdbb7(0x24c)](),
                _0x249508 = this[_0x2fdbb7(0x148)][_0x2fdbb7(0x3c3)]();
            ((this[_0x2fdbb7(0x8e)] = DoodadManager[_0x2fdbb7(0x32b)](_0x59866a, _0x249508)),
                (this['_currentDoodad'][_0x2fdbb7(0x110)] =
                    DoodadManager[_0x2fdbb7(0x373)](_0x249508)),
                (this[_0x2fdbb7(0x8e)]['yFrames'] = DoodadManager[_0x2fdbb7(0x3dc)](_0x249508)),
                this['enterDoodadPlacingMode']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2f3)] = function () {
            const _0x4738a6 = _0x9a8c74;
            (DoodadManager[_0x4738a6(0xe0)](!![]),
                this[_0x4738a6(0x2e6)][_0x4738a6(0xfd)](this[_0x4738a6(0x8e)]),
                this[_0x4738a6(0x1c3)][_0x4738a6(0x1d6)](),
                this[_0x4738a6(0x1c3)]['show'](),
                this[_0x4738a6(0x1c3)]['activate'](),
                this[_0x4738a6(0x1c3)][_0x4738a6(0xa8)](),
                this[_0x4738a6(0xa5)][_0x4738a6(0x1a7)](),
                this[_0x4738a6(0xa5)][_0x4738a6(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x2fe)] = function () {
            const _0x4ad048 = _0x9a8c74;
            (DoodadManager['setCanvasMode'](![]),
                this[_0x4ad048(0xa5)][_0x4ad048(0x12e)](),
                this['_gfdCanvasWindow'][_0x4ad048(0xdf)](),
                this[_0x4ad048(0x1c3)][_0x4ad048(0x12e)](),
                this[_0x4ad048(0x1c3)]['deactivate']());
            if (DoodadManager[_0x4ad048(0x1bf)] && DoodadManager['_editMode'])
                (this[_0x4ad048(0x36f)]['open'](),
                    this['_gfdSettingsWindow'][_0x4ad048(0x3cf)](),
                    this[_0x4ad048(0x36f)]['refresh']());
            else {
                if (this[_0x4ad048(0x8e)][_0x4ad048(0x9b)] > 0x0)
                    (this['_gfdTileWindow']['open'](), this['_gfdTileWindow'][_0x4ad048(0x3cf)]());
                else
                    this[_0x4ad048(0x8e)][_0x4ad048(0x235)] <= 0x0
                        ? (this[_0x4ad048(0x148)][_0x4ad048(0x1d6)](),
                          this[_0x4ad048(0x148)][_0x4ad048(0x3cf)]())
                        : (this['_gfdIconWindow'][_0x4ad048(0x1d6)](),
                          this[_0x4ad048(0x122)][_0x4ad048(0x3cf)]());
            }
            this[_0x4ad048(0x2e6)]['clearDoodadCursor']();
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x24a)] = function (_0x44555f) {
            const _0x1c939f = _0x9a8c74;
            (this['_gfdSettingsWindow'][_0x1c939f(0x1e1)](_0x44555f),
                this[_0x1c939f(0x36f)][_0x1c939f(0x1d6)](),
                this[_0x1c939f(0x36f)][_0x1c939f(0x3cf)](),
                this[_0x1c939f(0x36f)][_0x1c939f(0x27e)]('accept'),
                DoodadManager['setSettingsMode'](!![]),
                this[_0x1c939f(0x1c3)][_0x1c939f(0x12e)](),
                this[_0x1c939f(0x1c3)][_0x1c939f(0x143)](),
                this['_gfdDrawGridWindow'][_0x1c939f(0x12e)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xeb)] = function () {
            const _0x543835 = _0x9a8c74;
            (this['_gfdSettingsWindow'][_0x543835(0x3cf)](),
                this[_0x543835(0x36f)][_0x543835(0x3ae)]() === 'revert'
                    ? (this[_0x543835(0x36f)][_0x543835(0x325)](), this[_0x543835(0x406)]())
                    : this[_0x543835(0x36f)]['selectSymbol']('revert'));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x3da)] = function () {
            const _0x6d7ce7 = _0x9a8c74;
            (this['_gfdSettingsWindow']['revert'](),
                this['_gfdSettingsWindow'][_0x6d7ce7(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x406)] = function () {
            const _0x1b3878 = _0x9a8c74;
            (DoodadManager[_0x1b3878(0x255)](![]), this[_0x1b3878(0x36f)][_0x1b3878(0xdf)]());
            if (DoodadManager[_0x1b3878(0xa2)]) {
                (this[_0x1b3878(0x1c3)][_0x1b3878(0x1a7)](),
                    this['_gfdCanvasWindow'][_0x1b3878(0x3cf)](),
                    this[_0x1b3878(0xa5)]['show'](),
                    this[_0x1b3878(0xa5)][_0x1b3878(0xa8)]());
                return;
            }
            if (DoodadManager[_0x1b3878(0x1da)]) {
                (this['_gfdPickDoodadListWindow'][_0x1b3878(0x1d6)](),
                    this['_gfdPickDoodadListWindow'][_0x1b3878(0xa8)](),
                    this[_0x1b3878(0x16d)]['activate']());
                let _0x304df9 = this['_gfdPickDoodadListWindow']['index']();
                ((_0x304df9 = Math['min'](
                    _0x304df9,
                    this[_0x1b3878(0x16d)][_0x1b3878(0x2db)]() - 0x1
                )),
                    this['_gfdPickDoodadListWindow'][_0x1b3878(0x112)](_0x304df9));
            }
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x303)] = function () {
            const _0x4af230 = _0x9a8c74;
            (this[_0x4af230(0x347)][_0x4af230(0x3cf)](),
                this[_0x4af230(0x347)][_0x4af230(0x1d6)]());
            const _0x337147 = this[_0x4af230(0x36f)][_0x4af230(0x243)],
                _0x5eb41f = _0x337147['z'];
            this['_gfdSettingsLayersWindow'][_0x4af230(0x18f)](_0x5eb41f);
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x137)] = function () {
            const _0x3edd31 = _0x9a8c74;
            (this[_0x3edd31(0x347)]['close'](), this[_0x3edd31(0x36f)][_0x3edd31(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x85)] = function () {
            const _0x1d5d26 = _0x9a8c74,
                _0x2d2692 = this[_0x1d5d26(0x347)][_0x1d5d26(0x3c3)](),
                _0x5250f8 = this[_0x1d5d26(0x36f)][_0x1d5d26(0x243)];
            ((_0x5250f8['z'] = _0x2d2692),
                DoodadManager[_0x1d5d26(0x277)](),
                this[_0x1d5d26(0x137)](),
                this[_0x1d5d26(0x36f)][_0x1d5d26(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xf1)] = function () {
            const _0x3c5869 = _0x9a8c74;
            (this['_gfdSettingsHueWindow']['activate'](),
                this[_0x3c5869(0x3e2)][_0x3c5869(0x1d6)]());
            const _0x778517 = this['_gfdSettingsWindow']['_doodad'],
                _0x52c9f2 = Math[_0x3c5869(0x223)](_0x778517[_0x3c5869(0x20d)] / 0xa) * 0xa;
            (this['_gfdSettingsHueWindow'][_0x3c5869(0x18f)](_0x52c9f2),
                this[_0x3c5869(0x3e2)][_0x3c5869(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xed)] = function () {
            const _0x2342dd = _0x9a8c74;
            (this[_0x2342dd(0x3e2)][_0x2342dd(0xdf)](), this[_0x2342dd(0x36f)][_0x2342dd(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettHueOk'] = function () {
            const _0x5df82f = _0x9a8c74,
                _0x1b42ef = this['_gfdSettingsHueWindow'][_0x5df82f(0x3c3)](),
                _0x5e2043 = this[_0x5df82f(0x36f)][_0x5df82f(0x243)];
            ((_0x5e2043[_0x5df82f(0x20d)] = _0x1b42ef),
                DoodadManager[_0x5df82f(0x277)](),
                this[_0x5df82f(0xed)](),
                this['_gfdSettingsWindow']['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x92)] = function () {
            const _0x5b4446 = _0x9a8c74;
            (this[_0x5b4446(0x176)][_0x5b4446(0x3cf)](), this[_0x5b4446(0x176)]['open']());
            const _0x3c37b6 = this[_0x5b4446(0x36f)][_0x5b4446(0x243)];
            let _0x3ef081 = 0x0;
            for (
                let _0x18737f = 0x0;
                _0x18737f < this[_0x5b4446(0x176)][_0x5b4446(0x2db)]();
                _0x18737f++
            ) {
                const _0x1bbf85 =
                    this[_0x5b4446(0x176)][_0x5b4446(0x270)][_0x18737f][_0x5b4446(0x164)];
                _0x3c37b6['opacity'] <= _0x1bbf85 && (_0x3ef081 = _0x18737f);
            }
            (this['_gfdSettingsOpacityWindow'][_0x5b4446(0x112)](_0x3ef081),
                this[_0x5b4446(0x176)]['refresh']());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0xc0)] = function () {
            const _0x539046 = _0x9a8c74;
            (this[_0x539046(0x176)][_0x539046(0xdf)](), this[_0x539046(0x36f)][_0x539046(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettOpacityOk'] = function () {
            const _0x3f65e0 = _0x9a8c74,
                _0x49e267 = this[_0x3f65e0(0x176)]['currentExt'](),
                _0x11e9b8 = this[_0x3f65e0(0x36f)]['_doodad'];
            ((_0x11e9b8[_0x3f65e0(0x7d)] = _0x49e267),
                DoodadManager[_0x3f65e0(0x277)](),
                this[_0x3f65e0(0xc0)](),
                this[_0x3f65e0(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xbb)] = function () {
            const _0x538d32 = _0x9a8c74;
            (this[_0x538d32(0x26f)][_0x538d32(0x3cf)](),
                this[_0x538d32(0x26f)][_0x538d32(0x1d6)]());
            const _0x144c52 = this[_0x538d32(0x36f)][_0x538d32(0x243)],
                _0x8b9918 =
                    this[_0x538d32(0x36f)][_0x538d32(0x3ae)]() === _0x538d32(0x378)
                        ? _0x144c52[_0x538d32(0x378)]
                        : _0x144c52[_0x538d32(0x3c0)];
            let _0x58f5d7 = 0x0;
            for (
                let _0x1b7671 = 0x0;
                _0x1b7671 < this[_0x538d32(0x26f)]['maxItems']();
                _0x1b7671++
            ) {
                const _0x17dc26 = this[_0x538d32(0x26f)]['_list'][_0x1b7671][_0x538d32(0x164)];
                _0x8b9918 <= _0x17dc26 && (_0x58f5d7 = _0x1b7671);
            }
            (this['_gfdSettingsScaleWindow'][_0x538d32(0x112)](_0x58f5d7),
                this['_gfdSettingsScaleWindow'][_0x538d32(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3ba)] = function () {
            const _0x4ef907 = _0x9a8c74;
            (this['_gfdSettingsScaleWindow'][_0x4ef907(0xdf)](),
                this[_0x4ef907(0x36f)][_0x4ef907(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettScaleOk'] = function () {
            const _0x20dc33 = _0x9a8c74,
                _0x58a91e = this['_gfdSettingsScaleWindow'][_0x20dc33(0x3c3)](),
                _0x43cd45 = this[_0x20dc33(0x36f)][_0x20dc33(0x243)];
            (this[_0x20dc33(0x36f)][_0x20dc33(0x3ae)]() === _0x20dc33(0x378)
                ? (_0x43cd45[_0x20dc33(0x378)] = _0x58a91e)
                : (_0x43cd45[_0x20dc33(0x3c0)] = _0x58a91e),
                DoodadManager[_0x20dc33(0x277)](),
                this[_0x20dc33(0x3ba)](),
                this[_0x20dc33(0x36f)][_0x20dc33(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x345)] = function () {
            const _0x5d5ee2 = _0x9a8c74;
            (this[_0x5d5ee2(0x157)][_0x5d5ee2(0x3cf)](),
                this[_0x5d5ee2(0x157)][_0x5d5ee2(0x1d6)]());
            const _0x13277f = this[_0x5d5ee2(0x36f)]['_doodad'];
            (this['_gfdSettingsPositionTypeWindow'][_0x5d5ee2(0x18f)](_0x13277f[_0x5d5ee2(0x111)]),
                this['_gfdSettingsPositionTypeWindow'][_0x5d5ee2(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x23e)] = function () {
            const _0xa51d48 = _0x9a8c74;
            (this[_0xa51d48(0x157)][_0xa51d48(0xdf)](), this[_0xa51d48(0x36f)][_0xa51d48(0x3cf)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x301)] = function () {
            const _0x59838c = _0x9a8c74,
                _0xf909be = this['_gfdSettingsPositionTypeWindow'][_0x59838c(0x3c3)](),
                _0x1bf8f6 = this[_0x59838c(0x36f)][_0x59838c(0x243)];
            ((_0x1bf8f6[_0x59838c(0x111)] = _0xf909be),
                DoodadManager[_0x59838c(0x277)](),
                this[_0x59838c(0x23e)](),
                this[_0x59838c(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x320)] = function () {
            const _0x33d4d4 = _0x9a8c74;
            (this[_0x33d4d4(0x25e)][_0x33d4d4(0x3cf)](),
                this['_gfdSettingsAngleWindow'][_0x33d4d4(0x1d6)]());
            const _0x50ee1d = this[_0x33d4d4(0x36f)][_0x33d4d4(0x243)];
            (this[_0x33d4d4(0x25e)][_0x33d4d4(0x18f)](_0x50ee1d[_0x33d4d4(0x1c9)]),
                this[_0x33d4d4(0x25e)][_0x33d4d4(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x202)] = function () {
            const _0x55d548 = _0x9a8c74;
            (this['_gfdSettingsAngleWindow'][_0x55d548(0xdf)](),
                this[_0x55d548(0x36f)][_0x55d548(0x3cf)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x19e)] = function () {
            const _0x3e1ee4 = _0x9a8c74,
                _0x11281b = this[_0x3e1ee4(0x25e)]['currentExt'](),
                _0x1924d0 = this['_gfdSettingsWindow'][_0x3e1ee4(0x243)];
            ((_0x1924d0[_0x3e1ee4(0x1c9)] = _0x11281b),
                DoodadManager[_0x3e1ee4(0x277)](),
                this[_0x3e1ee4(0x202)](),
                this['_gfdSettingsWindow'][_0x3e1ee4(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x30b)] = function () {
            const _0x201587 = _0x9a8c74;
            (this[_0x201587(0x2a9)][_0x201587(0x3cf)](),
                this[_0x201587(0x2a9)][_0x201587(0x1d6)]());
            const _0x397e17 = this[_0x201587(0x36f)]['_doodad'];
            (this['_gfdSettingsIconIndexWindow'][_0x201587(0x18f)](_0x397e17[_0x201587(0x235)]),
                this[_0x201587(0x2a9)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3a3)] = function () {
            const _0x373791 = _0x9a8c74;
            (this['_gfdSettingsIconIndexWindow'][_0x373791(0xdf)](),
                this[_0x373791(0x36f)][_0x373791(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x21a)] = function () {
            const _0x4e1ab9 = _0x9a8c74,
                _0x4c9e33 = this[_0x4e1ab9(0x2a9)][_0x4e1ab9(0x3c3)](),
                _0x2acfa6 = this[_0x4e1ab9(0x36f)]['_doodad'];
            ((_0x2acfa6[_0x4e1ab9(0x235)] = _0x4c9e33),
                DoodadManager[_0x4e1ab9(0x277)](),
                this['cancelGFDSettIconIndex'](),
                this[_0x4e1ab9(0x36f)][_0x4e1ab9(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1af)] = function () {
            const _0x537fca = _0x9a8c74;
            (this[_0x537fca(0x3f9)][_0x537fca(0x3cf)](),
                this[_0x537fca(0x3f9)][_0x537fca(0x1d6)]());
            const _0x21a303 = this[_0x537fca(0x36f)][_0x537fca(0x243)];
            (this[_0x537fca(0x3f9)][_0x537fca(0x18f)](_0x21a303[_0x537fca(0x9b)]),
                this[_0x537fca(0x3f9)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3ed)] = function () {
            const _0x484f1c = _0x9a8c74;
            (this[_0x484f1c(0x3f9)]['close'](), this[_0x484f1c(0x36f)][_0x484f1c(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xcd)] = function () {
            const _0x183202 = _0x9a8c74,
                _0x40ad16 = this['_gfdSettingsTileIdWindow']['currentExt'](),
                _0x34135d = this[_0x183202(0x36f)][_0x183202(0x243)];
            ((_0x34135d[_0x183202(0x9b)] = _0x40ad16),
                DoodadManager[_0x183202(0x277)](),
                this[_0x183202(0x3ed)](),
                this[_0x183202(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2ab)] = function () {
            const _0x480ced = _0x9a8c74;
            (this[_0x480ced(0x177)][_0x480ced(0x3cf)](),
                this[_0x480ced(0x177)][_0x480ced(0x1d6)]());
            const _0x5b8963 = this[_0x480ced(0x36f)][_0x480ced(0x243)];
            (this['_gfdSettingsTileColsWindow'][_0x480ced(0x18f)](_0x5b8963['tileCols']),
                this['_gfdSettingsTileColsWindow'][_0x480ced(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x24d)] = function () {
            const _0x45f4b9 = _0x9a8c74;
            (this['_gfdSettingsTileColsWindow'][_0x45f4b9(0xdf)](),
                this[_0x45f4b9(0x36f)][_0x45f4b9(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x194)] = function () {
            const _0x13aa1c = _0x9a8c74,
                _0x2ef02f = this['_gfdSettingsTileColsWindow']['currentExt'](),
                _0x5a5b94 = this[_0x13aa1c(0x36f)]['_doodad'];
            ((_0x5a5b94['tileCols'] = _0x2ef02f),
                DoodadManager[_0x13aa1c(0x277)](),
                this[_0x13aa1c(0x24d)](),
                this[_0x13aa1c(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1a8)] = function () {
            const _0x5bd1cd = _0x9a8c74;
            (this[_0x5bd1cd(0x3fe)]['activate'](), this[_0x5bd1cd(0x3fe)][_0x5bd1cd(0x1d6)]());
            const _0x482cc2 = this[_0x5bd1cd(0x36f)]['_doodad'];
            (this['_gfdSettingsTileRowsWindow'][_0x5bd1cd(0x18f)](_0x482cc2['tileRows']),
                this[_0x5bd1cd(0x3fe)][_0x5bd1cd(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x32e)] = function () {
            const _0x33eff6 = _0x9a8c74;
            (this[_0x33eff6(0x3fe)][_0x33eff6(0xdf)](), this[_0x33eff6(0x36f)][_0x33eff6(0x3cf)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x2c2)] = function () {
            const _0x48eb90 = _0x9a8c74,
                _0x1e42bd = this[_0x48eb90(0x3fe)][_0x48eb90(0x3c3)](),
                _0x555583 = this[_0x48eb90(0x36f)]['_doodad'];
            ((_0x555583['tileRows'] = _0x1e42bd),
                DoodadManager['updateNewSettings'](),
                this['cancelGFDSettTileRows'](),
                this[_0x48eb90(0x36f)]['refresh']());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1ac)] = function () {
            const _0x3f2468 = _0x9a8c74;
            (this[_0x3f2468(0xef)][_0x3f2468(0x3cf)](), this[_0x3f2468(0xef)][_0x3f2468(0x1d6)]());
            const _0x5dc1ba = this['_gfdSettingsWindow'][_0x3f2468(0x243)];
            (this[_0x3f2468(0xef)][_0x3f2468(0x18f)](_0x5dc1ba[_0x3f2468(0x2c7)]),
                this[_0x3f2468(0xef)][_0x3f2468(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x86)] = function () {
            const _0x5198af = _0x9a8c74;
            (this['_gfdSettingsAnchorXWindow']['close'](),
                this['_gfdSettingsWindow'][_0x5198af(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettAnchorXOk'] = function () {
            const _0x3ba0db = _0x9a8c74,
                _0xefa6a1 = this[_0x3ba0db(0xef)][_0x3ba0db(0x3c3)](),
                _0x2f37ab = this['_gfdSettingsWindow'][_0x3ba0db(0x243)];
            ((_0x2f37ab[_0x3ba0db(0x2c7)] = _0xefa6a1),
                DoodadManager['updateNewSettings'](),
                this[_0x3ba0db(0x86)](),
                this['_gfdSettingsWindow'][_0x3ba0db(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1c2)] = function () {
            const _0x3bcf20 = _0x9a8c74;
            (this[_0x3bcf20(0x9c)][_0x3bcf20(0x3cf)](),
                this['_gfdSettingsAnchorYWindow'][_0x3bcf20(0x1d6)]());
            const _0x526b54 = this[_0x3bcf20(0x36f)]['_doodad'];
            (this[_0x3bcf20(0x9c)][_0x3bcf20(0x18f)](_0x526b54[_0x3bcf20(0x31a)]),
                this[_0x3bcf20(0x9c)]['refresh']());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x14f)] = function () {
            const _0x1f1727 = _0x9a8c74;
            (this[_0x1f1727(0x9c)][_0x1f1727(0xdf)](), this[_0x1f1727(0x36f)][_0x1f1727(0x3cf)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x366)] = function () {
            const _0xc141df = _0x9a8c74,
                _0x14cfc1 = this[_0xc141df(0x9c)][_0xc141df(0x3c3)](),
                _0x5e9f2c = this[_0xc141df(0x36f)]['_doodad'];
            ((_0x5e9f2c[_0xc141df(0x31a)] = _0x14cfc1),
                DoodadManager['updateNewSettings'](),
                this[_0xc141df(0x14f)](),
                this[_0xc141df(0x36f)][_0xc141df(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x17e)] = function () {
            const _0x1cd672 = _0x9a8c74;
            (this[_0x1cd672(0x382)][_0x1cd672(0x3cf)](), this[_0x1cd672(0x382)]['open']());
            const _0x1d5889 = this[_0x1cd672(0x36f)][_0x1cd672(0x243)];
            let _0x49bae7 = 0x0;
            for (
                let _0x25ce8f = 0x0;
                _0x25ce8f < this[_0x1cd672(0x382)][_0x1cd672(0x2db)]();
                _0x25ce8f++
            ) {
                const _0x5c9fec =
                    this[_0x1cd672(0x382)][_0x1cd672(0x270)][_0x25ce8f][_0x1cd672(0x164)];
                _0x1d5889[_0x1cd672(0x1a4)] >= _0x5c9fec && (_0x49bae7 = _0x25ce8f);
            }
            (this['_gfdSettingsFrameSpeedWindow']['select'](_0x49bae7),
                this[_0x1cd672(0x382)][_0x1cd672(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3c4)] = function () {
            const _0x5c84c6 = _0x9a8c74;
            (this[_0x5c84c6(0x382)][_0x5c84c6(0xdf)](), this[_0x5c84c6(0x36f)][_0x5c84c6(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1ab)] = function () {
            const _0x4d6cb8 = _0x9a8c74,
                _0x2fde60 = this[_0x4d6cb8(0x382)][_0x4d6cb8(0x3c3)](),
                _0x346aaf = this['_gfdSettingsWindow'][_0x4d6cb8(0x243)];
            ((_0x346aaf[_0x4d6cb8(0x1a4)] = _0x2fde60),
                DoodadManager[_0x4d6cb8(0x277)](),
                this[_0x4d6cb8(0x3c4)](),
                this['_gfdSettingsWindow'][_0x4d6cb8(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x28d)] = function () {
            const _0x23930b = _0x9a8c74;
            (this['_gfdSettingsBlendWindow'][_0x23930b(0x3cf)](),
                this['_gfdSettingsBlendWindow'][_0x23930b(0x1d6)]());
            const _0x54e0ef = this[_0x23930b(0x36f)][_0x23930b(0x243)];
            (this[_0x23930b(0x1ed)][_0x23930b(0x18f)](_0x54e0ef[_0x23930b(0x1d4)]),
                this[_0x23930b(0x1ed)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3e5)] = function () {
            const _0x49040c = _0x9a8c74;
            (this[_0x49040c(0x1ed)]['close'](), this[_0x49040c(0x36f)][_0x49040c(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x124)] = function () {
            const _0x3dc29d = _0x9a8c74,
                _0x410074 = this['_gfdSettingsBlendWindow']['currentExt'](),
                _0x410699 = this[_0x3dc29d(0x36f)][_0x3dc29d(0x243)];
            ((_0x410699[_0x3dc29d(0x1d4)] = _0x410074),
                DoodadManager['updateNewSettings'](),
                this[_0x3dc29d(0x3e5)](),
                this[_0x3dc29d(0x36f)][_0x3dc29d(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2d9)] = function () {
            const _0x4515cc = _0x9a8c74;
            (this[_0x4515cc(0x240)][_0x4515cc(0x3cf)](), this[_0x4515cc(0x240)]['open']());
            const _0x5c2ea4 = this[_0x4515cc(0x36f)]['_doodad'];
            (this[_0x4515cc(0x240)][_0x4515cc(0x18f)](_0x5c2ea4[_0x4515cc(0xe9)]),
                this[_0x4515cc(0x240)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDSettSmooth'] = function () {
            const _0x23d681 = _0x9a8c74;
            (this[_0x23d681(0x240)][_0x23d681(0xdf)](), this[_0x23d681(0x36f)][_0x23d681(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettSmoothOk'] = function () {
            const _0x581601 = _0x9a8c74,
                _0xea20d4 = this['_gfdSettingsSmoothWindow'][_0x581601(0x3c3)](),
                _0x92cd3b = this[_0x581601(0x36f)][_0x581601(0x243)];
            ((_0x92cd3b[_0x581601(0xe9)] = _0xea20d4),
                DoodadManager[_0x581601(0x277)](),
                this[_0x581601(0x218)](),
                this[_0x581601(0x36f)][_0x581601(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettingsContrast'] = function () {
            const _0xb25b81 = _0x9a8c74;
            (this['_gfdSettingsContrastWindow'][_0xb25b81(0x3cf)](),
                this[_0xb25b81(0x2bb)]['open']());
            const _0x206650 = this[_0xb25b81(0x36f)]['_doodad'];
            (this[_0xb25b81(0x2bb)]['selectExt'](_0x206650['contrast']),
                this[_0xb25b81(0x2bb)][_0xb25b81(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x178)] = function () {
            const _0x189140 = _0x9a8c74;
            (this['_gfdSettingsContrastWindow'][_0x189140(0xdf)](),
                this['_gfdSettingsWindow']['activate']());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x2d4)] = function () {
            const _0x4eb987 = _0x9a8c74,
                _0x19a4f8 = this[_0x4eb987(0x2bb)][_0x4eb987(0x3c3)](),
                _0x4ec453 = this['_gfdSettingsWindow'][_0x4eb987(0x243)];
            ((_0x4ec453[_0x4eb987(0x147)] = _0x19a4f8),
                DoodadManager['updateNewSettings'](),
                this['cancelGFDSettContrast'](),
                this[_0x4eb987(0x36f)][_0x4eb987(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2b0)] = function () {
            const _0x1dba95 = _0x9a8c74;
            (this[_0x1dba95(0x390)]['activate'](), this[_0x1dba95(0x390)][_0x1dba95(0x1d6)]());
            const _0x33e136 = this[_0x1dba95(0x36f)][_0x1dba95(0x243)];
            (this[_0x1dba95(0x390)]['selectExt'](_0x33e136['sepia']),
                this[_0x1dba95(0x390)][_0x1dba95(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x281)] = function () {
            const _0x5dbc42 = _0x9a8c74;
            (this[_0x5dbc42(0x390)][_0x5dbc42(0xdf)](), this[_0x5dbc42(0x36f)]['activate']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xb0)] = function () {
            const _0x428b93 = _0x9a8c74,
                _0x1a5fec = this[_0x428b93(0x390)][_0x428b93(0x3c3)](),
                _0x3b8f3f = this['_gfdSettingsWindow'][_0x428b93(0x243)];
            ((_0x3b8f3f[_0x428b93(0x2a5)] = _0x1a5fec),
                DoodadManager[_0x428b93(0x277)](),
                this[_0x428b93(0x281)](),
                this['_gfdSettingsWindow'][_0x428b93(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettingsBlur'] = function () {
            const _0x5a4643 = _0x9a8c74;
            (this['_gfdSettingsBlurWindow'][_0x5a4643(0x3cf)](),
                this['_gfdSettingsBlurWindow']['open']());
            const _0x493eb8 = this[_0x5a4643(0x36f)]['_doodad'];
            (this[_0x5a4643(0x138)][_0x5a4643(0x18f)](_0x493eb8[_0x5a4643(0x1e3)]),
                this[_0x5a4643(0x138)][_0x5a4643(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x31b)] = function () {
            const _0x48cd4c = _0x9a8c74;
            (this[_0x48cd4c(0x138)]['close'](), this[_0x48cd4c(0x36f)][_0x48cd4c(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1cd)] = function () {
            const _0x382cf6 = _0x9a8c74,
                _0x1cd922 = this[_0x382cf6(0x138)][_0x382cf6(0x3c3)](),
                _0x1563f0 = this['_gfdSettingsWindow'][_0x382cf6(0x243)];
            ((_0x1563f0[_0x382cf6(0x1e3)] = _0x1cd922),
                DoodadManager['updateNewSettings'](),
                this[_0x382cf6(0x31b)](),
                this[_0x382cf6(0x36f)][_0x382cf6(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x168)] = function () {
            const _0x363ba3 = _0x9a8c74;
            (this[_0x363ba3(0x26a)]['activate'](), this[_0x363ba3(0x26a)][_0x363ba3(0x1d6)]());
            const _0x18bab6 = this[_0x363ba3(0x36f)][_0x363ba3(0x243)];
            (this[_0x363ba3(0x26a)][_0x363ba3(0x18f)](_0x18bab6['outline']),
                this[_0x363ba3(0x26a)][_0x363ba3(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDSettOutline'] = function () {
            const _0x34203e = _0x9a8c74;
            (this['_gfdSettingsOutlineWindow'][_0x34203e(0xdf)](),
                this[_0x34203e(0x36f)][_0x34203e(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xdb)] = function () {
            const _0x526094 = _0x9a8c74,
                _0x301bee = this['_gfdSettingsOutlineWindow'][_0x526094(0x3c3)](),
                _0x50ff14 = this[_0x526094(0x36f)][_0x526094(0x243)];
            ((_0x50ff14['outline'] = _0x301bee),
                DoodadManager['updateNewSettings'](),
                this['cancelGFDSettOutline'](),
                this[_0x526094(0x36f)][_0x526094(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x36e)] = function () {
            const _0x506a8e = _0x9a8c74;
            (this['_gfdSettingsShadowWindow'][_0x506a8e(0x3cf)](),
                this['_gfdSettingsShadowWindow'][_0x506a8e(0x1d6)]());
            const _0x1e3f0b = this[_0x506a8e(0x36f)][_0x506a8e(0x243)];
            (this['_gfdSettingsShadowWindow'][_0x506a8e(0x18f)](_0x1e3f0b[_0x506a8e(0x252)]),
                this[_0x506a8e(0x2e5)][_0x506a8e(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1d2)] = function () {
            const _0x5326a5 = _0x9a8c74;
            (this[_0x5326a5(0x2e5)][_0x5326a5(0xdf)](),
                this['_gfdSettingsWindow'][_0x5326a5(0x3cf)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1ff)] = function () {
            const _0x3e7c57 = _0x9a8c74,
                _0x1b9592 = this[_0x3e7c57(0x2e5)][_0x3e7c57(0x3c3)](),
                _0x46db52 = this['_gfdSettingsWindow'][_0x3e7c57(0x243)];
            ((_0x46db52['shadow'] = _0x1b9592),
                DoodadManager['updateNewSettings'](),
                this['cancelGFDSettShadow'](),
                this['_gfdSettingsWindow']['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettingsGlow'] = function () {
            const _0x1ff0b = _0x9a8c74;
            (this[_0x1ff0b(0x3b2)][_0x1ff0b(0x3cf)](), this[_0x1ff0b(0x3b2)][_0x1ff0b(0x1d6)]());
            const _0x498ebc = this[_0x1ff0b(0x36f)]['_doodad'];
            (this[_0x1ff0b(0x3b2)][_0x1ff0b(0x18f)](_0x498ebc['glow']),
                this[_0x1ff0b(0x3b2)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDSettGlow'] = function () {
            const _0x4a9708 = _0x9a8c74;
            (this[_0x4a9708(0x3b2)][_0x4a9708(0xdf)](), this['_gfdSettingsWindow']['activate']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3fa)] = function () {
            const _0x3da745 = _0x9a8c74,
                _0x5570e0 = this[_0x3da745(0x3b2)][_0x3da745(0x3c3)](),
                _0xd7661c = this[_0x3da745(0x36f)][_0x3da745(0x243)];
            ((_0xd7661c[_0x3da745(0x185)] = _0x5570e0),
                DoodadManager['updateNewSettings'](),
                this[_0x3da745(0x135)](),
                this[_0x3da745(0x36f)][_0x3da745(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x361)] = function () {
            const _0x15b42b = _0x9a8c74;
            (this[_0x15b42b(0x1c3)]['hide'](),
                this[_0x15b42b(0x1c3)][_0x15b42b(0x143)](),
                this[_0x15b42b(0x33a)][_0x15b42b(0x1d6)](),
                this[_0x15b42b(0x33a)][_0x15b42b(0xa8)](),
                this[_0x15b42b(0x33a)][_0x15b42b(0x3cf)](),
                this[_0x15b42b(0x33a)][_0x15b42b(0x11d)](0x0),
                this[_0x15b42b(0xa5)][_0x15b42b(0x1d6)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDGridMenu'] = function () {
            const _0x5c8c67 = _0x9a8c74;
            (this[_0x5c8c67(0x1c3)][_0x5c8c67(0x1a7)](),
                this['_gfdCanvasWindow'][_0x5c8c67(0x3cf)](),
                this[_0x5c8c67(0x33a)][_0x5c8c67(0xdf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x319)] = function () {
            const _0x1b685d = _0x9a8c74;
            (DoodadManager[_0x1b685d(0xaf)](!DoodadManager[_0x1b685d(0x237)]),
                this['_gfdGridMenuWindow'][_0x1b685d(0x3cf)](),
                this[_0x1b685d(0x33a)][_0x1b685d(0xa8)](),
                this[_0x1b685d(0xa5)][_0x1b685d(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x2e8)] = function () {
            const _0x336c7e = _0x9a8c74;
            (this[_0x336c7e(0x22a)][_0x336c7e(0xdf)](),
                this['_gfdPickDoodadLayerWindow']['activate'](),
                this[_0x336c7e(0x24f)][_0x336c7e(0x1d6)](),
                this[_0x336c7e(0x24f)]['refresh'](),
                this['_gfdPickDoodadLayerWindow'][_0x336c7e(0x11d)](0x0));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDPickDoodadLayer'] = function () {
            const _0x3d5097 = _0x9a8c74;
            (this[_0x3d5097(0x22a)][_0x3d5097(0x3cf)](),
                this[_0x3d5097(0x22a)][_0x3d5097(0x1d6)](),
                this[_0x3d5097(0x22a)]['refresh'](),
                this[_0x3d5097(0x24f)]['close']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['openGFDPickDoodadList'] = function () {
            const _0x509355 = _0x9a8c74;
            DoodadManager['setEditMode'](!![]);
            const _0x1fc22d = this[_0x509355(0x24f)][_0x509355(0x3c3)]();
            (this['_gfdPickDoodadListWindow'][_0x509355(0x3cf)](),
                this['_gfdPickDoodadListWindow'][_0x509355(0x1d6)](),
                this['_gfdPickDoodadListWindow'][_0x509355(0x300)](_0x1fc22d),
                this[_0x509355(0x24f)][_0x509355(0xdf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xfb)] = function () {
            const _0x5e9398 = _0x9a8c74;
            (DoodadManager['setEditMode'](![]),
                this[_0x5e9398(0x24f)][_0x5e9398(0x3cf)](),
                this['_gfdPickDoodadLayerWindow'][_0x5e9398(0x1d6)](),
                this[_0x5e9398(0x24f)][_0x5e9398(0xa8)](),
                this[_0x5e9398(0x16d)]['close'](),
                $gameMap[_0x5e9398(0x37e)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDDoodadListSelect'] = function () {
            const _0x2a7596 = _0x9a8c74;
            this['_gfdPickDoodadListWindow']['close']();
            const _0x50ee80 = this[_0x2a7596(0x16d)]['currentExt']();
            this[_0x2a7596(0x24a)](_0x50ee80);
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x15c)] = function () {
            const _0x552050 = _0x9a8c74;
            (this[_0x552050(0x2e1)][_0x552050(0xdf)](), this[_0x552050(0x22a)]['activate']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdCFDImportMap'] = function () {
            const _0x16bd97 = _0x9a8c74;
            (this['_gfdImportWindow']['close'](), this[_0x16bd97(0x22a)]['activate']());
            const _0x439365 = parseInt(this[_0x16bd97(0x2e1)][_0x16bd97(0x3c3)]());
            $dataDoodads[_0x439365] = $dataDoodads[_0x439365] || [];
            const _0x5980f6 = JsonEx['makeDeepCopy']($dataDoodads[_0x439365]);
            (($dataDoodads[$gameMap[_0x16bd97(0x131)]()] = _0x5980f6),
                DoodadManager[_0x16bd97(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x217)] = function () {
            const _0x4a2d2b = _0x9a8c74,
                _0x3ed522 = this[_0x4a2d2b(0x36f)][_0x4a2d2b(0x243)];
            (DoodadManager['delete'](_0x3ed522),
                SoundManager['playUseSkill'](),
                this['cmdGFDSettingsAccept']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettingsPosition'] = function () {
            const _0x13a94d = _0x9a8c74;
            ((this[_0x13a94d(0x8e)] = this[_0x13a94d(0x36f)][_0x13a94d(0x243)]),
                this[_0x13a94d(0x2f3)](),
                this[_0x13a94d(0x36f)][_0x13a94d(0xdf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x284)] = function () {
            const _0x20d3e3 = _0x9a8c74;
            ((this[_0x20d3e3(0x1e2)] = new Window_Doodads_Settings_Party()),
                this[_0x20d3e3(0x2c5)](this[_0x20d3e3(0x1e2)]),
                this['_gfdWindows'][_0x20d3e3(0x1cc)](this[_0x20d3e3(0x1e2)]));
            const _0x2eb7ac = this[_0x20d3e3(0x1e2)];
            (_0x2eb7ac[_0x20d3e3(0x3e1)](
                _0x20d3e3(0x2bc),
                this[_0x20d3e3(0x385)][_0x20d3e3(0xee)](this)
            ),
                _0x2eb7ac[_0x20d3e3(0x3e1)](
                    _0x20d3e3(0x39d),
                    this[_0x20d3e3(0xc2)][_0x20d3e3(0xee)](this)
                ),
                _0x2eb7ac[_0x20d3e3(0x3e1)](
                    _0x20d3e3(0xd8),
                    this['cmdGFDSettPartyHave']['bind'](this)
                ),
                _0x2eb7ac[_0x20d3e3(0x3e1)](
                    _0x20d3e3(0x25b),
                    this[_0x20d3e3(0x286)][_0x20d3e3(0xee)](this)
                ));
        }),
        (Scene_Map['prototype'][_0x9a8c74(0xfa)] = function () {
            const _0x49ba99 = _0x9a8c74;
            (this['_gfdSettingsPartyWindow'][_0x49ba99(0x3cf)](),
                this['_gfdSettingsPartyWindow'][_0x49ba99(0x1d6)](),
                this[_0x49ba99(0x1e2)][_0x49ba99(0x11d)](0x0),
                this[_0x49ba99(0x1e2)][_0x49ba99(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cancelGFDSettParty'] = function () {
            const _0x266e60 = _0x9a8c74;
            (this[_0x266e60(0x1e2)][_0x266e60(0xdf)](), this[_0x266e60(0x36f)][_0x266e60(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xc2)] = function () {
            const _0x523d92 = _0x9a8c74,
                _0x59e5bd = this[_0x523d92(0x1e2)]['currentExt'](),
                _0x42c0c3 = this['_gfdSettingsWindow'][_0x523d92(0x243)];
            _0x42c0c3[_0x523d92(0xd8)] = _0x42c0c3[_0x523d92(0xd8)] || [];
            let _0x32f84e = _0x42c0c3['partyHave'][_0x523d92(0x3d1)](_0x59e5bd);
            (_0x32f84e >= 0x0 && _0x42c0c3[_0x523d92(0xd8)][_0x523d92(0x3b1)](_0x32f84e, 0x1),
                (_0x42c0c3[_0x523d92(0x25b)] = _0x42c0c3[_0x523d92(0x25b)] || []),
                (_0x32f84e = _0x42c0c3[_0x523d92(0x25b)][_0x523d92(0x3d1)](_0x59e5bd)),
                _0x32f84e >= 0x0 && _0x42c0c3[_0x523d92(0x25b)][_0x523d92(0x3b1)](_0x32f84e, 0x1),
                DoodadManager[_0x523d92(0x277)](),
                this[_0x523d92(0x36f)][_0x523d92(0xa8)](),
                this['_gfdSettingsPartyWindow']['activate'](),
                this[_0x523d92(0x1e2)][_0x523d92(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettPartyHave'] = function () {
            const _0x56fc1a = _0x9a8c74,
                _0x5c8bbb = this[_0x56fc1a(0x1e2)][_0x56fc1a(0x3c3)](),
                _0x57a73c = this[_0x56fc1a(0x36f)][_0x56fc1a(0x243)];
            _0x57a73c[_0x56fc1a(0xd8)] = _0x57a73c[_0x56fc1a(0xd8)] || [];
            let _0x40596e = _0x57a73c[_0x56fc1a(0xd8)]['indexOf'](_0x5c8bbb);
            (!_0x57a73c[_0x56fc1a(0xd8)][_0x56fc1a(0x39c)](_0x5c8bbb) &&
                _0x57a73c[_0x56fc1a(0xd8)][_0x56fc1a(0x1cc)](_0x5c8bbb),
                (_0x57a73c[_0x56fc1a(0x25b)] = _0x57a73c['partyMiss'] || []),
                (_0x40596e = _0x57a73c[_0x56fc1a(0x25b)][_0x56fc1a(0x3d1)](_0x5c8bbb)),
                _0x40596e >= 0x0 && _0x57a73c[_0x56fc1a(0x25b)][_0x56fc1a(0x3b1)](_0x40596e, 0x1),
                DoodadManager[_0x56fc1a(0x277)](),
                this[_0x56fc1a(0x36f)][_0x56fc1a(0xa8)](),
                this[_0x56fc1a(0x1e2)][_0x56fc1a(0x3cf)](),
                this[_0x56fc1a(0x1e2)][_0x56fc1a(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettPartyMiss'] = function () {
            const _0x1abd9e = _0x9a8c74,
                _0x47d59d = this[_0x1abd9e(0x1e2)][_0x1abd9e(0x3c3)](),
                _0x1acd89 = this[_0x1abd9e(0x36f)]['_doodad'];
            _0x1acd89['partyHave'] = _0x1acd89['partyHave'] || [];
            let _0x1c3e9d = _0x1acd89['partyHave'][_0x1abd9e(0x3d1)](_0x47d59d);
            (_0x1c3e9d >= 0x0 && _0x1acd89[_0x1abd9e(0xd8)][_0x1abd9e(0x3b1)](_0x1c3e9d, 0x1),
                (_0x1acd89[_0x1abd9e(0x25b)] = _0x1acd89[_0x1abd9e(0x25b)] || []),
                !_0x1acd89[_0x1abd9e(0x25b)][_0x1abd9e(0x39c)](_0x47d59d) &&
                    _0x1acd89['partyMiss']['push'](_0x47d59d),
                DoodadManager[_0x1abd9e(0x277)](),
                this[_0x1abd9e(0x36f)][_0x1abd9e(0xa8)](),
                this[_0x1abd9e(0x1e2)][_0x1abd9e(0x3cf)](),
                this[_0x1abd9e(0x1e2)][_0x1abd9e(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3ee)] = function () {
            const _0x23a2a9 = _0x9a8c74;
            ((this['_gfdSettingsSwitchWindow'] = new Window_Doodads_Settings_Switch()),
                this[_0x23a2a9(0x2c5)](this[_0x23a2a9(0x3bd)]),
                this['_gfdWindows']['push'](this['_gfdSettingsSwitchWindow']));
            const _0x3395db = this[_0x23a2a9(0x3bd)];
            (_0x3395db['setHandler']('cancel', this[_0x23a2a9(0x258)][_0x23a2a9(0xee)](this)),
                _0x3395db[_0x23a2a9(0x3e1)](
                    _0x23a2a9(0x114),
                    this[_0x23a2a9(0x316)][_0x23a2a9(0xee)](this)
                ),
                _0x3395db[_0x23a2a9(0x3e1)](
                    _0x23a2a9(0x379),
                    this[_0x23a2a9(0x1d8)][_0x23a2a9(0xee)](this)
                ),
                _0x3395db[_0x23a2a9(0x3e1)](_0x23a2a9(0x20c), this[_0x23a2a9(0xa1)]['bind'](this)));
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettingsSwitch'] = function () {
            const _0x27a35d = _0x9a8c74;
            (this['_gfdSettingsSwitchWindow']['activate'](),
                this[_0x27a35d(0x3bd)][_0x27a35d(0x1d6)](),
                this[_0x27a35d(0x3bd)]['smoothSelect'](0x0),
                this['_gfdSettingsSwitchWindow'][_0x27a35d(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x258)] = function () {
            const _0x5c3f42 = _0x9a8c74;
            (this[_0x5c3f42(0x3bd)][_0x5c3f42(0xdf)](), this[_0x5c3f42(0x36f)][_0x5c3f42(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x316)] = function () {
            const _0x464f52 = _0x9a8c74,
                _0x85ba87 = this[_0x464f52(0x3bd)]['currentExt'](),
                _0x1d3c2f = this[_0x464f52(0x36f)][_0x464f52(0x243)];
            _0x1d3c2f[_0x464f52(0x379)] = _0x1d3c2f['switchOn'] || [];
            let _0x31add5 = _0x1d3c2f[_0x464f52(0x379)]['indexOf'](_0x85ba87);
            (_0x31add5 >= 0x0 && _0x1d3c2f['switchOn']['splice'](_0x31add5, 0x1),
                (_0x1d3c2f['switchOff'] = _0x1d3c2f[_0x464f52(0x20c)] || []),
                (_0x31add5 = _0x1d3c2f[_0x464f52(0x20c)][_0x464f52(0x3d1)](_0x85ba87)),
                _0x31add5 >= 0x0 && _0x1d3c2f[_0x464f52(0x20c)][_0x464f52(0x3b1)](_0x31add5, 0x1),
                DoodadManager['updateNewSettings'](),
                this['_gfdSettingsWindow'][_0x464f52(0xa8)](),
                this[_0x464f52(0x3bd)][_0x464f52(0x3cf)](),
                this['_gfdSettingsSwitchWindow'][_0x464f52(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1d8)] = function () {
            const _0x5ccf5c = _0x9a8c74,
                _0x45c866 = this[_0x5ccf5c(0x3bd)][_0x5ccf5c(0x3c3)](),
                _0x3866e5 = this[_0x5ccf5c(0x36f)]['_doodad'];
            _0x3866e5[_0x5ccf5c(0x379)] = _0x3866e5[_0x5ccf5c(0x379)] || [];
            let _0x5207ff = _0x3866e5[_0x5ccf5c(0x379)][_0x5ccf5c(0x3d1)](_0x45c866);
            (!_0x3866e5['switchOn']['includes'](_0x45c866) &&
                _0x3866e5[_0x5ccf5c(0x379)][_0x5ccf5c(0x1cc)](_0x45c866),
                (_0x3866e5[_0x5ccf5c(0x20c)] = _0x3866e5[_0x5ccf5c(0x20c)] || []),
                (_0x5207ff = _0x3866e5[_0x5ccf5c(0x20c)]['indexOf'](_0x45c866)),
                _0x5207ff >= 0x0 && _0x3866e5[_0x5ccf5c(0x20c)][_0x5ccf5c(0x3b1)](_0x5207ff, 0x1),
                DoodadManager[_0x5ccf5c(0x277)](),
                this['_gfdSettingsWindow'][_0x5ccf5c(0xa8)](),
                this[_0x5ccf5c(0x3bd)][_0x5ccf5c(0x3cf)](),
                this[_0x5ccf5c(0x3bd)][_0x5ccf5c(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0xa1)] = function () {
            const _0x49adc6 = _0x9a8c74,
                _0x2ebfa0 = this[_0x49adc6(0x3bd)][_0x49adc6(0x3c3)](),
                _0x7ccbcb = this[_0x49adc6(0x36f)][_0x49adc6(0x243)];
            _0x7ccbcb['switchOn'] = _0x7ccbcb[_0x49adc6(0x379)] || [];
            let _0x1922f8 = _0x7ccbcb[_0x49adc6(0x379)][_0x49adc6(0x3d1)](_0x2ebfa0);
            (_0x1922f8 >= 0x0 && _0x7ccbcb['switchOn'][_0x49adc6(0x3b1)](_0x1922f8, 0x1),
                (_0x7ccbcb[_0x49adc6(0x20c)] = _0x7ccbcb[_0x49adc6(0x20c)] || []),
                !_0x7ccbcb[_0x49adc6(0x20c)][_0x49adc6(0x39c)](_0x2ebfa0) &&
                    _0x7ccbcb[_0x49adc6(0x20c)]['push'](_0x2ebfa0),
                DoodadManager[_0x49adc6(0x277)](),
                this['_gfdSettingsWindow'][_0x49adc6(0xa8)](),
                this[_0x49adc6(0x3bd)]['activate'](),
                this[_0x49adc6(0x3bd)][_0x49adc6(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x23a)] = function () {
            const _0x2a1d64 = _0x9a8c74;
            ((this[_0x2a1d64(0x129)] = new Window_Doodads_Settings_Tone_Presets()),
                this[_0x2a1d64(0x2c5)](this['_gfdSettingsTonePresetWindow']),
                this[_0x2a1d64(0xfc)][_0x2a1d64(0x1cc)](this['_gfdSettingsTonePresetWindow']));
            const _0xb723be = this['_gfdSettingsTonePresetWindow'];
            (_0xb723be['setHandler']('cancel', this[_0x2a1d64(0x1c5)][_0x2a1d64(0xee)](this)),
                _0xb723be[_0x2a1d64(0x3e1)](_0x2a1d64(0x98), this[_0x2a1d64(0x1fe)]['bind'](this)));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x134)] = function () {
            const _0x5a7b5a = _0x9a8c74;
            (this['_gfdSettingsTonePresetWindow'][_0x5a7b5a(0x3cf)](),
                this[_0x5a7b5a(0x129)][_0x5a7b5a(0x1d6)]());
            const _0x4104e9 = this[_0x5a7b5a(0x36f)][_0x5a7b5a(0x243)];
            ((_0x4104e9[_0x5a7b5a(0x3ad)] = _0x4104e9[_0x5a7b5a(0x3ad)] || 0x0),
                (_0x4104e9[_0x5a7b5a(0xd5)] = _0x4104e9[_0x5a7b5a(0xd5)] || 0x0),
                (_0x4104e9[_0x5a7b5a(0x1c8)] = _0x4104e9[_0x5a7b5a(0x1c8)] || 0x0),
                (_0x4104e9[_0x5a7b5a(0x236)] = _0x4104e9[_0x5a7b5a(0x236)] || 0x0));
            let _0x346a4e = 0x0;
            for (
                let _0xb7afd2 = 0x0;
                _0xb7afd2 < this[_0x5a7b5a(0x129)][_0x5a7b5a(0x2db)]();
                _0xb7afd2++
            ) {
                const _0xd141fe =
                    this[_0x5a7b5a(0x129)][_0x5a7b5a(0x270)][_0xb7afd2][_0x5a7b5a(0x164)];
                if (!_0xd141fe) continue;
                if (
                    !DoodadManager[_0x5a7b5a(0x78)](
                        {
                            red: _0xd141fe[0x0],
                            green: _0xd141fe[0x1],
                            blue: _0xd141fe[0x2],
                            grey: _0xd141fe[0x3],
                        },
                        _0x4104e9[_0x5a7b5a(0x3ad)],
                        _0x4104e9[_0x5a7b5a(0xd5)],
                        _0x4104e9['toneBlue'],
                        _0x4104e9['toneGreen']
                    )
                )
                    continue;
                _0x346a4e = _0xb7afd2;
            }
            (this['_gfdSettingsTonePresetWindow'][_0x5a7b5a(0x112)](_0x346a4e),
                this['_gfdSettingsTonePresetWindow'][_0x5a7b5a(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1c5)] = function () {
            const _0x8072eb = _0x9a8c74;
            (this[_0x8072eb(0x129)][_0x8072eb(0xdf)](), this[_0x8072eb(0x36f)][_0x8072eb(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1fe)] = function () {
            const _0x52fedd = _0x9a8c74,
                _0x3934d9 = this[_0x52fedd(0x129)][_0x52fedd(0x3c3)](),
                _0x304904 = this[_0x52fedd(0x36f)][_0x52fedd(0x243)];
            ((_0x304904['toneRed'] = _0x3934d9[0x0]),
                (_0x304904[_0x52fedd(0xd5)] = _0x3934d9[0x1]),
                (_0x304904[_0x52fedd(0x1c8)] = _0x3934d9[0x2]),
                (_0x304904[_0x52fedd(0x236)] = _0x3934d9[0x3]),
                DoodadManager[_0x52fedd(0x277)](),
                this[_0x52fedd(0x1c5)](),
                this[_0x52fedd(0x36f)][_0x52fedd(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x40a)] = function () {
            const _0xb19f6f = _0x9a8c74;
            ((this['_gfdSettingsToneRGBWindow'] = new Window_Doodads_Settings_Tone_RGB()),
                this[_0xb19f6f(0x2c5)](this[_0xb19f6f(0x342)]),
                this['_gfdWindows'][_0xb19f6f(0x1cc)](this[_0xb19f6f(0x342)]));
            const _0x4f16ed = this['_gfdSettingsToneRGBWindow'];
            (_0x4f16ed[_0xb19f6f(0x3e1)](_0xb19f6f(0x2bc), this[_0xb19f6f(0xc6)]['bind'](this)),
                _0x4f16ed[_0xb19f6f(0x3e1)](
                    _0xb19f6f(0x272),
                    this[_0xb19f6f(0x22b)][_0xb19f6f(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1cf)] = function () {
            const _0x21c8f2 = _0x9a8c74;
            ((this['_gfdToneColor'] = _0x21c8f2(0x340)),
                this[_0x21c8f2(0x342)]['activate'](),
                this[_0x21c8f2(0x342)][_0x21c8f2(0x1d6)]());
            const _0x4e5372 = this[_0x21c8f2(0x36f)][_0x21c8f2(0x243)];
            _0x4e5372[_0x21c8f2(0x3ad)] = _0x4e5372['toneRed'] || 0x0;
            const _0x8c7907 = _0x4e5372[_0x21c8f2(0x3ad)];
            let _0x212fbb = 0x0;
            for (
                let _0x14ddc4 = 0x0;
                _0x14ddc4 < this[_0x21c8f2(0x342)][_0x21c8f2(0x2db)]();
                _0x14ddc4++
            ) {
                const _0x147e59 =
                    this[_0x21c8f2(0x342)][_0x21c8f2(0x270)][_0x14ddc4][_0x21c8f2(0x164)];
                _0x8c7907 <= _0x147e59 && (_0x212fbb = _0x14ddc4);
            }
            (this[_0x21c8f2(0x342)]['select'](_0x212fbb),
                this[_0x21c8f2(0x342)][_0x21c8f2(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x17a)] = function () {
            const _0x3ac83a = _0x9a8c74;
            ((this[_0x3ac83a(0x15b)] = _0x3ac83a(0x360)),
                this[_0x3ac83a(0x342)][_0x3ac83a(0x3cf)](),
                this['_gfdSettingsToneRGBWindow'][_0x3ac83a(0x1d6)]());
            const _0xadd57c = this[_0x3ac83a(0x36f)]['_doodad'];
            _0xadd57c[_0x3ac83a(0xd5)] = _0xadd57c[_0x3ac83a(0xd5)] || 0x0;
            const _0x223d05 = _0xadd57c[_0x3ac83a(0xd5)];
            let _0x56967e = 0x0;
            for (
                let _0x4d5c7e = 0x0;
                _0x4d5c7e < this[_0x3ac83a(0x342)][_0x3ac83a(0x2db)]();
                _0x4d5c7e++
            ) {
                const _0x1ac987 =
                    this[_0x3ac83a(0x342)][_0x3ac83a(0x270)][_0x4d5c7e][_0x3ac83a(0x164)];
                _0x223d05 <= _0x1ac987 && (_0x56967e = _0x4d5c7e);
            }
            (this[_0x3ac83a(0x342)][_0x3ac83a(0x112)](_0x56967e),
                this[_0x3ac83a(0x342)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x30a)] = function () {
            const _0x58a59c = _0x9a8c74;
            ((this[_0x58a59c(0x15b)] = _0x58a59c(0x288)),
                this[_0x58a59c(0x342)]['activate'](),
                this['_gfdSettingsToneRGBWindow'][_0x58a59c(0x1d6)]());
            const _0x4a8f3e = this[_0x58a59c(0x36f)]['_doodad'];
            _0x4a8f3e['toneBlue'] = _0x4a8f3e[_0x58a59c(0x1c8)] || 0x0;
            const _0x34c64a = _0x4a8f3e[_0x58a59c(0x1c8)];
            let _0x482da3 = 0x0;
            for (
                let _0xfaea08 = 0x0;
                _0xfaea08 < this[_0x58a59c(0x342)][_0x58a59c(0x2db)]();
                _0xfaea08++
            ) {
                const _0x198a02 =
                    this['_gfdSettingsToneRGBWindow'][_0x58a59c(0x270)][_0xfaea08][
                        _0x58a59c(0x164)
                    ];
                _0x34c64a <= _0x198a02 && (_0x482da3 = _0xfaea08);
            }
            (this[_0x58a59c(0x342)][_0x58a59c(0x112)](_0x482da3),
                this[_0x58a59c(0x342)][_0x58a59c(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0xc6)] = function () {
            const _0x15e6b2 = _0x9a8c74;
            (this[_0x15e6b2(0x342)][_0x15e6b2(0xdf)](),
                this['_gfdSettingsWindow'][_0x15e6b2(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x22b)] = function () {
            const _0x6b551b = _0x9a8c74,
                _0x53cd4d = this[_0x6b551b(0x342)][_0x6b551b(0x3c3)](),
                _0x67808e = this[_0x6b551b(0x36f)][_0x6b551b(0x243)];
            if (this[_0x6b551b(0x15b)] === 'red') _0x67808e['toneRed'] = _0x53cd4d;
            else {
                if (this['_gfdToneColor'] === _0x6b551b(0x360)) _0x67808e['toneGreen'] = _0x53cd4d;
                else
                    this['_gfdToneColor'] === _0x6b551b(0x288) &&
                        (_0x67808e[_0x6b551b(0x1c8)] = _0x53cd4d);
            }
            (DoodadManager[_0x6b551b(0x277)](),
                this[_0x6b551b(0xc6)](),
                this[_0x6b551b(0x36f)][_0x6b551b(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x10b)] = function () {
            const _0x50c288 = _0x9a8c74;
            ((this['_gfdSettingsToneGreyWindow'] = new Window_Doodads_Settings_Tone_Grey()),
                this[_0x50c288(0x2c5)](this[_0x50c288(0x35d)]),
                this[_0x50c288(0xfc)][_0x50c288(0x1cc)](this[_0x50c288(0x35d)]));
            const _0x57bb8d = this[_0x50c288(0x35d)];
            (_0x57bb8d[_0x50c288(0x3e1)](
                _0x50c288(0x2bc),
                this['cancelGFDSettToneGrey']['bind'](this)
            ),
                _0x57bb8d[_0x50c288(0x3e1)](
                    _0x50c288(0x272),
                    this[_0x50c288(0xf7)][_0x50c288(0xee)](this)
                ));
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x407)] = function () {
            const _0x200104 = _0x9a8c74;
            (this[_0x200104(0x35d)][_0x200104(0x3cf)](),
                this[_0x200104(0x35d)][_0x200104(0x1d6)]());
            const _0x1153cb = this[_0x200104(0x36f)]['_doodad'];
            _0x1153cb[_0x200104(0x236)] = _0x1153cb[_0x200104(0x236)] || 0x0;
            const _0x579ea9 = _0x1153cb[_0x200104(0x236)];
            let _0x4a3c4c = 0x0;
            for (
                let _0x20fa85 = 0x0;
                _0x20fa85 < this[_0x200104(0x35d)][_0x200104(0x2db)]();
                _0x20fa85++
            ) {
                const _0x16d3de =
                    this[_0x200104(0x35d)][_0x200104(0x270)][_0x20fa85][_0x200104(0x164)];
                _0x579ea9 <= _0x16d3de && (_0x4a3c4c = _0x20fa85);
            }
            (this['_gfdSettingsToneGreyWindow'][_0x200104(0x112)](_0x4a3c4c),
                this[_0x200104(0x35d)][_0x200104(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x3e3)] = function () {
            const _0x20a408 = _0x9a8c74;
            (this[_0x20a408(0x35d)][_0x20a408(0xdf)](), this[_0x20a408(0x36f)][_0x20a408(0x3cf)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0xf7)] = function () {
            const _0x25e9f2 = _0x9a8c74,
                _0x4e6cec = this['_gfdSettingsToneGreyWindow'][_0x25e9f2(0x3c3)](),
                _0x364450 = this[_0x25e9f2(0x36f)][_0x25e9f2(0x243)];
            ((_0x364450['toneGrey'] = _0x4e6cec),
                DoodadManager['updateNewSettings'](),
                this[_0x25e9f2(0x3e3)](),
                this[_0x25e9f2(0x36f)][_0x25e9f2(0xa8)]());
        }),
        (Scene_Map['prototype'][_0x9a8c74(0x1d7)] = function () {
            const _0x2e397f = _0x9a8c74,
                _0x4cf352 = this['_gfdSettingsWindow'][_0x2e397f(0x243)];
            ((_0x4cf352[_0x2e397f(0x3ad)] = VisuMZ[_0x2e397f(0x229)](0x0, 0xff)),
                DoodadManager[_0x2e397f(0x277)](),
                this[_0x2e397f(0x36f)][_0x2e397f(0x3cf)](),
                this[_0x2e397f(0x36f)][_0x2e397f(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1ad)] = function () {
            const _0x138920 = _0x9a8c74,
                _0x3938c8 = this[_0x138920(0x36f)][_0x138920(0x243)];
            ((_0x3938c8[_0x138920(0xd5)] = VisuMZ[_0x138920(0x229)](0x0, 0xff)),
                DoodadManager[_0x138920(0x277)](),
                this[_0x138920(0x36f)][_0x138920(0x3cf)](),
                this[_0x138920(0x36f)][_0x138920(0xa8)]());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2ac)] = function () {
            const _0x5bade2 = _0x9a8c74,
                _0x84311b = this[_0x5bade2(0x36f)][_0x5bade2(0x243)];
            ((_0x84311b[_0x5bade2(0x1c8)] = VisuMZ[_0x5bade2(0x229)](0x0, 0xff)),
                DoodadManager[_0x5bade2(0x277)](),
                this[_0x5bade2(0x36f)][_0x5bade2(0x3cf)](),
                this[_0x5bade2(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x1f2)] = function () {
            const _0x175a9d = _0x9a8c74,
                _0x1a76c6 = this[_0x175a9d(0x36f)][_0x175a9d(0x243)];
            ((_0x1a76c6[_0x175a9d(0x236)] = VisuMZ[_0x175a9d(0x229)](0x0, 0xff)),
                DoodadManager[_0x175a9d(0x277)](),
                this[_0x175a9d(0x36f)][_0x175a9d(0x3cf)](),
                this[_0x175a9d(0x36f)]['refresh']());
        }),
        (Scene_Map[_0x9a8c74(0x11a)]['cmdGFDSettToneRandomAll'] = function () {
            const _0x4641c0 = _0x9a8c74,
                _0x49e2c8 = this[_0x4641c0(0x36f)]['_doodad'];
            ((_0x49e2c8[_0x4641c0(0x3ad)] = VisuMZ[_0x4641c0(0x229)](0x0, 0xff)),
                (_0x49e2c8[_0x4641c0(0xd5)] = VisuMZ[_0x4641c0(0x229)](0x0, 0xff)),
                (_0x49e2c8[_0x4641c0(0x1c8)] = VisuMZ[_0x4641c0(0x229)](0x0, 0xff)),
                (_0x49e2c8[_0x4641c0(0x236)] = VisuMZ[_0x4641c0(0x229)](0x0, 0xff)),
                DoodadManager[_0x4641c0(0x277)](),
                this[_0x4641c0(0x36f)]['activate'](),
                this[_0x4641c0(0x36f)][_0x4641c0(0xa8)]());
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x257)] =
            Sprite_Doodad[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)]),
        (Sprite_Doodad[_0x9a8c74(0x11a)]['update'] = function () {
            const _0x3d867b = _0x9a8c74;
            VisuMZ[_0x3d867b(0x268)]['Sprite_Doodad_update'][_0x3d867b(0x355)](this);
            if (!this[_0x3d867b(0xb9)] || !$gameTemp[_0x3d867b(0x136)]) return;
            this['updateSettingsOpacity']();
        }),
        (Sprite_Doodad[_0x9a8c74(0x11a)][_0x9a8c74(0x206)] = function () {
            const _0x574486 = _0x9a8c74;
            let _0x55e6de = this[_0x574486(0x10e)][_0x574486(0x7d)] || 0x0;
            if (DoodadManager['_canvasMode']) {
                if (DoodadManager['current']()['z'] !== this[_0x574486(0x10e)]['z'])
                    return _0x55e6de / 0x2;
                return _0x55e6de;
            }
            if (DoodadManager[_0x574486(0x1da)]) {
                const _0x34daa9 = SceneManager['_scene'][_0x574486(0x16d)][_0x574486(0x3c3)]();
                _0x34daa9 &&
                    (_0x34daa9 !== this[_0x574486(0x10e)] && (_0x55e6de /= 0x2),
                    _0x34daa9['z'] !== this[_0x574486(0x10e)]['z'] && (_0x55e6de /= 0x2));
            }
            return _0x55e6de;
        }),
        (Sprite_Doodad[_0x9a8c74(0x11a)][_0x9a8c74(0x2d5)] = function () {
            const _0x5100d1 = _0x9a8c74;
            this['opacity'] = this[_0x5100d1(0x206)]();
        }));
    function Sprite_DoodadCursor() {
        this['initialize'](...arguments);
    }
    ((Sprite_DoodadCursor[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Sprite_Doodad[_0x9a8c74(0x11a)]
    )),
        (Sprite_DoodadCursor[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Sprite_DoodadCursor),
        (Sprite_DoodadCursor[_0x9a8c74(0x11a)][_0x9a8c74(0xc4)] = function () {
            const _0x14ef5d = _0x9a8c74;
            (TouchInput[_0x14ef5d(0xb6)] === undefined &&
                ((TouchInput[_0x14ef5d(0xb6)] = Math[_0x14ef5d(0x223)](
                    Graphics[_0x14ef5d(0x3a9)] / 0x2
                )),
                (TouchInput[_0x14ef5d(0xda)] = Math[_0x14ef5d(0x223)](Graphics['height'] / 0x2))),
                Sprite_Doodad[_0x14ef5d(0x11a)][_0x14ef5d(0xc4)]['call'](this));
        }),
        (Sprite_DoodadCursor['prototype'][_0x9a8c74(0x35e)] = function () {
            const _0x39b356 = _0x9a8c74;
            ((this[_0x39b356(0x10e)] = DoodadManager[_0x39b356(0x8f)]()), this[_0x39b356(0xc4)]());
        }),
        (Sprite_DoodadCursor[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)] = function () {
            const _0x34f6d0 = _0x9a8c74;
            (Sprite_Doodad['prototype'][_0x34f6d0(0x33f)]['call'](this), this[_0x34f6d0(0x3b8)]());
        }),
        (Sprite_DoodadCursor[_0x9a8c74(0x11a)]['updatePosition'] = function () {
            const _0x3508c8 = _0x9a8c74,
                _0x657e52 = SceneManager[_0x3508c8(0x3cd)][_0x3508c8(0x1c3)];
            ((this['x'] = _0x657e52[_0x3508c8(0x3c2)]()),
                (this['y'] = _0x657e52[_0x3508c8(0x344)]()));
        }),
        (Spriteset_Map['prototype']['clearDoodadCursor'] = function () {
            const _0x5e74ec = _0x9a8c74;
            this['_doodadCursor'] &&
                (this[_0x5e74ec(0xd7)][_0x5e74ec(0x2dd)](),
                this[_0x5e74ec(0x225)][_0x5e74ec(0x1b5)](this[_0x5e74ec(0xd7)]));
        }),
        (Spriteset_Map['prototype'][_0x9a8c74(0xfd)] = function (_0x549ae6) {
            const _0x52d769 = _0x9a8c74;
            (this['clearDoodadCursor'](),
                (this[_0x52d769(0xd7)] = new Sprite_DoodadCursor(_0x549ae6)),
                this[_0x52d769(0x225)][_0x52d769(0x2c5)](this[_0x52d769(0xd7)]));
        }),
        (Spriteset_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2b8)] = function () {
            const _0x18df8a = _0x9a8c74;
            this[_0x18df8a(0x33b)] ? this[_0x18df8a(0x2a8)]() : this[_0x18df8a(0x28c)]();
        }),
        (Spriteset_Map[_0x9a8c74(0x11a)]['showRegionOverlayWindow'] = function () {
            const _0x272899 = _0x9a8c74;
            ((this['_regionOverlayWindowOn'] = !![]),
                !this['_regionOverlayWindow'] && this[_0x272899(0x108)](),
                this[_0x272899(0x1b3)][_0x272899(0x1a7)](),
                this['_regionOverlayWindow'][_0x272899(0x3cf)](),
                this['_regionOverlayWindowH'] &&
                    (this[_0x272899(0x14a)]['show'](),
                    this['_regionOverlayWindowH'][_0x272899(0x3cf)]()),
                this['_regionOverlayWindowV'] &&
                    (this[_0x272899(0x338)][_0x272899(0x1a7)](),
                    this[_0x272899(0x338)][_0x272899(0x3cf)]()),
                this[_0x272899(0x370)] &&
                    (this[_0x272899(0x370)]['show'](), this[_0x272899(0x370)][_0x272899(0x3cf)]()));
        }),
        (Spriteset_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x2a8)] = function () {
            const _0x598bca = _0x9a8c74;
            this[_0x598bca(0x33b)] = ![];
            if (!this[_0x598bca(0x1b3)]) return;
            (this[_0x598bca(0x1b3)][_0x598bca(0x12e)](),
                this[_0x598bca(0x1b3)][_0x598bca(0x143)](),
                this[_0x598bca(0x14a)] &&
                    (this['_regionOverlayWindowH'][_0x598bca(0x12e)](),
                    this[_0x598bca(0x14a)]['deactivate']()),
                this[_0x598bca(0x338)] &&
                    (this['_regionOverlayWindowV'][_0x598bca(0x12e)](),
                    this[_0x598bca(0x338)][_0x598bca(0x143)]()),
                this[_0x598bca(0x370)] &&
                    (this[_0x598bca(0x370)][_0x598bca(0x12e)](),
                    this[_0x598bca(0x370)][_0x598bca(0x143)]()));
        }),
        (Spriteset_Map[_0x9a8c74(0x11a)][_0x9a8c74(0x108)] = function () {
            const _0x366491 = _0x9a8c74;
            if (this[_0x366491(0x1b3)]) return;
            (Graphics['startLoading'](),
                (this[_0x366491(0x1b3)] = new Window_Doodads_RegionOverlay()),
                this['_tilemap'][_0x366491(0x2c5)](this[_0x366491(0x1b3)]),
                this[_0x366491(0x1b3)]['updatePosition'](),
                $gameMap['isLoopHorizontal']() &&
                    ((this['_regionOverlayWindowH'] = new Window_Doodads_RegionOverlay()),
                    (this[_0x366491(0x14a)][_0x366491(0x37a)] =
                        $gameMap[_0x366491(0x3a9)]() * $gameMap['tileWidth']()),
                    this[_0x366491(0x225)][_0x366491(0x2c5)](this[_0x366491(0x14a)]),
                    this[_0x366491(0x14a)]['updatePosition']()),
                $gameMap['isLoopVertical']() &&
                    ((this[_0x366491(0x338)] = new Window_Doodads_RegionOverlay()),
                    (this[_0x366491(0x338)][_0x366491(0x24b)] =
                        $gameMap[_0x366491(0x158)]() * $gameMap[_0x366491(0x287)]()),
                    this[_0x366491(0x225)][_0x366491(0x2c5)](this['_regionOverlayWindowV']),
                    this[_0x366491(0x338)]['updatePosition']()),
                $gameMap['isLoopHorizontal']() &&
                    $gameMap[_0x366491(0x1ea)]() &&
                    ((this[_0x366491(0x370)] = new Window_Doodads_RegionOverlay()),
                    (this['_regionOverlayWindowHV']['_pX'] =
                        $gameMap[_0x366491(0x3a9)]() * $gameMap[_0x366491(0x3b6)]()),
                    (this[_0x366491(0x370)]['_pY'] =
                        $gameMap[_0x366491(0x158)]() * $gameMap[_0x366491(0x287)]()),
                    this[_0x366491(0x225)][_0x366491(0x2c5)](this[_0x366491(0x370)]),
                    this[_0x366491(0x370)][_0x366491(0x3b8)]()),
                Graphics[_0x366491(0x3fd)]());
        }),
        (VisuMZ['DoodadsEditor'][_0x9a8c74(0x386)] = Window[_0x9a8c74(0x11a)]['initialize']),
        (Window[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x10c147 = _0x9a8c74;
            (VisuMZ[_0x10c147(0x268)][_0x10c147(0x386)][_0x10c147(0x355)](this),
                (this[_0x10c147(0x37d)] = ![]));
        }),
        (Window['prototype']['setGFD'] = function () {
            const _0x1cd206 = _0x9a8c74;
            ((this['_isGFDWindow'] = !![]),
                (this[_0x1cd206(0x7d)] = 0x0),
                (this['backOpacity'] = 0x0),
                (this[_0x1cd206(0x13f)] = 0x0),
                (this['openness'] = 0xff),
                this['deactivate']());
        }),
        (Window[_0x9a8c74(0x11a)][_0x9a8c74(0x24e)] = function () {
            return this['_isGFDWindow'];
        }),
        (VisuMZ[_0x9a8c74(0x268)]['Window_isOpen'] = Window[_0x9a8c74(0x11a)][_0x9a8c74(0x27a)]),
        (Window[_0x9a8c74(0x11a)][_0x9a8c74(0x27a)] = function () {
            const _0x5d70d4 = _0x9a8c74;
            if (this[_0x5d70d4(0x24e)]()) return this[_0x5d70d4(0x7d)] >= 0xff;
            return VisuMZ[_0x5d70d4(0x268)][_0x5d70d4(0xb7)][_0x5d70d4(0x355)](this);
        }),
        (VisuMZ['DoodadsEditor'][_0x9a8c74(0x275)] = Window[_0x9a8c74(0x11a)]['isClosed']),
        (Window['prototype']['isClosed'] = function () {
            const _0x10c579 = _0x9a8c74;
            if (this[_0x10c579(0x24e)]()) return this['opacity'] <= 0x0;
            return VisuMZ[_0x10c579(0x268)][_0x10c579(0x275)][_0x10c579(0x355)](this);
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x266)] = Window_Base['prototype'][_0x9a8c74(0x267)]),
        (Window_Base[_0x9a8c74(0x11a)]['updateOpen'] = function () {
            const _0x5c8776 = _0x9a8c74;
            if (!this[_0x5c8776(0x24e)]() || !this['_opening'])
                return VisuMZ[_0x5c8776(0x268)][_0x5c8776(0x266)]['call'](this);
            ((this[_0x5c8776(0x7d)] += 0x20),
                (this['backOpacity'] += 0x19),
                (this[_0x5c8776(0x13f)] += 0x20),
                this[_0x5c8776(0x27a)]() && (this[_0x5c8776(0x19a)] = ![]));
        }),
        (VisuMZ['DoodadsEditor'][_0x9a8c74(0x2cd)] =
            Window_Base[_0x9a8c74(0x11a)][_0x9a8c74(0x211)]),
        (Window_Base['prototype'][_0x9a8c74(0x211)] = function () {
            const _0xfba455 = _0x9a8c74;
            if (!this[_0xfba455(0x24e)]() || !this[_0xfba455(0x10c)])
                return VisuMZ['DoodadsEditor'][_0xfba455(0x2cd)]['call'](this);
            ((this[_0xfba455(0x7d)] -= 0x20),
                (this[_0xfba455(0x21c)] -= 0x18),
                (this[_0xfba455(0x13f)] -= 0x20),
                this[_0xfba455(0x2d7)]() && (this[_0xfba455(0x10c)] = ![]));
        }),
        (VisuMZ[_0x9a8c74(0x268)][_0x9a8c74(0x31f)] =
            Window_Base[_0x9a8c74(0x11a)][_0x9a8c74(0x1f5)]),
        (Window_Base[_0x9a8c74(0x11a)][_0x9a8c74(0x1f5)] = function () {
            const _0x2a9817 = _0x9a8c74;
            if (!this[_0x2a9817(0x24e)]())
                return VisuMZ['DoodadsEditor']['Window_Base_updateTone'][_0x2a9817(0x355)](this);
            this[_0x2a9817(0x2d0)](0x64, -0x32, -0x32);
        }),
        (Window_Base[_0x9a8c74(0x11a)][_0x9a8c74(0x3c1)] = function (
            _0x11bdf9,
            _0x2e2345,
            _0x3f9b64,
            _0x2e5386,
            _0xc3734f
        ) {
            const _0xcbbf6d = _0x9a8c74;
            if (_0x11bdf9 <= 0x0 || _0x11bdf9 >= Tilemap[_0xcbbf6d(0x19d)]) return;
            const _0x3b92f3 = ImageManager['loadTilesetBitmap'](_0x11bdf9, ![]),
                _0x5623f8 = $gameMap[_0xcbbf6d(0x3b6)](),
                _0x3f2a38 = $gameMap[_0xcbbf6d(0x287)](),
                _0x4ab021 =
                    ((Math[_0xcbbf6d(0x223)](_0x11bdf9 / 0x80) % 0x2) * 0x8 + (_0x11bdf9 % 0x8)) *
                    _0x5623f8,
                _0x128fbd = (Math[_0xcbbf6d(0x223)]((_0x11bdf9 % 0x100) / 0x8) % 0x10) * _0x3f2a38;
            return (
                (_0x2e5386 = _0x2e5386 ?? _0x5623f8),
                (_0xc3734f = _0xc3734f ?? _0x3f2a38),
                this[_0xcbbf6d(0x3d3)][_0xcbbf6d(0x289)](
                    _0x3b92f3,
                    _0x4ab021,
                    _0x128fbd,
                    _0x5623f8,
                    _0x3f2a38,
                    _0x2e2345,
                    _0x3f9b64,
                    _0x2e5386,
                    _0xc3734f
                ),
                _0x3b92f3
            );
        }));
    function Window_Doodads_Command(..._0x3059eb) {
        const _0x4b607b = _0x9a8c74;
        this[_0x4b607b(0x35b)](..._0x3059eb);
    }
    ((Window_Doodads_Command[_0x9a8c74(0x11a)] = Object['create'](
        Window_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Command),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x547a95 = _0x9a8c74,
                _0x129bcc = new Rectangle(
                    this['windowX'](),
                    this[_0x547a95(0x96)](),
                    this[_0x547a95(0x15e)](),
                    this[_0x547a95(0x231)]()
                );
            (Window_Command[_0x547a95(0x11a)][_0x547a95(0x35b)][_0x547a95(0x355)](this, _0x129bcc),
                this[_0x547a95(0x170)]());
        }),
        (Window_Doodads_Command[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x0;
        }),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x96)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x26af5c = _0x9a8c74;
            return Graphics[_0x26af5c(0x158)];
        }),
        (Window_Doodads_Command[_0x9a8c74(0x11a)][_0x9a8c74(0x1a9)] = function () {
            const _0xa52579 = _0x9a8c74;
            return Math['ceil'](
                (this[_0xa52579(0x270)]?.[_0xa52579(0x352)] ?? 0x5) / this['maxCols']()
            );
        }));
    function Window_Doodads_Menu(..._0x13214d) {
        this['initialize'](..._0x13214d);
    }
    ((Window_Doodads_Menu[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_Menu[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Menu),
        (Window_Doodads_Menu[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x4cb19b = _0x9a8c74;
            (this[_0x4cb19b(0x133)](_0x4cb19b(0x404), _0x4cb19b(0x2be)),
                this['addCommand']('Edit\x20Doodads', _0x4cb19b(0x346)),
                this[_0x4cb19b(0x133)](_0x4cb19b(0x250), 'clear'),
                this[_0x4cb19b(0x133)]('Import\x20from\x20Another\x20Map', 'import'),
                this[_0x4cb19b(0x133)]('', _0x4cb19b(0xb3), ![]),
                this[_0x4cb19b(0x133)](_0x4cb19b(0x172), _0x4cb19b(0x141)),
                this['addCommand']('', _0x4cb19b(0xb3), ![]),
                this[_0x4cb19b(0x133)]('Cancel\x20and\x20Close', _0x4cb19b(0x325)),
                this['addCommand']('Save\x20and\x20Close', _0x4cb19b(0x401)));
        }),
        (Window_Doodads_Menu['prototype']['windowHeight'] = function () {
            const _0x248b38 = _0x9a8c74;
            return this[_0x248b38(0x207)](0x9);
        }));
    function Window_Doodads_List(..._0x3373f2) {
        const _0x36b979 = _0x9a8c74;
        this[_0x36b979(0x35b)](..._0x3373f2);
    }
    ((Window_Doodads_List[_0x9a8c74(0x11a)] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_List),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x3a9875 = _0x9a8c74;
            ((this['_folder'] = []),
                Window_Doodads_Command[_0x3a9875(0x11a)][_0x3a9875(0x35b)][_0x3a9875(0x355)](this));
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return 0x190;
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0xb8)] = function () {
            const _0x20478a = _0x9a8c74;
            return this[_0x20478a(0x16f)];
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x54d780 = _0x9a8c74;
            return Graphics[_0x54d780(0x158)];
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x24c)] = function () {
            const _0x3b3da8 = _0x9a8c74;
            return this[_0x3b3da8(0x16f)][_0x3b3da8(0x3ff)](
                (_0x12b706, _0x3cee34) => _0x12b706 + (_0x3cee34 + '/'),
                ''
            );
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x2ad)] = function (_0x4090d4) {
            const _0x26f722 = _0x9a8c74;
            this[_0x26f722(0x16f)]['push'](_0x4090d4);
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x2b1)] = function () {
            const _0x4e4716 = _0x9a8c74;
            return this['_folder'][_0x4e4716(0xa9)]();
        }),
        (Window_Doodads_List['prototype']['makeCommandList'] = function () {
            const _0x20ffba = _0x9a8c74;
            (this[_0x20ffba(0x24c)]()[_0x20ffba(0x352)] <= 0x0
                ? (this[_0x20ffba(0x133)](_0x20ffba(0x3ce), _0x20ffba(0x397), !![]),
                  this[_0x20ffba(0x133)](_0x20ffba(0xbe), _0x20ffba(0x193), !![]))
                : this[_0x20ffba(0x133)]('../Previous\x20Folder', _0x20ffba(0x2bc), !![]),
                this[_0x20ffba(0xf2)](),
                this['addFileList']());
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x34c)] = function () {
            const _0x3e8a2b = _0x9a8c74,
                _0x5b4691 = require(_0x3e8a2b(0x2ed)),
                _0x1bc395 = _0x5b4691[_0x3e8a2b(0x8c)](process[_0x3e8a2b(0x296)][_0x3e8a2b(0x214)]);
            return _0x5b4691[_0x3e8a2b(0x329)](_0x1bc395, Doodads[_0x3e8a2b(0x2eb)]);
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0xf2)] = function () {
            const _0x442ad0 = _0x9a8c74,
                _0x6d985f = require('fs'),
                _0x5b6177 = [],
                _0x335b19 = this['getLocalPath']() + this[_0x442ad0(0x24c)]();
            _0x6d985f['readdirSync'](_0x335b19)['forEach'](_0x19574b => {
                const _0x5dc25c = _0x442ad0,
                    _0x483213 = _0x19574b;
                _0x19574b = _0x335b19 + '/' + _0x483213;
                const _0x570a73 = _0x6d985f[_0x5dc25c(0x3be)](_0x19574b);
                _0x570a73 &&
                    _0x570a73[_0x5dc25c(0x1be)]() &&
                    _0x5b6177[_0x5dc25c(0x1cc)](_0x483213);
            });
            const _0x27abb7 = _0x5b6177[_0x442ad0(0x352)];
            for (let _0x11fd04 = 0x0; _0x11fd04 < _0x27abb7; _0x11fd04++) {
                const _0x5c012d = _0x5b6177[_0x11fd04];
                _0x5c012d &&
                    this[_0x442ad0(0x133)](_0x5c012d + '/', _0x442ad0(0xb8), !![], _0x5c012d);
            }
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x371)] = function () {
            const _0x58d374 = _0x9a8c74,
                _0x2c086d = require('fs'),
                _0x4e2729 = [],
                _0x6d2fe1 = this[_0x58d374(0x34c)]() + this[_0x58d374(0x24c)]();
            _0x2c086d[_0x58d374(0x3f8)](_0x6d2fe1)['forEach'](_0x584067 => {
                const _0xcaef65 = _0x58d374;
                let _0x4e1765 = _0x584067;
                _0x584067 = _0x6d2fe1 + '/' + _0x4e1765;
                const _0x7b2c5c = _0x2c086d[_0xcaef65(0x3be)](_0x584067);
                if (_0x7b2c5c && _0x7b2c5c[_0xcaef65(0x1be)]()) {
                } else
                    _0x4e1765[_0xcaef65(0x79)](/.png/g) &&
                        ((_0x4e1765 = _0x4e1765[_0xcaef65(0xbf)](/.png/g, '')),
                        _0x4e2729['push'](_0x4e1765));
            });
            const _0x353110 = _0x4e2729['length'];
            for (let _0x3dfc6b = 0x0; _0x3dfc6b < _0x353110; _0x3dfc6b++) {
                let _0x116ade = _0x4e2729[_0x3dfc6b];
                _0x116ade && this[_0x58d374(0x133)](_0x116ade, 'file', !![], _0x116ade);
            }
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x146717) {
            const _0x488633 = _0x9a8c74,
                _0x52f111 = this[_0x488633(0x34f)](_0x146717),
                _0x3d9099 = this[_0x488633(0x205)](),
                _0x188a02 = this[_0x488633(0x285)](_0x146717),
                _0x5f0a1f = this['_list'][_0x146717][_0x488633(0x164)];
            let _0xed95f9 = 0x0,
                _0x16a66f = ![],
                _0x23625f = this[_0x488633(0x230)](_0x146717);
            (this['resetTextColor'](),
                this['changePaintOpacity'](this[_0x488633(0x2b9)](_0x146717)));
            switch (_0x188a02) {
                case _0x488633(0x397):
                    _0xed95f9 = 0x2;
                    break;
                case _0x488633(0x193):
                    _0xed95f9 = 0xb5;
                    break;
                case 'folder':
                    _0xed95f9 = 0xa4;
                    break;
                case _0x488633(0x15a):
                    ((_0x16a66f = !![]),
                        this[_0x488633(0x1e9)](_0x146717, _0x5f0a1f),
                        (_0x23625f = _0x23625f['replace'](/\[(\d+)x(\d+)\]/g, '')));
                    break;
            }
            ((_0xed95f9 > 0x0 || _0x16a66f) &&
                (this['drawIcon'](_0xed95f9, _0x52f111['x'] + 0x2, _0x52f111['y'] + 0x2),
                (_0x52f111['x'] += ImageManager[_0x488633(0x40c)] + 0x4),
                (_0x52f111[_0x488633(0x3a9)] -= ImageManager[_0x488633(0x40c)] + 0x4)),
                this[_0x488633(0x2f8)](
                    _0x23625f,
                    _0x52f111['x'],
                    _0x52f111['y'],
                    _0x52f111['width'],
                    _0x3d9099
                ));
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)][_0x9a8c74(0x1e9)] = function (_0x40a5dc, _0x1576b9) {
            const _0x59ee41 = _0x9a8c74,
                _0x5e9190 = ImageManager['loadDoodad'](
                    this[_0x59ee41(0x24c)]() + _0x1576b9,
                    0x0,
                    !![]
                );
            if (_0x5e9190['width'] <= 0x0)
                return setTimeout(
                    this['drawDoodadImage'][_0x59ee41(0xee)](this, _0x40a5dc, _0x1576b9),
                    0x5
                );
            const _0x3637e1 = this[_0x59ee41(0x34f)](_0x40a5dc),
                _0x816c9c = DoodadManager[_0x59ee41(0x373)](_0x1576b9),
                _0x760c14 = DoodadManager['getYFrames'](_0x1576b9),
                _0x2c7e0d = Math[_0x59ee41(0x223)](_0x5e9190[_0x59ee41(0x3a9)] / _0x816c9c),
                _0x31d3b5 = Math[_0x59ee41(0x223)](_0x5e9190[_0x59ee41(0x158)] / _0x760c14);
            let _0x4d7ebb = _0x2c7e0d,
                _0x5ef6ef = _0x31d3b5;
            if (_0x4d7ebb > ImageManager[_0x59ee41(0x40c)]) {
                const _0x22d099 = ImageManager[_0x59ee41(0x40c)] / _0x4d7ebb;
                ((_0x4d7ebb *= _0x22d099), (_0x5ef6ef *= _0x22d099));
            }
            if (_0x5ef6ef > ImageManager['iconHeight']) {
                const _0x3f3a25 = ImageManager[_0x59ee41(0x38b)] / _0x5ef6ef;
                ((_0x4d7ebb *= _0x3f3a25), (_0x5ef6ef *= _0x3f3a25));
            }
            const _0x1d8413 =
                    _0x3637e1['x'] + 0x2 + (ImageManager[_0x59ee41(0x40c)] - _0x4d7ebb) / 0x2,
                _0x3987df =
                    _0x3637e1['y'] + 0x2 + (ImageManager[_0x59ee41(0x38b)] - _0x5ef6ef) / 0x2;
            this[_0x59ee41(0x40b)]['blt'](
                _0x5e9190,
                0x0,
                0x0,
                _0x2c7e0d,
                _0x31d3b5,
                _0x1d8413,
                _0x3987df,
                _0x4d7ebb,
                _0x5ef6ef
            );
        }),
        (Window_Doodads_List[_0x9a8c74(0x11a)]['itemTextAlign'] = function () {
            return 'left';
        }));
    function Window_Doodads_Icons(..._0x5798d8) {
        this['initialize'](..._0x5798d8);
    }
    ((Window_Doodads_Icons[_0x9a8c74(0x11a)] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Icons),
        (Window_Doodads_Icons['prototype'][_0x9a8c74(0x362)] = function () {
            const _0x42b6fb = _0x9a8c74;
            return Math[_0x42b6fb(0x223)](
                (Graphics[_0x42b6fb(0x3a9)] - this[_0x42b6fb(0x15e)]()) / 0x2
            );
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            const _0x13c790 = _0x9a8c74,
                _0x3de6db = this[_0x13c790(0x32d)]() * (ImageManager[_0x13c790(0x40c)] + 0x4);
            return _0x3de6db + 0x18;
        }),
        (Window_Doodads_Icons['prototype']['itemPadding'] = function () {
            return 0x0;
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)]['windowHeight'] = function () {
            const _0x456638 = _0x9a8c74;
            return Graphics[_0x456638(0x158)];
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)][_0x9a8c74(0x32d)] = function () {
            return 0x10;
        }),
        (Window_Doodads_Icons['prototype'][_0x9a8c74(0x38d)] = function () {
            return ImageManager['iconHeight'] + 0x4;
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)][_0x9a8c74(0x156)] = function () {
            const _0x1beaf8 = _0x9a8c74;
            return this[_0x1beaf8(0x38d)]();
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)]['itemWidth'] = function () {
            return ImageManager['iconWidth'] + 0x4;
        }),
        (Window_Doodads_Icons['prototype'][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)]['rowSpacing'] = function () {
            return 0x0;
        }),
        (Window_Doodads_Icons[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x26ae74 = _0x9a8c74,
                _0x592668 = ImageManager[_0x26ae74(0x1c7)](_0x26ae74(0x3ce)),
                _0x1b9770 = Math[_0x26ae74(0x223)](
                    _0x592668[_0x26ae74(0x158)] / ImageManager[_0x26ae74(0x38b)]
                ),
                _0x3a5f3c = _0x1b9770 * this['maxCols']();
            for (let _0x3a6c19 = 0x0; _0x3a6c19 < _0x3a5f3c; _0x3a6c19++) {
                this['addCommand']('', _0x26ae74(0x235), _0x3a6c19 > 0x0, _0x3a6c19);
            }
        }),
        (Window_Doodads_Icons['prototype'][_0x9a8c74(0x1b9)] = function (_0x169185) {
            const _0x465b49 = _0x9a8c74,
                _0x5ea91f = this[_0x465b49(0x1a3)](_0x169185),
                _0x1afc29 = this['_list'][_0x169185]['ext'];
            (this[_0x465b49(0x1a6)](!![]),
                this['drawIcon'](_0x1afc29, _0x5ea91f['x'] + 0x2, _0x5ea91f['y'] + 0x2));
        }),
        (Window_Doodads_Icons['prototype']['drawIcon'] = function (
            _0x320fe5,
            _0x15ed5d,
            _0x1ca767
        ) {
            const _0xbd0ebf = _0x9a8c74,
                _0x3661a4 = ImageManager[_0xbd0ebf(0x1c7)](_0xbd0ebf(0x3ce)),
                _0x18deb4 = ImageManager['iconWidth'],
                _0x25fa7f = ImageManager[_0xbd0ebf(0x38b)],
                _0x249be0 = (_0x320fe5 % 0x10) * _0x18deb4,
                _0x50ebb0 = Math[_0xbd0ebf(0x223)](_0x320fe5 / 0x10) * _0x25fa7f;
            this[_0xbd0ebf(0x3d3)][_0xbd0ebf(0x289)](
                _0x3661a4,
                _0x249be0,
                _0x50ebb0,
                _0x18deb4,
                _0x25fa7f,
                _0x15ed5d,
                _0x1ca767
            );
        }));
    function Window_Doodads_Tiles(..._0x4deabb) {
        const _0x41a130 = _0x9a8c74;
        this[_0x41a130(0x35b)](..._0x4deabb);
    }
    ((Window_Doodads_Tiles[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Tiles),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            const _0x2c540f = _0x9a8c74;
            return Math['floor']((Graphics[_0x2c540f(0x3a9)] - this[_0x2c540f(0x15e)]()) / 0x2);
        }),
        (Window_Doodads_Tiles['prototype']['contentsWidth'] = function () {
            const _0x1b7483 = _0x9a8c74;
            return this[_0x1b7483(0x32d)]() * $gameMap[_0x1b7483(0x3b6)]();
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            const _0x9e6175 = _0x9a8c74,
                _0x12e424 = this[_0x9e6175(0x126)]();
            return _0x12e424 + 0x18;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0xf5)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x49367d = _0x9a8c74;
            return Graphics[_0x49367d(0x158)];
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)]['maxCols'] = function () {
            return 0x10;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x38d)] = function () {
            return $gameMap['tileHeight']();
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x156)] = function () {
            return this['lineHeight']();
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x2e7)] = function () {
            const _0x1bbee3 = _0x9a8c74;
            return $gameMap[_0x1bbee3(0x3b6)]();
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x7c)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x222)] = function (_0x526066) {
            const _0x2211c3 = _0x9a8c74;
            for (const _0x52878e of _0x526066) {
                this[_0x2211c3(0x133)]('', _0x2211c3(0x9b), _0x52878e > 0x0, _0x52878e);
            }
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x42d58b = _0x9a8c74,
                _0x5c4f57 = $gameMap[_0x42d58b(0x30d)]();
            if (!_0x5c4f57) return;
            (_0x5c4f57[_0x42d58b(0x3d8)][0x5] && this[_0x42d58b(0x222)](Doodads[_0x42d58b(0xd9)]),
                _0x5c4f57[_0x42d58b(0x3d8)][0x6] &&
                    this[_0x42d58b(0x222)](Doodads[_0x42d58b(0x3b3)]),
                _0x5c4f57['tilesetNames'][0x7] && this[_0x42d58b(0x222)](Doodads[_0x42d58b(0x3a0)]),
                _0x5c4f57['tilesetNames'][0x8] &&
                    this[_0x42d58b(0x222)](Doodads[_0x42d58b(0x2fb)]));
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x9b)] = function () {
            const _0x260c6f = _0x9a8c74;
            return this['_list'][this[_0x260c6f(0x357)]()]?.['ext'] ?? 0x0;
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x111b48) {
            const _0x464e17 = _0x9a8c74,
                _0x5c4460 = this[_0x464e17(0x1a3)](_0x111b48),
                _0x308d4b = this[_0x464e17(0x270)][_0x111b48][_0x464e17(0x164)];
            this[_0x464e17(0x1a6)](!![]);
            const _0xc9395c = this['drawTile'](_0x308d4b, _0x5c4460['x'], _0x5c4460['y']);
            _0xc9395c && !_0xc9395c['isReady']() && (this['_needsRefresh'] = _0xc9395c);
        }),
        (Window_Doodads_Tiles[_0x9a8c74(0x11a)]['update'] = function () {
            const _0x4e53b1 = _0x9a8c74;
            (Window_Doodads_Command[_0x4e53b1(0x11a)][_0x4e53b1(0x33f)][_0x4e53b1(0x355)](this),
                this[_0x4e53b1(0x309)] &&
                    this[_0x4e53b1(0x309)][_0x4e53b1(0x304)]() &&
                    ((this[_0x4e53b1(0x309)] = ![]), this[_0x4e53b1(0xa8)]()));
        }));
    function Window_Doodads_Settings(..._0x143dfe) {
        this['initialize'](..._0x143dfe);
    }
    ((Window_Doodads_Settings['prototype'] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['constructor'] = Window_Doodads_Settings),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            return 'left';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['windowHeight'] = function () {
            const _0x2b1f89 = _0x9a8c74;
            return Graphics[_0x2b1f89(0x158)];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['setDoodad'] = function (_0x58c2ab) {
            const _0x4a2cac = _0x9a8c74;
            ((this[_0x4a2cac(0x307)] = { ..._0x58c2ab }),
                (this[_0x4a2cac(0x243)] = _0x58c2ab),
                this[_0x4a2cac(0xa8)]());
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x3ef827 = _0x9a8c74;
            (this[_0x3ef827(0x187)](),
                this['addPositionCommands'](),
                this[_0x3ef827(0x2e3)](),
                this['addCustomCommands']());
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['addLineCommand'] = function () {
            const _0x2e8a6e = _0x9a8c74;
            this[_0x2e8a6e(0x270)][this['_list'][_0x2e8a6e(0x352)] - 0x1][_0x2e8a6e(0x27f)] !==
                _0x2e8a6e(0xc9) && this[_0x2e8a6e(0x133)](_0x2e8a6e(0xc9), _0x2e8a6e(0xb3), ![]);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x187)] = function () {
            const _0x582763 = _0x9a8c74;
            (this['addCommand'](_0x582763(0x2c3), _0x582763(0x20e)),
                this[_0x582763(0x133)]('Revert\x20Settings', _0x582763(0x325)),
                this['addCommand'](
                    _0x582763(0x380),
                    _0x582763(0x333),
                    !DoodadManager[_0x582763(0xa2)]
                ),
                this[_0x582763(0x263)]());
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x201)] = function () {
            const _0x37709c = _0x9a8c74;
            (this[_0x37709c(0x263)](),
                this['addCommand'](
                    _0x37709c(0x142),
                    _0x37709c(0x1df),
                    !DoodadManager['_canvasMode']
                ),
                this[_0x37709c(0x263)]());
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getSettingsList'] = function () {
            const _0x3d3843 = _0x9a8c74;
            return [
                { name: _0x3d3843(0x1ba), symbol: _0x3d3843(0x332) },
                { name: 'Hue', symbol: 'hue' },
                { name: _0x3d3843(0x3b7), symbol: 'opacity' },
                { name: 'Scale\x20X', symbol: _0x3d3843(0x378) },
                { name: _0x3d3843(0x2f2), symbol: _0x3d3843(0x3c0) },
                { name: 'Anchor\x20X', symbol: 'anchorX' },
                { name: 'Anchor\x20Y', symbol: 'anchorY' },
                {
                    name: _0x3d3843(0x18d),
                    symbol: _0x3d3843(0x189),
                    visible: this[_0x3d3843(0xe5)](),
                },
                { name: _0x3d3843(0x197), symbol: _0x3d3843(0x1d4) },
                { name: 'Smooth', symbol: _0x3d3843(0xe9) },
                { name: 'Position\x20Type', symbol: 'positionType' },
                { name: 'Angle', symbol: _0x3d3843(0x1c9) },
                {
                    name: '---line---',
                    symbol: 'none',
                    visible: this['_doodad']?.[_0x3d3843(0x235)] > 0x0,
                },
                {
                    name: _0x3d3843(0x11e),
                    symbol: _0x3d3843(0x235),
                    visible: this[_0x3d3843(0x243)]?.[_0x3d3843(0x235)] > 0x0,
                },
                {
                    name: _0x3d3843(0xc9),
                    symbol: _0x3d3843(0xb3),
                    visible: this['_doodad']?.[_0x3d3843(0x9b)] > 0x0,
                },
                {
                    name: _0x3d3843(0x162),
                    symbol: _0x3d3843(0x9b),
                    visible: this[_0x3d3843(0x243)]?.[_0x3d3843(0x9b)] > 0x0,
                },
                {
                    name: _0x3d3843(0x1b2),
                    symbol: _0x3d3843(0x396),
                    visible: this[_0x3d3843(0x243)]?.[_0x3d3843(0x9b)] > 0x0,
                },
                {
                    name: _0x3d3843(0x351),
                    symbol: _0x3d3843(0x195),
                    visible: this['_doodad']?.['tileId'] > 0x0,
                },
                { name: _0x3d3843(0xc9), symbol: _0x3d3843(0xb3) },
                { name: _0x3d3843(0x144), symbol: 'blur' },
                {
                    name: 'Outline',
                    symbol: _0x3d3843(0x408),
                    visible: Boolean(PIXI[_0x3d3843(0x365)]['OutlineFilter']),
                },
                {
                    name: 'Shadow',
                    symbol: _0x3d3843(0x252),
                    visible: Boolean(PIXI[_0x3d3843(0x365)]['DropShadowFilter']),
                },
                {
                    name: _0x3d3843(0xa6),
                    symbol: _0x3d3843(0x185),
                    visible: Boolean(PIXI[_0x3d3843(0x365)][_0x3d3843(0x20b)]),
                },
                { name: 'Contrast', symbol: _0x3d3843(0x147) },
                { name: _0x3d3843(0x30f), symbol: _0x3d3843(0x2a5) },
                { name: _0x3d3843(0xc9), symbol: 'none' },
                { name: 'Party', symbol: _0x3d3843(0x399) },
                { name: _0x3d3843(0x26e), symbol: _0x3d3843(0x3ab) },
                { name: '---line---', symbol: _0x3d3843(0xb3) },
                { name: 'Tone\x20Preset', symbol: _0x3d3843(0x272) },
                { name: _0x3d3843(0x31c), symbol: _0x3d3843(0x3ad) },
                { name: _0x3d3843(0x29f), symbol: 'toneGreen' },
                { name: _0x3d3843(0x12d), symbol: 'toneBlue' },
                { name: _0x3d3843(0x32f), symbol: _0x3d3843(0x236) },
                { name: _0x3d3843(0x145), symbol: _0x3d3843(0x80) },
                { name: _0x3d3843(0x11b), symbol: _0x3d3843(0x233) },
                { name: 'Tone:\x20Randomize\x20Blue', symbol: _0x3d3843(0x383) },
                { name: _0x3d3843(0x3b0), symbol: _0x3d3843(0x1ef) },
                { name: _0x3d3843(0x328), symbol: _0x3d3843(0x8b) },
            ];
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x130)] = function () {
            const _0x25e15b = _0x9a8c74,
                _0x5c92ea = this[_0x25e15b(0x3df)]()[_0x25e15b(0x7f)](
                    _0xfa70a1 => _0xfa70a1[_0x25e15b(0x93)] !== _0x25e15b(0xb3)
                );
            return (
                _0x5c92ea['sort']((_0x13328e, _0x3878cc) =>
                    _0x13328e['name'] > _0x3878cc['name']
                        ? 0x1
                        : _0x13328e['name'] < _0x3878cc[_0x25e15b(0x27f)]
                          ? -0x1
                          : 0x0
                ),
                _0x5c92ea
            );
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['addCommandList'] = function (_0x54aa34) {
            const _0x292077 = _0x9a8c74;
            for (const {
                name: _0x56d1e1,
                symbol: _0x472f8b,
                enabled: enabled = !![],
                visible: visible = !![],
            } of _0x54aa34) {
                if (!visible) continue;
                this[_0x292077(0x133)](_0x56d1e1, _0x472f8b, enabled);
            }
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x2e3)] = function () {
            const _0xa7291a = _0x9a8c74;
            if (DoodadManager['settings'][_0xa7291a(0x146)]) return;
            (this['addLineCommand'](), this['addCommandList'](this[_0xa7291a(0x3df)]()));
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x3cb)] = function () {
            const _0x4b716f = _0x9a8c74;
            (this['addLineCommand'](),
                DoodadManager[_0x4b716f(0x388)]['GFDAlphabet'] && this[_0x4b716f(0x263)](),
                this[_0x4b716f(0x402)](!![]),
                DoodadManager[_0x4b716f(0x388)][_0x4b716f(0x146)] &&
                    this[_0x4b716f(0x2da)](this['getSortedSettings']()),
                this[_0x4b716f(0x38f)]());
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x402)] = function () {}),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x38f)] = function () {}),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['isAnimated'] = function () {
            const _0x31c8ae = _0x9a8c74;
            return (
                this[_0x31c8ae(0x243)]?.[_0x31c8ae(0x110)] > 0x1 ||
                this[_0x31c8ae(0x243)]?.[_0x31c8ae(0x317)] > 0x1
            );
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x19f)] = function () {
            const _0x1661f3 = _0x9a8c74;
            return this[_0x1661f3(0x21b)]();
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0xb2)] = function () {
            const _0x451658 = _0x9a8c74;
            return this[_0x451658(0x243)][_0x451658(0x20d)];
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x265)] = function () {
            return this['_doodad']['opacity'];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x1ae)] = function () {
            const _0xcd8909 = _0x9a8c74;
            return this[_0xcd8909(0x243)]['scaleX'] + '%';
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x155)] = function () {
            const _0xc0ab10 = _0x9a8c74;
            return this[_0xc0ab10(0x243)][_0xc0ab10(0x3c0)] + '%';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_positionType'] = function () {
            const _0x2646c3 = _0x9a8c74;
            return this[_0x2646c3(0x243)][_0x2646c3(0x111)] === _0x2646c3(0x242)
                ? _0x2646c3(0x14d)
                : 'Map';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0xe1)] = function () {
            const _0x233711 = _0x9a8c74;
            return (this[_0x233711(0x243)][_0x233711(0x1c9)] ?? 0x0) + '°\x20';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x2cc)] = function () {
            const _0x84a644 = _0x9a8c74;
            return this[_0x84a644(0x243)][_0x84a644(0x9b)];
        }),
        (Window_Doodads_Settings['prototype']['getItemText_tileCols'] = function () {
            const _0x22d005 = _0x9a8c74;
            return this[_0x22d005(0x243)][_0x22d005(0x396)];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x109)] = function () {
            const _0x799236 = _0x9a8c74;
            return this[_0x799236(0x243)][_0x799236(0x195)];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0xf0)] = function () {
            const _0x1902c0 = _0x9a8c74;
            if (this['_doodad']['anchorX'] <= 0x0) return _0x1902c0(0x271);
            else return this[_0x1902c0(0x243)][_0x1902c0(0x2c7)] >= 0x1 ? 'Right' : 'Center';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x1d9)] = function () {
            const _0x3cddaf = _0x9a8c74;
            if (this[_0x3cddaf(0x243)][_0x3cddaf(0x31a)] <= 0x0) return _0x3cddaf(0x2ce);
            else
                return this[_0x3cddaf(0x243)][_0x3cddaf(0x31a)] >= 0x1
                    ? _0x3cddaf(0x259)
                    : 'Middle';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x2a6)] = function () {
            const _0xed2e80 = _0x9a8c74;
            return this[_0xed2e80(0xe5)]() ? this['_doodad'][_0xed2e80(0x1a4)] : _0xed2e80(0x3f3);
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x2e0)] = function () {
            return this['blendText']();
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x219)] = function () {
            const _0x11b269 = _0x9a8c74;
            return this[_0x11b269(0x243)][_0x11b269(0xe9)] ? 'Smooth' : _0x11b269(0x29e);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x171)] = function () {
            const _0x10c526 = _0x9a8c74;
            return this[_0x10c526(0x243)][_0x10c526(0x147)] ? _0x10c526(0x239) : _0x10c526(0x35a);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x2b6)] = function () {
            const _0x4cfbd8 = _0x9a8c74;
            return this[_0x4cfbd8(0x243)]['sepia'] ? 'ON' : _0x4cfbd8(0x2ef);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x3a5)] = function () {
            const _0x453072 = _0x9a8c74;
            return this[_0x453072(0x243)][_0x453072(0x1e3)] ? 'Blurred' : _0x453072(0x12b);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_outline'] = function () {
            const _0x4c861c = _0x9a8c74;
            return this[_0x4c861c(0x243)]['outline'] ? 'ON' : _0x4c861c(0x2ef);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_shadow'] = function () {
            const _0x36c27a = _0x9a8c74;
            return this[_0x36c27a(0x243)][_0x36c27a(0x252)] ? 'ON' : _0x36c27a(0x2ef);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0xab)] = function () {
            const _0x59919d = _0x9a8c74;
            return this['_doodad'][_0x59919d(0x185)] ? 'ON' : 'OFF';
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_party'] = function () {
            const _0x564a4e = _0x9a8c74;
            if (!this['_doodad']) return;
            if (
                this[_0x564a4e(0x243)][_0x564a4e(0xd8)]?.['length'] > 0x1 ||
                this[_0x564a4e(0x243)][_0x564a4e(0x25b)]?.[_0x564a4e(0x352)] > 0x1
            )
                return 'Many';
            if (
                this[_0x564a4e(0x243)][_0x564a4e(0xd8)]?.[_0x564a4e(0x352)] === 0x1 &&
                this[_0x564a4e(0x243)][_0x564a4e(0x25b)]?.[_0x564a4e(0x352)] === 0x1
            )
                return _0x564a4e(0x1f3);
            if (this[_0x564a4e(0x243)][_0x564a4e(0xd8)]?.[_0x564a4e(0x352)]) {
                const _0x39b04f = this[_0x564a4e(0x243)]['partyHave'][0x0],
                    _0x4e9658 = $gameActors[_0x564a4e(0x393)](_0x39b04f);
                return _0x4e9658 ? _0x4e9658['name']() + _0x564a4e(0x34d) : 'Null';
            }
            if (this[_0x564a4e(0x243)]['partyMiss']?.[_0x564a4e(0x352)]) {
                const _0x26e770 = this[_0x564a4e(0x243)][_0x564a4e(0x25b)][0x0],
                    _0x801240 = $gameActors[_0x564a4e(0x393)](_0x26e770);
                return _0x801240
                    ? _0x801240[_0x564a4e(0x27f)]() + _0x564a4e(0x154)
                    : _0x564a4e(0x9a);
            }
            return _0x564a4e(0x1a2);
        }),
        (Window_Doodads_Settings['prototype']['getItemText_toneSet'] = function () {
            const _0x322708 = _0x9a8c74,
                _0x4bf906 = this[_0x322708(0x243)]?.[_0x322708(0x3ad)] ?? 0x0,
                _0x595cef = this[_0x322708(0x243)]?.[_0x322708(0xd5)] ?? 0x0,
                _0x5e6474 = this[_0x322708(0x243)]?.[_0x322708(0x1c8)] ?? 0x0,
                _0x3e15ef = this[_0x322708(0x243)]?.[_0x322708(0x236)] ?? 0x0;
            for (const _0x523e91 in Doodads[_0x322708(0x3ea)]) {
                if (!Doodads[_0x322708(0x3ea)]['hasOwnProperty'](_0x523e91)) continue;
                const _0x1f287a = Doodads['toneColors'][_0x523e91];
                if (
                    !DoodadManager[_0x322708(0x78)](
                        _0x1f287a,
                        _0x4bf906,
                        _0x595cef,
                        _0x5e6474,
                        _0x3e15ef
                    )
                )
                    continue;
                return _0x523e91;
            }
            return _0x322708(0x2bf);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_toneRed'] = function () {
            const _0x3f3eee = _0x9a8c74;
            return this['_doodad']?.[_0x3f3eee(0x3ad)] ?? 0x0;
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText_toneGreen'] = function () {
            const _0x2585b8 = _0x9a8c74;
            return this[_0x2585b8(0x243)]?.[_0x2585b8(0xd5)] ?? 0x0;
        }),
        (Window_Doodads_Settings['prototype']['getItemText_toneBlue'] = function () {
            const _0x1b8651 = _0x9a8c74;
            return this[_0x1b8651(0x243)]?.[_0x1b8651(0x1c8)] ?? 0x0;
        }),
        (Window_Doodads_Settings['prototype']['getItemText_toneGrey'] = function () {
            const _0x2bc0fa = _0x9a8c74;
            return this['_doodad']?.[_0x2bc0fa(0x236)] ?? 0x0;
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x335)] = function () {
            const _0x58bc56 = _0x9a8c74;
            if (!this['_doodad']) return;
            if (
                this[_0x58bc56(0x243)][_0x58bc56(0x379)]?.[_0x58bc56(0x352)] > 0x1 ||
                this[_0x58bc56(0x243)][_0x58bc56(0x20c)]?.[_0x58bc56(0x352)] > 0x1
            )
                return _0x58bc56(0x1f3);
            if (
                this[_0x58bc56(0x243)][_0x58bc56(0x379)]?.[_0x58bc56(0x352)] === 0x1 &&
                this[_0x58bc56(0x243)]['switchOff']?.[_0x58bc56(0x352)] === 0x1
            )
                return _0x58bc56(0x1f3);
            if (this[_0x58bc56(0x243)][_0x58bc56(0x379)]?.[_0x58bc56(0x352)])
                return (
                    _0x58bc56(0xcb) +
                    this[_0x58bc56(0x243)][_0x58bc56(0x379)][0x0] +
                    _0x58bc56(0x274)
                );
            if (this[_0x58bc56(0x243)]['switchOff']?.[_0x58bc56(0x352)])
                return (
                    _0x58bc56(0xcb) +
                    this[_0x58bc56(0x243)][_0x58bc56(0x20c)][0x0] +
                    _0x58bc56(0x33d)
                );
            return _0x58bc56(0x1a2);
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['getItemText'] = function (_0x1390dc) {
            const _0x39196e = _0x9a8c74,
                _0x18b6e3 = _0x39196e(0x377) + _0x1390dc;
            if (this[_0x18b6e3]) return this[_0x18b6e3]();
        }),
        (Window_Doodads_Settings['prototype'][_0x9a8c74(0x1f7)] = function (_0xef8e82) {
            const _0xc1988d = _0x9a8c74;
            switch (_0xef8e82) {
                case _0xc1988d(0x3ad):
                    return this[_0xc1988d(0x3e9)]('#FF0000');
                case 'toneGreen':
                    return this['changeTextColor'](_0xc1988d(0x39f));
                case _0xc1988d(0x1c8):
                    return this['changeTextColor']('#0000FF');
                case _0xc1988d(0x236):
                    return this[_0xc1988d(0x3e9)]('#888888');
            }
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x2fb449) {
            const _0x546315 = _0x9a8c74;
            this['changeTextColor'](ColorManager[_0x546315(0x3aa)]());
            if (this[_0x546315(0x230)](_0x2fb449) === _0x546315(0xc9)) {
                this[_0x546315(0x23b)](this[_0x546315(0x34f)](_0x2fb449)['y']);
                return;
            }
            Window_Doodads_Command['prototype'][_0x546315(0x1b9)][_0x546315(0x355)](
                this,
                _0x2fb449
            );
            if (!this['_doodad']) return;
            const _0x25cbc3 = this[_0x546315(0x285)](_0x2fb449),
                _0x1c4680 = this[_0x546315(0x330)](_0x25cbc3) ?? '';
            if (_0x1c4680 !== '') {
                this[_0x546315(0x1f7)](_0x25cbc3);
                const _0x169809 = this[_0x546315(0x34f)](_0x2fb449);
                this['drawText'](
                    _0x1c4680,
                    _0x169809['x'],
                    _0x169809['y'],
                    _0x169809['width'],
                    _0x546315(0x28a)
                );
            }
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['drawHorzLine'] = function (_0x233768) {
            const _0x45b62b = _0x9a8c74,
                _0x26ea15 = _0x233768 + this['lineHeight']() / 0x2 - 0x1;
            ((this[_0x45b62b(0x40b)][_0x45b62b(0xe2)] = 0x80),
                this[_0x45b62b(0x40b)][_0x45b62b(0x280)](
                    0x8,
                    _0x26ea15,
                    this[_0x45b62b(0x126)]() - 0x10,
                    0x2,
                    this[_0x45b62b(0x249)]()
                ),
                (this['contents'][_0x45b62b(0xe2)] = 0xff));
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x249)] = function () {
            const _0x277b9e = _0x9a8c74;
            return ColorManager[_0x277b9e(0x3aa)]();
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x21b)] = function () {
            const _0x5ae6a8 = _0x9a8c74,
                _0x1da068 = DoodadManager[_0x5ae6a8(0x322)]()[_0x5ae6a8(0x3d1)](
                    this[_0x5ae6a8(0x243)]['z']
                );
            return DoodadManager[_0x5ae6a8(0x1fa)]()[_0x1da068];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)]['blendText'] = function () {
            const _0x53ca77 = _0x9a8c74,
                _0x1852c8 = DoodadManager[_0x53ca77(0x30e)](),
                _0x126a6d = this[_0x53ca77(0x243)]['blend'];
            return _0x1852c8[_0x126a6d]?.[_0x53ca77(0x27f)];
        }),
        (Window_Doodads_Settings[_0x9a8c74(0x11a)][_0x9a8c74(0x325)] = function () {
            const _0x3aeb9c = _0x9a8c74;
            (DoodadManager[_0x3aeb9c(0x2ff)](this[_0x3aeb9c(0x243)], this[_0x3aeb9c(0x307)]),
                DoodadManager[_0x3aeb9c(0x16c)](),
                DoodadManager[_0x3aeb9c(0x277)](),
                this['refresh']());
        }));
    function Window_Doodads_Settings_Layers(..._0x22f3cb) {
        const _0x22b962 = _0x9a8c74;
        this[_0x22b962(0x35b)](..._0x22f3cb);
    }
    ((Window_Doodads_Settings_Layers['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Layers),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)]['windowWidth'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)][_0x9a8c74(0x1bb)] = function () {
            const _0x54054c = _0x9a8c74,
                _0x2aedf0 = DoodadManager['layerList'](),
                _0x30b430 = DoodadManager[_0x54054c(0x1d3)]();
            if (_0x2aedf0[_0x54054c(0x352)] !== _0x30b430[_0x54054c(0x352)]) {
                console['error']('Invalid\x20Layer\x20List');
                return;
            }
            return _0x2aedf0[_0x54054c(0x395)]((_0x324279, _0x66b76) => ({
                value: _0x324279,
                title: _0x30b430[_0x66b76],
            }));
        }),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x18e1c9 = _0x9a8c74,
                _0x5bdcde = this[_0x18e1c9(0x1bb)]();
            _0x5bdcde['reverse']();
            for (const { title: _0x4694d0, value: _0x31c672 } of _0x5bdcde) {
                this[_0x18e1c9(0x133)](_0x4694d0, _0x18e1c9(0x332), !![], _0x31c672);
            }
        }),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x2bb3c6 = _0x9a8c74,
                _0xba2483 = DoodadManager['layerList']();
            return this['fittingHeight'](_0xba2483[_0x2bb3c6(0x352)]);
        }),
        (Window_Doodads_Settings_Layers[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            return 'left';
        }));
    function Window_Doodads_Settings_Hue(..._0x4f375f) {
        this['initialize'](..._0x4f375f);
    }
    ((Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Hue),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Hue['prototype'][_0x9a8c74(0x15e)] = function () {
            const _0x1245be = _0x9a8c74;
            return this[_0x1245be(0x38d)]() * 0x6 + this[_0x1245be(0xf5)]() * 0x2;
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x32d)] = function () {
            return 0x6;
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x34df83 = _0x9a8c74;
            for (let _0x54fa71 = 0x0; _0x54fa71 < 0x24; _0x54fa71++) {
                this[_0x34df83(0x133)]('', _0x34df83(0x20d), !![], _0x54fa71 * 0xa);
            }
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x647c2c) {
            const _0x29cb68 = _0x9a8c74;
            if (!this[_0x29cb68(0x1de)]()) return;
            this[_0x29cb68(0x1e9)](_0x647c2c);
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x1de)] = function () {
            const _0x216a71 = _0x9a8c74;
            return SceneManager[_0x216a71(0x3cd)][_0x216a71(0x36f)][_0x216a71(0x243)];
        }),
        (Window_Doodads_Settings_Hue[_0x9a8c74(0x11a)][_0x9a8c74(0x1e9)] = function (_0x37c454) {
            const _0x465876 = _0x9a8c74,
                _0x520be6 = this[_0x465876(0x1de)](),
                _0x5c8577 = _0x520be6['bitmap'],
                _0x2b2e41 = _0x520be6[_0x465876(0xb8)] + _0x5c8577,
                _0x327c55 = this[_0x465876(0x270)][_0x37c454]['ext'],
                _0x111b89 = ImageManager['loadDoodad'](_0x2b2e41, !![], _0x520be6[_0x465876(0x9b)]);
            if (_0x111b89[_0x465876(0x3a9)] <= 0x0)
                return setTimeout(this[_0x465876(0x1e9)]['bind'](this, _0x37c454, _0x5c8577), 0x5);
            const _0x50fa96 = this[_0x465876(0x1a3)](_0x37c454),
                _0x1be934 = DoodadManager[_0x465876(0x373)](_0x5c8577),
                _0x1d6ed3 = DoodadManager[_0x465876(0x3dc)](_0x5c8577),
                _0x2c965c = Math['floor'](_0x111b89['width'] / _0x1be934),
                _0x4aa616 = Math[_0x465876(0x223)](_0x111b89['height'] / _0x1d6ed3);
            let _0xa02c1c = _0x2c965c,
                _0x48570e = _0x4aa616;
            if (_0x520be6[_0x465876(0x235)])
                ((_0xa02c1c = ImageManager[_0x465876(0x40c)]),
                    (_0x48570e = ImageManager[_0x465876(0x38b)]));
            else {
                if (_0xa02c1c > ImageManager['iconWidth']) {
                    const _0x4c1d3b = ImageManager[_0x465876(0x40c)] / _0xa02c1c;
                    ((_0xa02c1c *= _0x4c1d3b), (_0x48570e *= _0x4c1d3b));
                }
                if (_0x48570e > ImageManager[_0x465876(0x38b)]) {
                    const _0x1bcc02 = ImageManager[_0x465876(0x38b)] / _0x48570e;
                    ((_0xa02c1c *= _0x1bcc02), (_0x48570e *= _0x1bcc02));
                }
            }
            const _0x25950f =
                    _0x50fa96['x'] + 0x2 + (ImageManager[_0x465876(0x40c)] - _0xa02c1c) / 0x2,
                _0x66a53e =
                    _0x50fa96['y'] + 0x2 + (ImageManager[_0x465876(0x38b)] - _0x48570e) / 0x2,
                _0x72421d = new Bitmap(_0xa02c1c, _0x48570e);
            if (_0x520be6[_0x465876(0x235)]) {
                const _0x314fc8 =
                        (_0x520be6[_0x465876(0x235)] % 0x10) * ImageManager[_0x465876(0x40c)],
                    _0x22ff1f =
                        Math[_0x465876(0x223)](_0x520be6[_0x465876(0x235)] / 0x10) *
                        ImageManager[_0x465876(0x38b)];
                _0x72421d[_0x465876(0x289)](
                    _0x111b89,
                    _0x314fc8,
                    _0x22ff1f,
                    ImageManager[_0x465876(0x40c)],
                    ImageManager[_0x465876(0x38b)],
                    0x0,
                    0x0,
                    ImageManager[_0x465876(0x40c)],
                    ImageManager['iconHeight']
                );
            } else {
                if (_0x520be6['tileId']) {
                    const _0x16b6c8 = $gameMap[_0x465876(0x3b6)](),
                        _0x574eca = $gameMap[_0x465876(0x287)](),
                        _0x318133 =
                            ((Math['floor'](_0x520be6['tileId'] / 0x80) % 0x2) * 0x8 +
                                (_0x520be6['tileId'] % 0x8)) *
                            _0x16b6c8,
                        _0x1ec9ff =
                            (Math[_0x465876(0x223)]((_0x520be6[_0x465876(0x9b)] % 0x100) / 0x8) %
                                0x10) *
                            _0x574eca;
                    _0x72421d[_0x465876(0x289)](
                        _0x111b89,
                        _0x318133,
                        _0x1ec9ff,
                        _0x16b6c8,
                        _0x574eca,
                        0x0,
                        0x0,
                        _0xa02c1c,
                        _0x48570e
                    );
                } else
                    _0x72421d[_0x465876(0x289)](
                        _0x111b89,
                        0x0,
                        0x0,
                        _0x2c965c,
                        _0x4aa616,
                        0x0,
                        0x0,
                        _0xa02c1c,
                        _0x48570e
                    );
            }
            (DoodadManager[_0x465876(0x33e)](_0x72421d, _0x327c55),
                this[_0x465876(0x40b)]['blt'](
                    _0x72421d,
                    0x0,
                    0x0,
                    _0xa02c1c,
                    _0x48570e,
                    _0x25950f,
                    _0x66a53e
                ));
        }));
    function Window_Doodads_Settings_Tone_Presets() {
        const _0x50c14a = _0x9a8c74;
        this[_0x50c14a(0x35b)][_0x50c14a(0x2a7)](this, arguments);
    }
    ((Window_Doodads_Settings_Tone_Presets[_0x9a8c74(0x11a)] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tone_Presets[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tone_Presets),
        (Window_Doodads_Settings_Tone_Presets['prototype'][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tone_Presets['prototype']['windowHeight'] = function () {
            const _0x147f7f = _0x9a8c74,
                _0x30a8e8 = this[_0x147f7f(0x207)](
                    Object[_0x147f7f(0x2e9)](Doodads[_0x147f7f(0x3ea)])[_0x147f7f(0x352)]
                );
            return Math[_0x147f7f(0x1d1)](Graphics[_0x147f7f(0x158)], _0x30a8e8);
        }),
        (Window_Doodads_Settings_Tone_Presets[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x56bebc = _0x9a8c74;
            for (const _0x584377 in Doodads[_0x56bebc(0x3ea)]) {
                if (!Doodads[_0x56bebc(0x3ea)][_0x56bebc(0x117)](_0x584377)) continue;
                const _0x50e837 = Doodads[_0x56bebc(0x3ea)][_0x584377];
                this[_0x56bebc(0x133)](_0x584377, _0x56bebc(0x98), !![], [
                    _0x50e837[_0x56bebc(0x340)],
                    _0x50e837[_0x56bebc(0x360)],
                    _0x50e837['blue'],
                    _0x50e837[_0x56bebc(0x3a6)],
                ]);
            }
        }),
        (Window_Doodads_Settings_Tone_Presets['prototype'][_0x9a8c74(0x205)] = function () {
            const _0x446291 = _0x9a8c74;
            return _0x446291(0x292);
        }));
    function Window_Doodads_Settings_Tone_RGB() {
        const _0x477670 = _0x9a8c74;
        this['initialize'][_0x477670(0x2a7)](this, arguments);
    }
    ((Window_Doodads_Settings_Tone_RGB[_0x9a8c74(0x11a)] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tone_RGB[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tone_RGB),
        (Window_Doodads_Settings_Tone_RGB[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tone_RGB['prototype'][_0x9a8c74(0x231)] = function () {
            const _0x2f6b3c = _0x9a8c74,
                _0x122a84 = this['fittingHeight'](0x13);
            return Math[_0x2f6b3c(0x1d1)](Graphics[_0x2f6b3c(0x158)], _0x122a84);
        }),
        (Window_Doodads_Settings_Tone_RGB['prototype'][_0x9a8c74(0x294)] = function () {
            const _0x3c7e4f = _0x9a8c74;
            (this[_0x3c7e4f(0x133)]('255', _0x3c7e4f(0x272), !![], 0xff),
                this[_0x3c7e4f(0x133)]('250', 'toneSet', !![], 0xfa),
                this['addCommand']('200', _0x3c7e4f(0x272), !![], 0xc8),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x26d), _0x3c7e4f(0x272), !![], 0x96),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x2fc), _0x3c7e4f(0x272), !![], 0x80),
                this[_0x3c7e4f(0x133)]('100', 'toneSet', !![], 0x64),
                this[_0x3c7e4f(0x133)]('75', _0x3c7e4f(0x272), !![], 0x4b),
                this['addCommand']('50', _0x3c7e4f(0x272), !![], 0x32),
                this[_0x3c7e4f(0x133)]('25', _0x3c7e4f(0x272), !![], 0x19),
                this['addCommand']('0', _0x3c7e4f(0x272), !![], 0x0),
                this[_0x3c7e4f(0x133)]('-25', _0x3c7e4f(0x272), !![], -0x19),
                this[_0x3c7e4f(0x133)]('-50', _0x3c7e4f(0x272), !![], -0x32),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x10d), _0x3c7e4f(0x272), !![], -0x4b),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x279), _0x3c7e4f(0x272), !![], -0x64),
                this[_0x3c7e4f(0x133)]('-128', 'toneSet', !![], -0x80),
                this['addCommand'](_0x3c7e4f(0xc7), _0x3c7e4f(0x272), !![], -0x96),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x1b7), 'toneSet', !![], -0xc8),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x1bc), _0x3c7e4f(0x272), !![], -0xfa),
                this[_0x3c7e4f(0x133)](_0x3c7e4f(0x97), _0x3c7e4f(0x272), !![], -0xff));
        }),
        (Window_Doodads_Settings_Tone_RGB[_0x9a8c74(0x11a)]['itemTextAlign'] = function () {
            const _0x36c0af = _0x9a8c74;
            return _0x36c0af(0x292);
        }));
    function Window_Doodads_Settings_Tone_Grey() {
        const _0xfeaedf = _0x9a8c74;
        this[_0xfeaedf(0x35b)][_0xfeaedf(0x2a7)](this, arguments);
    }
    ((Window_Doodads_Settings_Tone_Grey[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tone_Grey[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tone_Grey),
        (Window_Doodads_Settings_Tone_Grey[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tone_Grey['prototype'][_0x9a8c74(0x231)] = function () {
            const _0x177bb3 = _0x9a8c74,
                _0x4852c8 = this[_0x177bb3(0x207)](0xa);
            return Math[_0x177bb3(0x1d1)](Graphics[_0x177bb3(0x158)], _0x4852c8);
        }),
        (Window_Doodads_Settings_Tone_Grey[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x405810 = _0x9a8c74;
            (this[_0x405810(0x133)](_0x405810(0x3eb), 'toneSet', !![], 0xff),
                this[_0x405810(0x133)](_0x405810(0x3f5), _0x405810(0x272), !![], 0xfa),
                this[_0x405810(0x133)](_0x405810(0x7a), _0x405810(0x272), !![], 0xc8),
                this[_0x405810(0x133)]('150', _0x405810(0x272), !![], 0x96),
                this[_0x405810(0x133)](_0x405810(0x2fc), 'toneSet', !![], 0x80),
                this[_0x405810(0x133)]('100', _0x405810(0x272), !![], 0x64),
                this[_0x405810(0x133)]('75', _0x405810(0x272), !![], 0x4b),
                this['addCommand']('50', _0x405810(0x272), !![], 0x32),
                this[_0x405810(0x133)]('25', _0x405810(0x272), !![], 0x19),
                this[_0x405810(0x133)]('0', _0x405810(0x272), !![], 0x0));
        }),
        (Window_Doodads_Settings_Tone_Grey[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x319b90 = _0x9a8c74;
            return _0x319b90(0x292);
        }));
    function Window_Doodads_Settings_Party(..._0x5db956) {
        const _0x469a69 = _0x9a8c74;
        this[_0x469a69(0x35b)](..._0x5db956);
    }
    ((Window_Doodads_Settings_Party[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)]['constructor'] =
            Window_Doodads_Settings_Party),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Party['prototype'][_0x9a8c74(0x15e)] = function () {
            const _0x1e0ea1 = _0x9a8c74;
            return Graphics[_0x1e0ea1(0x3a9)] - 0x190;
        }),
        (Window_Doodads_Settings_Party['prototype'][_0x9a8c74(0x231)] = function () {
            const _0x33d6d4 = _0x9a8c74;
            return Graphics[_0x33d6d4(0x158)];
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)]['maxCols'] = function () {
            return 0x4;
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x1a3)] = function (_0x3f1ba0) {
            const _0x39d59a = _0x9a8c74,
                _0x36e5e9 = Window_Doodads_Command[_0x39d59a(0x11a)][_0x39d59a(0x1a3)][
                    _0x39d59a(0x355)
                ](this, _0x3f1ba0);
            this['_textWidth'] === undefined &&
                (this[_0x39d59a(0x1fc)] = this['textWidth']('-Missing-'));
            switch (_0x3f1ba0 % 0x4) {
                case 0x0:
                    _0x36e5e9[_0x39d59a(0x3a9)] =
                        this[_0x39d59a(0x40b)][_0x39d59a(0x3a9)] - this['_textWidth'] * 0x3;
                    break;
                case 0x1:
                    ((_0x36e5e9['x'] =
                        this[_0x39d59a(0x40b)][_0x39d59a(0x3a9)] - this['_textWidth'] * 0x3),
                        (_0x36e5e9[_0x39d59a(0x3a9)] = this[_0x39d59a(0x1fc)]));
                    break;
                case 0x2:
                    ((_0x36e5e9['x'] =
                        this['contents'][_0x39d59a(0x3a9)] - this[_0x39d59a(0x1fc)] * 0x2),
                        (_0x36e5e9[_0x39d59a(0x3a9)] = this[_0x39d59a(0x1fc)]));
                    break;
                case 0x3:
                    ((_0x36e5e9['x'] =
                        this['contents'][_0x39d59a(0x3a9)] - this[_0x39d59a(0x1fc)] * 0x1),
                        (_0x36e5e9[_0x39d59a(0x3a9)] = this[_0x39d59a(0x1fc)]));
                    break;
            }
            return _0x36e5e9;
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x67a622 = _0x9a8c74;
            for (const _0x522acd of $dataActors) {
                if (!_0x522acd) continue;
                const _0x161a44 = _0x522acd[_0x67a622(0x27f)];
                if (!_0x161a44) continue;
                const _0x108639 = _0x522acd['id'][_0x67a622(0x311)](0x4) + ':\x20' + _0x161a44;
                (this['addCommand'](_0x108639, _0x67a622(0x88)),
                    this['addCommand']('n/a', _0x67a622(0x39d), !![], _0x522acd['id']),
                    this[_0x67a622(0x133)]('Joined', _0x67a622(0xd8), !![], _0x522acd['id']),
                    this[_0x67a622(0x133)]('Missing', _0x67a622(0x25b), !![], _0x522acd['id']));
            }
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x1628b8) {
            const _0x48600c = _0x9a8c74,
                _0x2fca68 = this[_0x48600c(0x285)](_0x1628b8);
            if (_0x2fca68 === 'actorName')
                return Window_Doodads_Command['prototype'][_0x48600c(0x1b9)][_0x48600c(0x355)](
                    this,
                    _0x1628b8
                );
            const _0x732d85 = this[_0x48600c(0x270)][_0x1628b8][_0x48600c(0x164)],
                _0x45e661 = this[_0x48600c(0x34f)](_0x1628b8),
                _0x11c433 = _0x48600c(0x292);
            let _0x50cc08 = ![];
            this['resetTextColor']();
            const _0x442916 =
                SceneManager[_0x48600c(0x3cd)]['_gfdSettingsWindow'][_0x48600c(0x243)];
            if (!_0x442916) return;
            ((_0x442916['partyHave'] = _0x442916['partyHave'] || []),
                (_0x442916[_0x48600c(0x25b)] = _0x442916[_0x48600c(0x25b)] || []));
            if (_0x2fca68 === _0x48600c(0x39d))
                _0x50cc08 =
                    !_0x442916['partyHave'][_0x48600c(0x39c)](_0x732d85) &&
                    !_0x442916[_0x48600c(0x25b)][_0x48600c(0x39c)](_0x732d85);
            else {
                if (_0x2fca68 === _0x48600c(0xd8))
                    _0x50cc08 = _0x442916[_0x48600c(0xd8)]['includes'](_0x732d85);
                else
                    _0x2fca68 === _0x48600c(0x25b) &&
                        (_0x50cc08 = _0x442916[_0x48600c(0x25b)][_0x48600c(0x39c)](_0x732d85));
            }
            (this[_0x48600c(0x1a6)](_0x50cc08),
                this['drawText'](
                    this[_0x48600c(0x230)](_0x1628b8),
                    _0x45e661['x'],
                    _0x45e661['y'],
                    _0x45e661[_0x48600c(0x3a9)],
                    _0x11c433
                ));
        }),
        (Window_Doodads_Settings_Party[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x4bdbd3 = _0x9a8c74;
            return _0x4bdbd3(0x2ea);
        }));
    function Window_Doodads_Settings_Switch(..._0x26dfe0) {
        const _0x4f916c = _0x9a8c74;
        this[_0x4f916c(0x35b)](..._0x26dfe0);
    }
    ((Window_Doodads_Settings_Switch['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Switch),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)]['windowWidth'] = function () {
            const _0x501848 = _0x9a8c74;
            return Graphics[_0x501848(0x3a9)] - 0x190;
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            return Graphics['height'];
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x32d)] = function () {
            return 0x4;
        }),
        (Window_Doodads_Settings_Switch['prototype'][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x1a3)] = function (_0x943c18) {
            const _0x37723f = _0x9a8c74,
                _0x34a743 = Window_Doodads_Command[_0x37723f(0x11a)]['itemRect'][_0x37723f(0x355)](
                    this,
                    _0x943c18
                );
            this['_textWidth'] === undefined &&
                (this[_0x37723f(0x1fc)] = this['textWidth'](_0x37723f(0x1c1)));
            switch (_0x943c18 % 0x4) {
                case 0x0:
                    _0x34a743['width'] =
                        this[_0x37723f(0x40b)][_0x37723f(0x3a9)] - this['_textWidth'] * 0x3;
                    break;
                case 0x1:
                    ((_0x34a743['x'] =
                        this[_0x37723f(0x40b)]['width'] - this[_0x37723f(0x1fc)] * 0x3),
                        (_0x34a743[_0x37723f(0x3a9)] = this[_0x37723f(0x1fc)]));
                    break;
                case 0x2:
                    ((_0x34a743['x'] =
                        this[_0x37723f(0x40b)][_0x37723f(0x3a9)] - this[_0x37723f(0x1fc)] * 0x2),
                        (_0x34a743[_0x37723f(0x3a9)] = this[_0x37723f(0x1fc)]));
                    break;
                case 0x3:
                    ((_0x34a743['x'] =
                        this[_0x37723f(0x40b)][_0x37723f(0x3a9)] - this[_0x37723f(0x1fc)] * 0x1),
                        (_0x34a743[_0x37723f(0x3a9)] = this[_0x37723f(0x1fc)]));
                    break;
            }
            return _0x34a743;
        }),
        (Window_Doodads_Settings_Switch['prototype'][_0x9a8c74(0x294)] = function () {
            const _0xd5c1de = _0x9a8c74;
            for (
                let _0x56ef01 = 0x1;
                _0x56ef01 < $dataSystem[_0xd5c1de(0x3f6)][_0xd5c1de(0x352)];
                _0x56ef01++
            ) {
                const _0x11cf5b = $dataSystem[_0xd5c1de(0x3f6)][_0x56ef01];
                if (!_0x11cf5b) continue;
                const _0x1eed95 = _0x56ef01[_0xd5c1de(0x311)](0x4) + ':\x20' + _0x11cf5b;
                (this[_0xd5c1de(0x133)](_0x1eed95, _0xd5c1de(0x34b)),
                    this[_0xd5c1de(0x133)](_0xd5c1de(0x2b5), _0xd5c1de(0x114), !![], _0x56ef01),
                    this[_0xd5c1de(0x133)]('ON', _0xd5c1de(0x379), !![], _0x56ef01),
                    this[_0xd5c1de(0x133)](_0xd5c1de(0x2ef), _0xd5c1de(0x20c), !![], _0x56ef01));
            }
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x189593) {
            const _0x5e42c7 = _0x9a8c74,
                _0x497ea7 = this['commandSymbol'](_0x189593);
            if (_0x497ea7 === 'switchName')
                return Window_Doodads_Command[_0x5e42c7(0x11a)][_0x5e42c7(0x1b9)]['call'](
                    this,
                    _0x189593
                );
            const _0x23852c = this['_list'][_0x189593][_0x5e42c7(0x164)],
                _0x21221a = this[_0x5e42c7(0x34f)](_0x189593),
                _0x140c31 = _0x5e42c7(0x292);
            let _0xbab240 = ![];
            this[_0x5e42c7(0x1e0)]();
            const _0x4b1a25 = SceneManager[_0x5e42c7(0x3cd)][_0x5e42c7(0x36f)][_0x5e42c7(0x243)];
            if (!_0x4b1a25) return;
            switch (_0x497ea7) {
                case _0x5e42c7(0x114):
                    _0xbab240 =
                        !_0x4b1a25['switchOn']?.['includes'](_0x23852c) &&
                        !_0x4b1a25[_0x5e42c7(0x20c)]?.[_0x5e42c7(0x39c)](_0x23852c);
                    break;
                case 'switchOn':
                    _0xbab240 = _0x4b1a25[_0x5e42c7(0x379)]?.['includes'](_0x23852c);
                    break;
                case _0x5e42c7(0x20c):
                    _0xbab240 = _0x4b1a25[_0x5e42c7(0x20c)]?.['includes'](_0x23852c);
                    break;
            }
            (this['changePaintOpacity'](_0xbab240),
                this['drawText'](
                    this[_0x5e42c7(0x230)](_0x189593),
                    _0x21221a['x'],
                    _0x21221a['y'],
                    _0x21221a[_0x5e42c7(0x3a9)],
                    _0x140c31
                ));
        }),
        (Window_Doodads_Settings_Switch[_0x9a8c74(0x11a)]['itemTextAlign'] = function () {
            const _0x344872 = _0x9a8c74;
            return _0x344872(0x2ea);
        }));
    function Window_Doodads_Settings_Opacity(..._0x3c7038) {
        const _0x1f6b5d = _0x9a8c74;
        this[_0x1f6b5d(0x35b)](..._0x3c7038);
    }
    ((Window_Doodads_Settings_Opacity[_0x9a8c74(0x11a)] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Opacity['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Opacity),
        (Window_Doodads_Settings_Opacity[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Opacity[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x120cdd = _0x9a8c74;
            (this['addCommand'](
                _0x120cdd(0x84),
                _0x120cdd(0x7d),
                !![],
                Math[_0x120cdd(0x223)](0xff * 0x1)
            ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x1e8),
                    _0x120cdd(0x7d),
                    !![],
                    Math['floor'](0xff * 0.9)
                ),
                this['addCommand'](
                    _0x120cdd(0x103),
                    _0x120cdd(0x7d),
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.8)
                ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x2c4),
                    _0x120cdd(0x7d),
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.7)
                ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x132),
                    _0x120cdd(0x7d),
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.6)
                ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0xe7),
                    _0x120cdd(0x7d),
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.5)
                ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x392),
                    _0x120cdd(0x7d),
                    !![],
                    Math['floor'](0xff * 0.4)
                ),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x3db),
                    _0x120cdd(0x7d),
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.3)
                ),
                this['addCommand']('20%', 'opacity', !![], Math[_0x120cdd(0x223)](0xff * 0.2)),
                this[_0x120cdd(0x133)](
                    _0x120cdd(0x3d6),
                    'opacity',
                    !![],
                    Math[_0x120cdd(0x223)](0xff * 0.1)
                ),
                this['addCommand']('0%', _0x120cdd(0x7d), !![], Math['floor'](0xff * 0x0)));
        }),
        (Window_Doodads_Settings_Opacity[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (
            _0x4f093c
        ) {
            const _0x53c998 = _0x9a8c74;
            Window_Doodads_Command[_0x53c998(0x11a)][_0x53c998(0x1b9)][_0x53c998(0x355)](
                this,
                _0x4f093c
            );
            const _0x21210c = this[_0x53c998(0x34f)](_0x4f093c),
                _0x253607 = this[_0x53c998(0x270)][_0x4f093c][_0x53c998(0x164)];
            this[_0x53c998(0x2f8)](
                _0x253607,
                _0x21210c['x'],
                _0x21210c['y'],
                _0x21210c[_0x53c998(0x3a9)],
                _0x53c998(0x28a)
            );
        }));
    function Window_Doodads_Settings_Scale(..._0x2a59f2) {
        const _0x2ba824 = _0x9a8c74;
        this[_0x2ba824(0x35b)](..._0x2a59f2);
    }
    ((Window_Doodads_Settings_Scale['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Scale['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Scale),
        (Window_Doodads_Settings_Scale[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Scale['prototype'][_0x9a8c74(0x231)] = function () {
            const _0x27055b = _0x9a8c74,
                _0x2f6fb3 = this[_0x27055b(0x207)](0x13);
            return Math['min'](Graphics['height'], _0x2f6fb3);
        }),
        (Window_Doodads_Settings_Scale[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x537ace = _0x9a8c74;
            (this[_0x537ace(0x133)]('1000%', _0x537ace(0xd1), !![], 0x3e8),
                this[_0x537ace(0x133)](_0x537ace(0x306), _0x537ace(0xd1), !![], 0x320),
                this[_0x537ace(0x133)]('600%', 'scale', !![], 0x258),
                this['addCommand']('400%', _0x537ace(0xd1), !![], 0x190),
                this['addCommand'](_0x537ace(0x31e), _0x537ace(0xd1), !![], 0xc8),
                this['addCommand']('100%', _0x537ace(0xd1), !![], 0x64),
                this[_0x537ace(0x133)](_0x537ace(0xf4), 'scale', !![], 0x4b),
                this[_0x537ace(0x133)]('50%', _0x537ace(0xd1), !![], 0x32),
                this[_0x537ace(0x133)](_0x537ace(0xdd), _0x537ace(0xd1), !![], 0x19),
                this[_0x537ace(0x133)]('0%', 'scale', !![], 0x0),
                this['addCommand'](_0x537ace(0x94), _0x537ace(0xd1), !![], -0x19),
                this[_0x537ace(0x133)](_0x537ace(0x113), 'scale', !![], -0x32),
                this[_0x537ace(0x133)](_0x537ace(0x3f4), _0x537ace(0xd1), !![], -0x4b),
                this[_0x537ace(0x133)](_0x537ace(0x409), _0x537ace(0xd1), !![], -0x64),
                this[_0x537ace(0x133)]('-200%', 'scale', !![], -0xc8),
                this[_0x537ace(0x133)](_0x537ace(0x213), _0x537ace(0xd1), !![], -0x190),
                this[_0x537ace(0x133)]('-600%', 'scale', !![], -0x258),
                this[_0x537ace(0x133)]('-800%', _0x537ace(0xd1), !![], -0x320),
                this[_0x537ace(0x133)](_0x537ace(0x1f9), _0x537ace(0xd1), !![], -0x3e8));
        }),
        (Window_Doodads_Settings_Scale[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x11ffeb = _0x9a8c74;
            return _0x11ffeb(0x292);
        }));
    function Window_Doodads_Settings_Position_Type(..._0x41b9e1) {
        const _0x4e7e92 = _0x9a8c74;
        this[_0x4e7e92(0x35b)](..._0x41b9e1);
    }
    ((Window_Doodads_Settings_Position_Type[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_Settings_Position_Type['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Position_Type),
        (Window_Doodads_Settings_Position_Type['prototype'][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Position_Type[_0x9a8c74(0x11a)]['makeCommandList'] = function () {
            const _0x12c280 = _0x9a8c74;
            (this[_0x12c280(0x133)](_0x12c280(0x151), _0x12c280(0x111), !![], 'map'),
                this[_0x12c280(0x133)](_0x12c280(0x14d), _0x12c280(0x111), !![], _0x12c280(0x242)));
        }),
        (Window_Doodads_Settings_Position_Type[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x28a5b9 = _0x9a8c74;
            return _0x28a5b9(0x292);
        }));
    function Window_Doodads_Settings_Angle(..._0x595758) {
        this['initialize'](..._0x595758);
    }
    ((Window_Doodads_Settings_Angle[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Angle[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Angle),
        (Window_Doodads_Settings_Angle[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Angle[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x33acca = _0x9a8c74;
            for (let _0x36199d = 0x0; _0x36199d <= 0x13b; _0x36199d += 0x2d) {
                this['addCommand'](_0x36199d + '°', _0x33acca(0x1c9), !![], _0x36199d);
            }
        }),
        (Window_Doodads_Settings_Angle[_0x9a8c74(0x11a)]['itemTextAlign'] = function () {
            const _0x48d886 = _0x9a8c74;
            return _0x48d886(0x292);
        }));
    function Window_Doodads_Settings_Icon_Index(..._0x521b3a) {
        this['initialize'](..._0x521b3a);
    }
    ((Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Icon_Index['prototype']['constructor'] =
            Window_Doodads_Settings_Icon_Index),
        (Window_Doodads_Settings_Icon_Index['prototype']['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x126)] = function () {
            return this['maxCols']() * $gameMap['tileWidth']();
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)]['windowWidth'] = function () {
            const _0x4da9b6 = this['contentsWidth']();
            return _0x4da9b6 + 0x18;
        }),
        (Window_Doodads_Settings_Icon_Index['prototype'][_0x9a8c74(0xf5)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x3c99dc = _0x9a8c74;
            return Graphics[_0x3c99dc(0x158)];
        }),
        (Window_Doodads_Settings_Icon_Index['prototype'][_0x9a8c74(0x32d)] = function () {
            return 0x10;
        }),
        (Window_Doodads_Settings_Icon_Index['prototype'][_0x9a8c74(0x38d)] = function () {
            return ImageManager['iconHeight'] + 0x4;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x156)] = function () {
            const _0x4d1827 = _0x9a8c74;
            return this[_0x4d1827(0x38d)]();
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x2e7)] = function () {
            const _0x5f5cc9 = _0x9a8c74;
            return ImageManager[_0x5f5cc9(0x40c)] + 0x4;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)]['colSpacing'] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x7c)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x21f478 = _0x9a8c74,
                _0x1ff345 = ImageManager[_0x21f478(0x1c7)](_0x21f478(0x3ce)),
                _0x3d008e = Math[_0x21f478(0x223)](
                    _0x1ff345[_0x21f478(0x158)] / ImageManager[_0x21f478(0x38b)]
                ),
                _0x221dce = _0x3d008e * this['maxCols']();
            for (let _0x53925b = 0x0; _0x53925b < _0x221dce; _0x53925b++) {
                this[_0x21f478(0x133)]('', 'iconIndex', _0x53925b > 0x0, _0x53925b);
            }
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (
            _0x1b4d34
        ) {
            const _0xfb8abb = _0x9a8c74,
                _0x5cec96 = this['itemRect'](_0x1b4d34),
                _0x2bbb45 = this[_0xfb8abb(0x270)][_0x1b4d34][_0xfb8abb(0x164)];
            (this[_0xfb8abb(0x1a6)](!![]),
                this[_0xfb8abb(0x349)](_0x2bbb45, _0x5cec96['x'] + 0x2, _0x5cec96['y'] + 0x2));
        }),
        (Window_Doodads_Settings_Icon_Index[_0x9a8c74(0x11a)][_0x9a8c74(0x349)] = function (
            _0x5d78e0,
            _0x2a06a5,
            _0x264e7b
        ) {
            const _0x45f29e = _0x9a8c74,
                _0x52e8b3 = ImageManager[_0x45f29e(0x1c7)](_0x45f29e(0x3ce)),
                _0x2a1072 = ImageManager[_0x45f29e(0x40c)],
                _0x54632b = ImageManager[_0x45f29e(0x38b)],
                _0x4eea48 = (_0x5d78e0 % 0x10) * _0x2a1072,
                _0x5b669c = Math[_0x45f29e(0x223)](_0x5d78e0 / 0x10) * _0x54632b;
            this[_0x45f29e(0x3d3)]['blt'](
                _0x52e8b3,
                _0x4eea48,
                _0x5b669c,
                _0x2a1072,
                _0x54632b,
                _0x2a06a5,
                _0x264e7b
            );
        }));
    function Window_Doodads_Settings_Tile_ID(..._0x25028b) {
        const _0x3b6427 = _0x9a8c74;
        this[_0x3b6427(0x35b)](..._0x25028b);
    }
    ((Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tile_ID),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)]['contentsWidth'] = function () {
            const _0x296516 = _0x9a8c74;
            return this[_0x296516(0x32d)]() * $gameMap['tileWidth']();
        }),
        (Window_Doodads_Settings_Tile_ID['prototype'][_0x9a8c74(0x15e)] = function () {
            const _0x787346 = _0x9a8c74,
                _0x203722 = this[_0x787346(0x126)]();
            return _0x203722 + 0x18;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0xf5)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x5e0083 = _0x9a8c74;
            return Graphics[_0x5e0083(0x158)];
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)]['maxCols'] = function () {
            return 0x10;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x38d)] = function () {
            const _0xe6555 = _0x9a8c74;
            return $gameMap[_0xe6555(0x287)]();
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x156)] = function () {
            return this['lineHeight']();
        }),
        (Window_Doodads_Settings_Tile_ID['prototype'][_0x9a8c74(0x2e7)] = function () {
            const _0x184dd5 = _0x9a8c74;
            return $gameMap[_0x184dd5(0x3b6)]();
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x199)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Tile_ID['prototype'][_0x9a8c74(0x7c)] = function () {
            return 0x0;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x222)] = function (
            _0x141bb8
        ) {
            const _0x30c853 = _0x9a8c74;
            for (const _0x45a1ff of _0x141bb8) {
                this[_0x30c853(0x133)]('', _0x30c853(0x9b), _0x45a1ff > 0x0, _0x45a1ff);
            }
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0xf745d6 = _0x9a8c74,
                _0x4a6d75 = DoodadManager[_0xf745d6(0x8f)]();
            if (!_0x4a6d75?.[_0xf745d6(0x9b)]) return;
            const _0x504b30 = $gameMap['tileset']();
            (_0x504b30[_0xf745d6(0x3d8)][0x5] && this[_0xf745d6(0x222)](Doodads[_0xf745d6(0xd9)]),
                _0x504b30['tilesetNames'][0x6] && this['addTileRange'](Doodads[_0xf745d6(0x3b3)]),
                _0x504b30[_0xf745d6(0x3d8)][0x7] && this[_0xf745d6(0x222)](Doodads['dTiles']),
                _0x504b30[_0xf745d6(0x3d8)][0x8] && this['addTileRange'](Doodads['eTiles']));
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)][_0x9a8c74(0x9b)] = function () {
            const _0x198b22 = _0x9a8c74;
            return this[_0x198b22(0x270)][this['index']()]?.[_0x198b22(0x164)] ?? 0x0;
        }),
        (Window_Doodads_Settings_Tile_ID[_0x9a8c74(0x11a)]['drawItem'] = function (_0x69be86) {
            const _0x4ab8d8 = _0x9a8c74,
                _0x1d6094 = this['itemRect'](_0x69be86),
                _0x17230f = this[_0x4ab8d8(0x270)][_0x69be86][_0x4ab8d8(0x164)];
            this[_0x4ab8d8(0x1a6)](!![]);
            const _0x2c9b27 = this[_0x4ab8d8(0x3c1)](_0x17230f, _0x1d6094['x'], _0x1d6094['y']);
            _0x2c9b27 && !_0x2c9b27[_0x4ab8d8(0x304)]() && (this[_0x4ab8d8(0x309)] = _0x2c9b27);
        }),
        (Window_Doodads_Settings_Tile_ID['prototype'][_0x9a8c74(0x33f)] = function () {
            const _0xc70676 = _0x9a8c74;
            (Window_Doodads_Command['prototype']['update'][_0xc70676(0x355)](this),
                this[_0xc70676(0x309)] &&
                    this[_0xc70676(0x309)]['isReady']() &&
                    ((this[_0xc70676(0x309)] = ![]), this[_0xc70676(0xa8)]()));
        }));
    function Window_Doodads_Settings_Tile_Cols(..._0x32ca57) {
        this['initialize'](..._0x32ca57);
    }
    ((Window_Doodads_Settings_Tile_Cols['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tile_Cols['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tile_Cols),
        (Window_Doodads_Settings_Tile_Cols[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tile_Cols[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x4d47fa = _0x9a8c74;
            for (let _0x28aac5 = 0x1; _0x28aac5 <= 0x10; _0x28aac5++) {
                this[_0x4d47fa(0x133)](String(_0x28aac5), _0x4d47fa(0x396), !![], _0x28aac5);
            }
        }),
        (Window_Doodads_Settings_Tile_Cols[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            return 'center';
        }));
    function Window_Doodads_Settings_Tile_Rows(..._0x33bb25) {
        const _0x33dbe0 = _0x9a8c74;
        this[_0x33dbe0(0x35b)](..._0x33bb25);
    }
    ((Window_Doodads_Settings_Tile_Rows[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Tile_Rows[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Tile_Rows),
        (Window_Doodads_Settings_Tile_Rows[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Tile_Rows[_0x9a8c74(0x11a)]['makeCommandList'] = function () {
            const _0x578e5f = _0x9a8c74;
            for (let _0x5cd592 = 0x1; _0x5cd592 <= 0x10; _0x5cd592++) {
                this[_0x578e5f(0x133)](String(_0x5cd592), _0x578e5f(0x195), !![], _0x5cd592);
            }
        }),
        (Window_Doodads_Settings_Tile_Rows[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x35b9c1 = _0x9a8c74;
            return _0x35b9c1(0x292);
        }));
    function Window_Doodads_Settings_Anchor_X(..._0x160d3f) {
        const _0x2393f5 = _0x9a8c74;
        this[_0x2393f5(0x35b)](..._0x160d3f);
    }
    ((Window_Doodads_Settings_Anchor_X['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_Settings_Anchor_X[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Anchor_X),
        (Window_Doodads_Settings_Anchor_X[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Anchor_X[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x4b55d4 = _0x9a8c74;
            (this[_0x4b55d4(0x133)](_0x4b55d4(0x271), _0x4b55d4(0x2c7), !![], 0x0),
                this[_0x4b55d4(0x133)](_0x4b55d4(0x348), _0x4b55d4(0x2c7), !![], 0.5),
                this['addCommand'](_0x4b55d4(0x17d), _0x4b55d4(0x2c7), !![], 0x1));
        }),
        (Window_Doodads_Settings_Anchor_X[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x361376 = _0x9a8c74;
            return _0x361376(0x292);
        }));
    function Window_Doodads_Settings_Anchor_Y(..._0x2f963e) {
        const _0x424164 = _0x9a8c74;
        this[_0x424164(0x35b)](..._0x2f963e);
    }
    ((Window_Doodads_Settings_Anchor_Y[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Anchor_Y[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Anchor_Y),
        (Window_Doodads_Settings_Anchor_Y[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Anchor_Y[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x1aa5a5 = _0x9a8c74;
            (this['addCommand']('Head', _0x1aa5a5(0x31a), !![], 0x0),
                this[_0x1aa5a5(0x133)](_0x1aa5a5(0x37b), _0x1aa5a5(0x31a), !![], 0.5),
                this['addCommand'](_0x1aa5a5(0x259), _0x1aa5a5(0x31a), !![], 0x1));
        }),
        (Window_Doodads_Settings_Anchor_Y[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x3c83e3 = _0x9a8c74;
            return _0x3c83e3(0x292);
        }));
    function Window_Doodads_Settings_Frame_Speed(..._0x31c42c) {
        const _0x4d1fc0 = _0x9a8c74;
        this[_0x4d1fc0(0x35b)](..._0x31c42c);
    }
    ((Window_Doodads_Settings_Frame_Speed[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Frame_Speed[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Frame_Speed),
        (Window_Doodads_Settings_Frame_Speed[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Frame_Speed[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x145cb2 = _0x9a8c74;
            (this[_0x145cb2(0x133)](_0x145cb2(0x3af), _0x145cb2(0x1a4), !![], 0x1),
                this[_0x145cb2(0x133)](_0x145cb2(0x1a5), _0x145cb2(0x1a4), !![], 0x5),
                this[_0x145cb2(0x133)](_0x145cb2(0x276), 'frameUpdate', !![], 0xa),
                this[_0x145cb2(0x133)]('15:Quick', 'frameUpdate', !![], 0xf),
                this[_0x145cb2(0x133)](_0x145cb2(0x269), _0x145cb2(0x1a4), !![], 0x14),
                this['addCommand'](_0x145cb2(0xca), _0x145cb2(0x1a4), !![], 0x19),
                this[_0x145cb2(0x133)](_0x145cb2(0x302), _0x145cb2(0x1a4), !![], 0x1e),
                this[_0x145cb2(0x133)](_0x145cb2(0xd2), _0x145cb2(0x1a4), !![], 0x23),
                this[_0x145cb2(0x133)](_0x145cb2(0x2d6), _0x145cb2(0x1a4), !![], 0x28),
                this[_0x145cb2(0x133)](_0x145cb2(0xae), _0x145cb2(0x1a4), !![], 0x2d),
                this['addCommand'](_0x145cb2(0x161), _0x145cb2(0x1a4), !![], 0x32),
                this[_0x145cb2(0x133)](_0x145cb2(0xc8), _0x145cb2(0x1a4), !![], 0x37),
                this[_0x145cb2(0x133)]('60:Full\x20Snail', _0x145cb2(0x1a4), !![], 0x3c));
        }));
    function Window_Doodads_Settings_Blend(..._0x24afda) {
        this['initialize'](..._0x24afda);
    }
    ((Window_Doodads_Settings_Blend['prototype'] = Object['create'](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Blend[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Blend),
        (Window_Doodads_Settings_Blend[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Blend[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x3003fd = _0x9a8c74,
                _0x2167b6 = DoodadManager[_0x3003fd(0x30e)]();
            for (let _0x24d89a = 0x0; _0x24d89a < _0x2167b6[_0x3003fd(0x352)]; _0x24d89a++) {
                const _0x3d8b02 = _0x2167b6[_0x24d89a];
                _0x3d8b02[_0x3003fd(0x14e)] &&
                    this[_0x3003fd(0x133)](
                        _0x3d8b02[_0x3003fd(0x27f)],
                        _0x3003fd(0x1d4),
                        !![],
                        _0x24d89a
                    );
            }
        }),
        (Window_Doodads_Settings_Blend[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x2d5ead = _0x9a8c74;
            return _0x2d5ead(0x292);
        }));
    function Window_Doodads_Settings_Smooth(..._0x5a4b83) {
        const _0x3519bd = _0x9a8c74;
        this[_0x3519bd(0x35b)](..._0x5a4b83);
    }
    ((Window_Doodads_Settings_Smooth[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Smooth['prototype']['constructor'] =
            Window_Doodads_Settings_Smooth),
        (Window_Doodads_Settings_Smooth[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Smooth[_0x9a8c74(0x11a)]['makeCommandList'] = function () {
            const _0x4bdc56 = _0x9a8c74;
            (this[_0x4bdc56(0x133)](_0x4bdc56(0x173), _0x4bdc56(0xe9), !![], !![]),
                this[_0x4bdc56(0x133)](_0x4bdc56(0x29e), 'smooth', !![], ![]));
        }),
        (Window_Doodads_Settings_Smooth[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            return 'center';
        }));
    function Window_Doodads_Settings_Contrast(..._0x2e0002) {
        const _0x396abb = _0x9a8c74;
        this[_0x396abb(0x35b)](..._0x2e0002);
    }
    ((Window_Doodads_Settings_Contrast['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_Settings_Contrast[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Contrast),
        (Window_Doodads_Settings_Contrast[_0x9a8c74(0x11a)]['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Contrast['prototype']['makeCommandList'] = function () {
            const _0x4621db = _0x9a8c74;
            (this[_0x4621db(0x133)](_0x4621db(0x200), 'contrast', !![], !![]),
                this[_0x4621db(0x133)](_0x4621db(0x35a), _0x4621db(0x147), !![], ![]));
        }),
        (Window_Doodads_Settings_Contrast[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x869bb6 = _0x9a8c74;
            return _0x869bb6(0x292);
        }));
    function Window_Doodads_Settings_Sepia(..._0x47b7f8) {
        const _0x5a19c6 = _0x9a8c74;
        this[_0x5a19c6(0x35b)](..._0x47b7f8);
    }
    ((Window_Doodads_Settings_Sepia[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Sepia['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Sepia),
        (Window_Doodads_Settings_Sepia[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Sepia[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x1de01c = _0x9a8c74;
            (this[_0x1de01c(0x133)](_0x1de01c(0x89), 'sepia', !![], !![]),
                this['addCommand'](_0x1de01c(0x19b), 'sepia', !![], ![]));
        }),
        (Window_Doodads_Settings_Sepia[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x3c93de = _0x9a8c74;
            return _0x3c93de(0x292);
        }));
    function Window_Doodads_Settings_Blur(..._0x51c6f9) {
        const _0x1c0b64 = _0x9a8c74;
        this[_0x1c0b64(0x35b)](..._0x51c6f9);
    }
    ((Window_Doodads_Settings_Blur[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Blur[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Blur),
        (Window_Doodads_Settings_Blur['prototype'][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Blur[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x51d59f = _0x9a8c74;
            (this[_0x51d59f(0x133)]('Blur', _0x51d59f(0x1e3), !![], !![]),
                this[_0x51d59f(0x133)](_0x51d59f(0x12b), 'blur', !![], ![]));
        }),
        (Window_Doodads_Settings_Blur[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0xb4cc7 = _0x9a8c74;
            return _0xb4cc7(0x292);
        }));
    function Window_Doodads_Settings_Outline(..._0x2057df) {
        const _0x207f1a = _0x9a8c74;
        this[_0x207f1a(0x35b)](..._0x2057df);
    }
    ((Window_Doodads_Settings_Outline[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Outline[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Outline),
        (Window_Doodads_Settings_Outline['prototype'][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Outline[_0x9a8c74(0x11a)]['makeCommandList'] = function () {
            const _0x409b0f = _0x9a8c74;
            (this['addCommand']('ON', 'outline', !![], !![]),
                this[_0x409b0f(0x133)]('OFF', _0x409b0f(0x408), !![], ![]));
        }),
        (Window_Doodads_Settings_Outline['prototype']['itemTextAlign'] = function () {
            return 'center';
        }));
    function Window_Doodads_Settings_Shadow(..._0x277235) {
        this['initialize'](..._0x277235);
    }
    ((Window_Doodads_Settings_Shadow[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Shadow[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Shadow),
        (Window_Doodads_Settings_Shadow['prototype']['windowX'] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Shadow[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x428748 = _0x9a8c74;
            (this[_0x428748(0x133)]('ON', _0x428748(0x252), !![], !![]),
                this[_0x428748(0x133)](_0x428748(0x2ef), _0x428748(0x252), !![], ![]));
        }),
        (Window_Doodads_Settings_Shadow['prototype'][_0x9a8c74(0x205)] = function () {
            return 'center';
        }));
    function Window_Doodads_Settings_Glow(..._0x3f2676) {
        this['initialize'](..._0x3f2676);
    }
    ((Window_Doodads_Settings_Glow[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Settings_Glow[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_Settings_Glow),
        (Window_Doodads_Settings_Glow[_0x9a8c74(0x11a)][_0x9a8c74(0x362)] = function () {
            return 0x190;
        }),
        (Window_Doodads_Settings_Glow['prototype']['makeCommandList'] = function () {
            const _0x26f642 = _0x9a8c74;
            (this[_0x26f642(0x133)]('ON', _0x26f642(0x185), !![], !![]),
                this['addCommand'](_0x26f642(0x2ef), _0x26f642(0x185), !![], ![]));
        }),
        (Window_Doodads_Settings_Glow[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            const _0x22cce0 = _0x9a8c74;
            return _0x22cce0(0x292);
        }));
    function Window_Doodads_Grid_Menu(..._0x505910) {
        this['initialize'](..._0x505910);
    }
    ((Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Grid_Menu),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)]['windowX'] = function () {
            const _0x16b360 = _0x9a8c74;
            return (Graphics[_0x16b360(0x3a9)] - this[_0x16b360(0x15e)]()) / 0x2;
        }),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)]['windowY'] = function () {
            const _0x32a0d3 = _0x9a8c74;
            return (Graphics['height'] - this[_0x32a0d3(0x207)](0x4)) / 0x2;
        }),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)]['windowHeight'] = function () {
            const _0x3c9436 = _0x9a8c74;
            return this[_0x3c9436(0x207)](0x4);
        }),
        (Window_Doodads_Grid_Menu['prototype'][_0x9a8c74(0x294)] = function () {
            const _0x3985a0 = _0x9a8c74,
                _0x103c53 = DoodadManager[_0x3985a0(0x237)];
            (_0x103c53
                ? this['addCommand'](_0x3985a0(0x1e7), _0x3985a0(0x2df))
                : this[_0x3985a0(0x133)](_0x3985a0(0x2e4), _0x3985a0(0x2df)),
                this[_0x3985a0(0x133)]('', _0x3985a0(0xb3), ![]),
                this[_0x3985a0(0x133)](
                    _0x3985a0(0x1e4),
                    _0x3985a0(0x13e),
                    _0x103c53,
                    DoodadManager[_0x3985a0(0x11c)]()
                ),
                this[_0x3985a0(0x133)](
                    _0x3985a0(0x241),
                    _0x3985a0(0x196),
                    _0x103c53,
                    DoodadManager['gridLockY']()
                ));
        }),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x25d4e9) {
            const _0x2ca904 = _0x9a8c74;
            Window_Doodads_Command[_0x2ca904(0x11a)][_0x2ca904(0x1b9)][_0x2ca904(0x355)](
                this,
                _0x25d4e9
            );
            const _0x3e0140 = this[_0x2ca904(0x285)](_0x25d4e9);
            if (_0x3e0140 === _0x2ca904(0x13e) || _0x3e0140 === _0x2ca904(0x196)) {
                const _0x26a9cc = this[_0x2ca904(0x34f)](_0x25d4e9),
                    _0x272aed = this['_list'][_0x25d4e9]['ext'];
                this[_0x2ca904(0x2f8)](
                    _0x272aed,
                    _0x26a9cc['x'],
                    _0x26a9cc['y'],
                    _0x26a9cc[_0x2ca904(0x3a9)],
                    _0x2ca904(0x28a)
                );
            }
        }),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)] = function () {
            const _0x457b73 = _0x9a8c74;
            Window_Doodads_Command[_0x457b73(0x11a)][_0x457b73(0x33f)][_0x457b73(0x355)](this);
            if (!this[_0x457b73(0xcc)]) return;
            this['updateLeftRight']();
        }),
        (Window_Doodads_Grid_Menu[_0x9a8c74(0x11a)]['updateLeftRight'] = function () {
            const _0x4b1607 = _0x9a8c74;
            let _0xa48833;
            if (Input[_0x4b1607(0x1fd)](_0x4b1607(0x2ea))) _0xa48833 = -0x1;
            else {
                if (Input['isRepeated'](_0x4b1607(0x28a))) _0xa48833 = 0x1;
                else return;
            }
            const _0x52164f = this['commandSymbol'](this['index']());
            let _0x55bde4 = ![];
            if (_0x52164f === 'gridX')
                ((DoodadManager[_0x4b1607(0x2a4)] = Math[_0x4b1607(0x2f7)](
                    0x1,
                    DoodadManager[_0x4b1607(0x2a4)] + _0xa48833
                )),
                    (_0x55bde4 = !![]));
            else
                _0x52164f === 'gridY' &&
                    ((DoodadManager[_0x4b1607(0x3c5)] = Math[_0x4b1607(0x2f7)](
                        0x1,
                        DoodadManager['_gridLockY'] + _0xa48833
                    )),
                    (_0x55bde4 = !![]));
            _0x55bde4 &&
                (SoundManager[_0x4b1607(0x314)](),
                this[_0x4b1607(0xa8)](),
                DoodadManager[_0x4b1607(0x237)] &&
                    SceneManager['_scene']['_gfdDrawGridWindow'][_0x4b1607(0xa8)]());
        }));
    function Window_Doodads_PickDoodadLayer(..._0x2dd373) {
        const _0x2e6286 = _0x9a8c74;
        this[_0x2e6286(0x35b)](..._0x2dd373);
    }
    ((Window_Doodads_PickDoodadLayer['prototype'] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_PickDoodadLayer['prototype'][_0x9a8c74(0x186)] =
            Window_Doodads_PickDoodadLayer),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x59bc3c = _0x9a8c74;
            ((this[_0x59bc3c(0x1e5)] = $gameMap[_0x59bc3c(0x22c)]() || []),
                Window_Doodads_Command[_0x59bc3c(0x11a)]['initialize'][_0x59bc3c(0x355)](this));
        }),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return 0x190;
        }),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x19e6a3 = _0x9a8c74;
            return Graphics[_0x19e6a3(0x158)];
        }),
        (Window_Doodads_PickDoodadLayer['prototype'][_0x9a8c74(0x294)] = function () {
            const _0x492b8f = _0x9a8c74;
            (this[_0x492b8f(0x359)](),
                this['addCommand'](_0x492b8f(0x262), _0x492b8f(0x2bc), !![]),
                this['addCommand']('', _0x492b8f(0xb3), ![]),
                this[_0x492b8f(0x133)]('All\x20Doodads', _0x492b8f(0x346), !![], 0x64),
                this['addCommand']('', _0x492b8f(0xb3), ![]),
                this['addCommand'](
                    _0x492b8f(0x1f4),
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0x8],
                    0x8
                ),
                this[_0x492b8f(0x133)](_0x492b8f(0x149), 'edit', this[_0x492b8f(0x167)][0x7], 0x7),
                this[_0x492b8f(0x133)](
                    _0x492b8f(0x264),
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0x6],
                    0x6
                ),
                this['addCommand'](
                    '\x207.\x20Above\x20Char',
                    _0x492b8f(0x346),
                    this['_doodadsTotal'][0x5],
                    0x5
                ),
                this[_0x492b8f(0x133)](
                    _0x492b8f(0x36a),
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0x4],
                    0x4
                ),
                this['addCommand'](
                    _0x492b8f(0x308),
                    _0x492b8f(0x346),
                    this['_doodadsTotal'][0x3],
                    0x3
                ),
                this[_0x492b8f(0x133)](
                    _0x492b8f(0x336),
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0x2],
                    0x2
                ),
                this['addCommand'](
                    '\x203.\x20Below\x20Char',
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0x1],
                    0x1
                ),
                this['addCommand'](
                    _0x492b8f(0x337),
                    _0x492b8f(0x346),
                    this[_0x492b8f(0x167)][0.2],
                    0.2
                ),
                this[_0x492b8f(0x133)](
                    '\x201.\x20Lower\x20Layer',
                    _0x492b8f(0x346),
                    this['_doodadsTotal'][0.1],
                    0.1
                ),
                this[_0x492b8f(0x133)](
                    _0x492b8f(0x29d),
                    _0x492b8f(0x346),
                    this['_doodadsTotal'][0x0],
                    0x0
                ));
        }),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)]['itemTextAlign'] = function () {
            const _0x38f753 = _0x9a8c74;
            return _0x38f753(0x2ea);
        }),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)][_0x9a8c74(0x359)] = function () {
            const _0x509960 = _0x9a8c74;
            ((this[_0x509960(0x1e5)] = $gameMap[_0x509960(0x22c)]() || []),
                (this[_0x509960(0x167)] = {}));
            for (let _0x1a710d = 0x0; _0x1a710d < 0xb; ++_0x1a710d) {
                let _0xaddff2 = [0x8, 0x7, 0x6, 0x5, 0x4, 0x3, 0x2, 0x1, 0.2, 0.1, 0x0][_0x1a710d];
                const _0x4d140c = this[_0x509960(0x1e5)][_0x509960(0x352)];
                let _0xfc27a7 = 0x0;
                for (let _0x2c147d = 0x0; _0x2c147d < _0x4d140c; _0x2c147d++) {
                    const _0x470859 = this[_0x509960(0x1e5)][_0x2c147d];
                    if (_0x470859 && _0x470859['z'] === _0xaddff2) _0xfc27a7 += 0x1;
                }
                this[_0x509960(0x167)][_0xaddff2] = _0xfc27a7;
            }
        }),
        (Window_Doodads_PickDoodadLayer[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x24dd31) {
            const _0x1f04ef = _0x9a8c74;
            Window_Doodads_Command['prototype'][_0x1f04ef(0x1b9)]['call'](this, _0x24dd31);
            if (this['commandSymbol'](_0x24dd31) !== _0x1f04ef(0x346)) return;
            const _0x402613 = this[_0x1f04ef(0x270)][_0x24dd31][_0x1f04ef(0x164)];
            let _0x4a42f3;
            _0x402613 >= 0x64
                ? (_0x4a42f3 = this[_0x1f04ef(0x1e5)][_0x1f04ef(0x352)])
                : (_0x4a42f3 = this[_0x1f04ef(0x167)][_0x402613]);
            const _0x5b6b7c = this['itemRectWithPadding'](_0x24dd31),
                _0x530c84 = 'x' + _0x4a42f3;
            this['drawText'](
                _0x530c84,
                _0x5b6b7c['x'],
                _0x5b6b7c['y'],
                _0x5b6b7c[_0x1f04ef(0x3a9)],
                _0x1f04ef(0x28a)
            );
        }));
    function Window_Doodads_PickDoodadList(..._0x472d51) {
        const _0x28e5a3 = _0x9a8c74;
        this[_0x28e5a3(0x35b)](..._0x472d51);
    }
    ((Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_PickDoodadList),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x537fe1 = _0x9a8c74;
            ((this[_0x537fe1(0x1e5)] = $gameMap[_0x537fe1(0x22c)]() || []),
                (this[_0x537fe1(0x3c7)] = -0x1),
                Window_Doodads_Command['prototype'][_0x537fe1(0x35b)][_0x537fe1(0x355)](this));
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return 0x190;
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            const _0x2908b4 = _0x9a8c74;
            return Graphics[_0x2908b4(0x158)];
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x300)] = function (_0x3ff569) {
            const _0x5d1e3d = _0x9a8c74;
            ((this['_layer'] = _0x3ff569), this[_0x5d1e3d(0xa8)](), this['smoothSelect'](0x0));
        }),
        (Window_Doodads_PickDoodadList['prototype'][_0x9a8c74(0x294)] = function () {
            const _0x4f1b51 = _0x9a8c74;
            ((this[_0x4f1b51(0x1e5)] = $gameMap[_0x4f1b51(0x22c)]() || []),
                this[_0x4f1b51(0x133)](_0x4f1b51(0x36c), _0x4f1b51(0x2bc)));
            const _0x371039 = this['_doodads'][_0x4f1b51(0x352)];
            for (let _0x38be03 = 0x0; _0x38be03 < _0x371039; _0x38be03++) {
                const _0x26e9b6 = this['_doodads'][_0x38be03];
                if (!_0x26e9b6) continue;
                if (_0x26e9b6['z'] !== this[_0x4f1b51(0x3c7)] && this[_0x4f1b51(0x3c7)] !== 0x64)
                    continue;
                const _0x6721ef = _0x26e9b6['bitmap'][_0x4f1b51(0xbf)](/\[(\d+)x(\d+)\]/g, '');
                this[_0x4f1b51(0x133)](_0x6721ef, _0x4f1b51(0x1de), !![], _0x26e9b6);
            }
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x1b9)] = function (_0x46078a) {
            const _0x37df6f = _0x9a8c74;
            if (this[_0x37df6f(0x285)](_0x46078a) !== _0x37df6f(0x1de))
                return Window_Doodads_Command['prototype'][_0x37df6f(0x1b9)][_0x37df6f(0x355)](
                    this,
                    _0x46078a
                );
            const _0x5312be = this[_0x37df6f(0x270)][_0x46078a]['ext'],
                _0x1cbbe4 = this[_0x37df6f(0x1a3)](_0x46078a);
            if (_0x5312be[_0x37df6f(0x235)] > 0x0)
                (this[_0x37df6f(0x349)](
                    _0x5312be[_0x37df6f(0x235)],
                    _0x1cbbe4['x'] + 0x2,
                    _0x1cbbe4['y'] + 0x2
                ),
                    (_0x1cbbe4['x'] += ImageManager['iconWidth'] + 0x4),
                    (_0x1cbbe4[_0x37df6f(0x3a9)] -= ImageManager[_0x37df6f(0x40c)] + 0x4));
            else {
                if (_0x5312be[_0x37df6f(0x9b)] > 0x0) {
                    const _0x1f6047 = this['itemHeight'](),
                        _0xab316b =
                            (_0x1f6047 / $gameMap[_0x37df6f(0x287)]()) *
                            $gameMap[_0x37df6f(0x3b6)]();
                    (this[_0x37df6f(0x3c1)](
                        _0x5312be[_0x37df6f(0x9b)],
                        _0x1cbbe4['x'],
                        _0x1cbbe4['y'],
                        _0xab316b,
                        _0x1f6047
                    ),
                        (_0x1cbbe4['x'] += _0xab316b + 0x4),
                        (_0x1cbbe4[_0x37df6f(0x3a9)] -= _0xab316b + 0x4));
                } else
                    (this[_0x37df6f(0x1e9)](_0x46078a, _0x5312be),
                        (_0x1cbbe4['x'] += ImageManager['iconWidth'] + 0x4),
                        (_0x1cbbe4['width'] -= ImageManager[_0x37df6f(0x40c)] + 0x4));
            }
            this[_0x37df6f(0x2f8)](
                this[_0x37df6f(0x230)](_0x46078a),
                _0x1cbbe4['x'],
                _0x1cbbe4['y'],
                _0x1cbbe4[_0x37df6f(0x3a9)]
            );
        }),
        (Window_Doodads_PickDoodadList['prototype']['drawDoodadImage'] = function (
            _0x2b3f64,
            _0x32bec7
        ) {
            const _0x1541c4 = _0x9a8c74,
                _0x3959da = _0x32bec7[_0x1541c4(0xb8)],
                _0x51f941 = _0x32bec7['bitmap'],
                _0x42125b = _0x32bec7[_0x1541c4(0x20d)],
                _0x27c41b = ImageManager[_0x1541c4(0x16b)](_0x3959da + _0x51f941, _0x42125b, !![]);
            if (_0x27c41b[_0x1541c4(0x3a9)] <= 0x0)
                return setTimeout(
                    this[_0x1541c4(0x1e9)][_0x1541c4(0xee)](this, _0x2b3f64, _0x32bec7),
                    0x5
                );
            const _0x3422e3 = this['itemRect'](_0x2b3f64),
                _0x4cc5a6 = DoodadManager[_0x1541c4(0x373)](_0x51f941),
                _0xac5eb1 = DoodadManager[_0x1541c4(0x3dc)](_0x51f941),
                _0x3d9f9f = Math[_0x1541c4(0x223)](_0x27c41b['width'] / _0x4cc5a6),
                _0x203fd9 = Math['floor'](_0x27c41b[_0x1541c4(0x158)] / _0xac5eb1);
            let _0x3b6c73 = _0x3d9f9f,
                _0x284ced = _0x203fd9;
            if (_0x3b6c73 > ImageManager[_0x1541c4(0x40c)]) {
                const _0x599780 = ImageManager['iconWidth'] / _0x3b6c73;
                ((_0x3b6c73 *= _0x599780), (_0x284ced *= _0x599780));
            }
            if (_0x284ced > ImageManager[_0x1541c4(0x38b)]) {
                const _0x411e3a = ImageManager[_0x1541c4(0x38b)] / _0x284ced;
                ((_0x3b6c73 *= _0x411e3a), (_0x284ced *= _0x411e3a));
            }
            const _0xe14fdb = _0x3422e3['x'] + 0x2 + (ImageManager['iconWidth'] - _0x3b6c73) / 0x2,
                _0x3d32ea =
                    _0x3422e3['y'] + 0x2 + (ImageManager[_0x1541c4(0x38b)] - _0x284ced) / 0x2;
            this[_0x1541c4(0x40b)][_0x1541c4(0x289)](
                _0x27c41b,
                0x0,
                0x0,
                _0x3d9f9f,
                _0x203fd9,
                _0xe14fdb,
                _0x3d32ea,
                _0x3b6c73,
                _0x284ced
            );
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x112)] = function (_0x424c38) {
            const _0x5a65d4 = _0x9a8c74;
            Window_Doodads_Command[_0x5a65d4(0x11a)][_0x5a65d4(0x112)][_0x5a65d4(0x355)](
                this,
                _0x424c38
            );
            if (!this[_0x5a65d4(0xcc)]) return;
            const _0x5c0329 = this[_0x5a65d4(0x3c3)]();
            if (_0x5c0329) {
                $gameMap[_0x5a65d4(0x1ce)](_0x5c0329);
                if (TouchInput[_0x5a65d4(0x339)]()) return;
                this['x'] = _0x5c0329['x'] > 0x190 ? 0x0 : Graphics['width'] - 0x190;
            } else $gameMap[_0x5a65d4(0x37e)]();
        }),
        (Window_Doodads_PickDoodadList[_0x9a8c74(0x11a)][_0x9a8c74(0x21e)] = function () {
            return !![];
        }));
    function Window_Doodads_Import(..._0x34edef) {
        const _0x2853c7 = _0x9a8c74;
        this[_0x2853c7(0x35b)](..._0x34edef);
    }
    ((Window_Doodads_Import[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Doodads_Command['prototype']
    )),
        (Window_Doodads_Import[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Import),
        (Window_Doodads_Import['prototype'][_0x9a8c74(0x35b)] = function () {
            const _0x33a825 = _0x9a8c74;
            ((this[_0x33a825(0x1e5)] = $gameMap[_0x33a825(0x22c)]() || []),
                Window_Doodads_Command[_0x33a825(0x11a)][_0x33a825(0x35b)]['call'](this),
                (this['x'] = 0x190));
        }),
        (Window_Doodads_Import[_0x9a8c74(0x11a)][_0x9a8c74(0x15e)] = function () {
            return Graphics['width'] - 0x190;
        }),
        (Window_Doodads_Import[_0x9a8c74(0x11a)][_0x9a8c74(0x231)] = function () {
            return Graphics['height'];
        }),
        (Window_Doodads_Import[_0x9a8c74(0x11a)][_0x9a8c74(0x294)] = function () {
            const _0x1f6159 = _0x9a8c74,
                _0x42302a = $dataMapInfos,
                _0x474c69 = _0x42302a[_0x1f6159(0x352)];
            for (let _0x5b9018 = 0x1; _0x5b9018 < _0x474c69; _0x5b9018++) {
                const _0x5b3083 = _0x42302a[_0x5b9018];
                if (!_0x5b3083) continue;
                const _0xd8cf83 = _0x5b3083['id']['padZero'](0x3),
                    _0x571ec5 = _0x5b3083[_0x1f6159(0x27f)],
                    _0x17b700 = 'M%1:\x20%2',
                    _0x108ad7 = _0x17b700[_0x1f6159(0x11f)](_0xd8cf83, _0x571ec5);
                this[_0x1f6159(0x133)](_0x108ad7, _0x1f6159(0x395), !![], _0xd8cf83);
            }
        }),
        (Window_Doodads_Import[_0x9a8c74(0x11a)][_0x9a8c74(0x205)] = function () {
            return 'left';
        }));
    function Window_Doodads_Canvas(..._0x289d3c) {
        this['initialize'](..._0x289d3c);
    }
    ((Window_Doodads_Canvas[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Selectable[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_Canvas),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0x25113a = _0x9a8c74,
                _0x2dc917 = Graphics[_0x25113a(0x3a9)],
                _0x39e9db = Graphics[_0x25113a(0x158)];
            ((this['_tileWidth'] = $gameMap[_0x25113a(0x3b6)]()),
                (this[_0x25113a(0x2ca)] = $gameMap[_0x25113a(0x287)]()));
            const _0x1919fb = new Rectangle(0x0, 0x0, _0x2dc917, _0x39e9db);
            (Window_Selectable['prototype'][_0x25113a(0x35b)][_0x25113a(0x355)](this, _0x1919fb),
                this[_0x25113a(0xa8)](),
                (this[_0x25113a(0x7d)] = 0x0),
                (this[_0x25113a(0x21c)] = 0x0),
                this['hide'](),
                this[_0x25113a(0x143)]());
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x1f0)] = function () {
            const _0xcb90e9 = _0x9a8c74;
            this[_0xcb90e9(0x105)] = 0x0;
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x112)] = function (_0x1ae7ee) {}),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x210)] = function () {
            const _0x27d5ba = _0x9a8c74;
            return this[_0x27d5ba(0x38d)]() * 0x7;
        }),
        (Window_Doodads_Canvas['prototype']['refresh'] = function () {
            const _0x249771 = _0x9a8c74;
            this['contents'][_0x249771(0x2dd)]();
            const _0x1cce92 = this[_0x249771(0x210)]();
            let _0x58cfb5 = this[_0x249771(0x40b)][_0x249771(0x158)] - _0x1cce92;
            const _0x1a0044 = ImageManager['faceWidth'],
                _0x472f08 = this[_0x249771(0x40b)][_0x249771(0x3a9)] - _0x1a0044 * 0x2;
            (this[_0x249771(0x2d1)](
                0x0,
                _0x58cfb5,
                this[_0x249771(0x40b)][_0x249771(0x3a9)],
                _0x1cce92
            ),
                (_0x58cfb5 += this[_0x249771(0x38d)]() / 0x2));
            const _0x464bd7 = DoodadManager['current']();
            let _0x281b26;
            (_0x464bd7?.[_0x249771(0x9b)]
                ? (_0x281b26 = 'U\x20O\x20-\x20Change\x20Tile\x20-/+')
                : (_0x281b26 = _0x249771(0x2ec)),
                this[_0x249771(0x2f8)](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x0,
                    _0x472f08
                ),
                (_0x281b26 = 'Layer:\x20' + this[_0x249771(0x28b)]()),
                this[_0x249771(0x2f8)](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this['lineHeight']() * 0x0,
                    _0x472f08,
                    _0x249771(0x28a)
                ),
                DoodadManager[_0x249771(0x1da)]
                    ? (_0x281b26 = _0x249771(0x16a))
                    : (_0x281b26 = _0x249771(0x15d)),
                this[_0x249771(0x2f8)](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x1,
                    _0x472f08
                ),
                _0x464bd7?.['tileId']
                    ? (_0x281b26 = _0x249771(0x21f))
                    : (_0x281b26 = _0x249771(0x246)),
                this['drawText'](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x2,
                    _0x472f08
                ),
                (_0x281b26 = _0x249771(0x1eb) + this[_0x249771(0x1a1)]()),
                this[_0x249771(0x2f8)](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x2,
                    _0x472f08,
                    _0x249771(0x28a)
                ),
                (_0x281b26 = _0x249771(0x101)),
                this['drawText'](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x3,
                    _0x472f08
                ),
                (_0x281b26 = 'Y:\x20' + this[_0x249771(0x315)]()),
                this['drawText'](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x3,
                    _0x472f08,
                    'right'
                ),
                (_0x281b26 = _0x249771(0x212)),
                this[_0x249771(0x2f8)](
                    _0x281b26,
                    _0x1a0044,
                    _0x58cfb5 + this[_0x249771(0x38d)]() * 0x4,
                    _0x472f08
                ));
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)]['drawDarkRect'] = function (
            _0x588d1f,
            _0x127a13,
            _0x1a5254,
            _0x43b2d3
        ) {
            const _0xe33543 = _0x9a8c74,
                _0x4e795c = ColorManager[_0xe33543(0x3de)]();
            (this[_0xe33543(0x1a6)](![]),
                this['contents']['fillRect'](_0x588d1f, _0x127a13, _0x1a5254, _0x43b2d3, _0x4e795c),
                this[_0xe33543(0x1a6)](!![]));
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)]['currentLayer'] = function () {
            const _0x684d49 = _0x9a8c74;
            if (!DoodadManager[_0x684d49(0x8f)]()) return 0x5;
            return DoodadManager[_0x684d49(0x322)]()[_0x684d49(0x3d1)](
                DoodadManager[_0x684d49(0x8f)]()['z']
            );
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)] = function () {
            const _0x3e5b31 = _0x9a8c74;
            Window_Selectable[_0x3e5b31(0x11a)][_0x3e5b31(0x33f)][_0x3e5b31(0x355)](this);
            if (!this[_0x3e5b31(0xcc)]) return;
            (this[_0x3e5b31(0x36d)](), this[_0x3e5b31(0x341)](), this['updateClick']());
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x36d)] = function () {
            const _0x356098 = _0x9a8c74;
            (this['isReduceOpacity']()
                ? (this[_0x356098(0x13f)] -= 0x20)
                : (this[_0x356098(0x13f)] += 0x20),
                (this[_0x356098(0x13f)] = this[_0x356098(0x13f)]['clamp'](0x64, 0xff)));
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x3ef)] = function () {
            const _0xeeacd1 = _0x9a8c74;
            let _0x3ee9c5 = this[_0xeeacd1(0x40b)]['height'] - this['lineHeight']() * 0x6;
            if (TouchInput[_0xeeacd1(0xda)] > _0x3ee9c5) return !![];
            if (DoodadManager['_manualMove'] && DoodadManager[_0xeeacd1(0x2fa)] > _0x3ee9c5)
                return !![];
            return ![];
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x3c2)] = function () {
            const _0x5ed27f = _0x9a8c74;
            let _0x19d6ed;
            DoodadManager[_0x5ed27f(0x295)]
                ? (_0x19d6ed = DoodadManager[_0x5ed27f(0x13a)])
                : (_0x19d6ed = TouchInput['_mouseOverX']);
            const _0x59d61b = DoodadManager[_0x5ed27f(0x8f)]();
            if (DoodadManager[_0x5ed27f(0x237)] && _0x59d61b) {
                const _0x5b269b = DoodadManager['_gridLockX'],
                    _0x47584c =
                        _0x59d61b[_0x5ed27f(0x111)] === _0x5ed27f(0x242)
                            ? 0x0
                            : $gameMap[_0x5ed27f(0x2a1)](0x0) * this[_0x5ed27f(0x10a)];
                _0x19d6ed =
                    _0x47584c +
                    Math[_0x5ed27f(0x223)]((_0x19d6ed - _0x47584c) / _0x5b269b) * _0x5b269b;
                const _0x2685da = Math[_0x5ed27f(0x223)](
                    _0x5b269b * DoodadManager[_0x5ed27f(0x8f)]()[_0x5ed27f(0x2c7)]
                );
                _0x19d6ed += _0x2685da;
            }
            return Math['floor'](_0x19d6ed);
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)]['currentDoodadX'] = function () {
            const _0x49612f = _0x9a8c74;
            let _0x453238 = this['rawDoodadX']();
            return (
                DoodadManager['current']()?.[_0x49612f(0x111)] !== 'screen' &&
                    ((_0x453238 += $gameMap[_0x49612f(0x13d)] * this[_0x49612f(0x10a)]),
                    $gameMap[_0x49612f(0x384)]() &&
                        (_0x453238 = _0x453238 % ($gameMap['width']() * this[_0x49612f(0x10a)]))),
                Math[_0x49612f(0x223)](_0x453238)
            );
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x344)] = function () {
            const _0x3cb9c0 = _0x9a8c74;
            let _0x128307;
            DoodadManager[_0x3cb9c0(0x295)]
                ? (_0x128307 = DoodadManager[_0x3cb9c0(0x2fa)])
                : (_0x128307 = TouchInput['_mouseOverY']);
            const _0x95b4c3 = DoodadManager[_0x3cb9c0(0x8f)]();
            if (DoodadManager[_0x3cb9c0(0x237)] && _0x95b4c3) {
                const _0x38a98e = DoodadManager[_0x3cb9c0(0x3c5)],
                    _0x1194f1 =
                        _0x95b4c3[_0x3cb9c0(0x111)] === _0x3cb9c0(0x242)
                            ? 0x0
                            : $gameMap[_0x3cb9c0(0x13b)](0x0) * this[_0x3cb9c0(0x2ca)];
                _0x128307 =
                    _0x1194f1 + Math['floor']((_0x128307 - _0x1194f1) / _0x38a98e) * _0x38a98e;
                const _0x2a3620 = Math['floor'](
                    _0x38a98e * DoodadManager[_0x3cb9c0(0x8f)]()[_0x3cb9c0(0x31a)]
                );
                _0x128307 += _0x2a3620;
            }
            return Math[_0x3cb9c0(0x223)](_0x128307);
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)]['currentDoodadY'] = function () {
            const _0x4f08dd = _0x9a8c74;
            let _0x52462f = this[_0x4f08dd(0x344)]();
            return (
                DoodadManager[_0x4f08dd(0x8f)]()?.[_0x4f08dd(0x111)] !== _0x4f08dd(0x242) &&
                    ((_0x52462f += $gameMap[_0x4f08dd(0x3cc)] * this['_tileHeight']),
                    $gameMap[_0x4f08dd(0x1ea)]() &&
                        (_0x52462f = _0x52462f % ($gameMap['height']() * this[_0x4f08dd(0x2ca)]))),
                Math['floor'](_0x52462f)
            );
        }),
        (Window_Doodads_Canvas['prototype'][_0x9a8c74(0x341)] = function () {
            const _0x27d167 = _0x9a8c74;
            let _0x5c350d = ![];
            if (this[_0x27d167(0x1a1)]() !== this[_0x27d167(0x2cf)]) _0x5c350d = !![];
            if (this[_0x27d167(0x315)]() !== this[_0x27d167(0x358)]) _0x5c350d = !![];
            if (_0x5c350d) this[_0x27d167(0xa8)]();
        }),
        (Window_Doodads_Canvas[_0x9a8c74(0x11a)][_0x9a8c74(0x33c)] = function () {
            const _0x1fbb46 = _0x9a8c74;
            if (!this[_0x1fbb46(0xcc)]) return;
            if (!TouchInput[_0x1fbb46(0x298)]()) return;
            if (!this['isTouchedInsideFrame']()) return;
            if (DoodadManager[_0x1fbb46(0x295)])
                (SoundManager['playCursor'](), DoodadManager[_0x1fbb46(0xd3)](![]));
            else {
                if (DoodadManager[_0x1fbb46(0x1da)]) {
                    SoundManager[_0x1fbb46(0x260)]();
                    const _0x33a329 = DoodadManager[_0x1fbb46(0x8f)]();
                    ((_0x33a329['x'] = this['currentDoodadX']()),
                        (_0x33a329['y'] = this['currentDoodadY']()),
                        DoodadManager[_0x1fbb46(0xa8)]());
                } else {
                    SoundManager[_0x1fbb46(0x260)]();
                    const _0x1a9fdc = DoodadManager[_0x1fbb46(0x25c)]();
                    ((_0x1a9fdc['x'] = this[_0x1fbb46(0x1a1)]()),
                        (_0x1a9fdc['y'] = this[_0x1fbb46(0x315)]()),
                        DoodadManager['addNew'](_0x1a9fdc));
                }
            }
        }));
    function Window_Doodads_DrawGrid(..._0x306b4f) {
        const _0x49c169 = _0x9a8c74;
        this[_0x49c169(0x35b)](..._0x306b4f);
    }
    ((Window_Doodads_DrawGrid['prototype'] = Object['create'](Window_Base['prototype'])),
        (Window_Doodads_DrawGrid[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] = Window_Doodads_DrawGrid),
        (Window_Doodads_DrawGrid['prototype'][_0x9a8c74(0x35b)] = function () {
            const _0x35adf6 = _0x9a8c74,
                _0x4f9ee0 = Graphics[_0x35adf6(0x3a9)],
                _0xa1ff04 = Graphics[_0x35adf6(0x158)];
            ((this[_0x35adf6(0x10a)] = $gameMap[_0x35adf6(0x3b6)]()),
                (this[_0x35adf6(0x2ca)] = $gameMap['tileHeight']()));
            const _0x305016 = new Rectangle(0x0, 0x0, _0x4f9ee0, _0xa1ff04);
            (Window_Base[_0x35adf6(0x11a)][_0x35adf6(0x35b)]['call'](this, _0x305016),
                (this[_0x35adf6(0x105)] = 0x0),
                this['refresh'](),
                (this[_0x35adf6(0x7d)] = 0x0),
                (this[_0x35adf6(0x21c)] = 0x0),
                this[_0x35adf6(0x12e)](),
                this[_0x35adf6(0x143)]());
        }),
        (Window_Doodads_DrawGrid[_0x9a8c74(0x11a)][_0x9a8c74(0xba)] = function () {
            const _0x467495 = _0x9a8c74;
            ((this[_0x467495(0x318)] = 0x0),
                Window_Base[_0x467495(0x11a)][_0x467495(0xba)][_0x467495(0x355)](this));
        }),
        (Window_Doodads_DrawGrid[_0x9a8c74(0x11a)][_0x9a8c74(0xa8)] = function () {
            const _0x1e269a = _0x9a8c74;
            this[_0x1e269a(0x40b)]['clear']();
            if (!DoodadManager['_gridLockMode']) return;
            const _0x115c36 = DoodadManager['current']();
            ((this[_0x1e269a(0xd4)] = $gameMap[_0x1e269a(0x13d)]),
                (this['_lastDisplayY'] = $gameMap[_0x1e269a(0x3cc)]));
            const _0x1ee090 = DoodadManager[_0x1e269a(0x2a4)],
                _0x2f2da3 = DoodadManager['_gridLockY'],
                _0x40245f = $gameMap['adjustX'](0x0),
                _0x1eba8e = $gameMap[_0x1e269a(0x13b)](0x0),
                _0x51bfe7 = this[_0x1e269a(0x10a)] * _0x40245f,
                _0x960693 = this[_0x1e269a(0x2ca)] * _0x1eba8e,
                _0x464543 = _0x51bfe7 + $gameMap[_0x1e269a(0x3a9)]() * this['_tileWidth'],
                _0x14f4ab = _0x960693 + $gameMap[_0x1e269a(0x158)]() * this['_tileHeight'],
                _0x159c91 = this[_0x1e269a(0x105)],
                _0x3df702 = _0x115c36?.[_0x1e269a(0x111)] === 'screen' ? 0x0 : _0x51bfe7,
                _0x44a3b1 = _0x115c36?.[_0x1e269a(0x111)] === _0x1e269a(0x242) ? 0x0 : _0x960693,
                _0x45e9d3 = Math[_0x1e269a(0x1d1)](Graphics[_0x1e269a(0x3a9)], _0x464543);
            let _0x105d5c = Math[_0x1e269a(0x1d1)](Graphics[_0x1e269a(0x158)], _0x14f4ab);
            for (let _0x499693 = _0x3df702; _0x499693 < _0x45e9d3; _0x499693 += _0x1ee090) {
                if (_0x499693 + _0x1ee090 < 0x0) continue;
                for (let _0x3ec1b2 = _0x44a3b1; _0x3ec1b2 < _0x105d5c; _0x3ec1b2 += _0x2f2da3) {
                    if (_0x3ec1b2 + _0x2f2da3 < 0x0) continue;
                    const _0x2fe35e =
                            _0x499693 + _0x1ee090 < _0x464543 ? _0x1ee090 : _0x464543 - _0x499693,
                        _0x4e8a79 =
                            _0x3ec1b2 + _0x2f2da3 < _0x14f4ab ? _0x2f2da3 : _0x14f4ab - _0x3ec1b2,
                        _0x463eb7 = this['contents']['context'];
                    (_0x463eb7['save'](),
                        (_0x463eb7[_0x1e269a(0x1a0)] = _0x1e269a(0xf3)),
                        _0x463eb7[_0x1e269a(0xa7)](),
                        _0x463eb7[_0x1e269a(0x3a1)](_0x499693 - _0x159c91, _0x3ec1b2 - _0x159c91),
                        _0x463eb7[_0x1e269a(0x8d)](
                            _0x499693 - _0x159c91 + _0x2fe35e,
                            _0x3ec1b2 - _0x159c91
                        ),
                        _0x463eb7['stroke'](),
                        _0x463eb7[_0x1e269a(0xa7)](),
                        _0x463eb7[_0x1e269a(0x3a1)](_0x499693 - _0x159c91, _0x3ec1b2 - _0x159c91),
                        _0x463eb7[_0x1e269a(0x8d)](
                            _0x499693 - _0x159c91,
                            _0x3ec1b2 - _0x159c91 + _0x4e8a79
                        ),
                        _0x463eb7[_0x1e269a(0x1b8)]());
                }
            }
        }),
        (Window_Doodads_DrawGrid[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)] = function () {
            const _0x287a1f = _0x9a8c74;
            Window_Base[_0x287a1f(0x11a)][_0x287a1f(0x33f)]['call'](this);
            if (!DoodadManager[_0x287a1f(0x237)]) return;
            (this['_lastDisplayX'] !== $gameMap[_0x287a1f(0x13d)] ||
                this[_0x287a1f(0x221)] !== $gameMap[_0x287a1f(0x3cc)]) &&
                this[_0x287a1f(0xa8)]();
        }));
    function Window_Doodads_RegionOverlay(..._0x24a2c4) {
        const _0x297338 = _0x9a8c74;
        this[_0x297338(0x35b)](..._0x24a2c4);
    }
    ((Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)] = Object[_0x9a8c74(0x90)](
        Window_Base[_0x9a8c74(0x11a)]
    )),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x186)] =
            Window_Doodads_RegionOverlay),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x35b)] = function () {
            const _0xf216ca = _0x9a8c74,
                _0x16e3db = $gameMap[_0xf216ca(0x3a9)]() * $gameMap[_0xf216ca(0x3b6)](),
                _0x1a2039 = $gameMap['height']() * $gameMap[_0xf216ca(0x287)]();
            ((this[_0xf216ca(0xe4)] = $gameMap[_0xf216ca(0x3a9)]()),
                (this[_0xf216ca(0xc1)] = $gameMap[_0xf216ca(0x158)]()),
                (this['_tileWidth'] = $gameMap[_0xf216ca(0x3b6)]()),
                (this[_0xf216ca(0x2ca)] = $gameMap['tileHeight']()),
                (this[_0xf216ca(0x37a)] = 0x0),
                (this[_0xf216ca(0x24b)] = 0x0));
            const _0x4bc7f1 = new Rectangle(0x0, 0x0, _0x16e3db, _0x1a2039);
            (Window_Base['prototype'][_0xf216ca(0x35b)][_0xf216ca(0x355)](this, _0x4bc7f1),
                (this[_0xf216ca(0x7d)] = 0x0),
                (this[_0xf216ca(0x21c)] = 0x0),
                (this['z'] = 0x2328),
                this[_0xf216ca(0xa8)]());
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x1f0)] = function () {
            this['padding'] = 0x0;
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)]['itemPadding'] = function () {
            return 0x0;
        }),
        (Window_Doodads_RegionOverlay['prototype'][_0x9a8c74(0xa8)] = function () {
            const _0x115d60 = _0x9a8c74;
            this[_0x115d60(0x40b)]['clear']();
            for (let _0x11b62f = 0x0; _0x11b62f < this[_0x115d60(0xe4)]; ++_0x11b62f) {
                for (let _0x290b04 = 0x0; _0x290b04 < this[_0x115d60(0xc1)]; ++_0x290b04) {
                    $gameMap[_0x115d60(0x1d5)](_0x11b62f, _0x290b04) > 0x0 &&
                        this[_0x115d60(0x169)](_0x11b62f, _0x290b04);
                }
            }
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)]['drawRegion'] = function (
            _0x475431,
            _0x34f4e7
        ) {
            const _0x2d8eee = _0x9a8c74,
                _0x5038f6 = $gameMap[_0x2d8eee(0x1d5)](_0x475431, _0x34f4e7),
                _0x441321 = this['regionColor'](_0x5038f6),
                _0x46c5cd = this[_0x2d8eee(0x12f)](_0x5038f6, !![]),
                _0x392c57 = this[_0x2d8eee(0x159)](_0x475431, _0x34f4e7);
            ((this[_0x2d8eee(0x40b)][_0x2d8eee(0xe2)] = 0x64),
                this['contents'][_0x2d8eee(0x280)](
                    _0x392c57['x'],
                    _0x392c57['y'],
                    _0x392c57[_0x2d8eee(0x3a9)],
                    _0x392c57[_0x2d8eee(0x158)],
                    ColorManager[_0x2d8eee(0x191)](_0x46c5cd)
                ),
                this['contents'][_0x2d8eee(0x280)](
                    _0x392c57['x'] + 0x2,
                    _0x392c57['y'] + 0x2,
                    _0x392c57[_0x2d8eee(0x3a9)] - 0x4,
                    _0x392c57['height'] - 0x4,
                    ColorManager['rgbToStr'](_0x441321)
                ),
                (this[_0x2d8eee(0x40b)][_0x2d8eee(0xe2)] = 0xff),
                this[_0x2d8eee(0x40b)][_0x2d8eee(0x2f8)](
                    _0x5038f6,
                    _0x392c57['x'],
                    _0x392c57['y'],
                    _0x392c57[_0x2d8eee(0x3a9)],
                    _0x392c57['height'],
                    _0x2d8eee(0x292)
                ));
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x159)] = function (
            _0x2a9c3b,
            _0x34fce2
        ) {
            const _0x5bcb28 = _0x9a8c74;
            return {
                x: _0x2a9c3b * this[_0x5bcb28(0x10a)],
                y: _0x34fce2 * this[_0x5bcb28(0x2ca)],
                width: this[_0x5bcb28(0x10a)],
                height: this[_0x5bcb28(0x2ca)],
            };
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x12f)] = function (
            _0x41fcba,
            _0x168d0d
        ) {
            const _0x284825 = _0x9a8c74,
                _0x368e72 = ((_0x41fcba + 0xb) * 0x1e) % 0x168,
                _0xc4b54d = !_0x168d0d ? 0x64 : 0xa0,
                _0x1fc6a7 = !_0x168d0d ? 0x32 : 0x56;
            return ColorManager[_0x284825(0x2c1)](_0x368e72, _0xc4b54d, _0x1fc6a7);
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x33f)] = function () {
            const _0x1abae8 = _0x9a8c74;
            Window_Base[_0x1abae8(0x11a)][_0x1abae8(0x33f)]['call'](this);
            if (!this[_0x1abae8(0xcc)]) return;
            this[_0x1abae8(0x3b8)]();
        }),
        (Window_Doodads_RegionOverlay['prototype'][_0x9a8c74(0x3b8)] = function () {
            const _0x5863ba = _0x9a8c74;
            ((this['x'] = this[_0x5863ba(0x32c)]()), (this['y'] = this[_0x5863ba(0xbc)]()));
        }),
        (Window_Doodads_RegionOverlay[_0x9a8c74(0x11a)][_0x9a8c74(0x32c)] = function () {
            const _0x4dc77e = _0x9a8c74;
            let _0x4c16fd = this[_0x4dc77e(0x37a)];
            const _0x4807d1 = $gameMap[_0x4dc77e(0x13d)];
            return (
                (_0x4c16fd -= _0x4807d1 * this[_0x4dc77e(0x10a)]),
                Math[_0x4dc77e(0x223)](_0x4c16fd)
            );
        }),
        (Window_Doodads_RegionOverlay['prototype'][_0x9a8c74(0xbc)] = function () {
            const _0x1cac48 = _0x9a8c74;
            let _0x3a9b0b = this[_0x1cac48(0x24b)];
            const _0x462f1b = $gameMap[_0x1cac48(0x3cc)];
            return (
                (_0x3a9b0b -= _0x462f1b * this['_tileHeight']),
                Math[_0x1cac48(0x223)](_0x3a9b0b)
            );
        }));
}
