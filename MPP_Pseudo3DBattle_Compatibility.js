(() => {
    'use strict';

    // Store original methods
    const _Sprite_Animation_targetPseudo3DSpritePosition =
        Sprite_Animation.prototype.targetPseudo3DSpritePosition;
    const _Spriteset_Battle_convertSprite3dPlacement =
        Spriteset_Battle.prototype.convertSprite3dPlacement;

    // Override Sprite_Animation target position method
    Sprite_Animation.prototype.targetPseudo3DSpritePosition = function (sprite) {
        if (this._animation && this._animation.position === 3) {
            // For effects that should ignore 3D positioning
            return _Sprite_Animation_targetPseudo3DSpritePosition.call(this, sprite);
        }

        // For motion trails
        if (this._pseudo3dType === 'obj') {
            const placement = {
                x: sprite.x,
                y: sprite.y,
                scaleX: sprite.scale.x,
                scaleY: sprite.scale.y,
                converted: sprite._placementConverted,
            };

            if (!sprite._placementConverted) {
                BattleManager._spriteset.convertSprite3dPlacement(sprite);
            }

            const result = _Sprite_Animation_targetPseudo3DSpritePosition.call(this, sprite);

            if (!placement.converted) {
                sprite.x = placement.x;
                sprite.y = placement.y;
                sprite.scale.x = placement.scaleX;
                sprite.scale.y = placement.scaleY;
            }

            sprite._placementConverted = placement.converted;
            return result;
        }

        return _Sprite_Animation_targetPseudo3DSpritePosition.call(this, sprite);
    };

    // Override motion trail initialization to properly handle 3D positioning
    if (Imported.VisuMZ_3_ActSeqImpact) {
        const _Sprite_BattlerMotionTrail_initialize =
            Sprite_BattlerMotionTrail.prototype.initialize;
        Sprite_BattlerMotionTrail.prototype.initialize = function (source, data) {
            _Sprite_BattlerMotionTrail_initialize.call(this, source, data);
            // Set type to match the source battler
            this._pseudo3dType = source._pseudo3dType || 'obj';
            // Copy the source's altitude function
            this.pseudo3dAltitude = function () {
                return source.pseudo3dAltitude ? source.pseudo3dAltitude() : 0;
            };
        };

        // Override motion trail update to match source position
        const _Sprite_BattlerMotionTrail_update = Sprite_BattlerMotionTrail.prototype.update;
        Sprite_BattlerMotionTrail.prototype.update = function () {
            _Sprite_BattlerMotionTrail_update.call(this);
            if (this._source && this._source.parent) {
                // Match parent transformation
                this.x = this._source.x;
                this.y = this._source.y;
                this.scale.x = this._source.scale.x;
                this.scale.y = this._source.scale.y;
                // Apply opacity based on trail settings
                this.opacity = this._opacityRate * this._source.opacity;
            }
        };
    }

    // Override sprite conversion to handle motion trails
    Spriteset_Battle.prototype.convertSprite3dPlacement = function (sprite) {
        if (!sprite) return;

        // If this is a motion trail, use the source's position
        if (sprite instanceof Sprite_BattlerMotionTrail && sprite._source) {
            const sourceSprite = sprite._source;
            sprite.x = sourceSprite.x;
            sprite.y = sourceSprite.y;
            sprite.scale.x = sourceSprite.scale.x;
            sprite.scale.y = sourceSprite.scale.y;
            return;
        }

        const alt = sprite.pseudo3dAltitude ? sprite.pseudo3dAltitude() : 0;
        const groundX = sprite.x;
        const groundY = sprite.y + alt;
        const pos3d = Pseudo3DBattle.adjustPosition(groundX, groundY);

        sprite.x = pos3d.x;
        sprite.y = pos3d.y - alt * pos3d.scale;

        if (sprite._pseudo3dType === 'obj') {
            if (sprite instanceof Sprite_Battler && sprite._distortionSprite) {
                sprite._pseudo3dXMultiplier = pos3d.scale;
            }
            sprite.scale.x *= pos3d.scale;
            sprite.scale.y *= pos3d.scale;
        }

        sprite._placementConverted = true;
    };
})();
