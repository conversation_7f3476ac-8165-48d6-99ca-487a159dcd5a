//=============================================================================
// Ramza Plugins - Shield Block
// Ramza_ShieldBlock_MZ.js
// v1.04
//=============================================================================

var Ramza = Ramza || {};
Ramza.SB = Ramza.SB || {};
Ramza.SB.version = 1.04;
var Imported = Imported || {};
Imported['Ramza_ShieldBlock_MZ'] = true;

//=============================================================================
//=============================================================================
/*:
 * @target MZ
 * @plugindesc v1.04 Allows actors using a shield to block an incoming physical attack.
 * <AUTHOR>
 * @orderAfter VisuMZ_1_ElementStatusCore
 *
 * @param Visuals
 *
 * @param DefaultBlockAnimation
 * @text Block Animation
 * @type animation
 * @parent Visuals
 * @desc The default animation that plays over the target when a block occurs
 * @default 0
 *
 * @param DefaultSlamAnimation
 * @text Slam Animation
 * @type animation
 * @parent Visuals
 * @desc The default animation that plays over an attacker when the slam animation is called.
 * @default 0
 *
 * @param BlockText
 * @text Block Popup Text
 * @type text
 * @desc The text that pops up in place of the damage value when a block occurs.
 * @parent Visuals
 * @default Block
 *
 * @param OffsetX
 * @text X Offset
 * @parent BlockText
 * @desc Move the block popup left or right by changing the x offset.
 * @type number
 * @default 0
 *
 * @param OffsetY
 * @text Y Offset
 * @parent BlockText
 * @desc Move the block popup up or down by changing the y offset.
 * @type number
 * @default 0
 *
 * @param BlockWindowText
 * @text Block Result Text
 * @type text
 * @desc The message that shows in the battle log when a block occurs (top of the screen)
 * @parent Visuals
 * @default %1 blocks the attack!
 *
 * @param BlockSoundEffect
 * @text Block Sound Effect
 * @desc The sound effect played when a block occurs.
 * @parent Visuals
 * @type file
 * @dir audio/se/
 * @require 1
 * @default Hammer
 *
 * @param Rules
 *
 * @param BlockPhysical
 * @text Blockable Physical Skills?
 * @parent Rules
 * @desc If set to true, all physical skills can be blocked.
 * @type boolean
 * @default true
 *
 * @param BlockPercent
 * @parent Rules
 * @text Block Resistance
 * @desc The default float that all blocked damage is multiplied by.
 * @type text
 * @default 0
 *
 * @param BlockAmount
 * @parent Rules
 * @text Flat Block Value
 * @desc The default flat value subtracted from the incoming damage when an attack is blocked.
 * @type text
 * @default 0
 *
 * @param PartialEffect
 * @text Partial Block Effect
 * @parent Rules
 * @desc On a successful block where the target still takes damage, still apply effects of the blocked attack?
 * @type boolean
 * @default true
 *
 * @param Engine
 *
 * @param ShieldEtypeId
 * @text Shield EtypeId
 * @parent Engine
 * @desc Set the etypeId value for your shields.
 * @type number
 * @default 2
 * @min 1
 *
 * @param DefaultBlockEffect
 * @text Default Block Effect
 * @type combo
 * @desc This is the effect that will be called on all blocks, unless it is overridden
 * @parent Engine
 * @option Default
 * @default Default
 *
 * @param BlockEffects
 * @text Alternate Block Effects
 * @parent Engine
 * @desc Make your own custom block effects to select as default, or use in place of default via notetag.
 * @type struct<EffectList>[]
 * @default ["{\"Name\":\"Default\",\"Code\":\"\\\"value = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\n\\\"\"}","{\"Name\":\"Regeneration\",\"Code\":\"\\\"//target recovers HP equal to half the incoming damage before the block happened\\\\nvalue = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvar recovery = Math.round(value/2)\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\n$gameTemp.requestAnimation([b], 41)\\\\nsetTimeout(function(){\\\\nb.gainHp(recovery)\\\\nb.startDamagePopup()\\\\n}, 600)\\\\n\\\"\"}","{\"Name\":\"Thorns\",\"Code\":\"\\\"//target deals a percentage of blocked damage back to attacker\\\\nvalue = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvar originaldmg = value\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\nvar thornsdmg = originaldmg - value\\\\nthornsdmg *= 0.33\\\\nthornsdmg = Math.round(thornsdmg)\\\\naddedWait += 6\\\\na.gainHp(-thornsdmg)\\\\na.startDamagePopup()\\\\n\\\"\"}","{\"Name\":\"Parry\",\"Code\":\"\\\"//makes a melee swing\\\\nmotionRequested = \\\\\\\"attack\\\\\\\"\\\\nvalue = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\n\\\"\"}","{\"Name\":\"Regeneration VSBattleCore\",\"Code\":\"\\\"//target recovers HP equal to half the incoming damage before the block happened\\\\nvalue = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvar recovery = Math.round(value/2)\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\n$gameTemp.requestAnimation([b], 41)\\\\nsetTimeout(function(){\\\\nb.gainHp(recovery)\\\\nb.startDamagePopup()\\\\n}, 400)\\\\naddedWait += 16\\\"\"}","{\"Name\":\"Thorns VSBattleCore\",\"Code\":\"\\\"//target deals a percentage of blocked damage back to attacker\\\\nvalue = Ramza.SB.make_damage_value.call(this, target, critical)\\\\nvar originaldmg = value\\\\nvalue = this.applyBlockDamageModifier(target, value)\\\\nvar thornsdmg = originaldmg - value\\\\nthornsdmg *= 0.33\\\\nthornsdmg = Math.round(thornsdmg)\\\\nsetTimeout(function(){\\\\na.gainHp(-thornsdmg)\\\\na.startDamagePopup()\\\\n}, 300)\\\\naddedWait += 16\\\"\"}"]
 *
 * @param DisablingStates
 * @text Block Disabling States
 * @parent Engine
 * @type state[]
 * @desc A list of states that will disable blocking if the battler is afflicted with them.
 * @default []
 *
 *
 * @help
 * ============================================================================
 * Introduction:
 * ============================================================================
 *
 * Shield Block MZ is an upgraded port of Ramza_BlockChance for RPGMaker MV.
 * Actors who have blocking enabled will have a chance to block an incoming
 * physical attack. A successfully blocked skill has it's incoming damage
 * reduced by a configurable amount. The default is 100% of incoming damage
 * mitigated, but there are parameters to reduce incoming damage by a
 * percentage, a flat amount, or a combination of both.
 *
 * ============================================================================
 * How To Block:
 * ============================================================================
 *
 * Enabling block is done via any of the following:
 * -Equipping a shield that isn't tagged to prevent blocking.
 * -Equipping any other piece of equipment (weapon, armor) that is tagged to
 *  enable it.
 * -Being afflicted with a state that enables blocking.
 * -Having the tag in your actor, class, or enemy notebox.
 *
 * With block enabled, the actual chance to block is influenced via note tags
 * as well. Tags on weapons, armor, enemies, and states grant block rate,
 * which is added up to give the battlers total block rate, which can be
 * checked via (battler.blk) or (battler.xparam(10)).
 *
 * Likewise, the amount of damage removed from an incoming attack when it is
 * blocked is configurable via note tags; Block Percent and Block Value.
 *
 * Block Percent is the percentage of damage removed from an incoming attack.
 * The plugin parameter for this is a float value, which incoming damage is
 * multiplied by. The default value of 0 means that all shields using this
 * default value will block all of incoming damage.
 * The note tags for this parameter =remove= the value given in the tag from
 * 100%, meaning that a tag for 30% will cause an incoming blocked attack to
 * deal 30% less damage.
 *
 * Block Value is a flat number that is removed from incoming blocked damage
 * after the Block Percent is calculated. You can combine these two to make a
 * shield that reduces damage by x% +y, like how blocking works in popular MMO
 * games.
 *
 * If an incoming attack is fully resisted, no damage popup will display, as
 * no damage was dealt. If an attack is blocked, but still dealt some damage,
 * the popup will still show, and the skill still has a chance to cause any
 * added effects that it would cause on a hit, like poison or blindness.
 *
 * ============================================================================
 * Other Goodies
 * ============================================================================
 *
 * Disabling States:
 * You can specify states in the plugin parameters that will prevent the target
 * from being able to block. All states that prevent movement will also
 * automatically prevent blocking. You can add this effect to states like blind
 * to make them even more debilitating.
 *
 * Shield Piercing:
 * There are tags that can be used to reduce the effective block rate of a
 * defender. You can use shield piercing on skills to change the odds of a
 * block happening, or on enemies to cause them to ignore some of the players
 * block chance. Additionally, these can be negative, confering a bonus to
 * effective block rate instead.
 *
 * Animations:
 * A successful block will show a configurable animation over the defender.
 * This animation is set via note tags on shields, states, classes, actors
 * and enemies. As only one animation will show, priority is given to the value
 * from states first, then shield, class, actor, and enemy. Armors that aren't
 * shields cannot provide an animation to blocking, so keep that in mind.
 *
 * Additionally, a slam animation can be specified in the same way. This
 * animationis never actually used in the default engine, but you can use it
 * in an action sequence to draw a shield over the attacker to simulate
 * smacking someone with it. The same rules for the slam animation apply as
 * for the block animation above.
 *
 * Damage Popup:
 * RMMZ did away with the old system damage sprite sheet, and now draws damage
 * text on screen directly. That means a successful block will show a
 * configurable text popup, like how a miss shows the word miss!
 *
 * Block Effects:
 * You can now specify (almost) exactly what happens on a successful block. I
 * will go into further details about how this is done further into the
 * documentation. The default block effect will show the block animation, make
 * a guard pose under it, and apply the block multiplier to the incoming
 * damage.
 *
 * I have included some different effects in the plugin manager, and the
 * ability to create your own.
 *
 * -Defender heals HP based on amount blocked
 * -Attacker suffers damage based on amount blocked.
 * -Defender blocks the incoming attack with a weapon swing instead of a
 *  shield.
 *
 * The default effect can be set to any of these, or one you make yourself, but
 * anything that enables blocking can also enable one of these special effects
 * as well, allowing you to make special shields, or spells confer different
 * blocking effects.
 *
 * ============================================================================
 * Menu Integration:
 * ============================================================================
 *
 * The default menu system in RMMZ has no screen on which to show xparams such
 * as hit or crit rate, or block chance. As such, this plugin has no way to
 * show these values to the player.
 *
 * The version 1.07 update of the VisuStella Core Engine plugin brings in the
 * ability to define and show custom parameters on certain windows. Prior to
 * this update, I used my own functions to draw directly onto some of these
 * scenes, but this functionality is much more streamlined and better.
 *
 * To create a custom parameter open your Core Engine plugin parameters, open
 * 'custom parameters', and create a new one at the bottom of the list. Name
 * is what will be displayed on the menu. The abbreviation is how it is called,
 * the icon is the icon index, and the JS box will be given below. In the JS
 * box, replace the 'return 1;' line with what I give here.
 *
 * Currently, you can use these to display the following info:
 *
 * Name: Block Rate
 * Abbreviation: blkrt
 * icon 81 (or whatever you like)
 * type: float
 * JS: return user.getBlockRate();
 *
 * Note: DO NOT use 'blk' as the abbreviation for any custom parameters. This
 * will cause a crash error, and possibly other problems with blocking.
 *
 * Name: Shield Pierce
 * Abbreviation: shdprc
 * icon 77 (or whatever you like)
 * type: float
 * JS: return user.showShieldPiercingRate();
 *
 * Name: Block Strength
 * Abbreviation: blkstr
 * icon 128 (or whatever you like)
 * type: float
 * JS: return user.getBlockStrength();
 *
 * Name: Block Value
 * Abbreviation: blkamt
 * icon 129 (or whatever you like)
 * type: integer
 * JS: return user.getTotalBlockValue();
 *
 * Block Strength and Block Value were previously not able to be seen.
 * Block Strength is the percent of damage that is resisted when an attack is
 * blocked. The default value is 100%, meaning all damage is resisted.
 * Block Value is the flat block value that is removed from a successfully
 * blocked attack. This number is removed after the block strength has already
 * been calculated.
 *
 * In order to see any of these custom parameters on any menus, you needd to
 * put them in the lists in the plugin parameters for the given VS plugin.
 *
 * Core Engine -> Parameter Settings -> Displayed Parameters
 * Adding something to this list will show it on the default equip scene,
 * the default status scene, and some of the updated menu layouts for VS
 * MainMenuCore. Keep in mind that space on these windows is often quite
 * limited, in the case of the default status scene, adding even one param is
 * impossible to see without removing another one.
 *
 * Core Engine -> Paremeter Settings -> Extended Parameters
 * These parameters show on more VS custom menu content. The modified equip
 * scene, the shop scene, the item scene, and probably a few others. Space
 * in these windows is generally much less of a constraint, as well.
 *
 * You can also include these custom parameters on the updated status menu
 * as well, by including them in the columns plugin parameters.
 *
 * ElementStatusCore -> Status Menu Settings -> Displayed Parameters
 * Add in any of the custom parameters above to any of the columns.
 *
 * Other menu plugins:
 * You can call actor.blk to get a float value of the actor's current block
 * rate (or battler.blk) for use in a HUD or other menu system. You can also
 * call actor.getShieldPiercing() which returns a whole number equal to the
 * shield pierce percentage of the actor.
 *
 * ============================================================================
 * Note Tags:
 * ============================================================================
 *
 * <Enables Block>
 * This special tag can be placed on weapons, armors, states, classes, actors,
 * and enemies. Anything something with this tag on it enables blocking,
 * whether a shield is equipped or not.
 *
 * Usage Example:
 * Put this tag on a state to cause that state to enable blocking, together
 * with tags to pick a special block effect, block percent, block value, and
 * animation.
 *
 * <Cannot Block>
 * This tag can be on shields, states or enemies. A shield with this tag will
 * not enable blocking. An enemy with this tag will be unable to block. A state
 * with this tag will actively disable blocking when it is on a battler,
 * preventing them from blocking entirely.
 *
 * Usage Example:
 * Add this to items held in your shield slot that are not shields. Putting it
 * on a debuff state such as blind can make it extra nasty. Use this tag on
 * enemies that you don't want to be able to block at all.
 *
 * <Shield Pierce: X%>
 * Tag to be placed on equipment, enemies, skills, or states. A battlers total
 * shield pierce is added up, and then subtracted from the defender's block
 * rate when an action is checked for a block. This value can be negative.
 *
 * Usage Example:
 * Put this tag on a boss monster to make his physical attacks more likely to
 * land, or put it on physical skills you still want to be able to be blocked,
 * but less likely. You could also put a negative tag on a skill to make that
 * more likely to be blocked.
 *
 * <Block Chance: X%>
 * Can be placed on equipment, enemies actors, classes and states. All sources
 * of these tags are added together to get a final block rate, which is
 * checked against when a blockable skill targets you. Even if blocking is
 * enabled, if there are no block chance notetags on a battler, blocking will
 * be impossible.
 *
 * Usage Example:
 * Put different values on different shields to make larger shields better at
 * blocking. The same tag can be put on states or other armor to increase
 * block rate further. Enemies don't wear equipment, so they will need to have
 * this tag on their enemy notebox, or have a state with it on them in order to
 * block.
 *
 * <Block Percent: X%>
 * Can be placed on equipment, states, classes, actors or enemies. This % is
 * taken away from the incoming damage when it is blocked. A shield with 30%
 * block percent causes an incoming attack to deal 30% less damage. Multiple
 * sources of this are multiplied together. A shield that provides 50% and a
 * state that also provides 50% will reduce incoming damage by 75%.
 * Shields that do not have their blocking disabled will inherit the default
 * value from the plugin parameters, as will enemies. All others have a
 * default value of 0%
 *
 * Usage Example:
 * Use this tag to specify how effective a block is at mitigating damage.
 * Heavier shields could be set higher to allow more mitigation to offset
 * parameter penalties for wearing heavy armor. Putting this tag on a buff
 * spell will make blocking more effective at mitigation as well.
 *
 * <Block Value: X>
 * Placed on equipment, states, classes, actors, or enemies. This value is
 * removed from the incoming damage of a blocked attack after it has been
 * mutliplied by the Block Percent value above. Multiple sources of this tag
 * are added together. This tag will not cause a blocked attack to deal less
 * than zero damage.
 *
 * Usage Example:
 * Use by itself with no percent tag to have block always absorb a flat amount
 * of damage. Use in combination with the percent tag above to cause blocking
 * to behave more like it does in popular MMO games (a blocked hit deals x% +y
 * less damage).
 *
 * <Block Animation: X>
 * Specifies the animation ID shown when a block occurs. This tag can be on
 * a state, equipment, class, actor or enemy. When the engine is deciding which
 * animation to show, it prioritizes them in that order if they exist. When it
 * checks equipment for this tag, it prioritizes equipment in higher slot IDs,
 * meaning it looks at accessories first, then armor, then shield, then weapon.
 *
 * Usage Example:
 * Putting this tag on shield shows that animation when a block happens with it.
 * The way priority is set for this means that states active on a defender can
 * change the animation, so you can use that to make a fancy buff skill that
 * makes your block animation fancier. The same is also possible with
 * accessories.
 *
 * <Slam Animation: X>
 * Like block animation above, but this animation isn't used for anything by
 * default.
 *
 * Usage Example:
 * You can call this animation Id in an action sequence to show it over a
 * battler while it uses a shield slam-type skill. battler.getSlamAnimation()
 * returns the ID to be used with a show animation call.
 *
 * <Blockable>
 * Used on skills. A skill with this tag can be blocked. The plugin parameter
 * will turn this on for all physical skills.
 *
 * Usage Example:
 * Put this tag on a skill that you want to be able to be blocked. This tag
 * will work on all skills, not just physical ones, so you can make certain hit
 * or magical skills blockable as well using this.
 *
 * <Unblockable>
 * Like above, this tag is for skills. Putting it on a skill makes it unable to
 * be blocked.
 *
 * Usage Example:
 * Put this on a physical skill that you don't want to be blocked. Certain hit,
 * and magical skills already cannot be blocked, so this tag won't have any
 * effect on them.
 *
 * <Block Effect: name>
 * Can be on states, equipment, or enemies, and will be checked in that order.
 * The plugin parameters specify a default block effect that will be used if
 * one isn't specified via note tags. If checking against equipment, priority
 * is given to equips in the higher slot numbers, meaning it will prioritize
 * accessories and armor over weapons. Only equipment that also enables
 * blocking will change block effect. All states can change it, regardless of
 * whether they enable blocking or not.
 * Specifies a block effect that will happen on a successful block. The plugin
 * parameters have a section under Engine for setting these up, and making
 * custom effects.
 *
 * Usage Example:
 * Put this on a state or special equipment to change the effect of blocking
 * entirely. A battler afflicted with a state that enables blocking, and has a
 * block animation, and a block percent/value set can block without a shield.
 * You can use this to make defensive spells that defend against attacks
 * passively. There is more info on using this below.
 *
 * ============================================================================
 * Advanced Use:
 * ============================================================================
 *
 * Block Effects:
 * The plugin parameters contain a section for defining block effects - a block
 * effect is a block of code that is run after a block is determined to have
 * occured. I included many examples in the plugin already, but you can also
 * create your own. Lets look at the default block effect together to see what
 * it does:
 *
 * value = Ramza.SB.make_damage_value.call(this, target, critical)
 * value = this.applyBlockDamageModifier(target, value)
 *
 * These two lines are all that is necessary to create a block effect. The
 * first line is setting value equal to the incoming damage from the action,
 * the second line applies the block damage modifier to that value.
 *
 * Let's look at the parry effect instead:
 *
 * motionRequested = "attack"
 * value = Ramza.SB.make_damage_value.call(this, target, critical)
 * value = this.applyBlockDamageModifier(target, value)
 *
 * This one is different because it causes the defender to melee attack at
 * the incoming hit, rather than guard. As you can see, we set motionRequested
 * to "attack" to cause the battler to attack instead of guarding. If this
 * isn't set, a guard happens by default.
 *
 * Making Your Own Block Effect:
 * This function is called during the action result phase, before damage is
 * dealt, but after it has been calculated. It doesn't happen if a block
 * doesn't occur. You can use any function you want attached to anything that
 * the action_result has access to, as well as the global scope. Some examples:
 *
 * (a, user)
 * -The user of the skill
 * (b, target)
 * -The target of the skill
 * (result)
 * -The action result, you can use this to check if the result dealt hpdamage
 *  for example.
 * (item)
 * -The skill being used. You can use this to check properties of the incoming
 *  attack, such as element, to cause a conditional effect.
 * (v[x])
 * -All game variables (v[x] returns the value of variable x
 *
 * You can set motionRequest to any battler motion and it will play as well.
 * You can set priorityBlockAnimation to a number and blocking will play that
 * animation in place of the defenders block animation as well.
 * Making addedWait equal to a number will cause the battle system to wait
 * that many frames at the end of the effect.
 * You can look at the VSBattleCore battle effects and see how I injected
 * waits into them using setTimeout. This is advanced, so I'd recommend
 * avoiding doing that if you can, but sometimes it's not possible to get
 * the effect to look just right without it.
 *
 * If you are using VS_1_BattleCore, you may need to add in delays to make
 * animations play correctly, or to give enough time for battle motions to
 * show. You can see I have included two different versions of Regeneration and
 * the Thorns effect. While they both work with both the default and the VS
 * system, the VS ones are designed for maximum compatibility with the VS
 * system.
 *
 * Setting a Block Effect:
 * The default block effect is set in the plugin parameters. It is always used,
 * unless a different one is specified via note tags. See the note tag section
 * above for more information.
 *
 * ============================================================================
 * Plugin Dependencies:
 * ============================================================================
 *
 * None
 *
 * ============================================================================
 * Known Issues:
 * ============================================================================
 *
 * Sprites are drawn on screen in the order they are called. This means the
 * block damage popup can actually be covered by the block animation. This
 * appears to be a bug in the base RMMZ engine, as manually setting the z
 * value lower doesn't even draw animations below other graphics as it did in
 * RMMV. This issue doesn't happen if you're using VisuStella BattleCore.
 *
 * On some versions of VSBattleCore, when an actor blocks, the project crashes
 * before showing the block popup.
 * -Update your VSBattleCore to version 1.05, this was an issue in version 1.03
 * and 1.04 that was corrected by the VisuStella team in v1.05.
 *
 * ============================================================================
 * Terms of Use:
 * ============================================================================
 *
 * -This plugin may be used in commercial or non-commercial projects. With
 *  credit to me, Ramza.
 * -Purchase of this plugin allows you to use it in as many projects as you
 *  want.
 * -You may modify this plugin directly, for personal use only.
 * -Sharing this plugin, direct edits to it, or any demo projects that come
 *  with it is prohibited.
 * -You may share edits to this plugin as extensions to this one. Extensions
 *  require the base plugin to function.
 * -You can choose to sell extensions to this plugin for profit, crowdfunding,
 *  donations, etc, as long as the extension requires this base plugin to
 *  function.
 * -Do not modify the header of the plugin file, and do not claim ownership of
 *  the plugin in your own projects.
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * v1.04
 * -Added x and y offset parameters for the block damage popup. Change these
 *  to have more control over where the block popup shows on the screen, to
 *  prevent it from being stacked on top of the damage popup, for example.
 * v1.03
 * -Added a couple of functions to take advantage of custom parameters in the
 *  VisuStella Core Engine v1.07 update.
 * -Updated documentation to include info on how to add certain block related
 *  parameters to the VS Core Engine plugin.
 * -Plugin parameters related to displaying block rate and shield piercing
 *  have been removed from this plugin, as those values are not used anymore.
 * v1.02
 * -Corrected an issue where blocked attacks that dealt no damage would still
 * cause a zero damage popup in VSBattleCore.
 * v1.01
 * -Users who are using VisuStella CoreEngine can now set "BLK" and "SHDPRC" as
 *  parameters to show. This will make them show on the default equip scene, if
 *  there is space to draw it.
 * v1.00
 * -Initial Release
 *
 * ============================================================================
 *
 * Special Thanks
 * -Arisu of Team VisuStella
 *
 * ============================================================================
 * endofhelpfile
 */
/*~struct~EffectList:
 * @param Name
 * @type text
 * @desc The name to refer to this as.
 * @param Code
 * @text Effect Code
 * @type note
 * @desc The code ran during a block result
 */

//Process plugin parameters
//================================================================================

const params = PluginManager.parameters('Ramza_ShieldBlock_MZ');
Ramza.SB.parameters = {};
Ramza.SB.parameters.defaultBlockAnimation = Number(params['DefaultBlockAnimation']);
Ramza.SB.parameters.defaultSlamAnimation = Number(params['DefaultSlamAnimation']);
Ramza.SB.parameters.blockText = params['BlockText'];
Ramza.SB.parameters.offsetX = Number(params['OffsetX']);
Ramza.SB.parameters.offsetY = Number(params['OffsetY']);
Ramza.SB.parameters.blockWindowText = params['BlockWindowText'];
Ramza.SB.parameters.blockSoundEffect = {
    name: params['BlockSoundEffect'],
    pan: 0,
    pitch: 100,
    volume: 90,
};
Ramza.SB.parameters.blockPhysical = eval(params['BlockPhysical']);
Ramza.SB.parameters.partialEffect = eval(params['PartialEffect']);
Ramza.SB.parameters.blockPercent = params['BlockPercent'];
Ramza.SB.parameters.blockAmount = params['BlockAmount'];
Ramza.SB.parameters.defaultBlockEffect = params['DefaultBlockEffect'];
Ramza.SB.parameters.blockEffects = JSON.parse(params['BlockEffects']);
Ramza.SB.parameters.blockEffects = JSON.parse(params['BlockEffects']);
for (let i = 0; i < Ramza.SB.parameters.blockEffects.length; i++) {
    Ramza.SB.parameters.blockEffects[i] = JSON.parse(Ramza.SB.parameters.blockEffects[i]);
    Ramza.SB.parameters.blockEffects[i].Code = JSON.parse(Ramza.SB.parameters.blockEffects[i].Code);
}
Ramza.SB.parameters.shieldEtypeId = Number(params['ShieldEtypeId']);
Ramza.SB.parameters.disablingStates = eval(params['DisablingStates']);
for (let i = 0; i < Ramza.SB.parameters.disablingStates.length; i++) {
    Ramza.SB.parameters.disablingStates[i] = Number(Ramza.SB.parameters.disablingStates[i]);
}

//database loads
//================================================================================

Ramza.SB.DataManager_isDatabaseLoaded = DataManager.isDatabaseLoaded;
DataManager.isDatabaseLoaded = function () {
    if (!Ramza.SB.DataManager_isDatabaseLoaded.call(this)) return false;
    if (!Ramza._loaded_Shield_Block) {
        this.processBlockEnableNoteTags();
        this.processBlockClassNoteTags();
        this.processBlockActorNoteTags();
        this.processBlockEnableOnShields();
        this.processBlockDisablingStates();
        this.processShieldBlockStateNotetags($dataStates);
        this.processShieldBlockArmorNotetags($dataArmors);
        this.processShieldBlockEnemyNotetags($dataEnemies);
        this.processShieldBlockSkillNotetags($dataSkills);
        this.processShieldBlockWeaponNotetags($dataWeapons);
        Ramza._loaded_Shield_Block = true;
    }
    return true;
};

//Process Notetags
//================================================================================

DataManager.processBlockClassNoteTags = function () {
    const group = $dataClasses;
    const note1 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note2 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note3 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note4 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note5 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.blockValue = 0;
        obj.blockPercent = 1;
        for (let i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note2)) {
                obj.blockPercent *= 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note3)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note5)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            }
        }
    }
};

DataManager.processBlockActorNoteTags = function () {
    const group = $dataActors;
    const note1 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note2 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note3 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note4 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note5 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.blockValue = 0;
        obj.blockPercent = 1;
        for (let i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note2)) {
                obj.blockPercent *= 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note3)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note5)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            }
        }
    }
};

DataManager.processBlockEnableNoteTags = function () {
    const group = [$dataWeapons, $dataArmors, $dataActors, $dataClasses, $dataStates];
    const notetag = /<(?:ENABLES BLOCK)>/i;
    group.forEach(function (ele) {
        for (let i = 1; i < ele.length; i++) {
            var obj = ele[i];
            var notedata = obj.note.split(/[\r\n]+/);
            for (var n = 0; n < notedata.length; n++) {
                var line = notedata[n];
                if (line.match(notetag)) obj._enablesBlock = true;
            }
        }
    });
};

DataManager.processBlockEnableOnShields = function () {
    const group = $dataArmors;
    const notetag = /<(?:CANNOT BLOCK)>/i;
    group.forEach(function (ele) {
        if (ele && ele.etypeId == Ramza.SB.parameters.shieldEtypeId) {
            ele._enablesBlock = true;
            let notedata = ele.note.split(/[\r\n]+/);
            for (let i = 0; i < notedata.length; i++) {
                var line = notedata[i];
                if (line.match(notetag)) ele._enablesBlock = false;
            }
            if (ele._enablesBlock == false) delete ele._enablesBlock;
        }
    });
};

DataManager.processBlockDisablingStates = function () {
    const group = $dataStates;
    const notetag = /<(?:CANNOT BLOCK)>/i;
    group.forEach(function (ele) {
        if (ele) {
            if (Ramza.SB.parameters.disablingStates.contains(ele.id)) ele._disablesBlock = true;
            let notedata = ele.note.split(/[\r\n]+/);
            for (let i = 0; i < notedata.length; i++) {
                var line = notedata[i];
                if (line.match(notetag)) ele._disablesBlock = true;
            }
        }
    });
};

DataManager.processShieldBlockArmorNotetags = function (group) {
    const note1 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    const note2 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note3 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note4 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note5 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note6 = /<(?:SHIELD PIERCE):[ ]*([+-]?\d+)[%]?>/i;
    const note7 = /<(?:BLOCK EFFECT):[ ]*(.*)>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.blockAnimation = Ramza.SB.parameters.defaultBlockAnimation;
        obj.slamAnimation = Ramza.SB.parameters.defaultBlockAnimation;
        obj.blockValue = Ramza.SB.parameters.blockAmount;
        obj.blockPercent =
            obj.etypeId == Ramza.SB.parameters.shieldEtypeId ? Ramza.SB.parameters.blockPercent : 1;
        obj.shieldPierce = 0;
        for (let i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            } else if (line.match(note2)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note3)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note5)) {
                obj.blockPercent = 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note6)) {
                obj.shieldPierce = parseInt(RegExp.$1);
            } else if (line.match(note7)) {
                obj.blockEffect = String(RegExp.$1);
            }
        }
    }
};

DataManager.processShieldBlockEnemyNotetags = function (group) {
    const note1 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    const note2 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note3 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note4 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note5 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note6 = /<(?:CANNOT BLOCK)>/i;
    const note7 = /<(?:SHIELD PIERCE):[ ]*([+-]?\d+)[%]?>/i;
    const note8 = /<(?:BLOCK EFFECT):[ ]*(.*)>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.blockAnimation = Ramza.SB.parameters.defaultBlockAnimation;
        obj.slamAnimation = Ramza.SB.parameters.defaultBlockAnimation;
        obj.blockValue = Ramza.SB.parameters.blockAmount;
        obj.blockPercent = Ramza.SB.parameters.blockPercent;
        obj._cannotBlock = false;
        obj.shieldPierce = 0;
        for (let i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            } else if (line.match(note2)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note3)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note5)) {
                obj.blockPercent = 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note6)) {
                obj._cannotBlock = true;
            } else if (line.match(note7)) {
                obj.shieldPierce = parseInt(RegExp.$1);
            } else if (line.match(note8)) {
                obj.blockEffect = String(RegExp.$1);
            }
        }
    }
};

DataManager.processShieldBlockSkillNotetags = function (group) {
    const note1 = /<(?:BLOCKABLE)>/i;
    const note2 = /<(?:UNBLOCKABLE)>/i;
    const note3 = /<(?:SHIELD PIERCE):[ ]*([+-]?\d+)[%]?>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.blockable = obj.hitType == 1 ? Ramza.SB.parameters.blockPhysical : false;
        obj.shieldPierce = 0;
        for (let i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                obj.blockable = true;
            } else if (line.match(note2)) {
                obj.blockable = false;
            } else if (line.match(note3)) {
                obj.shieldPierce = parseInt(RegExp.$1);
            }
        }
    }
};

DataManager.processShieldBlockStateNotetags = function (group) {
    const note1 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    const note2 = /<(?:SHIELD PIERCE):[ ]*([+-]?\d+)[%]?>/i;
    const note3 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note4 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note5 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note6 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note7 = /<(?:BLOCK EFFECT):[ ]*(.*)>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.shieldPierce = 0;
        obj.blockValue = 0;
        obj.blockPercent = 1;
        for (var i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            } else if (line.match(note2)) {
                obj.shieldPierce = parseInt(RegExp.$1);
            } else if (line.match(note3)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.blockPercent *= 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note5)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note6)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note7)) {
                obj.blockEffect = String(RegExp.$1);
            }
        }
    }
};

DataManager.processShieldBlockWeaponNotetags = function (group) {
    const note1 = /<(?:SHIELD PIERCE):[ ]*([+-]?\d+)[%]?>/i;
    const note2 = /<(?:BLOCK CHANCE:)[ ]*([+-]?\d+)[%]?>/i;
    const note3 = /<(?:BLOCK VALUE):[ ](\d+)>/i;
    const note4 = /<(?:BLOCK PERCENT):[ ](\d+)[%]?>/i;
    const note5 = /<(?:BLOCK ANIMATION):[ ](\d+)>/i;
    const note6 = /<(?:SLAM ANIMATION):[ ](\d+)>/i;
    const note7 = /<(?:BLOCK EFFECT):[ ]*(.*)>/i;
    for (var n = 1; n < group.length; n++) {
        var obj = group[n];
        var notedata = obj.note.split(/[\r\n]+/);
        obj.shieldPierce = 0;
        for (var i = 0; i < notedata.length; i++) {
            var line = notedata[i];
            if (line.match(note1)) {
                obj.shieldPierce = parseInt(RegExp.$1);
            } else if (line.match(note2)) {
                var traitAdd = obj.traits.length;
                obj.traits[traitAdd] = {};
                obj.traits[traitAdd].code = 22;
                obj.traits[traitAdd].dataId = 10;
                obj.traits[traitAdd].value = parseInt(RegExp.$1) / 100;
            } else if (line.match(note3)) {
                obj.blockValue = parseInt(RegExp.$1);
            } else if (line.match(note4)) {
                obj.blockPercent = 1 - parseInt(RegExp.$1) / 100;
            } else if (line.match(note5)) {
                obj.blockAnimation = parseInt(RegExp.$1);
            } else if (line.match(note6)) {
                obj.slamAnimation = parseInt(RegExp.$1);
            } else if (line.match(note7)) {
                obj.blockEffect = String(RegExp.$1);
            }
        }
    }
};

//Defining block rate xparam
//================================================================================

Object.defineProperties(Game_BattlerBase.prototype, {
    // BLocK chance
    blk: {
        get: function () {
            return this.xparam(10);
        },
        configurable: true,
    },
});

//Game_BattlerBase
//================================================================================

Ramza.SB.xparam_plus = Game_BattlerBase.prototype.xparamPlus;
Game_BattlerBase.prototype.xparamPlus = function (xparamId) {
    if (xparamId == 10) {
        return 0;
    }
    return Ramza.SB.xparam_plus.call(this, xparamId);
};

Ramza.SB.xparam_rate = Game_BattlerBase.prototype.xparamRate;
Game_BattlerBase.prototype.xparamRate = function (xparamId) {
    if (xparamId == 10) {
        return 1;
    }
    return Ramza.SB.xparam_rate.call(this, xparamId);
};

Ramza.SB.xparam_flatBonus = Game_BattlerBase.prototype.xparamFlatBonus;
Game_BattlerBase.prototype.xparamFlatBonus = function (xparamId) {
    if (xparamId == 10) {
        return 0;
    }
    return Ramza.SB.xparam_flatBonus.call(this, xparamId);
};

Game_BattlerBase.prototype.canActivelyBlock = function () {
    //returns true if the battler is block capable, and isn't afflicted with a state that prevents blocking
    return this.restriction() < 4 && !this.isBlockDisabled() && this.isBlockCapable();
};

Game_BattlerBase.prototype.isBlockDisabled = function () {
    const states = this.states();
    const blocked = element => element._disablesBlock === true;
    return states.some(blocked);
};

Game_BattlerBase.prototype.getTotalBlockValue = function () {
    //returns the sum of all block value parameters on a battler
    return this.states().reduce((r, state) => r + state.blockValue, 0);
};

Game_BattlerBase.prototype.getTotalBlockResistFloat = function () {
    //returns the sum of all block value parameters on a battler
    return this.states().reduce((r, state) => r * state.blockPercent, 1);
};

Game_BattlerBase.prototype.getShieldPiercing = function () {
    return this.states().reduce((r, state) => r + state.shieldPierce, 0);
};

Game_BattlerBase.prototype.getBlockEffect = function () {
    const defaultEffect = Ramza.SB.parameters.defaultBlockEffect;
    return defaultEffect;
};

//Game_Battler
//================================================================================

Game_Battler.prototype.performBlock = function () {
    AudioManager.playSe(Ramza.SB.parameters.blockSoundEffect);
    //SoundManager.playEvasion();
};

//Game_Enemy
//================================================================================

Game_Enemy.prototype.getBlockEffect = function () {
    const defaultEffect = Game_BattlerBase.prototype.getBlockEffect.call(this);
    const enemyEffect = this.enemy().blockEffect;

    var statelist = this.states();
    statelist = statelist.filter(function (state) {
        if (state && state.blockEffect) {
            return state;
        }
    });
    var stateEffect = statelist.length > 0 ? statelist[0].blockEffect : undefined;

    return stateEffect ? stateEffect : enemyEffect ? enemyEffect : defaultEffect;
};

Game_Enemy.prototype.getTotalBlockValue = function () {
    //returns the sum of all block value parameters on a battler
    var statestotal = Game_BattlerBase.prototype.getTotalBlockValue.call(this);
    var enemyblock = eval(this.enemy().blockValue);
    return statestotal + enemyblock;
};

Game_Enemy.prototype.getTotalBlockResistFloat = function () {
    var basevalue = eval(this.enemy().blockPercent);
    var statestotal = Game_BattlerBase.prototype.getTotalBlockResistFloat.call(this);
    return (basevalue * statestotal).clamp(0, 1);
};

Game_Enemy.prototype.getShieldPiercing = function () {
    var basevalue = eval(this.enemy().shieldPierce);
    var statestotal = Game_BattlerBase.prototype.getShieldPiercing.call(this);
    return basevalue * statestotal;
};

Game_Enemy.prototype.isBlockCapable = function () {
    return !this.enemy()._cannotBlock;
};

Game_Enemy.prototype.getBlockAnimation = function () {
    var base = this.enemy().blockAnimation;
    var states = this.states().filter(function (state) {
        if (state && state._enablesBlock == true) {
            return state;
        }
    });
    states = states.length > 0 ? states[0].blockAnimation : undefined;
    return states ? states : base;
};

Game_Enemy.prototype.getSlamAnimation = function () {
    var base = this.enemy().slamAnimation;
    var states = this.states().filter(function (state) {
        if (state && state._enablesBlock == true) {
            return state;
        }
    });
    states = states.length > 0 ? states[0].slamAnimation : undefined;
    return states ? states : base;
};

//Game_Actor
//================================================================================
if (!Imported || !Imported['VisuMZ_1_BattleCore']) {
    Game_Actor.prototype.performBlock = function () {
        Game_Battler.prototype.performBlock.call(this);
        let battler = this;
        if ($gameTemp._blockMotion) {
            if ($gameTemp._blockMotion == 'attack') {
                this.performAttack();
            } else {
                this.requestMotion($gameTemp._blockMotion);
            }
        }
    };
} else {
    Game_Actor.prototype.performBlock = function () {
        Game_Battler.prototype.performBlock.call(this);
        let battler = this;
        if ($gameTemp._blockMotion) {
            if ($gameTemp._blockMotion == 'attack') {
                this.performAttack();
            } else {
                this.requestMotion($gameTemp._blockMotion);
            }
        }
        delete $gameTemp._blockMotion;
        //battler._motionRefresh = false
        setTimeout(function () {
            battler.clearMotion();
            battler._motionRefresh = true;
        }, 640);
        SceneManager._scene._logWindow._waitCount += 30;
    };
}

Game_Actor.prototype.isBlockCapable = function () {
    //returns true if the actor has something that enables blocking
    var checks = [];
    checks[0] = this.hasBlockEquip();
    checks[1] = this.hasBlockActorTag();
    checks[2] = this.hasBlockClassTag();
    checks[3] = this.hasBlockState();
    const enabled = element => element === true;
    return checks.some(enabled);
};

Game_Actor.prototype.hasBlockEquip = function () {
    let equips = this.equips();
    for (i = 0; i < equips.length; i++) {
        let equip = equips[i];
        if (!equip) continue;
        if (!equip._enablesBlock) continue;
        return true;
    }
    return false;
};

Game_Actor.prototype.hasBlockActorTag = function () {
    return $dataActors[this.actorId()]._enablesBlock == true;
};

Game_Actor.prototype.hasBlockClassTag = function () {
    return $dataClasses[this.currentClass().id]._enablesBlock == true;
};

Game_Actor.prototype.hasBlockState = function () {
    let states = this._states;
    for (i = 0; i < states.length; i++) {
        let state = $dataStates[states[i]];
        if (!state) continue;
        if (!state._enablesBlock) continue;
        return true;
    }
    return false;
};

Game_Actor.prototype.getTotalBlockValue = function () {
    //returns the sum of all block value parameters on an actor
    var statestotal = Game_BattlerBase.prototype.getTotalBlockValue.call(this);
    var equipstotal = this.equips().reduce(function (r, equip) {
        if (equip && equip.blockValue != undefined) {
            return r + eval(equip.blockValue);
        } else {
            return r;
        }
    }, 0);
    var actorbonus = this.actor().blockValue;
    var classbonus = this.currentClass().blockValue;
    return statestotal + equipstotal + actorbonus + classbonus;
};

Game_Actor.prototype.getTotalBlockResistFloat = function () {
    var basevalue = this.actor().blockPercent;
    var classmodifier = this.currentClass().blockPercent;
    var statestotal = Game_BattlerBase.prototype.getTotalBlockResistFloat.call(this);
    var equipstotal = this.equips().reduce(function (r, equip) {
        if (equip && equip.blockPercent != undefined) {
            return r * eval(equip.blockPercent);
        } else {
            return r;
        }
    }, 1);
    return (basevalue * classmodifier * statestotal * equipstotal).clamp(0, 1);
};

Game_Actor.prototype.getShieldPiercing = function () {
    var equips = this.equips().reduce(function (r, equip) {
        if (equip && equip.shieldPierce != undefined) {
            return r + eval(equip.shieldPierce);
        } else {
            return r;
        }
    }, 0);
    var states = Game_BattlerBase.prototype.getShieldPiercing.call(this);
    return equips + states;
};

Game_Actor.prototype.getBlockAnimation = function () {
    var equipped = this.equips().filter(function (equip) {
        if (equip && equip.etypeId == Ramza.SB.parameters.shieldEtypeId) {
            return equip;
        }
    });
    var classValue = this.currentClass().blockAnimation;
    var actorValue = this.actor().blockAnimation;
    equipped = equipped.length > 0 ? equipped[0].blockAnimation : undefined;
    var states = this.states().filter(function (state) {
        if (state && state._enablesBlock == true) {
            return state;
        }
    });
    states = states.length > 0 ? states[0].blockAnimation : undefined;
    return states
        ? states
        : equipped
          ? equipped
          : classValue
            ? classValue
            : actorValue
              ? actorValue
              : 0;
};

Game_Actor.prototype.getSlamAnimation = function () {
    var equipped = this.equips().filter(function (equip) {
        if (equip && equip.etypeId == Ramza.SB.parameters.shieldEtypeId) {
            return equip;
        }
    });
    var classValue = this.currentClass().slamAnimation;
    var actorValue = this.actor().slamAnimation;
    equipped = equipped.length > 0 ? equipped[0].slamAnimation : 0;
    var states = this.states().filter(function (state) {
        if (state && state._enablesBlock == true) {
            return state;
        }
    });
    states = states.length > 0 ? states[0].slamAnimation : undefined;
    return states
        ? states
        : equipped
          ? equipped
          : classValue
            ? classValue
            : actorValue
              ? actorValue
              : 0;
};

Game_Actor.prototype.getBlockEffect = function () {
    const defaultEffect = Game_BattlerBase.prototype.getBlockEffect.call(this);
    const equips = this.equips();
    var list = equips.filter(function (equip) {
        if (equip && equip._enablesBlock && equip.blockEffect) {
            return equip;
        }
    });
    var equipEffect = list.length > 0 ? list[list.length - 1].blockEffect : undefined;
    var statelist = this.states();
    statelist = statelist.filter(function (state) {
        if (state && state.blockEffect) {
            return state;
        }
    });
    var stateEffect = statelist.length > 0 ? list[0].blockEffect : undefined;
    return stateEffect ? stateEffect : equipEffect ? equipEffect : defaultEffect;
};

Game_Actor.prototype.getBlockRate = function () {
    //returns a processed version of Block Rate that clamps at 100%, and shows 0 if actor cannot block
    if (!this.isBlockCapable()) return 0;
    return Math.min(1, this.blk);
};

Game_Actor.prototype.showShieldPiercingRate = function () {
    //returns a processed shield piercing amount
    return Math.max(0, Math.min(1, this.getShieldPiercing() / 100));
};

Game_Actor.prototype.getBlockStrength = function () {
    //returns the % of damage resisted by a successful block
    return 1 - this.getTotalBlockResistFloat();
};

//Sprite_Damage
//================================================================================

Ramza.SB.sprite_damage_prototype_setup = Sprite_Damage.prototype.setup;
Sprite_Damage.prototype.setup = function (target) {
    const result = target.result();
    if (result.blocked) {
        var oldDmg =
            (!Imported || !Imported['VisuMZ_1_BattleCore']) && result.hpDamage > 0
                ? result.hpDamage
                : 0;
        this._colorType = 0;
        this.createBlock();
        result.blocked = false;
        $gameTemp.aBlock = true;
        if ((!Imported || !Imported['VisuMZ_1_BattleCore']) && oldDmg > 0) {
            let smth = this;
            setTimeout(function () {
                smth._colorType = oldDmg >= 0 ? 0 : 1;
                smth.createDigits(oldDmg);
            }, 200);
        } else if (Imported['VisuMZ_1_BattleCore'] && oldDmg <= 0) {
            result.hpAffected = false;
        }
        if ($gameTemp.clearThing) {
            target.clearDamagePopup();
            delete $gameTemp.clearThing;
        }
    } else {
        Ramza.SB.sprite_damage_prototype_setup.call(this, target);
    }
};

Sprite_Damage.prototype.createBlock = function () {
    const offsetX = Ramza.SB.parameters.offsetX;
    const offsetY = Ramza.SB.parameters.offsetY;
    const h = this.fontSize();
    const w = Math.floor(h * 3.0);
    const sprite = this.createChildSprite(w, h);
    sprite.x += offsetX;
    sprite.yOffset = true;
    sprite.y += offsetY;
    sprite.bitmap.drawText(Ramza.SB.parameters.blockText, 0, 0, w, h, 'center');
    sprite.dy = 0;
};

Ramza.SB.sprite_damage_update_child = Sprite_Damage.prototype.updateChild;
Sprite_Damage.prototype.updateChild = function (sprite) {
    Ramza.SB.sprite_damage_update_child.call(this, sprite);
    sprite.y = sprite.yOffset ? Math.round(sprite.y + Ramza.SB.parameters.offsetY) : sprite.y;
};

Ramza.SB.AR_clear = Game_ActionResult.prototype.clear;
Game_ActionResult.prototype.clear = function () {
    this.blocked = false;
    Ramza.SB.AR_clear.call(this);
};

//Game_Action
//================================================================================

Ramza.SB.game_action_apply = Game_Action.prototype.apply;
Game_Action.prototype.apply = function (target) {
    const result = target.result();
    this.subject().clearResult();
    result.clear();
    result.used = this.testApply(target);
    result.blocked = Math.random() < this.itemBlk(target);
    $gameTemp.blockedHit = result.blocked;
    Ramza.SB.game_action_apply.call(this, target);
};

Ramza.SB.make_damage_value = Game_Action.prototype.makeDamageValue;
Game_Action.prototype.makeDamageValue = function (target, critical) {
    if ($gameTemp.blockedHit) {
        target.result().blocked = true;
        const a = this.subject();
        const b = target;
        const result = target.result();
        const item = BattleManager._action._item;
        const user = a;
        const v = $gameVariables._data;
        var value = 0;
        var motionRequested = 'guard';
        var priorityBlockAnimation = null;
        var addedWait = 0;
        const defaultCode = Ramza.SB.parameters.blockEffects.filter(function (codes) {
            if (codes && codes.Name == 'Default') return codes;
        })[0].Code;
        var blockeffectmode = target.getBlockEffect().toLowerCase();
        var code = Ramza.SB.parameters.blockEffects.filter(function (codes) {
            if (codes && codes.Name.toLowerCase() == blockeffectmode) return codes;
        });
        var code = code[0] ? code[0].Code : defaultCode;
        try {
            eval(code);
        } catch (e) {
            console.log(e);
        }
        SceneManager._scene._logWindow._waitCount += addedWait;
        $gameTemp._blockMotion = motionRequested;
        $gameTemp._blockAnimation = priorityBlockAnimation;
        return value;
    } else {
        return Ramza.SB.make_damage_value.call(this, target, critical);
    }
};

Game_Action.prototype.applyBlockDamageModifier = function (target, value) {
    var multiplier = target.getTotalBlockResistFloat();
    var flatamount = target.getTotalBlockValue();
    var modifiedAmount = Math.max(Math.round(value * multiplier - flatamount), 0);
    delete $gameTemp.blockedHit;
    if (modifiedAmount == 0) $gameTemp.clearThing = true;
    return modifiedAmount;
};

Ramza.SB.apply_item_effect = Game_Action.prototype.applyItemEffect;
Game_Action.prototype.applyItemEffect = function (target, effect) {
    if (
        target._result.blocked &&
        (target._result.hpDamage == 0 || !Ramza.SB.parameters.partialEffect)
    ) {
    } else {
        Ramza.SB.apply_item_effect.call(this, target, effect);
    }
};

Game_Action.prototype.itemBlk = function (target) {
    if (this.isBlockable() && target.canActivelyBlock()) {
        return Math.max(
            0,
            target.blk - this.subject().getShieldPiercing() / 100 - this.item().shieldPierce / 100
        );
    } else {
        return 0;
    }
};

Game_Action.prototype.isBlockable = function () {
    return this.item().blockable === true;
};

//Window_BattleLog
//================================================================================

Window_BattleLog.prototype.performBlock = function (target) {
    if (!$gameTemp._blockAnimation) {
        $gameTemp.requestAnimation([target], target.getBlockAnimation());
    } else {
        $gameTemp.requestAnimation([target], $gameTemp._blockAnimation);
        delete $gameTemp._blockAnimation;
    }
    target.performBlock();
};

Window_BattleLog.prototype.displayBlock = function (target) {
    let fmt;
    const isActor = target.isActor();
    fmt = Ramza.SB.parameters.blockWindowText;
    this.push('performBlock', target);
    if (!Imported || !Imported['VisuMZ_1_BattleCore']) {
        this.push('addText', fmt.format(target.name()));
        if (target.result().hpDamage > 0) this.push('addText', this.makeHpDamageText(target));
    }
};

Ramza.SB.display_damage = Window_BattleLog.prototype.displayDamage;
Window_BattleLog.prototype.displayDamage = function (target) {
    if (target.result().blocked) {
        this.displayBlock(target);
    } else {
        Ramza.SB.display_damage.call(this, target);
    }
};

//Window_StatusData (VS menu plugin required)
//================================================================================
