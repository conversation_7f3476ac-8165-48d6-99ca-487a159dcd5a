/*:
 * @target MZ
 * @plugindesc Match-3 Minigame Core v1.88 - Fix match checking to reflect swapped icons
 */

(() => {
    const GRID_SIZE = 12;
    const ICON_SIZE = 32;
    const TILE_SIZE = 48;
    const ICON_START = 2980;
    const ICON_END = 2983;
    const HOLY_CROSS_ICON = 70;
    const DARK_VORTEX_ICON = 71;

    class Scene_Match3 extends Scene_Base {
        create() {
            super.create();
            this._comboPitch = 100;
            this._score = 0;
            this._combo = 0;
            // this.createScoreDisplay(); // Temporarily disable adding early
            this.createBackground();
            this._selectedX = 0;
            this._selectedY = 0;
            this._firstSelected = null;
            this._pendingClick = false;
            this._gridSprites = [];
            this._gridData = [];
            this._animating = false;
            this._clearQueue = [];
            this._fallingSprites = [];
            this.createGrid();
            this.createSelector();
            this.createHighlight();
            this.createScoreDisplay(); // Re-added here to ensure it's drawn after grid and selector
            this._turns = 0;
            this._maxCombo = 0;
            this._goalScore = 100000;
            this.createStatsDisplay();
        }

        createBackground() {
            this._backgroundSprite = new Sprite(ImageManager.loadParallax('Match3Background'));
            this._backgroundSprite.x = 0;
            this._backgroundSprite.y = 0;
            this.addChild(this._backgroundSprite);
        }

        update() {
            this.updateStatsDisplay();

            this.updateComboGlow();
            this.updateParticles();
            this.updateFallingSprites();

            // Tier popup timer logic
            if (this._tierPopupTimer > 0) {
                this._tierPopupTimer--;
                if (this._tierPopup) {
                    this._tierPopup.opacity -= 3;
                    this._tierPopup.y -= 0.2;
                }
                if (this._tierPopupTimer <= 0 && this._tierPopup) {
                    this._tierPopup.opacity = 0;
                    this._tierPopup.scale.set(1, 1);
                    this._tierPopup.y = this._startY + (GRID_SIZE * TILE_SIZE) / 2 - 40;
                }
            }

            super.update();

            // Flash fade logic
            if (this._flashSprite && this._flashFade > 0) {
                this._flashSprite.opacity -= 10;
                this._flashFade--;
                if (this._flashFade <= 0) this.removeChild(this._flashSprite);
            }
            if (!this._animating) {
                this.updateInput();
                this.updateMouseHover();
            }
            this.updateSelectorPosition();
            this.updateSelectorAnimation();
            this.updateHighlight();
            this.updateAnimations();
        }

        createGrid() {
            const totalWidth = GRID_SIZE * TILE_SIZE;
            const totalHeight = GRID_SIZE * TILE_SIZE;
            this._startX = (Graphics.width - totalWidth) / 2;
            this._startY = (Graphics.height - totalHeight) / 2;

            const iconRange = Array.from(
                { length: ICON_END - ICON_START + 1 },
                (_, i) => ICON_START + i
            );

            for (let y = 0; y < GRID_SIZE; y++) {
                this._gridData[y] = [];
                for (let x = 0; x < GRID_SIZE; x++) {
                    let candidates = [...iconRange];
                    if (x >= 2 && this._gridData[y][x - 1] === this._gridData[y][x - 2]) {
                        candidates = candidates.filter(i => i !== this._gridData[y][x - 1]);
                    }
                    if (y >= 2 && this._gridData[y - 1][x] === this._gridData[y - 2][x]) {
                        candidates = candidates.filter(i => i !== this._gridData[y - 1][x]);
                    }
                    const iconIndex = candidates[Math.floor(Math.random() * candidates.length)];
                    this._gridData[y][x] = iconIndex;

                    const sprite = new Sprite_Icon(iconIndex);
                    sprite.x = this._startX + x * TILE_SIZE;
                    sprite.y = this._startY + y * TILE_SIZE;
                    sprite._gridX = x;
                    sprite._gridY = y;
                    this.addChild(sprite);
                    this._gridSprites.push(sprite);
                }
            }
        }

        createSelector() {
            const size = ICON_SIZE + 4;
            const bm = new Bitmap(size, size);
            bm.strokeRect(0, 0, size, size, 'white');

            this._selector = new Sprite(bm);
            this._selector.anchor.set(0.5);
            this._selector._pulse = 0;
            this.addChild(this._selector);
            this.updateSelectorPosition();
        }

        createHighlight() {
            const size = ICON_SIZE + 4;
            const bm = new Bitmap(size, size);
            bm.fillRect(0, 0, size, size, 'rgba(255,255,0,0.4)');

            this._highlight = new Sprite(bm);
            this._highlight.anchor.set(0.5);
            this._highlight.visible = false;
            this.addChild(this._highlight);
        }

        updateSelectorPosition() {
            this._selector.x = this._startX + this._selectedX * TILE_SIZE + ICON_SIZE / 2;
            this._selector.y = this._startY + this._selectedY * TILE_SIZE + ICON_SIZE / 2;
        }

        updateFallingSprites() {
            if (this._fallingSprites.length === 0) return;
            let allLanded = true;
            for (const fall of this._fallingSprites) {
                const targetY = this._startY + fall.toY * TILE_SIZE;
                const dy = targetY - fall.sprite.y;
                if (Math.abs(dy) > 1) {
                    fall.sprite.y += dy * 0.25;
                    allLanded = false;
                } else {
                    fall.sprite.y = targetY;
                }
            }
            if (allLanded) {
                this._combo++;

                this._fallingSprites = [];
                const matched = this.findAllMatches();
                if (matched.flat().some(Boolean)) {
                    this.clearMatches(matched);
                }
            }
        }

        updateHighlight() {
            if (this._firstSelected) {
                const { x, y } = this._firstSelected;
                this._highlight.visible = true;
                this._highlight.x = this._startX + x * TILE_SIZE + ICON_SIZE / 2;
                this._highlight.y = this._startY + y * TILE_SIZE + ICON_SIZE / 2;
            } else {
                this._highlight.visible = false;
            }
        }

        updateSelectorAnimation() {
            const scale = 1 + Math.sin(this._selector._pulse) * 0.05;
            this._selector.scale.set(scale, scale);
            this._selector._pulse += 0.1;
        }

        updateInput() {
            if (Input.isTriggered('left') && this._selectedX > 0) this._selectedX--;
            if (Input.isTriggered('right') && this._selectedX < GRID_SIZE - 1) this._selectedX++;
            if (Input.isTriggered('up') && this._selectedY > 0) this._selectedY--;
            if (Input.isTriggered('down') && this._selectedY < GRID_SIZE - 1) this._selectedY++;

            if (Input.isTriggered('ok')) this.handleSelection(this._selectedX, this._selectedY);
        }

        updateMouseHover() {
            const tx = Math.floor((TouchInput.x - this._startX) / TILE_SIZE);
            const ty = Math.floor((TouchInput.y - this._startY) / TILE_SIZE);
            if (tx >= 0 && tx < GRID_SIZE && ty >= 0 && ty < GRID_SIZE) {
                this._selectedX = tx;
                this._selectedY = ty;

                if (TouchInput.isTriggered()) {
                    this._pendingClick = true;
                }
            }

            if (this._pendingClick) {
                this.handleSelection(this._selectedX, this._selectedY);
                this._pendingClick = false;
            }
        }

        handleSelection(x, y) {
            if (!this._firstSelected) {
                this._firstSelected = { x, y };
                SoundManager.playCursor();
            } else {
                const dx = Math.abs(x - this._firstSelected.x);
                const dy = Math.abs(y - this._firstSelected.y);
                const isAdjacent = dx + dy === 1;

                if (isAdjacent && this.simulateSwapAndCheck(this._firstSelected, { x, y })) {
                    this._combo = 1;
                    this._comboPitch = 100;
                    this._lastSwappedTile = { x, y };
                    this._otherSwappedTile = this._firstSelected;
                    this.startSwapAnimation(this._firstSelected, { x, y });
                    this._turns++;
                    SoundManager.playOk();
                } else {
                    SoundManager.playBuzzer();
                }
                this._firstSelected = null;
            }
        }

        simulateSwapAndCheck(a, b) {
            const clone = this._gridData.map(row => row.slice());
            const temp = clone[a.y][a.x];
            clone[a.y][a.x] = clone[b.y][b.x];
            clone[b.y][b.x] = temp;
            return (
                this.checkMatchAt(a.x, a.y, clone) ||
                this.checkMatchAt(b.x, b.y, clone) ||
                [HOLY_CROSS_ICON, DARK_VORTEX_ICON].includes(clone[a.y][a.x]) ||
                [HOLY_CROSS_ICON, DARK_VORTEX_ICON].includes(clone[b.y][b.x])
            );
        }

        checkMatchAt(x, y, grid) {
            const value = grid[y][x];
            let count = 1;
            let i = x - 1;
            while (i >= 0 && grid[y][i] === value) {
                count++;
                i--;
            }
            i = x + 1;
            while (i < GRID_SIZE && grid[y][i] === value) {
                count++;
                i++;
            }
            if (count >= 3) return true;

            count = 1;
            i = y - 1;
            while (i >= 0 && grid[i][x] === value) {
                count++;
                i--;
            }
            i = y + 1;
            while (i < GRID_SIZE && grid[i][x] === value) {
                count++;
                i++;
            }
            return count >= 3;
        }

        checkForLineOfFour(x, y) {
            const icon = this._gridData[y][x];
            let countH = 1,
                countV = 1;

            // Horizontal check
            for (let dx = x - 1; dx >= 0 && this._gridData[y][dx] === icon; dx--) countH++;
            for (let dx = x + 1; dx < GRID_SIZE && this._gridData[y][dx] === icon; dx++) countH++;

            // Vertical check
            for (let dy = y - 1; dy >= 0 && this._gridData[dy][x] === icon; dy--) countV++;
            for (let dy = y + 1; dy < GRID_SIZE && this._gridData[dy][x] === icon; dy++) countV++;

            return countH === 4 || countV === 4;
        }

        checkForLineOfFive(x, y) {
            const icon = this._gridData[y][x];
            let countH = 1,
                countV = 1;

            // Horizontal check
            for (let dx = x - 1; dx >= 0 && this._gridData[y][dx] === icon; dx--) countH++;
            for (let dx = x + 1; dx < GRID_SIZE && this._gridData[y][dx] === icon; dx++) countH++;

            // Vertical check
            for (let dy = y - 1; dy >= 0 && this._gridData[dy][x] === icon; dy--) countV++;
            for (let dy = y + 1; dy < GRID_SIZE && this._gridData[dy][x] === icon; dy++) countV++;

            return countH === 5 || countV === 5;
        }

        placeHolyCross(x, y) {
            const i = y * GRID_SIZE + x;

            // Remove the previous sprite if it exists
            if (this._gridSprites[i]) {
                this.removeChild(this._gridSprites[i]);
            }

            this._gridData[y][x] = HOLY_CROSS_ICON;
            const sprite = new Sprite_Icon(HOLY_CROSS_ICON);
            sprite.x = this._startX + x * TILE_SIZE;
            sprite.y = this._startY + y * TILE_SIZE;
            sprite._gridX = x;
            sprite._gridY = y;
            this.addChild(sprite);
            this._gridSprites[i] = sprite;
        }

        placeDarkVortex(x, y) {
            const i = y * GRID_SIZE + x;

            // Remove the previous sprite if it exists
            if (this._gridSprites[i]) {
                this.removeChild(this._gridSprites[i]);
            }

            this._gridData[y][x] = DARK_VORTEX_ICON;
            const sprite = new Sprite_Icon(DARK_VORTEX_ICON);
            sprite.x = this._startX + x * TILE_SIZE;
            sprite.y = this._startY + y * TILE_SIZE;
            sprite._gridX = x;
            sprite._gridY = y;
            this.addChild(sprite);
            this._gridSprites[i] = sprite;
        }

        activateHolyCross(pos) {
            AudioManager.playSe({ name: 'Magic1', pan: 0, pitch: 100, volume: 90 });
            this._comboPitch = 100;
            const matched = Array.from({ length: GRID_SIZE }, () => Array(GRID_SIZE).fill(false));
            for (let i = 0; i < GRID_SIZE; i++) {
                matched[pos.y][i] = true;
                matched[i][pos.x] = true;
            }
            matched[pos.y][pos.x] = true;
            this.clearMatches(matched);
        }

        activateDarkVortex(pos, swappedIcon) {
            const matched = Array.from({ length: GRID_SIZE }, () => Array(GRID_SIZE).fill(false));

            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (this._gridData[y][x] === swappedIcon) {
                        matched[y][x] = true;
                    }
                }
            }

            // Also remove the Dark Vortex tile itself
            matched[pos.y][pos.x] = true;
            this.clearMatches(matched);
        }

        findAllMatches() {
            const matched = Array.from({ length: GRID_SIZE }, () => Array(GRID_SIZE).fill(false));

            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE - 2; x++) {
                    const val = this._gridData[y][x];
                    if (
                        val &&
                        val === this._gridData[y][x + 1] &&
                        val === this._gridData[y][x + 2]
                    ) {
                        matched[y][x] = matched[y][x + 1] = matched[y][x + 2] = true;
                    }
                }
            }

            for (let x = 0; x < GRID_SIZE; x++) {
                for (let y = 0; y < GRID_SIZE - 2; y++) {
                    const val = this._gridData[y][x];
                    if (
                        val &&
                        val === this._gridData[y + 1][x] &&
                        val === this._gridData[y + 2][x]
                    ) {
                        matched[y][x] = matched[y + 1][x] = matched[y + 2][x] = true;
                    }
                }
            }

            return matched;
        }

        clearMatches(matched, swappedTile = null) {
            let clearedCount = 0;
            let bonusPoints = 0;
            if (this._lastSwappedTile && this._otherSwappedTile) {
                const tilesToCheck = [this._lastSwappedTile, this._otherSwappedTile];
                for (const tile of tilesToCheck) {
                    if (this.checkForLineOfFive(tile.x, tile.y)) {
                        matched[tile.y][tile.x] = false;
                        this.placeDarkVortex(tile.x, tile.y);
                    } else if (this.checkForLineOfFour(tile.x, tile.y)) {
                        matched[tile.y][tile.x] = false;
                        this.placeHolyCross(tile.x, tile.y);
                    }
                }
            }
            const particleBitmap = new Bitmap(20, 20);
            particleBitmap.drawCircle(10, 10, 10, 'white');
            AudioManager.playSe({ name: 'Magic1', pan: 0, pitch: this._comboPitch, volume: 90 });
            this._comboPitch += 10;
            for (let y = 0; y < GRID_SIZE; y++) {
                for (let x = 0; x < GRID_SIZE; x++) {
                    if (matched[y][x]) {
                        clearedCount++;
                        const icon = this._gridData[y][x];
                        if (icon === HOLY_CROSS_ICON) bonusPoints += 250;
                        else if (icon === DARK_VORTEX_ICON) bonusPoints += 500;
                        this._gridData[y][x] = null;
                        const i = y * GRID_SIZE + x;
                        const flash = this._gridSprites[i];
                        flash.setBlendColor([255, 255, 0, 128]);
                        this.removeChild(flash);

                        // ✨ Pre-pop match trace
                        const trace = new Sprite(new Bitmap(TILE_SIZE, TILE_SIZE));
                        trace.bitmap.fillRect(0, 0, TILE_SIZE, TILE_SIZE, 'rgba(255,255,255,0.1)');
                        trace.x = this._startX + x * TILE_SIZE;
                        trace.y = this._startY + y * TILE_SIZE;
                        trace.opacity = 180;
                        trace._fade = 30;
                        this.addChild(trace);
                        this._clearQueue.push(trace);

                        // ✨ Tiny spark burst on tile clear
                        const sparkBitmap = new Bitmap(6, 6);
                        sparkBitmap.drawCircle(3, 3, 3, 'white');
                        for (let s = 0; s < 6; s++) {
                            const spark = new Sprite(sparkBitmap);
                            spark.x = flash.x + ICON_SIZE / 2;
                            spark.y = flash.y + ICON_SIZE / 2;
                            spark.opacity = 255;
                            spark.scale.set(0.4 + Math.random() * 0.2);
                            const angle = Math.random() * 2 * Math.PI;
                            const speed = (0.5 + Math.random() * 1.5) * (1 + this._combo * 0.15);
                            spark._vx = Math.cos(angle) * speed;
                            spark._vy = Math.sin(angle) * speed;
                            this.addChild(spark);
                            this._clearQueue.push(spark);
                        }

                        // ✨ Add floating score popup
                        const popup = new Sprite(new Bitmap(60, 24));
                        popup.bitmap.fontSize = 20;
                        popup.bitmap.textColor = '#ffffff';
                        let bonusText = '+100';
                        if (icon === HOLY_CROSS_ICON) bonusText = '+250';
                        else if (icon === DARK_VORTEX_ICON) bonusText = '+500';
                        popup.bitmap.drawText(bonusText, 0, 0, 60, 24, 'center');
                        popup.x = flash.x;
                        popup.y = flash.y;
                        popup._vy = -1;
                        popup.opacity = 255;
                        this.addChild(popup);
                        this._clearQueue.push(popup);
                        for (let p = 0; p < 12; p++) {
                            const part = new Sprite(particleBitmap);
                            part.x = flash.x + ICON_SIZE / 2;
                            part.y = flash.y + ICON_SIZE / 2;
                            part.opacity = 255;
                            part.scale.set(0.3 + Math.random() * 0.2);
                            const angle = Math.random() * 2 * Math.PI;
                            const speed = (1.5 + Math.random()) * (1 + this._combo * 0.3);
                            part._vx = Math.cos(angle) * speed;
                            part._vy = Math.sin(angle) * speed;
                            const hue = Math.floor(Math.random() * 360);
                            part.setBlendColor([0, 0, 0, 0]);
                            part.setColorTone([
                                Math.sin(hue) * 255,
                                Math.sin(hue + 2) * 255,
                                Math.sin(hue + 4) * 255,
                                0,
                            ]);
                            this.addChild(part);
                            this._clearQueue.push(part);
                        }
                        this._gridSprites[i] = null;
                    }
                }
            }
            this._score += clearedCount * 100 + bonusPoints;

            this.updateScoreDisplay();
            this.updateStatsDisplay();
            this.applyGravity();
            this._lastSwappedTile = null;
        }

        applyGravity() {
            const iconRange = Array.from(
                { length: ICON_END - ICON_START + 1 },
                (_, i) => ICON_START + i
            );
            this._fallingSprites = [];
            for (let x = 0; x < GRID_SIZE; x++) {
                let column = [];
                for (let y = 0; y < GRID_SIZE; y++) {
                    const i = y * GRID_SIZE + x;
                    const icon = this._gridData[y][x];
                    const sprite = this._gridSprites[i];
                    if (icon !== null && sprite) {
                        column.push({ icon, sprite });
                    }
                }
                const missing = GRID_SIZE - column.length;
                for (let i = 0; i < missing; i++) {
                    const iconIndex = iconRange[Math.floor(Math.random() * iconRange.length)];
                    const sprite = new Sprite_Icon(iconIndex);
                    sprite.x = this._startX + x * TILE_SIZE;
                    sprite.y = this._startY - TILE_SIZE;
                    this.addChild(sprite);
                    column.unshift({ icon: iconIndex, sprite });
                }
                for (let y = 0; y < GRID_SIZE; y++) {
                    const i = y * GRID_SIZE + x;
                    const entry = column[y];
                    this._gridData[y][x] = entry.icon;
                    this._gridSprites[i] = entry.sprite;
                    this._fallingSprites.push({ sprite: entry.sprite, toY: y });
                }
            }
        }

        startSwapAnimation(a, b) {
            const i1 = a.y * GRID_SIZE + a.x;
            const i2 = b.y * GRID_SIZE + b.x;

            const s1 = this._gridSprites[i1];
            const s2 = this._gridSprites[i2];

            const duration = 15;
            this._animating = true;
            this._swapData = { s1, s2, a, b, frame: 0, duration };
        }

        updateParticles() {
            for (const sprite of this._clearQueue) {
                sprite.x += sprite._vx || 0;
                sprite.y += sprite._vy || 0;
                if (!sprite._vx) {
                    sprite._lifespan = (sprite._lifespan || 60) - 1;
                    if (sprite._lifespan <= 30) sprite.opacity -= 5;
                }
                if (sprite._fade !== undefined) {
                    sprite._fade--;
                    sprite.opacity -= 6;
                } else {
                    sprite.opacity -= 10;
                }
                if (sprite.opacity <= 0) {
                    this.removeChild(sprite);
                }
            }
            this._clearQueue = this._clearQueue.filter(s => s.opacity > 0);
        }

        createScoreDisplay() {
            const bmpWidth = 400;
            const bmpHeight = 40;

            // Score display
            this._scoreBitmap = new Bitmap(bmpWidth, bmpHeight);
            this._scoreBitmap.fontSize = 36;
            this._scoreBitmap.fontFace = $gameSystem.mainFontFace();
            this._scoreSprite = new Sprite(this._scoreBitmap);
            this._scoreSprite.anchor.x = 0.5;
            this._scoreSprite.x = Graphics.width / 2;
            this._scoreSprite.y = this._startY - 80;
            this.addChild(this._scoreSprite);

            // Combo display
            this._comboBitmap = new Bitmap(bmpWidth, bmpHeight);
            this._comboBitmap.fontSize = 36;
            this._comboBitmap.fontFace = $gameSystem.mainFontFace();
            this._comboSprite = new Sprite(this._comboBitmap);
            this._comboSprite.anchor.x = 0.5;
            this._comboSprite.x = Graphics.width / 2;
            this._comboSprite.y = this._startY + GRID_SIZE * TILE_SIZE + 30;
            this.addChild(this._comboSprite);

            // Tier popup sprite (created once)
            this._tierPopup = new Sprite();
            this._tierPopup.anchor.set(0.5);
            this._tierPopup.opacity = 0;
            this._tierPopup.x = Graphics.width / 2;
            this._tierPopup.y = this._startY + (GRID_SIZE * TILE_SIZE) / 2 - 40;
            this._tierPopupTimer = 0;
            this.addChildAt(this._tierPopup, 1); // Ensure it appears behind icons

            this.updateScoreDisplay();
        }

        updateScoreDisplay() {
            if (this._combo > this._maxCombo) this._maxCombo = this._combo;
            if (this._scoreBitmap) {
                this._scoreBitmap.clear();
                this._scoreBitmap.textColor = '#ffffff';
                this._scoreBitmap.drawText(
                    `Score: ${this._score}`,
                    0,
                    0,
                    this._scoreBitmap.width,
                    this._scoreBitmap.height,
                    'center'
                );
            }
            if (this._comboBitmap) {
                const dynamicFontSize = Math.min(36 + this._combo * 1.5, 50);
                this._comboBitmap.fontSize = dynamicFontSize;
                this._comboBitmap.clear();
                let comboColor = '#ffffff';
                if (this._combo >= 15) comboColor = '#ff3333';
                else if (this._combo >= 10) comboColor = '#ff9933';
                else if (this._combo >= 5) comboColor = '#ffff66';
                this._comboBitmap.textColor = comboColor;
                this._comboBitmap.drawText(
                    `Combo: ${this._combo}`,
                    0,
                    0,
                    this._comboBitmap.width,
                    this._comboBitmap.height,
                    'center'
                );
                this._comboSprite._glowAlpha = 128;

                if (this._tierPopup) {
                    let imageName = null;
                    if (this._combo % 5 === 0) {
                        if (this._combo >= 15) imageName = 'dance_perfect';
                        else if (this._combo >= 10) imageName = 'dance_great';
                        else if (this._combo >= 5) imageName = 'dance_good';
                    }

                    if (imageName) {
                        // Subtle screen shake
                        $gameScreen.startShake(5, 5, 20);

                        // Subtle white flash
                        this._flashSprite = new ScreenSprite();
                        this._flashSprite.setColor(255, 255, 255);
                        this._flashSprite.opacity = 100;
                        this.addChild(this._flashSprite);
                        this._flashFade = 10;
                        this._tierPopup.bitmap = ImageManager.loadPicture(imageName);
                        this._tierPopup.opacity = 255;
                        this._tierPopup.scale.set(2, 2);
                        this._tierPopupTimer = 90;
                        let tierPitch = 100 + (this._combo - 5) * 5;
                        tierPitch = Math.min(tierPitch, 150);
                        AudioManager.playSe({
                            name: 'Level Up 2',
                            pan: 0,
                            pitch: tierPitch,
                            volume: 50,
                        });

                        // Rainbow particle explosion
                        const particleBitmap = new Bitmap(12, 12);
                        particleBitmap.drawCircle(6, 6, 6, 'white');
                        for (let i = 0; i < 180; i++) {
                            const part = new Sprite(particleBitmap);
                            part.x = this._tierPopup.x;
                            part.y = this._tierPopup.y;
                            part.opacity = 255;
                            part.scale.set(0.6 + Math.random() * 0.4);
                            const angle = Math.random() * 2 * Math.PI;
                            const speed = 2 + Math.random() * 12;
                            part._vx = Math.cos(angle) * speed;
                            part._vy = Math.sin(angle) * speed;
                            const hue = Math.floor(Math.random() * 360);
                            part.setBlendColor([0, 0, 0, 0]);
                            part.setColorTone([
                                Math.sin(hue) * 255,
                                Math.sin(hue + 2) * 255,
                                Math.sin(hue + 4) * 255,
                                0,
                            ]);
                            this.addChild(part);
                            this._clearQueue.push(part);
                        }
                        this._tierPopupTimer = 90;
                    }
                }
            }
        }

        updateComboGlow() {
            if (this._comboSprite && this._comboSprite._glowAlpha > 0) {
                const current = this._comboSprite._glowAlpha;
                this._comboSprite.setBlendColor([255, 255, 0, current]);
                this._comboSprite._glowAlpha = Math.max(0, current - 8);
            }
        }

        updateAnimations() {
            if (this._fallingSprites.length > 0) return;
            if (this._swapData) {
                const { s1, s2, a, b, frame, duration } = this._swapData;
                const progress = frame / duration;

                s1.x = this._startX + (a.x + (b.x - a.x) * progress) * TILE_SIZE;
                s1.y = this._startY + (a.y + (b.y - a.y) * progress) * TILE_SIZE;
                s2.x = this._startX + (b.x + (a.x - b.x) * progress) * TILE_SIZE;
                s2.y = this._startY + (b.y + (a.y - b.y) * progress) * TILE_SIZE;

                this._swapData.frame++;

                if (this._swapData.frame > duration) {
                    const temp = this._gridData[a.y][a.x];
                    this._gridData[a.y][a.x] = this._gridData[b.y][b.x];
                    this._gridData[b.y][b.x] = temp;

                    const i1 = a.y * GRID_SIZE + a.x;
                    const i2 = b.y * GRID_SIZE + b.x;
                    const tempSprite = this._gridSprites[i1];
                    this._gridSprites[i1] = this._gridSprites[i2];
                    this._gridSprites[i2] = tempSprite;

                    this._swapData = null;
                    this._animating = false;

                    const aIcon = this._gridData[a.y][a.x];
                    const bIcon = this._gridData[b.y][b.x];

                    // Always check and clear normal matches first
                    const matched = this.findAllMatches();
                    this.clearMatches(matched, b);

                    // Then trigger special tile effects if present
                    if ([aIcon, bIcon].includes(HOLY_CROSS_ICON)) {
                        this.activateHolyCross(aIcon === HOLY_CROSS_ICON ? a : b);
                    } else if ([aIcon, bIcon].includes(DARK_VORTEX_ICON)) {
                        const vortexPos = aIcon === DARK_VORTEX_ICON ? a : b;
                        const otherIcon = aIcon === DARK_VORTEX_ICON ? bIcon : aIcon;
                        this.activateDarkVortex(vortexPos, otherIcon);
                    }
                    this._animating = false;
                }
            }
        }
    }

    class Sprite_Icon extends Sprite {
        constructor(iconIndex) {
            super();
            this.bitmap = ImageManager.loadSystem('IconSet');
            this.setIconIndex(iconIndex);
        }

        setIconIndex(index) {
            this._iconIndex = index;
            const pw = ImageManager.iconWidth;
            const ph = ImageManager.iconHeight;
            const sx = (index % 16) * pw;
            const sy = Math.floor(index / 16) * ph;
            this.setFrame(sx, sy, pw, ph);
        }
    }

    Scene_Match3.prototype.createStatsDisplay = function () {
        this._statsBitmap = new Bitmap(300, 80);
        this._statsBitmap.fontSize = 24;
        this._statsBitmap.fontFace = $gameSystem.mainFontFace();
        this._statsSprite = new Sprite(this._statsBitmap);
        this._statsSprite.x = Graphics.width - 320;
        this._statsSprite.y = 20;
        this.addChild(this._statsSprite);
    };

    Scene_Match3.prototype.updateStatsDisplay = function () {
        if (!this._statsBitmap) return;
        this._statsBitmap.clear();
        this._statsBitmap.textColor = '#ffffff';
        this._statsBitmap.drawText(`Turns: ${this._turns}`, 0, 0, 300, 24, 'left');
        this._statsBitmap.drawText(`Max Combo: ${this._maxCombo}`, 0, 24, 300, 24, 'left');
        this._statsBitmap.drawText(`Goal: ${this._goalScore}`, 0, 48, 300, 24, 'left');
    };

    window.Scene_Match3 = Scene_Match3;
})();
