//=============================================================================
// VisuStella MZ - Equip Passive System
// VisuMZ_2_EquipPassiveSys.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_2_EquipPassiveSys = true;
var VisuMZ = VisuMZ || {};
VisuMZ.EquipPassiveSys = VisuMZ.EquipPassiveSys || {};
VisuMZ.EquipPassiveSys.version = 1.02;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 2] [Version 1.02] [EquipPassiveSys]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Equip_Passive_System_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_SkillsStatesCore
 * @orderAfter VisuMZ_1_SkillsStatesCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Actors can now equip passive states to further enhance their battle
 * potential. With how flexible states are, equippable passive states can boost
 * actors in numerous ways. Equippable passive states can be learned in many
 * different ways and add further customization potential to your actors.
 *
 * Features include all (but not limited to) the following:
 *
 * * Passive states can be accessed and equipped from the skill scene.
 * * Passive states are limited to the passive capacity that an actor has and
 *   the amount of capacity that passive state costs.
 * * As passive are states at the fundamental level, they take on all the
 *   advantages and traits that states have like motions and overlays.
 * * Dictate which passives an actor can learn through notetags.
 * * Some passives can be linked to when skills are learned.
 * * Passives can have a variety of conditions before they are learned.
 * * Branch out passives so that when they're learned, more passives can be
 *   unlocked for that actor.
 * * Some of these conditions include leveling, winning battles, escaping,
 *   being afflicted by states, attacking a certain amount of times, and more!
 * * Globally learn and remove passive states across all actors.
 * * Optionally combines with the Skill Learn System to allow actors to learn
 *   equippable passives using the Skill Learn System's notetags and AP/SP.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_0_CoreEngine
 * * VisuMZ_1_SkillsStatesCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 2 ------
 *
 * This plugin is a Tier 2 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * How Equippable Passives Work
 * ============================================================================
 *
 * This section explains how Equippable Passives work in detail.
 *
 * ---
 *
 * States at the Core
 *
 * Equippable Passives are states that actors can toggle ON/OFF as long as the
 * actors have enough "Passive Capacity" to support the passives. As these are
 * states, they have all of the traits that states have access to in addition
 * to their motion and overlay related aspects.
 *
 * ---
 *
 * Unlock Conditions
 *
 * If an actor has unlearned Equippable Passives listed, that actor can attempt
 * to meet the conditions of those passive states and learn them. Actors will
 * not be able to learn passive states that aren't listed, regardless of the
 * actor fulfilling the unlock conditions for the unlisted Equippable Passives.
 *
 * Unlock conditions can range from things like fighting 5 battles since the
 * time the Equippable Passive has been listed to things like casting 8 magical
 * skills. There is a huge list of unlock conditions that can be used found in
 * the notetags section.
 *
 * ---
 *
 * Skill Learn System
 *
 * If unlock conditions are not your thing, actors can bypass all of them and
 * just straight up pay for them in the Skill Learn System as long as the
 * Equippable Passives are listed there. Naturally, this will require VisuMZ's
 * Skill Learn System plugin installed for this integration to work out.
 *
 * Actors can pay for Equippable Passives in the Skill Learn System using AP,
 * CP, JP, SP, items, weapons, armors, just about anything that normal skills
 * can be used to pay with.
 *
 * Once again, Equippable Passives through the Skill Learn System will not
 * require unlock conditions to be fulfilled in order to be bought and learned.
 * This functions as an alternative way for players to acquire Equippable
 * Passives if they're not a fan of the unlock system.
 *
 * This does not mean that all Equippable Passives have to be placed through
 * the Skill Learn System while being condition unlockable or vice versa. You
 * can have some passives exclusive to the Skill Learn System while others are
 * exclusive to the unlocking mechanisms at play.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_1_ElementStatusCore
 *
 * Certain notetags will become available if the VisuStella MZ Elements and
 * Status Menu Core plugin is installed in addition to this plugin. This
 * notetag is the <Equip Passive Learn Defeat name Trait: x> notetag.
 *
 * ---
 *
 * VisuMZ_2_SkillLearnSystem
 *
 * If you have the VisuStella MZ Skill Learn System installed in addition to
 * this plugin, you can integrate the passive learning aspect into the skill
 * learn system itself and pay for passives using AP, SP (as well as CP and JP
 * if the VisuStella MZ Class Change System is installed).
 *
 * Unlock conditions do NOT need to be fulfilled if passives are learned
 * through the Skill Learn System. This is because the normal unlocking passive
 * conditions are made specifically for learning passives organically through
 * playing the game while the Skill Learn System allows for players to
 * carefully choose their options and buy them on the spot.
 *
 * Passives placed through the Skill Learn System will use a different set of
 * notetags which will be listed in the notetags section of this plugin.
 *
 * Unlearned passives listed in the Skill Learn System will not necessarily
 * appear in the unlearned passives list of the Passives window unless you
 * have the organic notetags used to list them there.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Setup-Related Notetags ===
 *
 * ---
 *
 * <Equip Passive Cost: x>
 *
 * - Used for: State Notetags
 * - Determines the Passive Capacity cost of the Equippable Passive.
 * - Replace 'x' with a number representing the Passive Capacity cost.
 * - If this notetag is not used, the Passive Capacity cost will default to
 *   the setting found in the Plugin Parameters.
 *
 * ---
 *
 * <Learnable Equip Passive: id>
 * <Learnable Equip Passives: id, id, id>
 *
 * <Learnable Equip Passive: name>
 * <Learnable Equip Passives: name, name, name>
 *
 * <Learnable Equip Passives>
 *  name
 *  name
 *  name
 * </Learnable Equip Passives>
 *
 * - Used for: Actor, Class Notetags
 * - Determines which Equippable Passives that actors and classes can learn.
 * - Replace 'id' with a number representing the ID of the passive state.
 * - Replace 'name' with the name of the passive state.
 * - This does NOT put the passives in the Skill Learn System. This only adds
 *   them to the Passives command where they are learnable through meeting any
 *   necessary unlock conditions.
 *
 * ---
 *
 * <Equip Passive Icon: x>
 * <Equip Passive Name: name>
 *
 * - Used for: State Notetags
 * - Changes the icon and name of the Equippable Passive to something different
 *   than how it is listed in the database or found in other menus.
 * - This can be used for states that you may want to hide icons for so that
 *   they do not show up in the states list when the passive is equipped.
 * - If these notetags are not used, the passive will refer to the state's
 *   original icon and name.
 * - Replace 'x' with a number representing the icon index you want.
 * - Replace 'name' with the text of the name you want used.
 *
 * ---
 *
 * <Learned Equip Passive: id>
 * <Learned Equip Passives: id, id, id>
 *
 * <Learned Equip Passive: name>
 * <Learned Equip Passives: name, name, name>
 *
 * - Used for: Actor Notetags
 * - Allows this actor to already have learned this Equippable Passive by
 *   default so that it is available when the actor joins your party.
 * - These Equippable Passives do not have to be listed through the learnable
 *   notetags.
 * - Replace 'id' with a number representing the ID of the passive state.
 * - Replace 'name' with the name of the passive state.
 *
 * ---
 *
 * <Already Equip Passive: id>
 * <Already Equip Passives: id, id, id>
 *
 * <Already Equip Passive: name>
 * <Already Equip Passives: name, name, name>
 *
 * - Used for: Actor Notetags
 * - Allows this actor to already have learned this Equippable Passive and has
 *   it equipped by default so that it is available when the actor joins your
 *   party.
 * - These Equippable Passives do not have to be listed through the learnable
 *   notetags.
 * - Replace 'id' with a number representing the ID of the passive state.
 * - Replace 'name' with the name of the passive state.
 *
 * ---
 *
 * <Branch Learn Equip Passive: id>
 * <Branch Learn Equip Passives: id, id, id>
 *
 * <Branch Learn Equip Passive: name>
 * <Branch Learn Equip Passives: name, name, name>
 *
 * - Used for: State Notetags
 * - When this Equippable Passive is learned, also learn target passive(s).
 * - Target passive(s) does not have to be learnable listed.
 * - Replace 'id' with a number representing the ID of the target passive.
 * - Replace 'name' with the name of the target passive.
 *
 * ---
 *
 * <Branch Learnable Equip Passive: id>
 * <Branch Learnable Equip Passives: id, id, id>
 *
 * <Branch Learnable Equip Passive: name>
 * <Branch Learnable Equip Passives: name, name, name>
 *
 * - Used for: State Notetags
 * - When this Equippable Passive is learned, add target passive(s) to the
 *   actor's learnable passive list.
 * - Replace 'id' with a number representing the ID of the target passive.
 * - Replace 'name' with the name of the target passive.
 *
 * ---
 *
 * <Link Learn Equip Passive: id>
 * <Link Learn Equip Passives: id, id, id>
 *
 * <Link Learn Equip Passive: name>
 * <Link Learn Equip Passives: name, name, name>
 *
 * - Used for: Skill Notetags
 * - When this skill is learned, also learn target passive(s).
 * - Target passive(s) does not have to be learnable listed.
 * - Replace 'id' with a number representing the ID of the target passive.
 * - Replace 'name' with the name of the target passive.
 *
 * ---
 *
 * <Link Learnable Equip Passive: id>
 * <Link Learnable Equip Passives: id, id, id>
 *
 * <Link Learnable Equip Passive: name>
 * <Link Learnable Equip Passives: name, name, name>
 *
 * - Used for: Skill Notetags
 * - When this skill is learned, add target passive(s) to the actor's learnable
 *   passive list.
 * - Replace 'id' with a number representing the ID of the target passive.
 * - Replace 'name' with the name of the target passive.
 *
 * ---
 *
 * <Help Description>
 *  text
 *  text
 * </Help Description>
 *
 * - Used for: State Notetags
 * - Assigns a help description for the passive state.
 * - Replace 'text' with text you want displayed for the help window.
 * - This best works with one line to best fit other plugins.
 *
 * ---
 *
 * === Hiding-Related Notetags ===
 *
 * ---
 *
 * <Hide If Not Learned Equip Passive>
 *
 * - Used for: State Notetags
 * - Bypasses Passives listing and hides the passive state regardless of the
 *   Plugin Parameter settings.
 *
 * ---
 *
 * <Hide If Learned Equip Passive: id>
 * <Hide If Learned Equip Passive: name>
 *
 * <Hide If Learned All Equip Passives: id, id, id>
 * <Hide If Learned All Equip Passives: name, name, name>
 *
 * <Hide If Learned Any Equip Passives: id, id, id>
 * <Hide If Learned Any Equip Passives: name, name, name>
 *
 * - Used for: State Notetags
 * - Hides the passive state from the Passives listing based on whether or not
 *   other passives are learned.
 * - Replace 'id' with a number representing the ID of the passive state.
 * - Replace 'name' with the name of the passive state.
 * - The 'All' notetag variant requires all of the listed passives to be
 *   learned in order for this passive to be hidden.
 * - The 'Any' notetag variant requires only one of the listed passives to be
 *   learned in order for this passive to be hidden.
 *
 * ---
 *
 * === Masking-Related Notetags ===
 *
 * ---
 *
 * <Mask If Not Learned Equip Passive>
 * <No Mask If Not Learned Equip Passive>
 *
 * - Used for: State Notetags
 * - Bypasses the masking settings determined in the Plugin Parameters to mask
 *   or not mask the Equippable Passive if the passive is not learned.
 *
 * ---
 *
 * <Equip Passive Mask Name: name>
 *
 * - Used for: State Notetags
 * - Instead of displaying ?'s for the mask name, this allows you to insert
 *   custom mask names instead.
 * - Replace 'name' with the text you want for the mask name.
 *
 * ---
 *
 * === Unlock Conditions-Related Notetags ===
 *
 * ---
 *
 * <Equip Passive Learn Condition Text>
 *  text
 *  text
 * </Equip Passive Learn Condition Text>
 *
 * - Used for: State Notetags
 * - Assigns text to the Equip Passives learn unlock conditions.
 * - Replace 'text' with text you want displayed when this passive is selected
 *   and has not yet been learned.
 * - If this notetag is not used, the help description will default be
 *   automatically constructed through the plugin using Plugin Parameters.
 *
 * ---
 *
 * <Equip Passive Learn Level: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must reach level 'x'.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the level the actor must reach.
 *
 * ---
 *
 * <Equip Passive Learn Battles: x>
 * <Equip Passive Learn Victories: x>
 * <Equip Passive Learn Escapes: x>
 * <Equip Passive Learn Defeats: x>
 *
 * - Used for: State Notetags
 * - Adds a battle result-related unlock condition that the actor must fulfill
 *   in order to learn this Equippable Passive.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of battle results that
 *   must be fulfilled.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous battle results before listing.
 * - The 'Battles' notetag variant requires participating in any battles.
 * - The 'Victories' notetag variant requires winning battles.
 * - The 'Escapes' notetag variant requires successfully escaping battles.
 * - The 'Defeats' notetag variant requires losing battles.
 *
 * ---
 *
 * <Equip Passive Learn Attack Times: x>
 * <Equip Passive Learn Guard Times: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must attack or guard 'x' times.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of times the actor must
 *   attack or guard.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 *
 * ---
 *
 * <Equip Passive Learn Use Skills: x>
 * <Equip Passive Learn Use Physical Skills: x>
 * <Equip Passive Learn Use Magical Skills: x>
 * <Equip Passive Learn Use Certain Hit Skills: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must use 'x' skills.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of times the actor must
 *   use skills.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Skills' notetag variant allows any kind of skill usage.
 * - The 'Physical Skills' notetag variant requires physical hit skills.
 * - The 'Magical Skills' notetag variant requires magical hit skills.
 * - The 'Certain Hit Skills' notetag variant requires certain hit skills.
 *
 * ---
 *
 * <Equip Passive Learn SType id: x>
 * <Equip Passive Learn SType name: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must use 'x' skills that belong to
 *   a specific skill type.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'id' with a number representing the needed skill type's ID number.
 * - Replace 'name' with text representing the needed skill type's name.
 * - Replace 'x' with a number representing the number of times the actor must
 *   use skills belonging to the needed skill type.
 *
 * ---
 *
 * <Equip Passive Learn Use Items: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must use 'x' items.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of times the actor must
 *   use items.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - Any kind of item can be used.
 *
 * ---
 *
 * <Equip Passive Learn Inflict Critical Times: x>
 * <Equip Passive Learn Receive Critical Times: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must inflict or receive 'x'
 *   critical hits from actions.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of times the actor must
 *   inflict or receive critical hits.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Inflict' notetag variant requires the critical hit to be caused by
 *   the actor.
 * - The 'Receive' notetag variant requires the critical hit to be caused
 *   against the actor.
 *
 * ---
 *
 * <Equip Passive Learn Miss Times: x>
 * <Equip Passive Learn Evade Times: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must miss or evade 'x' actions.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of times the actor must
 *   miss or evade actions.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Miss' notetag variant requires the actor to miss an action.
 * - The 'Evade' notetag variant requires the actor to evade an action.
 *
 * ---
 *
 * <Equip Passive Learn Inflict Element id Damage: x>
 * <Equip Passive Learn Inflict Element name Damage: x>
 *
 * <Equip Passive Learn Receive Element id Damage: x>
 * <Equip Passive Learn Receive Element name Damage: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must inflict or receive damage
 *   from a specific element 'x' times.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'id' with a number representing the element ID number.
 * - Replace 'name' with text representing the element's name.
 * - Replace 'x' with a number representing the number of times the actor must
 *   inflict or receive damage from a specific element.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Inflict' notetag variant requires the elemental damage to be caused
 *   by the actor.
 * - The 'Receive' notetag variant requires the elemental damage to be caused
 *   against the actor.
 *
 * ---
 *
 * <Equip Passive Learn Inflict State id: x>
 * <Equip Passive Learn Inflict State name: x>
 *
 * <Equip Passive Learn Receive State id: x>
 * <Equip Passive Learn Receive State name: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must inflict or receive a specific
 *   state 'x' times.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'id' with a number representing the state ID number.
 * - Replace 'name' with text representing the state's name.
 * - Replace 'x' with a number representing the number of times the actor must
 *   inflict or receive a specific state.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Inflict' notetag variant requires the target state to be caused
 *   by the actor.
 * - The 'Receive' notetag variant requires the target state to be caused
 *   against the actor.
 *
 * ---
 *
 * <Equip Passive Learn Defeat name Trait: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_1_ElementStatusCore!
 * - Adds an unlock condition that the actor must defeat enemies with specific
 *   trait sets 'x' times.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'name' with text representing the trait set's name.
 * - Replace 'x' with a number representing the number of times the actor must
 *   defeat enemies with the target trait set.
 *
 * ---
 *
 * <Equip Passive Learn Inflict Total Damage: x>
 * <Equip Passive Learn Receive Total Damage: x>
 *
 * <Equip Passive Learn Inflict Total Healing: x>
 * <Equip Passive Learn Receive Total Healing: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must inflict or receive a total
 *   amounts of damage or healing since the time the passive is listed.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the total number of damage and
 *   healing the actor must reach.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Inflict' notetag variant requires the damage/healing to be caused
 *   by the actor.
 * - The 'Receive' notetag variant requires the damage/healing to be caused
 *   against the actor.
 *
 * ---
 *
 * <Equip Passive Learn Kill Count: x>
 * <Equip Passive Learn Death Count: x>
 * <Equip Passive Learn Assist Count: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must fulfill the needed amount of
 *   kills, suffer the amount of deaths, or partake in the number of assists
 *   since the time the passive is listed.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the number of kills, deaths, or
 *   assists the actor must reach.
 *   - The number counter will start from the moment Equippable Passive is
 *     listed in the learnable list.
 *   - This does NOT take into account previous actions before listing.
 * - The 'Kill' notetag variant refers to the number of enemies directly
 *   defeated by the actor (death via slip damage or events do not count).
 * - The 'Death' notetag variant refers to the number of times the actor must
 *   die (ie reaching 0 HP or receiving the Death state).
 * - The 'Assist' notetag variant refers to the number of times the actor is
 *   present in battle when an enemy is defeated and not directly by the actor.
 *
 * ---
 *
 * <Equip Passive Learn Have Gold: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the party must have 'x' gold present at the
 *   moment.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'x' with a number representing the needed amount of gold.
 *
 * ---
 *
 * <Equip Passive Learn Have Item id: x>
 * <Equip Passive Learn Have Item name: x>
 *
 * <Equip Passive Learn Have Weapon id: x>
 * <Equip Passive Learn Have Weapon name: x>
 *
 * <Equip Passive Learn Have Armor id: x>
 * <Equip Passive Learn Have Armor name: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the party must have 'x' quantities of a
 *   specific item, weapon, or armor present at the moment.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'id' with a number representing the ID of the item, weapon, or
 *   armor needed for the passive.
 * - Replace 'name' with text representing the name of the item, weapon, or
 *   armor needed for the passive.
 * - Replace 'x' with a number representing the needed amount of the item,
 *   weapon, or armor.
 *
 * ---
 *
 * <Equip Passive Learn Reach Param name: x>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must have 'x' value for its base
 *   parameter value at the moment.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'name' with 'MaxHP', 'MaxMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI',
 *   or 'LUK' to determine which parameter to be referenced.
 * - Replace 'x' with a number representing the needed parameter value.
 *
 * ---
 *
 * <Equip Passive Learn Reach XParam name: x%>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must have 'x' value for its
 *   X-parameter value at the moment.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'name' with 'HIT', 'EVA', 'CRI', 'CEV', 'MEV', 'MRF', 'CNT',
 *   'HRG', 'MRG', 'TRG' to determine which parameter to be referenced.
 * - Replace 'x' with a number representing the needed parameter percent value.
 *
 * ---
 *
 * <Equip Passive Learn Reach SParam name: x%>
 *
 * - Used for: State Notetags
 * - Adds an unlock condition that the actor must have 'x' value for its
 *   S-parameter value at the moment.
 * - If this Equippable Passive is found in the learnable list and all unlock
 *   conditions in addition to this unlock condition are met, then the actor
 *   will learn the Equippable Passive.
 * - Replace 'name' with 'TGR', 'GRD', 'REC', 'PHA', 'MCR', 'TCR', 'PDR',
 *   'MDR', 'FDR', 'EXR' to determine which parameter to be referenced.
 * - Replace 'x' with a number representing the needed parameter percent value.
 *
 * ---
 *
 * === Skill Learn System Integration-Related Notetags ===
 *
 * ---
 *
 * <Learn Passive: id>
 * <Learn Passives: id, id, id>
 *
 * <Learn Passive: name>
 * <Learn Passives: name, name, name>
 *
 * - Used for: Class Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines what Equippable Passives the class can learn through the
 *   Skill Learn System.
 * - Replace 'id' with a number representing the ID of the passive state that
 *   can be learned through the Skill Learn System menu.
 * - Replace 'name' with the name of the passive state that can be learned
 *   through the Skill Learn System menu.
 * - Multiple entries are permited.
 *
 * ---
 *
 * <Learn Passives>
 *  id
 *  id
 *  id
 *  name
 *  name
 *  name
 * </Learn Passives>
 *
 * - Used for: Class Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines what Equippable Passives the class can learn through the
 *   Skill Learn System.
 * - Replace 'id' with a number representing the ID of the passive state that
 *   can be learned through the Skill Learn System menu.
 * - Replace 'name' with the name of the passive state that can be learned
 *   through the Skill Learn System menu.
 * - Multiple middle entries are permited.
 *
 * ---
 *
 * <Learn AP Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the Ability Point cost needed for an actor to learn the passive
 *   state through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Ability Points needed
 *   to learn this passive state.
 * - If this notetag is not used, then the Ability Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn CP Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Requires VisuMZ_2_ClassChangeSystem!
 * - Determines the Class Point cost needed for an actor to learn the passive
 *   state through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this passive state.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn JP Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Requires VisuMZ_2_ClassChangeSystem!
 * - Determines the Job Point cost needed for an actor to learn the passive
 *   state through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this passive state.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn SP Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the Skill Point cost needed for an actor to learn the passive
 *   state through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of Skill Points needed
 *   to learn this passive state.
 * - If this notetag is not used, then the Skill Point cost will default to
 *   the value found in the settings.
 *
 * ---
 *
 * <Learn Item id Cost: x>
 * <Learn Item name Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the items needed to be consumed for an actor to learn the
 *   passive state through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the item needed to be
 *   consumed.
 * - Replace 'name' with the name of the item needed to be consumed.
 * - Replace 'x' with a number representing the amount of the item needed
 *   to learn this passive state.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Weapon id Cost: x>
 * <Learn Weapon name Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the weapons needed to be consumed for an actor to learn the
 *   passive state through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the weapon needed to be
 *   consumed.
 * - Replace 'name' with the name of the weapon needed to be consumed.
 * - Replace 'x' with a number representing the amount of the weapon needed
 *   to learn this passive state.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Armor id Cost: x>
 * <Learn Armor name Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the armors needed to be consumed for an actor to learn the
 *   passive state through the Skill Learn System.
 * - Replace 'id' with a number representing the ID of the armor needed to be
 *   consumed.
 * - Replace 'name' with the name of the armor needed to be consumed.
 * - Replace 'x' with a number representing the amount of the armor needed
 *   to learn this passive state.
 * - You may insert multiple copies of this notetag.
 *
 * ---
 *
 * <Learn Gold Cost: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Determines the gold cost needed for an actor to learn the passive state
 *   through the Skill Learn System.
 * - Replace 'x' with a number representing the amount of gold needed to learn
 *   this passive state.
 * - If this notetag is not used, then the gold cost will default to the value
 *   found in the settings.
 *
 * ---
 *
 * <Learn Show Level: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Actors must be at least the required level in order for the passive state
 *   to even appear visibly in the Skill Learn System menu.
 * - Replace 'x' with a number representing the required level for the actor
 *   in order for the passive state to visibly appear.
 *
 * ---
 *
 * <Learn Show Skill: id>
 * <Learn Show Skill: name>
 *
 * <Learn Show All Skills: id, id, id>
 * <Learn Show All Skills: name, name, name>
 *
 * <Learn Show Any Skills: id, id, id>
 * <Learn Show Any Skills: name, name, name>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - The actor must have already learned the above skills in order for the
 *   learnable passive state to appear visibly in the Skill Learn System menu.
 * - Replace 'id' with a number representing the ID of the skill required to be
 *   known by the actor in order to appear visibly in the menu.
 * - Replace 'name' with the name of the skill required to be known by the
 *   actor in order to appear visibly in the menu.
 * - The 'All' notetag variant requires all of the listed skills to be known
 *   before the learnable passive state will appear visibly in the menu.
 * - The 'Any' notetag variant requires any of the listed skills to be known
 *   before the learnable passive state will appear visibly in the menu.
 *
 * ---
 *
 * <Learn Show Switch: x>
 *
 * <Learn Show All Switches: x, x, x>
 *
 * <Learn Show Any Switches: x, x, x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - The switches must be in the ON position in order for the learnable passive
 *   state to appear visibly in the Skill Learn System menu.
 * - Replace 'x' with a number representing the ID of the switch required to be
 *   in the ON position in order to appear visibly in the menu.
 * - The 'All' notetag variant requires all of the switches to be in the ON
 *   position before the learnable passive state will appear visibly in the
 *   menu.
 * - The 'Any' notetag variant requires any of the switches to be in the ON
 *   position before the learnable passive state will appear visibly in the
 *   menu.
 *
 * ---
 *
 * <Learn Require Level: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Actors must be at least the required level in order for the passive state
 *   to be enabled in the Skill Learn System menu.
 * - Replace 'x' with a number representing the required level for the actor
 *   in order for the passive state to visibly appear.
 *
 * ---
 *
 * <Learn Require Skill: id>
 * <Learn Require Skill: name>
 *
 * <Learn Require All Skills: id, id, id>
 * <Learn Require All Skills: name, name, name>
 *
 * <Learn Require Any Skills: id, id, id>
 * <Learn Require Any Skills: name, name, name>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - The actor must have already learned the above skills in order for the
 *   learnable passive state to be enabled in the Skill Learn System menu.
 * - Replace 'id' with a number representing the ID of the skill required to be
 *   known by the actor in order to be enabled in the menu.
 * - Replace 'name' with the name of the skill required to be known by the
 *   actor in order to be enabled in the menu.
 * - The 'All' notetag variant requires all of the listed skills to be known
 *   before the learnable passive state will be enabled in the menu.
 * - The 'Any' notetag variant requires any of the listed skills to be known
 *   before the learnable passive state will be enabled in the menu.
 *
 * ---
 *
 * <Learn Require Switch: x>
 *
 * <Learn Require All Switches: x, x, x>
 *
 * <Learn Require Any Switches: x, x, x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - The switches must be in the ON position in order for the learnable passive
 *   state to be enabled in the Skill Learn System menu.
 * - Replace 'x' with a number representing the ID of the switch required to be
 *   in the ON position in order to be enabled in the menu.
 * - The 'All' notetag variant requires all of the switches to be in the ON
 *   position before the learnable passive state will be enabled in the menu.
 * - The 'Any' notetag variant requires any of the switches to be in the ON
 *   position before the learnable passive state will be enabled in the menu.
 *
 * ---
 *
 * <Learn Skill Animation: id>
 * <Learn Skill Animation: id, id, id>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Plays the animation(s) when this passive state is learned through the
 *   Skill Learn System's menu.
 * - This will override the default animation settings found in the plugin
 *   parameters and use the unique one set through notetags instead.
 * - Replace 'id' with the ID of the animation you wish to play.
 * - If multiple ID's are found, then each animation will play one by one in
 *   the order they are listed.
 *
 * ---
 *
 * <Learn Skill Fade Speed: x>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - This determines the speed at which the passive state's icon fades in
 *   during the skill learning animation.
 * - Replace 'x' with a number value to determine how fast the icon fades in.
 * - Use lower numbers for slower fade speeds and higher numbers for faster
 *   fade speeds.
 *
 * ---
 *
 * <Learn Skill Picture: filename>
 * <Picture: filename>
 *
 * - Used for: State Notetags
 * - Requires VisuMZ_2_SkillLearnSystem!
 * - Uses a picture from your project's /img/pictures/ folder instead of the
 *   passive state's icon during learning instead.
 * - Replace 'filename' with the filename of the image.
 *   - Do not include the file extension.
 * - Scaling will not apply to the picture.
 * - Use the <Picture: filename> version for any other plugins that may be
 *   using this as an image outside of learning skills, too.
 * - The size used for the image will vary based on your game's resolution.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Actor Plugin Commands ===
 *
 * ---
 *
 * Actor: Learn Equippable Passive
 * - Target actor(s) learns equippable passive state(s).
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to add as an equippable passive state.
 *
 *   Show Text Popup?:
 *   - Shows text popup of actor(s) learning the passive state?
 *
 * ---
 *
 * Actor: Forget Equippable Passive
 * - Target actor(s) forgets equippable passive state(s).
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to forget as an equippable passive state.
 *
 * ---
 *
 * Actor: Add Unlearned Equippable Passive
 * - Gives target actor(s) the ability to learn target passive state(s).
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to add as an unlearned equippable passive
 *     state.
 *
 * ---
 *
 * Actor: Remove Unlearned Equippable Passive
 * - Removes target actor(s) the ability to learn target passive state(s).
 *
 *   Actor ID(s):
 *   - Select which Actor ID(s) to affect.
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to remove unlearned equippable passive state.
 *
 * ---
 *
 * === Global Plugin Commands ===
 *
 * ---
 *
 * Global: Learn Equippable Passive
 * - All actors learn equippable passive state(s).
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to add as an equippable passive state.
 *
 *   Show Text Popup?:
 *   - Shows text popup of party learning the passive state?
 *
 * ---
 *
 * Global: Forget Equippable Passive
 * - All actors forget equippable passive state(s).
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to forget as an equippable passive state.
 *
 * ---
 *
 * Global: Add Unlearned Equippable Passive
 * - Gives all actors the ability to learn target passive state(s).
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to add as an unlearned equippable passive
 *     state.
 *
 * ---
 *
 * Global: Remove Unlearned Equippable Passive
 * - Removes from all actors the ability to learn target passive state(s).
 *
 *   Passive State ID(s):
 *   - Select which State ID(s) to remove unlearned equippable passive state.
 *
 * ---
 *
 * === System Plugin Commands ===
 *
 * ---
 *
 * System: Show Passives in Skill Scene?
 * - Shows/hides Passives command inside the skill scene.
 *
 *   Show/Hide?:
 *   - Shows/hides Passives command inside the skill scene.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * General settings for the Equip Passive System.
 *
 * ---
 *
 * General Settings:
 *
 *   Default Show Command:
 *   - Shows Passive Command by default?
 *
 *   Auto-Equip on Learn:
 *   - Automatically equips newly learned Passives.
 *
 *   Text Popup on Learn:
 *   - Produce a text popup when a Passive is learned?
 *
 *     Text Popup Format:
 *     - Text format used for text popup.
 *     - %1 - Actor, %2 - Passive, %3 - Icon
 *
 * ---
 *
 * Capacity Settings:
 *
 *   Capacity Formula:
 *   - What is the formula used to determine current max capacity?
 *
 *   Default Capacity Cost:
 *   - What is the default capacity cost of equipping a Passive?
 *
 *   Minimum Capacity Cap:
 *   - What is the minimum capacity value?
 *
 *   Maximum Capacity Cap:
 *   - What is the maximum capacity value?
 *
 *   Check Over-Capacity:
 *   - Checks over-capacity when EXP changes.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Vocabulary Settings
 * ============================================================================
 *
 * These settings let you adjust the text displayed for this plugin.
 *
 * ---
 *
 * Scene_Skill:
 *
 *   Command Name:
 *   - Text used for the Passives Command.
 *
 *     Command Icon:
 *     - Icon used for the Passives command and for any passives that are
 *       displayed without any icon.
 *
 *   Capacity Text:
 *   - Text used for Passives Capacity.
 *
 *     Capacity Icon:
 *     - Icon used to represent Passives Capacity when displayed as a limited
 *       resource.
 *
 *     Capacity Format:
 *     - Text format used to representing Capacity.
 *     - %1 - Current, %2 - Max, %3 - Icon
 *
 *   Cost Format:
 *   - Text format used for Capacity Cost.
 *   - %1 - Cost, %2 - Icon
 *
 *     Unlearned Text:
 *     - Text displayed instead of cost for unlearned Passives.
 *
 * ---
 *
 * Shop Status Window:
 *
 *   Shop Status Text:
 *   - Text used to representing Passives in shop status.
 *   - Requires VisuMZ_1_ItemsEquipsCore!
 *
 * ---
 *
 * Help Window:
 *
 *   Description Format:
 *   - Text format used to create help descriptions.
 *   - %1 - Unlocking Conditions
 *
 *   Word Wrap?:
 *   - Apply word wrap to unlock conditions?
 *   - Requires VisuMZ_1_MessageCore!
 *
 *   Spacing?:
 *   - Add spacing between conditions?
 *
 *   Spacer:
 *   - Text inserted between conditions.
 *
 *   Empty Descriptions:
 *   - Text used when no condition text is made.
 *
 * ---
 *
 * Unlock Condition Text:
 *
 *   Condition Met Color:
 *   - Use text colors from the Window Skin only.
 *
 *   Progress Format:
 *   - Text format used to indicate progress amount.
 *   - %1 - Progress Text
 *
 *     Fraction Format:
 *     - Text format used for progress fraction.
 *     - %1 - Current, %2 - Goal
 *
 *     Percent Format:
 *     - Text format used for percentile value.
 *     - %1 - Percent
 *
 *     Length Limit:
 *     - What is the character limit before a percentage is used instead
 *       fractions for progress text?
 *
 *     Complete:
 *     - Progress text used when unlock condition is fulfilled.
 *
 *   Level Format:
 *   - Text format used for level conditions.
 *   - %1 - Level, %2 - Progress
 *
 *   Battle Format:
 *   - Text format used for fought battles.
 *   * %1 - Needed, %2 - Progress
 *
 *     Victory Format:
 *     - Text format used for victorious battles.
 *     - %1 - Needed, %2 - Progress
 *
 *     Escape Format:
 *     - Text format used for escaped battles.
 *     - %1 - Needed, %2 - Progress
 *
 *     Defeat Format:
 *     - Text format used for lost battles.
 *     - %1 - Needed, %2 - Progress
 *
 *   Attack Format:
 *   - Text format used for attack times.
 *   - %1 - Needed, %2 - Progress
 *
 *     Guard Format:
 *     - Text format used for guard times.
 *     - %1 - Needed, %2 - Progress
 *
 *   Skill Format:
 *   - Text format used for skill times.
 *   - %1 - Needed, %2 - Progress
 *
 *     Physical Skills:
 *     - Text format used for physical skills.
 *     - %1 - Needed, %2 - Progress
 *
 *     Magical Skills:
 *     - Text format used for magical skills.
 *     - %1 - Needed, %2 - Progress
 *
 *     Certain Hit Skills:
 *     - Text format used for certain hit skills.
 *     - %1 - Needed, %2 - Progress
 *
 *   Item Format:
 *   - Text format used for item uses.
 *   - %1 - Needed, %2 - Progress
 *
 *   Deal Criticals:
 *   - Text format used for dealing criticals.
 *   - %1 - Needed, %2 - Progress
 *
 *     Take Criticals:
 *     - Text format used for taking criticals.
 *     - %1 - Needed, %2 - Progress
 *
 *   Miss Format:
 *   - Text format for missing attacks.
 *   - %1 - Needed, %2 - Progress
 *
 *     Evade Format:
 *     - Text format for evading attacks.
 *     - %1 - Needed, %2 - Progress
 *
 *   SType Use:
 *   - Text format for using SType Skills.
 *   - %1 - Needed, %2 - Progress, %3 - Type Text
 *
 *   Deal Element DMG:
 *   - Text format used for inflicting element damage.
 *   - %1 - Needed, %2 - Progress, %3 - Element
 *
 *     Take Element DMG:
 *     - Text format used for receiving element damage.
 *     - %1 - Needed, %2 - Progress, %3 - Element
 *
 *   Deal State:
 *   - Text format used for inflicting states.
 *   - %1 - Needed, %2 - Progress, %3 - State
 *
 *     Take State:
 *     - Text format used for receiving states.
 *     - %1 - Needed, %2 - Progress, %3 - State
 *
 *   Trait Slayer:
 *   - Text format for slaying trait types.
 *   - %1 - Needed, %2 - Progress, %3 - Type Text
 *
 *   Total Damage Dealt:
 *   - Text format for total damage dealt.
 *   - %1 - Needed, %2 - Progress
 *
 *     Total Damage Taken:
 *     - Text format for total damage received.
 *     - %1 - Needed, %2 - Progress
 *
 *   Total Healing Dealt:
 *   - Text format for total healing given.
 *   - %1 - Needed, %2 - Progress
 *
 *     Total Healing Taken:
 *     - Text format for total healing taken.
 *     - %1 - Needed, %2 - Progress
 *
 *   Kills Format:
 *   - Text format for kills performed.
 *   - %1 - Needed, %2 - Progress
 *
 *     Deaths Format:
 *     - Text format for deaths in battle.
 *     - %1 - Needed, %2 - Progress
 *
 *   Assists Format:
 *     - Text format for assists made.
 *     - %1 - Needed, %2 - Progress
 *
 *   Reach Gold Total:
 *   - Text format for reaching gold quantity.
 *   - %1 - Needed, %2 - Progress, %3 - Gold
 *
 *     Reach Item Total:
 *     - Text format for reaching item quantity.
 *     - %1 - Needed, %2 - Progress, %3 - Item
 *
 *     Reach Weapon Total:
 *     - Text format for reaching weapon quantity.
 *     - %1 - Needed, %2 - Progress, %3 - Weapon
 *
 *     Reach Armor Total:
 *     - Text format for reaching armor quantity.
 *     - %1 - Needed, %2 - Progress, %3 - Armor
 *
 *   Reach Base Param:
 *   - Text format for reaching base Param amount.
 *   - %1 - Needed, %2 - Progress, %3 - Param Name
 *
 *     Reach XParam Amount:
 *     - Text format for reaching X Param amount.
 *     - %1 - Needed, %2 - Progress, %3 - XParam Name
 *
 *     Reach SParam Amount:
 *     - Text format for reaching S Param amount.
 *     - %1 - Needed, %2 - Progress, %3 - SParam Name
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Window Settings
 * ============================================================================
 *
 * These settings let you adjust the windows displayed for this plugin.
 *
 * ---
 *
 * Equip Passive List:
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Equipped Color:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Show Capacity Costs?:
 *   - Shows capacity costs on Passives?
 *
 *     Show 0 Costs?:
 *     - Shows capacity costs if they cost 0?
 *
 *     Show 1 Costs?:
 *     - Shows capacity costs if they only cost 1?
 *
 *     Show Cost Numbers?:
 *     - Shows capacity cost values?
 *     - If not, displays multiple icons instead.
 *
 *       Cost Icon Limit:
 *       - If "Show Cost Numbers" is false, this is how many icons can be
 *         displayed max before showing number costs.
 *
 *   Sort Style:
 *   - How do you wish to sort passives by?
 *
 *   Show Unlearned?:
 *   - Shows unlearned passives in the list window?
 *
 *     Separate Unlearned?:
 *     - Separate unlearned passives from learned passives?
 *
 *     Mask Unlearned?:
 *     - Masks unlearned passives in list window?
 *
 *       Mask Icon:
 *       - What is the icon used for masked passives?
 *
 *       Mask Character:
 *       - Text used for masking per individual character.
 *
 *       Italics?:
 *     - Use italics for masked names?
 *
 * ---
 *
 * Passive Status Window:
 *
 *   Show Window?:
 *   - Shows this window in the scene?
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Max Capacity Color:
 *   - Use text colors from the Window Skin only.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.02: April 18, 2024
 * * Bug Fixes!
 * ** Fixed a bug that would cause a crash with the help description of a
 *    passive state not having anything to show. Fix made by Irina.
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New notetag added by Irina from other plugins:
 * *** <Help Description>
 * **** Assigns a help description for the passive state.
 * **** This is so you don't need other unrelated plugins to add a help
 *      description for your psasive states.
 *
 * Version 1.01: March 14, 2024
 * * Bug Fixes!
 * ** Fixed a bug that would cause a crash if the passive state did not have a
 *    help description. Fix made by Arisu.
 * ** Fixed a bug where elemental damage, states, and stypes, taken and dealt
 *    was not making progress for learning new medals. Fix by Arisu.
 * * Feature Update!
 * ** If a passive becomes hidden, it is no longer considered equipped.
 * ** Inflict and Receive State effects no longer require the target to be
 *    unaffilicted by the state before to count.
 *
 * Version 1.00 Official Release Date: March 25, 2024
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorLearnPassive
 * @text Actor: Learn Equippable Passive
 * @desc Target actor(s) learns equippable passive state(s).
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to add as an equippable passive state.
 * @default []
 *
 * @arg ShowTextPopup:eval
 * @text Show Text Popup?
 * @type boolean
 * @on Show
 * @off Don't Show
 * @desc Shows text popup of actor(s) learning the passive state?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorForgetPassive
 * @text Actor: Forget Equippable Passive
 * @desc Target actor(s) forgets equippable passive state(s).
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to forget as an equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorAddUnlearnedPassive
 * @text Actor: Add Unlearned Equippable Passive
 * @desc Gives target actor(s) the ability to learn target passive state(s).
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to add as an unlearned equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ActorRemoveUnlearnedPassive
 * @text Actor: Remove Unlearned Equippable Passive
 * @desc Removes target actor(s) the ability to learn target passive state(s).
 *
 * @arg ActorIDs:arraynum
 * @text Actor ID(s)
 * @type actor[]
 * @desc Select which Actor ID(s) to affect.
 * @default ["1"]
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to remove unlearned equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_G
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command GlobalLearnPassive
 * @text Global: Learn Equippable Passive
 * @desc All actors learn equippable passive state(s).
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to add as an equippable passive state.
 * @default []
 *
 * @arg ShowTextPopup:eval
 * @text Show Text Popup?
 * @type boolean
 * @on Show
 * @off Don't Show
 * @desc Shows text popup of party learning the passive state?
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command GlobalForgetPassive
 * @text Global: Forget Equippable Passive
 * @desc All actors forget equippable passive state(s).
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to forget as an equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command GlobalAddUnlearnedPassive
 * @text Global: Add Unlearned Equippable Passive
 * @desc Gives all actors the ability to learn target passive state(s).
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to add as an unlearned equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command GlobalRemoveUnlearnedPassive
 * @text Global: Remove Unlearned Equippable Passive
 * @desc Removes from all actors the ability to learn target passive state(s).
 *
 * @arg StateIDs:arraynum
 * @text Passive State ID(s)
 * @type state[]
 * @desc Select which State ID(s) to remove unlearned equippable passive state.
 * @default []
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_S
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command SystemShowPassiveCommand
 * @text System: Show Passives in Skill Scene?
 * @desc Shows/hides Passives command inside the skill scene.
 *
 * @arg Show:eval
 * @text Show/Hide?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Shows/hides Passives command inside the skill scene.
 * @default true
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param EquipPassiveSys
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc General settings for the Equip Passive System.
 * @default {"General":"","DefaultShowCommand:eval":"true","LearnAutoEquip:eval":"true","LearnPopup:eval":"true","TextPopupFmt:str":"%1 has learned %3%2!","CheckOverCapacity:eval":"true","Capacity":"","CapacityFormula:str":"Math.ceil(user.level / 5) * 5","DefaultCost:num":"1","MinimumCost:num":"1","MaximumCost:num":"100"}
 *
 * @param Vocab:struct
 * @text Vocabulary Settings
 * @type struct<Vocab>
 * @desc These settings let you adjust the text displayed for this plugin.
 * @default {"Scene":"","CommandName:str":"Passives","CommandIcon:num":"309","CapacityText:str":"Passive Capacity","CapacityIcon:num":"309","CapacityFmt:str":"%1/%2%3","CostFmt:str":"%1%2","Unlearned:str":"\\}Unlearned\\{","ShopStatus":"","ShopStatusText:str":"Passive Effect","HelpWindow":"","HelpFmt:str":"\\C[16]Learn Conditions:\\C[0] %1","helpWordWrap:eval":"true","helpSpacing:eval":"true","helpSpacer:str":",","helpNothing:str":"-","ConditionText":"","helpMeetConditionColor:num":"24","progressFmt:str":"(Progress %1)","progressFraction:str":"%1/%2","progressPercent:str":"%1%","progressLengthLimit:num":"7","progressComplete:str":"\\I[87]","level:str":"Reach Level %1 %2","battle:str":"Fight %1 Battles %2","victory:str":"Win %1 Battles %2","escapes:str":"Escape %1 Battles %2","defeat:str":"Lose %1 Battles %2","attackTimes:str":"Attack %1 Times %2","guardTimes:str":"Guard %1 Times %2","skillUse:str":"Use %1 Skills %2","physSkillUse:str":"Use %1 Physical Skills %2","magSkillUse:str":"Use %1 Magical Skills %2","certSkillUse:str":"Use %1 Certain Hit Skills %2","itemUse:str":"Use %1 Items %2","critDeal:str":"Deal %1 Critical Hits %2","critTake:str":"Take %1 Critical Hits %2","miss:str":"Miss %1 Times %2","evade:str":"Evade %1 Times %2","stypeUse:str":"Use %1 %3 Skills %2","elementDeal:str":"Inflict %3 Damage %1 Times %2","elementTake:str":"Receive %3 Damage %1 Times %2","stateDeal:str":"Inflict %3 %1 Times %2","stateTake:str":"Receive %3 %1 Times %2","traitSlayer:str":"Defeat %1 %3 Enemies %2","totalDmgDeal:str":"Inflict %1 Total Battle Damage %2","totalDmgTake:str":"Receive %1 Total Battle Damage %2","totalHealDeal:str":"Perform %1 Total Battle Healing %2","totalHealTake:str":"Receive %1 Total Battle Healing %2","kills:str":"Kill %1 Enemies %2","deaths:str":"Die %1 Times %2","assists:str":"Assist %1 Times %2","haveGold:str":"Possess ×%1%3 %2","haveItem:str":"Possess %3 ×%1 %2","haveWeapon:str":"Possess %3 ×%1 %2","haveArmor:str":"Possess %3 ×%1 %2","haveParam:str":"Reach %1 %3 %2","haveXParam:str":"Reach %1% %3 %2","haveSParam:str":"Reach %1% %3 %2"}
 *
 * @param Window:struct
 * @text Window Settings
 * @type struct<Window>
 * @desc These settings let you adjust the windows displayed for this plugin.
 * @default {"Window_EquipPassiveList":"","EquipPassiveList_BgType:num":"0","EquippedColor:str":"17","ShowCosts:eval":"true","ShowCost0:eval":"false","ShowCost1:eval":"true","ShowCostNumber:eval":"true","costIconLimit:num":"3","SortStyle:str":"id","ShowUnlearned:eval":"true","SeparateUnlearned:eval":"true","MaskUnlearned:eval":"true","MaskIcon:num":"307","MaskLetter:str":"?","MaskItalics:eval":"true","Window_EquipPassiveStatus":"","showStatusWindow:eval":"true","StatusWindow_BgType:num":"0","MaxCapacityColor:num":"17"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param General
 * @text General Settings
 *
 * @param DefaultShowCommand:eval
 * @text Default Show Command
 * @parent General
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows Passive Command by default?
 * @default true
 *
 * @param LearnAutoEquip:eval
 * @text Auto-Equip on Learn
 * @parent General
 * @type boolean
 * @on Auto-Equip
 * @off Don't Equip
 * @desc Automatically equips newly learned Passives.
 * @default true
 *
 * @param LearnPopup:eval
 * @text Text Popup on Learn
 * @parent General
 * @type boolean
 * @on Show Popup
 * @off Don't Show
 * @desc Produce a text popup when a Passive is learned?
 * @default true
 *
 * @param TextPopupFmt:str
 * @text Text Popup Format
 * @parent LearnPopup:eval
 * @desc Text format used for text popup.
 * %1 - Actor, %2 - Passive, %3 - Icon
 * @default %1 has learned %3%2!
 *
 * @param Capacity
 * @text Capacity Settings
 *
 * @param CapacityFormula:str
 * @text Capacity Formula
 * @parent Capacity
 * @desc What is the formula used to determine current max capacity?
 * @default Math.ceil(user.level / 5) * 5
 *
 * @param DefaultCost:num
 * @text Default Capacity Cost
 * @parent Capacity
 * @desc What is the default capacity cost of equipping a Passive?
 * @default 1
 *
 * @param MinimumCost:num
 * @text Minimum Capacity Cap
 * @parent Capacity
 * @type number
 * @min 1
 * @desc What is the minimum capacity value?
 * @default 1
 *
 * @param MaximumCost:num
 * @text Maximum Capacity Cap
 * @parent Capacity
 * @type number
 * @min 1
 * @desc What is the maximum capacity value?
 * @default 100
 *
 * @param CheckOverCapacity:eval
 * @text Check Over-Capacity
 * @parent Capacity
 * @type boolean
 * @on Check
 * @off Don't
 * @desc Checks over-capacity when EXP changes.
 * @default true
 *
 */
/* ----------------------------------------------------------------------------
 * Vocabulary Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Vocab:
 *
 * @param Scene
 * @text Scene_Skill
 *
 * @param CommandName:str
 * @text Command Name
 * @parent Scene
 * @desc Text used for the Passives Command.
 * @default Passives
 *
 * @param CommandIcon:num
 * @text Command Icon
 * @parent CommandName:str
 * @desc Icon used for the Passives command and for any
 * passives that are displayed without any icon.
 * @default 309
 *
 * @param CapacityText:str
 * @text Capacity Text
 * @parent Scene
 * @desc Text used for Passives Capacity.
 * @default Passive Capacity
 *
 * @param CapacityIcon:num
 * @text Capacity Icon
 * @parent CapacityText:str
 * @desc Icon used to represent Passives Capacity when
 * displayed as a limited resource.
 * @default 309
 *
 * @param CapacityFmt:str
 * @text Capacity Format
 * @parent CapacityText:str
 * @desc Text format used to representing Capacity.
 * %1 - Current, %2 - Max, %3 - Icon
 * @default %1/%2%3
 *
 * @param CostFmt:str
 * @text Cost Format
 * @parent Scene
 * @desc Text format used for Capacity Cost.
 * %1 - Cost, %2 - Icon
 * @default %1%2
 *
 * @param Unlearned:str
 * @text Unlearned Text
 * @parent CostFmt:str
 * @desc Text displayed instead of cost for unlearned Passives.
 * @default \}Unlearned\{
 *
 * @param ShopStatus
 * @text Shop Status Window
 *
 * @param ShopStatusText:str
 * @text Shop Status Text
 * @parent ShopStatus
 * @desc Text used to representing Passives in shop status.
 * Requires VisuMZ_1_ItemsEquipsCore!
 * @default Passive Effect
 *
 * @param HelpWindow
 * @text Help Window
 *
 * @param HelpFmt:str
 * @text Description Format
 * @parent HelpWindow
 * @desc Text format used to create help descriptions.
 * %1 - Unlocking Conditions
 * @default \C[16]Learn Conditions:\C[0] %1
 *
 * @param helpWordWrap:eval
 * @text Word Wrap?
 * @parent HelpWindow
 * @type boolean
 * @on Wordwrap
 * @off Normal
 * @desc Apply word wrap to unlock conditions?
 * Requires VisuMZ_1_MessageCore!
 * @default true
 *
 * @param helpSpacing:eval
 * @text Spacing?
 * @parent HelpWindow
 * @type boolean
 * @on Add Spacing
 * @off Don't Add
 * @desc Add spacing between conditions?
 * @default true
 *
 * @param helpSpacer:str
 * @text Spacer
 * @parent HelpWindow
 * @desc Text inserted between conditions.
 * @default ,
 *
 * @param helpNothing:str
 * @text Empty Descriptions
 * @parent HelpWindow
 * @desc Text used when no condition text is made.
 * @default -
 *
 * @param ConditionText
 * @text Unlock Condition Text
 *
 * @param helpMeetConditionColor:num
 * @text Condition Met Color
 * @parent ConditionText
 * @type number
 * @min 0
 * @desc Use text colors from the Window Skin only.
 * @default 24
 *
 * @param progressFmt:str
 * @text Progress Format
 * @parent ConditionText
 * @desc Text format used to indicate progress amount.
 * %1 - Progress Text
 * @default (Progress %1)
 *
 * @param progressFraction:str
 * @text Fraction Format
 * @parent progressFmt:str
 * @desc Text format used for progress fraction.
 * %1 - Current, %2 - Goal
 * @default %1/%2
 *
 * @param progressPercent:str
 * @text Percent Format
 * @parent progressFmt:str
 * @desc Text format used for percentile value.
 * %1 - Percent
 * @default %1%
 *
 * @param progressLengthLimit:num
 * @text Length Limit
 * @parent progressFmt:str
 * @type number
 * @min 1
 * @desc What is the character limit before a percentage is
 * used instead fractions for progress text?
 * @default 7
 *
 * @param progressComplete:str
 * @text Complete
 * @parent progressFmt:str
 * @desc Progress text used when unlock condition is fulfilled.
 * @default \I[87]
 *
 * @param level:str
 * @text Level Format
 * @parent ConditionText
 * @desc Text format used for level conditions.
 * %1 - Level, %2 - Progress
 * @default Reach Level %1 %2
 *
 * @param battle:str
 * @text Battle Format
 * @parent ConditionText
 * @desc Text format used for fought battles.
 * %1 - Needed, %2 - Progress
 * @default Fight %1 Battles %2
 *
 * @param victory:str
 * @text Victory Format
 * @parent battle:str
 * @desc Text format used for victorious battles.
 * %1 - Needed, %2 - Progress
 * @default Win %1 Battles %2
 *
 * @param escapes:str
 * @text Escape Format
 * @parent battle:str
 * @desc Text format used for escaped battles.
 * %1 - Needed, %2 - Progress
 * @default Escape %1 Battles %2
 *
 * @param defeat:str
 * @text Defeat Format
 * @parent battle:str
 * @desc Text format used for lost battles.
 * %1 - Needed, %2 - Progress
 * @default Lose %1 Battles %2
 *
 * @param attackTimes:str
 * @text Attack Format
 * @parent ConditionText
 * @desc Text format used for attack times.
 * %1 - Needed, %2 - Progress
 * @default Attack %1 Times %2
 *
 * @param guardTimes:str
 * @text Guard Format
 * @parent attackTimes:str
 * @desc Text format used for guard times.
 * %1 - Needed, %2 - Progress
 * @default Guard %1 Times %2
 *
 * @param skillUse:str
 * @text Skill Format
 * @parent ConditionText
 * @desc Text format used for skill times.
 * %1 - Needed, %2 - Progress
 * @default Use %1 Skills %2
 *
 * @param physSkillUse:str
 * @text Physical Skills
 * @parent attackTimes:str
 * @desc Text format used for physical skills.
 * %1 - Needed, %2 - Progress
 * @default Use %1 Physical Skills %2
 *
 * @param magSkillUse:str
 * @text Magical Skills
 * @parent attackTimes:str
 * @desc Text format used for magical skills.
 * %1 - Needed, %2 - Progress
 * @default Use %1 Magical Skills %2
 *
 * @param certSkillUse:str
 * @text Certain Hit Skills
 * @parent attackTimes:str
 * @desc Text format used for certain hit skills.
 * %1 - Needed, %2 - Progress
 * @default Use %1 Certain Hit Skills %2
 *
 * @param itemUse:str
 * @text Item Format
 * @parent ConditionText
 * @desc Text format used for item uses.
 * %1 - Needed, %2 - Progress
 * @default Use %1 Items %2
 *
 * @param critDeal:str
 * @text Deal Criticals
 * @parent ConditionText
 * @desc Text format used for dealing criticals.
 * %1 - Needed, %2 - Progress
 * @default Deal %1 Critical Hits %2
 *
 * @param critTake:str
 * @text Take Criticals
 * @parent critDeal:str
 * @desc Text format used for taking criticals
 * %1 - Needed, %2 - Progress
 * @default Take %1 Critical Hits %2
 *
 * @param miss:str
 * @text Miss Format
 * @parent ConditionText
 * @desc Text format for missing attacks.
 * %1 - Needed, %2 - Progress
 * @default Miss %1 Times %2
 *
 * @param evade:str
 * @text Evade Format
 * @parent miss:str
 * @desc Text format for evading attacks.
 * %1 - Needed, %2 - Progress
 * @default Evade %1 Times %2
 *
 * @param stypeUse:str
 * @text SType Use
 * @parent ConditionText
 * @desc Text format for using SType Skills.
 * %1 - Needed, %2 - Progress, %3 - Type Text
 * @default Use %1 %3 Skills %2
 *
 * @param elementDeal:str
 * @text Deal Element DMG
 * @parent ConditionText
 * @desc Text format used for inflicting element damage.
 * %1 - Needed, %2 - Progress, %3 - Element
 * @default Inflict %3 Damage %1 Times %2
 *
 * @param elementTake:str
 * @text Take Element DMG
 * @parent elementDeal:str
 * @desc Text format used for receiving element damage.
 * %1 - Needed, %2 - Progress, %3 - Element
 * @default Receive %3 Damage %1 Times %2
 *
 * @param stateDeal:str
 * @text Deal State
 * @parent ConditionText
 * @desc Text format used for inflicting states.
 * %1 - Needed, %2 - Progress, %3 - State
 * @default Inflict %3 %1 Times %2
 *
 * @param stateTake:str
 * @text Take State
 * @parent stateDeal:str
 * @desc Text format used for receiving states
 * %1 - Needed, %2 - Progress, %3 - State
 * @default Receive %3 %1 Times %2
 *
 * @param traitSlayer:str
 * @text Trait Slayer
 * @parent ConditionText
 * @desc Text format for slaying trait types.
 * %1 - Needed, %2 - Progress, %3 - Type Text
 * @default Defeat %1 %3 Enemies %2
 *
 * @param totalDmgDeal:str
 * @text Total Damage Dealt
 * @parent ConditionText
 * @desc Text format for total damage dealt.
 * %1 - Needed, %2 - Progress
 * @default Inflict %1 Total Battle Damage %2
 *
 * @param totalDmgTake:str
 * @text Total Damage Taken
 * @parent totalDmgDeal:str
 * @desc Text format for total damage received.
 * %1 - Needed, %2 - Progress
 * @default Receive %1 Total Battle Damage %2
 *
 * @param totalHealDeal:str
 * @text Total Healing Dealt
 * @parent ConditionText
 * @desc Text format for total healing given.
 * %1 - Needed, %2 - Progress
 * @default Perform %1 Total Battle Healing %2
 *
 * @param totalHealTake:str
 * @text Total Healing Taken
 * @parent totalHealDeal:str
 * @desc Text format for total healing taken.
 * %1 - Needed, %2 - Progress
 * @default Receive %1 Total Battle Healing %2
 *
 * @param kills:str
 * @text Kills Format
 * @parent ConditionText
 * @desc Text format for kills performed.
 * %1 - Needed, %2 - Progress
 * @default Kill %1 Enemies %2
 *
 * @param deaths:str
 * @text Deaths Format
 * @parent kills:str
 * @desc Text format for deaths in battle.
 * %1 - Needed, %2 - Progress
 * @default Die %1 Times %2
 *
 * @param assists:str
 * @text Assists Format
 * @parent kills:str
 * @desc Text format for assists made.
 * %1 - Needed, %2 - Progress
 * @default Assist %1 Times %2
 *
 * @param haveGold:str
 * @text Reach Gold Total
 * @parent ConditionText
 * @desc Text format for reaching gold quantity.
 * %1 - Needed, %2 - Progress, %3 - Gold
 * @default Possess ×%1%3 %2
 *
 * @param haveItem:str
 * @text Reach Item Total
 * @parent haveGold:str
 * @desc Text format for reaching item quantity.
 * %1 - Needed, %2 - Progress, %3 - Item
 * @default Possess %3 ×%1 %2
 *
 * @param haveWeapon:str
 * @text Reach Weapon Total
 * @parent haveGold:str
 * @desc Text format for reaching weapon quantity.
 * %1 - Needed, %2 - Progress, %3 - Weapon
 * @default Possess %3 ×%1 %2
 *
 * @param haveArmor:str
 * @text Reach Armor Total
 * @parent haveGold:str
 * @desc Text format for reaching armor quantity.
 * %1 - Needed, %2 - Progress, %3 - Armor
 * @default Possess %3 ×%1 %2
 *
 * @param haveParam:str
 * @text Reach Base Param
 * @parent ConditionText
 * @desc Text format for reaching base Param amount.
 * %1 - Needed, %2 - Progress, %3 - Param Name
 * @default Reach %1 %3 %2
 *
 * @param haveXParam:str
 * @text Reach XParam Amount
 * @parent haveParam:str
 * @desc Text format for reaching X Param amount.
 * %1 - Needed, %2 - Progress, %3 - XParam Name
 * @default Reach %1% %3 %2
 *
 * @param haveSParam:str
 * @text Reach SParam Amount
 * @parent haveParam:str
 * @desc Text format for reaching S Param amount.
 * %1 - Needed, %2 - Progress, %3 - SParam Name
 * @default Reach %1% %3 %2
 *
 */
/* ----------------------------------------------------------------------------
 * Window Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Window:
 *
 * @param Window_EquipPassiveList
 * @text Equip Passive List
 *
 * @param EquipPassiveList_BgType:num
 * @text Background Type
 * @parent Window_EquipPassiveList
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param EquippedColor:str
 * @text Equipped Color
 * @parent Window_EquipPassiveList
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 17
 *
 * @param ShowCosts:eval
 * @text Show Capacity Costs?
 * @parent Window_EquipPassiveList
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows capacity costs on Passives?
 * @default true
 *
 * @param ShowCost0:eval
 * @text Show 0 Costs?
 * @parent ShowCosts:eval
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows capacity costs if they cost 0?
 * @default false
 *
 * @param ShowCost1:eval
 * @text Show 1 Costs?
 * @parent ShowCosts:eval
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows capacity costs if they only cost 1?
 * @default true
 *
 * @param ShowCostNumber:eval
 * @text Show Cost Numbers?
 * @parent ShowCosts:eval
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows capacity cost values?
 * If not, displays multiple icons instead.
 * @default true
 *
 * @param costIconLimit:num
 * @text Cost Icon Limit
 * @parent ShowCostNumber:eval
 * @desc If "Show Cost Numbers" is false, this is how many icons
 * can be displayed max before showing number costs.
 * @default 3
 *
 * @param SortStyle:str
 * @text Sort Style
 * @parent Window_EquipPassiveList
 * @type select
 * @option ID - Sort by State ID
 * @value id
 * @option Name - Sort by State Name
 * @value name
 * @option Priority - Sort by State Priority
 * @value priority
 * @desc How do you wish to sort passives by?
 * @default id
 *
 * @param ShowUnlearned:eval
 * @text Show Unlearned?
 * @parent Window_EquipPassiveList
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows unlearned passives in the list window?
 * @default true
 *
 * @param SeparateUnlearned:eval
 * @text Separate Unlearned?
 * @parent ShowUnlearned:eval
 * @type boolean
 * @on Separate
 * @off Don't
 * @desc Separate unlearned passives from learned passives?
 * @default true
 *
 * @param MaskUnlearned:eval
 * @text Mask Unlearned?
 * @parent ShowUnlearned:eval
 * @type boolean
 * @on Mask
 * @off Don't
 * @desc Masks unlearned passives in list window?
 * @default true
 *
 * @param MaskIcon:num
 * @text Mask Icon
 * @parent MaskUnlearned:eval
 * @desc What is the icon used for masked passives?
 * @default 307
 *
 * @param MaskLetter:str
 * @text Mask Character
 * @parent MaskUnlearned:eval
 * @desc Text used for masking per individual character.
 * @default ?
 *
 * @param MaskItalics:eval
 * @text Italics?
 * @parent MaskUnlearned:eval
 * @type boolean
 * @on Italics
 * @off Normal
 * @desc Use italics for masked names?
 * @default true
 *
 * @param Window_EquipPassiveStatus
 * @text Passive Status Window
 *
 * @param showStatusWindow:eval
 * @text Show Window?
 * @parent Window_EquipPassiveStatus
 * @type boolean
 * @on Show
 * @off Don't
 * @desc Shows this window in the scene?
 * @default true
 *
 * @param StatusWindow_BgType:num
 * @text Background Type
 * @parent Window_EquipPassiveStatus
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param MaxCapacityColor:num
 * @text Max Capacity Color
 * @parent Window_EquipPassiveStatus
 * @type number
 * @min 0
 * @desc Use text colors from the Window Skin only.
 * @default 17
 *
 */
//=============================================================================
var tier = tier || 0x0;
var dependencies = ['VisuMZ_0_CoreEngine', 'VisuMZ_1_SkillsStatesCore'];
var pluginData = $plugins.filter(function (_0x265478) {
    return _0x265478.status && _0x265478.description.includes('[EquipPassiveSys]');
})[0x0];
VisuMZ.EquipPassiveSys.Settings = VisuMZ.EquipPassiveSys.Settings || {};
VisuMZ.ConvertParams = function (_0x54ed73, _0x15c1a5) {
    for (const _0x1a2497 in _0x15c1a5) {
        if (_0x1a2497.match(/(.*):(.*)/i)) {
            const _0x2bf460 = String(RegExp.$1);
            const _0x2788ef = String(RegExp.$2).toUpperCase().trim();
            let _0xbfced4;
            let _0x58a0e8;
            let _0x46f9c3;
            switch (_0x2788ef) {
                case 'NUM':
                    _0xbfced4 = _0x15c1a5[_0x1a2497] !== '' ? Number(_0x15c1a5[_0x1a2497]) : 0x0;
                    break;
                case 'ARRAYNUM':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0x5be9a9 => Number(_0x5be9a9));
                    break;
                case 'EVAL':
                    _0xbfced4 = _0x15c1a5[_0x1a2497] !== '' ? eval(_0x15c1a5[_0x1a2497]) : null;
                    break;
                case 'ARRAYEVAL':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0x17fef2 => eval(_0x17fef2));
                    break;
                case 'JSON':
                    _0xbfced4 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : '';
                    break;
                case 'ARRAYJSON':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0x2dff77 => JSON.parse(_0x2dff77));
                    break;
                case 'FUNC':
                    _0xbfced4 =
                        _0x15c1a5[_0x1a2497] !== ''
                            ? new Function(JSON.parse(_0x15c1a5[_0x1a2497]))
                            : new Function('return 0');
                    break;
                case 'ARRAYFUNC':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0x5640cf => new Function(JSON.parse(_0x5640cf)));
                    break;
                case 'STR':
                    _0xbfced4 = _0x15c1a5[_0x1a2497] !== '' ? String(_0x15c1a5[_0x1a2497]) : '';
                    break;
                case 'ARRAYSTR':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0x1f7ca6 => String(_0x1f7ca6));
                    break;
                case 'STRUCT':
                    _0x46f9c3 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : {};
                    _0xbfced4 = VisuMZ.ConvertParams({}, _0x46f9c3);
                    break;
                case 'ARRAYSTRUCT':
                    _0x58a0e8 = _0x15c1a5[_0x1a2497] !== '' ? JSON.parse(_0x15c1a5[_0x1a2497]) : [];
                    _0xbfced4 = _0x58a0e8.map(_0xfaebe1 =>
                        VisuMZ.ConvertParams({}, JSON.parse(_0xfaebe1))
                    );
                    break;
                default:
                    continue;
            }
            _0x54ed73[_0x2bf460] = _0xbfced4;
        }
    }
    return _0x54ed73;
};
(_0x51400c => {
    const _0x4d1acf = _0x51400c.name;
    for (const _0x55c42f of dependencies) {
        if (!Imported[_0x55c42f]) {
            alert(
                '%1 is missing a required plugin.\nPlease install %2 into the Plugin Manager.'.format(
                    _0x4d1acf,
                    _0x55c42f
                )
            );
            SceneManager.exit();
            break;
        }
    }
    const _0x4beeab = _0x51400c.description;
    if (_0x4beeab.match(/\[Version[ ](.*?)\]/i)) {
        const _0x5b3f2b = Number(RegExp.$1);
        if (_0x5b3f2b !== VisuMZ.EquipPassiveSys.version) {
            alert(
                "%1's version does not match plugin's. Please update it in the Plugin Manager.".format(
                    _0x4d1acf,
                    _0x5b3f2b
                )
            );
            SceneManager.exit();
        }
    }
    if (_0x4beeab.match(/\[Tier[ ](\d+)\]/i)) {
        const _0x3a7f9d = Number(RegExp.$1);
        if (_0x3a7f9d < tier) {
            alert(
                '%1 is incorrectly placed on the plugin list.\nIt is a Tier %2 plugin placed over other Tier %3 plugins.\nPlease reorder the plugin list from smallest to largest tier numbers.'.format(
                    _0x4d1acf,
                    _0x3a7f9d,
                    tier
                )
            );
            SceneManager.exit();
        } else {
            tier = Math.max(_0x3a7f9d, tier);
        }
    }
    VisuMZ.ConvertParams(VisuMZ.EquipPassiveSys.Settings, _0x51400c.parameters);
})(pluginData);
if (VisuMZ.CoreEngine.version < 1.79) {
    let text = '';
    text += 'VisuMZ_0_CoreEngine needs to be updated ';
    text += 'in order for VisuMZ_2_EquipPassiveSys to work.';
    alert(text);
    SceneManager.exit();
}
if (VisuMZ.SkillsStatesCore.version < 1.44) {
    let text = '';
    text += 'VisuMZ_1_SkillsStatesCore needs to be updated ';
    text += 'in order for VisuMZ_2_EquipPassiveSys to work.';
    alert(text);
    SceneManager.exit();
}
PluginManager.registerCommand(pluginData.name, 'ActorLearnPassive', _0x359f68 => {
    VisuMZ.ConvertParams(_0x359f68, _0x359f68);
    const _0x4f0223 = _0x359f68.ActorIDs || [];
    const _0x302619 = _0x359f68.StateIDs || [];
    const _0x4304f6 = !_0x359f68.ShowTextPopup;
    for (const _0x2be39e of _0x4f0223) {
        const _0x5eb704 = $gameActors.actor(_0x2be39e);
        if (!_0x5eb704) {
            continue;
        }
        for (const _0x375dc4 of _0x302619) {
            const _0x4b8bbc = $dataStates[_0x375dc4];
            if (!_0x4b8bbc) {
                continue;
            }
            if (_0x4b8bbc.name.trim() === '') {
                continue;
            }
            if (_0x4b8bbc.name.includes('-----')) {
                continue;
            }
            _0x5eb704.learnEquippedPassive(_0x4b8bbc, _0x4304f6);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'ActorForgetPassive', _0x2e7d91 => {
    VisuMZ.ConvertParams(_0x2e7d91, _0x2e7d91);
    const _0x36eed0 = _0x2e7d91.ActorIDs || [];
    const _0x55d18d = _0x2e7d91.StateIDs || [];
    for (const _0x6ce790 of _0x36eed0) {
        const _0x348d0c = $gameActors.actor(_0x6ce790);
        if (!_0x348d0c) {
            continue;
        }
        for (const _0x2ab147 of _0x55d18d) {
            const _0xb8df47 = $dataStates[_0x2ab147];
            if (!_0xb8df47) {
                continue;
            }
            if (_0xb8df47.name.trim() === '') {
                continue;
            }
            if (_0xb8df47.name.includes('-----')) {
                continue;
            }
            _0x348d0c.forgetEquippedPassive(_0xb8df47);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'ActorAddUnlearnedPassive', _0x5c509d => {
    VisuMZ.ConvertParams(_0x5c509d, _0x5c509d);
    const _0x34e5de = _0x5c509d.ActorIDs || [];
    const _0xdc294a = _0x5c509d.StateIDs || [];
    for (const _0x38b2d0 of _0x34e5de) {
        const _0x4893c7 = $gameActors.actor(_0x38b2d0);
        if (!_0x4893c7) {
            continue;
        }
        for (const _0x2d34b5 of _0xdc294a) {
            const _0x542b49 = $dataStates[_0x2d34b5];
            if (!_0x542b49) {
                continue;
            }
            if (_0x542b49.name.trim() === '') {
                continue;
            }
            if (_0x542b49.name.includes('-----')) {
                continue;
            }
            _0x4893c7.addUnlearnedEquippablePassive(_0x542b49);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'ActorRemoveUnlearnedPassive', _0x53024b => {
    VisuMZ.ConvertParams(_0x53024b, _0x53024b);
    const _0x34fdb4 = _0x53024b.ActorIDs || [];
    const _0x5b3150 = _0x53024b.StateIDs || [];
    for (const _0x1ce3a7 of _0x34fdb4) {
        const _0x3b98c1 = $gameActors.actor(_0x1ce3a7);
        if (!_0x3b98c1) {
            continue;
        }
        for (const _0x3587ab of _0x5b3150) {
            const _0x28e26b = $dataStates[_0x3587ab];
            if (!_0x28e26b) {
                continue;
            }
            if (_0x28e26b.name.trim() === '') {
                continue;
            }
            if (_0x28e26b.name.includes('-----')) {
                continue;
            }
            _0x3b98c1.removeUnlearnedEquippablePassive(_0x28e26b);
        }
    }
});
PluginManager.registerCommand(pluginData.name, 'GlobalLearnPassive', _0x1e53dc => {
    VisuMZ.ConvertParams(_0x1e53dc, _0x1e53dc);
    const _0x2f1c8a = _0x1e53dc.StateIDs || [];
    const _0x9fcb66 = !_0x1e53dc.ShowTextPopup;
    for (const _0x4f7e49 of _0x2f1c8a) {
        const _0x128ad7 = $dataStates[_0x4f7e49];
        if (!_0x128ad7) {
            continue;
        }
        if (_0x128ad7.name.trim() === '') {
            continue;
        }
        if (_0x128ad7.name.includes('-----')) {
            continue;
        }
        $gameParty.learnEquippedPassive(_0x128ad7, _0x9fcb66);
    }
});
PluginManager.registerCommand(pluginData.name, 'GlobalForgetPassive', _0x3bbf82 => {
    VisuMZ.ConvertParams(_0x3bbf82, _0x3bbf82);
    const _0x267182 = _0x3bbf82.StateIDs || [];
    for (const _0x5e0059 of _0x267182) {
        const _0x108a53 = $dataStates[_0x5e0059];
        if (!_0x108a53) {
            continue;
        }
        if (_0x108a53.name.trim() === '') {
            continue;
        }
        if (_0x108a53.name.includes('-----')) {
            continue;
        }
        $gameParty.forgetEquippedPassive(_0x108a53);
    }
});
PluginManager.registerCommand(pluginData.name, 'GlobalAddUnlearnedPassive', _0x461a50 => {
    VisuMZ.ConvertParams(_0x461a50, _0x461a50);
    const _0x1decd3 = _0x461a50.StateIDs || [];
    for (const _0x5bac03 of _0x1decd3) {
        const _0x54d382 = $dataStates[_0x5bac03];
        if (!_0x54d382) {
            continue;
        }
        if (_0x54d382.name.trim() === '') {
            continue;
        }
        if (_0x54d382.name.includes('-----')) {
            continue;
        }
        $gameParty.addUnlearnedEquippablePassive(_0x54d382);
    }
});
PluginManager.registerCommand(pluginData.name, 'GlobalRemoveUnlearnedPassive', _0x463b9f => {
    VisuMZ.ConvertParams(_0x463b9f, _0x463b9f);
    const _0x5c77e1 = _0x463b9f.StateIDs || [];
    for (const _0x1678e8 of _0x5c77e1) {
        const _0x5d7d19 = $dataStates[_0x1678e8];
        if (!_0x5d7d19) {
            continue;
        }
        if (_0x5d7d19.name.trim() === '') {
            continue;
        }
        if (_0x5d7d19.name.includes('-----')) {
            continue;
        }
        $gameParty.removeUnlearnedEquippablePassive(_0x5d7d19);
    }
});
PluginManager.registerCommand(pluginData.name, 'SystemShowPassiveCommand', _0x151839 => {
    VisuMZ.ConvertParams(_0x151839, _0x151839);
    const _0x1caef2 = _0x151839.Show;
    $gameSystem.setEquipPassiveCommandVisible(_0x1caef2);
});
VisuMZ.EquipPassiveSys.RegExp = {
    EquipCost: /<EQUIP PASSIVE (?:COST|SLOTS):[ ](\d+)>/i,
    EquipIcon: /<EQUIP PASSIVE ICON:[ ](\d+)>/i,
    EquipName: /<EQUIP PASSIVE NAME:[ ](.*)>/i,
    HelpDescription:
        /<(?:HELP|HELP DESCRIPTION|DESCRIPTION)>\s*([\s\S]*)\s*<\/(?:HELP|HELP DESCRIPTION|DESCRIPTION)>/i,
    HideUnlearned: /<HIDE IF NOT LEARNED EQUIP PASSIVE>/i,
    HideLearnedAllID: /<HIDE IF LEARNED (?:|ALL )EQUIP PASSIVE(?:|S):[ ](\d+)>/i,
    HideLearnedAnyID: /<HIDE IF LEARNED ANY EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    HideLearnedAllName: /<HIDE IF LEARNED (?:|ALL )EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    HideLearnedAnyName: /<HIDE IF LEARNED ANY EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    MaskUnlearned: /<MASK IF NOT LEARNED EQUIP PASSIVE>/i,
    NoMaskUnlearned: /<NO MASK IF NOT LEARNED EQUIP PASSIVE>/i,
    MaskName: /<EQUIP PASSIVE MASK NAME: (.*)>/i,
    LearnedEquipPassives: /<LEARNED EQUIP(?:|PED) PASSIVE(?:|S):[ ](.*)>/i,
    AlreadyEquipPassives: /<ALREADY EQUIP(?:|PED) PASSIVE(?:|S):[ ](.*)>/i,
    LearnableEquipPassivesA: /<LEARNABLE EQUIP(?:|PED) PASSIVE(?:|S):[ ](.*)>/i,
    LearnableEquipPassivesB:
        /<LEARNABLE EQUIP(?:|PED) PASSIVE>\s*([\s\S]*)\s*<\/LEARNABLE EQUIP(?:|PED) PASSIVE(?:|S)>/i,
    BranchLearn: /<BRANCH LEARN EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    BranchLearnable: /<BRANCH LEARNABLE EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    SkillLinkLearned: /<LINK LEARN EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    SkillLinkLearnable: /<LINK LEARNABLE EQUIP PASSIVE(?:|S):[ ](.*)>/i,
    CustomLearnCondText:
        /<EQUIP(?:|PED) PASSIVE LEARN CONDITION TEXT>\s*([\s\S]*)\s*<\/EQUIP(?:|PED) PASSIVE LEARN CONDITION TEXT>/i,
    LearnAny: /<EQUIP PASSIVE LEARN (.*)>/i,
    LearnLevel: /<EQUIP PASSIVE LEARN LEVEL:[ ](\d+)>/i,
    LearnBattles: /<EQUIP PASSIVE LEARN BATTLES:[ ](\d+)>/i,
    LearnVictory: /<EQUIP PASSIVE LEARN VICTORIES:[ ](\d+)>/i,
    LearnEscapes: /<EQUIP PASSIVE LEARN ESCAPES:[ ](\d+)>/i,
    LearnDefeats: /<EQUIP PASSIVE LEARN DEFEATS:[ ](\d+)>/i,
    LearnAttackTimes: /<EQUIP PASSIVE LEARN ATTACK TIMES:[ ](\d+)>/i,
    LearnGuardTimes: /<EQUIP PASSIVE LEARN GUARD TIMES:[ ](\d+)>/i,
    LearnSkillUsage: /<EQUIP PASSIVE LEARN USE SKILLS:[ ](\d+)>/i,
    LearnPhysSkill: /<EQUIP PASSIVE LEARN USE PHYSICAL SKILLS:[ ](\d+)>/i,
    LearnMagSkill: /<EQUIP PASSIVE LEARN USE MAGICAL SKILLS:[ ](\d+)>/i,
    LearnCertSkill: /<EQUIP PASSIVE LEARN USE CERTAIN HIT SKILLS:[ ](\d+)>/i,
    LearnItemUsage: /<EQUIP PASSIVE LEARN USE ITEMS:[ ](\d+)>/i,
    LearnDealCritHitTimes:
        /<EQUIP PASSIVE LEARN (?:DEAL|INFLICT) CRIT(?:|S|ICAL)(?:| HIT) TIMES:[ ](\d+)>/i,
    LearnTakeCritHitTimes:
        /<EQUIP PASSIVE LEARN (?:TAKE|RECEIVE) CRIT(?:|S|ICAL)(?:| HIT) TIMES:[ ](\d+)>/i,
    LearnMissTimes: /<EQUIP PASSIVE LEARN (?:MISS|MISSED) TIMES:[ ](\d+)>/i,
    LearnEvadeTimes: /<EQUIP PASSIVE LEARN (?:EVADE|EVASION) TIMES:[ ](\d+)>/i,
    LearnSTypeUsage: /<EQUIP PASSIVE LEARN USE STYPE (.*):[ ](\d+)>/gi,
    LearnElementDeal:
        /<EQUIP PASSIVE LEARN (?:DEAL|INFLICT) ELEMENT (.*) (?:DMG|DAMAGE):[ ](\d+)>/gi,
    LearnElementTake:
        /<EQUIP PASSIVE LEARN (?:TAKE|RECEIVE) ELEMENT (.*) (?:DMG|DAMAGE):[ ](\d+)>/gi,
    LearnStateDeal: /<EQUIP PASSIVE LEARN (?:DEAL|INFLICT) STATE (.*):[ ](\d+)>/gi,
    LearnStateTake: /<EQUIP PASSIVE LEARN (?:TAKE|RECEIVE) STATE (.*):[ ](\d+)>/gi,
    LearnDefeatTrait: /<EQUIP PASSIVE LEARN DEFEAT (.*) TRAIT:[ ](\d+)>/gi,
    LearnTotalDmgDeal: /<EQUIP PASSIVE LEARN (?:DEAL|INFLICT) TOTAL (?:DMG|DAMAGE):[ ](\d+)>/gi,
    LearnTotalDmgTake: /<EQUIP PASSIVE LEARN (?:TAKE|RECEIVE) TOTAL (?:DMG|DAMAGE):[ ](\d+)>/gi,
    LearnTotalHealDeal: /<EQUIP PASSIVE LEARN (?:DEAL|INFLICT) TOTAL (?:HEAL|HEALING):[ ](\d+)>/gi,
    LearnTotalHealTake: /<EQUIP PASSIVE LEARN (?:TAKE|RECEIVE) TOTAL (?:HEAL|HEALING):[ ](\d+)>/gi,
    LearnCountKills: /<EQUIP PASSIVE LEARN KILL COUNT:[ ](\d+)>/gi,
    LearnCountDeaths: /<EQUIP PASSIVE LEARN DEATH COUNT:[ ](\d+)>/gi,
    LearnCountAssists: /<EQUIP PASSIVE LEARN ASSIST COUNT:[ ](\d+)>/gi,
    LearnHaveGold: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) GOLD:[ ](\d+)>/i,
    LearnHaveItem: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) ITEM (.*):[ ](\d+)>/gi,
    LearnHaveWeapon: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) WEAPON (.*):[ ](\d+)>/gi,
    LearnHaveArmor: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) ARMOR (.*):[ ](\d+)>/gi,
    LearnHaveParam: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) (?:|BASE )PARAM[ ](.*):[ ](\d+)>/gi,
    LearnHaveXParam: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) (?:X|X )PARAM[ ](.*):[ ](\d+)([%％])>/gi,
    LearnHaveSParam: /<EQUIP PASSIVE LEARN (?:HAVE|REACH) (?:S|S )PARAM[ ](.*):[ ](\d+)([%％])>/gi,
};
VisuMZ.EquipPassiveSys.Scene_Boot_onDatabaseLoaded = Scene_Boot.prototype.onDatabaseLoaded;
Scene_Boot.prototype.onDatabaseLoaded = function () {
    VisuMZ.EquipPassiveSys.Scene_Boot_onDatabaseLoaded.call(this);
    VisuMZ.EquipPassiveSys.CheckCompatibility();
    this.process_VisuMZ_EquipPassiveStates();
};
VisuMZ.EquipPassiveSys.CheckCompatibility = function () {
    if (Imported.VisuMZ_1_ElementStatusCore && VisuMZ.ElementStatusCore.version < 1.23) {
        let _0x30b0cf = '';
        _0x30b0cf += 'Imported.VisuMZ_1_ElementStatusCore needs to be updated ';
        _0x30b0cf += 'in order for VisuMZ_2_EquipPassiveSys to work.';
        alert(_0x30b0cf);
        SceneManager.exit();
    }
    if (Imported.VisuMZ_2_SkillLearnSystem && VisuMZ.SkillLearnSystem.version < 1.13) {
        let _0x4e5bd9 = '';
        _0x4e5bd9 += 'Imported.VisuMZ_2_SkillLearnSystem needs to be updated ';
        _0x4e5bd9 += 'in order for VisuMZ_2_EquipPassiveSys to work.';
        alert(_0x4e5bd9);
        SceneManager.exit();
    }
};
Scene_Boot.prototype.process_VisuMZ_EquipPassiveStates = function () {
    this.process_VisuMZ_EquipPassiveStates_Notetags();
};
Scene_Boot.prototype.process_VisuMZ_EquipPassiveStates_Notetags = function () {
    if (VisuMZ.ParseAllNotetags) {
        return;
    }
    for (const _0x5337f0 of $dataStates) {
        if (!_0x5337f0) {
            continue;
        }
        VisuMZ.EquipPassiveSys.Parse_Notetags_Description(_0x5337f0);
    }
};
VisuMZ.EquipPassiveSys.ParseStateNotetags = VisuMZ.ParseStateNotetags;
VisuMZ.ParseStateNotetags = function (_0x47ce5d) {
    VisuMZ.EquipPassiveSys.ParseStateNotetags.call(this, _0x47ce5d);
    VisuMZ.EquipPassiveSys.Parse_Notetags_Description(_0x47ce5d);
};
VisuMZ.EquipPassiveSys.Parse_Notetags_Description = function (_0x5d5dae) {
    _0x5d5dae.description = _0x5d5dae.description || '';
    const _0x3c6342 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x414060 = _0x5d5dae.note;
    if (_0x414060.match(_0x3c6342.HelpDescription)) {
        _0x5d5dae.description = String(RegExp.$1).trim();
    }
};
DataManager.getItemIdWithName = function (_0x49c5b7) {
    _0x49c5b7 = _0x49c5b7.toUpperCase().trim();
    this._itemIDs = this._itemIDs || {};
    if (this._itemIDs[_0x49c5b7]) {
        return this._itemIDs[_0x49c5b7];
    }
    for (const _0x248101 of $dataItems) {
        if (!_0x248101) {
            continue;
        }
        this._itemIDs[_0x248101.name.toUpperCase().trim()] = _0x248101.id;
    }
    return this._itemIDs[_0x49c5b7] || 0x0;
};
DataManager.getWeaponIdWithName = function (_0x4ada43) {
    _0x4ada43 = _0x4ada43.toUpperCase().trim();
    this._weaponIDs = this._weaponIDs || {};
    if (this._weaponIDs[_0x4ada43]) {
        return this._weaponIDs[_0x4ada43];
    }
    for (const _0x443a00 of $dataWeapons) {
        if (!_0x443a00) {
            continue;
        }
        this._weaponIDs[_0x443a00.name.toUpperCase().trim()] = _0x443a00.id;
    }
    return this._weaponIDs[_0x4ada43] || 0x0;
};
DataManager.getArmorIdWithName = function (_0x22bded) {
    _0x22bded = _0x22bded.toUpperCase().trim();
    this._armorIDs = this._armorIDs || {};
    if (this._armorIDs[_0x22bded]) {
        return this._armorIDs[_0x22bded];
    }
    for (const _0x1cb35b of $dataArmors) {
        if (!_0x1cb35b) {
            continue;
        }
        this._armorIDs[_0x1cb35b.name.toUpperCase().trim()] = _0x1cb35b.id;
    }
    return this._armorIDs[_0x22bded] || 0x0;
};
DataManager.isState = function (_0x34f22c) {
    if (!_0x34f22c) {
        return false;
    }
    return _0x34f22c.autoRemovalTiming !== undefined && _0x34f22c.maxTurns !== undefined;
};
DataManager.getElementIdWithName = function (_0x156c14) {
    _0x156c14 = _0x156c14.toUpperCase().trim();
    this._elementIDs = this._elementIDs || {};
    if (this._elementIDs[_0x156c14]) {
        return this._elementIDs[_0x156c14];
    }
    let _0x5ae5ae = 0x1;
    for (const _0x55b3fc of $dataSystem.elements) {
        if (!_0x55b3fc) {
            continue;
        }
        let _0x148c66 = _0x55b3fc.toUpperCase();
        _0x148c66 = _0x148c66.replace(/\x1I\[(\d+)\]/gi, '');
        _0x148c66 = _0x148c66.replace(/\\I\[(\d+)\]/gi, '');
        this._elementIDs[_0x148c66] = _0x5ae5ae;
        _0x5ae5ae++;
    }
    return this._elementIDs[_0x156c14] || 0x0;
};
DataManager.getEquipPassiveCost = function (_0x4acaef) {
    if (!this.isState(_0x4acaef)) {
        return 0x0;
    }
    this._getEquipPassiveCost = this._getEquipPassiveCost || {};
    if (this._getEquipPassiveCost[_0x4acaef.id] !== undefined) {
        return this._getEquipPassiveCost[_0x4acaef.id];
    }
    let _0x1be2ae = Game_Actor.EQUIP_PASSIVE_SYS.defaultCost || 0x0;
    const _0x31fc36 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x8e36e0 = _0x4acaef.note || '';
    if (_0x8e36e0.match(_0x31fc36.EquipCost)) {
        _0x1be2ae = Number(RegExp.$1);
    }
    this._getEquipPassiveCost[_0x4acaef.id] = _0x1be2ae;
    return this._getEquipPassiveCost[_0x4acaef.id];
};
DataManager.getEquipPassiveIcon = function (_0x50cfff) {
    if (!this.isState(_0x50cfff)) {
        return 0x0;
    }
    this._getEquipPassiveIcon = this._getEquipPassiveIcon || {};
    if (this._getEquipPassiveIcon[_0x50cfff.id] !== undefined) {
        return this._getEquipPassiveIcon[_0x50cfff.id];
    }
    let _0x1d28c4 = _0x50cfff.iconIndex || ImageManager.EQUIP_PASSIVE_SYS.icon;
    const _0x2fe0c0 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x3483b5 = _0x50cfff.note || '';
    if (_0x3483b5.match(_0x2fe0c0.EquipIcon)) {
        _0x1d28c4 = Number(RegExp.$1);
    }
    this._getEquipPassiveIcon[_0x50cfff.id] = _0x1d28c4;
    return this._getEquipPassiveIcon[_0x50cfff.id];
};
DataManager.getEquipPassiveName = function (_0x59ed67) {
    if (!this.isState(_0x59ed67)) {
        return 0x0;
    }
    this._getEquipPassiveName = this._getEquipPassiveName || {};
    if (this._getEquipPassiveName[_0x59ed67.id] !== undefined) {
        return this._getEquipPassiveName[_0x59ed67.id];
    }
    let _0x2ce88b = _0x59ed67.name || '';
    const _0x5d6193 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x293a8b = _0x59ed67.note || '';
    if (_0x293a8b.match(_0x5d6193.EquipName)) {
        _0x2ce88b = String(RegExp.$1).trim();
    }
    this._getEquipPassiveName[_0x59ed67.id] = _0x2ce88b;
    return this._getEquipPassiveName[_0x59ed67.id];
};
DataManager.getLearnableEquippablePassiveIDs = function (_0x2f5056) {
    const _0x1ba2e1 = this.getLearnableEquippablePassivesFromObj(_0x2f5056.actor());
    const _0x503a08 = this.getLearnableEquippablePassivesFromObj(_0x2f5056.currentClass());
    let _0x1ada92 = _0x1ba2e1.concat(_0x503a08);
    _0x1ada92 = _0x1ada92.filter(
        (_0x4c022c, _0x322db2, _0x163da4) => _0x163da4.indexOf(_0x4c022c) === _0x322db2
    );
    return _0x1ada92;
};
DataManager.getLearnableEquippablePassivesFromObj = function (_0x8f29b3) {
    let _0x154d63 = null;
    this._cache_actor_getLearnableEquippablePassives =
        this._cache_actor_getLearnableEquippablePassives || {};
    this._cache_class_getLearnableEquippablePassives =
        this._cache_class_getLearnableEquippablePassives || {};
    if (_0x8f29b3.initialLevel !== undefined && _0x8f29b3.nickname !== undefined) {
        _0x154d63 = this._cache_actor_getLearnableEquippablePassives;
    } else {
        if (_0x8f29b3.expParams !== undefined && _0x8f29b3.learnings !== undefined) {
            _0x154d63 = this._cache_class_getLearnableEquippablePassives;
        } else {
            return [];
        }
    }
    if (_0x154d63[_0x8f29b3.id] !== undefined) {
        return _0x154d63[_0x8f29b3.id];
    }
    _0x154d63[_0x8f29b3.id] = [];
    const _0x5499a3 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x5b5bc2 = _0x8f29b3.note || '';
    if (_0x5b5bc2.match(_0x5499a3.LearnableEquipPassivesA)) {
        const _0x11ae59 = String(RegExp.$1)
            .split(',')
            .map(_0xa08cb2 => _0xa08cb2.trim());
        for (const _0x4f88dc of _0x11ae59) {
            const _0x26491c = /^\d+$/.test(_0x4f88dc);
            let _0x44964e = 0x0;
            if (_0x26491c) {
                _0x44964e = Number(_0x4f88dc);
            } else {
                _0x44964e = DataManager.getStateIdWithName(_0x4f88dc);
            }
            if (_0x44964e > 0x0) {
                _0x154d63[_0x8f29b3.id].push(_0x44964e);
            }
        }
    }
    if (_0x5b5bc2.match(_0x5499a3.LearnableEquipPassivesB)) {
        const _0x59f676 = String(RegExp.$1).split(/[\r\n]+/);
        for (const _0x48dd19 of _0x59f676) {
            const _0x45308a = /^\d+$/.test(_0x48dd19);
            let _0x22e0da = 0x0;
            if (_0x45308a) {
                _0x22e0da = Number(_0x48dd19);
            } else {
                _0x22e0da = DataManager.getStateIdWithName(_0x48dd19);
            }
            if (_0x22e0da > 0x0) {
                _0x154d63[_0x8f29b3.id].push(_0x22e0da);
            }
        }
    }
    return _0x154d63[_0x8f29b3.id];
};
ImageManager.EQUIP_PASSIVE_SYS = {
    icon: VisuMZ.EquipPassiveSys.Settings.Vocab.CommandIcon ?? 0x135,
    capacity: VisuMZ.EquipPassiveSys.Settings.Vocab.CapacityIcon ?? 0xa2,
};
TextManager.EQUIP_PASSIVE_SYS = {
    command: VisuMZ.EquipPassiveSys.Settings.Vocab.CommandName ?? 'Passives',
    capacity: VisuMZ.EquipPassiveSys.Settings.Vocab.CapacityText ?? 'Passive Capacity',
    capacityFmt: VisuMZ.EquipPassiveSys.Settings.Vocab.CapacityFmt ?? '%1/%2%3',
    costFmt: VisuMZ.EquipPassiveSys.Settings.Vocab.CostFmt ?? '%1%2',
    unlearned: VisuMZ.EquipPassiveSys.Settings.Vocab.Unlearned ?? '\\}Unlearned\\{',
    skillLearnShopStatus: VisuMZ.EquipPassiveSys.Settings.Vocab.ShopStatusText ?? 'Passive Effect',
    learnShowTextPopup: VisuMZ.EquipPassiveSys.Settings.General.LearnPopup ?? true,
    textPopupFmt: VisuMZ.EquipPassiveSys.Settings.General.TextPopupFmt ?? '%1 has learned %3%2!',
    helpFmt: VisuMZ.EquipPassiveSys.Settings.Vocab.HelpFmt ?? '\\C[16]Learn Conditions:\\C[0] %1',
    helpWordWrap: VisuMZ.EquipPassiveSys.Settings.Vocab.helpWordWrap ?? true,
    helpSpacing: VisuMZ.EquipPassiveSys.Settings.Vocab.helpSpacing ?? true,
    helpSpacer: VisuMZ.EquipPassiveSys.Settings.Vocab.helpSpacer ?? ',',
    helpNothing: VisuMZ.EquipPassiveSys.Settings.Vocab.helpNothing ?? '-',
    helpDescFmt: {
        progressFmt: VisuMZ.EquipPassiveSys.Settings.Vocab.progressFmt ?? '(Progress %1)',
        progressFraction: VisuMZ.EquipPassiveSys.Settings.Vocab.progressFraction ?? '%1/%2',
        progressPercent: VisuMZ.EquipPassiveSys.Settings.Vocab.progressPercent ?? '%1%',
        progressLengthLimit: VisuMZ.EquipPassiveSys.Settings.Vocab.progressLengthLimit ?? 0x7,
        progressComplete: VisuMZ.EquipPassiveSys.Settings.Vocab.progressComplete ?? '\\I[87]',
        level: VisuMZ.EquipPassiveSys.Settings.Vocab.level ?? 'Reach Level %1 %2',
        battle: VisuMZ.EquipPassiveSys.Settings.Vocab.battle ?? 'Fight %1 Battles %2',
        victory: VisuMZ.EquipPassiveSys.Settings.Vocab.victory ?? 'Win %1 Battles %2',
        escapes: VisuMZ.EquipPassiveSys.Settings.Vocab.escapes ?? 'Escape %1 Battles %2',
        defeat: VisuMZ.EquipPassiveSys.Settings.Vocab.defeat ?? 'Lose %1 Battles %2',
        attackTimes: VisuMZ.EquipPassiveSys.Settings.Vocab.attackTimes ?? 'Attack %1 Times %2',
        guardTimes: VisuMZ.EquipPassiveSys.Settings.Vocab.guardTimes ?? 'Guard %1 Times %2',
        skillUse: VisuMZ.EquipPassiveSys.Settings.Vocab.skillUse ?? 'Use %1 Skills %2',
        physSkillUse:
            VisuMZ.EquipPassiveSys.Settings.Vocab.physSkillUse ?? 'Use %1 Physical Skills %2',
        magSkillUse:
            VisuMZ.EquipPassiveSys.Settings.Vocab.magSkillUse ?? 'Use %1 Magical Skills %2',
        certSkillUse:
            VisuMZ.EquipPassiveSys.Settings.Vocab.certSkillUse ?? 'Use %1 Certain Hit Skills %2',
        itemUse: VisuMZ.EquipPassiveSys.Settings.Vocab.itemUse ?? 'Use %1 Items %2',
        critDeal: VisuMZ.EquipPassiveSys.Settings.Vocab.critDeal ?? 'Deal %1 Critical Hits %2',
        critTake: VisuMZ.EquipPassiveSys.Settings.Vocab.critTake ?? 'Take %1 Critical Hits %2',
        miss: VisuMZ.EquipPassiveSys.Settings.Vocab.miss ?? 'Miss %1 Times %2',
        evade: VisuMZ.EquipPassiveSys.Settings.Vocab.evade ?? 'Evade %1 Times %2',
        stypeUse: VisuMZ.EquipPassiveSys.Settings.Vocab.stypeUse ?? 'Use %1 %3 Skills %2',
        elementDeal:
            VisuMZ.EquipPassiveSys.Settings.Vocab.elementDeal ?? 'Inflict %3 Damage %1 Times %2',
        elementTake:
            VisuMZ.EquipPassiveSys.Settings.Vocab.elementTake ?? 'Receive %3 Damage %1 Times %2',
        stateDeal: VisuMZ.EquipPassiveSys.Settings.Vocab.stateDeal ?? 'Inflict %3 %1 Times %2',
        stateTake: VisuMZ.EquipPassiveSys.Settings.Vocab.stateTake ?? 'Receive %3 %1 Times %2',
        traitSlayer: VisuMZ.EquipPassiveSys.Settings.Vocab.traitSlayer ?? 'Defeat %1 %3 Enemies %2',
        totalDmgDeal:
            VisuMZ.EquipPassiveSys.Settings.Vocab.totalDmgDeal ??
            'Inflict %1 Total Battle Damage %2',
        totalDmgTake:
            VisuMZ.EquipPassiveSys.Settings.Vocab.totalDmgTake ??
            'Receive %1 Total Battle Damage %2',
        totalHealDeal:
            VisuMZ.EquipPassiveSys.Settings.Vocab.totalHealDeal ??
            'Perform %1 Total Battle Healing %2',
        totalHealTake:
            VisuMZ.EquipPassiveSys.Settings.Vocab.totalHealTake ??
            'Receive %1 Total Battle Healing %2',
        kills: VisuMZ.EquipPassiveSys.Settings.Vocab.kills ?? 'Kill %1 Enemies %2',
        deaths: VisuMZ.EquipPassiveSys.Settings.Vocab.deaths ?? 'Die %1 Times %2',
        assists: VisuMZ.EquipPassiveSys.Settings.Vocab.assists ?? 'Assist %1 Times %2',
        haveGold: VisuMZ.EquipPassiveSys.Settings.Vocab.haveGold ?? 'Possess ×%1%3 %2',
        haveItem: VisuMZ.EquipPassiveSys.Settings.Vocab.haveItem ?? 'Possess %3 ×%1 %2',
        haveWeapon: VisuMZ.EquipPassiveSys.Settings.Vocab.haveWeapon ?? 'Possess %3 ×%1 %2',
        haveArmor: VisuMZ.EquipPassiveSys.Settings.Vocab.haveArmor ?? 'Possess %3 ×%1 %2',
        haveParam: VisuMZ.EquipPassiveSys.Settings.Vocab.haveParam ?? 'Reach %1 %3 %2',
        haveXParam: VisuMZ.EquipPassiveSys.Settings.Vocab.haveXParam ?? 'Reach %1% %3 %2',
        haveSParam: VisuMZ.EquipPassiveSys.Settings.Vocab.haveSParam ?? 'Reach %1% %3 %2',
    },
    helpMeetConditionColor: VisuMZ.EquipPassiveSys.Settings.Vocab.helpMeetConditionColor ?? 0x18,
};
ColorManager.equipPassiveColor = function () {
    return this.getColor(Window_EquipPassiveList.SETTINGS.equipColor);
};
VisuMZ.EquipPassiveSys.Game_System_initialize = Game_System.prototype.initialize;
Game_System.prototype.initialize = function () {
    VisuMZ.EquipPassiveSys.Game_System_initialize.call(this);
    this.initEquipPassiveSystem();
};
Game_System.prototype.initEquipPassiveSystem = function () {
    this._equipPassiveSys_SceneSkill = Window_SkillType.EQUIP_PASSIVE_SYS.defaultShowEquipPassive;
};
Game_System.prototype.isEquipPassiveCommandVisible = function () {
    if (this._equipPassiveSys_SceneSkill === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._equipPassiveSys_SceneSkill;
};
Game_System.prototype.setEquipPassiveCommandVisible = function (_0x3c2eda) {
    if (this._equipPassiveSys_SceneSkill === undefined) {
        this.initEquipPassiveSystem();
    }
    this._equipPassiveSys_SceneSkill = _0x3c2eda;
};
Game_Actor.EQUIP_PASSIVE_SYS = {
    defaultCost: VisuMZ.EquipPassiveSys.Settings.General.DefaultCost ?? 0x1,
    minCapacity: VisuMZ.EquipPassiveSys.Settings.General.MinimumCost ?? 0x1,
    maxCapacity: VisuMZ.EquipPassiveSys.Settings.General.MaximumCost ?? 0x64,
    capacityFormula:
        VisuMZ.EquipPassiveSys.Settings.General.CapacityFormula ?? 'Math.ceil(user.level / 5) * 5',
    checkOverCapacityExp: VisuMZ.EquipPassiveSys.Settings.General.CheckOverCapacity ?? true,
    learnAutoEquip: VisuMZ.EquipPassiveSys.Settings.General.LearnAutoEquip ?? true,
};
VisuMZ.EquipPassiveSys.Game_Actor_setup = Game_Actor.prototype.setup;
Game_Actor.prototype.setup = function (_0x312f4f) {
    VisuMZ.EquipPassiveSys.Game_Actor_setup.call(this, _0x312f4f);
    this.initEquipPassiveSystem();
};
Game_Actor.prototype.initEquipPassiveSystem = function () {
    this._learnedEquippablePassives = [];
    this._learnableEquippablePassives = [];
    this._equippedPassives = [];
    if (VisuMZ.EquipPassiveSys.DEBUG) {
        for (let _0x37eeae = 0x4; _0x37eeae <= 0x1f; _0x37eeae++) {
            const _0x42adb6 = $dataStates[_0x37eeae];
            if (_0x42adb6.name === '') {
                continue;
            }
            if (_0x42adb6.name.includes('-----')) {
                continue;
            }
            this._learnedEquippablePassives.push(_0x37eeae);
        }
    }
    this.setupInitialLearnedEquippablePassives();
    this.setupInitialAlreadyEquippedPassives();
    this._learnedEquippablePassives.sort((_0x1546bf, _0x13a0aa) => _0x1546bf - _0x13a0aa);
};
Game_Actor.prototype.equippedPassives = function () {
    if (this._equippedPassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._equippedPassives
        .map(_0x4f99d4 => $dataStates[_0x4f99d4])
        .remove(null)
        .remove(undefined);
};
Game_Actor.prototype.equippedPassivesRawIDs = function () {
    if (this._equippedPassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._equippedPassives;
};
Game_Actor.prototype.isEquippablePassiveEquipped = function (_0x4f9f37) {
    if (!DataManager.isState(_0x4f9f37)) {
        return false;
    }
    if (this._equippedPassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._equippedPassives.includes(_0x4f9f37.id);
};
Game_Actor.prototype.processEquipPassive = function (_0x119e70) {
    if (!DataManager.isState(_0x119e70)) {
        return false;
    }
    if (this._equippedPassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._equippedPassives.includes(_0x119e70.id)) {
        return;
    }
    let _0x1e373c = null;
    if (!this._tempActor && Imported.VisuMZ_1_ItemsEquipsCore) {
        _0x1e373c = JsonEx.makeDeepCopy(this);
        _0x1e373c._tempActor = true;
    }
    this._equippedPassives.push(_0x119e70.id);
    this._cache = {};
    this.refresh();
    if (!this._tempActor && Imported.VisuMZ_1_ItemsEquipsCore) {
        this.equipAdjustHpMp(_0x1e373c);
    }
};
Game_Actor.prototype.removeEquipPassive = function (_0x43d8c3) {
    if (!DataManager.isState(_0x43d8c3)) {
        return false;
    }
    if (this._equippedPassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (!this._equippedPassives.includes(_0x43d8c3.id)) {
        return;
    }
    let _0x63d0a3 = null;
    if (!this._tempActor && Imported.VisuMZ_1_ItemsEquipsCore) {
        _0x63d0a3 = JsonEx.makeDeepCopy(this);
        _0x63d0a3._tempActor = true;
    }
    this._equippedPassives.remove(_0x43d8c3.id);
    this._cache = {};
    this.refresh();
    if (!this._tempActor && Imported.VisuMZ_1_ItemsEquipsCore) {
        this.equipAdjustHpMp(_0x63d0a3);
    }
};
Game_Actor.prototype.learnEquippedPassive = function (_0x176085, _0x162456) {
    if (!DataManager.isState(_0x176085)) {
        return;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._learnedEquippablePassives.includes(_0x176085.id)) {
        return;
    }
    this._learnedEquippablePassives.push(_0x176085.id);
    this._learnedEquippablePassives.sort((_0x3561cc, _0x19974b) => _0x3561cc - _0x19974b);
    if (Game_Actor.EQUIP_PASSIVE_SYS.learnAutoEquip && $gameSystem.isEquipPassiveCommandVisible()) {
        if (this.hasEquipPassiveCapacityFor(_0x176085)) {
            this.processEquipPassive(_0x176085);
        }
    }
    if (
        !_0x162456 &&
        TextManager.EQUIP_PASSIVE_SYS.learnShowTextPopup &&
        this === $gameActors.actor(this.actorId())
    ) {
        const _0x55e7fc = TextManager.EQUIP_PASSIVE_SYS.textPopupFmt;
        const _0x173ed8 = DataManager.getEquipPassiveName(_0x176085);
        const _0x1dfea9 = '\\I[%1]'.format(DataManager.getEquipPassiveIcon(_0x176085));
        const _0x14cce7 = _0x55e7fc.format(this.name(), _0x173ed8, _0x1dfea9);
        $textPopup(_0x14cce7);
    }
    this.branchLearnEquipPassives(_0x176085, _0x162456);
    this.branchLearnableEquipPassives(_0x176085);
    this.refresh();
};
Game_Actor.prototype.forgetEquippedPassive = function (_0x1c508d) {
    if (!DataManager.isState(_0x1c508d)) {
        return;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (!this._learnedEquippablePassives.includes(_0x1c508d.id)) {
        return;
    }
    this._learnedEquippablePassives.remove(_0x1c508d.id);
    this._equippedPassives.remove(_0x1c508d.id);
    this._learnedEquippablePassives.sort((_0x35a7f5, _0x4eb0a7) => _0x35a7f5 - _0x4eb0a7);
    this.refresh();
};
Game_Actor.prototype.isLearnedEquippedPassive = function (_0x4b0f1) {
    if (!DataManager.isState(_0x4b0f1)) {
        return false;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return (
        this._learnedEquippablePassives.includes(_0x4b0f1.id) ||
        $gameParty.isLearnedEquippedPassive(_0x4b0f1)
    );
};
Game_Actor.prototype.addUnlearnedEquippablePassive = function (_0x148bd0) {
    if (!DataManager.isState(_0x148bd0)) {
        return;
    }
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._learnableEquippablePassives.includes(_0x148bd0.id)) {
        return;
    }
    this._learnableEquippablePassives.push(_0x148bd0.id);
    this._learnableEquippablePassives.sort((_0x5aaa29, _0x59b5d1) => _0x5aaa29 - _0x59b5d1);
};
Game_Actor.prototype.removeUnlearnedEquippablePassive = function (_0x371f78) {
    if (!DataManager.isState(_0x371f78)) {
        return;
    }
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (!this._learnableEquippablePassives.includes(_0x371f78.id)) {
        return;
    }
    this._learnableEquippablePassives.remove(_0x371f78.id);
    this._learnableEquippablePassives.sort((_0x30c436, _0x1196a9) => _0x30c436 - _0x1196a9);
};
Game_Actor.prototype.setupInitialLearnedEquippablePassives = function () {
    const _0x2884cb = VisuMZ.EquipPassiveSys.RegExp;
    const _0x3c75f6 = this.actor().note || '';
    if (_0x3c75f6.match(_0x2884cb.LearnedEquipPassives)) {
        const _0x3382d0 = String(RegExp.$1)
            .split(',')
            .map(_0x2871ea => _0x2871ea.trim());
        for (const _0x373869 of _0x3382d0) {
            const _0x55432c = /^\d+$/.test(_0x373869);
            let _0x411add = 0x0;
            if (_0x55432c) {
                _0x411add = Number(_0x373869);
            } else {
                _0x411add = DataManager.getStateIdWithName(_0x373869);
            }
            if (_0x411add > 0x0) {
                if (!this._learnedEquippablePassives.includes(_0x411add)) {
                    this._learnedEquippablePassives.push(_0x411add);
                }
            }
        }
    }
};
Game_Actor.prototype.setupInitialAlreadyEquippedPassives = function () {
    const _0xf4f692 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x580a8d = this.actor().note || '';
    if (_0x580a8d.match(_0xf4f692.AlreadyEquipPassives)) {
        const _0x47411b = String(RegExp.$1)
            .split(',')
            .map(_0x38ecca => _0x38ecca.trim());
        for (const _0xd35405 of _0x47411b) {
            const _0x45f8a1 = /^\d+$/.test(_0xd35405);
            let _0x4a10c4 = 0x0;
            if (_0x45f8a1) {
                _0x4a10c4 = Number(_0xd35405);
            } else {
                _0x4a10c4 = DataManager.getStateIdWithName(_0xd35405);
            }
            if (_0x4a10c4 > 0x0) {
                if (!this._learnedEquippablePassives.includes(_0x4a10c4)) {
                    this._learnedEquippablePassives.push(_0x4a10c4);
                }
                const _0x2d0ed1 = $dataStates[_0x4a10c4];
                if (this.hasEquipPassiveCapacityFor(_0x2d0ed1)) {
                    this.processEquipPassive(_0x2d0ed1);
                }
            }
        }
    }
};
Game_Actor.prototype.availableLearnedEquippablePassives = function () {
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    let _0x523ef6 = [];
    _0x523ef6 = _0x523ef6.concat(this._learnedEquippablePassives);
    _0x523ef6 = _0x523ef6.concat($gameParty.getLearnEquippedPassiveRawIDs());
    _0x523ef6 = _0x523ef6.filter(
        (_0x8167cf, _0x3c4fd0, _0x6541d9) => _0x6541d9.indexOf(_0x8167cf) === _0x3c4fd0
    );
    return _0x523ef6
        .map(_0x218ff7 => $dataStates[_0x218ff7])
        .remove(null)
        .remove(undefined);
};
Game_Actor.prototype.availableUnlearnedEquippablePassives = function () {
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    let _0x31072f = this._learnableEquippablePassives.clone();
    _0x31072f = _0x31072f.concat(DataManager.getLearnableEquippablePassiveIDs(this));
    _0x31072f = _0x31072f.concat($gameParty.getUnlearnedEquippedPassiveRawIDs());
    _0x31072f = _0x31072f.filter(_0x59ed50 => !this._learnedEquippablePassives.includes(_0x59ed50));
    _0x31072f = _0x31072f.filter(
        (_0x12202c, _0x4cab39, _0x5d06de) => _0x5d06de.indexOf(_0x12202c) === _0x4cab39
    );
    return _0x31072f
        .map(_0x2e964e => $dataStates[_0x2e964e])
        .remove(null)
        .remove(undefined);
};
Game_Actor.prototype.branchLearnEquipPassives = function (_0x824625, _0x594844) {
    const _0x401894 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x41275b = _0x824625.note || '';
    if (_0x41275b.match(_0x401894.BranchLearn)) {
        const _0x539519 = String(RegExp.$1)
            .split(',')
            .map(_0x37f5f2 => _0x37f5f2.trim());
        for (const _0x5149ae of _0x539519) {
            const _0x447234 = /^\d+$/.test(_0x5149ae);
            let _0xf3de21 = 0x0;
            if (_0x447234) {
                _0xf3de21 = Number(_0x5149ae);
            } else {
                _0xf3de21 = DataManager.getStateIdWithName(_0x5149ae);
            }
            if (_0xf3de21 > 0x0) {
                this.learnEquippedPassive($dataStates[_0xf3de21], _0x594844);
            }
        }
    }
};
Game_Actor.prototype.branchLearnableEquipPassives = function (_0x4a3d69) {
    const _0x1acba5 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x3bfac0 = _0x4a3d69.note || '';
    if (_0x3bfac0.match(_0x1acba5.BranchLearnable)) {
        const _0x51cd15 = String(RegExp.$1)
            .split(',')
            .map(_0x5474d5 => _0x5474d5.trim());
        for (const _0x25ee80 of _0x51cd15) {
            const _0x5618e8 = /^\d+$/.test(_0x25ee80);
            let _0xd0af23 = 0x0;
            if (_0x5618e8) {
                _0xd0af23 = Number(_0x25ee80);
            } else {
                _0xd0af23 = DataManager.getStateIdWithName(_0x25ee80);
            }
            if (_0xd0af23 > 0x0) {
                this.addUnlearnedEquippablePassive($dataStates[_0xd0af23]);
            }
        }
    }
};
VisuMZ.EquipPassiveSys.Game_BattlerBase_addPassiveStatesFromOtherPlugins =
    Game_BattlerBase.prototype.addPassiveStatesFromOtherPlugins;
Game_BattlerBase.prototype.addPassiveStatesFromOtherPlugins = function () {
    VisuMZ.EquipPassiveSys.Game_BattlerBase_addPassiveStatesFromOtherPlugins.call(this);
    this.addEquippablePassiveStates();
};
Game_BattlerBase.prototype.addEquippablePassiveStates = function () {};
Game_Actor.prototype.addEquippablePassiveStates = function () {
    const _0xeac33d = this._cache.passiveStates;
    const _0x234b6f = this.equippedPassivesRawIDs();
    this._cache.passiveStates = _0xeac33d.concat(_0x234b6f);
};
Game_Actor.prototype.equipPassiveCurrentCapacity = function () {
    return this.equippedPassives().reduce(
        (_0xf3bd04, _0x18ec3d) => _0xf3bd04 + DataManager.getEquipPassiveCost(_0x18ec3d),
        0x0
    );
};
Game_Actor.prototype.equipPassiveMaxCapacity = function () {
    const _0x3609f4 = Game_Actor.EQUIP_PASSIVE_SYS;
    const _0x587b57 = _0x3609f4.capacityFormula;
    let _0x4827b9 = _0x3609f4.minCapacity;
    try {
        window.user = this;
        _0x4827b9 = eval(_0x587b57);
        window.user = undefined;
    } catch (_0x4a5588) {
        _0x4827b9 = _0x3609f4.minCapacity;
    }
    _0x4827b9 = _0x4827b9.clamp(_0x3609f4.minCapacity, _0x3609f4.maxCapacity);
    return _0x4827b9;
};
Game_Actor.prototype.hasEquipPassiveCapacityFor = function (_0x2f4089) {
    if (!_0x2f4089) {
        return false;
    }
    const _0xee2613 = DataManager.getEquipPassiveCost(_0x2f4089);
    const _0x4b8fd3 = this.equipPassiveCurrentCapacity();
    const _0x25de2e = this.equipPassiveMaxCapacity();
    return _0x4b8fd3 + _0xee2613 <= _0x25de2e;
};
Game_Actor.prototype.releaseOverEquipPassiveCapacity = function () {
    if (!Game_Actor.EQUIP_PASSIVE_SYS.checkOverCapacityExp) {
        return;
    }
    let _0x3165d1 = false;
    for (;;) {
        if (this.equippedPassives().length <= 0x0) {
            break;
        }
        if (this.equipPassiveCurrentCapacity() <= this.equipPassiveMaxCapacity()) {
            break;
        }
        const _0x247c07 = this.equippedPassives().pop();
        this.removeEquipPassive(_0x247c07);
        _0x3165d1 = true;
    }
    if (_0x3165d1) {
        this.refresh();
    }
};
VisuMZ.EquipPassiveSys.Game_Actor_changeExp = Game_Actor.prototype.changeExp;
Game_Actor.prototype.changeExp = function (_0x586405, _0x2f7cb7) {
    const _0x1569aa = this._level;
    VisuMZ.EquipPassiveSys.Game_Actor_changeExp.call(this, _0x586405, _0x2f7cb7);
    if (this._level === _0x1569aa) {
        return;
    }
    this.releaseOverEquipPassiveCapacity();
    this.checkLearnNewEquipPassives();
};
VisuMZ.EquipPassiveSys.Game_Battler_onBattleEnd = Game_Battler.prototype.onBattleEnd;
Game_Battler.prototype.onBattleEnd = function () {
    VisuMZ.EquipPassiveSys.Game_Battler_onBattleEnd.call(this);
    if (this.isActor()) {
        this.checkLearnNewEquipPassives();
    }
};
Game_Actor.prototype.checkLearnNewEquipPassives = function () {
    const _0x33f049 = this.availableUnlearnedEquippablePassives();
    for (const _0x487cf4 of _0x33f049) {
        if (!_0x487cf4) {
            continue;
        }
        if (this.isLearnedEquippedPassive(_0x487cf4)) {
            continue;
        }
        if (VisuMZ.EquipPassiveSys.MeetsLearnConditions(this, _0x487cf4)) {
            this.learnEquippedPassive(_0x487cf4);
        }
    }
};
VisuMZ.EquipPassiveSys.MeetsLearnConditions = function (_0x448e60, _0x25425c) {
    const _0x2acc88 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x3d66e1 = _0x25425c.note || '';
    let _0xa181f1 = false;
    if (!_0x3d66e1.match(_0x2acc88.LearnAny)) {
        return _0xa181f1;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnLevel)) {
        const _0x1bcb67 = Number(RegExp.$1);
        if (_0x1bcb67 > _0x448e60.level) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnBattles)) {
        const _0x5418f2 = Number(RegExp.$1);
        if (_0x5418f2 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'battle', 'all')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnVictory)) {
        const _0x4864a8 = Number(RegExp.$1);
        if (_0x4864a8 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'battle', 'victory')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnEscapes)) {
        const _0x28a3d7 = Number(RegExp.$1);
        if (_0x28a3d7 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'battle', 'escape')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnDefeats)) {
        const _0x3ce001 = Number(RegExp.$1);
        if (_0x3ce001 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'battle', 'defeat')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnAttackTimes)) {
        const _0x17782b = Number(RegExp.$1);
        if (
            _0x17782b > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'attack')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnGuardTimes)) {
        const _0x2b61f7 = Number(RegExp.$1);
        if (_0x2b61f7 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'guard')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnSkillUsage)) {
        const _0x138c91 = Number(RegExp.$1);
        if (_0x138c91 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'skill')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnPhysSkill)) {
        const _0x57b46b = Number(RegExp.$1);
        if (
            _0x57b46b >
            _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'physicalSkill')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnMagSkill)) {
        const _0x42a529 = Number(RegExp.$1);
        if (
            _0x42a529 >
            _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'magicalSkill')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnCertSkill)) {
        const _0xe29518 = Number(RegExp.$1);
        if (
            _0xe29518 >
            _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'certainHitSkill')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnItemUsage)) {
        const _0x2d0dd6 = Number(RegExp.$1);
        if (_0x2d0dd6 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'item')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnDealCritHitTimes)) {
        const _0x5e5274 = Number(RegExp.$1);
        if (
            _0x5e5274 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'critDeal')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnTakeCritHitTimes)) {
        const _0x1ce8ca = Number(RegExp.$1);
        if (
            _0x1ce8ca > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'critTake')
        ) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnMissTimes)) {
        const _0x2d8ed3 = Number(RegExp.$1);
        if (_0x2d8ed3 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'miss')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnEvadeTimes)) {
        const _0x237956 = Number(RegExp.$1);
        if (_0x237956 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'actionTimes', 'evade')) {
            return false;
        }
        _0xa181f1 = true;
    }
    {
        const _0x19366c = _0x3d66e1.match(_0x2acc88.LearnSTypeUsage);
        if (_0x19366c) {
            for (const _0x5ad379 of _0x19366c) {
                _0x5ad379.match(_0x2acc88.LearnSTypeUsage);
                let _0x2afbcc = String(RegExp.$1);
                const _0x8bf2ba = Number(RegExp.$2);
                const _0x401173 = /^\d+$/.test(_0x2afbcc);
                _0x2afbcc = _0x401173
                    ? Number(_0x2afbcc)
                    : DataManager.getStypeIdWithName(_0x2afbcc);
                if (
                    _0x8bf2ba >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'stype', _0x2afbcc)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x3efae = _0x3d66e1.match(_0x2acc88.LearnElementDeal);
        if (_0x3efae) {
            for (const _0x377877 of _0x3efae) {
                _0x377877.match(_0x2acc88.LearnElementDeal);
                let _0x51f1d2 = String(RegExp.$1);
                const _0x5e3df9 = Number(RegExp.$2);
                const _0x4366f5 = /^\d+$/.test(_0x51f1d2);
                _0x51f1d2 = _0x4366f5
                    ? Number(_0x51f1d2)
                    : DataManager.getElementIdWithName(_0x51f1d2);
                if (
                    _0x5e3df9 >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'elementDeal', _0x51f1d2)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x240559 = _0x3d66e1.match(_0x2acc88.LearnElementTake);
        if (_0x240559) {
            for (const _0xe9d2c7 of _0x240559) {
                _0xe9d2c7.match(_0x2acc88.LearnElementTake);
                let _0x33033d = String(RegExp.$1);
                const _0x297adc = Number(RegExp.$2);
                const _0x2eae91 = /^\d+$/.test(_0x33033d);
                _0x33033d = _0x2eae91
                    ? Number(_0x33033d)
                    : DataManager.getElementIdWithName(_0x33033d);
                if (
                    _0x297adc >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'elementTake', _0x33033d)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x5d9598 = _0x3d66e1.match(_0x2acc88.LearnStateDeal);
        if (_0x5d9598) {
            for (const _0x4adfb1 of _0x5d9598) {
                _0x4adfb1.match(_0x2acc88.LearnStateDeal);
                let _0x14af33 = String(RegExp.$1);
                const _0x5544a3 = Number(RegExp.$2);
                _0x14af33 = DataManager.getStateIdWithName(_0x14af33);
                if (
                    _0x5544a3 >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'stateDeal', _0x14af33)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x4ca889 = _0x3d66e1.match(_0x2acc88.LearnStateTake);
        if (_0x4ca889) {
            for (const _0x1aef42 of _0x4ca889) {
                _0x1aef42.match(_0x2acc88.LearnStateTake);
                let _0x137f09 = String(RegExp.$1);
                const _0x50371e = Number(RegExp.$2);
                _0x137f09 = DataManager.getStateIdWithName(_0x137f09);
                if (
                    _0x50371e >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'stateTake', _0x137f09)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    if (Imported.VisuMZ_1_ElementStatusCore) {
        const _0x1aff2a = _0x3d66e1.match(_0x2acc88.LearnDefeatTrait);
        if (_0x1aff2a) {
            for (const _0x1b9832 of _0x1aff2a) {
                _0x1b9832.match(_0x2acc88.LearnDefeatTrait);
                const _0x104955 = String(RegExp.$1).toUpperCase().trim();
                const _0x2c98c9 = Number(RegExp.$2);
                if (
                    _0x2c98c9 >
                    _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'traitSlayer', _0x104955)
                ) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    if (_0x3d66e1.match(_0x2acc88.LearnTotalDmgDeal)) {
        const _0x1be784 = Number(RegExp.$1);
        if (_0x1be784 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'hp', 'dmgDeal')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnTotalDmgTake)) {
        const _0x15c007 = Number(RegExp.$1);
        if (_0x15c007 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'hp', 'dmgTake')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnTotalHealDeal)) {
        const _0x367ab0 = Number(RegExp.$1);
        if (_0x367ab0 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'hp', 'healDeal')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnTotalHealTake)) {
        const _0x14fb79 = Number(RegExp.$1);
        if (_0x14fb79 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'hp', 'healTake')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnCountKills)) {
        const _0x295b32 = Number(RegExp.$1);
        if (_0x295b32 > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'kda', 'kills')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnCountDeaths)) {
        const _0x371c5e = Number(RegExp.$1);
        if (_0x371c5e > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'kda', 'deaths')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnCountAssists)) {
        const _0x42695d = Number(RegExp.$1);
        if (_0x42695d > _0x448e60.getEquipPassiveLearnProgress(_0x25425c, 'kda', 'assists')) {
            return false;
        }
        _0xa181f1 = true;
    }
    if (_0x3d66e1.match(_0x2acc88.LearnHaveGold)) {
        const _0x3359a2 = Number(RegExp.$1);
        if (_0x3359a2 > $gameParty.gold()) {
            return false;
        }
        _0xa181f1 = true;
    }
    {
        const _0x526668 = _0x3d66e1.match(_0x2acc88.LearnHaveItem);
        if (_0x526668) {
            for (const _0x49addf of _0x526668) {
                _0x49addf.match(_0x2acc88.LearnHaveItem);
                const _0x3207aa = String(RegExp.$1);
                const _0x477e7b = Number(RegExp.$2);
                const _0x169af5 = /^\d+$/.test(_0x3207aa);
                const _0xfc3e76 = _0x169af5
                    ? Number(_0x3207aa)
                    : DataManager.getItemIdWithName(_0x3207aa);
                const _0x24b188 = $dataItems[_0xfc3e76];
                if (!_0x24b188) {
                    continue;
                }
                if (_0x477e7b > $gameParty.numItems(_0x24b188)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x3b3c20 = _0x3d66e1.match(_0x2acc88.LearnHaveWeapon);
        if (_0x3b3c20) {
            for (const _0x17ef0a of _0x3b3c20) {
                _0x17ef0a.match(_0x2acc88.LearnHaveWeapon);
                const _0x1ff202 = String(RegExp.$1);
                const _0x573f2a = Number(RegExp.$2);
                const _0x400205 = /^\d+$/.test(_0x1ff202);
                const _0x1b15b1 = _0x400205
                    ? Number(_0x1ff202)
                    : DataManager.getWeaponIdWithName(_0x1ff202);
                const _0x175d1f = $dataWeapons[_0x1b15b1];
                if (!_0x175d1f) {
                    continue;
                }
                if (_0x573f2a > $gameParty.numItems(_0x175d1f)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x4f0b01 = _0x3d66e1.match(_0x2acc88.LearnHaveArmor);
        if (_0x4f0b01) {
            for (const _0x52ce03 of _0x4f0b01) {
                _0x52ce03.match(_0x2acc88.LearnHaveArmor);
                const _0x7ab175 = String(RegExp.$1);
                const _0x156042 = Number(RegExp.$2);
                const _0x1bd429 = /^\d+$/.test(_0x7ab175);
                const _0x5e8df7 = _0x1bd429
                    ? Number(_0x7ab175)
                    : DataManager.getArmorIdWithName(_0x7ab175);
                const _0x470431 = $dataArmors[_0x5e8df7];
                if (!_0x470431) {
                    continue;
                }
                if (_0x156042 > $gameParty.numItems(_0x470431)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x2f5827 = _0x3d66e1.match(_0x2acc88.LearnHaveParam);
        if (_0x2f5827) {
            for (const _0x3a106e of _0x2f5827) {
                _0x3a106e.match(_0x2acc88.LearnHaveParam);
                let _0x257ba8 = String(RegExp.$1).toUpperCase().trim();
                const _0x58c77a = Number(RegExp.$2);
                if (_0x257ba8 === 'MHP') {
                    _0x257ba8 = 'MAXHP';
                }
                if (_0x257ba8 === 'MAX HP') {
                    _0x257ba8 = 'MAXHP';
                }
                if (_0x257ba8 === 'MMP') {
                    _0x257ba8 = 'MAXMP';
                }
                if (_0x257ba8 === 'MAX MP') {
                    _0x257ba8 = 'MAXMP';
                }
                const _0x481e64 = ['MAXHP', 'MAXMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
                const _0x274027 = _0x481e64.indexOf(_0x257ba8);
                if (_0x58c77a > _0x448e60.param(_0x274027)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x23a01f = _0x3d66e1.match(_0x2acc88.LearnHaveXParam);
        if (_0x23a01f) {
            for (const _0x1cc345 of _0x23a01f) {
                _0x1cc345.match(_0x2acc88.LearnHaveXParam);
                let _0x130b6a = String(RegExp.$1).toUpperCase().trim();
                const _0x12f021 = Number(RegExp.$2) * 0.01;
                const _0x29cdb5 = [
                    'HIT',
                    'EVA',
                    'CRI',
                    'CEV',
                    'MEV',
                    'MRF',
                    'CNT',
                    'HRG',
                    'MRG',
                    'TRG',
                ];
                const _0x2f1af2 = _0x29cdb5.indexOf(_0x130b6a);
                if (_0x12f021 > _0x448e60.xparam(_0x2f1af2)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    {
        const _0x37702a = _0x3d66e1.match(_0x2acc88.LearnHaveSParam);
        if (_0x37702a) {
            for (const _0x57bc7b of _0x37702a) {
                _0x57bc7b.match(_0x2acc88.LearnHaveSParam);
                let _0x449a78 = String(RegExp.$1).toUpperCase().trim();
                const _0x434c16 = Number(RegExp.$2) * 0.01;
                const _0x136696 = [
                    'TGR',
                    'GRD',
                    'REC',
                    'PHA',
                    'MCR',
                    'TCR',
                    'PDR',
                    'MDR',
                    'FDR',
                    'EXR',
                ];
                const _0x40f648 = _0x136696.indexOf(_0x449a78);
                if (_0x434c16 > _0x448e60.sparam(_0x40f648)) {
                    return false;
                }
                _0xa181f1 = true;
            }
        }
    }
    return _0xa181f1;
};
Game_Actor.prototype.getEquipPassiveLearnProgress = function (_0x531a58, _0x51f78c, _0x164166) {
    _0x531a58 = _0x531a58.id || _0x531a58;
    this._equipPassiveLearnProgress = this._equipPassiveLearnProgress || {};
    this._equipPassiveLearnProgress[_0x531a58] = this._equipPassiveLearnProgress[_0x531a58] || {};
    this._equipPassiveLearnProgress[_0x531a58][_0x51f78c] =
        this._equipPassiveLearnProgress[_0x531a58][_0x51f78c] || {};
    this._equipPassiveLearnProgress[_0x531a58][_0x51f78c][_0x164166] =
        this._equipPassiveLearnProgress[_0x531a58][_0x51f78c][_0x164166] || 0x0;
    return this._equipPassiveLearnProgress[_0x531a58][_0x51f78c][_0x164166];
};
Game_Actor.prototype.updateEquipPassiveLearnProgress = function (
    _0x543e49,
    _0x2aecf1,
    _0x49fe94,
    _0x2a53ed
) {
    _0x543e49 = _0x543e49.id || _0x543e49;
    this._equipPassiveLearnProgress = this._equipPassiveLearnProgress || {};
    this._equipPassiveLearnProgress[_0x543e49] = this._equipPassiveLearnProgress[_0x543e49] || {};
    this._equipPassiveLearnProgress[_0x543e49][_0x2aecf1] =
        this._equipPassiveLearnProgress[_0x543e49][_0x2aecf1] || {};
    this._equipPassiveLearnProgress[_0x543e49][_0x2aecf1][_0x49fe94] =
        this._equipPassiveLearnProgress[_0x543e49][_0x2aecf1][_0x49fe94] || 0x0;
    this._equipPassiveLearnProgress[_0x543e49][_0x2aecf1][_0x49fe94] += _0x2a53ed;
};
VisuMZ.EquipPassiveSys.Game_Battler_processBattleCoreJS =
    Game_Battler.prototype.processBattleCoreJS;
Game_Battler.prototype.processBattleCoreJS = function (_0xc9a87b) {
    VisuMZ.EquipPassiveSys.Game_Battler_processBattleCoreJS.call(this, _0xc9a87b);
    if (!this.isActor()) {
        return;
    }
    if (_0xc9a87b === 'BattleVictoryJS') {
        this.updateEquipPassiveBattleVictory();
    } else {
        if (_0xc9a87b === 'EscapeSuccessJS') {
            this.updateEquipPassiveBattleEscape();
        } else {
            if (_0xc9a87b === 'BattleDefeatJS') {
                this.updateEquipPassiveBattleDefeat();
            }
        }
    }
};
Game_Actor.prototype.updateEquipPassiveBattleVictory = function () {
    const _0x38a1cf = VisuMZ.EquipPassiveSys.RegExp;
    const _0x2c547b = this.availableUnlearnedEquippablePassives();
    for (const _0x544dfb of _0x2c547b) {
        if (!_0x544dfb) {
            continue;
        }
        const _0x4128d1 = _0x544dfb.note || '';
        if (_0x4128d1.match(_0x38a1cf.LearnBattles)) {
            this.updateEquipPassiveLearnProgress(_0x544dfb, 'battle', 'all', 0x1);
        }
        if (_0x4128d1.match(_0x38a1cf.LearnVictory)) {
            this.updateEquipPassiveLearnProgress(_0x544dfb, 'battle', 'victory', 0x1);
        }
    }
};
Game_Actor.prototype.updateEquipPassiveBattleEscape = function () {
    const _0x2f8dd9 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x4b463f = this.availableUnlearnedEquippablePassives();
    for (const _0x3a06eb of _0x4b463f) {
        if (!_0x3a06eb) {
            continue;
        }
        const _0xd51367 = _0x3a06eb.note || '';
        if (_0xd51367.match(_0x2f8dd9.LearnBattles)) {
            this.updateEquipPassiveLearnProgress(_0x3a06eb, 'battle', 'all', 0x1);
        }
        if (_0xd51367.match(_0x2f8dd9.LearnVictory)) {
            this.updateEquipPassiveLearnProgress(_0x3a06eb, 'battle', 'escape', 0x1);
        }
    }
};
Game_Actor.prototype.updateEquipPassiveBattleDefeat = function () {
    const _0x1eed59 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x1ddf78 = this.availableUnlearnedEquippablePassives();
    for (const _0x1d15ca of _0x1ddf78) {
        if (!_0x1d15ca) {
            continue;
        }
        const _0x424452 = _0x1d15ca.note || '';
        if (_0x424452.match(_0x1eed59.LearnBattles)) {
            this.updateEquipPassiveLearnProgress(_0x1d15ca, 'battle', 'all', 0x1);
        }
        if (_0x424452.match(_0x1eed59.LearnVictory)) {
            this.updateEquipPassiveLearnProgress(_0x1d15ca, 'battle', 'defeat', 0x1);
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Action_applyGlobal = Game_Action.prototype.applyGlobal;
Game_Action.prototype.applyGlobal = function () {
    VisuMZ.EquipPassiveSys.Game_Action_applyGlobal.call(this);
    if (this.subject() && this.subject().isActor()) {
        this.subject().updateEquipPassiveActionUsage(this);
    }
};
Game_Actor.prototype.updateEquipPassiveActionUsage = function (_0x58cdc4) {
    const _0x59981d = VisuMZ.EquipPassiveSys.RegExp;
    const _0x59b81a = this.availableUnlearnedEquippablePassives();
    for (const _0x41dea6 of _0x59b81a) {
        if (!_0x41dea6) {
            continue;
        }
        const _0x56aaee = _0x41dea6.note || '';
        if (_0x56aaee.match(_0x59981d.LearnAttackTimes)) {
            if (_0x58cdc4.isAttack()) {
                this.updateEquipPassiveLearnProgress(_0x41dea6, 'actionTimes', 'attack', 0x1);
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnGuardTimes)) {
            if (_0x58cdc4.isGuard()) {
                this.updateEquipPassiveLearnProgress(_0x41dea6, 'actionTimes', 'guard', 0x1);
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnSkillUsage)) {
            if (_0x58cdc4.isSkill() && !_0x58cdc4.isAttack() && !_0x58cdc4.isGuard()) {
                this.updateEquipPassiveLearnProgress(_0x41dea6, 'actionTimes', 'skill', 0x1);
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnPhysSkill)) {
            if (
                _0x58cdc4.isSkill() &&
                _0x58cdc4.isPhysical() &&
                !_0x58cdc4.isAttack() &&
                !_0x58cdc4.isGuard()
            ) {
                this.updateEquipPassiveLearnProgress(
                    _0x41dea6,
                    'actionTimes',
                    'physicalSkill',
                    0x1
                );
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnMagSkill)) {
            if (
                _0x58cdc4.isSkill() &&
                _0x58cdc4.isMagical() &&
                !_0x58cdc4.isAttack() &&
                !_0x58cdc4.isGuard()
            ) {
                this.updateEquipPassiveLearnProgress(_0x41dea6, 'actionTimes', 'magicalSkill', 0x1);
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnCertSkill)) {
            if (
                _0x58cdc4.isSkill() &&
                _0x58cdc4.isCertainHit() &&
                !_0x58cdc4.isAttack() &&
                !_0x58cdc4.isGuard()
            ) {
                this.updateEquipPassiveLearnProgress(
                    _0x41dea6,
                    'actionTimes',
                    'certainHitSkill',
                    0x1
                );
            }
        }
        if (_0x56aaee.match(_0x59981d.LearnItemUsage)) {
            if (_0x58cdc4.isItem()) {
                this.updateEquipPassiveLearnProgress(_0x41dea6, 'actionTimes', 'item', 0x1);
            }
        }
        {
            const _0x1cbac0 = _0x56aaee.match(_0x59981d.LearnSTypeUsage);
            if (_0x1cbac0 && _0x58cdc4.isSkill() && !_0x58cdc4.isAttack() && !_0x58cdc4.isGuard()) {
                for (const _0x51bd12 of _0x1cbac0) {
                    _0x51bd12.match(_0x59981d.LearnSTypeUsage);
                    let _0x111bf8 = String(RegExp.$1);
                    const _0x227647 = /^\d+$/.test(_0x111bf8);
                    _0x111bf8 = _0x227647
                        ? Number(_0x111bf8)
                        : DataManager.getStypeIdWithName(_0x111bf8);
                    if (Imported.VisuMZ_1_SkillsStatesCore) {
                        const _0x48f85d = DataManager.getSkillTypes(_0x58cdc4.item());
                        if (!_0x48f85d.includes(_0x111bf8)) {
                            continue;
                        }
                    } else {
                        if (_0x58cdc4.item().stypeId !== _0x111bf8) {
                            continue;
                        }
                    }
                    this.updateEquipPassiveLearnProgress(_0x41dea6, 'stype', _0x111bf8, 0x1);
                }
            }
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Action_apply = Game_Action.prototype.apply;
Game_Action.prototype.apply = function (_0x43a50f) {
    VisuMZ.EquipPassiveSys.Game_Action_apply.call(this, _0x43a50f);
    const _0x7711ec = _0x43a50f.result();
    if (this.subject().isActor()) {
        this.subject().updateEquipPassiveActionResult(_0x7711ec, true);
    }
    if (_0x43a50f.isActor()) {
        _0x43a50f.updateEquipPassiveActionResult(_0x7711ec, false);
    }
};
Game_Actor.prototype.updateEquipPassiveActionResult = function (_0x231f48, _0xcf57a1) {
    const _0x10b799 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x3caec2 = this.availableUnlearnedEquippablePassives();
    const _0x478cb2 = _0xcf57a1 ? 'LearnDealCritHitTimes' : 'LearnTakeCritHitTimes';
    const _0x3cc9cb = _0xcf57a1 ? 'critDeal' : 'critTake';
    const _0xa751ad = _0xcf57a1 ? 'LearnMissTimes' : 'LearnEvadeTimes';
    const _0x150df8 = _0xcf57a1 ? 'miss' : 'evade';
    for (const _0x374d85 of _0x3caec2) {
        if (!_0x374d85) {
            continue;
        }
        const _0x34704a = _0x374d85.note || '';
        if (_0x34704a.match(_0x10b799[_0x478cb2])) {
            if (_0x231f48.critical) {
                this.updateEquipPassiveLearnProgress(_0x374d85, 'actionTimes', _0x3cc9cb, 0x1);
            }
        }
        if (_0x34704a.match(_0x10b799[_0xa751ad])) {
            if (_0x231f48.missed || _0x231f48.evaded) {
                this.updateEquipPassiveLearnProgress(_0x374d85, 'actionTimes', _0x150df8, 0x1);
            }
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Action_executeDamage = Game_Action.prototype.executeDamage;
Game_Action.prototype.executeDamage = function (_0x1ff8fd, _0x2f91a0) {
    VisuMZ.EquipPassiveSys.Game_Action_executeDamage.call(this, _0x1ff8fd, _0x2f91a0);
    if (_0x2f91a0 <= 0x0) {
        return;
    }
    if (this.subject().isActor()) {
        this.subject().updateEquipPassiveActionElement(this, true);
    }
    if (_0x1ff8fd.isActor()) {
        _0x1ff8fd.updateEquipPassiveActionElement(this, false);
    }
};
Game_Actor.prototype.updateEquipPassiveActionElement = function (_0x3c35b2, _0x3d56a7) {
    const _0x2bea03 = VisuMZ.EquipPassiveSys.GetElements(_0x3c35b2);
    if (_0x2bea03.length <= 0x0) {
        return;
    }
    const _0x5a9e97 = _0x3d56a7 ? 'elementDeal' : 'elementTake';
    const _0x5e6394 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x225f51 = _0x3d56a7 ? 'LearnElementDeal' : 'LearnElementTake';
    const _0x318bbd = this.availableUnlearnedEquippablePassives();
    for (const _0x4d2933 of _0x318bbd) {
        if (!_0x4d2933) {
            continue;
        }
        const _0x124bec = _0x4d2933.note || '';
        const _0x1fb4f9 = _0x124bec.match(_0x5e6394[_0x225f51]);
        if (_0x1fb4f9) {
            for (const _0x3e1559 of _0x1fb4f9) {
                _0x3e1559.match(_0x5e6394[_0x225f51]);
                let _0x545a8b = String(RegExp.$1);
                const _0xc37c8e = /^\d+$/.test(_0x545a8b);
                _0x545a8b = _0xc37c8e
                    ? Number(_0x545a8b)
                    : DataManager.getElementIdWithName(_0x545a8b);
                if (_0x2bea03.includes(_0x545a8b)) {
                    this.updateEquipPassiveLearnProgress(_0x4d2933, _0x5a9e97, _0x545a8b, 0x1);
                }
            }
        }
    }
};
VisuMZ.EquipPassiveSys.GetElements = function (_0x3344f1) {
    let _0xd5b414 = [];
    if (Imported.VisuMZ_1_ElementStatusCore) {
        _0xd5b414 = _0x3344f1.elements();
    } else {
        const _0x4902a9 = _0x3344f1.item().damage.elementId;
        if (_0x4902a9 < 0x0) {
            _0xd5b414 = _0x3344f1.subject().attackElements();
        } else {
            _0xd5b414 = [_0x4902a9];
        }
    }
    return _0xd5b414;
};
VisuMZ.EquipPassiveSys.Game_Battler_addState = Game_Battler.prototype.addState;
Game_Battler.prototype.addState = function (_0xf84050) {
    VisuMZ.EquipPassiveSys.Game_Battler_addState.call(this, _0xf84050);
    if (this.isStateAffected(_0xf84050)) {
        const _0x4e07ab = BattleManager._subject;
        if ($gameParty.inBattle() && _0x4e07ab && _0x4e07ab.isActor()) {
            _0x4e07ab.updateEquipPassiveActionState(_0xf84050, true);
        }
        if (this.isActor()) {
            this.updateEquipPassiveActionState(_0xf84050, false);
            if (_0xf84050 === this.deathStateId()) {
                this.updateEquipPassiveKda('deaths');
            }
        }
        if (
            this.isEnemy() &&
            _0xf84050 === this.deathStateId() &&
            BattleManager._subject &&
            BattleManager._subject.isActor()
        ) {
            BattleManager._subject.updateEquipPassiveTraitSlayer(this);
            BattleManager._subject.updateEquipPassiveKda('kills');
            for (const _0x1a5d68 of $gameParty.battleMembers()) {
                if (!_0x1a5d68) {
                    continue;
                }
                if (_0x1a5d68 === BattleManager._subject) {
                    continue;
                }
                if (!_0x1a5d68.isAlive()) {
                    continue;
                }
                _0x1a5d68.updateEquipPassiveKda('assists');
            }
        }
    }
};
Game_Actor.prototype.updateEquipPassiveActionState = function (_0x103054, _0x1f6cd8) {
    if (!$dataStates[_0x103054]) {
        return;
    }
    const _0x3db3e5 = _0x1f6cd8 ? 'stateDeal' : 'stateTake';
    const _0x4ca860 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x26a4b5 = _0x1f6cd8 ? 'LearnStateDeal' : 'LearnStateTake';
    const _0x36a8d1 = this.availableUnlearnedEquippablePassives();
    for (const _0x3d7301 of _0x36a8d1) {
        if (!_0x3d7301) {
            continue;
        }
        const _0x592312 = _0x3d7301.note || '';
        const _0xf69063 = _0x592312.match(_0x4ca860[_0x26a4b5]);
        if (_0xf69063) {
            for (const _0x5b559a of _0xf69063) {
                _0x5b559a.match(_0x4ca860[_0x26a4b5]);
                let _0xc09b16 = String(RegExp.$1);
                const _0x33369d = /^\d+$/.test(_0xc09b16);
                _0xc09b16 = _0x33369d
                    ? Number(_0xc09b16)
                    : DataManager.getStateIdWithName(_0xc09b16);
                if (_0x103054 === _0xc09b16) {
                    this.updateEquipPassiveLearnProgress(_0x3d7301, _0x3db3e5, _0x103054, 0x1);
                }
            }
        }
    }
};
Game_Actor.prototype.updateEquipPassiveTraitSlayer = function (_0x3afc9e) {
    if (!_0x3afc9e) {
        return;
    }
    if (!Imported.VisuMZ_1_ElementStatusCore) {
        return;
    }
    const _0x2dacef = VisuMZ.EquipPassiveSys.RegExp;
    const _0x34f6d4 = this.availableUnlearnedEquippablePassives();
    for (const _0x4fb7a1 of _0x34f6d4) {
        if (!_0x4fb7a1) {
            continue;
        }
        const _0x33c9b3 = _0x4fb7a1.note || '';
        const _0x23c128 = _0x33c9b3.match(_0x2dacef.LearnDefeatTrait);
        if (_0x23c128) {
            for (const _0xb6da86 of _0x23c128) {
                _0xb6da86.match(_0x2dacef.LearnDefeatTrait);
                const _0x5a042d = String(RegExp.$1).toUpperCase().trim();
                if (_0x3afc9e.hasTraitSet(_0x5a042d)) {
                    this.updateEquipPassiveLearnProgress(_0x4fb7a1, 'traitSlayer', _0x5a042d, 0x1);
                }
            }
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Battler_gainHp = Game_Battler.prototype.gainHp;
Game_Battler.prototype.gainHp = function (_0x1593fa) {
    const _0x48cc59 = this.hp;
    VisuMZ.EquipPassiveSys.Game_Battler_gainHp.call(this, _0x1593fa);
    const _0x48dba7 = this.hp - _0x48cc59;
    if (_0x48dba7 === 0x0) {
        return;
    }
    if (!$gameParty.inBattle()) {
        return;
    }
    if (this.isActor()) {
        this.updateEquipPassiveHpChanges(_0x1593fa, false);
    }
    if (BattleManager._subject && BattleManager._subject.isActor()) {
        BattleManager._subject.updateEquipPassiveHpChanges(_0x1593fa, true);
    }
};
Game_Actor.prototype.updateEquipPassiveHpChanges = function (_0x57f430, _0x3c0ce4) {
    if (!Imported.VisuMZ_1_ElementStatusCore) {
        return;
    }
    let _0x204305 = 'LearnTotal';
    _0x204305 += _0x57f430 > 0x0 ? 'Heal' : 'Dmg';
    _0x204305 += _0x3c0ce4 ? 'Deal' : 'Take';
    let _0x2b7f03 = _0x57f430 > 0x0 ? 'heal' : 'dmg';
    _0x2b7f03 += _0x3c0ce4 ? 'Deal' : 'Take';
    const _0x310202 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x46039a = this.availableUnlearnedEquippablePassives();
    for (const _0x17d733 of _0x46039a) {
        if (!_0x17d733) {
            continue;
        }
        const _0x4fec1c = _0x17d733.note || '';
        if (_0x4fec1c.match(_0x310202[_0x204305])) {
            this.updateEquipPassiveLearnProgress(_0x17d733, 'hp', _0x2b7f03, Math.abs(_0x57f430));
        }
    }
};
Game_Actor.prototype.updateEquipPassiveKda = function (_0x390a01) {
    const _0x184a8b = VisuMZ.EquipPassiveSys.RegExp;
    const _0x180b55 = this.availableUnlearnedEquippablePassives();
    const _0x424773 = {
        kills: 'LearnCountKills',
        deaths: 'LearnCountDeaths',
        assists: 'LearnCountAssists',
    };
    const _0x5576c3 = _0x424773[_0x390a01];
    for (const _0x5d0c33 of _0x180b55) {
        if (!_0x5d0c33) {
            continue;
        }
        const _0x5021df = _0x5d0c33.note || '';
        if (_0x5021df.match(_0x184a8b[_0x5576c3])) {
            this.updateEquipPassiveLearnProgress(_0x5d0c33, 'kda', _0x390a01, 0x1);
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Actor_learnSkill = Game_Actor.prototype.learnSkill;
Game_Actor.prototype.learnSkill = function (_0x225c78) {
    VisuMZ.EquipPassiveSys.Game_Actor_learnSkill.call(this, _0x225c78);
    VisuMZ.EquipPassiveSys.LinkLearn(this, $dataSkills[_0x225c78]);
};
VisuMZ.EquipPassiveSys.LinkLearn = function (_0x189223, _0x1d9511) {
    if (!_0x189223) {
        return;
    }
    if (!_0x1d9511) {
        return;
    }
    const _0x1a505b = VisuMZ.EquipPassiveSys.RegExp;
    const _0x583bde = _0x1d9511.note || '';
    if (_0x583bde.match(_0x1a505b.SkillLinkLearned)) {
        const _0xd5e8c3 = String(RegExp.$1)
            .split(',')
            .map(_0x2dc5df => _0x2dc5df.trim());
        for (const _0x41ec9f of _0xd5e8c3) {
            const _0xf55ea6 = /^\d+$/.test(_0x41ec9f);
            let _0x215126 = 0x0;
            if (_0xf55ea6) {
                _0x215126 = Number(_0x41ec9f);
            } else {
                _0x215126 = DataManager.getStateIdWithName(_0x41ec9f);
            }
            if (_0x215126 > 0x0) {
                const _0x5e6161 = $dataStates[_0x215126];
                _0x189223.learnEquippedPassive(_0x5e6161);
            }
        }
    }
    if (_0x583bde.match(_0x1a505b.SkillLinkLearnable)) {
        const _0x990b9d = String(RegExp.$1)
            .split(',')
            .map(_0x22a22f => _0x22a22f.trim());
        for (const _0xdb6bc4 of _0x990b9d) {
            const _0x2501c1 = /^\d+$/.test(_0xdb6bc4);
            let _0xd1923b = 0x0;
            if (_0x2501c1) {
                _0xd1923b = Number(_0xdb6bc4);
            } else {
                _0xd1923b = DataManager.getStateIdWithName(_0xdb6bc4);
            }
            if (_0xd1923b > 0x0) {
                const _0x46245f = $dataStates[_0xd1923b];
                _0x189223.addUnlearnedEquippablePassive(_0x46245f);
            }
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Actor_refresh = Game_Actor.prototype.refresh;
Game_Actor.prototype.refresh = function () {
    VisuMZ.EquipPassiveSys.Game_Actor_refresh.call(this);
    if (this._bypassUnequipHiddenPassive) {
        return;
    }
    this.unequipHiddenPassives();
};
Game_Actor.prototype.unequipHiddenPassives = function () {
    this._bypassUnequipHiddenPassive = true;
    const _0x46a196 = this.equippedPassives().filter(_0x401101 =>
        VisuMZ.EquipPassiveSys.IsPassiveHidden(this, _0x401101)
    );
    for (const _0x3488be of _0x46a196) {
        this.removeEquipPassive(_0x3488be);
    }
    this._bypassUnequipHiddenPassive = false;
    if (_0x46a196.length > 0x0) {
        VisuMZ.EquipPassiveSys.Game_Actor_refresh.call(this);
        const _0x4db4c2 = SceneManager._scene;
        if (_0x4db4c2 && _0x4db4c2._statusWindow) {
            _0x4db4c2._statusWindow.refresh();
        }
    }
};
VisuMZ.EquipPassiveSys.Game_Party_initialize = Game_Party.prototype.initialize;
Game_Party.prototype.initialize = function () {
    VisuMZ.EquipPassiveSys.Game_Party_initialize.call(this);
    this.initEquipPassiveSystem();
};
Game_Party.prototype.initEquipPassiveSystem = function () {
    this._learnedEquippablePassives = [];
    this._learnableEquippablePassives = [];
};
Game_Party.prototype.getLearnEquippedPassiveRawIDs = function () {
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._learnedEquippablePassives;
};
Game_Party.prototype.learnEquippedPassive = function (_0xde2866, _0x4450fb) {
    if (!DataManager.isState(_0xde2866)) {
        return;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._learnedEquippablePassives.includes(_0xde2866.id)) {
        return;
    }
    this._learnedEquippablePassives.push(_0xde2866.id);
    this._learnedEquippablePassives.sort((_0x20e56a, _0x9e5d1b) => _0x20e56a - _0x9e5d1b);
    this.branchLearnableEquipPassives(_0xde2866);
    if (Game_Actor.EQUIP_PASSIVE_SYS.learnAutoEquip && $gameSystem.isEquipPassiveCommandVisible()) {
        for (const _0x4c528c of this.allMembers()) {
            if (!_0x4c528c) {
                continue;
            }
            _0x4c528c.refresh();
            if (_0x4c528c.hasEquipPassiveCapacityFor(_0xde2866)) {
                _0x4c528c.processEquipPassive(_0xde2866);
            }
        }
    }
    if (!_0x4450fb && TextManager.EQUIP_PASSIVE_SYS.learnShowTextPopup) {
        const _0x57abb3 = TextManager.EQUIP_PASSIVE_SYS.textPopupFmt;
        const _0x3d8531 = DataManager.getEquipPassiveName(_0xde2866);
        const _0x4150d1 = '\\I[%1]'.format(DataManager.getEquipPassiveIcon(_0xde2866));
        const _0x2adc96 = _0x57abb3.format(this.name(), _0x3d8531, _0x4150d1);
        $textPopup(_0x2adc96);
    }
};
Game_Party.prototype.branchLearnableEquipPassives = function (_0xb0c851) {
    const _0x3b0d03 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x44d681 = _0xb0c851.note || '';
    if (_0x44d681.match(_0x3b0d03.BranchLearnable)) {
        const _0x3a1ed6 = String(RegExp.$1)
            .split(',')
            .map(_0x4c2b31 => _0x4c2b31.trim());
        for (const _0x289d94 of _0x3a1ed6) {
            const _0x361d7b = /^\d+$/.test(_0x289d94);
            let _0x14a56b = 0x0;
            if (_0x361d7b) {
                _0x14a56b = Number(_0x289d94);
            } else {
                _0x14a56b = DataManager.getStateIdWithName(_0x289d94);
            }
            if (_0x14a56b > 0x0) {
                this.addUnlearnedEquippablePassive($dataStates[_0x14a56b]);
            }
        }
    }
};
Game_Party.prototype.isLearnedEquippedPassive = function (_0x57131f) {
    if (!DataManager.isState(_0x57131f)) {
        return;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._learnedEquippablePassives.includes(_0x57131f.id);
};
Game_Party.prototype.forgetEquippedPassive = function (_0x479a2b) {
    if (!DataManager.isState(_0x479a2b)) {
        return;
    }
    if (this._learnedEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    this._learnedEquippablePassives.remove(_0x479a2b.id);
    this._learnedEquippablePassives.sort((_0x100085, _0x23f5b0) => _0x100085 - _0x23f5b0);
    for (const _0x1b96d8 of this.allMembers()) {
        if (!_0x1b96d8) {
            continue;
        }
        _0x1b96d8.forgetEquippedPassive(_0x479a2b);
    }
};
Game_Party.prototype.getUnlearnedEquippedPassiveRawIDs = function () {
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    return this._learnableEquippablePassives;
};
Game_Party.prototype.addUnlearnedEquippablePassive = function (_0x78566a) {
    if (!DataManager.isState(_0x78566a)) {
        return;
    }
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    if (this._learnableEquippablePassives.includes(_0x78566a.id)) {
        return;
    }
    this._learnableEquippablePassives.push(_0x78566a.id);
    this._learnableEquippablePassives.sort((_0x4bee28, _0x418244) => _0x4bee28 - _0x418244);
};
Game_Party.prototype.removeUnlearnedEquippablePassive = function (_0x302346) {
    if (!DataManager.isState(_0x302346)) {
        return;
    }
    if (this._learnableEquippablePassives === undefined) {
        this.initEquipPassiveSystem();
    }
    this._learnableEquippablePassives.remove(_0x302346.id);
    this._learnableEquippablePassives.sort((_0x4d77d2, _0x363630) => _0x4d77d2 - _0x363630);
    for (const _0xcebf40 of this.allMembers()) {
        if (!_0xcebf40) {
            continue;
        }
        _0xcebf40.removeUnlearnedEquippablePassive(_0x302346);
    }
};
VisuMZ.EquipPassiveSys.Scene_Skill_create = Scene_Skill.prototype.create;
Scene_Skill.prototype.create = function () {
    VisuMZ.EquipPassiveSys.Scene_Skill_create.call(this);
    this.createEquipPassiveWindow();
    this.createPassiveStatusWindow();
};
VisuMZ.EquipPassiveSys.Scene_Skill_createSkillTypeWindow =
    Scene_Skill.prototype.createSkillTypeWindow;
Scene_Skill.prototype.createSkillTypeWindow = function () {
    VisuMZ.EquipPassiveSys.Scene_Skill_createSkillTypeWindow.call(this);
    this._skillTypeWindow.setHandler('equipPassives', this.commandEquipPassives.bind(this));
};
Scene_Skill.prototype.createEquipPassiveWindow = function () {
    const _0xaed1b5 = this.equipPassiveWindowRect();
    this._equipPassiveWindow = new Window_EquipPassiveList(_0xaed1b5);
    this._equipPassiveWindow.setHelpWindow(this._helpWindow);
    this._equipPassiveWindow.setHandler('ok', this.onEquipPassiveOk.bind(this));
    this._equipPassiveWindow.setHandler('cancel', this.onEquipPassiveCancel.bind(this));
    this.addWindow(this._equipPassiveWindow);
    const _0x62e315 = Window_EquipPassiveList.SETTINGS.bgType;
    this._equipPassiveWindow.setBackgroundType(_0x62e315 || 0x0);
};
Scene_Skill.prototype.equipPassiveWindowRect = function () {
    const _0x2d1f0e = this._statusWindow.y + this._statusWindow.height;
    const _0x32fdd2 = Graphics.boxWidth;
    let _0x4de5b2 = this.mainAreaHeight() - this._statusWindow.height;
    if (Window_EquipPassiveStatus.SETTINGS.showWindow) {
        _0x4de5b2 -= this.calcWindowHeight(0x1, false);
    }
    return new Rectangle(0x0, _0x2d1f0e, _0x32fdd2, _0x4de5b2);
};
Scene_Skill.prototype.createPassiveStatusWindow = function () {
    if (!Window_EquipPassiveStatus.SETTINGS.showWindow) {
        return;
    }
    const _0x1f8f50 = this.passiveStatusWindowRect();
    this._passiveStatusWindow = new Window_EquipPassiveStatus(_0x1f8f50);
    this._passiveStatusWindow.setActor(this.actor());
    this.addWindow(this._passiveStatusWindow);
    const _0xa370f0 = Window_EquipPassiveStatus.SETTINGS.bgType;
    this._passiveStatusWindow.setBackgroundType(_0xa370f0 || 0x0);
};
Scene_Skill.prototype.passiveStatusWindowRect = function () {
    const _0x45aec2 = this.equipPassiveWindowRect();
    const _0x3900d0 = _0x45aec2.x;
    const _0x5af03a = _0x45aec2.y + _0x45aec2.height;
    const _0xb94743 = _0x45aec2.width;
    const _0x3405a6 = this.calcWindowHeight(0x1, false);
    return new Rectangle(_0x3900d0, _0x5af03a, _0xb94743, _0x3405a6);
};
VisuMZ.EquipPassiveSys.Scene_Skill_refreshActor = Scene_Skill.prototype.refreshActor;
Scene_Skill.prototype.refreshActor = function () {
    VisuMZ.EquipPassiveSys.Scene_Skill_refreshActor.call(this);
    this.actor().checkLearnNewEquipPassives();
    if (this._equipPassiveWindow) {
        this._equipPassiveWindow.setActor(this.actor());
        this._equipPassiveWindow.refresh();
    }
    if (this._passiveStatusWindow) {
        this._passiveStatusWindow.setActor(this.actor());
        this._passiveStatusWindow.refresh();
    }
};
Scene_Skill.prototype.commandEquipPassives = function () {
    this._equipPassiveWindow.activate();
    this._equipPassiveWindow.selectLast();
};
Scene_Skill.prototype.onEquipPassiveOk = function () {
    const _0xe5a774 = this._equipPassiveWindow.item();
    if (this._actor.isEquippablePassiveEquipped(_0xe5a774)) {
        this._actor.removeEquipPassive(_0xe5a774);
    } else {
        this._actor.processEquipPassive(_0xe5a774);
    }
    this._statusWindow.refresh();
    this._itemWindow.refresh();
    this._equipPassiveWindow.refresh();
    this._equipPassiveWindow.activate();
    if (this._passiveStatusWindow) {
        this._passiveStatusWindow.refresh();
    }
};
Scene_Skill.prototype.onEquipPassiveCancel = function () {
    this._equipPassiveWindow.deselect();
    this._skillTypeWindow.activate();
};
Window_Base.prototype.drawEquippablePassiveState = function (
    _0x2ff54c,
    _0x117c0e,
    _0x4a57f0,
    _0x4a90ef,
    _0x128a08
) {
    if (!_0x2ff54c) {
        return;
    }
    const _0x6747c8 = _0x4a57f0 + (this.lineHeight() - ImageManager.iconHeight) / 0x2;
    const _0x23b65d = ImageManager.iconWidth + 0x4;
    const _0x27af85 = Math.max(0x0, _0x4a90ef - _0x23b65d);
    this.resetTextColor();
    if (_0x128a08 && _0x128a08.equippedPassives().includes(_0x2ff54c)) {
        this.changeTextColor(ColorManager.equipPassiveColor());
    }
    this.drawIcon(DataManager.getEquipPassiveIcon(_0x2ff54c), _0x117c0e, _0x6747c8);
    this.drawText(
        DataManager.getEquipPassiveName(_0x2ff54c),
        _0x117c0e + _0x23b65d,
        _0x4a57f0,
        _0x27af85
    );
};
Window_Base.prototype.drawEquippablePassiveCost = function (
    _0x183ac9,
    _0x10fb68,
    _0x18a240,
    _0x46e17a
) {
    if (!_0x183ac9) {
        return;
    }
    const _0x1ba89b = Window_EquipPassiveList.SETTINGS;
    if (!_0x1ba89b.showCostAny) {
        return;
    }
    const _0x3400a6 = DataManager.getEquipPassiveCost(_0x183ac9);
    if (_0x3400a6 === 0x1 && !_0x1ba89b.showCost1) {
        return;
    }
    if (_0x3400a6 < 0x1 && !_0x1ba89b.showCost0) {
        return;
    }
    this.resetFontSettings();
    const _0x45999f = '\\I[%1]'.format(ImageManager.EQUIP_PASSIVE_SYS.capacity);
    let _0x821ac = '';
    if (_0x1ba89b.costNumber || _0x3400a6 > _0x1ba89b.costIconLimit) {
        _0x821ac = TextManager.EQUIP_PASSIVE_SYS.costFmt.format(_0x3400a6, _0x45999f);
    } else {
        let _0x3e4c76 = _0x3400a6;
        while (_0x3e4c76--) {
            _0x821ac += _0x45999f;
        }
    }
    const _0x1f650c = this.textSizeEx(_0x821ac).width;
    const _0x3aa02d = _0x10fb68 + _0x46e17a - _0x1f650c;
    this.drawTextEx(_0x821ac, _0x3aa02d, _0x18a240);
};
Window_Base.prototype.drawEquippablePassiveMask = function (
    _0x5239ed,
    _0x459341,
    _0x284183,
    _0x4bf390
) {
    if (!_0x5239ed) {
        return;
    }
    const _0x294df7 = _0x284183 + (this.lineHeight() - ImageManager.iconHeight) / 0x2;
    const _0x98c5ac = ImageManager.iconWidth + 0x4;
    const _0x388e06 = Math.max(0x0, _0x4bf390 - _0x98c5ac);
    this.resetTextColor();
    const _0x1122aa = Window_EquipPassiveList.SETTINGS.maskIcon;
    this.drawIcon(_0x1122aa, _0x459341, _0x294df7);
    const _0x1256aa = VisuMZ.EquipPassiveSys.MaskName(_0x5239ed);
    this.contents.fontItalic = Window_EquipPassiveList.SETTINGS.maskItalics;
    this.drawText(_0x1256aa, _0x459341 + _0x98c5ac, _0x284183, _0x388e06);
    this.contents.fontItalic = false;
};
VisuMZ.EquipPassiveSys.MaskName = function (_0x46ece7) {
    const _0x41c768 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x1251ec = _0x46ece7.note || '';
    if (_0x1251ec.match(_0x41c768.MaskName)) {
        return String(RegExp.$1).trim();
    }
    const _0x48fd72 = Window_EquipPassiveList.SETTINGS.maskLetter;
    return Array(_0x46ece7.name.length + 0x1).join(_0x48fd72);
};
Window_Base.prototype.meetSkillLearnPassiveStateConditions = function (_0x475ea1) {
    if (!SceneManager._scene) {
        return false;
    }
    if (SceneManager._scene.constructor !== Scene_Skill) {
        return false;
    }
    if (!_0x475ea1) {
        return false;
    }
    if (!DataManager.isState(_0x475ea1)) {
        return false;
    }
    return SceneManager._scene._itemWindow.isSkillLearnMode();
};
VisuMZ.EquipPassiveSys.Window_Base_drawItemName = Window_Base.prototype.drawItemName;
Window_Base.prototype.drawItemName = function (_0x571231, _0x1e1621, _0x1d24ed, _0x598f25) {
    const _0x5c47e4 = _0x571231 ? _0x571231.name : '';
    const _0x5d21f6 = _0x571231 ? _0x571231.iconIndex : 0x0;
    if (this.meetSkillLearnPassiveStateConditions(_0x571231)) {
        _0x571231.name = DataManager.getEquipPassiveName(_0x571231);
        _0x571231.iconIndex = DataManager.getEquipPassiveIcon(_0x571231);
    }
    VisuMZ.EquipPassiveSys.Window_Base_drawItemName.call(
        this,
        _0x571231,
        _0x1e1621,
        _0x1d24ed,
        _0x598f25
    );
    if (this.meetSkillLearnPassiveStateConditions(_0x571231)) {
        _0x571231.name = _0x5c47e4;
        _0x571231.iconIndex = _0x5d21f6;
    }
};
VisuMZ.EquipPassiveSys.Window_Selectable_setHelpWindowItem =
    Window_Selectable.prototype.setHelpWindowItem;
Window_Selectable.prototype.setHelpWindowItem = function (_0x1b88fb) {
    if (_0x1b88fb) {
        _0x1b88fb.description = _0x1b88fb.description || '';
        _0x1b88fb.description = String(_0x1b88fb.description);
    }
    VisuMZ.EquipPassiveSys.Window_Selectable_setHelpWindowItem.call(this, _0x1b88fb);
};
Window_SkillType.EQUIP_PASSIVE_SYS = {
    defaultShowEquipPassive: VisuMZ.EquipPassiveSys.Settings.General.DefaultShowCommand ?? true,
};
VisuMZ.EquipPassiveSys.Window_SkillType_makeCommandList =
    Window_SkillType.prototype.makeCommandList;
Window_SkillType.prototype.makeCommandList = function () {
    VisuMZ.EquipPassiveSys.Window_SkillType_makeCommandList.call(this);
    this.addEquipPassiveCommand();
};
Window_SkillType.prototype.addEquipPassiveCommand = function () {
    if (!this.isEquipPassiveCommandVisible()) {
        return;
    }
    let _0x1ef4d2 = TextManager.EQUIP_PASSIVE_SYS.command;
    if (this.commandStyle() !== 'text') {
        const _0x306f76 = ImageManager.EQUIP_PASSIVE_SYS.icon;
        _0x1ef4d2 = '\\I[%1]%2'.format(_0x306f76, _0x1ef4d2);
    }
    this.addCommand(_0x1ef4d2, 'equipPassives', true, 'equipPassives');
};
Window_SkillType.prototype.isEquipPassiveCommandVisible = function () {
    return $gameSystem.isEquipPassiveCommandVisible();
};
VisuMZ.EquipPassiveSys.Window_SkillList_setStypeId = Window_SkillList.prototype.setStypeId;
Window_SkillList.prototype.setStypeId = function (_0x272e82) {
    const _0x483260 = this._stypeId !== _0x272e82;
    if (!_0x483260) {
        return;
    }
    this.show();
    const _0x1d370a = SceneManager._scene._equipPassiveWindow;
    if (_0x1d370a) {
        _0x1d370a.hide();
    }
    const _0x28f99f = SceneManager._scene._passiveStatusWindow;
    if (_0x28f99f) {
        _0x28f99f.hide();
    }
    const _0xb2b825 = this._statusWindow;
    if (_0xb2b825) {
        _0xb2b825.show();
    }
    VisuMZ.EquipPassiveSys.Window_SkillList_setStypeId.call(this, _0x272e82);
    if (_0x483260 && _0x1d370a && _0x272e82 === 'equipPassives') {
        if (_0xb2b825) {
            _0xb2b825.hide();
        }
        this.hide();
        if (this._actor) {
            this._actor.checkLearnNewEquipPassives();
        }
        _0x1d370a.refresh();
        _0x1d370a.show();
        if (_0x28f99f) {
            _0x28f99f.refresh();
            _0x28f99f.show();
        }
    }
};
Window_SkillList.prototype.makeSkillLearnPassivesList = function () {
    const _0xa2bef1 = DataManager.getSkillLearnPassiveSkillsFromClass(
        this._actor.currentClass().id
    );
    const _0x1e98d4 = _0xa2bef1
        .map(_0x5ea5f7 => $dataStates[_0x5ea5f7])
        .filter(_0x460904 => this.includes(_0x460904));
    this._data = this._data.concat(_0x1e98d4);
};
DataManager.getSkillLearnPassiveSkillsFromClass = function (_0x1a0f1b) {
    if (!$dataClasses[_0x1a0f1b]) {
        return [];
    }
    const _0x2315fa = [];
    const _0x19dbc0 = $dataClasses[_0x1a0f1b].note;
    const _0x29faac = VisuMZ.SkillLearnSystem.RegExp;
    const _0x55d690 = _0x19dbc0.match(_0x29faac.LearnSkillPassiveA);
    if (_0x55d690) {
        for (const _0x18b730 of _0x55d690) {
            if (!_0x18b730) {
                continue;
            }
            _0x18b730.match(_0x29faac.LearnSkillPassiveA);
            const _0x1b68fb = String(RegExp.$1)
                .split(',')
                .map(_0x5ea06f => _0x5ea06f.trim());
            for (let _0x258b8d of _0x1b68fb) {
                _0x258b8d = (String(_0x258b8d) || '').trim();
                const _0x7eb39b = /^\d+$/.test(_0x258b8d);
                if (_0x7eb39b) {
                    _0x2315fa.push(Number(_0x258b8d));
                } else {
                    _0x2315fa.push(DataManager.getStateIdWithName(_0x258b8d));
                }
            }
        }
    }
    const _0x41c44c = _0x19dbc0.match(_0x29faac.LearnSkillPassiveB);
    if (_0x41c44c) {
        for (const _0x21825f of _0x41c44c) {
            if (!_0x21825f) {
                continue;
            }
            _0x21825f.match(_0x29faac.LearnSkillPassiveB);
            const _0x390b9a = String(RegExp.$1).split(/[\r\n]+/);
            for (let _0x3e8bf3 of _0x390b9a) {
                _0x3e8bf3 = (String(_0x3e8bf3) || '').trim();
                const _0xc3df6c = /^\d+$/.test(_0x3e8bf3);
                if (_0xc3df6c) {
                    _0x2315fa.push(Number(_0x3e8bf3));
                } else {
                    _0x2315fa.push(DataManager.getStateIdWithName(_0x3e8bf3));
                }
            }
        }
    }
    return _0x2315fa
        .sort((_0x2be8ca, _0x5a1d56) => _0x2be8ca - _0x5a1d56)
        .filter((_0x45eb48, _0x181c9d, _0x273eba) => _0x273eba.indexOf(_0x45eb48) === _0x181c9d);
};
VisuMZ.EquipPassiveSys.Window_ShopStatus_drawItemData = Window_ShopStatus.prototype.drawItemData;
Window_ShopStatus.prototype.drawItemData = function () {
    if (this.meetSkillLearnPassiveStateConditions(this._item)) {
        this.drawPassiveStateData();
    } else {
        VisuMZ.EquipPassiveSys.Window_ShopStatus_drawItemData.call(this);
    }
};
Window_ShopStatus.prototype.drawPassiveStateData = function () {
    this.resetFontSettings();
    let _0x3a473e = this.innerWidth;
    let _0x215957 = this.innerHeight;
    let _0x54b667 = 0x0;
    this.contentsBack.clearRect(0x0, _0x54b667, _0x3a473e, _0x215957 - _0x54b667);
    this.drawItemName(
        this._item,
        0x0 + this.itemPadding(),
        _0x54b667,
        _0x3a473e - this.itemPadding() * 0x2
    );
    this.drawItemDarkRect(0x0, _0x54b667, _0x3a473e);
    _0x54b667 += this.lineHeight();
    const _0x5b0e82 = TextManager.EQUIP_PASSIVE_SYS.skillLearnShopStatus || '';
    this.drawItemKeyData(_0x5b0e82, 0x0, _0x54b667, _0x3a473e, true);
    this.drawItemDarkRect(0x0, _0x54b667, _0x3a473e);
    _0x54b667 += this.lineHeight();
    this.drawItemDarkRect(0x0, _0x54b667, _0x3a473e, _0x215957 - _0x54b667);
};
function Window_EquipPassiveList() {
    this.initialize(...arguments);
}
Window_EquipPassiveList.prototype = Object.create(Window_Selectable.prototype);
Window_EquipPassiveList.prototype.constructor = Window_EquipPassiveList;
Window_EquipPassiveList.SETTINGS = {
    bgType: VisuMZ.EquipPassiveSys.Settings.Window.EquipPassiveList_BgType ?? 0x0,
    equipColor: VisuMZ.EquipPassiveSys.Settings.Window.EquippedColor ?? 0x11,
    showCostAny: VisuMZ.EquipPassiveSys.Settings.Window.ShowCosts ?? true,
    showCost1: VisuMZ.EquipPassiveSys.Settings.Window.ShowCost1 ?? true,
    showCost0: VisuMZ.EquipPassiveSys.Settings.Window.ShowCost0 ?? false,
    costNumber: VisuMZ.EquipPassiveSys.Settings.Window.ShowCostNumber ?? true,
    costIconLimit: VisuMZ.EquipPassiveSys.Settings.Window.costIconLimit ?? 0x3,
    sortBy: VisuMZ.EquipPassiveSys.Settings.Window.SortStyle ?? 'id',
    showUnlearned: VisuMZ.EquipPassiveSys.Settings.Window.ShowUnlearned ?? true,
    separateUnlearned: VisuMZ.EquipPassiveSys.Settings.Window.SeparateUnlearned ?? true,
    maskUnlearned: VisuMZ.EquipPassiveSys.Settings.Window.MaskUnlearned ?? false,
    maskItalics: VisuMZ.EquipPassiveSys.Settings.Window.MaskItalics ?? true,
    maskIcon: VisuMZ.EquipPassiveSys.Settings.Window.MaskIcon ?? 0x133,
    maskLetter: VisuMZ.EquipPassiveSys.Settings.Window.MaskLetter ?? '?',
};
Window_EquipPassiveList.prototype.initialize = function (_0x1c9598) {
    Window_Selectable.prototype.initialize.call(this, _0x1c9598);
    this._actor = null;
    this._data = [];
    this.hide();
};
Window_EquipPassiveList.prototype.setActor = function (_0x2e8c56) {
    if (this._actor !== _0x2e8c56) {
        this._actor = _0x2e8c56;
        this.refresh();
        this.scrollTo(0x0, 0x0);
    }
};
Window_EquipPassiveList.prototype.maxCols = function () {
    return 0x2;
};
Window_EquipPassiveList.prototype.colSpacing = function () {
    return 0x10;
};
Window_EquipPassiveList.prototype.maxItems = function () {
    return this._data ? this._data.length : 0x1;
};
Window_EquipPassiveList.prototype.item = function () {
    return this.itemAt(this.index());
};
Window_EquipPassiveList.prototype.itemAt = function (_0x341b41) {
    return this._data && _0x341b41 >= 0x0 ? this._data[_0x341b41] : null;
};
Window_EquipPassiveList.prototype.makeItemList = function () {
    if (this._actor) {
        this._data = this.availableItems();
    } else {
        this._data = [];
    }
};
Window_EquipPassiveList.prototype.availableItems = function () {
    if (!this._actor) {
        return [];
    }
    let _0x20c648 = this._actor.availableLearnedEquippablePassives();
    if (Window_EquipPassiveList.SETTINGS.separateUnlearned) {
        _0x20c648 = this.sortItems(_0x20c648);
    }
    if (Window_EquipPassiveList.SETTINGS.showUnlearned) {
        let _0x117e7d = this._actor.availableUnlearnedEquippablePassives();
        if (Window_EquipPassiveList.SETTINGS.separateUnlearned) {
            _0x117e7d = this.sortItems(_0x117e7d);
        }
        _0x20c648 = _0x20c648.concat(_0x117e7d);
    }
    _0x20c648 = _0x20c648.filter(
        (_0x4101f9, _0x2453cf, _0x14b037) => _0x14b037.indexOf(_0x4101f9) === _0x2453cf
    );
    if (!Window_EquipPassiveList.SETTINGS.separateUnlearned) {
        _0x20c648 = this.sortItems(_0x20c648);
    }
    _0x20c648 = _0x20c648.filter(_0x1dc29d => this.includes(_0x1dc29d));
    return _0x20c648;
};
Window_EquipPassiveList.prototype.includes = function (_0x485a03) {
    if (VisuMZ.EquipPassiveSys.IsPassiveHidden(this._actor, _0x485a03)) {
        return false;
    }
    return true;
};
VisuMZ.EquipPassiveSys.IsPassiveHidden = function (_0x3af6bc, _0x25e04f) {
    const _0x597022 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x372001 = _0x25e04f.note || '';
    if (_0x372001.match(_0x597022.HideUnlearned)) {
        if (!_0x3af6bc.isLearnedEquippedPassive(_0x25e04f)) {
            return true;
        }
    }
    if (_0x372001.match(_0x597022.HideLearnedAllID)) {
        const _0x49dd1f = String(RegExp.$1)
            .split(',')
            .map(_0x189e23 => Number(_0x189e23));
        if (
            _0x49dd1f.every(_0x52ff3a => _0x3af6bc.isLearnedEquippedPassive($dataStates[_0x52ff3a]))
        ) {
            return true;
        }
    }
    if (_0x372001.match(_0x597022.HideLearnedAnyID)) {
        const _0x7b75de = String(RegExp.$1)
            .split(',')
            .map(_0x3e5ede => Number(_0x3e5ede));
        if (
            _0x7b75de.some(_0x5a56b5 => _0x3af6bc.isLearnedEquippedPassive($dataStates[_0x5a56b5]))
        ) {
            return true;
        }
    }
    if (_0x372001.match(_0x597022.HideLearnedAllName)) {
        const _0x112366 = String(RegExp.$1)
            .split(',')
            .map(_0x5850ee => DataManager.getStateIdWithName(_0x5850ee));
        if (
            _0x112366.every(_0x3e6998 => _0x3af6bc.isLearnedEquippedPassive($dataStates[_0x3e6998]))
        ) {
            return true;
        }
    }
    if (_0x372001.match(_0x597022.HideLearnedAnyName)) {
        const _0xd6d342 = String(RegExp.$1)
            .split(',')
            .map(_0x7651ef => DataManager.getStateIdWithName(_0x7651ef));
        if (
            _0xd6d342.some(_0x44f8c3 => _0x3af6bc.isLearnedEquippedPassive($dataStates[_0x44f8c3]))
        ) {
            return true;
        }
    }
    return false;
};
Window_EquipPassiveList.prototype.sortItems = function (_0x5bdfcc) {
    const _0x1d3255 = Window_EquipPassiveList.SETTINGS.sortBy;
    if (_0x1d3255 === 'id') {
        return _0x5bdfcc.sort((_0x3e8411, _0x3aa14f) => _0x3e8411.id - _0x3aa14f.id);
    } else {
        if (_0x1d3255 === 'name') {
            return _0x5bdfcc.sort((_0x58201a, _0x201779) =>
                DataManager.getEquipPassiveName(_0x58201a) >
                DataManager.getEquipPassiveName(_0x201779)
                    ? 0x1
                    : -0x1
            );
        } else {
            if (_0x1d3255 === 'priority') {
                return _0x5bdfcc.sort(
                    (_0x1d1961, _0x4fa733) => _0x1d1961.priority - _0x4fa733.priority
                );
            } else {
                return _0x5bdfcc;
            }
        }
    }
};
Window_EquipPassiveList.prototype.selectLast = function () {
    this.forceSelect(0x0);
};
Window_EquipPassiveList.prototype.drawItem = function (_0x17dcc0) {
    const _0x25c5e3 = this.itemAt(_0x17dcc0);
    if (!_0x25c5e3) {
        return;
    }
    const _0x21b130 = this.itemLineRect(_0x17dcc0);
    this.changePaintOpacity(this.isEnabled(_0x25c5e3));
    if (this._actor.isLearnedEquippedPassive(_0x25c5e3)) {
        this.drawEquippablePassiveState(
            _0x25c5e3,
            _0x21b130.x,
            _0x21b130.y,
            _0x21b130.width,
            this._actor
        );
        this.drawEquippablePassiveCost(_0x25c5e3, _0x21b130.x, _0x21b130.y, _0x21b130.width);
    } else {
        if (VisuMZ.EquipPassiveSys.IsMasked(_0x25c5e3)) {
            this.drawEquippablePassiveMask(_0x25c5e3, _0x21b130.x, _0x21b130.y, _0x21b130.width);
        } else {
            this.drawEquippablePassiveState(
                _0x25c5e3,
                _0x21b130.x,
                _0x21b130.y,
                _0x21b130.width,
                this._actor
            );
        }
        this.drawUnlearnedText(_0x21b130);
    }
};
VisuMZ.EquipPassiveSys.IsMasked = function (_0x2aa5a1) {
    const _0x141ba2 = VisuMZ.EquipPassiveSys.RegExp;
    const _0x4c5eba = _0x2aa5a1.note || '';
    if (_0x4c5eba.match(_0x141ba2.MaskUnlearned)) {
        return true;
    }
    if (_0x4c5eba.match(_0x141ba2.NoMaskUnlearned)) {
        return false;
    }
    return Window_EquipPassiveList.SETTINGS.maskUnlearned;
};
Window_EquipPassiveList.prototype.drawUnlearnedText = function (_0x17b764) {
    const _0x52fa69 = TextManager.EQUIP_PASSIVE_SYS.unlearned;
    const _0x32af31 = this.textSizeEx(_0x52fa69).width;
    const _0x4e5b64 = _0x17b764.x + _0x17b764.width - _0x32af31;
    this.drawTextEx(_0x52fa69, _0x4e5b64, _0x17b764.y);
    this.resetFontSettings();
};
Window_EquipPassiveList.prototype.updateHelp = function () {
    if (this._actor && this._actor.isLearnedEquippedPassive(this.item())) {
        if (this.item()) {
            this.item().description = this.item().description || '-';
        }
        this.setHelpWindowItem(this.item());
    } else {
        const _0x166eb7 = VisuMZ.EquipPassiveSys.MakeUnlockHelpText(this._actor, this.item());
        if (this._helpWindow) {
            this._helpWindow.setText(_0x166eb7);
        }
    }
};
Window_EquipPassiveList.prototype.refresh = function () {
    this.makeItemList();
    Window_Selectable.prototype.refresh.call(this);
};
Window_EquipPassiveList.prototype.playOkSound = function () {
    SoundManager.playEquip();
};
Window_EquipPassiveList.prototype.isCurrentItemEnabled = function () {
    return this.isEnabled(this.item());
};
Window_EquipPassiveList.prototype.isEnabled = function (_0x5f0ab9) {
    if (!_0x5f0ab9) {
        return false;
    }
    if (!this._actor) {
        return false;
    }
    if (this._actor.isEquippablePassiveEquipped(_0x5f0ab9)) {
        return true;
    }
    return this._actor.isLearnedEquippedPassive(_0x5f0ab9)
        ? this._actor.hasEquipPassiveCapacityFor(_0x5f0ab9)
        : false;
};
VisuMZ.EquipPassiveSys.MakeUnlockHelpText = function (_0x5c0046, _0x22f4e1) {
    if (!_0x22f4e1) {
        return '';
    }
    const _0x13fa06 = TextManager.EQUIP_PASSIVE_SYS;
    const _0x5c8d4b = VisuMZ.EquipPassiveSys.RegExp;
    const _0x8e3ccc = _0x22f4e1.note || '';
    let _0x11c135 = '';
    if (_0x8e3ccc.match(_0x5c8d4b.CustomLearnCondText)) {
        _0x11c135 = String(RegExp.$1).trim();
    } else {
        if (_0x8e3ccc.match(_0x5c8d4b.LearnLevel)) {
            const _0x7ca191 = Number(RegExp.$1);
            const _0x45dffa = _0x5c0046.level;
            _0x11c135 = this.ApplyUnlockHelpTextCondition(_0x11c135, _0x7ca191, _0x45dffa, 'level');
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnBattles)) {
            const _0x5bfdb1 = Number(RegExp.$1);
            const _0x27d27a = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'battle', 'all');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x5bfdb1,
                _0x27d27a,
                'battle'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnVictory)) {
            const _0x14dd5a = Number(RegExp.$1);
            const _0x36f7c9 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'battle',
                'victory'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x14dd5a,
                _0x36f7c9,
                'victory'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnEscapes)) {
            const _0x393d81 = Number(RegExp.$1);
            const _0x3e940c = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'battle', 'escape');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x393d81,
                _0x3e940c,
                'escapes'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnDefeats)) {
            const _0x4c6665 = Number(RegExp.$1);
            const _0x533517 = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'battle', 'defeat');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x4c6665,
                _0x533517,
                'defeat'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnAttackTimes)) {
            const _0x290b89 = Number(RegExp.$1);
            const _0x34232a = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'attack'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x290b89,
                _0x34232a,
                'attackTimes'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnGuardTimes)) {
            const _0x1b0449 = Number(RegExp.$1);
            const _0x5ddbf5 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'guard'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x1b0449,
                _0x5ddbf5,
                'guardTimes'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnSkillUsage)) {
            const _0x3487ab = Number(RegExp.$1);
            const _0x42a0ba = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'skill'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x3487ab,
                _0x42a0ba,
                'skillUse'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnPhysSkill)) {
            const _0x52fcf6 = Number(RegExp.$1);
            const _0x1334e5 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'physicalSkill'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x52fcf6,
                _0x1334e5,
                'physSkillUse'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnMagSkill)) {
            const _0x376980 = Number(RegExp.$1);
            const _0x252584 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'magicalSkill'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x376980,
                _0x252584,
                'magSkillUse'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnCertSkill)) {
            const _0x57a600 = Number(RegExp.$1);
            const _0x4e5620 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'certainHitSkill'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x57a600,
                _0x4e5620,
                'certSkillUse'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnItemUsage)) {
            const _0x3f063e = Number(RegExp.$1);
            const _0x3cee25 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'item'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x3f063e,
                _0x3cee25,
                'itemUse'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnDealCritHitTimes)) {
            const _0xb969b0 = Number(RegExp.$1);
            const _0x592123 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'critDeal'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0xb969b0,
                _0x592123,
                'critDeal'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnTakeCritHitTimes)) {
            const _0x1a7643 = Number(RegExp.$1);
            const _0x221ff8 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'critTake'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x1a7643,
                _0x221ff8,
                'critTake'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnMissTimes)) {
            const _0x197f97 = Number(RegExp.$1);
            const _0x44f186 = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'miss'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(_0x11c135, _0x197f97, _0x44f186, 'miss');
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnEvadeTimes)) {
            const _0x143096 = Number(RegExp.$1);
            const _0x5e818c = _0x5c0046.getEquipPassiveLearnProgress(
                _0x22f4e1,
                'actionTimes',
                'evade'
            );
            _0x11c135 = this.ApplyUnlockHelpTextCondition(_0x11c135, _0x143096, _0x5e818c, 'evade');
        }
        {
            const _0x3f70a2 = _0x8e3ccc.match(_0x5c8d4b.LearnSTypeUsage);
            if (_0x3f70a2) {
                for (const _0x528841 of _0x3f70a2) {
                    _0x528841.match(_0x5c8d4b.LearnSTypeUsage);
                    let _0x23a981 = String(RegExp.$1);
                    const _0x85885c = Number(RegExp.$2);
                    const _0x1cc0e0 = /^\d+$/.test(_0x23a981);
                    _0x23a981 = _0x1cc0e0
                        ? Number(_0x23a981)
                        : DataManager.getStypeIdWithName(_0x23a981);
                    const _0xa094d6 = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'stype',
                        _0x23a981
                    );
                    const _0x10babd = $dataSystem.skillTypes[_0x23a981];
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x85885c,
                        _0xa094d6,
                        'stypeUse',
                        _0x10babd
                    );
                }
            }
        }
        {
            const _0x3f4e49 = _0x8e3ccc.match(_0x5c8d4b.LearnElementDeal);
            if (_0x3f4e49) {
                for (const _0x51f997 of _0x3f4e49) {
                    _0x51f997.match(_0x5c8d4b.LearnElementDeal);
                    let _0x2235ef = String(RegExp.$1);
                    const _0x187f67 = Number(RegExp.$2);
                    const _0x42b1fb = /^\d+$/.test(_0x2235ef);
                    _0x2235ef = _0x42b1fb
                        ? Number(_0x2235ef)
                        : DataManager.getElementIdWithName(_0x2235ef);
                    const _0xabcb21 = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'elementDeal',
                        _0x2235ef
                    );
                    const _0x44cc2e = $dataSystem.elements[_0x2235ef];
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x187f67,
                        _0xabcb21,
                        'elementDeal',
                        _0x44cc2e
                    );
                }
            }
        }
        {
            const _0x8407be = _0x8e3ccc.match(_0x5c8d4b.LearnElementTake);
            if (_0x8407be) {
                for (const _0x2bff7a of _0x8407be) {
                    _0x2bff7a.match(_0x5c8d4b.LearnElementTake);
                    let _0x29efbd = String(RegExp.$1);
                    const _0x214136 = Number(RegExp.$2);
                    const _0x553522 = /^\d+$/.test(_0x29efbd);
                    _0x29efbd = _0x553522
                        ? Number(_0x29efbd)
                        : DataManager.getElementIdWithName(_0x29efbd);
                    const _0x5c5102 = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'elementTake',
                        _0x29efbd
                    );
                    const _0x302f89 = $dataSystem.elements[_0x29efbd];
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x214136,
                        _0x5c5102,
                        'elementTake',
                        _0x302f89
                    );
                }
            }
        }
        {
            const _0x4734df = _0x8e3ccc.match(_0x5c8d4b.LearnStateDeal);
            if (_0x4734df) {
                for (const _0x314460 of _0x4734df) {
                    _0x314460.match(_0x5c8d4b.LearnStateDeal);
                    let _0x5ed42b = String(RegExp.$1);
                    const _0x231ee0 = Number(RegExp.$2);
                    _0x5ed42b = DataManager.getStateIdWithName(_0x5ed42b);
                    const _0x5ce1e9 = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'stateDeal',
                        _0x5ed42b
                    );
                    const _0x11ad70 = $dataStates[_0x5ed42b];
                    const _0x5bd06a = '\\I[%1]%2'.format(_0x11ad70.iconIndex, _0x11ad70.name);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x231ee0,
                        _0x5ce1e9,
                        'stateDeal',
                        _0x5bd06a
                    );
                }
            }
        }
        {
            const _0x1c3093 = _0x8e3ccc.match(_0x5c8d4b.LearnStateTake);
            if (_0x1c3093) {
                for (const _0x2cf3b2 of _0x1c3093) {
                    _0x2cf3b2.match(_0x5c8d4b.LearnStateTake);
                    let _0x2cc624 = String(RegExp.$1);
                    const _0x1fdabb = Number(RegExp.$2);
                    _0x2cc624 = DataManager.getStateIdWithName(_0x2cc624);
                    const _0x39741c = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'stateTake',
                        _0x2cc624
                    );
                    const _0x307076 = $dataStates[_0x2cc624];
                    const _0x5bee99 = '\\I[%1]%2'.format(_0x307076.iconIndex, _0x307076.name);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x1fdabb,
                        _0x39741c,
                        'stateTake',
                        _0x5bee99
                    );
                }
            }
        }
        if (Imported.VisuMZ_1_ElementStatusCore) {
            const _0x75f230 = _0x8e3ccc.match(_0x5c8d4b.LearnDefeatTrait);
            if (_0x75f230) {
                for (const _0x881d42 of _0x75f230) {
                    _0x881d42.match(_0x5c8d4b.LearnDefeatTrait);
                    const _0x38b11d = String(RegExp.$1);
                    const _0xc00423 = Number(RegExp.$2);
                    const _0x161c42 = _0x5c0046.getEquipPassiveLearnProgress(
                        _0x22f4e1,
                        'traitSlayer',
                        _0x38b11d.toUpperCase().trim()
                    );
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0xc00423,
                        _0x161c42,
                        'traitSlayer',
                        _0x38b11d
                    );
                }
            }
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnTotalDmgDeal)) {
            const _0x2bef96 = Number(RegExp.$1);
            const _0x414353 = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'hp', 'dmgDeal');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x2bef96,
                _0x414353,
                'totalDmgDeal'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnTotalDmgTake)) {
            const _0x2fbb5b = Number(RegExp.$1);
            const _0x1eee43 = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'hp', 'dmgTake');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x2fbb5b,
                _0x1eee43,
                'totalDmgTake'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnTotalHealDeal)) {
            const _0x29de0b = Number(RegExp.$1);
            const _0x41500a = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'hp', 'healDeal');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x29de0b,
                _0x41500a,
                'totalHealDeal'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnTotalHealTake)) {
            const _0x16d3b9 = Number(RegExp.$1);
            const _0x2aa397 = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'hp', 'healTake');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x16d3b9,
                _0x2aa397,
                'totalHealTake'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnCountKills)) {
            const _0x399a62 = Number(RegExp.$1);
            const _0x199091 = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'kda', 'kills');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(_0x11c135, _0x399a62, _0x199091, 'kills');
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnCountDeaths)) {
            const _0x1022c7 = Number(RegExp.$1);
            const _0x12249c = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'kda', 'deaths');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x1022c7,
                _0x12249c,
                'deaths'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnCountAssists)) {
            const _0x57fbb1 = Number(RegExp.$1);
            const _0xaae2cf = _0x5c0046.getEquipPassiveLearnProgress(_0x22f4e1, 'kda', 'assists');
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x57fbb1,
                _0xaae2cf,
                'assists'
            );
        }
        if (_0x8e3ccc.match(_0x5c8d4b.LearnHaveGold)) {
            const _0x4d60a5 = Number(RegExp.$1);
            const _0x5943fa = $gameParty.gold();
            const _0x975c35 = TextManager.currencyUnit;
            _0x11c135 = this.ApplyUnlockHelpTextCondition(
                _0x11c135,
                _0x4d60a5,
                _0x5943fa,
                'haveGold',
                _0x975c35
            );
        }
        {
            const _0xbc160e = _0x8e3ccc.match(_0x5c8d4b.LearnHaveItem);
            if (_0xbc160e) {
                for (const _0x1a6ee1 of _0xbc160e) {
                    _0x1a6ee1.match(_0x5c8d4b.LearnHaveItem);
                    const _0x132466 = String(RegExp.$1);
                    const _0x5c6b52 = Number(RegExp.$2);
                    const _0x3b14f6 = /^\d+$/.test(_0x132466);
                    const _0x5073ff = _0x3b14f6
                        ? Number(_0x132466)
                        : DataManager.getItemIdWithName(_0x132466);
                    const _0x4c4795 = $dataItems[_0x5073ff];
                    if (!_0x4c4795) {
                        continue;
                    }
                    const _0x329cf1 = $gameParty.numItems(_0x4c4795);
                    const _0x3ca19f = '\\I[%1]%2'.format(_0x4c4795.iconIndex, _0x4c4795.name);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x5c6b52,
                        _0x329cf1,
                        'haveItem',
                        _0x3ca19f
                    );
                }
            }
        }
        {
            const _0x5f3494 = _0x8e3ccc.match(_0x5c8d4b.LearnHaveWeapon);
            if (_0x5f3494) {
                for (const _0x49ba98 of _0x5f3494) {
                    _0x49ba98.match(_0x5c8d4b.LearnHaveWeapon);
                    const _0xb97061 = String(RegExp.$1);
                    const _0x679d17 = Number(RegExp.$2);
                    const _0x2961b6 = /^\d+$/.test(_0xb97061);
                    const _0x1d506d = _0x2961b6
                        ? Number(_0xb97061)
                        : DataManager.getWeaponIdWithName(_0xb97061);
                    const _0x2d3e03 = $dataWeapons[_0x1d506d];
                    if (!_0x2d3e03) {
                        continue;
                    }
                    const _0xb77f4d = $gameParty.numItems(_0x2d3e03);
                    const _0x1ed63e = '\\I[%1]%2'.format(_0x2d3e03.iconIndex, _0x2d3e03.name);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x679d17,
                        _0xb77f4d,
                        'haveWeapon',
                        _0x1ed63e
                    );
                }
            }
        }
        {
            const _0x1e36fe = _0x8e3ccc.match(_0x5c8d4b.LearnHaveArmor);
            if (_0x1e36fe) {
                for (const _0x33b337 of _0x1e36fe) {
                    _0x33b337.match(_0x5c8d4b.LearnHaveArmor);
                    const _0x5eb528 = String(RegExp.$1);
                    const _0x2c1657 = Number(RegExp.$2);
                    const _0x18061f = /^\d+$/.test(_0x5eb528);
                    const _0x3561fb = _0x18061f
                        ? Number(_0x5eb528)
                        : DataManager.getArmorIdWithName(_0x5eb528);
                    const _0x2e8e0d = $dataArmors[_0x3561fb];
                    if (!_0x2e8e0d) {
                        continue;
                    }
                    const _0x661e9 = $gameParty.numItems(_0x2e8e0d);
                    const _0x51a518 = '\\I[%1]%2'.format(_0x2e8e0d.iconIndex, _0x2e8e0d.name);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x2c1657,
                        _0x661e9,
                        'haveArmor',
                        _0x51a518
                    );
                }
            }
        }
        {
            const _0x4d1b16 = _0x8e3ccc.match(_0x5c8d4b.LearnHaveParam);
            if (_0x4d1b16) {
                for (const _0x43432a of _0x4d1b16) {
                    _0x43432a.match(_0x5c8d4b.LearnHaveParam);
                    let _0x146dc = String(RegExp.$1).toUpperCase().trim();
                    const _0x3fb48d = Number(RegExp.$2);
                    if (_0x146dc === 'MHP') {
                        _0x146dc = 'MAXHP';
                    }
                    if (_0x146dc === 'MAX HP') {
                        _0x146dc = 'MAXHP';
                    }
                    if (_0x146dc === 'MMP') {
                        _0x146dc = 'MAXMP';
                    }
                    if (_0x146dc === 'MAX MP') {
                        _0x146dc = 'MAXMP';
                    }
                    const _0x99354 = ['MAXHP', 'MAXMP', 'ATK', 'DEF', 'MAT', 'MDF', 'AGI', 'LUK'];
                    const _0x4e9405 = _0x99354.indexOf(_0x146dc);
                    const _0x5dc2e9 = _0x5c0046.param(_0x4e9405);
                    const _0x345b98 = TextManager.paramName(_0x146dc);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x3fb48d,
                        _0x5dc2e9,
                        'haveParam',
                        _0x345b98
                    );
                }
            }
        }
        {
            const _0x44b2cd = _0x8e3ccc.match(_0x5c8d4b.LearnHaveXParam);
            if (_0x44b2cd) {
                for (const _0x561d03 of _0x44b2cd) {
                    _0x561d03.match(_0x5c8d4b.LearnHaveXParam);
                    let _0x475cd3 = String(RegExp.$1).toUpperCase().trim();
                    const _0x22e42e = Number(RegExp.$2);
                    const _0x1ce513 = [
                        'HIT',
                        'EVA',
                        'CRI',
                        'CEV',
                        'MEV',
                        'MRF',
                        'CNT',
                        'HRG',
                        'MRG',
                        'TRG',
                    ];
                    const _0x6c2bb8 = _0x1ce513.indexOf(_0x475cd3);
                    const _0x59a14e = Math.round(_0x5c0046.xparam(_0x6c2bb8) * 0x64);
                    const _0x10b44f = TextManager.paramName(_0x475cd3);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x22e42e,
                        _0x59a14e,
                        'haveXParam',
                        _0x10b44f
                    );
                }
            }
        }
        {
            const _0x42f555 = _0x8e3ccc.match(_0x5c8d4b.LearnHaveSParam);
            if (_0x42f555) {
                for (const _0x16d079 of _0x42f555) {
                    _0x16d079.match(_0x5c8d4b.LearnHaveSParam);
                    let _0x4c6159 = String(RegExp.$1).toUpperCase().trim();
                    const _0x101b40 = Number(RegExp.$2);
                    const _0x2640fd = [
                        'TGR',
                        'GRD',
                        'REC',
                        'PHA',
                        'MCR',
                        'TCR',
                        'PDR',
                        'MDR',
                        'FDR',
                        'EXR',
                    ];
                    const _0xb18ff0 = _0x2640fd.indexOf(_0x4c6159);
                    const _0x18f0fd = Math.round(_0x5c0046.sparam(_0xb18ff0) * 0x64);
                    const _0x193a5c = TextManager.paramName(_0x4c6159);
                    _0x11c135 = this.ApplyUnlockHelpTextCondition(
                        _0x11c135,
                        _0x101b40,
                        _0x18f0fd,
                        'haveSParam',
                        _0x193a5c
                    );
                }
            }
        }
    }
    if (_0x11c135 === '') {
        _0x11c135 = _0x13fa06.helpNothing;
    }
    let _0x1c47c0 = _0x13fa06.helpFmt.format(_0x11c135);
    if (_0x13fa06.helpWordWrap && Imported.VisuMZ_1_MessageCore) {
        _0x1c47c0 = '<WordWrap>' + _0x1c47c0;
    }
    return _0x1c47c0;
};
VisuMZ.EquipPassiveSys.ApplyUnlockHelpTextCondition = function (
    _0x37316a,
    _0x4afe7f,
    _0x37ce3f,
    _0xfcd0a,
    _0x3b043c
) {
    const _0xd5c1fe = TextManager.EQUIP_PASSIVE_SYS;
    const _0x548881 = _0xd5c1fe.helpDescFmt;
    _0x37ce3f = _0x37ce3f.clamp(0x0, _0x4afe7f);
    let _0x47bbfd = '';
    const _0x39f6c7 = _0x548881.progressFmt;
    const _0x431290 = _0x548881.progressFraction.format(_0x37ce3f, _0x4afe7f);
    const _0x5012c3 = _0x548881.progressPercent.format(Math.round((_0x37ce3f / _0x4afe7f) * 0x64));
    _0x47bbfd = _0x39f6c7.format(
        _0x431290.length > _0x548881.progressLengthLimit ? _0x5012c3 : _0x431290
    );
    if (_0x37316a.length > 0x0) {
        _0x37316a += _0xd5c1fe.helpSpacer;
        if (_0xd5c1fe.helpSpacing) {
            _0x37316a += ' ';
        }
    }
    if (_0x37ce3f >= _0x4afe7f) {
        _0x37316a += '\\C[%1]'.format(_0xd5c1fe.helpMeetConditionColor);
        _0x47bbfd = _0x548881.progressComplete;
    }
    _0x37316a += (_0x548881[_0xfcd0a] || '---')
        .format(_0x4afe7f, _0x47bbfd, _0x3b043c || '')
        .trim();
    if (_0x37ce3f >= _0x4afe7f) {
        _0x37316a += '\\C[0]';
    }
    return _0x37316a;
};
function Window_EquipPassiveStatus() {
    this.initialize(...arguments);
}
Window_EquipPassiveStatus.prototype = Object.create(Window_Base.prototype);
Window_EquipPassiveStatus.prototype.constructor = Window_EquipPassiveStatus;
Window_EquipPassiveStatus.SETTINGS = {
    showWindow: VisuMZ.EquipPassiveSys.Settings.Window.showStatusWindow ?? true,
    bgType: VisuMZ.EquipPassiveSys.Settings.Window.StatusWindow_BgType ?? 0x0,
    maxCapacityTextColor: VisuMZ.EquipPassiveSys.Settings.Window.MaxCapacityColor ?? 0x11,
};
Window_EquipPassiveStatus.prototype.initialize = function (_0x28393e) {
    Window_Base.prototype.initialize.call(this, _0x28393e);
    this.hide();
};
Window_EquipPassiveStatus.prototype.setActor = function (_0x2ad427) {
    if (this._actor !== _0x2ad427) {
        this._actor = _0x2ad427;
        this.refresh();
    }
};
Window_EquipPassiveStatus.prototype.refresh = function () {
    this.contents.clear();
    this.resetFontSettings();
    {
        const _0x26de39 = TextManager.EQUIP_PASSIVE_SYS.capacity;
        this.changeTextColor(ColorManager.systemColor());
        const _0x2a7a9a = this.innerWidth - this.itemPadding() * 0x2;
        this.drawText(_0x26de39, this.itemPadding(), 0x0, _0x2a7a9a, 'left');
        this.resetFontSettings();
    }
    if (this._actor) {
        const _0x253e0e = TextManager.EQUIP_PASSIVE_SYS.capacityFmt;
        let _0x37c252 = this._actor.equipPassiveCurrentCapacity();
        let _0x34b51b = this._actor.equipPassiveMaxCapacity();
        const _0x222f26 = Window_EquipPassiveStatus.SETTINGS.maxCapacityTextColor || 0x0;
        if (_0x222f26 > 0x0 && _0x37c252 >= _0x34b51b) {
            _0x37c252 = '\\C[%1]%2\\C[0]'.format(_0x222f26, _0x37c252);
            _0x34b51b = '\\C[%1]%2\\C[0]'.format(_0x222f26, _0x34b51b);
        }
        const _0x35f4a7 = '\\I[%1]'.format(ImageManager.EQUIP_PASSIVE_SYS.capacity);
        const _0x2df654 = _0x253e0e.format(_0x37c252, _0x34b51b, _0x35f4a7);
        const _0x45785c = this.textSizeEx(_0x2df654).width;
        const _0x47adbf = this.innerWidth - this.itemPadding() - _0x45785c;
        this.drawTextEx(_0x2df654, _0x47adbf, 0x0);
    }
};
VisuMZ.EquipPassiveSys.DEBUG = false;
