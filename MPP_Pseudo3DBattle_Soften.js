// === MODIFIED: Cap Camera Transition Speed Only ===
// Keeps all original behavior except limiting duration for ultra-fast camera moves

(() => {
    const clampDuration = move => {
        if (!move) return null;
        if (typeof move.duration === 'number' && move.duration < 250) {
            move.duration = 250; // enforce a smoother, slower camera transition
        }
        if (Array.isArray(move.route)) {
            for (const r of move.route) {
                if (typeof r.duration === 'number' && r.duration < 250) {
                    r.duration = 250; // enforce smooth route transitions
                }
            }
        }
        return move;
    };

    const keys = Object.keys(Pseudo3DBattle.moveMethods);
    for (const key of keys) {
        const original = Pseudo3DBattle.moveMethods[key];
        if (typeof original === 'function') {
            Pseudo3DBattle.moveMethods[key] = function (...args) {
                const result = original.apply(this, args);
                return clampDuration(result);
            };
        }
    }
})();
