//=============================================================================
// DhoomEquipmentSockets.js
//=============================================================================
var Imported = Imported || {};
Imported.Dhoom_EquipmentSockets = '1.3';

var Dhoom = Dhoom || {};
Dhoom.EquipmentSockets = Dhoom.EquipmentSockets || {};
/*:
 * @plugindesc Dhoom EquipmentSockets v1.3 - 22/05/2024 - 05/06/2024
 * <AUTHOR> | DrDhoom#8315
 * @url drd-workshop.blogspot.com
 *
 * @param Show Confirmation On Empty Socket
 * @desc Show confirmation window before installing into an empty socket?
 * @type boolean
 * @default true
 *
 * @param Show Confirmation On Filled Socket
 * @desc Show confirmation window before installing into a filled socket?
 * @type boolean
 * @default true
 *
 * @param Confirmation Text Empty Socket
 * @desc Confirmation text before installing into an empty socket.
 * @default You won't be able to retrieve the gem back after installed, proceed?
 *
 * @param Confirmation Text Filled Socket
 * @desc Confirmation text before installing into a filled socket.
 * @default The already installed gem will be destroyed and replaced, proceed?
 *
 * @param Empty Slot Icon
 * @desc Empty socket slot icon at the end of equipments name.
 * @type icon
 * @default 16
 *
 * @param Draw Empty Slot Icon Has Item
 * @desc Draw empty slot icon when there's socket installed?
 * @type boolean
 * @default true
 *
 * @param Item Categories
 * @type struct<itemCategory>[]
 * @default ["{\"Type\":\"AllWeapons\",\"Icon\":\"97\",\"SwitchID\":\"0\"}","{\"Type\":\"EType:2\",\"Icon\":\"128\",\"SwitchID\":\"0\"}","{\"Type\":\"EType:3\",\"Icon\":\"131\",\"SwitchID\":\"0\"}","{\"Type\":\"EType:4\",\"Icon\":\"137\",\"SwitchID\":\"0\"}","{\"Type\":\"EType:5\",\"Icon\":\"145\",\"SwitchID\":\"0\"}"]
 *
 * @param Menu Background
 * @descs Background setting.
 * @type struct<imageSetting>
 * @default {"anchor":"7","x":"0","y":"0","filename":"","opacity":"255","scaleX":"100.0","scaleY":"100.0"}
 *
 * @param Menu Display Settings
 * @desc Static texts and images displayed on the menu.
 * @type struct<menuDisplaySetting>[]
 * @default []
 *
 * @param Help Window Setting
 * @desc Help window setting.
 * @type struct<helpWindow>
 * @default {"x":"0","y":"52","width":"1272","height":"96","padding":"12","opacity":"255","lineHeight":"36","style":"{\"name\":\"rmmz-mainfont, Verdana, sans-serif\",\"size\":\"27\",\"color\":\"#FFFFFF\",\"outlineWidth\":\"4\",\"outlineColor\":\"#000000\",\"shadowBlur\":\"0\",\"shadowColor\":\"#000000\",\"bold\":\"false\",\"italic\":\"false\",\"spacing\":\"0\",\"align\":\"left\",\"case\":\"unchanged\"}","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}"}
 *
 * @param Equip Window Setting
 * @desc Equipment list window setting.
 * @type struct<itemWindowSetting>
 * @default {"visual":"false","x":"0","y":"216","width":"636","height":"184","padding":"12","opacity":"255","lineHeight":"36","style":"{\"name\":\"rmmz-mainfont, Verdana, sans-serif\",\"size\":\"27\",\"color\":\"#FFFFFF\",\"outlineWidth\":\"4\",\"outlineColor\":\"#000000\",\"shadowBlur\":\"0\",\"shadowColor\":\"#000000\",\"bold\":\"false\",\"italic\":\"false\",\"spacing\":\"0\",\"align\":\"left\",\"case\":\"unchanged\"}","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}"}
 *
 * @param Slot Window Setting
 * @desc Socket slot window setting.
 * @type struct<slotWindowSetting>
 * @default {"visual":"false","x":"636","y":"216","width":"318","height":"496","padding":"12","opacity":"255","lineHeight":"36","style":"{\"name\":\"rmmz-mainfont, Verdana, sans-serif\",\"size\":\"27\",\"color\":\"#FFFFFF\",\"outlineWidth\":\"4\",\"outlineColor\":\"#000000\",\"shadowBlur\":\"0\",\"shadowColor\":\"#000000\",\"bold\":\"false\",\"italic\":\"false\",\"spacing\":\"0\",\"align\":\"left\",\"case\":\"unchanged\"}","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}"}
 *
 * @param Item Window Setting
 * @desc Item window setting.
 * @type struct<itemWindowSetting>
 * @default {"visual":"false","x":"954","y":"216","width":"318","height":"496","padding":"12","opacity":"255","lineHeight":"36","style":"{\"name\":\"rmmz-mainfont, Verdana, sans-serif\",\"size\":\"27\",\"color\":\"#FFFFFF\",\"outlineWidth\":\"4\",\"outlineColor\":\"#000000\",\"shadowBlur\":\"0\",\"shadowColor\":\"#000000\",\"bold\":\"false\",\"italic\":\"false\",\"spacing\":\"0\",\"align\":\"left\",\"case\":\"unchanged\"}","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}"}
 *
 * @param Status Window Setting
 * @desc Status window setting.
 * @type struct<statusWindowSetting>
 * @default {"x":"0","y":"400","width":"636","height":"312","padding":"12","opacity":"255","style":"{\"name\":\"rmmz-mainfont, Verdana, sans-serif\",\"size\":\"27\",\"color\":\"#FFFFFF\",\"outlineWidth\":\"4\",\"outlineColor\":\"#000000\",\"shadowBlur\":\"0\",\"shadowColor\":\"#000000\",\"bold\":\"false\",\"italic\":\"false\",\"spacing\":\"0\",\"align\":\"left\",\"case\":\"unchanged\"}","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}","params":"[\"MAXHP\",\"MAXMP\",\"ATK\",\"DEF\",\"MAT\",\"MDF\",\"AGI\",\"LUK\"]"}
 *
 * @param Confirmation Window Setting
 * @desc Confirmation window setting.
 * @type struct<confirmationWindowSetting>
 * @default {"x":"322","y":"304","width":"636","height":"148","padding":"12","opacity":"255","background":"{\"anchor\":\"5\",\"x\":\"0\",\"y\":\"0\",\"filename\":\"\",\"opacity\":\"255\",\"scaleX\":\"100.0\",\"scaleY\":\"100.0\"}","helpText":"{\"text\":\"%1\",\"x\":\"0\",\"y\":\"0\",\"width\":\"612\",\"height\":\"72\",\"style\":\"{\\\"name\\\":\\\"rmmz-mainfont, Verdana, sans-serif\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"true\",\"spacing\":\"3\",\"centerVertical\":\"false\"}","confirm":"{\"text\":\"Confirm\",\"x\":\"0\",\"y\":\"72\",\"width\":\"306\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"rmmz-mainfont, Verdana, sans-serif\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"false\",\"spacing\":\"3\",\"centerVertical\":\"false\"}","cancel":"{\"text\":\"Cancel\",\"x\":\"306\",\"y\":\"72\",\"width\":\"306\",\"height\":\"36\",\"style\":\"{\\\"name\\\":\\\"rmmz-mainfont, Verdana, sans-serif\\\",\\\"size\\\":\\\"27\\\",\\\"color\\\":\\\"#FFFFFF\\\",\\\"outlineWidth\\\":\\\"4\\\",\\\"outlineColor\\\":\\\"#000000\\\",\\\"shadowBlur\\\":\\\"0\\\",\\\"shadowColor\\\":\\\"#000000\\\",\\\"bold\\\":\\\"false\\\",\\\"italic\\\":\\\"false\\\",\\\"spacing\\\":\\\"0\\\",\\\"align\\\":\\\"center\\\",\\\"case\\\":\\\"unchanged\\\"}\",\"wordwrap\":\"false\",\"spacing\":\"3\",\"centerVertical\":\"false\"}"}
 *
 * @help ============================================================================
 * Introduction
 * ============================================================================
 *
 * Added ability to add sockets to equipments. An item can be insterted to the
 * socket to gain bonus stats.
 *
 * ============================================================================
 * Item Notetags
 * ============================================================================
 *
 * <socketType: TYPE, TYPE, ...>
 * - Define which socket types this item can be installed to.
 * - Case sensitive.
 *
 * <socketParam: PARAM, VALUE>
 * - PARAM: mhp, mmp, atk, def, agi, luk, mdf, mat, hit, eva, cri, cev, mev,
 *          mrf, cnt, hrg, mrg, trg, tgr, grd, rec, pha, mcr, tcr, pdr, mdr,
 *          fdr, exr.
 * - VALUE: Can be whole number or percentage.
 * Ex. <socketParam: atk, %120>
 *     <socketParam: mdf, -20>
 *
 * <socketStateTrait: STATEID, STATEID, ...>
 * - Transfer traits from states.
 * - Parameter multiplier (such as atk, def, etc and not Ex nor Sp) will modify
 *   the equipment parameters directly and will not be treated as actor traits.
 *
 * <socketAutoState: STATEID, STATEID, ...>
 * - Automatically add states to the equipping actor at the start of a battle.
 *
 * ============================================================================
 * Weapon & Armor Notetags
 * ============================================================================
 *
 * <sockets: TYPE, TYPE, ...>
 * - Define amount of socket slots and the slot type>
 * - Case sensitive.
 *
 * ============================================================================
 *
 * @command OpenEquipSocketMenu
 * @text Open Equipment Socket Menu
 * @desc Open equipment socket menu.
 */

/*~struct~confirmationWindowSetting:
@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param width
@text Width
@type number
@min 4
@default 1

@param height
@text Height
@type number
@min 4
@default 1

@param padding
@text Padding
@type number
@min 0
@default 0

@param opacity
@text Window Opacity
@type number
@min 0
@max 255
@default 255

@param background
@text Background Image
@type struct<imageSetting>
@default 

@param helpText
@text Help Text Setting
@desc %1 = Help text.
@type struct<textSetting>
@default 

@param confirm
@text Confirm Setting
@type struct<textSetting>
@default 

@param cancel
@text Cancel Setting
@type struct<textSetting>
@default 
*/

/*~struct~statusWindowSetting:
@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param width
@text Width
@type number
@min 4
@default 1

@param height
@text Height
@type number
@min 4
@default 1

@param padding
@text Padding
@type number
@min 0
@default 0

@param opacity
@text Window Opacity
@type number
@min 0
@max 255
@default 255

@param style
@text Text Style
@type struct<FontStyle>
@default 

@param background
@text Background Image
@type struct<imageSetting>
@default 

@param params
@text Parameters
@type combo[]
@option MHP
@option MMP
@option ATK
@option DEF
@option MAT
@option MDF
@option AGI
@option LUK
@option HIT
@option EVA
@option CRI
@option CEV
@option MEV
@option MRF
@option CNT
@option HRG
@option MRG
@option TRG
@option TGR
@option GRD
@option REC
@option PHA
@option TCR
@option MCR
@option PDR
@option MDR
@option FDR
@option EXR
@default []
*/

/*~struct~slotWindowSetting:
@param visual
@text Use Visual Item Inventory?
@desc When set to false, use the default item list instead.
@type boolean
@default true

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param width
@text Width
@type number
@min 4
@default 1

@param height
@text Height
@type number
@min 4
@default 1

@param padding
@text Padding
@type number
@min 0
@default 0

@param opacity
@text Window Opacity
@type number
@min 0
@max 255
@default 255

@param lineHeight
@text Line Height
@type number
@min 1
@default 32

@param style
@text Text Style
@type struct<FontStyle>
@default 

@param background
@text Background Image
@type struct<imageSetting>
@default 

@param Empty Slot Text
@desc Text for empty slot.
@default Empty
*/

/*~struct~itemWindowSetting:
@param visual
@text Use Visual Item Inventory?
@desc When set to false, use the default item list instead.
@type boolean
@default true

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param width
@text Width
@type number
@min 4
@default 1

@param height
@text Height
@type number
@min 4
@default 1

@param padding
@text Padding
@type number
@min 0
@default 0

@param opacity
@text Window Opacity
@type number
@min 0
@max 255
@default 255

@param lineHeight
@text Line Height
@type number
@min 1
@default 32

@param style
@text Text Style
@type struct<FontStyle>
@default 

@param background
@text Background Image
@type struct<imageSetting>
@default 
*/

/*~struct~itemCategory:
@param Type
@text Type
@type combo
@option AllItems
@option 
@option RegularItems
@option KeyItems
@option HiddenItemA
@option HiddenItemB
@option 
@option Consumable
@option Nonconsumable
@option 
@option AlwaysUsable
@option BattleUsable
@option FieldUsable
@option NeverUsable
@option 
@option AllWeapons
@option WType:x
@option 
@option AllArmors
@option AType:x
@option 
@option EType:x
@option 
@option Category:x
@option
@desc A list of the item categories displayed in the Item/Shop
menus. Replace x with ID numbers or text.
@default RegularItems

@param Icon
@text Icon
@desc Icon used for this category.
Use 0 for no icon.
@default 0

@param SwitchID
@text Visibility Switch
@type switch
@desc This Switch must be turned ON in order for the category to show.
Use 0 for no Switch requirement.
@default 0
*/

/*~struct~helpWindow:
@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param width
@text Width
@type number
@min 4
@default 1

@param height
@text Height
@type number
@min 4
@default 1

@param padding
@text Padding
@type number
@min 0
@default 0

@param opacity
@text Window Opacity
@type number
@min 0
@max 255
@default 255

@param lineHeight
@text Line Height
@type number
@min 1
@default 32

@param style
@text Text Style
@type struct<FontStyle>
@default 

@param background
@text Background Image
@type struct<imageSetting>
@default 
*/

/*~struct~menuDisplaySetting:
@param anchor
@text Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 7

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param background
@text Background Image Filename
@type file
@dir img/system/

@param images
@text Image Settings
@type struct<imageSetting>[]
@default []

@param texts
@text Text Settings
@type struct<textSetting>[]
@default []
*/

/*~struct~buttonSetting:
@param filename
@text Image Filename
@dir img/system/
@type file

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param anchor
@text Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 7

@param selected
@text Selected Color
@type struct<colorSetting>
@default {"r":"255","g":"255","b":"255","a":"255"}

@param disabled
@text Disabled Color
@type struct<colorSetting>
@default {"r":"255","g":"255","b":"255","a":"255"}

@param mouse
@text Mouse Hover Filename
@type file
@dir img/cursors/
@default select

@param text
@text Text Setting
@type struct<textSetting>
@default 
*/

/*~struct~colorSetting:
@param r
@text Red Value
@type number
@min 0
@max 255
@default 255

@param g
@text Green Value
@type number
@min 0
@max 255
@default 255

@param b
@text Blue Value
@type number
@min 0
@max 255
@default 255

@param a
@text Alpha Value
@type number
@min 0
@max 255
@default 255
*/

/*~struct~imageSetting:
@param anchor
@text Anchor Point
@type select
@option Bottom-Left
@value 1
@option Bottom-Center
@value 2
@option Bottom-Right
@value 3
@option Middle-Left
@value 4
@option Middle-Center
@value 5
@option Middle-Right
@value 6
@option Top-Left
@value 7
@option Top-Center
@value 8
@option Top-Right
@value 9
@default 5

@param x
@text X Position
@desc Position on X axis.
@type number
@min -9999999
@default 0

@param y
@text Y Position
@desc Position on Y axis.
@type number
@min -9999999
@default 0

@param filename
@text Filename
@desc Image filename.
@type file
@dir img/system/

@param opacity
@text Opacity
@desc Image opacity.
@type number
@min 0
@max 255
@default 255

@param scaleX
@text Scale X
@desc Horizontal scale, in percentage.
@type number
@min -999999
@decimals 1
@default 100.0

@param scaleY
@text Scale Y
@desc Vertical scale, in percentage.
@type number
@min -999999
@decimals 1
@default 100.0
*/

/*~struct~textSetting:
@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area.
@type number
@min 4
@default 1

@param height
@text Text Area Height
@desc Height of text area.
@type number
@min 4
@default 1

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param wordwrap
@text Word Wrap
@type boolean
@default true

@param spacing
@text Vertical Spacing
@type number
@min -999999
@default 3

@param centerVertical
@text Center Vertically?
@type boolean
@default true
*/

/*~struct~commandTextSetting:
@param text
@text Text

@param x
@text X Position
@type number
@min -999999999
@default 0

@param y
@text Y Position
@type number
@min -999999999
@default 0

@param width
@text Text Area Width
@desc Width of text area. 0 = Auto.
@type number
@min 0
@default 0

@param height
@text Text Area Height
@desc Height of text area. 0 = Auto.
@type number
@min 0
@default 0

@param style
@text Text Style
@desc Style setting.
@type struct<FontStyle>
@default {"name":"","size":"32","color":"#FFFFFF","outlineWidth":"1","outlineColor":"#000000","bold":"false","italic":"false","spacing":"0","align":"center"}

@param wordwrap
@text Word Wrap
@type boolean
@default true

@param spacing
@text Vertical Spacing
@type number
@min -999999
@default 3

@param centerVertical
@text Center Vertically?
@type boolean
@default true
*/

/*~struct~FontStyle:
@param name
@text Font Name
@desc Font name, leave empty if you want to use the default font.
@default 

@param size
@text Font Size
@desc Font size
@default 32
@type number
@min 1

@param color
@text Font Color
@desc Font color
@default #FFFFFF

@param outlineWidth
@text Font Outline Width
@desc Font outline width
@default 1
@type number

@param outlineColor
@text Font Outline Color
@desc Font outline color
@default #000000

@param shadowBlur
@text Shadow Blur
@desc Shadow blur strength.
@type number
@min 0
@default 0

@param shadowColor
@text Shadow color
@default #000000

@param bold
@text Font Bold
@desc Font bold
@default false
@type boolean

@param italic
@text Font Italic
@desc Font italic
@default false
@type boolean

@param spacing
@text Font Spacing
@type number
@min 0
@default 0

@param align
@text Text Alignment
@desc Text alignment
@default center
@type select
@option left
@option center
@option right

@param case
@text Text Case
@type select
@option unchanged
@option lowercase
@option UPPERCASE
@option Titlecase
@default unchanged
*/

Dhoom.Parameters = $plugins.filter(function (obj) {
    return obj.description.match(/Dhoom EquipmentSockets/);
})[0].parameters;
if (!Dhoom.jsonParse) {
    Dhoom.jsonParse = function (string) {
        if (typeof string === 'string') {
            try {
                return JSON.parse(
                    string,
                    function (key, value) {
                        if (typeof value === 'string') {
                            try {
                                return this.jsonParse(value);
                            } catch (e) {
                                return value;
                            }
                        } else {
                            return value;
                        }
                    }.bind(this)
                );
            } catch (e) {
                return string;
            }
        } else {
            return string;
        }
    };
}
if (!Dhoom.loadParam) {
    Dhoom.loadParam = function (sym) {
        return Dhoom.jsonParse(Dhoom.Parameters[sym]);
    };
}
Dhoom.EquipmentSockets.loadParameters = function () {
    for (let name in Dhoom.Parameters) {
        let sym = name.replace(/\s+/g, '');
        sym =
            (sym[1] && sym[1].toUpperCase() === sym[1] ? sym[0] : sym[0].toLowerCase()) +
            sym.slice(1);
        Dhoom.EquipmentSockets[sym] = Dhoom.loadParam(name);
    }
};
Dhoom.EquipmentSockets.loadParameters();

Dhoom.EquipmentSockets.startEquipID = 2000;
Dhoom.EquipmentSockets.realWeaponsLength = 1;
Dhoom.EquipmentSockets.realArmorsLength = 1;
Dhoom.EquipmentSockets.addedLength = 500;

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Array
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Array.prototype.unique = function () {
    let result = [];
    for (let i = 0; i < this.length; i++) {
        if (this.indexOf(this[i]) === i) result.push(this[i]);
    }
    return result;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// DataManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.DataManager_isDatabaseLoaded = DataManager.isDatabaseLoaded;
DataManager.isDatabaseLoaded = function () {
    if (!Dhoom.EquipmentSockets.DataManager_isDatabaseLoaded.call(this)) return false;
    if (!Dhoom.EquipmentSockets.isEquipmentSocketsInitialized) {
        this.DhoomInitEquipmentSockets();
        Dhoom.EquipmentSockets.isEquipmentSocketsInitialized = true;
    }
    return true;
};

DataManager.DhoomInitEquipmentSockets = function () {
    let group = [].concat($dataWeapons).concat($dataArmors);
    for (let i = 1; i < group.length; i++) {
        let object = group[i];
        if (object) {
            let notedata = object.note.split(/[\r\n]+/);
            for (let n = 0; n < notedata.length; n++) {
                let match = notedata[n].match(/<sockets:(,?\s*.+)+>/i);
                if (match) {
                    object.socketTypes = match[1].split(',').map(s => s.trim());
                    object.socketItems = new Array(object.socketTypes.length);
                    object.socketRealName = object.name;
                    object.socketBaseItem = object.id;
                    EquipSocketManager.makeName(object);
                }
            }
        }
    }
    group = $dataItems;
    for (let i = 1; i < group.length; i++) {
        let object = group[i];
        if (object) {
            let notedata = object.note.split(/[\r\n]+/);
            for (let n = 0; n < notedata.length; n++) {
                let match = notedata[n].match(/<(?:sockettype|sockettypes):(,?\s*.+)+>/i);
                if (match) {
                    object.socketTypes = match[1].split(',').map(s => s.trim());
                }
                match = notedata[n].match(/<socketparam:\s*(\w+),\s*([-+]?\d+)>/i);
                if (match) {
                    object.socketParams = object.socketParams || {};
                    let type = match[1].toLowerCase().trim();
                    object.socketParams[type] = Number(match[2]);
                } else {
                    match = notedata[n].match(/<socketparam:\s*(\w+),\s*%([-+]?\d+)>/i);
                    if (match) {
                        object.socketParamRates = object.socketParamRates || {};
                        let type = match[1].toLowerCase().trim();
                        object.socketParamRates[type] = Number(match[2]);
                    }
                }
                match = notedata[n].match(
                    /<(?:socketstatetrait|socketstatetraits):(,?\s*(\d+)+)+>/i
                );
                if (match) {
                    object.socketStateTraits = match[1].split(',').map(s => Number(s));
                }
                match = notedata[n].match(/<(?:socketautostate|socketautostates):(,?\s*(\d+)+)+>/i);
                if (match) {
                    object.socketAutoStates = match[1].split(',').map(s => Number(s));
                }
            }
        }
    }
};

Dhoom.EquipmentSockets.DataManager_createGameObjects = DataManager.createGameObjects;
DataManager.createGameObjects = function () {
    Dhoom.EquipmentSockets.DataManager_createGameObjects.call(this);
    EquipSocketManager.clearIndependentEquipments();
};

Dhoom.EquipmentSockets.DataManager_makeSaveContents = DataManager.makeSaveContents;
DataManager.makeSaveContents = function () {
    const contents = Dhoom.EquipmentSockets.DataManager_makeSaveContents.call(this);
    contents.socketWeapons = EquipSocketManager.getSaveContents($dataWeapons);
    contents.socketArmors = EquipSocketManager.getSaveContents($dataArmors);
    return contents;
};

Dhoom.EquipmentSockets.DataManager_extractSaveContents = DataManager.extractSaveContents;
DataManager.extractSaveContents = function (contents) {
    Dhoom.EquipmentSockets.DataManager_extractSaveContents.call(this, contents);
    EquipSocketManager.extractSaveContents(contents.socketWeapons, $dataWeapons);
    EquipSocketManager.extractSaveContents(contents.socketArmors, $dataArmors);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// EquipSocketManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
function EquipSocketManager() {
    throw new Error('This is a static class');
}

EquipSocketManager.getParamId = function (name) {
    return ['mhp', 'mmp', 'atk', 'def', 'mat', 'mdf', 'agi', 'luk'].indexOf(name);
};

EquipSocketManager.getXParamId = function (name) {
    return ['hit', 'eva', 'cri', 'cev', 'mev', 'mrf', 'cnt', 'hrg', 'mrg', 'trg'].indexOf(name);
};

EquipSocketManager.getSParamId = function (name) {
    return ['tgr', 'grd', 'rec', 'pha', 'mcr', 'tcr', 'pdr', 'mdr', 'fdr', 'exr'].indexOf(name);
};

EquipSocketManager.initEquipmentEmptySlot = function () {
    if (!$gameSystem._socketEmptyEquipmentSlots) {
        $gameSystem._socketEmptyEquipmentSlots = { weapons: [], armors: [] };
    }
};

EquipSocketManager.hasSocket = function (equip) {
    return equip.socketItems && equip.socketItems.length > 0;
};

EquipSocketManager.isSocketItem = function (item, type) {
    return !!(
        DataManager.isItem(item) &&
        item.socketTypes &&
        ((!type && item.socketTypes.length) || item.socketTypes.contains(type))
    );
};

EquipSocketManager.canInstallSocketItem = function (equip, index, item) {
    if (this.isSocketItem(item) && equip.socketTypes[index]) {
        return item.socketTypes.contains(equip.socketTypes[index]);
    }
    return false;
};

EquipSocketManager.isIndependent = function (equip) {
    if (!equip || !equip.socketBaseItem) return false;
    let container = this.getEquipContainer(equip);
    if (container) return container.indexOf(equip) !== equip.socketBaseItem;
    return false;
};

EquipSocketManager.makeName = function (equip) {
    if (this.hasSocket(equip)) {
        let realName = equip.socketRealName;
        let icons = [];
        for (let i = 0; i < equip.socketItems.length; i++) {
            let icon = equip.socketItems[i]
                ? $dataItems[equip.socketItems[i]].iconIndex
                : this.emptyIconIndex();
            icons.push('\\IS[' + icon + ']');
        }
        let newName = realName + ' ' + icons.join('');
        equip.name = newName;
    }
};

EquipSocketManager.emptyIconIndex = function () {
    return Dhoom.EquipmentSockets.emptySlotIcon;
};

EquipSocketManager.installSocket = function (equip, index, item) {
    if (
        this.hasSocket(equip) &&
        (!item || this.canInstallSocketItem(equip, index, item)) &&
        equip.socketItems.length > index
    ) {
        let container = this.getEquipContainer(equip);
        if (container) {
            equip.socketItems[index] = item ? item.id : null;
            let id = container.indexOf(equip);
            let oldEquip = equip;
            let newEquip = JsonEx.makeDeepCopy(container[equip.socketBaseItem]);
            newEquip.id = oldEquip.id;
            this.redoProcess(newEquip, oldEquip.socketItems);
            this.makeName(newEquip);
            container[id] = newEquip;
        }
    }
};

EquipSocketManager.previewInstallSocket = function (equip, index, item) {
    let container = this.getEquipContainer(equip);
    if (container) {
        let items = equip.socketItems.clone();
        items[index] = item ? item.id : null;
        let newEquip = JsonEx.makeDeepCopy(container[equip.socketBaseItem]);
        newEquip.id = equip.id;
        this.redoProcess(newEquip, items);
        this.makeName(newEquip);
        return newEquip;
    }
};

EquipSocketManager.redoProcess = function (equip, items) {
    let states = [];
    items.forEach((itemId, i) => {
        equip.socketItems[i] = itemId;
        if (itemId) {
            let item = $dataItems[itemId];
            this.processSocketTransfer(equip, item);
            if (item.socketStateTraits)
                states = states.concat(item.socketStateTraits.map(id => $dataStates[id]));
        }
    }, this);
    this.processStateTransfer(equip, states);
    this.roundParamValues(equip);
};

EquipSocketManager.processSocketTransfer = function (equip, item) {
    this.processParamTransfer(equip, item);
    this.processParamRateTransfer(equip, item);
};

EquipSocketManager.processStateTransfer = function (equip, states) {
    states.forEach(state => this.processTraitTransfer(equip, state.traits), this);
};

EquipSocketManager.processTraitTransfer = function (equip, traits) {
    traits.forEach(trait => {
        if (trait.code === Game_BattlerBase.TRAIT_PARAM) {
            equip.params[trait.dataId] *= trait.value;
        } else {
            equip.traits.push(trait);
        }
    }, this);
};

EquipSocketManager.roundParamValues = function (equip) {
    for (let i = 0; i < 8; i++) {
        equip.params[i] = Math.floor(equip.params[i]);
    }
};

EquipSocketManager.uninstallSocket = function (equip, index) {
    if (equip.socketItems[index]) {
        let item = $dataItems[equip.socketItems[index]];
        this.reverseParamTransfer(equip, item);
        this.reverseParamRateTransfer(equip, item);
    }
};

EquipSocketManager.getEquipContainer = function (equip) {
    if (DataManager.isWeapon(equip)) return $dataWeapons;
    if (DataManager.isArmor(equip)) return $dataArmors;
    return null;
};

EquipSocketManager.processParamTransfer = function (equip, item) {
    if (!item) return;
    if (!item.socketParams) return;
    let types = Object.keys(item.socketParams);
    for (let i = 0; i < types.length; i++) {
        let type = types[i];
        let paramId = this.getParamId(type);
        if (paramId >= 0) {
            equip.params[paramId] += item.socketParams[type];
        } else {
            paramId = this.getXParamId(type);
            if (paramId >= 0) {
                equip.traits.push({
                    code: Game_BattlerBase.TRAIT_XPARAM,
                    dataId: paramId,
                    value: item.socketParams[type] / 100,
                });
            } else {
                paramId = this.getSParamId(type);
                if (paramId >= 0) {
                    equip.traits.push({
                        code: Game_BattlerBase.TRAIT_SPARAM,
                        dataId: paramId,
                        value: item.socketParams[type] / 100,
                    });
                }
            }
        }
    }
};

EquipSocketManager.processParamRateTransfer = function (equip, item) {
    if (!item) return;
    if (!item.socketParamRates) return;
    let types = Object.keys(item.socketParamRates);
    for (let i = 0; i < types.length; i++) {
        let type = types[i];
        let paramId = this.getParamId(type);
        if (paramId >= 0) {
            equip.traits.push({
                code: Game_BattlerBase.TRAIT_PARAM,
                dataId: paramId,
                value: item.socketParamRates[type] / 100,
            });
        } else {
            paramId = this.getXParamId(type);
            if (paramId >= 0) {
                equip.traits.push({
                    code: Game_BattlerBase.TRAIT_XPARAM,
                    dataId: paramId,
                    value: item.socketParamRates[type] / 100,
                });
            } else {
                paramId = this.getSParamId(type);
                if (paramId >= 0) {
                    equip.traits.push({
                        code: Game_BattlerBase.TRAIT_SPARAM,
                        dataId: paramId,
                        value: item.socketParamRates[type] / 100,
                    });
                }
            }
        }
    }
};

EquipSocketManager.reverseParamTransfer = function (equip, item) {
    if (!item) return;
    if (!item.socketParams) return;
    let types = Object.keys(item.socketParams);
    for (let i = 0; i < types.length; i++) {
        let type = types[i];
        let paramId = this.getParamId(type);
        if (paramId >= 0) {
            equip.params[paramId] -= item.socketParams[type];
        } else {
            paramId = this.getXParamId(type);
            if (paramId >= 0) {
                this.removeItemTrait(
                    equip,
                    Game_BattlerBase.TRAIT_XPARAM,
                    paramId,
                    item.socketParams[type] / 100
                );
            } else {
                paramId = this.getSParamId(type);
                if (paramId >= 0) {
                    this.removeItemTrait(
                        equip,
                        Game_BattlerBase.TRAIT_SPARAM,
                        paramId,
                        item.socketParams[type] / 100
                    );
                }
            }
        }
    }
};

EquipSocketManager.reverseParamRateTransfer = function (equip, item) {
    if (!item) return;
    if (!item.socketParamRates) return;
    let types = Object.keys(item.socketParamRates);
    for (let i = 0; i < types.length; i++) {
        let type = types[i];
        let paramId = this.getParamId(type);
        if (paramId >= 0) {
            this.removeItemTrait(
                equip,
                Game_BattlerBase.TRAIT_PARAM,
                paramId,
                item.socketParamRates[type] / 100
            );
        } else {
            paramId = this.getXParamId(type);
            if (paramId >= 0) {
                this.removeItemTrait(
                    equip,
                    Game_BattlerBase.TRAIT_XPARAM,
                    paramId,
                    item.socketParamRates[type] / 100
                );
            } else {
                paramId = this.getSParamId(type);
                if (paramId >= 0) {
                    this.removeItemTrait(
                        equip,
                        Game_BattlerBase.TRAIT_SPARAM,
                        paramId,
                        item.socketParamRates[type] / 100
                    );
                }
            }
        }
    }
};

EquipSocketManager.removeItemTrait = function (item, code, dataId, value) {
    for (let i = 0; i < item.traits.length; i++) {
        let trait = item.traits[i];
        if (
            trait.code === code &&
            trait.dataId === dataId &&
            Math.floor(trait.value * 100) === Math.floor(value * 100)
        ) {
            item.traits.splice(i, 1);
            return;
        }
    }
};

EquipSocketManager.createIndependentEquipment = function (base) {
    this.initEquipmentEmptySlot();
    let container = this.getEquipContainer(base);
    if (this.isIndependent(base)) base = container[base.socketBaseItem];
    if (container) {
        let newId = Dhoom.EquipmentSockets.startEquipID;
        if (container.length > newId) newId = container.length;
        let type = container === $dataWeapons ? 'weapons' : 'armors';
        if ($gameSystem._socketEmptyEquipmentSlots[type].length)
            newId = $gameSystem._socketEmptyEquipmentSlots[type].shift();
        container[newId] = JsonEx.makeDeepCopy(base);
        container[newId].id = newId;
        return container[newId];
    }
    return null;
};

EquipSocketManager.getSaveContents = function (container) {
    let contents = {};
    for (let i = Dhoom.EquipmentSockets.startEquipID; i < container.length; i++) {
        if (container[i])
            contents[i] = { base: container[i].socketBaseItem, sockets: container[i].socketItems };
    }
    return contents;
};

EquipSocketManager.extractSaveContents = function (contents, container) {
    if (contents) {
        let ids = Object.keys(contents);
        for (let i = 0; i < ids.length; i++) {
            let id = ids[i];
            let item = JsonEx.makeDeepCopy(container[contents[id].base]);
            item.id = id;
            this.redoProcess(item, contents[id].sockets);
            this.makeName(item);
            container[id] = item;
        }
    }
};

EquipSocketManager.clearIndependentEquipments = function () {
    $dataWeapons.splice(Dhoom.EquipmentSockets.startEquipID);
    $dataArmors.splice(Dhoom.EquipmentSockets.startEquipID);
};

EquipSocketManager.deleteIndependentEquipment = function (equip) {
    this.initEquipmentEmptySlot();
    let container = this.getEquipContainer(equip);
    if (container) {
        let index = container.indexOf(equip);
        container[index] = null;
        $gameSystem._socketEmptyEquipmentSlots[
            container === $dataWeapons ? 'weapons' : 'armors'
        ].push(index);
    }
};

EquipSocketManager.paramValueByName = function (equip, name, formatted) {
    name = (name || '').toLowerCase();
    if (this.getParamId(name) !== -1) {
        return equip.params[this.getParamId(name)];
    }
    if (this.getXParamId(name) !== -1) {
        let value = this.equipXParam(equip, name);
        return formatted ? Math.round(value * 100) + '%' : value;
    }
    if (this.getSParamId(name) !== -1) {
        let value = this.equipSParam(equip, name);
        return formatted ? Math.round(value * 100) + '%' : value;
    }
    return null;
};

EquipSocketManager.equipXParam = function (equip, name) {
    let id = this.getXParamId(name);
    let traits = equip.traits.filter(
        trait => trait.code === Game_BattlerBase.TRAIT_XPARAM && trait.dataId === id
    );
    return traits.reduce((r, trait) => r + trait.value, 0);
};

EquipSocketManager.equipSParam = function (equip, name) {
    let id = this.getSParamId(name);
    let traits = equip.traits.filter(
        trait => trait.code === Game_BattlerBase.TRAIT_SPARAM && trait.dataId === id
    );
    return traits.reduce((r, trait) => r + trait.value, 0);
};

EquipSocketManager.getAutoStates = function (equip) {
    let result = [];
    if (equip.socketItems) {
        for (let i = 0; i < equip.socketItems.length; i++) {
            let id = equip.socketItems[i];
            if (id) {
                let item = $dataItems[id];
                if (item.socketAutoStates) result = result.concat(item.socketAutoStates);
            }
        }
    }
    return result.unique();
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// String
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
String.prototype.toTitleCase = function () {
    return this.replace(/([^\W_]+[^\s-]*) */g, function (txt) {
        return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
    });
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Bitmap
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Bitmap_initialize = Bitmap.prototype.initialize;
Bitmap.prototype.initialize = function (width, height) {
    Dhoom.EquipmentSockets.Bitmap_initialize.call(this, width, height);
    this.spacing = 0;
};

Bitmap.prototype.changeTextStyle = function (style) {
    this.fontFace = style.name.length > 0 ? style.name : 'sans-serif';
    this.fontSize = style.size;
    this.textColor = style.color;
    this.outlineWidth = style.outlineWidth;
    this.outlineColor = style.outlineColor;
    this.fontBold = style.bold;
    this.fontItalic = style.italic;
    this.fontShadowBlur = style.shadowBlur;
    this.fontShadowColor = style.shadowColor;
    this.spacing = (style.spacing || 0) + 'px';
    this.fontCase = style.case ? style.case.toLowerCase() : '';
};

Dhoom.EquipmentSockets.Bitmap_drawText = Bitmap.prototype.drawText;
Bitmap.prototype.drawText = function (text, x, y, maxWidth, lineHeight, align) {
    if (this.fontItalic) maxWidth -= 4;
    this._context.letterSpacing = this.spacing;
    Dhoom.EquipmentSockets.Bitmap_drawText.call(this, text, x, y, maxWidth, lineHeight, align);
};

Dhoom.EquipmentSockets.Bitmap_drawTextOutline = Bitmap.prototype._drawTextOutline;
Bitmap.prototype._drawTextOutline = function (text, tx, ty, maxWidth) {
    if (this.outlineWidth === 0) return;
    if (this.fontShadowBlur) {
        let context = this._context;
        context.shadowBlur = this.fontShadowBlur;
        context.shadowColor = this.fontShadowColor;
    }
    Dhoom.EquipmentSockets.Bitmap_drawTextOutline.call(this, text, tx, ty, maxWidth);
};

Dhoom.EquipmentSockets.Bitmap_drawTextBody = Bitmap.prototype._drawTextBody;
Bitmap.prototype._drawTextBody = function (text, tx, ty, maxWidth) {
    if (this.outlineWidth === 0 && this.fontShadowBlur) {
        let context = this._context;
        context.shadowBlur = this.fontShadowBlur;
        context.shadowColor = this.fontShadowColor;
    }
    Dhoom.EquipmentSockets.Bitmap_drawTextBody.call(this, text, tx, ty, maxWidth);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Actor
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Game_Actor_onBattleStart = Game_Actor.prototype.onBattleStart;
Game_Actor.prototype.onBattleStart = function (advantageous) {
    this.applyEquipmentAutoStates();
    Dhoom.EquipmentSockets.Game_Actor_onBattleStart.apply(this, arguments);
};

Game_Actor.prototype.applyEquipmentAutoStates = function () {
    let equips = this.equips();
    let stateIds = [];
    for (let i = 0; i < equips.length; i++) {
        if (equips[i]) {
            stateIds = stateIds.concat(EquipSocketManager.getAutoStates(equips[i]));
        }
    }
    stateIds.unique().forEach(id => {
        this.addState(id);
    }, this);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Game_Party
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Game_Party_isAnyMemberEquipped = Game_Party.prototype.isAnyMemberEquipped;
Game_Party.prototype.isAnyMemberEquipped = function (item) {
    if (EquipSocketManager.isIndependent(item)) {
        item = EquipSocketManager.getEquipContainer(item)[item.socketBaseItem];
    }
    let result = Dhoom.EquipmentSockets.Game_Party_isAnyMemberEquipped.call(this, item);
    if (!result) {
        for (let i = 0; i < this.members().length; i++) {
            let actor = this.members()[i];
            for (let j = 0; j < actor.equips().length; j++) {
                let equip = actor.equips()[j];
                if (EquipSocketManager.isIndependent(equip)) {
                    if (
                        EquipSocketManager.getEquipContainer(equip)[equip.socketBaseItem] === item
                    ) {
                        return true;
                    }
                }
                if (equip === item) {
                    return true;
                }
            }
        }
    }
    return result;
};

if (Imported.VisuMZ_1_ItemsEquipsCore) {
    Dhoom.EquipmentSockets.Game_Party_partyArtifacts = Game_Party.prototype.partyArtifacts;
    Game_Party.prototype.partyArtifacts = function () {
        let armors = this.armors();
        if (
            VisuMZ.ItemsEquipsCore.artifactIDs &&
            VisuMZ.ItemsEquipsCore.artifactIDs.partyArtifactIDs &&
            armors.length < VisuMZ.ItemsEquipsCore.artifactIDs.partyArtifactIDs.length
        ) {
            let result = [];
            for (let i = 0; i < armors.length; i++) {
                let armor = armors[i];
                if (armor && DataManager.isPartyArtifact(armor)) {
                    let num = DataManager.isStackableArtifact(armor) ? this.numItems(armor) : 1;
                    while (num--) {
                        result.push(armor);
                    }
                }
            }
            return result;
        }
        return Dhoom.EquipmentSockets.Game_Party_partyArtifacts.call(this);
    };

    Dhoom.EquipmentSockets.Game_Party_troopArtifacts = Game_Party.prototype.troopArtifacts;
    Game_Party.prototype.troopArtifacts = function () {
        let armors = this.armors();
        if (
            VisuMZ.ItemsEquipsCore.artifactIDs &&
            VisuMZ.ItemsEquipsCore.artifactIDs.troopArtifactIDs &&
            armors.length < VisuMZ.ItemsEquipsCore.artifactIDs.troopArtifactIDs.length
        ) {
            let result = [];
            for (let i = 0; i < armors.length; i++) {
                let armor = armors[i];
                if (armor && DataManager.isTroopArtifact(armor)) {
                    let num = DataManager.isStackableArtifact(armor) ? this.numItems(armor) : 1;
                    while (num--) {
                        result.push(armor);
                    }
                }
            }
            return result;
        }
        return Dhoom.EquipmentSockets.Game_Party_troopArtifacts.call(this);
    };
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Sprite
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Sprite.prototype.setAnchor = function (anchor) {
    this.anchor.x = 0;
    this.anchor.y = 0;
    if ([2, 5, 8].contains(anchor)) this.anchor.x = 0.5;
    if ([3, 6, 9].contains(anchor)) this.anchor.x = 1;
    if ([4, 5, 6].contains(anchor)) this.anchor.y = 0.5;
    if ([1, 2, 3].contains(anchor)) this.anchor.y = 1;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_Base
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Window_Base_processEscapeCharacter =
    Window_Base.prototype.processEscapeCharacter;
Window_Base.prototype.processEscapeCharacter = function (code, textState) {
    Dhoom.EquipmentSockets.Window_Base_processEscapeCharacter.call(this, code, textState);
    if (code === 'IS') {
        this.processDrawSocketIcon(this.obtainEscapeParam(textState), textState);
    }
};

Window_Base.prototype.processDrawSocketIcon = function (iconIndex, textState) {
    if (textState.drawing) {
        if (
            iconIndex !== EquipSocketManager.emptyIconIndex() &&
            Dhoom.EquipmentSockets.drawEmptySlotIconHasItem
        ) {
            this.drawIcon(EquipSocketManager.emptyIconIndex(), textState.x + 2, textState.y + 2);
        }
        this.drawIcon(iconIndex, textState.x + 2, textState.y + 2);
    }
    textState.x += ImageManager.iconWidth + 4;
};

Window_Base.prototype.drawItemName = function (item, x, y, width) {
    if (item) {
        const iconY = y + (this.lineHeight() - ImageManager.iconHeight) / 0x2;
        const iw = ImageManager.iconWidth + 0x4;
        const maxWidth = Math.max(0x0, width - iw);
        this.changeTextColor(ColorManager.getItemColor(item));
        this.drawIcon(item.iconIndex, x, iconY);
        this.drawTextEx(item.name, x + iw, y, maxWidth);
        this.resetTextColor();
    }
};

Window_Base.prototype.sliceText = function (text, width) {
    width = width || this.contents.width;
    let result = [];
    let texts = text.split(' ');
    let t = '';
    let s = null;
    let newLineTags = this.sliceTextNewLineTags();
    while (texts.length) {
        let c = texts.shift();
        if (c.contains('\n')) {
            s = c.split('\n');
            c = s[0];
        }
        if (this.textSizeEx((t + ' ' + c).trim()).width <= width) {
            t = (t + ' ' + c).trim();
        } else if (t) {
            result.push(t);
            t = c;
        }
        if (s) {
            result.push(t);
            t = s[1];
            s = null;
        }
    }
    if (t) result.push(t);
    if (result.length === 0) result.push(text);
    return result;
};

Window_Base.prototype.sliceTextNewLineTags = function () {
    return [];
};

Dhoom.EquipmentSockets.Window_Base_resetFontSettings = Window_Base.prototype.resetFontSettings;
Window_Base.prototype.resetFontSettings = function () {
    if (this._isDrawingCustomText) return;
    Dhoom.EquipmentSockets.Window_Base_resetFontSettings.call(this);
};

Window_Base.prototype.drawCustomText = function (x, y, setting, replaces) {
    this._isDrawingCustomText = true;
    this._customTextSetting = setting;
    this.contents.changeTextStyle(setting.style);
    let text = setting.text;
    text = text.format.apply(text, replaces);
    text = this.convertEscapeCharacters(text);
    let texts;
    if (setting.style.case.toLowerCase() === 'lowercase') text = text.toLowerCase();
    if (setting.style.case.toLowerCase() === 'uppercase') text = text.toUpperCase();
    if (setting.style.case.toLowerCase() === 'titlecase') text = text.toTitleCase();
    if (setting.wordwrap) {
        texts = this.sliceText(text, setting.width);
    } else {
        texts = [text];
    }
    this.contents.changeTextStyle(setting.style);
    let lh = this.contents.fontSize + 2;
    let spacing = setting.spacing;
    let width = setting.width;
    let sy = setting.centerVertical
        ? (setting.height - (texts.length * (lh + spacing) - spacing)) / 2
        : 0;
    for (let i = 0; i < texts.length; i++) {
        this.contents.changeTextStyle(setting.style);
        let cw = this.textSizeEx(texts[i]).width + this.contents.outlineWidth * 2;
        let sx = this.contents.outlineWidth;
        if (setting.style.align === 'center') {
            sx = (width - cw) / 2;
        }
        if (setting.style.align === 'right') {
            sx = width - cw;
        }
        this.contents.changeTextStyle(setting.style);
        this.drawTextEx(texts[i], x + sx + setting.x, y + sy + setting.y);
        sy += lh + spacing;
    }
    this._isDrawingCustomText = false;
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ItemList
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Window_ItemList_drawItemNumber = Window_ItemList.prototype.drawItemNumber;
Window_ItemList.prototype.drawItemNumber = function (item, x, y, width) {
    if (EquipSocketManager.isIndependent(item)) return;
    Dhoom.EquipmentSockets.Window_ItemList_drawItemNumber.call(this, item, x, y, width);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_VisualItemTooltip
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Window_VisualItemTooltip_refresh =
    Window_VisualItemTooltip.prototype.refresh;
Window_VisualItemTooltip.prototype.refresh = function () {
    this._socketRefresh = true;
    Dhoom.EquipmentSockets.Window_VisualItemTooltip_refresh.call(this);
    this._socketRefresh = undefined;
};

Dhoom.EquipmentSockets.Window_VisualItemTooltip_drawText =
    Window_VisualItemTooltip.prototype.drawText;
Window_VisualItemTooltip.prototype.drawText = function (text, x, y, maxWidth, align) {
    if (this._socketRefresh) {
        align = align || 'left';
        if (align != 'left') {
            let width = this.textSizeEx(text).width;
            if (align === 'center') x += Math.round((maxWidth - width) / 2);
            if (align === 'right') x += maxWidth - width;
        }
        this.drawTextEx(text, x, y, maxWidth);
    } else {
        Dhoom.EquipmentSockets.Window_VisualItemTooltip_drawText.apply(this, arguments);
    }
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketHelp
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketHelp extends Window_Help {
    constructor() {
        super(...arguments);
    }

    initialize() {
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
        this.createBackgroundSprite();
    }

    setting() {
        return Dhoom.EquipmentSockets.helpWindowSetting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    lineHeight() {
        return this.setting().lineHeight;
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketEquipList
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketEquipList extends Window_ItemList {
    constructor() {
        super(...arguments);
    }

    initialize() {
        this._memberEquips = [];
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
    }

    setting() {
        return Dhoom.EquipmentSockets.equipWindowSetting;
    }

    includes(item) {
        let result = super.includes(item);
        if (result) return EquipSocketManager.hasSocket(item);
        return false;
    }

    usesVisualItemInventory() {
        return this.setting().visual;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    lineHeight() {
        return this.setting().lineHeight;
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }

    setSlotWindow(wnd) {
        this._slotWindow = wnd;
        this.updateHelp();
    }

    setStatusWindow(wnd) {
        this._statusWindow = wnd;
        this.updateHelp();
    }

    updateHelp() {
        if (this.active) {
            super.updateHelp();
        }
        if (this._slotWindow) this.updateSlotWindow();
        if (this._statusWindow) this.updateStatusWindow();
    }

    updateSlotWindow() {
        this._slotWindow.setItem(this.item());
    }

    updateStatusWindow() {
        this._statusWindow.setItem(this.item());
    }

    isEnabled(item) {
        return true;
    }

    selectItem(item) {
        this.select(this._data.indexOf(item));
    }

    makeItemList() {
        this._data = [];
        this._memberEquips = [];
        $gameParty.members().forEach(actor => {
            actor
                .equips()
                .filter(item => this.includes(item))
                .forEach(item => {
                    this._data.push(item);
                    this._memberEquips.push(actor.actorId());
                }, this);
        }, this);
        this._data = this._data.concat($gameParty.equipItems().filter(item => this.includes(item)));
    }

    drawItem(index) {
        this._drawingItemIndex = index;
        super.drawItem(index);
    }

    drawItemNumber(item, x, y, width) {
        if (this.isActorEquip(this._drawingItemIndex)) {
            let actor = $gameActors.actor(this._memberEquips[this._drawingItemIndex]);
            this.drawText(actor.name(), x, y, width, 'right');
            return;
        }
        if (EquipSocketManager.isIndependent(item)) return;
        super.drawItemNumber(item, x, y, width);
    }

    isActorEquip(index) {
        return this._memberEquips[index];
    }

    cursorDown(wrap) {
        super.cursorDown(wrap);
        this.updateHelp();
    }

    cursorUp(wrap) {
        super.cursorUp(wrap);
        this.updateHelp();
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_ItemSocketsCategory
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_ItemSocketsCategory extends Window_ItemCategory {
    constructor() {
        super(...arguments);
    }

    makeCommandList() {
        for (const command of Dhoom.EquipmentSockets.itemCategories) {
            this.addItemCategory(command);
        }
        this.select(this.index());
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketSlotList
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketSlotList extends Window_ItemList {
    constructor() {
        super(...arguments);
    }

    initialize() {
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
        this._item = null;
    }

    setting() {
        return Dhoom.EquipmentSockets.slotWindowSetting;
    }

    usesVisualItemInventory() {
        return this.setting().visual;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    lineHeight() {
        return this.setting().lineHeight;
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }

    maxItems() {
        return this._item ? this._item.socketItems.length : 0;
    }

    maxCols() {
        return this.usesVisualItemInventory() ? Math.floor(this.innerWidth / this.itemHeight()) : 1;
    }

    colSpacing() {
        return 0;
    }

    rowSpacing() {
        return this.usesVisualItemInventory() ? 0 : super.rowSpacing();
    }

    itemWidth() {
        return this.usesVisualItemInventory() ? this.itemHeight() : super.itemWidth();
    }

    itemHeight() {
        if (this.usesVisualItemInventory()) {
            if (this._visualItemHeight !== undefined) {
                return this._visualItemHeight;
            }
            const height = Math.ceil(Window_ItemList.VISUAL_ITEM_ICON_SIZE / this.lineHeight());
            this._visualItemHeight = Math.round(height * this.lineHeight()) + 8;
            return this._visualItemHeight;
        } else {
            return super.itemHeight();
        }
    }

    setItem(item) {
        if (this._item !== item) {
            this._item = item;
            this.refresh();
        }
    }

    makeItemList() {}

    drawItem(index) {
        if (this._item) {
            if (this.usesVisualItemInventory()) {
                this.drawItemVisualItemInventory(index);
            } else {
                let itemId = this._item.socketItems[index];
                const rect = this.itemLineRect(index);
                const iconY = rect.y + (this.lineHeight() - ImageManager.iconHeight) / 2;
                const textMargin = ImageManager.iconWidth + 4;
                const itemWidth = Math.max(0, rect.width - textMargin);
                if (itemId) {
                    if (Dhoom.EquipmentSockets.drawEmptySlotIconHasItem)
                        this.drawIcon(EquipSocketManager.emptyIconIndex(), rect.x, iconY);
                    this.drawItemName($dataItems[itemId], rect.x, rect.y, rect.width);
                } else {
                    this.resetTextColor();
                    this.drawIcon(EquipSocketManager.emptyIconIndex(), rect.x, iconY);
                    let text = this.setting().emptySlotText;
                    if (text === undefined) text = 'Empty';
                    this.drawText(text, rect.x + textMargin, rect.y, itemWidth);
                }
            }
        }
    }

    drawItemVisualItemInventory(index) {
        let itemId = this._item.socketItems[index];
        const rect = this.itemRectWithPadding(index);
        if (itemId) {
            this.drawIconSize(
                $dataItems[itemId].iconIndex,
                rect.x,
                rect.y,
                rect.width,
                rect.height
            );
        } else {
            this.drawIconSize(
                EquipSocketManager.emptyIconIndex(),
                rect.x,
                rect.y,
                rect.width,
                rect.height
            );
        }
    }

    setItemWindow(wnd) {
        this._itemWindow = wnd;
        this.updateHelp();
    }

    updateHelp() {
        super.updateHelp();
        this._helpWindow.setItem(
            this._item && this._item.socketItems[this.index()]
                ? $dataItems[this._item.socketItems[this.index()]]
                : null
        );
        if (this._itemWindow) this.updateItemWindow();
    }

    updateItemWindow() {
        this._itemWindow.setType(this.type());
        this._itemWindow.setSlotIndex(this.index());
        this._itemWindow.setItem(this._item);
        this._itemWindow.setSlotItem(this.item());
    }

    type() {
        if (this._item) return this._item.socketTypes[this.index()];
        return null;
    }

    drawIconSize(iconIndex, x, y, width, height) {
        const bitmap = ImageManager.loadSystem('IconSet');
        const pw = ImageManager.iconWidth;
        const ph = ImageManager.iconHeight;
        const sx = (iconIndex % 16) * pw;
        const sy = Math.floor(iconIndex / 16) * ph;
        width = width || pw;
        height = height || ph;
        this.contents._context.imageSmoothingEnabled = Window_ItemList.VISUAL_ITEM_ICON_SMOOTHING;
        this.contents.blt(bitmap, sx, sy, pw, ph, x, y, width, height);
        this.contents._context.imageSmoothingEnabled = true;
    }

    item() {
        return this._item && this._item.socketItems[this.index()]
            ? $dataItems[this._item.socketItems[this.index()]]
            : null;
    }

    isEnabled(item) {
        return true;
    }

    cursorDown(wrap) {
        super.cursorDown(wrap);
        this.updateHelp();
    }

    cursorUp(wrap) {
        super.cursorUp(wrap);
        this.updateHelp();
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketItemSocketList
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketItemSocketList extends Window_ItemList {
    constructor() {
        super(...arguments);
    }

    initialize() {
        this._slotIndex = -1;
        this._item = null;
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
        this.createBackgroundSprite();
    }

    setting() {
        return Dhoom.EquipmentSockets.itemWindowSetting;
    }

    usesVisualItemInventory() {
        return this.setting().visual;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    lineHeight() {
        return this.setting().lineHeight;
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }

    includes(item) {
        return EquipSocketManager.isSocketItem(item);
    }

    setType(type) {
        if (this._type !== type) {
            this._type = type;
            this.refresh();
        }
    }

    setItem(item) {
        if (this._item !== item) {
            this._item = item;
            this.updateHelp();
        }
    }

    setSlotIndex(index) {
        if (this._slotIndex !== index) {
            this._slotIndex = index;
            this.updateHelp();
        }
    }

    setSlotItem(item) {
        if (this._slotItem !== item) {
            this._slotItem = item;
            this.refresh();
        }
    }

    makeItemList() {
        this._data = $gameParty.items().filter(item => this.includes(item));
        if (this.includes(null)) {
            this._data.push(null);
        }
    }

    includes(item) {
        if (item && EquipSocketManager.isSocketItem(item))
            return !this._type || item.socketTypes.contains(this._type);
        return false;
    }

    isEnabled(item) {
        if (this._slotItem === item) return false;
        return !!item;
    }

    setStatusWindow(wnd) {
        this._statusWindow = wnd;
        this.updateHelp();
    }

    setSlotWindow(wnd) {
        this._slotWindow = wnd;
    }

    updateHelp() {
        super.updateHelp();
        this.updateStatusWindow();
    }

    updateStatusWindow() {
        if (this._statusWindow) {
            if (this._item && this._slotIndex >= 0 && this.item()) {
                let item = EquipSocketManager.previewInstallSocket(
                    this._item,
                    this._slotIndex,
                    this.item()
                );
                this._statusWindow.setSlotItem(item);
            } else {
                this._statusWindow.setSlotItem(null);
            }
        }
    }

    deselect() {
        super.deselect();
        this.updateStatusWindow();
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketItemStats
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketItemStats extends Window_EquipStatus {
    constructor() {
        super(...arguments);
    }

    initialize() {
        Window_Selectable.prototype.initialize.call(this, this.windowRect());
        this.opacity = this.setting().opacity;
        this._item = null;
        this._slotItem = null;
        this.createBackgroundSprite();
    }

    setting() {
        return Dhoom.EquipmentSockets.statusWindowSetting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }

    refresh() {
        this.contents.clear();
        if (this._item) this.drawParams();
    }

    drawParams() {
        this.resetFontSettings();
        const params = this.setting().params;
        const lineHeight = this.lineHeight();
        const padding = this.itemPadding();
        const baseX = 0;
        const baseY = 0;
        const baseWidth = this.innerWidth;
        const valueFontSize = 22;
        const useIcons = true;

        // Calculate Widths
        let paramNameWidth = Math.max(
            ...params.map(param => this.textWidth(TextManager.param(param)))
        );
        paramNameWidth += padding * 2;
        if (useIcons) paramNameWidth += ImageManager.iconWidth + 4;
        let arrowWidth = 32;
        const totalDivides = this.innerWidth >= 500 ? 3 : 2;
        let paramValueWidth = Math.floor((baseWidth - paramNameWidth - arrowWidth) / totalDivides);
        paramNameWidth = baseWidth - paramValueWidth * totalDivides - arrowWidth;

        // Draw Parameters
        let x = baseX;
        let y = baseY;
        let value = 0;
        let diffValue = 0;
        let alter = 2;
        params.forEach((paramName, paramId) => {
            this.resetFontSettings();
            // Draw Param Name
            this.drawItemDarkRect(x, y, paramNameWidth, lineHeight, alter);
            this.drawParamName(paramId, x, y, paramNameWidth, paramName);
            this.resetFontSettings();
            x += paramNameWidth;

            // Draw Param Before
            this.contents.fontSize = valueFontSize;
            this.drawItemDarkRect(x, y, paramValueWidth, lineHeight, alter);
            this.drawBeforeParamValue(paramId, x, y, paramValueWidth, paramName);
            this.resetFontSettings();
            x += paramValueWidth;

            // Draw Arrow
            this.drawItemDarkRect(x, y, arrowWidth, lineHeight, alter);
            this.drawRightArrow(x, y);
            x += arrowWidth;

            // Draw Param After
            this.contents.fontSize = valueFontSize;
            this.drawItemDarkRect(x, y, paramValueWidth, lineHeight, alter);
            this.drawAfterParamValue(paramId, x, y, paramValueWidth, paramName);
            x += paramValueWidth;

            // Draw Param Change
            if (totalDivides > 2) {
                this.drawItemDarkRect(x, y, paramValueWidth, lineHeight, alter);
                this.drawUpdatedParamValueDiff(paramId, x, y, paramValueWidth, paramName);
            }

            // Prepare Next Parameter
            x = baseX;
            y += lineHeight;
            alter = alter === 2 ? 1 : 2;
        }, this);
    }

    drawParamName(paramId, x, y, width, paramName) {
        this.changeTextColor(ColorManager.systemColor());
        if (Imported.VisuMZ_0_CoreEngine) {
            this.drawParamText(x + this.itemPadding(), y, width, paramName, false);
        } else {
            this.drawText(TextManager.param(paramId), x + this.itemPadding(), y, width);
        }
    }

    drawBeforeParamValue(paramId, x, y, width, paramName) {
        let text = '';
        if (this._item) {
            text = EquipSocketManager.paramValueByName(this._item, paramName, true);
        }
        this.drawText(text, x, y, width - this.itemPadding(), 'right');
    }

    drawAfterParamValue(paramId, x, y, width, paramName) {
        if (this._slotItem) {
            const before = EquipSocketManager.paramValueByName(this._item, paramName, false);
            const after = EquipSocketManager.paramValueByName(this._slotItem, paramName, false);
            const diffValue = after - before;
            this.changeTextColor(ColorManager.paramchangeTextColor(diffValue));
            this.drawText(
                EquipSocketManager.paramValueByName(this._slotItem, paramName, true),
                x,
                y,
                width - this.itemPadding(),
                'right'
            );
        }
    }

    drawUpdatedParamValueDiff(paramId, x, y, width, paramName) {
        if (this._slotItem) {
            const before = EquipSocketManager.paramValueByName(this._item, paramName, false);
            const after = EquipSocketManager.paramValueByName(this._slotItem, paramName, false);
            let percentage = before % 1 !== 0 || after % 1 !== 0;
            const diffValue = after - before;
            let text = diffValue;
            if (percentage) {
                text = Math.round(diffValue * 100) + '%';
            }
            if (diffValue !== 0) {
                this.changeTextColor(ColorManager.paramchangeTextColor(diffValue));
                text = (diffValue > 0 ? '(+%1)' : '(%1)').format(text);
                this.drawText(text, x + this.itemPadding(), y, width, 'left');
            }
        }
    }

    setItem(item) {
        if (this._item !== item) {
            this._item = item;
            this.refresh();
        }
    }

    setSlotItem(item) {
        if (this._slotItem !== item) {
            this._slotItem = item;
            this.refresh();
        }
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_SocketConfirmation
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_SocketConfirmation extends Window_Selectable {
    constructor() {
        super(...arguments);
    }

    initialize() {
        this._text = Dhoom.EquipmentSockets.confirmationTextEmptySocket;
        super.initialize(this.windowRect());
        this.opacity = this.setting().opacity;
        this.createBackgroundSprite();
        this.refresh();
    }

    setting() {
        return Dhoom.EquipmentSockets.confirmationWindowSetting;
    }

    windowRect() {
        return new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
    }

    contentsHeight() {
        return this.innerHeight;
    }

    updatePadding() {
        this.padding = this.setting().padding;
    }

    createBackgroundSprite() {
        let setting = this.setting().background;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChildToBack(this._backgroundSprite);
    }

    itemRect(index) {
        let setting = this.setting().cancel;
        if (index === 0) setting = this.setting().confirm;
        return new Rectangle(setting.x, setting.y, setting.width, setting.height);
    }

    refresh() {
        this.contents.clear();
        this.drawHelpText();
        this.drawConfirmText();
        this.drawCancelText();
    }

    drawHelpText() {
        this.drawCustomText(0, 0, this.setting().helpText, [this._text]);
    }

    drawConfirmText() {
        this.drawItemBackground(0);
        this.drawCustomText(0, 0, this.setting().confirm, []);
    }

    drawCancelText() {
        this.drawItemBackground(1);
        this.drawCustomText(0, 0, this.setting().cancel, []);
    }

    maxItems() {
        return 2;
    }

    cursorLeft() {
        this.select(this.index() === 0 ? 1 : 0);
    }

    cursorRight() {
        this.select(this.index() === 0 ? 1 : 0);
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Window_MenuDisplayText
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Window_MenuDisplayText extends Window_Base {
    constructor() {
        super(...arguments);
    }

    initialize(setting, replaceTexts) {
        this._setting = setting;
        this._replaceTexts = replaceTexts || [];
        let rect = new Rectangle(
            this.setting().x,
            this.setting().y,
            this.setting().width,
            this.setting().height
        );
        super.initialize(rect);
        this.refresh();
        this.opacity = 0;
    }

    setting() {
        return this._setting;
    }

    updatePadding() {
        this.padding = 0;
    }

    lineHeight() {
        return this.contents.fontSize + 4 + this.contents.outlineWidth;
    }

    resetFontSettings() {
        super.resetFontSettings();
        this.contents.changeTextStyle(this.setting().style);
    }

    calcTextHeight(textState, all) {
        this.resetFontSettings();
        return super.calcTextHeight(textState, all);
    }

    refresh() {
        this.contents.clear();
        this.contents.changeTextStyle(this.setting().style);
        this.drawTextContent();
    }

    drawTextContent() {
        let text = this.text();
        let texts = [];
        if (this.setting().wordwrap) {
            texts = this.sliceText(text);
        } else {
            texts = [text];
        }
        let heights = [];
        let totalHeight = 0;
        let spacing = this.setting().spacing;
        for (let i = 0; i < texts.length; i++) {
            let h = this.calcTextHeight({ index: 0, x: 0, y: 0, left: 0, text: texts[i] });
            heights.push(h);
            totalHeight += h + spacing;
        }
        totalHeight -= spacing;
        let y = this.setting().centerVertical ? (this.contents.height - totalHeight) / 2 : 0;
        for (let i = 0; i < texts.length; i++) {
            let cw = this.textSizeEx(texts[i]).width + this.contents.outlineWidth * 2;
            let x = this.contents.outlineWidth;
            if (this.setting().style.align === 'center') {
                x = (this.contents.width - cw) / 2;
            }
            if (this.setting().style.align === 'right') {
                x = this.contents.width - cw;
            }
            this.drawTextEx(texts[i], x, y, this.contents.width);
            y += heights[i];
        }
    }

    text() {
        let result = this.setting().text;
        result = result.format.apply(result, this._replaceTexts);
        result = this.convertEscapeCharacters(result);
        if (this.contents.fontCase === 'lowercase') result = result.toLowerCase();
        if (this.contents.fontCase === 'uppercase') result = result.toUpperCase();
        if (this.contents.fontCase === 'titlecase') result = result.toTitleCase();
        return result;
    }

    setReplaceTexts(replaceTexts) {
        replaceTexts = replaceTexts || [];
        if (!this._replaceTexts.equals(replaceTexts)) {
            this._replaceTexts = replaceTexts;
            if (this.setting().text.match(/%\d+/)) this.refresh();
        }
    }

    normalColor() {
        return this.setting().style.color;
    }

    changeTextColor(color) {
        if (color === ColorManager.textColor(0)) color = this.normalColor();
        super.changeTextColor(color);
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Spriteset_MenuDisplay
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Spriteset_MenuDisplay extends Sprite {
    constructor() {
        super(...arguments);
    }

    initialize(setting, replaceTexts) {
        super.initialize();
        this._setting = setting;
        this._replaceTexts = replaceTexts || [];
        this.refreshBitmap();
        this.createImageSprites();
        this.createTextSprites();
        this.setPosition();
    }

    setting() {
        return this._setting;
    }

    refreshBitmap() {
        this.bitmap = ImageManager.loadSystem(this.setting().background);
    }

    createImageSprites() {
        this._imageSprites = [];
        for (let i = 0; i < this.setting().images.length; i++) {
            let setting = this.setting().images[i];
            let sprite = new Sprite();
            sprite.bitmap = ImageManager.loadSystem(setting.filename);
            sprite.x = setting.x;
            sprite.y = setting.y;
            sprite.scale.x = setting.scaleX / 100;
            sprite.scale.y = setting.scaleY / 100;
            sprite.opacity = setting.opacity;
            sprite.setAnchor(setting.anchor);
            this._imageSprites.push(sprite);
            this.addChild(sprite);
        }
    }

    createTextSprites() {
        this._textSprites = [];
        for (let i = 0; i < this.setting().texts.length; i++) {
            let sprite = new Window_MenuDisplayText(this.setting().texts[i], this._replaceTexts);
            this._textSprites.push(sprite);
            this.addChild(sprite);
        }
    }

    setPosition() {
        if (this.bitmap.isReady()) {
            let result = this.setting().x;
            if ([2, 5, 8].contains(this.setting().anchor)) {
                result -= this.width / 2;
            }
            if ([3, 6, 9].contains(this.setting().anchor)) {
                result -= this.width;
            }
            this.x = Math.round(result);
            result = this.setting().y;
            if ([4, 5, 6].contains(this.setting().anchor)) {
                result -= this.height / 2;
            }
            if ([1, 2, 3].contains(this.setting().anchor)) {
                result -= this.height;
            }
            this.y = Math.round(result);
        } else {
            this._needUpdate = true;
        }
    }

    update() {
        super.update();
        if (this._needUpdate && this.bitmap.isReady()) {
            this._needUpdate = false;
            this.setPosition();
        }
    }

    setReplaceTexts(replaceTexts) {
        replaceTexts = replaceTexts || [];
        if (!this._replaceTexts.equals(replaceTexts)) {
            this._textSprites.forEach(sprite => sprite.setReplaceTexts(replaceTexts));
            this._replaceTexts = replaceTexts;
        }
    }

    refresh() {
        this._textSprites.forEach(sprite => sprite.refresh());
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Scene_EquipSockets
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
class Scene_EquipSockets extends Scene_Item {
    constructor() {
        super(...arguments);
    }

    create() {
        super.create();
        this.createSocketSlotWindow();
        this.createSocketItemWindow();
        this.createItemStatsWindow();
        this.createConfirmationWindow();
    }

    createBackground() {
        super.createBackground();
        this.createBackgroundSprite();
        this.createMenuDisplaySprites();
    }

    createBackgroundSprite() {
        let setting = Dhoom.EquipmentSockets.menuBackground;
        this._backgroundSprite = new Sprite();
        this._backgroundSprite.bitmap = ImageManager.loadSystem(setting.filename);
        this._backgroundSprite.x = setting.x;
        this._backgroundSprite.y = setting.y;
        this._backgroundSprite.opacity = setting.opacity;
        this._backgroundSprite.scale.x = setting.scaleX / 100;
        this._backgroundSprite.scale.y = setting.scaleY / 100;
        this.addChild(this._backgroundSprite);
    }

    createMenuDisplaySprites() {
        this._displaySprites = [];
        var displays = Dhoom.EquipmentSockets.menuDisplaySettings;
        for (var i = 0; i < displays.length; i++) {
            var sprite = new Spriteset_MenuDisplay(displays[i]);
            this.addChild(sprite);
            this._displaySprites.push(sprite);
        }
    }

    createHelpWindow() {
        this._helpWindow = new Window_SocketHelp();
        this.addWindow(this._helpWindow);
    }

    createItemWindow() {
        this._itemWindow = new Window_SocketEquipList();
        this._itemWindow.setHelpWindow(this._helpWindow);
        this._itemWindow.setHandler('ok', this.onItemOk.bind(this));
        this._itemWindow.setHandler('cancel', this.onItemCancel.bind(this));
        this.addWindow(this._itemWindow);
        this._categoryWindow.setItemWindow(this._itemWindow);
        if (!this._categoryWindow.needsSelection()) {
            this._itemWindow.y -= this._categoryWindow.height;
            this._itemWindow.height += this._categoryWindow.height;
            this._itemWindow.createContents();
            this._categoryWindow.update();
            this._categoryWindow.hide();
            this._categoryWindow.deactivate();
            this.onCategoryOk();
        }
        if (this.isUseModernControls()) {
            this.postCreateItemWindowModernControls();
        }
        if (this.allowCreateStatusWindow()) {
            this.createStatusWindow();
        }
    }

    createCategoryWindow() {
        const rect = this.categoryWindowRect();
        this._categoryWindow = new Window_ItemSocketsCategory(rect);
        this._categoryWindow.setHelpWindow(this._helpWindow);
        this._categoryWindow.setHandler('ok', this.onCategoryOk.bind(this));
        this._categoryWindow.setHandler('cancel', this.popScene.bind(this));
        this.addWindow(this._categoryWindow);
        if (this.isUseModernControls()) {
            this.postCreateCategoryWindowItemsEquipsCore();
        }
    }

    createSocketSlotWindow() {
        this._slotWindow = new Window_SocketSlotList();
        this._slotWindow.setHandler('ok', this.onSlotOk.bind(this));
        this._slotWindow.setHandler('cancel', this.onSlotCancel.bind(this));
        this._slotWindow.setHelpWindow(this._helpWindow);
        this.addWindow(this._slotWindow);
        this._itemWindow.setSlotWindow(this._slotWindow);
    }

    createSocketItemWindow() {
        this._socketItemWindow = new Window_SocketItemSocketList();
        this._socketItemWindow.setHandler('ok', this.onSocketItemOk.bind(this));
        this._socketItemWindow.setHandler('cancel', this.onSocketItemCancel.bind(this));
        this._socketItemWindow.setHelpWindow(this._helpWindow);
        this._socketItemWindow.setType(null);
        this.addWindow(this._socketItemWindow);
        this._slotWindow.setItemWindow(this._socketItemWindow);
    }

    createItemStatsWindow() {
        this._itemStatsWindow = new Window_SocketItemStats();
        this.addWindow(this._itemStatsWindow);
        this._itemWindow.setStatusWindow(this._itemStatsWindow);
        this._socketItemWindow.setStatusWindow(this._itemStatsWindow);
    }

    createConfirmationWindow() {
        this._confirmationWindow = new Window_SocketConfirmation();
        this._confirmationWindow.setHandler('ok', this.onConfirmationOk.bind(this));
        this._confirmationWindow.setHandler('cancel', this.onConfirmationCancel.bind(this));
        this._confirmationWindow.hide();
        this.addWindow(this._confirmationWindow);
    }

    onItemOk() {
        this._slotWindow.activate();
        this._slotWindow.select(0);
        this._slotWindow.updateHelp(); // Add this line to refresh the help window
    }

    onSlotOk() {
        this._socketItemWindow.activate();
        this._socketItemWindow.select(0);
        this._socketItemWindow.updateHelp();
    }

    onSlotCancel() {
        this._slotWindow.deselect();
        this._itemWindow.activate();
        this._socketItemWindow.setType(null);
        this._itemWindow.updateHelp(); // Add this line to refresh the help window
    }

    onSocketItemOk() {
        if (this._slotWindow.item()) {
            if (Dhoom.EquipmentSockets.showConfirmationOnFilledSocket) {
                this.showConfirmation();
            } else {
                this.processSocket();
            }
        } else {
            if (Dhoom.EquipmentSockets.showConfirmationOnEmptySocket) {
                this.showConfirmation();
            } else {
                this.processSocket();
            }
        }
    }

    onSocketItemCancel() {
        this._slotWindow.activate();
        this._socketItemWindow.deselect();
        this._slotWindow.updateHelp();
    }

    showConfirmation() {
        this._confirmationWindow._text = this._slotWindow.item()
            ? Dhoom.EquipmentSockets.confirmationTextFilledSocket
            : Dhoom.EquipmentSockets.confirmationTextEmptySocket;
        this._confirmationWindow.refresh();
        this._confirmationWindow.select(0);
        this._confirmationWindow.show();
        this._confirmationWindow.activate();
    }

    onConfirmationOk() {
        if (this._confirmationWindow.index() === 0) {
            this._confirmationWindow.hide();
            this.processSocket();
        } else {
            this.onConfirmationCancel();
        }
    }

    onConfirmationCancel() {
        this._confirmationWindow.hide();
        this._socketItemWindow.activate();
    }

    processSocket() {
        let equip = this._itemWindow.item();
        if (!EquipSocketManager.isIndependent(equip)) {
            let oldEquip = equip;
            equip = EquipSocketManager.createIndependentEquipment(equip);
            $gameParty.gainItem(equip, 1);
            $gameParty.loseItem(this._itemWindow.item(), 1);
            let actorId = this._itemWindow.isActorEquip(this._itemWindow.index());
            if (actorId) {
                let actor = $gameActors.actor(actorId);
                let slotId = actor.equips().indexOf(oldEquip);
                actor.changeEquip(slotId, equip);
                $gameParty.loseItem(this._itemWindow.item(), 1);
            }
            this._itemWindow.refresh();
            this._itemWindow.selectItem(equip);
        }
        $gameParty.loseItem(this._socketItemWindow.item(), 1);
        EquipSocketManager.installSocket(
            equip,
            this._slotWindow.index(),
            this._socketItemWindow.item()
        );
        this._socketItemWindow.deselect();
        this._socketItemWindow.refresh();
        this._itemWindow.refresh();
        this._itemWindow.updateHelp();
        this._itemWindow.reselect();
        this._slotWindow.activate();
        this._slotWindow.refresh();
        this._slotWindow.updateHelp();
        SoundManager.playEquip();
    }
}

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// Scene_Shop
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
Dhoom.EquipmentSockets.Scene_Shop_doSell = Scene_Shop.prototype.doSell;
Scene_Shop.prototype.doSell = function (number) {
    Dhoom.EquipmentSockets.Scene_Shop_doSell.call(this, number);
    if (
        this._item &&
        EquipSocketManager.isIndependent(this._item) &&
        !$gameParty.numItems(this._item)
    )
        EquipSocketManager.deleteIndependentEquipment(this._item);
};

//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
// PluginManager
//vvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvvv
PluginManager.registerCommand('DhoomEquipmentSockets', 'OpenEquipSocketMenu', function () {
    SceneManager.push(Scene_EquipSockets);
});
