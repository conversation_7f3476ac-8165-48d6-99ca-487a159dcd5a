const padrhidden = 0;
const padrshown = 552;

const aliasPadrItem = Scene_Item.prototype.create;
Scene_Item.prototype.create = function () {
    VisuMZ.ItemsEquipsCore.Settings.StatusWindow.Width = padrhidden;
    aliasPadrItem.call(this);
};

const aliasPadrSS = Scene_Shop.prototype.create;
Scene_Shop.prototype.create = function () {
    VisuMZ.ItemsEquipsCore.Settings.StatusWindow.Width = padrshown;
    aliasPadrSS.call(this);
    console.log('In Shop?');
};

const aliasPadrSkill = Scene_Skill.prototype.create;
Scene_Skill.prototype.create = function () {
    VisuMZ.ItemsEquipsCore.Settings.StatusWindow.Width = padrhidden;
    aliasPadrSkill.call(this);
};
