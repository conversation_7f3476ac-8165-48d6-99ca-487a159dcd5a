//=============================================================================
// VisuStella MZ - Multi-Layer HP Gauge
// VisuMZ_4_MultiLayerHpGauge.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_MultiLayerHpGauge = true;

var VisuMZ = VisuMZ || {};
VisuMZ.MultiLayerHpGauge = VisuMZ.MultiLayerHpGauge || {};
VisuMZ.MultiLayerHpGauge.version = 1.0;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.00] [MultiLayerHpGauge]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Multi-Layer_HP_Gauge_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Want to give certain enemies some more significance than others? Like giving
 * them a giant Multi-Layer HP Gauge spread across the top of the screen in a
 * super imposing type of fashion? This plugin will do just that! Multi-Layer
 * HP Gauges can contain upwards of 10 layers while displaying all of their
 * states in a spread out fashion. Your players will know this enemy is a boss
 * that means business.
 *
 * Features include all (but not limited to) the following:
 *
 * * Designate which database enemies will have their HP Gauges put on display
 *   at the top of the screen to indicate their importance.
 * * These HP gauges can have multiple layers of health bars to make for a
 *   better representation of how tanky they are.
 * * Control the colors associated with each HP Gauge layer to allow for better
 *   distinctions on how close the player is to defeating the enemy.
 * * Up to a total of 10 different HP Gauge Layers can be used with different
 *   color settings for each layer.
 * * Adds states to be displayed in wide form in order to display more than
 *   the current style of rotating states.
 * * Lots of extra features with other VisuStella plugins if they are installed
 *   together with this plugin.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Battle Log Position Shift
 *
 * The Battle Log is usually displayed at the top of the screen. This plugin
 * will shift the Battle Log down by a specified amount depending on the number
 * of Multi-Layer HP Gauges are displayed on screen at a time. You can adjust
 * the amount the shift occurs. If you want to disable this, change the shift
 * amount to 0.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_3_StateTooltips
 *
 * If VisuStella MZ's State Tooltips plugin is installed, players can also view
 * state tooltips when hovering the mouse over the respective Multi-Layer HP
 * Gauge sheets.
 *
 * ---
 *
 * VisuMZ_3_VisualGaugeStyles
 *
 * If VisuStella MZ's Visual Gauge Styles plugin is installed, you can apply
 * gauge styles to the Multi-Layer HP Gauges for this plugin.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_1_BattleCore
 *
 * To reduce redundancy, there are options to remove the HP Gauges if an enemy
 * already has a dedicated Multi-Layer HP Gauge shown at the top of the screen.
 * Likewise, the same is done for state icons.
 *
 * If you don't want these UI elements removed, you can disable this change by
 * altering the respective Plugin Parameters.
 *
 * ---
 *
 * VisuMZ's Battle Systems
 *
 * Since the position of the Multi-Layer HP Gauge will most likely overlap with
 * any turn order or action count UI elements at the top of the screen, this
 * plugin provides the option to offset them via how many Multi-Layer HP Gauge
 * rows are present.
 *
 * ---
 *
 * VisuMZ_4_BreakShields
 *
 * As Break Shields can be displayed in part with the state icons, the reduced
 * redundancy Plugin Parameters allow the UI elements to be removed as to not
 * clutter upt he screen too much.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Enemy-Related Notetags ===
 *
 * ---
 *
 * <Show Multi-Layer HP Gauge>
 * <Hide Multi-Layer HP Gauge>
 *
 * - Used for: Enemy Notetags
 * - Determines if the enemy will have the Multi-Layer HP Gauge visible or not
 *   and bypasses the default setting found in the Plugin Parameters.
 * - Keep in mind that using any of the other notetags found below will also
 *   prompt the Multi-Layer HP Gauge to 'Show'. This makes the 'Show' notetag a
 *   bit redundant but it is there for those who want extra clarity in their
 *   note boxes.
 *
 * ---
 *
 * <Multi-Layer HP Gauge Persist>
 * <Multi-Layer HP Gauge Temporal>
 *
 * - Used for: Enemy Notetags
 * - Determines if the Multi-Layer HP Gauge is persistant or temporal and will
 *   bypass the default settings found in the Plugin Parameters.
 * - When 'Persist' is used, the Multi-Layer HP Gauge will stay visible even
 *   after the enemy tied to it has died in combat.
 * - When 'Temporal' is used, the Multi-Layer HP Gauge will vanish after the
 *   enemy tied to it has died in combat, although it will reappear if it is
 *   revived later.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * ---
 *
 * <Multi-Layer HP Gauge Layers: x>
 *
 * - Used for: Enemy Notetags
 * - Sets the total number of layers used for the enemy as 'x' layers.
 * - Replace 'x' with a number representing a number between 1 and 10 as the
 *   total number of layers used.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * ---
 *
 * <Multi-Layer HP Gauge Face: filename, index>
 * <Multi-Layer HP Gauge Graphic: filename, index>
 * <Multi-Layer HP Gauge Face Graphic: filename, index>
 *
 * - Used for: Enemy Notetags
 * - Changes the graphic used by the enemy to this face graphic.
 * - Replace 'filename' with the name of the image file to pick from the game
 *   project's /img/faces/ folder.
 *   - Filenames are case sensitive.
 *   - Leave out the filename extension from the notetag.
 * - Replace 'index' with a number representing the face graphic cell used.
 *   - Index values start at 0.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * ---
 *
 * <Multi-Layer HP Gauge BgColor: color1>
 * <Multi-Layer HP Gauge BG Color: color1>
 * <Multi-Layer HP Gauge Background Color: color1>
 *
 * <Multi-Layer HP Gauge BgColor: color1, color2>
 * <Multi-Layer HP Gauge BG Color: color1, color2>
 * <Multi-Layer HP Gauge Background Color: color1, color2>
 *
 * - Used for: Enemy Notetags
 * - Adjusts the background color(s) used for the enemy graphic.
 * - Replace 'color1' and/or 'color2' with either a number from 0 to 31
 *   representing the text color or in the format of '#rrggbb' to custom pick a
 *   hex color.
 * - If two colors are used, a vertical gradient will form.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * EXAMPLES:
 *
 *   <Multi-Layer HP Gauge BgColor: 2>
 *   <Multi-Layer HP Gauge BgColor: #ff0000>
 *   <Multi-Layer HP Gauge BgColor: 2, 18>
 *   <Multi-Layer HP Gauge BgColor: #ff0000, #000000>
 *
 * ---
 *
 * <Multi-Layer HP Gauge Border Color: color>
 *
 * - Used for: Enemy Notetags
 * - Adjusts the border color used for the enemy graphic.
 * - Replace 'color' with either a number from 0 to 31 representing the text
 *   color or in the format of '#rrggbb' to custom pick a hex color.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * EXAMPLES:
 *
 *   <Multi-Layer HP Gauge Border Color: 2>
 *   <Multi-Layer HP Gauge Border Color: #ff0000>
 *
 * ---
 *
 * <Multi-Layer HP Gauge Border Size: x>
 * <Multi-Layer HP Gauge Border Thick: x>
 * <Multi-Layer HP Gauge Border Thickness: x>
 *
 * - Used for: Enemy Notetags
 * - Determines the thickness of the color section of the border.
 * - Replace 'x' with a number representing how thick the color section of the
 *   border is in pixels.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Also sets the visibility of the Multi-Layer HP Gauge to 'Show'.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * Adjust the general settings for the Multi-Layer HP Gauge.
 *
 * ---
 *
 * Screen
 *
 *   Max Width:
 *   - What is the max screen area that is taken up by Multi-Layer HP Gauges?
 *
 *   Gauges Per Row:
 *   - How many gauges are displayed per row?
 *   - When the quantity exceeds this number, start a new row.
 *
 *   Row Spacing:
 *   - How many pixels are used inbetween rows to space out the stacked
 *     Multi-Layer HP Gauges?
 *
 *   Mid-Battle Fade Speed:
 *   - How fast should the gauges fade out mid-battle?
 *   - Lower numbers are slower. Higher numbers are faster.
 *
 *   End Battle Fade Speed:
 *   - How fast should the gauges fade out on ending battle?
 *   - Lower numbers are slower. Higher numbers are faster.
 *
 * ---
 *
 * Properties
 *
 *   Buffer X:
 *   - What is the minimum pixel distance between individual parts?
 *
 *   Enable State Tooltips:
 *   - Enables state tooltips when hovered over?
 *   - Requires VisuMZ_3_StateTooltips!
 *
 *   Graphic Size:
 *   - What is the standard pixel size for the enemy graphic?
 *   - This value is also used to adjust individual part positions.
 *
 *   Reposition for Help?:
 *   - Reposition the gauges when the Help Window is open?
 *
 *     Reposition Y:
 *     - How many pixels to offset the gauge reposition?
 *     - Negative: up. Positive: down.
 *
 *   Update Frequency:
 *   - How many frames of wait should there be before updating the individual
 *     Multi-Layer HP Gauges?
 *
 * ---
 *
 * Offset
 *
 *   Offset X:
 *   - How many pixels to offset the whole gauge's X?
 *   - Negative: left. Positive: right.
 *
 *   Offset Y:
 *   - How many pixels to offset the whole gauge's Y?
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * Battle Log
 *
 *   Reposition Window?:
 *   - Repositions the battle log window to make room for the
 *     Multi-Layer HP Gauge?
 *
 *   Per Row Offset Y:
 *   - Offset Battle Log's Y by this amount per row?
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Default Settings
 * ============================================================================
 *
 * These are the default values used for this plugin. These settings can be
 * individually changed via notetags.
 *
 * ---
 *
 * General
 *
 *   Show Gauge?:
 *   - Show Multi-Layer HP Gauges for each enemy by default?
 *
 *   Persistant Gauges?:
 *   - Are Multi-Layer HP Gauges persistant by default?
 *   - Persistant means they remain after the enemy dies.
 *
 *   Default Layers:
 *   - How many layers are used by default when an enemy has a
 *     Multi-Layer HP Gauge in effect?
 *
 * ---
 *
 * Graphic
 *
 *   Background Color 1:
 *   Background Color 2:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Border Color:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 *   Border Thickness:
 *   - What is the thickness of the colored band for the enemy
 *     graphic's border?
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Enemy Graphic Settings
 * ============================================================================
 *
 * Adjust the settings for the Enemy Graphic part of the Multi-Layer HP Gauge.
 *
 * ---
 *
 * General
 *
 *   Show Enemy Graphic?:
 *   - Show the "Graphic" part of the Multi-Layer HP Gauge?
 *   - This displays the enemy graphic.
 *
 *   Show Enemy Letter?:
 *   - Show the enemy's letter on the graphic?
 *
 *   Font Name:
 *   - The font name used for the text of the Letter.
 *   - Leave empty to use the default game's font.
 *
 *   Font Size:
 *   - The font size used for the text of the Letter.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Gauge Settings
 * ============================================================================
 *
 * Adjust the settings for the HP Gauge part of the Multi-Layer HP Gauge.
 *
 * ---
 *
 * General
 *
 *   Show Gauge?:
 *   - Show the "Gauge" part of the Multi-Layer HP Gauge?
 *   - I mean, why wouldn't you?
 *   - That's why you got this plugin.
 *
 *   Gauge Height:
 *   - What is the height of the gauge in pixels?
 *   - Minimum: 1. Maximum: 32.
 *
 *   Style Name:
 *   - Select the gauge style to use for the gauge.
 *   - Requires VisuMZ_3_VisualGaugeStyles!
 *
 * ---
 *
 * Vocabulary
 *
 *   Value Format:
 *   - Text format used for the gauge value text.
 *   - %1 - Current Value, %2 - Max Value, %3 - Percentage
 *
 *   Decimal Places:
 *   - How many decimal places should the percent digits go if they're used
 *     for the value?
 *
 * ---
 *
 * Offset
 *
 *   Offset X:
 *   - How many pixels to offset the gauge part's X?
 *   - Negative: left. Positive: right.
 *
 *   Offset Y:
 *   - How many pixels to offset the gauge part's Y?
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Layer Color Settings
 * ============================================================================
 *
 * Adjust what colors are used for each gauge layer.
 *
 * Layer 1 uses default HP Gauge Colors.
 *
 * ---
 *
 * Layer 2-10 Sets
 *
 *   Color 1:
 *   Color 2:
 *   - Use #rrggbb for custom colors or regular numbers for text colors from
 *     the Window Skin.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: States Settings
 * ============================================================================
 *
 * Adjust the settings for the states part of the Multi-Layer HP Gauge.
 *
 * ---
 *
 * General
 *
 *   Show States?:
 *   - Show the "States" part of the Multi-Layer HP Gauge?
 *   - If off, hides all states, buffs, and Break Shields.
 *
 *   Show Break Shields?:
 *   - Add Break Shields to the list of visible objects?
 *   - Requires VisuMZ_4_BreakShields!
 *
 * ---
 *
 * Offset
 *
 *   Offset X:
 *   - How many pixels to offset the states part's X?
 *   - Negative: left. Positive: right.
 *
 *   Offset Y:
 *   - How many pixels to offset the states part's Y?
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Compatibility Settings
 * ============================================================================
 *
 * Adjust compatibility settings with other plugins.
 *
 * ---
 *
 * Battler-Related > Reduced Redundancy
 *
 *   Break Shields:
 *   - Removes enemy battler Break Shields if redundant.
 *   - Requires VisuMZ_4_BreakShields!
 *
 *   HP Gauge:
 *   - Removes enemy battler HP Gauges if redundant.
 *   - Requires VisuMZ_1_BattleCore!
 *
 *   State Icons:
 *   - Removes enemy battler state icons if redundant.
 *
 * ---
 *
 * Battle Data Offset > Battle Systems
 *
 *   Each Row Offset Y:
 *   - Offset Y position by this for each row.
 *   - Negative: up. Positive: down.
 *
 *   Closed Help Offset Y:
 *   - Offset Y position when help window is closed.
 *   - Negative: up. Positive: down.
 *
 *   Open Help Offset Y:
 *   - Offset Y position when help window is open.
 *   - Negative: up. Positive: down.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Olivia
 * * Irina
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.00 Official Release Date: April 7, 2023
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MultiLayerHpGauge
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc Adjust the general settings for the Multi-Layer HP Gauge.
 * @default {"Screen":"","maxWidth:num":"816","perRow:num":"4","rowSpacing:num":"4","endBattleFadeSpeed:num":"24","Properties":"","bufferX:num":"4","stateTooltipsEnable:eval":"true","faceSize:num":"64","midFadeSpeed:num":"16","repositionForHelp:eval":"true","repositionHelpY:num":"+108","checkFrequency:num":"20","Offset":"","offsetX:num":"+0","offsetY:num":"+0","Window_BattleLog":"","repositionBattleLog:eval":"true","battleLogPerRowOffsetY:num":"+64"}
 *
 * @param Defaults:struct
 * @text Default Settings
 * @type struct<Defaults>
 * @desc These are the default values used for this plugin.
 * These settings can be individually changed via notetags.
 * @default {"General":"","showDefault:eval":"false","persist:eval":"true","defaultLayers:num":"1","Graphic":"","bgColor1:str":"19","bgColor2:str":"18","borderColor:str":"2","borderthickness:num":"2"}
 *
 * @param Parts
 * @text Multi-Layer HP Gauge Parts
 *
 * @param Graphic:struct
 * @text Enemy Graphic Settings
 * @parent Parts
 * @type struct<Graphic>
 * @desc Adjust the settings for the Enemy Graphic part of the
 * Multi-Layer HP Gauge.
 * @default {"show:eval":"true","drawLetter:eval":"true","letterFontName:str":"","letterFontSize:num":"16"}
 *
 * @param Gauge:struct
 * @text Gauge Settings
 * @parent Parts
 * @type struct<Gauge>
 * @desc Adjust the settings for the HP Gauge part of the
 * Multi-Layer HP Gauge.
 * @default {"General":"","show:eval":"true","gaugeHeight:num":"24","styleName:str":"Lean","Vocab":"","valueFmt:str":"%3%","valuePercentDigits:num":"2","Offset":"","offsetX:num":"+0","offsetY:num":"+4"}
 *
 * @param LayerColors:struct
 * @text Layer Color Settings
 * @parent Gauge:struct
 * @type struct<LayerColors>
 * @desc Adjust what colors are used for each gauge layer.
 * Layer 1 uses default HP Gauge Colors.
 * @default {"Layer2":"","layer2_color1:str":"#fff200","layer2_color2:str":"#fff799","Layer3":"","layer3_color1:str":"#39b54a","layer3_color2:str":"#7cc576","Layer4":"","layer4_color1:str":"#00a99d","layer4_color2:str":"#7accc8","Layer5":"","layer5_color1:str":"#00aeef","layer5_color2:str":"#6dcff6","Layer6":"","layer6_color1:str":"#0054a6","layer6_color2:str":"#8393ca","Layer7":"","layer7_color1:str":"#2e3192","layer7_color2:str":"#605ca8","Layer8":"","layer8_color1:str":"#662d91","layer8_color2:str":"#a186be","Layer9":"","layer9_color1:str":"#f06eaa","layer9_color2:str":"#ffdeec","Layer10":"","layer10_color1:str":"#ed1c24","layer10_color2:str":"#f26c4f"}
 *
 * @param States:struct
 * @text States Settings
 * @parent Parts
 * @type struct<States>
 * @desc Adjust the settings for the states part of the
 * Multi-Layer HP Gauge.
 * @default {"General":"","show:eval":"true","breakShields:eval":"true","Offset":"","offsetX:num":"+0","offsetY:num":"+28"}
 *
 * @param Compatibility:struct
 * @text Compatibility Settings
 * @type struct<Compatibility>
 * @desc Adjust compatibility settings with other plugins.
 * @default {"Battler":"","ReduceRed":"","reduceRedundantBreakShield:eval":"true","reduceRedundantHpGauge:eval":"true","reduceRedundantStateIcon:eval":"true","GaugeOffset":"","BattleSysAtb":"","atbEachRowOffsetY:num":"+64","atbNormalOffsetY:num":"+24","atbHelpOffsetY:num":"+12","BattleSysBtb":"","btbEachRowOffsetY:num":"+64","btbNormalOffsetY:num":"+0","btbHelpOffsetY:num":"+12","BattleSysCtb":"","ctbEachRowOffsetY:num":"+64","ctbNormalOffsetY:num":"+0","ctbHelpOffsetY:num":"+12","BattleSysEtb":"","etbEachRowOffsetY:num":"+64","etbNormalOffsetY:num":"+0","etbHelpOffsetY:num":"-56","BattleSysFtb":"","ftbEachRowOffsetY:num":"+64","ftbNormalOffsetY:num":"+0","ftbHelpOffsetY:num":"-56","BattleSysOtb":"","otbEachRowOffsetY:num":"+64","otbNormalOffsetY:num":"-6","otbHelpOffsetY:num":"-12","BattleSysPtb":"","ptbEachRowOffsetY:num":"+64","ptbNormalOffsetY:num":"+0","ptbHelpOffsetY:num":"-56","BattleSysStb":"","stbEachRowOffsetY:num":"+64","stbNormalOffsetY:num":"+0","stbHelpOffsetY:num":"+12"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param Screen
 *
 * @param maxWidth:num
 * @text Max Width
 * @parent Screen
 * @min 1
 * @desc What is the max screen area that is taken up by Multi-Layer HP Gauges?
 * @default 816
 *
 * @param perRow:num
 * @text Gauges Per Row
 * @parent Screen
 * @min 1
 * @desc How many gauges are displayed per row?
 * When the quantity exceeds this number, start a new row.
 * @default 4
 *
 * @param rowSpacing:num
 * @text Row Spacing
 * @parent Screen
 * @min 0
 * @desc How many pixels are used inbetween rows to space out
 * the stacked Multi-Layer HP Gauges?
 * @default 4
 *
 * @param midFadeSpeed:num
 * @text Mid-Battle Fade Speed
 * @parent Screen
 * @min 1
 * @desc How fast should the gauges fade out mid-battle?
 * Lower numbers are slower. Higher numbers are faster.
 * @default 16
 *
 * @param endBattleFadeSpeed:num
 * @text End Battle Fade Speed
 * @parent Screen
 * @min 1
 * @desc How fast should the gauges fade out on ending battle?
 * Lower numbers are slower. Higher numbers are faster.
 * @default 24
 *
 * @param Properties
 *
 * @param bufferX:num
 * @text Buffer X
 * @parent Properties
 * @min 0
 * @desc What is the minimum pixel distance between individual parts?
 * @default 4
 *
 * @param stateTooltipsEnable:eval
 * @text Enable State Tooltips
 * @parent Properties
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enables state tooltips when hovered over?
 * Requires VisuMZ_3_StateTooltips!
 * @default true
 *
 * @param faceSize:num
 * @text Graphic Size
 * @parent Properties
 * @min 1
 * @desc What is the standard pixel size for the enemy graphic?
 * This value is also used to adjust individual part positions.
 * @default 64
 *
 * @param repositionForHelp:eval
 * @text Reposition for Help?
 * @parent Properties
 * @type boolean
 * @on Reposition
 * @off Stay
 * @desc Reposition the gauges when the Help Window is open?
 * @default true
 *
 * @param repositionHelpY:num
 * @text Reposition Y
 * @parent repositionForHelp:eval
 * @desc How many pixels to offset the gauge reposition?
 * Negative: up. Positive: down.
 * @default +108
 *
 * @param checkFrequency:num
 * @text Update Frequency
 * @parent Properties
 * @min 1
 * @desc How many frames of wait should there be before updating
 * the individual Multi-Layer HP Gauges?
 * @default 20
 *
 * @param Offset
 *
 * @param offsetX:num
 * @text Offset X
 * @parent Offset
 * @desc How many pixels to offset the whole gauge's X?
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param offsetY:num
 * @text Offset Y
 * @parent Offset
 * @desc How many pixels to offset the whole gauge's Y?
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param Window_BattleLog
 * @text Battle Log
 *
 * @param repositionBattleLog:eval
 * @text Reposition Window?
 * @parent Window_BattleLog
 * @type boolean
 * @on Reposition
 * @off Keep As Is
 * @desc Repositions the battle log window to make room for
 * the Multi-Layer HP Gauge?
 * @default true
 *
 * @param battleLogPerRowOffsetY:num
 * @text Per Row Offset Y
 * @parent Window_BattleLog
 * @desc Offset Battle Log's Y by this amount per row?
 * Negative: up. Positive: down.
 * @default +64
 *
 */
/* ----------------------------------------------------------------------------
 * Defaults Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Defaults:
 *
 * @param General
 *
 * @param showDefault:eval
 * @text Show Gauge?
 * @parent General
 * @type boolean
 * @on Show
 * @off Don't Show
 * @desc Show Multi-Layer HP Gauges for each enemy by default?
 * @default false
 *
 * @param persist:eval
 * @text Persistant Gauges?
 * @parent General
 * @type boolean
 * @on Show
 * @off Don't Show
 * @desc Are Multi-Layer HP Gauges persistant by default?
 * Persistant means they remain after the enemy dies.
 * @default true
 *
 * @param defaultLayers:num
 * @text Default Layers
 * @parent General
 * @type number
 * @min 1
 * @max 10
 * @desc How many layers are used by default when an enemy has
 * a Multi-Layer HP Gauge in effect?
 * @default 1
 *
 * @param Graphic
 *
 * @param bgColor1:str
 * @text Background Color 1
 * @parent Graphic
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 19
 *
 * @param bgColor2:str
 * @text Background Color 2
 * @parent Graphic
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 18
 *
 * @param borderColor:str
 * @text Border Color
 * @parent Graphic
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default 2
 *
 * @param borderthickness:num
 * @text Border Thickness
 * @parent Graphic
 * @type number
 * @min 1
 * @desc What is the thickness of the colored band for the enemy
 * graphic's border?
 * @default 2
 *
 */
/* ----------------------------------------------------------------------------
 * Graphic Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Graphic:
 *
 * @param show:eval
 * @text Show Enemy Graphic?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the "Graphic" part of the Multi-Layer HP Gauge?
 * This displays the enemy graphic.
 * @default true
 *
 * @param drawLetter:eval
 * @text Show Enemy Letter?
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the enemy's letter on the graphic?
 * @default true
 *
 * @param letterFontName:str
 * @text Font Name
 * @parent drawLetter:eval
 * @desc The font name used for the text of the Letter.
 * Leave empty to use the default game's font.
 * @default
 *
 * @param letterFontSize:num
 * @text Font Size
 * @parent drawLetter:eval
 * @min 1
 * @desc The font size used for the text of the Letter.
 * @default 16
 *
 */
/* ----------------------------------------------------------------------------
 * Gauge Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Gauge:
 *
 * @param General
 *
 * @param show:eval
 * @text Show Gauge?
 * @parent General
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the "Gauge" part of the Multi-Layer HP Gauge?
 * I mean, why wouldn't you? That's why you got this plugin.
 * @default true
 *
 * @param gaugeHeight:num
 * @text Gauge Height
 * @parent General
 * @type number
 * @min 1
 * @max 32
 * @desc What is the height of the gauge in pixels?
 * Minimum: 1. Maximum: 32.
 * @default 24
 *
 * @param styleName:str
 * @text Style Name
 * @parent General
 * @type select
 * @option -
 * @option Normal
 * @option -
 * @option Arrow
 * @option Dipper
 * @option Flag
 * @option Growth
 * @option Lean
 * @option Quad
 * @option Stagger
 * @option Trapezoid
 * @option -
 * @option HalfStep
 * @option ThirdStep
 * @option FourthStep
 * @option FifthStep
 * @option SixthStep
 * @option EighthStep
 * @option TenthStep
 * @option -
 * @option HalfSection
 * @option ThirdSection
 * @option FourthSection
 * @option FifthSection
 * @option SixthSection
 * @option EighthSection
 * @option TenthSection
 * @option -
 * @option SegmentBy10
 * @option SegmentBy20
 * @option SegmentBy25
 * @option SegmentBy50
 * @option SegmentBy100
 * @option SegmentBy200
 * @option SegmentBy250
 * @option SegmentBy500
 * @option SegmentBy1000
 * @option -
 * @desc Select the gauge style to use for the gauge.
 * Requires VisuMZ_3_VisualGaugeStyles!
 * @default Lean
 *
 * @param Vocab
 * @text Vocabulary
 *
 * @param valueFmt:str
 * @text Value Format
 * @parent Vocab
 * @desc Text format used for the gauge value text.
 * %1 - Current Value, %2 - Max Value, %3 - Percentage
 * @default %3%
 *
 * @param valuePercentDigits:num
 * @text Decimal Places
 * @parent Vocab
 * @type number
 * @desc How many decimal places should the percent digits
 * go if they're used for the value?
 * @default 2
 *
 * @param Offset
 *
 * @param offsetX:num
 * @text Offset X
 * @parent Offset
 * @desc How many pixels to offset the gauge part's X?
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param offsetY:num
 * @text Offset Y
 * @parent Offset
 * @desc How many pixels to offset the gauge part's Y?
 * Negative: up. Positive: down.
 * @default +4
 *
 */
/* ----------------------------------------------------------------------------
 * States Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~States:
 *
 * @param General
 *
 * @param show:eval
 * @text Show States?
 * @parent General
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the "States" part of the Multi-Layer HP Gauge?
 * If off, hides all states, buffs, and Break Shields.
 * @default true
 *
 * @param breakShields:eval
 * @text Show Break Shields?
 * @parent General
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Add Break Shields to the list of visible objects?
 * Requires VisuMZ_4_BreakShields!
 * @default true
 *
 * @param Offset
 *
 * @param offsetX:num
 * @text Offset X
 * @parent Offset
 * @desc How many pixels to offset the states part's X?
 * Negative: left. Positive: right.
 * @default +0
 *
 * @param offsetY:num
 * @text Offset Y
 * @parent Offset
 * @desc How many pixels to offset the states part's Y?
 * Negative: up. Positive: down.
 * @default +28
 *
 */
/* ----------------------------------------------------------------------------
 * Layer Colors Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~LayerColors:
 *
 * @param Layer2
 * @text Layer 2 Set
 *
 * @param layer2_color1:str
 * @text Color 1
 * @parent Layer2
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #fff200
 *
 * @param layer2_color2:str
 * @text Color 2
 * @parent Layer2
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #fff799
 *
 * @param Layer3
 * @text Layer 3 Set
 *
 * @param layer3_color1:str
 * @text Color 1
 * @parent Layer3
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #39b54a
 *
 * @param layer3_color2:str
 * @text Color 2
 * @parent Layer3
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #7cc576
 *
 * @param Layer4
 * @text Layer 4 Set
 *
 * @param layer4_color1:str
 * @text Color 1
 * @parent Layer4
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #00a99d
 *
 * @param layer4_color2:str
 * @text Color 2
 * @parent Layer4
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #7accc8
 *
 * @param Layer5
 * @text Layer 5 Set
 *
 * @param layer5_color1:str
 * @text Color 1
 * @parent Layer5
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #00aeef
 *
 * @param layer5_color2:str
 * @text Color 2
 * @parent Layer5
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #6dcff6
 *
 * @param Layer6
 * @text Layer 6 Set
 *
 * @param layer6_color1:str
 * @text Color 1
 * @parent Layer6
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #0054a6
 *
 * @param layer6_color2:str
 * @text Color 2
 * @parent Layer6
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #8393ca
 *
 * @param Layer7
 * @text Layer 7 Set
 *
 * @param layer7_color1:str
 * @text Color 1
 * @parent Layer7
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #2e3192
 *
 * @param layer7_color2:str
 * @text Color 2
 * @parent Layer7
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #605ca8
 *
 * @param Layer8
 * @text Layer 8 Set
 *
 * @param layer8_color1:str
 * @text Color 1
 * @parent Layer8
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #662d91
 *
 * @param layer8_color2:str
 * @text Color 2
 * @parent Layer8
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #a186be
 *
 * @param Layer9
 * @text Layer 9 Set
 *
 * @param layer9_color1:str
 * @text Color 1
 * @parent Layer9
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #f06eaa
 *
 * @param layer9_color2:str
 * @text Color 2
 * @parent Layer9
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #ffdeec
 *
 * @param Layer10
 * @text Layer 10 Set
 *
 * @param layer10_color1:str
 * @text Color 1
 * @parent Layer10
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #ed1c24
 *
 * @param layer10_color2:str
 * @text Color 2
 * @parent Layer10
 * @desc Use #rrggbb for custom colors or regular numbers
 * for text colors from the Window Skin.
 * @default #f26c4f
 *
 */
/* ----------------------------------------------------------------------------
 * Compatibility Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Compatibility:
 *
 * @param Battler
 * @text Battler-Related
 *
 * @param ReduceRed
 * @text Reduced Redundancy
 * @parent Battler
 *
 * @param reduceRedundantBreakShield:eval
 * @text Break Shields
 * @parent ReduceRed
 * @type boolean
 * @on Reduce
 * @off Keep
 * @desc Removes enemy battler Break Shields if redundant.
 * Requires VisuMZ_4_BreakShields!
 * @default true
 *
 * @param reduceRedundantHpGauge:eval
 * @text HP Gauge
 * @parent ReduceRed
 * @type boolean
 * @on Reduce
 * @off Keep
 * @desc Removes enemy battler HP Gauges if redundant.
 * Requires VisuMZ_1_BattleCore!
 * @default true
 *
 * @param reduceRedundantStateIcon:eval
 * @text State Icons
 * @parent ReduceRed
 * @type boolean
 * @on Reduce
 * @off Keep
 * @desc Removes enemy battler state icons if redundant.
 * @default true
 *
 * @param BattleDataOffset
 * @text Battle Data Offset
 *
 * @param BattleSysAtb
 * @text Battle System - ATB
 * @parent BattleDataOffset
 *
 * @param atbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysAtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param atbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysAtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +24
 *
 * @param atbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysAtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default +12
 *
 * @param BattleSysBtb
 * @text Battle System - BTB
 * @parent GaugeOffset
 *
 * @param btbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysBtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param btbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysBtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param btbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysBtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default +12
 *
 * @param BattleSysCtb
 * @text Battle System - CTB
 * @parent GaugeOffset
 *
 * @param ctbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysCtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param ctbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysCtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param ctbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysCtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default +12
 *
 * @param BattleSysEtb
 * @text Battle System - ETB
 * @parent GaugeOffset
 *
 * @param etbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysEtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param etbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysEtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param etbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysEtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default -56
 *
 * @param BattleSysFtb
 * @text Battle System - FTB
 * @parent GaugeOffset
 *
 * @param ftbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysFtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param ftbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysFtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param ftbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysFtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default -56
 *
 * @param BattleSysOtb
 * @text Battle System - OTB
 * @parent GaugeOffset
 *
 * @param otbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysOtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param otbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysOtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default -6
 *
 * @param otbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysOtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default -12
 *
 * @param BattleSysPtb
 * @text Battle System - PTB
 * @parent GaugeOffset
 *
 * @param ptbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysPtb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param ptbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysPtb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param ptbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysPtb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default -56
 *
 * @param BattleSysStb
 * @text Battle System - STB
 * @parent GaugeOffset
 *
 * @param stbEachRowOffsetY:num
 * @text Each Row Offset Y
 * @parent BattleSysStb
 * @desc Offset Y position by this for each row.
 * Negative: up. Positive: down.
 * @default +64
 *
 * @param stbNormalOffsetY:num
 * @text Closed Help Offset Y
 * @parent BattleSysStb
 * @desc Offset Y position when help window is closed.
 * Negative: up. Positive: down.
 * @default +0
 *
 * @param stbHelpOffsetY:num
 * @text Open Help Offset Y
 * @parent BattleSysStb
 * @desc Offset Y position when help window is open.
 * Negative: up. Positive: down.
 * @default +12
 *
 */
//=============================================================================

const _0x2a1ad3 = _0x1480;
(function (_0xf111df, _0x394e84) {
    const _0x5291cc = _0x1480,
        _0x5d9b97 = _0xf111df();
    while (!![]) {
        try {
            const _0x481b16 =
                parseInt(_0x5291cc(0x2ab)) / 0x1 +
                (-parseInt(_0x5291cc(0x31a)) / 0x2) * (parseInt(_0x5291cc(0x332)) / 0x3) +
                parseInt(_0x5291cc(0x373)) / 0x4 +
                -parseInt(_0x5291cc(0x254)) / 0x5 +
                -parseInt(_0x5291cc(0x374)) / 0x6 +
                (parseInt(_0x5291cc(0x274)) / 0x7) * (parseInt(_0x5291cc(0x1e5)) / 0x8) +
                (parseInt(_0x5291cc(0x362)) / 0x9) * (parseInt(_0x5291cc(0x1d9)) / 0xa);
            if (_0x481b16 === _0x394e84) break;
            else _0x5d9b97['push'](_0x5d9b97['shift']());
        } catch (_0x26e4f6) {
            _0x5d9b97['push'](_0x5d9b97['shift']());
        }
    }
})(_0x5f02, 0x23cc9);
var label = _0x2a1ad3(0x28e),
    tier = tier || 0x0,
    dependencies = [],
    pluginData = $plugins[_0x2a1ad3(0x2cb)](function (_0x4a6c4e) {
        const _0xc5a55d = _0x2a1ad3;
        return (
            _0x4a6c4e[_0xc5a55d(0x2f4)] &&
            _0x4a6c4e['description'][_0xc5a55d(0x350)]('[' + label + ']')
        );
    })[0x0];
((VisuMZ[label][_0x2a1ad3(0x326)] = VisuMZ[label][_0x2a1ad3(0x326)] || {}),
    (VisuMZ[_0x2a1ad3(0x33f)] = function (_0x5f1d6b, _0x169654) {
        const _0x21152f = _0x2a1ad3;
        for (const _0x55c92b in _0x169654) {
            if (_0x55c92b['match'](/(.*):(.*)/i)) {
                const _0x6a3c8a = String(RegExp['$1']),
                    _0x2199c1 = String(RegExp['$2'])[_0x21152f(0x372)]()['trim']();
                let _0x516bce, _0x521596, _0x1fe92b;
                switch (_0x2199c1) {
                    case 'NUM':
                        _0x516bce =
                            _0x169654[_0x55c92b] !== '' ? Number(_0x169654[_0x55c92b]) : 0x0;
                        break;
                    case _0x21152f(0x336):
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== ''
                                ? JSON[_0x21152f(0x235)](_0x169654[_0x55c92b])
                                : []),
                            (_0x516bce = _0x521596['map'](_0x3cef33 => Number(_0x3cef33))));
                        break;
                    case _0x21152f(0x223):
                        _0x516bce = _0x169654[_0x55c92b] !== '' ? eval(_0x169654[_0x55c92b]) : null;
                        break;
                    case 'ARRAYEVAL':
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== '' ? JSON['parse'](_0x169654[_0x55c92b]) : []),
                            (_0x516bce = _0x521596[_0x21152f(0x1e0)](_0x339bc0 =>
                                eval(_0x339bc0)
                            )));
                        break;
                    case _0x21152f(0x34f):
                        _0x516bce =
                            _0x169654[_0x55c92b] !== ''
                                ? JSON[_0x21152f(0x235)](_0x169654[_0x55c92b])
                                : '';
                        break;
                    case _0x21152f(0x37d):
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== ''
                                ? JSON[_0x21152f(0x235)](_0x169654[_0x55c92b])
                                : []),
                            (_0x516bce = _0x521596[_0x21152f(0x1e0)](_0x21252b =>
                                JSON[_0x21152f(0x235)](_0x21252b)
                            )));
                        break;
                    case _0x21152f(0x314):
                        _0x516bce =
                            _0x169654[_0x55c92b] !== ''
                                ? new Function(JSON['parse'](_0x169654[_0x55c92b]))
                                : new Function(_0x21152f(0x376));
                        break;
                    case 'ARRAYFUNC':
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== '' ? JSON['parse'](_0x169654[_0x55c92b]) : []),
                            (_0x516bce = _0x521596[_0x21152f(0x1e0)](
                                _0x5f02e4 => new Function(JSON[_0x21152f(0x235)](_0x5f02e4))
                            )));
                        break;
                    case 'STR':
                        _0x516bce = _0x169654[_0x55c92b] !== '' ? String(_0x169654[_0x55c92b]) : '';
                        break;
                    case _0x21152f(0x322):
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== ''
                                ? JSON[_0x21152f(0x235)](_0x169654[_0x55c92b])
                                : []),
                            (_0x516bce = _0x521596[_0x21152f(0x1e0)](_0x1d8a3f =>
                                String(_0x1d8a3f)
                            )));
                        break;
                    case _0x21152f(0x299):
                        ((_0x1fe92b =
                            _0x169654[_0x55c92b] !== '' ? JSON['parse'](_0x169654[_0x55c92b]) : {}),
                            (_0x516bce = VisuMZ['ConvertParams']({}, _0x1fe92b)));
                        break;
                    case 'ARRAYSTRUCT':
                        ((_0x521596 =
                            _0x169654[_0x55c92b] !== ''
                                ? JSON[_0x21152f(0x235)](_0x169654[_0x55c92b])
                                : []),
                            (_0x516bce = _0x521596[_0x21152f(0x1e0)](_0xb5e9b =>
                                VisuMZ[_0x21152f(0x33f)]({}, JSON[_0x21152f(0x235)](_0xb5e9b))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x5f1d6b[_0x6a3c8a] = _0x516bce;
            }
        }
        return _0x5f1d6b;
    }),
    (_0x3b0254 => {
        const _0x551d80 = _0x2a1ad3,
            _0x144fd0 = _0x3b0254[_0x551d80(0x231)];
        for (const _0x5b8b86 of dependencies) {
            if (!Imported[_0x5b8b86]) {
                (alert(_0x551d80(0x1f1)[_0x551d80(0x226)](_0x144fd0, _0x5b8b86)),
                    SceneManager['exit']());
                break;
            }
        }
        const _0x1c80ee = _0x3b0254['description'];
        if (_0x1c80ee['match'](/\[Version[ ](.*?)\]/i)) {
            const _0x133a6a = Number(RegExp['$1']);
            _0x133a6a !== VisuMZ[label][_0x551d80(0x307)] &&
                (alert(
                    '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.'[
                        'format'
                    ](_0x144fd0, _0x133a6a)
                ),
                SceneManager[_0x551d80(0x2a2)]());
        }
        if (_0x1c80ee[_0x551d80(0x276)](/\[Tier[ ](\d+)\]/i)) {
            const _0x127d61 = Number(RegExp['$1']);
            _0x127d61 < tier
                ? (alert(_0x551d80(0x1ed)[_0x551d80(0x226)](_0x144fd0, _0x127d61, tier)),
                  SceneManager['exit']())
                : (tier = Math[_0x551d80(0x241)](_0x127d61, tier));
        }
        VisuMZ[_0x551d80(0x33f)](VisuMZ[label][_0x551d80(0x326)], _0x3b0254[_0x551d80(0x20d)]);
    })(pluginData),
    (VisuMZ[_0x2a1ad3(0x28e)]['RegExp'] = {
        showMultiLayerGauge: /<SHOW MULTI(?:|-| )LAYER (?:HP |)GAUGE>/i,
        hideMultiLayerGauge: /<HIDE MULTI(?:|-| )LAYER (?:HP |)GAUGE>/i,
        persistMultiLayerGauge: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE (?:PERSIST|PERSISTANT)>/i,
        temporalMultiLayerGauge: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE (?:TEMP|TEMPORAL|TEMPORARY)>/i,
        layers: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE LAYERS:[ ](\d+)>/i,
        faceGraphic:
            /<MULTI(?:|-| )LAYER (?:HP |)GAUGE (?:FACE|GRAPHIC|FACE GRAPHIC):[ ](.*),[ ]*(\d+)>/i,
        bgColor: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE (?:BG|BG |BACKGROUND )COLOR:[ ](.*)>/i,
        borderColor: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE BORDER COLOR:[ ](.*)>/i,
        borderThick: /<MULTI(?:|-| )LAYER (?:HP |)GAUGE BORDER (?:THICK|THICKNESS|SIZE):[ ](\d+)>/i,
    }),
    (ImageManager[_0x2a1ad3(0x309)] = ImageManager[_0x2a1ad3(0x309)] || 0x9),
    (ImageManager[_0x2a1ad3(0x1ce)] = ImageManager[_0x2a1ad3(0x1ce)] || 0x6),
    (TextManager[_0x2a1ad3(0x290)] = {
        valueFmt:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x1fc)] ??
            _0x2a1ad3(0x246),
        valuePercentDigits:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x23e)] ??
            0x2,
    }),
    (ColorManager['MULTI_LAYER_HP_GAUGE'] = {
        color1: {
            layer2:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x30a)] ??
                _0x2a1ad3(0x28f),
            layer3:
                VisuMZ['MultiLayerHpGauge']['Settings'][_0x2a1ad3(0x2e8)]['layer3_color1'] ??
                _0x2a1ad3(0x1db),
            layer4:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x1f4)] ??
                _0x2a1ad3(0x1d4),
            layer5:
                VisuMZ['MultiLayerHpGauge']['Settings']['LayerColors']['layer5_color1'] ??
                '#00aeef',
            layer6:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)]['layer6_color1'] ??
                '#0054a6',
            layer7:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x2b0)] ??
                _0x2a1ad3(0x1df),
            layer8:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x244)] ??
                '#662d91',
            layer9:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x1d7)] ??
                _0x2a1ad3(0x2d6),
            layer10:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x36d)] ??
                '#ed1c24',
        },
        color2: {
            layer2:
                VisuMZ['MultiLayerHpGauge']['Settings']['LayerColors']['layer2_color2'] ??
                _0x2a1ad3(0x293),
            layer3:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x32d)] ??
                '#7cc576',
            layer4:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x1e9)] ??
                _0x2a1ad3(0x1da),
            layer5:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x1cd)] ??
                _0x2a1ad3(0x1dd),
            layer6:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)]['layer6_color2'] ??
                _0x2a1ad3(0x2e7),
            layer7:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)]['LayerColors'][_0x2a1ad3(0x24b)] ??
                _0x2a1ad3(0x275),
            layer8:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x2e8)]['layer8_color2'] ??
                _0x2a1ad3(0x33d),
            layer9:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x2e8)][_0x2a1ad3(0x1f0)] ??
                '#ffdeec',
            layer10:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings']['LayerColors'][_0x2a1ad3(0x1cb)] ??
                _0x2a1ad3(0x1dc),
        },
    }),
    (ColorManager[_0x2a1ad3(0x24a)] = function (_0x23bf9f) {
        const _0x5794d6 = _0x2a1ad3;
        return (
            (_0x23bf9f = String(_0x23bf9f)),
            _0x23bf9f['match'](/#(.*)/i)
                ? '#%1'[_0x5794d6(0x226)](String(RegExp['$1']))
                : this[_0x5794d6(0x33b)](Number(_0x23bf9f))
        );
    }),
    (ColorManager['getMultiLayerHpGaugeColor1'] = function (_0x37dfc6) {
        const _0x48fedc = _0x2a1ad3;
        if (_0x37dfc6 < 0x1) return this[_0x48fedc(0x2a8)]();
        else {
            if (_0x37dfc6 === 0x1) return this['hpGaugeColor1']();
            else {
                const _0x316d9a = _0x48fedc(0x346)[_0x48fedc(0x226)](_0x37dfc6['clamp'](0x2, 0xa)),
                    _0x58fb96 = ColorManager['MULTI_LAYER_HP_GAUGE'][_0x48fedc(0x2cf)][_0x316d9a];
                return this[_0x48fedc(0x24a)](_0x58fb96);
            }
        }
    }),
    (ColorManager[_0x2a1ad3(0x325)] = function (_0x2206c6) {
        const _0xaa5203 = _0x2a1ad3;
        if (_0x2206c6 < 0x1) return this[_0xaa5203(0x2a8)]();
        else {
            if (_0x2206c6 === 0x1) return this[_0xaa5203(0x1cc)]();
            else {
                const _0x14bc00 = _0xaa5203(0x346)[_0xaa5203(0x226)](
                        _0x2206c6[_0xaa5203(0x273)](0x2, 0xa)
                    ),
                    _0x1347be = ColorManager[_0xaa5203(0x290)]['color2'][_0x14bc00];
                return this[_0xaa5203(0x24a)](_0x1347be);
            }
        }
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x1d8)] = BattleManager[_0x2a1ad3(0x30b)]),
    (BattleManager[_0x2a1ad3(0x30b)] = function () {
        const _0x14c6d2 = _0x2a1ad3;
        (VisuMZ[_0x14c6d2(0x28e)][_0x14c6d2(0x1d8)][_0x14c6d2(0x34b)](this),
            !$gameTroop[_0x14c6d2(0x256)]() && $gameTroop[_0x14c6d2(0x34d)]());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x218)] = Game_BattlerBase['prototype'][_0x2a1ad3(0x1f6)]),
    (Game_BattlerBase['prototype'][_0x2a1ad3(0x1f6)] = function () {
        const _0x205094 = _0x2a1ad3;
        VisuMZ[_0x205094(0x28e)][_0x205094(0x218)]['call'](this);
        if (this[_0x205094(0x22a)]()) $gameTroop[_0x205094(0x34d)]();
    }),
    (VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x2e2)] = Game_BattlerBase[_0x2a1ad3(0x35f)]['appear']),
    (Game_BattlerBase[_0x2a1ad3(0x35f)][_0x2a1ad3(0x34c)] = function () {
        const _0x3aeab8 = _0x2a1ad3;
        VisuMZ[_0x3aeab8(0x28e)][_0x3aeab8(0x2e2)][_0x3aeab8(0x34b)](this);
        if (this[_0x3aeab8(0x22a)]()) $gameTroop[_0x3aeab8(0x34d)]();
    }),
    (Game_Enemy[_0x2a1ad3(0x290)] = {
        showDefault:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)]['Defaults']['showDefault'] ?? ![],
        persist:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x270)][_0x2a1ad3(0x2a3)] ??
            !![],
        defaultLayers:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x270)]['defaultLayers'] ?? 0x1,
        bgColor1: VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Defaults'][_0x2a1ad3(0x2fc)] ?? 0x13,
        bgColor2:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x270)][_0x2a1ad3(0x2c5)] ?? 0x12,
        borderColor:
            VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x270)][_0x2a1ad3(0x249)] ?? 0x2,
        borderthickness:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Defaults']['borderthickness'] ?? 0x2,
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1fe)] = function () {
        const _0x1f3ef7 = _0x2a1ad3;
        if (!this['enemy']()) return ![];
        return (
            this[_0x1f3ef7(0x2a4)]() &&
            this['meetsMultiLayerGaugeLifeState']() &&
            this[_0x1f3ef7(0x1ee)]()
        );
    }),
    (Game_Enemy['prototype'][_0x2a1ad3(0x1ee)] = function () {
        const _0x20d419 = _0x2a1ad3;
        if (this[_0x20d419(0x2d8)] !== undefined) return this['_canShowMultiLayerHpGauge'];
        this[_0x20d419(0x2d8)] = Game_Enemy[_0x20d419(0x290)]['showDefault'];
        const _0x22c6ca = VisuMZ[_0x20d419(0x28e)]['RegExp'],
            _0x3e7d10 = this[_0x20d419(0x360)]()[_0x20d419(0x2aa)] || '';
        if (_0x3e7d10['match'](_0x22c6ca['showMultiLayerGauge'])) this[_0x20d419(0x2d8)] = !![];
        else {
            if (_0x3e7d10[_0x20d419(0x276)](_0x22c6ca[_0x20d419(0x1e2)]))
                this[_0x20d419(0x2d8)] = !![];
            else {
                if (_0x3e7d10[_0x20d419(0x276)](_0x22c6ca[_0x20d419(0x29e)]))
                    this['_canShowMultiLayerHpGauge'] = !![];
                else {
                    if (_0x3e7d10[_0x20d419(0x276)](_0x22c6ca[_0x20d419(0x2b3)]))
                        this[_0x20d419(0x2d8)] = !![];
                    else {
                        if (_0x3e7d10['match'](_0x22c6ca[_0x20d419(0x2d9)]))
                            this[_0x20d419(0x2d8)] = !![];
                        else {
                            if (_0x3e7d10['match'](_0x22c6ca['borderColor']))
                                this['_canShowMultiLayerHpGauge'] = !![];
                            else {
                                if (_0x3e7d10[_0x20d419(0x276)](_0x22c6ca[_0x20d419(0x1ea)]))
                                    this[_0x20d419(0x2d8)] = !![];
                                else {
                                    if (_0x3e7d10[_0x20d419(0x276)](_0x22c6ca[_0x20d419(0x370)]))
                                        this[_0x20d419(0x2d8)] = !![];
                                    else
                                        _0x3e7d10['match'](_0x22c6ca['hideMultiLayerGauge']) &&
                                            (this[_0x20d419(0x2d8)] = ![]);
                                }
                            }
                        }
                    }
                }
            }
        }
        return this[_0x20d419(0x2d8)];
    }),
    (Game_Enemy['prototype']['meetsMultiLayerGaugeLifeState'] = function () {
        const _0x384b15 = _0x2a1ad3;
        return this[_0x384b15(0x1e7)]() ? !![] : !this[_0x384b15(0x1f2)]();
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1e7)] = function () {
        const _0x483fdc = _0x2a1ad3,
            _0x18e98c = VisuMZ['MultiLayerHpGauge'][_0x483fdc(0x295)],
            _0x21cad4 = this[_0x483fdc(0x360)]()['note'] || '';
        if (_0x21cad4['match'](_0x18e98c[_0x483fdc(0x1e2)])) return !![];
        else {
            if (_0x21cad4[_0x483fdc(0x276)](_0x18e98c[_0x483fdc(0x29e)])) return ![];
        }
        return Game_Enemy[_0x483fdc(0x290)][_0x483fdc(0x2a3)];
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x25c)] = function () {
        const _0x5abba0 = _0x2a1ad3;
        if (this[_0x5abba0(0x340)] !== undefined) return this[_0x5abba0(0x340)];
        this[_0x5abba0(0x340)] = {
            bgColor1: Game_Enemy[_0x5abba0(0x290)]['bgColor1'],
            bgColor2: Game_Enemy[_0x5abba0(0x290)][_0x5abba0(0x2c5)],
        };
        const _0x3dbf67 = VisuMZ[_0x5abba0(0x28e)][_0x5abba0(0x295)],
            _0x5be191 = this[_0x5abba0(0x360)]()[_0x5abba0(0x2aa)] || '';
        if (_0x5be191[_0x5abba0(0x276)](_0x3dbf67[_0x5abba0(0x2d9)])) {
            const _0x2e3b6a = String(RegExp['$1'])
                [_0x5abba0(0x212)](',')
                [_0x5abba0(0x1e0)](_0x1285f8 => _0x1285f8[_0x5abba0(0x344)]());
            ((this[_0x5abba0(0x340)][_0x5abba0(0x2fc)] = _0x2e3b6a[0x0]),
                (this['_multiLayerHpGaugeBgColorData'][_0x5abba0(0x2c5)] =
                    _0x2e3b6a[0x1] || _0x2e3b6a[0x0]));
        }
        return this['_multiLayerHpGaugeBgColorData'];
    }),
    (Game_Enemy['prototype'][_0x2a1ad3(0x207)] = function () {
        const _0x2ed846 = _0x2a1ad3;
        ((this['_multiLayerHpGaugeBgColorData'] = undefined), this[_0x2ed846(0x25c)]());
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1e8)] = function () {
        const _0x155bef = _0x2a1ad3;
        return this[_0x155bef(0x25c)]()[_0x155bef(0x2fc)];
    }),
    (Game_Enemy['prototype'][_0x2a1ad3(0x221)] = function () {
        const _0x363488 = _0x2a1ad3;
        return this[_0x363488(0x25c)]()[_0x363488(0x2c5)];
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)]['getMultiLayerHpGaugeBorderData'] = function () {
        const _0x8ec200 = _0x2a1ad3;
        if (this[_0x8ec200(0x357)] !== undefined) return this[_0x8ec200(0x357)];
        this[_0x8ec200(0x357)] = {
            color: Game_Enemy['MULTI_LAYER_HP_GAUGE'][_0x8ec200(0x249)],
            thick: Game_Enemy[_0x8ec200(0x290)][_0x8ec200(0x29b)],
        };
        const _0x1dc86f = VisuMZ[_0x8ec200(0x28e)][_0x8ec200(0x295)],
            _0x84624a = this[_0x8ec200(0x360)]()[_0x8ec200(0x2aa)] || '';
        return (
            _0x84624a['match'](_0x1dc86f[_0x8ec200(0x249)]) &&
                (this[_0x8ec200(0x357)][_0x8ec200(0x2bc)] = String(RegExp['$1'])[
                    _0x8ec200(0x344)
                ]()),
            _0x84624a[_0x8ec200(0x276)](_0x1dc86f['borderThick']) &&
                (this[_0x8ec200(0x357)][_0x8ec200(0x2b7)] = Math[_0x8ec200(0x241)](
                    Number(RegExp['$1']),
                    0x1
                )),
            this[_0x8ec200(0x357)]
        );
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x207)] = function () {
        const _0x5b4fbb = _0x2a1ad3;
        ((this[_0x5b4fbb(0x357)] = undefined), this[_0x5b4fbb(0x371)]());
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x36c)] = function () {
        const _0x7a5f11 = _0x2a1ad3;
        return this[_0x7a5f11(0x371)]()[_0x7a5f11(0x2bc)];
    }),
    (Game_Enemy['prototype'][_0x2a1ad3(0x280)] = function () {
        return this['getMultiLayerHpGaugeBorderData']()['thick'];
    }),
    (Game_Enemy['prototype'][_0x2a1ad3(0x2c6)] = function () {
        const _0x21070e = _0x2a1ad3;
        if (this[_0x21070e(0x364)]() !== '') return _0x21070e(0x271);
        else {
            if (Imported[_0x21070e(0x28c)] && this[_0x21070e(0x26a)]()) return 'svactor';
            else return $gameSystem[_0x21070e(0x317)]() ? _0x21070e(0x323) : 'enemy';
        }
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2fe)] = function () {
        const _0x5a5089 = _0x2a1ad3;
        if (this[_0x5a5089(0x2ee)] !== undefined) return this[_0x5a5089(0x2ee)];
        this[_0x5a5089(0x2ee)] = { name: '', index: 0x0 };
        const _0x4a2623 = VisuMZ[_0x5a5089(0x28e)][_0x5a5089(0x295)],
            _0x33dea2 = this[_0x5a5089(0x360)]()[_0x5a5089(0x2aa)] || '';
        return (
            _0x33dea2[_0x5a5089(0x276)](_0x4a2623[_0x5a5089(0x2b3)]) &&
                (this[_0x5a5089(0x2ee)] = {
                    name: String(RegExp['$1'])[_0x5a5089(0x344)](),
                    index: Math['max'](Number(RegExp['$2']), 0x0),
                }),
            this[_0x5a5089(0x2ee)]
        );
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x354)] = function () {
        const _0x576204 = _0x2a1ad3;
        ((this[_0x576204(0x2ee)] = undefined), this['getMultiLayerHpGaugeFaceGraphicData']());
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x364)] = function () {
        const _0x4c1f31 = _0x2a1ad3;
        return this[_0x4c1f31(0x2fe)]()['name'];
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x358)] = function () {
        const _0x355418 = _0x2a1ad3;
        return this['getMultiLayerHpGaugeFaceGraphicData']()[_0x355418(0x2f3)];
    }),
    (Game_Enemy['prototype']['getMultiLayerHpGaugeTotalLayers'] = function () {
        const _0x5747e1 = _0x2a1ad3;
        if (this['_multiLayerHpGaugeTotalLayers'] !== undefined) return this[_0x5747e1(0x355)];
        this[_0x5747e1(0x355)] = Game_Enemy[_0x5747e1(0x290)]['defaultLayers'];
        const _0xec2d6f = VisuMZ[_0x5747e1(0x28e)]['RegExp'],
            _0x17600e = this[_0x5747e1(0x360)]()[_0x5747e1(0x2aa)] || '';
        return (
            _0x17600e[_0x5747e1(0x276)](_0xec2d6f[_0x5747e1(0x370)]) &&
                (this['_multiLayerHpGaugeTotalLayers'] = Number(RegExp['$1'])[_0x5747e1(0x273)](
                    0x1,
                    0xa
                )),
            this[_0x5747e1(0x355)]
        );
    }),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x368)] = function () {
        const _0xf57ee1 = this['getMultiLayerHpGaugeTotalLayers']();
        if (_0xf57ee1 <= 0x1) return 0x1;
        const _0x232908 = this['mhp'] / _0xf57ee1;
        let _0x697271 = this['hp'] / _0x232908;
        return (
            _0x697271 % 0x1 === 0x0 ? (_0x697271 += 0x1) : (_0x697271 = Math['ceil'](_0x697271)),
            _0x697271
        );
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x315)] = Game_Troop[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27e)]),
    (Game_Troop[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27e)] = function (_0x4b9523) {
        const _0x4cb90b = _0x2a1ad3;
        (VisuMZ['MultiLayerHpGauge']['Game_Troop_setup'][_0x4cb90b(0x34b)](this, _0x4b9523),
            this['clearMultiLayerHpGaugeMembers']());
    }),
    (Game_Troop[_0x2a1ad3(0x35f)][_0x2a1ad3(0x363)] = function () {
        const _0x1aa1ad = _0x2a1ad3;
        if (this[_0x1aa1ad(0x261)] !== undefined) return this[_0x1aa1ad(0x261)];
        return (
            (this[_0x1aa1ad(0x261)] = this[_0x1aa1ad(0x240)]()[_0x1aa1ad(0x2cb)](
                _0x5b9b65 => _0x5b9b65 && _0x5b9b65[_0x1aa1ad(0x1fe)]()
            )),
            this['_cache_visibleMultiLayerHpGaugeMembers']
        );
    }),
    (Game_Troop[_0x2a1ad3(0x35f)][_0x2a1ad3(0x34d)] = function () {
        const _0x295364 = _0x2a1ad3;
        ((this[_0x295364(0x261)] = undefined), this[_0x295364(0x363)]());
    }),
    (Game_Troop[_0x2a1ad3(0x35f)][_0x2a1ad3(0x37e)] = function () {
        const _0xedbd = _0x2a1ad3;
        return this['visibleMultiLayerHpGaugeMembers']()[_0xedbd(0x2dd)];
    }),
    (Game_Troop['prototype'][_0x2a1ad3(0x2a6)] = function () {
        const _0x1ae72b = _0x2a1ad3;
        return Math[_0x1ae72b(0x241)](this[_0x1ae72b(0x37e)](), 0x1);
    }),
    (Game_Troop[_0x2a1ad3(0x35f)]['totalVisibleMultiLayerHpGaugeRows'] = function () {
        const _0x11effb = _0x2a1ad3,
            _0x4e3605 = this[_0x11effb(0x37e)](),
            _0x5b587e = Scene_Battle[_0x11effb(0x290)][_0x11effb(0x2ec)];
        return Math[_0x11effb(0x2cd)](_0x4e3605 / _0x5b587e);
    }),
    (Scene_Battle[_0x2a1ad3(0x290)] = {
        maxWidth:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)]['General'][_0x2a1ad3(0x379)] ?? 0x330,
        perRow:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x2ec)] ?? 0x4,
        rowSpacing: VisuMZ[_0x2a1ad3(0x28e)]['Settings']['General'][_0x2a1ad3(0x2f5)] ?? 0x4,
        fadeSpeed:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x378)] ?? 0x18,
    }),
    (VisuMZ[_0x2a1ad3(0x28e)]['Scene_Battle_createAllWindows'] =
        Scene_Battle['prototype'][_0x2a1ad3(0x330)]),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x330)] = function () {
        const _0x4519db = _0x2a1ad3;
        (this[_0x4519db(0x2e1)](),
            VisuMZ[_0x4519db(0x28e)][_0x4519db(0x2b4)][_0x4519db(0x34b)](this));
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2e1)] = function () {
        const _0x46db26 = _0x2a1ad3;
        (this['createMultiLayerHpGaugeContainer'](), this[_0x46db26(0x29c)]());
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)]['createMultiLayerHpGaugeContainer'] = function () {
        const _0x420a67 = _0x2a1ad3;
        ((this[_0x420a67(0x272)] = new Sprite()), this[_0x420a67(0x2d3)](this[_0x420a67(0x272)]));
        const _0x1e9748 = Scene_Battle[_0x420a67(0x290)]['maxWidth'],
            _0x1d9da6 = Math['floor']((Graphics[_0x420a67(0x282)] - _0x1e9748) / 0x2);
        this['_multiLayerHpGaugeContainer']['x'] = _0x1d9da6;
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)]['createMultiLayerHpGaugeSprites'] = function () {
        const _0x18851f = _0x2a1ad3,
            _0x3c19cd = $gameTroop[_0x18851f(0x240)]();
        for (const _0x44bdec of _0x3c19cd) {
            if (!_0x44bdec) continue;
            this['addMultiLayerHpGaugeSprite'](_0x44bdec);
        }
    }),
    (Scene_Battle['prototype']['addMultiLayerHpGaugeSprite'] = function (_0x4fafb1) {
        const _0x504e75 = _0x2a1ad3,
            _0x4bb1cb = new Sprite_MultiLayerHpContainer(_0x4fafb1);
        this[_0x504e75(0x272)]['addChild'](_0x4bb1cb);
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x31b)] = Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x21b)]),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x21b)] = function () {
        const _0x566c21 = _0x2a1ad3;
        (VisuMZ['MultiLayerHpGauge'][_0x566c21(0x31b)]['call'](this),
            this['updateMultiLayerHpGaugeContainer']());
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x377)] = function () {
        const _0x285c5f = _0x2a1ad3;
        (this[_0x285c5f(0x2e3)](), this[_0x285c5f(0x210)]());
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2e3)] = function () {
        const _0x41760f = _0x2a1ad3;
        (BattleManager[_0x41760f(0x211)] === _0x41760f(0x2ce) || BattleManager[_0x41760f(0x2ea)]) &&
            this[_0x41760f(0x272)] &&
            (this['_multiLayerHpGaugeContainer'][_0x41760f(0x26f)] -=
                Scene_Battle[_0x41760f(0x290)][_0x41760f(0x23a)]);
    }),
    (Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x210)] = function () {
        const _0x5ee8a4 = _0x2a1ad3,
            _0x5253d6 = this['_multiLayerHpGaugeContainer']['children']['filter'](
                _0x628fb3 => _0x628fb3['_hold'] && _0x628fb3[_0x5ee8a4(0x26f)] <= 0x0
            );
        for (const _0x501116 of _0x5253d6) {
            (this[_0x5ee8a4(0x272)][_0x5ee8a4(0x356)](_0x501116), _0x501116[_0x5ee8a4(0x21f)]());
        }
    }),
    (VisuMZ['MultiLayerHpGauge']['Scene_Battle_createDisplayObjects'] =
        Scene_Battle[_0x2a1ad3(0x35f)][_0x2a1ad3(0x31d)]),
    (Scene_Battle['prototype']['createDisplayObjects'] = function () {
        const _0x3eeebf = _0x2a1ad3;
        VisuMZ[_0x3eeebf(0x28e)]['Scene_Battle_createDisplayObjects'][_0x3eeebf(0x34b)](this);
        if (this[_0x3eeebf(0x2c3)]) this[_0x3eeebf(0x2c3)][_0x3eeebf(0x2c7)]();
    }));
function _0x5f02() {
    const _0xf1ad84 = [
        'deathStateId',
        'note',
        '70334RQfwPD',
        'Game_Battler_removeState',
        '_lastLetter',
        'createBitmap',
        'calcWidth',
        'layer7_color1',
        'drawLetter',
        'otbHelpOffsetY',
        'faceGraphic',
        'Scene_Battle_createAllWindows',
        'ctbEachRowOffsetY',
        'checkUpdateRequests',
        'thick',
        'drawActorIcons',
        'iconIndex',
        'setBitmapSize',
        'offsetX',
        'color',
        'children',
        'round',
        'loadSvEnemy',
        '_textWidth',
        'findTargetSprite',
        'stbHelpOffsetY',
        '_logWindow',
        'letterFontName',
        'bgColor2',
        'getMultiLayerHpGaugeGraphicType',
        'registerMultiLayerHpGaugePositionY',
        'drawText',
        '_bgSprite',
        'Game_BattlerBase_updateStateTurns',
        'filter',
        'gaugeRate',
        'ceil',
        'battleEnd',
        'color1',
        'etb',
        '_context',
        'faceWidth',
        'addWindow',
        'reduceRedundantHpGauge',
        'checkNeedReplacement',
        '#f06eaa',
        '_frameWidth',
        '_canShowMultiLayerHpGauge',
        'bgColor',
        'drawValue',
        '_hold',
        'Window_BattleLog_update',
        'length',
        '_requestMultiLayerHpGaugeStateUpdate',
        '_statesSprite',
        'floor',
        'createMultiLayerHpGauges',
        'Game_BattlerBase_appear',
        'updateMultiLayerHpGaugeContainerEndBattle',
        'labelOutlineColor',
        'Window_FTB_TurnOrder_updatePosition',
        '_breakShieldSprite',
        '#8393ca',
        'LayerColors',
        'addLoadListener',
        '_victoryPhase',
        'eachRowOffsetY',
        'perRow',
        'Window_STB_TurnOrder_updatePosition',
        '_multiLayerHpGaugeFaceGraphicData',
        'otbEachRowOffsetY',
        'labelColor',
        'prepareGraphic',
        'createBgSprite',
        'index',
        'status',
        'rowSpacing',
        '_borderSprite',
        'Game_BattlerBase_clearStates',
        'bitmapWidth',
        'drawBorderSprite',
        'svBattlerName',
        '_graphicHue',
        'bgColor1',
        'gaugeX',
        'getMultiLayerHpGaugeFaceGraphicData',
        'show',
        'drawBgSprite',
        'Game_Battler_onTurnEnd',
        'hpA',
        '_graphicSv',
        'loadEnemy',
        '_lastWidth',
        'createLetterSprite',
        'version',
        'VisuMZ_2_BattleSystemBTB',
        'svActorHorzCells',
        'layer2_color1',
        'endAction',
        'strokeRect',
        'requestMultiLayerHpGaugeStateUpdate',
        'updateBreakShieldIcon',
        'calcWindowHeight',
        'ptbEachRowOffsetY',
        'helpOffsetY',
        'removeState',
        'visible',
        'FUNC',
        'Game_Troop_setup',
        'repositionHelpY',
        'isSideView',
        'createDrawWindow',
        'offset',
        '22WPaoSA',
        'Scene_Battle_update',
        'battleLogPerRowOffsetY',
        'createDisplayObjects',
        'processReplacement',
        'changeFaceGraphic',
        'etbHelpOffsetY',
        '_multiLayerHpGaugePositionY',
        'ARRAYSTR',
        'svenemy',
        'refresh',
        'getMultiLayerHpGaugeColor2',
        'Settings',
        'updateGraphic',
        'bitmap',
        'Gauge',
        '_graphicFaceName',
        'battlerHue',
        'faceHeight',
        'layer3_color2',
        'Game_Battler_onBattleStart',
        'reduceRedundantBreakShield',
        'createAllWindows',
        'blt',
        '23235SnaZiJ',
        'constructor',
        'ptb',
        'paintOpacity',
        'ARRAYNUM',
        'calcPositionY',
        'BREAK_SHIELDS_STUN_STATE',
        'totalVisibleMultiLayerHpGaugeRows',
        'createLinearGradient',
        'textColor',
        'gradientFillRect',
        '#a186be',
        'updateMultiLyerHpGaugePositionY',
        'ConvertParams',
        '_multiLayerHpGaugeBgColorData',
        'VisuMZ_2_BattleSystemCTB',
        'battler',
        'btbNormalOffsetY',
        'trim',
        'getMultiLayerHpGaugeTotalLayers',
        'layer%1',
        'Window_OTB_TurnOrder_updatePosition',
        'stateIcon',
        'itemHeight',
        'updatePositionY',
        'call',
        'appear',
        'clearMultiLayerHpGaugeMembers',
        'fontFace',
        'JSON',
        'includes',
        'isStateAffected',
        'placeBreakShieldIcon',
        'breakShields',
        'updateMultiLayerHpGaugeFaceGraphicData',
        '_multiLayerHpGaugeTotalLayers',
        'removeChild',
        '_multiLayerHpGaugeBorderData',
        'getMultiLayerHpGaugeFaceIndex',
        'GetPolygonStyle',
        '_battler',
        'midFadeSpeed',
        'createBorderSprite',
        'updateGaugeWidth',
        'loadSvActor',
        'prototype',
        'enemy',
        'ctb',
        '33498wvRmxw',
        'visibleMultiLayerHpGaugeMembers',
        'getMultiLayerHpGaugeFaceName',
        '_letterSprite',
        '_lastPositionY',
        'isVisualHpGaugeDisplayed',
        'currentMultiLayerHpGaugeLayer',
        'Window_CTB_TurnOrder_updatePosition',
        'applyNewBitmap',
        'onBattleStart',
        'getMultiLayerHpGaugeBorderColor',
        'layer10_color1',
        'drawVisualStyleGaugeFront',
        'GroupDigits',
        'layers',
        'getMultiLayerHpGaugeBorderData',
        'toUpperCase',
        '250536UnZZbs',
        '483912teSvoG',
        'hpRate',
        'return\x200',
        'updateMultiLayerHpGaugeContainer',
        'endBattleFadeSpeed',
        'maxWidth',
        'useDigitGrouping',
        'atbEachRowOffsetY',
        '_spriteset',
        'ARRAYJSON',
        'totalVisibleMultiLayerHpGauges',
        'createBattlerGraphicSprite',
        'layer10_color2',
        'hpGaugeColor2',
        'layer5_color2',
        'svActorVertCells',
        'reduceRedundancy',
        'bufferX',
        '_lastTotalVisibleGauges',
        'createBattlerGaugeStates',
        'VisuMZ_2_BattleSystemPTB',
        '#00a99d',
        'VisuMZ_2_BattleSystemOTB',
        'otb',
        'layer9_color1',
        'BattleManager_endAction',
        '1130dqkSeW',
        '#7accc8',
        '#39b54a',
        '#f26c4f',
        '#6dcff6',
        'calcPositionX',
        '#2e3192',
        'map',
        'Graphic',
        'persistMultiLayerGauge',
        '_bitmapWidth',
        'currentMaxValue',
        '15600RxfLoc',
        'drawLetterSprite',
        'isMultiLayerGaugeLifeStatePersistant',
        'getMultiLayerHpGaugeBgColor1',
        'layer4_color2',
        'borderThick',
        'VisuMZ_2_BattleSystemFTB',
        'redraw',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'canShowMultiLayerHpGauge',
        'toFixed',
        'layer9_color2',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'isDead',
        '_stateIconSprite',
        'layer4_color1',
        '_gaugeSprite',
        'revive',
        'label',
        'addBreakShieldIcon',
        'BREAK_SHIELDS_ENEMIES',
        '_scene',
        'clearStates',
        'valueFmt',
        'changeSvActorGraphic',
        'showMultiLayerHpGauge',
        'textHeight',
        'create',
        'right',
        'drawVisualStyleGaugeBack',
        'iconHeight',
        'reposition',
        'VisuMZ_2_BattleSystemETB',
        'updateOpacity',
        'updateMultiLayerHpGaugeBorderData',
        'setFrame',
        'initialize',
        'normalOffsetY',
        'maxHpSegmentAmount',
        'indexOf',
        'parameters',
        'BottomPosition',
        'min',
        'updateMultiLayerHpGaugeContainerRemoval',
        '_phase',
        'split',
        'getMultiLayerHpGaugeColor1',
        'labelY',
        '_lastIndex',
        'drawFullVisualStyleGauge',
        'Window_BTB_TurnOrder_updatePosition',
        'Game_BattlerBase_revive',
        'Compatibility',
        'States',
        'update',
        '_graphicType',
        '_graphicSprite',
        'letterFontSize',
        'destroy',
        'setBlendColor',
        'getMultiLayerHpGaugeBgColor2',
        'Sprite_Enemy_updateStateSprite',
        'EVAL',
        'drawLabel',
        'height',
        'format',
        'setIndexData',
        'width',
        'faceSize',
        'isEnemy',
        'General',
        'left',
        'VisuMZ_0_CoreEngine',
        'stateTooltipsEnable',
        'mainSprite',
        'mainFontFace',
        'name',
        'updateBitmap',
        'fontSize',
        'VisuMZ_4_BreakShields',
        'parse',
        'btbEachRowOffsetY',
        'setupValueFont',
        'setTotalGauges',
        'mhp',
        'fadeSpeed',
        'updateLetterSprite',
        '_lastPositionX',
        'setHue',
        'valuePercentDigits',
        'addState',
        'members',
        'max',
        'clear',
        'iconWidth',
        'layer8_color1',
        '_graphicEnemy',
        '%3%',
        '_letter',
        '_finishChecks',
        'borderColor',
        'getColor',
        'layer7_color2',
        'getStateTooltipBattler',
        'otbNormalOffsetY',
        'stb',
        'calcBitmapWidth',
        'drawStateIcons',
        'RepositionTopForHelp',
        'svactor',
        'drawFullGauge',
        '1429620oQkOUW',
        'offsetY',
        'isAllDead',
        'repositionForHelp',
        '_dummyWindow',
        'changeEnemyGraphic',
        'loadFace',
        'updateGraphicHue',
        'getMultiLayerHpGaugeBgColorData',
        'createBattlerGaugeSprite',
        'updateStatesWidth',
        'atbNormalOffsetY',
        'updateStateTurns',
        '_cache_visibleMultiLayerHpGaugeMembers',
        '_blendColor',
        '_targetMaxValue',
        'updatePositionX',
        'contents',
        'updateStateSprite',
        'DisplayPosition',
        '_targetValue',
        'bind',
        'hasSvBattler',
        'setWidth',
        'Window_ETB_TurnOrder_updatePosition',
        'addMultiLayerHpGaugeSprite',
        'ClearTextOffset',
        'opacity',
        'Defaults',
        'face',
        '_multiLayerHpGaugeContainer',
        'clamp',
        '161XBfnYQ',
        '#605ca8',
        'match',
        'updateSelectionEffect',
        'VisualGaugeStyles',
        'Sprite_FieldGaugeATB_updatePosition',
        'VisuMZ_2_BattleSystemATB',
        'addChild',
        'updatePosition',
        'VisuMZ_3_VisualGaugeStyles',
        'setup',
        'SETTINGS',
        'getMultiLayerHpGaugeBorderThickness',
        'labelOutlineWidth',
        'boxWidth',
        '_graphicFaceIndex',
        'resize',
        'styleName',
        'etbEachRowOffsetY',
        'currentValue',
        'drawGauge',
        'gaugeHeight',
        'Window_PTB_TurnOrder_updatePosition',
        'top',
        'VisuMZ_1_BattleCore',
        'onTurnEnd',
        'MultiLayerHpGauge',
        '#fff200',
        'MULTI_LAYER_HP_GAUGE',
        'createGraphicSprite',
        'fillRect',
        '#fff799',
        'bitmapHeight',
        'RegExp',
        '_helpWindow',
        'btbHelpOffsetY',
        'ptbHelpOffsetY',
        'STRUCT',
        '_lastPlural',
        'borderthickness',
        'createMultiLayerHpGaugeSprites',
        'hpGauge',
        'temporalMultiLayerGauge',
        'getStyleName',
        'finishChecks',
        'VisuMZ_2_BattleSystemSTB',
        'exit',
        'persist',
        'isAppeared',
        'Game_Battler_addState',
        'totalVisibleMultiLayerHpGaugeCount',
        'normalColor',
        'gaugeBackColor',
    ];
    _0x5f02 = function () {
        return _0xf1ad84;
    };
    return _0x5f02();
}
function Sprite_MultiLayerHpContainer() {
    const _0x1aed92 = _0x2a1ad3;
    this[_0x1aed92(0x209)](...arguments);
}
((Sprite_MultiLayerHpContainer['prototype'] = Object[_0x2a1ad3(0x200)](
    Sprite_Clickable[_0x2a1ad3(0x35f)]
)),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x333)] =
        Sprite_MultiLayerHpContainer),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x27f)] = {
        bufferX: VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['General']['bufferX'] ?? 0x4,
        checkFrequency:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)]['checkFrequency'] ?? 0x14,
        faceSize: VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['General']['faceSize'] ?? 0x40,
        fadeSpeed: VisuMZ[_0x2a1ad3(0x28e)]['Settings']['General'][_0x2a1ad3(0x35b)] ?? 0x10,
        repositionForHelp:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x257)] ??
            !![],
        repositionHelpY:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x316)] ?? 0x6c,
        stateTooltipsEnable:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['General']['stateTooltipsEnable'] ?? !![],
        offset: {
            x:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x2bb)] ??
                0x0,
            y: VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['General'][_0x2a1ad3(0x255)] ?? 0x0,
        },
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x209)] = function (_0x47c290) {
        const _0x1a1864 = _0x2a1ad3;
        ((this[_0x1a1864(0x35a)] = _0x47c290),
            Sprite_Clickable['prototype'][_0x1a1864(0x209)][_0x1a1864(0x34b)](this),
            (this[_0x1a1864(0x26f)] = 0x0),
            this['createBattlerGraphicSprite'](),
            this['createBattlerGaugeSprite'](),
            this[_0x1a1864(0x1d2)](),
            this['finishChecks']());
    }),
    (Sprite_MultiLayerHpContainer['prototype'][_0x2a1ad3(0x37f)] = function () {
        const _0x3b2342 = _0x2a1ad3;
        if (!Sprite_MultiLayerHpFace['SETTINGS'][_0x3b2342(0x2ff)]) return;
        const _0x5d719b = new Sprite_MultiLayerHpFace(this['_battler']);
        (this['addChild'](_0x5d719b), (this['_graphicsSprite'] = _0x5d719b));
    }),
    (Sprite_MultiLayerHpContainer['prototype'][_0x2a1ad3(0x229)] = function () {
        const _0x430c72 = _0x2a1ad3;
        return Sprite_MultiLayerHpFace[_0x430c72(0x27f)]['show']
            ? Sprite_MultiLayerHpContainer['SETTINGS'][_0x430c72(0x229)]
            : 0x0;
    }),
    (Sprite_MultiLayerHpContainer['prototype'][_0x2a1ad3(0x25d)] = function () {
        const _0x22f568 = _0x2a1ad3;
        if (!Sprite_MultiLayerHpGauge[_0x22f568(0x27f)][_0x22f568(0x2ff)]) return;
        const _0x3a2d9b = new Sprite_MultiLayerHpGauge(this[_0x22f568(0x35a)]);
        (this[_0x22f568(0x27b)](_0x3a2d9b), (this[_0x22f568(0x1f5)] = _0x3a2d9b));
        const _0x5b067e = this[_0x22f568(0x229)](),
            _0x3b3729 = Sprite_MultiLayerHpContainer[_0x22f568(0x27f)][_0x22f568(0x1d0)],
            _0x3814c3 = Sprite_MultiLayerHpGauge['SETTINGS']['offset'];
        ((_0x3a2d9b['x'] = _0x5b067e),
            (_0x3a2d9b['x'] += _0x3b3729),
            (_0x3a2d9b['x'] += _0x3814c3['x']),
            (_0x3a2d9b['y'] = 0x0),
            (_0x3a2d9b['y'] += _0x3814c3['y']),
            _0x3a2d9b[_0x22f568(0x27e)](this['_battler'], 'hp'),
            this[_0x22f568(0x35d)]());
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x24f)] = function () {
        const _0x223b20 = _0x2a1ad3,
            _0x58f93b = this['faceSize'](),
            _0x41d40a = Sprite_MultiLayerHpContainer[_0x223b20(0x27f)][_0x223b20(0x1d0)],
            _0x338dac = Scene_Battle[_0x223b20(0x290)][_0x223b20(0x379)],
            _0x1e77a0 = Math[_0x223b20(0x20f)](
                $gameTroop[_0x223b20(0x2a6)](),
                Scene_Battle[_0x223b20(0x290)][_0x223b20(0x2ec)]
            );
        return Math[_0x223b20(0x2cd)](_0x338dac / _0x1e77a0) - _0x41d40a * 0x2 - _0x58f93b;
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x35d)] = function () {
        const _0x36f59b = _0x2a1ad3;
        if (!this['_gaugeSprite']) return;
        const _0x1d1bc0 = this['calcBitmapWidth']();
        this['_gaugeSprite'][_0x36f59b(0x26b)](_0x1d1bc0);
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1d2)] = function () {
        const _0x25f8b4 = _0x2a1ad3;
        if (!Sprite_MultiLayerHpStates[_0x25f8b4(0x27f)]['show']) return;
        const _0x5a7318 = new Sprite_MultiLayerHpStates(this[_0x25f8b4(0x35a)]);
        (this['addChild'](_0x5a7318), (this[_0x25f8b4(0x2df)] = _0x5a7318));
        const _0x50c7ed = this['faceSize'](),
            _0x2f2ca6 = Sprite_MultiLayerHpContainer[_0x25f8b4(0x27f)]['bufferX'],
            _0x59c81a = Sprite_MultiLayerHpStates['SETTINGS']['offset'];
        ((_0x5a7318['x'] = _0x50c7ed),
            (_0x5a7318['x'] += _0x2f2ca6),
            (_0x5a7318['x'] += _0x59c81a['x']),
            (_0x5a7318['y'] = 0x0),
            (_0x5a7318['y'] += _0x59c81a['y']),
            this[_0x25f8b4(0x25e)]());
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x25e)] = function () {
        const _0x3720ce = _0x2a1ad3;
        if (!this[_0x3720ce(0x2df)]) return;
        const _0x5c09b4 = this[_0x3720ce(0x24f)]();
        this[_0x3720ce(0x2df)]['setWidth'](_0x5c09b4);
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2a0)] = function () {
        const _0x1525e9 = _0x2a1ad3;
        (this['_lastTotalVisibleGauges'] !== $gameTroop[_0x1525e9(0x2a6)]() &&
            (this[_0x1525e9(0x238)](), this[_0x1525e9(0x2ba)]()),
            this[_0x1525e9(0x215)] !==
                $gameTroop[_0x1525e9(0x363)]()[_0x1525e9(0x20c)](this[_0x1525e9(0x35a)]) &&
                (this[_0x1525e9(0x227)](), this[_0x1525e9(0x264)]()),
            (this[_0x1525e9(0x248)] = !![]));
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x238)] = function () {
        this['_lastTotalVisibleGauges'] = $gameTroop['totalVisibleMultiLayerHpGaugeCount']();
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2af)] = function () {
        const _0x38d21b = _0x2a1ad3,
            _0x4ef98e = Scene_Battle['MULTI_LAYER_HP_GAUGE'][_0x38d21b(0x379)],
            _0x4d8731 = Math['min'](
                this[_0x38d21b(0x1d1)],
                Scene_Battle[_0x38d21b(0x290)][_0x38d21b(0x2ec)]
            );
        return Math[_0x38d21b(0x2e0)](_0x4ef98e / _0x4d8731);
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2ba)] = function () {
        const _0x3f4953 = _0x2a1ad3,
            _0x49e2cb = this[_0x3f4953(0x2af)]();
        this['_lastWidth'] = _0x49e2cb;
        const _0x56d7aa = Sprite_MultiLayerHpContainer[_0x3f4953(0x27f)][_0x3f4953(0x229)];
        (this[_0x3f4953(0x328)]
            ? (this['bitmap'][_0x3f4953(0x242)](),
              this['bitmap'][_0x3f4953(0x284)](_0x49e2cb, _0x56d7aa),
              (this[_0x3f4953(0x228)] = _0x49e2cb),
              (this[_0x3f4953(0x225)] = _0x56d7aa),
              this[_0x3f4953(0x35d)](),
              this[_0x3f4953(0x25e)]())
            : (this[_0x3f4953(0x328)] = new Bitmap(_0x49e2cb, _0x56d7aa)),
            (this[_0x3f4953(0x215)] = undefined));
    }),
    (Sprite_MultiLayerHpContainer['prototype'][_0x2a1ad3(0x227)] = function () {
        const _0xb9d2a1 = _0x2a1ad3;
        this[_0xb9d2a1(0x215)] = $gameTroop['visibleMultiLayerHpGaugeMembers']()[_0xb9d2a1(0x20c)](
            this[_0xb9d2a1(0x35a)]
        );
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x21b)] = function () {
        const _0x4d8b9b = _0x2a1ad3;
        Sprite_Clickable['prototype'][_0x4d8b9b(0x21b)][_0x4d8b9b(0x34b)](this);
        if (!this[_0x4d8b9b(0x35a)]) return;
        (Graphics['frameCount'] %
            Sprite_MultiLayerHpContainer[_0x4d8b9b(0x27f)]['checkFrequency'] ===
            0x0 && this[_0x4d8b9b(0x2d5)](),
            this[_0x4d8b9b(0x34a)](),
            this[_0x4d8b9b(0x206)](),
            this[_0x4d8b9b(0x277)]());
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1de)] = function () {
        const _0xf8bb95 = _0x2a1ad3;
        if (this[_0xf8bb95(0x215)] < 0x0) return Graphics['width'] * 0xa;
        const _0x44a623 = Scene_Battle['MULTI_LAYER_HP_GAUGE'][_0xf8bb95(0x379)],
            _0x2f568c = Math['min'](
                this[_0xf8bb95(0x1d1)],
                Scene_Battle[_0xf8bb95(0x290)][_0xf8bb95(0x2ec)]
            ),
            _0x489209 = Math[_0xf8bb95(0x2cd)](_0x44a623 / _0x2f568c),
            _0x1084f7 =
                this[_0xf8bb95(0x215)] % Scene_Battle['MULTI_LAYER_HP_GAUGE'][_0xf8bb95(0x2ec)];
        let _0x21f5bc = _0x489209 * _0x1084f7;
        return (
            (_0x21f5bc += Sprite_MultiLayerHpContainer[_0xf8bb95(0x27f)][_0xf8bb95(0x319)]['x']),
            _0x21f5bc
        );
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x264)] = function () {
        const _0xae011d = _0x2a1ad3;
        if (this['_hold']) return;
        if (this[_0xae011d(0x215)] === undefined) return;
        if (this[_0xae011d(0x215)] < 0x0) return (this['x'] = Graphics[_0xae011d(0x228)] * 0xa);
        const _0x4f5cd5 = this[_0xae011d(0x1de)]();
        ((this[_0xae011d(0x23c)] = _0x4f5cd5), (this['x'] = _0x4f5cd5));
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x337)] = function () {
        const _0x3a50ea = _0x2a1ad3;
        if (this[_0x3a50ea(0x215)] < 0x0) return Graphics['height'] * 0xa;
        const _0x4eacaa = Sprite_MultiLayerHpContainer['SETTINGS'],
            _0x7d2d5e = Math[_0x3a50ea(0x2e0)](
                this['_lastIndex'] / Scene_Battle[_0x3a50ea(0x290)][_0x3a50ea(0x2ec)]
            );
        let _0x26bff4 = _0x7d2d5e * (0x4 + _0x4eacaa['faceSize']);
        return (
            (_0x26bff4 += Sprite_MultiLayerHpContainer[_0x3a50ea(0x27f)][_0x3a50ea(0x319)]['y']),
            _0x26bff4
        );
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x34a)] = function () {
        const _0xf8949e = _0x2a1ad3;
        if (this[_0xf8949e(0x2db)]) return;
        if (this['_lastIndex'] === undefined) return;
        if (this['_lastIndex'] < 0x0) return (this['y'] = Graphics[_0xf8949e(0x225)] * 0xa);
        const _0x3423ee = Sprite_MultiLayerHpContainer[_0xf8949e(0x27f)];
        let _0x13e715 = this['calcPositionY']();
        this[_0xf8949e(0x366)] = _0x13e715;
        const _0x406638 = SceneManager[_0xf8949e(0x1fa)][_0xf8949e(0x296)];
        (_0x406638 &&
            _0x406638[_0xf8949e(0x313)] &&
            _0x3423ee[_0xf8949e(0x257)] &&
            (_0x13e715 += _0x3423ee[_0xf8949e(0x316)]),
            (this['y'] = _0x13e715));
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)]['updateOpacity'] = function () {
        const _0x18bc63 = _0x2a1ad3,
            _0x450d9e = Sprite_MultiLayerHpContainer[_0x18bc63(0x27f)]['fadeSpeed'];
        this[_0x18bc63(0x26f)] += this['_hold'] ? -_0x450d9e : _0x450d9e;
    }),
    (Sprite_MultiLayerHpContainer['prototype'][_0x2a1ad3(0x277)] = function () {
        const _0x30075b = _0x2a1ad3;
        if (!this[_0x30075b(0x35a)]) return;
        const _0x29977a = SceneManager['_scene'][_0x30075b(0x37c)];
        if (!_0x29977a) return;
        const _0x1e5f7f = _0x29977a[_0x30075b(0x2c1)](this[_0x30075b(0x35a)]);
        if (!_0x1e5f7f) return;
        const _0x5dfb8f = _0x1e5f7f[_0x30075b(0x22f)]();
        if (!_0x5dfb8f) return;
        this[_0x30075b(0x220)](_0x5dfb8f[_0x30075b(0x262)]);
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2d5)] = function () {
        const _0x8add06 = _0x2a1ad3;
        if (!this['_finishChecks']) return;
        if (this[_0x8add06(0x1d1)] !== $gameTroop[_0x8add06(0x2a6)]()) {
            this[_0x8add06(0x238)]();
            if (this[_0x8add06(0x305)] !== this['calcWidth']()) return this['processReplacement']();
        }
        if (
            this['_lastIndex'] !==
            $gameTroop['visibleMultiLayerHpGaugeMembers']()[_0x8add06(0x20c)](
                this[_0x8add06(0x35a)]
            )
        ) {
            this['setIndexData']();
            if (
                this[_0x8add06(0x23c)] !== this[_0x8add06(0x1de)]() ||
                this['_lastPositionY'] !== this[_0x8add06(0x337)]()
            )
                return this['processReplacement']();
        }
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x31e)] = function () {
        const _0x6e2c1c = _0x2a1ad3;
        this[_0x6e2c1c(0x2db)] = !![];
        for (const _0x34caeb of this[_0x6e2c1c(0x2bd)]) {
            if (_0x34caeb) _0x34caeb[_0x6e2c1c(0x2db)] = !![];
        }
        const _0x1a1cff = SceneManager[_0x6e2c1c(0x1fa)];
        if (_0x1a1cff) _0x1a1cff[_0x6e2c1c(0x26d)](this[_0x6e2c1c(0x35a)]);
    }),
    (Sprite_MultiLayerHpContainer[_0x2a1ad3(0x35f)][_0x2a1ad3(0x24c)] = function () {
        const _0x237428 = _0x2a1ad3;
        if (this[_0x237428(0x2db)]) return null;
        if (!Sprite_MultiLayerHpContainer[_0x237428(0x27f)][_0x237428(0x22e)]) return null;
        return this[_0x237428(0x35a)];
    }));
function Sprite_MultiLayerHpFace() {
    const _0x4b82c6 = _0x2a1ad3;
    this[_0x4b82c6(0x209)](...arguments);
}
((Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)] = Object[_0x2a1ad3(0x200)](Sprite[_0x2a1ad3(0x35f)])),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x333)] = Sprite_MultiLayerHpFace),
    (Sprite_MultiLayerHpFace['SETTINGS'] = {
        show:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x1e1)][_0x2a1ad3(0x2ff)] ?? !![],
        drawLetter:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x1e1)][_0x2a1ad3(0x2b1)] ?? !![],
        letterFontName:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Graphic']['letterFontName'] ?? '',
        letterFontSize:
            VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x1e1)]['letterFontSize'] ?? 0x10,
    }),
    (Sprite_MultiLayerHpFace['prototype'][_0x2a1ad3(0x209)] = function (_0x20f766) {
        const _0x45136d = _0x2a1ad3;
        ((this[_0x45136d(0x35a)] = _0x20f766),
            Sprite[_0x45136d(0x35f)][_0x45136d(0x209)][_0x45136d(0x34b)](this),
            this[_0x45136d(0x2f2)](),
            this[_0x45136d(0x291)](),
            this[_0x45136d(0x35c)](),
            this[_0x45136d(0x306)]());
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)]['createBgSprite'] = function () {
        const _0x22e28e = _0x2a1ad3,
            _0xf84f09 = Sprite_MultiLayerHpContainer[_0x22e28e(0x27f)]['faceSize'];
        ((this[_0x22e28e(0x2c9)] = new Sprite()),
            this[_0x22e28e(0x27b)](this[_0x22e28e(0x2c9)]),
            (this[_0x22e28e(0x2c9)][_0x22e28e(0x328)] = new Bitmap(_0xf84f09, _0xf84f09)),
            this[_0x22e28e(0x300)]());
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x291)] = function () {
        const _0x386e05 = _0x2a1ad3,
            _0x3c2f90 = Sprite_MultiLayerHpContainer['SETTINGS'][_0x386e05(0x229)];
        ((this[_0x386e05(0x21d)] = new Sprite()),
            this['addChild'](this['_graphicSprite']),
            (this['_graphicSprite'][_0x386e05(0x328)] = new Bitmap(_0x3c2f90, _0x3c2f90)),
            this['prepareGraphic']());
    }),
    (Sprite_MultiLayerHpFace['prototype'][_0x2a1ad3(0x35c)] = function () {
        const _0x3ce98d = _0x2a1ad3,
            _0x1ca2ec = Sprite_MultiLayerHpContainer[_0x3ce98d(0x27f)]['faceSize'];
        ((this[_0x3ce98d(0x2f6)] = new Sprite()),
            this[_0x3ce98d(0x27b)](this[_0x3ce98d(0x2f6)]),
            (this[_0x3ce98d(0x2f6)][_0x3ce98d(0x328)] = new Bitmap(_0x1ca2ec, _0x1ca2ec)),
            this[_0x3ce98d(0x2f9)]());
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x306)] = function () {
        const _0xc57258 = _0x2a1ad3;
        if (!Sprite_MultiLayerHpFace[_0xc57258(0x27f)][_0xc57258(0x2b1)]) return;
        const _0x3f9465 = Sprite_MultiLayerHpContainer[_0xc57258(0x27f)][_0xc57258(0x229)];
        ((this['_letterSprite'] = new Sprite()),
            this[_0xc57258(0x27b)](this['_letterSprite']),
            (this[_0xc57258(0x365)]['bitmap'] = new Bitmap(_0x3f9465, _0x3f9465)),
            this[_0xc57258(0x23b)]());
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x300)] = function () {
        const _0x5c51b1 = _0x2a1ad3,
            _0x8a9181 = this[_0x5c51b1(0x2c9)][_0x5c51b1(0x328)],
            _0x1b2d08 = ColorManager[_0x5c51b1(0x24a)](this[_0x5c51b1(0x35a)][_0x5c51b1(0x1e8)]()),
            _0x2e86cd = ColorManager[_0x5c51b1(0x24a)](this[_0x5c51b1(0x35a)][_0x5c51b1(0x221)]()),
            _0x36c853 = Sprite_MultiLayerHpContainer[_0x5c51b1(0x27f)][_0x5c51b1(0x229)];
        (_0x8a9181[_0x5c51b1(0x242)](),
            _0x8a9181['gradientFillRect'](
                0x0,
                0x0,
                _0x36c853,
                _0x36c853,
                _0x1b2d08,
                _0x2e86cd,
                !![]
            ),
            _0x8a9181[_0x5c51b1(0x30c)](0x0, 0x0, _0x36c853, _0x36c853, _0x1b2d08));
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2f9)] = function () {
        const _0x5df5a0 = _0x2a1ad3,
            _0x5e2c47 = this[_0x5df5a0(0x2f6)][_0x5df5a0(0x328)],
            _0x1cfeae = '#000000',
            _0xedd29c = ColorManager[_0x5df5a0(0x24a)](this[_0x5df5a0(0x35a)][_0x5df5a0(0x36c)]()),
            _0x573176 = this[_0x5df5a0(0x35a)][_0x5df5a0(0x280)](),
            _0x50fc75 = Sprite_MultiLayerHpContainer[_0x5df5a0(0x27f)][_0x5df5a0(0x229)];
        let _0x19209b = 0x0;
        (_0x5e2c47[_0x5df5a0(0x242)](),
            _0x5e2c47[_0x5df5a0(0x292)](
                _0x19209b,
                _0x19209b,
                _0x50fc75 - _0x19209b * 0x2,
                _0x50fc75 - _0x19209b * 0x2,
                _0x1cfeae
            ),
            (_0x19209b += 0x1),
            _0x5e2c47[_0x5df5a0(0x292)](
                _0x19209b,
                _0x19209b,
                _0x50fc75 - _0x19209b * 0x2,
                _0x50fc75 - _0x19209b * 0x2,
                _0xedd29c
            ),
            (_0x19209b += _0x573176),
            _0x5e2c47[_0x5df5a0(0x292)](
                _0x19209b,
                _0x19209b,
                _0x50fc75 - _0x19209b * 0x2,
                _0x50fc75 - _0x19209b * 0x2,
                _0x1cfeae
            ),
            (_0x19209b += 0x1),
            _0x5e2c47['clearRect'](
                _0x19209b,
                _0x19209b,
                _0x50fc75 - _0x19209b * 0x2,
                _0x50fc75 - _0x19209b * 0x2
            ));
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1e6)] = function () {
        const _0x5681a8 = _0x2a1ad3;
        if (!this[_0x5681a8(0x365)]) return;
        const _0x16dd7f = this[_0x5681a8(0x365)]['bitmap'],
            _0x50de1e = this[_0x5681a8(0x2ad)];
        if (!_0x50de1e) return;
        const _0x1a23dd = Sprite_MultiLayerHpFace['SETTINGS'],
            _0x4fab95 = Sprite_MultiLayerHpContainer[_0x5681a8(0x27f)][_0x5681a8(0x229)];
        _0x16dd7f['clear']();
        if (!this[_0x5681a8(0x29a)]) return;
        ((_0x16dd7f[_0x5681a8(0x34e)] =
            _0x1a23dd[_0x5681a8(0x2c4)] || $gameSystem[_0x5681a8(0x230)]()),
            (_0x16dd7f[_0x5681a8(0x233)] = _0x1a23dd[_0x5681a8(0x21e)] || 0x10),
            _0x16dd7f[_0x5681a8(0x2c8)](
                _0x50de1e['trim'](),
                0x0,
                _0x4fab95 / 0x2,
                (_0x4fab95 * 0x7) / 0x8,
                _0x4fab95 / 0x2,
                _0x5681a8(0x201)
            ));
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2f1)] = function () {
        const _0x69620e = _0x2a1ad3;
        this[_0x69620e(0x21c)] = this[_0x69620e(0x35a)][_0x69620e(0x2c6)]();
        let _0x10d561;
        switch (this[_0x69620e(0x21c)]) {
            case 'face':
                ((this[_0x69620e(0x32a)] = this['_battler'][_0x69620e(0x364)]()),
                    (this[_0x69620e(0x283)] = this[_0x69620e(0x35a)][_0x69620e(0x358)]()),
                    (_0x10d561 = ImageManager[_0x69620e(0x25a)](this[_0x69620e(0x32a)])),
                    _0x10d561['addLoadListener'](
                        this[_0x69620e(0x31f)][_0x69620e(0x269)](this, _0x10d561)
                    ));
                break;
            case _0x69620e(0x252):
                ((this['_graphicSv'] = this[_0x69620e(0x35a)][_0x69620e(0x2fa)]()),
                    (_0x10d561 = ImageManager[_0x69620e(0x35e)](this[_0x69620e(0x303)])),
                    _0x10d561[_0x69620e(0x2e9)](
                        this['changeSvActorGraphic'][_0x69620e(0x269)](this, _0x10d561)
                    ));
                break;
            case 'svenemy':
                ((this[_0x69620e(0x245)] = this['_battler']['battlerName']()),
                    (_0x10d561 = ImageManager[_0x69620e(0x2bf)](this['_graphicEnemy'])),
                    _0x10d561[_0x69620e(0x2e9)](
                        this[_0x69620e(0x259)][_0x69620e(0x269)](this, _0x10d561)
                    ));
                break;
            case 'enemy':
                ((this[_0x69620e(0x245)] = this['_battler']['battlerName']()),
                    (_0x10d561 = ImageManager[_0x69620e(0x304)](this[_0x69620e(0x245)])),
                    _0x10d561[_0x69620e(0x2e9)](this[_0x69620e(0x259)]['bind'](this, _0x10d561)));
                break;
        }
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x31f)] = function (_0x444a0e) {
        const _0x489632 = _0x2a1ad3,
            _0x140f66 = this['_graphicSprite'][_0x489632(0x328)],
            _0x281714 = this[_0x489632(0x35a)][_0x489632(0x358)]() || 0x0,
            _0x588f1e = Sprite_MultiLayerHpContainer[_0x489632(0x27f)][_0x489632(0x229)],
            _0x1c6de6 = _0x588f1e,
            _0x51cd62 = _0x588f1e,
            _0x4103ef = ImageManager['faceWidth'],
            _0x1fd301 = ImageManager[_0x489632(0x32c)],
            _0x5f2bfe = _0x588f1e / Math[_0x489632(0x241)](_0x4103ef, _0x1fd301),
            _0x1d5477 = ImageManager[_0x489632(0x2d2)],
            _0x4eab3c = ImageManager['faceHeight'],
            _0x394a44 = (_0x281714 % 0x4) * _0x4103ef + (_0x4103ef - _0x1d5477) / 0x2,
            _0x54dd79 =
                Math[_0x489632(0x2e0)](_0x281714 / 0x4) * _0x1fd301 + (_0x1fd301 - _0x4eab3c) / 0x2,
            _0x13c089 = (_0x1c6de6 - _0x4103ef * _0x5f2bfe) / 0x2,
            _0x5ea5d5 = (_0x51cd62 - _0x1fd301 * _0x5f2bfe) / 0x2;
        (_0x140f66['clear'](),
            _0x140f66[_0x489632(0x331)](
                _0x444a0e,
                _0x394a44,
                _0x54dd79,
                _0x1d5477,
                _0x4eab3c,
                _0x13c089,
                _0x5ea5d5,
                _0x588f1e,
                _0x588f1e
            ));
    }),
    (Sprite_MultiLayerHpFace['prototype'][_0x2a1ad3(0x1fd)] = function (_0x385de6) {
        const _0x33126f = _0x2a1ad3,
            _0x1127ae = this[_0x33126f(0x21d)][_0x33126f(0x328)],
            _0x5e754b = Sprite_MultiLayerHpContainer['SETTINGS']['faceSize'],
            _0x386411 = _0x5e754b,
            _0x3297c8 = _0x5e754b,
            _0xa009a6 = this[_0x33126f(0x303)][_0x33126f(0x276)](/\$/i),
            _0x16b7f2 = _0xa009a6 ? 0x1 : ImageManager['svActorHorzCells'],
            _0x52f85c = _0xa009a6 ? 0x1 : ImageManager[_0x33126f(0x1ce)],
            _0x102f18 = _0x385de6['width'] / _0x16b7f2,
            _0x3c0019 = _0x385de6[_0x33126f(0x225)] / _0x52f85c,
            _0x37e2d8 = Math[_0x33126f(0x20f)](0x1, _0x5e754b / _0x102f18, _0x5e754b / _0x3c0019),
            _0x4bac9e = _0x102f18 * _0x37e2d8,
            _0x2d1ab5 = _0x3c0019 * _0x37e2d8,
            _0x4f24b9 = Math['round']((_0x386411 - _0x4bac9e) / 0x2),
            _0x44cef1 = Math[_0x33126f(0x2be)]((_0x3297c8 - _0x2d1ab5) / 0x2);
        (_0x1127ae[_0x33126f(0x242)](),
            _0x1127ae[_0x33126f(0x331)](
                _0x385de6,
                0x0,
                0x0,
                _0x102f18,
                _0x3c0019,
                _0x4f24b9,
                _0x44cef1,
                _0x4bac9e,
                _0x2d1ab5
            ));
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x259)] = function (_0x603d23) {
        const _0x460a1c = _0x2a1ad3,
            _0x3c6b1f = this['_graphicSprite'][_0x460a1c(0x328)],
            _0x16d300 = Sprite_MultiLayerHpContainer[_0x460a1c(0x27f)]['faceSize'],
            _0x5af4a2 = _0x16d300,
            _0xc684e4 = _0x16d300,
            _0x4b2b13 = Math[_0x460a1c(0x20f)](
                0x1,
                _0x16d300 / _0x603d23[_0x460a1c(0x228)],
                _0x16d300 / _0x603d23['height']
            ),
            _0x329716 = _0x603d23['width'] * _0x4b2b13,
            _0x3a963e = _0x603d23['height'] * _0x4b2b13,
            _0x53323e = Math[_0x460a1c(0x2be)]((_0x5af4a2 - _0x329716) / 0x2),
            _0x39f2a6 = Math[_0x460a1c(0x2be)]((_0xc684e4 - _0x3a963e) / 0x2);
        (_0x3c6b1f[_0x460a1c(0x242)](),
            _0x3c6b1f[_0x460a1c(0x331)](
                _0x603d23,
                0x0,
                0x0,
                _0x603d23[_0x460a1c(0x228)],
                _0x603d23[_0x460a1c(0x225)],
                _0x53323e,
                _0x39f2a6,
                _0x329716,
                _0x3a963e
            ));
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x21b)] = function () {
        const _0x1c50ba = _0x2a1ad3;
        Sprite['prototype']['update']['call'](this);
        if (!this[_0x1c50ba(0x35a)]) return;
        if (!this[_0x1c50ba(0x35a)]['showMultiLayerHpGauge']()) return;
        if (this[_0x1c50ba(0x2db)]) return;
        (this['updateGraphic'](), this[_0x1c50ba(0x25b)](), this['updateLetterSprite']());
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x327)] = function () {
        const _0x3d1af6 = _0x2a1ad3;
        if (!this[_0x3d1af6(0x35a)]) return;
        if (!this[_0x3d1af6(0x21d)]) return;
        if (this[_0x3d1af6(0x21c)] !== this['_battler'][_0x3d1af6(0x2c6)]())
            return this[_0x3d1af6(0x2f1)]();
        switch (this[_0x3d1af6(0x21c)]) {
            case _0x3d1af6(0x271):
                this[_0x3d1af6(0x32a)] !== this[_0x3d1af6(0x35a)][_0x3d1af6(0x364)]() &&
                    this[_0x3d1af6(0x2f1)]();
                this[_0x3d1af6(0x283)] !== this[_0x3d1af6(0x35a)][_0x3d1af6(0x358)]() &&
                    this[_0x3d1af6(0x2f1)]();
                break;
            case _0x3d1af6(0x252):
                this[_0x3d1af6(0x303)] !== this[_0x3d1af6(0x35a)][_0x3d1af6(0x2fa)]() &&
                    this[_0x3d1af6(0x2f1)]();
                break;
            case _0x3d1af6(0x323):
            case _0x3d1af6(0x360):
                this[_0x3d1af6(0x245)] !== this[_0x3d1af6(0x35a)]['battlerName']() &&
                    this['prepareGraphic']();
                break;
        }
    }),
    (Sprite_MultiLayerHpFace[_0x2a1ad3(0x35f)][_0x2a1ad3(0x25b)] = function () {
        const _0x4f9293 = _0x2a1ad3;
        if (!this[_0x4f9293(0x35a)]) return;
        if (!this[_0x4f9293(0x21d)]) return;
        if (this[_0x4f9293(0x2fb)] === this['_battler'][_0x4f9293(0x32b)]()) return;
        ((this['_graphicHue'] = this[_0x4f9293(0x35a)][_0x4f9293(0x32b)]()),
            this[_0x4f9293(0x21d)][_0x4f9293(0x23d)](
                this[_0x4f9293(0x35a)][_0x4f9293(0x26a)]() ? 0x0 : this[_0x4f9293(0x2fb)]
            ));
    }),
    (Sprite_MultiLayerHpFace['prototype']['updateLetterSprite'] = function () {
        const _0x4470bd = _0x2a1ad3;
        if (!this[_0x4470bd(0x35a)]) return;
        if (!this[_0x4470bd(0x365)]) return;
        if (
            this[_0x4470bd(0x2ad)] === this[_0x4470bd(0x35a)][_0x4470bd(0x247)] &&
            this['_lastPlural'] === this[_0x4470bd(0x35a)]['_plural']
        )
            return;
        ((this[_0x4470bd(0x2ad)] = this[_0x4470bd(0x35a)][_0x4470bd(0x247)]),
            (this[_0x4470bd(0x29a)] = this['_battler']['_plural']),
            this['drawLetterSprite']());
    }));
function Sprite_MultiLayerHpGauge() {
    const _0x2e920e = _0x2a1ad3;
    this[_0x2e920e(0x209)](...arguments);
}
((Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)] = Object[_0x2a1ad3(0x200)](
    Sprite_Gauge[_0x2a1ad3(0x35f)]
)),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x333)] = Sprite_MultiLayerHpGauge),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x27f)] = {
        show:
            VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x2ff)] ??
            !![],
        bitmapHeight: 0x20,
        gaugeHeight:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x289)] ?? 0x18,
        styleName:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x285)] ??
            'quad',
        offset: {
            x: VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Gauge'][_0x2a1ad3(0x2bb)] ?? 0x0,
            y:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x329)][_0x2a1ad3(0x255)] ??
                0x4,
        },
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x209)] = function () {
        const _0x5a4557 = _0x2a1ad3;
        Sprite_Gauge['prototype'][_0x5a4557(0x209)][_0x5a4557(0x34b)](this);
    }),
    (Sprite_MultiLayerHpGauge['prototype']['setWidth'] = function (_0x17ba8a) {
        const _0x130fe2 = _0x2a1ad3;
        ((this['_bitmapWidth'] = _0x17ba8a),
            this['createBitmap'](),
            this[_0x130fe2(0x35a)] &&
                ((this[_0x130fe2(0x268)] = -0x1),
                (this[_0x130fe2(0x263)] = -0x1),
                this[_0x130fe2(0x232)]()));
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x2ae)] = function () {
        const _0x588054 = _0x2a1ad3,
            _0x1d5624 = this[_0x588054(0x2f8)](),
            _0x304a58 = this[_0x588054(0x294)]();
        this['bitmap']
            ? (this[_0x588054(0x328)]['resize'](_0x1d5624, _0x304a58),
              (this['width'] = _0x1d5624),
              (this['height'] = _0x304a58))
            : (this[_0x588054(0x328)] = new Bitmap(_0x1d5624, _0x304a58));
    }),
    (Sprite_MultiLayerHpGauge['prototype']['bitmapHeight'] = function () {
        const _0x228123 = _0x2a1ad3;
        return Sprite_MultiLayerHpGauge[_0x228123(0x27f)][_0x228123(0x294)];
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x289)] = function () {
        const _0x576503 = _0x2a1ad3;
        return Sprite_MultiLayerHpGauge[_0x576503(0x27f)]['gaugeHeight'];
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2f8)] = function () {
        const _0xab6d8c = _0x2a1ad3;
        return this[_0xab6d8c(0x1e3)] || 0x80;
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)]['currentDisplayedValue'] = function () {
        const _0x217ff5 = _0x2a1ad3;
        let _0xac9071 = this[_0x217ff5(0x287)]();
        return (
            Imported[_0x217ff5(0x22d)] &&
                this['useDigitGrouping']() &&
                (_0xac9071 = VisuMZ[_0x217ff5(0x36f)](_0xac9071)),
            _0xac9071
        );
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2fd)] = function () {
        return 0x0;
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x1f7)] = function () {
        const _0xf1899c = _0x2a1ad3;
        return this['_battler'] ? this['_battler']['name']() : TextManager[_0xf1899c(0x302)];
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x214)] = function () {
        return 0x0;
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2f0)] = function () {
        const _0x3f92a0 = _0x2a1ad3;
        return ColorManager[_0x3f92a0(0x2a7)]();
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x2e4)] = function () {
        return this['valueOutlineColor']();
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x281)] = function () {
        return this['valueOutlineWidth']();
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x232)] = function () {
        const _0x2e1548 = _0x2a1ad3;
        if (!this['_battler']) return;
        if (!this['_battler'][_0x2e1548(0x1fe)]()) return;
        if (this[_0x2e1548(0x2db)]) return;
        Sprite_Gauge[_0x2e1548(0x35f)][_0x2e1548(0x232)][_0x2e1548(0x34b)](this);
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x1ec)] = function () {
        const _0x3e227a = _0x2a1ad3;
        (this[_0x3e227a(0x288)](),
            this[_0x3e227a(0x2da)](),
            this[_0x3e227a(0x224)](),
            Imported[_0x3e227a(0x27d)] && VisuMZ[_0x3e227a(0x278)][_0x3e227a(0x26e)]());
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x2da)] = function () {
        const _0xb0b52d = _0x2a1ad3,
            _0x2ec39a = this[_0xb0b52d(0x287)](),
            _0x1ae8fc = this[_0xb0b52d(0x1e4)](),
            _0x3d6b18 = TextManager[_0xb0b52d(0x290)][_0xb0b52d(0x1fc)],
            _0x11767f = TextManager[_0xb0b52d(0x290)][_0xb0b52d(0x23e)],
            _0x525046 = ((_0x2ec39a / _0x1ae8fc) * 0x64)[_0xb0b52d(0x1ef)](_0x11767f),
            _0x10e88e = Imported[_0xb0b52d(0x22d)] && this[_0xb0b52d(0x37a)](),
            _0xfb1cbc = _0x10e88e ? VisuMZ[_0xb0b52d(0x36f)](_0x2ec39a) : _0x2ec39a,
            _0x5caae9 = _0x10e88e ? VisuMZ[_0xb0b52d(0x36f)](_0x1ae8fc) : _0x5caae9,
            _0x4078c1 = _0x3d6b18[_0xb0b52d(0x226)](_0xfb1cbc, _0x5caae9, _0x525046),
            _0x2532f8 = this[_0xb0b52d(0x2f8)](),
            _0x1ab700 = this['textHeight'] ? this[_0xb0b52d(0x1ff)]() : this[_0xb0b52d(0x294)](),
            _0x118168 = _0x2532f8 - 0x2,
            _0x3df8df = _0x1ab700;
        (this[_0xb0b52d(0x237)](),
            (this['bitmap'][_0xb0b52d(0x33b)] = ColorManager[_0xb0b52d(0x2a7)]()),
            this[_0xb0b52d(0x328)][_0xb0b52d(0x2c8)](
                _0x4078c1,
                0x0,
                0x0,
                _0x118168,
                _0x3df8df,
                _0xb0b52d(0x201)
            ),
            (this[_0xb0b52d(0x2c0)] = this['bitmap']['measureTextWidth'](_0x4078c1)),
            Imported[_0xb0b52d(0x27d)] && VisuMZ[_0xb0b52d(0x278)]['ClearTextOffset']());
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x224)] = function () {
        const _0x42065c = _0x2a1ad3,
            _0x42875f = this['label'](),
            _0x4933d5 = this['bitmap']['measureTextWidth'](_0x42875f);
        if (_0x4933d5 + this[_0x42065c(0x2c0)] + 0x28 > this[_0x42065c(0x328)][_0x42065c(0x228)])
            return;
        const _0x29a748 = this[_0x42065c(0x2f8)](),
            _0x4f58db = this[_0x42065c(0x1ff)]
                ? this[_0x42065c(0x1ff)]()
                : this[_0x42065c(0x294)](),
            _0x512c8b = 0x4,
            _0x5c39c = 0x0,
            _0x352a27 = _0x29a748,
            _0x55de09 = _0x4f58db;
        (this['setupLabelFont'](),
            (this[_0x42065c(0x328)][_0x42065c(0x335)] = 0xff),
            this['bitmap']['drawText'](
                _0x42875f,
                _0x512c8b,
                _0x5c39c,
                _0x352a27,
                _0x55de09,
                _0x42065c(0x22c)
            ),
            Imported[_0x42065c(0x27d)] && VisuMZ['VisualGaugeStyles'][_0x42065c(0x26e)]());
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x288)] = function () {
        const _0x4828bb = _0x2a1ad3,
            _0xcb7153 = this[_0x4828bb(0x35a)][_0x4828bb(0x368)](),
            _0x3b5d5a = this[_0x4828bb(0x2f8)](),
            _0x9d5e8a = this[_0x4828bb(0x1ff)] ? this['textHeight']() : this[_0x4828bb(0x294)](),
            _0x42f444 = this[_0x4828bb(0x289)](),
            _0x1ad0ff = 0x0,
            _0x4cff5f = _0x9d5e8a - _0x42f444,
            _0x308a79 = _0x3b5d5a - _0x1ad0ff,
            _0x2adf86 = _0x42f444;
        (this[_0x4828bb(0x328)][_0x4828bb(0x242)](),
            this[_0x4828bb(0x253)](_0xcb7153, _0x1ad0ff, _0x4cff5f, _0x308a79, _0x2adf86));
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2cc)] = function () {
        const _0x1e092a = _0x2a1ad3,
            _0xb52fc4 = this['_battler']['getMultiLayerHpGaugeTotalLayers']();
        if (_0xb52fc4 <= 0x1) return this[_0x1e092a(0x35a)][_0x1e092a(0x375)]();
        const _0xb31234 = this[_0x1e092a(0x35a)][_0x1e092a(0x239)] / _0xb52fc4,
            _0x2c00ee = Math[_0x1e092a(0x2e0)](this[_0x1e092a(0x35a)]['hp'] / _0xb31234),
            _0x1e93f6 = this[_0x1e092a(0x35a)]['hp'] - _0xb31234 * _0x2c00ee;
        return _0x1e93f6 / _0xb31234;
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)]['drawFullGauge'] = function (
        _0x33b646,
        _0x4fcf48,
        _0x50655d,
        _0x1a0f3b,
        _0x23364f
    ) {
        const _0xadaf8f = _0x2a1ad3;
        if (Imported[_0xadaf8f(0x27d)]) {
            this[_0xadaf8f(0x216)](_0x33b646, _0x4fcf48, _0x50655d, _0x1a0f3b, _0x23364f);
            return;
        }
        const _0x580158 = this['gaugeBackColor']();
        (this['bitmap'][_0xadaf8f(0x292)](_0x4fcf48, _0x50655d, _0x1a0f3b, _0x23364f, _0x580158),
            (_0x4fcf48 += 0x1),
            (_0x50655d += 0x1),
            (_0x1a0f3b -= 0x2),
            (_0x23364f -= 0x2));
        const _0x5cda06 = this['gaugeRate']();
        if (_0x33b646 > 0x1 && _0x5cda06 < 0x1) {
            const _0x3b933e = ColorManager[_0xadaf8f(0x213)](_0x33b646 - 0x1),
                _0x5b0471 = ColorManager['getMultiLayerHpGaugeColor2'](_0x33b646 - 0x1);
            this[_0xadaf8f(0x328)][_0xadaf8f(0x33c)](
                _0x4fcf48,
                _0x50655d,
                _0x1a0f3b,
                _0x23364f,
                _0x3b933e,
                _0x5b0471
            );
        }
        const _0x33d5b1 = Math[_0xadaf8f(0x2e0)](_0x1a0f3b * _0x5cda06);
        _0x33b646 > 0x1 &&
            this[_0xadaf8f(0x328)]['fillRect'](
                _0x4fcf48,
                _0x50655d,
                _0x33d5b1 + 0x1,
                _0x23364f,
                _0x580158
            );
        const _0xb02a5a = ColorManager[_0xadaf8f(0x213)](_0x33b646),
            _0x1a3dcf = ColorManager[_0xadaf8f(0x325)](_0x33b646);
        this[_0xadaf8f(0x328)][_0xadaf8f(0x33c)](
            _0x4fcf48,
            _0x50655d,
            _0x33d5b1,
            _0x23364f,
            _0xb02a5a,
            _0x1a3dcf
        );
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)]['maxHpSegmentAmount'] = function () {
        const _0x1f0b92 = _0x2a1ad3,
            _0x21f8bd = this[_0x1f0b92(0x35a)][_0x1f0b92(0x345)]();
        return this[_0x1f0b92(0x35a)]['mhp'] / Math[_0x1f0b92(0x241)](0x1, _0x21f8bd);
    }),
    (Sprite_MultiLayerHpGauge['prototype'][_0x2a1ad3(0x216)] = function (
        _0x52dfcf,
        _0x5cb3f5,
        _0x2c544a,
        _0x18a8ed,
        _0x315f34
    ) {
        const _0x50f5ef = _0x2a1ad3,
            _0x40fe27 = this[_0x50f5ef(0x285)]();
        VisuMZ[_0x50f5ef(0x278)]['_maxValueSegment'] = this[_0x50f5ef(0x20b)]();
        const _0x2d193f = VisuMZ[_0x50f5ef(0x278)][_0x50f5ef(0x359)](
                _0x40fe27,
                _0x5cb3f5,
                _0x2c544a,
                _0x18a8ed,
                _0x315f34,
                0x1,
                !![]
            ),
            _0x4bf6e5 = this['gaugeBackColor']();
        this['bitmap'][_0x50f5ef(0x202)](_0x2d193f, _0x4bf6e5);
        const _0x2b4598 = this[_0x50f5ef(0x2cc)]();
        if (_0x52dfcf > 0x1 && _0x2b4598 < 0x1) {
            const _0x121a52 = ColorManager[_0x50f5ef(0x213)](_0x52dfcf - 0x1),
                _0x3daae7 = ColorManager[_0x50f5ef(0x325)](_0x52dfcf - 0x1),
                _0x18a9ce = VisuMZ[_0x50f5ef(0x278)][_0x50f5ef(0x359)](
                    _0x40fe27,
                    _0x5cb3f5,
                    _0x2c544a,
                    _0x18a8ed,
                    _0x315f34,
                    0x1,
                    ![]
                ),
                _0x59df51 = this[_0x50f5ef(0x328)]['_context'][_0x50f5ef(0x33a)](
                    _0x5cb3f5,
                    _0x2c544a,
                    _0x5cb3f5 + _0x18a8ed,
                    _0x2c544a
                );
            this[_0x50f5ef(0x328)][_0x50f5ef(0x36e)](_0x18a9ce, _0x121a52, _0x3daae7, _0x59df51);
        }
        const _0x3411a2 = ColorManager[_0x50f5ef(0x213)](_0x52dfcf),
            _0x5f3ffc = ColorManager[_0x50f5ef(0x325)](_0x52dfcf),
            _0x3f72ea = this[_0x50f5ef(0x328)][_0x50f5ef(0x2d1)][_0x50f5ef(0x33a)](
                _0x5cb3f5,
                _0x2c544a,
                _0x5cb3f5 + _0x18a8ed,
                _0x2c544a
            ),
            _0x1e786b = VisuMZ[_0x50f5ef(0x278)][_0x50f5ef(0x359)](
                _0x40fe27,
                _0x5cb3f5,
                _0x2c544a,
                _0x18a8ed,
                _0x315f34,
                _0x2b4598,
                ![]
            );
        this['bitmap'][_0x50f5ef(0x36e)](_0x1e786b, _0x3411a2, _0x5f3ffc, _0x3f72ea, _0x4bf6e5);
    }),
    (Sprite_MultiLayerHpGauge[_0x2a1ad3(0x35f)][_0x2a1ad3(0x29f)] = function () {
        const _0x41ce86 = _0x2a1ad3;
        return Sprite_MultiLayerHpGauge[_0x41ce86(0x27f)][_0x41ce86(0x285)];
    }));
function Sprite_MultiLayerHpStates() {
    const _0x32f5d8 = _0x2a1ad3;
    this[_0x32f5d8(0x209)](...arguments);
}
((Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)] = Object[_0x2a1ad3(0x200)](Sprite[_0x2a1ad3(0x35f)])),
    (Sprite_MultiLayerHpStates['prototype'][_0x2a1ad3(0x333)] = Sprite_MultiLayerHpStates),
    (Sprite_MultiLayerHpStates['SETTINGS'] = {
        show:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x21a)][_0x2a1ad3(0x2ff)] ?? !![],
        breakShields:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x21a)][_0x2a1ad3(0x353)] ?? !![],
        offset: {
            x: VisuMZ['MultiLayerHpGauge']['Settings']['States'][_0x2a1ad3(0x2bb)] ?? 0x0,
            y:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x21a)][_0x2a1ad3(0x255)] ??
                0x1c,
        },
    }),
    (Sprite_MultiLayerHpStates['prototype']['initialize'] = function (_0x371322) {
        const _0x30814c = _0x2a1ad3;
        ((this[_0x30814c(0x35a)] = _0x371322),
            Sprite[_0x30814c(0x35f)][_0x30814c(0x209)][_0x30814c(0x34b)](this),
            this['createDrawWindow'](),
            this[_0x30814c(0x2ae)](),
            this[_0x30814c(0x35a)][_0x30814c(0x30d)]());
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x318)] = function () {
        const _0x52c028 = _0x2a1ad3,
            _0x31dd0b = {
                x: 0x0,
                y: 0x0,
                width: Graphics[_0x52c028(0x228)],
                height: SceneManager['_scene'][_0x52c028(0x30f)](0x1, ![]),
            };
        this[_0x52c028(0x258)] = new Window_MultiLayerHpGaugeStatusBase(_0x31dd0b);
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2ae)] = function () {
        const _0x31e972 = _0x2a1ad3,
            _0x2a2d6d = Graphics[_0x31e972(0x228)],
            _0x2e3c61 = ImageManager['iconHeight'];
        this['bitmap'] = new Bitmap(_0x2a2d6d, _0x2e3c61);
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x26b)] = function (_0x45d0f7) {
        const _0x29c86f = _0x2a1ad3;
        (this[_0x29c86f(0x208)](0x0, 0x0, _0x45d0f7, ImageManager[_0x29c86f(0x203)]),
            (this['width'] = _0x45d0f7),
            (this['_frameWidth'] = _0x45d0f7));
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x21b)] = function () {
        const _0x16feec = _0x2a1ad3;
        Sprite[_0x16feec(0x35f)]['update'][_0x16feec(0x34b)](this);
        if (!this[_0x16feec(0x35a)]) return;
        if (!this[_0x16feec(0x35a)][_0x16feec(0x1fe)]()) return;
        if (this[_0x16feec(0x2db)]) return;
        (this[_0x16feec(0x2b6)](), this['updateBreakShieldIcon']());
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)]['checkUpdateRequests'] = function () {
        const _0x536bca = _0x2a1ad3;
        this[_0x536bca(0x35a)][_0x536bca(0x2de)] &&
            ((this[_0x536bca(0x35a)][_0x536bca(0x2de)] = undefined), this[_0x536bca(0x324)]());
    }),
    (Sprite_MultiLayerHpStates['prototype'][_0x2a1ad3(0x30e)] = function () {
        const _0x47d6d8 = _0x2a1ad3;
        if (!this['_breakShieldSprite']) return;
        const _0x2c5dc8 = Game_Battler[_0x47d6d8(0x338)];
        if (_0x2c5dc8 <= 0x0) return;
        this[_0x47d6d8(0x35a)][_0x47d6d8(0x351)](_0x2c5dc8)
            ? (this[_0x47d6d8(0x2e6)][_0x47d6d8(0x26f)] = 0x0)
            : (this[_0x47d6d8(0x2e6)]['opacity'] = 0xff);
    }),
    (Game_BattlerBase[_0x2a1ad3(0x35f)]['requestMultiLayerHpGaugeStateUpdate'] = function () {}),
    (Game_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x30d)] = function () {
        const _0x178e49 = _0x2a1ad3;
        this[_0x178e49(0x1fe)]() && (this[_0x178e49(0x2de)] = !![]);
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2ca)] = Game_BattlerBase['prototype'][_0x2a1ad3(0x260)]),
    (Game_BattlerBase[_0x2a1ad3(0x35f)][_0x2a1ad3(0x260)] = function () {
        const _0x4f4fe8 = _0x2a1ad3;
        (VisuMZ['MultiLayerHpGauge'][_0x4f4fe8(0x2ca)][_0x4f4fe8(0x34b)](this),
            this['requestMultiLayerHpGaugeStateUpdate']());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x32e)] = Game_Battler[_0x2a1ad3(0x35f)][_0x2a1ad3(0x36b)]),
    (Game_Battler[_0x2a1ad3(0x35f)][_0x2a1ad3(0x36b)] = function (_0x48b815) {
        const _0x518cee = _0x2a1ad3;
        (VisuMZ['MultiLayerHpGauge'][_0x518cee(0x32e)][_0x518cee(0x34b)](this, _0x48b815),
            this['requestMultiLayerHpGaugeStateUpdate']());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)]['Game_Battler_addState'] =
        Game_Battler[_0x2a1ad3(0x35f)][_0x2a1ad3(0x23f)]),
    (Game_Battler['prototype'][_0x2a1ad3(0x23f)] = function (_0x49ee50) {
        const _0x46919e = _0x2a1ad3;
        (VisuMZ[_0x46919e(0x28e)][_0x46919e(0x2a5)][_0x46919e(0x34b)](this, _0x49ee50),
            this['requestMultiLayerHpGaugeStateUpdate']());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2ac)] = Game_Battler[_0x2a1ad3(0x35f)]['removeState']),
    (Game_Battler[_0x2a1ad3(0x35f)][_0x2a1ad3(0x312)] = function (_0x3de015) {
        const _0x4548e2 = _0x2a1ad3;
        (VisuMZ['MultiLayerHpGauge'][_0x4548e2(0x2ac)][_0x4548e2(0x34b)](this, _0x3de015),
            this[_0x4548e2(0x30d)]());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2f7)] =
        Game_BattlerBase[_0x2a1ad3(0x35f)][_0x2a1ad3(0x1fb)]),
    (Game_BattlerBase['prototype'][_0x2a1ad3(0x1fb)] = function () {
        (VisuMZ['MultiLayerHpGauge']['Game_BattlerBase_clearStates']['call'](this),
            this['requestMultiLayerHpGaugeStateUpdate']());
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x301)] = Game_Battler[_0x2a1ad3(0x35f)]['onTurnEnd']),
    (Game_Battler['prototype'][_0x2a1ad3(0x28d)] = function () {
        const _0x438767 = _0x2a1ad3;
        (VisuMZ[_0x438767(0x28e)][_0x438767(0x301)]['call'](this), this[_0x438767(0x30d)]());
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x324)] = function () {
        const _0x16664b = _0x2a1ad3;
        (this['clearBitmaps'](),
            this[_0x16664b(0x250)](),
            this[_0x16664b(0x1f8)](),
            this[_0x16664b(0x36a)]());
    }),
    (Sprite_MultiLayerHpStates['prototype']['clearBitmaps'] = function () {
        const _0x22d391 = _0x2a1ad3;
        (this[_0x22d391(0x328)][_0x22d391(0x242)](),
            this['_dummyWindow'][_0x22d391(0x265)][_0x22d391(0x242)]());
    }),
    (Sprite_MultiLayerHpStates['prototype'][_0x2a1ad3(0x250)] = function () {
        const _0x449f5f = _0x2a1ad3,
            _0x31bdbe = this[_0x449f5f(0x258)]['innerWidth'];
        this[_0x449f5f(0x258)][_0x449f5f(0x2b8)](this[_0x449f5f(0x35a)], 0x0, 0x0, _0x31bdbe);
    }),
    (Sprite_MultiLayerHpStates['prototype'][_0x2a1ad3(0x1f8)] = function () {
        const _0x4dc385 = _0x2a1ad3;
        if (!this[_0x4dc385(0x35a)]) return;
        if (!Imported[_0x4dc385(0x234)]) return;
        if (!Game_Battler[_0x4dc385(0x1f9)]) return;
        if (!Sprite_MultiLayerHpStates[_0x4dc385(0x27f)][_0x4dc385(0x353)]) return;
        if (this['_breakShieldSprite']) return;
        ((this[_0x4dc385(0x2e6)] = new Sprite_BreakShieldIcon()),
            this[_0x4dc385(0x27b)](this[_0x4dc385(0x2e6)]),
            this['_breakShieldSprite'][_0x4dc385(0x27e)](this[_0x4dc385(0x35a)], ![]),
            this[_0x4dc385(0x2e6)]['move'](
                ImageManager[_0x4dc385(0x243)] / 0x2,
                ImageManager[_0x4dc385(0x203)] / 0x2 + 0x2
            ),
            this[_0x4dc385(0x2e6)][_0x4dc385(0x2ff)]());
    }),
    (Sprite_MultiLayerHpStates[_0x2a1ad3(0x35f)][_0x2a1ad3(0x36a)] = function () {
        const _0x10790c = _0x2a1ad3;
        this[_0x10790c(0x328)] = this[_0x10790c(0x258)]['contents'];
        if (this[_0x10790c(0x2d7)]) {
            const _0x1d063a =
                Math[_0x10790c(0x2e0)](this[_0x10790c(0x2d7)] / ImageManager[_0x10790c(0x243)]) *
                ImageManager[_0x10790c(0x243)];
            this[_0x10790c(0x208)](0x0, 0x0, _0x1d063a, this[_0x10790c(0x328)][_0x10790c(0x225)]);
        }
    }),
    (Window_BattleLog[_0x2a1ad3(0x290)] = {
        reposition:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['General']['repositionBattleLog'] ?? !![],
        perRowOffsetY:
            VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x22b)][_0x2a1ad3(0x31c)] ?? 0x40,
    }),
    (Window_BattleLog[_0x2a1ad3(0x35f)][_0x2a1ad3(0x2c7)] = function () {
        const _0x4f9fa9 = _0x2a1ad3;
        this[_0x4f9fa9(0x321)] = this['y'];
    }),
    (VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2dc)] = Window_BattleLog[_0x2a1ad3(0x35f)]['update']),
    (Window_BattleLog[_0x2a1ad3(0x35f)]['update'] = function () {
        const _0x2614ec = _0x2a1ad3;
        (VisuMZ[_0x2614ec(0x28e)]['Window_BattleLog_update'][_0x2614ec(0x34b)](this),
            this[_0x2614ec(0x33e)]());
    }),
    (Window_BattleLog[_0x2a1ad3(0x35f)][_0x2a1ad3(0x33e)] = function () {
        const _0x4f58fb = _0x2a1ad3;
        if (!Window_BattleLog[_0x4f58fb(0x290)][_0x4f58fb(0x204)]) return;
        if (this['_multiLayerHpGaugePositionY'] === undefined) return;
        let _0x403b75 = this[_0x4f58fb(0x321)];
        const _0x1f6b25 = $gameTroop['totalVisibleMultiLayerHpGaugeRows']();
        (_0x1f6b25 > 0x0 &&
            (_0x403b75 += Window_BattleLog[_0x4f58fb(0x290)]['perRowOffsetY'] * _0x1f6b25),
            (this['y'] = _0x403b75));
    }));
function Window_MultiLayerHpGaugeStatusBase() {
    this['initialize'](...arguments);
}
((Window_MultiLayerHpGaugeStatusBase[_0x2a1ad3(0x35f)] = Object['create'](
    Window_StatusBase[_0x2a1ad3(0x35f)]
)),
    (Window_MultiLayerHpGaugeStatusBase[_0x2a1ad3(0x35f)]['constructor'] =
        Window_MultiLayerHpGaugeStatusBase),
    (Window_MultiLayerHpGaugeStatusBase[_0x2a1ad3(0x35f)][_0x2a1ad3(0x209)] = function (_0x2a74da) {
        const _0x38b68d = _0x2a1ad3;
        Window_StatusBase[_0x38b68d(0x35f)]['initialize'][_0x38b68d(0x34b)](this, _0x2a74da);
    }),
    (Window_MultiLayerHpGaugeStatusBase['prototype']['itemHeight'] = function () {
        const _0x1795aa = _0x2a1ad3;
        return Window_Scrollable[_0x1795aa(0x35f)][_0x1795aa(0x349)][_0x1795aa(0x34b)](this);
    }),
    (Window_MultiLayerHpGaugeStatusBase[_0x2a1ad3(0x35f)]['shouldDisplayBreakShields'] = function (
        _0x28f742
    ) {
        const _0x5cc892 = _0x2a1ad3;
        if (!Sprite_MultiLayerHpStates[_0x5cc892(0x27f)]['breakShields']) return ![];
        if (!Game_Battler[_0x5cc892(0x1f9)]) return ![];
        const _0x3281f9 = Game_Battler['BREAK_SHIELDS_STUN_STATE'];
        if (_0x28f742[_0x5cc892(0x351)](_0x3281f9) && $dataStates[_0x3281f9]['iconIndex'] > 0x0)
            return ![];
        if (
            _0x28f742['isDead']() &&
            $dataStates[_0x28f742[_0x5cc892(0x2a9)]()][_0x5cc892(0x2b9)] > 0x0
        )
            return ![];
        return !![];
    }),
    (Window_MultiLayerHpGaugeStatusBase[_0x2a1ad3(0x35f)][_0x2a1ad3(0x352)] = function (
        _0x16c42b,
        _0x30bf51,
        _0x2e5369
    ) {}),
    (VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x219)] = {
        battler: {
            reduceRedundancy: {
                hpGauge:
                    VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x219)][_0x2a1ad3(0x2d4)] ??
                    !![],
                stateIcon:
                    VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x219)][
                        'reduceRedundantStateIcon'
                    ] ?? !![],
                breakShields:
                    VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][
                        _0x2a1ad3(0x32f)
                    ] ?? !![],
            },
        },
        atb: {
            eachRowOffsetY:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x37b)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x25f)] ??
                +0x18,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x219)]['atbHelpOffsetY'] ?? +0xc,
        },
        btb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x236)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x343)] ??
                +0x0,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x297)] ??
                +0xc,
        },
        ctb: {
            eachRowOffsetY:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x2b5)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)]['ctbNormalOffsetY'] ??
                +0x0,
            helpOffsetY:
                VisuMZ['MultiLayerHpGauge']['Settings'][_0x2a1ad3(0x219)]['ctbHelpOffsetY'] ?? +0xc,
        },
        etb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x286)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)]['etbNormalOffsetY'] ??
                +0x0,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x219)][_0x2a1ad3(0x320)] ?? -0x38,
        },
        ftb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Compatibility']['ftbEachRowOffsetY'] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)]['ftbNormalOffsetY'] ??
                +0x0,
            helpOffsetY:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)]['ftbHelpOffsetY'] ??
                -0x38,
        },
        otb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Compatibility'][_0x2a1ad3(0x2ef)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x24d)] ??
                -0x6,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x2b2)] ??
                -0xc,
        },
        ptb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Compatibility'][_0x2a1ad3(0x310)] ??
                +0x40,
            normalOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings'][_0x2a1ad3(0x219)]['ptbNormalOffsetY'] ?? +0x0,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)]['Settings']['Compatibility'][_0x2a1ad3(0x298)] ?? -0x38,
        },
        stb: {
            eachRowOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)]['Compatibility']['stbEachRowOffsetY'] ??
                +0x40,
            normalOffsetY:
                VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][
                    'stbNormalOffsetY'
                ] ?? +0x0,
            helpOffsetY:
                VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x326)][_0x2a1ad3(0x219)][_0x2a1ad3(0x2c2)] ??
                +0xc,
        },
    }),
    (VisuMZ[_0x2a1ad3(0x28e)]['Sprite_Battler_isVisualHpGaugeDisplayed'] =
        Sprite_Battler[_0x2a1ad3(0x35f)]['isVisualHpGaugeDisplayed']),
    (Sprite_Battler[_0x2a1ad3(0x35f)][_0x2a1ad3(0x367)] = function () {
        const _0x393174 = _0x2a1ad3;
        if (this['_battler'] && this[_0x393174(0x35a)][_0x393174(0x22a)]()) {
            const _0x53e06b =
                VisuMZ[_0x393174(0x28e)]['Compatibility'][_0x393174(0x342)][_0x393174(0x1cf)];
            if (
                this['_battler']['showMultiLayerHpGauge']() &&
                _0x53e06b[_0x393174(0x29d)] &&
                Sprite_MultiLayerHpGauge['SETTINGS']['show']
            )
                return ![];
        }
        return VisuMZ[_0x393174(0x28e)]['Sprite_Battler_isVisualHpGaugeDisplayed']['call'](this);
    }),
    (VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x222)] =
        Sprite_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x266)]),
    (Sprite_Enemy[_0x2a1ad3(0x35f)][_0x2a1ad3(0x266)] = function () {
        const _0x4581c1 = _0x2a1ad3;
        VisuMZ[_0x4581c1(0x28e)][_0x4581c1(0x222)][_0x4581c1(0x34b)](this);
        if (this[_0x4581c1(0x35a)] && this[_0x4581c1(0x1f3)]) {
            const _0x462c0d =
                VisuMZ[_0x4581c1(0x28e)]['Compatibility'][_0x4581c1(0x342)]['reduceRedundancy'];
            _0x462c0d[_0x4581c1(0x348)] &&
                Sprite_MultiLayerHpStates[_0x4581c1(0x27f)]['show'] &&
                (this['_stateIconSprite']['y'] = Graphics[_0x4581c1(0x225)] * 0xa);
        }
    }));
Imported[_0x2a1ad3(0x27a)] &&
    ((VisuMZ['MultiLayerHpGauge'][_0x2a1ad3(0x279)] =
        Sprite_FieldGaugeATB[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)]),
    (Sprite_FieldGaugeATB[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x19d709 = _0x2a1ad3;
        VisuMZ[_0x19d709(0x28e)][_0x19d709(0x279)][_0x19d709(0x34b)](this);
        if (Sprite_FieldGaugeATB[_0x19d709(0x326)][_0x19d709(0x267)] !== _0x19d709(0x28b)) return;
        const _0x2aad44 = $gameTroop[_0x19d709(0x339)]();
        if (_0x2aad44 <= 0x0) return;
        const _0x10bfd0 = VisuMZ[_0x19d709(0x28e)]['Compatibility']['atb'],
            _0x259509 = _0x10bfd0[_0x19d709(0x2eb)];
        let _0x35ff70 = _0x259509 * _0x2aad44;
        const _0x1fcb5a = SceneManager[_0x19d709(0x1fa)][_0x19d709(0x296)];
        (_0x1fcb5a &&
        _0x1fcb5a[_0x19d709(0x313)] &&
        Sprite_FieldGaugeATB[_0x19d709(0x326)][_0x19d709(0x251)]
            ? (_0x35ff70 += _0x10bfd0[_0x19d709(0x311)])
            : (_0x35ff70 += _0x10bfd0[_0x19d709(0x20a)]),
            (this['y'] += _0x35ff70));
    }));
function _0x1480(_0xf88255, _0x4888d9) {
    const _0x5f0227 = _0x5f02();
    return (
        (_0x1480 = function (_0x148012, _0x56ac9c) {
            _0x148012 = _0x148012 - 0x1cb;
            let _0x5bd3a5 = _0x5f0227[_0x148012];
            return _0x5bd3a5;
        }),
        _0x1480(_0xf88255, _0x4888d9)
    );
}
Imported[_0x2a1ad3(0x308)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x217)] =
        Window_BTB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)]),
    (Window_BTB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x361b6d = _0x2a1ad3;
        VisuMZ['MultiLayerHpGauge'][_0x361b6d(0x217)]['call'](this);
        if (Window_BTB_TurnOrder['Settings'][_0x361b6d(0x267)] !== _0x361b6d(0x28b)) return;
        const _0x2801dd = $gameTroop[_0x361b6d(0x339)]();
        if (_0x2801dd <= 0x0) return;
        const _0x4e86ae = VisuMZ['MultiLayerHpGauge'][_0x361b6d(0x219)]['btb'],
            _0x1a2d54 = _0x4e86ae[_0x361b6d(0x2eb)];
        let _0xbf126d = _0x1a2d54 * _0x2801dd;
        const _0x3badee = SceneManager[_0x361b6d(0x1fa)][_0x361b6d(0x296)];
        (_0x3badee &&
        _0x3badee[_0x361b6d(0x313)] &&
        Window_BTB_TurnOrder[_0x361b6d(0x326)]['RepositionTopForHelp']
            ? (_0xbf126d += _0x4e86ae[_0x361b6d(0x311)])
            : (_0xbf126d += _0x4e86ae[_0x361b6d(0x20a)]),
            (this['y'] += _0xbf126d));
    }));
Imported[_0x2a1ad3(0x341)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x369)] =
        Window_CTB_TurnOrder[_0x2a1ad3(0x35f)]['updatePosition']),
    (Window_CTB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x41472d = _0x2a1ad3;
        VisuMZ['MultiLayerHpGauge'][_0x41472d(0x369)][_0x41472d(0x34b)](this);
        if (Window_CTB_TurnOrder[_0x41472d(0x326)][_0x41472d(0x267)] !== _0x41472d(0x28b)) return;
        const _0x2b98c9 = $gameTroop[_0x41472d(0x339)]();
        if (_0x2b98c9 <= 0x0) return;
        const _0x376eb2 = VisuMZ[_0x41472d(0x28e)][_0x41472d(0x219)][_0x41472d(0x361)],
            _0x56ae85 = _0x376eb2['eachRowOffsetY'];
        let _0x579872 = _0x56ae85 * _0x2b98c9;
        const _0x32e316 = SceneManager['_scene']['_helpWindow'];
        (_0x32e316 &&
        _0x32e316[_0x41472d(0x313)] &&
        Window_CTB_TurnOrder['Settings'][_0x41472d(0x251)]
            ? (_0x579872 += _0x376eb2['helpOffsetY'])
            : (_0x579872 += _0x376eb2[_0x41472d(0x20a)]),
            (this['y'] += _0x579872));
    }));
Imported[_0x2a1ad3(0x205)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x26c)] =
        Window_ETB_ActionCount['prototype'][_0x2a1ad3(0x27c)]),
    (Window_ETB_ActionCount['prototype'][_0x2a1ad3(0x27c)] = function () {
        const _0x4f7055 = _0x2a1ad3;
        VisuMZ[_0x4f7055(0x28e)][_0x4f7055(0x26c)][_0x4f7055(0x34b)](this);
        if (Window_ETB_ActionCount[_0x4f7055(0x326)][_0x4f7055(0x20e)]) return;
        const _0x18bcc9 = $gameTroop[_0x4f7055(0x339)]();
        if (_0x18bcc9 <= 0x0) return;
        const _0x387970 = VisuMZ[_0x4f7055(0x28e)][_0x4f7055(0x219)][_0x4f7055(0x2d0)],
            _0x77d886 = _0x387970[_0x4f7055(0x2eb)];
        let _0x532d12 = _0x77d886 * _0x18bcc9;
        const _0x4c2135 = SceneManager[_0x4f7055(0x1fa)][_0x4f7055(0x296)];
        (_0x4c2135 &&
        _0x4c2135[_0x4f7055(0x313)] &&
        Window_ETB_ActionCount[_0x4f7055(0x326)][_0x4f7055(0x251)]
            ? (_0x532d12 += _0x387970[_0x4f7055(0x311)])
            : (_0x532d12 += _0x387970[_0x4f7055(0x20a)]),
            (this['y'] += _0x532d12));
    }));
Imported[_0x2a1ad3(0x1eb)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2e5)] =
        Window_FTB_ActionCount[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)]),
    (Window_FTB_ActionCount['prototype'][_0x2a1ad3(0x27c)] = function () {
        const _0x13e58a = _0x2a1ad3;
        VisuMZ[_0x13e58a(0x28e)][_0x13e58a(0x2e5)]['call'](this);
        if (Window_FTB_ActionCount[_0x13e58a(0x326)][_0x13e58a(0x20e)]) return;
        const _0x2de3ed = $gameTroop[_0x13e58a(0x339)]();
        if (_0x2de3ed <= 0x0) return;
        const _0x22b08b = VisuMZ[_0x13e58a(0x28e)][_0x13e58a(0x219)]['ftb'],
            _0x4fbbda = _0x22b08b[_0x13e58a(0x2eb)];
        let _0x1eb47a = _0x4fbbda * _0x2de3ed;
        const _0x49242d = SceneManager[_0x13e58a(0x1fa)][_0x13e58a(0x296)];
        (_0x49242d &&
        _0x49242d[_0x13e58a(0x313)] &&
        Window_FTB_ActionCount[_0x13e58a(0x326)][_0x13e58a(0x251)]
            ? (_0x1eb47a += _0x22b08b[_0x13e58a(0x311)])
            : (_0x1eb47a += _0x22b08b[_0x13e58a(0x20a)]),
            (this['y'] += _0x1eb47a));
    }));
Imported[_0x2a1ad3(0x1d5)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x347)] =
        Window_OTB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)]),
    (Window_OTB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x8431f5 = _0x2a1ad3;
        VisuMZ[_0x8431f5(0x28e)][_0x8431f5(0x347)]['call'](this);
        if (Window_OTB_TurnOrder[_0x8431f5(0x326)][_0x8431f5(0x267)] !== _0x8431f5(0x28b)) return;
        const _0x8bd12b = $gameTroop['totalVisibleMultiLayerHpGaugeRows']();
        if (_0x8bd12b <= 0x0) return;
        const _0x44b527 = VisuMZ[_0x8431f5(0x28e)]['Compatibility'][_0x8431f5(0x1d6)],
            _0x4f071c = _0x44b527[_0x8431f5(0x2eb)];
        let _0x1003fd = _0x4f071c * _0x8bd12b;
        const _0x42c437 = SceneManager['_scene'][_0x8431f5(0x296)];
        (_0x42c437 &&
        _0x42c437[_0x8431f5(0x313)] &&
        Window_OTB_TurnOrder[_0x8431f5(0x326)][_0x8431f5(0x251)]
            ? (_0x1003fd += _0x44b527['helpOffsetY'])
            : (_0x1003fd += _0x44b527[_0x8431f5(0x20a)]),
            (this['y'] += _0x1003fd));
    }));
Imported[_0x2a1ad3(0x1d3)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x28a)] =
        Window_PTB_ActionCount[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)]),
    (Window_PTB_ActionCount[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x56d36a = _0x2a1ad3;
        VisuMZ['MultiLayerHpGauge'][_0x56d36a(0x28a)][_0x56d36a(0x34b)](this);
        if (Window_PTB_ActionCount['Settings'][_0x56d36a(0x20e)]) return;
        const _0x4f4cac = $gameTroop['totalVisibleMultiLayerHpGaugeRows']();
        if (_0x4f4cac <= 0x0) return;
        const _0x1e941d = VisuMZ['MultiLayerHpGauge'][_0x56d36a(0x219)][_0x56d36a(0x334)],
            _0x9f7464 = _0x1e941d[_0x56d36a(0x2eb)];
        let _0x1c52ae = _0x9f7464 * _0x4f4cac;
        const _0x1690ca = SceneManager[_0x56d36a(0x1fa)][_0x56d36a(0x296)];
        (_0x1690ca &&
        _0x1690ca[_0x56d36a(0x313)] &&
        Window_PTB_ActionCount[_0x56d36a(0x326)][_0x56d36a(0x251)]
            ? (_0x1c52ae += _0x1e941d['helpOffsetY'])
            : (_0x1c52ae += _0x1e941d[_0x56d36a(0x20a)]),
            (this['y'] += _0x1c52ae));
    }));
Imported[_0x2a1ad3(0x2a1)] &&
    ((VisuMZ[_0x2a1ad3(0x28e)][_0x2a1ad3(0x2ed)] =
        Window_STB_TurnOrder['prototype'][_0x2a1ad3(0x27c)]),
    (Window_STB_TurnOrder[_0x2a1ad3(0x35f)][_0x2a1ad3(0x27c)] = function () {
        const _0x5932c8 = _0x2a1ad3;
        VisuMZ[_0x5932c8(0x28e)][_0x5932c8(0x2ed)][_0x5932c8(0x34b)](this);
        if (Window_STB_TurnOrder[_0x5932c8(0x326)][_0x5932c8(0x267)] !== _0x5932c8(0x28b)) return;
        const _0x596ea5 = $gameTroop[_0x5932c8(0x339)]();
        if (_0x596ea5 <= 0x0) return;
        const _0x196976 = VisuMZ[_0x5932c8(0x28e)]['Compatibility'][_0x5932c8(0x24e)],
            _0x44681f = _0x196976[_0x5932c8(0x2eb)];
        let _0x868529 = _0x44681f * _0x596ea5;
        const _0x31b195 = SceneManager[_0x5932c8(0x1fa)][_0x5932c8(0x296)];
        (_0x31b195 &&
        _0x31b195[_0x5932c8(0x313)] &&
        Window_STB_TurnOrder[_0x5932c8(0x326)][_0x5932c8(0x251)]
            ? (_0x868529 += _0x196976['helpOffsetY'])
            : (_0x868529 += _0x196976[_0x5932c8(0x20a)]),
            (this['y'] += _0x868529));
    }));
