//=============================================================================
// VisuStella MZ - Visual Item Inventory
// VisuMZ_4_VisualItemInv.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_VisualItemInv = true;

var VisuMZ = VisuMZ || {};
VisuMZ.VisualItemInv = VisuMZ.VisualItemInv || {};
VisuMZ.VisualItemInv.version = 1.06;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.06] [VisualItemInv]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Visual_Item_Inventory_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 * @orderAfter VisuMZ_1_ItemsEquipsCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin changes the item list displayed in-game to become more visual
 * and show bigger images, either as icons or pictures. The enlarged item,
 * weapon, and armor images will show their item quantities next to them while
 * a tooltip window appears above their selected cell to show the item's name.
 *
 * Features include all (but not limited to) the following:
 *
 * * Changes the item inventory windows to become more visual.
 * * Enlarged item images can be either icons or picture images.
 * * Alter how large you want the images to appear with the Plugin Parameters.
 * * Add different color backgrounds for different items.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Window Columns and Spacing
 *
 * It should come off as no surprise that these windows will have their usual
 * column counts changed to adjust for the item images shown. The columns will
 * be based on how many of the item icons can fit inside of the window.
 *
 * ---
 *
 * Item Quantity Positioning
 *
 * The item quantity will now be positioned to show in the lower right of any
 * window cell with an enlarged icon. Due to this being a much smaller area
 * than what is usually provided, some plugins may have incredibly squished
 * appearances when it comes to displaying item quantity in some areas.
 *
 * This needs to be adjusted in those plugins individually.
 *
 * ---
 *
 * Items and Equips Core
 *
 * For the Equip Menu, the remove item entry has been changed to show only the
 * enlarged icon. This is to keep consistency with the rest of the plugin.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Picture-Related Notetags ===
 *
 * ---
 *
 * <Visual Item Picture: filename>
 * <Picture: filename>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - Uses a picture from your project's /img/pictures/ folder instead of the
 *   item's icon inside the item windows instead.
 * - Replace 'filename' with the filename of the image.
 *   - Do not include the file extension.
 * - Scaling will not apply to the picture.
 * - Use the <Picture: filename> version for any other plugins that may be
 *   using this as an image outside of this plugin, too.
 * - The size used for the image will vary based on the icon size settings.
 *
 * ---
 *
 * === Background Colors-Related Notetags ===
 *
 * ---
 *
 * <Visual Item BG Color 1: x>
 * <Visual Item BG Color 2: x>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - Changes the background color(s) for the item to text color 'x'.
 * - Replace 'x' with a number from 0 to 31 to represent a text color.
 *
 * ---
 *
 * <Visual Item BG Color 1: #rrggbb>
 * <Visual Item BG Color 2: #rrggbb>
 *
 * - Used for: Item, Weapon, Armor Notetags
 * - Changes the background color(s) for the item to a hex color.
 * - Use #rrggbb for custom colors.
 * - You can find out what hex codes belong to which color from this website:
 *   https://htmlcolorcodes.com/
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Visual Item Inventory Settings
 * ============================================================================
 *
 * These settings allow you to adjust how the Visual Item Inventory windows
 * appear and which ones they appear in.
 *
 * ---
 *
 * General
 *
 *   Applied Windows:
 *   - Insert the name of their constructors here to apply them.
 *   - Only works with windows made from Window_ItemList.
 *
 *   Icon Size:
 *   - The icon size used for the Visual Item windows.
 *
 *   Icon Smoothing?:
 *   - Do you wish to smooth out the icons or pixelate them?
 *
 * ---
 *
 * Item Quantity Outline
 *
 *   Outline Color:
 *   - Colors with a bit of alpha settings.
 *   - Format rgba(0-255, 0-255, 0-255, 0-1)
 *
 *   Outline Size:
 *   - How thick are the outlines for the item quantity?
 *
 * ---
 *
 * Tooltip Window
 *
 *   Show Tooltip Window?:
 *   - Show the tooltip window?
 *
 *   Background Type:
 *   - Select background type for this window.
 *
 *   Buffer Width:
 *   - How much to buffer this window's width by?
 *
 *   Font Size:
 *   - What should this window's font size be?
 *
 *   Offset X:
 *   Offset Y:
 *   - How much to offset this window's X/Y position by?
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Yanfly
 * * Arisu
 * * Olivia
 * * Irina
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.06: June 13, 2024
 * * Bug Fixes!
 * ** Fixed a visual overlapping error. Fix made by Olivia.
 *
 * Version 1.05: March 14, 2024
 * * Compatibility Update!
 * ** Added better compatibility with VisuMZ_3_ItemAmplifySkills! The confirm
 *    icon should now be displayed properly. Update made by Irina.
 *
 * Version 1.04: July 13, 2023
 * * Compatibility Update!
 * ** Added compatibility with Quest Journal System's newest Quest Label update
 *    in order for the Quest Label to show up in the visual inventory. Update
 *    made by Irina.
 *
 * Version 1.03: August 25, 2022
 * * Feature Update!
 * ** Updated the boundaries for visual item name display positions to always
 *    fit within the verticality of the game screen. Fix made by Irina.
 *
 * Version 1.02: July 16, 2021
 * * Bug Fixes!
 * ** Visual glitch fixed that would make item quantity not appear. Fix made
 *    by Arisu.
 *
 * Version 1.01: February 19, 2021
 * * Feature Update!
 * ** No longer requires VisuStella MZ Items and Equips Core dependency.
 *
 * Version 1.00 Official Release Date: February 26, 2021
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param VisualItemInv
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General
 *
 * @param Constructors:arraystr
 * @text Applied Windows
 * @parent General
 * @type string[]
 * @desc Insert the name of their constructors here to apply them.
 * Only works with windows made from Window_ItemList.
 * @default ["Window_ItemList","Window_EquipItem","Window_ShopSell","Window_EventItem","Window_BattleItem"]
 *
 * @param IconSize:num
 * @text Icon Size
 * @parent General
 * @desc The icon size used for the Visual Item windows.
 * @default 64
 *
 * @param IconSmoothing:eval
 * @text Icon Smoothing?
 * @parent General
 * @type boolean
 * @on Smooth
 * @off Pixelate
 * @desc Do you wish to smooth out the icons or pixelate them?
 * @default false
 *
 * @param Outline
 * @text Item Quantity Outline
 *
 * @param OutlineColor:num
 * @text Outline Color
 * @parent Outline
 * @desc Colors with a bit of alpha settings.
 * Format rgba(0-255, 0-255, 0-255, 0-1)
 * @default rgba(0, 0, 0, 1.0)
 *
 * @param OutlineSize:num
 * @text Outline Size
 * @parent Outline
 * @desc How thick are the outlines for the item quantity?
 * @default 4
 *
 * @param Tooltip
 * @text Tooltip Window
 *
 * @param ShowTooltip:eval
 * @text Show Tooltip Window?
 * @parent Tooltip
 * @type boolean
 * @on Show
 * @off Hide
 * @desc Show the tooltip window?
 * @default true
 *
 * @param TooltipBgType:num
 * @text Background Type
 * @parent Tooltip
 * @type select
 * @option 0 - Window
 * @value 0
 * @option 1 - Dim
 * @value 1
 * @option 2 - Transparent
 * @value 2
 * @desc Select background type for this window.
 * @default 0
 *
 * @param TooltipBufferWidth:num
 * @text Buffer Width
 * @parent Tooltip
 * @desc How much to buffer this window's width by?
 * @default 16
 *
 * @param TooltipFontSize:num
 * @text Font Size
 * @parent Tooltip
 * @desc What should this window's font size be?
 * @default 22
 *
 * @param TooltipOffsetX:num
 * @text Offset X
 * @parent Tooltip
 * @desc How much to offset this window's X position by?
 * @default 0
 *
 * @param TooltipOffsetY:num
 * @text Offset Y
 * @parent Tooltip
 * @desc How much to offset this window's Y position by?
 * @default 8
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================

const _0x40658c = _0x4c58;
(function (_0x57c24d, _0x59cffb) {
    const _0x29d64c = _0x4c58,
        _0x1a65f7 = _0x57c24d();
    while (!![]) {
        try {
            const _0x34a883 =
                (parseInt(_0x29d64c(0x22a)) / (-0x252b + -0x1df * -0xa + -0x1 * -0x1276)) *
                    (parseInt(_0x29d64c(0x17d)) / (-0x6 * 0x122 + 0xe * 0x142 + 0x6 * -0x1cd)) +
                (-parseInt(_0x29d64c(0x287)) / (0x2553 + -0x442 * 0x2 + -0x61 * 0x4c)) *
                    (parseInt(_0x29d64c(0x216)) / (0x1 * -0x1edf + 0x11b1 + -0x6 * -0x233)) +
                (parseInt(_0x29d64c(0x159)) / (-0x1f5 * -0x2 + 0x5bb + -0x70 * 0x16)) *
                    (-parseInt(_0x29d64c(0x1ea)) / (-0x1ccc + 0x1f87 + -0x2b5)) +
                parseInt(_0x29d64c(0x251)) / (-0xad3 * -0x3 + -0x35e * -0x3 + 0x614 * -0x7) +
                parseInt(_0x29d64c(0x157)) / (0x2e3 * 0x1 + 0x2405 * 0x1 + 0x2 * -0x1370) +
                (parseInt(_0x29d64c(0x1d2)) / (-0xfba + -0x9c7 + 0x198a)) *
                    (parseInt(_0x29d64c(0x29d)) / (0x2e * -0x61 + -0x1 * 0xb75 + 0x1ced)) +
                (parseInt(_0x29d64c(0x285)) / (0xa68 + 0x1 * 0x1e01 + -0x285e)) *
                    (-parseInt(_0x29d64c(0x219)) / (0x8 * -0x1d6 + -0xc * -0x2e1 + 0x10 * -0x13d));
            if (_0x34a883 === _0x59cffb) break;
            else _0x1a65f7['push'](_0x1a65f7['shift']());
        } catch (_0x5631e4) {
            _0x1a65f7['push'](_0x1a65f7['shift']());
        }
    }
})(_0x282e, 0x1bd345 + -0x1d61a3 + 0x1 * 0x105a2b);
function _0x282e() {
    const _0x245331 = [
        'bility',
        '4031188rhlCJn',
        'parse',
        'olor',
        'aced\x20on\x20th',
        'Spacing',
        'Constructo',
        'maxCols',
        'NUM',
        'parameters',
        'ipvEl',
        'uestLabelV',
        'width',
        'TooltipWin',
        'GAJzE',
        'outlineWid',
        'VisuMZ_2_Q',
        'Inv',
        'length',
        's.\x0aPlease\x20',
        'OffsetY',
        '_item',
        'FmKRD',
        'urnCheck',
        'yGRTg',
        'item',
        'aNaQf',
        'mList_colS',
        'rowSpacing',
        'floor',
        'ConvertHex',
        'bgColorNum',
        'OffsetX',
        'ToRgba',
        'contents',
        'dXOrx',
        'RemoveEqui',
        'e_drawItem',
        'ewLabelVis',
        'ectable_it',
        'drawItemVi',
        'eItemQuest',
        'VISUAL_ITE',
        'ired\x20plugi',
        'updatePosi',
        'bfnqt',
        'ItemInvent',
        'zLMWt',
        'textWidth',
        'format',
        'call',
        'ItemNumber',
        'QuestSyste',
        '317757QQlhKI',
        '0|3|2|4|1',
        '606147rSlvsP',
        'anager.',
        'ntoryToolt',
        'max',
        'daZKS',
        'OutlineCol',
        'prototype',
        'pSell_maxC',
        'eItemNewLa',
        'mallest\x20to',
        '_parentWin',
        'FRdjt',
        'split',
        'iconHeight',
        'trim',
        'rgba(0,\x200,',
        'cJOIi',
        'mpWJk',
        'VeOvO',
        'M_ICON_SIZ',
        'dow',
        'taGPV',
        '36130EPyDnK',
        'itemBackCo',
        '_windowLay',
        'mInventory',
        'placeNewLa',
        'setupVisua',
        'contentsBa',
        'QeYpk',
        'initialize',
        'getItemCol',
        'BUFFER_WID',
        'New',
        'Item',
        'fontSize',
        'ier\x20number',
        'ARRAYSTR',
        'cEljb',
        'rrectly\x20pl',
        'tSize',
        'mList_init',
        'thPadding',
        'ams',
        '%1\x27s\x20versi',
        'drawBackgr',
        'tion',
        '1968200HUBupl',
        'JNaoa',
        '1151925lxdGtq',
        'rgba(',
        'sualItemIn',
        'ckground',
        'kHtrL',
        'nventory',
        'ItemQuanti',
        'pIcon',
        'exit',
        'WxvuN',
        'ents',
        'bUnEe',
        'updatePadd',
        'ITEM_AMPLI',
        'map',
        'KlFkG',
        'IconSize',
        'drawItemAm',
        'EquipScene',
        'refresh',
        'xMKFc',
        'YhirL',
        'ventory',
        'e\x20plugin\x20l',
        'temsEquips',
        'KLUei',
        'YmjoI',
        'isShowQues',
        'ing',
        'isOpen',
        'createVisu',
        'includes',
        'height',
        'YSuZK',
        'guRri',
        '\x20into\x20the\x20',
        '16ajLotY',
        'UpdateHelp',
        'ipItem_col',
        'mList_maxC',
        'ARRAYSTRUC',
        'STRUCT',
        'GobCZ',
        'OFFSET_Y',
        'ARRAYFUNC',
        'itemAt',
        'bind',
        'mList_call',
        'ConvertPar',
        'round',
        'Help',
        'opacity',
        'Window_Ite',
        'QNsmR',
        'ItemBackgr',
        'ist\x20from\x20s',
        'note',
        'ferWidth',
        'ShowToolti',
        'placeQuest',
        'ialize',
        '_amplifySk',
        'reorder\x20th',
        'uestLabel',
        'ItemsEquip',
        'mList_rowS',
        'descriptio',
        'TooltipBuf',
        'JciSx',
        '_context',
        'qmRCb',
        'isNewItem',
        'visible',
        'usesVisual',
        'ettings',
        'Window_Sho',
        'uVHFT',
        'sCore',
        'TooltipOff',
        'Window_Bas',
        'aced\x20over\x20',
        'Number',
        'FdNOq',
        'JYbzS',
        'hhZJz',
        'FONT_SIZE',
        'itemRectWi',
        'updateVisi',
        '_clientAre',
        'mPicture',
        'update',
        'ugin\x27s.\x20Pl',
        'bxfMc',
        'lineHeight',
        'wolHA',
        'KbYlP',
        'ist.\x0aIt\x20is',
        'drawBigIco',
        'blt',
        'mber',
        'toUpperCas',
        'pacing',
        'OTHING',
        'e\x20it\x20in\x20th',
        'Settings',
        'KdkkJ',
        'ARRAYJSON',
        'dKmBm',
        'WzLdO',
        'VisuMZ_1_I',
        '0.5',
        '_visualIte',
        'Core',
        'setBackgro',
        'clamp',
        'drawText',
        'gradientFi',
        'pSell_colS',
        'ipItem_max',
        'active',
        'vSvrF',
        '1089zgPNIR',
        'bigPicture',
        'HAzXB',
        'FY_SETTING',
        'getItemNam',
        'msOKD',
        'mList_draw',
        'Window_Sel',
        'ipWindow',
        'version',
        'min',
        'STR',
        'RegExp',
        'numItems',
        'isualItemI',
        'BG_TYPE',
        'entory',
        'wlbyD',
        'colSpacing',
        'aTnET',
        'resetFontS',
        'addLoadLis',
        'drawBigIte',
        'placeItemQ',
        '18ifwCOr',
        '\x200,\x201.0)',
        'WFJjn',
        'ceil',
        'bQiPu',
        'TooltipBgT',
        'return\x200',
        'constructo',
        'substring',
        'status',
        'OFddt',
        'loadPictur',
        'OutlineSiz',
        'TORS',
        'create',
        'WaOkL',
        'e\x20Plugin\x20M',
        'ure',
        'visualPict',
        'xwDJQ',
        'EezKs',
        'drawItemBa',
        'ckgroundVi',
        'hingEnable',
        'addChild',
        'M_CONSTRUC',
        'outlineCol',
        'mHeight',
        'Plugin\x20Man',
        'changePain',
        'ory',
        'iconIndex',
        'ill',
        'aGKZp',
        'ualItemInv',
        '_cursorRec',
        'tener',
        'xAdrL',
        'name',
        'mIcon',
        '_scene',
        'plifyConfi',
        'ols',
        'setItem',
        '12vjzRqz',
        'ntSettings',
        'padding',
        '12WkyQqE',
        'woibJ',
        'ype',
        'ewLabel',
        'COLOR',
        'OJSvc',
        'innerWidth',
        'itemRect',
        'ound',
        'undType',
        'M_OUTLINE_',
        'drawItemNu',
        'Window_Equ',
        'backOpacit',
        'isQuestIte',
        'FUNC',
        'resetTextC',
        '129598VmKKta',
        'replace',
        'isDrawItem',
        'VisualItem',
        'center',
        'refreshRet',
        'ease\x20updat',
        'ZaWSY',
        'OFFSET_X',
        'mList_plac',
        'isEnabled',
        'on\x20does\x20no',
        'bel',
        'tOpacity',
        'drawItem',
        'drawRemove',
        'bgColorHex',
        'lItemInvFo',
        'placeItemN',
        'install\x20%2',
        'itemHeight',
        'n.\x0aPlease\x20',
        'ItemScene',
        'M_ICON_SMO',
        'emHeight',
        '\x20largest\x20t',
        'imageSmoot',
        'setX',
        'atmWy',
        'Cols',
        'right',
        'ARRAYEVAL',
        'mberVisual',
        'Label',
        'XofQA',
        'iUYDB',
        'SIZE',
        'match',
    ];
    _0x282e = function () {
        return _0x245331;
    };
    return _0x282e();
}
var label = _0x40658c(0x22d) + _0x40658c(0x261),
    tier = tier || -0x117f + 0x5d * 0x60 + -0x1161,
    dependencies = [],
    pluginData = $plugins['filter'](function (_0x201c0c) {
        const _0x4b74f2 = _0x40658c,
            _0x2d5051 = {
                GAJzE: function (_0x22a12c, _0xd7b72f) {
                    return _0x22a12c + _0xd7b72f;
                },
            };
        return (
            _0x201c0c[_0x4b74f2(0x1f3)] &&
            _0x201c0c[_0x4b74f2(0x19b) + 'n'][_0x4b74f2(0x178)](
                _0x2d5051[_0x4b74f2(0x25e)](_0x2d5051[_0x4b74f2(0x25e)]('[', label), ']')
            )
        );
    })[0x10b5 + 0x19fc + 0x2ab1 * -0x1];
function _0x4c58(_0x5acc23, _0x54d2a5) {
    const _0x4fd16a = _0x282e();
    return (
        (_0x4c58 = function (_0x45846d, _0x3cf9cd) {
            _0x45846d = _0x45846d - (-0x15f6 + -0x1 * -0x3b + -0x1 * -0x1712);
            let _0x48c2f5 = _0x4fd16a[_0x45846d];
            return _0x48c2f5;
        }),
        _0x4c58(_0x5acc23, _0x54d2a5)
    );
}
((VisuMZ[label][_0x40658c(0x1c1)] = VisuMZ[label]['Settings'] || {}),
    (VisuMZ[_0x40658c(0x189) + _0x40658c(0x2b2)] = function (_0x1a2c74, _0xb26ea3) {
        const _0x1589b3 = _0x40658c,
            _0x5477d7 = {
                DeJiq: function (_0x96ecd3, _0x543de9) {
                    return _0x96ecd3(_0x543de9);
                },
                JciSx: function (_0x1d0089, _0x5d3d59) {
                    return _0x1d0089(_0x5d3d59);
                },
                ipvEl: _0x1589b3(0x258),
                OFddt: function (_0x52eecb, _0x281ef9) {
                    return _0x52eecb !== _0x281ef9;
                },
                RnssZ: function (_0x4fe5b9, _0x1328d4) {
                    return _0x4fe5b9(_0x1328d4);
                },
                FmKRD: 'ARRAYNUM',
                YSuZK: 'EVAL',
                daZKS: function (_0x516257, _0x1173a6) {
                    return _0x516257 !== _0x1173a6;
                },
                QNsmR: function (_0x50a260, _0x5b5425) {
                    return _0x50a260(_0x5b5425);
                },
                zLMWt: _0x1589b3(0x249),
                cFeDt: 'JSON',
                MnUvE: function (_0x1dbe00, _0x4780d4) {
                    return _0x1dbe00 !== _0x4780d4;
                },
                KbYlP: _0x1589b3(0x1c3),
                GobCZ: function (_0x32723e, _0x273727) {
                    return _0x32723e !== _0x273727;
                },
                QeYpk: _0x1589b3(0x228),
                ixvez: _0x1589b3(0x1f0),
                WxvuN: _0x1589b3(0x185),
                YmjoI: _0x1589b3(0x1dd),
                YAbBI: _0x1589b3(0x2ac),
                YhirL: _0x1589b3(0x182),
                vDpTb: _0x1589b3(0x181) + 'T',
            };
        for (const _0x4e17dd in _0xb26ea3) {
            if (_0x4e17dd['match'](/(.*):(.*)/i)) {
                const _0x173048 = _0x5477d7['DeJiq'](String, RegExp['$1']),
                    _0x4e71c0 = _0x5477d7[_0x1589b3(0x19d)](String, RegExp['$2'])
                        [_0x1589b3(0x1bd) + 'e']()
                        [_0x1589b3(0x295)]();
                let _0x247b9b, _0x86b70c, _0x5f167a;
                switch (_0x4e71c0) {
                    case _0x5477d7[_0x1589b3(0x25a)]:
                        _0x247b9b = _0x5477d7['OFddt'](_0xb26ea3[_0x4e17dd], '')
                            ? _0x5477d7['RnssZ'](Number, _0xb26ea3[_0x4e17dd])
                            : -0x13 * 0x10f + -0x128d + 0x26aa * 0x1;
                        break;
                    case _0x5477d7[_0x1589b3(0x266)]:
                        ((_0x86b70c = _0x5477d7[_0x1589b3(0x1f4)](_0xb26ea3[_0x4e17dd], '')
                            ? JSON['parse'](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c[_0x1589b3(0x167)](_0xe0d6fd =>
                                Number(_0xe0d6fd)
                            )));
                        break;
                    case _0x5477d7[_0x1589b3(0x17a)]:
                        _0x247b9b = _0x5477d7[_0x1589b3(0x28b)](_0xb26ea3[_0x4e17dd], '')
                            ? _0x5477d7[_0x1589b3(0x18e)](eval, _0xb26ea3[_0x4e17dd])
                            : null;
                        break;
                    case _0x5477d7[_0x1589b3(0x27f)]:
                        ((_0x86b70c = _0x5477d7[_0x1589b3(0x1f4)](_0xb26ea3[_0x4e17dd], '')
                            ? JSON['parse'](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c['map'](_0x3da83b => eval(_0x3da83b))));
                        break;
                    case _0x5477d7['cFeDt']:
                        _0x247b9b = _0x5477d7['MnUvE'](_0xb26ea3[_0x4e17dd], '')
                            ? JSON[_0x1589b3(0x252)](_0xb26ea3[_0x4e17dd])
                            : '';
                        break;
                    case _0x5477d7[_0x1589b3(0x1b8)]:
                        ((_0x86b70c = _0x5477d7[_0x1589b3(0x183)](_0xb26ea3[_0x4e17dd], '')
                            ? JSON['parse'](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c[_0x1589b3(0x167)](_0x191176 =>
                                JSON[_0x1589b3(0x252)](_0x191176)
                            )));
                        break;
                    case _0x5477d7[_0x1589b3(0x2a4)]:
                        _0x247b9b = _0x5477d7['MnUvE'](_0xb26ea3[_0x4e17dd], '')
                            ? new Function(JSON[_0x1589b3(0x252)](_0xb26ea3[_0x4e17dd]))
                            : new Function(_0x5477d7['ixvez']);
                        break;
                    case _0x5477d7[_0x1589b3(0x162)]:
                        ((_0x86b70c = _0x5477d7['GobCZ'](_0xb26ea3[_0x4e17dd], '')
                            ? JSON[_0x1589b3(0x252)](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c[_0x1589b3(0x167)](
                                _0x9af57b => new Function(JSON[_0x1589b3(0x252)](_0x9af57b))
                            )));
                        break;
                    case _0x5477d7[_0x1589b3(0x173)]:
                        _0x247b9b = _0x5477d7[_0x1589b3(0x28b)](_0xb26ea3[_0x4e17dd], '')
                            ? _0x5477d7[_0x1589b3(0x19d)](String, _0xb26ea3[_0x4e17dd])
                            : '';
                        break;
                    case _0x5477d7['YAbBI']:
                        ((_0x86b70c = _0x5477d7[_0x1589b3(0x1f4)](_0xb26ea3[_0x4e17dd], '')
                            ? JSON['parse'](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c[_0x1589b3(0x167)](_0x207519 =>
                                String(_0x207519)
                            )));
                        break;
                    case _0x5477d7[_0x1589b3(0x16e)]:
                        ((_0x5f167a = _0x5477d7[_0x1589b3(0x28b)](_0xb26ea3[_0x4e17dd], '')
                            ? JSON[_0x1589b3(0x252)](_0xb26ea3[_0x4e17dd])
                            : {}),
                            (_0x247b9b = VisuMZ['ConvertPar' + 'ams']({}, _0x5f167a)));
                        break;
                    case _0x5477d7['vDpTb']:
                        ((_0x86b70c = _0x5477d7['OFddt'](_0xb26ea3[_0x4e17dd], '')
                            ? JSON[_0x1589b3(0x252)](_0xb26ea3[_0x4e17dd])
                            : []),
                            (_0x247b9b = _0x86b70c['map'](_0x1f5f11 =>
                                VisuMZ[_0x1589b3(0x189) + 'ams'](
                                    {},
                                    JSON[_0x1589b3(0x252)](_0x1f5f11)
                                )
                            )));
                        break;
                    default:
                        continue;
                }
                _0x1a2c74[_0x173048] = _0x247b9b;
            }
        }
        return _0x1a2c74;
    }),
    (_0x30cc2f => {
        const _0x3d1cca = _0x40658c,
            _0x10ea0b = {
                yGRTg: function (_0x359b99, _0x1e6282) {
                    return _0x359b99(_0x1e6282);
                },
                CVHYH:
                    '%1\x20is\x20miss' +
                    'ing\x20a\x20requ' +
                    _0x3d1cca(0x27b) +
                    _0x3d1cca(0x23f) +
                    _0x3d1cca(0x23d) +
                    _0x3d1cca(0x17c) +
                    _0x3d1cca(0x206) +
                    'ager.',
                vSvrF: function (_0x2574d0, _0x19c23e) {
                    return _0x2574d0 !== _0x19c23e;
                },
                aGkkY:
                    _0x3d1cca(0x2b3) +
                    _0x3d1cca(0x235) +
                    't\x20match\x20pl' +
                    _0x3d1cca(0x1b4) +
                    _0x3d1cca(0x230) +
                    _0x3d1cca(0x1c0) +
                    _0x3d1cca(0x1fa) +
                    _0x3d1cca(0x288),
                rWmrK: function (_0x5b7e35, _0x3e9239) {
                    return _0x5b7e35 < _0x3e9239;
                },
                wlbyD:
                    '%1\x20is\x20inco' +
                    _0x3d1cca(0x2ae) +
                    _0x3d1cca(0x254) +
                    _0x3d1cca(0x170) +
                    _0x3d1cca(0x1b9) +
                    '\x20a\x20Tier\x20%2' +
                    '\x20plugin\x20pl' +
                    _0x3d1cca(0x1a9) +
                    'other\x20Tier' +
                    '\x20%3\x20plugin' +
                    _0x3d1cca(0x263) +
                    _0x3d1cca(0x197) +
                    _0x3d1cca(0x170) +
                    _0x3d1cca(0x190) +
                    _0x3d1cca(0x290) +
                    _0x3d1cca(0x243) +
                    _0x3d1cca(0x2ab) +
                    's.',
            },
            _0x1f330d = _0x30cc2f['name'];
        for (const _0x1a2461 of dependencies) {
            if (!Imported[_0x1a2461]) {
                (_0x10ea0b[_0x3d1cca(0x268)](
                    alert,
                    _0x10ea0b['CVHYH'][_0x3d1cca(0x281)](_0x1f330d, _0x1a2461)
                ),
                    SceneManager[_0x3d1cca(0x161)]());
                break;
            }
        }
        const _0x267ba0 = _0x30cc2f['descriptio' + 'n'];
        if (_0x267ba0[_0x3d1cca(0x24f)](/\[Version[ ](.*?)\]/i)) {
            const _0x2d3b23 = _0x10ea0b[_0x3d1cca(0x268)](Number, RegExp['$1']);
            _0x10ea0b[_0x3d1cca(0x1d1)](_0x2d3b23, VisuMZ[label][_0x3d1cca(0x1db)]) &&
                (_0x10ea0b[_0x3d1cca(0x268)](
                    alert,
                    _0x10ea0b['aGkkY'][_0x3d1cca(0x281)](_0x1f330d, _0x2d3b23)
                ),
                SceneManager[_0x3d1cca(0x161)]());
        }
        if (_0x267ba0[_0x3d1cca(0x24f)](/\[Tier[ ](\d+)\]/i)) {
            const _0x1471c2 = _0x10ea0b[_0x3d1cca(0x268)](Number, RegExp['$1']);
            _0x10ea0b['rWmrK'](_0x1471c2, tier)
                ? (_0x10ea0b['yGRTg'](
                      alert,
                      _0x10ea0b[_0x3d1cca(0x1e3)]['format'](_0x1f330d, _0x1471c2, tier)
                  ),
                  SceneManager['exit']())
                : (tier = Math[_0x3d1cca(0x28a)](_0x1471c2, tier));
        }
        VisuMZ['ConvertPar' + _0x3d1cca(0x2b2)](
            VisuMZ[label][_0x3d1cca(0x1c1)],
            _0x30cc2f[_0x3d1cca(0x259)]
        );
    })(pluginData),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)]['RegExp'] = {
        visualPicture: /<(?:VISUAL|VISUAL ITEM) (?:PICTURE|FILENAME):[ ](.*)>/i,
        bigPicture: /<PICTURE:[ ](.*)>/i,
        bgColorNum1: /<(?:VISUAL|VISUAL ITEM)[ ](?:BG|BACKGROUND)[ ]COLOR[ ]1:[ ](\d+)>/i,
        bgColorNum2: /<(?:VISUAL|VISUAL ITEM)[ ](?:BG|BACKGROUND)[ ]COLOR[ ]2:[ ](\d+)>/i,
        bgColorHex1: /<(?:VISUAL|VISUAL ITEM)[ ](?:BG|BACKGROUND)[ ]COLOR[ ]1:[ ]#(.*)>/i,
        bgColorHex2: /<(?:VISUAL|VISUAL ITEM)[ ](?:BG|BACKGROUND)[ ]COLOR[ ]2:[ ]#(.*)>/i,
    }),
    (Window_ItemList[_0x40658c(0x27a) + 'M_ICON_SIZ' + 'E'] =
        VisuMZ['VisualItem' + 'Inv'][_0x40658c(0x1c1)][_0x40658c(0x169)] ||
        0x346 * -0x1 + 0x1 * -0x2325 + 0x13 * 0x209),
    (Window_ItemList[_0x40658c(0x27a) + 'M_ICON_SMO' + _0x40658c(0x1bf)] =
        VisuMZ['VisualItem' + _0x40658c(0x261)]['Settings']['IconSmooth' + _0x40658c(0x175)] ||
        ![]),
    (Window_ItemList['VISUAL_ITE' + _0x40658c(0x223) + _0x40658c(0x21d)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)][_0x40658c(0x28c) + 'or'] ||
        _0x40658c(0x296) + _0x40658c(0x1eb)),
    (Window_ItemList[_0x40658c(0x27a) + _0x40658c(0x223) + _0x40658c(0x24e)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)][_0x40658c(0x1f6) + 'e'] ||
        -0x115d + -0x1fa7 + -0x2 * -0x1882),
    (Window_ItemList[_0x40658c(0x27a) + _0x40658c(0x203) + _0x40658c(0x1f7)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)][_0x40658c(0x256) + 'rs'] ||
        -0xfec + -0x77 + 0x1063),
    (Window_ItemList['prototype'][_0x40658c(0x1a2) + _0x40658c(0x27e) + _0x40658c(0x208)] =
        function () {
            const _0x198f0b = _0x40658c;
            return Window_ItemList[_0x198f0b(0x27a) + 'M_CONSTRUC' + _0x198f0b(0x1f7)][
                _0x198f0b(0x178)
            ](this['constructo' + 'r'][_0x198f0b(0x210)]);
        }),
    (VisuMZ['VisualItem' + 'Inv'][_0x40658c(0x1d9) + _0x40658c(0x277) + _0x40658c(0x242)] =
        Window_Selectable[_0x40658c(0x28d)]['itemHeight']),
    (Window_ItemList[_0x40658c(0x28d)]['itemHeight'] = function () {
        const _0xd0c133 = _0x40658c,
            _0x2e28ae = {
                KLUei: function (_0x5bc5e7, _0x4bd434) {
                    return _0x5bc5e7 !== _0x4bd434;
                },
                cEljb: function (_0x1299b3, _0x2861aa) {
                    return _0x1299b3 / _0x2861aa;
                },
                gSEFG: function (_0x1fb6a6, _0x6e777c) {
                    return _0x1fb6a6 + _0x6e777c;
                },
                RmiIS: function (_0x3750e0, _0xffe66a) {
                    return _0x3750e0 * _0xffe66a;
                },
            };
        if (this[_0xd0c133(0x1a2) + 'ItemInvent' + _0xd0c133(0x208)]()) {
            if (_0x2e28ae[_0xd0c133(0x172)](this['_visualIte' + 'mHeight'], undefined))
                return this['_visualIte' + _0xd0c133(0x205)];
            const _0x7e3ed1 = Math[_0xd0c133(0x1ed)](
                _0x2e28ae[_0xd0c133(0x2ad)](
                    Window_ItemList[_0xd0c133(0x27a) + 'M_ICON_SIZ' + 'E'],
                    this[_0xd0c133(0x1b6)]()
                )
            );
            return (
                (this[_0xd0c133(0x1c8) + _0xd0c133(0x205)] = _0x2e28ae['gSEFG'](
                    Math[_0xd0c133(0x18a)](_0x2e28ae['RmiIS'](_0x7e3ed1, this['lineHeight']())),
                    0x1067 + -0x90 * 0x33 + 0xc51
                )),
                this['_visualIte' + _0xd0c133(0x205)]
            );
        } else
            return VisuMZ[_0xd0c133(0x22d) + _0xd0c133(0x261)][
                'Window_Sel' + 'ectable_it' + _0xd0c133(0x242)
            ][_0xd0c133(0x282)](this);
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x18d) + 'mList_maxC' + _0x40658c(0x214)
    ] = Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x257)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x257)] = function () {
        const _0x538e4e = _0x40658c,
            _0x4b0238 = {
                OJSvc: function (_0xe90804, _0x50fda9) {
                    return _0xe90804 / _0x50fda9;
                },
            };
        return this[_0x538e4e(0x1a2) + 'ItemInvent' + 'ory']()
            ? Math[_0x538e4e(0x1ed)](
                  _0x4b0238[_0x538e4e(0x21e)](this['innerWidth'], this[_0x538e4e(0x23e)]())
              )
            : VisuMZ['VisualItem' + _0x538e4e(0x261)][_0x538e4e(0x18d) + _0x538e4e(0x180) + 'ols'][
                  'call'
              ](this);
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        'Window_Ite' + _0x40658c(0x26b) + _0x40658c(0x1be)
    ] = Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1e4)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1e4)] = function () {
        const _0x2e1594 = _0x40658c;
        return this['usesVisual' + _0x2e1594(0x27e) + 'ory']()
            ? -0x84d * 0x1 + 0x1 * -0x2569 + 0x2 * 0x16db
            : VisuMZ[_0x2e1594(0x22d) + 'Inv']['Window_Ite' + _0x2e1594(0x26b) + 'pacing'][
                  _0x2e1594(0x282)
              ](this);
    }),
    (VisuMZ[_0x40658c(0x22d) + 'Inv'][_0x40658c(0x18d) + _0x40658c(0x19a) + _0x40658c(0x1be)] =
        Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x26c)]),
    (Window_ItemList[_0x40658c(0x28d)]['rowSpacing'] = function () {
        const _0x38170a = _0x40658c;
        return this[_0x38170a(0x1a2) + _0x38170a(0x27e) + _0x38170a(0x208)]()
            ? 0x19da + -0xa4c + 0xf8e * -0x1
            : VisuMZ[_0x38170a(0x22d) + _0x38170a(0x261)][
                  _0x38170a(0x18d) + _0x38170a(0x19a) + _0x38170a(0x1be)
              ][_0x38170a(0x282)](this);
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x18d) + 'mList_draw' + _0x40658c(0x2a9)
    ] = Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x238)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x238)] = function (_0x407cb3) {
        const _0x4f936e = _0x40658c;
        this['usesVisual' + _0x4f936e(0x27e) + 'ory']()
            ? this[_0x4f936e(0x278) + 'sualItemIn' + _0x4f936e(0x16f)](_0x407cb3)
            : VisuMZ[_0x4f936e(0x22d) + 'Inv'][_0x4f936e(0x18d) + 'mList_draw' + _0x4f936e(0x2a9)][
                  'call'
              ](this, _0x407cb3);
    }),
    (Window_ItemList['prototype']['drawItemVi' + _0x40658c(0x15b) + _0x40658c(0x16f)] = function (
        _0x1510aa
    ) {
        const _0x216238 = _0x40658c,
            _0x44ca26 = {
                iUYDB: function (_0x4e1947, _0x577910) {
                    return _0x4e1947 === _0x577910;
                },
                atmWy: function (_0x42eb6f, _0x413848) {
                    return _0x42eb6f(_0x413848);
                },
                guRri: _0x216238(0x286),
                lXrQm: function (_0xb194ca, _0xed32fc) {
                    return _0xb194ca - _0xed32fc;
                },
                flxcO: function (_0xe23b70, _0x30376c) {
                    return _0xe23b70 + _0x30376c;
                },
            },
            _0xfc79c0 = this['itemAt'](_0x1510aa);
        if (this[_0x216238(0x196) + 'ill'] && _0x44ca26[_0x216238(0x24d)](_0xfc79c0, null))
            return this['drawItemAm' + _0x216238(0x213) + 'rm'](_0x1510aa);
        if (!_0xfc79c0) return;
        const _0x20264c = VisuMZ[_0x216238(0x22d) + _0x216238(0x261)][_0x216238(0x1de)],
            _0x360129 = _0xfc79c0[_0x216238(0x191)],
            _0x360ef4 = this[_0x216238(0x1af) + _0x216238(0x2b1)](_0x1510aa);
        if (
            _0x360129['match'](_0x20264c[_0x216238(0x1fc) + _0x216238(0x1fb)]) ||
            _0x360129[_0x216238(0x24f)](_0x20264c[_0x216238(0x1d3)])
        ) {
            const _0xa8563c = _0x44ca26[_0x216238(0x246)](String, RegExp['$1'])[_0x216238(0x295)](),
                _0x2406f0 = ImageManager[_0x216238(0x1f5) + 'e'](_0xa8563c);
            _0x2406f0[_0x216238(0x1e7) + _0x216238(0x20e)](
                this[_0x216238(0x1e8) + _0x216238(0x1b2)][_0x216238(0x187)](
                    this,
                    _0xfc79c0,
                    _0x2406f0,
                    _0x360ef4
                )
            );
        } else {
            const _0x589433 = _0x44ca26[_0x216238(0x17b)][_0x216238(0x293)]('|');
            let _0x1ea443 = -0x2db * 0x1 + 0x1 * 0x1055 + -0xd7a;
            while (!![]) {
                switch (_0x589433[_0x1ea443++]) {
                    case '0':
                        this[_0x216238(0x207) + _0x216238(0x237)](this['isEnabled'](_0xfc79c0));
                        continue;
                    case '1':
                        this['changePain' + _0x216238(0x237)](!![]);
                        continue;
                    case '2':
                        this[_0x216238(0x224) + 'mber'](
                            _0xfc79c0,
                            _0x360ef4['x'],
                            _0x44ca26['lXrQm'](
                                _0x44ca26['flxcO'](_0x360ef4['y'], _0x360ef4[_0x216238(0x179)]),
                                this[_0x216238(0x1b6)]()
                            ),
                            _0x360ef4[_0x216238(0x25c)]
                        );
                        continue;
                    case '3':
                        this[_0x216238(0x1e8) + _0x216238(0x211)](_0xfc79c0, _0x360ef4);
                        continue;
                    case '4':
                        this[_0x216238(0x1e6) + _0x216238(0x1a3)]();
                        continue;
                }
                break;
            }
        }
        (this[_0x216238(0x23c) + 'ewLabel'](_0x1510aa),
            this[_0x216238(0x1e9) + _0x216238(0x198)](_0x1510aa));
    }),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1e8) + _0x40658c(0x1b2)] = function (
        _0x562bc1,
        _0xfaefd9,
        _0x36a319
    ) {
        const _0x196038 = _0x40658c,
            _0x5dde70 = {
                woibJ: function (_0x349152, _0x23949d) {
                    return _0x349152 + _0x23949d;
                },
                xAdrL: function (_0x31b228, _0x4e5894) {
                    return _0x31b228 - _0x4e5894;
                },
                HtNNH: function (_0xf2af90, _0x295cdb) {
                    return _0xf2af90 / _0x295cdb;
                },
                cJOIi: function (_0x12814d, _0x5e381b) {
                    return _0x12814d / _0x5e381b;
                },
                bQiPu: function (_0x4b8dea, _0x37f20e) {
                    return _0x4b8dea * _0x37f20e;
                },
                taGPV: function (_0x537753, _0x3907fc) {
                    return _0x537753 / _0x3907fc;
                },
                ZaWSY: function (_0x23fc46, _0x37ef48) {
                    return _0x23fc46 - _0x37ef48;
                },
            };
        this[_0x196038(0x207) + 'tOpacity'](this[_0x196038(0x234)](_0x562bc1));
        let _0xbc22aa = _0x5dde70['woibJ'](
                _0x36a319['x'],
                0x1 * -0x193f + 0x1a * 0x16d + -0x1 * 0xbd1
            ),
            _0x4395a1 = _0x5dde70['woibJ'](_0x36a319['y'], 0x21a3 + -0xd * 0x1a0 + -0xc81),
            _0x318213 = _0x5dde70[_0x196038(0x20f)](
                _0x36a319[_0x196038(0x25c)],
                0x2 * 0xae2 + 0x24e5 + 0x3aa5 * -0x1
            ),
            _0xd0cf89 = _0x5dde70[_0x196038(0x20f)](
                _0x36a319[_0x196038(0x179)],
                0x3d * -0x1 + -0x2a5 * -0x5 + -0x2 * 0x67c
            ),
            _0x54959f = Math[_0x196038(0x1dc)](_0x318213, _0xd0cf89);
        const _0x942534 = _0x5dde70['HtNNH'](_0x54959f, _0xfaefd9[_0x196038(0x25c)]),
            _0x1ae16e = _0x5dde70[_0x196038(0x297)](_0x54959f, _0xfaefd9[_0x196038(0x179)]),
            _0x5380bb = Math[_0x196038(0x1dc)](
                _0x942534,
                _0x1ae16e,
                0x1 * 0x1707 + 0x4 * 0xd3 + -0x1a52
            );
        let _0x29f803 = Math[_0x196038(0x18a)](
                _0x5dde70[_0x196038(0x1ee)](_0xfaefd9[_0x196038(0x25c)], _0x5380bb)
            ),
            _0x1f9cea = Math[_0x196038(0x18a)](
                _0x5dde70[_0x196038(0x1ee)](_0xfaefd9[_0x196038(0x179)], _0x5380bb)
            );
        ((_0xbc22aa += Math['round'](
            _0x5dde70[_0x196038(0x297)](
                _0x5dde70[_0x196038(0x20f)](_0x318213, _0x29f803),
                0xf3e * 0x2 + -0xf2b + -0xf4f
            )
        )),
            (_0x4395a1 += Math[_0x196038(0x18a)](
                _0x5dde70[_0x196038(0x29c)](
                    _0x5dde70[_0x196038(0x231)](_0xd0cf89, _0x1f9cea),
                    0xe18 + 0x3b * -0x15 + -0x93f * 0x1
                )
            )));
        const _0x69393e = _0xfaefd9[_0x196038(0x25c)],
            _0x383da7 = _0xfaefd9[_0x196038(0x179)],
            _0x475669 =
                this[_0x196038(0x272)][_0x196038(0x19e)][_0x196038(0x244) + _0x196038(0x201) + 'd'];
        ((this['contents'][_0x196038(0x19e)][_0x196038(0x244) + _0x196038(0x201) + 'd'] = !![]),
            this[_0x196038(0x272)][_0x196038(0x1bb)](
                _0xfaefd9,
                0xa69 + 0x1d * 0x7b + -0xc2c * 0x2,
                0x3cd * 0x3 + -0x1 * 0x3fd + 0x76a * -0x1,
                _0x69393e,
                _0x383da7,
                _0xbc22aa,
                _0x4395a1,
                _0x29f803,
                _0x1f9cea
            ),
            (this[_0x196038(0x272)][_0x196038(0x19e)]['imageSmoot' + _0x196038(0x201) + 'd'] =
                _0x475669),
            this[_0x196038(0x224) + _0x196038(0x1bc)](
                _0x562bc1,
                _0x36a319['x'],
                _0x5dde70[_0x196038(0x231)](
                    _0x5dde70[_0x196038(0x21a)](_0x36a319['y'], _0x36a319[_0x196038(0x179)]),
                    this[_0x196038(0x1b6)]()
                ),
                _0x36a319[_0x196038(0x25c)]
            ),
            this['resetFontS' + _0x196038(0x1a3)](),
            this[_0x196038(0x207) + _0x196038(0x237)](!![]));
    }),
    (Window_ItemList['prototype'][_0x40658c(0x1e8) + _0x40658c(0x211)] = function (
        _0x14a7d1,
        _0xeac8cc
    ) {
        const _0x2fa7eb = _0x40658c,
            _0x304b50 = _0x14a7d1[_0x2fa7eb(0x209)];
        this[_0x2fa7eb(0x1ba) + 'n'](_0x304b50, _0xeac8cc);
    }),
    (Window_ItemList['prototype'][_0x40658c(0x1ba) + 'n'] = function (_0x473ea2, _0x44f13f) {
        const _0x24c855 = _0x40658c,
            _0x5e1a51 = {
                aNaQf: function (_0x147c2c, _0x556c47) {
                    return _0x147c2c / _0x556c47;
                },
                KwXpn: function (_0x52bead, _0x3dff67) {
                    return _0x52bead - _0x3dff67;
                },
                mpWJk: function (_0x3f3b62, _0x2d6d23) {
                    return _0x3f3b62 - _0x2d6d23;
                },
                DaMlD: 'IconSet',
                wolHA: function (_0x511bd1, _0x3d7019) {
                    return _0x511bd1 * _0x3d7019;
                },
                pjhNI: function (_0x139037, _0x222f9f) {
                    return _0x139037 % _0x222f9f;
                },
            };
        let _0x588c07 = _0x44f13f['x'],
            _0x5b9e9c = _0x44f13f['y'],
            _0xcf31ce = Window_ItemList[_0x24c855(0x27a) + _0x24c855(0x29a) + 'E'];
        ((_0x588c07 += Math[_0x24c855(0x18a)](
            _0x5e1a51[_0x24c855(0x26a)](
                _0x5e1a51['KwXpn'](_0x44f13f[_0x24c855(0x25c)], _0xcf31ce),
                -0x6e9 + -0x22b * 0x11 + -0xd * -0x35e
            )
        )),
            (_0x5b9e9c += Math[_0x24c855(0x18a)](
                _0x5e1a51[_0x24c855(0x26a)](
                    _0x5e1a51[_0x24c855(0x298)](_0x44f13f['height'], _0xcf31ce),
                    -0x26c6 + 0x23d7 + -0x1 * -0x2f1
                )
            )));
        const _0x2beeca = ImageManager['loadSystem'](_0x5e1a51['DaMlD']),
            _0x47a9bf = ImageManager['iconWidth'],
            _0x483648 = ImageManager[_0x24c855(0x294)],
            _0x2e853c = _0x5e1a51[_0x24c855(0x1b7)](
                _0x5e1a51['pjhNI'](_0x473ea2, 0x100 * -0x24 + -0x201d + 0x442d),
                _0x47a9bf
            ),
            _0x114b40 = _0x5e1a51[_0x24c855(0x1b7)](
                Math[_0x24c855(0x26d)](
                    _0x5e1a51['aNaQf'](_0x473ea2, -0x613 + 0x1 * 0x1c97 + -0x3be * 0x6)
                ),
                _0x483648
            );
        ((this['contents'][_0x24c855(0x19e)][_0x24c855(0x244) + _0x24c855(0x201) + 'd'] =
            Window_ItemList['VISUAL_ITE' + _0x24c855(0x241) + _0x24c855(0x1bf)]),
            this[_0x24c855(0x272)][_0x24c855(0x1bb)](
                _0x2beeca,
                _0x2e853c,
                _0x114b40,
                _0x47a9bf,
                _0x483648,
                _0x588c07,
                _0x5b9e9c,
                _0xcf31ce,
                _0xcf31ce
            ),
            (this['contents'][_0x24c855(0x19e)][_0x24c855(0x244) + 'hingEnable' + 'd'] = !![]));
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x18d) + _0x40658c(0x1d8) + _0x40658c(0x283)
    ] = Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x224) + _0x40658c(0x1bc)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x224) + _0x40658c(0x1bc)] = function (
        _0x124659,
        _0x5472df,
        _0x567577,
        _0x310338
    ) {
        const _0x277c87 = _0x40658c;
        this[_0x277c87(0x1a2) + _0x277c87(0x27e) + _0x277c87(0x208)]()
            ? (this[_0x277c87(0x2a2) + _0x277c87(0x23b) + _0x277c87(0x217)](),
              VisuMZ['VisualItem' + _0x277c87(0x261)][
                  _0x277c87(0x18d) + 'mList_draw' + 'ItemNumber'
              ][_0x277c87(0x282)](this, _0x124659, _0x5472df, _0x567577, _0x310338),
              this[_0x277c87(0x1e6) + 'ettings']())
            : VisuMZ['VisualItem' + _0x277c87(0x261)][
                  _0x277c87(0x18d) + _0x277c87(0x1d8) + _0x277c87(0x283)
              ][_0x277c87(0x282)](this, _0x124659, _0x5472df, _0x567577, _0x310338);
    }),
    (Window_Base[_0x40658c(0x28d)][_0x40658c(0x2a2) + _0x40658c(0x23b) + _0x40658c(0x217)] =
        function () {
            const _0x36dfe3 = _0x40658c;
            (this[_0x36dfe3(0x1e6) + _0x36dfe3(0x1a3)](),
                (this[_0x36dfe3(0x272)][_0x36dfe3(0x204) + 'or'] =
                    Window_ItemList[_0x36dfe3(0x27a) + _0x36dfe3(0x223) + 'COLOR']),
                (this[_0x36dfe3(0x272)][_0x36dfe3(0x25f) + 'th'] =
                    Window_ItemList['VISUAL_ITE' + _0x36dfe3(0x223) + _0x36dfe3(0x24e)]));
        }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        'Window_Ite' + _0x40658c(0x2b0) + _0x40658c(0x195)
    ] = Window_ItemList['prototype'][_0x40658c(0x2a5)]),
    (Window_ItemList[_0x40658c(0x28d)]['initialize'] = function (_0x2c07ba) {
        const _0xa091e0 = _0x40658c;
        (VisuMZ[_0xa091e0(0x22d) + 'Inv']['Window_Ite' + 'mList_init' + _0xa091e0(0x195)][
            _0xa091e0(0x282)
        ](this, _0x2c07ba),
            this[_0xa091e0(0x177) + 'alItemInve' + _0xa091e0(0x289) + _0xa091e0(0x1da)]());
    }),
    (Window_ItemList[_0x40658c(0x28d)][
        'createVisu' + 'alItemInve' + _0x40658c(0x289) + _0x40658c(0x1da)
    ] = function () {
        const _0x4278be = _0x40658c;
        if (!this[_0x4278be(0x1a2) + 'ItemInvent' + _0x4278be(0x208)]()) return;
        if (!VisuMZ[_0x4278be(0x22d) + 'Inv'][_0x4278be(0x1c1)][_0x4278be(0x193) + 'p']) return;
        ((this[_0x4278be(0x1c8) + _0x4278be(0x2a0) + _0x4278be(0x25d) + _0x4278be(0x29b)] =
            new Window_VisualItemTooltip(this)),
            SceneManager[_0x4278be(0x212)][_0x4278be(0x202)](
                this[_0x4278be(0x1c8) + _0x4278be(0x2a0) + _0x4278be(0x25d) + _0x4278be(0x29b)]
            ));
    }),
    (VisuMZ['VisualItem' + _0x40658c(0x261)][
        _0x40658c(0x18d) + _0x40658c(0x188) + _0x40658c(0x17e)
    ] = Window_ItemList[_0x40658c(0x28d)]['callUpdate' + _0x40658c(0x18b)]),
    (Window_ItemList['prototype']['callUpdate' + _0x40658c(0x18b)] = function () {
        const _0x5c016c = _0x40658c;
        (VisuMZ[_0x5c016c(0x22d) + _0x5c016c(0x261)][
            _0x5c016c(0x18d) + _0x5c016c(0x188) + 'UpdateHelp'
        ][_0x5c016c(0x282)](this),
            this[_0x5c016c(0x1c8) + 'mInventory' + _0x5c016c(0x25d) + 'dow'] &&
                (this[_0x5c016c(0x1c8) + _0x5c016c(0x2a0) + _0x5c016c(0x25d) + _0x5c016c(0x29b)][
                    _0x5c016c(0x215)
                ](this[_0x5c016c(0x269)]()),
                this[_0x5c016c(0x196) + _0x5c016c(0x20a)] &&
                    this[_0x5c016c(0x1c8) + _0x5c016c(0x2a0) + _0x5c016c(0x25d) + _0x5c016c(0x29b)][
                        'refresh'
                    ]()));
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x18d) + _0x40658c(0x1d8) + _0x40658c(0x18f) + _0x40658c(0x221)
    ] = Window_ItemList['prototype'][_0x40658c(0x1ff) + 'ckground']),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1ff) + _0x40658c(0x15c)] = function (_0x4f9334) {
        const _0x131909 = _0x40658c;
        this[_0x131909(0x1a2) + _0x131909(0x27e) + _0x131909(0x208)]()
            ? this[_0x131909(0x1ff) + _0x131909(0x200) + _0x131909(0x15b) + _0x131909(0x16f)](
                  _0x4f9334
              )
            : VisuMZ[_0x131909(0x22d) + _0x131909(0x261)][
                  _0x131909(0x18d) + _0x131909(0x1d8) + _0x131909(0x18f) + _0x131909(0x221)
              ][_0x131909(0x282)](this, _0x4f9334);
        const _0x1003c9 = this[_0x131909(0x220)](_0x4f9334);
        this[_0x131909(0x2b4) + 'oundRect'](_0x1003c9);
    }),
    (Window_ItemList[_0x40658c(0x28d)][
        _0x40658c(0x1ff) + 'ckgroundVi' + _0x40658c(0x15b) + _0x40658c(0x16f)
    ] = function (_0x21cba3) {
        const _0x3a9552 = _0x40658c,
            _0x86e4c9 = {
                XofQA: function (_0x1fa21a, _0x31c613) {
                    return _0x1fa21a(_0x31c613);
                },
                dXOrx: function (_0x423fd7, _0xabf206) {
                    return _0x423fd7 + _0xabf206;
                },
                dKmBm: function (_0x57b09e, _0x99c3e1) {
                    return _0x57b09e(_0x99c3e1);
                },
            },
            _0x23936f = this[_0x3a9552(0x186)](_0x21cba3);
        if (!_0x23936f) {
            VisuMZ[_0x3a9552(0x22d) + _0x3a9552(0x261)][
                _0x3a9552(0x18d) + _0x3a9552(0x1d8) + 'ItemBackgr' + 'ound'
            ][_0x3a9552(0x282)](this, _0x21cba3);
            return;
        }
        const _0xd81a71 = VisuMZ['VisualItem' + 'Inv'][_0x3a9552(0x1de)],
            _0x69ff7e = _0x23936f[_0x3a9552(0x191)];
        let _0x51f2ae = ColorManager['itemBackCo' + 'lor1'](),
            _0x59e984 = ColorManager[_0x3a9552(0x29e) + 'lor2']();
        _0x69ff7e[_0x3a9552(0x24f)](_0xd81a71[_0x3a9552(0x26f) + '1']) &&
            (_0x51f2ae = ColorManager['textColor'](
                _0x86e4c9[_0x3a9552(0x24c)](Number, RegExp['$1'])
            ));
        _0x69ff7e['match'](_0xd81a71['bgColorNum' + '2']) &&
            (_0x59e984 = ColorManager['textColor'](
                _0x86e4c9[_0x3a9552(0x24c)](Number, RegExp['$1'])
            ));
        _0x69ff7e[_0x3a9552(0x24f)](_0xd81a71[_0x3a9552(0x23a) + '1']) &&
            (_0x51f2ae = _0x86e4c9[_0x3a9552(0x273)](
                '#',
                _0x86e4c9[_0x3a9552(0x1c4)](String, RegExp['$1'])
            ));
        _0x69ff7e[_0x3a9552(0x24f)](_0xd81a71[_0x3a9552(0x23a) + '2']) &&
            (_0x59e984 = _0x86e4c9['dXOrx'](
                '#',
                _0x86e4c9[_0x3a9552(0x24c)](String, RegExp['$1'])
            ));
        const _0xb8ed85 = this['itemRect'](_0x21cba3),
            _0x1a58c5 = _0xb8ed85['x'],
            _0xf52bea = _0xb8ed85['y'],
            _0x299fc6 = _0xb8ed85['width'],
            _0x6910d8 = _0xb8ed85['height'];
        ((this[_0x3a9552(0x2a3) + 'ck']['paintOpaci' + 'ty'] = -0xd95 + 0xc2f + -0x1 * -0x265),
            this[_0x3a9552(0x2a3) + 'ck'][_0x3a9552(0x1cd) + 'llRect'](
                _0x1a58c5,
                _0xf52bea,
                _0x299fc6,
                _0x6910d8,
                _0x51f2ae,
                _0x59e984,
                !![]
            ),
            this[_0x3a9552(0x2a3) + 'ck']['strokeRect'](
                _0x1a58c5,
                _0xf52bea,
                _0x299fc6,
                _0x6910d8,
                _0x51f2ae
            ));
    }),
    (VisuMZ['VisualItem' + _0x40658c(0x261)][_0x40658c(0x26e) + _0x40658c(0x271)] = function (
        _0x326a97
    ) {
        const _0x4d2deb = _0x40658c,
            _0x2a1e23 = {
                bxfMc: function (_0xba5610, _0x3c3b55) {
                    return _0xba5610 === _0x3c3b55;
                },
                FRdjt: function (_0x525c11, _0x2121d4) {
                    return _0x525c11 + _0x2121d4;
                },
                eNcur: function (_0x178564, _0xee67eb) {
                    return _0x178564 + _0xee67eb;
                },
                JYbzS: function (_0x22b67d, _0xb518ca) {
                    return _0x22b67d + _0xb518ca;
                },
                kHtrL: function (_0x23453b, _0x26efe1) {
                    return _0x23453b + _0x26efe1;
                },
                JNaoa: function (_0x4203e9, _0x2b554c, _0x1d373a) {
                    return _0x4203e9(_0x2b554c, _0x1d373a);
                },
                KlFkG: function (_0x264cb6, _0x15fc6d, _0x2161bc) {
                    return _0x264cb6(_0x15fc6d, _0x2161bc);
                },
                msOKD: function (_0x5b652a, _0x4f727d) {
                    return _0x5b652a + _0x4f727d;
                },
                WzLdO: function (_0x108007, _0x11f941) {
                    return _0x108007 + _0x11f941;
                },
                hhZJz: _0x4d2deb(0x15a),
                VeOvO: _0x4d2deb(0x1c7),
            };
        _0x326a97 = _0x326a97[_0x4d2deb(0x22b)]('#', '');
        _0x2a1e23[_0x4d2deb(0x1b5)](_0x326a97[_0x4d2deb(0x262)], -0x15 * 0xfc + -0xb5d + 0x200c) &&
            (_0x326a97 = _0x2a1e23[_0x4d2deb(0x292)](
                _0x2a1e23['FRdjt'](
                    _0x2a1e23['eNcur'](
                        _0x2a1e23[_0x4d2deb(0x1ac)](
                            _0x2a1e23[_0x4d2deb(0x15d)](
                                _0x326a97[-0x8a1 + 0x381 + 0x520],
                                _0x326a97[0x598 * 0x2 + 0x89 * 0x1 + 0xbb9 * -0x1]
                            ),
                            _0x326a97[-0x25f * 0x3 + -0x1 * -0x2473 + 0x9c7 * -0x3]
                        ),
                        _0x326a97[0x2485 * -0x1 + -0x3 * 0x83c + -0x22 * -0x1cd]
                    ),
                    _0x326a97[-0x1d1c + -0x1 * 0x1a9f + -0x13 * -0x2ef]
                ),
                _0x326a97[-0x8d5 + -0x284 * 0x5 + -0x156b * -0x1]
            ));
        var _0x1e65c9 = _0x2a1e23[_0x4d2deb(0x158)](
                parseInt,
                _0x326a97[_0x4d2deb(0x1f2)](
                    -0x15e0 + -0x190f + 0x2eef,
                    0x1 * 0x9aa + -0x1a52 + 0x2c7 * 0x6
                ),
                -0x12ba + -0x1e1 * 0xf + 0x2ef9
            ),
            _0x2236b3 = _0x2a1e23[_0x4d2deb(0x158)](
                parseInt,
                _0x326a97['substring'](
                    0x1afb * 0x1 + -0xcd * -0x7 + -0x2094,
                    0x281 * -0x3 + -0x54 * 0x4d + 0x20cb
                ),
                -0x33f + -0xd3c + 0x108b
            ),
            _0x68d0d = _0x2a1e23[_0x4d2deb(0x168)](
                parseInt,
                _0x326a97['substring'](
                    -0x37 * 0x79 + 0x5 * 0x266 + 0xe05,
                    -0x227e + -0x4f * 0x1 + 0x22d3 * 0x1
                ),
                -0x3d * -0x5b + -0x1e56 + 0x8b7
            );
        return _0x2a1e23[_0x4d2deb(0x292)](
            _0x2a1e23['JYbzS'](
                _0x2a1e23[_0x4d2deb(0x1d7)](
                    _0x2a1e23['FRdjt'](
                        _0x2a1e23['eNcur'](
                            _0x2a1e23[_0x4d2deb(0x292)](
                                _0x2a1e23[_0x4d2deb(0x15d)](
                                    _0x2a1e23[_0x4d2deb(0x1c5)](
                                        _0x2a1e23[_0x4d2deb(0x1ad)],
                                        _0x1e65c9
                                    ),
                                    ','
                                ),
                                _0x2236b3
                            ),
                            ','
                        ),
                        _0x68d0d
                    ),
                    ','
                ),
                _0x2a1e23[_0x4d2deb(0x299)]
            ),
            ')'
        );
    }),
    (VisuMZ[_0x40658c(0x22d) + 'Inv'][_0x40658c(0x1a8) + _0x40658c(0x275) + _0x40658c(0x1aa)] =
        Window_Base[_0x40658c(0x28d)][_0x40658c(0x224) + 'mber']),
    (Window_Base[_0x40658c(0x28d)][_0x40658c(0x224) + _0x40658c(0x1bc)] = function (
        _0x7bd10b,
        _0x1d4fd1,
        _0x6c035e,
        _0x161bfc
    ) {
        const _0x3c1d7a = _0x40658c;
        this[_0x3c1d7a(0x1a2) + _0x3c1d7a(0x27e) + _0x3c1d7a(0x208)] &&
        this['usesVisual' + 'ItemInvent' + _0x3c1d7a(0x208)]()
            ? this[_0x3c1d7a(0x224) + _0x3c1d7a(0x24a) + 'ItemInvent' + _0x3c1d7a(0x208)](
                  _0x7bd10b,
                  _0x1d4fd1,
                  _0x6c035e,
                  _0x161bfc
              )
            : VisuMZ[_0x3c1d7a(0x22d) + _0x3c1d7a(0x261)][
                  _0x3c1d7a(0x1a8) + 'e_drawItem' + _0x3c1d7a(0x1aa)
              ][_0x3c1d7a(0x282)](this, _0x7bd10b, _0x1d4fd1, _0x6c035e, _0x161bfc);
    }),
    (Window_Base[_0x40658c(0x28d)][_0x40658c(0x224) + 'mberVisual' + _0x40658c(0x27e) + 'ory'] =
        function (_0x38465, _0x1631b7, _0x1a9ab1, _0x2649ea) {
            const _0x1df375 = _0x40658c,
                _0xaf3f7 = { XDsIu: _0x1df375(0x248) };
            if (this[_0x1df375(0x22c) + _0x1df375(0x1aa)](_0x38465)) {
                this[_0x1df375(0x2a2) + 'lItemInvFo' + 'ntSettings']();
                const _0x44bc21 =
                        VisuMZ[_0x1df375(0x199) + _0x1df375(0x1a6)][_0x1df375(0x1c1)][
                            _0x1df375(0x240)
                        ],
                    _0x50e027 = _0x44bc21[_0x1df375(0x15f) + 'tyFmt'],
                    _0x10e4bc = _0x50e027['format']($gameParty[_0x1df375(0x1df)](_0x38465));
                ((this['contents']['fontSize'] = _0x44bc21[_0x1df375(0x15f) + 'tyFontSize']),
                    this[_0x1df375(0x1cc)](
                        _0x10e4bc,
                        _0x1631b7,
                        _0x1a9ab1,
                        _0x2649ea,
                        _0xaf3f7['XDsIu']
                    ),
                    this['resetFontS' + _0x1df375(0x1a3)]());
            }
        }),
    (VisuMZ[_0x40658c(0x22d) + 'Inv'][_0x40658c(0x18d) + 'mList_plac' + _0x40658c(0x28f) + 'bel'] =
        Window_ItemList['prototype'][_0x40658c(0x23c) + _0x40658c(0x21c)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x23c) + _0x40658c(0x21c)] = function (_0x3ebd46) {
        const _0xe10029 = _0x40658c;
        this[_0xe10029(0x1a2) + _0xe10029(0x27e) + _0xe10029(0x208)]()
            ? this[_0xe10029(0x23c) + _0xe10029(0x276) + _0xe10029(0x20c) + _0xe10029(0x1e2)](
                  _0x3ebd46
              )
            : VisuMZ[_0xe10029(0x22d) + 'Inv'][
                  _0xe10029(0x18d) + _0xe10029(0x233) + _0xe10029(0x28f) + _0xe10029(0x236)
              ][_0xe10029(0x282)](this, _0x3ebd46);
    }),
    (Window_ItemList[_0x40658c(0x28d)][
        _0x40658c(0x23c) + _0x40658c(0x276) + _0x40658c(0x20c) + 'entory'
    ] = function (_0x3dd48d) {
        const _0x3c2721 = _0x40658c,
            _0x43e0e5 = {
                HAzXB: function (_0x1235e6, _0x1ecd15) {
                    return _0x1235e6 + _0x1ecd15;
                },
            };
        if (!Imported[_0x3c2721(0x1c6) + _0x3c2721(0x171) + _0x3c2721(0x1c9)]) return;
        const _0x330f7f = this[_0x3c2721(0x186)](_0x3dd48d);
        if (!_0x330f7f || !this['isShowNew']()) return;
        if (!$gameParty[_0x3c2721(0x1a0)](_0x330f7f)) return;
        const _0x4b972b = this[_0x3c2721(0x1af) + _0x3c2721(0x2b1)](_0x3dd48d),
            _0x4715cf = _0x4b972b['x'],
            _0x27e455 = _0x4b972b['y'],
            _0x87e8e2 =
                VisuMZ[_0x3c2721(0x199) + 'sCore'][_0x3c2721(0x1c1)][_0x3c2721(0x2a8)][
                    _0x3c2721(0x270)
                ],
            _0x3e4671 =
                VisuMZ[_0x3c2721(0x199) + _0x3c2721(0x1a6)]['Settings'][_0x3c2721(0x2a8)][
                    _0x3c2721(0x264)
                ];
        this[_0x3c2721(0x2a1) + 'bel'](
            _0x330f7f,
            _0x43e0e5[_0x3c2721(0x1d4)](_0x4715cf, _0x87e8e2),
            _0x43e0e5[_0x3c2721(0x1d4)](_0x27e455, _0x3e4671)
        );
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x18d) + _0x40658c(0x233) + _0x40658c(0x279) + _0x40658c(0x24b)
    ] = Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1e9) + _0x40658c(0x198)]),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x1e9) + _0x40658c(0x198)] = function (_0x4f4f4b) {
        const _0x353234 = _0x40658c;
        this[_0x353234(0x1a2) + 'ItemInvent' + _0x353234(0x208)]()
            ? this[_0x353234(0x1e9) + _0x353234(0x25b) + _0x353234(0x1e0) + _0x353234(0x15e)](
                  _0x4f4f4b
              )
            : VisuMZ[_0x353234(0x22d) + _0x353234(0x261)][
                  _0x353234(0x18d) + 'mList_plac' + _0x353234(0x279) + _0x353234(0x24b)
              ][_0x353234(0x282)](this, _0x4f4f4b);
    }),
    (Window_ItemList['prototype'][
        _0x40658c(0x1e9) + _0x40658c(0x25b) + _0x40658c(0x1e0) + _0x40658c(0x15e)
    ] = function (_0x2df898) {
        const _0x497993 = _0x40658c,
            _0x111a63 = {
                WFJjn: function (_0x40b58e, _0x11337a) {
                    return _0x40b58e + _0x11337a;
                },
            };
        if (!Imported[_0x497993(0x260) + 'uestSystem']) return;
        const _0xa7110b = this['itemAt'](_0x2df898);
        if (!_0xa7110b || !this[_0x497993(0x174) + 't']()) return;
        if (!$gameParty[_0x497993(0x227) + 'm'](_0xa7110b)) return;
        const _0x139250 = this[_0x497993(0x1af) + _0x497993(0x2b1)](_0x2df898),
            _0x198099 = _0x139250['x'],
            _0x36ba94 = _0x139250['y'],
            _0x3d4b54 =
                VisuMZ[_0x497993(0x284) + 'm'][_0x497993(0x1c1)][_0x497993(0x24b)]['OffsetX'],
            _0x48514d =
                VisuMZ[_0x497993(0x284) + 'm'][_0x497993(0x1c1)][_0x497993(0x24b)][
                    _0x497993(0x264)
                ];
        this[_0x497993(0x194) + _0x497993(0x24b)](
            _0xa7110b,
            _0x111a63[_0x497993(0x1ec)](_0x198099, _0x3d4b54),
            _0x111a63[_0x497993(0x1ec)](_0x36ba94, _0x48514d)
        );
    }),
    (Window_ItemList[_0x40658c(0x28d)][_0x40658c(0x16a) + 'plifyConfi' + 'rm'] = function (
        _0x35983c
    ) {
        const _0x50c852 = _0x40658c,
            _0x570bd2 = this['itemRectWi' + _0x50c852(0x2b1)](_0x35983c);
        (this[_0x50c852(0x207) + _0x50c852(0x237)](this['isEnabled'](null)),
            this['resetFontS' + _0x50c852(0x1a3)]());
        const _0x2a0df6 =
            Window_BattleItem['ITEM_AMPLI' + _0x50c852(0x1d5) + 'S']['confirmIco' + 'n'];
        this[_0x50c852(0x1ba) + 'n'](_0x2a0df6, _0x570bd2);
    }),
    (VisuMZ[_0x40658c(0x22d) + 'Inv'][_0x40658c(0x225) + 'ipItem_max' + _0x40658c(0x247)] =
        Window_EquipItem[_0x40658c(0x28d)][_0x40658c(0x257)]),
    (Window_EquipItem[_0x40658c(0x28d)][_0x40658c(0x257)] = function () {
        const _0x47d65a = _0x40658c;
        return this[_0x47d65a(0x1a2) + 'ItemInvent' + _0x47d65a(0x208)]()
            ? Window_ItemList[_0x47d65a(0x28d)][_0x47d65a(0x257)][_0x47d65a(0x282)](this)
            : VisuMZ[_0x47d65a(0x22d) + _0x47d65a(0x261)][
                  _0x47d65a(0x225) + _0x47d65a(0x1cf) + 'Cols'
              ][_0x47d65a(0x282)](this);
    }),
    (VisuMZ['VisualItem' + _0x40658c(0x261)]['Window_Equ' + 'ipItem_col' + _0x40658c(0x255)] =
        Window_EquipItem['prototype'][_0x40658c(0x1e4)]),
    (Window_EquipItem['prototype'][_0x40658c(0x1e4)] = function () {
        const _0x2bd667 = _0x40658c;
        return this[_0x2bd667(0x1a2) + 'ItemInvent' + _0x2bd667(0x208)]()
            ? Window_ItemList['prototype'][_0x2bd667(0x1e4)][_0x2bd667(0x282)](this)
            : VisuMZ['VisualItem' + _0x2bd667(0x261)][
                  _0x2bd667(0x225) + _0x2bd667(0x17f) + _0x2bd667(0x255)
              ]['call'](this);
    }),
    (Window_EquipItem[_0x40658c(0x28d)][_0x40658c(0x239) + _0x40658c(0x2a9)] = function (
        _0x10c26b
    ) {
        const _0x1a955f = _0x40658c,
            _0x5f2fa4 = this[_0x1a955f(0x1af) + _0x1a955f(0x2b1)](_0x10c26b),
            _0x20a866 = VisuMZ[_0x1a955f(0x199) + 'sCore'][_0x1a955f(0x1c1)][_0x1a955f(0x16b)],
            _0x26713a = _0x20a866[_0x1a955f(0x274) + _0x1a955f(0x160)];
        (this[_0x1a955f(0x207) + _0x1a955f(0x237)](![]),
            this['drawBigIco' + 'n'](_0x26713a, _0x5f2fa4),
            this[_0x1a955f(0x207) + _0x1a955f(0x237)](!![]));
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        _0x40658c(0x1a4) + _0x40658c(0x28e) + _0x40658c(0x214)
    ] = Window_ShopSell['prototype']['maxCols']),
    (Window_ShopSell[_0x40658c(0x28d)][_0x40658c(0x257)] = function () {
        const _0x657071 = _0x40658c;
        return this[_0x657071(0x1a2) + _0x657071(0x27e) + 'ory']()
            ? Window_ItemList[_0x657071(0x28d)]['maxCols']['call'](this)
            : VisuMZ[_0x657071(0x22d) + _0x657071(0x261)][
                  _0x657071(0x1a4) + _0x657071(0x28e) + _0x657071(0x214)
              ][_0x657071(0x282)](this);
    }),
    (VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][
        'Window_Sho' + _0x40658c(0x1ce) + _0x40658c(0x1be)
    ] = Window_ShopSell[_0x40658c(0x28d)][_0x40658c(0x1e4)]),
    (Window_ShopSell[_0x40658c(0x28d)][_0x40658c(0x1e4)] = function () {
        const _0x1b3142 = _0x40658c;
        return this[_0x1b3142(0x1a2) + _0x1b3142(0x27e) + 'ory']()
            ? Window_ItemList[_0x1b3142(0x28d)][_0x1b3142(0x1e4)]['call'](this)
            : VisuMZ[_0x1b3142(0x22d) + _0x1b3142(0x261)][
                  _0x1b3142(0x1a4) + _0x1b3142(0x1ce) + _0x1b3142(0x1be)
              ][_0x1b3142(0x282)](this);
    }));
function Window_VisualItemTooltip() {
    this['initialize'](...arguments);
}
((Window_VisualItemTooltip['prototype'] = Object[_0x40658c(0x1f8)](Window_Base['prototype'])),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x1f1) + 'r'] = Window_VisualItemTooltip),
    (Window_VisualItemTooltip[_0x40658c(0x1e1)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)][
            _0x40658c(0x1ef) + _0x40658c(0x21b)
        ]),
    (Window_VisualItemTooltip[_0x40658c(0x2a7) + 'TH'] =
        VisuMZ['VisualItem' + _0x40658c(0x261)]['Settings'][_0x40658c(0x19c) + _0x40658c(0x192)]),
    (Window_VisualItemTooltip['FONT_SIZE'] =
        VisuMZ[_0x40658c(0x22d) + 'Inv'][_0x40658c(0x1c1)]['TooltipFon' + _0x40658c(0x2af)]),
    (Window_VisualItemTooltip[_0x40658c(0x232)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)][
            _0x40658c(0x1a7) + _0x40658c(0x245)
        ]),
    (Window_VisualItemTooltip[_0x40658c(0x184)] =
        VisuMZ[_0x40658c(0x22d) + _0x40658c(0x261)][_0x40658c(0x1c1)]['TooltipOff' + 'setY']),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x2a5)] = function (_0x33bbdc) {
        const _0xb32294 = _0x40658c;
        this[_0xb32294(0x291) + _0xb32294(0x29b)] = _0x33bbdc;
        const _0x7495f3 = new Rectangle(
            0xa6 * 0xa + 0x2 * -0x1369 + -0x102b * -0x2,
            0x485 + -0x1173 + 0x1 * 0xcee,
            -0x180a * 0x1 + 0x709 * 0x1 + 0x1101 * 0x1,
            this[_0xb32294(0x1b6)]()
        );
        (Window_Base[_0xb32294(0x28d)][_0xb32294(0x2a5)][_0xb32294(0x282)](this, _0x7495f3),
            (this[_0xb32294(0x1a1)] = ![]),
            (this[_0xb32294(0x226) + 'y'] = -0xa3a + -0x986 + 0x14bf),
            (this[_0xb32294(0x18c)] = 0xc * 0x83 + 0x44a * -0x9 + -0x23b * -0xf),
            (this[_0xb32294(0x265)] = null));
    }),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x165) + _0x40658c(0x175)] = function () {
        this['padding'] = -0x19de + -0xcfa + 0x26d8;
    }),
    (Window_VisualItemTooltip['prototype']['setItem'] = function (_0x415dcc) {
        const _0x456dad = _0x40658c,
            _0x471398 = {
                FdNOq: function (_0x303879, _0x1887bf) {
                    return _0x303879 === _0x1887bf;
                },
            };
        if (
            _0x471398[_0x456dad(0x1ab)](this[_0x456dad(0x265)], _0x415dcc) &&
            !this['_amplifySk' + 'ill']
        )
            return;
        ((this[_0x456dad(0x265)] = _0x415dcc), this[_0x456dad(0x16c)]());
    }),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x22f) + _0x40658c(0x267)] = function () {
        const _0x395fbe = _0x40658c;
        if (
            this[_0x395fbe(0x291) + _0x395fbe(0x29b)] &&
            this[_0x395fbe(0x291) + _0x395fbe(0x29b)][_0x395fbe(0x196) + _0x395fbe(0x20a)]
        ) {
            if (!this[_0x395fbe(0x265)]) return !![];
        }
        return !!this[_0x395fbe(0x265)];
    }),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x1d6) + 'e'] = function () {
        const _0x1b684b = _0x40658c;
        if (
            this[_0x1b684b(0x291) + 'dow'] &&
            this[_0x1b684b(0x291) + _0x1b684b(0x29b)]['_amplifySk' + _0x1b684b(0x20a)] &&
            !this[_0x1b684b(0x265)]
        )
            return TextManager[_0x1b684b(0x166) + 'FY_CONFIRM'];
        return this[_0x1b684b(0x265)] ? this[_0x1b684b(0x265)]['name'] : '';
    }),
    (Window_VisualItemTooltip['prototype'][_0x40658c(0x16c)] = function () {
        const _0x265991 = _0x40658c,
            _0x2b6ee4 = {
                xwDJQ: function (_0x4a4dc1, _0x3ed446) {
                    return _0x4a4dc1 + _0x3ed446;
                },
                aTnET: _0x265991(0x22e),
            };
        this['contents']['clear']();
        if (!this[_0x265991(0x22f) + _0x265991(0x267)]()) return;
        (this[_0x265991(0x1e6) + _0x265991(0x1a3)](),
            (this[_0x265991(0x272)][_0x265991(0x2aa)] =
                Window_VisualItemTooltip[_0x265991(0x1ae)]));
        const _0x4e63c3 = this[_0x265991(0x1d6) + 'e'](),
            _0x29160c = _0x2b6ee4[_0x265991(0x1fd)](
                this[_0x265991(0x280)](_0x4e63c3),
                Window_VisualItemTooltip[_0x265991(0x2a7) + 'TH']
            );
        ((this['width'] = Math[_0x265991(0x1ed)](_0x29160c)),
            this['createCont' + _0x265991(0x163)](),
            (this[_0x265991(0x272)][_0x265991(0x2aa)] = Window_VisualItemTooltip['FONT_SIZE']));
        if (Imported[_0x265991(0x1c6) + _0x265991(0x171) + _0x265991(0x1c9)]) {
            const _0x2282d8 = ColorManager[_0x265991(0x2a6) + 'or'](this['_item']);
            this['changeText' + 'Color'](_0x2282d8);
        }
        (this[_0x265991(0x1cc)](
            _0x4e63c3,
            -0x743 + 0xfc5 + 0xc6 * -0xb,
            -0xb * 0x2e0 + -0x8b8 + 0x2858,
            this[_0x265991(0x21f)],
            _0x2b6ee4[_0x265991(0x1e5)]
        ),
            this[_0x265991(0x229) + _0x265991(0x253)](),
            this[_0x265991(0x1ca) + _0x265991(0x222)](Window_VisualItemTooltip[_0x265991(0x1e1)]));
    }),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x1b3)] = function () {
        const _0x112e68 = _0x40658c;
        (Window_Base[_0x112e68(0x28d)]['update'][_0x112e68(0x282)](this),
            this[_0x112e68(0x1b0) + _0x112e68(0x250)](),
            this[_0x112e68(0x27c) + 'tion']());
    }),
    (Window_VisualItemTooltip[_0x40658c(0x28d)][_0x40658c(0x1b0) + 'bility'] = function () {
        const _0x21dc0d = _0x40658c,
            _0x31b745 = {
                WaOkL: function (_0x5c7dfd, _0xec6d28) {
                    return _0x5c7dfd !== _0xec6d28;
                },
            },
            _0x16db52 = this['visible'];
        ((this['visible'] =
            this[_0x21dc0d(0x265)] &&
            this['_parentWin' + _0x21dc0d(0x29b)][_0x21dc0d(0x1d0)] &&
            this['_parentWin' + 'dow'][_0x21dc0d(0x176)]()),
            this['_parentWin' + _0x21dc0d(0x29b)] &&
                this[_0x21dc0d(0x291) + _0x21dc0d(0x29b)][_0x21dc0d(0x196) + _0x21dc0d(0x20a)] &&
                !this[_0x21dc0d(0x265)] &&
                (this[_0x21dc0d(0x1a1)] = !![]),
            _0x31b745[_0x21dc0d(0x1f9)](_0x16db52, this[_0x21dc0d(0x1a1)]) &&
                SceneManager[_0x21dc0d(0x212)][_0x21dc0d(0x202)](this));
    }),
    (Window_VisualItemTooltip['prototype'][_0x40658c(0x27c) + _0x40658c(0x2b5)] = function () {
        const _0xfcd874 = _0x40658c,
            _0x3d88ce = {
                bUnEe: function (_0x15b178, _0x480341) {
                    return _0x15b178 + _0x480341;
                },
                aGKZp: function (_0x15c2ad, _0x1cc244) {
                    return _0x15c2ad - _0x1cc244;
                },
                xMKFc: function (_0x38f617, _0x577f88) {
                    return _0x38f617 + _0x577f88;
                },
                EezKs: function (_0x334357, _0x3e552b) {
                    return _0x334357 / _0x3e552b;
                },
                bfnqt: function (_0x58157d, _0x1a6116) {
                    return _0x58157d / _0x1a6116;
                },
                uVHFT: function (_0x185fd7, _0x820a50) {
                    return _0x185fd7 + _0x820a50;
                },
                qmRCb: function (_0x321dae, _0x30d42c) {
                    return _0x321dae - _0x30d42c;
                },
                HXtZQ: function (_0x47bee2, _0x305e28) {
                    return _0x47bee2 + _0x305e28;
                },
                KdkkJ: function (_0x567c61, _0x4a9c21) {
                    return _0x567c61 - _0x4a9c21;
                },
            };
        if (!this[_0xfcd874(0x1a1)]) return;
        const _0x2c4a3a = SceneManager[_0xfcd874(0x212)][_0xfcd874(0x29f) + 'er'],
            _0x2dc5e5 = this[_0xfcd874(0x291) + _0xfcd874(0x29b)];
        let _0x3b4f06 = _0x3d88ce['bUnEe'](_0x2dc5e5['x'], _0x2c4a3a['x']),
            _0x2f070b = _0x3d88ce[_0xfcd874(0x164)](_0x2dc5e5['y'], _0x2c4a3a['y']);
        const _0x59ca83 = _0x2dc5e5[_0xfcd874(0x20d) + 't'],
            _0x23bd73 = _0x2dc5e5[_0xfcd874(0x1b1) + 'a'];
        ((_0x3b4f06 += _0x3d88ce[_0xfcd874(0x164)](
            _0x3d88ce[_0xfcd874(0x20b)](
                _0x3d88ce[_0xfcd874(0x16d)](
                    _0x59ca83['x'],
                    _0x3d88ce[_0xfcd874(0x1fe)](
                        _0x59ca83[_0xfcd874(0x25c)],
                        -0x18d1 * -0x1 + -0xbe9 * 0x3 + 0xaec
                    )
                ),
                _0x3d88ce[_0xfcd874(0x27d)](this[_0xfcd874(0x25c)], 0x8dc + -0x1 * 0xa1c + 0x142)
            ),
            _0x23bd73['x']
        )),
            (_0x2f070b += _0x3d88ce[_0xfcd874(0x1a5)](
                _0x3d88ce[_0xfcd874(0x20b)](_0x59ca83['y'], this[_0xfcd874(0x179)]),
                _0x23bd73['y']
            )));
        let _0x1ad90f = _0x3d88ce[_0xfcd874(0x164)](
            _0x3d88ce[_0xfcd874(0x19f)](
                _0x3d88ce['HXtZQ'](_0x2dc5e5['y'], _0x2c4a3a['y']),
                this[_0xfcd874(0x179)]
            ),
            _0x2dc5e5[_0xfcd874(0x218)]
        );
        ((_0x1ad90f += Window_VisualItemTooltip[_0xfcd874(0x184)]),
            (_0x3b4f06 += Window_VisualItemTooltip[_0xfcd874(0x232)]),
            (_0x2f070b += Window_VisualItemTooltip[_0xfcd874(0x184)]),
            (this['x'] = Math[_0xfcd874(0x18a)](_0x3b4f06)[_0xfcd874(0x1cb)](
                -0x595 + -0xe78 + 0x140d,
                _0x3d88ce[_0xfcd874(0x20b)](Graphics[_0xfcd874(0x25c)], this[_0xfcd874(0x25c)])
            )),
            (this['y'] = Math[_0xfcd874(0x18a)](_0x2f070b)[_0xfcd874(0x1cb)](
                0x13a4 + 0x7a0 + -0x1b44,
                _0x3d88ce[_0xfcd874(0x1c2)](Graphics[_0xfcd874(0x179)], this[_0xfcd874(0x179)])
            )));
    }));
