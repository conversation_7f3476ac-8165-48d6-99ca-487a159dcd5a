//=============================================================================
// Drag_randSkillChange.js
//=============================================================================

/*:
 * @target MZ
 * @plugindesc A plugin to set a chance for skill to trigger another skill instead.
 * v1.0.0
 * <AUTHOR>
 *
 * @url
 *
 * @help
 *
 * Use this notetag in a skill notes :
 *
 * <randChange: skillId, variableId>
 *
 * I.E : <randChange: 14, 5>
 *
 * will make your skill have a % chance defined by variable 5 to change into skill
 * 14 instead.
 * (force the skill, no need to be learned)
 *
 * You can also use the notetag
 *
 * <randChangeReqSkill: skillId, skillId, ...>
 *
 * to set some required skill to be learned in order for the <randChange> notetag
 * to happen.
 *
 * I.E : <randChangeReqSkill: 10>
 */

var Imported = Imported || {};
Imported.DragRandSkillChange = true;

var Drag = Drag || {};
Drag.randSkillChange = Drag.randSkillChange || {};
Drag.randSkillChange.alias = {};

(function () {
    Drag.randSkillChange.BattleManager_processTurn = BattleManager.processTurn;
    BattleManager.processTurn = function () {
        Drag.randSkillChange.checkAltSkill(this._subject, this._subject.currentAction());
        Drag.randSkillChange.BattleManager_processTurn.call(this);
    };

    Drag.randSkillChange.checkAltSkill = function (subject, action) {
        if (!subject || !action) return;
        if (action?._item?._dataClass === 'skill') {
            let skill = $dataSkills[action._item._itemId];
            if (skill?.meta?.randChange) {
                let req = true;
                if (skill?.meta?.randChangeReqSkill)
                    for (skillReq of skill.meta.randChangeReqSkill.split(','))
                        if (!subject.isLearnedSkill(parseInt(skillReq))) req = false;
                let data = skill.meta.randChange.split(',');
                let skillId = parseInt(data[0]);
                let gamevar = parseInt(data[1]);
                let roll = Math.randomInt(100);
                if (roll < $gameVariables.value(gamevar) && req) action.setSkill(skillId);
            }
        }
    };
})();
