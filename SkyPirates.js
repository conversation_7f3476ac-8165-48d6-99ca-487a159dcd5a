/*:
 * @target MZ
 * @plugindesc Sky Pirates Airship Shooter v1.1.1 - Optimized Edition
 * <AUTHOR> Assistant
 *
 * @command StartSkyPirates
 * @text Start Sky Pirates
 * @desc Start the Sky Pirates airship shooter minigame
 *
 * @help SkyPirates.js
 *
 * A side-scrolling airship shooter where you pilot a flying ship through
 * cloud-filled skies, battling enemy airships and sky creatures.
 *
 * NEW FEATURES:
 * - Multiple weapon types (Laser, Plasma, Gatling, Rocket)
 * - Special abilities (Overcharge, Time Warp, Rapid Repair, Chain Lightning)
 * - Boss battles every 5 levels
 * - Enhanced visual effects and particle systems
 * - Power-up combinations and mega shots
 *
 * OPTIMIZATIONS:
 * - Improved object pooling with pre-allocation
 * - Spatial partitioning for collision detection
 * - Reduced garbage collection with object reuse
 * - Optimized rendering with dirty flag system
 * - Frame-rate adaptive updates
 *
 * Controls:
 * - Arrow Keys: Move ship
 * - Z/Enter: Fire primary weapon
 * - X: Fire secondary weapon
 * - C: Activate special ability
 * - V: Switch weapon type
 * - B: Switch special ability
 * - ESC: Exit game
 */

(() => {
    'use strict';

    // Pre-allocated object pools for better performance
    const SpatialGrid = {
        cellSize: 100,
        grid: new Map(),
        getKey: (x, y) => `${Math.floor(x / 100)},${Math.floor(y / 100)}`,
        clear: () => SpatialGrid.grid.clear(),
        add: (obj, x, y) => {
            const key = SpatialGrid.getKey(x, y);
            if (!SpatialGrid.grid.has(key)) SpatialGrid.grid.set(key, []);
            SpatialGrid.grid.get(key).push(obj);
        },
        getNearby: (x, y, radius = 100) => {
            const nearby = [];
            const centerKey = SpatialGrid.getKey(x, y);
            const [cx, cy] = centerKey.split(',').map(Number);
            for (let dx = -1; dx <= 1; dx++) {
                for (let dy = -1; dy <= 1; dy++) {
                    const key = `${cx + dx},${cy + dy}`;
                    const cell = SpatialGrid.grid.get(key);
                    if (cell) nearby.push(...cell);
                }
            }
            return nearby;
        },
    };

    // Add controller button mappings
    Input.keyMapper[89] = 'shift'; // Y key on keyboard
    Input.gamepadMapper[3] = 'shift'; // Y button on Xbox controller
    Input.gamepadMapper[4] = 'tab'; // L2 trigger on Xbox controller
    Input.gamepadMapper[5] = 'control'; // R2 trigger on Xbox controller

    const pluginName = 'SkyPirates';

    // Game state with optimized data structures
    let gameData = {
        score: 0,
        lives: 3,
        level: 1,
        ship: null,
        enemies: [],
        projectiles: [],
        powerUps: [],
        clouds: [],
        background: null,
        gameWon: false,
        gameLost: false,
        comboMultiplier: 1,
        lastKillTime: 0,
        // screenShake: 0, // Removed screen shake functionality
        particleEffects: [],
        // Enhanced weapon systems
        primaryWeapon: 'laser',
        weaponHeat: 0,
        maxHeat: 100,
        heatDissipation: 0.5,
        // New weapon types
        availableWeapons: ['laser', 'plasma', 'gatling', 'rocket'],
        currentWeaponIndex: 0,
        // Special ability cooldowns
        powerUpTimer: 0,
        // Debug mode - set to true to see collision circles
        debugMode: false,
        // Continuous spawning system
        spawnTimer: 0,
        spawnInterval: 60, // Spawn every 60 frames (1 second)
        maxEnemiesOnScreen: 8,
        enemiesKilledThisLevel: 0,
        enemiesRequiredForNextLevel: 20,
        // Wave management
        currentWave: 0,
        waveEnemies: [],
        waveSpawnIndex: 0,
        // Power-up system
        activeFirePowerups: [], // [{type: 'spread', timer: 1200}, ...] (max 2)
        // Elemental powerup system
        elementalPowerups: [], // [{type: 'lightning', weapon: 'laser', timer: 3600}, ...] (max 3)
        elementalEffects: {
            lightning: {
                chainChance: 0.6,
                chainJumps: 2,
                chainRange: 500,
                stunDuration: 120,
                damageMultiplier: 1.2,
            },
            fire: { burnDamage: 10, burnDuration: 180, damageMultiplier: 1.1 },
            ice: {
                slowEffect: 0.3,
                slowDuration: 240,
                freezeChance: 0.3,
                freezeDuration: 180,
                damageMultiplier: 0.9,
            },
        },
        // Enhanced visual effects
        particleSystems: [],
        screenEffects: [],
        // Weather system
        weatherType: 'clear', // clear, rain, storm, wind
        weatherTimer: 0,
        // Add to gameData
        megaMissileCooldown: 0, // frames
        megaMissileCooldownMax: 720, // 12 seconds at 60fps
        // Performance monitoring
        frameCount: 0,
        lastFrameTime: 0,
        fps: 60,
        performanceStats: {
            collisionChecks: 0,
            objectsCreated: 0,
            objectsDestroyed: 0,
            spatialQueries: 0,
        },
        // Optimization flags
        needsSpatialUpdate: true,
        lastSpatialUpdate: 0,
        spatialUpdateInterval: 10, // Update spatial grid every 10 frames
        // Pre-allocated arrays for better performance
        tempArray: [],
        collisionPairs: [],
        // Progression system
        playerTitle: 'Rookie',
        damageBonus: 0,
        achievements: [],
        milestoneRewards: [],
    };

    // Pre-allocate common objects to reduce garbage collection
    const tempVec2 = { x: 0, y: 0 };
    const tempRect = { x: 0, y: 0, width: 0, height: 0 };

    // Move hasPowerup helper to top-level scope
    function hasPowerup(type) {
        if (!gameData.activeFirePowerups) return 0;
        const powerup = gameData.activeFirePowerups.find(p => p.type === type);
        return powerup ? powerup.level : 0;
    }

    // Elemental powerup helper functions
    function hasElementalPowerup(elementType) {
        if (!gameData.elementalPowerups) return false;
        return gameData.elementalPowerups.some(p => p.type === elementType);
    }

    function getElementalPowerup(elementType) {
        if (!gameData.elementalPowerups) return null;
        return gameData.elementalPowerups.find(p => p.type === elementType);
    }

    // Optimized Particle System with object pooling
    class ParticleSystem {
        static pool = [];
        static getOrCreate(x, y, type = 'explosion') {
            let system = ParticleSystem.pool.find(p => !p.active);
            if (system) {
                system.reset(x, y, type);
                return system;
            }
            system = new ParticleSystem(x, y, type);
            ParticleSystem.pool.push(system);
            return system;
        }

        constructor(x, y, type = 'explosion') {
            this.x = x;
            this.y = y;
            this.type = type;
            this.particles = [];
            this.life = 60;
            this.maxLife = 60;
            this.active = true;
            this.createParticles();
        }

        reset(x, y, type = 'explosion') {
            this.x = x;
            this.y = y;
            this.type = type;
            this.life = 60;
            this.maxLife = 60;
            this.active = true;
            this.particles.length = 0; // Clear array without creating new one
            this.createParticles();
        }

        createParticles() {
            const particleCount =
                this.type === 'explosion'
                    ? 20
                    : this.type === 'trail'
                      ? 8
                      : this.type === 'spark'
                        ? 15
                        : 10;

            for (let i = 0; i < particleCount; i++) {
                const particle = {
                    x: this.x + (Math.random() - 0.5) * 20,
                    y: this.y + (Math.random() - 0.5) * 20,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 30 + Math.random() * 30,
                    maxLife: 30 + Math.random() * 30,
                    size: 2 + Math.random() * 6,
                    color: this.getParticleColor(),
                    rotation: Math.random() * Math.PI * 2,
                    rotationSpeed: (Math.random() - 0.5) * 0.2,
                };
                this.particles.push(particle);
            }
        }

        getParticleColor() {
            const colorMap = {
                explosion: ['#FF4500', '#FFD700', '#FF6347', '#FF8C00'],
                trail: ['#00FFFF', '#87CEEB', '#B0E0E6'],
                spark: ['#FFFFFF', '#FFFF00', '#FFD700'],
                lightning: ['#FFFF00', '#FFD700', '#FFFFFF', '#87CEEB'],
                fire: ['#FF4500', '#FF6347', '#FF8C00', '#FFD700'],
                ice: ['#87CEEB', '#B0E0E6', '#FFFFFF', '#E0F6FF'],
            };
            const colors = colorMap[this.type] || ['#FFFFFF'];
            return colors[Math.floor(Math.random() * colors.length)];
        }

        update() {
            this.life--;
            for (let i = this.particles.length - 1; i >= 0; i--) {
                const particle = this.particles[i];
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.vx *= 0.98;
                particle.vy *= 0.98;
                particle.life--;
                particle.rotation += particle.rotationSpeed;

                if (particle.life <= 0) {
                    this.particles.splice(i, 1);
                }
            }

            if (this.particles.length === 0) {
                this.active = false;
            }
        }

        render(context) {
            for (const particle of this.particles) {
                const alpha = particle.life / particle.maxLife;
                context.save();
                context.globalAlpha = alpha;
                context.translate(particle.x, particle.y);
                context.rotate(particle.rotation);
                context.fillStyle = particle.color;
                context.beginPath();
                context.arc(0, 0, particle.size, 0, Math.PI * 2);
                context.fill();
                context.restore();
            }
        }
    }

    // Enemy Airship class
    class EnemyAirship extends Sprite {
        constructor(x, y, type = 'scout') {
            super();
            this.bitmap = new Bitmap(60, 40);
            this.anchor.set(0.5, 0.5);
            this.x = x;
            this.y = y;
            this.type = type;
            this.health = this.getHealthForType();
            this.maxHealth = this.health;
            this.speed = this.getSpeedForType();
            this.fireRate = this.getFireRateForType();
            this.fireTimer = 0;
            this.pattern = this.getPatternForType();
            this.patternTimer = 0;
            this.createEnemyGraphics();
        }

        getHealthForType() {
            switch (this.type) {
                case 'drone':
                    return 60; // Fodder
                case 'scout':
                    return 90; // Agile, fragile
                case 'fighter':
                    return 180; // Duelist
                case 'bomber':
                    return 300; // Area denial
                case 'carrier':
                    return 600; // Spawner/support
                case 'interceptor':
                    return 120; // Fast, agile
                case 'sniper':
                    return 150; // Long-range specialist
                case 'swarm':
                    return 40; // Small, numerous
                case 'tank':
                    return 450; // Heavy armor
                case 'stealth':
                    return 100; // Invisible threat
                default:
                    return 120;
            }
        }

        getSpeedForType() {
            switch (this.type) {
                case 'drone':
                    return 2; // Slower for better shooting
                case 'scout':
                    return 1.5;
                case 'fighter':
                    return 1;
                case 'bomber':
                    return 0.5;
                case 'carrier':
                    return 0.3;
                case 'interceptor':
                    return 2.5; // Very fast
                case 'sniper':
                    return 0.8; // Slow, precise
                case 'swarm':
                    return 3; // Very fast, small
                case 'tank':
                    return 0.3; // Very slow
                case 'stealth':
                    return 1.2; // Moderate speed
                default:
                    return 1;
            }
        }

        getFireRateForType() {
            switch (this.type) {
                case 'drone':
                    return 450; // Much slower fire rate for fodder
                case 'scout':
                    return 300;
                case 'fighter':
                    return 240;
                case 'bomber':
                    return 450;
                case 'carrier':
                    return 600;
                case 'interceptor':
                    return 200; // Fast firing
                case 'sniper':
                    return 600; // Slow, powerful shots
                case 'swarm':
                    return 300; // Moderate fire rate
                case 'tank':
                    return 400; // Slow, heavy shots
                case 'stealth':
                    return 350; // Moderate when visible
                default:
                    return 300;
            }
        }

        getPatternForType() {
            switch (this.type) {
                case 'drone':
                    return 'straight'; // Simple straight movement
                case 'scout':
                    return 'straight';
                case 'fighter':
                    return 'zigzag';
                case 'bomber':
                    return 'dive';
                case 'carrier':
                    return 'patrol';
                case 'interceptor':
                    return 'evasive'; // Dodges shots
                case 'sniper':
                    return 'straight'; // Steady approach
                case 'swarm':
                    return 'chaotic'; // Random movement
                case 'tank':
                    return 'straight'; // Slow, steady
                case 'stealth':
                    return 'ambush'; // Appears suddenly
                default:
                    return 'straight';
            }
        }

        createEnemyGraphics() {
            const context = this.bitmap.context;
            const centerX = 30;
            const centerY = 20;

            let mainColor, accentColor, detailColor;

            switch (this.type) {
                case 'drone':
                    mainColor = '#2F4F4F'; // Dark slate gray
                    accentColor = '#00FF00'; // Bright green
                    detailColor = '#32CD32'; // Lime green
                    break;
                case 'scout':
                    mainColor = '#4B0082'; // Indigo
                    accentColor = '#FF4500'; // Orange red
                    detailColor = '#FF6347'; // Tomato
                    break;
                case 'fighter':
                    mainColor = '#000080'; // Navy
                    accentColor = '#00BFFF'; // Deep sky blue
                    detailColor = '#87CEEB'; // Sky blue
                    break;
                case 'bomber':
                    mainColor = '#2F2F2F'; // Dark gray
                    accentColor = '#FF0000'; // Red
                    detailColor = '#DC143C'; // Crimson
                    break;
                case 'carrier':
                    mainColor = '#000000'; // Black
                    accentColor = '#FFD700'; // Gold
                    detailColor = '#FFA500'; // Orange
                    break;
                case 'interceptor':
                    mainColor = '#FF1493'; // Deep pink
                    accentColor = '#00FFFF'; // Cyan
                    detailColor = '#FF69B4'; // Hot pink
                    break;
                case 'sniper':
                    mainColor = '#8B4513'; // Saddle brown
                    accentColor = '#FFD700'; // Gold
                    detailColor = '#DEB887'; // Burlywood
                    break;
                case 'swarm':
                    mainColor = '#32CD32'; // Lime green
                    accentColor = '#FF4500'; // Orange red
                    detailColor = '#00FF00'; // Green
                    break;
                case 'tank':
                    mainColor = '#696969'; // Dim gray
                    accentColor = '#FF0000'; // Red
                    detailColor = '#DC143C'; // Crimson
                    break;
                case 'stealth':
                    mainColor = '#2F2F2F'; // Dark gray
                    accentColor = '#00FF00'; // Green
                    detailColor = '#32CD32'; // Lime green
                    break;
                default:
                    mainColor = '#4B0082';
                    accentColor = '#FF4500';
                    detailColor = '#FF6347';
            }

            // Clear the bitmap
            context.clearRect(0, 0, 60, 40);

            // Main drone body - hexagonal shape
            context.fillStyle = mainColor;
            context.beginPath();
            context.moveTo(centerX - 15, centerY - 8);
            context.lineTo(centerX - 8, centerY - 12);
            context.lineTo(centerX + 8, centerY - 12);
            context.lineTo(centerX + 15, centerY - 8);
            context.lineTo(centerX + 15, centerY + 8);
            context.lineTo(centerX + 8, centerY + 12);
            context.lineTo(centerX - 8, centerY + 12);
            context.lineTo(centerX - 15, centerY + 8);
            context.closePath();
            context.fill();

            // Drone outline
            context.strokeStyle = accentColor;
            context.lineWidth = 2;
            context.stroke();

            // Panel lines (subtle X)
            context.save();
            context.strokeStyle = 'rgba(255,255,255,0.15)';
            context.lineWidth = 1;
            context.beginPath();
            context.moveTo(centerX - 10, centerY - 6);
            context.lineTo(centerX + 10, centerY + 6);
            context.moveTo(centerX + 10, centerY - 6);
            context.lineTo(centerX - 10, centerY + 6);
            context.stroke();
            context.restore();

            // Central core/eye
            context.fillStyle = accentColor;
            context.beginPath();
            context.arc(centerX, centerY, 6, 0, Math.PI * 2);
            context.fill();
            // Inner core detail
            context.fillStyle = detailColor;
            context.beginPath();
            context.arc(centerX, centerY, 3, 0, Math.PI * 2);
            context.fill();
            // Glowing eye highlight
            context.save();
            context.globalAlpha = 0.5;
            context.fillStyle = '#fff';
            context.beginPath();
            context.arc(centerX + 2, centerY - 2, 1.2, 0, Math.PI * 2);
            context.fill();
            context.restore();

            // Weapon pods - geometric shapes
            context.fillStyle = detailColor;
            // Left weapon pod
            context.beginPath();
            context.moveTo(centerX - 20, centerY - 3);
            context.lineTo(centerX - 25, centerY - 3);
            context.lineTo(centerX - 25, centerY + 3);
            context.lineTo(centerX - 20, centerY + 3);
            context.closePath();
            context.fill();
            // Right weapon pod
            context.beginPath();
            context.moveTo(centerX + 20, centerY - 3);
            context.lineTo(centerX + 25, centerY - 3);
            context.lineTo(centerX + 25, centerY + 3);
            context.lineTo(centerX + 20, centerY + 3);
            context.closePath();
            context.fill();
            // Energy conduits - thin lines
            context.strokeStyle = accentColor;
            context.lineWidth = 1;
            // Left conduit
            context.beginPath();
            context.moveTo(centerX - 15, centerY);
            context.lineTo(centerX - 20, centerY);
            context.stroke();
            // Right conduit
            context.beginPath();
            context.moveTo(centerX + 15, centerY);
            context.lineTo(centerX + 20, centerY);
            context.stroke();
            // Thrusters (bottom)
            context.save();
            context.globalAlpha = 0.7;
            context.fillStyle = '#87CEEB';
            context.beginPath();
            context.arc(centerX - 8, centerY + 13, 2, 0, Math.PI * 2);
            context.arc(centerX + 8, centerY + 13, 2, 0, Math.PI * 2);
            context.fill();
            context.restore();
            // Type-specific details
            switch (this.type) {
                case 'drone':
                    // Side sensors
                    context.fillStyle = '#00FFCC';
                    context.beginPath();
                    context.arc(centerX - 6, centerY - 4, 1.2, 0, Math.PI * 2);
                    context.arc(centerX + 6, centerY - 4, 1.2, 0, Math.PI * 2);
                    context.fill();
                    // Subtle X pattern already added
                    break;
                case 'scout':
                    // Antenna
                    context.save();
                    context.strokeStyle = '#fff';
                    context.lineWidth = 1.2;
                    context.beginPath();
                    context.moveTo(centerX, centerY - 6);
                    context.lineTo(centerX, centerY - 14);
                    context.stroke();
                    context.restore();
                    // Wing lights
                    context.fillStyle = '#FFD700';
                    context.beginPath();
                    context.arc(centerX - 15, centerY + 8, 1.2, 0, Math.PI * 2);
                    context.arc(centerX + 15, centerY + 8, 1.2, 0, Math.PI * 2);
                    context.fill();
                    // Top and bottom sensors
                    context.fillStyle = '#00FFCC';
                    context.fillRect(centerX - 2, centerY - 15, 4, 2);
                    context.fillRect(centerX - 2, centerY + 12, 4, 2);
                    break;
                case 'fighter':
                    // Forward cannons
                    context.save();
                    context.strokeStyle = '#FF3333';
                    context.lineWidth = 2;
                    context.beginPath();
                    context.moveTo(centerX - 10, centerY - 2);
                    context.lineTo(centerX - 18, centerY - 6);
                    context.moveTo(centerX + 10, centerY - 2);
                    context.lineTo(centerX + 18, centerY - 6);
                    context.stroke();
                    context.restore();
                    // Red visor
                    context.fillStyle = '#FF3333';
                    context.fillRect(centerX - 5, centerY - 2, 10, 3);
                    // Wing extensions
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 25, centerY - 8, 8, 16);
                    context.fillRect(centerX + 17, centerY - 8, 8, 16);
                    break;
                case 'bomber':
                    // Bomb bay doors
                    context.fillStyle = '#333';
                    context.fillRect(centerX - 6, centerY + 10, 12, 3);
                    // Armor rivets
                    context.fillStyle = '#AAA';
                    for (let i = -1; i <= 1; i++) {
                        context.beginPath();
                        context.arc(centerX + i * 6, centerY - 10, 1, 0, Math.PI * 2);
                        context.arc(centerX + i * 6, centerY + 10, 1, 0, Math.PI * 2);
                        context.fill();
                    }
                    // Extra armor plates
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 12, centerY - 15, 24, 4);
                    context.fillRect(centerX - 12, centerY + 11, 24, 4);
                    break;
                case 'carrier':
                    // Command bridge tower
                    context.fillStyle = '#FFD700';
                    context.fillRect(centerX - 2, centerY - 18, 4, 8);
                    // Hangar bay doors
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 8, centerY + 14, 16, 4);
                    // Portholes/windows
                    context.fillStyle = '#87CEEB';
                    for (let i = -1; i <= 1; i++) {
                        context.beginPath();
                        context.arc(centerX + i * 6, centerY + 6, 1.2, 0, Math.PI * 2);
                        context.fill();
                    }
                    break;
                case 'interceptor':
                    // Sleek aerodynamic design
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 20, centerY - 2, 8, 4);
                    context.fillRect(centerX + 12, centerY - 2, 8, 4);
                    // Evasion sensors
                    context.fillStyle = '#00FFFF';
                    context.beginPath();
                    context.arc(centerX - 8, centerY - 6, 1.5, 0, Math.PI * 2);
                    context.arc(centerX + 8, centerY - 6, 1.5, 0, Math.PI * 2);
                    context.fill();
                    break;
                case 'sniper':
                    // Long barrel cannon
                    context.strokeStyle = detailColor;
                    context.lineWidth = 3;
                    context.beginPath();
                    context.moveTo(centerX - 15, centerY);
                    context.lineTo(centerX - 30, centerY);
                    context.stroke();
                    // Scope
                    context.fillStyle = '#FFD700';
                    context.beginPath();
                    context.arc(centerX - 5, centerY - 3, 3, 0, Math.PI * 2);
                    context.fill();
                    break;
                case 'swarm':
                    // Small, compact design
                    context.scale(0.7, 0.7);
                    context.translate(centerX * 0.4, centerY * 0.4);
                    // Swarm markings
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 8, centerY - 8, 16, 16);
                    context.restore();
                    break;
                case 'tank':
                    // Heavy armor plating
                    context.fillStyle = detailColor;
                    context.fillRect(centerX - 18, centerY - 12, 36, 24);
                    // Armor rivets
                    context.fillStyle = '#AAA';
                    for (let i = -2; i <= 2; i++) {
                        for (let j = -1; j <= 1; j++) {
                            context.beginPath();
                            context.arc(centerX + i * 6, centerY + j * 8, 1, 0, Math.PI * 2);
                            context.fill();
                        }
                    }
                    break;
                case 'stealth':
                    // Cloaking field effect
                    context.save();
                    context.globalAlpha = 0.6;
                    context.strokeStyle = '#00FF00';
                    context.lineWidth = 2;
                    context.setLineDash([5, 5]);
                    context.strokeRect(centerX - 18, centerY - 12, 36, 24);
                    context.restore();
                    // Stealth sensors
                    context.fillStyle = '#00FF00';
                    context.beginPath();
                    context.arc(centerX - 10, centerY - 8, 1.5, 0, Math.PI * 2);
                    context.arc(centerX + 10, centerY - 8, 1.5, 0, Math.PI * 2);
                    context.fill();
                    break;
            }
        }

        update() {
            if (this.destroyed) return;
            this.patternTimer++;
            let speedMultiplier = 1;
            if (gameData.timeWarpActive) speedMultiplier = 0.5;

            // Apply slow effect BEFORE movement calculations
            if (this._slowTicks > 0) {
                this._slowTicks--;
                speedMultiplier *= this._slowEffect || 0.5;
            }
            // Unique movement/AI per type
            switch (this.type) {
                case 'drone':
                    // Straight, sometimes jitters or speeds up
                    if (!this._droneJitterTimer) this._droneJitterTimer = 0;
                    this._droneJitterTimer--;
                    if (this._droneJitterTimer <= 0 && Math.random() < 0.01) {
                        this._droneJitterTimer = 30 + Math.random() * 30;
                        this._droneJitterY = (Math.random() - 0.5) * 30;
                        this._droneSpeedBoost = Math.random() < 0.5 ? 2 : 1;
                    }
                    this.x -= this.speed * (this._droneSpeedBoost || 1) * speedMultiplier;
                    this.y += (this._droneJitterY || 0) * 0.05 * speedMultiplier;
                    if (this._droneJitterTimer <= 0) {
                        this._droneJitterY = 0;
                        this._droneSpeedBoost = 1;
                    }
                    break;
                case 'scout':
                    // Smooth zigzag, dodges if shot is near
                    if (!this._scoutDodgeTimer) this._scoutDodgeTimer = 0;
                    this.x -= this.speed * 0.75 * speedMultiplier;
                    this.y += Math.sin(this.patternTimer * 0.03) * 6 * speedMultiplier;
                    // Dodge burst if player shot is close
                    if (this._scoutDodgeTimer <= 0 && this._isPlayerShotNear()) {
                        this._scoutDodgeTimer = 18;
                        this._scoutDodgeDir = Math.random() < 0.5 ? -1 : 1;
                    }
                    if (this._scoutDodgeTimer > 0) {
                        this.y += this._scoutDodgeDir * 3 * speedMultiplier;
                        this._scoutDodgeTimer--;
                    }
                    break;
                case 'fighter':
                    // Smooth wide sine wave, pauses then dashes
                    if (!this._fighterDashTimer) this._fighterDashTimer = 0;
                    if (!this._fighterPauseTimer) this._fighterPauseTimer = 0;
                    if (this._fighterPauseTimer > 0) {
                        this._fighterPauseTimer--;
                        // Telegraphed pause (could flash sprite here)
                    } else if (this._fighterDashTimer > 0) {
                        this.x -= this.speed * 2 * speedMultiplier;
                        this._fighterDashTimer--;
                    } else {
                        this.x -= this.speed * 0.5 * speedMultiplier;
                        this.y += Math.sin(this.patternTimer * 0.015) * 10 * speedMultiplier;
                        if (Math.random() < 0.005) {
                            this._fighterPauseTimer = 18;
                            this._fighterDashTimer = 18;
                        }
                    }
                    // Track player Y
                    if (gameData.ship) {
                        const dy = gameData.ship.y - this.y;
                        this.y += Math.sign(dy) * Math.min(Math.abs(dy), 0.4 * speedMultiplier);
                    }
                    break;
                case 'bomber':
                    // Slow smooth arc, then dive bomb
                    if (!this._bomberDiveTimer) this._bomberDiveTimer = 0;
                    if (this._bomberDiveTimer > 0) {
                        this.x -= this.speed * 0.25 * speedMultiplier;
                        this.y += 2 * speedMultiplier;
                        this._bomberDiveTimer--;
                    } else {
                        this.x -= this.speed * 0.35 * speedMultiplier;
                        this.y += Math.sin(this.patternTimer * 0.008) * 6 * speedMultiplier;
                        // Dive if above player and random chance
                        if (
                            gameData.ship &&
                            Math.abs(this.x - gameData.ship.x) < 80 &&
                            Math.random() < 0.01
                        ) {
                            this._bomberDiveTimer = 30;
                        }
                    }
                    break;
                case 'carrier':
                    // Smooth patrols, stops to launch minions
                    if (!this._carrierPatrolDir) this._carrierPatrolDir = 1;
                    if (!this._carrierStopTimer) this._carrierStopTimer = 0;
                    if (this._carrierStopTimer > 0) {
                        this._carrierStopTimer--;
                        // Could flash or animate here
                    } else {
                        this.x -= this.speed * 0.25 * this._carrierPatrolDir * speedMultiplier;
                        this.y += Math.sin(this.patternTimer * 0.01) * 4 * speedMultiplier;
                        if (this.x < Graphics.width - 300) this._carrierPatrolDir = -1;
                        if (this.x > Graphics.width - 80) this._carrierPatrolDir = 1;
                        if (Math.random() < 0.01) this._carrierStopTimer = 36;
                    }
                    break;
                case 'interceptor':
                    // Fast evasive movement, dodges shots
                    if (!this._interceptorDodgeTimer) this._interceptorDodgeTimer = 0;
                    this.x -= this.speed * speedMultiplier;
                    this.y += Math.sin(this.patternTimer * 0.05) * 8 * speedMultiplier;
                    // Evasive dodge when shot is near
                    if (this._interceptorDodgeTimer <= 0 && this._isPlayerShotNear()) {
                        this._interceptorDodgeTimer = 15;
                        this._interceptorDodgeDir = Math.random() < 0.5 ? -1 : 1;
                    }
                    if (this._interceptorDodgeTimer > 0) {
                        this.y += this._interceptorDodgeDir * 4 * speedMultiplier;
                        this._interceptorDodgeTimer--;
                    }
                    break;
                case 'sniper':
                    // Slow, steady approach, precise positioning
                    this.x -= this.speed * 0.6 * speedMultiplier;
                    // Track player Y position for better aim
                    if (gameData.ship) {
                        const dy = gameData.ship.y - this.y;
                        this.y += Math.sign(dy) * Math.min(Math.abs(dy), 0.3 * speedMultiplier);
                    }
                    break;
                case 'swarm':
                    // Chaotic, fast movement
                    this.x -= this.speed * speedMultiplier;
                    this.y += (Math.random() - 0.5) * 4 * speedMultiplier;
                    // Random direction changes
                    if (Math.random() < 0.02) {
                        this._swarmDir = (Math.random() - 0.5) * 2;
                    }
                    if (this._swarmDir) {
                        this.y += this._swarmDir * speedMultiplier;
                    }
                    break;
                case 'tank':
                    // Slow, steady, heavily armored
                    this.x -= this.speed * 0.4 * speedMultiplier;
                    this.y += Math.sin(this.patternTimer * 0.005) * 3 * speedMultiplier;
                    break;
                case 'stealth':
                    // Stealth movement with cloaking
                    if (!this._stealthVisible) this._stealthVisible = false;
                    if (!this._stealthTimer) this._stealthTimer = 0;

                    if (this._stealthTimer <= 0) {
                        this._stealthVisible = !this._stealthVisible;
                        this._stealthTimer = this._stealthVisible ? 120 : 180; // Visible for 2s, invisible for 3s
                    }
                    this._stealthTimer--;

                    if (this._stealthVisible) {
                        this.x -= this.speed * speedMultiplier;
                        this.y += Math.sin(this.patternTimer * 0.02) * 5 * speedMultiplier;
                        this.alpha = 1;
                    } else {
                        this.x -= this.speed * 0.5 * speedMultiplier;
                        this.alpha = 0.3;
                    }
                    break;
                default:
                    // Fallback to old pattern
                    this.x -= this.speed * speedMultiplier;
            }

            // Process status effects
            if (this._burnTicks > 0) {
                this._burnTicks--;
                if (this._burnTicks % 30 === 0) {
                    // Every 0.5 seconds
                    this.takeDamage(this._burnDamage || 5);
                    // Create fire particles for burn damage
                    if (this.scene && this.scene.createFireEffect) {
                        this.scene.createFireEffect(this.x, this.y, 30);
                    }
                    // Burn spread: 30% chance to spread to nearby enemies within 80px
                    if (this.scene && gameData && gameData.enemies) {
                        gameData.enemies.forEach(enemy => {
                            if (enemy !== this && !enemy._burnTicks && !enemy.destroyed) {
                                const dx = enemy.x - this.x;
                                const dy = enemy.y - this.y;
                                if (dx * dx + dy * dy < 6400) {
                                    // 80px radius
                                    if (Math.random() < 0.3) {
                                        enemy._burnTicks =
                                            gameData.elementalEffects.fire.burnDuration;
                                        enemy._burnDamage =
                                            gameData.elementalEffects.fire.burnDamage;
                                        if (this.scene.createFireEffect) {
                                            this.scene.createFireEffect(enemy.x, enemy.y, 30);
                                        }
                                    }
                                }
                            }
                        });
                    }
                }
            }

            if (this._stunTicks > 0) {
                this._stunTicks--;
                // Stunned enemies can't move or fire
                if (this._stunTicks > 0) {
                    return; // Skip movement and firing
                }
            }

            if (this._freezeTicks > 0) {
                this._freezeTicks--;
                // Frozen enemies can't move or fire
                if (this._freezeTicks > 0) {
                    return; // Skip movement and firing
                }
            }

            // Remove if off screen
            if (this.x < -50) {
                this.destroyed = true;
                if (this.scene && this.scene.children && this.scene.children.includes(this)) {
                    this.scene.removeChild(this);
                }
                const index = gameData.enemies.indexOf(this);
                if (index > -1) {
                    gameData.enemies.splice(index, 1);
                }
                this.visible = false;
                this.active = false;
                return;
            }
            // Firing
            this.fireTimer++;
            if (this.fireTimer >= this.fireRate) {
                this.fire();
                this.fireTimer = 0;
            }
        }
        // Helper: is player shot near (for scout dodge)
        _isPlayerShotNear() {
            return gameData.projectiles.some(
                p =>
                    p.type === 'player' &&
                    Math.abs(p.x - this.x) < 40 &&
                    Math.abs(p.y - this.y) < 40
            );
        }
        fire() {
            if (!gameData.ship || this.destroyed) return;
            switch (this.type) {
                case 'drone':
                    // Single slow bullet - reduced firing probability
                    if (Math.random() < 0.7) return; // 30% chance to fire
                    this._fireAtPlayer(1, 1.2, 0);
                    break;
                case 'scout':
                    // Burst (1-2 shots), sometimes leads player - reduced burst size
                    if (Math.random() < 0.6) return; // 40% chance to fire
                    const burst = 1 + Math.floor(Math.random() * 2); // 1-2 shots instead of 2-3
                    for (let i = 0; i < burst; i++) {
                        const spread = (i - (burst - 1) / 2) * 0.12;
                        const lead = Math.random() < 0.5 ? 0 : 18;
                        this._fireAtPlayer(1, 1.6, spread, lead);
                    }
                    break;
                case 'fighter':
                    // Reduced triple shot, less frequent fan
                    if (Math.random() < 0.6) return; // 40% chance to fire
                    for (let i = -1; i <= 1; i++) {
                        this._fireAtPlayer(1, 2.2, i * 0.18);
                    }
                    if (Math.random() < 0.2) {
                        // Reduced from 30% to 20%
                        for (let i = -1; i <= 1; i++) {
                            // Reduced from -2 to 2 to -1 to 1
                            this._fireAtPlayer(1, 1.5, i * 0.35);
                        }
                    }
                    break;
                case 'bomber':
                    // Drop bomb if above player - reduced frequency
                    if (Math.random() < 0.7) return; // 30% chance to fire
                    if (gameData.ship && Math.abs(this.x - gameData.ship.x) < 60) {
                        this._fireBomb();
                    }
                    break;
                case 'carrier':
                    // Launch minion or reduced barrage
                    if (Math.random() < 0.6) return; // 40% chance to fire
                    if (Math.random() < 0.6) {
                        this._launchMinion();
                    } else {
                        for (let i = -1; i <= 1; i++) {
                            // Reduced from -2 to 2 to -1 to 1
                            this._fireAtPlayer(1, 1.1, i * 0.25);
                        }
                    }
                    break;
                case 'interceptor':
                    // Fast, accurate shots
                    if (Math.random() < 0.5) return; // 50% chance to fire
                    this._fireAtPlayer(1, 2.5, 0);
                    break;
                case 'sniper':
                    // High-damage, long-range shots
                    if (Math.random() < 0.7) return; // 30% chance to fire
                    const sniperProj = new Projectile(this.x, this.y, -3, 0, 'enemy');
                    sniperProj.scene = this.scene;
                    sniperProj.damage = 50; // High damage
                    gameData.projectiles.push(sniperProj);
                    if (this.scene) this.scene.addChild(sniperProj);
                    break;
                case 'swarm':
                    // Rapid, weak shots
                    if (Math.random() < 0.4) return; // 60% chance to fire
                    this._fireAtPlayer(1, 1.8, (Math.random() - 0.5) * 0.3);
                    break;
                case 'tank':
                    // Heavy, slow shots
                    if (Math.random() < 0.8) return; // 20% chance to fire
                    const tankProj = new Projectile(this.x, this.y, -1.5, 0, 'enemy');
                    tankProj.scene = this.scene;
                    tankProj.damage = 35; // Heavy damage
                    gameData.projectiles.push(tankProj);
                    if (this.scene) this.scene.addChild(tankProj);
                    break;
                case 'stealth':
                    // Only fire when visible
                    if (!this._stealthVisible || Math.random() < 0.6) return;
                    this._fireAtPlayer(1, 1.4, 0);
                    break;
                default:
                    // Fallback: single shot
                    if (Math.random() < 0.7) return; // 30% chance to fire
                    this._fireAtPlayer(1, 1.2, 0);
            }
        }
        // Helper: fire at player with spread/lead
        _fireAtPlayer(count, speed, spread = 0, lead = 0) {
            if (!gameData.ship) return;
            const dx = gameData.ship.x + lead - this.x;
            const dy = gameData.ship.y - this.y;
            const dist = Math.sqrt(dx * dx + dy * dy) || 1;
            const vx = (dx / dist) * speed;
            const vy = (dy / dist) * speed;
            const angle = Math.atan2(dy, dx) + spread;
            const proj = new Projectile(
                this.x,
                this.y,
                Math.cos(angle) * speed,
                Math.sin(angle) * speed,
                'enemy'
            );
            proj.scene = this.scene;
            gameData.projectiles.push(proj);
            if (this.scene) this.scene.addChild(proj);
        }
        // Helper: drop bomb
        _fireBomb() {
            const vy = 1.2 + Math.random() * 0.6;
            const bomb = new Projectile(this.x, this.y, -1, vy, 'enemy');
            bomb.scene = this.scene;
            bomb.damage = 40;
            bomb._isBomb = true;
            gameData.projectiles.push(bomb);
            if (this.scene) this.scene.addChild(bomb);
        }
        // Helper: launch minion
        _launchMinion() {
            const minionType = Math.random() < 0.5 ? 'drone' : 'scout';
            const minion = new EnemyAirship(
                this.x - 30,
                this.y + (Math.random() - 0.5) * 40,
                minionType
            );
            minion.scene = this.scene;
            gameData.enemies.push(minion);
            if (this.scene) this.scene.addChild(minion);
        }

        takeDamage(amount, projectileType) {
            if (this.destroyed) return; // Don't take damage if already destroyed

            // Damage popup
            if (this.scene && this.scene.createDamagePopup) {
                this.scene.createDamagePopup(this.x, this.y, amount);
            }

            this.health -= amount;

            if (gameData.debugMode) {
                console.log(
                    `Enemy hit! Health: ${this.health}/${this.maxHealth}, Damage: ${amount}`
                );
            }

            // Only show impact particles if not destroyed by this hit
            if (this.health > 0 && this.scene && this.scene.createDamageImpactParticles) {
                this.scene.createDamageImpactParticles(this.x, this.y, amount, projectileType);
            }

            if (this.health <= 0) {
                if (gameData.debugMode) {
                    console.log('Enemy destroyed!');
                }
                this.destroy();
            }
        }

        destroy() {
            // Create explosion based on enemy type
            if (this.scene && this.scene.createExplosion) {
                let explosionSize = 2; // Default size
                switch (this.type) {
                    case 'drone':
                        explosionSize = 1;
                        break; // Small explosion
                    case 'scout':
                        explosionSize = 2;
                        break;
                    case 'fighter':
                        explosionSize = 3;
                        break;
                    case 'bomber':
                        explosionSize = 4;
                        break;
                    case 'carrier':
                        explosionSize = 5;
                        break;
                    case 'interceptor':
                        explosionSize = 2;
                        break;
                    case 'sniper':
                        explosionSize = 3;
                        break;
                    case 'swarm':
                        explosionSize = 1;
                        break;
                    case 'tank':
                        explosionSize = 4;
                        break;
                    case 'stealth':
                        explosionSize = 2;
                        break;
                }
                this.scene.createExplosion(this.x, this.y, explosionSize);
            }

            // Play explosion sound based on enemy type
            if (this.type === 'drone') {
                audioManager.play('smallExplosion');
            } else {
                audioManager.play('explosion');
            }

            // Add score
            const baseScore = this.getHealthForType() * 10;
            gameData.score = Math.floor(gameData.score + baseScore * gameData.comboMultiplier);

            // Track kills for level progression
            gameData.enemiesKilledThisLevel++;

            // Combo system
            const currentTime = Date.now();
            if (currentTime - gameData.lastKillTime < 2000) {
                gameData.comboMultiplier += 0.1;
            } else {
                gameData.comboMultiplier = 1;
            }
            gameData.lastKillTime = currentTime;

            // Mark as destroyed to prevent further updates
            this.destroyed = true;

            // Remove from scene
            if (this.scene && this.scene.children && this.scene.children.includes(this)) {
                this.scene.removeChild(this);
            }

            // Remove from game data
            const index = gameData.enemies.indexOf(this);
            if (index > -1) {
                gameData.enemies.splice(index, 1);
            }

            // Hide the sprite
            this.visible = false;
            this.active = false;
            if (this._wasFrozen) {
                // Shatter into shards - reduced count
                const shardCount = 3 + Math.floor(Math.random() * 3); // Reduced from 8-12 to 3-6
                for (let i = 0; i < shardCount; i++) {
                    const angle = (Math.PI * 2 * i) / shardCount + Math.random() * 0.2;
                    const speed = 8 + Math.random() * 4;
                    const vx = Math.cos(angle) * speed;
                    const vy = Math.sin(angle) * speed;
                    const shard = Projectile.getOrCreate(
                        this.x,
                        this.y,
                        vx,
                        vy,
                        'ice-shard',
                        angle
                    );
                    shard.scene = this.scene;
                    shard.damage = 30;
                    shard._shard = true;
                    gameData.projectiles.push(shard);
                    if (this.scene) this.scene.addChild(shard);
                }
                if (this.scene && this.scene.createIceEffect) {
                    this.scene.createIceEffect(this.x, this.y, 60);
                }
            }
        }
    }

    // Boss class (add this after EnemyAirship, before Scene_SkyPirates)
    class Boss extends EnemyAirship {
        constructor(x, y, bossType = 'destroyer') {
            super(x, y, bossType);
            this.bossType = bossType;
            // Much larger bitmap for bosses
            this.bitmap = new Bitmap(200, 120);
            this.anchor.set(0.5, 0.5);

            // Bosses have much more health and unique patterns
            switch (bossType) {
                case 'destroyer':
                    this.health = this.maxHealth = 15000;
                    this.speed = 1.2;
                    this.pattern = 'bossZigzag';
                    this.fireRate = 90; // Reduced from 45 to 90 (half the firing rate)
                    this.cannonCount = 3;
                    break;
                case 'carrier':
                    this.health = this.maxHealth = 25000;
                    this.speed = 0.8;
                    this.pattern = 'bossSummon';
                    this.fireRate = 120; // Reduced from 60 to 120 (half the firing rate)
                    this.cannonCount = 4;
                    break;
                case 'dreadnought':
                    this.health = this.maxHealth = 40000;
                    this.speed = 0.6;
                    this.pattern = 'bossBulletHell';
                    this.fireRate = 60; // Reduced from 30 to 60 (half the firing rate)
                    this.cannonCount = 6;
                    break;
                case 'titan':
                    this.health = this.maxHealth = 60000;
                    this.speed = 0.4;
                    this.pattern = 'bossUltimate';
                    this.fireRate = 45; // Faster than dreadnought for ultimate challenge
                    this.cannonCount = 8;
                    break;
                default:
                    this.health = this.maxHealth = 15000;
                    this.speed = 1.2;
                    this.pattern = 'bossZigzag';
                    this.fireRate = 90; // Reduced from 45 to 90 (half the firing rate)
                    this.cannonCount = 3;
            }
            this.fireTimer = this.fireRate; // Start ready to fire immediately
            this.bossPhase = 1;
            this.phaseTimer = 0;
            this.cannonAngles = [];
            this.createBossGraphics();
        }

        createBossGraphics() {
            const ctx = this.bitmap.context;
            ctx.clearRect(0, 0, 200, 120);

            // Boss-specific designs
            switch (this.bossType) {
                case 'destroyer':
                    this.createDestroyerGraphics(ctx);
                    break;
                case 'carrier':
                    this.createCarrierGraphics(ctx);
                    break;
                case 'dreadnought':
                    this.createDreadnoughtGraphics(ctx);
                    break;
                case 'titan':
                    this.createTitanGraphics(ctx);
                    break;
            }
        }

        createDestroyerGraphics(ctx) {
            // Main hull - dark red battleship with metallic sheen
            const hullGradient = ctx.createLinearGradient(20, 60, 180, 60);
            hullGradient.addColorStop(0, '#4A0000');
            hullGradient.addColorStop(0.3, '#8B0000');
            hullGradient.addColorStop(0.5, '#DC143C');
            hullGradient.addColorStop(0.7, '#B22222');
            hullGradient.addColorStop(1, '#4A0000');
            ctx.fillStyle = hullGradient;
            ctx.fillRect(20, 40, 160, 40);

            // Hull armor plates with rivets
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 3;
            for (let i = 0; i < 4; i++) {
                ctx.strokeRect(30 + i * 35, 35, 30, 50);
                // Rivets on each plate
                ctx.fillStyle = '#FFD700';
                for (let j = 0; j < 3; j++) {
                    ctx.beginPath();
                    ctx.arc(35 + i * 35 + j * 10, 40 + j * 15, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            // Command bridge with detailed structure
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(80, 30, 40, 20);
            // Bridge windows
            ctx.fillStyle = '#87CEEB';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(85 + i * 10, 35, 6, 8);
            }
            // Bridge antenna
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(100, 30);
            ctx.lineTo(100, 20);
            ctx.stroke();
            ctx.beginPath();
            ctx.arc(100, 20, 3, 0, Math.PI * 2);
            ctx.fill();

            // Multiple heavy cannons with detailed barrels
            const cannonPositions = [
                { x: 30, y: 50 },
                { x: 60, y: 45 },
                { x: 90, y: 50 },
                { x: 120, y: 45 },
                { x: 150, y: 50 },
                { x: 180, y: 45 },
            ];

            this.cannonAngles = [];
            cannonPositions.forEach((pos, i) => {
                // Cannon base with metallic texture
                const baseGradient = ctx.createLinearGradient(
                    pos.x - 5,
                    pos.y - 3,
                    pos.x + 5,
                    pos.y + 3
                );
                baseGradient.addColorStop(0, '#222');
                baseGradient.addColorStop(0.5, '#666');
                baseGradient.addColorStop(1, '#222');
                ctx.fillStyle = baseGradient;
                ctx.fillRect(pos.x - 5, pos.y - 3, 10, 6);

                // Cannon barrel with heat discoloration
                const barrelGradient = ctx.createLinearGradient(
                    pos.x - 8,
                    pos.y - 2,
                    pos.x + 8,
                    pos.y + 2
                );
                barrelGradient.addColorStop(0, '#444');
                barrelGradient.addColorStop(0.3, '#888');
                barrelGradient.addColorStop(0.7, '#AAA');
                barrelGradient.addColorStop(1, '#FF6600');
                ctx.fillStyle = barrelGradient;
                ctx.fillRect(pos.x - 8, pos.y - 2, 16, 4);

                // Cannon cooling fins
                ctx.fillStyle = '#666';
                for (let j = 0; j < 3; j++) {
                    ctx.fillRect(pos.x - 6 + j * 4, pos.y - 4, 2, 8);
                }

                this.cannonAngles.push({
                    x: pos.x - 8,
                    y: pos.y,
                    angle: Math.PI + (i - 2.5) * 0.2,
                });
            });

            // Energy conduits and power systems
            ctx.strokeStyle = '#00BFFF';
            ctx.lineWidth = 2;
            // Main power line
            ctx.beginPath();
            ctx.moveTo(25, 60);
            ctx.lineTo(175, 60);
            ctx.stroke();
            // Energy nodes
            ctx.fillStyle = '#00BFFF';
            for (let i = 0; i < 5; i++) {
                ctx.beginPath();
                ctx.arc(35 + i * 35, 60, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // Hull damage indicators (red warning lights)
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 6; i++) {
                ctx.beginPath();
                ctx.arc(25 + i * 25, 45, 2, 0, Math.PI * 2);
                ctx.fill();
            }

            // Name label with enhanced styling
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#FFD700';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 3;
            ctx.strokeText('DESTROYER', 100, 110);
            ctx.fillText('DESTROYER', 100, 110);
        }

        createCarrierGraphics(ctx) {
            // Main hull - massive carrier with detailed plating
            const hullGradient = ctx.createLinearGradient(10, 60, 190, 60);
            hullGradient.addColorStop(0, '#1a2a2a');
            hullGradient.addColorStop(0.3, '#2F4F4F');
            hullGradient.addColorStop(0.5, '#4682B4');
            hullGradient.addColorStop(0.7, '#5F9EA0');
            hullGradient.addColorStop(1, '#1a2a2a');
            ctx.fillStyle = hullGradient;
            ctx.fillRect(10, 30, 180, 60);

            // Hull armor plating with rivets
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;
            for (let i = 0; i < 6; i++) {
                ctx.strokeRect(15 + i * 28, 35, 25, 50);
                // Rivets on each plate
                ctx.fillStyle = '#FFD700';
                for (let j = 0; j < 4; j++) {
                    ctx.beginPath();
                    ctx.arc(20 + i * 28 + j * 5, 40 + j * 12, 1.5, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            // Flight deck with runway markings
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(20, 25, 160, 10);
            ctx.fillRect(20, 85, 160, 10);
            // Runway center line
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(25, 30);
            ctx.lineTo(175, 30);
            ctx.moveTo(25, 90);
            ctx.lineTo(175, 90);
            ctx.stroke();
            ctx.setLineDash([]);

            // Command tower with detailed structure
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(85, 20, 30, 40);
            // Tower windows
            ctx.fillStyle = '#87CEEB';
            for (let i = 0; i < 3; i++) {
                for (let j = 0; j < 2; j++) {
                    ctx.fillRect(90 + i * 8, 25 + j * 15, 5, 8);
                }
            }
            // Tower antenna array
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 2;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(95 + i * 10, 20);
                ctx.lineTo(95 + i * 10, 15);
                ctx.stroke();
                ctx.beginPath();
                ctx.arc(95 + i * 10, 15, 2, 0, Math.PI * 2);
                ctx.fill();
            }

            // Multiple advanced cannons with targeting systems
            const cannonPositions = [
                { x: 25, y: 50 },
                { x: 50, y: 45 },
                { x: 75, y: 50 },
                { x: 100, y: 45 },
                { x: 125, y: 50 },
                { x: 150, y: 45 },
                { x: 175, y: 50 },
            ];

            this.cannonAngles = [];
            cannonPositions.forEach((pos, i) => {
                // Cannon base with metallic texture
                const baseGradient = ctx.createLinearGradient(
                    pos.x - 4,
                    pos.y - 3,
                    pos.x + 4,
                    pos.y + 3
                );
                baseGradient.addColorStop(0, '#222');
                baseGradient.addColorStop(0.5, '#555');
                baseGradient.addColorStop(1, '#222');
                ctx.fillStyle = baseGradient;
                ctx.fillRect(pos.x - 4, pos.y - 3, 8, 6);

                // Cannon barrel with energy glow
                const barrelGradient = ctx.createLinearGradient(
                    pos.x - 6,
                    pos.y - 2,
                    pos.x + 6,
                    pos.y + 2
                );
                barrelGradient.addColorStop(0, '#333');
                barrelGradient.addColorStop(0.5, '#777');
                barrelGradient.addColorStop(1, '#00FFFF');
                ctx.fillStyle = barrelGradient;
                ctx.fillRect(pos.x - 6, pos.y - 2, 12, 4);

                // Targeting laser sights
                ctx.strokeStyle = '#00FFFF';
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(pos.x - 8, pos.y);
                ctx.lineTo(pos.x - 15, pos.y);
                ctx.stroke();

                this.cannonAngles.push({
                    x: pos.x - 6,
                    y: pos.y,
                    angle: Math.PI + (i - 3) * 0.15,
                });
            });

            // Hangar bays with launch systems
            ctx.fillStyle = '#000';
            for (let i = 0; i < 3; i++) {
                ctx.fillRect(40 + i * 40, 40, 20, 40);
                // Launch rails
                ctx.strokeStyle = '#FFD700';
                ctx.lineWidth = 2;
                ctx.beginPath();
                ctx.moveTo(45 + i * 40, 45);
                ctx.lineTo(55 + i * 40, 45);
                ctx.moveTo(45 + i * 40, 75);
                ctx.lineTo(55 + i * 40, 75);
                ctx.stroke();
            }

            // Energy core systems
            ctx.fillStyle = '#00FFFF';
            for (let i = 0; i < 4; i++) {
                ctx.beginPath();
                ctx.arc(35 + i * 40, 65, 4, 0, Math.PI * 2);
                ctx.fill();
            }

            // Name label with enhanced styling
            ctx.font = 'bold 16px Arial';
            ctx.fillStyle = '#FFD700';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 3;
            ctx.strokeText('CARRIER', 100, 110);
            ctx.fillText('CARRIER', 100, 110);
        }

        createDreadnoughtGraphics(ctx) {
            // Main hull - massive dreadnought with ominous design
            const hullGradient = ctx.createLinearGradient(5, 60, 195, 60);
            hullGradient.addColorStop(0, '#000000');
            hullGradient.addColorStop(0.2, '#1a1a1a');
            hullGradient.addColorStop(0.4, '#333333');
            hullGradient.addColorStop(0.6, '#555555');
            hullGradient.addColorStop(0.8, '#333333');
            hullGradient.addColorStop(1, '#000000');
            ctx.fillStyle = hullGradient;
            ctx.fillRect(5, 20, 190, 80);

            // Hull armor plating with menacing spikes
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 4;
            for (let i = 0; i < 6; i++) {
                ctx.strokeRect(10 + i * 30, 25, 25, 70);
                // Spikes on each plate
                ctx.fillStyle = '#FFD700';
                for (let j = 0; j < 3; j++) {
                    ctx.beginPath();
                    ctx.moveTo(15 + i * 30 + j * 8, 25);
                    ctx.lineTo(20 + i * 30 + j * 8, 20);
                    ctx.lineTo(25 + i * 30 + j * 8, 25);
                    ctx.closePath();
                    ctx.fill();
                }
            }

            // Command bridge with dark energy
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(90, 15, 20, 25);
            // Bridge windows with red glow
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 2; i++) {
                ctx.fillRect(95 + i * 8, 20, 5, 8);
            }
            // Bridge antenna with energy discharge
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(100, 15);
            ctx.lineTo(100, 8);
            ctx.stroke();
            // Energy discharge effect
            ctx.strokeStyle = '#FF6600';
            ctx.lineWidth = 2;
            for (let i = 0; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(100, 8);
                ctx.lineTo(95 + i * 3, 5);
                ctx.stroke();
            }

            // Multiple heavy cannons with plasma cores
            const cannonPositions = [
                { x: 20, y: 50 },
                { x: 45, y: 45 },
                { x: 70, y: 50 },
                { x: 95, y: 45 },
                { x: 120, y: 50 },
                { x: 145, y: 45 },
                { x: 170, y: 50 },
                { x: 195, y: 45 },
            ];

            this.cannonAngles = [];
            cannonPositions.forEach((pos, i) => {
                // Heavy cannon base with dark metal
                const baseGradient = ctx.createLinearGradient(
                    pos.x - 6,
                    pos.y - 4,
                    pos.x + 6,
                    pos.y + 4
                );
                baseGradient.addColorStop(0, '#000');
                baseGradient.addColorStop(0.3, '#333');
                baseGradient.addColorStop(0.7, '#666');
                baseGradient.addColorStop(1, '#000');
                ctx.fillStyle = baseGradient;
                ctx.fillRect(pos.x - 6, pos.y - 4, 12, 8);

                // Heavy cannon barrel with plasma glow
                const barrelGradient = ctx.createLinearGradient(
                    pos.x - 10,
                    pos.y - 3,
                    pos.x + 10,
                    pos.y + 3
                );
                barrelGradient.addColorStop(0, '#222');
                barrelGradient.addColorStop(0.3, '#555');
                barrelGradient.addColorStop(0.7, '#888');
                barrelGradient.addColorStop(1, '#FF0000');
                ctx.fillStyle = barrelGradient;
                ctx.fillRect(pos.x - 10, pos.y - 3, 20, 6);

                // Plasma energy rings
                ctx.strokeStyle = '#FF0000';
                ctx.lineWidth = 2;
                for (let j = 0; j < 2; j++) {
                    ctx.beginPath();
                    ctx.arc(pos.x - 5 + j * 10, pos.y, 3, 0, Math.PI * 2);
                    ctx.stroke();
                }

                this.cannonAngles.push({
                    x: pos.x - 10,
                    y: pos.y,
                    angle: Math.PI + (i - 3.5) * 0.12,
                });
            });

            // Energy cores with pulsing effect
            ctx.fillStyle = '#FF0000';
            ctx.beginPath();
            ctx.arc(50, 60, 8, 0, Math.PI * 2);
            ctx.arc(150, 60, 8, 0, Math.PI * 2);
            ctx.fill();
            // Core energy rings
            ctx.strokeStyle = '#FF6600';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(50, 60, 12, 0, Math.PI * 2);
            ctx.arc(150, 60, 12, 0, Math.PI * 2);
            ctx.stroke();

            // Hull damage indicators (more ominous red)
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.arc(15 + i * 22, 45, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // Energy conduits with dark power
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(10, 60);
            ctx.lineTo(190, 60);
            ctx.stroke();
            // Power nodes
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 6; i++) {
                ctx.beginPath();
                ctx.arc(20 + i * 30, 60, 4, 0, Math.PI * 2);
                ctx.fill();
            }

            // Name label with menacing styling
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#FF0000';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 4;
            ctx.strokeText('DREADNOUGHT', 100, 110);
            ctx.fillText('DREADNOUGHT', 100, 110);
        }

        createTitanGraphics(ctx) {
            // Main hull - massive titan with ominous dark design
            const hullGradient = ctx.createLinearGradient(5, 60, 195, 60);
            hullGradient.addColorStop(0, '#000000');
            hullGradient.addColorStop(0.2, '#1a1a1a');
            hullGradient.addColorStop(0.4, '#2d2d2d');
            hullGradient.addColorStop(0.6, '#404040');
            hullGradient.addColorStop(0.8, '#1a1a1a');
            hullGradient.addColorStop(1, '#000000');
            ctx.fillStyle = hullGradient;
            ctx.fillRect(5, 20, 190, 80);

            // Titan armor plating with dark metallic texture
            ctx.strokeStyle = '#FFD700';
            ctx.lineWidth = 3;
            for (let i = 0; i < 8; i++) {
                ctx.strokeRect(10 + i * 22, 25, 20, 70);
                // Rivets on each plate
                ctx.fillStyle = '#FFD700';
                for (let j = 0; j < 5; j++) {
                    ctx.beginPath();
                    ctx.arc(15 + i * 22 + j * 4, 30 + j * 15, 2, 0, Math.PI * 2);
                    ctx.fill();
                }
            }

            // Central command structure with ominous glow
            ctx.fillStyle = '#FF0000';
            ctx.fillRect(80, 15, 40, 30);
            // Command bridge windows with red glow
            ctx.fillStyle = '#FF4444';
            for (let i = 0; i < 4; i++) {
                ctx.fillRect(85 + i * 8, 20, 5, 10);
            }
            // Central antenna with energy pulse
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.moveTo(100, 15);
            ctx.lineTo(100, 5);
            ctx.stroke();
            ctx.beginPath();
            ctx.arc(100, 5, 4, 0, Math.PI * 2);
            ctx.fill();

            // Multiple heavy cannons with energy cores
            const cannonPositions = [
                { x: 20, y: 50 },
                { x: 45, y: 45 },
                { x: 70, y: 50 },
                { x: 95, y: 45 },
                { x: 120, y: 50 },
                { x: 145, y: 45 },
                { x: 170, y: 50 },
                { x: 195, y: 45 },
            ];

            this.cannonAngles = [];
            cannonPositions.forEach((pos, i) => {
                // Cannon base with dark metallic texture
                const baseGradient = ctx.createLinearGradient(
                    pos.x - 6,
                    pos.y - 4,
                    pos.x + 6,
                    pos.y + 4
                );
                baseGradient.addColorStop(0, '#000');
                baseGradient.addColorStop(0.5, '#333');
                baseGradient.addColorStop(1, '#000');
                ctx.fillStyle = baseGradient;
                ctx.fillRect(pos.x - 6, pos.y - 4, 12, 8);

                // Cannon barrel with energy glow
                const barrelGradient = ctx.createLinearGradient(
                    pos.x - 10,
                    pos.y - 3,
                    pos.x + 10,
                    pos.y + 3
                );
                barrelGradient.addColorStop(0, '#333');
                barrelGradient.addColorStop(0.3, '#666');
                barrelGradient.addColorStop(0.7, '#999');
                barrelGradient.addColorStop(1, '#FF0000');
                ctx.fillStyle = barrelGradient;
                ctx.fillRect(pos.x - 10, pos.y - 3, 20, 6);

                // Energy core in each cannon
                ctx.fillStyle = '#FF0000';
                ctx.beginPath();
                ctx.arc(pos.x, pos.y, 3, 0, Math.PI * 2);
                ctx.fill();

                this.cannonAngles.push({
                    x: pos.x - 10,
                    y: pos.y,
                    angle: Math.PI + (i - 3.5) * 0.15,
                });
            });

            // Energy conduits with pulsing effect
            ctx.strokeStyle = '#FF0000';
            ctx.lineWidth = 3;
            // Main power lines
            ctx.beginPath();
            ctx.moveTo(15, 60);
            ctx.lineTo(185, 60);
            ctx.stroke();
            // Secondary power lines
            ctx.beginPath();
            ctx.moveTo(15, 70);
            ctx.lineTo(185, 70);
            ctx.stroke();
            // Energy nodes with pulsing effect
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.arc(25 + i * 20, 60, 4, 0, Math.PI * 2);
                ctx.fill();
                ctx.beginPath();
                ctx.arc(25 + i * 20, 70, 4, 0, Math.PI * 2);
                ctx.fill();
            }

            // Hull damage indicators (red warning lights)
            ctx.fillStyle = '#FF0000';
            for (let i = 0; i < 8; i++) {
                ctx.beginPath();
                ctx.arc(15 + i * 22, 40, 3, 0, Math.PI * 2);
                ctx.fill();
            }

            // Name label with ultimate styling
            ctx.font = 'bold 20px Arial';
            ctx.fillStyle = '#FF0000';
            ctx.textAlign = 'center';
            ctx.strokeStyle = '#000';
            ctx.lineWidth = 5;
            ctx.strokeText('TITAN', 100, 110);
            ctx.fillText('TITAN', 100, 110);
        }

        update() {
            if (this.destroyed) return;
            this.patternTimer++;
            this.phaseTimer++;

            // Boss stays on the right side and tracks the player vertically
            const rightEdge = Graphics.width - 300; // Further right for bigger bosses
            if (this.x > rightEdge) {
                this.x -= this.speed;
                if (this.x < rightEdge) this.x = rightEdge;
            } else {
                // Track player vertically
                if (gameData.ship) {
                    const targetY = gameData.ship.y;
                    const dy = targetY - this.y;
                    // Smoothly follow the player
                    this.y += Math.sign(dy) * Math.min(Math.abs(dy), this.speed * 1.2);
                }
            }
            // Clamp position to screen
            this.y = Math.max(80, Math.min(Graphics.height - 80, this.y));

            // Boss-specific actions (summon, bullet hell, etc)
            switch (this.pattern) {
                case 'bossSummon':
                    // Summon minions every few seconds
                    if (this.phaseTimer % 180 === 0 && this.scene) {
                        for (let i = 0; i < 3; i++) {
                            const minion = new EnemyAirship(
                                this.x - 60,
                                this.y + (i - 1) * 40,
                                'fighter'
                            );
                            minion.scene = this.scene;
                            gameData.enemies.push(minion);
                            this.scene.addChild(minion);
                        }
                    }
                    break;
                case 'bossBulletHell':
                    // Fire bullet patterns (reduced by half)
                    if (this.fireTimer % 15 === 0 && this.scene) {
                        for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 4) {
                            // Changed from /8 to /4 (half as many)
                            const vx = Math.cos(angle) * 2.5; // Much slower bullet hell
                            const vy = Math.sin(angle) * 2.5;
                            const proj = new Projectile(this.x, this.y, vx, vy, 'enemy');
                            proj.scene = this.scene;
                            gameData.projectiles.push(proj);
                            this.scene.addChild(proj);
                        }
                    }
                    break;
                case 'bossUltimate':
                    // Ultimate boss pattern - combines bullet hell with summoning
                    if (this.fireTimer % 10 === 0 && this.scene) {
                        // Intense bullet pattern
                        for (let angle = 0; angle < Math.PI * 2; angle += Math.PI / 6) {
                            const vx = Math.cos(angle) * 3;
                            const vy = Math.sin(angle) * 3;
                            const proj = new Projectile(this.x, this.y, vx, vy, 'enemy');
                            proj.scene = this.scene;
                            proj.damage = 30; // Ultimate boss shots do more damage
                            gameData.projectiles.push(proj);
                            this.scene.addChild(proj);
                        }
                    }
                    // Summon elite minions every 5 seconds
                    if (this.phaseTimer % 300 === 0 && this.scene) {
                        for (let i = 0; i < 2; i++) {
                            const minion = new EnemyAirship(
                                this.x - 80,
                                this.y + (i - 0.5) * 60,
                                'tank'
                            );
                            minion.scene = this.scene;
                            gameData.enemies.push(minion);
                            this.scene.addChild(minion);
                        }
                    }
                    break;
            }

            // Fire at player with multiple cannons
            this.fireTimer++;
            if (this.fireTimer >= this.fireRate) {
                this.fireBossCannons();
                this.fireTimer = 0;
            }

            // Remove if off screen (shouldn't happen for bosses, but just in case)
            if (this.x < -150) {
                this.destroyed = true;
                if (this.scene && this.scene.children && this.scene.children.includes(this)) {
                    this.scene.removeChild(this);
                }
                const index = gameData.enemies.indexOf(this);
                if (index > -1) {
                    gameData.enemies.splice(index, 1);
                }
                this.visible = false;
                this.active = false;
                return;
            }
        }

        fireBossCannons() {
            if (!gameData.ship || !this.scene) return;

            // Fire from multiple cannons with long range (only quarter of them)
            this.cannonAngles.forEach((cannon, index) => {
                // Only fire from every fourth cannon (reduce by 75%)
                if (index % 4 === 0) {
                    const cannonX = this.x - 100 + cannon.x; // Adjust for boss position
                    const cannonY = this.y - 60 + cannon.y;

                    // Calculate angle to player
                    const dx = gameData.ship.x - cannonX;
                    const dy = gameData.ship.y - cannonY;
                    const angle = Math.atan2(dy, dx);

                    // Long range shots
                    const speed = 6.0; // Much slower boss projectiles
                    const vx = Math.cos(angle) * speed;
                    const vy = Math.sin(angle) * speed;

                    const proj = new Projectile(cannonX, cannonY, vx, vy, 'enemy');
                    proj.scene = this.scene;
                    proj.damage = 25; // Boss shots do more damage
                    gameData.projectiles.push(proj);
                    this.scene.addChild(proj);
                }
            });
        }

        takeDamage(amount, projectileType) {
            if (this.destroyed) return;
            // Damage popup
            if (this.scene && this.scene.createDamagePopup) {
                this.scene.createDamagePopup(this.x, this.y, amount);
            }
            this.health -= amount;
            gameData.bossHealth = this.health;

            // Show impact particles for boss hits
            if (this.health > 0 && this.scene && this.scene.createDamageImpactParticles) {
                this.scene.createDamageImpactParticles(this.x, this.y, amount, projectileType);
            }

            if (this.health <= 0) {
                this.destroy();
            }
        }

        destroy() {
            if (this.scene && this.scene.createExplosion) {
                this.scene.createExplosion(this.x, this.y, 5);
            }

            // Play boss explosion sound
            audioManager.play('explosion');

            this.destroyed = true;
            if (this.scene && this.scene.children && this.scene.children.includes(this)) {
                this.scene.removeChild(this);
            }
            const index = gameData.enemies.indexOf(this);
            if (index > -1) {
                gameData.enemies.splice(index, 1);
            }
            this.visible = false;
            this.active = false;
            gameData.bossHealth = 0;
            if (this._wasFrozen) {
                // Shatter into shards - reduced count
                const shardCount = 5 + Math.floor(Math.random() * 3); // Reduced from 12-16 to 5-8
                for (let i = 0; i < shardCount; i++) {
                    const angle = (Math.PI * 2 * i) / shardCount + Math.random() * 0.2;
                    const speed = 10 + Math.random() * 4;
                    const vx = Math.cos(angle) * speed;
                    const vy = Math.sin(angle) * speed;
                    const shard = Projectile.getOrCreate(
                        this.x,
                        this.y,
                        vx,
                        vy,
                        'ice-shard',
                        angle
                    );
                    shard.scene = this.scene;
                    shard.damage = 40;
                    shard._shard = true;
                    gameData.projectiles.push(shard);
                    if (this.scene) this.scene.addChild(shard);
                }
                if (this.scene && this.scene.createIceEffect) {
                    this.scene.createIceEffect(this.x, this.y, 80);
                }
            }
        }
    }

    // Projectile class
    class Projectile extends Sprite {
        static pool = [];
        static poolIndex = 0;
        static maxPoolSize = 200;

        static getOrCreate(x, y, vx, vy, type = 'player', angle = 0) {
            // Try to find an inactive projectile in the pool
            let proj = null;
            for (let i = 0; i < Projectile.pool.length; i++) {
                if (!Projectile.pool[i].active) {
                    proj = Projectile.pool[i];
                    break;
                }
            }

            if (proj) {
                proj.reset(x, y, vx, vy, type, angle);
                return proj;
            }

            // None available, create new if under max pool size
            if (Projectile.pool.length < Projectile.maxPoolSize) {
                proj = new Projectile(x, y, vx, vy, type, angle);
                Projectile.pool.push(proj);
                gameData.performanceStats.objectsCreated++;
            } else {
                // Reuse oldest projectile if pool is full
                proj = Projectile.pool[Projectile.poolIndex];
                Projectile.poolIndex = (Projectile.poolIndex + 1) % Projectile.maxPoolSize;
                proj.reset(x, y, vx, vy, type, angle);
            }
            return proj;
        }

        constructor(x, y, vx, vy, type = 'player', angle = 0) {
            super();
            this.active = true;
            this.destroyed = false;
            this.bitmap = new Bitmap(8, 8);
            this.anchor.set(0.5, 0.5);
            this.x = x;
            this.y = y;
            this.vx = vx;
            this.vy = vy;
            this.type = type;
            this.angle = angle;
            this.damage = type === 'player' ? 25 : 15;
            this.createProjectileGraphics();
            this.rotation = angle;
        }

        reset(x, y, vx, vy, type = 'player', angle = 0) {
            this.active = true;
            this.destroyed = false;
            this.visible = true;
            this.x = x;
            this.y = y;
            this.vx = vx;
            this.vy = vy;
            this.type = type;
            this.angle = angle;
            this.damage = type === 'player' ? 25 : 15;

            // Reuse bitmap if possible, otherwise create new
            if (type === 'missile' || type === 'laser' || type === 'wave-laser') {
                if (!this.bitmap || this.bitmap.width !== 16) {
                    this.bitmap = new Bitmap(16, 8);
                }
            } else if (type === 'wave') {
                if (!this.bitmap || this.bitmap.width !== 8) {
                    this.bitmap = new Bitmap(8, 8);
                }
            } else {
                if (!this.bitmap || this.bitmap.width !== 8) {
                    this.bitmap = new Bitmap(8, 8);
                }
            }

            this.createProjectileGraphics();
            this.rotation = angle;

            // Reset custom properties
            this._piercing = false;
            this._wavePhase = undefined;
            this._spreadAngle = undefined;
            this._waveLevel = undefined;
            this._waveDirection = undefined;
            this._originalX = undefined;
            this._originalY = undefined;
            this._megaMissile = undefined;
            this._homing = undefined;
            this._chainChance = undefined;
            this._burn = undefined;
            this._ricochet = undefined;
            this._hasRicocheted = undefined;
            this._elementalType = undefined;
            this._elementalLevel = undefined;
            this._bigExplosion = undefined;
            this._shard = undefined;

            // Remove from parent if present (will be re-added)
            if (this.parent && this.parent.removeChild) {
                this.parent.removeChild(this);
            }
        }
        createProjectileGraphics() {
            // Bulletproof projectile graphics logic for all weapon/powerup combos
            if (this.type === 'laser' || this.type === 'wave-laser') {
                // Classic red/yellow/white beam
                this.bitmap = new Bitmap(24, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 24, 6);
                const grad = ctx.createLinearGradient(0, 3, 24, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.2, '#FFD700');
                grad.addColorStop(0.5, '#FF3333');
                grad.addColorStop(0.8, '#FFD700');
                grad.addColorStop(1, '#fff');
                ctx.save();
                ctx.shadowColor = '#FF3333';
                ctx.shadowBlur = 12;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(12, 3, 12, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'laser-plasma') {
                // Orange beam
                this.bitmap = new Bitmap(24, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 24, 6);
                const grad = ctx.createLinearGradient(0, 3, 24, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.2, '#FFB347');
                grad.addColorStop(0.5, '#FF6600');
                grad.addColorStop(0.8, '#FFB347');
                grad.addColorStop(1, '#fff');
                ctx.save();
                ctx.shadowColor = '#FF6600';
                ctx.shadowBlur = 12;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(12, 3, 12, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'laser-gatling') {
                // Magenta beam
                this.bitmap = new Bitmap(24, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 24, 6);
                const grad = ctx.createLinearGradient(0, 3, 24, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.2, '#FF99FF');
                grad.addColorStop(0.5, '#FF33CC');
                grad.addColorStop(0.8, '#FF99FF');
                grad.addColorStop(1, '#fff');
                ctx.save();
                ctx.shadowColor = '#FF33CC';
                ctx.shadowBlur = 12;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(12, 3, 12, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'laser-rocket') {
                // Purple beam
                this.bitmap = new Bitmap(24, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 24, 6);
                const grad = ctx.createLinearGradient(0, 3, 24, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.2, '#B266FF');
                grad.addColorStop(0.5, '#9933FF');
                grad.addColorStop(0.8, '#B266FF');
                grad.addColorStop(1, '#fff');
                ctx.save();
                ctx.shadowColor = '#9933FF';
                ctx.shadowBlur = 12;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(12, 3, 12, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'wave') {
                // Wave pattern (blue/cyan)
                this.bitmap = new Bitmap(16, 16);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 16, 16);
                const gradient = ctx.createRadialGradient(8, 8, 0, 8, 8, 8);
                gradient.addColorStop(0, '#FFFFFF');
                gradient.addColorStop(0.3, '#00FFFF');
                gradient.addColorStop(0.7, '#3399FF');
                gradient.addColorStop(1, '#0033FF');
                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(8, 8, 8, 0, Math.PI * 2);
                ctx.fill();
                return;
            } else if (this.type === 'player') {
                // Highly visible blue laser with white core and dark outline
                this.bitmap = new Bitmap(16, 10);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 16, 10);
                // Outer dark outline
                ctx.save();
                ctx.beginPath();
                ctx.ellipse(8, 5, 7, 3, 0, 0, Math.PI * 2);
                ctx.strokeStyle = '#002244';
                ctx.lineWidth = 2.5;
                ctx.stroke();
                ctx.restore();
                // Bright blue glow
                ctx.save();
                ctx.shadowColor = '#00e0ff';
                ctx.shadowBlur = 16;
                ctx.beginPath();
                ctx.ellipse(8, 5, 6, 2.5, 0, 0, Math.PI * 2);
                ctx.fillStyle = '#00bfff';
                ctx.fill();
                ctx.restore();
                // White hot core
                ctx.save();
                ctx.beginPath();
                ctx.ellipse(8, 5, 3, 1.2, 0, 0, Math.PI * 2);
                ctx.fillStyle = '#fff';
                ctx.globalAlpha = 0.95;
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'plasma') {
                // Cyan orb/ball
                this.bitmap = new Bitmap(14, 14);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 14, 14);
                const grad = ctx.createRadialGradient(7, 7, 0, 7, 7, 7);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.3, '#B0E0FF');
                grad.addColorStop(0.7, '#00FFFF');
                grad.addColorStop(1, '#3399FF');
                ctx.save();
                ctx.shadowColor = '#00FFFF';
                ctx.shadowBlur = 8;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.arc(7, 7, 7, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'gatling') {
                // Yellow/orange bullet
                this.bitmap = new Bitmap(10, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 10, 6);
                const grad = ctx.createLinearGradient(0, 3, 10, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.3, '#FFD700');
                grad.addColorStop(0.7, '#FF6600');
                grad.addColorStop(1, '#FFD700');
                ctx.save();
                ctx.shadowColor = '#FFD700';
                ctx.shadowBlur = 6;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(5, 3, 5, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'rocket') {
                // Red/gray missile
                this.bitmap = new Bitmap(16, 8);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 16, 8);
                // Body
                ctx.save();
                ctx.fillStyle = '#888';
                ctx.fillRect(2, 2, 10, 4);
                // Nose
                ctx.fillStyle = '#FF3333';
                ctx.beginPath();
                ctx.moveTo(12, 2);
                ctx.lineTo(16, 4);
                ctx.lineTo(12, 6);
                ctx.closePath();
                ctx.fill();
                // Fins
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(2, 1, 2, 2);
                ctx.fillRect(2, 5, 2, 2);
                ctx.restore();
                return;
            } else if (this.type === 'turret') {
                // Green bullet/beam
                this.bitmap = new Bitmap(12, 6);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 12, 6);
                const grad = ctx.createLinearGradient(0, 3, 12, 3);
                grad.addColorStop(0, '#fff');
                grad.addColorStop(0.5, '#00FF66');
                grad.addColorStop(1, '#008800');
                ctx.save();
                ctx.shadowColor = '#00FF66';
                ctx.shadowBlur = 8;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(6, 3, 6, 2, 0, 0, Math.PI * 2);
                ctx.fill();
                ctx.restore();
                return;
            } else if (this.type === 'missile') {
                // Orange/gray missile
                this.bitmap = new Bitmap(16, 8);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 16, 8);
                // Body
                ctx.save();
                ctx.fillStyle = '#AAA';
                ctx.fillRect(2, 2, 10, 4);
                // Nose
                ctx.fillStyle = '#FF9900';
                ctx.beginPath();
                ctx.moveTo(12, 2);
                ctx.lineTo(16, 4);
                ctx.lineTo(12, 6);
                ctx.closePath();
                ctx.fill();
                // Fins
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(2, 1, 2, 2);
                ctx.fillRect(2, 5, 2, 2);
                ctx.restore();
                return;
            } else if (this.type === 'mega-missile') {
                // Large blue/white missile
                this.bitmap = new Bitmap(24, 12);
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 24, 12);
                // Body
                ctx.save();
                ctx.fillStyle = '#B0E0FF';
                ctx.fillRect(4, 4, 14, 4);
                // Nose
                ctx.fillStyle = '#fff';
                ctx.beginPath();
                ctx.moveTo(18, 4);
                ctx.lineTo(24, 6);
                ctx.lineTo(18, 8);
                ctx.closePath();
                ctx.fill();
                // Fins
                ctx.fillStyle = '#3399FF';
                ctx.fillRect(4, 2, 3, 3);
                ctx.fillRect(4, 7, 3, 3);
                ctx.restore();
                return;
            } else if (this.type === 'enemy') {
                // Larger red/orange bullet
                this.bitmap = new Bitmap(18, 12); // was 10, 6
                const ctx = this.bitmap.context;
                ctx.clearRect(0, 0, 18, 12);
                const grad = ctx.createLinearGradient(0, 6, 18, 6);
                grad.addColorStop(0, '#FF0000');
                grad.addColorStop(0.5, '#FF6600');
                grad.addColorStop(1, '#fff');
                ctx.save();
                ctx.shadowColor = '#FF6600';
                ctx.shadowBlur = 10;
                ctx.fillStyle = grad;
                ctx.beginPath();
                ctx.ellipse(9, 6, 9, 4, 0, 0, Math.PI * 2); // was (5, 3, 5, 2, ...)
                ctx.fill();
                ctx.restore();
                return;
            }

            // Elemental projectile effects - add visual effects to existing projectiles
            if (this._elementalType) {
                // Add elemental glow effects to existing projectiles
                const originalBitmap = this.bitmap;
                const enhancedBitmap = new Bitmap(
                    originalBitmap.width + 10,
                    originalBitmap.height + 10
                );
                const ctx = enhancedBitmap.context;

                // Draw the original projectile
                ctx.drawImage(originalBitmap._canvas, 5, 5);

                // Add elemental glow
                ctx.save();
                ctx.globalCompositeOperation = 'source-over';

                switch (this._elementalType) {
                    case 'lightning':
                        ctx.shadowColor = '#FFFF00';
                        ctx.shadowBlur = 15;
                        ctx.globalAlpha = 0.6;
                        ctx.drawImage(originalBitmap._canvas, 5, 5);
                        break;
                    case 'fire':
                        ctx.shadowColor = '#FF4500';
                        ctx.shadowBlur = 12;
                        ctx.globalAlpha = 0.5;
                        ctx.drawImage(originalBitmap._canvas, 5, 5);
                        break;
                    case 'ice':
                        ctx.shadowColor = '#87CEEB';
                        ctx.shadowBlur = 10;
                        ctx.globalAlpha = 0.4;
                        ctx.drawImage(originalBitmap._canvas, 5, 5);
                        break;
                }

                ctx.restore();
                this.bitmap = enhancedBitmap;
                return;
            }

            // Fallback for unknown types: white dot with black outline
            this.bitmap = new Bitmap(10, 10);
            const ctx = this.bitmap.context;
            ctx.clearRect(0, 0, 10, 10);
            ctx.save();
            ctx.beginPath();
            ctx.arc(5, 5, 4, 0, Math.PI * 2);
            ctx.fillStyle = '#fff';
            ctx.fill();
            ctx.lineWidth = 2;
            ctx.strokeStyle = '#000';
            ctx.stroke();
            ctx.restore();
            return;
        }

        update() {
            if (!this.active) return;

            let speedMultiplier = 1;
            if (gameData.timeWarpActive) {
                speedMultiplier = 0.5; // 50% slower during time warp
            }

            this.x += this.vx * speedMultiplier;
            this.y += this.vy * speedMultiplier;

            // Rotate projectiles to face their travel direction
            if (
                this.type === 'enemy' ||
                this.type === 'missile' ||
                this.type === 'mega-missile' ||
                this.type === 'rocket'
            ) {
                this.rotation = Math.atan2(this.vy, this.vx);
            }

            // Remove if off screen (optimized bounds checking)
            if (
                this.x < -50 ||
                this.x > Graphics.width + 50 ||
                this.y < -50 ||
                this.y > Graphics.height + 50
            ) {
                this.destroy();
                return; // Early return to avoid unnecessary processing
            }
            // --- 2. Heatseeking Missiles ---
            if (this.type === 'missile') {
                // Retarget every frame: always seek nearest enemy
                let nearest = null;
                let minDist = Infinity;
                for (const enemy of gameData.enemies) {
                    const dx = enemy.x - this.x;
                    const dy = enemy.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    if (dist < minDist) {
                        minDist = dist;
                        nearest = enemy;
                    }
                }
                if (nearest) {
                    // Adjust velocity toward the enemy
                    const dx = nearest.x - this.x;
                    const dy = nearest.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy) || 1;
                    const homingStrength = 0.2; // How sharply the missile turns
                    const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy) || 8;
                    const desiredVx = (dx / dist) * speed;
                    const desiredVy = (dy / dist) * speed;
                    this.vx += (desiredVx - this.vx) * homingStrength;
                    this.vy += (desiredVy - this.vy) * homingStrength;
                    // Clamp speed
                    const newSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                    const maxSpeed = 10;
                    if (newSpeed > maxSpeed) {
                        this.vx = (this.vx / newSpeed) * maxSpeed;
                        this.vy = (this.vy / newSpeed) * maxSpeed;
                    }
                }
                // If no enemies, missile continues on last trajectory
            }
            // --- 3. Sine Wave for 'wave' and 'wave-laser' types ---
            if (this.type === 'wave' || this.type === 'wave-laser') {
                // Use _waveDirection for opposite rotation
                const dir = this._waveDirection || 1;
                this._wavePhase = (this._wavePhase || 0) + 0.4 * dir; // Opposite phase increment

                // Tighter wave pattern
                const baseAmplitude = this.type === 'wave-laser' ? 14 : 18;
                const level = this._waveLevel || 1;
                const waveAmplitude =
                    (baseAmplitude + (level - 1) * 10) * (this._waveAmplitudeRandom || 1); // Level 2 = +10 amplitude, randomized
                const waveY = Math.sin(this._wavePhase) * waveAmplitude;

                // Store original position if not already stored
                if (this._originalX === undefined) {
                    this._originalX = this.x;
                    this._originalY = this.y;
                }

                // Move forward along the spread angle
                const spreadAngle = this._spreadAngle || 0;
                this.x += Math.cos(spreadAngle) * 12;
                this.y += Math.sin(spreadAngle) * 12;

                // Apply wave movement perpendicular to the spread direction
                const perpAngle = spreadAngle + Math.PI / 2; // 90 degrees perpendicular
                this.x += Math.cos(perpAngle) * waveY * 0.3; // Slight horizontal wave component
                this.y += Math.sin(perpAngle) * waveY; // Main vertical wave component

                // Remove if off screen (expanded boundaries for tall waves)
                if (
                    this.x < -50 ||
                    this.x > Graphics.width + 50 ||
                    this.y < -100 ||
                    this.y > Graphics.height + 100
                ) {
                    this.destroy();
                }
            }
            // Mega-missile homing
            if (this._megaMissile && this._homing) {
                let nearest = null;
                let minDist = Infinity;
                for (const enemy of gameData.enemies) {
                    const dx = enemy.x - this.x;
                    const dy = enemy.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy);
                    if (dist < minDist) {
                        minDist = dist;
                        nearest = enemy;
                    }
                }
                if (nearest) {
                    const dx = nearest.x - this.x;
                    const dy = nearest.y - this.y;
                    const dist = Math.sqrt(dx * dx + dy * dy) || 1;
                    const homingStrength = 0.8; // More aggressive
                    const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy) || 24;
                    const desiredVx = (dx / dist) * speed;
                    const desiredVy = (dy / dist) * speed;
                    // Snap more strongly to new target
                    this.vx += (desiredVx - this.vx) * homingStrength;
                    this.vy += (desiredVy - this.vy) * homingStrength;
                    // Clamp speed
                    const newSpeed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
                    const maxSpeed = 28;
                    if (newSpeed > maxSpeed) {
                        this.vx = (this.vx / newSpeed) * maxSpeed;
                        this.vy = (this.vy / newSpeed) * maxSpeed;
                    }
                }
            }
        }

        destroy() {
            // Instead of removing from pool, mark as inactive and hide
            this.active = false;
            this.destroyed = true;
            this.visible = false;
            // Remove from parent if present
            if (this.parent && this.parent.removeChild) {
                this.parent.removeChild(this);
            } else if (this.scene && this.scene.removeChild) {
                this.scene.removeChild(this);
            }
            // Remove from gameData.projectiles
            const index = gameData.projectiles.indexOf(this);
            if (index > -1) {
                gameData.projectiles.splice(index, 1);
            }
        }
    }

    // Cloud class for background
    class Cloud extends Sprite {
        constructor(x, y, size = 1, skyColors = { top: '#7FB3D3', bottom: '#87CEEB' }) {
            super();
            this.bitmap = new Bitmap(120 * size, 80 * size);
            this.anchor.set(0.5, 0.5);
            this.x = x;
            this.y = y;
            this.size = size;
            this.skyColors = skyColors;
            this.speed = 0.5 + Math.random() * 0.5;
            this.reflection = null;
            this.createCloudGraphics();
            this.createReflection();
        }

        // Helper to blend two hex colors
        blendColor(hex1, hex2, t) {
            const c1 = parseInt(hex1.slice(1), 16);
            const c2 = parseInt(hex2.slice(1), 16);
            const r = Math.round((c1 >> 16) * (1 - t) + (c2 >> 16) * t);
            const g = Math.round(((c1 >> 8) & 0xff) * (1 - t) + ((c2 >> 8) & 0xff) * t);
            const b = Math.round((c1 & 0xff) * (1 - t) + (c2 & 0xff) * t);
            return `#${((r << 16) | (g << 8) | b).toString(16).padStart(6, '0')}`;
        }

        createCloudGraphics() {
            const context = this.bitmap.context;
            const centerX = 60 * this.size;
            const centerY = 40 * this.size;

            // Sample sky color at this.y
            const t = Math.min(1, Math.max(0, this.y / Graphics.height));
            const skyColor = this.blendColor(this.skyColors.top, this.skyColors.bottom, t);

            // Blend white with sky color for tint
            const cloudColor = this.blendColor('#ffffff', skyColor, 0.65);

            // Cloud gradient
            const gradient = context.createRadialGradient(
                centerX,
                centerY,
                0,
                centerX,
                centerY,
                50 * this.size
            );
            gradient.addColorStop(0, cloudColor + 'e6'); // more opaque
            gradient.addColorStop(0.7, cloudColor + '99');
            gradient.addColorStop(1, cloudColor + '00');

            context.fillStyle = gradient;

            // Draw multiple circles to create cloud shape
            const circles = [
                { x: centerX, y: centerY, r: 30 * this.size },
                { x: centerX - 20 * this.size, y: centerY, r: 25 * this.size },
                { x: centerX + 20 * this.size, y: centerY, r: 25 * this.size },
                { x: centerX, y: centerY - 15 * this.size, r: 20 * this.size },
                { x: centerX, y: centerY + 15 * this.size, r: 20 * this.size },
            ];

            circles.forEach(circle => {
                context.beginPath();
                context.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2);
                context.fill();
            });
        }

        createReflection() {
            // Create reflection sprite
            this.reflection = new Sprite();
            this.reflection.bitmap = new Bitmap(120 * this.size, 80 * this.size);
            this.reflection.anchor.set(0.5, 0.5);
            this.reflection.visible = false; // Only visible during day
            this.updateReflectionPosition();
            this.updateReflectionGraphics();
        }

        updateReflectionPosition() {
            if (this.reflection) {
                const waterLine = Graphics.height * 0.6;
                this.reflection.x = this.x;
                this.reflection.y = waterLine + (waterLine - this.y);
                this.reflection.scale.y = -0.6; // Flip and compress vertically

                // Only show reflection if it's actually in the water area
                this.reflection.visible = this.reflection.y >= waterLine;
            }
        }

        updateReflectionGraphics() {
            if (this.reflection && this.reflection.bitmap) {
                const context = this.reflection.bitmap.context;
                const centerX = 60 * this.size;
                const centerY = 40 * this.size;

                // Clear the bitmap
                context.clearRect(
                    0,
                    0,
                    this.reflection.bitmap.width,
                    this.reflection.bitmap.height
                );

                // Sample sky color at reflection position
                const t = Math.min(1, Math.max(0, this.reflection.y / Graphics.height));
                const skyColor = this.blendColor(this.skyColors.top, this.skyColors.bottom, t);
                const cloudColor = this.blendColor('#ffffff', skyColor, 0.65);

                // Cloud gradient for reflection
                const gradient = context.createRadialGradient(
                    centerX,
                    centerY,
                    0,
                    centerX,
                    centerY,
                    50 * this.size
                );
                gradient.addColorStop(0, cloudColor + 'e6');
                gradient.addColorStop(0.7, cloudColor + '99');
                gradient.addColorStop(1, cloudColor + '00');

                context.fillStyle = gradient;

                // Draw multiple circles to create cloud shape
                const circles = [
                    { x: centerX, y: centerY, r: 30 * this.size },
                    { x: centerX - 20 * this.size, y: centerY, r: 25 * this.size },
                    { x: centerX + 20 * this.size, y: centerY, r: 25 * this.size },
                    { x: centerX, y: centerY - 15 * this.size, r: 20 * this.size },
                    { x: centerX, y: centerY + 15 * this.size, r: 20 * this.size },
                ];

                circles.forEach(circle => {
                    context.beginPath();
                    context.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2);
                    context.fill();
                });
            }
        }

        update() {
            this.x -= this.speed;

            // Update reflection position and visibility
            if (this.reflection) {
                this.updateReflectionPosition();

                // Show reflection during day and night, but with different alpha
                const cycle = this.scene ? this.scene._dayNightCycle : 0;
                const isDay = cycle >= 0.18 && cycle <= 0.82;

                // Update reflection alpha based on time of day and distance from horizon
                if (this.reflection.visible) {
                    const waterLine = Graphics.height * 0.6;
                    const oceanHeight = Graphics.height - waterLine;
                    const distanceFromHorizon = (this.reflection.y - waterLine) / oceanHeight;

                    // Different alpha for day vs night
                    let baseAlpha = isDay ? 0.3 : 0.15; // Night reflections are more subtle
                    const alpha =
                        baseAlpha *
                        (1 - distanceFromHorizon) *
                        (0.7 + 0.3 * Math.sin(gameData.frameCount * 0.01));
                    this.reflection.alpha = alpha;
                }
            }

            // Remove if off screen or below spawn line
            const spawnLine = Graphics.height * 0.5;
            if (this.x < -100 || this.y > spawnLine) {
                this.destroy();
            }
        }

        destroy() {
            if (this.scene) this.scene.removeChild(this);

            const index = gameData.clouds.indexOf(this);
            if (index > -1) {
                gameData.clouds.splice(index, 1);
            }
        }
    }

    // --- PowerUp Class ---
    class PowerUp extends Sprite {
        constructor(type, x, y) {
            super();
            this.type = type; // 'spread', 'double', etc.
            this.bitmap = new Bitmap(32, 32);
            this.anchor.set(0.5, 0.5);
            this.x = x;
            this.y = y;
            this.vx = -2;
            this.createPowerUpGraphics();
        }
        createPowerUpGraphics() {
            const ctx = this.bitmap.context;
            ctx.clearRect(0, 0, 32, 32);
            ctx.save();
            ctx.globalAlpha = 0.85;
            ctx.beginPath();
            ctx.arc(16, 16, 14, 0, Math.PI * 2);
            const colorMap = {
                spread: '#00FFAA',
                double: '#FFAA00',
                wave: '#3399FF',
                laser: '#FF3333',
                lightning: '#FFFF00',
                fire: '#FF4500',
                ice: '#87CEEB',
            };
            ctx.fillStyle = colorMap[this.type] || '#FFD700';
            ctx.fill();
            ctx.lineWidth = 3;
            ctx.strokeStyle = '#FFF';
            ctx.stroke();
            ctx.font = 'bold 18px Arial';
            ctx.fillStyle = '#222';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            const letterMap = {
                spread: 'S',
                double: 'D',
                wave: 'W',
                laser: 'L',
                lightning: '⚡',
                fire: '🔥',
                ice: '❄',
            };
            ctx.fillText(letterMap[this.type] || '?', 16, 18);
            ctx.restore();
        }
        update() {
            this.x += this.vx;
            if (this.x < -32) this.destroy();
        }
        destroy() {
            if (this.parent && this.parent.removeChild) this.parent.removeChild(this);
            const idx = gameData.powerUps.indexOf(this);
            if (idx > -1) gameData.powerUps.splice(idx, 1);
        }
    }

    // Player Airship class
    class Airship extends Sprite {
        constructor(x, y) {
            super();
            this.bitmap = new Bitmap(180, 140); // More padding for top/bottom
            this.anchor.set(0.5, 0.5);
            this.x = x || 150;
            this.y = y || Graphics.height / 2;
            this.vx = 0;
            this.vy = 0;
            this.maxSpeed = 8;
            this.acceleration = 0.8;
            this.friction = 0.92;
            this.health = 500;
            this.maxHealth = 500;
            this.invulnerable = false;
            this.invulnerabilityTimer = 0;
            this.engineTrail = [];
            this.turretAngle = Math.PI; // Default facing left (180 degrees)
            this.createShipGraphics();
            // Turret timer
            this.turretTimer = 0;
            this.turretInterval = 60; // 1 seconds at 60fps (much faster firing)
        }

        createShipGraphics(turretAngle = this.turretAngle) {
            const context = this.bitmap.context;
            const centerX = 90;
            const centerY = 70;
            context.clearRect(0, 0, 180, 140);

            // --- Balloon ---
            context.save();
            context.beginPath();
            context.ellipse(centerX, centerY - 28, 50, 22, 0, 0, Math.PI * 2);
            context.closePath();
            const balloonGradient = context.createLinearGradient(
                centerX - 50,
                centerY - 28,
                centerX + 50,
                centerY - 28
            );
            balloonGradient.addColorStop(0, '#e6d8a8');
            balloonGradient.addColorStop(0.5, '#fffbe0');
            balloonGradient.addColorStop(1, '#bfa76a');
            context.fillStyle = balloonGradient;
            context.fill();
            context.strokeStyle = '#a68b4a';
            context.lineWidth = 2;
            context.stroke();
            context.restore();

            // --- Balloon seams ---
            context.save();
            context.strokeStyle = 'rgba(180,150,80,0.5)';
            context.lineWidth = 1;
            for (let i = -3; i <= 3; i++) {
                context.beginPath();
                context.ellipse(centerX, centerY - 28, 50 - Math.abs(i) * 7, 22, 0, 0, Math.PI * 2);
                context.stroke();
            }
            context.restore();

            // --- Ropes/Struts ---
            context.save();
            context.strokeStyle = '#a68b4a';
            context.lineWidth = 2;
            const ropeTargets = [
                { x: centerX - 28, y: centerY + 22 },
                { x: centerX - 14, y: centerY + 22 },
                { x: centerX, y: centerY + 22 },
                { x: centerX + 14, y: centerY + 22 },
                { x: centerX + 28, y: centerY + 22 },
            ];
            const ropeSources = [centerX - 32, centerX - 16, centerX, centerX + 16, centerX + 32];
            for (let i = 0; i < ropeTargets.length; i++) {
                context.beginPath();
                context.moveTo(ropeSources[i], centerY - 10);
                context.lineTo(ropeTargets[i].x, ropeTargets[i].y);
                context.stroke();
            }
            context.restore();

            // --- Hull (smaller) ---
            context.save();
            context.beginPath();
            context.moveTo(centerX - 38, centerY + 22);
            context.lineTo(centerX + 38, centerY + 22);
            context.lineTo(centerX + 28, centerY + 52);
            context.lineTo(centerX - 28, centerY + 52);
            context.closePath();
            const hullGradient = context.createLinearGradient(
                centerX - 38,
                centerY + 32,
                centerX + 38,
                centerY + 52
            );
            hullGradient.addColorStop(0, '#8B5C2A');
            hullGradient.addColorStop(0.5, '#D2A679');
            hullGradient.addColorStop(1, '#8B5C2A');
            context.fillStyle = hullGradient;
            context.fill();
            context.strokeStyle = '#654321';
            context.lineWidth = 2;
            context.stroke();
            context.restore();

            // --- Metallic trim ---
            context.save();
            context.strokeStyle = '#b0b0b0';
            context.lineWidth = 2;
            context.beginPath();
            context.moveTo(centerX - 38, centerY + 22);
            context.lineTo(centerX + 38, centerY + 22);
            context.stroke();
            context.restore();

            // --- Deck ---
            context.save();
            context.fillStyle = '#e6d8a8';
            context.fillRect(centerX - 14, centerY + 28, 28, 6);
            context.strokeStyle = '#bfa76a';
            context.lineWidth = 1.5;
            context.strokeRect(centerX - 14, centerY + 28, 28, 6);
            context.restore();

            // --- Portholes ---
            context.save();
            for (let i = -1; i <= 1; i++) {
                context.beginPath();
                context.arc(centerX + i * 14, centerY + 40, 4, 0, Math.PI * 2);
                context.fillStyle = '#b0e0ff';
                context.fill();
                context.strokeStyle = '#4682B4';
                context.lineWidth = 1;
                context.stroke();
            }
            context.restore();

            // --- Cabin windows (smaller) ---
            context.save();
            context.beginPath();
            context.arc(centerX, centerY + 32, 6, 0, Math.PI, true);
            context.fillStyle = '#b0e0ff';
            context.fill();
            context.strokeStyle = '#4682B4';
            context.lineWidth = 1.5;
            context.stroke();
            context.restore();

            // --- Gun/Cannon at the front (mirror of turret) ---
            context.save();
            // Gun base
            context.translate(centerX + 38, centerY + 30);
            context.fillStyle = '#444';
            context.beginPath();
            context.arc(0, 0, 8, 0, Math.PI * 2);
            context.fill();
            // Gun barrel (facing right)
            context.fillStyle = '#888';
            context.fillRect(0, -3, 16, 6);
            context.restore();

            // --- Side Turret (left side) ---
            context.save();
            // Turret base
            context.translate(centerX - 38, centerY + 30);
            context.fillStyle = '#444';
            context.beginPath();
            context.arc(0, 0, 8, 0, Math.PI * 2);
            context.fill();
            // Turret barrel (aiming at target)
            context.rotate(turretAngle);
            context.fillStyle = '#888';
            context.fillRect(0, -3, 16, 6); // Fixed: barrel now points in rotation direction
            context.restore();
            // Force bitmap update for RPG Maker rendering
            if (this.bitmap && this.bitmap.baseTexture && this.bitmap.baseTexture.update) {
                this.bitmap.baseTexture.update();
            } else if (this.bitmap && this.bitmap._setDirty) {
                this.bitmap._setDirty();
            }
        }

        update() {
            // Handle input
            if (Input.isPressed('left')) {
                this.vx -= this.acceleration;
            }
            if (Input.isPressed('right')) {
                this.vx += this.acceleration;
            }
            if (Input.isPressed('up')) {
                this.vy -= this.acceleration;
            }
            if (Input.isPressed('down')) {
                this.vy += this.acceleration;
            }

            // Apply friction
            this.vx *= this.friction;
            this.vy *= this.friction;

            // Limit speed
            const speed = Math.sqrt(this.vx * this.vx + this.vy * this.vy);
            if (speed > this.maxSpeed) {
                this.vx = (this.vx / speed) * this.maxSpeed;
                this.vy = (this.vy / speed) * this.maxSpeed;
            }

            // Update position
            this.x += this.vx;
            this.y += this.vy;

            // Boundary constraints
            this.x = Math.max(40, Math.min(Graphics.width - 40, this.x));
            this.y = Math.max(30, Math.min(Graphics.height - 30, this.y));

            // Update engine trail with enhanced particles
            this.engineTrail.push({
                x: this.x - 35,
                y: this.y,
                life: 20,
                vx: -2 - Math.random() * 2,
                vy: (Math.random() - 0.5) * 2,
                color: ['#00FFFF', '#87CEEB', '#B0E0E6'][Math.floor(Math.random() * 3)],
            });

            // Update trail particles
            this.engineTrail.forEach(particle => {
                particle.x += particle.vx;
                particle.y += particle.vy;
                particle.life--;
            });

            // Remove dead particles
            this.engineTrail = this.engineTrail.filter(particle => particle.life > 0);

            // Update invulnerability
            if (this.invulnerable) {
                this.invulnerabilityTimer--;
                this.alpha = this.invulnerabilityTimer % 10 < 5 ? 0.5 : 1;
                if (this.invulnerabilityTimer <= 0) {
                    this.invulnerable = false;
                    this.alpha = 1;
                }
            }

            // Weapon heat management - only cool when not firing
            if (gameData.weaponHeat > 0 && !Input.isPressed('ok')) {
                const oldHeat = gameData.weaponHeat;
                gameData.weaponHeat -= gameData.heatDissipation * 2; // Faster cooling
                if (gameData.weaponHeat < 0) gameData.weaponHeat = 0;

                // Check if heat just reached 0 (optimal temperature)
                if (oldHeat > 0 && gameData.weaponHeat === 0) {
                    if (this.parent && this.parent.createOptimalTemperatureEffect) {
                        this.parent.createOptimalTemperatureEffect();
                    }
                }
            }

            // --- Automated turret logic ---
            // Turret aiming: always track nearest enemy
            const centerX = 90;
            const centerY = 70;
            const turretX = this.x - centerX + (centerX - 38);
            const turretY = this.y - centerY + (centerY + 30);
            let nearest = null;
            let minDist = Infinity;
            for (const enemy of gameData.enemies) {
                const dx = enemy.x - turretX;
                const dy = enemy.y - turretY;
                const dist = Math.sqrt(dx * dx + dy * dy);
                if (dist < minDist) {
                    minDist = dist;
                    nearest = enemy;
                }
            }
            if (nearest) {
                // Predictive aiming (same as fireSideTurret)
                const speed = 20;
                if (!nearest._lastX) {
                    nearest._lastX = nearest.x;
                    nearest._lastY = nearest.y;
                }
                const enemyVx = nearest.x - nearest._lastX;
                const enemyVy = nearest.y - nearest._lastY;
                nearest._lastX = nearest.x;
                nearest._lastY = nearest.y;
                const dx = nearest.x - turretX;
                const dy = nearest.y - turretY;
                const a = enemyVx * enemyVx + enemyVy * enemyVy - speed * speed;
                const b = 2 * (dx * enemyVx + dy * enemyVy);
                const c = dx * dx + dy * dy;
                let t = 0;
                if (Math.abs(a) < 0.0001) {
                    t = c / -b;
                } else {
                    const disc = b * b - 4 * a * c;
                    if (disc >= 0) {
                        const t1 = (-b + Math.sqrt(disc)) / (2 * a);
                        const t2 = (-b - Math.sqrt(disc)) / (2 * a);
                        t = Math.min(t1, t2);
                        if (t < 0) t = Math.max(t1, t2);
                    }
                }
                if (!t || t < 0.05) t = 0.05;
                const targetX = nearest.x + enemyVx * t;
                const targetY = nearest.y + enemyVy * t;
                const baseAngle = Math.atan2(targetY - turretY, targetX - turretX);
                this.updateTurretAngle(baseAngle);
            } else {
                // If no enemies, reset turret to default position
                this.updateTurretAngle(Math.PI);
            }
            this.turretTimer++;
            if (this.turretTimer >= this.turretInterval) {
                this.fireSideTurret();
                this.turretTimer = 0;
            }

            // ... existing code ...
        }

        updateTurretAngle(angle) {
            // Smooth turret rotation - interpolate towards target angle
            const angleDiff = angle - this.turretAngle;
            // Normalize angle difference to shortest path
            let normalizedDiff = angleDiff;
            while (normalizedDiff > Math.PI) normalizedDiff -= Math.PI * 2;
            while (normalizedDiff < -Math.PI) normalizedDiff += Math.PI * 2;
            // Smooth interpolation (0.1 = slow, 0.3 = fast)
            this.turretAngle += normalizedDiff * 0.2;
            this.createShipGraphics();
        }

        takeDamage(amount) {
            if (this.invulnerable) return;

            // Damage popup
            if (this.scene && this.scene.createDamagePopup) {
                this.scene.createDamagePopup(this.x, this.y, amount);
            }

            this.health -= amount;
            this.invulnerable = true;
            this.invulnerabilityTimer = 120; // 2 seconds

            // Screen shake removed
            // gameData.screenShake = 10;

            if (this.health <= 0) {
                this.destroy();
            }
        }

        destroy() {
            // Create explosion effect
            if (this.scene && this.scene.createExplosion) {
                this.scene.createExplosion(this.x, this.y, 3);
            }

            gameData.lives--;
            if (gameData.lives <= 0) {
                gameData.gameLost = true;
            } else {
                // Respawn
                this.health = this.maxHealth;
                this.x = 150;
                this.y = Graphics.height / 2;
                this.vx = 0;
                this.vy = 0;
                this.invulnerable = true;
                this.invulnerabilityTimer = 180; // 3 seconds invulnerability on respawn
            }
        }

        fireSideTurret() {
            // Find nearest enemy
            if (!gameData.enemies.length) return;
            const ship = this;
            const centerX = 90;
            const centerY = 70;
            // Turret position (left side)
            const turretX = ship.x - centerX + (centerX - 38);
            const turretY = ship.y - centerY + (centerY + 30);
            // Find nearest enemy
            let nearest = null;
            let minDist = Infinity;
            for (const enemy of gameData.enemies) {
                const dx = enemy.x - turretX;
                const dy = enemy.y - turretY;
                const dist = Math.sqrt(dx * dx + dy * dy);
                if (dist < minDist) {
                    minDist = dist;
                    nearest = enemy;
                }
            }
            if (!nearest) return;
            // Predictive aiming
            const speed = 20; // projectile speed
            if (!nearest._lastX) {
                nearest._lastX = nearest.x;
                nearest._lastY = nearest.y;
            }
            const enemyVx = nearest.x - nearest._lastX;
            const enemyVy = nearest.y - nearest._lastY;
            nearest._lastX = nearest.x;
            nearest._lastY = nearest.y;
            const dx = nearest.x - turretX;
            const dy = nearest.y - turretY;
            const a = enemyVx * enemyVx + enemyVy * enemyVy - speed * speed;
            const b = 2 * (dx * enemyVx + dy * enemyVy);
            const c = dx * dx + dy * dy;
            let t = 0;
            if (Math.abs(a) < 0.0001) {
                t = c / -b;
            } else {
                const disc = b * b - 4 * a * c;
                if (disc >= 0) {
                    const t1 = (-b + Math.sqrt(disc)) / (2 * a);
                    const t2 = (-b - Math.sqrt(disc)) / (2 * a);
                    t = Math.min(t1, t2);
                    if (t < 0) t = Math.max(t1, t2);
                }
            }
            if (!t || t < 0.05) t = 0.05;
            const targetX = nearest.x + enemyVx * t;
            const targetY = nearest.y + enemyVy * t;
            const baseAngle = Math.atan2(targetY - turretY, targetX - turretX);

            // Update turret angle to aim at target
            this.updateTurretAngle(baseAngle);

            // --- Powerup logic mirroring main weapon ---
            const spreadLevel = hasPowerup('spread');
            const doubleLevel = hasPowerup('double');
            const waveLevel = hasPowerup('wave');
            const laserLevel = hasPowerup('laser');
            let shots = [];
            if (spreadLevel > 0) {
                if (spreadLevel === 1) {
                    const angles = [-0.2, 0, 0.2];
                    for (const a of angles) shots.push({ angle: baseAngle + a, yOffset: 0 });
                } else {
                    const angles = [-0.3, -0.15, 0, 0.15, 0.3];
                    for (const a of angles) shots.push({ angle: baseAngle + a, yOffset: 0 });
                }
            } else {
                shots.push({ angle: baseAngle, yOffset: 0 });
            }
            if (waveLevel > 0) {
                shots = shots.map(s => ({ ...s, _waveDirection: 1 }));
            }
            if (doubleLevel > 0) {
                if (doubleLevel === 1) {
                    shots = shots.flatMap(s => [
                        { ...s, yOffset: (s.yOffset || 0) - 8 },
                        { ...s, yOffset: (s.yOffset || 0) + 8 },
                    ]);
                } else {
                    shots = shots.flatMap(s => [
                        { ...s, yOffset: (s.yOffset || 0) - 12 },
                        { ...s, yOffset: s.yOffset || 0 },
                        { ...s, yOffset: (s.yOffset || 0) + 12 },
                    ]);
                }
            }
            for (const s of shots) {
                const vx = Math.cos(s.angle) * speed;
                const vy = Math.sin(s.angle) * speed;
                const projectileY = turretY + (s.yOffset || 0);
                let projectileType = 'turret';
                let damage = 30;
                if (laserLevel > 0 && waveLevel > 0) {
                    projectileType = 'wave-laser';
                    damage = 75 + (laserLevel - 1) * 25;
                } else if (laserLevel > 0) {
                    projectileType = 'laser';
                    damage = 100 + (laserLevel - 1) * 50;
                } else if (waveLevel > 0) {
                    projectileType = 'wave';
                    damage = 25 + (waveLevel - 1) * 15;
                }

                // Heat-based damage scaling for turret: 150% at 0 heat, 100% at 50% heat, 60% at 100% heat
                const heatPercent = gameData.weaponHeat / gameData.maxHeat;
                const minMultiplier = 0.6; // 60%
                const maxMultiplier = 1.5; // 150%
                const damageMultiplier =
                    maxMultiplier - (maxMultiplier - minMultiplier) * heatPercent;
                damage = Math.floor(damage * damageMultiplier);

                const proj = Projectile.getOrCreate(
                    turretX,
                    projectileY,
                    vx,
                    vy,
                    projectileType,
                    s.angle
                );
                proj.scene = this.parent;
                proj.damage = damage;
                if (projectileType === 'laser' || projectileType === 'wave-laser') {
                    proj._piercing = true;
                }
                if (projectileType === 'wave' || projectileType === 'wave-laser') {
                    proj._baseType = 'plasma';
                    proj._wavePhase = Math.random() * Math.PI * 2;
                    proj._spreadAngle = s.angle;
                    proj._waveLevel = waveLevel;
                    proj._waveDirection = s._waveDirection || 1;
                    proj._waveAmplitudeRandom = 0.7 + Math.random() * 0.6;
                }
                gameData.projectiles.push(proj);
                if (this.parent && this.parent.addChild) this.parent.addChild(proj);
            }
        }
    }

    // Main scene
    class Scene_SkyPirates extends Scene_Base {
        create() {
            super.create();
            this.createBackground();
            this.createUI();
            this.createShip();
            this.createClouds();

            // Initialize audio system
            audioManager.init();

            this.startGame();
        }

        createBackground() {
            // Sky gradient background
            this._backgroundSprite = new Sprite();
            this._backgroundSprite.bitmap = new Bitmap(Graphics.width, Graphics.height);

            // Day/Night cycle variables
            this._dayNightCycle = 0; // 0 = midnight, 0.25 = sunrise, 0.5 = noon, 0.75 = sunset, 1 = next midnight
            this._cycleSpeed = 0.00005; // Much slower cycle (100x slower than 0.01)
            this._lastBackgroundUpdate = 0;
            this._backgroundUpdateInterval = 5; // Update background every 5 frames for better performance

            // Create stars for night time
            this._stars = [];
            for (let i = 0; i < 100; i++) {
                this._stars.push({
                    x: Math.random() * Graphics.width,
                    y: Math.random() * (Graphics.height * 0.6), // Only in upper 60% of screen
                    size: 0.5 + Math.random() * 1.5,
                    alpha: 0.3 + Math.random() * 0.7,
                    twinkle: Math.random() * Math.PI * 2,
                });
            }

            this.updateBackground();
            this.addChild(this._backgroundSprite);

            // Force an immediate background update for testing
            console.log('Initial background created, cycle:', this._dayNightCycle);
        }

        updateBackground() {
            console.log('updateBackground called, cycle:', this._dayNightCycle);
            const context = this._backgroundSprite.bitmap.context;
            const cycle = this._dayNightCycle;

            // Clear the background
            context.clearRect(0, 0, Graphics.width, Graphics.height);

            // Get sky colors based on time of day
            const skyColors = this.getSkyColors(cycle);

            // Create sky gradient
            const gradient = context.createLinearGradient(0, 0, 0, Graphics.height);
            gradient.addColorStop(0, skyColors.top);
            gradient.addColorStop(1, skyColors.bottom);

            context.fillStyle = gradient;
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            // --- Draw ocean at bottom 35% ---
            // Use the current sky's bottom color for the ocean's top
            const oceanTop = Math.floor(Graphics.height * 0.65);
            const oceanHeight = Graphics.height - oceanTop;
            const skyBottom = skyColors.bottom;

            // Helper to darken a hex color
            function darkenColor(hex, factor) {
                // hex: "#RRGGBB", factor: 0 (no change) to 1 (black)
                const r = Math.round(parseInt(hex.substr(1, 2), 16) * (1 - factor));
                const g = Math.round(parseInt(hex.substr(3, 2), 16) * (1 - factor));
                const b = Math.round(parseInt(hex.substr(5, 2), 16) * (1 - factor));
                return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
            }
            const oceanBottom = darkenColor(skyBottom, 0.5);

            const oceanGradient = context.createLinearGradient(0, oceanTop, 0, Graphics.height);
            oceanGradient.addColorStop(0, skyBottom); // Match sky at horizon
            oceanGradient.addColorStop(1, oceanBottom); // Darker at bottom
            context.fillStyle = oceanGradient;
            context.fillRect(0, oceanTop, Graphics.width, oceanHeight);

            // Draw sun/moon
            this.drawSunOrMoon(context, cycle);

            // Draw stars during night
            if (cycle < 0.18 || cycle > 0.82) {
                this.drawStars(context);
            }

            // --- Reflections on the ocean ---
            // Helper: vertical fade for reflections
            function reflectionAlpha(y) {
                // y: 0 at oceanTop, 1 at bottom
                return Math.max(0, 0.5 * (1 - y)); // 0.5 at horizon, fades to 0
            }

            // Arc parameters for sun/moon
            const arcLeft = Graphics.width * 0.1;
            const arcRight = Graphics.width * 0.9;
            const arcTop = Graphics.height * 0.12;
            const arcBottom = Graphics.height * 0.6;
            function getArcPos(t) {
                const angle = Math.PI * (1 - t);
                const x = arcLeft + (arcRight - arcLeft) * t;
                const y = arcBottom - Math.sin(angle) * (arcBottom - arcTop);
                return { x, y };
            }

            // Sun reflection
            let sunT = (cycle - 0.25) / 0.5;

            // Moon reflection
            let moonT = (((cycle + 0.5) % 1) - 0.25) / 0.5;

            // Star reflections (night only)
            if (cycle < 0.18 || cycle > 0.82) {
                for (const star of this._stars) {
                    const twinkle = Math.sin(star.twinkle + gameData.frameCount * 0.02) * 0.3 + 0.7;
                    const alpha = star.alpha * twinkle * 0.18;
                    const rx = star.x;
                    const ry = oceanTop + (oceanTop - star.y);
                    context.save();
                    context.globalAlpha = alpha;
                    context.beginPath();
                    context.arc(rx, ry, star.size, 0, Math.PI * 2);
                    context.fillStyle = '#fff';
                    context.shadowColor = '#fff';
                    context.shadowBlur = 4;
                    context.fill();
                    context.restore();
                }
            }

            // Cloud reflections (day only) - smooth movement
            if (cycle >= 0.18 && cycle <= 0.82) {
                for (const cloud of gameData.clouds) {
                    if (cloud && cloud.x >= 0 && cloud.x <= Graphics.width) {
                        // Mirror the cloud position across the water line
                        const rx = cloud.x;
                        const ry = oceanTop + (oceanTop - cloud.y);

                        // Only draw reflection if it's in the ocean area
                        if (ry >= oceanTop && ry <= Graphics.height) {
                            // Calculate reflection alpha based on distance from horizon
                            const distanceFromHorizon = (ry - oceanTop) / oceanHeight;
                            const alpha =
                                0.15 *
                                (1 - distanceFromHorizon) *
                                (0.7 + 0.3 * Math.sin(gameData.frameCount * 0.01));

                            context.save();
                            context.globalAlpha = alpha;
                            context.translate(rx, ry);
                            context.scale(1, -0.6); // Flip and compress vertically

                            // Use the cloud's existing bitmap for smooth reflection
                            if (cloud.bitmap && cloud.bitmap._canvas) {
                                context.drawImage(
                                    cloud.bitmap._canvas,
                                    -cloud.bitmap.width / 2,
                                    -cloud.bitmap.height / 2
                                );
                            } else {
                                // Fallback: recreate cloud graphics for reflection
                                const centerX = 0;
                                const centerY = 0;
                                const cloudSize = cloud.size;

                                // Sample sky color at reflection position
                                const t = Math.min(1, Math.max(0, ry / Graphics.height));
                                const skyColor = cloud.blendColor(
                                    skyColors.top,
                                    skyColors.bottom,
                                    t
                                );
                                const cloudColor = cloud.blendColor('#ffffff', skyColor, 0.65);

                                // Cloud gradient for reflection
                                const gradient = context.createRadialGradient(
                                    centerX,
                                    centerY,
                                    0,
                                    centerX,
                                    centerY,
                                    50 * cloudSize
                                );
                                gradient.addColorStop(0, cloudColor + 'e6');
                                gradient.addColorStop(0.7, cloudColor + '99');
                                gradient.addColorStop(1, cloudColor + '00');

                                context.fillStyle = gradient;

                                // Draw multiple circles to create cloud shape
                                const circles = [
                                    { x: centerX, y: centerY, r: 30 * cloudSize },
                                    {
                                        x: centerX - 20 * cloudSize,
                                        y: centerY,
                                        r: 25 * cloudSize,
                                    },
                                    {
                                        x: centerX + 20 * cloudSize,
                                        y: centerY,
                                        r: 25 * cloudSize,
                                    },
                                    {
                                        x: centerX,
                                        y: centerY - 15 * cloudSize,
                                        r: 20 * cloudSize,
                                    },
                                    {
                                        x: centerX,
                                        y: centerY + 15 * cloudSize,
                                        r: 20 * cloudSize,
                                    },
                                ];

                                circles.forEach(circle => {
                                    context.beginPath();
                                    context.arc(circle.x, circle.y, circle.r, 0, Math.PI * 2);
                                    context.fill();
                                });
                            }

                            context.restore();
                        }
                    }
                }
            }

            // --- Glitter path (sparkly reflection) under sun and moon ---
            function drawEnhancedGlitterPath(
                cx,
                topY,
                baseColor,
                gradColor,
                baseAlpha = 0.32,
                count = 48
            ) {
                for (let i = 0; i < count; i++) {
                    const fade = 1 - i / count;
                    const wavePhase = gameData.frameCount / 40 + i * 0.18;
                    // No vertical sine wave, only random jitter
                    const y =
                        oceanTop +
                        i * (oceanHeight / count) +
                        (Math.random() - 0.5) * 2 * (1 - i / count);
                    const waveX = Math.sin(wavePhase + 1.5) * 18 * fade; // 18px amplitude, less at bottom
                    const x = cx + waveX + (Math.random() - 0.5) * 32 * fade;
                    const width = 80 * Math.pow(fade, 1.2) + 8 + Math.random() * 18 * fade; // Wide at top, narrow at bottom
                    const alpha =
                        baseAlpha *
                        fade *
                        (0.8 + Math.random() * 0.5) *
                        (0.7 + 0.3 * Math.sin(wavePhase));

                    // Color gradient for the line
                    const grad = context.createLinearGradient(x - width / 2, y, x + width / 2, y);
                    grad.addColorStop(0, gradColor);
                    grad.addColorStop(0.5, baseColor);
                    grad.addColorStop(1, gradColor);

                    context.save();
                    context.globalAlpha = alpha;
                    context.beginPath();
                    context.moveTo(x - width / 2, y);
                    context.lineTo(x + width / 2, y);
                    context.lineWidth = 2.5 + 2.5 * fade;
                    context.strokeStyle = grad;
                    context.shadowColor = baseColor;
                    context.shadowBlur = 12 * fade;
                    context.stroke();
                    context.restore();
                }
            }

            // Sun glitter path
            if (sunT >= 0 && sunT <= 1) {
                const sunPos = getArcPos(Math.max(0, Math.min(1, sunT)));
                const rx = sunPos.x;
                drawEnhancedGlitterPath(rx, oceanTop, '#fff7b3', '#ffe066', 0.28, 48);
            }

            // Moon glitter path
            if (moonT >= 0 && moonT <= 1) {
                const moonPos = getArcPos(Math.max(0, Math.min(1, moonT)));
                const rx = moonPos.x;
                drawEnhancedGlitterPath(rx, oceanTop, '#e6eaff', '#b3cfff', 0.22, 48);
            }

            // Force bitmap refresh so changes are visible
            if (this._backgroundSprite.bitmap._setDirty) {
                this._backgroundSprite.bitmap._setDirty();
            } else if (
                this._backgroundSprite.bitmap.baseTexture &&
                this._backgroundSprite.bitmap.baseTexture.update
            ) {
                this._backgroundSprite.bitmap.baseTexture.update();
            }
        }

        getSkyColors(cycle) {
            // Define color stops for the day/night cycle
            const colorStops = [
                { t: 0.0, top: '#0a1a3a', bot: '#1a2a4a' }, // Midnight
                { t: 0.18, top: '#2b1a3a', bot: '#4a2a4a' }, // Pre-dawn
                { t: 0.25, top: '#ffbfa3', bot: '#ff7e5f' }, // Sunrise
                { t: 0.32, top: '#ffe6b3', bot: '#b3e3ff' }, // Early morning
                { t: 0.5, top: '#b3e3ff', bot: '#4a90af' }, // Noon
                { t: 0.68, top: '#ffe6b3', bot: '#b3e3ff' }, // Afternoon
                { t: 0.75, top: '#ffbfa3', bot: '#ff7e5f' }, // Sunset
                { t: 0.82, top: '#2b1a3a', bot: '#4a2a4a' }, // Dusk
                { t: 1.0, top: '#0a1a3a', bot: '#1a2a4a' }, // Midnight again
            ];

            // Find the appropriate color stops
            let i = 0;
            while (i < colorStops.length - 1 && cycle > colorStops[i + 1].t) i++;

            const t0 = colorStops[i];
            const t1 = colorStops[(i + 1) % colorStops.length];

            // Calculate interpolation factor
            let localT = (cycle - t0.t) / (t1.t - t0.t);
            if (t1.t < t0.t) localT = (cycle - t0.t) / (1 + t1.t - t0.t);

            // Interpolate colors
            const top = this.lerpColor(t0.top, t1.top, localT);
            const bottom = this.lerpColor(t0.bot, t1.bot, localT);

            return { top, bottom };
        }

        lerpColor(color1, color2, factor) {
            // Convert hex colors to RGB
            const c1 = this.hexToRgb(color1);
            const c2 = this.hexToRgb(color2);

            // Interpolate each component
            const r = Math.round(c1.r + (c2.r - c1.r) * factor);
            const g = Math.round(c1.g + (c2.g - c1.g) * factor);
            const b = Math.round(c1.b + (c2.b - c1.b) * factor);

            // Convert back to hex
            return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`;
        }

        hexToRgb(hex) {
            const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
            return result
                ? {
                      r: parseInt(result[1], 16),
                      g: parseInt(result[2], 16),
                      b: parseInt(result[3], 16),
                  }
                : { r: 0, g: 0, b: 0 };
        }

        drawSunOrMoon(context, cycle) {
            // Sun: 0.25 (sunrise) to 0.75 (sunset)
            // Moon: 0.75 (moonrise) to next 0.25 (moonset)
            const sunCycle = cycle;
            const moonCycle = (cycle + 0.5) % 1; // Moon is always opposite the sun

            // Arc parameters
            const arcLeft = Graphics.width * 0.1;
            const arcRight = Graphics.width * 0.9;
            const arcTop = Graphics.height * 0.12;
            const arcBottom = Graphics.height * 0.6;

            // Helper to get position along arc
            function getArcPos(t) {
                // t: 0 (rise, left) to 1 (set, right)
                const angle = Math.PI * (1 - t); // Pi (left) to 0 (right)
                const x = arcLeft + (arcRight - arcLeft) * t;
                const y = arcBottom - Math.sin(angle) * (arcBottom - arcTop);
                return { x, y };
            }

            // Sun position and alpha
            let sunT = (sunCycle - 0.25) / 0.5; // 0 at sunrise, 1 at sunset
            let sunAlpha = 0;
            if (sunT >= 0 && sunT <= 1) sunAlpha = 1;
            sunT = Math.max(0, Math.min(1, sunT));
            const sunPos = getArcPos(sunT);

            // Moon position and alpha
            let moonT = (moonCycle - 0.25) / 0.5;
            let moonAlpha = 0;
            if (moonT >= 0 && moonT <= 1) moonAlpha = 1;
            moonT = Math.max(0, Math.min(1, moonT));
            const moonPos = getArcPos(moonT);

            // Fade in/out at horizon (optional, for realism)
            function horizonFade(t) {
                return Math.max(0, Math.min(1, (t - 0.05) / 0.15, (1.05 - t) / 0.15));
            }

            // Draw sun
            if (sunAlpha > 0) {
                context.save();
                context.globalAlpha = sunAlpha * horizonFade(sunT);
                context.beginPath();
                context.arc(sunPos.x, sunPos.y, 40, 0, Math.PI * 2);
                context.fillStyle = '#fff7b3';
                context.shadowColor = '#fff7b3';
                context.shadowBlur = 32;
                context.fill();
                context.globalAlpha = 1;
                context.shadowBlur = 0;
                context.restore();
            }

            // Draw moon
            if (moonAlpha > 0) {
                context.save();
                context.globalAlpha = moonAlpha * horizonFade(moonT);
                context.beginPath();
                context.arc(moonPos.x, moonPos.y, 38, 0, Math.PI * 2);
                context.fillStyle = '#e6eaff';
                context.shadowColor = '#e6eaff';
                context.shadowBlur = 18;
                context.fill();
                // Crescent shadow
                context.globalAlpha *= 0.5;
                context.beginPath();
                context.arc(moonPos.x + 10, moonPos.y - 5, 32, 0, Math.PI * 2);
                context.fillStyle = '#b3b7c7';
                context.fill();
                context.globalAlpha = 1;
                context.shadowBlur = 0;
                context.restore();
            }
        }

        drawStars(context) {
            context.save();
            context.globalAlpha = 0.7;

            for (const star of this._stars) {
                // Add twinkling effect
                const twinkle = Math.sin(star.twinkle + gameData.frameCount * 0.02) * 0.3 + 0.7;
                const alpha = star.alpha * twinkle;

                context.beginPath();
                context.arc(star.x, star.y, star.size, 0, Math.PI * 2);
                context.fillStyle = `rgba(255, 255, 255, ${alpha})`;
                context.shadowColor = '#fff';
                context.shadowBlur = 8;
                context.fill();
                context.shadowBlur = 0;
            }

            context.restore();
        }

        lerp(a, b, factor) {
            return a + (b - a) * factor;
        }

        createUI() {
            // Score and lives window (increased height for all content)
            this._uiWindow = new Window_Base(new Rectangle(10, 10, 300, 450));
            this._uiWindow.opacity = 0; // Fully transparent
            this.addChild(this._uiWindow);

            // Instructions window (increased height to fit all lines)
            this._instructionsWindow = new Window_Base(
                new Rectangle(Graphics.width - 250, 10, 240, 180)
            );
            this._instructionsWindow.opacity = 0; // Fully transparent
            this.addChild(this._instructionsWindow);

            // Boss HP bar (top center, hidden by default)
            this._bossHpBar = new Sprite();
            this._bossHpBar.bitmap = new Bitmap(420, 48);
            this._bossHpBar.x = (Graphics.width - 420) / 2;
            this._bossHpBar.y = 10;
            this._bossHpBar.visible = false;
            this.addChild(this._bossHpBar);

            this.updateUI();
        }

        createShip() {
            gameData.ship = new Airship(150, Graphics.height / 2);
            this.addChild(gameData.ship);
        }

        createClouds() {
            // Create initial clouds (behind everything) - only above 50% line
            const spawnLine = Graphics.height * 0.5;
            for (let i = 0; i < 10; i++) {
                const x = Math.random() * Graphics.width;
                const y = Math.random() * spawnLine; // Only spawn above 50% line
                const size = 0.5 + Math.random() * 1.5;
                const skyColors = this.getSkyColors(this._dayNightCycle);
                const cloud = new Cloud(x, y, size, skyColors);
                gameData.clouds.push(cloud);
                this.addChild(cloud);
                // Add reflection sprite to scene
                if (cloud.reflection) {
                    this.addChild(cloud.reflection);
                }
                // Move cloud to back of display list
                if (this.children.length > 1) {
                    this.removeChild(cloud);
                    this.addChildAt(cloud, 1); // Insert after background (index 0) but before everything else
                }
            }
        }

        startGame() {
            gameData.score = 0;
            gameData.lives = 3;
            gameData.level = 1;
            gameData.enemies = [];
            gameData.projectiles = [];
            gameData.powerUps = [];
            gameData.gameWon = false;
            gameData.gameLost = false;
            gameData.comboMultiplier = 1;
            gameData.lastKillTime = 0;
            gameData.activeFirePowerups = [];
            gameData.megaMissileCooldown = 0;

            // Remove waveEnemies and waveSpawnIndex from gameData initialization
            // gameData.waveEnemies = [];
            // gameData.waveSpawnIndex = 0;
        }

        // --- PowerUp Spawning: randomize type ---
        spawnPowerUp() {
            const types = ['spread', 'double', 'wave', 'laser', 'lightning', 'fire', 'ice'];
            const type = types[Math.floor(Math.random() * types.length)];
            const x = Graphics.width + 32;
            const y = 60 + Math.random() * (Graphics.height - 120);
            const powerUp = new PowerUp(type, x, y);
            gameData.powerUps.push(powerUp);
            this.addChild(powerUp);
        }

        spawnEnemyFormation(formationType, enemyTypes) {
            const baseX = Graphics.width + 100;

            // Helper function to get Y position based on enemy type
            const getYPosition = enemyType => {
                // Weaving enemies (fighter, carrier) spawn closer to center for more movement room
                if (enemyType === 'fighter' || enemyType === 'carrier') {
                    const centerY = Graphics.height / 2;
                    const range = 120; // Smaller range around center
                    return centerY + (Math.random() - 0.5) * range;
                } else {
                    // Other enemies use the original spawn range
                    return 80 + Math.random() * (Graphics.height - 160);
                }
            };

            const baseY = getYPosition(enemyTypes[0]); // Use first enemy type for base position

            switch (formationType) {
                case 'single':
                    // Single enemy spawn
                    const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                    const y = getYPosition(type);
                    const enemy = new EnemyAirship(baseX, y, type);
                    enemy.scene = this;
                    gameData.enemies.push(enemy);
                    this.addChild(enemy);
                    break;

                case 'line':
                    // Horizontal line formation
                    const lineCount = 3 + Math.floor(Math.random() * 3); // 3-5 enemies
                    const lineSpacing = 80;
                    const lineStartX = baseX + ((lineCount - 1) * lineSpacing) / 2;

                    for (let i = 0; i < lineCount; i++) {
                        const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                        const y = getYPosition(type);
                        const enemy = new EnemyAirship(lineStartX - i * lineSpacing, y, type);
                        enemy.scene = this;
                        gameData.enemies.push(enemy);
                        this.addChild(enemy);
                    }
                    break;

                case 'cluster':
                    // Tight cluster formation
                    const clusterCount = 4 + Math.floor(Math.random() * 3); // 4-6 enemies
                    const clusterRadius = 60;

                    for (let i = 0; i < clusterCount; i++) {
                        const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                        const y = getYPosition(type);
                        const angle = (Math.PI * 2 * i) / clusterCount;
                        const offsetX =
                            Math.cos(angle) * clusterRadius * (0.5 + Math.random() * 0.5);
                        const offsetY =
                            Math.sin(angle) * clusterRadius * (0.5 + Math.random() * 0.5);
                        const enemy = new EnemyAirship(baseX + offsetX, y + offsetY, type);
                        enemy.scene = this;
                        gameData.enemies.push(enemy);
                        this.addChild(enemy);
                    }
                    break;

                case 'v_formation':
                    // V-formation (like geese)
                    const vCount = 5;
                    const vSpacing = 60;
                    const vAngle = Math.PI / 6; // 30 degrees

                    // Leader
                    const leaderType = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                    const leaderY = getYPosition(leaderType);
                    const leader = new EnemyAirship(baseX, leaderY, leaderType);
                    leader.scene = this;
                    gameData.enemies.push(leader);
                    this.addChild(leader);

                    // Left wing
                    for (let i = 1; i <= 2; i++) {
                        const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                        const y = getYPosition(type);
                        const offsetX = -i * vSpacing * Math.cos(vAngle);
                        const offsetY = -i * vSpacing * Math.sin(vAngle);
                        const enemy = new EnemyAirship(baseX + offsetX, y + offsetY, type);
                        enemy.scene = this;
                        gameData.enemies.push(enemy);
                        this.addChild(enemy);
                    }

                    // Right wing
                    for (let i = 1; i <= 2; i++) {
                        const type = enemyTypes[Math.floor(Math.random() * enemyTypes.length)];
                        const y = getYPosition(type);
                        const offsetX = -i * vSpacing * Math.cos(vAngle);
                        const offsetY = i * vSpacing * Math.sin(vAngle);
                        const enemy = new EnemyAirship(baseX + offsetX, y + offsetY, type);
                        enemy.scene = this;
                        gameData.enemies.push(enemy);
                        this.addChild(enemy);
                    }
                    break;
            }
        }

        update() {
            super.update();

            // Increment frame counter for performance tracking
            gameData.frameCount++;

            // Update day/night cycle
            this._dayNightCycle += this._cycleSpeed;
            if (this._dayNightCycle >= 1) {
                this._dayNightCycle = 0; // Reset cycle
            }

            // Debug: Log cycle progress every 100 frames
            if (gameData.frameCount % 100 === 0) {
                console.log(`Day/Night Cycle: ${(this._dayNightCycle * 100).toFixed(1)}%`);
            }

            // Update background every few frames for performance
            if (
                gameData.frameCount - this._lastBackgroundUpdate >=
                this._backgroundUpdateInterval
            ) {
                this.updateBackground();
                this._lastBackgroundUpdate = gameData.frameCount;
                // Debug: Log background updates
                console.log(
                    `Background updated at frame ${gameData.frameCount}, cycle: ${(this._dayNightCycle * 100).toFixed(1)}%`
                );
            }

            // Performance monitoring
            this.updatePerformanceStats();

            if (Input.isTriggered('cancel')) {
                SceneManager.pop();
                return;
            }

            // --- 1. Game Over Handling ---
            if (gameData.gameWon || gameData.gameLost) {
                if (!this._gameOverSprite && gameData.gameLost) {
                    this.clearGameObjects(); // Clear all game objects on GAME OVER
                    this._gameOverSprite = new Sprite();
                    this._gameOverSprite.bitmap = new Bitmap(600, 120);
                    const ctx = this._gameOverSprite.bitmap.context;
                    ctx.fillStyle = 'rgba(0,0,0,0.8)';
                    ctx.fillRect(0, 0, 600, 120);
                    ctx.font = 'bold 48px Arial';
                    ctx.fillStyle = '#FFD700';
                    ctx.textAlign = 'center';
                    ctx.fillText('GAME OVER', 300, 70);
                    ctx.font = '24px Arial';
                    ctx.fillStyle = '#FFF';
                    ctx.fillText('Press Z/Enter to Restart', 300, 110);
                    this._gameOverSprite.x = Graphics.width / 2 - 300;
                    this._gameOverSprite.y = Graphics.height / 2 - 60;
                    this.addChild(this._gameOverSprite);
                    if (gameData.ship) gameData.ship.visible = false;
                }
                if (Input.isTriggered('ok')) {
                    if (this._gameOverSprite) {
                        this.removeChild(this._gameOverSprite);
                        this._gameOverSprite = null;
                    }
                    this.restartGame();
                    return;
                }
                return;
            }

            // Update game objects with optimized loops
            if (gameData.ship) gameData.ship.update();

            // Update enemies with early exit for destroyed ones
            for (let i = gameData.enemies.length - 1; i >= 0; i--) {
                const enemy = gameData.enemies[i];
                if (enemy.destroyed) {
                    gameData.enemies.splice(i, 1);
                    gameData.performanceStats.objectsDestroyed++;
                } else {
                    enemy.update();
                }
            }

            // Update projectiles with early exit and pooling
            for (let i = gameData.projectiles.length - 1; i >= 0; i--) {
                const projectile = gameData.projectiles[i];
                if (!projectile.parent || projectile.destroyed) {
                    gameData.projectiles.splice(i, 1);
                    gameData.performanceStats.objectsDestroyed++;
                } else {
                    projectile.update();
                }
            }

            // Update clouds
            for (const cloud of gameData.clouds) {
                cloud.update();
            }

            // Update powerups
            for (const powerUp of gameData.powerUps) {
                powerUp.update();
            }

            // Limit total projectiles for performance (especially with fast laser fire)
            const maxProjectiles = 150; // Reasonable limit
            if (gameData.projectiles.length > maxProjectiles) {
                // Remove oldest projectiles first
                const excess = gameData.projectiles.length - maxProjectiles;
                for (let i = 0; i < excess; i++) {
                    const oldProj = gameData.projectiles[i];
                    if (oldProj && oldProj.parent) {
                        oldProj.destroy();
                    }
                }
            }

            // Update mega missile cooldown
            if (gameData.megaMissileCooldown > 0) {
                gameData.megaMissileCooldown--;
            }

            // --- Enhanced Weapon System ---
            // Weapon switching
            if (Input.isTriggered('v') || Input.isTriggered('pagedown')) {
                gameData.currentWeaponIndex =
                    (gameData.currentWeaponIndex + 1) % gameData.availableWeapons.length;
                gameData.primaryWeapon = gameData.availableWeapons[gameData.currentWeaponIndex];
            }
            // Previous weapon (LB/B key)
            if (Input.isTriggered('b') || Input.isTriggered('pageup')) {
                gameData.currentWeaponIndex =
                    (gameData.currentWeaponIndex - 1 + gameData.availableWeapons.length) %
                    gameData.availableWeapons.length;
                gameData.primaryWeapon = gameData.availableWeapons[gameData.currentWeaponIndex];
            }

            // --- 1. Continuous Fire for Primary Weapon ---
            if (Input.isPressed('ok') && gameData.ship) {
                if (!this._primaryFireCooldown || this._primaryFireCooldown <= 0) {
                    this.firePrimaryWeapon();
                    // Different cooldowns based on weapon type
                    const weaponType = gameData.primaryWeapon;
                    const laserLevel = hasPowerup('laser');
                    let cooldown = 5;

                    switch (weaponType) {
                        case 'gatling':
                            cooldown = 5; // Balanced fire rate
                            break;
                        case 'plasma':
                            cooldown = 12; // Slightly faster
                            break;
                        case 'rocket':
                            cooldown = 20; // More usable
                            break;
                        default:
                            cooldown = laserLevel > 0 ? 3 : 6; // Laser buffed
                    }

                    // Overcharge effect: double fire rate
                    if (gameData.overchargeActive) {
                        cooldown = Math.max(1, Math.floor(cooldown / 2));
                    }

                    this._primaryFireCooldown = cooldown;
                }
            }
            if (this._primaryFireCooldown > 0) this._primaryFireCooldown--;

            // Toggle debug mode with 'D' key
            if (Input.isTriggered('d')) {
                gameData.debugMode = !gameData.debugMode;
                if (!gameData.debugMode && this._debugOverlay) {
                    this.removeChild(this._debugOverlay);
                    this._debugOverlay = null;
                }
            }

            // Spawn new clouds (behind everything) - only above 50% line
            if (Math.random() < 0.02) {
                const x = Graphics.width + 50;
                const spawnLine = Graphics.height * 0.5;
                const y = Math.random() * spawnLine; // Only spawn above 50% line
                const size = 0.5 + Math.random() * 1.5;
                const skyColors = this.getSkyColors(this._dayNightCycle);
                const cloud = new Cloud(x, y, size, skyColors);
                gameData.clouds.push(cloud);
                this.addChild(cloud);
                // Add reflection sprite to scene
                if (cloud.reflection) {
                    this.addChild(cloud.reflection);
                }
                // Move cloud to back of display list
                if (this.children.length > 1) {
                    this.removeChild(cloud);
                    this.addChildAt(cloud, 1); // Insert after background (index 0) but before everything else
                }
            }

            // Check collisions
            this.checkCollisions();

            // Smart enemy spawning with formations
            gameData.spawnTimer++;
            if (
                gameData.spawnTimer >= gameData.spawnInterval &&
                gameData.enemies.length < gameData.maxEnemiesOnScreen &&
                gameData.enemiesKilledThisLevel < gameData.enemiesRequiredForNextLevel
            ) {
                // Determine enemy type distribution based on level - one new type per level
                let enemyTypes = [];
                // Enemy type progression - one new type every 2 levels
                if (gameData.level <= 2) {
                    enemyTypes = ['drone'];
                } else if (gameData.level <= 4) {
                    enemyTypes = ['drone', 'scout'];
                } else if (gameData.level <= 6) {
                    enemyTypes = ['drone', 'scout', 'swarm'];
                } else if (gameData.level <= 8) {
                    enemyTypes = ['drone', 'scout', 'swarm', 'fighter'];
                } else if (gameData.level <= 10) {
                    enemyTypes = ['drone', 'scout', 'swarm', 'fighter', 'interceptor'];
                } else if (gameData.level <= 12) {
                    enemyTypes = ['drone', 'scout', 'swarm', 'fighter', 'interceptor', 'bomber'];
                } else if (gameData.level <= 14) {
                    enemyTypes = [
                        'drone',
                        'scout',
                        'swarm',
                        'fighter',
                        'interceptor',
                        'bomber',
                        'sniper',
                    ];
                } else if (gameData.level <= 16) {
                    enemyTypes = [
                        'drone',
                        'scout',
                        'swarm',
                        'fighter',
                        'interceptor',
                        'bomber',
                        'sniper',
                        'carrier',
                    ];
                } else if (gameData.level <= 18) {
                    enemyTypes = [
                        'drone',
                        'scout',
                        'swarm',
                        'fighter',
                        'interceptor',
                        'bomber',
                        'sniper',
                        'carrier',
                        'tank',
                    ];
                } else {
                    // Level 19+ includes all enemy types
                    enemyTypes = [
                        'drone',
                        'scout',
                        'swarm',
                        'fighter',
                        'interceptor',
                        'bomber',
                        'sniper',
                        'carrier',
                        'tank',
                        'stealth',
                    ];
                }

                // Choose formation type based on level and random chance
                const formationTypes = ['single', 'line', 'cluster', 'v_formation'];
                const formationWeights = [0.4, 0.25, 0.25, 0.1]; // 40% single, 25% line, 25% cluster, 10% V-formation

                let totalWeight = 0;
                const random = Math.random();
                let chosenFormation = 'single';

                for (let i = 0; i < formationTypes.length; i++) {
                    totalWeight += formationWeights[i];
                    if (random <= totalWeight) {
                        chosenFormation = formationTypes[i];
                        break;
                    }
                }

                this.spawnEnemyFormation(chosenFormation, enemyTypes);
                gameData.spawnTimer = 0;
                // Speed up spawning as level progresses
                gameData.spawnInterval = Math.max(30, 60 - gameData.level * 2);
            }

            if (!this._powerUpSpawnTimer) this._powerUpSpawnTimer = 600;
            this._powerUpSpawnTimer--;
            if (this._powerUpSpawnTimer <= 0) {
                this.spawnPowerUp();
                this._powerUpSpawnTimer = 900 + Math.random() * 600; // 15-25 seconds
            }

            // Check level completion
            if (gameData.enemiesKilledThisLevel >= gameData.enemiesRequiredForNextLevel) {
                // Check if this is a boss level (every 5 levels)
                if (gameData.level % 5 === 0 && !gameData.bossActive) {
                    this.spawnBoss();
                } else {
                    this.nextLevel();
                }
            }

            // Check boss defeat
            if (gameData.bossActive && gameData.bossHealth <= 0) {
                gameData.bossActive = false;
                this.nextLevel();
            }

            // Update UI
            this.updateUI();

            // Debug rendering
            if (gameData.debugMode) {
                this.renderDebugInfo();
            }

            // Screen shake removed - always keep camera stable
            this.x = 0;
            this.y = 0;

            // Update powerup timers
            for (let i = gameData.activeFirePowerups.length - 1; i >= 0; i--) {
                gameData.activeFirePowerups[i].timer--;
                if (gameData.activeFirePowerups[i].timer <= 0) {
                    gameData.activeFirePowerups.splice(i, 1);
                }
            }

            // Update elemental powerup timers
            for (let i = gameData.elementalPowerups.length - 1; i >= 0; i--) {
                gameData.elementalPowerups[i].timer--;
                if (gameData.elementalPowerups[i].timer <= 0) {
                    gameData.elementalPowerups.splice(i, 1);
                }
            }
            // Mega missile cooldown timer
            if (gameData.megaMissileCooldown > 0) gameData.megaMissileCooldown--;
            // Mega missile input (X key, hold or press)
            if (
                (Input.isPressed('x') || Input.isPressed('shift')) &&
                gameData.ship &&
                gameData.megaMissileCooldown === 0
            ) {
                this.fireMegaMissileSalvo();
                gameData.megaMissileCooldown = gameData.megaMissileCooldownMax;
            }
        }

        firePrimaryWeapon() {
            if (!gameData.ship) return;

            const weaponType = gameData.primaryWeapon;
            const ship = gameData.ship;
            const centerX = 90;
            const centerY = 70;
            const cannonBaseX = ship.x - centerX + (centerX + 54);
            const cannonBaseY = ship.y - centerY + (centerY + 36);
            const cannonAngle = -Math.PI / 16;
            const tipOffsetX = Math.cos(cannonAngle) * 14 - Math.sin(cannonAngle) * -3;
            const tipOffsetY = Math.sin(cannonAngle) * 14 + Math.cos(cannonAngle) * -3;
            const x = cannonBaseX + tipOffsetX;
            const y = cannonBaseY + tipOffsetY;

            const spreadLevel = hasPowerup('spread');
            const doubleLevel = hasPowerup('double');
            const waveLevel = hasPowerup('wave');
            const laserLevel = hasPowerup('laser');

            // Different weapon behaviors
            switch (weaponType) {
                case 'plasma':
                    this.firePlasmaWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel);
                    break;
                case 'gatling':
                    this.fireGatlingWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel);
                    break;
                case 'rocket':
                    this.fireRocketWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel);
                    break;
                default:
                    this.fireLaserWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel);
            }

            // Heat generation varies by weapon (balanced buildup)
            const heatMap = {
                plasma: 6,
                gatling: 3,
                rocket: 8,
                laser: 4,
            };
            gameData.weaponHeat += heatMap[weaponType] || 5;
            // Cap heat at maximum
            if (gameData.weaponHeat > gameData.maxHeat) {
                gameData.weaponHeat = gameData.maxHeat;
            }
        }

        firePlasmaWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel) {
            // Plasma: slow, high single-target. With powerup: applies burn (DoT) and laser visuals.
            const shots = this.buildShotPattern(spreadLevel, doubleLevel, waveLevel, laserLevel);
            for (const s of shots) {
                const vx = Math.cos(s.angle) * 8;
                const vy = Math.sin(s.angle) * 8;
                const projectileY = y + (s.yOffset || 0);
                let projType;
                if (waveLevel > 0 && laserLevel > 0) {
                    projType = 'wave-laser';
                } else if (waveLevel > 0) {
                    projType = 'wave';
                } else if (laserLevel > 0) {
                    projType = 'laser-plasma';
                } else {
                    projType = 'plasma';
                }
                const proj = Projectile.getOrCreate(x, projectileY, vx, vy, projType, s.angle);
                proj.scene = this;
                proj.damage = 80;
                // Heat-based damage scaling: 150% at 0 heat, 100% at 50% heat, 60% at 100% heat
                const heatPercent = gameData.weaponHeat / gameData.maxHeat;
                const minMultiplier = 0.6; // 60%
                const maxMultiplier = 1.5; // 150%
                const damageMultiplier =
                    maxMultiplier - (maxMultiplier - minMultiplier) * heatPercent;
                proj.damage = Math.floor(proj.damage * damageMultiplier);
                // Overcharge effect: double damage
                if (gameData.overchargeActive) {
                    proj.damage *= 2;
                }

                // Elemental powerup effects
                let elementalType = null;
                let elementalPowerup = null;
                if (hasElementalPowerup('lightning')) {
                    elementalType = 'lightning';
                    elementalPowerup = getElementalPowerup('lightning');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.lightning.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('fire')) {
                    elementalType = 'fire';
                    elementalPowerup = getElementalPowerup('fire');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.fire.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('ice')) {
                    elementalType = 'ice';
                    elementalPowerup = getElementalPowerup('ice');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.ice.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                }

                proj._elementalType = elementalType;
                proj._elementalLevel = elementalPowerup ? elementalPowerup.level : 1;
                proj.scale.x = 1.5;
                proj.scale.y = 1.5;
                if (laserLevel > 0) {
                    proj._burn = true; // Laser powerup: applies burn
                    proj._piercing = true; // Laser powerup: always pierce
                }
                if (projType === 'wave' || projType === 'wave-laser') {
                    proj._baseType = 'plasma';
                    proj._wavePhase = Math.random() * Math.PI * 2;
                    proj._spreadAngle = s.angle;
                    proj._waveLevel = waveLevel;
                    proj._waveDirection = s._waveDirection || 1;
                    proj._waveAmplitudeRandom = 0.7 + Math.random() * 0.6;
                }
                gameData.projectiles.push(proj);
                this.addChild(proj);
            }

            // Play plasma sound
            audioManager.play('plasma');
        }

        fireGatlingWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel) {
            // Gatling: rapid, low dmg, 10% crit. With powerup: ricochet to 1 other enemy and laser visuals.
            const shots = this.buildShotPattern(spreadLevel, doubleLevel, waveLevel, laserLevel);
            for (const s of shots) {
                const vx = Math.cos(s.angle) * 16;
                const vy = Math.sin(s.angle) * 16;
                const projectileY = y + (s.yOffset || 0);
                let projType;
                if (waveLevel > 0 && laserLevel > 0) {
                    projType = 'wave-laser';
                } else if (waveLevel > 0) {
                    projType = 'wave';
                } else if (laserLevel > 0) {
                    projType = 'laser-gatling';
                } else {
                    projType = 'gatling';
                }
                const proj = Projectile.getOrCreate(x, projectileY, vx, vy, projType, s.angle);
                proj.scene = this;
                proj.damage = 15;
                // Heat-based damage reduction with minimum floor
                const heatPercent = gameData.weaponHeat / gameData.maxHeat;
                const minMultiplier = 0.1; // 10% minimum (1 damage for 15 base damage)
                const maxMultiplier = 1.2; // 120% maximum
                const damageMultiplier =
                    maxMultiplier - (maxMultiplier - minMultiplier) * heatPercent;
                proj.damage = Math.floor(proj.damage * damageMultiplier);
                // Overcharge effect: double damage
                if (gameData.overchargeActive) {
                    proj.damage *= 2;
                }

                // Elemental powerup effects
                let elementalType = null;
                let elementalPowerup = null;
                if (hasElementalPowerup('lightning')) {
                    elementalType = 'lightning';
                    elementalPowerup = getElementalPowerup('lightning');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.lightning.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('fire')) {
                    elementalType = 'fire';
                    elementalPowerup = getElementalPowerup('fire');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.fire.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('ice')) {
                    elementalType = 'ice';
                    elementalPowerup = getElementalPowerup('ice');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.ice.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                }

                proj._elementalType = elementalType;
                proj._elementalLevel = elementalPowerup ? elementalPowerup.level : 1;
                proj.scale.x = 0.8;
                proj.scale.y = 0.8;
                proj._crit = Math.random() < 0.08; // Reduced from 10% to 8%
                if (proj._crit) proj.damage *= 2.5; // Reduced from 3x to 2.5x
                if (laserLevel > 0) {
                    proj._ricochet = true; // Laser powerup: ricochet
                    proj._piercing = true; // Laser powerup: always pierce
                }
                if (projType === 'wave' || projType === 'wave-laser') {
                    proj._baseType = 'gatling';
                    proj._wavePhase = Math.random() * Math.PI * 2;
                    proj._spreadAngle = s.angle;
                    proj._waveLevel = waveLevel;
                    proj._waveDirection = s._waveDirection || 1;
                    proj._waveAmplitudeRandom = 0.7 + Math.random() * 0.6;
                }
                gameData.projectiles.push(proj);
                this.addChild(proj);
            }

            // Play gatling sound
            audioManager.play('gatling');
        }

        fireRocketWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel) {
            // Rocket: high dmg, small AoE. With powerup: large AoE and laser visuals.
            const shots = this.buildShotPattern(spreadLevel, doubleLevel, waveLevel, laserLevel);
            for (const s of shots) {
                const vx = Math.cos(s.angle) * 10;
                const vy = Math.sin(s.angle) * 10;
                const projectileY = y + (s.yOffset || 0);
                let projType;
                if (waveLevel > 0 && laserLevel > 0) {
                    projType = 'wave-laser';
                } else if (waveLevel > 0) {
                    projType = 'wave';
                } else if (laserLevel > 0) {
                    projType = 'laser-rocket';
                } else {
                    projType = 'rocket';
                }
                const proj = Projectile.getOrCreate(x, projectileY, vx, vy, projType, s.angle);
                proj.scene = this;
                proj.damage = 120;
                // Heat-based damage reduction with minimum floor
                const heatPercent = gameData.weaponHeat / gameData.maxHeat;
                const minMultiplier = 0.25; // 25% minimum (30 damage for 120 base damage)
                const maxMultiplier = 1.5; // 150% maximum
                const damageMultiplier =
                    maxMultiplier - (maxMultiplier - minMultiplier) * heatPercent;
                proj.damage = Math.floor(proj.damage * damageMultiplier);
                // Overcharge effect: double damage
                if (gameData.overchargeActive) {
                    proj.damage *= 2;
                }

                // Elemental powerup effects
                let elementalType = null;
                let elementalPowerup = null;
                if (hasElementalPowerup('lightning')) {
                    elementalType = 'lightning';
                    elementalPowerup = getElementalPowerup('lightning');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.lightning.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('fire')) {
                    elementalType = 'fire';
                    elementalPowerup = getElementalPowerup('fire');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.fire.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('ice')) {
                    elementalType = 'ice';
                    elementalPowerup = getElementalPowerup('ice');
                    proj.damage = Math.floor(
                        proj.damage *
                            Math.pow(
                                gameData.elementalEffects.ice.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                }

                proj._elementalType = elementalType;
                proj._elementalLevel = elementalPowerup ? elementalPowerup.level : 1;
                proj.scale.x = 1.0;
                proj.scale.y = 1.0;
                proj._bigExplosion = laserLevel > 0; // Laser powerup: large AoE
                if (projType === 'wave' || projType === 'wave-laser') {
                    proj._baseType = 'rocket';
                    proj._wavePhase = Math.random() * Math.PI * 2;
                    proj._spreadAngle = s.angle;
                    proj._waveLevel = waveLevel;
                    proj._waveDirection = s._waveDirection || 1;
                    proj._waveAmplitudeRandom = 0.7 + Math.random() * 0.6;
                }
                gameData.projectiles.push(proj);
                this.addChild(proj);
            }

            // Play rocket sound
            audioManager.play('rocket');
        }

        fireLaserWeapon(x, y, spreadLevel, doubleLevel, waveLevel, laserLevel) {
            // Laser: single-target, fast, moderate damage. With powerup: piercing, 20% chain.
            const shots = this.buildShotPattern(spreadLevel, doubleLevel, waveLevel, laserLevel);
            for (const s of shots) {
                const vx = Math.cos(s.angle) * (laserLevel > 0 ? 24 : 12);
                const vy = Math.sin(s.angle) * (laserLevel > 0 ? 24 : 12);
                const projectileY = y + (s.yOffset || 0);
                let projectileType = 'player';
                let damage = 40; // Buffed from 25
                if (laserLevel > 0 && waveLevel > 0) {
                    projectileType = 'wave-laser';
                    damage = 90 + (laserLevel - 1) * 30; // Buffed scaling
                } else if (laserLevel > 0) {
                    projectileType = 'laser';
                    damage = 120 + (laserLevel - 1) * 60; // Buffed scaling
                } else if (waveLevel > 0) {
                    projectileType = 'wave';
                    damage = 40 + (waveLevel - 1) * 20; // Buffed scaling
                }

                // Heat-based damage scaling: 150% at 0 heat, 100% at 50% heat, 60% at 100% heat
                const heatPercent = gameData.weaponHeat / gameData.maxHeat;
                const minMultiplier = 0.6; // 60%
                const maxMultiplier = 1.5; // 150%
                const damageMultiplier =
                    maxMultiplier - (maxMultiplier - minMultiplier) * heatPercent;
                damage = Math.floor(damage * damageMultiplier);

                // Overcharge effect: double damage
                if (gameData.overchargeActive) {
                    damage *= 2;
                }

                // Elemental powerup effects
                let elementalType = null;
                let elementalPowerup = null;
                if (hasElementalPowerup('lightning')) {
                    elementalType = 'lightning';
                    elementalPowerup = getElementalPowerup('lightning');
                    damage = Math.floor(
                        damage *
                            Math.pow(
                                gameData.elementalEffects.lightning.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('fire')) {
                    elementalType = 'fire';
                    elementalPowerup = getElementalPowerup('fire');
                    damage = Math.floor(
                        damage *
                            Math.pow(
                                gameData.elementalEffects.fire.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                } else if (hasElementalPowerup('ice')) {
                    elementalType = 'ice';
                    elementalPowerup = getElementalPowerup('ice');
                    damage = Math.floor(
                        damage *
                            Math.pow(
                                gameData.elementalEffects.ice.damageMultiplier,
                                elementalPowerup.level || 1
                            )
                    );
                }

                const proj = Projectile.getOrCreate(
                    x,
                    projectileY,
                    vx,
                    vy,
                    projectileType,
                    s.angle
                );
                proj.scene = this;
                proj.damage = damage;
                proj._elementalType = elementalType;
                proj._elementalLevel = elementalPowerup ? elementalPowerup.level : 1;

                if (projectileType === 'laser' || projectileType === 'wave-laser') {
                    proj._piercing = true;
                    proj._chainChance = 0.2; // 20% chance to chain
                } else {
                    proj._piercing = false;
                    proj._chainChance = 0;
                }
                if (projectileType === 'wave' || projectileType === 'wave-laser') {
                    proj._baseType = 'laser';
                    proj._wavePhase = Math.random() * Math.PI * 2;
                    proj._spreadAngle = s.angle;
                    proj._waveLevel = waveLevel;
                    proj._waveDirection = s._waveDirection || 1;
                    proj._waveAmplitudeRandom = 0.7 + Math.random() * 0.6;
                }
                gameData.projectiles.push(proj);
                this.addChild(proj);
            }

            // Play laser sound
            audioManager.play('laser');
        }

        buildShotPattern(spreadLevel, doubleLevel, waveLevel, laserLevel) {
            let shots = [];

            if (spreadLevel > 0) {
                if (spreadLevel === 1) {
                    const angles = [-0.2, 0, 0.2];
                    for (const a of angles) shots.push({ angle: a, yOffset: 0 });
                } else {
                    const angles = [-0.3, -0.15, 0, 0.15, 0.3];
                    for (const a of angles) shots.push({ angle: a, yOffset: 0 });
                }
            } else {
                shots.push({ angle: 0, yOffset: 0 });
            }

            if (waveLevel > 0) {
                shots = shots.map(s => ({ ...s, _waveDirection: 1 }));
            }

            if (doubleLevel > 0) {
                if (doubleLevel === 1) {
                    shots = shots.flatMap(s => [
                        { ...s, yOffset: (s.yOffset || 0) - 8 },
                        { ...s, yOffset: (s.yOffset || 0) + 8 },
                    ]);
                } else {
                    shots = shots.flatMap(s => [
                        { ...s, yOffset: (s.yOffset || 0) - 12 },
                        { ...s, yOffset: s.yOffset || 0 },
                        { ...s, yOffset: (s.yOffset || 0) + 12 },
                    ]);
                }
            }

            return shots;
        }

        checkCollisions() {
            // Update spatial grid periodically for better performance
            if (
                gameData.frameCount - gameData.lastSpatialUpdate >=
                gameData.spatialUpdateInterval
            ) {
                SpatialGrid.clear();
                for (const enemy of gameData.enemies) {
                    if (!enemy.destroyed) {
                        SpatialGrid.add(enemy, enemy.x, enemy.y);
                    }
                }
                gameData.lastSpatialUpdate = gameData.frameCount;
            }

            // Player projectiles vs enemies - optimized with spatial partitioning
            const playerProjectiles = gameData.projectiles.filter(proj =>
                [
                    'player',
                    'missile',
                    'wave',
                    'laser',
                    'wave-laser',
                    'turret',
                    'plasma',
                    'gatling',
                    'rocket',
                    'laser-plasma',
                    'laser-gatling',
                    'laser-rocket',
                    'mega-missile',
                    'ice-shard',
                ].includes(proj.type)
            );

            // Use spatial grid for collision detection
            for (const projectile of playerProjectiles) {
                const nearbyEnemies = SpatialGrid.getNearby(projectile.x, projectile.y, 150);
                gameData.performanceStats.spatialQueries++;

                for (const enemy of nearbyEnemies) {
                    if (enemy.destroyed) continue;

                    const dx = projectile.x - enemy.x;
                    const dy = projectile.y - enemy.y;
                    const distSquared = dx * dx + dy * dy;
                    gameData.performanceStats.collisionChecks++;

                    let hit = false;
                    if (projectile.type === 'laser' || projectile.type === 'wave-laser') {
                        hit = Math.abs(dx) < 60 && Math.abs(dy) < 20;
                    } else {
                        hit = distSquared < 1600; // 40px squared
                    }

                    if (hit) {
                        // Ice shard projectiles deal damage to enemies
                        if (projectile._shard) {
                            enemy.takeDamage(projectile.damage, 'ice-shard');
                            if (!projectile._piercing) projectile.destroy();
                        }

                        // --- Laser chain effect ---
                        if (
                            (projectile.type === 'laser' || projectile.type === 'wave-laser') &&
                            projectile._chainChance &&
                            Math.random() < projectile._chainChance
                        ) {
                            const chainTarget = nearbyEnemies.find(
                                e =>
                                    e !== enemy &&
                                    !e.destroyed &&
                                    Math.sqrt((e.x - enemy.x) ** 2 + (e.y - enemy.y) ** 2) < 100
                            );
                            if (chainTarget) {
                                chainTarget.takeDamage(Math.floor(projectile.damage / 2));
                            }
                        }

                        // --- Plasma burn effect ---
                        if (projectile.type === 'plasma' && projectile._burn) {
                            enemy._burnTicks = 180;
                            enemy._burnDamage = Math.floor(projectile.damage / 6);
                        }

                        // --- Gatling ricochet effect ---
                        if (
                            projectile.type === 'gatling' &&
                            projectile._ricochet &&
                            !projectile._hasRicocheted
                        ) {
                            const ricochetTarget = nearbyEnemies.find(
                                e =>
                                    e !== enemy &&
                                    !e.destroyed &&
                                    Math.sqrt((e.x - enemy.x) ** 2 + (e.y - enemy.y) ** 2) < 120
                            );
                            if (ricochetTarget) {
                                const angle = Math.atan2(
                                    ricochetTarget.y - enemy.y,
                                    ricochetTarget.x - enemy.x
                                );
                                const ricochetProj = Projectile.getOrCreate(
                                    enemy.x,
                                    enemy.y,
                                    Math.cos(angle) * 16,
                                    Math.sin(angle) * 16,
                                    'gatling',
                                    angle
                                );
                                ricochetProj.scene = this;
                                ricochetProj.damage = projectile.damage;
                                ricochetProj._hasRicocheted = true;
                                gameData.projectiles.push(ricochetProj);
                                this.addChild(ricochetProj);
                            }
                        }

                        // --- Elemental effects ---
                        if (projectile._elementalType === 'lightning') {
                            const level = projectile._elementalLevel || 1;
                            const chainChance =
                                gameData.elementalEffects.lightning.chainChance + 0.2 * (level - 1);
                            const stunDuration =
                                gameData.elementalEffects.lightning.stunDuration + 60 * (level - 1);
                            const chainJumps = gameData.elementalEffects.lightning.chainJumps;
                            const chainRange = gameData.elementalEffects.lightning.chainRange;
                            let chained = 0;
                            let lastTarget = enemy;
                            let alreadyHit = [enemy];

                            while (chained < chainJumps && Math.random() < chainChance) {
                                const chainTarget = nearbyEnemies.find(
                                    e =>
                                        !alreadyHit.includes(e) &&
                                        !e.destroyed &&
                                        Math.sqrt(
                                            (e.x - lastTarget.x) ** 2 + (e.y - lastTarget.y) ** 2
                                        ) < chainRange
                                );
                                if (chainTarget) {
                                    chainTarget.takeDamage(Math.floor(projectile.damage * 0.5));
                                    chainTarget._stunTicks = stunDuration;
                                    if (this.createLightningChain) {
                                        this.createLightningChain(
                                            lastTarget.x,
                                            lastTarget.y,
                                            chainTarget.x,
                                            chainTarget.y
                                        );
                                    }
                                    alreadyHit.push(chainTarget);
                                    lastTarget = chainTarget;
                                    chained++;
                                } else {
                                    break;
                                }
                            }
                            enemy._stunTicks = stunDuration;
                            // Flash effect removed for lightning powerup - only lightning arcs remain
                        } else if (projectile._elementalType === 'fire') {
                            enemy._burnTicks = gameData.elementalEffects.fire.burnDuration;
                            enemy._burnDamage = gameData.elementalEffects.fire.burnDamage;
                            if (this.createFireEffect) {
                                this.createFireEffect(enemy.x, enemy.y, 60);
                            }
                        } else if (projectile._elementalType === 'ice') {
                            const level = projectile._elementalLevel || 1;
                            enemy._slowTicks =
                                gameData.elementalEffects.ice.slowDuration + 60 * (level - 1);
                            enemy._slowEffect = gameData.elementalEffects.ice.slowEffect;
                            const freezeChance =
                                gameData.elementalEffects.ice.freezeChance + 0.15 * (level - 1);
                            const freezeDuration =
                                gameData.elementalEffects.ice.freezeDuration + 60 * (level - 1);
                            if (Math.random() < freezeChance) {
                                enemy._freezeTicks = freezeDuration;
                                if (this.createIceEffect) {
                                    this.createIceEffect(enemy.x, enemy.y, freezeDuration);
                                }
                                enemy._wasFrozen = true;
                            } else {
                                if (this.createIceEffect) {
                                    this.createIceEffect(enemy.x, enemy.y, 60);
                                }
                            }
                        }

                        // --- Mega-missile explosion effect ---
                        if (projectile.type === 'mega-missile') {
                            if (this.createExplosion)
                                this.createExplosion(projectile.x, projectile.y, 2);
                            audioManager.play('explosion');
                            enemy.takeDamage(projectile.damage, projectile.type);
                            projectile.destroy();
                        }
                        // --- Rocket AoE effect ---
                        else if (
                            projectile.type === 'rocket' ||
                            projectile.type === 'laser-rocket'
                        ) {
                            const aoeRadius = projectile._bigExplosion ? 240 : 120;
                            let aoeHit = false;

                            for (const otherEnemy of nearbyEnemies) {
                                if (otherEnemy === enemy || otherEnemy.destroyed) continue;
                                const dist = Math.sqrt(
                                    (otherEnemy.x - projectile.x) ** 2 +
                                        (otherEnemy.y - projectile.y) ** 2
                                );
                                if (dist < aoeRadius) {
                                    if (!otherEnemy.scene) otherEnemy.scene = this;
                                    const aoeDamage = Math.floor(projectile.damage * 0.8);
                                    otherEnemy.takeDamage(aoeDamage, projectile.type);
                                    if (this.createDamagePopup) {
                                        this.createDamagePopup(
                                            otherEnemy.x,
                                            otherEnemy.y,
                                            aoeDamage
                                        );
                                    }
                                    aoeHit = true;
                                }
                            }

                            if (projectile._bigExplosion && this.createExplosion)
                                this.createExplosion(projectile.x, projectile.y, 3);
                            enemy.takeDamage(projectile.damage, projectile.type);
                            if (!projectile._piercing) projectile.destroy();
                        } else {
                            enemy.takeDamage(projectile.damage, projectile.type);
                            if (!projectile._piercing) projectile.destroy();
                        }
                    }
                }
            }

            // Enemy projectiles vs player - optimized with squared distance
            if (gameData.ship) {
                const enemyProjectiles = gameData.projectiles.filter(proj => proj.type === 'enemy');
                for (const projectile of enemyProjectiles) {
                    const dx = projectile.x - gameData.ship.x;
                    const dy = projectile.y - gameData.ship.y;
                    const distSquared = dx * dx + dy * dy;

                    if (distSquared < 2500) {
                        // 50px squared
                        gameData.ship.takeDamage(projectile.damage);
                        projectile.destroy();
                    }
                }
            }

            // PowerUp collection
            let collectedPowerUp = null;
            for (const powerUp of gameData.powerUps) {
                if (gameData.ship) {
                    const dx = powerUp.x - gameData.ship.x;
                    const dy = powerUp.y - gameData.ship.y;
                    // Elliptical hitbox (width 40, height 32)
                    const ellipse = (dx * dx) / (20 * 20) + (dy * dy) / (16 * 16) <= 1;
                    // Generous circular hitbox (radius 48)
                    const circle = dx * dx + dy * dy < 48 * 48;
                    if (ellipse || circle) {
                        collectedPowerUp = powerUp;
                        break;
                    }
                }
            }
            if (collectedPowerUp) {
                // Check if it's an elemental powerup
                if (['lightning', 'fire', 'ice'].includes(collectedPowerUp.type)) {
                    // Remove any other element
                    gameData.elementalPowerups = gameData.elementalPowerups.filter(
                        p => p.type === collectedPowerUp.type
                    );
                    let idx = gameData.elementalPowerups.findIndex(
                        p => p.type === collectedPowerUp.type
                    );
                    if (idx !== -1) {
                        // Upgrade existing elemental powerup and refresh timer
                        const existingPowerup = gameData.elementalPowerups[idx];
                        existingPowerup.level = Math.min((existingPowerup.level || 1) + 1, 2); // Max level 2
                        existingPowerup.timer = 3600; // 60 seconds at 60fps
                    } else {
                        // Add new elemental powerup
                        gameData.elementalPowerups.push({
                            type: collectedPowerUp.type,
                            timer: 3600,
                            level: 1,
                        });
                    }
                } else {
                    // Regular powerup stacking logic
                    let idx = gameData.activeFirePowerups.findIndex(
                        p => p.type === collectedPowerUp.type
                    );
                    if (idx !== -1) {
                        // Upgrade existing powerup level and refresh timer
                        const existingPowerup = gameData.activeFirePowerups[idx];
                        existingPowerup.level = Math.min((existingPowerup.level || 1) + 1, 2); // Max level 2
                        existingPowerup.timer = 3600; // 60 seconds at 60fps
                    } else {
                        // Add new powerup, remove oldest if at max capacity
                        if (gameData.activeFirePowerups.length >= 2) {
                            gameData.activeFirePowerups.shift();
                        }
                        gameData.activeFirePowerups.push({
                            type: collectedPowerUp.type,
                            timer: 3600,
                            level: 1,
                        }); // Start at level 1
                    }
                }

                // Play powerup sound
                audioManager.play('powerup');

                // Create special effect for elemental powerups
                if (['lightning', 'fire', 'ice'].includes(collectedPowerUp.type)) {
                    const effectColors = {
                        lightning: '#FFFF00',
                        fire: '#FF4500',
                        ice: '#87CEEB',
                    };
                    const color = effectColors[collectedPowerUp.type];

                    // Create elemental collection effect
                    const effect = new Sprite();
                    effect.bitmap = new Bitmap(200, 200);
                    const ctx = effect.bitmap.context;

                    effect.anchor.set(0.5, 0.5);
                    effect.x = collectedPowerUp.x;
                    effect.y = collectedPowerUp.y;
                    effect.alpha = 0.8;

                    // Draw elemental symbol
                    ctx.save();
                    ctx.strokeStyle = color;
                    ctx.lineWidth = 4;
                    ctx.lineCap = 'round';
                    ctx.shadowColor = color;
                    ctx.shadowBlur = 15;

                    switch (collectedPowerUp.type) {
                        case 'lightning':
                            // Lightning bolt
                            ctx.beginPath();
                            ctx.moveTo(100, 50);
                            ctx.lineTo(80, 100);
                            ctx.lineTo(120, 100);
                            ctx.lineTo(100, 150);
                            ctx.stroke();
                            break;
                        case 'fire':
                            // Fire symbol
                            ctx.beginPath();
                            ctx.arc(100, 100, 40, 0, Math.PI * 2);
                            ctx.stroke();
                            ctx.beginPath();
                            ctx.arc(100, 100, 20, 0, Math.PI * 2);
                            ctx.stroke();
                            break;
                        case 'ice':
                            // Ice crystal
                            ctx.beginPath();
                            ctx.moveTo(100, 50);
                            ctx.lineTo(120, 100);
                            ctx.lineTo(100, 150);
                            ctx.lineTo(80, 100);
                            ctx.closePath();
                            ctx.stroke();
                            break;
                    }

                    ctx.restore();

                    this.addChild(effect);

                    // Animate the effect
                    let frame = 0;
                    const animateEffect = () => {
                        if (!effect.parent) return;
                        frame++;
                        const progress = frame / 30;

                        effect.alpha = 0.8 * (1 - progress);
                        effect.scale.x = 1 + progress * 0.5;
                        effect.scale.y = 1 + progress * 0.5;

                        if (progress < 1) {
                            requestAnimationFrame(animateEffect);
                        } else {
                            if (effect.parent) this.removeChild(effect);
                        }
                    };

                    requestAnimationFrame(animateEffect);
                }

                // Remove the collected powerup from the game
                gameData.powerUps = gameData.powerUps.filter(p => p !== collectedPowerUp);
                this.removeChild(collectedPowerUp);
                collectedPowerUp.destroy();
            }

            // Enemy collides with player ship - optimized with squared distance
            if (gameData.ship) {
                const activeEnemies = gameData.enemies.filter(enemy => !enemy.destroyed);
                activeEnemies.forEach(enemy => {
                    const dx = enemy.x - gameData.ship.x;
                    const dy = enemy.y - gameData.ship.y;
                    const distSquared = dx * dx + dy * dy;
                    // Use 45-50 pixel radius for collision (squared)
                    if (distSquared < 2304) {
                        // 48px squared
                        gameData.ship.takeDamage(30);
                        enemy.destroyed = true;
                        if (enemy.parent) enemy.parent.removeChild(enemy);
                    }
                });
            }
        }

        nextLevel() {
            gameData.level++;
            gameData.enemiesKilledThisLevel = 0;
            gameData.enemiesRequiredForNextLevel = 20 + gameData.level * 5; // More enemies required per level
            gameData.spawnInterval = Math.max(30, 60 - gameData.level * 2); // Faster spawning

            // Title progression
            if (gameData.level === 5) {
                gameData.playerTitle = 'Veteran';
                gameData.damageBonus = 10;
            } else if (gameData.level === 10) {
                gameData.playerTitle = 'Ace Pilot';
                gameData.damageBonus = 20;
            } else if (gameData.level === 15) {
                gameData.playerTitle = 'Elite Commander';
                gameData.damageBonus = 30;
            } else if (gameData.level === 20) {
                gameData.playerTitle = 'Legendary Sky Captain';
                gameData.damageBonus = 50;
            }

            // Play level up sound
            audioManager.play('levelUp');
        }

        spawnBoss() {
            gameData.bossActive = true;

            // Determine boss type based on level
            let bossType = 'destroyer';
            if (gameData.level >= 20) bossType = 'titan';
            else if (gameData.level >= 15) bossType = 'dreadnought';
            else if (gameData.level >= 10) bossType = 'carrier';

            const boss = new Boss(Graphics.width + 100, Graphics.height / 2, bossType);
            boss.scene = this; // Ensure scene reference for attacks and popups
            gameData.bossHealth = boss.health;
            gameData.bossMaxHealth = boss.maxHealth;
            gameData.enemies.push(boss);
            this.addChild(boss);

            // Play boss spawn sound
            audioManager.play('bossSpawn');
        }

        createExplosion(x, y, size = 1) {
            // Optimized explosion system with batched rendering
            const particleCount = size === 1 ? 6 : size === 2 ? 10 : 18;
            const maxDistance = size === 1 ? 20 : size === 2 ? 30 : 50;

            // Create explosion container
            const explosionContainer = new Sprite();
            explosionContainer.x = x;
            explosionContainer.y = y;
            this.addChild(explosionContainer);

            // Pre-create particle bitmaps for reuse
            const particleBitmaps = this.getExplosionParticleBitmaps();

            // Create particles with optimized data structure
            const particles = [];
            for (let i = 0; i < particleCount; i++) {
                const angle = (Math.PI * 2 * i) / particleCount;
                const speed = 2 + Math.random() * 6;
                const particleSize = 4 + Math.random() * 8;
                const particleType = Math.floor(Math.random() * 3);

                const particle = new Sprite();
                particle.bitmap = particleBitmaps[particleType];
                particle.anchor.set(0.5, 0.5);
                particle.x = (Math.random() - 0.5) * 10;
                particle.y = (Math.random() - 0.5) * 10;
                particle.vx = Math.cos(angle) * speed + (Math.random() - 0.5) * 1;
                particle.vy = Math.sin(angle) * speed + (Math.random() - 0.5) * 1;
                particle.life = 23 + Math.random() * 15;
                particle.maxLife = particle.life;
                particle.rotation = Math.random() * Math.PI * 2;
                particle.rotationSpeed = (Math.random() - 0.5) * 0.2;
                particle.scale.x = 1;
                particle.scale.y = 1;

                explosionContainer.addChild(particle);
                particles.push(particle);
            }

            // Single animation loop for all particles
            let frame = 0;
            const animateExplosion = () => {
                if (!explosionContainer.parent) return;

                frame++;
                let activeParticles = 0;

                for (const particle of particles) {
                    if (particle.life > 0) {
                        // Update particle physics
                        particle.x += particle.vx;
                        particle.y += particle.vy;
                        particle.rotation += particle.rotationSpeed;
                        particle.vy += 0.1;
                        particle.vx *= 0.98;
                        particle.vy *= 0.98;
                        particle.life--;

                        // Update visual properties
                        const lifeRatio = particle.life / particle.maxLife;
                        particle.alpha = lifeRatio;
                        particle.scale.x = 0.5 + lifeRatio * 0.5;
                        particle.scale.y = 0.5 + lifeRatio * 0.5;

                        activeParticles++;
                    }
                }

                // Continue animation if particles are still alive
                if (activeParticles > 0) {
                    requestAnimationFrame(animateExplosion);
                } else {
                    // Clean up when all particles are dead
                    if (explosionContainer.parent) {
                        explosionContainer.parent.removeChild(explosionContainer);
                    }
                }
            };

            requestAnimationFrame(animateExplosion);

            // Create shockwave and flash effects
            this.createShockwave(x, y, maxDistance);
            this.createFlashEffect(x, y, size);
        }

        getExplosionParticleBitmaps() {
            // Cache particle bitmaps to avoid recreation
            if (!this._explosionParticleBitmaps) {
                this._explosionParticleBitmaps = [];

                // Create fire particle bitmap
                const fireBitmap = new Bitmap(12, 12);
                const fireCtx = fireBitmap.context;
                const fireGradient = fireCtx.createRadialGradient(6, 6, 0, 6, 6, 6);
                fireGradient.addColorStop(0, '#FFFF00');
                fireGradient.addColorStop(0.3, '#FFA500');
                fireGradient.addColorStop(0.7, '#FF4500');
                fireGradient.addColorStop(1, '#FF0000');
                fireCtx.fillStyle = fireGradient;
                fireCtx.beginPath();
                fireCtx.arc(6, 6, 6, 0, Math.PI * 2);
                fireCtx.fill();
                this._explosionParticleBitmaps.push(fireBitmap);

                // Create smoke particle bitmap
                const smokeBitmap = new Bitmap(12, 12);
                const smokeCtx = smokeBitmap.context;
                const smokeGradient = smokeCtx.createRadialGradient(6, 6, 0, 6, 6, 6);
                smokeGradient.addColorStop(0, '#FFFFFF');
                smokeGradient.addColorStop(0.3, '#CCCCCC');
                smokeGradient.addColorStop(0.7, '#666666');
                smokeGradient.addColorStop(1, '#000000');
                smokeCtx.fillStyle = smokeGradient;
                smokeCtx.beginPath();
                smokeCtx.arc(6, 6, 6, 0, Math.PI * 2);
                smokeCtx.fill();
                this._explosionParticleBitmaps.push(smokeBitmap);

                // Create spark particle bitmap
                const sparkBitmap = new Bitmap(12, 12);
                const sparkCtx = sparkBitmap.context;
                const sparkGradient = sparkCtx.createRadialGradient(6, 6, 0, 6, 6, 6);
                sparkGradient.addColorStop(0, '#FFFFFF');
                sparkGradient.addColorStop(0.5, '#87CEEB');
                sparkGradient.addColorStop(1, '#4682B4');
                sparkCtx.fillStyle = sparkGradient;
                sparkCtx.beginPath();
                sparkCtx.arc(6, 6, 6, 0, Math.PI * 2);
                sparkCtx.fill();
                this._explosionParticleBitmaps.push(sparkBitmap);
            }

            return this._explosionParticleBitmaps;
        }

        createFlashEffect(x, y, size) {
            const flash = new Sprite();
            flash.bitmap = new Bitmap(Graphics.width, Graphics.height);
            const context = flash.bitmap.context;

            // Create radial flash from explosion center (halved radius)
            const gradient = context.createRadialGradient(x, y, 0, x, y, 100 * size);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(0.3, 'rgba(255, 255, 200, 0.4)');
            gradient.addColorStop(0.7, 'rgba(255, 200, 100, 0.2)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

            context.fillStyle = gradient;
            context.fillRect(0, 0, Graphics.width, Graphics.height);

            flash.alpha = 0;
            this.addChild(flash);

            // Animate flash
            let frame = 0;
            const animateFlash = () => {
                // Prevent animation if flash is removed
                if (!flash.parent) return;
                frame++;
                const progress = frame / 15; // 15 frames for flash

                if (progress < 0.5) {
                    // Flash in
                    flash.alpha = progress * 2;
                } else {
                    // Flash out
                    flash.alpha = (1 - progress) * 2;
                }

                if (progress < 1) {
                    requestAnimationFrame(animateFlash);
                } else {
                    if (flash.parent) this.removeChild(flash);
                }
            };

            requestAnimationFrame(animateFlash);
        }

        createShockwave(x, y, maxDistance) {
            const shockwave = new Sprite();
            shockwave.bitmap = new Bitmap(maxDistance * 2, maxDistance * 2);
            const context = shockwave.bitmap.context;

            shockwave.anchor.set(0.5, 0.5);
            shockwave.x = x;
            shockwave.y = y;
            shockwave.scale.x = 0.1;
            shockwave.scale.y = 0.1;
            shockwave.alpha = 0.8;

            // Draw shockwave ring
            context.strokeStyle = 'rgba(255, 255, 255, 0.6)';
            context.lineWidth = 3;
            context.beginPath();
            context.arc(maxDistance, maxDistance, maxDistance - 10, 0, Math.PI * 2);
            context.stroke();

            this.addChild(shockwave);

            // Animate shockwave expansion
            let frame = 0;
            const animateShockwave = () => {
                // Prevent animation if shockwave is removed
                if (!shockwave.parent) return;
                frame++;
                const progress = frame / 30; // 30 frames animation

                shockwave.scale.x = 0.1 + progress * 0.9;
                shockwave.scale.y = 0.1 + progress * 0.9;
                shockwave.alpha = 0.8 * (1 - progress);

                if (progress < 1) {
                    requestAnimationFrame(animateShockwave);
                } else {
                    if (shockwave.parent) this.removeChild(shockwave);
                }
            };

            requestAnimationFrame(animateShockwave);
        }

        // Elemental visual effects
        createLightningChain(x, y, targetX, targetY) {
            const lightning = new Sprite();
            lightning.bitmap = new Bitmap(Math.abs(targetX - x) + 20, Math.abs(targetY - y) + 20);
            const context = lightning.bitmap.context;

            lightning.anchor.set(0.5, 0.5);
            lightning.x = (x + targetX) / 2;
            lightning.y = (y + targetY) / 2;
            lightning.alpha = 0.9;

            // Draw lightning bolt with zigzag pattern
            context.strokeStyle = '#FFFF00';
            context.lineWidth = 4;
            context.lineCap = 'round';
            context.lineJoin = 'round';

            const dx = targetX - x;
            const dy = targetY - y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const segments = Math.max(3, Math.floor(distance / 30));

            context.beginPath();
            context.moveTo(10, 10);

            for (let i = 1; i <= segments; i++) {
                const t = i / segments;
                const baseX = 10 + dx * t;
                const baseY = 10 + dy * t;
                const offset = (Math.random() - 0.5) * 15;
                const offsetX = offset * Math.cos(Math.atan2(dy, dx) + Math.PI / 2);
                const offsetY = offset * Math.sin(Math.atan2(dy, dx) + Math.PI / 2);

                context.lineTo(baseX + offsetX, baseY + offsetY);
            }

            context.stroke();

            // Add glow effect
            context.shadowColor = '#FFFF00';
            context.shadowBlur = 10;
            context.stroke();

            this.addChild(lightning);

            // Animate lightning flash
            let frame = 0;
            const animateLightning = () => {
                if (!lightning.parent) return;
                frame++;
                const progress = frame / 10; // 10 frames for lightning flash

                lightning.alpha = 0.9 * (1 - progress);

                if (progress < 1) {
                    requestAnimationFrame(animateLightning);
                } else {
                    if (lightning.parent) this.removeChild(lightning);
                }
            };

            requestAnimationFrame(animateLightning);
        }

        createFireEffect(x, y, duration = 60) {
            const fireParticles = [];
            const fireSprite = new Sprite();
            fireSprite.x = x;
            fireSprite.y = y;

            // Create fire particles
            for (let i = 0; i < 15; i++) {
                const particle = {
                    x: (Math.random() - 0.5) * 20,
                    y: (Math.random() - 0.5) * 20,
                    vx: (Math.random() - 0.5) * 2,
                    vy: -Math.random() * 3 - 1,
                    life: 30 + Math.random() * 30,
                    maxLife: 30 + Math.random() * 30,
                    size: 3 + Math.random() * 4,
                    color: ['#FF4500', '#FF6347', '#FF8C00', '#FFD700'][
                        Math.floor(Math.random() * 4)
                    ],
                };
                fireParticles.push(particle);
            }

            fireSprite.bitmap = new Bitmap(60, 60);
            fireSprite.anchor.set(0.5, 0.5);

            this.addChild(fireSprite);

            // Animate fire particles
            let frame = 0;
            const animateFire = () => {
                if (!fireSprite.parent || frame >= duration) {
                    if (fireSprite.parent) this.removeChild(fireSprite);
                    return;
                }

                frame++;
                const ctx = fireSprite.bitmap.context;
                ctx.clearRect(0, 0, 60, 60);

                fireParticles.forEach(particle => {
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.vy += 0.1; // Gravity
                    particle.life--;

                    if (particle.life > 0) {
                        const alpha = particle.life / particle.maxLife;
                        ctx.save();
                        ctx.globalAlpha = alpha;
                        ctx.fillStyle = particle.color;
                        ctx.beginPath();
                        ctx.arc(
                            30 + particle.x,
                            30 + particle.y,
                            particle.size * alpha,
                            0,
                            Math.PI * 2
                        );
                        ctx.fill();
                        ctx.restore();
                    }
                });

                requestAnimationFrame(animateFire);
            };

            requestAnimationFrame(animateFire);
        }

        createIceEffect(x, y, duration = 120) {
            const iceSprite = new Sprite();
            iceSprite.bitmap = new Bitmap(80, 80);
            const context = iceSprite.bitmap.context;

            iceSprite.anchor.set(0.5, 0.5);
            iceSprite.x = x;
            iceSprite.y = y;
            iceSprite.alpha = 0.8;

            // Draw ice crystal pattern
            context.strokeStyle = '#87CEEB';
            context.lineWidth = 3;
            context.lineCap = 'round';

            // Draw multiple ice crystals
            for (let i = 0; i < 6; i++) {
                const angle = (Math.PI * 2 * i) / 6;
                const radius = 20 + Math.random() * 15;
                const x1 = 40 + Math.cos(angle) * radius;
                const y1 = 40 + Math.sin(angle) * radius;
                const x2 = 40 + Math.cos(angle) * (radius + 10);
                const y2 = 40 + Math.sin(angle) * (radius + 10);

                context.beginPath();
                context.moveTo(x1, y1);
                context.lineTo(x2, y2);
                context.stroke();
            }

            // Add glow effect
            context.shadowColor = '#87CEEB';
            context.shadowBlur = 8;
            context.stroke();

            this.addChild(iceSprite);

            // Animate ice crystal
            let frame = 0;
            const animateIce = () => {
                if (!iceSprite.parent || frame >= duration) {
                    if (iceSprite.parent) this.removeChild(iceSprite);
                    return;
                }

                frame++;
                const progress = frame / duration;

                iceSprite.alpha = 0.8 * (1 - progress);
                iceSprite.scale.x = 1 + progress * 0.5;
                iceSprite.scale.y = 1 + progress * 0.5;

                requestAnimationFrame(animateIce);
            };

            requestAnimationFrame(animateIce);
        }

        updateUI() {
            if (this._uiWindow) {
                // Only update UI if values have changed
                const currentUIState = {
                    level: gameData.level,
                    score: gameData.score,
                    lives: gameData.lives,
                    hp: gameData.ship ? gameData.ship.health : 0,
                    maxHp: gameData.ship ? gameData.ship.maxHealth : 0,
                    kills: gameData.enemiesKilledThisLevel,
                    required: gameData.enemiesRequiredForNextLevel,
                    heat: gameData.weaponHeat,
                    maxHeat: gameData.maxHeat,
                    weapon: gameData.primaryWeapon,
                    powerups: gameData.activeFirePowerups ? gameData.activeFirePowerups.length : 0,
                    megaCooldown: gameData.megaMissileCooldown,
                };

                // Check if UI needs updating
                if (
                    !this._lastUIState ||
                    JSON.stringify(this._lastUIState) !== JSON.stringify(currentUIState)
                ) {
                    this._uiWindow.contents.clear();
                    this._uiWindow.contents.fontSize = 16;
                    this._uiWindow.contents.drawText(`Level: ${gameData.level}`, 10, 10, 280, 20);
                    this._uiWindow.contents.drawText(`Score: ${gameData.score}`, 10, 30, 280, 20);
                    this._uiWindow.contents.drawText(`Lives: ${gameData.lives}`, 10, 50, 280, 20);

                    // Draw player title and damage bonus
                    if (gameData.playerTitle && gameData.playerTitle !== 'Rookie') {
                        this._uiWindow.contents.textColor = '#FFD700';
                        this._uiWindow.contents.drawText(
                            `${gameData.playerTitle}`,
                            10,
                            70,
                            280,
                            20
                        );
                        this._uiWindow.contents.textColor = '#00FF00';
                        this._uiWindow.contents.drawText(
                            `+${gameData.damageBonus}% Damage`,
                            10,
                            90,
                            280,
                            20
                        );
                        this._uiWindow.contents.textColor = '#ffffff';
                    }

                    // Show player HP
                    if (gameData.ship) {
                        const hp = gameData.ship.health;
                        const maxHp = gameData.ship.maxHealth;
                        const hpY =
                            gameData.playerTitle && gameData.playerTitle !== 'Rookie' ? 110 : 90;
                        this._uiWindow.contents.drawText(`HP: ${hp}/${maxHp}`, 10, hpY, 280, 20);
                        // HP bar
                        const hpPercent = hp / maxHp;
                        let hpColor = '#00FF00';
                        if (hpPercent < 0.33) hpColor = '#FF3333';
                        else if (hpPercent < 0.66) hpColor = '#FFFF00';
                        this._uiWindow.contents.fillRect(10, hpY + 20, 100, 10, '#333');
                        this._uiWindow.contents.fillRect(
                            10,
                            hpY + 20,
                            Math.max(0, hpPercent * 100),
                            10,
                            hpColor
                        );
                    }
                    const killsY =
                        gameData.playerTitle && gameData.playerTitle !== 'Rookie' ? 130 : 70;
                    this._uiWindow.contents.drawText(
                        `Kills: ${gameData.enemiesKilledThisLevel}/${gameData.enemiesRequiredForNextLevel}`,
                        10,
                        killsY,
                        280,
                        20
                    );

                    // Damage percentage display
                    const heatPercentDecimal = gameData.weaponHeat / gameData.maxHeat;
                    const minMultiplier = 0.6;
                    const maxMultiplier = 1.5;
                    const damageMultiplier =
                        maxMultiplier - (maxMultiplier - minMultiplier) * heatPercentDecimal;
                    const damagePercent = Math.round(damageMultiplier * 100);
                    this._uiWindow.contents.textColor =
                        damagePercent >= 100
                            ? '#00FF00'
                            : damagePercent >= 90
                              ? '#FFFF00'
                              : '#FF6666';
                    const damageY =
                        gameData.playerTitle && gameData.playerTitle !== 'Rookie' ? 150 : 125;
                    this._uiWindow.contents.drawText(
                        `Damage: ${damagePercent}%`,
                        10,
                        damageY,
                        280,
                        20
                    );
                    this._uiWindow.contents.textColor = '#ffffff';

                    // Heat bar
                    const heatPercent = (gameData.weaponHeat / gameData.maxHeat) * 100;
                    const heatY =
                        gameData.playerTitle && gameData.playerTitle !== 'Rookie' ? 170 : 145;
                    this._uiWindow.contents.fillRect(10, heatY, 100, 10, '#333');
                    this._uiWindow.contents.fillRect(
                        10,
                        heatY,
                        heatPercent,
                        10,
                        heatPercent > 80 ? '#FF0000' : '#FF8000'
                    );

                    // Weapon display
                    const weaponNames = {
                        laser: 'Laser',
                        plasma: 'Plasma',
                        gatling: 'Gatling',
                        rocket: 'Rocket',
                    };
                    let y = gameData.playerTitle && gameData.playerTitle !== 'Rookie' ? 190 : 165;
                    this._uiWindow.contents.textColor = '#00FFFF';
                    this._uiWindow.contents.drawText(
                        `Weapon: ${weaponNames[gameData.primaryWeapon]}`,
                        10,
                        y,
                        280,
                        20
                    );
                    y += 20;
                    this._uiWindow.contents.textColor = '#ffffff';

                    // Active powerups display
                    if (gameData.activeFirePowerups && gameData.activeFirePowerups.length > 0) {
                        y += 6;
                        this._uiWindow.contents.textColor = '#FFD700';
                        this._uiWindow.contents.fontBold = true;
                        this._uiWindow.contents.drawText('Active Powerups:', 10, y, 280, 20);
                        this._uiWindow.contents.fontBold = false;
                        y += 20;
                        gameData.activeFirePowerups.forEach(powerup => {
                            this._uiWindow.contents.textColor = '#00FF00';
                            const timeLeft = Math.ceil(powerup.timer / 60);
                            const powerupNames = {
                                spread: 'Spread Shot',
                                double: 'Double Shot',
                                wave: 'Wave Shot',
                                laser: 'Piercing Laser',
                            };
                            const name = powerupNames[powerup.type] || powerup.type;
                            const level = powerup.level || 1;
                            const levelText = level > 1 ? ` Lv${level}` : '';
                            this._uiWindow.contents.drawText(
                                `  ${name}${levelText}: ${timeLeft}s`,
                                20,
                                y,
                                260,
                                20
                            );
                            y += 20;
                        });
                        this._uiWindow.contents.textColor = '#ffffff';
                    }

                    // Elemental powerups display
                    if (gameData.elementalPowerups && gameData.elementalPowerups.length > 0) {
                        this._uiWindow.contents.textColor = '#FF00FF';
                        this._uiWindow.contents.fontBold = true;
                        this._uiWindow.contents.drawText('Elemental Powerups:', 10, y, 280, 20);
                        this._uiWindow.contents.fontBold = false;
                        y += 22;
                        gameData.elementalPowerups.forEach(powerup => {
                            const name = elementalNames[powerup.type] || powerup.type;
                            const color = elementalColors[powerup.type] || '#FF00FF';
                            this._uiWindow.contents.textColor = color;
                            let text = `${name}`;
                            if (powerup.level > 1) text += ` Lv${powerup.level}`;
                            text += `: ${Math.ceil(powerup.timer / 60)}s`;
                            this._uiWindow.contents.drawText(text, 20, y, 260, 20);
                            y += 20;
                        });
                        this._uiWindow.contents.textColor = '#FFF';
                    }

                    // Mega missile cooldown display
                    if (gameData.megaMissileCooldown > 0) {
                        this._uiWindow.contents.textColor = '#FFD700';
                        this._uiWindow.contents.drawText(
                            `Mega Missiles: ${Math.ceil(gameData.megaMissileCooldown / 60)}s`,
                            10,
                            y,
                            280,
                            20
                        );
                        this._uiWindow.contents.textColor = '#ffffff';
                        y += 20;
                    }

                    // Cache the current state
                    this._lastUIState = currentUIState;
                }
            }

            if (this._instructionsWindow) {
                this._instructionsWindow.contents.clear();
                this._instructionsWindow.contents.fontSize = 14;
                this._instructionsWindow.contents.drawText('Arrow Keys: Move', 10, 10, 220, 20);
                this._instructionsWindow.contents.drawText('Z/Enter: Fire Weapon', 10, 30, 220, 20);
                this._instructionsWindow.contents.drawText('X: Mega Missiles', 10, 50, 220, 20);
                this._instructionsWindow.contents.drawText('V/RB: Next Weapon', 10, 70, 220, 20);
                this._instructionsWindow.contents.drawText(
                    'B/LB: Previous Weapon',
                    10,
                    90,
                    220,
                    20
                );
                this._instructionsWindow.contents.drawText('ESC: Exit', 10, 110, 220, 20);
            }
            // Boss HP bar
            if (this._bossHpBar) {
                if (gameData.bossActive && gameData.bossHealth > 0 && gameData.bossMaxHealth > 0) {
                    this._bossHpBar.visible = true;
                    const ctx = this._bossHpBar.bitmap.context;
                    this._bossHpBar.bitmap.clear();
                    // Bar background
                    ctx.fillStyle = 'rgba(0,0,0,0.7)';
                    ctx.fillRect(0, 0, 420, 48);
                    // Bar border
                    ctx.strokeStyle = '#FFD700';
                    ctx.lineWidth = 3;
                    ctx.strokeRect(6, 18, 408, 18);
                    // HP bar
                    const hpPercent = Math.max(0, gameData.bossHealth / gameData.bossMaxHealth);
                    ctx.fillStyle =
                        hpPercent > 0.5 ? '#00FF00' : hpPercent > 0.2 ? '#FFFF00' : '#FF3333';
                    ctx.fillRect(8, 20, Math.floor(404 * hpPercent), 14);
                    // Boss name
                    ctx.font = 'bold 20px Arial';
                    ctx.fillStyle = '#FFD700';
                    ctx.textAlign = 'center';
                    let bossName = 'BOSS';
                    if (gameData.bossMaxHealth === 15000) bossName = 'DESTROYER';
                    else if (gameData.bossMaxHealth === 25000) bossName = 'CARRIER';
                    else if (gameData.bossMaxHealth === 40000) bossName = 'DREADNOUGHT';
                    else if (gameData.bossMaxHealth === 60000) bossName = 'TITAN';
                    ctx.fillText(bossName, 210, 15);
                    // HP numbers
                    ctx.font = '16px Arial';
                    ctx.fillStyle = '#fff';
                    ctx.fillText(`${gameData.bossHealth} / ${gameData.bossMaxHealth}`, 210, 40);
                } else {
                    this._bossHpBar.visible = false;
                }
            }
        }

        renderDebugInfo() {
            // Create debug overlay if it doesn't exist
            if (!this._debugOverlay) {
                this._debugOverlay = new Sprite();
                this._debugOverlay.bitmap = new Bitmap(Graphics.width, Graphics.height);
                this.addChild(this._debugOverlay);
            }

            this._debugOverlay.bitmap.clear();
            const context = this._debugOverlay.bitmap.context;

            // Draw collision circles for player ship
            if (gameData.ship) {
                context.strokeStyle = 'rgba(0, 255, 0, 0.5)';
                context.lineWidth = 2;
                context.beginPath();
                context.arc(gameData.ship.x, gameData.ship.y, 50, 0, Math.PI * 2);
                context.stroke();
            }

            // Draw collision circles for enemies
            context.strokeStyle = 'rgba(255, 0, 0, 0.5)';
            context.lineWidth = 2;
            gameData.enemies.forEach(enemy => {
                context.beginPath();
                context.arc(enemy.x, enemy.y, 40, 0, Math.PI * 2);
                context.stroke();
            });

            // Draw collision circles for projectiles
            context.strokeStyle = 'rgba(0, 0, 255, 0.5)';
            context.lineWidth = 1;
            gameData.projectiles.forEach(projectile => {
                context.beginPath();
                context.arc(projectile.x, projectile.y, 4, 0, Math.PI * 2);
                context.stroke();
            });

            // Draw collision circles for powerups
            context.strokeStyle = 'rgba(255, 255, 0, 0.5)'; // Yellow for powerups
            context.lineWidth = 2;
            gameData.powerUps.forEach(powerUp => {
                context.beginPath();
                context.arc(powerUp.x, powerUp.y, 16, 0, Math.PI * 2); // PowerUp is 32x32, use 16 for collision
                context.stroke();
            });

            // Draw day/night cycle info
            context.font = '14px Arial';
            context.fillStyle = 'rgba(255, 255, 255, 0.8)';
            context.textAlign = 'left';
            context.fillText(
                `Day/Night: ${(this._dayNightCycle * 100).toFixed(1)}%`,
                10,
                Graphics.height - 60
            );
            context.fillText(`Time: ${this.getTimeOfDayString()}`, 10, Graphics.height - 40);
        }

        getTimeOfDayString() {
            const cycle = this._dayNightCycle;
            if (cycle < 0.18) return 'Night';
            if (cycle < 0.25) return 'Pre-Dawn';
            if (cycle < 0.32) return 'Sunrise';
            if (cycle < 0.68) return 'Day';
            if (cycle < 0.75) return 'Sunset';
            if (cycle < 0.82) return 'Dusk';
            return 'Night';
        }

        restartGame() {
            // Clean up existing game elements
            if (gameData.ship) {
                this.removeChild(gameData.ship);
                gameData.ship = null;
            }

            gameData.enemies.forEach(enemy => this.removeChild(enemy));
            gameData.projectiles.forEach(projectile => this.removeChild(projectile));
            gameData.clouds.forEach(cloud => this.removeChild(cloud));
            gameData.powerUps.forEach(powerUp => this.removeChild(powerUp)); // Also remove powerups

            // Reset game state
            gameData.enemies = [];
            gameData.projectiles = [];
            gameData.clouds = [];
            gameData.powerUps = []; // Clear powerups
            gameData.gameWon = false;
            gameData.gameLost = false;
            gameData.activeFirePowerups = []; // Reset powerup
            gameData.powerUpTimer = 0; // Reset powerup timer

            // Recreate game elements
            this.createShip();
            this.createClouds();
            this.startGame();
        }

        createDamagePopup(x, y, amount) {
            // Add cooldown to prevent spam from rapid-fire weapons
            if (!this._lastDamagePopupTime) this._lastDamagePopupTime = {};
            const key = `${Math.floor(x / 20)},${Math.floor(y / 20)}`; // Group nearby popups
            const currentTime = Date.now();
            if (
                this._lastDamagePopupTime[key] &&
                currentTime - this._lastDamagePopupTime[key] < 100
            ) {
                return; // Skip if too soon (100ms cooldown)
            }
            this._lastDamagePopupTime[key] = currentTime;

            const popup = new Sprite();
            popup.bitmap = new Bitmap(80, 32);
            const ctx = popup.bitmap.context;
            ctx.save();
            ctx.font = 'bold 22px Arial';
            ctx.textAlign = 'center';
            ctx.lineWidth = 4;
            ctx.strokeStyle = 'black';
            ctx.strokeText(amount, 40, 24);
            ctx.fillStyle = 'white';
            ctx.fillText(amount, 40, 24);
            ctx.restore();
            popup.x = x - 40;
            popup.y = y - 32;
            popup.alpha = 1;
            this.addChild(popup);
            let frame = 0;
            const animate = () => {
                if (!popup.parent) return;
                frame++;
                popup.y -= 1.2;
                popup.alpha = 1 - frame / 45;
                if (frame < 45) {
                    requestAnimationFrame(animate);
                } else {
                    if (popup.parent) this.removeChild(popup);
                }
            };
            requestAnimationFrame(animate);
        }

        createDamageImpactParticles(x, y, amount = 20, projectileType = 'player') {
            // Determine number of sparks: 1 for very low, up to 6 for very high damage
            let count = Math.max(1, Math.min(6, Math.round(amount / 30)));
            // Weapon type color logic
            let colorStops;
            switch (projectileType) {
                case 'gatling':
                case 'laser-gatling':
                    colorStops = ['#FFFFAA', '#FFD700', '#FF6600']; // yellow/orange
                    break;
                case 'plasma':
                case 'wave':
                case 'wave-laser':
                    colorStops = ['#B0E0FF', '#00FFFF', '#3399FF']; // blue/cyan
                    break;
                case 'rocket':
                case 'laser-rocket':
                    colorStops = ['#FFDDCC', '#FF6600', '#FF0000']; // red/orange
                    break;
                case 'laser':
                case 'player':
                default:
                    colorStops = ['#FFFFFF', '#B0E0FF', '#3399FF']; // white/blue
                    break;
            }
            for (let i = 0; i < count; i++) {
                // For gatling, add a natural angle spread (narrow cone, random)
                let angle;
                if (projectileType === 'gatling' || projectileType === 'laser-gatling') {
                    // Centered at 0, spread -30 to +30 degrees
                    angle = ((Math.random() - 0.5) * Math.PI) / 3;
                } else {
                    angle = Math.random() * Math.PI * 2;
                }
                const speed = 2 + Math.random() * 2;
                const particle = new Sprite();
                const particleSize = 6 + Math.random() * 4;
                particle.bitmap = new Bitmap(particleSize, particleSize);
                const context = particle.bitmap.context;
                const centerX = particleSize / 2;
                const centerY = particleSize / 2;
                // Dynamic spark color
                const gradient = context.createRadialGradient(
                    centerX,
                    centerY,
                    0,
                    centerX,
                    centerY,
                    particleSize / 2
                );
                gradient.addColorStop(0, colorStops[0]);
                gradient.addColorStop(0.5, colorStops[1]);
                gradient.addColorStop(1, colorStops[2]);
                context.fillStyle = gradient;
                context.beginPath();
                context.arc(centerX, centerY, particleSize / 2, 0, Math.PI * 2);
                context.fill();
                particle.anchor.set(0.5, 0.5);
                particle.x = x + (Math.random() - 0.5) * 8;
                particle.y = y + (Math.random() - 0.5) * 8;
                particle.vx = Math.cos(angle) * speed;
                particle.vy = Math.sin(angle) * speed;
                particle.life = 18 + Math.random() * 8;
                particle.maxLife = particle.life;
                particle.rotation = Math.random() * Math.PI * 2;
                particle.rotationSpeed = (Math.random() - 0.5) * 0.3;
                this.addChild(particle);
                const animateParticle = () => {
                    if (!particle.parent) return;
                    particle.x += particle.vx;
                    particle.y += particle.vy;
                    particle.rotation += particle.rotationSpeed;
                    particle.vx *= 0.95;
                    particle.vy *= 0.95;
                    particle.life--;
                    const lifeRatio = particle.life / particle.maxLife;
                    particle.alpha = lifeRatio;
                    particle.scale.x = 0.5 + lifeRatio * 0.5;
                    particle.scale.y = 0.5 + lifeRatio * 0.5;
                    if (particle.life <= 0) {
                        if (particle.parent) this.removeChild(particle);
                    } else {
                        requestAnimationFrame(animateParticle);
                    }
                };
                requestAnimationFrame(animateParticle);
            }
        }
        fireMegaMissileSalvo() {
            if (!gameData.ship) return;

            // Check cooldown
            if (gameData.megaMissileCooldown > 0) return;

            // Fire one missile per enemy
            const enemies = gameData.enemies.filter(e => !e.destroyed);
            if (enemies.length === 0) return;

            // Set cooldown (6 seconds at 60fps)
            gameData.megaMissileCooldown = 360;

            // Fire one missile per enemy with staggered timing
            enemies.forEach((enemy, index) => {
                setTimeout(() => {
                    this.fireMegaMissileAtTarget(enemy);
                }, index * 150); // Staggered launch (0, 150ms, 300ms, etc.)
            });
        }
        fireMegaMissileAtTarget(targetEnemy) {
            if (!gameData.ship || !targetEnemy) return;

            // Fire from ship's nose
            const x = gameData.ship.x + 38;
            const y = gameData.ship.y + 30;
            const angle = Math.atan2(targetEnemy.y - y, targetEnemy.x - x);
            const speed = 16;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const proj = Projectile.getOrCreate(x, y, vx, vy, 'mega-missile', angle);
            proj.scene = this;
            proj.damage = 400;
            proj._megaMissile = true;
            proj._homing = true;
            proj._piercing = true;
            proj.scale.x = 1.2;
            proj.scale.y = 1.2;
            gameData.projectiles.push(proj);
            this.addChild(proj);

            // Play mega missile sound
            audioManager.play('megaMissile');
        }

        fireMegaMissile() {
            if (!gameData.ship) return;
            // Find nearest enemy
            let nearest = null;
            let minDist = Infinity;
            for (const enemy of gameData.enemies) {
                const dx = enemy.x - gameData.ship.x;
                const dy = enemy.y - gameData.ship.y;
                const dist = Math.sqrt(dx * dx + dy * dy);
                if (dist < minDist) {
                    minDist = dist;
                    nearest = enemy;
                }
            }
            // Fire from ship's nose
            const x = gameData.ship.x + 38;
            const y = gameData.ship.y + 30;
            let angle = 0;
            if (nearest) {
                angle = Math.atan2(nearest.y - y, nearest.x - x);
            }
            const speed = 16;
            const vx = Math.cos(angle) * speed;
            const vy = Math.sin(angle) * speed;
            const proj = Projectile.getOrCreate(x, y, vx, vy, 'mega-missile', angle);
            proj.scene = this;
            proj.damage = 400;
            proj._megaMissile = true;
            proj._homing = true;
            proj._piercing = true;
            proj.scale.x = 1.2;
            proj.scale.y = 1.2;
            gameData.projectiles.push(proj);
            this.addChild(proj);

            // Play mega missile sound
            audioManager.play('megaMissile');
        }
        // Add this method to Scene_SkyPirates

        // Add this method to clear all game objects except UI and overlays
        clearGameObjects() {
            // Remove all enemies, projectiles, clouds, powerups, and boss HP bar
            gameData.enemies.forEach(e => {
                if (e.parent) e.parent.removeChild(e);
            });
            gameData.projectiles.forEach(p => {
                if (p.parent) p.parent.removeChild(p);
            });
            gameData.clouds.forEach(c => {
                if (typeof c.destroy === 'function') c.destroy();
                else if (c.parent) c.parent.removeChild(c);
            });
            gameData.powerUps.forEach(pu => {
                if (pu.parent) pu.parent.removeChild(pu);
            });
            if (gameData.ship && gameData.ship.parent)
                gameData.ship.parent.removeChild(gameData.ship);
            if (this._bossHpBar && this._bossHpBar.parent)
                this._bossHpBar.parent.removeChild(this._bossHpBar);
        }

        updatePerformanceStats() {
            const now = performance.now();

            // Calculate FPS every 60 frames
            if (gameData.frameCount % 60 === 0) {
                const elapsed = now - gameData.lastFrameTime;
                gameData.fps = Math.round(60000 / elapsed);
                gameData.lastFrameTime = now;

                // Reset performance counters
                gameData.performanceStats.spatialQueries = 0;
                gameData.performanceStats.collisionChecks = 0;
                gameData.performanceStats.objectsCreated = 0;
                gameData.performanceStats.objectsDestroyed = 0;

                // Adaptive performance adjustments
                if (gameData.fps < 45) {
                    // Reduce visual effects when FPS is low
                    gameData.spatialUpdateInterval = Math.min(
                        20,
                        gameData.spatialUpdateInterval + 2
                    );
                    gameData.maxEnemiesOnScreen = Math.max(4, gameData.maxEnemiesOnScreen - 1);
                } else if (gameData.fps > 55) {
                    // Increase effects when FPS is good
                    gameData.spatialUpdateInterval = Math.max(
                        5,
                        gameData.spatialUpdateInterval - 1
                    );
                    gameData.maxEnemiesOnScreen = Math.min(12, gameData.maxEnemiesOnScreen + 1);
                }
            }

            // Log performance warnings
            if (gameData.fps < 30) {
                console.warn(`Low FPS detected: ${gameData.fps}. Consider reducing object count.`);
            }
        }
    }

    // Plugin commands
    PluginManager.registerCommand(pluginName, 'StartSkyPirates', () => {
        SceneManager.push(Scene_SkyPirates);
    });

    // Global test function
    window.startSkyPirates = () => {
        SceneManager.push(Scene_SkyPirates);
    };

    window.Scene_SkyPirates = Scene_SkyPirates;

    // Audio Manager for synthesized sounds
    class AudioManager {
        constructor() {
            this.audioContext = null;
            this.masterGain = null;
            this.soundGain = null;
            this.musicGain = null;
            this.initialized = false;
            this.sounds = {};
            this.musicTracks = {};
            this.currentMusic = null;
            this.volume = 0.3;
            this.musicVolume = 0.2;
            this.soundVolume = 0.4;
        }

        // Helper function to add subtle variations to repetitive sounds
        getVariation(baseValue, variationPercent = 0.15) {
            const variation = (Math.random() - 0.5) * 2 * variationPercent;
            return baseValue * (1 + variation);
        }

        init() {
            if (this.initialized) return;

            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                this.masterGain = this.audioContext.createGain();
                this.soundGain = this.audioContext.createGain();
                this.musicGain = this.audioContext.createGain();

                this.masterGain.connect(this.audioContext.destination);
                this.soundGain.connect(this.masterGain);
                this.musicGain.connect(this.masterGain);

                this.masterGain.gain.value = this.volume;
                this.soundGain.gain.value = this.soundVolume;
                this.musicGain.gain.value = this.musicVolume;

                this.initialized = true;
                this.createSounds();
            } catch (e) {
                console.log('Audio not supported:', e);
            }
        }

        createSounds() {
            // Weapon sounds - soft, satisfying
            this.sounds.laser = this.createLaserSound();
            this.sounds.plasma = this.createPlasmaSound();
            this.sounds.gatling = this.createGatlingSound();
            this.sounds.rocket = this.createRocketSound();
            this.sounds.missile = this.createMissileSound();
            this.sounds.megaMissile = this.createMegaMissileSound();

            // Impact sounds - gentle but impactful
            this.sounds.hit = this.createHitSound();
            this.sounds.explosion = this.createExplosionSound();
            this.sounds.smallExplosion = this.createSmallExplosionSound();

            // UI sounds - pleasant feedback
            this.sounds.powerup = this.createPowerupSound();
            this.sounds.levelUp = this.createLevelUpSound();
            this.sounds.bossSpawn = this.createBossSpawnSound();

            // Ambient sounds
            this.sounds.engine = this.createEngineSound();
            this.sounds.wind = this.createWindSound();
        }

        createLaserSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                const baseFreq = 800;
                const baseFreqEnd = 400;
                const baseGain = 0.1;

                // Add subtle variations
                const freqStart = this.getVariation(baseFreq, 0.12);
                const freqEnd = this.getVariation(baseFreqEnd, 0.15);
                const gainStart = this.getVariation(baseGain, 0.2);

                oscillator.frequency.setValueAtTime(freqStart, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    freqEnd,
                    this.audioContext.currentTime + 0.1
                );

                gainNode.gain.setValueAtTime(gainStart, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.1
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.1);
            };
        }

        createPlasmaSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sawtooth';
                const baseFreq = 300;
                const baseFreqEnd = 150;
                const baseGain = 0.08;
                const baseFilterStart = 2000;
                const baseFilterEnd = 500;

                // Add subtle variations
                const freqStart = this.getVariation(baseFreq, 0.1);
                const freqEnd = this.getVariation(baseFreqEnd, 0.12);
                const gainStart = this.getVariation(baseGain, 0.25);
                const filterStart = this.getVariation(baseFilterStart, 0.08);
                const filterEnd = this.getVariation(baseFilterEnd, 0.1);

                oscillator.frequency.setValueAtTime(freqStart, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    freqEnd,
                    this.audioContext.currentTime + 0.15
                );

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(filterStart, this.audioContext.currentTime);
                filter.frequency.exponentialRampToValueAtTime(
                    filterEnd,
                    this.audioContext.currentTime + 0.15
                );

                gainNode.gain.setValueAtTime(gainStart, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.15
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.15);
            };
        }

        createGatlingSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'square';
                const baseFreq = 600;
                const baseFreqEnd = 400;
                const baseGain = 0.06;
                const baseFilterStart = 1500;
                const baseFilterEnd = 800;

                // Add subtle variations (slightly more variation for gatling since it's rapid-fire)
                const freqStart = this.getVariation(baseFreq, 0.15);
                const freqEnd = this.getVariation(baseFreqEnd, 0.18);
                const gainStart = this.getVariation(baseGain, 0.3);
                const filterStart = this.getVariation(baseFilterStart, 0.1);
                const filterEnd = this.getVariation(baseFilterEnd, 0.12);

                oscillator.frequency.setValueAtTime(freqStart, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    freqEnd,
                    this.audioContext.currentTime + 0.08
                );

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(filterStart, this.audioContext.currentTime);
                filter.frequency.exponentialRampToValueAtTime(
                    filterEnd,
                    this.audioContext.currentTime + 0.08
                );

                gainNode.gain.setValueAtTime(gainStart, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.08
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.08);
            };
        }

        createRocketSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'triangle';
                const baseFreq = 200;
                const baseFreqEnd = 100;
                const baseGain = 0.12;
                const baseFilterStart = 800;
                const baseFilterEnd = 400;

                // Add subtle variations
                const freqStart = this.getVariation(baseFreq, 0.1);
                const freqEnd = this.getVariation(baseFreqEnd, 0.12);
                const gainStart = this.getVariation(baseGain, 0.2);
                const filterStart = this.getVariation(baseFilterStart, 0.08);
                const filterEnd = this.getVariation(baseFilterEnd, 0.1);

                oscillator.frequency.setValueAtTime(freqStart, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    freqEnd,
                    this.audioContext.currentTime + 0.2
                );

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(filterStart, this.audioContext.currentTime);
                filter.frequency.exponentialRampToValueAtTime(
                    filterEnd,
                    this.audioContext.currentTime + 0.2
                );

                gainNode.gain.setValueAtTime(gainStart, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.2
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.2);
            };
        }

        createMissileSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                const baseFreq = 400;
                const baseFreqEnd = 200;
                const baseGain = 0.1;

                // Add subtle variations
                const freqStart = this.getVariation(baseFreq, 0.1);
                const freqEnd = this.getVariation(baseFreqEnd, 0.12);
                const gainStart = this.getVariation(baseGain, 0.2);

                oscillator.frequency.setValueAtTime(freqStart, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    freqEnd,
                    this.audioContext.currentTime + 0.12
                );

                gainNode.gain.setValueAtTime(gainStart, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.12
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.12);
            };
        }

        createMegaMissileSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sawtooth';
                oscillator.frequency.setValueAtTime(150, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    75,
                    this.audioContext.currentTime + 0.3
                );

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(600, this.audioContext.currentTime);
                filter.frequency.exponentialRampToValueAtTime(
                    300,
                    this.audioContext.currentTime + 0.3
                );

                gainNode.gain.setValueAtTime(0.15, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.3
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.3);
            };
        }

        createHitSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(300, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    150,
                    this.audioContext.currentTime + 0.1
                );

                gainNode.gain.setValueAtTime(0.08, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.1
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.1);
            };
        }

        createExplosionSound() {
            return () => {
                if (!this.initialized) return;

                const noise = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                // Create noise buffer
                const bufferSize = this.audioContext.sampleRate * 0.3;
                const buffer = this.audioContext.createBuffer(
                    1,
                    bufferSize,
                    this.audioContext.sampleRate
                );
                const output = buffer.getChannelData(0);

                for (let i = 0; i < bufferSize; i++) {
                    output[i] = Math.random() * 2 - 1;
                }

                noise.buffer = buffer;
                noise.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(2000, this.audioContext.currentTime);
                filter.frequency.exponentialRampToValueAtTime(
                    200,
                    this.audioContext.currentTime + 0.3
                );

                gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.3
                );

                noise.start(this.audioContext.currentTime);
                noise.stop(this.audioContext.currentTime + 0.3);
            };
        }

        createSmallExplosionSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'triangle';
                oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    200,
                    this.audioContext.currentTime + 0.15
                );

                gainNode.gain.setValueAtTime(0.06, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.15
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.15);
            };
        }

        createPowerupSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    1200,
                    this.audioContext.currentTime + 0.2
                );

                gainNode.gain.setValueAtTime(0.08, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.2
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.2);
            };
        }

        createLevelUpSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(400, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    800,
                    this.audioContext.currentTime + 0.1
                );
                oscillator.frequency.exponentialRampToValueAtTime(
                    1200,
                    this.audioContext.currentTime + 0.2
                );

                gainNode.gain.setValueAtTime(0.1, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.3
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.3);
            };
        }

        createBossSpawnSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();

                oscillator.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sawtooth';
                oscillator.frequency.setValueAtTime(200, this.audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(
                    100,
                    this.audioContext.currentTime + 0.5
                );

                gainNode.gain.setValueAtTime(0.12, this.audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(
                    0.01,
                    this.audioContext.currentTime + 0.5
                );

                oscillator.start(this.audioContext.currentTime);
                oscillator.stop(this.audioContext.currentTime + 0.5);
            };
        }

        createEngineSound() {
            return () => {
                if (!this.initialized) return;

                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                oscillator.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(120, this.audioContext.currentTime);

                filter.type = 'lowpass';
                filter.frequency.setValueAtTime(400, this.audioContext.currentTime);

                gainNode.gain.setValueAtTime(0.02, this.audioContext.currentTime);

                oscillator.start(this.audioContext.currentTime);
                return () => {
                    oscillator.stop();
                };
            };
        }

        createWindSound() {
            return () => {
                if (!this.initialized) return;

                const noise = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                const filter = this.audioContext.createBiquadFilter();

                // Create wind noise buffer
                const bufferSize = this.audioContext.sampleRate * 0.5;
                const buffer = this.audioContext.createBuffer(
                    1,
                    bufferSize,
                    this.audioContext.sampleRate
                );
                const output = buffer.getChannelData(0);

                for (let i = 0; i < bufferSize; i++) {
                    output[i] = (Math.random() * 2 - 1) * 0.3;
                }

                noise.buffer = buffer;
                noise.loop = true;
                noise.connect(filter);
                filter.connect(gainNode);
                gainNode.connect(this.soundGain);

                filter.type = 'highpass';
                filter.frequency.setValueAtTime(800, this.audioContext.currentTime);

                gainNode.gain.setValueAtTime(0.01, this.audioContext.currentTime);

                noise.start(this.audioContext.currentTime);
                return () => {
                    noise.stop();
                };
            };
        }

        play(soundName) {
            if (this.sounds[soundName]) {
                this.sounds[soundName]();
            }
        }

        setVolume(volume) {
            this.volume = Math.max(0, Math.min(1, volume));
            if (this.masterGain) {
                this.masterGain.gain.value = this.volume;
            }
        }

        setSoundVolume(volume) {
            this.soundVolume = Math.max(0, Math.min(1, volume));
            if (this.soundGain) {
                this.soundGain.gain.value = this.soundVolume;
            }
        }

        setMusicVolume(volume) {
            this.musicVolume = Math.max(0, Math.min(1, volume));
            if (this.musicGain) {
                this.musicGain.gain.value = this.musicVolume;
            }
        }
    }

    // Global audio manager instance
    const audioManager = new AudioManager();

    // Add at the top level, after gameData or before any UI code
    const elementalNames = {
        lightning: '⚡ Lightning',
        fire: '🔥 Fire',
        ice: '❄ Ice',
    };
    const elementalColors = {
        lightning: '#FFFF00',
        fire: '#FF4500',
        ice: '#87CEEB',
    };
})();
