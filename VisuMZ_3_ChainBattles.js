//=============================================================================
// VisuStella MZ - Chain Battles
// VisuMZ_3_ChainBattles.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_3_ChainBattles = true;

var VisuMZ = VisuMZ || {};
VisuMZ.ChainBattles = VisuMZ.ChainBattles || {};
VisuMZ.ChainBattles.version = 1.01;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 3] [Version 1.01] [ChainBattles]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Chain_Battles_VisuStella_MZ
 * @base VisuMZ_0_CoreEngine
 * @base VisuMZ_1_BattleCore
 * @orderAfter VisuMZ_1_BattleCore
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * Ever wanted to have a continuous stream of battles without the victory
 * sequence appearing until the very end? The Chain Battles plugin will allow
 * RPG Maker MZ to do just that. As the player's party progresses forward, they
 * maintain their states, buffs, and debuffs. The such effects will keep their
 * stacks and turns. Chain Battles will make creating a marathon of battles
 * a possibility.
 *
 * Features include all (but not limited to) the following:
 *
 * * Queue up battles to be chained one after the other. This can be done in or
 *   out of battle. An unlimited amount of battles can be chained.
 * * Chained battles can be randomized across a pool of Troop ID's, based off
 *   the random encounter pool, or calculated through JavaScript.
 * * Battlebacks can be changed as chain battles continue to give a scenary
 *   change and a sense of progression.
 * * Any states, buffs, and/or debuffs that are applied to battlers as they
 *   transition from one battle to another will be carried over with their
 *   turn durations intact.
 * * Battle rewards such as EXP, Gold, and Drop Rates can be affected by the
 *   total number of chain battles, increasing the overall multiplier (or
 *   decreasing it if you so wish).
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Required Plugin List ------
 *
 * * VisuMZ_0_CoreEngine
 * * VisuMZ_1_BattleCore
 *
 * This plugin requires the above listed plugins to be installed inside your
 * game's Plugin Manager list in order to work. You cannot start your game with
 * this plugin enabled without the listed plugins.
 *
 * ------ Tier 3 ------
 *
 * This plugin is a Tier 3 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Chain Battle Carry Over
 * ============================================================================
 *
 * The following section will explain what happens whenever chain battles
 * occur and describe exactly what is carried over.
 *
 * ---
 *
 * HP, MP, and TP
 *
 * HP and MP, by default, are static across battles and will not reset
 * themselves at the start of each chained battle.
 *
 * TP, however, will depend. If TP is preserved, then the TP values will be
 * maintained as chain battles progress. If TP is not preserved, then, by
 * default, the battler will gain a random amount of TP at the start of each
 * chained battle.
 *
 * ---
 *
 * Turn Count
 *
 * When chaining into the next battle, the turn count will be preserved and
 * then increased by 1. This means if you end the first battle at Turn 10, then
 * you will start the second battle at Turn 11. This applies to TPB battle
 * systems as well.
 *
 * ---
 *
 * Troop Event Page Span
 *
 * If a troop event page's span is set to "battle", it will be reset at the
 * start of each chain battle. This means even if you are utilizing the same
 * conditions as before for the same page, the same page's span will be reset.
 *
 * ---
 *
 * ============================================================================
 * Extra Features
 * ============================================================================
 *
 * There are some extra features found if other VisuStella MZ plugins are found
 * present in the Plugin Manager list.
 *
 * ---
 *
 * VisuMZ_2_ClassChangeSystem
 *
 * This plugin offers bonus reward multipliers for the Class Change System's
 * CP and JP resource points earned from battle.
 *
 * ---
 *
 * VisuMZ_2_SkillLearnSystem
 *
 * This plugin offers bonus reward multipliers for the Skill Learn System's
 * AP and SP resource points earned from battle.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_4_ExtraEnemyDrops
 *
 * Extra Rewards will be carried over into subsequently chained battles instead
 * of being cleared. However, Forced Rewards will still overwrite everything.
 * Keep this in mind as you use these Extra Enemy Drops Plugin Commands.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Chain Battle Plugin Commands ===
 *
 * ---
 *
 * Chain Battle: Queue Troop ID(s)
 * - Setup the next Troop ID as a part of a chain battle.
 * - If there are multiple, one will be randomly picked.
 *
 *   Troop ID(s):
 *   - Select which Troop ID(s) to register as the next potential battle.
 *
 *   Change Battleback?:
 *   - Change the battlebacks for this queued battle?
 *
 *     Battleback 1:
 *     Battleback 2:
 *     - Filename used for the battleback image.
 *     - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * Chain Battle: Queue Encounter Pool
 * - Setup the next battle from the random encounter pool.
 * - If there are multiple, one will be randomly picked.
 *
 *   Change Battleback?:
 *   - Change the battlebacks for this queued battle?
 *
 *     Battleback 1:
 *     Battleback 2:
 *     - Filename used for the battleback image.
 *     - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * Chain Battle: Queue JavaScript ID
 * - Use JavaScript to determine which Troop ID to queue up for a chain battle.
 *
 *   JS: Troop ID:
 *   - Use JavaScript code to determine what Troop ID to queue up for a
 *     chain battle.
 *
 *   Change Battleback?:
 *   - Change the battlebacks for this queued battle?
 *
 *     Battleback 1:
 *     Battleback 2:
 *     - Filename used for the battleback image.
 *     - Leave empty if you don't wish to use one.
 *
 * ---
 *
 * Chain Battle: Clear Chains
 * - Clears any stored Chain Battles, allowing the battle to end after
 * the current one.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * General settings related to Chain Battles.
 *
 * ---
 *
 * Delay
 *
 *   Frames:
 *   - How many frames should be delayed on average?
 *
 *   Allow Fast Forward?:
 *   - Allow fast forwarding the delay by holding down the OK or Cancel
 *     buttons?
 *
 * ---
 *
 * Tracking
 *
 *   Variable: Chains:
 *   - Automatically tracks total chained battles.
 *   - Insert Variable ID '0' to not use.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: Reward Multipliers
 * ============================================================================
 *
 * Reward multipliers based on the number of Chain Battles in total.
 *
 * ---
 *
 * Settings
 *
 *   Enable Multipliers?:
 *   - Enable victory reward multipliers?
 *
 * ---
 *
 * General
 *
 *   EXP Rates:
 *   Gold Rates:
 *   Drop Rates:
 *   - What rates do you want per total chain battles?
 *   - 1.0 = 100%, 1.5 = 150%
 *
 * ---
 *
 * Compatibility > Class Change System
 *
 *   CP Rates:
 *   JP Rates:
 *   - What rates do you want per total chain battles?
 *   - 1.0 = 100%, 1.5 = 150%
 *
 * ---
 *
 * Compatibility > Skill Learn System
 *
 *   AP Rates:
 *   SP Rates:
 *   - What rates do you want per total chain battles?
 *   - 1.0 = 100%, 1.5 = 150%
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Olivia
 * * Arisu
 * * Irina
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.01: October 21, 2021
 * * Bug Fixes!
 * ** Battle win/lose branches should now carry over. Fix made by Arisu.
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameter added by Arisu:
 * *** Plugin Parameters > General > Animation > Chain Walk Forward?
 * **** Does player party perform walk up animation for chain battles?
 *
 * Version 1.00 Official Release Date: September 8, 2021
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChainBattleQueueTroop
 * @text Chain Battle: Queue Troop ID(s)
 * @desc Setup the next Troop ID as a part of a chain battle.
 * If there are multiple, one will be randomly picked.
 *
 * @arg TroopIDs:arraynum
 * @text Troop ID(s)
 * @parent Step1
 * @type troop[]
 * @desc Select which Troop ID(s) to register as the next potential battle.
 * @default ["1"]
 *
 * @arg ChangeBattleback:eval
 * @text Change Battleback?
 * @type boolean
 * @on Change
 * @off Don't Change
 * @desc Change the battlebacks for this queued battle?
 * @default false
 *
 * @arg Filename1:str
 * @text Battleback 1
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks1/
 * @require 1
 * @desc Filename used for the battleback 1 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @arg Filename2:str
 * @text Battleback 2
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks2/
 * @require 1
 * @desc Filename used for the battleback 2 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChainBattleQueueEncounter
 * @text Chain Battle: Queue Encounter Pool
 * @desc Setup the next battle from the random encounter pool.
 * If there are multiple, one will be randomly picked.
 *
 * @arg ChangeBattleback:eval
 * @text Change Battleback?
 * @type boolean
 * @on Change
 * @off Don't Change
 * @desc Change the battlebacks for this queued battle?
 * @default false
 *
 * @arg Filename1:str
 * @text Battleback 1
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks1/
 * @require 1
 * @desc Filename used for the battleback 1 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @arg Filename2:str
 * @text Battleback 2
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks2/
 * @require 1
 * @desc Filename used for the battleback 2 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChainBattleQueueJavaScript
 * @text Chain Battle: Queue JavaScript ID
 * @desc Use JavaScript to determine which Troop ID to queue
 * up for a chain battle.
 *
 * @arg calcTroopID:func
 * @text JS: Troop ID
 * @parent Step1
 * @type note
 * @desc Use JavaScript code to determine what Troop ID to queue up for a chain battle.
 * @default "// Declare Troop ID\nlet troopID = 1;\n\n// Calculations\n\n// Return Troop ID\nreturn troopID;"
 *
 * @arg ChangeBattleback:eval
 * @text Change Battleback?
 * @type boolean
 * @on Change
 * @off Don't Change
 * @desc Change the battlebacks for this queued battle?
 * @default false
 *
 * @arg Filename1:str
 * @text Battleback 1
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks1/
 * @require 1
 * @desc Filename used for the battleback 1 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @arg Filename2:str
 * @text Battleback 2
 * @parent ChangeBattleback:eval
 * @type file
 * @dir img/battlebacks2/
 * @require 1
 * @desc Filename used for the battleback 2 image.
 * Leave empty if you don't wish to use one.
 * @default
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ChainBattleClear
 * @text Chain Battle: Clear Chains
 * @desc Clears any stored Chain Battles, allowing the battle
 * to end after the current one.
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param ChainBattles
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param General:struct
 * @text General Settings
 * @type struct<General>
 * @desc General settings related to Chain Battles.
 * @default {"Delay":"","DelayFrames:num":"120","AllowFastFwd:eval":"true","Tracking":"","TrackTotalChainVariable:num":"0"}
 *
 * @param Multipliers:struct
 * @text Reward Multipliers
 * @type struct<Multipliers>
 * @desc Reward multipliers based on the number of Chain Battles in total.
 * @default {"Enable:eval":"true","General":"","ExpRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","GoldRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","DropRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","Compatibility":"","ClassChange":"VisuMZ_2_ClassChangeSystem","CpRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","JpRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","SkillLearn":"VisuMZ_2_SkillLearnSystem","ApRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]","SpRates:arraynum":"[\"1.0\",\"1.1\",\"1.3\",\"1.6\",\"2.0\",\"3.0\"]"}
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
/* ----------------------------------------------------------------------------
 * General Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~General:
 *
 * @param Animation
 *
 * @param WalkForward:eval
 * @text Chain Walk Forward?
 * @parent Animation
 * @type boolean
 * @on Walk Forward
 * @off Don't Walk
 * @desc Does player party perform walk up animation for chain battles?
 * @default true
 *
 * @param Delay
 *
 * @param DelayFrames:num
 * @text Frames
 * @parent Delay
 * @desc How many frames should be delayed on average?
 * @default 120
 *
 * @param AllowFastFwd:eval
 * @text Allow Fast Forward?
 * @parent Delay
 * @type boolean
 * @on Allow
 * @off Disallow
 * @desc Allow fast forwarding the delay by holding down the OK or Cancel buttons?
 * @default true
 *
 * @param Tracking
 *
 * @param TrackTotalChainVariable:num
 * @text Variable: Chains
 * @parent Tracking
 * @type variable
 * @desc Automatically tracks total chained battles.
 * Insert Variable ID '0' to not use.
 * @default 0
 *
 */
/* ----------------------------------------------------------------------------
 * Multipliers Settings
 * ----------------------------------------------------------------------------
 */
/*~struct~Multipliers:
 *
 * @param Enable:eval
 * @text Enable Multipliers?
 * @type boolean
 * @on Enable
 * @off Disable
 * @desc Enable victory reward multipliers?
 * @default true
 *
 * @param General
 *
 * @param ExpRates:arraynum
 * @text EXP Rates
 * @parent General
 * @type string[]
 * @desc What EXP rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param GoldRates:arraynum
 * @text Gold Rates
 * @parent General
 * @type string[]
 * @desc What gold rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param DropRates:arraynum
 * @text Drop Rates
 * @parent General
 * @type string[]
 * @desc What drop rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param Compatibility
 *
 * @param ClassChange
 * @text Class Change System
 * @parent Compatibility
 * @default VisuMZ_2_ClassChangeSystem
 *
 * @param CpRates:arraynum
 * @text CP Rates
 * @parent ClassChange
 * @type string[]
 * @desc What CP rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param JpRates:arraynum
 * @text JP Rates
 * @parent ClassChange
 * @type string[]
 * @desc What JP rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param SkillLearn
 * @text Skill Learn System
 * @parent Compatibility
 * @default VisuMZ_2_SkillLearnSystem
 *
 * @param ApRates:arraynum
 * @text AP Rates
 * @parent SkillLearn
 * @type string[]
 * @desc What AP rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 * @param SpRates:arraynum
 * @text SP Rates
 * @parent SkillLearn
 * @type string[]
 * @desc What SP rates do you want per total chain battles?
 * 1.0 = 100%, 1.5 = 150%
 * @default ["1.0","1.1","1.3","1.6","2.0","3.0"]
 *
 */
//=============================================================================

const _0x2c8f83 = _0x39d7;
(function (_0x52f2a4, _0x49ddf4) {
    const _0x593dd9 = _0x39d7,
        _0x1b57c3 = _0x52f2a4();
    while (!![]) {
        try {
            const _0x3b2b07 =
                parseInt(_0x593dd9(0x232)) / 0x1 +
                (parseInt(_0x593dd9(0x1c8)) / 0x2) * (-parseInt(_0x593dd9(0x21b)) / 0x3) +
                parseInt(_0x593dd9(0x1b2)) / 0x4 +
                -parseInt(_0x593dd9(0x238)) / 0x5 +
                (-parseInt(_0x593dd9(0x196)) / 0x6) * (-parseInt(_0x593dd9(0x1c0)) / 0x7) +
                parseInt(_0x593dd9(0x174)) / 0x8 +
                parseInt(_0x593dd9(0x18e)) / 0x9;
            if (_0x3b2b07 === _0x49ddf4) break;
            else _0x1b57c3['push'](_0x1b57c3['shift']());
        } catch (_0x363a0f) {
            _0x1b57c3['push'](_0x1b57c3['shift']());
        }
    }
})(_0x3340, 0x43be1);
var label = _0x2c8f83(0x230),
    tier = tier || 0x0,
    dependencies = ['VisuMZ_0_CoreEngine', 'VisuMZ_1_BattleCore'],
    pluginData = $plugins[_0x2c8f83(0x1dd)](function (_0x5c61cf) {
        const _0x1a2763 = _0x2c8f83;
        return (
            _0x5c61cf[_0x1a2763(0x172)] &&
            _0x5c61cf[_0x1a2763(0x213)][_0x1a2763(0x21e)]('[' + label + ']')
        );
    })[0x0];
function _0x39d7(_0x3cb349, _0x16bede) {
    const _0x334039 = _0x3340();
    return (
        (_0x39d7 = function (_0x39d77b, _0x51aff8) {
            _0x39d77b = _0x39d77b - 0x16e;
            let _0x24890d = _0x334039[_0x39d77b];
            return _0x24890d;
        }),
        _0x39d7(_0x3cb349, _0x16bede)
    );
}
((VisuMZ[label][_0x2c8f83(0x1d6)] = VisuMZ[label][_0x2c8f83(0x1d6)] || {}),
    (VisuMZ['ConvertParams'] = function (_0x1d8696, _0x38b0a0) {
        const _0x19d2d2 = _0x2c8f83;
        for (const _0x19c720 in _0x38b0a0) {
            if (_0x19c720['match'](/(.*):(.*)/i)) {
                const _0x276f36 = String(RegExp['$1']),
                    _0x2da4a9 = String(RegExp['$2'])[_0x19d2d2(0x1ce)]()[_0x19d2d2(0x1d0)]();
                let _0x581cdb, _0x328be5, _0x5b03e8;
                switch (_0x2da4a9) {
                    case _0x19d2d2(0x1fb):
                        _0x581cdb =
                            _0x38b0a0[_0x19c720] !== '' ? Number(_0x38b0a0[_0x19c720]) : 0x0;
                        break;
                    case _0x19d2d2(0x1b4):
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : []),
                            (_0x581cdb = _0x328be5[_0x19d2d2(0x22c)](_0x508057 =>
                                Number(_0x508057)
                            )));
                        break;
                    case _0x19d2d2(0x1ff):
                        _0x581cdb = _0x38b0a0[_0x19c720] !== '' ? eval(_0x38b0a0[_0x19c720]) : null;
                        break;
                    case _0x19d2d2(0x175):
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : []),
                            (_0x581cdb = _0x328be5['map'](_0x207527 => eval(_0x207527))));
                        break;
                    case _0x19d2d2(0x191):
                        _0x581cdb =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : '';
                        break;
                    case 'ARRAYJSON':
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : []),
                            (_0x581cdb = _0x328be5[_0x19d2d2(0x22c)](_0x3123e5 =>
                                JSON[_0x19d2d2(0x1c9)](_0x3123e5)
                            )));
                        break;
                    case 'FUNC':
                        _0x581cdb =
                            _0x38b0a0[_0x19c720] !== ''
                                ? new Function(JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720]))
                                : new Function(_0x19d2d2(0x23c));
                        break;
                    case _0x19d2d2(0x1f7):
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : []),
                            (_0x581cdb = _0x328be5[_0x19d2d2(0x22c)](
                                _0xc6d7c6 => new Function(JSON[_0x19d2d2(0x1c9)](_0xc6d7c6))
                            )));
                        break;
                    case _0x19d2d2(0x23a):
                        _0x581cdb = _0x38b0a0[_0x19c720] !== '' ? String(_0x38b0a0[_0x19c720]) : '';
                        break;
                    case _0x19d2d2(0x20d):
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : []),
                            (_0x581cdb = _0x328be5[_0x19d2d2(0x22c)](_0x4da469 =>
                                String(_0x4da469)
                            )));
                        break;
                    case _0x19d2d2(0x18a):
                        ((_0x5b03e8 =
                            _0x38b0a0[_0x19c720] !== ''
                                ? JSON[_0x19d2d2(0x1c9)](_0x38b0a0[_0x19c720])
                                : {}),
                            (_0x581cdb = VisuMZ[_0x19d2d2(0x1c5)]({}, _0x5b03e8)));
                        break;
                    case _0x19d2d2(0x1a5):
                        ((_0x328be5 =
                            _0x38b0a0[_0x19c720] !== '' ? JSON['parse'](_0x38b0a0[_0x19c720]) : []),
                            (_0x581cdb = _0x328be5[_0x19d2d2(0x22c)](_0x82b38e =>
                                VisuMZ[_0x19d2d2(0x1c5)]({}, JSON[_0x19d2d2(0x1c9)](_0x82b38e))
                            )));
                        break;
                    default:
                        continue;
                }
                _0x1d8696[_0x276f36] = _0x581cdb;
            }
        }
        return _0x1d8696;
    }),
    (_0x29a634 => {
        const _0x56efaa = _0x2c8f83,
            _0x33534b = _0x29a634[_0x56efaa(0x21c)];
        for (const _0x427aa5 of dependencies) {
            if (!Imported[_0x427aa5]) {
                (alert(_0x56efaa(0x185)[_0x56efaa(0x1db)](_0x33534b, _0x427aa5)),
                    SceneManager[_0x56efaa(0x1bf)]());
                break;
            }
        }
        const _0xf82a92 = _0x29a634[_0x56efaa(0x213)];
        if (_0xf82a92['match'](/\[Version[ ](.*?)\]/i)) {
            const _0x4d3804 = Number(RegExp['$1']);
            _0x4d3804 !== VisuMZ[label][_0x56efaa(0x16e)] &&
                (alert(_0x56efaa(0x1a8)['format'](_0x33534b, _0x4d3804)),
                SceneManager[_0x56efaa(0x1bf)]());
        }
        if (_0xf82a92['match'](/\[Tier[ ](\d+)\]/i)) {
            const _0x20e2e5 = Number(RegExp['$1']);
            _0x20e2e5 < tier
                ? 'MDltU' === _0x56efaa(0x1c1)
                    ? (alert(
                          '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.'[
                              _0x56efaa(0x1db)
                          ](_0x33534b, _0x20e2e5, tier)
                      ),
                      SceneManager['exit']())
                    : this[_0x56efaa(0x178)]()
                : (tier = Math[_0x56efaa(0x217)](_0x20e2e5, tier));
        }
        VisuMZ[_0x56efaa(0x1c5)](VisuMZ[label][_0x56efaa(0x1d6)], _0x29a634[_0x56efaa(0x17f)]);
    })(pluginData),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x193)] = function (_0x4331d0, _0x2c34a4) {
        const _0xb00fc0 = _0x2c8f83,
            _0x1a9892 = {
                troopId: _0x4331d0,
                changeBattleback: _0x2c34a4['ChangeBattleback'],
                battleback1: _0x2c34a4[_0xb00fc0(0x203)],
                battleback2: _0x2c34a4['Filename2'],
            };
        $gameTemp['setupChainBattleSettings'](_0x1a9892);
    }),
    PluginManager[_0x2c8f83(0x1e1)](pluginData['name'], _0x2c8f83(0x1ac), _0x217ad4 => {
        const _0x461645 = _0x2c8f83;
        VisuMZ['ConvertParams'](_0x217ad4, _0x217ad4);
        const _0xee39ab = _0x217ad4[_0x461645(0x1e6)],
            _0x154acb = _0xee39ab[Math[_0x461645(0x236)](_0xee39ab[_0x461645(0x1a1)])];
        if (_0x154acb > 0x0) {
            if (_0x461645(0x1ec) !== _0x461645(0x1ec)) {
                let _0x2ac30b =
                    _0x138590[_0x461645(0x230)]['Game_Troop_skillPointsTotal']['call'](this);
                if (_0x5545ae[_0x461645(0x239)]()) {
                    let _0x2ca8aa = _0x1f79a4[_0x461645(0x176)](),
                        _0x3390ba = _0x4d38c5[_0x461645(0x18d)],
                        _0x35bae9 = _0x3390ba[_0x2ca8aa] ?? _0x3390ba[_0x3390ba['length'] - 0x1];
                    _0x2ac30b *= _0x35bae9;
                }
                return _0x3622eb[_0x461645(0x1da)](_0x2ac30b);
            } else VisuMZ['ChainBattles'][_0x461645(0x193)](_0x154acb, _0x217ad4);
        }
    }),
    PluginManager[_0x2c8f83(0x1e1)](pluginData['name'], _0x2c8f83(0x1d3), _0x133534 => {
        const _0x251f9c = _0x2c8f83;
        VisuMZ[_0x251f9c(0x1c5)](_0x133534, _0x133534);
        let _0x122384 = 0x0;
        if (BattleManager[_0x251f9c(0x1a7)]()) _0x122384 = $gameTroop[_0x251f9c(0x1ba)];
        else {
            if ('eAica' !== _0x251f9c(0x1c4)) return _0x3998ba[_0x251f9c(0x200)];
            else _0x122384 = $gamePlayer['makeEncounterTroopId']();
        }
        _0x122384 > 0x0 && VisuMZ[_0x251f9c(0x230)][_0x251f9c(0x193)](_0x122384, _0x133534);
    }),
    PluginManager[_0x2c8f83(0x1e1)](pluginData[_0x2c8f83(0x21c)], _0x2c8f83(0x1c7), _0x1cffde => {
        const _0x331233 = _0x2c8f83;
        VisuMZ[_0x331233(0x1c5)](_0x1cffde, _0x1cffde);
        const _0x277b86 = _0x1cffde[_0x331233(0x17b)]();
        _0x277b86 > 0x0 && VisuMZ[_0x331233(0x230)][_0x331233(0x193)](_0x277b86, _0x1cffde);
    }),
    PluginManager[_0x2c8f83(0x1e1)](pluginData[_0x2c8f83(0x21c)], _0x2c8f83(0x220), _0x36979c => {
        $gameTemp['clearChainBattleSettings']();
    }),
    (BattleManager[_0x2c8f83(0x234)] =
        VisuMZ['ChainBattles'][_0x2c8f83(0x1d6)][_0x2c8f83(0x20a)][_0x2c8f83(0x179)]),
    (BattleManager['CHAIN_BATTLE_DELAY_FRAMES'] =
        VisuMZ['ChainBattles'][_0x2c8f83(0x1d6)]['General'][_0x2c8f83(0x223)]),
    (BattleManager[_0x2c8f83(0x1d8)] = 0x1),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x235)] = BattleManager[_0x2c8f83(0x22d)]),
    (BattleManager[_0x2c8f83(0x22d)] = function () {
        const _0x1bfbb4 = _0x2c8f83;
        (VisuMZ[_0x1bfbb4(0x230)][_0x1bfbb4(0x235)][_0x1bfbb4(0x1ab)](this),
            (this[_0x1bfbb4(0x18c)] = ![]),
            (this['_chainBattleDelayDuration'] = 0x0),
            (this[_0x1bfbb4(0x226)] = ![]));
    }),
    (VisuMZ['ChainBattles'][_0x2c8f83(0x1d7)] = BattleManager[_0x2c8f83(0x1fc)]),
    (BattleManager[_0x2c8f83(0x1fc)] = function (_0x25ddf4) {
        const _0x212fa3 = _0x2c8f83;
        ($gameTemp[_0x212fa3(0x1be)](),
            $gameTroop['clearCarryOverChainBattleDeadMembers'](),
            VisuMZ[_0x212fa3(0x230)]['BattleManager_endBattle'][_0x212fa3(0x1ab)](this, _0x25ddf4),
            $gameTroop['clearChainBattleData']());
    }),
    (VisuMZ['ChainBattles']['BattleManager_checkBattleEnd'] = BattleManager[_0x2c8f83(0x1ad)]),
    (BattleManager[_0x2c8f83(0x1ad)] = function () {
        const _0x16ac9b = _0x2c8f83;
        if (
            !this[_0x16ac9b(0x22a)]() &&
            !$gameParty[_0x16ac9b(0x1fa)]() &&
            $gameTroop[_0x16ac9b(0x1fa)]()
        ) {
            if ('XTuTv' === _0x16ac9b(0x1a0)) {
                const _0x39787e = $gameTemp[_0x16ac9b(0x19b)]();
                if (_0x39787e) return (this['setupChainBattle'](), ![]);
            } else
                (_0x53ea68[_0x16ac9b(0x1be)](),
                    _0x3c9be4[_0x16ac9b(0x171)](),
                    _0x5116b8[_0x16ac9b(0x230)][_0x16ac9b(0x1d7)]['call'](this, _0x30d5d8),
                    _0x480256[_0x16ac9b(0x20e)]());
        }
        return VisuMZ[_0x16ac9b(0x230)]['BattleManager_checkBattleEnd'][_0x16ac9b(0x1ab)](this);
    }),
    (BattleManager[_0x2c8f83(0x19e)] = function () {
        const _0x12c402 = _0x2c8f83;
        if (this[_0x12c402(0x23e)]()) {
            let _0x29330e = BattleManager[_0x12c402(0x1d8)];
            while (_0x29330e--) {
                this['endTurn']();
            }
        }
        SceneManager[_0x12c402(0x1b3)]['closeChainBattleWindows']();
        ((this[_0x12c402(0x18c)] = !![]),
            (this[_0x12c402(0x190)] = BattleManager[_0x12c402(0x197)]),
            this[_0x12c402(0x190)] <= 0x0 && this['gotoChainBattle']());
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x182)] = BattleManager[_0x2c8f83(0x1f4)]),
    (BattleManager[_0x2c8f83(0x1f4)] = function (_0x54cf8f) {
        const _0x51e638 = _0x2c8f83;
        if (this['_chainBattleTransition']) {
            this[_0x51e638(0x18f)]();
            return;
        }
        VisuMZ[_0x51e638(0x230)][_0x51e638(0x182)][_0x51e638(0x1ab)](this, _0x54cf8f);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1a3)] = BattleManager[_0x2c8f83(0x1ea)]),
    (BattleManager['updateTurn'] = function (_0x499fdf) {
        const _0x469feb = _0x2c8f83;
        if (this[_0x469feb(0x18c)]) return;
        VisuMZ['ChainBattles'][_0x469feb(0x1a3)][_0x469feb(0x1ab)](this, _0x499fdf);
    }),
    (BattleManager[_0x2c8f83(0x18f)] = function () {
        const _0xc9b844 = _0x2c8f83;
        BattleManager['CHAIN_BATTLE_DELAY_FAST_FWD'] &&
            (Input['isPressed']('ok') ||
                Input[_0xc9b844(0x1b1)](_0xc9b844(0x1bb)) ||
                TouchInput[_0xc9b844(0x1b1)]()) &&
            (this[_0xc9b844(0x190)] -= 0x3);
        if (this[_0xc9b844(0x190)]-- <= 0x0) {
            if (_0xc9b844(0x1f2) !== _0xc9b844(0x209)) this['gotoChainBattle']();
            else {
                if (this['_chainBattleTransition']) return;
                _0x1534fd[_0xc9b844(0x230)][_0xc9b844(0x1a9)][_0xc9b844(0x1ab)](this);
            }
        }
    }),
    (BattleManager[_0x2c8f83(0x1e0)] = function () {
        SceneManager['goto'](Scene_ChainBattleTransition);
    }),
    (VisuMZ[_0x2c8f83(0x230)]['BattleManager_makeRewards'] = BattleManager['makeRewards']),
    (BattleManager[_0x2c8f83(0x1ca)] = function () {
        const _0x2e445a = _0x2c8f83;
        ($gameTroop[_0x2e445a(0x218)](),
            VisuMZ[_0x2e445a(0x230)]['BattleManager_makeRewards'][_0x2e445a(0x1ab)](this));
    }),
    (Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x1f6)] = function (_0x49336f) {
        const _0x28e0a9 = _0x2c8f83;
        ((this[_0x28e0a9(0x233)] = this[_0x28e0a9(0x233)] || []),
            this[_0x28e0a9(0x233)]['push'](JsonEx[_0x28e0a9(0x1e2)](_0x49336f)));
    }),
    (Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x1be)] = function () {
        const _0x4893a8 = _0x2c8f83;
        this[_0x4893a8(0x233)] = undefined;
    }),
    (Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x207)] = function () {
        const _0xf8fded = _0x2c8f83;
        ((this[_0xf8fded(0x233)] = this[_0xf8fded(0x233)] || []),
            this[_0xf8fded(0x233)]['shift']());
    }),
    (Game_Temp['prototype'][_0x2c8f83(0x19b)] = function () {
        const _0x1b448f = _0x2c8f83;
        return this['_chainBattleQueue'] ? this[_0x1b448f(0x233)][0x0] : null;
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1a9)] = Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x1cf)]),
    (Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x1cf)] = function () {
        const _0x3677d7 = _0x2c8f83;
        if (this[_0x3677d7(0x18c)]) return;
        VisuMZ[_0x3677d7(0x230)][_0x3677d7(0x1a9)]['call'](this);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1f0)] =
        Game_Temp[_0x2c8f83(0x208)]['applyForcedGameTroopSettingsCoreEngine']),
    (Game_Temp[_0x2c8f83(0x208)][_0x2c8f83(0x204)] = function (_0x283bb1) {
        const _0x894e2e = _0x2c8f83;
        if (this[_0x894e2e(0x18c)]) return;
        VisuMZ[_0x894e2e(0x230)][_0x894e2e(0x1f0)][_0x894e2e(0x1ab)](this, _0x283bb1);
    }),
    (Game_System[_0x2c8f83(0x1cd)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x1fd)]),
    (Game_System[_0x2c8f83(0x170)] =
        VisuMZ[_0x2c8f83(0x230)]['Settings'][_0x2c8f83(0x20a)][_0x2c8f83(0x1cc)]),
    (Game_System[_0x2c8f83(0x208)]['isChainBonusEnabled'] = function () {
        const _0x4d60a0 = _0x2c8f83;
        return SceneManager[_0x4d60a0(0x221)]() && Game_System[_0x4d60a0(0x1cd)];
    }),
    (Game_System['prototype']['updateTotalChainBattlesVariable'] = function () {
        const _0x303dce = _0x2c8f83;
        if (Game_System[_0x303dce(0x170)] <= 0x0) return;
        if (!$gameTroop) return;
        const _0xd02c2f = Game_System['CHAIN_BATTLE_TOTAL_VARIABLE'],
            _0xb4f142 = $gameTroop[_0x303dce(0x176)]();
        $gameVariables[_0x303dce(0x23d)](_0xd02c2f, _0xb4f142);
    }),
    (VisuMZ[_0x2c8f83(0x230)]['Game_Battler_onBattleEnd'] =
        Game_Battler[_0x2c8f83(0x208)][_0x2c8f83(0x1e3)]),
    (Game_Battler[_0x2c8f83(0x208)]['onBattleEnd'] = function () {
        const _0xa518f7 = _0x2c8f83,
            _0x4f0d41 = $gameTemp[_0xa518f7(0x19b)]();
        if (_0x4f0d41) return;
        VisuMZ[_0xa518f7(0x230)][_0xa518f7(0x20b)]['call'](this);
    }),
    (VisuMZ[_0x2c8f83(0x230)]['Game_Enemy_dropItemRate'] = Game_Enemy['prototype']['dropItemRate']),
    (Game_Enemy[_0x2c8f83(0x208)][_0x2c8f83(0x1b5)] = function () {
        const _0x5bcc9d = _0x2c8f83;
        let _0x218a74 = VisuMZ[_0x5bcc9d(0x230)][_0x5bcc9d(0x228)]['call'](this);
        if ($gameSystem[_0x5bcc9d(0x239)]()) {
            let _0x3a394e = $gameTroop['getTotalChainBattles'](),
                _0x367f88 = Game_Troop['CHAIN_BATTLE_BONUS_DROP_RATE'],
                _0x2c865e = _0x367f88[_0x3a394e] ?? _0x367f88[_0x367f88[_0x5bcc9d(0x1a1)] - 0x1];
            _0x218a74 *= _0x2c865e;
        }
        return _0x218a74;
    }),
    (Game_Unit[_0x2c8f83(0x208)][_0x2c8f83(0x218)] = function () {
        const _0x569bbf = _0x2c8f83;
        this[_0x569bbf(0x1fe)] = !![];
    }),
    (Game_Unit[_0x2c8f83(0x208)][_0x2c8f83(0x171)] = function () {
        const _0x258691 = _0x2c8f83;
        this[_0x258691(0x1fe)] = ![];
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1ee)] = Game_Unit[_0x2c8f83(0x208)][_0x2c8f83(0x1c2)]),
    (Game_Unit[_0x2c8f83(0x208)][_0x2c8f83(0x1c2)] = function () {
        const _0x542ec8 = _0x2c8f83;
        let _0x42bea3 = VisuMZ[_0x542ec8(0x230)][_0x542ec8(0x1ee)][_0x542ec8(0x1ab)](this);
        if (this[_0x542ec8(0x17d)] === Game_Troop && this[_0x542ec8(0x1fe)]) {
            if ('mopRU' !== _0x542ec8(0x1f8))
                return _0x427df4[_0x542ec8(0x230)][_0x542ec8(0x205)][_0x542ec8(0x1ab)](this);
            else
                ((this[_0x542ec8(0x177)] = this['_carryOverDeadMembers'] || []),
                    (_0x42bea3 = this[_0x542ec8(0x177)][_0x542ec8(0x222)](_0x42bea3)));
        }
        return _0x42bea3;
    }),
    (Game_Troop[_0x2c8f83(0x1dc)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x1d4)]),
    (Game_Troop[_0x2c8f83(0x23f)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x224)]),
    (Game_Troop['CHAIN_BATTLE_BONUS_DROP_RATE'] =
        VisuMZ['ChainBattles'][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x1d1)]),
    Game_Troop[_0x2c8f83(0x1dc)][_0x2c8f83(0x201)](0x1),
    Game_Troop[_0x2c8f83(0x23f)]['unshift'](0x1),
    Game_Troop[_0x2c8f83(0x16f)][_0x2c8f83(0x201)](0x1),
    (Game_Troop[_0x2c8f83(0x215)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)]['Multipliers']['ApRates']),
    (Game_Troop[_0x2c8f83(0x18d)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x1b0)]),
    Game_Troop[_0x2c8f83(0x215)]['unshift'](0x1),
    Game_Troop[_0x2c8f83(0x18d)][_0x2c8f83(0x201)](0x1),
    (Game_Troop['CHAIN_BATTLE_BONUS_CP'] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)][_0x2c8f83(0x225)][_0x2c8f83(0x19f)]),
    (Game_Troop[_0x2c8f83(0x192)] =
        VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1d6)]['Multipliers'][_0x2c8f83(0x1e8)]),
    Game_Troop[_0x2c8f83(0x22e)]['unshift'](0x1),
    Game_Troop[_0x2c8f83(0x192)][_0x2c8f83(0x201)](0x1),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x211)] = Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x19c)]),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x19c)] = function () {
        const _0x52a39e = _0x2c8f83;
        (VisuMZ[_0x52a39e(0x230)]['Game_Troop_clear'][_0x52a39e(0x1ab)](this),
            this['clearChainBattleData']());
    }),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x20e)] = function () {
        const _0x168d08 = _0x2c8f83;
        ((this[_0x168d08(0x177)] = []),
            (this[_0x168d08(0x1fe)] = ![]),
            (this[_0x168d08(0x21d)] = 0x0),
            $gameSystem[_0x168d08(0x1f5)](),
            (this[_0x168d08(0x180)] = ![]),
            (this['_chainBattleback1'] = ''),
            (this[_0x168d08(0x200)] = ''));
    }),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1ed)] = function (_0x38c550) {
        const _0x5589b5 = _0x2c8f83;
        _0x38c550[_0x5589b5(0x1eb)] = this[_0x5589b5(0x1eb)]();
        if (BattleManager['isTpb']()) _0x38c550[_0x5589b5(0x1eb)] += 0x1;
        ((this[_0x5589b5(0x177)] = this[_0x5589b5(0x177)] || []),
            (_0x38c550[_0x5589b5(0x1c2)] = _0x38c550[_0x5589b5(0x1c2)] || []),
            (_0x38c550[_0x5589b5(0x1c2)] = this[_0x5589b5(0x177)][_0x5589b5(0x222)](
                _0x38c550[_0x5589b5(0x1c2)]
            )),
            (_0x38c550[_0x5589b5(0x1c2)] = _0x38c550['deadMembers'][_0x5589b5(0x222)](
                $gameTroop['deadMembers']()
            )),
            (this['_chainBattleTotal'] = this['_chainBattleTotal'] || 0x1),
            (this['_chainBattleTotal'] += 0x1),
            (_0x38c550['chainBattleTotal'] = this['_chainBattleTotal']),
            $gameSystem['updateTotalChainBattlesVariable'](),
            (_0x38c550[_0x5589b5(0x1f1)] = this[_0x5589b5(0x22b)]),
            (_0x38c550[_0x5589b5(0x212)] = this[_0x5589b5(0x1aa)]));
    }),
    (Game_Troop['prototype'][_0x2c8f83(0x176)] = function () {
        const _0x34be5b = _0x2c8f83;
        return ((this[_0x34be5b(0x21d)] = this[_0x34be5b(0x21d)] || 0x1), this[_0x34be5b(0x21d)]);
    }),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x181)] = function (_0x5afb6b) {
        const _0x3b3c55 = _0x2c8f83;
        ((this[_0x3b3c55(0x17a)] = _0x5afb6b['turnCount']),
            (this[_0x3b3c55(0x177)] = _0x5afb6b[_0x3b3c55(0x1c2)]),
            (this['_chainBattleTotal'] = _0x5afb6b['chainBattleTotal'] || 0x1),
            $gameSystem[_0x3b3c55(0x1f5)](),
            (this[_0x3b3c55(0x22b)] = _0x5afb6b[_0x3b3c55(0x1f1)]),
            (this[_0x3b3c55(0x1aa)] = _0x5afb6b['bonusRewards']),
            (this[_0x3b3c55(0x180)] = _0x5afb6b[_0x3b3c55(0x22f)]),
            (this['_chainBattleback1'] = _0x5afb6b[_0x3b3c55(0x21f)]),
            (this[_0x3b3c55(0x200)] = _0x5afb6b['battleback2']));
        if (BattleManager[_0x3b3c55(0x184)]())
            for (const _0x233620 of this[_0x3b3c55(0x1d9)]()) {
                if ('pcCrF' === _0x3b3c55(0x17e)) this[_0x3b3c55(0x1e0)]();
                else {
                    if (!_0x233620) continue;
                    _0x233620['_tpbTurnCount'] = this[_0x3b3c55(0x17a)];
                }
            }
    }),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x206)] = function () {
        const _0x460c12 = _0x2c8f83;
        $gameTemp[_0x460c12(0x1ae)]() &&
            console[_0x460c12(0x227)](
                this[_0x460c12(0x1c2)]()[_0x460c12(0x22c)](_0x31b67c =>
                    _0x31b67c[_0x460c12(0x21c)]()
                )
            );
    }),
    (VisuMZ[_0x2c8f83(0x230)]['Game_Troop_expRate'] =
        Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x186)]),
    (Game_Troop['prototype'][_0x2c8f83(0x186)] = function () {
        const _0x493140 = _0x2c8f83;
        let _0x347092 = VisuMZ[_0x493140(0x230)]['Game_Troop_expRate'][_0x493140(0x1ab)](this);
        if ($gameSystem[_0x493140(0x239)]()) {
            if ('kIVAa' === _0x493140(0x1f3)) {
                const _0x447b54 = _0x2e6539[_0x493140(0x19b)]();
                if (_0x447b54) return;
                _0x48e369['ChainBattles'][_0x493140(0x20b)][_0x493140(0x1ab)](this);
            } else {
                let _0x4ce9a0 = $gameTroop[_0x493140(0x176)](),
                    _0x3078e1 = Game_Troop[_0x493140(0x1dc)],
                    _0xd07a3e =
                        _0x3078e1[_0x4ce9a0] ?? _0x3078e1[_0x3078e1[_0x493140(0x1a1)] - 0x1];
                _0x347092 *= _0xd07a3e;
            }
        }
        return Math['ceil'](_0x347092);
    }),
    (VisuMZ[_0x2c8f83(0x230)]['Game_Troop_goldRate'] = Game_Troop['prototype'][_0x2c8f83(0x1b9)]),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1b9)] = function () {
        const _0x52433d = _0x2c8f83;
        let _0xff3741 = VisuMZ['ChainBattles']['Game_Troop_goldRate'][_0x52433d(0x1ab)](this);
        if ($gameSystem['isChainBonusEnabled']()) {
            let _0x27bb2b = $gameTroop[_0x52433d(0x176)](),
                _0x369c9d = Game_Troop[_0x52433d(0x23f)],
                _0x45fbf9 = _0x369c9d[_0x27bb2b] ?? _0x369c9d[_0x369c9d['length'] - 0x1];
            _0xff3741 *= _0x45fbf9;
        }
        return _0xff3741;
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1bc)] = Game_Troop['prototype'][_0x2c8f83(0x231)]),
    (Game_Troop[_0x2c8f83(0x208)]['abilityPointsTotal'] = function () {
        const _0x220729 = _0x2c8f83;
        let _0x2b91e3 =
            VisuMZ[_0x220729(0x230)]['Game_Troop_abilityPointsTotal'][_0x220729(0x1ab)](this);
        if ($gameSystem[_0x220729(0x239)]()) {
            let _0x59bfb = $gameTroop[_0x220729(0x176)](),
                _0x431682 = Game_Troop['CHAIN_BATTLE_BONUS_AP'],
                _0x1b2b0c = _0x431682[_0x59bfb] ?? _0x431682[_0x431682[_0x220729(0x1a1)] - 0x1];
            _0x2b91e3 *= _0x1b2b0c;
        }
        return Math['ceil'](_0x2b91e3);
    }),
    (VisuMZ['ChainBattles'][_0x2c8f83(0x199)] = Game_Troop[_0x2c8f83(0x208)]['skillPointsTotal']),
    (Game_Troop[_0x2c8f83(0x208)]['skillPointsTotal'] = function () {
        const _0x290eaa = _0x2c8f83;
        let _0x27eea1 = VisuMZ[_0x290eaa(0x230)][_0x290eaa(0x199)][_0x290eaa(0x1ab)](this);
        if ($gameSystem['isChainBonusEnabled']()) {
            if ('HMVwW' === 'SBmaR') {
                let _0x5859b0 = _0x57cb9b[_0x290eaa(0x230)]['Game_Troop_goldRate']['call'](this);
                if (_0x5c748b['isChainBonusEnabled']()) {
                    let _0x506019 = _0x37895b[_0x290eaa(0x176)](),
                        _0x3ce375 = _0x47b29b['CHAIN_BATTLE_BONUS_GOLD'],
                        _0x858aec = _0x3ce375[_0x506019] ?? _0x3ce375[_0x3ce375['length'] - 0x1];
                    _0x5859b0 *= _0x858aec;
                }
                return _0x5859b0;
            } else {
                let _0x5c2cb6 = $gameTroop[_0x290eaa(0x176)](),
                    _0x5ed66e = Game_Troop[_0x290eaa(0x18d)],
                    _0x43fb7f = _0x5ed66e[_0x5c2cb6] ?? _0x5ed66e[_0x5ed66e['length'] - 0x1];
                _0x27eea1 *= _0x43fb7f;
            }
        }
        return Math[_0x290eaa(0x1da)](_0x27eea1);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1de)] = Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1cb)]),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1cb)] = function () {
        const _0x2dac43 = _0x2c8f83;
        let _0x578374 = VisuMZ['ChainBattles'][_0x2dac43(0x1de)]['call'](this);
        if ($gameSystem['isChainBonusEnabled']()) {
            let _0x4acf9b = $gameTroop[_0x2dac43(0x176)](),
                _0x3bf7a1 = Game_Troop[_0x2dac43(0x22e)],
                _0x1ab8e4 = _0x3bf7a1[_0x4acf9b] ?? _0x3bf7a1[_0x3bf7a1['length'] - 0x1];
            _0x578374 *= _0x1ab8e4;
        }
        return Math['ceil'](_0x578374);
    }),
    (VisuMZ[_0x2c8f83(0x230)]['Game_Troop_jobPointsTotal'] =
        Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1bd)]),
    (Game_Troop[_0x2c8f83(0x208)][_0x2c8f83(0x1bd)] = function () {
        const _0x411a77 = _0x2c8f83;
        let _0x3cd7fc = VisuMZ[_0x411a77(0x230)][_0x411a77(0x21a)][_0x411a77(0x1ab)](this);
        if ($gameSystem[_0x411a77(0x239)]()) {
            if ('OeQQz' === 'OeQQz') {
                let _0x2314e7 = $gameTroop[_0x411a77(0x176)](),
                    _0x1376ab = Game_Troop['CHAIN_BATTLE_BONUS_JP'],
                    _0x42b2e5 = _0x1376ab[_0x2314e7] ?? _0x1376ab[_0x1376ab['length'] - 0x1];
                _0x3cd7fc *= _0x42b2e5;
            } else
                (_0x1c8f78(_0x411a77(0x202)[_0x411a77(0x1db)](_0x4e6b91, _0x4cfd08, _0xff753a)),
                    _0x23aecb[_0x411a77(0x1bf)]());
        }
        return Math[_0x411a77(0x1da)](_0x3cd7fc);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x183)] = Scene_Battle[_0x2c8f83(0x208)][_0x2c8f83(0x187)]),
    (Scene_Battle[_0x2c8f83(0x208)]['isAnyInputWindowActive'] = function () {
        const _0x32770a = _0x2c8f83;
        if (BattleManager[_0x32770a(0x18c)]) return !![];
        return VisuMZ['ChainBattles'][_0x32770a(0x183)][_0x32770a(0x1ab)](this);
    }),
    (Scene_Battle[_0x2c8f83(0x208)][_0x2c8f83(0x237)] = function () {
        const _0x172ce2 = _0x2c8f83,
            _0x13a719 = [
                this[_0x172ce2(0x1a6)],
                this[_0x172ce2(0x1b6)],
                this[_0x172ce2(0x1d5)],
                this[_0x172ce2(0x1e9)],
                this[_0x172ce2(0x1b7)],
                this[_0x172ce2(0x19d)],
            ];
        for (const _0x2e87a3 of _0x13a719) {
            _0x2e87a3[_0x172ce2(0x1e4)] &&
                (_0x172ce2(0x20c) !== _0x172ce2(0x1df)
                    ? (_0x2e87a3[_0x172ce2(0x229)](), _0x2e87a3['close']())
                    : _0x108c5e[_0x172ce2(0x1e4)] &&
                      (_0x52927b['deactivate'](), _0x41b532[_0x172ce2(0x1d2)]()));
        }
    }));
function Scene_ChainBattleTransition() {
    const _0x328694 = _0x2c8f83;
    this[_0x328694(0x216)](...arguments);
}
function _0x3340() {
    const _0x5cbfa5 = [
        'ARRAYFUNC',
        'mopRU',
        'Sprite_Actor_moveToStartPositionBattleCore',
        'isAllDead',
        'NUM',
        'endBattle',
        'Enable',
        '_carryDeadMembersFlag',
        'EVAL',
        '_chainBattleback2',
        'unshift',
        '%1\x20is\x20incorrectly\x20placed\x20on\x20the\x20plugin\x20list.\x0aIt\x20is\x20a\x20Tier\x20%2\x20plugin\x20placed\x20over\x20other\x20Tier\x20%3\x20plugins.\x0aPlease\x20reorder\x20the\x20plugin\x20list\x20from\x20smallest\x20to\x20largest\x20tier\x20numbers.',
        'Filename1',
        'applyForcedGameTroopSettingsCoreEngine',
        'Sprite_Battleback_battleback1Name',
        'debugChainBattleDeadMemberList',
        'shiftChainBattleQueue',
        'prototype',
        'hyKrS',
        'General',
        'Game_Battler_onBattleEnd',
        'xAhSn',
        'ARRAYSTR',
        'clearChainBattleData',
        'create',
        'WalkForward',
        'Game_Troop_clear',
        'bonusRewards',
        'description',
        '_canLose',
        'CHAIN_BATTLE_BONUS_AP',
        'initialize',
        'max',
        'addCarryOverChainBattleDeadMembers',
        'setupNextChainBattle',
        'Game_Troop_jobPointsTotal',
        '9RxaZny',
        'name',
        '_chainBattleTotal',
        'includes',
        'battleback1',
        'ChainBattleClear',
        'isSceneBattle',
        'concat',
        'DelayFrames',
        'GoldRates',
        'Multipliers',
        '_chainBattleFlag',
        'log',
        'Game_Enemy_dropItemRate',
        'deactivate',
        'checkAbort',
        '_forcedRewards',
        'map',
        'initMembers',
        'CHAIN_BATTLE_BONUS_CP',
        'changeBattleback',
        'ChainBattles',
        'abilityPointsTotal',
        '346967KadkIk',
        '_chainBattleQueue',
        'CHAIN_BATTLE_DELAY_FAST_FWD',
        'BattleManager_initMembers',
        'randomInt',
        'closeChainBattleWindows',
        '1950900HunLtF',
        'isChainBonusEnabled',
        'STR',
        'battleback2Name',
        'return\x200',
        'setValue',
        'isTurnBased',
        'CHAIN_BATTLE_BONUS_GOLD',
        'version',
        'CHAIN_BATTLE_BONUS_DROP_RATE',
        'CHAIN_BATTLE_TOTAL_VARIABLE',
        'clearCarryOverChainBattleDeadMembers',
        'status',
        'troopId',
        '1485896BcAEqs',
        'ARRAYEVAL',
        'getTotalChainBattles',
        '_carryOverDeadMembers',
        'endTurn',
        'AllowFastFwd',
        '_turnCount',
        'calcTroopID',
        'battleback1Name',
        'constructor',
        'VIRiV',
        'parameters',
        '_chainBattlebackUse',
        'carryOverChainBattleData',
        'BattleManager_update',
        'Scene_Battle_isAnyInputWindowActive',
        'isTpb',
        '%1\x20is\x20missing\x20a\x20required\x20plugin.\x0aPlease\x20install\x20%2\x20into\x20the\x20Plugin\x20Manager.',
        'expRate',
        'isAnyInputWindowActive',
        'getBattleSystem',
        'toLowerCase',
        'STRUCT',
        '_forcedBattleLayout',
        '_chainBattleTransition',
        'CHAIN_BATTLE_BONUS_SP',
        '326844hDUpnY',
        'updateChainBattleTransition',
        '_chainBattleDelayDuration',
        'JSON',
        'CHAIN_BATTLE_BONUS_JP',
        'SetupChainBattles',
        'BattleCore',
        '_forcedBattleSys',
        '8886cdMfeO',
        'CHAIN_BATTLE_DELAY_FRAMES',
        'battleSys',
        'Game_Troop_skillPointsTotal',
        'setup',
        'getChainBattleSettings',
        'clear',
        '_enemyWindow',
        'setupChainBattle',
        'CpRates',
        'XTuTv',
        'length',
        '_eventCallback',
        'BattleManager_updateTurn',
        'setEventCallback',
        'ARRAYSTRUCT',
        '_partyCommandWindow',
        'isBattleTest',
        '%1\x27s\x20version\x20does\x20not\x20match\x20plugin\x27s.\x20Please\x20update\x20it\x20in\x20the\x20Plugin\x20Manager.',
        'Game_Temp_clearForcedGameTroopSettingsCoreEngine',
        '_bonusRewards',
        'call',
        'ChainBattleQueueTroop',
        'checkBattleEnd',
        'isPlaytest',
        'battleLayout',
        'SpRates',
        'isPressed',
        '605052ulsfxV',
        '_scene',
        'ARRAYNUM',
        'dropItemRate',
        '_actorCommandWindow',
        '_actorWindow',
        'moveToStartPositionBattleCore',
        'goldRate',
        '_troopId',
        'cancel',
        'Game_Troop_abilityPointsTotal',
        'jobPointsTotal',
        'clearChainBattleSettings',
        'exit',
        '7MgDnwc',
        'MDltU',
        'deadMembers',
        '_chainBattleback1',
        'eAica',
        'ConvertParams',
        'start',
        'ChainBattleQueueJavaScript',
        '36074TbrqUf',
        'parse',
        'makeRewards',
        'classPointsTotal',
        'TrackTotalChainVariable',
        'CHAIN_BATTLE_BONUS_ENABLE',
        'toUpperCase',
        'clearForcedGameTroopSettingsCoreEngine',
        'trim',
        'DropRates',
        'close',
        'ChainBattleQueueEncounter',
        'ExpRates',
        '_skillWindow',
        'Settings',
        'BattleManager_endBattle',
        'CHAIN_BATTLE_TURN_COUNT_ADVANCE',
        'members',
        'ceil',
        'format',
        'CHAIN_BATTLE_BONUS_EXP',
        'filter',
        'Game_Troop_classPointsTotal',
        'YLaPk',
        'gotoChainBattle',
        'registerCommand',
        'makeDeepCopy',
        'onBattleEnd',
        'active',
        'goto',
        'TroopIDs',
        'setBattleTest',
        'JpRates',
        '_itemWindow',
        'updateTurn',
        'turnCount',
        'ckGbZ',
        'addChainBattleData',
        'Game_Unit_deadMembers',
        'Sprite_Battleback_battleback2Name',
        'Game_Temp_applyForcedGameTroopSettingsCoreEngine',
        'forcedRewards',
        'NsXPd',
        'ksuvu',
        'update',
        'updateTotalChainBattlesVariable',
        'setupChainBattleSettings',
    ];
    _0x3340 = function () {
        return _0x5cbfa5;
    };
    return _0x3340();
}
((Scene_ChainBattleTransition[_0x2c8f83(0x208)] = Object[_0x2c8f83(0x20f)](
    Scene_Base[_0x2c8f83(0x208)]
)),
    (Scene_ChainBattleTransition['prototype']['constructor'] = Scene_ChainBattleTransition),
    (Scene_ChainBattleTransition[_0x2c8f83(0x208)][_0x2c8f83(0x216)] = function () {
        Scene_Base['prototype']['initialize']['call'](this);
    }),
    (Scene_ChainBattleTransition['prototype']['start'] = function () {
        const _0x26f61a = _0x2c8f83;
        (Scene_Base[_0x26f61a(0x208)][_0x26f61a(0x1c6)][_0x26f61a(0x1ab)](this),
            this[_0x26f61a(0x1ed)](),
            this[_0x26f61a(0x219)](),
            this[_0x26f61a(0x181)](),
            this['goToBattleScene']());
    }),
    (Scene_ChainBattleTransition[_0x2c8f83(0x208)]['addChainBattleData'] = function () {
        const _0x4b4827 = _0x2c8f83,
            _0x129a5f = $gameTemp[_0x4b4827(0x19b)]();
        ($gameTroop[_0x4b4827(0x1ed)](_0x129a5f),
            (_0x129a5f[_0x4b4827(0x198)] = $gameSystem[_0x4b4827(0x188)]()),
            (_0x129a5f[_0x4b4827(0x1af)] =
                $gameTemp[_0x4b4827(0x18b)] ||
                VisuMZ[_0x4b4827(0x194)][_0x4b4827(0x1d6)]['BattleLayout']['Style']
                    [_0x4b4827(0x189)]()
                    [_0x4b4827(0x1d0)]()),
            ($gameTemp[_0x4b4827(0x18c)] = !![]));
    }),
    (Scene_ChainBattleTransition[_0x2c8f83(0x208)][_0x2c8f83(0x219)] = function () {
        const _0x4a1485 = _0x2c8f83,
            _0x3c7d44 = $gameTemp['getChainBattleSettings'](),
            _0xa02ec8 = _0x3c7d44[_0x4a1485(0x173)],
            _0x458674 = BattleManager['_canEscape'],
            _0x18af4d = BattleManager[_0x4a1485(0x214)],
            _0x25161d = BattleManager['isBattleTest'](),
            _0x248df7 = BattleManager[_0x4a1485(0x1a2)];
        (BattleManager[_0x4a1485(0x19a)](_0xa02ec8, _0x458674, _0x18af4d),
            BattleManager[_0x4a1485(0x1e7)](_0x25161d),
            BattleManager[_0x4a1485(0x1a4)](_0x248df7),
            (BattleManager[_0x4a1485(0x226)] = !![]));
    }),
    (Scene_ChainBattleTransition[_0x2c8f83(0x208)][_0x2c8f83(0x181)] = function () {
        const _0x40f4bc = _0x2c8f83,
            _0x36414e = $gameTemp[_0x40f4bc(0x19b)]();
        ($gameTroop[_0x40f4bc(0x181)](_0x36414e),
            ($gameTemp[_0x40f4bc(0x195)] = _0x36414e[_0x40f4bc(0x198)]),
            ($gameTemp[_0x40f4bc(0x18b)] = _0x36414e[_0x40f4bc(0x1af)]));
    }),
    (Scene_ChainBattleTransition['prototype']['goToBattleScene'] = function () {
        const _0x4fa556 = _0x2c8f83;
        ($gameTemp['shiftChainBattleQueue'](),
            SceneManager[_0x4fa556(0x1e5)](Scene_Battle),
            ($gameTemp[_0x4fa556(0x18c)] = ![]));
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1f9)] = Sprite_Actor[_0x2c8f83(0x208)][_0x2c8f83(0x1b8)]),
    (Sprite_Actor[_0x2c8f83(0x208)]['moveToStartPositionBattleCore'] = function (_0x4ae216) {
        const _0x42d34e = _0x2c8f83;
        if (BattleManager['_chainBattleFlag']) {
            const _0x2c3bc0 =
                VisuMZ[_0x42d34e(0x230)]['Settings'][_0x42d34e(0x20a)][_0x42d34e(0x210)] ?? !![];
            if (!_0x2c3bc0) return;
        }
        VisuMZ[_0x42d34e(0x230)][_0x42d34e(0x1f9)][_0x42d34e(0x1ab)](this, _0x4ae216);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x205)] = Sprite_Battleback['prototype'][_0x2c8f83(0x17c)]),
    (Sprite_Battleback[_0x2c8f83(0x208)][_0x2c8f83(0x17c)] = function () {
        const _0x27ca2c = _0x2c8f83;
        return $gameTroop[_0x27ca2c(0x180)]
            ? $gameTroop[_0x27ca2c(0x1c3)]
            : VisuMZ[_0x27ca2c(0x230)]['Sprite_Battleback_battleback1Name'][_0x27ca2c(0x1ab)](this);
    }),
    (VisuMZ[_0x2c8f83(0x230)][_0x2c8f83(0x1ef)] =
        Sprite_Battleback[_0x2c8f83(0x208)][_0x2c8f83(0x23b)]),
    (Sprite_Battleback['prototype'][_0x2c8f83(0x23b)] = function () {
        const _0x1e59e6 = _0x2c8f83;
        return $gameTroop[_0x1e59e6(0x180)]
            ? $gameTroop['_chainBattleback2']
            : VisuMZ[_0x1e59e6(0x230)]['Sprite_Battleback_battleback2Name'][_0x1e59e6(0x1ab)](this);
    }));
