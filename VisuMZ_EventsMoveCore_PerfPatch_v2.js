//=============================================================================
// VisuMZ_EventsMoveCore_PerfPatch_v2.js
//=============================================================================
/*:
 * @target MZ
 * @plugindesc [Patch] Advanced Performance optimizations for VisuMZ_1_EventsMoveCore.js (deobfuscated and optimized)
 * <AUTHOR> Deobfuscation Patch
 * @orderAfter VisuMZ_1_EventsMoveCore
 * @help
 * This patch optimizes VisuMZ_1_EventsMoveCore.js by:
 * 1. Throttling event updates for off-screen events.
 * 2. Caching expensive calculations (label text, sprite updates).
 * 3. Limiting maximum active events per map.
 * 4. Optimizing sprite update loops.
 * 5. Deobfuscating and fixing performance bottlenecks.
 *
 * @param eventUpdateThrottle
 * @text Event Update Throttle (frames)
 * @type number
 * @min 1
 * @desc Update off-screen events every N frames. Higher = better performance. Default: 5
 * @default 5
 *
 * @param maxActiveEvents
 * @text Max Active Events
 * @type number
 * @min 1
 * @desc Maximum number of active events per map. Extra events will be ignored. Default: 50
 * @default 50
 *
 * @param enableOffScreenSkip
 * @text Enable Off-Screen Skip
 * @type boolean
 * @desc Skip updates for events outside the visible screen area. Default: true
 * @default true
 *
 * @param enableLabelCache
 * @text Enable Label Cache
 * @type boolean
 * @desc Cache expensive label text operations. Default: true
 * @default true
 *
 * @param debugMode
 * @text Debug Mode
 * @type boolean
 * @desc Show performance information in console. Default: false
 * @default false
 */

(() => {
    'use strict';

    // Check if VisuMZ_1_EventsMoveCore is loaded
    if (!Imported.VisuMZ_1_EventsMoveCore) {
        console.warn(
            '[VisuMZ_EventsMoveCore_PerfPatch_v2] VisuMZ_1_EventsMoveCore not found. Patch disabled.'
        );
        return;
    }

    const parameters = PluginManager.parameters('VisuMZ_EventsMoveCore_PerfPatch_v2');
    const settings = {
        eventUpdateThrottle: Number(parameters.eventUpdateThrottle || 5),
        maxActiveEvents: Number(parameters.maxActiveEvents || 50),
        enableOffScreenSkip: parameters.enableOffScreenSkip !== 'false',
        enableLabelCache: parameters.enableLabelCache !== 'false',
        debugMode: parameters.debugMode === 'true',
    };

    // Debug logging
    function debugLog(message) {
        if (settings.debugMode) {
            console.log(`[VisuMZ_EventsMoveCore_PerfPatch_v2] ${message}`);
        }
    }

    // Performance tracking
    const perfStats = {
        eventsSkipped: 0,
        eventsUpdated: 0,
        labelCacheHits: 0,
        labelCacheMisses: 0,
        spriteUpdatesSkipped: 0,
    };

    // Caches
    const labelTextCache = new Map();
    const visibilityCache = new Map();

    // 🎯 OPTIMIZATION 1: Throttle event updates for off-screen events (FIXED - no movement delay)
    const _Game_Event_update = Game_Event.prototype.update;
    Game_Event.prototype.update = function () {
        // Initialize update counter
        if (!this._updateCounter) this._updateCounter = 0;
        this._updateCounter++;

        // Always update events that are always supposed to update
        if (this._alwaysUpdateMove) {
            _Game_Event_update.call(this);
            perfStats.eventsUpdated++;
            return;
        }

        // Always update events with touch triggers immediately (no delay for touch input)
        if (this._trigger === 2 || this._trigger === 3 || this._trigger === 4) {
            // Touch, Event Touch, Player Touch
            _Game_Event_update.call(this);
            perfStats.eventsUpdated++;
            return;
        }

        // CRITICAL FIX: Never throttle moving events or events with active movement routes
        if (this.isMoving() || this._moveRoute || this._moveRouteIndex !== undefined) {
            _Game_Event_update.call(this);
            perfStats.eventsUpdated++;
            return;
        }

        // CRITICAL FIX: Never throttle events that are part of active cutscenes or have active interpreters
        if (this._interpreter || $gameMap.isEventRunning() || this._starting) {
            _Game_Event_update.call(this);
            perfStats.eventsUpdated++;
            return;
        }

        // 🎯 OPTIMIZATION 2: Off-screen event skipping with caching (for static events only)
        if (settings.enableOffScreenSkip) {
            const visibilityKey = `${this._mapId}_${this._eventId}_${$gameMap._displayX}_${$gameMap._displayY}`;
            let isNearScreen = visibilityCache.get(visibilityKey);

            // Update visibility cache every 30 frames
            if (isNearScreen === undefined || this._updateCounter % 30 === 0) {
                isNearScreen = this.isNearTheScreen();
                visibilityCache.set(visibilityKey, isNearScreen);

                // Limit cache size
                if (visibilityCache.size > 100) {
                    const firstKey = visibilityCache.keys().next().value;
                    visibilityCache.delete(firstKey);
                }
            }

            if (!isNearScreen) {
                // Throttle off-screen STATIC events heavily
                if (this._updateCounter % (settings.eventUpdateThrottle * 3) !== 0) {
                    perfStats.eventsSkipped++;
                    return;
                }
            }
            // REMOVED: On-screen throttling that was causing half-speed movement
        }

        _Game_Event_update.call(this);
        perfStats.eventsUpdated++;
    };

    // 🎯 OPTIMIZATION 3: Cache expensive label text operations
    if (settings.enableLabelCache) {
        const _Game_Event_updateEventLabelText = Game_Event.prototype.updateEventLabelText;
        Game_Event.prototype.updateEventLabelText = function () {
            if (!this._labelWindow) return;

            // Create cache key from event ID and original text
            const cacheKey = `${this._mapId}_${this._eventId}_${this._labelWindow.originalText}`;

            // Check cache first
            if (labelTextCache.has(cacheKey)) {
                this._labelWindow.text = labelTextCache.get(cacheKey);
                perfStats.labelCacheHits++;
                return;
            }

            // Call original method
            _Game_Event_updateEventLabelText.call(this);

            // Cache the result
            labelTextCache.set(cacheKey, this._labelWindow.text);
            perfStats.labelCacheMisses++;

            // Limit cache size to prevent memory leaks
            if (labelTextCache.size > 200) {
                const firstKey = labelTextCache.keys().next().value;
                labelTextCache.delete(firstKey);
            }
        };
    }

    // 🎯 OPTIMIZATION 4: Throttle sprite updates (DISABLED - was causing sprite scaling visual bugs)
    // This optimization was interfering with other plugins that handle sprite scaling/visual effects
    // Sprite updates need to run every frame for smooth visual effects

    /*
  const _Sprite_Character_updateEventsAndMovementCore = Sprite_Character.prototype.updateEventsAndMovementCore;
  Sprite_Character.prototype.updateEventsAndMovementCore = function() {
    // Initialize update counter
    if (!this._spriteUpdateCounter) this._spriteUpdateCounter = 0;
    this._spriteUpdateCounter++;

    // Throttle expensive sprite updates
    if (this._spriteUpdateCounter % 3 !== 0) {
      perfStats.spriteUpdatesSkipped++;
      return;
    }

    _Sprite_Character_updateEventsAndMovementCore.call(this);
  };
  */

    // 🎯 OPTIMIZATION 5: Optimize label sprite updates (CONSERVATIVE - only throttle labels, not character sprites)
    const _Sprite_EventLabel_update = Sprite_EventLabel.prototype.update;
    Sprite_EventLabel.prototype.update = function () {
        // Initialize update counter
        if (!this._labelUpdateCounter) this._labelUpdateCounter = 0;
        this._labelUpdateCounter++;

        // Throttle label updates (labels are less critical for visual smoothness)
        if (this._labelUpdateCounter % 3 !== 0) {
            // Reduced from 5 to 3 for better responsiveness
            return;
        }

        _Sprite_EventLabel_update.call(this);
    };

    // 🎯 OPTIMIZATION 6: Limit maximum active events (DISABLED - was interfering with touch input)
    // This optimization has been disabled because it was preventing proper touch input handling
    // The event limiting was interfering with eventsXy() calls needed for touch processing

    // If you want to re-enable this optimization, you'll need to find a way to exclude
    // touch input processing from the event limiting without using expensive stack traces

    /*
  const _Game_Map_events = Game_Map.prototype.events;
  Game_Map.prototype.events = function() {
    const allEvents = _Game_Map_events.call(this);

    // Limit to maximum active events for performance
    if (allEvents.length > settings.maxActiveEvents) {
      debugLog(`Limiting events from ${allEvents.length} to ${settings.maxActiveEvents} for performance`);
      return allEvents.slice(0, settings.maxActiveEvents);
    }

    return allEvents;
  };
  */

    // 🎯 OPTIMIZATION 7: Performance monitoring
    if (settings.debugMode) {
        setInterval(() => {
            debugLog(
                `Performance Stats: Events Updated=${perfStats.eventsUpdated}, Skipped=${perfStats.eventsSkipped}, Sprite Updates Skipped=${perfStats.spriteUpdatesSkipped}, Label Cache Hits=${perfStats.labelCacheHits}, Misses=${perfStats.labelCacheMisses}`
            );

            // Reset counters
            perfStats.eventsSkipped = 0;
            perfStats.eventsUpdated = 0;
            perfStats.spriteUpdatesSkipped = 0;
            perfStats.labelCacheHits = 0;
            perfStats.labelCacheMisses = 0;
        }, 5000); // Every 5 seconds
    }

    // 🎯 OPTIMIZATION 8: Clean up caches on scene change
    const _Scene_Map_terminate = Scene_Map.prototype.terminate;
    Scene_Map.prototype.terminate = function () {
        _Scene_Map_terminate.call(this);

        // Clear caches to prevent memory leaks
        labelTextCache.clear();
        visibilityCache.clear();
        debugLog('Cleared all caches on scene change');
    };

    debugLog(
        `Events Move Core Performance Patch v2 loaded! Settings: Throttle=${settings.eventUpdateThrottle}, MaxEvents=${settings.maxActiveEvents}, OffScreenSkip=${settings.enableOffScreenSkip}, LabelCache=${settings.enableLabelCache}`
    );
})();
