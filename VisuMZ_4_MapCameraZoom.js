//=============================================================================
// VisuStella MZ - Map Camera Zoom
// VisuMZ_4_MapCameraZoom.js
//=============================================================================

var Imported = Imported || {};
Imported.VisuMZ_4_MapCameraZoom = true;
var VisuMZ = VisuMZ || {};
VisuMZ.MapCameraZoom = VisuMZ.MapCameraZoom || {};
VisuMZ.MapCameraZoom.version = 1.07;

//=============================================================================
/*:
 * @target MZ
 * @plugindesc [RPG Maker MZ] [Tier 4] [Version 1.07] [MapCameraZoom]
 * <AUTHOR>
 * @url http://www.yanfly.moe/wiki/Map_Camera_Zoom_VisuStella_MZ
 * @orderAfter VisuMZ_0_CoreEngine
 *
 * @help
 * ============================================================================
 * Introduction
 * ============================================================================
 *
 * This plugin enables the ability to zoom the in-game camera inward and make
 * the visible game area larger and more focused. The camera can also focus on
 * events or specific tiles other than just the player, making it helpful for
 * cutscenes. Easing accessibility also makes the zoom and camera shifts more
 * soft and less rough feeling.
 *
 * Features include all (but not limited to) the following:
 *
 * * Zoom ability allows the camera to zoom inward and enlarge the focal point.
 * * Auto-zoom notetag allows for the camera to automatically shift when
 *   entering specific maps.
 * * Camera focus function allows the game camera to instantly move over to the
 *   target event or target tile.
 * * Easing accessibility allow for smoothing zooming and camera focus changes
 *   alongside dedicated wait time control.
 * * Wait for Zoom and Wait for Camera Focus plugin commands are available for
 *   more on the go flexibility in eventing.
 *
 * ============================================================================
 * Requirements
 * ============================================================================
 *
 * This plugin is made for RPG Maker MZ. This will not work in other iterations
 * of RPG Maker.
 *
 * ------ Tier 4 ------
 *
 * This plugin is a Tier 4 plugin. Place it under other plugins of lower tier
 * value on your Plugin Manager list (ie: 0, 1, 2, 3, 4, 5). This is to ensure
 * that your plugins will have the best compatibility with the rest of the
 * VisuStella MZ library.
 *
 * ============================================================================
 * Caution
 * ============================================================================
 *
 * When using this plugin, there are things to be cautious about.
 *
 * ---
 *
 * Screen Tearing
 *
 * When using non-whole odd numbers like 1.3, 1.5, and 1.7, the likelihood of
 * there being a "screen tearing" effect for the tilemap or for sprites is
 * greatly increased. This can be avoided by having sprites with a pixel-worth
 * of buffering space or by just simply avoiding to use non-whole odd numbers
 * altogether.
 *
 * ---
 *
 * ============================================================================
 * Major Changes
 * ============================================================================
 *
 * This plugin adds some new hard-coded features to RPG Maker MZ's functions.
 * The following is a list of them.
 *
 * ---
 *
 * Cannot Go Under 100%
 *
 * You can zoom in (aka go above 100% zoom), but you cannot zoom out (aka go
 * under 100% zoom). The reasoning behind this is because of the limitation
 * between PixiJS and WebGL. Going under 100% zoom will break the tilemap and
 * cause large chunks of it to go missing.
 *
 * This is true even without this plugin installed as you can try to use the
 * innate RPG Maker MZ zoom functions and try to set the zoom scale under 100%.
 * The tileset will immediately start to fall apart.
 *
 * ---
 *
 * Sprites No Longer Smoothed
 *
 * When using this plugin, certain resources like on-map character sprites and
 * some tile sprites will have bitmap smoothing removed. The reason for this is
 * due to PixiJS's texture bleeding problem when the sprites are zoomed in. If
 * left alone, this causes an ugly filmy border around the edges of the
 * sprite's dimensions that are otherwise an eye-sore to look at.
 *
 * ---
 *
 * ============================================================================
 * VisuStella MZ Compatibility
 * ============================================================================
 *
 * While this plugin is compatible with the majority of the VisuStella MZ
 * plugin library, it is not compatible with specific plugins or specific
 * features. This section will highlight the main plugins/features that will
 * not be compatible with this plugin or put focus on how the make certain
 * features compatible.
 *
 * ---
 *
 * VisuMZ_0_CoreEngine
 *
 * Having the VisuMZ Core Engine installed will enable you to use easing when
 * it comes to zooming and camera panning.
 *
 * ---
 *
 * Picture Zooming
 *
 * If you are NOT using the VisuMZ Core Engine, pictures will be bound to the
 * zoom scale. This is NOT a bug. If you are using pictures in a completely
 * vanilla RPG Maker MZ project without any plugins installed and enter a
 * battle, the battle zoom will also make the pictures zoom in as well.
 *
 * ---
 *
 * ============================================================================
 * Notetags
 * ============================================================================
 *
 * The following are notetags that have been added through this plugin. These
 * notetags will not work with your game if this plugin is OFF or not present.
 *
 * ---
 *
 * === Map-Related Notetags ===
 *
 * ---
 *
 * <Zoom: x%>
 * <AutoZoom: x%>
 * <Auto Zoom: x%>
 *
 * - Used for: Map Notetags
 * - Causes the game camera to automatically zoom to x% when entering a map
 *   with this notetag.
 *   - This does NOT reverse itself when exiting the map. The zoom settings
 *     will carry over to other maps unless those maps have their own auto-zoom
 *     notetag present.
 * - The notetag variants do the same thing. Which you choose to use is
 *   entirely up to personal preference.
 * - Replace 'x' with a percentage value above 100% to represent the zoom scale
 *   you wish to change to when entering this map.
 *   - 'x' cannot be under 100%! Read the "Cannot Go Under 100%" section for
 *     more information as to why.
 *
 * ---
 *
 * ============================================================================
 * Plugin Commands
 * ============================================================================
 *
 * The following are Plugin Commands that come with this plugin. They can be
 * accessed through the Plugin Command event command.
 *
 * ---
 *
 * === Camera Plugin Commands ===
 *
 * ---
 *
 * Camera: Focus Player
 * - Puts the camera focus on the player character.
 *
 *   Duration:
 *   - How many frames should it take to finish focus?
 *   - 60 frames = 1 second.
 *
 *   Easing Type:
 *   - Select which easing type you wish to apply.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Camera: Focus Target Event
 * - Puts the camera focus on target event.
 *
 *   Event ID:
 *   - Insert the ID of the event to focus on.
 *   - Use 0 for this event.
 *   - You may use JavaScript code.
 *
 *   Duration:
 *   - How many frames should it take to finish focus?
 *   - 60 frames = 1 second.
 *
 *   Easing Type:
 *   - Select which easing type you wish to apply.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Camera: Focus Target Tile
 * - Puts the camera focus on target map tile.
 *
 *   Map Tile X:
 *   - What is the X coordinate of the target map tile?
 *   - You may use JavaScript code.
 *
 *   Map Tile Y:
 *   - What is the Y coordinate of the target map tile?
 *   - You may use JavaScript code.
 *
 *   Duration:
 *   - How many frames should it take to finish focus?
 *   - 60 frames = 1 second.
 *
 *   Easing Type:
 *   - Select which easing type you wish to apply.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Camera: Wait for Focus
 * - Waits for camera focus to finish changing before continuing.
 *
 * ---
 *
 * === Zoom Plugin Commands ===
 *
 * ---
 *
 * Zoom: Change Zoom
 * - Change the current zoom amount.
 *
 *   Target Zoom Scale:
 *   - What is the target zoom scale?
 *   - 1.0 = 100%; 1.5 = 150%; 2.0 = 200%;
 *   - Cannot go under 1.0!
 *
 *   Duration:
 *   - How many frames should it take to finish zooming?
 *   - 60 frames = 1 second.
 *
 *   Easing Type:
 *   - Select which easing type you wish to apply.
 *   - Requires VisuMZ_0_CoreEngine!
 *
 * ---
 *
 * Zoom: Wait for Zoom
 * - Waits for zoom to finish changing before continuing.
 *
 * ---
 *
 * ============================================================================
 * Plugin Parameters: General Settings
 * ============================================================================
 *
 * These are the general settings used for the Map Camera Zoom plugin.
 *
 * ---
 *
 * Settings
 *
 *   Default Zoom:
 *   - What is the default zoom value?
 *   - 1.0 = 100%; 1.5 = 150%; 2.0 = 200%;
 *   - Cannot go under 1.0!
 *
 *   Adapt Battle Encounter Ani:
 *   - Adapt the battle encounter zoom effect?
 *   - Occurs when entering battle from the map.
 *
 *   Force Pixelated Map:
 *   - Force the map's tilesets to be rendered in pixelated form regardless of
 *     what other plugins may do.
 *   - This is primarily for pixel art games that would look better with more
 *     pixelated tiles when zoomed in.
 *
 * ---
 *
 * Compatibility
 *
 *   Map Lock Adjust:
 *   - Adjusts the Map Lock effect to the map's display position when exiting
 *     menus.
 *   - For VisuMZ_4_VisualParallaxes.
 *   - Best left false unless you know what you're doing.
 *
 * ---
 *
 * ============================================================================
 * Terms of Use
 * ============================================================================
 *
 * 1. These plugins may be used in free or commercial games provided that they
 * have been acquired through legitimate means at VisuStella.com and/or any
 * other official approved VisuStella sources. Exceptions and special
 * circumstances that may prohibit usage will be listed on VisuStella.com.
 *
 * 2. All of the listed coders found in the Credits section of this plugin must
 * be given credit in your games or credited as a collective under the name:
 * "VisuStella".
 *
 * 3. You may edit the source code to suit your needs, so long as you do not
 * claim the source code belongs to you. VisuStella also does not take
 * responsibility for the plugin if any changes have been made to the plugin's
 * code, nor does VisuStella take responsibility for user-provided custom code
 * used for custom control effects including advanced JavaScript notetags
 * and/or plugin parameters that allow custom JavaScript code.
 *
 * 4. You may NOT redistribute these plugins nor take code from this plugin to
 * use as your own. These plugins and their code are only to be downloaded from
 * VisuStella.com and other official/approved VisuStella sources. A list of
 * official/approved sources can also be found on VisuStella.com.
 *
 * 5. VisuStella is not responsible for problems found in your game due to
 * unintended usage, incompatibility problems with plugins outside of the
 * VisuStella MZ library, plugin versions that aren't up to date, nor
 * responsible for the proper working of compatibility patches made by any
 * third parties. VisuStella is not responsible for errors caused by any
 * user-provided custom code used for custom control effects including advanced
 * JavaScript notetags and/or plugin parameters that allow JavaScript code.
 *
 * 6. If a compatibility patch needs to be made through a third party that is
 * unaffiliated with VisuStella that involves using code from the VisuStella MZ
 * library, contact must be made with a member from VisuStella and have it
 * approved. The patch would be placed on VisuStella.com as a free download
 * to the public. Such patches cannot be sold for monetary gain, including
 * commissions, crowdfunding, and/or donations.
 *
 * 7. If this VisuStella MZ plugin is a paid product, all project team members
 * must purchase their own individual copies of the paid product if they are to
 * use it. Usage includes working on related game mechanics, managing related
 * code, and/or using related Plugin Commands and features. Redistribution of
 * the plugin and/or its code to other members of the team is NOT allowed
 * unless they own the plugin itself as that conflicts with Article 4.
 *
 * 8. Any extensions and/or addendums made to this plugin's Terms of Use can be
 * found on VisuStella.com and must be followed.
 *
 * ============================================================================
 * Credits
 * ============================================================================
 *
 * If you are using this plugin, credit the following people in your game:
 *
 * Team VisuStella
 * * Arisu
 * * Irina
 * * Olivia
 * * Yanfly
 *
 * ============================================================================
 * Changelog
 * ============================================================================
 *
 * Version 1.07: December 19, 2024
 * * Compatibility Update!
 * ** Added better compatibility with outside plugins when doing battle
 *    transitions. Camera will automatically focus the player if the
 *    "Adapt Battle Encounter Ani" is enabled.
 * ** Added better compatibility with VisuMZ's Event Title Scene plugin.
 *
 * Version 1.06: October 17, 2024
 * * Compatibility Update!
 * ** Added better compatibility with Visual Parallaxes when using plugin
 *    commands to focus scroll.
 *
 * Version 1.05: July 18, 2024
 * * Compatibility Update!
 * ** Plugin now works better with Movement Core's smooth scroll.
 *
 * Version 1.04: April 18, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * * New Features!
 * ** New Plugin Parameter added by Arisu:
 * *** Plugin Parameters > Compatibility > Map Lock Adjust
 * **** Adjusts the Map Lock effect to the map's display position when exiting
 *      menus.
 * **** For VisuMZ_4_VisualParallaxes.
 * **** Best left false unless you know what you're doing.
 *
 * Version 1.03: March 14, 2024
 * * Documentation Update!
 * ** Help file updated for new features.
 * ** Added a new section called "Caution":
 * *** When using non-whole odd numbers like 1.3, 1.5, and 1.7, the likelihood
 *     of there being a "screen tearing" effect for the tilemap or for sprites
 *     is greatly increased. This can be avoided by having sprites with a
 *     pixel-worth of buffering space or by just simply avoiding to use
 *     non-whole odd numbers altogether.
 * * New Features!
 * ** New Plugin Parameter added by Irina:
 * *** Plugin Parameters > Force Pixelated Map
 * **** Force the map's tilesets to be rendered in pixelated form regardless of
 *      what other plugins may do.
 * **** This is primarily for pixel art games that would look better with more
 *      pixelated tiles when zoomed in.
 *
 * Version 1.02: July 13, 2023
 * * Optimization Update!
 * ** Plugin should run more optimized.
 *
 * Version 1.01: March 16, 2023
 * * Compatibility Update
 * ** Better camera zoom with VisuStella MZ Movement Effect's Smooth Scrolling
 *    when this plugin's 'Adapt Battle Encounter Ani' setting is turned off.
 *
 * Version 1.00 Official Release Date: November 2, 2022
 * * Finished Plugin!
 *
 * ============================================================================
 * End of Helpfile
 * ============================================================================
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Begin
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CameraFocusPlayer
 * @text Camera: Focus Player
 * @desc Puts the camera focus on the player character.
 *
 * @arg Duration:num
 * @text Duration
 * @type number
 * @min 1
 * @desc How many frames should it take to finish focus?
 * 60 frames = 1 second.
 * @default 60
 *
 * @arg EasingType:str
 * @text Easing Type
 * @type combo
 * @option Linear
 * @option InSine
 * @option OutSine
 * @option InOutSine
 * @option InQuad
 * @option OutQuad
 * @option InOutQuad
 * @option InCubic
 * @option OutCubic
 * @option InOutCubic
 * @option InQuart
 * @option OutQuart
 * @option InOutQuart
 * @option InQuint
 * @option OutQuint
 * @option InOutQuint
 * @option InExpo
 * @option OutExpo
 * @option InOutExpo
 * @option InCirc
 * @option OutCirc
 * @option InOutCirc
 * @option InBack
 * @option OutBack
 * @option InOutBack
 * @option InElastic
 * @option OutElastic
 * @option InOutElastic
 * @option InBounce
 * @option OutBounce
 * @option InOutBounce
 * @desc Select which easing type you wish to apply.
 * Requires VisuMZ_0_CoreEngine!
 * @default InOutSine
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CameraFocusTargetEvent
 * @text Camera: Focus Target Event
 * @desc Puts the camera focus on target event.
 *
 * @arg EventID:eval
 * @text Event ID
 * @desc Insert the ID of the event to focus on.
 * Use 0 for this event. You may use JavaScript code.
 * @default 0
 *
 * @arg Duration:num
 * @text Duration
 * @type number
 * @min 1
 * @desc How many frames should it take to finish focus?
 * 60 frames = 1 second.
 * @default 60
 *
 * @arg EasingType:str
 * @text Easing Type
 * @type combo
 * @option Linear
 * @option InSine
 * @option OutSine
 * @option InOutSine
 * @option InQuad
 * @option OutQuad
 * @option InOutQuad
 * @option InCubic
 * @option OutCubic
 * @option InOutCubic
 * @option InQuart
 * @option OutQuart
 * @option InOutQuart
 * @option InQuint
 * @option OutQuint
 * @option InOutQuint
 * @option InExpo
 * @option OutExpo
 * @option InOutExpo
 * @option InCirc
 * @option OutCirc
 * @option InOutCirc
 * @option InBack
 * @option OutBack
 * @option InOutBack
 * @option InElastic
 * @option OutElastic
 * @option InOutElastic
 * @option InBounce
 * @option OutBounce
 * @option InOutBounce
 * @desc Select which easing type you wish to apply.
 * Requires VisuMZ_0_CoreEngine!
 * @default InOutSine
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CameraFocusTargetTile
 * @text Camera: Focus Target Tile
 * @desc Puts the camera focus on target map tile.
 *
 * @arg MapX:eval
 * @text Map Tile X
 * @desc What is the X coordinate of the target map tile?
 * You may use JavaScript code.
 * @default 0
 *
 * @arg MapY:eval
 * @text Map Tile Y
 * @desc What is the Y coordinate of the target map tile?
 * You may use JavaScript code.
 * @default 0
 *
 * @arg Duration:num
 * @text Duration
 * @type number
 * @min 1
 * @desc How many frames should it take to finish focus?
 * 60 frames = 1 second.
 * @default 60
 *
 * @arg EasingType:str
 * @text Easing Type
 * @type combo
 * @option Linear
 * @option InSine
 * @option OutSine
 * @option InOutSine
 * @option InQuad
 * @option OutQuad
 * @option InOutQuad
 * @option InCubic
 * @option OutCubic
 * @option InOutCubic
 * @option InQuart
 * @option OutQuart
 * @option InOutQuart
 * @option InQuint
 * @option OutQuint
 * @option InOutQuint
 * @option InExpo
 * @option OutExpo
 * @option InOutExpo
 * @option InCirc
 * @option OutCirc
 * @option InOutCirc
 * @option InBack
 * @option OutBack
 * @option InOutBack
 * @option InElastic
 * @option OutElastic
 * @option InOutElastic
 * @option InBounce
 * @option OutBounce
 * @option InOutBounce
 * @desc Select which easing type you wish to apply.
 * Requires VisuMZ_0_CoreEngine!
 * @default InOutSine
 *
 * @ --------------------------------------------------------------------------
 *
 * @command CameraFocusWait
 * @text Camera: Wait for Focus
 * @desc Waits for camera focus to finish changing before continuing.
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_Zoom
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ZoomChange
 * @text Zoom: Change Zoom
 * @desc Change the current zoom amount.
 *
 * @arg TargetScale:num
 * @text Target Zoom Scale
 * @desc What is the target zoom scale?
 * 1.0 = 100%; 1.5 = 150%; 2.0 = 200%; Cannot go under 1.0!
 * @default 1.0
 *
 * @arg Duration:num
 * @text Duration
 * @type number
 * @min 1
 * @desc How many frames should it take to finish zooming?
 * 60 frames = 1 second.
 * @default 60
 *
 * @arg EasingType:str
 * @text Easing Type
 * @type combo
 * @option Linear
 * @option InSine
 * @option OutSine
 * @option InOutSine
 * @option InQuad
 * @option OutQuad
 * @option InOutQuad
 * @option InCubic
 * @option OutCubic
 * @option InOutCubic
 * @option InQuart
 * @option OutQuart
 * @option InOutQuart
 * @option InQuint
 * @option OutQuint
 * @option InOutQuint
 * @option InExpo
 * @option OutExpo
 * @option InOutExpo
 * @option InCirc
 * @option OutCirc
 * @option InOutCirc
 * @option InBack
 * @option OutBack
 * @option InOutBack
 * @option InElastic
 * @option OutElastic
 * @option InOutElastic
 * @option InBounce
 * @option OutBounce
 * @option InOutBounce
 * @desc Select which easing type you wish to apply.
 * Requires VisuMZ_0_CoreEngine!
 * @default InOutSine
 *
 * @ --------------------------------------------------------------------------
 *
 * @command ZoomWait
 * @text Zoom: Wait for Zoom
 * @desc Waits for zoom to finish changing before continuing.
 *
 * @ --------------------------------------------------------------------------
 *
 * @command Separator_End
 * @text -
 * @desc -
 *
 * @ --------------------------------------------------------------------------
 *
 * @ ==========================================================================
 * @ Plugin Parameters
 * @ ==========================================================================
 *
 * @param BreakHead
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param MapCameraZoom
 * @default Plugin Parameters
 *
 * @param ATTENTION
 * @default READ THE HELP FILE
 *
 * @param BreakSettings
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param DefaultZoom:num
 * @text Default Zoom
 * @desc What is the default zoom value?
 * 1.0 = 100%; 1.5 = 150%; 2.0 = 200%; Cannot go under 1.0!
 * @default 1.0
 *
 * @param AdaptBattleEncZoom:eval
 * @text Adapt Battle Encounter Ani
 * @parent Animation
 * @type boolean
 * @on Adapt
 * @off Unchanged
 * @desc Adapt the battle encounter zoom effect?
 * Occurs when entering battle from the map.
 * @default true
 *
 * @param ForcePixelatedMap:eval
 * @text Force Pixelated Map
 * @parent Animation
 * @type boolean
 * @on Force
 * @off Don't Force
 * @desc Force the map's tilesets to be rendered in pixelated form
 * regardless of what other plugins may do.
 * @default false
 *
 * @param Compatibility
 * @text Compatability Parameters
 *
 * @param VisualParallaxAdjust:eval
 * @text Map Lock Adjust
 * @parent Compatibility
 * @type boolean
 * @on Adjust
 * @off Don't Adjust
 * @desc Adjusts the Map Lock effect to the map's display position
 * when exiting menus. For VisuMZ_4_VisualParallaxes.
 * @default false
 *
 * @param BreakEnd1
 * @text --------------------------
 * @default ----------------------------------
 *
 * @param End Of
 * @default Plugin Parameters
 *
 * @param BreakEnd2
 * @text --------------------------
 * @default ----------------------------------
 *
 */
//=============================================================================
var tier = tier || 0x0;
var dependencies = [];
var pluginData = $plugins.filter(function (_0x9f21c5) {
    return _0x9f21c5.status && _0x9f21c5.description.includes('[MapCameraZoom]');
})[0x0];
VisuMZ.MapCameraZoom.Settings = VisuMZ.MapCameraZoom.Settings || {};
VisuMZ.ConvertParams = function (_0x35bc1c, _0x38f28a) {
    for (const _0x5a1293 in _0x38f28a) {
        if (_0x5a1293.match(/(.*):(.*)/i)) {
            const _0x58612b = String(RegExp.$1);
            const _0x317a39 = String(RegExp.$2).toUpperCase().trim();
            let _0x5af4a9;
            let _0x558f87;
            let _0x361aed;
            switch (_0x317a39) {
                case 'NUM':
                    _0x5af4a9 = _0x38f28a[_0x5a1293] !== '' ? Number(_0x38f28a[_0x5a1293]) : 0x0;
                    break;
                case 'ARRAYNUM':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x5a7d1e => Number(_0x5a7d1e));
                    break;
                case 'EVAL':
                    _0x5af4a9 = _0x38f28a[_0x5a1293] !== '' ? eval(_0x38f28a[_0x5a1293]) : null;
                    break;
                case 'ARRAYEVAL':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x53964a => eval(_0x53964a));
                    break;
                case 'JSON':
                    _0x5af4a9 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : '';
                    break;
                case 'ARRAYJSON':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x290487 => JSON.parse(_0x290487));
                    break;
                case 'FUNC':
                    _0x5af4a9 =
                        _0x38f28a[_0x5a1293] !== ''
                            ? new Function(JSON.parse(_0x38f28a[_0x5a1293]))
                            : new Function('return 0');
                    break;
                case 'ARRAYFUNC':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x4e4e7f => new Function(JSON.parse(_0x4e4e7f)));
                    break;
                case 'STR':
                    _0x5af4a9 = _0x38f28a[_0x5a1293] !== '' ? String(_0x38f28a[_0x5a1293]) : '';
                    break;
                case 'ARRAYSTR':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x2b9e11 => String(_0x2b9e11));
                    break;
                case 'STRUCT':
                    _0x361aed = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : {};
                    _0x5af4a9 = VisuMZ.ConvertParams({}, _0x361aed);
                    break;
                case 'ARRAYSTRUCT':
                    _0x558f87 = _0x38f28a[_0x5a1293] !== '' ? JSON.parse(_0x38f28a[_0x5a1293]) : [];
                    _0x5af4a9 = _0x558f87.map(_0x5a5605 =>
                        VisuMZ.ConvertParams({}, JSON.parse(_0x5a5605))
                    );
                    break;
                default:
                    continue;
            }
            _0x35bc1c[_0x58612b] = _0x5af4a9;
        }
    }
    return _0x35bc1c;
};
(_0xafe5be => {
    const _0x1797ce = _0xafe5be.name;
    for (const _0x28484e of dependencies) {
        if (!Imported[_0x28484e]) {
            alert(
                '%1 is missing a required plugin.\nPlease install %2 into the Plugin Manager.'.format(
                    _0x1797ce,
                    _0x28484e
                )
            );
            SceneManager.exit();
            break;
        }
    }
    const _0x2667ef = _0xafe5be.description;
    if (_0x2667ef.match(/\[Version[ ](.*?)\]/i)) {
        const _0x8a90b6 = Number(RegExp.$1);
        if (_0x8a90b6 !== VisuMZ.MapCameraZoom.version) {
            alert(
                "%1's version does not match plugin's. Please update it in the Plugin Manager.".format(
                    _0x1797ce,
                    _0x8a90b6
                )
            );
            SceneManager.exit();
        }
    }
    if (_0x2667ef.match(/\[Tier[ ](\d+)\]/i)) {
        const _0x3eb382 = Number(RegExp.$1);
        if (_0x3eb382 < tier) {
            alert(
                '%1 is incorrectly placed on the plugin list.\nIt is a Tier %2 plugin placed over other Tier %3 plugins.\nPlease reorder the plugin list from smallest to largest tier numbers.'.format(
                    _0x1797ce,
                    _0x3eb382,
                    tier
                )
            );
            SceneManager.exit();
        } else {
            tier = Math.max(_0x3eb382, tier);
        }
    }
    VisuMZ.ConvertParams(VisuMZ.MapCameraZoom.Settings, _0xafe5be.parameters);
})(pluginData);
(() => {
    if (Utils.RPGMAKER_VERSION < '1.1.0') {
        alert('Map Camera Zoom requires RPG Maker MZ version 1.1.0 or above.');
        SceneManager.exit();
    }
})();
PluginManager.registerCommand(pluginData.name, 'CameraFocusPlayer', _0x4db1be => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    if ($gamePlayer.isMapCameraFocusTarget()) {
        return;
    }
    VisuMZ.ConvertParams(_0x4db1be, _0x4db1be);
    const _0x4da047 = _0x4db1be.Duration || 0x1;
    const _0x310119 = _0x4db1be.EasingType || 'Linear';
    $gameScreen.setMapCameraFocusToPlayer(_0x4da047, _0x310119);
});
PluginManager.registerCommand(pluginData.name, 'CameraFocusTargetEvent', _0x10ca9a => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x10ca9a, _0x10ca9a);
    const _0x1cd9e2 = $gameTemp.getLastPluginCommandInterpreter();
    const _0x71f1e5 = _0x10ca9a.EventID || _0x1cd9e2.eventId();
    const _0x2c5c4 = $gameMap.event(_0x71f1e5);
    const _0x45ce69 = _0x10ca9a.Duration || 0x1;
    const _0x4464f8 = _0x10ca9a.EasingType || 'Linear';
    if (!_0x2c5c4) {
        return;
    }
    $gameScreen.setMapCameraFocusToEvent(_0x71f1e5, _0x45ce69, _0x4464f8);
});
PluginManager.registerCommand(pluginData.name, 'CameraFocusTargetTile', _0x582cb8 => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x582cb8, _0x582cb8);
    const _0xcf0af = _0x582cb8.MapX.clamp(0x0, $gameMap.width() - 0x1);
    const _0x4c0306 = _0x582cb8.MapY.clamp(0x0, $gameMap.height() - 0x1);
    const _0x477aa9 = _0x582cb8.Duration || 0x1;
    const _0x448b79 = _0x582cb8.EasingType || 'Linear';
    $gameScreen.setMapCameraFocusToTile(_0xcf0af, _0x4c0306, _0x477aa9, _0x448b79);
});
PluginManager.registerCommand(pluginData.name, 'CameraFocusWait', _0x4bf598 => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    const _0x181666 = $gameTemp.getLastPluginCommandInterpreter();
    _0x181666.setWaitMode('mapCameraFocus');
});
PluginManager.registerCommand(pluginData.name, 'ZoomChange', _0x46e504 => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    VisuMZ.ConvertParams(_0x46e504, _0x46e504);
    let _0x27c235 = _0x46e504.TargetScale;
    if (_0x27c235 < Game_Screen.MIN_ZOOM && $gameTemp.isPlaytest()) {
        alert('Zoom cannot go under 100%.');
        _0x27c235 = Game_Screen.MIN_ZOOM;
    }
    const _0x108e15 = _0x46e504.Duration || 0x1;
    const _0x5e69c4 = _0x46e504.EasingType || 'Linear';
    $gameScreen.startMapZoom(_0x27c235, _0x108e15, _0x5e69c4);
});
PluginManager.registerCommand(pluginData.name, 'ZoomWait', _0x349ab4 => {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return;
    }
    const _0x434227 = $gameTemp.getLastPluginCommandInterpreter();
    _0x434227.setWaitMode('mapZoom');
});
VisuMZ.MapCameraZoom.RegExp = {
    AutoZoom: /<(?:ZOOM|AUTO ZOOM|AUTOZOOM):[ ](\d+)([%％])>/i,
};
VisuMZ.MapCameraZoom.ImageManager_loadCharacter = ImageManager.loadCharacter;
ImageManager.loadCharacter = function (_0xa8b056) {
    const _0x59665d = VisuMZ.MapCameraZoom.ImageManager_loadCharacter.call(this, _0xa8b056);
    _0x59665d.smooth = false;
    return _0x59665d;
};
VisuMZ.MapCameraZoom.ImageManager_loadSystem = ImageManager.loadSystem;
ImageManager.loadSystem = function (_0x321a0f) {
    const _0x8596af = VisuMZ.MapCameraZoom.ImageManager_loadSystem.call(this, _0x321a0f);
    if (_0x321a0f === 'IconSet') {
        _0x8596af.smooth = false;
    }
    return _0x8596af;
};
VisuMZ.MapCameraZoom.ImageManager_loadTileset = ImageManager.loadTileset;
ImageManager.loadTileset = function (_0x6df8fb) {
    const _0x491a55 = VisuMZ.MapCameraZoom.ImageManager_loadTileset.call(this, _0x6df8fb);
    _0x491a55.smooth = false;
    return _0x491a55;
};
SceneManager.isSceneMap = function () {
    return this._scene && this._scene.constructor === Scene_Map;
};
SceneManager.isInstanceOfSceneMap = function () {
    return this._scene && this._scene instanceof Scene_Map;
};
Game_Temp.prototype.setLastPluginCommandInterpreter = function (_0x97c78a) {
    this._lastPluginCommandInterpreter = _0x97c78a;
};
Game_Temp.prototype.getLastPluginCommandInterpreter = function () {
    return this._lastPluginCommandInterpreter;
};
VisuMZ.MapCameraZoom.Game_Interpreter_PluginCommand = Game_Interpreter.prototype.command357;
Game_Interpreter.prototype.command357 = function (_0x416e74) {
    $gameTemp.setLastPluginCommandInterpreter(this);
    return VisuMZ.MapCameraZoom.Game_Interpreter_PluginCommand.call(this, _0x416e74);
};
Game_Screen.MIN_ZOOM = 0x1;
Game_Screen.DEFAULT_MAP_ZOOM_SCALE = Math.max(
    Game_Screen.MIN_ZOOM,
    VisuMZ.MapCameraZoom.Settings.DefaultZoom || 0x1
);
VisuMZ.MapCameraZoom.Game_Screen_initialize = Game_Screen.prototype.initialize;
Game_Screen.prototype.initialize = function () {
    VisuMZ.MapCameraZoom.Game_Screen_initialize.call(this);
    this.setupMapCameraZoom();
};
Game_Screen.prototype.setupMapCameraZoom = function () {
    this.setupMapZoomSettings();
    this.setupMapCameraSettings();
};
Game_Screen.prototype.centerMapCameraZoom = function (_0x5537a1) {
    const _0x1d1bf9 = this.mapCameraFocusTarget();
    $gameMap.centerMapCameraZoom(_0x1d1bf9._realX, _0x1d1bf9._realY, _0x5537a1);
};
VisuMZ.MapCameraZoom.Game_Screen_updateZoom = Game_Screen.prototype.updateZoom;
Game_Screen.prototype.updateZoom = function () {
    VisuMZ.MapCameraZoom.Game_Screen_updateZoom.call(this);
    this.updateMapZoom();
    this.updateMapCameraFocus();
};
Game_Screen.prototype.setupMapZoomSettings = function () {
    this._mapZoomSettings = {
        scale: Game_Screen.DEFAULT_MAP_ZOOM_SCALE,
        targetScale: Game_Screen.DEFAULT_MAP_ZOOM_SCALE,
        duration: 0x0,
        wholeDuration: 0x0,
        easingType: 'Linear',
    };
    this._mapEnterBattleZoom = {
        scale: 0x1,
        targetScale: 0x1,
        duration: 0x0,
        wholeDuration: 0x0,
        easingType: 'Linear',
    };
};
Game_Screen.prototype.mapZoomSettings = function () {
    if (this._mapZoomSettings === undefined) {
        this.setupMapZoomSettings();
    }
    return this._mapZoomSettings;
};
Game_Screen.prototype.mapZoomEnterBattleSettings = function () {
    if (this._mapEnterBattleZoom === undefined) {
        this.setupMapZoomSettings();
    }
    return this._mapEnterBattleZoom;
};
Game_Screen.prototype.startMapZoom = function (_0x461603, _0x19effc, _0x22e537) {
    const _0x598e53 = this.mapZoomSettings();
    if (_0x598e53.targetScale === _0x461603) {
        return;
    }
    _0x598e53.targetScale = _0x461603;
    _0x598e53.duration = _0x19effc || 0x1;
    _0x598e53.wholeDuration = _0x19effc || 0x1;
    _0x598e53.easingType = _0x22e537;
};
VisuMZ.MapCameraZoom.Game_Screen_zoomScale = Game_Screen.prototype.zoomScale;
Game_Screen.prototype.zoomScale = function () {
    let _0x166a14 = VisuMZ.MapCameraZoom.Game_Screen_zoomScale.call(this);
    if (!this.allowExtendMapZoom()) {
        return _0x166a14;
    }
    if (SceneManager.isInstanceOfSceneMap()) {
        _0x166a14 *= Math.max(this.mapZoomSettings().scale, Game_Screen.MIN_ZOOM);
        _0x166a14 *= Math.max(this.mapZoomEnterBattleSettings().scale, Game_Screen.MIN_ZOOM);
    }
    return _0x166a14;
};
Game_Screen.prototype.allowExtendMapZoom = function () {
    if (!SceneManager.isInstanceOfSceneMap()) {
        return false;
    }
    if ($gameTemp._doodadEditorMode) {
        return false;
    }
    if (Imported.VisuMZ_2_FurnitureSystem && $gameMap.isFurnitureSystemMode()) {
        return false;
    }
    return true;
};
Game_Screen.prototype.updateMapZoom = function () {
    const _0x5b01b0 = this.mapZoomSettings();
    if (_0x5b01b0.duration <= 0x0) {
        return;
    }
    const _0x49463d = _0x5b01b0.duration;
    const _0x5a4469 = _0x5b01b0.wholeDuration;
    const _0x17f48c = _0x5b01b0.easingType || 'Linear';
    const _0xdb960b = _0x5b01b0.scale;
    const _0x24e729 = _0x5b01b0.targetScale;
    _0x5b01b0.scale = VisuMZ.MapCameraZoom.applyEasing(
        _0xdb960b,
        _0x24e729,
        _0x49463d,
        _0x5a4469,
        _0x17f48c
    );
    this.centerMapCameraZoom(true);
    _0x5b01b0.duration--;
    if (_0x5b01b0.duration <= 0x0) {
        this.onUpdateMapZoomEnd();
    }
};
Game_Screen.prototype.onUpdateMapZoomEnd = function () {
    const _0xab5bea = this.mapZoomSettings();
    _0xab5bea.scale = _0xab5bea.targetScale;
};
VisuMZ.MapCameraZoom.applyEasing = function (
    _0x142c62,
    _0x1a3a8f,
    _0x2bc118,
    _0x28bc0c,
    _0x1b073f
) {
    const _0x1255fd = VisuMZ.ApplyEasing(
        (_0x28bc0c - _0x2bc118) / _0x28bc0c,
        _0x1b073f || 'Linear'
    );
    const _0xc85ea8 = VisuMZ.ApplyEasing(
        (_0x28bc0c - _0x2bc118 + 0x1) / _0x28bc0c,
        _0x1b073f || 'Linear'
    );
    const _0x4903e8 = (_0x142c62 - _0x1a3a8f * _0x1255fd) / (0x1 - _0x1255fd);
    return _0x4903e8 + (_0x1a3a8f - _0x4903e8) * _0xc85ea8;
};
if (!VisuMZ.ApplyEasing) {
    VisuMZ.ApplyEasing = function (_0xa79c05, _0x3e2144) {
        return _0xa79c05;
    };
}
Game_Screen.prototype.setupMapCameraSettings = function () {
    this._mapCameraSettings = {
        playerFocus: true,
        eventFocus: false,
        eventTargetID: 0x0,
        tileFocus: false,
        tileCoordinates: {
            _realX: 0x0,
            _realY: 0x0,
        },
        duration: 0x0,
        wholeDuration: 0x0,
        easingType: 'Linear',
        currentCamera: {
            _realX: 0x0,
            _realY: 0x0,
        },
    };
};
Game_Screen.prototype.mapCameraSettings = function () {
    if (this._mapCameraSettings === undefined) {
        this.setupMapCameraSettings();
    }
    return this._mapCameraSettings;
};
Game_Screen.prototype.mapCameraFocusTarget = function (_0x15f47f) {
    const _0x5797f6 = this.mapCameraSettings();
    if (!_0x15f47f && _0x5797f6.duration > 0x0) {
        return _0x5797f6.currentCamera;
    } else {
        if (_0x5797f6.playerFocus) {
            return $gamePlayer;
        } else {
            if (_0x5797f6.eventFocus) {
                return $gameMap.event(_0x5797f6.eventTargetID) || $gamePlayer;
            } else {
                if (_0x5797f6.tileFocus) {
                    return _0x5797f6.tileCoordinates;
                }
            }
        }
    }
    return $gamePlayer;
};
Game_Screen.prototype.isChangingMapCameraFocusTargets = function () {
    return this.mapCameraFocusTarget() === this.mapCameraSettings().currentCamera;
};
Game_Screen.prototype.setCurrentCameraFocusTile = function (_0x383c9b, _0x1bbd09) {
    const _0x6d43d5 = this.mapCameraSettings();
    const _0xd80d21 = this.mapCameraFocusTarget();
    _0x6d43d5.currentCamera._realX = _0xd80d21._realX;
    _0x6d43d5.currentCamera._realY = _0xd80d21._realY;
    _0x6d43d5.duration = _0x383c9b || 0x1;
    _0x6d43d5.wholeDuration = _0x383c9b || 0x1;
    _0x6d43d5.easingType = _0x1bbd09 || 'Linear';
};
Game_Screen.prototype.setMapCameraFocusToPlayer = function (_0x283b6b, _0x33a5f2) {
    const _0x5e6bb7 = this.mapCameraSettings();
    if ($gamePlayer.isMapCameraFocusTarget()) {
        return;
    }
    this.setCurrentCameraFocusTile(_0x283b6b, _0x33a5f2);
    _0x5e6bb7.playerFocus = true;
    _0x5e6bb7.eventFocus = false;
    _0x5e6bb7.tileFocus = false;
    const _0x38dac9 = _0x5e6bb7.tileCoordinates;
    _0x38dac9._realX = -0x1;
    _0x38dac9._realY = -0x1;
};
Game_Screen.prototype.setMapCameraFocusToEvent = function (_0x2991e9, _0x1d1150, _0x49cda7) {
    const _0x33098e = $gameMap.event(_0x2991e9);
    if (!_0x33098e) {
        return;
    }
    const _0x58240b = this.mapCameraSettings();
    if (_0x33098e.isMapCameraFocusTarget()) {
        return;
    }
    this.setCurrentCameraFocusTile(_0x1d1150, _0x49cda7);
    _0x58240b.playerFocus = false;
    _0x58240b.eventFocus = true;
    _0x58240b.tileFocus = false;
    _0x58240b.eventTargetID = _0x2991e9;
    const _0x40f5fd = _0x58240b.tileCoordinates;
    _0x40f5fd._realX = -0x1;
    _0x40f5fd._realY = -0x1;
};
Game_Screen.prototype.setMapCameraFocusToTile = function (
    _0x2e22e8,
    _0x51e23b,
    _0x1f5f6f,
    _0xf81477
) {
    const _0x1fe6cb = this.mapCameraSettings();
    const _0x96a1e6 = _0x1fe6cb.tileCoordinates;
    if (_0x96a1e6._realX === _0x2e22e8 && _0x96a1e6._realY === _0x51e23b) {
        return;
    }
    this.setCurrentCameraFocusTile(_0x1f5f6f, _0xf81477);
    _0x1fe6cb.playerFocus = false;
    _0x1fe6cb.eventFocus = false;
    _0x1fe6cb.tileFocus = true;
    _0x1fe6cb.tileCoordinates._realX = _0x2e22e8;
    _0x1fe6cb.tileCoordinates._realY = _0x51e23b;
};
Game_Screen.prototype.updateMapCameraFocus = function () {
    const _0x382c4c = this.mapCameraSettings();
    if (_0x382c4c.duration <= 0x0) {
        return;
    }
    const _0x225568 = _0x382c4c.duration;
    const _0x128805 = _0x382c4c.wholeDuration;
    const _0x194d6d = _0x382c4c.easingType || 'Linear';
    const _0x36a50d = _0x382c4c.currentCamera;
    const _0x205bf8 = this.mapCameraFocusTarget(true);
    const _0x2c6121 = $gameMap._displayX;
    const _0xbacd2a = $gameMap._displayY;
    _0x36a50d._realX = VisuMZ.MapCameraZoom.applyEasing(
        _0x36a50d._realX,
        _0x205bf8._realX,
        _0x225568,
        _0x128805,
        _0x194d6d
    );
    _0x36a50d._realY = VisuMZ.MapCameraZoom.applyEasing(
        _0x36a50d._realY,
        _0x205bf8._realY,
        _0x225568,
        _0x128805,
        _0x194d6d
    );
    this.centerMapCameraZoom(true);
    if (this.updateMapCameraFocusSmooth()) {
        const _0x2b24d5 = $gameMap._displayX;
        const _0x145843 = $gameMap._displayY;
        $gameMap._displayX = VisuMZ.MapCameraZoom.applyEasing(
            _0x2c6121,
            _0x2b24d5,
            _0x225568,
            _0x128805,
            _0x194d6d
        );
        $gameMap._displayY = VisuMZ.MapCameraZoom.applyEasing(
            _0xbacd2a,
            _0x145843,
            _0x225568,
            _0x128805,
            _0x194d6d
        );
    }
    _0x382c4c.duration--;
    if (_0x382c4c.duration <= 0x0) {
        this.onUpdateMapCameraFocusEnd();
    }
};
Game_Screen.prototype.updateMapCameraFocusSmooth = function () {
    return false;
    if (!Imported.VisuMZ_2_MovementEffects) {
        return false;
    }
    if (!$gamePlayer.canSmoothScroll()) {
        return false;
    }
    const _0x76d7cf = this.mapCameraSettings();
    const _0x137268 = _0x76d7cf.duration;
    const _0xb8b81f = _0x76d7cf.wholeDuration;
    return _0x137268 > _0xb8b81f;
};
Game_Screen.prototype.onUpdateMapCameraFocusEnd = function () {
    const _0x355365 = this.mapCameraSettings();
    const _0x25adf4 = _0x355365.currentCamera;
    const _0x5d14e3 = this.mapCameraFocusTarget(true);
    _0x25adf4._realX = _0x5d14e3._realX;
    _0x25adf4._realY = _0x5d14e3._realY;
};
Game_Picture.prototype.xScrollLinkedOffset = function () {
    const _0x2bcbce = $gameMap.displayX() * $gameMap.tileWidth();
    return (this._x - _0x2bcbce) * $gameScreen.zoomScale();
};
Game_Picture.prototype.yScrollLinkedOffset = function () {
    const _0x50d3f1 = $gameMap.displayY() * $gameMap.tileHeight();
    return (this._y - _0x50d3f1) * $gameScreen.zoomScale();
};
VisuMZ.MapCameraZoom.Game_Map_setup = Game_Map.prototype.setup;
Game_Map.prototype.setup = function (_0x1ae407) {
    VisuMZ.MapCameraZoom.Game_Map_setup.call(this, _0x1ae407);
    this.setupMapCameraZoomNotetags();
    this._mapCameraParallaxUpdates = 0x0;
};
Game_Map.prototype.setupMapCameraZoomNotetags = function () {
    const _0x4464b5 = VisuMZ.MapCameraZoom.RegExp;
    const _0x274a99 = $dataMap ? $dataMap.note || '' : '';
    if (_0x274a99.match(_0x4464b5.AutoZoom)) {
        let _0x52dfbc = Number(RegExp.$1) * 0.01;
        if (_0x52dfbc < 0x1 && $gameTemp.isPlaytest()) {
            alert('Zoom cannot go under 100%.');
        }
        _0x52dfbc = Math.max(Game_Screen.MIN_ZOOM, _0x52dfbc);
        $gameScreen.mapZoomSettings().scale = _0x52dfbc;
        $gameScreen.mapZoomSettings().targetScale = _0x52dfbc;
        $gameScreen.mapZoomSettings().duration = 0x0;
    }
    $gameScreen.centerMapCameraZoom();
};
Game_Map.prototype.centerMapCameraZoom = function (_0x463db2, _0x18663d, _0x45b6f0) {
    _0x463db2 -= $gamePlayer.centerX();
    _0x18663d -= $gamePlayer.centerY();
    if (_0x45b6f0) {
        this.setDisplayPosMapCameraZoom(_0x463db2, _0x18663d);
    }
    this.updateMapCameraCenteredParallax(_0x463db2, _0x18663d);
    this.updateMapScrollLinkedCenteredParallax(_0x463db2, _0x18663d);
};
Game_Map.prototype.setDisplayPosMapCameraZoom = function (_0x45da21, _0x411d39) {
    if (this.isLoopHorizontal()) {
        this._displayX = _0x45da21.mod(this.width());
        this._parallaxX = _0x45da21;
    } else {
        const _0xd52904 = this.width() - this.screenTileX();
        this._displayX = _0xd52904 < 0x0 ? _0xd52904 / 0x2 : _0x45da21.clamp(0x0, _0xd52904);
        this._parallaxX = this._displayX;
    }
    if (this.isLoopVertical()) {
        this._displayY = _0x411d39.mod(this.height());
        this._parallaxY = _0x411d39;
    } else {
        const _0x351ed6 = this.height() - this.screenTileY();
        this._displayY = _0x351ed6 < 0x0 ? _0x351ed6 / 0x2 : _0x411d39.clamp(0x0, _0x351ed6);
        this._parallaxY = this._displayY;
    }
};
Game_Map.prototype.updateMapCameraCenteredParallax = function (_0x8d1f85, _0x6ba7a) {
    const _0xc5b958 = this._mapCameraParallaxUpdates || 0x0;
    if (_0xc5b958 <= 0x0) {
        return;
    }
    if (this._parallaxLoopX) {
        this._parallaxX += (this._parallaxSx / this.tileWidth() / 0x2) * _0xc5b958;
    }
    if (this._parallaxLoopY) {
        this._parallaxY += (this._parallaxSy / this.tileHeight() / 0x2) * _0xc5b958;
    }
};
Game_Map.prototype.updateMapScrollLinkedCenteredParallax = function (_0x31c4f5, _0x159c40) {
    if (this.meetsVisualParallaxMapScrollLinkRequirements()) {
        this._visualParallaxSettings = this._visualParallaxSettings || [];
        for (const _0x312de0 of this.getVisualParallaxes()) {
            if (!_0x312de0) {
                continue;
            }
            if (_0x312de0._parallaxZero) {
                _0x312de0._parallaxX = this._displayX;
                _0x312de0._parallaxY = this._displayY;
            }
        }
    }
};
Game_Map.prototype.meetsVisualParallaxMapScrollLinkRequirements = function (_0x284bcd, _0xdf1866) {
    if (!Imported.VisuMZ_4_VisualParallaxes) {
        return false;
    }
    if (VisuMZ.MapCameraZoom.Settings.VisualParallaxAdjust) {
        return true;
    }
    if ($gameScreen.mapCameraSettings().duration > 0x0) {
        return true;
    }
    return false;
};
VisuMZ.MapCameraZoom.Game_Map_updateParallax = Game_Map.prototype.updateParallax;
Game_Map.prototype.updateParallax = function () {
    VisuMZ.MapCameraZoom.Game_Map_updateParallax.call(this);
    this._mapCameraParallaxUpdates = this._mapCameraParallaxUpdates || 0x0;
    this._mapCameraParallaxUpdates++;
};
VisuMZ.MapCameraZoom.Game_Map_parallaxOx = Game_Map.prototype.parallaxOx;
Game_Map.prototype.parallaxOx = function () {
    let _0x53ac9f = VisuMZ.MapCameraZoom.Game_Map_parallaxOx.call(this);
    if (this._parallaxZero) {
        _0x53ac9f = Math.floor(_0x53ac9f);
    }
    return _0x53ac9f;
};
VisuMZ.MapCameraZoom.Game_Map_parallaxOy = Game_Map.prototype.parallaxOy;
Game_Map.prototype.parallaxOy = function () {
    let _0x56a3fa = VisuMZ.MapCameraZoom.Game_Map_parallaxOy.call(this);
    if (this._parallaxZero) {
        _0x56a3fa = Math.floor(_0x56a3fa);
    }
    return _0x56a3fa;
};
Game_Map.prototype.canvasToMapX = function (_0x1034c1) {
    const _0x5e8c43 = this.tileWidth() * $gameScreen.zoomScale();
    const _0x18eb02 = this._displayX * _0x5e8c43;
    const _0x47b98d = Math.floor((_0x18eb02 + _0x1034c1) / _0x5e8c43);
    return this.roundX(_0x47b98d);
};
Game_Map.prototype.canvasToMapY = function (_0x384cf6) {
    const _0x219cb4 = this.tileHeight() * $gameScreen.zoomScale();
    const _0x273097 = this._displayY * _0x219cb4;
    const _0x4cf0fa = Math.floor((_0x273097 + _0x384cf6) / _0x219cb4);
    return this.roundY(_0x4cf0fa);
};
VisuMZ.MapCameraZoom.Game_Map_screenTileX = Game_Map.prototype.screenTileX;
Game_Map.prototype.screenTileX = function () {
    const _0x2e30b0 = VisuMZ.MapCameraZoom.Game_Map_screenTileX.call(this);
    return _0x2e30b0 / $gameScreen.zoomScale();
};
VisuMZ.MapCameraZoom.Game_Map_screenTileY = Game_Map.prototype.screenTileY;
Game_Map.prototype.screenTileY = function () {
    const _0x11358d = VisuMZ.MapCameraZoom.Game_Map_screenTileY.call(this);
    return _0x11358d / $gameScreen.zoomScale();
};
Game_CharacterBase.prototype.isMapCameraFocusTarget = function () {
    return $gameScreen.mapCameraFocusTarget() === this;
};
VisuMZ.MapCameraZoom.Game_Player_clearTransferInfo = Game_Player.prototype.clearTransferInfo;
Game_Player.prototype.clearTransferInfo = function () {
    VisuMZ.MapCameraZoom.Game_Player_clearTransferInfo.call(this);
    $gameScreen.setMapCameraFocusToPlayer(0x1, 'Linear');
    $gameScreen.centerMapCameraZoom();
};
VisuMZ.MapCameraZoom.Game_Player_updateScroll = Game_Player.prototype.updateScroll;
Game_Player.prototype.updateScroll = function (_0x544d20, _0x12cca2) {
    if (!this.isMapCameraFocusTarget()) {
        return;
    }
    VisuMZ.MapCameraZoom.Game_Player_updateScroll.call(this, _0x544d20, _0x12cca2);
};
Game_Event.prototype.centerX = function () {
    return Game_Player.prototype.centerX.call(this);
};
Game_Event.prototype.centerY = function () {
    return Game_Player.prototype.centerY.call(this);
};
VisuMZ.MapCameraZoom.Game_Event_update = Game_Event.prototype.update;
Game_Event.prototype.update = function () {
    const _0x5d9690 = this.scrolledX();
    const _0x97abf6 = this.scrolledY();
    VisuMZ.MapCameraZoom.Game_Event_update.call(this);
    if (!this.isMapCameraFocusTarget()) {
        return;
    }
    this.updateScroll(_0x5d9690, _0x97abf6);
};
Game_Event.prototype.updateScroll = function (_0x4b8fbc, _0x1ff2e5) {
    return Game_Player.prototype.updateScroll.call(this, _0x4b8fbc, _0x1ff2e5);
};
Game_Event.prototype.canSmoothScroll = function () {
    try {
        return Game_Player.prototype.canSmoothScroll.call(this);
    } catch (_0x3477e0) {
        return false;
    }
};
Game_Event.prototype.updateScrollSmoothCamera = function (_0x2e78ea, _0x4208b3) {
    try {
        Game_Player.prototype.updateScrollSmoothCamera.call(this, _0x2e78ea, _0x4208b3);
    } catch (_0x444854) {
        VisuMZ.MovementEffects.Game_Player_updateScroll.call(this, _0x2e78ea, _0x4208b3);
    }
};
Game_Event.prototype.isInAirship = function () {
    return false;
};
VisuMZ.MapCameraZoom.Game_Interpreter_updateWaitMode = Game_Interpreter.prototype.updateWaitMode;
Game_Interpreter.prototype.updateWaitMode = function () {
    if (this._waitMode === 'mapCameraFocus') {
        if ($gameScreen.mapCameraSettings().duration > 0x0) {
            return true;
        }
        this._waitMode = '';
    } else {
        if (this._waitMode === 'mapZoom') {
            if ($gameScreen.mapZoomSettings().duration > 0x0) {
                return true;
            }
            this._waitMode = '';
        }
    }
    return VisuMZ.MapCameraZoom.Game_Interpreter_updateWaitMode.call(this);
};
Scene_Map.MAP_ZOOM_ENTER_BATTLE_ADAPT = VisuMZ.MapCameraZoom.Settings.AdaptBattleEncZoom;
VisuMZ.MapCameraZoom.Scene_Map_start = Scene_Map.prototype.start;
Scene_Map.prototype.start = function () {
    VisuMZ.MapCameraZoom.Scene_Map_start.call(this);
    if (Scene_Map.MAP_ZOOM_ENTER_BATTLE_ADAPT) {
        $gameScreen.mapZoomEnterBattleSettings().scale = 0x1;
        const _0x4f55db = this.shouldCenterMapCameraZoom();
        $gameScreen.centerMapCameraZoom(_0x4f55db);
    }
};
Scene_Map.prototype.shouldCenterMapCameraZoom = function () {
    if (SceneManager.isPreviousScene(Scene_Menu)) {
        return false;
    }
    if (SceneManager.isPreviousScene(Scene_Boot)) {
        return false;
    }
    return true;
};
VisuMZ.MapCameraZoom.Scene_Map_updateEncounterEffect = Scene_Map.prototype.updateEncounterEffect;
Scene_Map.prototype.updateEncounterEffect = function () {
    $gameTemp._mapZoomEnterBattle = Scene_Map.MAP_ZOOM_ENTER_BATTLE_ADAPT;
    VisuMZ.MapCameraZoom.Scene_Map_updateEncounterEffect.call(this);
    $gameTemp._mapZoomEnterBattle = undefined;
};
VisuMZ.MapCameraZoom.Game_Screen_setZoom = Game_Screen.prototype.setZoom;
Game_Screen.prototype.setZoom = function (_0x50fb8c, _0x1a0bb1, _0x4eae84) {
    if ($gameTemp._mapZoomEnterBattle) {
        this.setBattleEncounterZoom(_0x4eae84);
    } else {
        VisuMZ.MapCameraZoom.Game_Screen_setZoom.call(this, _0x50fb8c, _0x1a0bb1, _0x4eae84);
    }
};
Game_Screen.prototype.setBattleEncounterZoom = function (_0x2d9ebb) {
    this.mapZoomEnterBattleSettings().scale = _0x2d9ebb;
    this.centerMapCameraZoom();
    const _0x3050f1 = this.mapCameraFocusTarget();
    $gamePlayer.center(_0x3050f1.x, _0x3050f1.y);
};
VisuMZ.MapCameraZoom.Game_System_isSmoothCameraEnabled =
    Game_System.prototype.isSmoothCameraEnabled;
Game_System.prototype.isSmoothCameraEnabled = function () {
    if (!Scene_Map.MAP_ZOOM_ENTER_BATTLE_ADAPT && SceneManager.isNextScene(Scene_Battle)) {
        return false;
    }
    return VisuMZ.MapCameraZoom.Game_System_isSmoothCameraEnabled.call(this);
};
VisuMZ.MapCameraZoom.Sprite_AnimationMV_updatePosition =
    Sprite_AnimationMV.prototype.updatePosition;
Sprite_AnimationMV.prototype.updatePosition = function () {
    if (SceneManager.isInstanceOfSceneMap() && this._animation.position === 0x3) {
        this.updateMapZoomPosition();
    } else {
        VisuMZ.MapCameraZoom.Sprite_AnimationMV_updatePosition.call(this);
    }
};
Sprite_AnimationMV.prototype.updateMapZoomPosition = function () {
    const _0x2d6beb = SceneManager._scene._spriteset;
    const _0xe397b7 = $gameScreen.zoomScale();
    const _0x28192e = 0.5 / _0xe397b7;
    const _0xe8c4ac = -_0x2d6beb.x / _0xe397b7;
    const _0x5c6bb5 = -_0x2d6beb.y / _0xe397b7;
    this.x = this.parent.width * _0x28192e + _0xe8c4ac;
    this.y = this.parent.height * _0x28192e + _0x5c6bb5;
};
if (VisuMZ.MapCameraZoom.Settings.ForcePixelatedMap ?? true) {
    Tilemap.Renderer.prototype._createInternalTextures = function () {
        this._destroyInternalTextures();
        for (let _0x4efcc2 = 0x0; _0x4efcc2 < Tilemap.Layer.MAX_GL_TEXTURES; _0x4efcc2++) {
            const _0x2ee6a1 = new PIXI.BaseRenderTexture();
            _0x2ee6a1.resize(0x800, 0x800);
            _0x2ee6a1.scaleMode = PIXI.SCALE_MODES.NEAREST;
            this._internalTextures.push(_0x2ee6a1);
        }
    };
}
